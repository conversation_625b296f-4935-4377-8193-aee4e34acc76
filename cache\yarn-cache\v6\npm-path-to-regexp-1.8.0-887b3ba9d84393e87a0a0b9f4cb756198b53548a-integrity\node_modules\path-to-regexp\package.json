{"name": "path-to-regexp", "description": "Express style path to RegExp utility", "version": "1.8.0", "main": "index.js", "typings": "index.d.ts", "files": ["index.js", "index.d.ts", "LICENSE"], "scripts": {"lint": "standard", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "prepublish": "typings install", "test": "npm run lint && npm run test-cov"}, "keywords": ["express", "regexp", "route", "routing"], "component": {"scripts": {"path-to-regexp": "index.js"}}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pillarjs/path-to-regexp.git"}, "devDependencies": {"chai": "^2.3.0", "istanbul": "~0.3.0", "mocha": "~2.2.4", "standard": "~3.7.3", "ts-node": "^0.5.5", "typescript": "^1.8.7", "typings": "^1.0.4"}, "dependencies": {"isarray": "0.0.1"}}