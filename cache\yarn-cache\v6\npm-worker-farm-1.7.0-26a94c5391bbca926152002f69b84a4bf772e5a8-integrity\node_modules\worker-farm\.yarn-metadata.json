{"manifest": {"name": "worker-farm", "description": "Distribute processing tasks to child processes with an über-simple API and baked-in durability & custom concurrency options.", "version": "1.7.0", "homepage": "https://github.com/rvagg/node-worker-farm", "authors": ["<PERSON> Vagg @rvagg <<EMAIL>> (https://github.com/rvagg)"], "keywords": ["worker", "child", "processing", "farm"], "main": "./lib/index.js", "repository": {"type": "git", "url": "https://github.com/rvagg/node-worker-farm.git"}, "dependencies": {"errno": "~0.1.7"}, "devDependencies": {"tape": "~4.10.1"}, "scripts": {"test": "node ./tests/"}, "types": "./index.d.ts", "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-worker-farm-1.7.0-26a94c5391bbca926152002f69b84a4bf772e5a8-integrity\\node_modules\\worker-farm\\package.json", "readmeFilename": "README.md", "readme": "# Worker Farm [![Build Status](https://secure.travis-ci.org/rvagg/node-worker-farm.svg)](http://travis-ci.org/rvagg/node-worker-farm)\n\n[![NPM](https://nodei.co/npm/worker-farm.png?downloads=true&downloadRank=true&stars=true)](https://nodei.co/npm/worker-farm/) [![NPM](https://nodei.co/npm-dl/worker-farm.png?months=6&height=3)](https://nodei.co/npm/worker-farm/)\n\n\nDistribute processing tasks to child processes with an über-simple API and baked-in durability & custom concurrency options. *Available in npm as <strong>worker-farm</strong>*.\n\n## Example\n\nGiven a file, *child.js*:\n\n```js\nmodule.exports = function (inp, callback) {\n  callback(null, inp + ' BAR (' + process.pid + ')')\n}\n```\n\nAnd a main file:\n\n```js\nvar workerFarm = require('worker-farm')\n  , workers    = workerFarm(require.resolve('./child'))\n  , ret        = 0\n\nfor (var i = 0; i < 10; i++) {\n  workers('#' + i + ' FOO', function (err, outp) {\n    console.log(outp)\n    if (++ret == 10)\n      workerFarm.end(workers)\n  })\n}\n```\n\nWe'll get an output something like the following:\n\n```\n#1 FOO BAR (8546)\n#0 FOO BAR (8545)\n#8 FOO BAR (8545)\n#9 FOO BAR (8546)\n#2 FOO BAR (8548)\n#4 FOO BAR (8551)\n#3 FOO BAR (8549)\n#6 FOO BAR (8555)\n#5 FOO BAR (8553)\n#7 FOO BAR (8557)\n```\n\nThis example is contained in the *[examples/basic](https://github.com/rvagg/node-worker-farm/tree/master/examples/basic/)* directory.\n\n### Example #1: Estimating π using child workers\n\nYou will also find a more complex example in *[examples/pi](https://github.com/rvagg/node-worker-farm/tree/master/examples/pi/)* that estimates the value of **π** by using a Monte Carlo *area-under-the-curve* method and compares the speed of doing it all in-process vs using child workers to complete separate portions.\n\nRunning `node examples/pi` will give you something like:\n\n```\nDoing it the slow (single-process) way...\nπ ≈ 3.1416269360000006  (0.0000342824102075312 away from actual!)\ntook 8341 milliseconds\nDoing it the fast (multi-process) way...\nπ ≈ 3.1416233600000036  (0.00003070641021052367 away from actual!)\ntook 1985 milliseconds\n```\n\n## Durability\n\nAn important feature of Worker Farm is **call durability**. If a child process dies for any reason during the execution of call(s), those calls will be re-queued and taken care of by other child processes. In this way, when you ask for something to be done, unless there is something *seriously* wrong with what you're doing, you should get a result on your callback function.\n\n## My use-case\n\nThere are other libraries for managing worker processes available but my use-case was fairly specific: I need to make heavy use of the [node-java](https://github.com/nearinfinity/node-java) library to interact with JVM code. Unfortunately, because the JVM garbage collector is so difficult to interact with, it's prone to killing your Node process when the GC kicks under heavy load. For safety I needed a durable way to make calls so that (1) it wouldn't kill my main process and (2) any calls that weren't successful would be resubmitted for processing.\n\nWorker Farm allows me to spin up multiple JVMs to be controlled by Node, and have a single, uncomplicated API that acts the same way as an in-process API and the calls will be taken care of by a child process even if an error kills a child process while it is working as the call will simply be passed to a new child process.\n\n**But**, don't think that Worker Farm is specific to that use-case, it's designed to be very generic and simple to adapt to anything requiring the use of child Node processes.\n\n## API\n\nWorker Farm exports a main function and an `end()` method. The main function sets up a \"farm\" of coordinated child-process workers and it can be used to instantiate multiple farms, all operating independently.\n\n### workerFarm([options, ]pathToModule[, exportedMethods])\n\nIn its most basic form, you call `workerFarm()` with the path to a module file to be invoked by the child process. You should use an **absolute path** to the module file, the best way to obtain the path is with `require.resolve('./path/to/module')`, this function can be used in exactly the same way as `require('./path/to/module')` but it returns an absolute path.\n\n#### `exportedMethods`\n\nIf your module exports a single function on `module.exports` then you should omit the final parameter. However, if you are exporting multiple functions on `module.exports` then you should list them in an Array of Strings:\n\n```js\nvar workers = workerFarm(require.resolve('./mod'), [ 'doSomething', 'doSomethingElse' ])\nworkers.doSomething(function () {})\nworkers.doSomethingElse(function () {})\n```\n\nListing the available methods will instruct Worker Farm what API to provide you with on the returned object. If you don't list a `exportedMethods` Array then you'll get a single callable function to use; but if you list the available methods then you'll get an object with callable functions by those names.\n\n**It is assumed that each function you call on your child module will take a `callback` function as the last argument.**\n\n#### `options`\n\nIf you don't provide an `options` object then the following defaults will be used:\n\n```js\n{\n    workerOptions               : {}\n  , maxCallsPerWorker           : Infinity\n  , maxConcurrentWorkers        : require('os').cpus().length\n  , maxConcurrentCallsPerWorker : 10\n  , maxConcurrentCalls          : Infinity\n  , maxCallTime                 : Infinity\n  , maxRetries                  : Infinity\n  , autoStart                   : false\n  , onChild                     : function() {}\n}\n```\n\n  * **<code>workerOptions</code>** allows you to customize all the parameters passed to child nodes. This object supports [all possible options of `child_process.fork`](https://nodejs.org/api/child_process.html#child_process_child_process_fork_modulepath_args_options). The default options passed are the parent `execArgv`, `cwd` and `env`. Any (or all) of them can be overridden, and others can be added as well.\n\n  * **<code>maxCallsPerWorker</code>** allows you to control the lifespan of your child processes. A positive number will indicate that you only want each child to accept that many calls before it is terminated. This may be useful if you need to control memory leaks or similar in child processes.\n\n  * **<code>maxConcurrentWorkers</code>** will set the number of child processes to maintain concurrently. By default it is set to the number of CPUs available on the current system, but it can be any reasonable number, including `1`.\n\n  * **<code>maxConcurrentCallsPerWorker</code>** allows you to control the *concurrency* of individual child processes. Calls are placed into a queue and farmed out to child processes according to the number of calls they are allowed to handle concurrently. It is arbitrarily set to 10 by default so that calls are shared relatively evenly across workers, however if your calls predictably take a similar amount of time then you could set it to `Infinity` and Worker Farm won't queue any calls but spread them evenly across child processes and let them go at it. If your calls aren't I/O bound then it won't matter what value you use here as the individual workers won't be able to execute more than a single call at a time.\n\n  * **<code>maxConcurrentCalls</code>** allows you to control the maximum number of calls in the queue&mdash;either actively being processed or waiting for a worker to be processed. `Infinity` indicates no limit but if you have conditions that may endlessly queue jobs and you need to set a limit then provide a `>0` value and any calls that push the limit will return on their callback with a `MaxConcurrentCallsError` error (check `err.type == 'MaxConcurrentCallsError'`).\n\n  * **<code>maxCallTime</code>** *(use with caution, understand what this does before you use it!)* when `!== Infinity`, will cap a time, in milliseconds, that *any single call* can take to execute in a worker. If this time limit is exceeded by just a single call then the worker running that call will be killed and any calls running on that worker will have their callbacks returned with a `TimeoutError` (check `err.type == 'TimeoutError'`). If you are running with `maxConcurrentCallsPerWorker` value greater than `1` then **all calls currently executing** will fail and will be automatically resubmitted uless you've changed the `maxRetries` option. Use this if you have jobs that may potentially end in infinite loops that you can't programatically end with your child code. Preferably run this with a `maxConcurrentCallsPerWorker` so you don't interrupt other calls when you have a timeout. This timeout operates on a per-call basis but will interrupt a whole worker.\n\n  * **<code>maxRetries</code>** allows you to control the max number of call requeues after worker termination (unexpected or timeout). By default this option is set to `Infinity` which means that each call of each terminated worker will always be auto requeued. When the number of retries exceeds `maxRetries` value, the job callback will be executed with a `ProcessTerminatedError`. Note that if you are running with finite `maxCallTime` and `maxConcurrentCallsPerWorkers` greater than `1` then any `TimeoutError` will increase the retries counter *for each* concurrent call of the terminated worker.\n\n  * **<code>autoStart</code>** when set to `true` will start the workers as early as possible. Use this when your workers have to do expensive initialization. That way they'll be ready when the first request comes through.\n\n  * **<code>onChild</code>** when new child process starts this callback will be called with subprocess object as an argument. Use this when you need to add some custom communication with child processes.\n\n### workerFarm.end(farm)\n\nChild processes stay alive waiting for jobs indefinitely and your farm manager will stay alive managing its workers, so if you need it to stop then you have to do so explicitly. If you send your farm API to `workerFarm.end()` then it'll cleanly end your worker processes. Note though that it's a *soft* ending so it'll wait for child processes to finish what they are working on before asking them to die.\n\nAny calls that are queued and not yet being handled by a child process will be discarded. `end()` only waits for those currently in progress.\n\nOnce you end a farm, it won't handle any more calls, so don't even try!\n\n## Related\n\n* [farm-cli](https://github.com/Kikobeats/farm-cli) – Launch a farm of workers from CLI.\n\n## License\n\nWorker Farm is Copyright (c) 2014 Rod Vagg [@rvagg](https://twitter.com/rvagg) and licensed under the MIT license. All rights not explicitly granted in the MIT license are reserved. See the included LICENSE.md file for more details.\n", "licenseText": "The MIT License (MIT)\n=====================\n\nCopyright (c) 2014 LevelUP contributors\n---------------------------------------\n\n*LevelUP contributors listed at <https://github.com/rvagg/node-levelup#contributors>*\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/worker-farm/-/worker-farm-1.7.0.tgz#26a94c5391bbca926152002f69b84a4bf772e5a8", "type": "tarball", "reference": "https://registry.yarnpkg.com/worker-farm/-/worker-farm-1.7.0.tgz", "hash": "26a94c5391bbca926152002f69b84a4bf772e5a8", "integrity": "sha512-rvw3QTZc8lAxyVrqcSGVm5yP/IJ2UcB3U0graE3LCFoZ0Yn2x4EoVSqJKdB/T5M+FLcRPjz4TDacRf3OCfNUzw==", "registry": "npm", "packageName": "worker-farm", "cacheIntegrity": "sha512-rvw3QTZc8lAxyVrqcSGVm5yP/IJ2UcB3U0graE3LCFoZ0Yn2x4EoVSqJKdB/T5M+FLcRPjz4TDacRf3OCfNUzw== sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag="}, "registry": "npm", "hash": "26a94c5391bbca926152002f69b84a4bf772e5a8"}