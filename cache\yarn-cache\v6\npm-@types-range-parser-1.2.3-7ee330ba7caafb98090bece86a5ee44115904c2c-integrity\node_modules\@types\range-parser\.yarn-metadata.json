{"manifest": {"name": "@types/range-parser", "version": "1.2.3", "description": "TypeScript definitions for range-parser", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/tlaziuk"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "71bf3049d2d484657017f768fe0c54c4e9f03ee340b5a62a56523455925a00ae", "typeScriptVersion": "2.0", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-range-parser-1.2.3-7ee330ba7caafb98090bece86a5ee44115904c2c-integrity\\node_modules\\@types\\range-parser\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/range-parser`\n\n# Summary\nThis package contains type definitions for range-parser (https://github.com/jshttp/range-parser).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/range-parser\n\nAdditional Details\n * Last updated: Fri, 07 Dec 2018 19:21:52 GMT\n * Dependencies: none\n * Global values: none\n\n# Credits\nThese definitions were written by <PERSON><PERSON> <https://github.com/tlazi<PERSON>>.\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation. All rights reserved.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/range-parser/-/range-parser-1.2.3.tgz#7ee330ba7caafb98090bece86a5ee44115904c2c", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/range-parser/-/range-parser-1.2.3.tgz", "hash": "7ee330ba7caafb98090bece86a5ee44115904c2c", "integrity": "sha512-ewFXqrQHlFsgc09MK5jP5iR7vumV/BYayNC6PgJO2LPe8vrnNFyjQjSppfEngITi0qvfKtzFvgKymGheFM9UOA==", "registry": "npm", "packageName": "@types/range-parser", "cacheIntegrity": "sha512-ewFXqrQHlFsgc09MK5jP5iR7vumV/BYayNC6PgJO2LPe8vrnNFyjQjSppfEngITi0qvfKtzFvgKymGheFM9UOA== sha1-fuMwunyq+5gJC+zoal7kQRWQTCw="}, "registry": "npm", "hash": "7ee330ba7caafb98090bece86a5ee44115904c2c"}