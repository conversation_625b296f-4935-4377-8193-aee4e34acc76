-- Animation System for Weather Health System

local QBCore = exports['qb-core']:GetCoreObject()

-- Animation tracking
local activeAnimations = {}
local animationQueue = {}
local isPlayingAnimation = false

-- Weather-based ambient animations
local weatherAnimations = {
    ['RAIN'] = {
        {dict = "amb@world_human_stand_impatient@male@no_var@base", anim = "base", chance = 15},
        {dict = "anim@mp_player_intcelebrationfemale@wipe_sweat", anim = "wipe_sweat", chance = 10}
    },
    ['SNOW'] = {
        {dict = "amb@world_human_hang_out_street@male_c@idle_a", anim = "idle_b", chance = 25},
        {dict = "anim@mp_player_intcelebrationfemale@sneeze", anim = "sneeze", chance = 20}
    },
    ['THUNDER'] = {
        {dict = "amb@world_human_stand_impatient@male@no_var@base", anim = "base", chance = 30}
    },
    ['EXTRASUNNY'] = {
        {dict = "anim@mp_player_intcelebrationfemale@wipe_sweat", anim = "wipe_sweat", chance = 20}
    }
}

-- Initialize animation system
CreateThread(function()
    while not QBCore do
        Wait(100)
    end
    
    StartWeatherAnimationLoop()
    StartAnimationProcessor()
end)

-- Weather-based animation loop
function StartWeatherAnimationLoop()
    CreateThread(function()
        while true do
            local currentWeather = exports['qb-weatherhealth']:getCurrentWeather()
            
            if currentWeather and weatherAnimations[currentWeather] then
                ProcessWeatherAnimations(currentWeather)
            end
            
            Wait(math.random(30000, 90000)) -- Random interval 30s-1.5min
        end
    end)
end

-- Animation queue processor
function StartAnimationProcessor()
    CreateThread(function()
        while true do
            if #animationQueue > 0 and not isPlayingAnimation then
                local nextAnim = table.remove(animationQueue, 1)
                PlayQueuedAnimation(nextAnim)
            end
            
            Wait(100)
        end
    end)
end

-- Process weather-based animations
function ProcessWeatherAnimations(weather)
    local ped = PlayerPedId()
    
    -- Don't play weather animations if player is busy
    if IsPedInAnyVehicle(ped, false) or 
       IsPedRagdoll(ped) or 
       IsPedFalling(ped) or
       IsPedSwimming(ped) or
       exports['qb-weatherhealth']:isPlayerSick() then
        return
    end
    
    -- Skip if player is in interior
    if Utils.IsPlayerInInterior() then
        return
    end
    
    local animations = weatherAnimations[weather]
    for _, animData in ipairs(animations) do
        if Utils.ShouldPlayAnimation(animData.chance) then
            QueueAnimation({
                type = 'weather',
                dict = animData.dict,
                anim = animData.anim,
                duration = animData.duration or 3000,
                flag = animData.flag or 0,
                weather = weather
            })
            break -- Only play one weather animation at a time
        end
    end
end

-- Queue animation for processing
function QueueAnimation(animData)
    table.insert(animationQueue, animData)
    Utils.Debug("Queued animation: %s/%s (type: %s)", animData.dict, animData.anim, animData.type)
end

-- Play queued animation
function PlayQueuedAnimation(animData)
    if isPlayingAnimation then return end
    
    local ped = PlayerPedId()
    
    -- Final check before playing
    if IsPedInAnyVehicle(ped, false) or IsPedRagdoll(ped) or IsPedFalling(ped) then
        return
    end
    
    isPlayingAnimation = true
    
    RequestAnimDict(animData.dict)
    while not HasAnimDictLoaded(animData.dict) do
        Wait(100)
    end
    
    -- Store current animation info
    activeAnimations[animData.type] = {
        dict = animData.dict,
        anim = animData.anim,
        startTime = GetGameTimer()
    }
    
    TaskPlayAnim(ped, animData.dict, animData.anim, 8.0, -8.0, 
                animData.duration or 3000, animData.flag or 0, 0, false, false, false)
    
    Utils.Debug("Playing animation: %s/%s", animData.dict, animData.anim)
    
    -- Wait for animation to finish
    Wait(animData.duration or 3000)
    
    RemoveAnimDict(animData.dict)
    activeAnimations[animData.type] = nil
    isPlayingAnimation = false
end

-- Play symptom animation (called from player.lua)
function PlaySymptomAnimation(symptom)
    local animConfig = Config.Animations.symptoms[symptom]
    if not animConfig then return end
    
    QueueAnimation({
        type = 'symptom',
        dict = animConfig.dict,
        anim = animConfig.anim,
        duration = animConfig.duration,
        flag = animConfig.flag or 0,
        symptom = symptom
    })
end

-- Play NPC animation (called from npc.lua)
function PlayNPCAnimation(ped, animationType)
    if not DoesEntityExist(ped) then return end
    
    local animConfig = Config.Animations.symptoms[animationType] or 
                      Config.Animations.weather[animationType]
    
    if not animConfig then return end
    
    RequestAnimDict(animConfig.dict)
    while not HasAnimDictLoaded(animConfig.dict) do
        Wait(100)
    end
    
    TaskPlayAnim(ped, animConfig.dict, animConfig.anim, 8.0, -8.0, 
                animConfig.duration or -1, animConfig.loop and 1 or 0, 0, false, false, false)
    
    RemoveAnimDict(animConfig.dict)
    
    Utils.Debug("NPC animation played: %s", animationType)
end

-- Specialized animation functions
function PlayCoughAnimation()
    QueueAnimation({
        type = 'symptom',
        dict = "timetable@gardener@smoking_joint",
        anim = "idle_cough",
        duration = 3000,
        flag = 0
    })
end

function PlaySneezeAnimation()
    QueueAnimation({
        type = 'symptom',
        dict = "anim@mp_player_intcelebrationfemale@sneeze",
        anim = "sneeze",
        duration = 2000,
        flag = 0
    })
end

function PlayShiverAnimation()
    QueueAnimation({
        type = 'symptom',
        dict = "amb@world_human_hang_out_street@male_c@idle_a",
        anim = "idle_b",
        duration = 4000,
        flag = 0 -- Don't loop for player
    })
end

function PlayWipeSweatAnimation()
    QueueAnimation({
        type = 'symptom',
        dict = "anim@mp_player_intcelebrationfemale@wipe_sweat",
        anim = "wipe_sweat",
        duration = 3000,
        flag = 0
    })
end

-- Weather-specific player reactions
function PlayWeatherReaction(weather, intensity)
    intensity = intensity or 1
    
    if weather == 'RAIN' or weather == 'THUNDER' then
        -- Player covers head or looks for shelter
        QueueAnimation({
            type = 'weather',
            dict = "amb@world_human_stand_impatient@male@no_var@base",
            anim = "base",
            duration = 4000 * intensity,
            flag = 0
        })
    elseif weather == 'SNOW' or weather == 'BLIZZARD' then
        -- Player shivers and rubs hands
        PlayShiverAnimation()
    elseif weather == 'EXTRASUNNY' then
        -- Player wipes sweat
        PlayWipeSweatAnimation()
    end
end

-- Stop all animations
function StopAllAnimations()
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    -- Clear animation queue
    animationQueue = {}
    activeAnimations = {}
    isPlayingAnimation = false
    
    Utils.Debug("All animations stopped")
end

-- Check if specific animation is playing
function IsAnimationPlaying(animType)
    return activeAnimations[animType] ~= nil
end

-- Get animation progress
function GetAnimationProgress(animType)
    local animData = activeAnimations[animType]
    if not animData then return 0 end
    
    local elapsed = GetGameTimer() - animData.startTime
    return math.min(1.0, elapsed / 3000) -- Assume 3 second default duration
end

-- Force play animation (bypasses queue)
function ForcePlayAnimation(dict, anim, duration, flag)
    local ped = PlayerPedId()
    
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        Wait(100)
    end
    
    ClearPedTasks(ped)
    TaskPlayAnim(ped, dict, anim, 8.0, -8.0, duration or 3000, flag or 0, 0, false, false, false)
    
    RemoveAnimDict(dict)
    
    Utils.Debug("Force played animation: %s/%s", dict, anim)
end

-- Animation event handlers
RegisterNetEvent('weatherhealth:client:playAnimation', function(animType, data)
    if animType == 'symptom' then
        PlaySymptomAnimation(data.symptom)
    elseif animType == 'weather' then
        PlayWeatherReaction(data.weather, data.intensity)
    elseif animType == 'custom' then
        QueueAnimation(data)
    end
end)

RegisterNetEvent('weatherhealth:client:stopAnimations', function()
    StopAllAnimations()
end)

RegisterNetEvent('weatherhealth:client:forceAnimation', function(dict, anim, duration, flag)
    ForcePlayAnimation(dict, anim, duration, flag)
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        StopAllAnimations()
    end
end)

-- Export functions
exports('playSymptomAnimation', PlaySymptomAnimation)
exports('playNPCAnimation', PlayNPCAnimation)
exports('playWeatherReaction', PlayWeatherReaction)
exports('queueAnimation', QueueAnimation)
exports('stopAllAnimations', StopAllAnimations)
exports('isAnimationPlaying', IsAnimationPlaying)
exports('forcePlayAnimation', ForcePlayAnimation)
