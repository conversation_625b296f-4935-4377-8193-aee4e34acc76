local Translations = {
    error = {
        canceled = 'Annulleret',
        bled_out = 'Du er blødt ud...',
        impossible = 'Handling umulig...',
        no_player = 'Ingen spiller i nærheden',
        no_firstaid = 'Du har brug for et førstehjælpssæt',
        no_bandage = 'Du skal bruge en bandage',
        beds_taken = 'Sengene er optaget...',
        possessions_taken = 'Alle dine ejendele er blevet taget...',
        not_enough_money = 'Du har ikke penge nok på dig...',
        cant_help = 'Du kan ikke hjælpe denne person...',
        not_ems = 'Du er ikke EMS eller ikke logget ind',
        not_online = 'Spiller ikke online'
    },
    success = {
        revived = 'Du genoplivede en person',
        healthy_player = 'Spilleren er sund',
        helped_player = 'Du hjalp personen',
        wounds_healed = 'Dine sår er blevet helet!',
        being_helped = 'Du bliver hjulpet...'
    },
    info = {
        civ_died = 'Borger død',
        civ_down = 'Borger faldet om',
        civ_call = 'Borger opkald',
        self_death = 'sig selv eller en NPC',
        wep_unknown = 'Ukendt',
        respawn_txt = 'GENOPLIVER OM: ~r~%{deathtime}~s~ SEKUNDER',
        respawn_revive = 'HOLD [~r~E~s~] I %{holdtime} SEKUNDER FOR AT RESPAWNE FOR ~r~%{cost}~s~ KR',
        bleed_out = 'DU BLØDER UD OM: ~r~%{time}~s~ SEKUNDER',
        bleed_out_help = 'DU BLØDER UD OM: ~r~%{time}~s~ SEKUNDER, DU KAN BLIVE HJULPET',
        request_help = 'TRYK [~r~G~s~] FOR AT ANMODE HJÆLP',
        help_requested = 'EMS-PERSONALE HAR FÅET BESKED',
        amb_plate = 'AMBU', -- Bør kun bestå af 4 tegn, da de sidste 4 er tilfældige 4 cifre
        heli_plate = 'LIFE', -- Bør kun bestå af 4 tegn, da de sidste 4 er tilfældige 4 cifre
        status = 'Status Tjek',
        is_status = 'Du %{status}',
        healthy = 'Du er rask igen!',
        safe = 'Hospital sikkerhed',
        pb_hospital = 'Pillbox Hospital',
        pain_message = '%{limb} %{severity}',
        many_places = 'Du har ondt mange steder...',
        bleed_alert = '%{bleedstate}',
        ems_alert = 'EMS alarm - %{text}',
        mr = 'Hr.',
        mrs = 'Fru',
        dr_needed = 'Der er brug for en læge på Pillbox Hospital',
        ems_report = 'Lægerapport',
        message_sent = 'Besked der skal sendes',
        check_health = 'Tjek en spillers helbred',
        heal_player = 'Helbred en spiller',
        revive_player = 'Genopliv en spiller',
        revive_player_a = 'Genopliv en spiller eller dig selv (kun admin)',
        player_id = 'Spiller id (kan være tomt)',
        pain_level = 'Indstil dit eller en spillers smerteniveau (kun admin)',
        kill = 'Dræb en spiller eller dig selv (kun admin)',
        heal_player_a = 'Heal en spiller eller dig selv (kun admin)',
    },
    mail = {
        sender = 'Pillbox Hospital',
        subject = 'Hospitalomkostninger',
        message = 'Kære %{gender} %{lastname}, <br /><br />Hermed har du modtaget en mail med omkostningerne ved det sidste hospitalsbesøg.<br />De endelige omkostninger er blevet: <strong>%{costs} kr.</strong><br /><br />Vi ønsker dig god bedring!'
    },
    states = {
        irritated = 'er irriteret',
        quite_painful = 'er i store smerter',
        painful = 'er i smerter',
        really_painful = 'er i meget store smerter',
        little_bleed = 'bløder en lille smule...',
        bleed = 'bløder...',
        lot_bleed = 'bløder meget...',
        big_bleed = 'har en stor blødning...',
    },
    menu = {
        amb_vehicles = 'Ambulancebiler',
        status = 'Sundhedsstatus',
        close = '⬅ Luk menu',
    },
    text = {
        pstash_button = '[E] - Personligt lager',
        pstash = 'Personligt lager',
        onduty_button = '[E] - Gå på vagt',
        offduty_button = '[E] - Gå af vagt',
        duty = 'På/Af Vagt',
        armory_button = '[E] - Lager',
        armory = 'Lager',
        veh_button = '[E] - Tag / Gem køretøj',
        heli_button = '[E] - Tag / Gem Helikopter',
        elevator_roof = '[E] - Tag elevatoren til taget',
        elevator_main = '[E] - Tag elevatoren ned',
        bed_out = '[E] - For at komme ud af sengen..',
        call_doc = '[E] - Ring til læge',
        call = 'Ring',
        check_in = '[E] Tjek ind',
        check = 'Tjek ind',
        lie_bed = '[E] - At ligge i sengen'
    },
    body = {
        head = 'Hoved',
        neck = 'Hals',
        spine = 'Rygrad',
        upper_body = 'Overkroppen',
        lower_body = 'Underkrop',
        left_arm = 'Venstre arm',
        left_hand = 'Venstre hånd',
        left_fingers = 'Venstre fingre',
        left_leg = 'Venstre ben',
        left_foot = 'Venstre fod',
        right_arm = 'Højre arm',
        right_hand = 'Højre hånd',
        right_fingers = 'Højre fingre',
        right_leg = 'Højre ben',
        right_foot = 'Højre fod',
    },
    progress = {
        ifaks = 'Tager ifak...',
        bandage = 'Bruger bandage...',
        painkillers = 'Tager smertestillende medicin...',
        revive = 'Genopliver person...',
        healing = 'Heler sår...',
        checking_in = 'Tjekker ind...',
    },
    logs = {
        death_log_title = "%{playername} (%{playerid}) er død",
        death_log_message = "%{killername} har dræbt %{playername} med **%{weaponlabel}** (%{weaponname})",
    }
}

if GetConvar('qb_locale', 'en') == 'da' then
    Lang = Locale:new({
        phrases = Translations,
        warnOnMissing = true,
        fallbackLang = Lang,
    })
end
