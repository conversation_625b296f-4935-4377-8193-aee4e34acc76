Config = {}

Config.PoliceOnDutyRequired = 0           -- amount of police required to be on duty to rob a house
Config.LimitTime = true                   -- Use in-game clock hours to set the time the houses can be robbed
Config.MinimumTime = 6                    -- only needed if LimitTime is true
Config.MaximumTime = 22                   -- only needed if LimitTime is true
Config.TimeToCloseDoors = 25              -- in minutes (only start counting after one person enters the house)
Config.RequireScrewdriver = true          -- if true, you need a screwdriver to rob a house when not using advanced lockpick
Config.ChanceToBreakLockPick = 30         -- chance in percent to break a normal lockpick if failed
Config.ChanceToBreakAdvancedLockPick = 15 -- chance in percent to break a advanced lockpick if failed
Config.ChanceToAlertPolice = 20           -- chance in percent to call the police if a house robbery is in progress
Config.MinZOffset = 45

Config.Rewards = {
    { -- house tier
        ['cabin'] = {
            { item = 'plastic',         min = 1, max = 3 },
            { item = 'diamond_ring',    min = 1, max = 1 },
            { item = 'goldchain',       min = 1, max = 1 },
            { item = 'weed_skunk',      min = 1, max = 3 },
            { item = 'thermite',        min = 1, max = 1 },
            { item = 'cryptostick',     min = 1, max = 1 },
            { item = 'weapon_golfclub', min = 1, max = 1 },
        },
        ['kitchen'] = {
            { item = 'tosti',     min = 1, max = 3 },
            { item = 'sandwich',  min = 1, max = 2 },
            { item = 'goldchain', min = 1, max = 1 }
        },
        ['chest'] = {
            { item = 'plastic',             min = 1, max = 4 },
            { item = 'rolex',               min = 1, max = 2 },
            { item = 'diamond_ring',        min = 1, max = 1 },
            { item = 'goldchain',           min = 1, max = 1 },
            { item = 'weed_skunk',          min = 1, max = 5 },
            { item = 'thermite',            min = 1, max = 1 },
            { item = 'cryptostick',         min = 1, max = 1 },
            { item = 'weapon_combatpistol', min = 1, max = 1 }
        },
        ['livingroom'] = {
            { item = 'plastic',      min = 1, max = 4 },
            { item = 'rolex',        min = 1, max = 1 },
            { item = 'diamond_ring', min = 1, max = 1 },
            { item = 'goldchain',    min = 1, max = 1 },
            { item = 'thermite',     min = 1, max = 1 },
            { item = 'cryptostick',  min = 1, max = 1 },
            { item = 'tablet',       min = 1, max = 1 },
            { item = 'pistol_ammo',  min = 1, max = 3 }
        }
    }
}

Config.Houses = {
    ['perfectdrive1'] = {
        ['coords'] = vector4(-784.72, 459.77, 100.39, 34.89),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['perfectdrive2'] = {
        ['coords'] = vector4(-762.21, 430.96, 100.2, 198.27),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['perfectdrive3'] = {
        ['coords'] = vector4(-678.01, 512.13, 113.53, 12.05),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['perfectdrive4'] = {
        ['coords'] = vector4(-640.92, 520.61, 109.88, 9.94),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['perfectdrive5'] = {
        ['coords'] = vector4(-622.84, 488.88, 108.88, 186.87),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['perfectdrive6'] = {
        ['coords'] = vector4(-595.55, 530.28, 107.75, 13.0),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['miltonroad1'] = {
        ['coords'] = vector4(-536.67, 477.36, 103.19, 235.55),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['miltonroad2'] = {
        ['coords'] = vector4(-526.64, 516.97, 112.94, 226.17),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['miltonroad3'] = {
        ['coords'] = vector4(-554.48, 541.26, 110.71, 338.6),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['miltonroad4'] = {
        ['coords'] = vector4(-500.82, 551.92, 120.6, 159.51),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['didiondrive1'] = {
        ['coords'] = vector4(-418.09, 568.99, 125.06, 333.59),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['didiondrive2'] = {
        ['coords'] = vector4(-411.06, 529.18, 122.18, 167.27),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['didiondrive3'] = {
        ['coords'] = vector4(-348.73, 514.95, 120.65, 316.96),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['didiondrive4'] = {
        ['coords'] = vector4(-355.93, 469.76, 112.65, 104.28),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['hillcrest1'] = {
        ['coords'] = vector4(-908.66, 693.67, 151.44, 104.28),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['hillcrest2'] = {
        ['coords'] = vector4(-951.28, 682.65, 153.58, 177.85),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['hillcrest3'] = {
        ['coords'] = vector4(-1056.34, 761.34, 167.32, 92.27),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['northsheldon1'] = {
        ['coords'] = vector4(-1130.87, 784.44, 163.89, 56.39),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['northsheldon2'] = {
        ['coords'] = vector4(-1117.77, 761.48, 164.29, 205.7),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['northsheldon3'] = {
        ['coords'] = vector4(-1065.02, 772.17, 170.06, 179.03),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['northsheldon4'] = {
        ['coords'] = vector4(-962.7, 814.09, 177.57, 0.44),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['northsheldon5'] = {
        ['coords'] = vector4(-867.34, 785.0, 191.93, 182.44),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['wildoats1'] = {
        ['coords'] = vector4(80.09, 486.26, 148.2, 25.28),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['wildoats2'] = {
        ['coords'] = vector4(57.56, 449.68, 147.03, 147.72),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['wildoats3'] = {
        ['coords'] = vector4(-110.05, 501.83, 143.38, 171.41),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['jamestown1'] = {
        ['coords'] = vector4(385.18, -1881.78, 26.03, 40.83),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['jamestown2'] = {
        ['coords'] = vector4(412.47, -1856.38, 27.32, 129.24),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['jamestown3'] = {
        ['coords'] = vector4(427.21, -1842.09, 28.46, 134.19),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['roylow1'] = {
        ['coords'] = vector4(348.77, -1820.95, 28.89, 135.8),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['roylow2'] = {
        ['coords'] = vector4(329.42, -1845.8, 27.75, 228.98),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['roylow3'] = {
        ['coords'] = vector4(320.27, -1854.06, 27.51, 225.09),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['covenant1'] = {
        ['coords'] = vector4(192.29, -1883.23, 25.06, 326.47),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['covenant2'] = {
        ['coords'] = vector4(171.52, -1871.53, 24.4, 245.19),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['covenant3'] = {
        ['coords'] = vector4(128.24, -1897.02, 23.67, 239.54),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['covenant4'] = {
        ['coords'] = vector4(130.6, -1853.22, 25.23, 329.64),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['grove1'] = {
        ['coords'] = vector4(76.36, -1948.1, 21.17, 44.97),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['grove2'] = {
        ['coords'] = vector4(101.03, -1912.16, 21.41, 331.84),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['grove3'] = {
        ['coords'] = vector4(126.73, -1930.12, 21.38, 210.98),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['grove4'] = {
        ['coords'] = vector4(114.25, -1961.19, 21.33, 198.18),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['prosperity1'] = {
        ['coords'] = vector4(-1076.33, -1026.96, 4.54, 118.14),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['prosperity2'] = {
        ['coords'] = vector4(-1064.64, -1057.42, 6.41, 115.35),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['prosperity3'] = {
        ['coords'] = vector4(-1063.77, -1054.99, 2.15, 120.47),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['prosperity4'] = {
        ['coords'] = vector4(-1054.07, -1000.2, 6.41, 296.69),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['prosperity5'] = {
        ['coords'] = vector4(-985.89, -1121.74, 4.55, 297.63),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['prosperity6'] = {
        ['coords'] = vector4(-1024.42, -1140.0, 2.75, 213.84),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['prosperity7'] = {
        ['coords'] = vector4(-1074.13, -1152.73, 2.16, 118.65),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['prosperity8'] = {
        ['coords'] = vector4(-1063.57, -1160.35, 2.75, 210.36),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['prosperity10'] = {
        ['coords'] = vector4(-869.53, -1103.41, 6.45, 29.2),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['prosperity11'] = {
        ['coords'] = vector4(-869.07, -1105.48, 2.49, 25.76),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['grapeseed1'] = {
        ['coords'] = vector4(1662.07, 4776.19, 42.07, 92.92),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['grapeseed2'] = {
        ['coords'] = vector4(1664.04, 4739.68, 42.01, 111.19),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['paleto1'] = {
        ['coords'] = vector4(-360.24, 6260.61, 31.9, 136.27),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['paleto2'] = {
        ['coords'] = vector4(-366.57, 6214.12, 31.84, 317.03),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['paleto3'] = {
        ['coords'] = vector4(-447.87, 6260.25, 30.05, 338.35),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['paleto4'] = {
        ['coords'] = vector4(-245.99, 6414.45, 31.46, 305.54),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['paleto5'] = {
        ['coords'] = vector4(-9.59, 6654.23, 31.7, 31.66),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['paleto6'] = {
        ['coords'] = vector4(25.1, 6601.76, 32.47, 133.36),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['paletoHotels1'] = {
        ['coords'] = vector4(-159.84, 6432.65, 31.93, 310.91),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
    ['paletoHotels2'] = {
        ['coords'] = vector4(-149.98, 6416.07, 31.92, 225.27),
        ['opened'] = false,
        ['tier'] = 1,
        ['furniture'] = {
            { ['type'] = 'cabin',      ['coords'] = vector3(4.15, 7.82, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_bcabinet') },
            { ['type'] = 'cabin',      ['coords'] = vector3(5.95, 9.34, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_closet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-1.03, 0.78, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'chest',      ['coords'] = vector3(6.904, 3.987, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(0.933, 1.254, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_chest') },
            { ['type'] = 'cabin',      ['coords'] = vector3(6.19, 3.35, 1.0),   ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_cabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-2.20, -0.30, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_kcabinet') },
            { ['type'] = 'kitchen',    ['coords'] = vector3(-4.35, -0.64, 1.0), ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.90, 4.42, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
            { ['type'] = 'livingroom', ['coords'] = vector3(-6.98, 7.91, 1.0),  ['searched'] = false, ['isBusy'] = false, ['text'] = Lang:t('searching.search_shelves') },
        }
    },
}
