{"manifest": {"name": "@types/mime", "version": "2.0.3", "description": "TypeScript definitions for mime", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/jedigo"}, {"name": "<PERSON>", "url": "https://github.com/dhritzkiv"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mime"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "75aed3d4a994aa68fd6cea1ee8943bc2163a4ecb83f5168cd4f956a488fce32f", "typeScriptVersion": "3.0", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-mime-2.0.3-c893b73721db73699943bfc3653b1deb7faa4a3a-integrity\\node_modules\\@types\\mime\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/mime`\n\n# Summary\nThis package contains type definitions for mime (https://github.com/broofa/node-mime).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mime.\n\n### Additional Details\n * Last updated: Wed, 22 Jul 2020 18:12:53 GMT\n * Dependencies: none\n * Global values: `mime`, `mimelite`\n\n# Credits\nThese definitions were written by [<PERSON>](https://github.com/jedigo), and [<PERSON>](https://github.com/dhritzkiv).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/mime/-/mime-2.0.3.tgz#c893b73721db73699943bfc3653b1deb7faa4a3a", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/mime/-/mime-2.0.3.tgz", "hash": "c893b73721db73699943bfc3653b1deb7faa4a3a", "integrity": "sha512-<PERSON>s9s4CDbqwocc5pOAnh8ShfrnMcPHuJYzVcSUU7lrh8Ni5HuIqX3oilL86p3dlTrk0LzHRCgA/GQ7uNCw6l2Q==", "registry": "npm", "packageName": "@types/mime", "cacheIntegrity": "sha512-<PERSON>s9s4CDbqwocc5pOAnh8ShfrnMcPHuJYzVcSUU7lrh8Ni5HuIqX3oilL86p3dlTrk0LzHRCgA/GQ7uNCw6l2Q== sha1-yJO3NyHbc2mZQ7/DZTsd63+qSjo="}, "registry": "npm", "hash": "c893b73721db73699943bfc3653b1deb7faa4a3a"}