{"name": "is-date-object", "version": "1.0.2", "author": "<PERSON>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "Is this value a JS Date object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "node --harmony --es-staging test", "posttest": "npx aud", "coverage": "covert test/index.js", "lint": "eslint .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/ljharb/is-date-object.git"}, "keywords": ["Date", "ES6", "toStringTag", "@@toStringTag", "Date object"], "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^15.0.2", "auto-changelog": "^1.16.2", "covert": "^1.1.1", "eslint": "^6.7.2", "foreach": "^2.0.5", "indexof": "^0.0.1", "is": "^3.3.0", "safe-publish-latest": "^1.1.4", "tape": "^4.12.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}}