{"manifest": {"name": "tty-browserify", "version": "0.0.0", "description": "the tty module from node core for browsers", "main": "index.js", "dependencies": {}, "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tape test/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/tty-browserify.git"}, "homepage": "https://github.com/substack/tty-browserify", "keywords": ["tty", "browser", "browserify"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-tty-browserify-0.0.0-a157ba402da24e9bf957f9aa69d524eed42901a6-integrity\\node_modules\\tty-browserify\\package.json", "readmeFilename": "readme.markdown", "readme": "# tty-browserify\n", "licenseText": "This software is released under the MIT license:\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\n<PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/tty-browserify/-/tty-browserify-0.0.0.tgz#a157ba402da24e9bf957f9aa69d524eed42901a6", "type": "tarball", "reference": "https://registry.yarnpkg.com/tty-browserify/-/tty-browserify-0.0.0.tgz", "hash": "a157ba402da24e9bf957f9aa69d524eed42901a6", "integrity": "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=", "registry": "npm", "packageName": "tty-browserify", "cacheIntegrity": "sha512-JVa5ijo+j/sOoHGjw0sxw734b1LhBkQ3bvUGNdxnVXDCX81Yx7TFgnZygxrIIWn23hbfTaMYLwRmAxFyDuFmIw== sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY="}, "registry": "npm", "hash": "a157ba402da24e9bf957f9aa69d524eed42901a6"}