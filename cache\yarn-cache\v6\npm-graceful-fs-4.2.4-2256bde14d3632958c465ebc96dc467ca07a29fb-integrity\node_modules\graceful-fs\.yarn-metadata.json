{"manifest": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.2.4", "repository": {"type": "git", "url": "https://github.com/isaacs/node-graceful-fs"}, "main": "graceful-fs.js", "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^12.7.0"}, "files": ["fs.js", "graceful-fs.js", "legacy-streams.js", "polyfills.js", "clone.js"], "dependencies": {}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-graceful-fs-4.2.4-2256bde14d3632958c465ebc96dc467ca07a29fb-integrity\\node_modules\\graceful-fs\\package.json", "readmeFilename": "README.md", "readme": "# graceful-fs\n\ngraceful-fs functions as a drop-in replacement for the fs module,\nmaking various improvements.\n\nThe improvements are meant to normalize behavior across different\nplatforms and environments, and to make filesystem access more\nresilient to errors.\n\n## Improvements over [fs module](https://nodejs.org/api/fs.html)\n\n* Queues up `open` and `readdir` calls, and retries them once\n  something closes if there is an EMFILE error from too many file\n  descriptors.\n* fixes `lchmod` for Node versions prior to 0.6.2.\n* implements `fs.lutimes` if possible. Otherwise it becomes a noop.\n* ignores `EINVAL` and `EPERM` errors in `chown`, `fchown` or\n  `lchown` if the user isn't root.\n* makes `lchmod` and `lchown` become noops, if not available.\n* retries reading a file if `read` results in EAGAIN error.\n\nOn Windows, it retries renaming a file for up to one second if `EACCESS`\nor `EPERM` error occurs, likely because antivirus software has locked\nthe directory.\n\n## USAGE\n\n```javascript\n// use just like fs\nvar fs = require('graceful-fs')\n\n// now go and do stuff with it...\nfs.readFileSync('some-file-or-whatever')\n```\n\n## Global Patching\n\nIf you want to patch the global fs module (or any other fs-like\nmodule) you can do this:\n\n```javascript\n// Make sure to read the caveat below.\nvar realFs = require('fs')\nvar gracefulFs = require('graceful-fs')\ngracefulFs.gracefulify(realFs)\n```\n\nThis should only ever be done at the top-level application layer, in\norder to delay on EMFILE errors from any fs-using dependencies.  You\nshould **not** do this in a library, because it can cause unexpected\ndelays in other parts of the program.\n\n## Changes\n\nThis module is fairly stable at this point, and used by a lot of\nthings.  That being said, because it implements a subtle behavior\nchange in a core part of the node API, even modest changes can be\nextremely breaking, and the versioning is thus biased towards\nbumping the major when in doubt.\n\nThe main change between major versions has been switching between\nproviding a fully-patched `fs` module vs monkey-patching the node core\nbuiltin, and the approach by which a non-monkey-patched `fs` was\ncreated.\n\nThe goal is to trade `EMFILE` errors for slower fs operations.  So, if\nyou try to open a zillion files, rather than crashing, `open`\noperations will be queued up and wait for something else to `close`.\n\nThere are advantages to each approach.  Monkey-patching the fs means\nthat no `EMFILE` errors can possibly occur anywhere in your\napplication, because everything is using the same core `fs` module,\nwhich is patched.  However, it can also obviously cause undesirable\nside-effects, especially if the module is loaded multiple times.\n\nImplementing a separate-but-identical patched `fs` module is more\nsurgical (and doesn't run the risk of patching multiple times), but\nalso imposes the challenge of keeping in sync with the core module.\n\nThe current approach loads the `fs` module, and then creates a\nlookalike object that has all the same methods, except a few that are\npatched.  It is safe to use in all versions of Node from 0.8 through\n7.0.\n\n### v4\n\n* Do not monkey-patch the fs module.  This module may now be used as a\n  drop-in dep, and users can opt into monkey-patching the fs builtin\n  if their app requires it.\n\n### v3\n\n* Monkey-patch fs, because the eval approach no longer works on recent\n  node.\n* fixed possible type-error throw if rename fails on windows\n* verify that we *never* get EMFILE errors\n* Ignore ENOSYS from chmod/chown\n* clarify that graceful-fs must be used as a drop-in\n\n### v2.1.0\n\n* Use eval rather than monkey-patching fs.\n* readdir: Always sort the results\n* win32: requeue a file if error has an OK status\n\n### v2.0\n\n* A return to monkey patching\n* wrap process.cwd\n\n### v1.1\n\n* wrap readFile\n* Wrap fs.writeFile.\n* readdir protection\n* Don't clobber the fs builtin\n* Handle fs.read EAGAIN errors by trying again\n* Expose the curOpen counter\n* No-op lchown/lchmod if not implemented\n* fs.rename patch only for win32\n* Patch fs.rename to handle AV software on Windows\n* Close #4 Chown should not fail on einval or eperm if non-root\n* Fix isaacs/fstream#1 Only wrap fs one time\n* Fix #3 Start at 1024 max files, then back off on EMFILE\n* lutimes that doens't blow up on Linux\n* A full on-rewrite using a queue instead of just swallowing the EMFILE error\n* Wrap Read/Write streams as well\n\n### 1.0\n\n* Update engines for node 0.6\n* Be lstat-graceful on Windows\n* first\n", "licenseText": "The ISC License\n\nCopyright (c) <PERSON>, <PERSON>, and Contributors\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, <PERSON><PERSON>A OR PROFITS, WHETHER IN AN\nACTION OF CONTRACT, NEG<PERSON>IGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/graceful-fs/-/graceful-fs-4.2.4.tgz#2256bde14d3632958c465ebc96dc467ca07a29fb", "type": "tarball", "reference": "https://registry.yarnpkg.com/graceful-fs/-/graceful-fs-4.2.4.tgz", "hash": "2256bde14d3632958c465ebc96dc467ca07a29fb", "integrity": "sha512-WjKPNJF79dtJAVniUlGGWHYGz2jWxT6VhN/4m1NdkbZ2nOsEF+cI1Edgql5zCRhs/VsQYRvrXctxktVXZUkixw==", "registry": "npm", "packageName": "graceful-fs", "cacheIntegrity": "sha512-WjKPNJF79dtJAVniUlGGWHYGz2jWxT6VhN/4m1NdkbZ2nOsEF+cI1Edgql5zCRhs/VsQYRvrXctxktVXZUkixw== sha1-Ila94U02MpWMRl68ltxGfKB6Kfs="}, "registry": "npm", "hash": "2256bde14d3632958c465ebc96dc467ca07a29fb"}