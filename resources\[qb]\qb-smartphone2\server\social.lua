local QBCore = exports['qb-core']:GetCoreObject()

-- Globální proměnné pro sociální síť
local PostCooldowns = {}

-- Callback pro získání postů
QBCore.Functions.CreateCallback('qb-smartphone2:server:getSocialPosts', function(source, cb, page)
    page = page or 1
    local limit = 20
    local offset = (page - 1) * limit
    
    MySQL.Async.fetchAll([[
        SELECT p.*, 
               (SELECT COUNT(*) FROM phone_social_likes WHERE post_id = p.id) as likes_count,
               (SELECT COUNT(*) FROM phone_social_comments WHERE post_id = p.id) as comments_count,
               (SELECT COUNT(*) FROM phone_social_likes WHERE post_id = p.id AND citizenid = ?) as user_liked
        FROM phone_social_posts p 
        ORDER BY p.created_at DESC 
        LIMIT ? OFFSET ?
    ]], {QBCore.Functions.GetPlayer(source).PlayerData.citizenid, limit, offset}, function(result)
        cb(result or {})
    end)
end)

-- Event pro vytvoření postu
RegisterNetEvent('qb-smartphone2:server:createSocialPost', function(postData)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Kontrola cooldown
    if PostCooldowns[citizenid] and os.time() - PostCooldowns[citizenid] < Config.PostCooldown then
        local remaining = Config.PostCooldown - (os.time() - PostCooldowns[citizenid])
        TriggerClientEvent('QBCore:Notify', src, 'Musíš počkat ' .. remaining .. ' sekund před dalším postem!', 'error')
        return
    end
    
    -- Kontrola délky obsahu
    if string.len(postData.content) > Config.MaxPostLength then
        TriggerClientEvent('QBCore:Notify', src, 'Post je příliš dlouhý!', 'error')
        return
    end
    
    local authorName = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname
    
    -- Uložení postu
    MySQL.Async.execute('INSERT INTO phone_social_posts (citizenid, author_name, content, image) VALUES (?, ?, ?, ?)', {
        citizenid,
        authorName,
        postData.content,
        postData.image or nil
    }, function(insertId)
        if insertId then
            PostCooldowns[citizenid] = os.time()
            
            TriggerClientEvent('QBCore:Notify', src, 'Post byl zveřejněn!', 'success')
            
            -- Odeslání nového postu všem online hráčům
            local newPost = {
                id = insertId,
                citizenid = citizenid,
                author_name = authorName,
                content = postData.content,
                image = postData.image,
                likes_count = 0,
                comments_count = 0,
                user_liked = 0,
                created_at = os.date('%Y-%m-%d %H:%M:%S')
            }
            
            local players = QBCore.Functions.GetQBPlayers()
            for _, player in pairs(players) do
                TriggerClientEvent('qb-smartphone2:client:newSocialPost', player.PlayerData.source, newPost)
            end
        else
            TriggerClientEvent('QBCore:Notify', src, 'Chyba při zveřejňování postu!', 'error')
        end
    end)
end)

-- Event pro lajkování postu
RegisterNetEvent('qb-smartphone2:server:likeSocialPost', function(postId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Kontrola, zda už uživatel lajkoval
    MySQL.Async.fetchAll('SELECT * FROM phone_social_likes WHERE post_id = ? AND citizenid = ?', {postId, citizenid}, function(result)
        if result[1] then
            -- Odebrat lajk
            MySQL.Async.execute('DELETE FROM phone_social_likes WHERE post_id = ? AND citizenid = ?', {postId, citizenid}, function(affectedRows)
                if affectedRows > 0 then
                    -- Aktualizovat počet lajků v postu
                    MySQL.Async.execute('UPDATE phone_social_posts SET likes = likes - 1 WHERE id = ?', {postId})
                    
                    TriggerClientEvent('qb-smartphone2:client:postLikeUpdated', src, postId, false)
                    
                    -- Oznámit všem online hráčům
                    local players = QBCore.Functions.GetQBPlayers()
                    for _, player in pairs(players) do
                        TriggerClientEvent('qb-smartphone2:client:postLikeCountUpdated', player.PlayerData.source, postId, -1)
                    end
                end
            end)
        else
            -- Přidat lajk
            MySQL.Async.execute('INSERT INTO phone_social_likes (post_id, citizenid) VALUES (?, ?)', {postId, citizenid}, function(insertId)
                if insertId then
                    -- Aktualizovat počet lajků v postu
                    MySQL.Async.execute('UPDATE phone_social_posts SET likes = likes + 1 WHERE id = ?', {postId})
                    
                    TriggerClientEvent('qb-smartphone2:client:postLikeUpdated', src, postId, true)
                    
                    -- Oznámit všem online hráčům
                    local players = QBCore.Functions.GetQBPlayers()
                    for _, player in pairs(players) do
                        TriggerClientEvent('qb-smartphone2:client:postLikeCountUpdated', player.PlayerData.source, postId, 1)
                    end
                    
                    -- Odeslat notifikaci autorovi postu
                    MySQL.Async.fetchAll('SELECT citizenid, author_name FROM phone_social_posts WHERE id = ?', {postId}, function(postResult)
                        if postResult[1] and postResult[1].citizenid ~= citizenid then
                            local AuthorPlayer = QBCore.Functions.GetPlayerByCitizenId(postResult[1].citizenid)
                            if AuthorPlayer then
                                local likerName = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname
                                exports['qb-smartphone2']:SendNotification(AuthorPlayer.PlayerData.charinfo.phone, 'Nový lajk', likerName .. ' lajkoval tvůj post', 'social', {
                                    postId = postId
                                })
                            end
                        end
                    end)
                end
            end)
        end
    end)
end)

-- Callback pro získání komentářů k postu
QBCore.Functions.CreateCallback('qb-smartphone2:server:getPostComments', function(source, cb, postId)
    MySQL.Async.fetchAll('SELECT * FROM phone_social_comments WHERE post_id = ? ORDER BY created_at ASC', {postId}, function(result)
        cb(result or {})
    end)
end)

-- Event pro přidání komentáře
RegisterNetEvent('qb-smartphone2:server:addComment', function(postId, content)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Kontrola délky komentáře
    if string.len(content) > Config.MaxCommentLength then
        TriggerClientEvent('QBCore:Notify', src, 'Komentář je příliš dlouhý!', 'error')
        return
    end
    
    local authorName = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname
    
    -- Uložení komentáře
    MySQL.Async.execute('INSERT INTO phone_social_comments (post_id, citizenid, author_name, content) VALUES (?, ?, ?, ?)', {
        postId, citizenid, authorName, content
    }, function(insertId)
        if insertId then
            -- Aktualizovat počet komentářů
            MySQL.Async.execute('UPDATE phone_social_posts SET comments_count = comments_count + 1 WHERE id = ?', {postId})
            
            TriggerClientEvent('QBCore:Notify', src, 'Komentář byl přidán!', 'success')
            
            local newComment = {
                id = insertId,
                post_id = postId,
                citizenid = citizenid,
                author_name = authorName,
                content = content,
                created_at = os.date('%Y-%m-%d %H:%M:%S')
            }
            
            -- Oznámit všem online hráčům
            local players = QBCore.Functions.GetQBPlayers()
            for _, player in pairs(players) do
                TriggerClientEvent('qb-smartphone2:client:newComment', player.PlayerData.source, newComment)
            end
            
            -- Odeslat notifikaci autorovi postu
            MySQL.Async.fetchAll('SELECT citizenid FROM phone_social_posts WHERE id = ?', {postId}, function(postResult)
                if postResult[1] and postResult[1].citizenid ~= citizenid then
                    local AuthorPlayer = QBCore.Functions.GetPlayerByCitizenId(postResult[1].citizenid)
                    if AuthorPlayer then
                        exports['qb-smartphone2']:SendNotification(AuthorPlayer.PlayerData.charinfo.phone, 'Nový komentář', authorName .. ' okomentoval tvůj post', 'social', {
                            postId = postId
                        })
                    end
                end
            end)
        else
            TriggerClientEvent('QBCore:Notify', src, 'Chyba při přidávání komentáře!', 'error')
        end
    end)
end)

-- Event pro smazání postu
RegisterNetEvent('qb-smartphone2:server:deletePost', function(postId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Kontrola vlastnictví postu nebo admin práva
    MySQL.Async.fetchAll('SELECT citizenid FROM phone_social_posts WHERE id = ?', {postId}, function(result)
        if not result[1] then
            TriggerClientEvent('QBCore:Notify', src, 'Post nenalezen!', 'error')
            return
        end
        
        local isOwner = result[1].citizenid == citizenid
        local isAdmin = QBCore.Functions.HasPermission(src, 'admin') -- Nebo jiná admin kontrola
        
        if not isOwner and not isAdmin then
            TriggerClientEvent('QBCore:Notify', src, 'Nemáš oprávnění smazat tento post!', 'error')
            return
        end
        
        -- Smazání postu a souvisejících dat
        MySQL.Async.execute('DELETE FROM phone_social_posts WHERE id = ?', {postId}, function(affectedRows)
            if affectedRows > 0 then
                -- Smazání lajků a komentářů
                MySQL.Async.execute('DELETE FROM phone_social_likes WHERE post_id = ?', {postId})
                MySQL.Async.execute('DELETE FROM phone_social_comments WHERE post_id = ?', {postId})
                
                TriggerClientEvent('QBCore:Notify', src, 'Post byl smazán!', 'success')
                
                -- Oznámit všem online hráčům
                local players = QBCore.Functions.GetQBPlayers()
                for _, player in pairs(players) do
                    TriggerClientEvent('qb-smartphone2:client:postDeleted', player.PlayerData.source, postId)
                end
            else
                TriggerClientEvent('QBCore:Notify', src, 'Chyba při mazání postu!', 'error')
            end
        end)
    end)
end)

-- Event pro sledování uživatele
RegisterNetEvent('qb-smartphone2:server:followUser', function(targetCitizenid)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    if citizenid == targetCitizenid then
        TriggerClientEvent('QBCore:Notify', src, 'Nemůžeš sledovat sám sebe!', 'error')
        return
    end
    
    -- Kontrola, zda už sleduje
    MySQL.Async.fetchAll('SELECT * FROM phone_social_follows WHERE follower_id = ? AND following_id = ?', {citizenid, targetCitizenid}, function(result)
        if result[1] then
            -- Přestat sledovat
            MySQL.Async.execute('DELETE FROM phone_social_follows WHERE follower_id = ? AND following_id = ?', {citizenid, targetCitizenid}, function(affectedRows)
                if affectedRows > 0 then
                    TriggerClientEvent('QBCore:Notify', src, 'Přestal jsi sledovat tohoto uživatele', 'info')
                    TriggerClientEvent('qb-smartphone2:client:followStatusUpdated', src, targetCitizenid, false)
                end
            end)
        else
            -- Začít sledovat
            MySQL.Async.execute('INSERT INTO phone_social_follows (follower_id, following_id) VALUES (?, ?)', {citizenid, targetCitizenid}, function(insertId)
                if insertId then
                    TriggerClientEvent('QBCore:Notify', src, 'Začal jsi sledovat tohoto uživatele', 'success')
                    TriggerClientEvent('qb-smartphone2:client:followStatusUpdated', src, targetCitizenid, true)
                    
                    -- Odeslat notifikaci sledovanému uživateli
                    local TargetPlayer = QBCore.Functions.GetPlayerByCitizenId(targetCitizenid)
                    if TargetPlayer then
                        local followerName = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname
                        exports['qb-smartphone2']:SendNotification(TargetPlayer.PlayerData.charinfo.phone, 'Nový sledovatel', followerName .. ' tě začal sledovat', 'social')
                    end
                end
            end)
        end
    end)
end)

-- Callback pro získání uživatelského profilu
QBCore.Functions.CreateCallback('qb-smartphone2:server:getUserProfile', function(source, cb, targetCitizenid)
    MySQL.Async.fetchAll([[
        SELECT 
            (SELECT COUNT(*) FROM phone_social_posts WHERE citizenid = ?) as posts_count,
            (SELECT COUNT(*) FROM phone_social_follows WHERE following_id = ?) as followers_count,
            (SELECT COUNT(*) FROM phone_social_follows WHERE follower_id = ?) as following_count,
            (SELECT COUNT(*) FROM phone_social_follows WHERE follower_id = ? AND following_id = ?) as is_following
    ]], {targetCitizenid, targetCitizenid, targetCitizenid, QBCore.Functions.GetPlayer(source).PlayerData.citizenid, targetCitizenid}, function(result)
        cb(result[1] or {})
    end)
end)

-- Export funkce pro jiné scripty
exports('CreateSocialPost', function(citizenid, content, image)
    local p = promise.new()
    
    MySQL.Async.execute('INSERT INTO phone_social_posts (citizenid, author_name, content, image) VALUES (?, ?, ?, ?)', {
        citizenid, 'System', content, image
    }, function(insertId)
        p:resolve(insertId ~= nil)
    end)
    
    return Citizen.Await(p)
end)

exports('DeleteSocialPost', function(postId)
    local p = promise.new()
    
    MySQL.Async.execute('DELETE FROM phone_social_posts WHERE id = ?', {postId}, function(affectedRows)
        if affectedRows > 0 then
            MySQL.Async.execute('DELETE FROM phone_social_likes WHERE post_id = ?', {postId})
            MySQL.Async.execute('DELETE FROM phone_social_comments WHERE post_id = ?', {postId})
        end
        p:resolve(affectedRows > 0)
    end)
    
    return Citizen.Await(p)
end)
