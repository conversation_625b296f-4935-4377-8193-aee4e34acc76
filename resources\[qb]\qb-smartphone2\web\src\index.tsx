import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import { ThemeProvider } from './contexts/ThemeContext';
import { PhoneProvider } from './contexts/PhoneContext';
import { NotificationProvider } from './contexts/NotificationContext';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <ThemeProvider>
      <PhoneProvider>
        <NotificationProvider>
          <App />
        </NotificationProvider>
      </PhoneProvider>
    </ThemeProvider>
  </React.StrictMode>
);
