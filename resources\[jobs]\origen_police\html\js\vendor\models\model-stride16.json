{"format": "graph-model", "generatedBy": "1.14.0", "convertedBy": "TensorFlow.js Converter v1.2.6", "modelTopology": {"node": [{"name": "sub_2", "op": "Placeholder", "attr": {"shape": {"shape": {"dim": [{"size": "1"}, {"size": "-1"}, {"size": "-1"}, {"size": "3"}]}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/offset_2/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "34"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/offset_2/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "34"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_13_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_13_pointwise/weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "384"}]}}}}}, {"name": "MobilenetV1/Conv2d_13_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_13_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "384"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_12_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_12_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_12_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_12_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "384"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_11_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_11_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_11_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_11_depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "384"}, {"size": "1"}]}}}}}, {"name": "MobilenetV1/Conv2d_10_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_10_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_10_depthwise/depthwise_bias", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}}}, {"name": "MobilenetV1/Conv2d_10_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "384"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_9_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_9_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_9_depthwise/depthwise_bias", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}}}, {"name": "MobilenetV1/Conv2d_9_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "384"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_8_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_8_pointwise/weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "384"}]}}}}}, {"name": "MobilenetV1/Conv2d_8_depthwise/depthwise_bias", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}}}, {"name": "MobilenetV1/Conv2d_8_depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "384"}, {"size": "1"}]}}}}}, {"name": "MobilenetV1/Conv2d_7_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_7_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_7_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_7_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "384"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_6_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_6_pointwise/weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "192"}, {"size": "384"}]}}}}}, {"name": "MobilenetV1/Conv2d_6_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_6_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "192"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_5_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_5_pointwise/weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "192"}, {"size": "192"}]}}}}}, {"name": "MobilenetV1/Conv2d_5_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_5_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "192"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_4_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_4_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_4_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_4_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "96"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_3_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_3_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_3_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_3_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "96"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_2_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_2_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "48"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_2_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_2_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "48"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_1_pointwise/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_1_pointwise/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_1_depthwise/depthwise_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_1_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_0/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_0/weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "3"}, {"size": "24"}]}}}}}, {"name": "MobilenetV1/segment_2/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/segment_2/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "clip_by_value/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "clip_by_value/Minimum/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/part_offset_2/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/part_offset_2/weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "48"}]}}}}}, {"name": "MobilenetV1/part_heatmap_2/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/part_heatmap_2/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/embedding_2/Conv2D_bias", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "34"}]}}}}}, {"name": "MobilenetV1/embedding_2/weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "34"}]}}}}}, {"name": "MobilenetV1/heatmap_2/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "17"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/heatmap_2/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "17"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/displacement_fwd_2/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/displacement_fwd_2/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/displacement_bwd_2/Conv2D_bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/displacement_bwd_2/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_0/BatchNorm/batchnorm_1/add_1/conv", "op": "Conv2D", "input": ["sub_2", "MobilenetV1/Conv2d_0/weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "MobilenetV1/Conv2d_0/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_0/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_0/Conv2D_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_0/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_0/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_1_depthwise/BatchNorm/batchnorm_1/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/Conv2d_0/Relu6", "MobilenetV1/Conv2d_1_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "MobilenetV1/Conv2d_1_depthwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_1_depthwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_1_depthwise/depthwise_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_1_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_1_depthwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_1_pointwise/BatchNorm/batchnorm_1/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_1_depthwise/Relu6", "MobilenetV1/Conv2d_1_pointwise/weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "MobilenetV1/Conv2d_1_pointwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_1_pointwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_1_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_1_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_1_pointwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_2_depthwise/BatchNorm/batchnorm_1/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/Conv2d_1_pointwise/Relu6", "MobilenetV1/Conv2d_2_depthwise/depthwise_weights"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_2_depthwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_2_depthwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_2_depthwise/depthwise_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_2_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_2_depthwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_2_pointwise/BatchNorm/batchnorm_1/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_2_depthwise/Relu6", "MobilenetV1/Conv2d_2_pointwise/weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}}}, {"name": "MobilenetV1/Conv2d_2_pointwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_2_pointwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_2_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_2_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_2_pointwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_3_depthwise/BatchNorm/batchnorm_1/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/Conv2d_2_pointwise/Relu6", "MobilenetV1/Conv2d_3_depthwise/depthwise_weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "MobilenetV1/Conv2d_3_depthwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_3_depthwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_3_depthwise/depthwise_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_3_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_3_depthwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_3_pointwise/BatchNorm/batchnorm_1/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_3_depthwise/Relu6", "MobilenetV1/Conv2d_3_pointwise/weights"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "MobilenetV1/Conv2d_3_pointwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_3_pointwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_3_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_3_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_3_pointwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_4_depthwise/BatchNorm/batchnorm_1/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/Conv2d_3_pointwise/Relu6", "MobilenetV1/Conv2d_4_depthwise/depthwise_weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}}}, {"name": "MobilenetV1/Conv2d_4_depthwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_4_depthwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_4_depthwise/depthwise_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_4_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_4_depthwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_4_pointwise/BatchNorm/batchnorm_1/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_4_depthwise/Relu6", "MobilenetV1/Conv2d_4_pointwise/weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "MobilenetV1/Conv2d_4_pointwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_4_pointwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_4_pointwise/Conv2D_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_4_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_4_pointwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_5_depthwise/BatchNorm/batchnorm_1/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/Conv2d_4_pointwise/Relu6", "MobilenetV1/Conv2d_5_depthwise/depthwise_weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_5_depthwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_5_depthwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_5_depthwise/depthwise_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_5_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_5_depthwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_5_pointwise/BatchNorm/batchnorm_1/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_5_depthwise/Relu6", "MobilenetV1/Conv2d_5_pointwise/weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "MobilenetV1/Conv2d_5_pointwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_5_pointwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_5_pointwise/Conv2D_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_5_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_5_pointwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_6_depthwise/BatchNorm/batchnorm_1/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/Conv2d_5_pointwise/Relu6", "MobilenetV1/Conv2d_6_depthwise/depthwise_weights"], "attr": {"strides": {"list": {"i": ["1", "2", "2", "1"]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_6_depthwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_6_depthwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_6_depthwise/depthwise_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_6_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_6_depthwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_6_pointwise/BatchNorm/batchnorm_1/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_6_depthwise/Relu6", "MobilenetV1/Conv2d_6_pointwise/weights"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "MobilenetV1/Conv2d_6_pointwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_6_pointwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_6_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_6_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_6_pointwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_7_depthwise/BatchNorm/batchnorm_1/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/Conv2d_6_pointwise/Relu6", "MobilenetV1/Conv2d_7_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_7_depthwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_7_depthwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_7_depthwise/depthwise_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_7_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_7_depthwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_7_pointwise/BatchNorm/batchnorm_1/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_7_depthwise/Relu6", "MobilenetV1/Conv2d_7_pointwise/weights"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "MobilenetV1/Conv2d_7_pointwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_7_pointwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_7_pointwise/Conv2D_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_7_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_7_pointwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_8_depthwise/BatchNorm/batchnorm_1/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/Conv2d_7_pointwise/Relu6", "MobilenetV1/Conv2d_8_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "MobilenetV1/Conv2d_8_depthwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_8_depthwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_8_depthwise/depthwise_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_8_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_8_depthwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_8_pointwise/BatchNorm/batchnorm_1/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_8_depthwise/Relu6", "MobilenetV1/Conv2d_8_pointwise/weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "MobilenetV1/Conv2d_8_pointwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_8_pointwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_8_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_8_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_8_pointwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_9_depthwise/BatchNorm/batchnorm_1/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/Conv2d_8_pointwise/Relu6", "MobilenetV1/Conv2d_9_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_9_depthwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_9_depthwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_9_depthwise/depthwise_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_9_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_9_depthwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_9_pointwise/BatchNorm/batchnorm_1/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_9_depthwise/Relu6", "MobilenetV1/Conv2d_9_pointwise/weights"], "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "MobilenetV1/Conv2d_9_pointwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_9_pointwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_9_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_9_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_9_pointwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_10_depthwise/BatchNorm/batchnorm_1/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/Conv2d_9_pointwise/Relu6", "MobilenetV1/Conv2d_10_depthwise/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "MobilenetV1/Conv2d_10_depthwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_10_depthwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_10_depthwise/depthwise_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_10_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_10_depthwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_10_pointwise/BatchNorm/batchnorm_1/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_10_depthwise/Relu6", "MobilenetV1/Conv2d_10_pointwise/weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}}}, {"name": "MobilenetV1/Conv2d_10_pointwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_10_pointwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_10_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_10_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_10_pointwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_11_depthwise/BatchNorm/batchnorm_1/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/Conv2d_10_pointwise/Relu6", "MobilenetV1/Conv2d_11_depthwise/depthwise_weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "MobilenetV1/Conv2d_11_depthwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_11_depthwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_11_depthwise/depthwise_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_11_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_11_depthwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_11_pointwise/BatchNorm/batchnorm_1/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_11_depthwise/Relu6", "MobilenetV1/Conv2d_11_pointwise/weights"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_11_pointwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_11_pointwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_11_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_11_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_11_pointwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_12_depthwise/BatchNorm/batchnorm_1/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/Conv2d_11_pointwise/Relu6", "MobilenetV1/Conv2d_12_depthwise/depthwise_weights"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "MobilenetV1/Conv2d_12_depthwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_12_depthwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_12_depthwise/depthwise_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_12_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_12_depthwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_12_pointwise/BatchNorm/batchnorm_1/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_12_depthwise/Relu6", "MobilenetV1/Conv2d_12_pointwise/weights"], "attr": {"explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_12_pointwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_12_pointwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_12_pointwise/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_12_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_12_pointwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_13_depthwise/BatchNorm/batchnorm_1/add_1/conv", "op": "DepthwiseConv2dNative", "input": ["MobilenetV1/Conv2d_12_pointwise/Relu6", "MobilenetV1/Conv2d_13_depthwise/depthwise_weights"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_13_depthwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_13_depthwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_13_depthwise/depthwise_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/Conv2d_13_depthwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_13_depthwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_13_pointwise/BatchNorm/batchnorm_1/add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_13_depthwise/Relu6", "MobilenetV1/Conv2d_13_pointwise/weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}}}, {"name": "MobilenetV1/Conv2d_13_pointwise/BatchNorm/batchnorm_1/add_1", "op": "BiasAdd", "input": ["MobilenetV1/Conv2d_13_pointwise/BatchNorm/batchnorm_1/add_1/conv", "MobilenetV1/Conv2d_13_pointwise/Conv2D_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/Conv2d_13_pointwise/Relu6", "op": "Relu6", "input": ["MobilenetV1/Conv2d_13_pointwise/BatchNorm/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "float_short_offsets/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_13_pointwise/Relu6", "MobilenetV1/offset_2/weights"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "float_segments/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_13_pointwise/Relu6", "MobilenetV1/segment_2/weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "add_1/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_13_pointwise/Relu6", "MobilenetV1/part_offset_2/weights"], "attr": {"explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "float_part_heatmaps/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_13_pointwise/Relu6", "MobilenetV1/part_heatmap_2/weights"], "attr": {"use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "float_long_offsets/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_13_pointwise/Relu6", "MobilenetV1/embedding_2/weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "float_heatmaps/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_13_pointwise/Relu6", "MobilenetV1/heatmap_2/weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "MobilenetV1/displacement_fwd_2/BiasAdd/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_13_pointwise/Relu6", "MobilenetV1/displacement_fwd_2/weights"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV1/displacement_bwd_2/BiasAdd/conv", "op": "Conv2D", "input": ["MobilenetV1/Conv2d_13_pointwise/Relu6", "MobilenetV1/displacement_bwd_2/weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}}}, {"name": "float_short_offsets", "op": "BiasAdd", "input": ["float_short_offsets/conv", "MobilenetV1/offset_2/Conv2D_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "float_segments", "op": "BiasAdd", "input": ["float_segments/conv", "MobilenetV1/segment_2/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "add_1", "op": "BiasAdd", "input": ["add_1/conv", "MobilenetV1/part_offset_2/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "float_part_heatmaps", "op": "BiasAdd", "input": ["float_part_heatmaps/conv", "MobilenetV1/part_heatmap_2/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "float_long_offsets", "op": "BiasAdd", "input": ["float_long_offsets/conv", "MobilenetV1/embedding_2/Conv2D_bias"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "float_heatmaps", "op": "BiasAdd", "input": ["float_heatmaps/conv", "MobilenetV1/heatmap_2/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/displacement_fwd_2/BiasAdd", "op": "BiasAdd", "input": ["MobilenetV1/displacement_fwd_2/BiasAdd/conv", "MobilenetV1/displacement_fwd_2/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "MobilenetV1/displacement_bwd_2/BiasAdd", "op": "BiasAdd", "input": ["MobilenetV1/displacement_bwd_2/BiasAdd/conv", "MobilenetV1/displacement_bwd_2/Conv2D_bias"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "clip_by_value/Minimum", "op": "Minimum", "input": ["add_1", "clip_by_value/Minimum/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "float_part_offsets", "op": "Maximum", "input": ["clip_by_value/Minimum", "clip_by_value/y"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {"producer": 38}}, "weightsManifest": [{"paths": ["group1-shard1of1.bin"], "weights": [{"name": "MobilenetV1/offset_2/Conv2D_bias", "shape": [34], "dtype": "float32", "quantization": {"min": -0.08792929969136244, "scale": 2.122050866187915e-06, "dtype": "uint16"}}, {"name": "MobilenetV1/offset_2/weights", "shape": [1, 1, 384, 34], "dtype": "float32", "quantization": {"min": -0.8012001821994236, "scale": 2.4171851270120786e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_13_pointwise/Conv2D_bias", "shape": [384], "dtype": "float32", "quantization": {"min": -3.3090264518646406, "scale": 0.00011599223401095907, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_13_pointwise/weights", "shape": [1, 1, 384, 384], "dtype": "float32", "quantization": {"min": -1.3656869902166575, "scale": 4.2503718845247815e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_13_depthwise/depthwise_bias", "shape": [384], "dtype": "float32", "quantization": {"min": -2.021474768930454, "scale": 6.314740625173228e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_13_depthwise/depthwise_weights", "shape": [3, 3, 384, 1], "dtype": "float32", "quantization": {"min": -2.2761803466140282, "scale": 8.442492291139157e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_12_pointwise/Conv2D_bias", "shape": [384], "dtype": "float32", "quantization": {"min": -1.0674666925108551, "scale": 3.317999168565383e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_12_pointwise/weights", "shape": [1, 1, 384, 384], "dtype": "float32", "quantization": {"min": -0.5572230073564739, "scale": 1.703941677440138e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_12_depthwise/depthwise_bias", "shape": [384], "dtype": "float32", "quantization": {"min": -6.0177228460052135, "scale": 0.0001553883039224627, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_12_depthwise/depthwise_weights", "shape": [3, 3, 384, 1], "dtype": "float32", "quantization": {"min": -2.239977986568661, "scale": 7.851307348645851e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_11_pointwise/Conv2D_bias", "shape": [384], "dtype": "float32", "quantization": {"min": -10.24955907384218, "scale": 0.0002587553728469915, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_11_pointwise/weights", "shape": [1, 1, 384, 384], "dtype": "float32", "quantization": {"min": -1.3085542446289855, "scale": 3.8843334262318495e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_11_depthwise/depthwise_bias", "shape": [384], "dtype": "float32", "quantization": {"min": -6.3592472289459305, "scale": 0.00022323330743658268, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_11_depthwise/depthwise_weights", "shape": [3, 3, 384, 1], "dtype": "float32", "quantization": {"min": -3.2175203089584175, "scale": 9.50607235193198e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_10_pointwise/Conv2D_bias", "shape": [384], "dtype": "float32", "quantization": {"min": -7.7282513225634295, "scale": 0.0002081067245412384, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_10_pointwise/weights", "shape": [1, 1, 384, 384], "dtype": "float32", "quantization": {"min": -0.8857060482311533, "scale": 2.7586932293999667e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_10_depthwise/depthwise_bias", "shape": [384], "dtype": "float32", "quantization": {"min": -21.617181046185326, "scale": 0.0004354439820760883, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_10_depthwise/depthwise_weights", "shape": [3, 3, 384, 1], "dtype": "float32", "quantization": {"min": -3.058203296347778, "scale": 8.500439993183918e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_9_pointwise/Conv2D_bias", "shape": [384], "dtype": "float32", "quantization": {"min": -9.532602104500755, "scale": 0.00021809742162763696, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_9_pointwise/weights", "shape": [1, 1, 384, 384], "dtype": "float32", "quantization": {"min": -1.2318892582626848, "scale": 4.187820431950928e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_9_depthwise/depthwise_bias", "shape": [384], "dtype": "float32", "quantization": {"min": -9.755448520579199, "scale": 0.00031122821887316, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_9_depthwise/depthwise_weights", "shape": [3, 3, 384, 1], "dtype": "float32", "quantization": {"min": -3.5617484403764337, "scale": 0.00010704298973301779, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_8_pointwise/Conv2D_bias", "shape": [384], "dtype": "float32", "quantization": {"min": -5.5248427009058645, "scale": 0.00019761929752497995, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_8_pointwise/weights", "shape": [1, 1, 384, 384], "dtype": "float32", "quantization": {"min": -1.052610190725803, "scale": 3.2387009345121783e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_8_depthwise/depthwise_bias", "shape": [384], "dtype": "float32", "quantization": {"min": -8.688729857161505, "scale": 0.0003088447679650768, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_8_depthwise/depthwise_weights", "shape": [3, 3, 384, 1], "dtype": "float32", "quantization": {"min": -3.8003165168002586, "scale": 0.0001165705504984589, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_7_pointwise/Conv2D_bias", "shape": [384], "dtype": "float32", "quantization": {"min": -3.9073248303654156, "scale": 0.00013394552227779011, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_7_pointwise/weights", "shape": [1, 1, 384, 384], "dtype": "float32", "quantization": {"min": -0.6759259337772082, "scale": 2.4373501145867885e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_7_depthwise/depthwise_bias", "shape": [384], "dtype": "float32", "quantization": {"min": -15.266294376049942, "scale": 0.00032908588868398237, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_7_depthwise/depthwise_weights", "shape": [3, 3, 384, 1], "dtype": "float32", "quantization": {"min": -3.353680647939314, "scale": 0.00010332688319744012, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_6_pointwise/Conv2D_bias", "shape": [384], "dtype": "float32", "quantization": {"min": -8.239143574278751, "scale": 0.00031484365372305977, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_6_pointwise/weights", "shape": [1, 1, 192, 384], "dtype": "float32", "quantization": {"min": -2.179767121851758, "scale": 6.872118042346096e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_6_depthwise/depthwise_bias", "shape": [192], "dtype": "float32", "quantization": {"min": -2.4646471631911666, "scale": 0.000142952680424057, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_6_depthwise/depthwise_weights", "shape": [3, 3, 192, 1], "dtype": "float32", "quantization": {"min": -1.5102104315361569, "scale": 5.842658741628586e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_5_pointwise/Conv2D_bias", "shape": [192], "dtype": "float32", "quantization": {"min": -5.169686393857121, "scale": 0.00024399124003478953, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_5_pointwise/weights", "shape": [1, 1, 192, 192], "dtype": "float32", "quantization": {"min": -4.751811932851696, "scale": 0.00012347179246074305, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_5_depthwise/depthwise_bias", "shape": [192], "dtype": "float32", "quantization": {"min": -7.193127291591997, "scale": 0.00027495612903145894, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_5_depthwise/depthwise_weights", "shape": [3, 3, 192, 1], "dtype": "float32", "quantization": {"min": -4.047022895932315, "scale": 0.00012306966597531673, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_4_pointwise/Conv2D_bias", "shape": [192], "dtype": "float32", "quantization": {"min": -18.890830239385963, "scale": 0.0005241047120016081, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_4_pointwise/weights", "shape": [1, 1, 96, 192], "dtype": "float32", "quantization": {"min": -2.243241217997324, "scale": 7.299128682514964e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_4_depthwise/depthwise_bias", "shape": [96], "dtype": "float32", "quantization": {"min": -9.222006461505188, "scale": 0.00033478568436452435, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_4_depthwise/depthwise_weights", "shape": [3, 3, 96, 1], "dtype": "float32", "quantization": {"min": -1.6009748706903484, "scale": 5.03134780229525e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_3_pointwise/Conv2D_bias", "shape": [96], "dtype": "float32", "quantization": {"min": -34.6891905514592, "scale": 0.0006677290244934496, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_3_pointwise/weights", "shape": [1, 1, 96, 96], "dtype": "float32", "quantization": {"min": -12.592897676924977, "scale": 0.0003341532048220819, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_3_depthwise/depthwise_bias", "shape": [96], "dtype": "float32", "quantization": {"min": -8.757352745115524, "scale": 0.0005486031914499483, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_3_depthwise/depthwise_weights", "shape": [3, 3, 96, 1], "dtype": "float32", "quantization": {"min": -14.098358237345922, "scale": 0.000407785214975441, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_2_pointwise/Conv2D_bias", "shape": [96], "dtype": "float32", "quantization": {"min": -7.5692298396060975, "scale": 0.00025808005181240744, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_2_pointwise/weights", "shape": [1, 1, 48, 96], "dtype": "float32", "quantization": {"min": -3.9138179843991647, "scale": 9.669239282553462e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_2_depthwise/depthwise_bias", "shape": [48], "dtype": "float32", "quantization": {"min": -1.5462309135364398, "scale": 0.0001422868237357541, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_2_depthwise/depthwise_weights", "shape": [3, 3, 48, 1], "dtype": "float32", "quantization": {"min": -20.911123784615974, "scale": 0.0008107915080693255, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_1_pointwise/Conv2D_bias", "shape": [48], "dtype": "float32", "quantization": {"min": -47.36236553828067, "scale": 0.0009320548172445276, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_1_pointwise/weights", "shape": [1, 1, 24, 48], "dtype": "float32", "quantization": {"min": -8.813134815917186, "scale": 0.00029330187752653043, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_1_depthwise/depthwise_bias", "shape": [24], "dtype": "float32", "quantization": {"min": -3.1182402688814643, "scale": 0.00013777405862596494, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_1_depthwise/depthwise_weights", "shape": [3, 3, 24, 1], "dtype": "float32", "quantization": {"min": -52.492978168841745, "scale": 0.0016077481828129171, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_0/Conv2D_bias", "shape": [24], "dtype": "float32", "quantization": {"min": -4.586093136774391, "scale": 0.00011263337516944742, "dtype": "uint16"}}, {"name": "MobilenetV1/Conv2d_0/weights", "shape": [3, 3, 3, 24], "dtype": "float32", "quantization": {"min": -5.022108225597982, "scale": 0.0001399890794591772, "dtype": "uint16"}}, {"name": "MobilenetV1/segment_2/Conv2D_bias", "shape": [1], "dtype": "float32", "quantization": {"min": -0.23390831053256989, "scale": 1.0, "dtype": "uint16"}}, {"name": "MobilenetV1/segment_2/weights", "shape": [1, 1, 384, 1], "dtype": "float32", "quantization": {"min": -0.460302003457525, "scale": 1.4182339273401681e-05, "dtype": "uint16"}}, {"name": "clip_by_value/y", "shape": [], "dtype": "float32", "quantization": {"min": 0.0, "scale": 1.0, "dtype": "uint16"}}, {"name": "clip_by_value/Minimum/y", "shape": [], "dtype": "float32", "quantization": {"min": 1.0, "scale": 1.0, "dtype": "uint16"}}, {"name": "MobilenetV1/part_offset_2/Conv2D_bias", "shape": [48], "dtype": "float32", "quantization": {"min": 0.4809534251689911, "scale": 5.226613837320927e-07, "dtype": "uint16"}}, {"name": "MobilenetV1/part_offset_2/weights", "shape": [1, 1, 384, 48], "dtype": "float32", "quantization": {"min": -0.15968976886005762, "scale": 4.9444149258462895e-06, "dtype": "uint16"}}, {"name": "MobilenetV1/part_heatmap_2/Conv2D_bias", "shape": [24], "dtype": "float32", "quantization": {"min": -0.01967313677013407, "scale": 1.208052610999943e-06, "dtype": "uint16"}}, {"name": "MobilenetV1/part_heatmap_2/weights", "shape": [1, 1, 384, 24], "dtype": "float32", "quantization": {"min": -0.3484475968940367, "scale": 9.488021698952667e-06, "dtype": "uint16"}}, {"name": "MobilenetV1/embedding_2/Conv2D_bias", "shape": [34], "dtype": "float32", "quantization": {"min": -0.12531072802701257, "scale": 3.412508592549565e-06, "dtype": "uint16"}}, {"name": "MobilenetV1/embedding_2/weights", "shape": [1, 1, 384, 34], "dtype": "float32", "quantization": {"min": -0.5138838545139388, "scale": 1.5659074702560832e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/heatmap_2/Conv2D_bias", "shape": [17], "dtype": "float32", "quantization": {"min": -0.33422020077705383, "scale": 1.6993667583330242e-06, "dtype": "uint16"}}, {"name": "MobilenetV1/heatmap_2/weights", "shape": [1, 1, 384, 17], "dtype": "float32", "quantization": {"min": -0.23803269427928808, "scale": 7.229542726781718e-06, "dtype": "uint16"}}, {"name": "MobilenetV1/displacement_fwd_2/Conv2D_bias", "shape": [32], "dtype": "float32", "quantization": {"min": -0.03593770705690425, "scale": 3.512972341828373e-06, "dtype": "uint16"}}, {"name": "MobilenetV1/displacement_fwd_2/weights", "shape": [1, 1, 384, 32], "dtype": "float32", "quantization": {"min": -0.40644382450212607, "scale": 1.3052984279726574e-05, "dtype": "uint16"}}, {"name": "MobilenetV1/displacement_bwd_2/Conv2D_bias", "shape": [32], "dtype": "float32", "quantization": {"min": -0.2753727621489462, "scale": 5.0646060867532215e-06, "dtype": "uint16"}}, {"name": "MobilenetV1/displacement_bwd_2/weights", "shape": [1, 1, 384, 32], "dtype": "float32", "quantization": {"min": -0.49668018700756905, "scale": 1.3596501149947141e-05, "dtype": "uint16"}}]}]}