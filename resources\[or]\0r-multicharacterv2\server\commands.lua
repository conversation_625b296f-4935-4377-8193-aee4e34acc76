lib.addCommand('deletecharacter', {
    help = 'Delete a character',
    params = {
        {
            name = 'citizenid',
            type = 'string',
            help = 'Target player\'s citizen id',
        },
    },
    restricted = 'group.admin'
}, function(source, args, raw)
    if source == 0 then
        return print('^2[0r-multicharacterv2]^7 This command can only be executed in-game')
    end

    local cid = Items(args.citizenid)
 
    if cid then
        lib.callback('0r-multicharacterv2:server:deleteCharacter', false, function()
            cb('ok')
        end, cid)
    end
end)

lib.addCommand('logout', {
    help = 'Logout a player',
    params = {},
    restricted = 'group.admin'
}, function(source, args, raw)
    if source == 0 then
        return print('^2[0r-multicharacterv2]^7 This command can only be executed in-game')
    end

    if not hasDonePreloading[source] then
        return print('^2[0r-multicharacterv2]^7 This player is not preloaded')
    end

    if CoreName == 'qb-core' then
        QBCore.Player.Logout(source)
    elseif CoreName == 'qbx_core' then
        QBX:Logout(source)
    end

    print('^2[0r-multicharacterv2]^7 '..GetPlayerName(source)..' (Source: '..source..') exited!')

    TriggerClientEvent('0r-multicharacterv2:client:playerLoaded', source)
end)

lib.addCommand('createslotkey', {
    help = 'Create a slot key',
    params = {
        {
            name = 'count',
            type = 'number',
            help = 'Slot amount',
        },
    },
    restricted = 'group.admin'
}, function(source, args, raw)
    local code = math.random(100000, 999999)
    local slots = tonumber(args.count)

    if slots then
        CreateCode(code, slots)
    end
end)