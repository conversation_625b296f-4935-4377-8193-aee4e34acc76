local L0_1, L1_1, L2_1, L3_1, L4_1, L5_1, L6_1, L7_1, L8_1, L9_1, L10_1, L11_1, L12_1, L13_1, L14_1, L15_1
L0_1 = 1
L1_1 = false
L2_1 = false
L3_1 = false
L4_1 = nil
L5_1 = false
L6_1 = false
L7_1 = Citizen
L7_1 = L7_1.CreateThread
function L8_1()
  local L0_2, L1_2, L2_2
  L0_2 = Wait
  L1_2 = 5000
  L0_2(L1_2)
  L0_2 = SendNUIMessage
  L1_2 = {}
  L1_2.action = "translations"
  L2_2 = Config
  L2_2 = L2_2.Translations
  L1_2.translations = L2_2
  L0_2(L1_2)
end
L7_1(L8_1)
L7_1 = RegisterCommand
L8_1 = Config
L8_1 = L8_1.Commands
L8_1 = L8_1.Minimap
L8_1 = L8_1.cmd
function L9_1()
  local L0_2, L1_2, L2_2, L3_2
  L0_2 = Config
  L0_2 = L0_2.ChangeMinimapSize
  if not L0_2 then
    return
  end
  L0_2 = IsNuiFocused
  L0_2 = L0_2()
  if L0_2 then
    return
  end
  L0_2 = FW_GetPlayerData
  L1_2 = false
  L0_2 = L0_2(L1_2)
  L1_2 = CanOpenTablet
  L2_2 = L0_2.job
  L2_2 = L2_2.name
  L1_2 = L1_2(L2_2)
  L1_2 = L1_2[1]
  if L1_2 then
    L1_2 = L0_2.job
    L1_2 = L1_2.onduty
    if L1_2 then
      L1_2 = L0_1
      if 3 == L1_2 then
        L1_2 = 1
        L0_1 = L1_2
      else
        L1_2 = L0_1
        L1_2 = L1_2 + 1
        L0_1 = L1_2
      end
      L1_2 = L0_1
      if 1 == L1_2 then
        L1_2 = SetBigmapActive
        L2_2 = false
        L3_2 = false
        L1_2(L2_2, L3_2)
      else
        L1_2 = L0_1
        if 2 == L1_2 then
          L1_2 = SetBigmapActive
          L2_2 = true
          L3_2 = false
          L1_2(L2_2, L3_2)
        else
          L1_2 = L0_1
          if 3 == L1_2 then
            L1_2 = SetBigmapActive
            L2_2 = true
            L3_2 = true
            L1_2(L2_2, L3_2)
          end
        end
      end
    end
  end
end
L7_1(L8_1, L9_1)
L7_1 = RegisterKeyMapping
L8_1 = Config
L8_1 = L8_1.Commands
L8_1 = L8_1.Minimap
L8_1 = L8_1.cmd
L9_1 = Config
L9_1 = L9_1.Commands
L9_1 = L9_1.Minimap
L9_1 = L9_1.description
L10_1 = "keyboard"
L11_1 = Config
L11_1 = L11_1.Commands
L11_1 = L11_1.Minimap
L11_1 = L11_1.key
L7_1(L8_1, L9_1, L10_1, L11_1)
L7_1 = RegisterCommand
L8_1 = Config
L8_1 = L8_1.Commands
L8_1 = L8_1.QuickAccess
L8_1 = L8_1.cmd
function L9_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2
  L3_2 = IsNuiFocused
  L3_2 = L3_2()
  if L3_2 then
    return
  end
  L3_2 = FW_GetPlayerData
  L4_2 = false
  L3_2 = L3_2(L4_2)
  L4_2 = CanOpenQuickAccessMenu
  L4_2 = L4_2()
  if L4_2 then
    L4_2 = SendNUIMessage
    L5_2 = {}
    L5_2.action = "OpenQuickAccess"
    L6_2 = L3_1
    if not L6_2 then
      L6_2 = Config
      L6_2 = L6_2.Translations
      if L6_2 then
        goto lbl_25
      end
    end
    L6_2 = nil
    ::lbl_25::
    L5_2.translations = L6_2
    L6_2 = exports
    L6_2 = L6_2.origen_police
    L7_2 = L6_2
    L6_2 = L6_2.GerPermissions
    L6_2 = L6_2(L7_2)
    L5_2.permissions = L6_2
    L6_2 = {}
    L7_2 = L3_2.job
    L7_2 = L7_2.onduty
    L6_2.duty = L7_2
    L7_2 = L3_2.job
    L7_2 = L7_2.name
    L6_2.name = L7_2
    L7_2 = L3_2.job
    L7_2 = L7_2.grade
    L7_2 = L7_2.level
    L6_2.level = L7_2
    L5_2.jobData = L6_2
    L6_2 = {}
    L7_2 = Config
    L7_2 = L7_2.Confiscate
    L6_2.confiscate = L7_2
    L5_2.options = L6_2
    L4_2(L5_2)
    L4_2 = SetNuiFocus
    L5_2 = true
    L6_2 = true
    L4_2(L5_2, L6_2)
    L4_2 = Citizen
    L4_2 = L4_2.CreateThread
    function L5_2()
      local L0_3, L1_3, L2_3, L3_3
      L0_3 = SetNuiFocusKeepInput
      L1_3 = true
      L0_3(L1_3)
      while true do
        L0_3 = IsNuiFocused
        L0_3 = L0_3()
        if not L0_3 then
          break
        end
        L0_3 = Citizen
        L0_3 = L0_3.Wait
        L1_3 = 0
        L0_3(L1_3)
        L0_3 = DisableAllControlActions
        L1_3 = 0
        L0_3(L1_3)
        L0_3 = EnableControlAction
        L1_3 = 0
        L2_3 = 249
        L3_3 = true
        L0_3(L1_3, L2_3, L3_3)
        L0_3 = EnableControlAction
        L1_3 = 1
        L2_3 = 249
        L3_3 = true
        L0_3(L1_3, L2_3, L3_3)
        L0_3 = EnableControlAction
        L1_3 = 2
        L2_3 = 249
        L3_3 = true
        L0_3(L1_3, L2_3, L3_3)
        L0_3 = EnableControlAction
        L1_3 = 0
        L2_3 = 22
        L3_3 = true
        L0_3(L1_3, L2_3, L3_3)
        L0_3 = EnableControlAction
        L1_3 = 0
        L2_3 = 30
        L3_3 = true
        L0_3(L1_3, L2_3, L3_3)
        L0_3 = EnableControlAction
        L1_3 = 0
        L2_3 = 31
        L3_3 = true
        L0_3(L1_3, L2_3, L3_3)
        L0_3 = EnableControlAction
        L1_3 = 0
        L2_3 = 32
        L3_3 = true
        L0_3(L1_3, L2_3, L3_3)
        L0_3 = EnableControlAction
        L1_3 = 0
        L2_3 = 33
        L3_3 = true
        L0_3(L1_3, L2_3, L3_3)
        L0_3 = EnableControlAction
        L1_3 = 0
        L2_3 = 34
        L3_3 = true
        L0_3(L1_3, L2_3, L3_3)
        L0_3 = EnableControlAction
        L1_3 = 0
        L2_3 = 35
        L3_3 = true
        L0_3(L1_3, L2_3, L3_3)
        L0_3 = EnableControlAction
        L1_3 = 0
        L2_3 = 59
        L3_3 = true
        L0_3(L1_3, L2_3, L3_3)
        L0_3 = EnableControlAction
        L1_3 = 0
        L2_3 = 71
        L3_3 = true
        L0_3(L1_3, L2_3, L3_3)
        L0_3 = EnableControlAction
        L1_3 = 0
        L2_3 = 72
        L3_3 = true
        L0_3(L1_3, L2_3, L3_3)
        L0_3 = EnableControlAction
        L1_3 = 0
        L2_3 = 76
        L3_3 = true
        L0_3(L1_3, L2_3, L3_3)
        L0_3 = EnableControlAction
        L1_3 = 0
        L2_3 = 21
        L3_3 = true
        L0_3(L1_3, L2_3, L3_3)
      end
      L0_3 = SetNuiFocusKeepInput
      L1_3 = false
      L0_3(L1_3)
      while true do
        L0_3 = IsControlPressed
        L1_3 = 0
        L2_3 = 200
        L0_3 = L0_3(L1_3, L2_3)
        if not L0_3 then
          L0_3 = IsDisabledControlPressed
          L1_3 = 0
          L2_3 = 200
          L0_3 = L0_3(L1_3, L2_3)
          if not L0_3 then
            break
          end
        end
        L0_3 = Citizen
        L0_3 = L0_3.Wait
        L1_3 = 0
        L0_3(L1_3)
        L0_3 = DisableControlAction
        L1_3 = 0
        L2_3 = 200
        L3_3 = true
        L0_3(L1_3, L2_3, L3_3)
      end
      L0_3 = DisableControlAction
      L1_3 = 0
      L2_3 = 200
      L3_3 = true
      L0_3(L1_3, L2_3, L3_3)
    end
    L4_2(L5_2)
  end
end
L7_1(L8_1, L9_1)
L7_1 = RegisterNUICallback
L8_1 = "translationsRecived"
function L9_1(A0_2, A1_2)
  local L2_2, L3_2
  L2_2 = true
  L3_1 = L2_2
  L2_2 = A1_2
  L3_2 = true
  L2_2(L3_2)
end
L7_1(L8_1, L9_1)
L7_1 = RegisterKeyMapping
L8_1 = Config
L8_1 = L8_1.Commands
L8_1 = L8_1.QuickAccess
L8_1 = L8_1.cmd
L9_1 = Config
L9_1 = L9_1.Commands
L9_1 = L9_1.QuickAccess
L9_1 = L9_1.description
L10_1 = "keyboard"
L11_1 = Config
L11_1 = L11_1.Commands
L11_1 = L11_1.QuickAccess
L11_1 = L11_1.key
L7_1(L8_1, L9_1, L10_1, L11_1)
L7_1 = RegisterNUICallback
L8_1 = "notification"
function L9_1(A0_2, A1_2)
  local L2_2, L3_2
  L2_2 = ShowNotification
  L3_2 = A0_2
  L2_2(L3_2)
  L2_2 = A1_2
  L3_2 = true
  L2_2(L3_2)
end
L7_1(L8_1, L9_1)
L7_1 = RegisterNUICallback
L8_1 = "quickaction"
function L9_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2
  L2_2 = A0_2.action
  if "ReferenceIcon" == L2_2 then
    L2_2 = PlayerPedId
    L2_2 = L2_2()
    L3_2 = GetVehiclePedIsIn
    L4_2 = L2_2
    L5_2 = false
    L3_2 = L3_2(L4_2, L5_2)
    if 0 ~= L3_2 then
      L4_2 = GetPedInVehicleSeat
      L5_2 = L3_2
      L6_2 = -1
      L4_2 = L4_2(L5_2, L6_2)
      if L4_2 == L2_2 then
        goto lbl_34
      end
      L4_2 = GetPedInVehicleSeat
      L5_2 = L3_2
      L6_2 = 0
      L4_2 = L4_2(L5_2, L6_2)
      if L4_2 == L2_2 then
        goto lbl_34
      end
    end
    L4_2 = TriggerServerEvent
    L5_2 = "origen_police:server:updateref"
    L6_2 = {}
    L7_2 = tonumber
    L8_2 = A0_2.id
    L7_2 = L7_2(L8_2)
    L6_2.sprite = L7_2
    L4_2(L5_2, L6_2)
    goto lbl_240
    ::lbl_34::
    L4_2 = {}
    L5_2 = -1
    L6_2 = 0
    L7_2 = 1
    for L8_2 = L5_2, L6_2, L7_2 do
      L9_2 = GetPedInVehicleSeat
      L10_2 = L3_2
      L11_2 = L8_2
      L9_2 = L9_2(L10_2, L11_2)
      if 0 ~= L9_2 then
        L10_2 = table
        L10_2 = L10_2.insert
        L11_2 = L4_2
        L12_2 = GetPlayerServerId
        L13_2 = NetworkGetPlayerIndexFromPed
        L14_2 = L9_2
        L13_2, L14_2 = L13_2(L14_2)
        L12_2, L13_2, L14_2 = L12_2(L13_2, L14_2)
        L10_2(L11_2, L12_2, L13_2, L14_2)
      end
    end
    L5_2 = TriggerServerEvent
    L6_2 = "origen_police:server:updateref"
    L7_2 = {}
    L8_2 = tonumber
    L9_2 = A0_2.id
    L8_2 = L8_2(L9_2)
    L7_2.sprite = L8_2
    L8_2 = L4_2
    L5_2(L6_2, L7_2, L8_2)
  else
    L2_2 = A0_2.action
    if "ReferenceColor" == L2_2 then
      L2_2 = PlayerPedId
      L2_2 = L2_2()
      L3_2 = GetVehiclePedIsIn
      L4_2 = L2_2
      L5_2 = false
      L3_2 = L3_2(L4_2, L5_2)
      if 0 ~= L3_2 then
        L4_2 = GetPedInVehicleSeat
        L5_2 = L3_2
        L6_2 = -1
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 == L2_2 then
          goto lbl_100
        end
        L4_2 = GetPedInVehicleSeat
        L5_2 = L3_2
        L6_2 = 0
        L4_2 = L4_2(L5_2, L6_2)
        if L4_2 == L2_2 then
          goto lbl_100
        end
      end
      L4_2 = TriggerServerEvent
      L5_2 = "origen_police:server:updateref"
      L6_2 = {}
      L7_2 = tonumber
      L8_2 = A0_2.id
      L7_2 = L7_2(L8_2)
      L6_2.color = L7_2
      L4_2(L5_2, L6_2)
      goto lbl_240
      ::lbl_100::
      L4_2 = {}
      L5_2 = -1
      L6_2 = 0
      L7_2 = 1
      for L8_2 = L5_2, L6_2, L7_2 do
        L9_2 = GetPedInVehicleSeat
        L10_2 = L3_2
        L11_2 = L8_2
        L9_2 = L9_2(L10_2, L11_2)
        if 0 ~= L9_2 then
          L10_2 = table
          L10_2 = L10_2.insert
          L11_2 = L4_2
          L12_2 = GetPlayerServerId
          L13_2 = NetworkGetPlayerIndexFromPed
          L14_2 = L9_2
          L13_2, L14_2 = L13_2(L14_2)
          L12_2, L13_2, L14_2 = L12_2(L13_2, L14_2)
          L10_2(L11_2, L12_2, L13_2, L14_2)
        end
      end
      L5_2 = TriggerServerEvent
      L6_2 = "origen_police:server:updateref"
      L7_2 = {}
      L8_2 = tonumber
      L9_2 = A0_2.id
      L8_2 = L8_2(L9_2)
      L7_2.color = L8_2
      L8_2 = L4_2
      L5_2(L6_2, L7_2, L8_2)
    else
      L2_2 = A0_2.action
      if "ToggleReady" == L2_2 then
        L2_2 = PlayerPedId
        L2_2 = L2_2()
        L3_2 = GetVehiclePedIsIn
        L4_2 = L2_2
        L5_2 = false
        L3_2 = L3_2(L4_2, L5_2)
        if 0 ~= L3_2 then
          L4_2 = GetPedInVehicleSeat
          L5_2 = L3_2
          L6_2 = -1
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == L2_2 then
            goto lbl_163
          end
          L4_2 = GetPedInVehicleSeat
          L5_2 = L3_2
          L6_2 = 0
          L4_2 = L4_2(L5_2, L6_2)
          if L4_2 == L2_2 then
            goto lbl_163
          end
        end
        L4_2 = TriggerServerEvent
        L5_2 = "origen_police:server:Ready"
        L6_2 = Config
        L6_2 = L6_2.PoliceJobName
        L7_2 = A0_2.state
        L4_2(L5_2, L6_2, L7_2)
        goto lbl_240
        ::lbl_163::
        L4_2 = {}
        L5_2 = -1
        L6_2 = 0
        L7_2 = 1
        for L8_2 = L5_2, L6_2, L7_2 do
          L9_2 = GetPedInVehicleSeat
          L10_2 = L3_2
          L11_2 = L8_2
          L9_2 = L9_2(L10_2, L11_2)
          if 0 ~= L9_2 then
            L10_2 = table
            L10_2 = L10_2.insert
            L11_2 = L4_2
            L12_2 = GetPlayerServerId
            L13_2 = NetworkGetPlayerIndexFromPed
            L14_2 = L9_2
            L13_2, L14_2 = L13_2(L14_2)
            L12_2, L13_2, L14_2 = L12_2(L13_2, L14_2)
            L10_2(L11_2, L12_2, L13_2, L14_2)
          end
        end
        L5_2 = TriggerServerEvent
        L6_2 = "origen_police:server:Ready"
        L7_2 = Config
        L7_2 = L7_2.PoliceJobName
        L8_2 = A0_2.state
        L9_2 = L4_2
        L5_2(L6_2, L7_2, L8_2, L9_2)
      else
        L2_2 = A0_2.action
        if "RadioCalls" == L2_2 then
          L2_2 = Calls
          L3_2 = A0_2.id
          L2_2(L3_2)
        else
          L2_2 = A0_2.command
          if L2_2 then
            L2_2 = type
            L3_2 = A0_2.command
            L2_2 = L2_2(L3_2)
            if "table" == L2_2 then
              L2_2 = pairs
              L3_2 = A0_2.command
              L2_2, L3_2, L4_2, L5_2 = L2_2(L3_2)
              for L6_2, L7_2 in L2_2, L3_2, L4_2, L5_2 do
                L8_2 = Config
                L8_2 = L8_2.Framework
                if L6_2 == L8_2 then
                  L8_2 = ExecuteCommand
                  L9_2 = L7_2
                  L8_2(L9_2)
                end
              end
            else
              L2_2 = ExecuteCommand
              L3_2 = A0_2.command
              L2_2(L3_2)
            end
          else
            L2_2 = A0_2.event
            if L2_2 then
              L2_2 = TriggerEvent
              L3_2 = A0_2.event
              L2_2(L3_2)
            else
              L2_2 = A0_2.serverevent
              if L2_2 then
                L2_2 = TriggerServerEvent
                L3_2 = A0_2.serverevent
                L2_2(L3_2)
              end
            end
          end
        end
      end
    end
  end
  ::lbl_240::
  L2_2 = A1_2
  L3_2 = true
  L2_2(L3_2)
end
L7_1(L8_1, L9_1)
L7_1 = RegisterCommand
L8_1 = "esposar"
function L9_1()
  local L0_2, L1_2, L2_2
  L0_2 = FW_GetPlayerData
  L1_2 = false
  L0_2 = L0_2(L1_2)
  L1_2 = CanOpenTablet
  L2_2 = L0_2.job
  L2_2 = L2_2.name
  L1_2 = L1_2(L2_2)
  L1_2 = L1_2[1]
  if L1_2 then
    L1_2 = TriggerEvent
    L2_2 = "origen_police:client:cuffuncuff"
    L1_2(L2_2)
  end
end
L7_1(L8_1, L9_1)
L7_1 = false
L8_1 = RegisterNetEvent
L9_1 = "origen_police:client:setcuffstate"
function L10_1(A0_2)
  local L1_2, L2_2
  L1_2 = L7_1
  L1_2 = not L1_2
  L7_1 = L1_2
  L1_2 = Citizen
  L1_2 = L1_2.CreateThread
  function L2_2()
    local L0_3, L1_3, L2_3, L3_3, L4_3, L5_3, L6_3, L7_3, L8_3, L9_3, L10_3, L11_3, L12_3
    L0_3 = TriggerServerEvent
    L1_3 = "origen_police:server:playcuffanim"
    L2_3 = A0_2
    L3_3 = L7_1
    L0_3(L1_3, L2_3, L3_3)
    L0_3 = GetPlayerPed
    L1_3 = GetPlayerFromServerId
    L2_3 = A0_2
    L1_3, L2_3, L3_3, L4_3, L5_3, L6_3, L7_3, L8_3, L9_3, L10_3, L11_3, L12_3 = L1_3(L2_3)
    L0_3 = L0_3(L1_3, L2_3, L3_3, L4_3, L5_3, L6_3, L7_3, L8_3, L9_3, L10_3, L11_3, L12_3)
    L1_3 = SetEntityCoords
    L2_3 = PlayerPedId
    L2_3 = L2_3()
    L3_3 = GetEntityCoords
    L4_3 = L0_3
    L3_3 = L3_3(L4_3)
    L4_3 = GetEntityForwardVector
    L5_3 = L0_3
    L4_3 = L4_3(L5_3)
    L3_3 = L3_3 + L4_3
    L1_3(L2_3, L3_3)
    L1_3 = SetEntityHeading
    L2_3 = PlayerPedId
    L2_3 = L2_3()
    L3_3 = GetEntityHeading
    L4_3 = L0_3
    L3_3, L4_3, L5_3, L6_3, L7_3, L8_3, L9_3, L10_3, L11_3, L12_3 = L3_3(L4_3)
    L1_3(L2_3, L3_3, L4_3, L5_3, L6_3, L7_3, L8_3, L9_3, L10_3, L11_3, L12_3)
    L1_3 = Citizen
    L1_3 = L1_3.Wait
    L2_3 = 5000
    L1_3(L2_3)
    L1_3 = PlayerPedId
    L1_3 = L1_3()
    L2_3 = LocalPlayer
    L2_3 = L2_3.state
    L3_3 = L7_1
    L2_3.invBusy = L3_3
    L2_3 = GetResourceState
    L3_3 = "qs-inventory"
    L2_3 = L2_3(L3_3)
    if "started" == L2_3 then
      L2_3 = exports
      L2_3 = L2_3["qs-inventory"]
      L3_3 = L2_3
      L2_3 = L2_3.setInventoryDisabled
      L4_3 = true
      L2_3(L3_3, L4_3)
    end
    while true do
      L2_3 = L7_1
      if not L2_3 then
        break
      end
      L2_3 = Citizen
      L2_3 = L2_3.Wait
      L3_3 = 0
      L2_3(L3_3)
      L2_3 = GiveWeaponToPed
      L3_3 = L1_3
      L4_3 = GetHashKey
      L5_3 = "WEAPON_UNARMED"
      L4_3 = L4_3(L5_3)
      L5_3 = 0
      L6_3 = false
      L7_3 = true
      L2_3(L3_3, L4_3, L5_3, L6_3, L7_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 68
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 69
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 21
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 24
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 257
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 25
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 263
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 45
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 22
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 44
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 37
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 23
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 288
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 289
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 170
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 167
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 0
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 26
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 73
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 59
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 71
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 72
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 49
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 2
      L4_3 = 36
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 47
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 264
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 257
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 140
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 141
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 142
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 143
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 75
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 27
      L4_3 = 75
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 45
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 80
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 140
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 250
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 263
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = DisableControlAction
      L3_3 = 0
      L4_3 = 310
      L5_3 = true
      L2_3(L3_3, L4_3, L5_3)
      L2_3 = IsEntityPlayingAnim
      L3_3 = L1_3
      L4_3 = "mp_arresting"
      L5_3 = "idle"
      L6_3 = 3
      L2_3 = L2_3(L3_3, L4_3, L5_3, L6_3)
      if not L2_3 then
        L2_3 = TaskPlayAnim
        L3_3 = L1_3
        L4_3 = "mp_arresting"
        L5_3 = "idle"
        L6_3 = 8.0
        L7_3 = 8.0
        L8_3 = -1
        L9_3 = 51
        L10_3 = false
        L11_3 = false
        L12_3 = false
        L2_3(L3_3, L4_3, L5_3, L6_3, L7_3, L8_3, L9_3, L10_3, L11_3, L12_3)
      end
    end
    L2_3 = GetResourceState
    L3_3 = "qs-inventory"
    L2_3 = L2_3(L3_3)
    if "started" == L2_3 then
      L2_3 = exports
      L2_3 = L2_3["qs-inventory"]
      L3_3 = L2_3
      L2_3 = L2_3.setInventoryDisabled
      L4_3 = false
      L2_3(L3_3, L4_3)
    end
  end
  L1_2(L2_2)
end
L8_1(L9_1, L10_1)
L8_1 = RegisterNetEvent
L9_1 = "origen_police:client:playarrestanim"
function L10_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L1_2 = RequestAnimDict
  L2_2 = "mp_arrest_paired"
  L1_2(L2_2)
  while true do
    L1_2 = HasAnimDictLoaded
    L2_2 = "mp_arrest_paired"
    L1_2 = L1_2(L2_2)
    if L1_2 then
      break
    end
    L1_2 = Wait
    L2_2 = 1
    L1_2(L2_2)
  end
  L1_2 = RequestAnimDict
  L2_2 = "mp_arresting"
  L1_2(L2_2)
  while true do
    L1_2 = HasAnimDictLoaded
    L2_2 = "mp_arresting"
    L1_2 = L1_2(L2_2)
    if L1_2 then
      break
    end
    L1_2 = Wait
    L2_2 = 1
    L1_2(L2_2)
  end
  if A0_2 then
    L1_2 = TaskPlayAnim
    L2_2 = PlayerPedId
    L2_2 = L2_2()
    L3_2 = "mp_arrest_paired"
    L4_2 = "cop_p2_back_right"
    L5_2 = 8.0
    L6_2 = -8.0
    L7_2 = 3750
    L8_2 = 2
    L9_2 = 0
    L10_2 = 0
    L11_2 = 0
    L12_2 = 0
    L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  else
    L1_2 = TaskPlayAnim
    L2_2 = PlayerPedId
    L2_2 = L2_2()
    L3_2 = "mp_arresting"
    L4_2 = "a_uncuff"
    L5_2 = 8.0
    L6_2 = -8.0
    L7_2 = -1
    L8_2 = 2
    L9_2 = 0
    L10_2 = 0
    L11_2 = 0
    L12_2 = 0
    L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  end
  L1_2 = Citizen
  L1_2 = L1_2.Wait
  L2_2 = 3500
  L1_2(L2_2)
  L1_2 = ClearPedTasks
  L2_2 = PlayerPedId
  L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2 = L2_2()
  L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
end
L8_1(L9_1, L10_1)
L8_1 = RegisterNetEvent
L9_1 = "origen_police:client:playarrestedanim"
function L10_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  L1_2 = RequestAnimDict
  L2_2 = "mp_arrest_paired"
  L1_2(L2_2)
  while true do
    L1_2 = HasAnimDictLoaded
    L2_2 = "mp_arrest_paired"
    L1_2 = L1_2(L2_2)
    if L1_2 then
      break
    end
    L1_2 = Wait
    L2_2 = 1
    L1_2(L2_2)
  end
  L1_2 = RequestAnimDict
  L2_2 = "mp_arresting"
  L1_2(L2_2)
  while true do
    L1_2 = HasAnimDictLoaded
    L2_2 = "mp_arresting"
    L1_2 = L1_2(L2_2)
    if L1_2 then
      break
    end
    L1_2 = Wait
    L2_2 = 1
    L1_2(L2_2)
  end
  L1_2 = PlayerPedId
  L1_2 = L1_2()
  if A0_2 then
    L2_2 = TaskPlayAnim
    L3_2 = PlayerPedId
    L3_2 = L3_2()
    L4_2 = "mp_arrest_paired"
    L5_2 = "crook_p2_back_right"
    L6_2 = 8.0
    L7_2 = -8.0
    L8_2 = 3750
    L9_2 = 2
    L10_2 = 0
    L11_2 = 0
    L12_2 = 0
    L13_2 = 0
    L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
    L2_2 = Config
    L2_2 = L2_2.HandCuff
    L2_2 = L2_2.SkillCheck
    L2_2 = L2_2.enabled
    if not L2_2 then
      L2_2 = Wait
      L3_2 = 3500
      L2_2(L3_2)
      L2_2 = GetEntityCoords
      L3_2 = GetPlayerPed
      L4_2 = PlayerId
      L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L4_2()
      L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      L4_2 = false
      L2_2 = L2_2(L3_2, L4_2)
      L3_2 = CreateObject
      L4_2 = GetHashKey
      L5_2 = "p_cs_cuffs_02_s"
      L4_2 = L4_2(L5_2)
      L5_2 = L2_2.x
      L6_2 = L2_2.y
      L7_2 = L2_2.z
      L8_2 = true
      L9_2 = true
      L10_2 = true
      L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
      L4_1 = L3_2
      L3_2 = AttachEntityToEntity
      L4_2 = L4_1
      L5_2 = L1_2
      L6_2 = GetPedBoneIndex
      L7_2 = L1_2
      L8_2 = 60309
      L6_2 = L6_2(L7_2, L8_2)
      L7_2 = -0.02
      L8_2 = 0.06
      L9_2 = 0.03
      L10_2 = 290.0
      L11_2 = 155.0
      L12_2 = 80.0
      L13_2 = true
      L14_2 = false
      L15_2 = false
      L16_2 = false
      L17_2 = 0
      L18_2 = true
      L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      L3_2 = TaskPlayAnim
      L4_2 = PlayerPedId
      L4_2 = L4_2()
      L5_2 = "mp_arresting"
      L6_2 = "idle"
      L7_2 = 8.0
      L8_2 = -8.0
      L9_2 = -1
      L10_2 = 49
      L11_2 = 0.0
      L12_2 = false
      L13_2 = false
      L14_2 = false
      L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
      return
    end
    SkillSuccess = nil
    L2_2 = Citizen
    L2_2 = L2_2.CreateThread
    function L3_2()
      local L0_3, L1_3
      L0_3 = Config
      L0_3 = L0_3.HandCuff
      L0_3 = L0_3.SkillCheck
      L0_3 = L0_3.func
      L0_3 = L0_3()
      SkillSuccess = L0_3
    end
    L2_2(L3_2)
    L2_2 = Citizen
    L2_2 = L2_2.Wait
    L3_2 = Config
    L3_2 = L3_2.HandCuff
    L3_2 = L3_2.SkillCheck
    L3_2 = L3_2.WaitChance
    L2_2(L3_2)
    L2_2 = SkillSuccess
    if not L2_2 then
      L2_2 = GetEntityCoords
      L3_2 = GetPlayerPed
      L4_2 = PlayerId
      L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L4_2()
      L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      L4_2 = false
      L2_2 = L2_2(L3_2, L4_2)
      L3_2 = CreateObject
      L4_2 = GetHashKey
      L5_2 = "p_cs_cuffs_02_s"
      L4_2 = L4_2(L5_2)
      L5_2 = L2_2.x
      L6_2 = L2_2.y
      L7_2 = L2_2.z
      L8_2 = true
      L9_2 = true
      L10_2 = true
      L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
      L4_1 = L3_2
      L3_2 = AttachEntityToEntity
      L4_2 = L4_1
      L5_2 = L1_2
      L6_2 = GetPedBoneIndex
      L7_2 = L1_2
      L8_2 = 60309
      L6_2 = L6_2(L7_2, L8_2)
      L7_2 = -0.02
      L8_2 = 0.06
      L9_2 = 0.03
      L10_2 = 290.0
      L11_2 = 155.0
      L12_2 = 80.0
      L13_2 = true
      L14_2 = false
      L15_2 = false
      L16_2 = false
      L17_2 = 0
      L18_2 = true
      L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      L3_2 = TaskPlayAnim
      L4_2 = PlayerPedId
      L4_2 = L4_2()
      L5_2 = "mp_arresting"
      L6_2 = "idle"
      L7_2 = 8.0
      L8_2 = -8.0
      L9_2 = -1
      L10_2 = 49
      L11_2 = 0.0
      L12_2 = false
      L13_2 = false
      L14_2 = false
      L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
    else
      L2_2 = false
      L7_1 = L2_2
      L2_2 = ClearPedTasks
      L3_2 = PlayerPedId
      L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L3_2()
      L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    end
  else
    L2_2 = L4_1
    if nil ~= L2_2 then
      L2_2 = DetachEntity
      L3_2 = L4_1
      L4_2 = true
      L5_2 = true
      L2_2(L3_2, L4_2, L5_2)
      L2_2 = DeleteEntity
      L3_2 = L4_1
      L2_2(L3_2)
      L2_2 = nil
      L4_1 = L2_2
    end
    L2_2 = TaskPlayAnim
    L3_2 = PlayerPedId
    L3_2 = L3_2()
    L4_2 = "mp_arresting"
    L5_2 = "b_uncuff"
    L6_2 = 8.0
    L7_2 = -8.0
    L8_2 = -1
    L9_2 = 2
    L10_2 = 0
    L11_2 = 0
    L12_2 = 0
    L13_2 = 0
    L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
    L2_2 = Citizen
    L2_2 = L2_2.Wait
    L3_2 = 3500
    L2_2(L3_2)
    L2_2 = ClearPedTasks
    L3_2 = PlayerPedId
    L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L3_2()
    L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
  end
end
L8_1(L9_1, L10_1)
L8_1 = exports
L9_1 = "RemoveHandCuff"
function L10_1()
  local L0_2, L1_2, L2_2, L3_2
  L0_2 = false
  L7_1 = L0_2
  L0_2 = L4_1
  if nil ~= L0_2 then
    L0_2 = DetachEntity
    L1_2 = L4_1
    L2_2 = true
    L3_2 = true
    L0_2(L1_2, L2_2, L3_2)
    L0_2 = DeleteEntity
    L1_2 = L4_1
    L0_2(L1_2)
    L0_2 = nil
    L4_1 = L0_2
  end
  L0_2 = ClearPedTasks
  L1_2 = PlayerPedId
  L1_2, L2_2, L3_2 = L1_2()
  L0_2(L1_2, L2_2, L3_2)
end
L8_1(L9_1, L10_1)
L8_1 = RegisterNetEvent
L9_1 = "origen_police:client:cuffuncuff"
function L10_1()
  local L0_2, L1_2
  L0_2 = origen_police_cuffuncuff
  L0_2()
end
L8_1(L9_1, L10_1)
function L8_1()
  local L0_2, L1_2, L2_2
  L0_2 = IsPedInAnyVehicle
  L1_2 = PlayerPedId
  L1_2 = L1_2()
  L2_2 = false
  L0_2 = L0_2(L1_2, L2_2)
  if L0_2 then
    return
  end
  L0_2 = handCuff
  function L1_2(A0_3)
    local L1_3, L2_3, L3_3, L4_3, L5_3, L6_3
    if A0_3 then
      L1_3 = FW_GetClosestPlayer
      L1_3, L2_3 = L1_3()
      if -1 ~= L1_3 and L2_3 <= 3.0 and L2_3 > 0 then
        L3_3 = L2_1
        if not L3_3 then
          L3_3 = TriggerServerEvent
          L4_3 = "origen_police:server:cuffuncuffplayer"
          L5_3 = GetPlayerServerId
          L6_3 = L1_3
          L5_3, L6_3 = L5_3(L6_3)
          L3_3(L4_3, L5_3, L6_3)
        else
          L3_3 = ShowNotification
          L4_3 = Config
          L4_3 = L4_3.Translations
          L4_3 = L4_3.CantUncuff
          L5_3 = "error"
          L3_3(L4_3, L5_3)
        end
      else
        L3_3 = ShowNotification
        L4_3 = Config
        L4_3 = L4_3.Translations
        L4_3 = L4_3.NoPersonNear
        L5_3 = "error"
        L3_3(L4_3, L5_3)
      end
    else
      L1_3 = ShowNotification
      L2_3 = Config
      L2_3 = L2_3.Translations
      L2_3 = L2_3.CantDoThis
      L3_3 = "error"
      L1_3(L2_3, L3_3)
    end
  end
  L0_2(L1_2)
end
origen_police_cuffuncuff = L8_1
function L8_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L0_2 = FW_GetClosestPlayer
  L0_2, L1_2 = L0_2()
  if -1 ~= L0_2 and L1_2 <= 3.0 and L1_2 > 0 then
    L2_2 = GetPlayerPed
    L3_2 = L0_2
    L2_2 = L2_2(L3_2)
    L3_2 = IsEntityAttachedToEntity
    L4_2 = L2_2
    L5_2 = PlayerPedId
    L5_2, L6_2, L7_2, L8_2 = L5_2()
    L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2)
    L3_2 = not L3_2
    if L3_2 then
      L4_2 = IsEntityPlayingAnim
      L5_2 = L2_2
      L6_2 = "mp_arresting"
      L7_2 = "idle"
      L8_2 = 3
      L4_2 = L4_2(L5_2, L6_2, L7_2, L8_2)
      if L4_2 then
        goto lbl_31
      end
    end
    L4_2 = L2_1
    ::lbl_31::
    if L4_2 then
      L4_2 = TriggerServerEvent
      L5_2 = "origen_police:server:dragplayer"
      L6_2 = GetPlayerServerId
      L7_2 = L0_2
      L6_2 = L6_2(L7_2)
      L7_2 = L3_2
      L4_2(L5_2, L6_2, L7_2)
      if L3_2 then
        L4_2 = GetPlayerServerId
        L5_2 = L0_2
        L4_2 = L4_2(L5_2)
        if L4_2 then
          goto lbl_46
        end
      end
      L4_2 = false
      ::lbl_46::
      L2_1 = L4_2
      L4_2 = Citizen
      L4_2 = L4_2.CreateThread
      function L5_2()
        local L0_3, L1_3, L2_3, L3_3, L4_3, L5_3, L6_3, L7_3, L8_3, L9_3, L10_3
        L0_3 = RequestAnimDict
        L1_3 = "amb@world_human_drinking@coffee@male@base"
        L0_3(L1_3)
        while true do
          L0_3 = HasAnimDictLoaded
          L1_3 = "amb@world_human_drinking@coffee@male@base"
          L0_3 = L0_3(L1_3)
          if L0_3 then
            break
          end
          L0_3 = Citizen
          L0_3 = L0_3.Wait
          L1_3 = 10
          L0_3(L1_3)
        end
        while true do
          L0_3 = L2_1
          if not L0_3 then
            break
          end
          L0_3 = DoesEntityExist
          L1_3 = L2_2
          L0_3 = L0_3(L1_3)
          if not L0_3 then
            break
          end
          L0_3 = IsPedOnFoot
          L1_3 = L2_2
          L0_3 = L0_3(L1_3)
          if not L0_3 then
            break
          end
          L0_3 = IsPedDeadOrDying
          L1_3 = L2_2
          L2_3 = true
          L0_3 = L0_3(L1_3, L2_3)
          if L0_3 then
            break
          end
          L0_3 = Citizen
          L0_3 = L0_3.Wait
          L1_3 = 100
          L0_3(L1_3)
          L0_3 = IsEntityPlayingAnim
          L1_3 = PlayerPedId
          L1_3 = L1_3()
          L2_3 = "amb@world_human_drinking@coffee@male@base"
          L3_3 = "base"
          L4_3 = 3
          L0_3 = L0_3(L1_3, L2_3, L3_3, L4_3)
          if not L0_3 then
            L0_3 = TaskPlayAnim
            L1_3 = PlayerPedId
            L1_3 = L1_3()
            L2_3 = "amb@world_human_drinking@coffee@male@base"
            L3_3 = "base"
            L4_3 = 8.0
            L5_3 = 8.0
            L6_3 = -1
            L7_3 = 49
            L8_3 = false
            L9_3 = false
            L10_3 = false
            L0_3(L1_3, L2_3, L3_3, L4_3, L5_3, L6_3, L7_3, L8_3, L9_3, L10_3)
          end
        end
        L0_3 = false
        L2_1 = L0_3
        L0_3 = ClearPedTasks
        L1_3 = PlayerPedId
        L1_3, L2_3, L3_3, L4_3, L5_3, L6_3, L7_3, L8_3, L9_3, L10_3 = L1_3()
        L0_3(L1_3, L2_3, L3_3, L4_3, L5_3, L6_3, L7_3, L8_3, L9_3, L10_3)
      end
      L4_2(L5_2)
    else
      L4_2 = ShowNotification
      L5_2 = Config
      L5_2 = L5_2.Translations
      L5_2 = L5_2.HasToBeCuffed
      L6_2 = "error"
      L4_2(L5_2, L6_2)
    end
  else
    L2_2 = ShowNotification
    L3_2 = Config
    L3_2 = L3_2.Translations
    L3_2 = L3_2.NoPersonNear
    L4_2 = "error"
    L2_2(L3_2, L4_2)
  end
end
origen_police_drag = L8_1
function L8_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2
  L1_1 = A0_2
  L2_2 = L1_1
  if L2_2 then
    L2_2 = false
    L3_2 = PlayerPedId
    L3_2 = L3_2()
    L4_2 = GetPlayerFromServerId
    L5_2 = A1_2
    L4_2 = L4_2(L5_2)
    L5_2 = GetPlayerPed
    L6_2 = L4_2
    L5_2 = L5_2(L6_2)
    L6_2 = AttachEntityToEntity
    L7_2 = L3_2
    L8_2 = L5_2
    L9_2 = 11816
    L10_2 = 0.28
    L11_2 = 0.43
    L12_2 = 0.0
    L13_2 = 0.0
    L14_2 = 0.0
    L15_2 = 0.0
    L16_2 = false
    L17_2 = false
    L18_2 = false
    L19_2 = false
    L20_2 = 2
    L21_2 = true
    L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2)
    L6_2 = Citizen
    L6_2 = L6_2.CreateThread
    function L7_2()
      local L0_3, L1_3, L2_3, L3_3, L4_3, L5_3, L6_3, L7_3, L8_3, L9_3, L10_3, L11_3, L12_3, L13_3, L14_3, L15_3
      while true do
        L0_3 = L1_1
        if not L0_3 then
          break
        end
        L0_3 = DoesEntityExist
        L1_3 = L5_2
        L0_3 = L0_3(L1_3)
        if not L0_3 then
          break
        end
        L0_3 = IsPedOnFoot
        L1_3 = L5_2
        L0_3 = L0_3(L1_3)
        if not L0_3 then
          break
        end
        L0_3 = IsPedDeadOrDying
        L1_3 = L5_2
        L2_3 = true
        L0_3 = L0_3(L1_3, L2_3)
        if L0_3 then
          break
        end
        L0_3 = Citizen
        L0_3 = L0_3.Wait
        L1_3 = 100
        L0_3(L1_3)
        L0_3 = PlayerPedId
        L0_3 = L0_3()
        L3_2 = L0_3
        L0_3 = GetPlayerPed
        L1_3 = L4_2
        L0_3 = L0_3(L1_3)
        L5_2 = L0_3
        L0_3 = IsEntityAttachedToEntity
        L1_3 = L3_2
        L2_3 = L5_2
        L0_3 = L0_3(L1_3, L2_3)
        if not L0_3 then
          L0_3 = AttachEntityToEntity
          L1_3 = L3_2
          L2_3 = L5_2
          L3_3 = 11816
          L4_3 = 0.28
          L5_3 = 0.43
          L6_3 = 0.0
          L7_3 = 0.0
          L8_3 = 0.0
          L9_3 = 0.0
          L10_3 = false
          L11_3 = false
          L12_3 = false
          L13_3 = false
          L14_3 = 2
          L15_3 = true
          L0_3(L1_3, L2_3, L3_3, L4_3, L5_3, L6_3, L7_3, L8_3, L9_3, L10_3, L11_3, L12_3, L13_3, L14_3, L15_3)
        end
        L0_3 = GetEntitySpeed
        L1_3 = L5_2
        L0_3 = L0_3(L1_3)
        L1_3 = 0.1
        if L0_3 > L1_3 then
          L0_3 = true
          L2_2 = L0_3
          L0_3 = TaskGoStraightToCoord
          L1_3 = L3_2
          L2_3 = GetEntityCoords
          L3_3 = L3_2
          L2_3 = L2_3(L3_3)
          L3_3 = GetEntityForwardVector
          L4_3 = L3_2
          L3_3 = L3_3(L4_3)
          L3_3 = L3_3 * 5.0
          L2_3 = L2_3 + L3_3
          L3_3 = GetEntitySpeed
          L4_3 = L5_2
          L3_3 = L3_3(L4_3)
          if L3_3 > 2.0 then
            L3_3 = 2.0
            if L3_3 then
              goto lbl_83
            end
          end
          L3_3 = 1.0
          ::lbl_83::
          L4_3 = -1
          L5_3 = GetEntityHeading
          L6_3 = L3_2
          L5_3 = L5_3(L6_3)
          L6_3 = 0.0
          L0_3(L1_3, L2_3, L3_3, L4_3, L5_3, L6_3)
        else
          L0_3 = L2_2
          if L0_3 then
            L0_3 = false
            L2_2 = L0_3
            L0_3 = ClearPedTasks
            L1_3 = L3_2
            L0_3(L1_3)
          end
        end
      end
      L0_3 = false
      L1_1 = L0_3
      L0_3 = DetachEntity
      L1_3 = PlayerPedId
      L1_3 = L1_3()
      L2_3 = true
      L3_3 = false
      L0_3(L1_3, L2_3, L3_3)
      L0_3 = ClearPedTasks
      L1_3 = L3_2
      L0_3(L1_3)
    end
    L6_2(L7_2)
  else
    L2_2 = DetachEntity
    L3_2 = PlayerPedId
    L3_2 = L3_2()
    L4_2 = true
    L5_2 = false
    L2_2(L3_2, L4_2, L5_2)
  end
end
origen_police_setdragstate = L8_1
L8_1 = RegisterNetEvent
L9_1 = "origen_police:client:setdragstate"
function L10_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2
  L2_2 = origen_police_setdragstate
  L3_2 = A0_2
  L4_2 = A1_2
  L2_2(L3_2, L4_2)
end
L8_1(L9_1, L10_1)
L8_1 = RegisterNetEvent
L9_1 = "origen_police:client:drag"
function L10_1()
  local L0_2, L1_2
  L0_2 = origen_police_drag
  L0_2()
end
L8_1(L9_1, L10_1)
function L8_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  L0_2 = FW_GetClosestPlayer
  L0_2, L1_2 = L0_2()
  L2_2 = FW_GetClosestVehicle
  L2_2 = L2_2()
  L3_2 = FW_GetPlayerData
  L4_2 = false
  L3_2 = L3_2(L4_2)
  L4_2 = CanOpenTablet
  L5_2 = L3_2.job
  L5_2 = L5_2.name
  L4_2 = L4_2(L5_2)
  L4_2 = L4_2[1]
  if L4_2 then
    L4_2 = L3_2.job
    L4_2 = L4_2.onduty
    if L4_2 then
      goto lbl_20
    end
  end
  do return end
  ::lbl_20::
  if -1 ~= L0_2 and L1_2 <= 3.0 and L1_2 > 0 then
    L4_2 = GetEntityCoords
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    L5_2 = GetEntityCoords
    L6_2 = PlayerPedId
    L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L6_2()
    L5_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
    L4_2 = L4_2 - L5_2
    L4_2 = #L4_2
    if L4_2 < 5 then
      L4_2 = L2_1
      if L4_2 then
        L4_2 = false
        L2_1 = L4_2
        L4_2 = ClearPedTasks
        L5_2 = PlayerPedId
        L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L5_2()
        L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
        L4_2 = TriggerServerEvent
        L5_2 = "origen_police:server:dragplayer"
        L6_2 = GetPlayerServerId
        L7_2 = L0_2
        L6_2 = L6_2(L7_2)
        L7_2 = false
        L4_2(L5_2, L6_2, L7_2)
      end
      L4_2 = IsPedInAnyVehicle
      L5_2 = GetPlayerPed
      L6_2 = L0_2
      L5_2 = L5_2(L6_2)
      L6_2 = false
      L4_2 = L4_2(L5_2, L6_2)
      if not L4_2 then
        L4_2 = GetVehicleModelNumberOfSeats
        L5_2 = GetEntityModel
        L6_2 = L2_2
        L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L5_2(L6_2)
        L4_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
        L4_2 = L4_2 - 2
        L5_2 = 0
        L6_2 = L4_2
        L7_2 = 1
        for L8_2 = L5_2, L6_2, L7_2 do
          L9_2 = IsVehicleSeatFree
          L10_2 = L2_2
          L11_2 = L4_2 - L8_2
          L9_2 = L9_2(L10_2, L11_2)
          if L9_2 then
            L9_2 = UseCommand
            L10_2 = "me"
            L11_2 = Config
            L11_2 = L11_2.Translations
            L11_2 = L11_2.VehicleinofME
            L9_2(L10_2, L11_2)
            L9_2 = TriggerServerEvent
            L10_2 = "origen_police:server:vehicleinof"
            L11_2 = GetPlayerServerId
            L12_2 = L0_2
            L11_2 = L11_2(L12_2)
            L12_2 = NetworkGetNetworkIdFromEntity
            L13_2 = L2_2
            L12_2 = L12_2(L13_2)
            L13_2 = L4_2 - L8_2
            L9_2(L10_2, L11_2, L12_2, L13_2)
            return
          end
        end
        L5_2 = ShowNotification
        L6_2 = Config
        L6_2 = L6_2.Translations
        L6_2 = L6_2.noSeat
        L5_2(L6_2)
      else
        L4_2 = UseCommand
        L5_2 = "me"
        L6_2 = Config
        L6_2 = L6_2.Translations
        L6_2 = L6_2.VehicleofinME
        L4_2(L5_2, L6_2)
        L4_2 = TriggerServerEvent
        L5_2 = "origen_police:server:vehicleinof"
        L6_2 = GetPlayerServerId
        L7_2 = L0_2
        L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L6_2(L7_2)
        L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
      end
  end
  else
    L4_2 = ShowNotification
    L5_2 = Config
    L5_2 = L5_2.Translations
    L5_2 = L5_2.NoPersonNear
    L4_2(L5_2)
  end
end
origen_police_vehicleinof = L8_1
function L8_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  if A0_2 then
    L2_2 = TaskWarpPedIntoVehicle
    L3_2 = PlayerPedId
    L3_2 = L3_2()
    L4_2 = NetworkGetEntityFromNetworkId
    L5_2 = A0_2
    L4_2 = L4_2(L5_2)
    L5_2 = A1_2
    L2_2(L3_2, L4_2, L5_2)
  else
    L2_2 = TaskLeaveVehicle
    L3_2 = PlayerPedId
    L3_2 = L3_2()
    L4_2 = GetVehiclePedIsIn
    L5_2 = PlayerPedId
    L5_2 = L5_2()
    L6_2 = false
    L4_2 = L4_2(L5_2, L6_2)
    L5_2 = 16
    L2_2(L3_2, L4_2, L5_2)
    L2_2 = L7_1
    if L2_2 then
      L2_2 = Wait
      L3_2 = 100
      L2_2(L3_2)
      L2_2 = TaskPlayAnim
      L3_2 = PlayerPedId
      L3_2 = L3_2()
      L4_2 = "mp_arresting"
      L5_2 = "idle"
      L6_2 = 8.0
      L7_2 = 8.0
      L8_2 = -1
      L9_2 = 51
      L10_2 = false
      L11_2 = false
      L12_2 = false
      L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
    end
  end
end
origen_police_statevehicleinof = L8_1
L8_1 = RegisterCommand
L9_1 = "vehicleinof"
function L10_1()
  local L0_2, L1_2
  L0_2 = origen_police_vehicleinof
  L0_2()
end
L8_1(L9_1, L10_1)
L8_1 = RegisterNetEvent
L9_1 = "origen_police:client:vehicleinof"
function L10_1()
  local L0_2, L1_2
  L0_2 = origen_police_vehicleinof
  L0_2()
end
L8_1(L9_1, L10_1)
L8_1 = RegisterNetEvent
L9_1 = "origen_police:client:statevehicleinof"
function L10_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2
  L2_2 = origen_police_statevehicleinof
  L3_2 = A0_2
  L4_2 = A1_2
  L2_2(L3_2, L4_2)
end
L8_1(L9_1, L10_1)
function L8_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2
  L0_2 = FW_GetPlayerData
  L1_2 = false
  L0_2 = L0_2(L1_2)
  L1_2 = CanOpenTablet
  L2_2 = L0_2.job
  L2_2 = L2_2.name
  L1_2 = L1_2(L2_2)
  L1_2 = L1_2[1]
  if L1_2 then
    L1_2 = L0_2.job
    L1_2 = L1_2.onduty
    if L1_2 then
      goto lbl_16
    end
  end
  do return end
  ::lbl_16::
  L1_2 = FW_GetClosestPlayer
  L1_2, L2_2 = L1_2()
  if -1 ~= L1_2 and L2_2 <= 3.0 and L2_2 > 0 then
    L3_2 = CanSearchPlayer
    L4_2 = L1_2
    L3_2 = L3_2(L4_2)
    if L3_2 then
      L3_2 = GetPlayerServerId
      L4_2 = L1_2
      L3_2 = L3_2(L4_2)
      L4_2 = SearchClosestPlayer
      L5_2 = L3_2
      L4_2(L5_2)
    else
      L3_2 = ShowNotification
      L4_2 = Config
      L4_2 = L4_2.Translations
      L4_2 = L4_2.NotCuffed
      L5_2 = "error"
      L3_2(L4_2, L5_2)
    end
  else
    L3_2 = ShowNotification
    L4_2 = Config
    L4_2 = L4_2.Translations
    L4_2 = L4_2.NoPersonNear
    L5_2 = "error"
    L3_2(L4_2, L5_2)
  end
end
SearchClosest = L8_1
L8_1 = RegisterCommand
L9_1 = "cachearPolice"
L10_1 = SearchClosest
L8_1(L9_1, L10_1)
function L8_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L1_2 = Citizen
  L1_2 = L1_2.Wait
  L2_2 = 300
  L1_2(L2_2)
  while true do
    L1_2 = Citizen
    L1_2 = L1_2.Wait
    L2_2 = 50
    L1_2(L2_2)
    L1_2 = GetPlayerFromServerId
    L2_2 = A0_2
    L1_2 = L1_2(L2_2)
    L2_2 = GetPlayerPed
    L3_2 = L1_2
    L2_2 = L2_2(L3_2)
    L3_2 = GetEntityCoords
    L4_2 = L2_2
    L3_2 = L3_2(L4_2)
    L4_2 = GetEntityCoords
    L5_2 = PlayerPedId
    L5_2, L6_2, L7_2 = L5_2()
    L4_2 = L4_2(L5_2, L6_2, L7_2)
    L5_2 = inInventory
    if false == L5_2 then
      break
    end
    L5_2 = L3_2 - L4_2
    L5_2 = #L5_2
    if L5_2 > 4 then
      L5_2 = TriggerEvent
      L6_2 = Config
      L6_2 = L6_2.Inventory
      L7_2 = ":closeInv"
      L6_2 = L6_2 .. L7_2
      L5_2(L6_2)
      L5_2 = TriggerEvent
      L6_2 = Config
      L6_2 = L6_2.Inventory
      L7_2 = ":client:closeInventory"
      L6_2 = L6_2 .. L7_2
      L5_2(L6_2)
      L5_2 = ShowNotification
      L6_2 = Config
      L6_2 = L6_2.Translations
      L6_2 = L6_2.PersonFar
      L7_2 = "error"
      L5_2(L6_2, L7_2)
      break
    end
  end
end
StartSearchDistance = L8_1
L8_1 = exports
L9_1 = "IsHandcuffed"
function L10_1()
  local L0_2, L1_2
  L0_2 = L7_1
  return L0_2
end
L8_1(L9_1, L10_1)
L8_1 = AddReplaceExport
L9_1 = "qb-policejob"
L10_1 = "IsHandcuffed"
function L11_1()
  local L0_2, L1_2
  L0_2 = L7_1
  return L0_2
end
L8_1(L9_1, L10_1, L11_1)
function L8_1(A0_2)
  local L1_2, L2_2
  while true do
    L1_2 = HasAnimDictLoaded
    L2_2 = A0_2
    L1_2 = L1_2(L2_2)
    if L1_2 then
      break
    end
    L1_2 = RequestAnimDict
    L2_2 = A0_2
    L1_2(L2_2)
    L1_2 = Citizen
    L1_2 = L1_2.Wait
    L2_2 = 5
    L1_2(L2_2)
  end
end
function L9_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L0_2 = L5_1
  if L0_2 then
    L0_2 = L8_1
    L1_2 = "move_strafe@first_person@generic"
    L0_2(L1_2)
    L0_2 = TaskPlayAnim
    L1_2 = PlayerPedId
    L1_2 = L1_2()
    L2_2 = "move_strafe@first_person@generic"
    L3_2 = "walk_bwd_180_loop"
    L4_2 = 3.0
    L5_2 = 1.0
    L6_2 = -1
    L7_2 = 1
    L8_2 = 0
    L9_2 = 0
    L10_2 = 0
    L11_2 = 0
    L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
  end
end
function L10_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L0_2 = L5_1
  if L0_2 then
    L0_2 = L8_1
    L1_2 = "move_strafe@first_person@generic"
    L0_2(L1_2)
    L0_2 = TaskPlayAnim
    L1_2 = PlayerPedId
    L1_2 = L1_2()
    L2_2 = "move_strafe@first_person@generic"
    L3_2 = "exit"
    L4_2 = 3.0
    L5_2 = 1.0
    L6_2 = -1
    L7_2 = 1
    L8_2 = 0
    L9_2 = 0
    L10_2 = 0
    L11_2 = 0
    L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
  end
end
L11_1 = RegisterNetEvent
L12_1 = "origen_police:client:getDragged"
function L13_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2
  L2_2 = GetPlayerPed
  L3_2 = GetPlayerFromServerId
  L4_2 = A0_2
  L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2 = L3_2(L4_2)
  L2_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2)
  L3_2 = PlayerPedId
  L3_2 = L3_2()
  if "escort" == A1_2 then
    L4_2 = L6_1
    if not L4_2 then
      L4_2 = ClearPedTasks
      L5_2 = L3_2
      L4_2(L5_2)
      L4_2 = true
      L6_1 = L4_2
      L4_2 = L8_1
      L5_2 = "mp_arresting"
      L4_2(L5_2)
      L4_2 = L8_1
      L5_2 = "move_m@generic_variations@walk"
      L4_2(L5_2)
      L4_2 = TaskPlayAnim
      L5_2 = L3_2
      L6_2 = "mp_arresting"
      L7_2 = "idle"
      L8_2 = 8.0
      L9_2 = -8
      L10_2 = -1
      L11_2 = 49
      L12_2 = 0.0
      L13_2 = false
      L14_2 = false
      L15_2 = false
      L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
      L4_2 = AttachEntityToEntity
      L5_2 = L3_2
      L6_2 = L2_2
      L7_2 = 1816
      L8_2 = 0.25
      L9_2 = 0.49
      L10_2 = 0.0
      L11_2 = 0.0
      L12_2 = 0.0
      L13_2 = 0.0
      L14_2 = false
      L15_2 = false
      L16_2 = false
      L17_2 = false
      L18_2 = 2
      L19_2 = true
      L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2)
      L4_2 = amBeingEscorted
      L5_2 = L2_2
      L4_2(L5_2)
    else
      L4_2 = false
      L6_1 = L4_2
      L4_2 = DetachEntity
      L5_2 = L3_2
      L4_2(L5_2)
      L4_2 = ClearPedTasks
      L5_2 = L3_2
      L4_2(L5_2)
    end
  elseif "stopdrag" == A1_2 then
    L4_2 = false
    L6_1 = L4_2
    L4_2 = DetachEntity
    L5_2 = L3_2
    L4_2(L5_2)
    L4_2 = ClearPedTasks
    L5_2 = L3_2
    L4_2(L5_2)
  else
    L4_2 = L6_1
    if not L4_2 then
      L4_2 = true
      L6_1 = L4_2
      L4_2 = ClearPedTasks
      L5_2 = L3_2
      L4_2(L5_2)
      L4_2 = L8_1
      L5_2 = "combat@drag_ped@"
      L4_2(L5_2)
      L4_2 = TaskPlayAnim
      L5_2 = L3_2
      L6_2 = "combat@drag_ped@"
      L7_2 = "injured_drag_ped"
      L8_2 = 8.0
      L9_2 = -8
      L10_2 = -1
      L11_2 = 5
      L12_2 = 0
      L13_2 = false
      L14_2 = false
      L15_2 = false
      L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
      L4_2 = AttachEntityToEntity
      L5_2 = L3_2
      L6_2 = L2_2
      L7_2 = 1816
      L8_2 = 4103
      L9_2 = 0.48
      L10_2 = 0.0
      L11_2 = 0.0
      L12_2 = 0.0
      L13_2 = 0.0
      L14_2 = 0.0
      L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
    else
      L4_2 = false
      L6_1 = L4_2
      L4_2 = DetachEntity
      L5_2 = L3_2
      L4_2(L5_2)
      L4_2 = ClearPedTasks
      L5_2 = L3_2
      L4_2(L5_2)
    end
  end
end
L11_1(L12_1, L13_1)
L11_1 = RegisterNetEvent
L12_1 = "origen_police:client:doAnimation"
function L13_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  L1_2 = PlayerPedId
  L1_2 = L1_2()
  if "escort" == A0_2 then
    L2_2 = isEscorting
    if L2_2 then
      L2_2 = ClearPedTasks
      L3_2 = L1_2
      L2_2(L3_2)
      isEscorting = false
    else
      isEscorting = true
      L2_2 = L8_1
      L3_2 = "amb@world_human_drinking@coffee@male@base"
      L2_2(L3_2)
      L2_2 = IsEntityPlayingAnim
      L3_2 = L1_2
      L4_2 = "amb@world_human_drinking@coffee@male@base"
      L5_2 = "base"
      L6_2 = 3
      L2_2 = L2_2(L3_2, L4_2, L5_2, L6_2)
      if 1 ~= L2_2 then
        L2_2 = TaskPlayAnim
        L3_2 = L1_2
        L4_2 = "amb@world_human_drinking@coffee@male@base"
        L5_2 = "base"
        L6_2 = 8.0
        L7_2 = -8
        L8_2 = -1
        L9_2 = 51
        L10_2 = 0
        L11_2 = false
        L12_2 = false
        L13_2 = false
        L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
      end
    end
  elseif "stopdrag" == A0_2 then
    L2_2 = ClearPedTasks
    L3_2 = L1_2
    L2_2(L3_2)
  else
    L2_2 = L5_1
    if L2_2 then
      L2_2 = ClearPedTasks
      L3_2 = L1_2
      L2_2(L3_2)
      L2_2 = false
      L5_1 = L2_2
    else
      L2_2 = true
      L5_1 = L2_2
      L2_2 = L8_1
      L3_2 = "combat@drag_ped@"
      L2_2(L3_2)
      L2_2 = IsEntityPlayingAnim
      L3_2 = L1_2
      L4_2 = "combat@drag_ped@"
      L5_2 = "injured_drag_plyr"
      L6_2 = 3
      L2_2 = L2_2(L3_2, L4_2, L5_2, L6_2)
      if 1 ~= L2_2 then
        L2_2 = TaskPlayAnim
        L3_2 = L1_2
        L4_2 = "combat@drag_ped@"
        L5_2 = "injured_drag_plyr"
        L6_2 = 8.0
        L7_2 = -8
        L8_2 = -1
        L9_2 = 51
        L10_2 = 0
        L11_2 = false
        L12_2 = false
        L13_2 = false
        L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
      end
    end
  end
end
L11_1(L12_1, L13_1)
L11_1 = RegisterCommand
L12_1 = "drag"
function L13_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2
  L0_2 = FW_GetPlayerData
  L1_2 = false
  L0_2 = L0_2(L1_2)
  L1_2 = CanOpenTablet
  L2_2 = L0_2.job
  L2_2 = L2_2.name
  L1_2 = L1_2(L2_2)
  L1_2 = L1_2[1]
  if L1_2 then
    L1_2 = L0_2.job
    L1_2 = L1_2.onduty
    if L1_2 then
      goto lbl_16
    end
  end
  do return end
  ::lbl_16::
  L1_2 = FW_GetClosestPlayer
  L1_2, L2_2 = L1_2()
  if -1 ~= L1_2 and L2_2 <= 3.0 then
    L3_2 = TriggerServerEvent
    L4_2 = "origen_police:server:attachPlayer"
    L5_2 = GetPlayerServerId
    L6_2 = L1_2
    L5_2 = L5_2(L6_2)
    L6_2 = "drag"
    L3_2(L4_2, L5_2, L6_2)
  else
    L3_2 = ShowNotification
    L4_2 = Config
    L4_2 = L4_2.Translations
    L4_2 = L4_2.NoPersonNear
    L5_2 = "error"
    L3_2(L4_2, L5_2)
  end
end
L14_1 = false
L11_1(L12_1, L13_1, L14_1)
L11_1 = RegisterCommand
L12_1 = "+walkBackwards"
L13_1 = L9_1
L14_1 = false
L11_1(L12_1, L13_1, L14_1)
L11_1 = RegisterCommand
L12_1 = "-walkBackwards"
L13_1 = L10_1
L14_1 = false
L11_1(L12_1, L13_1, L14_1)
L11_1 = RegisterCommand
L12_1 = "StopWalkDrag"
function L13_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2
  L0_2 = L5_1
  if L0_2 then
    L0_2 = ClearPedTasks
    L1_2 = PlayerPedId
    L1_2, L2_2, L3_2, L4_2, L5_2 = L1_2()
    L0_2(L1_2, L2_2, L3_2, L4_2, L5_2)
    L0_2 = false
    L5_1 = L0_2
    L0_2 = FW_GetClosestPlayer
    L0_2, L1_2 = L0_2()
    if -1 ~= L0_2 and L1_2 <= 3.0 then
      L2_2 = TriggerServerEvent
      L3_2 = "origen_police:server:attachPlayer"
      L4_2 = GetPlayerServerId
      L5_2 = L0_2
      L4_2 = L4_2(L5_2)
      L5_2 = "stopdrag"
      L2_2(L3_2, L4_2, L5_2)
    end
  end
end
L14_1 = false
L11_1(L12_1, L13_1, L14_1)
L11_1 = Config
L11_1 = L11_1.Commands
L11_1 = L11_1.HandCuff
if L11_1 then
  L11_1 = RegisterKeyMapping
  L12_1 = "esposar"
  L13_1 = Config
  L13_1 = L13_1.Commands
  L13_1 = L13_1.HandCuff
  L13_1 = L13_1.description
  L14_1 = "keyboard"
  L15_1 = Config
  L15_1 = L15_1.Commands
  L15_1 = L15_1.HandCuff
  L15_1 = L15_1.key
  L11_1(L12_1, L13_1, L14_1, L15_1)
end
L11_1 = Config
L11_1 = L11_1.Commands
L11_1 = L11_1.QRR
if L11_1 then
  L11_1 = RegisterKeyMapping
  L12_1 = "qrr"
  L13_1 = Config
  L13_1 = L13_1.Commands
  L13_1 = L13_1.QRR
  L13_1 = L13_1.description
  L14_1 = "keyboard"
  L15_1 = Config
  L15_1 = L15_1.Commands
  L15_1 = L15_1.QRR
  L15_1 = L15_1.key
  L11_1(L12_1, L13_1, L14_1, L15_1)
end
L11_1 = Config
L11_1 = L11_1.Commands
L11_1 = L11_1.Ten20
if L11_1 then
  L11_1 = RegisterKeyMapping
  L12_1 = "10-20"
  L13_1 = Config
  L13_1 = L13_1.Commands
  L13_1 = L13_1.Ten20
  L13_1 = L13_1.description
  L14_1 = "keyboard"
  L15_1 = Config
  L15_1 = L15_1.Commands
  L15_1 = L15_1.Ten20
  L15_1 = L15_1.key
  L11_1(L12_1, L13_1, L14_1, L15_1)
end
L11_1 = Config
L11_1 = L11_1.Commands
L11_1 = L11_1.Tackle
if L11_1 then
  L11_1 = RegisterKeyMapping
  L12_1 = "placaje"
  L13_1 = Config
  L13_1 = L13_1.Commands
  L13_1 = L13_1.Tackle
  L13_1 = L13_1.description
  L14_1 = "keyboard"
  L15_1 = Config
  L15_1 = L15_1.Commands
  L15_1 = L15_1.Tackle
  L15_1 = L15_1.key
  L11_1(L12_1, L13_1, L14_1, L15_1)
end
L11_1 = Config
L11_1 = L11_1.Commands
L11_1 = L11_1.VehicleInto
if L11_1 then
  L11_1 = RegisterKeyMapping
  L12_1 = "vehicleinof"
  L13_1 = Config
  L13_1 = L13_1.Commands
  L13_1 = L13_1.VehicleInto
  L13_1 = L13_1.description
  L14_1 = "keyboard"
  L15_1 = Config
  L15_1 = L15_1.Commands
  L15_1 = L15_1.VehicleInto
  L15_1 = L15_1.key
  L11_1(L12_1, L13_1, L14_1, L15_1)
end
L11_1 = RegisterKeyMapping
L12_1 = "+walkBackwards"
L13_1 = "Walk Backwards"
L14_1 = "keyboard"
L15_1 = "S"
L11_1(L12_1, L13_1, L14_1, L15_1)
L11_1 = RegisterKeyMapping
L12_1 = "StopWalkDrag"
L13_1 = "Cancel drag"
L14_1 = "keyboard"
L15_1 = "X"
L11_1(L12_1, L13_1, L14_1, L15_1)
L11_1 = exports
L12_1 = "origenPoliceCuff"
L13_1 = origen_police_cuffuncuff
L11_1(L12_1, L13_1)
L11_1 = exports
L12_1 = "origenPoliceDrag"
L13_1 = origen_police_drag
L11_1(L12_1, L13_1)
L11_1 = exports
L12_1 = "origenPoliceVehicleInof"
L13_1 = origen_police_vehicleinof
L11_1(L12_1, L13_1)
