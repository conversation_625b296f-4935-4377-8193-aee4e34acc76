import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { usePhone } from '../contexts/PhoneContext';
import { useTheme } from '../contexts/ThemeContext';
import { useNotifications } from '../contexts/NotificationContext';
import { formatTime } from '../utils/misc';
import './HomeScreen.css';

const HomeScreen: React.FC = () => {
  const { phoneData, openApp } = usePhone();
  const { colors } = useTheme();
  const { unreadCount } = useNotifications();
  const [currentPage, setCurrentPage] = useState(0);

  const apps = [
    { id: 'contacts', name: '<PERSON><PERSON><PERSON><PERSON>', icon: 'fas fa-address-book', color: '#34C759' },
    { id: 'messages', name: '<PERSON>pr<PERSON><PERSON>', icon: 'fas fa-sms', color: '#007AFF', badge: unreadCount },
    { id: 'calls', name: '<PERSON><PERSON><PERSON>', icon: 'fas fa-phone', color: '#34C759' },
    { id: 'camera', name: 'F<PERSON><PERSON><PERSON><PERSON><PERSON>', icon: 'fas fa-camera', color: '#8E8E93' },
    { id: 'gallery', name: 'Galerie', icon: 'fas fa-images', color: '#FF9500' },
    { id: 'gps', name: 'GPS', icon: 'fas fa-map-marked-alt', color: '#007AFF' },
    { id: 'settings', name: 'Nastavení', icon: 'fas fa-cog', color: '#8E8E93' },
    { id: 'social', name: 'Sociální síť', icon: 'fas fa-share-alt', color: '#FF3B30' },
    { id: 'marketplace', name: 'Marketplace', icon: 'fas fa-store', color: '#FF9500' },
    { id: 'banking', name: 'Banka', icon: 'fas fa-university', color: '#34C759' },
    { id: 'email', name: 'Email', icon: 'fas fa-envelope', color: '#007AFF' },
    { id: 'calendar', name: 'Kalendář', icon: 'fas fa-calendar', color: '#FF3B30' },
    { id: 'games', name: 'Hry', icon: 'fas fa-gamepad', color: '#5856D6' },
    { id: 'darkweb', name: 'DarkWeb', icon: 'fas fa-user-secret', color: '#8E8E93', hidden: true },
  ];

  const visibleApps = apps.filter(app => !app.hidden);
  const appsPerPage = 16;
  const totalPages = Math.ceil(visibleApps.length / appsPerPage);
  const currentApps = visibleApps.slice(currentPage * appsPerPage, (currentPage + 1) * appsPerPage);

  const wallpaper = phoneData?.settings.wallpaper || 'default.jpg';
  const currentTime = new Date();

  const handleAppClick = (appId: string) => {
    openApp(appId);
  };

  const handlePageChange = (direction: 'left' | 'right') => {
    if (direction === 'left' && currentPage > 0) {
      setCurrentPage(currentPage - 1);
    } else if (direction === 'right' && currentPage < totalPages - 1) {
      setCurrentPage(currentPage + 1);
    }
  };

  return (
    <motion.div
      className="home-screen"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      style={{
        backgroundImage: `url(/wallpapers/${wallpaper})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    >
      {/* Overlay */}
      <div className="home-overlay" />
      
      {/* Content */}
      <div className="home-content">
        {/* Time Widget */}
        <motion.div
          className="time-widget"
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          <div className="time-large">{formatTime(currentTime)}</div>
          <div className="date-small">{currentTime.toLocaleDateString('cs-CZ', { 
            weekday: 'long', 
            day: 'numeric', 
            month: 'long' 
          })}</div>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          className="quick-actions"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <div className="quick-action" onClick={() => openApp('camera')}>
            <i className="fas fa-camera" />
          </div>
          <div className="quick-action" onClick={() => openApp('messages')}>
            <i className="fas fa-sms" />
            {unreadCount > 0 && <div className="badge">{unreadCount}</div>}
          </div>
          <div className="quick-action" onClick={() => openApp('calls')}>
            <i className="fas fa-phone" />
          </div>
        </motion.div>

        {/* App Grid */}
        <motion.div
          className="app-grid-container"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          <div className="app-grid">
            {currentApps.map((app, index) => (
              <motion.div
                key={app.id}
                className="app-item"
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.5 + index * 0.05 }}
                onClick={() => handleAppClick(app.id)}
              >
                <div 
                  className="app-icon"
                  style={{ backgroundColor: app.color }}
                >
                  <i className={app.icon} />
                  {app.badge && app.badge > 0 && (
                    <div className="app-badge">{app.badge > 99 ? '99+' : app.badge}</div>
                  )}
                </div>
                <div className="app-name">{app.name}</div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Page Indicators */}
        {totalPages > 1 && (
          <div className="page-indicators">
            {[...Array(totalPages)].map((_, index) => (
              <div
                key={index}
                className={`page-dot ${index === currentPage ? 'active' : ''}`}
                onClick={() => setCurrentPage(index)}
                style={{
                  backgroundColor: index === currentPage ? colors.text : 'rgba(255, 255, 255, 0.5)'
                }}
              />
            ))}
          </div>
        )}

        {/* Dock */}
        <motion.div
          className="dock"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.6 }}
        >
          <div className="dock-item" onClick={() => openApp('contacts')}>
            <i className="fas fa-address-book" style={{ color: '#34C759' }} />
          </div>
          <div className="dock-item" onClick={() => openApp('messages')}>
            <i className="fas fa-sms" style={{ color: '#007AFF' }} />
            {unreadCount > 0 && <div className="dock-badge">{unreadCount}</div>}
          </div>
          <div className="dock-item" onClick={() => openApp('calls')}>
            <i className="fas fa-phone" style={{ color: '#34C759' }} />
          </div>
          <div className="dock-item" onClick={() => openApp('banking')}>
            <i className="fas fa-university" style={{ color: '#34C759' }} />
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default HomeScreen;
