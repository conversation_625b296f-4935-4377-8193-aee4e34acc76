{"name": "mississippi", "version": "3.0.0", "description": "a collection of useful streams", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "max ogden", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^3.0.0", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}, "devDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/maxogden/mississippi.git"}, "engines": {"node": ">=4.0.0"}, "bugs": {"url": "https://github.com/maxogden/mississippi/issues"}, "homepage": "https://github.com/maxogden/mississippi#readme"}