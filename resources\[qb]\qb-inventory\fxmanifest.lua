fx_version 'cerulean'
game 'gta5'
lua54 'yes'
author '<PERSON><PERSON><PERSON>'
description 'Player inventory system providing a variety of features for storing and managing items'
version '1.2.4'

shared_scripts {
    'locales/locale.lua',
    -- '@qb-core/shared/locale.lua',
    'data/*.lua',
    'locales/en.lua',
    'locales/*.lua',
    'shared/cores.lua',
    'shared/config.lua'
}

server_scripts {
    'data/*.lua',
	'@oxmysql/lib/MySQL.lua',
    'server/core.lua',
    'server/main.lua',
    'test_inventory.lua', -- Temporary test script
}

client_scripts{
    'data/*.lua',
    'client/*.lua',
}



ui_page {
    'html/ui.html'
}

files {
    'html/ui.html',
    'html/css/main.css',
    'html/js/app.js',
    'html/images/*.png',
    'html/images/*.jpg',
    'html/ammo_images/*.png',
    'html/attachment_images/*.png',
    'html/*.ttf',
    'data/*.lua',
}

-- dependency 'qb-weapons'
