{"name": "async", "description": "Higher-order functions and common patterns for asynchronous code", "version": "3.2.0", "main": "dist/async.js", "author": "<PERSON><PERSON>", "homepage": "https://caolan.github.io/async/", "repository": {"type": "git", "url": "https://github.com/caolan/async.git"}, "bugs": {"url": "https://github.com/caolan/async/issues"}, "keywords": ["async", "callback", "module", "utility"], "dependencies": {}, "devDependencies": {"babel-core": "^6.26.3", "babel-eslint": "^8.2.6", "babel-minify": "^0.5.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-istanbul": "^5.1.4", "babel-plugin-syntax-async-generators": "^6.13.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "babel-register": "^6.26.0", "babelify": "^8.0.0", "benchmark": "^2.1.1", "bluebird": "^3.4.6", "browserify": "^16.2.3", "chai": "^4.2.0", "cheerio": "^0.22.0", "coveralls": "^3.0.4", "es6-promise": "^2.3.0", "eslint": "^6.0.1", "eslint-plugin-prefer-arrow": "^1.1.5", "fs-extra": "^0.26.7", "jsdoc": "^3.6.2", "karma": "^4.1.0", "karma-browserify": "^5.3.0", "karma-edge-launcher": "^0.4.2", "karma-firefox-launcher": "^1.1.0", "karma-junit-reporter": "^1.2.0", "karma-mocha": "^1.2.0", "karma-mocha-reporter": "^2.2.0", "karma-safari-launcher": "^1.0.0", "mocha": "^6.1.4", "mocha-junit-reporter": "^1.18.0", "native-promise-only": "^0.8.0-a", "nyc": "^14.1.1", "rimraf": "^2.5.0", "rollup": "^0.63.4", "rollup-plugin-node-resolve": "^2.0.0", "rollup-plugin-npm": "^2.0.0", "rsvp": "^3.0.18", "semver": "^5.5.0", "yargs": "^11.0.0"}, "scripts": {"coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "lint": "eslint --fix lib/ test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "mocha-browser-test": "karma start", "mocha-node-test": "mocha", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "test": "npm run lint && npm run mocha-node-test"}, "license": "MIT", "nyc": {"exclude": ["test"]}, "module": "dist/async.mjs"}