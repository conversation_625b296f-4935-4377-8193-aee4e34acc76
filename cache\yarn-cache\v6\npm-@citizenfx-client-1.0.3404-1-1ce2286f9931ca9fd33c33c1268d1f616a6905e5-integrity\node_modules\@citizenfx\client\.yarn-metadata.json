{"manifest": {"name": "@citizenfx/client", "version": "1.0.3404-1", "description": "Typings for the CitizenFX client JS API.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": {}, "license": "ISC", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@citizenfx-client-1.0.3404-1-1ce2286f9931ca9fd33c33c1268d1f616a6905e5-integrity\\node_modules\\@citizenfx\\client\\package.json", "readmeFilename": "README.md", "readme": "# FiveM client typings"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@citizenfx/client/-/client-1.0.3404-1.tgz#1ce2286f9931ca9fd33c33c1268d1f616a6905e5", "type": "tarball", "reference": "https://registry.yarnpkg.com/@citizenfx/client/-/client-1.0.3404-1.tgz", "hash": "1ce2286f9931ca9fd33c33c1268d1f616a6905e5", "integrity": "sha512-k4DSNnlwU+R5FOPZ8KTIlU2jije1R9yEZxP8L5a437g7fF+0dyIR9x2oie73hA1O2xa9e0WyAUE9q3HtbMYaFg==", "registry": "npm", "packageName": "@citizenfx/client", "cacheIntegrity": "sha512-k4DSNnlwU+R5FOPZ8KTIlU2jije1R9yEZxP8L5a437g7fF+0dyIR9x2oie73hA1O2xa9e0WyAUE9q3HtbMYaFg== sha1-HOIob5kxyp/TPDPBJo0fYWppBeU="}, "registry": "npm", "hash": "1ce2286f9931ca9fd33c33c1268d1f616a6905e5"}