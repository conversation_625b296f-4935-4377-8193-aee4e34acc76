# Škoda Scala Police Car

## Popis
Policejní vozidlo Škoda Scala pro FiveM server s QBCore frameworkem.

## Informace o vozidle
- **Model**: scalapcr
- **Značka**: Škoda
- **Typ**: <PERSON><PERSON><PERSON> vozidlo
- **Třída**: Emergency Vehicle
- **Handling**: police3

## Instalace
1. Zkopírujte složku `skodascala` do složky `[cars]`
2. Přidejte `ensure skodascala` do vašeho `server.cfg`
3. Restartujte server

## Struktura složky
```
skodascala/
├── data/                    # Metadata soubory
│   ├── vehicles.meta       # Definice vozidla
│   ├── carcols.meta        # Barvy a materiály
│   ├── carvariations.meta  # Varianty vozidla
│   ├── handling.meta       # Jízdní vlastnosti
│   ├── vehiclelayouts.meta # Rozložení vozidla
│   ├── dlctext.meta        # Textové definice
│   ├── shop_vehicle.meta   # Obchodní nastavení
│   ├── content.xml         # Obsah DLC
│   └── setup2.xml          # Nastavení
├── stream/                  # 3D modely a textury
│   ├── scalapcr.yft        # 3D model vozidla
│   ├── scalapcr.ytd        # Textury vozidla
│   ├── scalapcr_hi.yft     # High-res model
│   └── skoda_scala.ytd     # Dodatečné textury
├── fxmanifest.lua          # FiveM manifest
└── README.md               # Tato dokumentace
```

## Spawn kód
```lua
-- Pro spawnutí vozidla použijte:
local vehicle = CreateVehicle(GetHashKey("scalapcr"), x, y, z, heading, true, false)
```

## Poznámky
- Vozidlo je určeno pro policejní použití
- Obsahuje policejní výbavu a zvuky
- Má speciální policejní livery

## Autor
DDCZ

## Verze
1.0.0
