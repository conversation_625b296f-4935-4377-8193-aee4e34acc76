{"manifest": {"name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "1.5.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "typings": "index.d.ts", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "devDependencies": {"babel-minify": "^0.5.1", "benchmark": "^2.1.4", "browserify": "^16.3.0", "standard": "*", "tape": "4.x"}, "homepage": "https://github.com/beatgammit/base64-js", "keywords": ["base64"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "scripts": {"build": "browserify -s base64js -r ./ | minify > base64js.min.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-base64-js-1.5.1-1b1b440160a5bf7ad40b650f095963481903930a-integrity\\node_modules\\base64-js\\package.json", "readmeFilename": "README.md", "readme": "base64-js\n=========\n\n`base64-js` does basic base64 encoding/decoding in pure JS.\n\n[![build status](https://secure.travis-ci.org/beatgammit/base64-js.png)](http://travis-ci.org/beatgammit/base64-js)\n\nMany browsers already have base64 encoding/decoding functionality, but it is for text data, not all-purpose binary data.\n\nSometimes encoding/decoding binary data in the browser is useful, and that is what this module does.\n\n## install\n\nWith [npm](https://npmjs.org) do:\n\n`npm install base64-js` and `var base64js = require('base64-js')`\n\nFor use in web browsers do:\n\n`<script src=\"base64js.min.js\"></script>`\n\n[Get supported base64-js with the Tidelift Subscription](https://tidelift.com/subscription/pkg/npm-base64-js?utm_source=npm-base64-js&utm_medium=referral&utm_campaign=readme)\n\n## methods\n\n`base64js` has three exposed functions, `byteLength`, `toByteArray` and `fromByteArray`, which both take a single argument.\n\n* `byteLength` - Takes a base64 string and returns length of byte array\n* `toByteArray` - Takes a base64 string and returns a byte array\n* `fromByteArray` - Takes a byte array and returns a base64 string\n\n## license\n\nMIT\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 Jameson Little\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/base64-js/-/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a", "type": "tarball", "reference": "https://registry.yarnpkg.com/base64-js/-/base64-js-1.5.1.tgz", "hash": "1b1b440160a5bf7ad40b650f095963481903930a", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "registry": "npm", "packageName": "base64-js", "cacheIntegrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA== sha1-GxtEAWClv3rUC2UPCVljSBkDkwo="}, "registry": "npm", "hash": "1b1b440160a5bf7ad40b650f095963481903930a"}