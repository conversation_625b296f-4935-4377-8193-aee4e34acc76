{"manifest": {"name": "collection-visit", "description": "Visit a method over the items in an object, or map visit over the objects in an array.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/collection-visit", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/collection-visit.git"}, "bugs": {"url": "https://github.com/jonschlinkert/collection-visit/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}, "devDependencies": {"clone-deep": "^0.2.4", "gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.12", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.0", "mocha": "^3.2.0"}, "keywords": ["array", "arrays", "collection", "context", "function", "helper", "invoke", "key", "map", "method", "object", "objects", "value", "visit", "visitor"], "verb": {"related": {"list": ["base-methods", "map-visit", "object-visit"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-collection-visit-1.0.0-4bc0373c164bc3291b4d368c829cf1a80a59dca0-integrity\\node_modules\\collection-visit\\package.json", "readmeFilename": "README.md", "readme": "# collection-visit [![NPM version](https://img.shields.io/npm/v/collection-visit.svg?style=flat)](https://www.npmjs.com/package/collection-visit) [![NPM monthly downloads](https://img.shields.io/npm/dm/collection-visit.svg?style=flat)](https://npmjs.org/package/collection-visit)  [![NPM total downloads](https://img.shields.io/npm/dt/collection-visit.svg?style=flat)](https://npmjs.org/package/collection-visit) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/collection-visit.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/collection-visit)\n\n> Visit a method over the items in an object, or map visit over the objects in an array.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save collection-visit\n```\n\n## Usage\n\n```js\nvar visit = require('collection-visit');\n\nvar ctx = {\n  data: {},\n  set: function (key, value) {\n    if (typeof key === 'object') {\n      visit(ctx, 'set', key);\n    } else {\n      ctx.data[key] = value;\n    }\n  }\n};\n\nctx.set('a', 'a');\nctx.set('b', 'b');\nctx.set('c', 'c');\nctx.set({d: {e: 'f'}});\n\nconsole.log(ctx.data);\n//=> {a: 'a', b: 'b', c: 'c', d: { e: 'f' }};\n```\n\n## About\n\n### Related projects\n\n* [base-methods](https://www.npmjs.com/package/base-methods): base-methods is the foundation for creating modular, unit testable and highly pluggable node.js applications, starting… [more](https://github.com/jonschlinkert/base-methods) | [homepage](https://github.com/jonschlinkert/base-methods \"base-methods is the foundation for creating modular, unit testable and highly pluggable node.js applications, starting with a handful of common methods, like `set`, `get`, `del` and `use`.\")\n* [map-visit](https://www.npmjs.com/package/map-visit): Map `visit` over an array of objects. | [homepage](https://github.com/jonschlinkert/map-visit \"Map `visit` over an array of objects.\")\n* [object-visit](https://www.npmjs.com/package/object-visit): Call a specified method on each value in the given object. | [homepage](https://github.com/jonschlinkert/object-visit \"Call a specified method on each value in the given object.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 13 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 9 | [doowb](https://github.com/doowb) |\n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.5.0, on April 09, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015, 2017, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/collection-visit/-/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0", "type": "tarball", "reference": "https://registry.yarnpkg.com/collection-visit/-/collection-visit-1.0.0.tgz", "hash": "4bc0373c164bc3291b4d368c829cf1a80a59dca0", "integrity": "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=", "registry": "npm", "packageName": "collection-visit", "cacheIntegrity": "sha512-lNkKvzEeMBBjUGHZ+q6z9pSJla0KWAQPvtzhEV9+iGyQYG+pBpl7xKDhxoNSOZH2hhv0v5k0y2yAM4o4SjoSkw== sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA="}, "registry": "npm", "hash": "4bc0373c164bc3291b4d368c829cf1a80a59dca0"}