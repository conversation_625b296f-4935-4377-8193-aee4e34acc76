{"manifest": {"name": "browserify-sign", "version": "4.2.1", "description": "adds node crypto signing for browsers", "bugs": {"url": "https://github.com/crypto-browserify/browserify-sign/issues"}, "license": "ISC", "files": ["browser", "index.js", "algos.js"], "main": "index.js", "repository": {"type": "git", "url": "https://github.com/crypto-browserify/browserify-sign.git"}, "scripts": {"coverage": "nyc npm run unit", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "dependencies": {"bn.js": "^5.1.1", "browserify-rsa": "^4.0.1", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "elliptic": "^6.5.3", "inherits": "^2.0.4", "parse-asn1": "^5.1.5", "readable-stream": "^3.6.0", "safe-buffer": "^5.2.0"}, "devDependencies": {"nyc": "^15.0.1", "standard": "^14.3.3", "tape": "^5.0.0"}, "browser": "browser/index.js", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-browserify-sign-4.2.1-eaf4add46dd54be3bb3b36c0cf15abbeba7956c3-integrity\\node_modules\\browserify-sign\\package.json", "readmeFilename": "README.md", "readme": "# browserify-sign\n\n[![NPM Package](https://img.shields.io/npm/v/browserify-sign.svg?style=flat-square)](https://www.npmjs.org/package/browserify-sign)\n[![Build Status](https://img.shields.io/travis/crypto-browserify/browserify-sign.svg?branch=master&style=flat-square)](https://travis-ci.org/crypto-browserify/browserify-sign)\n[![Dependency status](https://img.shields.io/david/crypto-browserify/browserify-sign.svg?style=flat-square)](https://david-dm.org/crypto-browserify/browserify-sign#info=dependencies)\n\n[![js-standard-style](https://cdn.rawgit.com/feross/standard/master/badge.svg)](https://github.com/feross/standard)\n\nA package to duplicate the functionality of node's crypto public key functions, much of this is based on [<PERSON><PERSON>du<PERSON>'s](https://github.com/indutny) work on [indutny/tls.js](https://github.com/indutny/tls.js).\n\n## LICENSE\n\nISC\n", "licenseText": "Copyright (c) 2014-2015 <PERSON> and browserify-sign contributors\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\nOR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/browserify-sign/-/browserify-sign-4.2.1.tgz#eaf4add46dd54be3bb3b36c0cf15abbeba7956c3", "type": "tarball", "reference": "https://registry.yarnpkg.com/browserify-sign/-/browserify-sign-4.2.1.tgz", "hash": "eaf4add46dd54be3bb3b36c0cf15abbeba7956c3", "integrity": "sha512-/vrA5fguVAKKAVTNJjgSm1tRQDHUU6DbwO9IROu/0WAzC8PKhucDSh18J0RMvVeHAn5puMd+QHC2erPRNf8lmg==", "registry": "npm", "packageName": "browserify-sign", "cacheIntegrity": "sha512-/vrA5fguVAKKAVTNJjgSm1tRQDHUU6DbwO9IROu/0WAzC8PKhucDSh18J0RMvVeHAn5puMd+QHC2erPRNf8lmg== sha1-6vSt1G3VS+O7OzbAzxWrvrp5VsM="}, "registry": "npm", "hash": "eaf4add46dd54be3bb3b36c0cf15abbeba7956c3"}