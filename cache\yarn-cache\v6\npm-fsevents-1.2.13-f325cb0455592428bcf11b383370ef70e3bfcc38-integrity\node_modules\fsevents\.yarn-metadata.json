{"manifest": {"name": "fsevents", "version": "1.2.13", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1"}, "os": ["darwin"], "engines": {"node": ">= 4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node install.js"}, "repository": {"type": "git", "url": "https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-fsevents-1.2.13-f325cb0455592428bcf11b383370ef70e3bfcc38-integrity\\node_modules\\fsevents\\package.json", "readmeFilename": "Readme.md", "readme": "# fsevents [![NPM](https://nodei.co/npm/fsevents.png)](https://nodei.co/npm/fsevents/)\n\nNative access to OS X FSEvents in [Node.js](http://nodejs.org/)\n\nThe FSEvents API in OS X allows applications to register for notifications of\nchanges to a given directory tree. It is a very fast and lightweight alternative\nto kqueue.\n\nThis is a low-level library. For a cross-compatible file watching module that\nuses fsevents, check out [<PERSON><PERSON><PERSON>](https://www.npmjs.com/package/chokidar).\n\n* [Module Site & GitHub](https://github.com/strongloop/fsevents)\n* [NPM Page](https://npmjs.org/package/fsevents)\n\n## Installation\n\n\t$ npm install fsevents\n\n## Usage\n\n```js\nvar fsevents = require('fsevents');\nvar watcher = fsevents(__dirname);\nwatcher.on('fsevent', function(path, flags, id) { }); // RAW Event as emitted by OS-X\nwatcher.on('change', function(path, info) { }); // Common Event for all changes\nwatcher.start() // To start observation\nwatcher.stop()  // To end observation\n```\n\n### Events\n\n * *fsevent* - RAW Event as emitted by OS-X\n * *change* - Common Event for all changes\n * *created* - A File-System-Item has been created\n * *deleted* - A File-System-Item has been deleted\n * *modified* - A File-System-Item has been modified\n * *moved-out* - A File-System-Item has been moved away from this location\n * *moved-in* - A File-System-Item has been moved into this location\n\nAll events except *fsevent* take an *info* object as the second parameter of the callback. The structure of this object is:\n\n```js\n{\n  \"event\": \"<event-type>\",\n  \"id\": <eventi-id>,\n  \"path\": \"<path-that-this-is-about>\",\n  \"type\": \"<file|directory|symlink>\",\n  \"changes\": {\n    \"inode\": true, // Has the iNode Meta-Information changed\n    \"finder\": false, // Has the Finder Meta-Data changed\n    \"access\": false, // Have the access permissions changed\n    \"xattrs\": false // Have the xAttributes changed\n  },\n  \"flags\": <raw-flags>\n}\n```\n\n## MIT License\n\nCopyright (C) 2010-2014 Philipp Dunkel\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "licenseText": "MIT License\n-----------\n\nCopyright (C) 2010-2014 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/fsevents/-/fsevents-1.2.13.tgz#f325cb0455592428bcf11b383370ef70e3bfcc38", "type": "tarball", "reference": "https://registry.yarnpkg.com/fsevents/-/fsevents-1.2.13.tgz", "hash": "f325cb0455592428bcf11b383370ef70e3bfcc38", "integrity": "sha512-oWb1Z6mkHIskLzEJ/XWX0srkpkTQ7vaopMQkyaEIoq0fmtFVxOthb8cCxeT+p3ynTdkk/RZwbgG4brR5BeWECw==", "registry": "npm", "packageName": "fsevents", "cacheIntegrity": "sha512-oWb1Z6mkHIskLzEJ/XWX0srkpkTQ7vaopMQkyaEIoq0fmtFVxOthb8cCxeT+p3ynTdkk/RZwbgG4brR5BeWECw== sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg="}, "registry": "npm", "hash": "f325cb0455592428bcf11b383370ef70e3bfcc38"}