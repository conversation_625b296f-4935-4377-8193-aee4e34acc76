import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNotifications } from '../contexts/NotificationContext';
import { useTheme } from '../contexts/ThemeContext';
import { formatRelativeTime } from '../utils/misc';
import './NotificationCenter.css';

const NotificationCenter: React.FC = () => {
  const { notifications, markAsRead, removeNotification, clearNotifications } = useNotifications();
  const { colors } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  const handleNotificationClick = (notification: any) => {
    markAsRead(notification.id);
    
    // Handle notification action based on app
    switch (notification.app) {
      case 'messages':
        // Open messages app
        break;
      case 'calls':
        // Open calls app
        break;
      case 'social':
        // Open social app
        break;
      default:
        break;
    }
  };

  const getNotificationIcon = (app: string) => {
    switch (app) {
      case 'messages':
        return 'fas fa-sms';
      case 'calls':
        return 'fas fa-phone';
      case 'social':
        return 'fas fa-share-alt';
      case 'banking':
        return 'fas fa-university';
      case 'marketplace':
        return 'fas fa-store';
      case 'darkweb':
        return 'fas fa-user-secret';
      case 'email':
        return 'fas fa-envelope';
      default:
        return 'fas fa-bell';
    }
  };

  const getNotificationColor = (app: string) => {
    switch (app) {
      case 'messages':
        return '#007AFF';
      case 'calls':
        return '#34C759';
      case 'social':
        return '#FF3B30';
      case 'banking':
        return '#34C759';
      case 'marketplace':
        return '#FF9500';
      case 'darkweb':
        return '#8E8E93';
      case 'email':
        return '#007AFF';
      default:
        return colors.primary;
    }
  };

  return (
    <>
      {/* Notification Banner */}
      <AnimatePresence>
        {notifications.slice(0, 1).map((notification) => (
          <motion.div
            key={notification.id}
            className="notification-banner"
            initial={{ y: -100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -100, opacity: 0 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            onClick={() => handleNotificationClick(notification)}
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border
            }}
          >
            <div 
              className="notification-icon"
              style={{ backgroundColor: getNotificationColor(notification.app) }}
            >
              <i className={getNotificationIcon(notification.app)} />
            </div>
            <div className="notification-content">
              <div className="notification-title" style={{ color: colors.text }}>
                {notification.title}
              </div>
              <div className="notification-message" style={{ color: colors.textSecondary }}>
                {notification.message}
              </div>
            </div>
            <div className="notification-time" style={{ color: colors.textSecondary }}>
              {formatRelativeTime(notification.timestamp)}
            </div>
            <button
              className="notification-close"
              onClick={(e) => {
                e.stopPropagation();
                removeNotification(notification.id);
              }}
              style={{ color: colors.textSecondary }}
            >
              <i className="fas fa-times" />
            </button>
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Notification Center */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="notification-center"
            initial={{ y: '-100%' }}
            animate={{ y: 0 }}
            exit={{ y: '-100%' }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            style={{ backgroundColor: colors.background }}
          >
            <div className="notification-center-header">
              <h2 style={{ color: colors.text }}>Notifikace</h2>
              <div className="notification-center-actions">
                {notifications.length > 0 && (
                  <button
                    className="clear-all-btn"
                    onClick={clearNotifications}
                    style={{ color: colors.primary }}
                  >
                    Vymazat vše
                  </button>
                )}
                <button
                  className="close-btn"
                  onClick={() => setIsOpen(false)}
                  style={{ color: colors.textSecondary }}
                >
                  <i className="fas fa-times" />
                </button>
              </div>
            </div>

            <div className="notification-list">
              {notifications.length === 0 ? (
                <div className="no-notifications" style={{ color: colors.textSecondary }}>
                  <i className="fas fa-bell-slash" />
                  <p>Žádné notifikace</p>
                </div>
              ) : (
                notifications.map((notification) => (
                  <motion.div
                    key={notification.id}
                    className={`notification-item ${!notification.read ? 'unread' : ''}`}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    onClick={() => handleNotificationClick(notification)}
                    style={{
                      backgroundColor: !notification.read ? colors.surface : 'transparent',
                      borderColor: colors.border
                    }}
                  >
                    <div 
                      className="notification-item-icon"
                      style={{ backgroundColor: getNotificationColor(notification.app) }}
                    >
                      <i className={getNotificationIcon(notification.app)} />
                    </div>
                    <div className="notification-item-content">
                      <div className="notification-item-header">
                        <span 
                          className="notification-item-title"
                          style={{ color: colors.text }}
                        >
                          {notification.title}
                        </span>
                        <span 
                          className="notification-item-time"
                          style={{ color: colors.textSecondary }}
                        >
                          {formatRelativeTime(notification.timestamp)}
                        </span>
                      </div>
                      <div 
                        className="notification-item-message"
                        style={{ color: colors.textSecondary }}
                      >
                        {notification.message}
                      </div>
                    </div>
                    <button
                      className="notification-item-remove"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeNotification(notification.id);
                      }}
                      style={{ color: colors.textSecondary }}
                    >
                      <i className="fas fa-times" />
                    </button>
                  </motion.div>
                ))
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Pull down indicator */}
      <div 
        className="notification-pull-indicator"
        onClick={() => setIsOpen(!isOpen)}
        style={{ backgroundColor: colors.textSecondary }}
      />
    </>
  );
};

export default NotificationCenter;
