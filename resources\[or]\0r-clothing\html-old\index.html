<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="stylesheet" href="style.css" />
        <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.2/css/all.css">
        <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.2/css/sharp-thin.css">
        <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.2/css/sharp-solid.css">
        <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.2/css/sharp-regular.css">
        <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.2/css/sharp-light.css">
        <script src="nui://game/ui/jquery.js" type="text/javascript"></script>
    </head>
    <body>
        <div id="mainDivDialog">
            <h4 class="are_you_sure"></h4>
            <span id="mainDivDialogSpan" class="confirm_character_creation"></span>
            <div id="mainDivDialogButtons">
                <div class="mainDivDialogButtonGreen" onclick="finalizeCharacter()"></div>
                <div class="mainDivDialogButtonRed" onclick="discardCharacterCreation()"></div>
                <div class="mainDivDialogButtonRed" onclick="closeDialog()"></div>
            </div>
        </div>
        <div id="mainDivEffect"></div>
        <div class="mainDiv" id="mainDiv-CharacterCreationMenu" style="display: none;">
            <div id="mainDivTop">
                <div id="mainDivTopTop">
                    <div id="mainDivTopTextDiv">
                        <h4 id="mainText2" style="color: #FFF; font-family: ttfirs; font-size: 1.45vw;"></h4>
                        <span class="character_creator_description" style="color: rgba(255, 255, 255, 0.41); font-family: 'Open Sans', serif; font-size: 0.665vw; font-weight: 700;">CREATE YOUR UNIQUE CHARACTER EXACTLY HOW YOU IMAGINE IT NOW</span>
                    </div>
                </div>
            </div>
            <div class="mainDivBottom">
                <div id="mainDivBottomLeft">
                    <div id="mainDivBottomLeftBottom">
                        <!-- Peds -->
                        <div class="mainDivBottomLeftBottomInside" id="mainDivBottomLeftBottomInside-Peds" style="display: none;">
                            <div class="mainDivBottomLeftBottomDiv" id="MalePeds" style="width: 98%; max-height: 13.75vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="male_peds"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-MalePeds"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-MalePeds"></div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" id="FemalePeds" style="width: 98%; max-height: 13.75vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="female_peds"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-FemalePeds"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-FemalePeds"></div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" id="PedModelInput" style="height: fit-content; width: 98%;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="custom_ped_input"></h4>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" style="overflow: hidden;">
                                    <input class="mainDivBottomLeftBottomDivBottomInput" id="mainDivBottomLeftBottomDivBottomInput-Ped" type="text">
                                </div>
                            </div>
                        </div>
                        <!-- Face -->
                        <div class="mainDivBottomLeftBottomInside" id="mainDivBottomLeftBottomInside-Face" style="display: none;">
                            <!-- <div id="mainDivBottomLeftBottomDivTitle"><span>Randomize Face</span></div> -->
                            <div class="mainDivBottomLeftBottomInsideOutDiv" id="mainDivBottomLeftBottomInsideOutDiv-FaceOne" style="width: 93%;">
                                <div class="mainDivBottomLeftBottomDiv" id="FaceOne" style="max-height: 13.75vh;">
                                    <div id="mainDivBottomLeftBottomDivTop">
                                        <div id="mainDivBottomLeftBottomDivTopInside">
                                            <h4 class="face_one"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-FaceOne"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-FaceOne"></div>
                                </div>
                                <div class="mainDivBottomLeftBottomDiv" id="SkinOne" style="max-height: 13.75vh;">
                                    <div id="mainDivBottomLeftBottomDivTop">
                                        <div id="mainDivBottomLeftBottomDivTopInside">
                                            <h4 class="skin_one"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-SkinOne"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-SkinOne"></div>
                                </div>
                            </div>
                            <!-- Two -->
                            <div class="mainDivBottomLeftBottomInsideOutDiv" id="mainDivBottomLeftBottomInsideOutDiv-FaceTwo">
                                <div class="mainDivBottomLeftBottomDiv" id="FaceTwo" style="max-height: 13.75vh;">
                                    <div id="mainDivBottomLeftBottomDivTop">
                                        <div id="mainDivBottomLeftBottomDivTopInside">
                                            <h4 class="face_two"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-FaceTwo"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-FaceTwo"></div>
                                </div>
                                <div class="mainDivBottomLeftBottomDiv" id="SkinTwo" style="max-height: 13.75vh;">
                                    <div id="mainDivBottomLeftBottomDivTop">
                                        <div id="mainDivBottomLeftBottomDivTopInside">
                                            <h4 class="skin_two"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-SkinTwo"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-SkinTwo"></div>
                                </div>
                            </div>
                            <!-- Three -->
                            <div class="mainDivBottomLeftBottomInsideOutDiv" id="mainDivBottomLeftBottomInsideOutDiv-FaceThree">
                                <div class="mainDivBottomLeftBottomDiv" id="FaceThree" style="max-height: 13.75vh;">
                                    <div id="mainDivBottomLeftBottomDivTop">
                                        <div id="mainDivBottomLeftBottomDivTopInside">
                                            <h4 class="face_three"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-FaceThree"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-FaceThree"></div>
                                </div>
                                <div class="mainDivBottomLeftBottomDiv" id="SkinThree" style="max-height: 13.75vh;">
                                    <div id="mainDivBottomLeftBottomDivTop">
                                        <div id="mainDivBottomLeftBottomDivTopInside">
                                            <h4 class="skin_three"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-SkinThree"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-SkinThree"></div>
                                </div>
                            </div>
                            <!-- Mixer Inputs -->
                            <div class="mainDivBottomLeftBottomDiv">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="face_mix"></h4>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="padding-top: 0.6vw; padding-bottom: 0.6vw;">
                                    <input class="mainDivBottomLeftBottomDivBottomInputSlider" type="range" min="1" max="100" value="0" id="mainDivBottomLeftBottomDivBottomInputSlider-FaceMix">
                                </div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="skin_mix"></h4>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="padding-top: 0.6vw; padding-bottom: 0.6vw;">
                                    <input class="mainDivBottomLeftBottomDivBottomInputSlider" type="range" min="1" max="100" value="100" id="mainDivBottomLeftBottomDivBottomInputSlider-SkinMix">
                                </div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="third_mix"></h4>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="padding-top: 0.6vw; padding-bottom: 0.6vw;">
                                    <input class="mainDivBottomLeftBottomDivBottomInputSlider" type="range" min="1" max="100" value="0" id="mainDivBottomLeftBottomDivBottomInputSlider-ThirdMix">
                                </div>
                            </div>
                        </div>
                        <!-- Face Feat. -->
                        <div class="mainDivBottomLeftBottomInside" id="mainDivBottomLeftBottomInside-FaceFeatures" style="display: none; gap: 0.15vw;">
                            <!-- Nose -->
                            <div class="mainDivBottomLeftBottomDiv" style="gap: 0.1vw; max-height: 18vh; width: 98%;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="nose"></h4>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="flex-wrap: wrap; background: transparent; border: none; justify-content: space-between; gap: 0.4vw;">
                                    <!-- Line 1 -->
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="bone_height"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="NosePeak-Slider">
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="width"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="NoseWidth-Slider">
                                    </div>
                                    <!-- Line 2 -->
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="peak_length"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="NoseLength-Slider">
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="peak_height"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="NoseTip-Slider">
                                    </div>
                                    <!-- Line 3 -->
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="bone_twist"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="NoseBoneTwist-Slider">
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="peak_lowering"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="NoseBoneCurveness-Slider">
                                    </div>
                                </div>
                            </div>
                            <!-- Eyebrows -->
                            <div class="mainDivBottomLeftBottomDiv" style="gap: 0.1vw; width: 98%;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="eyebrows"></h4>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="flex-wrap: wrap; background: transparent; border: none; justify-content: space-between; gap: 0.4vw;">
                                    <!-- Line 1 -->
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="eyebrow_depth"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="EyebrowDepth-Slider">
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="eyebrow_height"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="EyebrowHeight-Slider">
                                    </div>
                                </div>
                            </div>
                            <!-- Cheeks -->
                            <div class="mainDivBottomLeftBottomDiv" style="gap: 0.1vw; max-height: 18vh; width: 98%;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="cheeks"></h4>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="flex-wrap: wrap; background: transparent; border: none; justify-content: space-between; gap: 0.4vw;">
                                    <!-- Line 1 -->
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="bone_width"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="CheekBoneWidth-Slider">
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="bone_height"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="CheekBoneHeight-Slider">
                                    </div>
                                    <!-- Line 2 -->
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%;">
                                        <span class="bone_width"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="CheekBoneWidth2-Slider">
                                    </div>
                                </div>
                            </div>
                            <!-- Jaw Bone -->
                            <div class="mainDivBottomLeftBottomDiv" style="gap: 0.1vw; width: 98%;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="jaw_bone"></h4>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="flex-wrap: wrap; background: transparent; border: none; justify-content: space-between; gap: 0.4vw;">
                                    <!-- Line 1 -->
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="bone_length"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="JawBoneLength-Slider">
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="bone_width"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="JawBoneWidth-Slider">
                                    </div>
                                </div>
                            </div>
                            <!-- Chin -->
                            <div class="mainDivBottomLeftBottomDiv" style="gap: 0.1vw; width: 98%;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="chin"></h4>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="flex-wrap: wrap; background: transparent; border: none; justify-content: space-between; gap: 0.4vw;">
                                    <!-- Line 1 -->
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="bone_length"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="ChinBoneLength-Slider">
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="bone_height"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="ChinBoneHeight-Slider">
                                    </div>
                                    <!-- Line 2 -->
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="chin_cleft"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="ChinCleft-Slider">
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="bone_width"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="ChinBoneWidth-Slider">
                                    </div>
                                </div>
                            </div>
                            <!-- Miscellaneous Features -->
                            <div class="mainDivBottomLeftBottomDiv" style="gap: 0.1vw; max-height: 18vh; width: 98%;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="misc_features"></h4>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="flex-wrap: wrap; background: transparent; border: none; justify-content: space-between; gap: 0.4vw;">
                                    <!-- Line 1 -->
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="lips_thickness"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="LipsThickness-Slider">
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2">
                                        <span class="eyes_squint"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="EyesSquint-Slider">
                                    </div>
                                    <!-- Line 2 -->
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%;">
                                        <span class="neck_thickness"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" type="range" min="1" max="100" value="50" id="NeckThickness-Slider">
                                    </div>
                                </div>
                            </div>
                            <!-- Eye Color -->
                            <div class="mainDivBottomLeftBottomDiv" id="EyeColor" style="width: 98%; max-height: 13.75vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="eye_color"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-EyeColor"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-EyeColor"></div>
                            </div>
                        </div>
                        <!-- Skin -->
                        <div class="mainDivBottomLeftBottomInside" id="mainDivBottomLeftBottomInside-Skin" style="display: none;">
                            <!-- Blemishes -->
                            <div class="mainDivBottomLeftBottomDiv" id="Blemishes" style="width: 98%; max-height: 13.75vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="blemishes"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Blemishes"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Blemishes"></div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                    <span class="opacity"></span>
                                    <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="Blemishes-OpacitySlider" type="range" min="1" max="100" value="80">
                                </div>
                            </div>
                            <!-- Ageing -->
                            <div class="mainDivBottomLeftBottomDiv" id="Ageing" style="width: 98%; max-height: 13.75vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="ageing"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Ageing"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Ageing"></div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                    <span class="opacity"></span>
                                    <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="Ageing-OpacitySlider" type="range" min="1" max="100" value="80">
                                </div>
                            </div>
                            <!-- Complexion -->
                            <div class="mainDivBottomLeftBottomDiv" id="Complexion" style="width: 98%; max-height: 13.75vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="complexion"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Complexion"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Complexion"></div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                    <span class="opacity"></span>
                                    <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="Complexion-OpacitySlider" type="range" min="1" max="100" value="80">
                                </div>
                            </div>
                            <!-- Sun Damage -->
                            <div class="mainDivBottomLeftBottomDiv" id="SunDamage" style="width: 98%; max-height: 13.75vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="sun_damage"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-SunDamage"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-SunDamage"></div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                    <span class="opacity"></span>
                                    <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="SunDamage-OpacitySlider" type="range" min="1" max="100" value="80">
                                </div>
                            </div>
                            <!-- Moles/Freckles -->
                            <div class="mainDivBottomLeftBottomDiv" id="MolesFreckles" style="width: 98%; max-height: 13.75vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="moles_freckles"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-MolesFreckles"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-MolesFreckles"></div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                    <span class="opacity"></span>
                                    <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="MolesFreckles-OpacitySlider" type="range" min="1" max="100" value="80">
                                </div>
                            </div>
                            <!-- Chest Hair -->
                            <div class="mainDivBottomLeftBottomDiv" id="ChestHair" style="width: 98%; max-height: 13.75vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="chest_hair"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-ChestHair"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-ChestHair"></div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                    <span class="opacity"></span>
                                    <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="ChestHair-OpacitySlider" type="range" min="1" max="100" value="80">
                                </div>
                            </div>
                            <!-- Body Blemishes -->
                            <div class="mainDivBottomLeftBottomDiv" id="BodyBlemishes" style="width: 98%; max-height: 13.75vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="body_blemishes"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-BodyBlemishes"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-BodyBlemishes"></div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                    <span class="opacity"></span>
                                    <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="BodyBlemishes-OpacitySlider" type="range" min="1" max="100" value="80">
                                </div>
                            </div>
                            <!-- Add Body Blemishes -->
                            <div class="mainDivBottomLeftBottomDiv" id="AddBodyBlemishes" style="width: 98%; max-height: 13.75vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="add_body_blemishes"></h4>
                                        <!-- <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-AddBodyBlemishes"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div> -->
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-AddBodyBlemishes"></div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                    <span class="opacity"></span>
                                    <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="AddBodyBlemishes-OpacitySlider" type="range" min="1" max="100" value="80">
                                </div>
                            </div>
                        </div>
                        <!-- Hair -->
                        <div class="mainDivBottomLeftBottomInside" id="mainDivBottomLeftBottomInside-Hair" style="display: none;">
                            <!-- Eyebrows -->
                            <div class="mainDivBottomLeftBottomDiv" id="Eyebrows" style="width: 98%; max-height: 13.75vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="eyebrows"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Eyebrows"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Eyebrows"></div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                    <span class="opacity"></span>
                                    <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="Eyebrows-OpacitySlider" type="range" min="1" max="100" value="80">
                                </div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" id="EyebrowColors" style="width: 98%; max-height: 53.5vh;">
                                <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                    <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                        <h4 style="font-size: 0.65vw;" class="color"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-EyebrowColors"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-EyebrowColors" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                </div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" id="EyebrowHighlightColors" style="width: 98%; max-height: 53.5vh;">
                                <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                    <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                        <h4 style="font-size: 0.65vw;" class="highlight_color"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-EyebrowHighlightColors"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-EyebrowHighlightColors" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                </div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" id="FacialHairs" style="width: 98%; max-height: 13.75vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="facial_hairs"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-FacialHairs"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-FacialHairs"></div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                    <span class="opacity"></span>
                                    <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="FacialHairs-OpacitySlider" type="range" min="1" max="100" value="80">
                                </div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" id="FacialHairsColors" style="width: 98%; max-height: 53.5vh;">
                                <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                    <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                        <h4 style="font-size: 0.65vw;" class="facial_hairs_colors"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-FacialHairsColors"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-FacialHairsColors" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                </div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" id="FacialHairsHighlightColors" style="width: 98%; max-height: 53.5vh;">
                                <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                    <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                        <h4 style="font-size: 0.65vw;" class="highlight_color"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-FacialHairsHighlightColors"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-FacialHairsHighlightColors" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                </div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" id="Hairs" style="width: 98%; max-height: 13.75vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="hairs"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Hairs"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Hairs"></div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" style="gap: 0.1vw; max-height: 18vh; width: 98%;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="hair_texture"></h4>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 100%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                        <div class="MDBLBDBEDButton" style="width: 5.5%;" onclick="changeHairTexture('left')"><i class="fa-regular fa-chevron-left"></i></div>
                                        <span style="width: 79%;" id="hairTextureNumSpan">0</span>
                                        <div class="MDBLBDBEDButton" style="width: 5.5%;" onclick="changeHairTexture('right')"><i class="fa-regular fa-chevron-right"></i></div>
                                    </div>
                                </div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" style="gap: 0.1vw; max-height: 18vh; width: 98%;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="hair_fade"></h4>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 100%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                        <div class="MDBLBDBEDButton" style="width: 5.5%;" onclick="changeHairFade('left')"><i class="fa-regular fa-chevron-left"></i></div>
                                        <span style="width: 79%;" id="hairFadeNumSpan">0</span>
                                        <div class="MDBLBDBEDButton" style="width: 5.5%;" onclick="changeHairFade('right')"><i class="fa-regular fa-chevron-right"></i></div>
                                    </div>
                                </div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" id="HairsColors" style="width: 98%; max-height: 53.5vh;">
                                <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                    <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                        <h4 style="font-size: 0.65vw;" class="hairs_colors"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-HairsColors"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-HairsColors" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                </div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" id="HairsHighlightColors" style="width: 98%; max-height: 53.5vh;">
                                <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                    <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                        <h4 style="font-size: 0.65vw;" class="highlight_color"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-HairsHighlightColors"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-HairsHighlightColors" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                </div>
                            </div>
                        </div>
                        <!-- Makeup -->
                        <div class="mainDivBottomLeftBottomInside" id="mainDivBottomLeftBottomInside-Makeup" style="display: none;">
                            <!-- Makeup -->
                            <div class="mainDivBottomLeftBottomDiv" id="Makeup" style="width: 98%; max-height: 13.95vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="makeup"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Makeup"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Makeup"></div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                    <span class="opacity"></span>
                                    <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="Makeup-OpacitySlider" type="range" min="1" max="100" value="80">
                                </div>
                            </div>
                            <!-- Makeup First Color -->
                            <div class="mainDivBottomLeftBottomDiv" id="FirstMakeupColor" style="width: 98%; max-height: 53.5vh;">
                                <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                    <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                        <h4 style="font-size: 0.65vw;" class="first_color"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-FirstMakeupColor"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-FirstMakeupColor" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                </div>
                            </div>
                            <!-- Makeup Second Color -->
                            <div class="mainDivBottomLeftBottomDiv" id="SecondMakeupColor" style="width: 98%; max-height: 53.5vh;">
                                <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                    <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                        <h4 style="font-size: 0.65vw;" class="second_color"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-SecondMakeupColor"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-SecondMakeupColor" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                </div>
                            </div>
                            <!-- Blush -->
                            <div class="mainDivBottomLeftBottomDiv" id="Blush" style="width: 98%; max-height: 13.95vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="blush"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Blush"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Blush"></div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                    <span class="opacity"></span>
                                    <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="Blush-OpacitySlider" type="range" min="1" max="100" value="80">
                                </div>
                            </div>
                            <!-- Blush First Color -->
                            <div class="mainDivBottomLeftBottomDiv" id="FirstBlushColor" style="width: 98%; max-height: 53.5vh;">
                                <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                    <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                        <h4 style="font-size: 0.65vw;" class="first_color"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-FirstBlushColor"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-FirstBlushColor" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                </div>
                            </div>
                            <!-- Blush Second Color -->
                            <div class="mainDivBottomLeftBottomDiv" id="SecondBlushColor" style="width: 98%; max-height: 53.5vh;">
                                <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                    <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                        <h4 style="font-size: 0.65vw;" class="second_color"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-SecondBlushColor"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-SecondBlushColor" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                </div>
                            </div>
                            <!-- Lipstick -->
                            <div class="mainDivBottomLeftBottomDiv" id="Lipstick" style="width: 98%; max-height: 13.95vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="lipstick"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Lipstick"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Lipstick"></div>
                            </div>
                            <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                    <span class="opacity"></span>
                                    <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="Lipstick-OpacitySlider" type="range" min="1" max="100" value="80">
                                </div>
                            </div>
                            <!-- Lipstick First Color -->
                            <div class="mainDivBottomLeftBottomDiv" id="FirstLipstickColor" style="width: 98%; max-height: 53.5vh;">
                                <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                    <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                        <h4 style="font-size: 0.65vw;" class="first_color"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-FirstLipstickColor"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-FirstLipstickColor" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                </div>
                            </div>
                            <!-- Lipstick Second Color -->
                            <div class="mainDivBottomLeftBottomDiv" id="SecondLipstickColor" style="width: 98%; max-height: 53.5vh;">
                                <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                    <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                        <h4 style="font-size: 0.65vw;" class="second_color"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-SecondLipstickColor"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-SecondLipstickColor" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                </div>
                            </div>
                        </div>
                        <!-- Clothing -->
                        <div class="mainDivBottomLeftBottomInside" id="mainDivBottomLeftBottomInside-Clothing" style="display: none;">
                            <!-- Jacket -->
                            <div class="mainDivBottomLeftBottomDiv" id="Jacket" style="width: 98%; max-height: 19.10vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="jacket"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Jacket"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Jacket"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-JacketBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-JacketSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-JacketBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-JacketBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-JacketSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-JacketBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Undershirt -->
                            <div class="mainDivBottomLeftBottomDiv" id="Undershirt" style="width: 98%; max-height: 19.10vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                       <h4 class="undershirt"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Undershirt"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Undershirt"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-UndershirtBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-UndershirtSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-UndershirtBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-UndershirtBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-UndershirtSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-UndershirtBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Arms/Gloves -->
                            <div class="mainDivBottomLeftBottomDiv" id="Arms/Gloves" style="width: 98%; max-height: 19.10vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="arms_gloves"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Arms/Gloves"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Arms/Gloves"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Arms/GlovesBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Arms/GlovesSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Arms/GlovesBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Arms/GlovesBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Arms/GlovesSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Arms/GlovesBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Pants -->
                            <div class="mainDivBottomLeftBottomDiv" id="Pants" style="width: 98%; max-height: 19.10vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="pants"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Pants"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Pants"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-PantsBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-PantsSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-PantsBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-PantsBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-PantsSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-PantsBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Shoes -->
                            <div class="mainDivBottomLeftBottomDiv" id="Shoes" style="width: 98%; max-height: 19.10vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="shoes"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Shoes"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Shoes"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-ShoesBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-ShoesSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-ShoesBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-ShoesBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-ShoesSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-ShoesBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Decals -->
                            <div class="mainDivBottomLeftBottomDiv" id="Decals" style="width: 98%; max-height: 19.10vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="decals"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Decals"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Decals"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-DecalsBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-DecalsSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-DecalsBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-DecalsBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-DecalsSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-DecalsBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Masks -->
                            <div class="mainDivBottomLeftBottomDiv" id="Masks" style="width: 98%; max-height: 19.10vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="masks"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Masks"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Masks"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-MasksBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-MasksSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-MasksBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-MasksBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-MasksSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-MasksBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Scarfs/Necklaces -->
                            <div class="mainDivBottomLeftBottomDiv" id="Scarfs/Necklaces" style="width: 98%; max-height: 19.10vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="scarfs_necklaces"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Scarfs/Necklaces"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Scarfs/Necklaces"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Scarfs/NecklacesBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Scarfs/NecklacesSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Scarfs/NecklacesBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Scarfs/NecklacesBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Scarfs/NecklacesSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Scarfs/NecklacesBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Vest -->
                            <div class="mainDivBottomLeftBottomDiv" id="Vest" style="width: 98%; max-height: 19.10vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="vest"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Vest"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Vest"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-VestBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-VestSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-VestBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-VestBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-VestSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-VestBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Bag -->
                            <div class="mainDivBottomLeftBottomDiv" id="Bag" style="width: 98%; max-height: 19.10vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="bag"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Bag"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Bag"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-BagBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-BagSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-BagBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-BagBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-BagSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-BagBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mainDivBottomLeftBottomInside" id="mainDivBottomLeftBottomInside-Accessories" style="display: none;">
                            <!-- Hat -->
                            <div class="mainDivBottomLeftBottomDiv" id="Hat" style="width: 98%; max-height: 19.10vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="hat"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Hat"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Hat"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-HatBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-HatSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-HatBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-HatBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-HatSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-HatBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Glasses -->
                            <div class="mainDivBottomLeftBottomDiv" id="Glasses" style="width: 98%; max-height: 19.10vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="glasses"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Glasses"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Glasses"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-GlassesBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-GlassesSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-GlassesBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-GlassesBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-GlassesSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-GlassesBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Earrings -->
                            <div class="mainDivBottomLeftBottomDiv" id="Earrings" style="width: 98%; max-height: 19.10vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="earrings"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Earrings"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Earrings"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-EarringsBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-EarringsSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-EarringsBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-EarringsBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-EarringsSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-EarringsBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Watches -->
                            <div class="mainDivBottomLeftBottomDiv" id="Watches" style="width: 98%; max-height: 19.10vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="watches"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Watches"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Watches"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-WatchesBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-WatchesSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-WatchesBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-WatchesBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-WatchesSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-WatchesBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Bracelets -->
                            <div class="mainDivBottomLeftBottomDiv" id="Bracelets" style="width: 98%; max-height: 19.10vh;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="bracelets"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Bracelets"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Bracelets"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-BraceletsBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-BraceletsSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-BraceletsBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-BraceletsBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-BraceletsSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-BraceletsBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="mainDivBottomLeftBottomHomePayment" style="width: 100%; gap: 0.7vw; padding-top: 0.3vw; padding-bottom: 0.5vw;">
                            <div class="mainDivBottomLeftBottomButton" id="mainDivBottomLeftBottomButton-Finish" style="display: flex; width: 94.7%;" onclick="finishCharacterCreation()"><span>Finish</span></div>
                            <div id="mainDivBottomLeftBottomHomePaymentDiv">
                                <div id="mainDivBottomLeftBottomButtons">
                                    <div class="mainDivBottomLeftBottomButton" style="background: radial-gradient(132.55% 105.85% at 50% 50%, rgba(255, 103, 103, 0.71) 0%, rgba(255, 103, 103, 0.00) 100%);" onclick="goBack()"><span>Back</span></div>
                                    <div class="mainDivBottomLeftBottomButton" style="background: radial-gradient(132.55% 105.85% at 50% 50%, rgba(82, 203, 255, 0.71) 0%, rgba(82, 203, 255, 0.00) 100%);" onclick="nextPage()"><span>Next</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="mainDiv" id="mainDiv-ClothingHomeMenu" style="display: none;">
            <div id="mainDivTop">
                <div id="mainDivTopTop">
                    <div id="mainDivTopTextDiv">
                        <h4 id="mainText" style="color: #FFF; font-family: ttfirs; font-size: 1.45vw;"></h4>
                        <span class="character_creator_description" style="color: rgba(255, 255, 255, 0.41); font-family: 'Open Sans', serif; font-size: 0.665vw; font-weight: 700;"></span>
                    </div>
                </div>
            </div>
            <div class="mainDivBottom" id="mainDivBottom-HomeMenu" style="display: none;">
                <div id="mainDivBottomLeft">
                    <div id="mainDivBottomLeftBottom">
                        <div class="mainDivBottomLeftBottomInside">
                            <!-- Jacket -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Jacket" style="width: 100%; max-height: 19.10vh; display: none;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="jacket"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Jacket"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Jacket"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-JacketBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-JacketSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-JacketBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-JacketBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-JacketSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-JacketBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Hat -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Hat" style="width: 100%; max-height: 19.10vh; display: none;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="hat"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Hat"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Hat"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-HatBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-HatSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-HatBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-HatBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-HatSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-HatBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Bag -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Bag" style="width: 100%; max-height: 19.10vh; display: none;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="bag"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Bag"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Bag"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-BagBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-BagSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-BagBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-BagBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-BagSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-BagBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Glasses -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Glasses" style="width: 100%; max-height: 19.10vh; display: none;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="glasses"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Glasses"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Glasses"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-GlassesBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-GlassesSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-GlassesBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-GlassesBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-GlassesSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-GlassesBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Earrings -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Earrings" style="width: 100%; max-height: 19.10vh; display: none;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="earrings"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Earrings"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Earrings"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-EarringsBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-EarringsSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-EarringsBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-EarringsBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-EarringsSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-EarringsBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Watches -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Watches" style="width: 100%; max-height: 19.10vh; display: none;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="watches"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Watches"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Watches"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-WatchesBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-WatchesSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-WatchesBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-WatchesBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-WatchesSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-WatchesBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Bracelets -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Bracelets" style="width: 100%; max-height: 19.10vh; display: none;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="bracelets"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Bracelets"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Bracelets"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-BraceletsBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-BraceletsSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-BraceletsBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-BraceletsBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-BraceletsSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-BraceletsBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Undershirt -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Undershirt" style="width: 100%; max-height: 19.10vh; display: none;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                       <h4 class="undershirt"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Undershirt"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Undershirt"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-UndershirtBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-UndershirtSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-UndershirtBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-UndershirtBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-UndershirtSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-UndershirtBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Decals -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Decals" style="width: 100%; max-height: 19.10vh; display: none;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="decals"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Decals"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Decals"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-DecalsBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-DecalsSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-DecalsBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-DecalsBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-DecalsSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-DecalsBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Vest -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Vest" style="width: 100%; max-height: 19.10vh; display: none;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="vest"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Vest"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Vest"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-VestBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-VestSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-VestBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-VestBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-VestSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-VestBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Scarfs/Necklaces -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Scarfs/Necklaces" style="width: 100%; max-height: 19.10vh; display: none;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="scarfs_necklaces"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Scarfs/Necklaces"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Scarfs/Necklaces"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-Scarfs/NecklacesBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-Scarfs/NecklacesSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-Scarfs/NecklacesBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-Scarfs/NecklacesBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-Scarfs/NecklacesSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-Scarfs/NecklacesBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Shoes -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Shoes" style="width: 100%; max-height: 19.10vh; display: none;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="shoes"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Shoes"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Shoes"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-ShoesBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-ShoesSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-ShoesBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-ShoesBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-ShoesSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-ShoesBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Arms / Gloves -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Arms/Gloves" style="width: 100%; max-height: 19.10vh; display: none;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="arms_gloves"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Arms/Gloves"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Arms/Gloves"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-Arms/GlovesBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-Arms/GlovesSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-Arms/GlovesBtnRight-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-Arms/GlovesBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-Arms/GlovesSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-Arms/GlovesBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Tattoo -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Tattoo" style="width: 100%; display: none;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 id="tattosH4"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Tattoo" onclick="expandDiv('mainDivBottomLeftBottomInside-Home-Tattoo')"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Tattoo"></div>
                            </div>
                            <!-- Hair 2 -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Hairs2" style="width: 98%; max-height: 68.95vh; display: none;">
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Hairs" style="width: 98%; max-height: 13.75vh;">
                                    <div id="mainDivBottomLeftBottomDivTop">
                                        <div id="mainDivBottomLeftBottomDivTopInside">
                                            <h4 class="hairs"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Hairs"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Hairs"></div>
                                </div>
                                <div class="mainDivBottomLeftBottomDiv" style="gap: 0.1vw; max-height: 18vh; width: 98%;">
                                    <div id="mainDivBottomLeftBottomDivTop">
                                        <div id="mainDivBottomLeftBottomDivTopInside">
                                            <h4 class="hair_texture"></h4>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 100%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" style="width: 5.5%;" onclick="changeHairTexture('left')"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 79%;" id="hairTextureNumSpan-Home">0</span>
                                            <div class="MDBLBDBEDButton" style="width: 5.5%;" onclick="changeHairTexture('right')"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDiv" style="gap: 0.1vw; max-height: 18vh; width: 98%;">
                                    <div id="mainDivBottomLeftBottomDivTop">
                                        <div id="mainDivBottomLeftBottomDivTopInside">
                                            <h4 class="hair_fade"></h4>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 100%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" style="width: 5.5%;" onclick="changeHairFade('left')"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 79%;" id="hairFadeNumSpan-Home">0</span>
                                            <div class="MDBLBDBEDButton" style="width: 5.5%;" onclick="changeHairFade('right')"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-HairsColors" style="width: 98%; max-height: 53.5vh;">
                                    <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                        <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                            <h4 style="font-size: 0.65vw;" class="hairs_colors"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-HairsColors"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                        <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-HairsColors" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-HairsHighlightColors" style="width: 98%; max-height: 53.5vh;">
                                    <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                        <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                            <h4 style="font-size: 0.65vw;" class="highlight_color"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-HairsHighlightColors"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                        <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-HairsHighlightColors" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                    </div>
                                </div>
                                <!-- Facial Hair -->
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-FacialHairs" style="width: 98%; max-height: 13.75vh;">
                                    <div id="mainDivBottomLeftBottomDivTop">
                                        <div id="mainDivBottomLeftBottomDivTopInside">
                                            <h4 class="facial_hairs"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-FacialHairs"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-FacialHairs"></div>
                                </div>
                                <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                        <span class="opacity"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="FacialHairsHome-OpacitySlider" type="range" min="1" max="100" value="80">
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-FacialHairsColors" style="width: 98%; max-height: 53.5vh;">
                                    <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                        <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                            <h4 style="font-size: 0.65vw;" class="facial_hairs_colors"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-FacialHairsColors"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                        <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-FacialHairsColors" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-FacialHairsHighlightColors" style="width: 98%; max-height: 53.5vh;">
                                    <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                        <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                            <h4 style="font-size: 0.65vw;" class="highlight_color"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-FacialHairsHighlightColors"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                        <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-FacialHairsHighlightColors" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                    </div>
                                </div>
                                <!-- Eyebrows -->
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Eyebrows" style="width: 98%; max-height: 13.75vh;">
                                    <div id="mainDivBottomLeftBottomDivTop">
                                        <div id="mainDivBottomLeftBottomDivTopInside">
                                            <h4 class="eyebrows"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Eyebrows"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Eyebrows"></div>
                                </div>
                                <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                        <span class="opacity"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="EyebrowsHome-OpacitySlider" type="range" min="1" max="100" value="80">
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-EyebrowColors" style="width: 98%; max-height: 53.5vh;">
                                    <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                        <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                            <h4 style="font-size: 0.65vw;" class="color"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-EyebrowColors"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                        <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-EyebrowColors" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-EyebrowHighlightColors" style="width: 98%; max-height: 53.5vh;">
                                    <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                        <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                            <h4 style="font-size: 0.65vw;" class="highlight_color"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-EyebrowHighlightColors"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                        <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-EyebrowHighlightColors" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                    </div>
                                </div>
                            </div>
                            <!-- Hair 3 -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Hairs3" style="width: 98%; max-height: 68.95vh; display: none;">
                                <!-- Chest Hair -->
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-ChestHair" style="width: 98%; max-height: 13.75vh;">
                                    <div id="mainDivBottomLeftBottomDivTop">
                                        <div id="mainDivBottomLeftBottomDivTopInside">
                                            <h4 class="chest_hair"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-ChestHair"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-ChestHair"></div>
                                </div>
                                <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                        <span class="opacity"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="ChestHairHome-OpacitySlider" type="range" min="1" max="100" value="80">
                                    </div>
                                </div>
                                <!-- Makeup -->
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Makeup" style="width: 98%; max-height: 13.95vh;">
                                    <div id="mainDivBottomLeftBottomDivTop">
                                        <div id="mainDivBottomLeftBottomDivTopInside">
                                            <h4 class="makeup"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Makeup"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Makeup"></div>
                                </div>
                                <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                        <span class="opacity"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="MakeupHome-OpacitySlider" type="range" min="1" max="100" value="80">
                                    </div>
                                </div>
                                <!-- Makeup First Color -->
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-FirstMakeupColor" style="width: 98%; max-height: 53.5vh;">
                                    <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                        <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                            <h4 style="font-size: 0.65vw;" class="first_color"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-FirstMakeupColor"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                        <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-FirstMakeupColor" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                    </div>
                                </div>
                                <!-- Makeup Second Color -->
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-SecondMakeupColor" style="width: 98%; max-height: 53.5vh;">
                                    <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                        <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                            <h4 style="font-size: 0.65vw;" class="second_color"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-SecondMakeupColor"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                        <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-SecondMakeupColor" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                    </div>
                                </div>
                                <!-- Blush -->
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Blush" style="width: 98%; max-height: 13.95vh;">
                                    <div id="mainDivBottomLeftBottomDivTop">
                                        <div id="mainDivBottomLeftBottomDivTopInside">
                                            <h4 class="blush"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Blush"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Blush"></div>
                                </div>
                                <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                        <span class="opacity"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="BlushHome-OpacitySlider" type="range" min="1" max="100" value="80">
                                    </div>
                                </div>
                                <!-- Blush First Color -->
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-FirstBlushColor" style="width: 98%; max-height: 53.5vh;">
                                    <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                        <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                            <h4 style="font-size: 0.65vw;" class="first_color"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-FirstBlushColor"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                        <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-FirstBlushColor" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                    </div>
                                </div>
                                <!-- Blush Second Color -->
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-SecondBlushColor" style="width: 98%; max-height: 53.5vh;">
                                    <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                        <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                            <h4 style="font-size: 0.65vw;" class="second_color"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-SecondBlushColor"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                        <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-SecondBlushColor" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                    </div>
                                </div>
                                <!-- Lipstick -->
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Lipstick" style="width: 98%; max-height: 13.95vh;">
                                    <div id="mainDivBottomLeftBottomDivTop">
                                        <div id="mainDivBottomLeftBottomDivTopInside">
                                            <h4 class="lipstick"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Lipstick"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Lipstick"></div>
                                </div>
                                <div class="mainDivBottomLeftBottomDiv" style="align-items: flex-start; justify-content: flex-start; width: 98%;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 95.9%;">
                                        <span class="opacity"></span>
                                        <input class="mainDivBottomLeftBottomDivBottomInputSlider2" id="LipstickHome-OpacitySlider" type="range" min="1" max="100" value="80">
                                    </div>
                                </div>
                                <!-- Lipstick First Color -->
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-FirstLipstickColor" style="width: 98%; max-height: 53.5vh;">
                                    <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                        <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                            <h4 style="font-size: 0.65vw;" class="first_color"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-FirstLipstickColor"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                        <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-FirstLipstickColor" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                    </div>
                                </div>
                                <!-- Lipstick Second Color -->
                                <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-SecondLipstickColor" style="width: 98%; max-height: 53.5vh;">
                                    <div id="mainDivBottomLeftBottomDivTop" style="background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%); flex-direction: column;">
                                        <div id="mainDivBottomLeftBottomDivTopInside" style="padding-top: 0.2vw; padding-bottom: 0.2vw;">
                                            <h4 style="font-size: 0.65vw;" class="second_color"></h4>
                                            <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-SecondLipstickColor"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                        </div>
                                        <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-SecondLipstickColor" style="gap: 0.35vw 0.3vw; overflow: hidden; width: 95%; display: none; padding-top: 0.3vw; padding-bottom: 0.3vw;"></div>
                                    </div>
                                </div>
                            </div>
                            <!-- Pants -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Pants" style="width: 100%; max-height: 19.10vh; display: none;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="pants"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Pants"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Pants"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-PantsBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-PantsSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-PantsSpan-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-PantsBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-PantsSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-PantsBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Masks -->
                            <div class="mainDivBottomLeftBottomDiv" id="mainDivBottomLeftBottomInside-Home-Masks" style="width: 100%; max-height: 19.10vh; display: none;">
                                <div id="mainDivBottomLeftBottomDivTop">
                                    <div id="mainDivBottomLeftBottomDivTopInside">
                                        <h4 class="masks"></h4>
                                        <div id="mainDivBottomLeftBottomDivTopExpandDiv"><span class="click"></span><div class="mainDivBottomLeftBottomDivTopExpandDivButton" id="mainDivBottomLeftBottomDivTopExpandDivButton-Home-Masks"><i class="fa-solid fa-chevron-down" style="color: #FFF; opacity: 0.73; margin-top: 19.4%;"></i></div></div>
                                    </div>
                                </div>
                                <div class="mainDivBottomLeftBottomDivBottom" id="mainDivBottomLeftBottomDivBottom-Home-Masks"></div>
                                <div style="width: 99.4%; height: fit-content; position: relative; display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-MasksBtnLeft-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-MasksSpan-ComponentVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-MasksSpan-ComponentVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                    <div class="mainDivBottomLeftBottomDivBottomEmptyDiv" style="width: 48%; background: transparent; padding-bottom: 0; border: none; justify-content: space-between;">
                                        <div class="mainDivBottomLeftBottomDivBottomEmptyDiv2" style="width: 100%; flex-direction: row; color: #FFF; text-shadow: none; font-size: 0.7vw; padding-left: 0; padding-top: 0.5vw; padding-bottom: 0.5vw; align-items: center;">
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-MasksBtnLeft-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-left"></i></div>
                                            <span style="width: 59%;" id="mainDivBottomLeftBottomDivBottom-Home-MasksSpan-TextureVariation">-1</span>
                                            <div class="MDBLBDBEDButton" id="mainDivBottomLeftBottomDivBottom-Home-MasksBtnRight-TextureVariation" style="width: 11%;"><i class="fa-regular fa-chevron-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="mainDivBottomLeftBottomHomePayment" style="width: 99%;">
                            <div id="mainDivBottomLeftBottomHomePaymentDiv" style="width: 94%;">
                                <div style="width: 50%; height: 100%; position: relative; display: flex; align-items: flex-start; justify-content: center; flex-direction: column; line-height: 0.8vw;">
                                    <span class="payment" style="color: #FFF; font-weight: 500; font-family: 'Gilroy-SemiBold'; font-size: 0.7vw;" class="payment"></span>
                                    <span class="amount" style="color: rgba(255, 255, 255, 0.41); font-weight: 500; font-family: 'Gilroy-Regular'; font-size: 0.65vw;" class="amount"></span>
                                </div>
                                <div style="width: 50%; height: 100%; position: relative; display: flex; align-items: flex-end; justify-content: center; flex-direction: column; line-height: 0.8vw;">
                                    <span style="color: #FFF; font-weight: 500; font-family: 'Gilroy-SemiBold'; font-size: 0.7vw;" id="paymentSpan">0$</span>
                                    <span class="vat_included" style="color: rgba(255, 255, 255, 0.41); font-weight: 500; font-family: 'Gilroy-Regular'; font-size: 0.65vw;" aria-colspan="vat_included"></span>
                                </div>
                            </div>
                            <div id="mainDivBottomLeftBottomHomePaymentDiv2">
                                <div id="mainDivBottomLeftBottomHomePaymentDivButton" style="background: radial-gradient(132.55% 105.85% at 50% 50%, rgba(255, 103, 103, 0.71) 0%, rgba(255, 103, 103, 0.00) 100%);" onclick="goBackHome()"><span class="go_back"></span></div>
                                <div id="mainDivBottomLeftBottomHomePaymentDivButton" style="background: radial-gradient(132.55% 105.85% at 50% 50%, rgba(82, 203, 255, 0.71) 0%, rgba(82, 203, 255, 0.00) 100%);" onclick="openPaymentDialog()"><span class="pay"></span></div>
                            </div>
                            <div id="mainDivBottomLeftBottomHomePaymentDiv3" style="display: none;">
                                <div id="mainDivBottomLeftBottomHomePaymentDivButton" style="background: radial-gradient(132.55% 105.85% at 50% 50%, rgba(82, 203, 255, 0.71) 0%, rgba(82, 203, 255, 0.00) 100%);" onclick="goBack2()"><span class="back"></span></div>
                                <div id="mainDivBottomLeftBottomHomePaymentDivButton" style="background: radial-gradient(132.55% 105.85% at 50% 50%, rgba(82, 203, 255, 0.71) 0%, rgba(82, 203, 255, 0.00) 100%);" onclick="nextPage2()"><span class="next"></span></div>
                                <div id="mainDivBottomLeftBottomHomePaymentDivButton" style="background: radial-gradient(132.55% 105.85% at 50% 50%, rgba(255, 103, 103, 0.71) 0%, rgba(255, 103, 103, 0.00) 100%);" onclick="openPaymentDialog()"><span class="pay"></span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mainDivBottom" id="mainDivBottom-HomeMenuMain" style="height: 82%; display: flex;">
                <div id="mainDivBottomLeft">
                    <div id="mainDivBottomLeftBottomHome">
                        <!-- Left Side -->
                        <div class="mainDivBottomLeftBottomHomeDiv" id="mainDivBottomLeftBottomHomeDiv-Jacket" onclick="openClothingHome('Jacket')">
                            <img src="files/home-menu/jacket.png" style="width: 4.2vw; margin-bottom: 22%;">
                            <div id="mainDivBottomLeftBottomHomeDivBottomDiv"><span class="jacket"></span></div>
                        </div>
                        <div class="mainDivBottomLeftBottomHomeDiv" id="mainDivBottomLeftBottomHomeDiv-Hat" onclick="openClothingHome('Hat')">
                            <img src="files/home-menu/hat.png" style="width: 4.2vw; margin-bottom: 22%;">
                            <div id="mainDivBottomLeftBottomHomeDivBottomDiv"><span class="hat"></span></div>
                        </div>
                        <div class="mainDivBottomLeftBottomHomeDiv" id="mainDivBottomLeftBottomHomeDiv-Vest" onclick="openClothingHome('Vest')">
                            <img src="files/home-menu/vest.png" style="width: 4.2vw; margin-bottom: 22%;">
                            <div id="mainDivBottomLeftBottomHomeDivBottomDiv"><span class="vest"></span></div>
                        </div>
                        <div class="mainDivBottomLeftBottomHomeDiv" id="mainDivBottomLeftBottomHomeDiv-Pants" onclick="openClothingHome('Pants')">
                            <img src="files/home-menu/pants.png" style="width: 4.2vw; margin-bottom: 22%;">
                            <div id="mainDivBottomLeftBottomHomeDivBottomDiv"><span class="pants"></span></div>
                        </div>
                        <div class="mainDivBottomLeftBottomHomeDiv" id="mainDivBottomLeftBottomHomeDiv-Undershirt" onclick="openClothingHome('Undershirt')">
                            <img src="files/home-menu/undershirt.png" style="width: 4.2vw; margin-bottom: 22%;">
                            <div id="mainDivBottomLeftBottomHomeDivBottomDiv"><span class="undershirt"></span></div>
                        </div>
                        <!-- Right Side -->
                        <div class="mainDivBottomLeftBottomHomeDiv" id="mainDivBottomLeftBottomHomeDiv-Bag" onclick="openClothingHome('Bag')">
                            <img src="files/home-menu/bag.png" style="width: 4.2vw; margin-bottom: 22%;">
                            <div id="mainDivBottomLeftBottomHomeDivBottomDiv"><span class="bag"></span></div>
                        </div>
                        <div class="mainDivBottomLeftBottomHomeDiv" id="mainDivBottomLeftBottomHomeDiv-Shoes" onclick="openClothingHome('Shoes')">
                            <img src="files/home-menu/shoes.png" style="width: 4.2vw; margin-bottom: 22%;">
                            <div id="mainDivBottomLeftBottomHomeDivBottomDiv"><span class="shoes"></span></div>
                        </div>
                        <div class="mainDivBottomLeftBottomHomeDiv" id="mainDivBottomLeftBottomHomeDiv-Arms/Gloves" onclick="openClothingHome('Arms/Gloves')">
                            <img src="files/home-menu/armsgloves.png" style="width: 4.2vw; margin-bottom: 22%;">
                            <div id="mainDivBottomLeftBottomHomeDivBottomDiv"><span class="arms_gloves"></span></div>
                        </div>
                        <div class="mainDivBottomLeftBottomHomeDiv" id="mainDivBottomLeftBottomHomeDiv-Masks" onclick="openClothingHome('Masks')">
                            <img src="files/home-menu/mask.png" style="width: 4.2vw; margin-bottom: 22%;">
                            <div id="mainDivBottomLeftBottomHomeDivBottomDiv"><span class="masks"></span></div>
                        </div>
                        <div class="mainDivBottomLeftBottomHomeDiv" id="mainDivBottomLeftBottomHomeDiv-Watches" onclick="openClothingHome('Watches')">
                            <img src="files/home-menu/watch.png" style="width: 4.2vw; margin-bottom: 22%;">
                            <div id="mainDivBottomLeftBottomHomeDivBottomDiv"><span class="watches"></span></div>
                        </div>
                        <div class="mainDivBottomLeftBottomHomeDiv" id="mainDivBottomLeftBottomHomeDiv-Earrings" onclick="openClothingHome('Earrings')">
                            <img src="files/home-menu/earrings.png" style="width: 4.2vw; margin-bottom: 22%;">
                            <div id="mainDivBottomLeftBottomHomeDivBottomDiv"><span class="earrings"></span></div>
                        </div>
                        <div class="mainDivBottomLeftBottomHomeDiv" id="mainDivBottomLeftBottomHomeDiv-Scarfs/Necklaces" onclick="openClothingHome('Scarfs/Necklaces')">
                            <img src="files/home-menu/scarfsnecklaces.png" style="width: 4.2vw; margin-bottom: 22%;">
                            <div id="mainDivBottomLeftBottomHomeDivBottomDiv"><span class="scarfs_necklaces"></span></div>
                        </div>
                        <div class="mainDivBottomLeftBottomHomeDiv" id="mainDivBottomLeftBottomHomeDiv-Decals" onclick="openClothingHome('Decals')">
                            <img src="files/home-menu/decal.png" style="width: 4.2vw; margin-bottom: 22%;">
                            <div id="mainDivBottomLeftBottomHomeDivBottomDiv"><span class="decals"></span></div>
                        </div>
                        <div class="mainDivBottomLeftBottomHomeDiv" id="mainDivBottomLeftBottomHomeDiv-Glasses" onclick="openClothingHome('Glasses')">
                            <img src="files/home-menu/glasses.png" style="width: 4.2vw; margin-bottom: 22%;">
                            <div id="mainDivBottomLeftBottomHomeDivBottomDiv"><span class="glasses"></span></div>
                        </div>
                        <div class="mainDivBottomLeftBottomHomeDiv" id="mainDivBottomLeftBottomHomeDiv-Bracelets" onclick="openClothingHome('Bracelets')">
                            <img src="files/home-menu/bracelet.png" style="width: 4.2vw; margin-bottom: 22%;">
                            <div id="mainDivBottomLeftBottomHomeDivBottomDiv"><span class="bracelets"></span></div>
                        </div>
                    </div>
                    <div id="mainDivBottomLeftBottomHomePayment">
                        <div id="mainDivBottomLeftBottomHomePaymentDiv">
                            <div style="width: 50%; height: 100%; position: relative; display: flex; align-items: flex-start; justify-content: center; flex-direction: column; line-height: 0.8vw;">
                                <span class="payment" style="color: #FFF; font-weight: 500; font-family: 'Gilroy-SemiBold'; font-size: 0.7vw;"></span>
                                <span class="amount" style="color: rgba(255, 255, 255, 0.41); font-weight: 500; font-family: 'Gilroy-Regular'; font-size: 0.65vw;"></span>
                            </div>
                            <div style="width: 50%; height: 100%; position: relative; display: flex; align-items: flex-end; justify-content: center; flex-direction: column; line-height: 0.8vw;">
                                <span style="color: #FFF; font-weight: 500; font-family: 'Gilroy-SemiBold'; font-size: 0.7vw;" id="paymentSpan2">0$</span>
                                <span class="vat_included" style="color: rgba(255, 255, 255, 0.41); font-weight: 500; font-family: 'Gilroy-Regular'; font-size: 0.65vw;"></span>
                            </div>
                        </div>
                        <div id="mainDivBottomLeftBottomHomePaymentDiv">
                            <div id="mainDivBottomLeftBottomHomePaymentDivButton" style="background: radial-gradient(132.55% 105.85% at 50% 50%, rgba(255, 103, 103, 0.71) 0%, rgba(255, 103, 103, 0.00) 100%);" onclick="openPaymentDialog()"><span class="pay_via_bank"></span></div>
                            <div id="mainDivBottomLeftBottomHomePaymentDivButton" style="background: radial-gradient(132.55% 105.85% at 50% 50%, rgba(82, 203, 255, 0.71) 0%, rgba(82, 203, 255, 0.00) 100%);" onclick="openPaymentDialog()"><span class="pay_via_cash"></span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="mainDivOutsideButtons">
            <div id="mainDivOutsideButton-showMouseInfos" class="mainDivOutsideButton" onclick="showMouseInfos()"><i class="fa-solid fa-computer-mouse-scrollwheel"></i></div>
            <div class="mainDivOutsideButtonDiv">
                <div id="mainDivOutsideButton-ClothRemoverMenu" class="mainDivOutsideButton" onclick="showClothRemoveButtons()"><i class="fa-solid fa-clothes-hanger"></i></div>
                <div id="mainDivOutsideButton-Hat" class="mainDivOutsideButton mainDivOutsideButton-Hat" style="display: none;" onclick="removeCloth('Hat', '0')"><i class="fa-solid fa-hat-cowboy"></i></div>
                <div id="mainDivOutsideButton-Masks" class="mainDivOutsideButton mainDivOutsideButton-Masks" style="display: none;" onclick="removeCloth('Masks', '1')"><i class="fa-solid fa-masks-theater"></i></div>
                <div id="mainDivOutsideButton-Glasses" class="mainDivOutsideButton mainDivOutsideButton-Glasses" style="display: none;" onclick="removeCloth('Glasses', '1')"><i class="fa-solid fa-glasses"></i></div>
                <div id="mainDivOutsideButton-Jacket" class="mainDivOutsideButton mainDivOutsideButton-Jacket" style="display: none;" onclick="removeCloth('Jacket', '11')"><i class="fa-solid fa-shirt"></i></div>
                <div id="mainDivOutsideButton-Bag" class="mainDivOutsideButton mainDivOutsideButton-Bag" style="display: none;" onclick="removeCloth('Bag', '5')"><i class="fa-solid fa-bag-shopping"></i></div>
                <div id="mainDivOutsideButton-Hairs" class="mainDivOutsideButton mainDivOutsideButton-Hairs" style="display: none;" onclick="removeCloth('Hairs', '2')"><i class="fa-solid fa-mustache"></i></div>
                <div id="mainDivOutsideButton-Shoes" class="mainDivOutsideButton mainDivOutsideButton-Shoes" style="display: none;" onclick="removeCloth('Shoes', '6')"><i class="fa-solid fa-boot-heeled"></i></div>
                <div id="mainDivOutsideButton-Pants" class="mainDivOutsideButton mainDivOutsideButton-Pants" style="display: none;" onclick="removeCloth('Pants', '4')"><i class="fa-sharp fa-solid fa-stocking"></i></div>
            </div>
            <div class="mainDivOutsideButtonDiv" id="mainDivOutsideButtonDiv-ClothCompare">
                <div id="mainDivOutsideButton-ClothCompareMenu" class="mainDivOutsideButton" onclick="showClothCompareMenu()"><i class="fa-solid fa-mitten"></i></div>
                <div id="mainDivOutsideButton-ClothCompare1" class="mainDivOutsideButton mainDivOutsideButton-ClothCompare1" style="display: none;" onclick="saveClothingToCompare(1)"><i class="fa-solid fa-square-1"></i></div>
                <div id="mainDivOutsideButton-ClothCompare2" class="mainDivOutsideButton mainDivOutsideButton-ClothCompare2" style="display: none;" onclick="saveClothingToCompare(2)"><i class="fa-solid fa-square-2"></i></div>
                <div id="mainDivOutsideButton-ClothCompareConfirm" class="mainDivOutsideButton mainDivOutsideButton-ClothCompareConfirm" style="display: none;" onclick="confirmCompare()"><i class="fa-solid fa-code-compare"></i></div>
            </div>
            <div id="mainDivOutsideButton-3DMenu" class="mainDivOutsideButton" onclick="enable3DMenu()"><i class="fa-solid fa-cube"></i></div>
        </div>
        <div id="animPosInfoDiv">
            <div id="APIDKeyDiv">
                <div id="APIDKeyDivLeft"><span>ESC</span></div>
                <div id="APIDKeyDivRight"><span>Close Comparing Clothes</span></div>
            </div>
            <div id="APIDKeyDiv">
                <div id="APIDKeyDivLeft"><span>Enter</span></div>
                <div id="APIDKeyDivRight"><span>Confirm Selected Cloth</span></div>
            </div>
        </div>
        <div id="mouseInfosDiv">
            <div class="mouseInfo">
                <i class="fa-solid fa-arrows-up-down"></i>
                <span class="adjust_camera"></span>
            </div>
            <div class="mouseInfo">
                <i class="fa-solid fa-arrows-rotate"></i>
                <span class="click_to_rotate"></span>
            </div>
            <div class="mouseInfo">
                <i class="fa-solid fa-camera-rotate"></i>
                <span class="use_arrow_to_rotate"></span>
            </div>
            <div class="mouseInfo">
                <i class="fa-solid fa-hand-pointer"></i>
                <span class="click_to_adjust_height"></span>
            </div>
            <div class="mouseInfo">
                <i class="fa-solid fa-magnifying-glass-plus"></i>
                <span class="scroll_to_adjust_zoom"></span>
            </div>
        </div>  
        <div id="pedDiv"></div>
        <div id="pedDiv2"></div>
        <div id="pedDiv3"></div>
        <script src="index.js"></script>
    </body>
</html>