{"manifest": {"name": "is-regex", "version": "1.1.1", "description": "Is this value a JS regex? Works cross-realm/iframe, and despite ES6 @@toStringTag", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "node --harmony --es-staging test", "posttest": "npx aud --production", "coverage": "covert test/index.js", "lint": "eslint .", "eccheck": "eclint check *.js **/*.js > /dev/null", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/ljharb/is-regex.git"}, "bugs": {"url": "https://github.com/ljharb/is-regex/issues"}, "homepage": "https://github.com/ljharb/is-regex", "keywords": ["regex", "regexp", "is", "regular expression", "regular", "expression"], "dependencies": {"has-symbols": "^1.0.1"}, "devDependencies": {"@ljharb/eslint-config": "^17.1.0", "aud": "^1.1.2", "auto-changelog": "^2.2.0", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^7.6.0", "foreach": "^2.0.5", "safe-publish-latest": "^1.1.4", "tape": "^5.0.1"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-regex-1.1.1-c6f98aacc546f6cec5468a07b7b153ab564a57b9-integrity\\node_modules\\is-regex\\package.json", "readmeFilename": "README.md", "readme": "#is-regex <sup>[![Version Badge][2]][1]</sup>\n\n[![Build Status][3]][4]\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\n[![browser support][9]][10]\n\nIs this value a JS regex?\nThis module works cross-realm/iframe, and despite ES6 @@toStringTag.\n\n## Example\n\n```js\nvar isRegex = require('is-regex');\nvar assert = require('assert');\n\nassert.notOk(isRegex(undefined));\nassert.notOk(isRegex(null));\nassert.notOk(isRegex(false));\nassert.notOk(isRegex(true));\nassert.notOk(isRegex(42));\nassert.notOk(isRegex('foo'));\nassert.notOk(isRegex(function () {}));\nassert.notOk(isRegex([]));\nassert.notOk(isRegex({}));\n\nassert.ok(isRegex(/a/g));\nassert.ok(isRegex(new RegExp('a', 'g')));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[1]: https://npmjs.org/package/is-regex\n[2]: http://versionbadg.es/ljharb/is-regex.svg\n[3]: https://travis-ci.org/ljharb/is-regex.svg\n[4]: https://travis-ci.org/ljharb/is-regex\n[5]: https://david-dm.org/ljharb/is-regex.svg\n[6]: https://david-dm.org/ljharb/is-regex\n[7]: https://david-dm.org/ljharb/is-regex/dev-status.svg\n[8]: https://david-dm.org/ljharb/is-regex#info=devDependencies\n[9]: https://ci.testling.com/ljharb/is-regex.png\n[10]: https://ci.testling.com/ljharb/is-regex\n[11]: https://nodei.co/npm/is-regex.png?downloads=true&stars=true\n[license-image]: http://img.shields.io/npm/l/is-regex.svg\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/is-regex.svg\n[downloads-url]: http://npm-stat.com/charts.html?package=is-regex\n\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 Jordan Harband\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-regex/-/is-regex-1.1.1.tgz#c6f98aacc546f6cec5468a07b7b153ab564a57b9", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-regex/-/is-regex-1.1.1.tgz", "hash": "c6f98aacc546f6cec5468a07b7b153ab564a57b9", "integrity": "sha512-1+QkEcxiLlB7VEyFtyBg94e08OAsvq7FUBgApTq/w2ymCLyKJgDPsybBENVtA7XCQEgEXxKPonG+mvYRxh/LIg==", "registry": "npm", "packageName": "is-regex", "cacheIntegrity": "sha512-1+QkEcxiLlB7VEyFtyBg94e08OAsvq7FUBgApTq/w2ymCLyKJgDPsybBENVtA7XCQEgEXxKPonG+mvYRxh/LIg== sha1-xvmKrMVG9s7FRooHt7FTq1ZKV7k="}, "registry": "npm", "hash": "c6f98aacc546f6cec5468a07b7b153ab564a57b9"}