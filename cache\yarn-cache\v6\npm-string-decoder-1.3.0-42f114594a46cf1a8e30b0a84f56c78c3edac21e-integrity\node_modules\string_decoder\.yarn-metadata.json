{"manifest": {"name": "string_decoder", "version": "1.3.0", "description": "The string_decoder module from Node core", "main": "lib/string_decoder.js", "files": ["lib"], "dependencies": {"safe-buffer": "~5.2.0"}, "devDependencies": {"babel-polyfill": "^6.23.0", "core-util-is": "^1.0.2", "inherits": "^2.0.3", "tap": "~0.4.8"}, "scripts": {"test": "tap test/parallel/*.js && node test/verify-dependencies", "ci": "tap test/parallel/*.js test/ours/*.js --tap | tee test.tap && node test/verify-dependencies.js"}, "repository": {"type": "git", "url": "git://github.com/nodejs/string_decoder.git"}, "homepage": "https://github.com/nodejs/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-string-decoder-1.3.0-42f114594a46cf1a8e30b0a84f56c78c3edac21e-integrity\\node_modules\\string_decoder\\package.json", "readmeFilename": "README.md", "readme": "# string_decoder\n\n***Node-core v8.9.4 string_decoder for userland***\n\n\n[![NPM](https://nodei.co/npm/string_decoder.png?downloads=true&downloadRank=true)](https://nodei.co/npm/string_decoder/)\n[![NPM](https://nodei.co/npm-dl/string_decoder.png?&months=6&height=3)](https://nodei.co/npm/string_decoder/)\n\n\n```bash\nnpm install --save string_decoder\n```\n\n***Node-core string_decoder for userland***\n\nThis package is a mirror of the string_decoder implementation in Node-core.\n\nFull documentation may be found on the [Node.js website](https://nodejs.org/dist/v8.9.4/docs/api/).\n\nAs of version 1.0.0 **string_decoder** uses semantic versioning.\n\n## Previous versions\n\nPrevious version numbers match the versions found in Node core, e.g. 0.10.24 matches Node 0.10.24, likewise 0.11.10 matches Node 0.11.10.\n\n## Update\n\nThe *build/* directory contains a build script that will scrape the source from the [nodejs/node](https://github.com/nodejs/node) repo given a specific Node version.\n\n## Streams Working Group\n\n`string_decoder` is maintained by the Streams Working Group, which\noversees the development and maintenance of the Streams API within\nNode.js. The responsibilities of the Streams Working Group include:\n\n* Addressing stream issues on the Node.js issue tracker.\n* Authoring and editing stream documentation within the Node.js project.\n* Reviewing changes to stream subclasses within the Node.js project.\n* Redirecting changes to streams from the Node.js project to this\n  project.\n* Assisting in the implementation of stream providers within Node.js.\n* Recommending versions of `readable-stream` to be included in Node.js.\n* Messaging about the future of streams to give the community advance\n  notice of changes.\n\nSee [readable-stream](https://github.com/nodejs/readable-stream) for\nmore details.\n", "licenseText": "Node.js is licensed for use as follows:\n\n\"\"\"\nCopyright Node.js contributors. All rights reserved.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n\"\"\"\n\nThis license applies to parts of Node.js originating from the\nhttps://github.com/joyent/node repository:\n\n\"\"\"\nCopyright Joyent, Inc. and other Node contributors. All rights reserved.\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n\"\"\"\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/string_decoder/-/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e", "type": "tarball", "reference": "https://registry.yarnpkg.com/string_decoder/-/string_decoder-1.3.0.tgz", "hash": "42f114594a46cf1a8e30b0a84f56c78c3edac21e", "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "registry": "npm", "packageName": "string_decoder", "cacheIntegrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA== sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4="}, "registry": "npm", "hash": "42f114594a46cf1a8e30b0a84f56c78c3edac21e"}