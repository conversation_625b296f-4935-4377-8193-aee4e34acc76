{"manifest": {"name": "utila", "version": "0.4.0", "description": "notareplacementforunderscore", "main": "lib/utila.js", "devDependencies": {"coffee-script": "~1.6.3", "little-popo": "~0.1"}, "scripts": {"prepublish": "coffee --bare --compile --output ./lib ./src"}, "repository": {"type": "git", "url": "https://github.com/AriaMinaei/utila.git"}, "keywords": ["utilities"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/AriaMinaei/utila/issues"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-utila-0.4.0-8a16a05d445657a3aea5eecc5b12a4fa5379772c-integrity\\node_modules\\utila\\package.json", "readmeFilename": "README.md", "readme": "notareplacementforunderscore\n\n# Installation\n\n**npm**: `npm install utila`\n\n**bower**: available via bower as in `bower install utila`, but you should run `npm install` before you can use it.", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 Aria Minaei\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/utila/-/utila-0.4.0.tgz#8a16a05d445657a3aea5eecc5b12a4fa5379772c", "type": "tarball", "reference": "https://registry.yarnpkg.com/utila/-/utila-0.4.0.tgz", "hash": "8a16a05d445657a3aea5eecc5b12a4fa5379772c", "integrity": "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=", "registry": "npm", "packageName": "utila", "cacheIntegrity": "sha512-Z0DbgELS9/L/75wZbro8xAnT50pBVFQZ+hUEueGDU5FN51YSCYM+jdxsfCiHjwNP/4LCDD0i/graKpeBnOXKRA== sha1-ihagXURWV6Oupe7MWxKk+lN5dyw="}, "registry": "npm", "hash": "8a16a05d445657a3aea5eecc5b12a4fa5379772c"}