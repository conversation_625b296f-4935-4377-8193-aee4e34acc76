import { isEnvBrowser } from './misc';

interface DebugEvent {
  action: string;
  data?: any;
}

/**
 * Utility function for dispatching NUI events in browser environment
 * @param events - Array of debug events to dispatch
 * @param timer - Delay in ms before dispatching events (default: 1000)
 */
export const debugData = (events: DebugEvent[], timer = 1000): void => {
  if (!isEnvBrowser()) return;

  for (const event of events) {
    setTimeout(() => {
      window.dispatchEvent(
        new MessageEvent('message', {
          data: {
            action: event.action,
            data: event.data,
          },
        })
      );
    }, timer);
  }
};
