{"manifest": {"name": "create-hmac", "version": "1.1.7", "description": "node style hmacs in the browser", "files": ["browser.js", "index.js", "legacy.js"], "main": "index.js", "scripts": {"standard": "standard", "test": "npm run-script standard && npm run-script unit", "unit": "node test.js | tspec"}, "repository": {"type": "git", "url": "https://github.com/crypto-browserify/createHmac.git"}, "keywords": ["crypto", "hmac"], "author": {}, "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/createHmac/issues"}, "homepage": "https://github.com/crypto-browserify/createHmac", "devDependencies": {"hash-test-vectors": "^1.3.2", "standard": "^5.3.1", "tap-spec": "^2.1.2", "tape": "^3.0.3"}, "dependencies": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}, "browser": "./browser.js", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-create-hmac-1.1.7-69170c78b3ab957147b2b8b04572e47ead2243ff-integrity\\node_modules\\create-hmac\\package.json", "readmeFilename": "README.md", "readme": "# create-hmac\n\n[![NPM Package](https://img.shields.io/npm/v/create-hmac.svg?style=flat-square)](https://www.npmjs.org/package/create-hmac)\n[![Build Status](https://img.shields.io/travis/crypto-browserify/createHmac.svg?branch=master&style=flat-square)](https://travis-ci.org/crypto-browserify/createHmac)\n[![Dependency status](https://img.shields.io/david/crypto-browserify/createHmac.svg?style=flat-square)](https://david-dm.org/crypto-browserify/createHmac#info=dependencies)\n\n[![js-standard-style](https://cdn.rawgit.com/feross/standard/master/badge.svg)](https://github.com/feross/standard)\n\nNode style HMACs for use in the browser, with native HMAC functions in node. API is the same as HMACs in node:\n\n```js\nvar createHmac = require('create-hmac')\nvar hmac = createHmac('sha224', Buffer.from('secret key'))\nhmac.update('synchronous write') //optional encoding parameter\nhmac.digest() // synchronously get result with optional encoding parameter\n\nhmac.write('write to it as a stream')\nhmac.end() //remember it's a stream\nhmac.read() //only if you ended it as a stream though\n```\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2017 crypto-browserify contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/create-hmac/-/create-hmac-1.1.7.tgz#69170c78b3ab957147b2b8b04572e47ead2243ff", "type": "tarball", "reference": "https://registry.yarnpkg.com/create-hmac/-/create-hmac-1.1.7.tgz", "hash": "69170c78b3ab957147b2b8b04572e47ead2243ff", "integrity": "sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg==", "registry": "npm", "packageName": "create-hmac", "cacheIntegrity": "sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg== sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8="}, "registry": "npm", "hash": "69170c78b3ab957147b2b8b04572e47ead2243ff"}