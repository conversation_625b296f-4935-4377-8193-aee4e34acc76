{"manifest": {"name": "uuid", "version": "3.4.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "bin\\uuid"}, "devDependencies": {"@commitlint/cli": "~8.2.0", "@commitlint/config-conventional": "~8.2.0", "eslint": "~6.4.0", "husky": "~3.0.5", "mocha": "6.2.0", "runmd": "1.2.1", "standard-version": "7.0.0"}, "scripts": {"lint": "eslint .", "test": "npm run lint && mocha test/test.js", "md": "runmd --watch --output=README.md README_js.md", "release": "standard-version", "prepare": "runmd --output=README.md README_js.md"}, "browser": {"./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js", "./lib/md5.js": "./lib/md5-browser.js"}, "repository": {"type": "git", "url": "https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-uuid-3.4.0-b23e4358afa8a202fe7a100af1f5f883f02007ee-integrity\\node_modules\\uuid\\package.json", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "readme": "<!--\n  -- This file is auto-generated from README_js.md. Changes should be made there.\n  -->\n\n# uuid [![Build Status](https://secure.travis-ci.org/kelektiv/node-uuid.svg?branch=master)](http://travis-ci.org/kelektiv/node-uuid) #\n\nSimple, fast generation of [RFC4122](http://www.ietf.org/rfc/rfc4122.txt) UUIDS.\n\nFeatures:\n\n* Support for version 1, 3, 4 and 5 UUIDs\n* Cross-platform\n* Uses cryptographically-strong random number APIs (when available)\n* Zero-dependency, small footprint (... but not [this small](https://gist.github.com/982883))\n\n[**Deprecation warning**: The use of `require('uuid')` is deprecated and will not be\nsupported after version 3.x of this module.  Instead, use `require('uuid/[v1|v3|v4|v5]')` as shown in the examples below.]\n\n## Quickstart - CommonJS (Recommended)\n\n```shell\nnpm install uuid\n```\n\nThen generate your uuid version of choice ...\n\nVersion 1 (timestamp):\n\n```javascript\nconst uuidv1 = require('uuid/v1');\nuuidv1(); // ⇨ '2c5ea4c0-4067-11e9-8bad-9b1deb4d3b7d'\n\n```\n\nVersion 3 (namespace):\n\n```javascript\nconst uuidv3 = require('uuid/v3');\n\n// ... using predefined DNS namespace (for domain names)\nuuidv3('hello.example.com', uuidv3.DNS); // ⇨ '9125a8dc-52ee-365b-a5aa-81b0b3681cf6'\n\n// ... using predefined URL namespace (for, well, URLs)\nuuidv3('http://example.com/hello', uuidv3.URL); // ⇨ 'c6235813-3ba4-3801-ae84-e0a6ebb7d138'\n\n// ... using a custom namespace\n//\n// Note: Custom namespaces should be a UUID string specific to your application!\n// E.g. the one here was generated using this modules `uuid` CLI.\nconst MY_NAMESPACE = '1b671a64-40d5-491e-99b0-da01ff1f3341';\nuuidv3('Hello, World!', MY_NAMESPACE); // ⇨ 'e8b5a51d-11c8-3310-a6ab-367563f20686'\n\n```\n\nVersion 4 (random):\n\n```javascript\nconst uuidv4 = require('uuid/v4');\nuuidv4(); // ⇨ '1b9d6bcd-bbfd-4b2d-9b5d-ab8dfbbd4bed'\n\n```\n\nVersion 5 (namespace):\n\n```javascript\nconst uuidv5 = require('uuid/v5');\n\n// ... using predefined DNS namespace (for domain names)\nuuidv5('hello.example.com', uuidv5.DNS); // ⇨ 'fdda765f-fc57-5604-a269-52a7df8164ec'\n\n// ... using predefined URL namespace (for, well, URLs)\nuuidv5('http://example.com/hello', uuidv5.URL); // ⇨ '3bbcee75-cecc-5b56-8031-b6641c1ed1f1'\n\n// ... using a custom namespace\n//\n// Note: Custom namespaces should be a UUID string specific to your application!\n// E.g. the one here was generated using this modules `uuid` CLI.\nconst MY_NAMESPACE = '1b671a64-40d5-491e-99b0-da01ff1f3341';\nuuidv5('Hello, World!', MY_NAMESPACE); // ⇨ '630eb68f-e0fa-5ecc-887a-7c7a62614681'\n\n```\n\n## API\n\n### Version 1\n\n```javascript\nconst uuidv1 = require('uuid/v1');\n\n// Incantations\nuuidv1();\nuuidv1(options);\nuuidv1(options, buffer, offset);\n```\n\nGenerate and return a RFC4122 v1 (timestamp-based) UUID.\n\n* `options` - (Object) Optional uuid state to apply. Properties may include:\n\n  * `node` - (Array) Node id as Array of 6 bytes (per 4.1.6). Default: Randomly generated ID.  See note 1.\n  * `clockseq` - (Number between 0 - 0x3fff) RFC clock sequence.  Default: An internally maintained clockseq is used.\n  * `msecs` - (Number) Time in milliseconds since unix Epoch.  Default: The current time is used.\n  * `nsecs` - (Number between 0-9999) additional time, in 100-nanosecond units. Ignored if `msecs` is unspecified. Default: internal uuid counter is used, as per *******.\n\n* `buffer` - (Array | Buffer) Array or buffer where UUID bytes are to be written.\n* `offset` - (Number) Starting index in `buffer` at which to begin writing.\n\nReturns `buffer`, if specified, otherwise the string form of the UUID\n\nNote: The default [node id](https://tools.ietf.org/html/rfc4122#section-4.1.6) (the last 12 digits in the UUID) is generated once, randomly, on process startup, and then remains unchanged for the duration of the process.\n\nExample: Generate string UUID with fully-specified options\n\n```javascript\nconst v1options = {\n  node: [0x01, 0x23, 0x45, 0x67, 0x89, 0xab],\n  clockseq: 0x1234,\n  msecs: new Date('2011-11-01').getTime(),\n  nsecs: 5678\n};\nuuidv1(v1options); // ⇨ '710b962e-041c-11e1-9234-0123456789ab'\n\n```\n\nExample: In-place generation of two binary IDs\n\n```javascript\n// Generate two ids in an array\nconst arr = new Array();\nuuidv1(null, arr, 0);  // ⇨ \n  // [\n  //    44,  94, 164, 192,  64, 103,\n  //    17, 233, 146,  52, 155,  29,\n  //   235,  77,  59, 125\n  // ]\nuuidv1(null, arr, 16); // ⇨ \n  // [\n  //    44, 94, 164, 192,  64, 103, 17, 233,\n  //   146, 52, 155,  29, 235,  77, 59, 125,\n  //    44, 94, 164, 193,  64, 103, 17, 233,\n  //   146, 52, 155,  29, 235,  77, 59, 125\n  // ]\n\n```\n\n### Version 3\n\n```javascript\nconst uuidv3 = require('uuid/v3');\n\n// Incantations\nuuidv3(name, namespace);\nuuidv3(name, namespace, buffer);\nuuidv3(name, namespace, buffer, offset);\n```\n\nGenerate and return a RFC4122 v3 UUID.\n\n* `name` - (String | Array[]) \"name\" to create UUID with\n* `namespace` - (String | Array[]) \"namespace\" UUID either as a String or Array[16] of byte values\n* `buffer` - (Array | Buffer) Array or buffer where UUID bytes are to be written.\n* `offset` - (Number) Starting index in `buffer` at which to begin writing. Default = 0\n\nReturns `buffer`, if specified, otherwise the string form of the UUID\n\nExample:\n\n```javascript\nuuidv3('hello world', MY_NAMESPACE);  // ⇨ '042ffd34-d989-321c-ad06-f60826172424'\n\n```\n\n### Version 4\n\n```javascript\nconst uuidv4 = require('uuid/v4')\n\n// Incantations\nuuidv4();\nuuidv4(options);\nuuidv4(options, buffer, offset);\n```\n\nGenerate and return a RFC4122 v4 UUID.\n\n* `options` - (Object) Optional uuid state to apply. Properties may include:\n  * `random` - (Number[16]) Array of 16 numbers (0-255) to use in place of randomly generated values\n  * `rng` - (Function) Random # generator function that returns an Array[16] of byte values (0-255)\n* `buffer` - (Array | Buffer) Array or buffer where UUID bytes are to be written.\n* `offset` - (Number) Starting index in `buffer` at which to begin writing.\n\nReturns `buffer`, if specified, otherwise the string form of the UUID\n\nExample: Generate string UUID with predefined `random` values\n\n```javascript\nconst v4options = {\n  random: [\n    0x10, 0x91, 0x56, 0xbe, 0xc4, 0xfb, 0xc1, 0xea,\n    0x71, 0xb4, 0xef, 0xe1, 0x67, 0x1c, 0x58, 0x36\n  ]\n};\nuuidv4(v4options); // ⇨ '109156be-c4fb-41ea-b1b4-efe1671c5836'\n\n```\n\nExample: Generate two IDs in a single buffer\n\n```javascript\nconst buffer = new Array();\nuuidv4(null, buffer, 0);  // ⇨ \n  // [\n  //   155, 29, 235,  77,  59,\n  //   125, 75, 173, 155, 221,\n  //    43, 13, 123,  61, 203,\n  //   109\n  // ]\nuuidv4(null, buffer, 16); // ⇨ \n  // [\n  //   155,  29, 235,  77,  59, 125,  75, 173,\n  //   155, 221,  43,  13, 123,  61, 203, 109,\n  //    27, 157, 107, 205, 187, 253,  75,  45,\n  //   155,  93, 171, 141, 251, 189,  75, 237\n  // ]\n\n```\n\n### Version 5\n\n```javascript\nconst uuidv5 = require('uuid/v5');\n\n// Incantations\nuuidv5(name, namespace);\nuuidv5(name, namespace, buffer);\nuuidv5(name, namespace, buffer, offset);\n```\n\nGenerate and return a RFC4122 v5 UUID.\n\n* `name` - (String | Array[]) \"name\" to create UUID with\n* `namespace` - (String | Array[]) \"namespace\" UUID either as a String or Array[16] of byte values\n* `buffer` - (Array | Buffer) Array or buffer where UUID bytes are to be written.\n* `offset` - (Number) Starting index in `buffer` at which to begin writing. Default = 0\n\nReturns `buffer`, if specified, otherwise the string form of the UUID\n\nExample:\n\n```javascript\nuuidv5('hello world', MY_NAMESPACE);  // ⇨ '9f282611-e0fd-5650-8953-89c8e342da0b'\n\n```\n\n## Command Line\n\nUUIDs can be generated from the command line with the `uuid` command.\n\n```shell\n$ uuid\nddeb27fb-d9a0-4624-be4d-4615062daed4\n\n$ uuid v1\n02d37060-d446-11e7-a9fa-7bdae751ebe1\n```\n\nType `uuid --help` for usage details\n\n## Testing\n\n```shell\nnpm test\n```\n\n----\nMarkdown generated from [README_js.md](README_js.md) by [![RunMD Logo](http://i.imgur.com/h0FVyzU.png)](https://github.com/broofa/runmd)", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2010-2016 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/uuid/-/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee", "type": "tarball", "reference": "https://registry.yarnpkg.com/uuid/-/uuid-3.4.0.tgz", "hash": "b23e4358afa8a202fe7a100af1f5f883f02007ee", "integrity": "sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==", "registry": "npm", "packageName": "uuid", "cacheIntegrity": "sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A== sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4="}, "registry": "npm", "hash": "b23e4358afa8a202fe7a100af1f5f883f02007ee"}