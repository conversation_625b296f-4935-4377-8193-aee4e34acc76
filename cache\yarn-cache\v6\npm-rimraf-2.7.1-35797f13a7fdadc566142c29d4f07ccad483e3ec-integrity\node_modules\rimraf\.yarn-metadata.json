{"manifest": {"name": "<PERSON><PERSON><PERSON>", "version": "2.7.1", "main": "rimraf.js", "description": "A deep deletion module for node (like `rm -rf`)", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git://github.com/isaacs/rimraf.git"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "test": "tap test/*.js"}, "bin": {"rimraf": "bin.js"}, "dependencies": {"glob": "^7.1.3"}, "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "devDependencies": {"mkdirp": "^0.5.1", "tap": "^12.1.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-rimraf-2.7.1-35797f13a7fdadc566142c29d4f07ccad483e3ec-integrity\\node_modules\\rimraf\\package.json", "readmeFilename": "README.md", "readme": "[![Build Status](https://travis-ci.org/isaacs/rimraf.svg?branch=master)](https://travis-ci.org/isaacs/rimraf) [![Dependency Status](https://david-dm.org/isaacs/rimraf.svg)](https://david-dm.org/isaacs/rimraf) [![devDependency Status](https://david-dm.org/isaacs/rimraf/dev-status.svg)](https://david-dm.org/isaacs/rimraf#info=devDependencies)\n\nThe [UNIX command](http://en.wikipedia.org/wiki/Rm_(Unix)) `rm -rf` for node.\n\nInstall with `npm install rimraf`, or just drop rimraf.js somewhere.\n\n## API\n\n`rimraf(f, [opts], callback)`\n\nThe first parameter will be interpreted as a globbing pattern for files. If you\nwant to disable globbing you can do so with `opts.disableGlob` (defaults to\n`false`). This might be handy, for instance, if you have filenames that contain\nglobbing wildcard characters.\n\nThe callback will be called with an error if there is one.  Certain\nerrors are handled for you:\n\n* Windows: `EBUSY` and `ENOTEMPTY` - rimraf will back off a maximum of\n  `opts.maxBusyTries` times before giving up, adding 100ms of wait\n  between each attempt.  The default `maxBusyTries` is 3.\n* `ENOENT` - If the file doesn't exist, rimraf will return\n  successfully, since your desired outcome is already the case.\n* `EMFILE` - Since `readdir` requires opening a file descriptor, it's\n  possible to hit `EMFILE` if too many file descriptors are in use.\n  In the sync case, there's nothing to be done for this.  But in the\n  async case, rimraf will gradually back off with timeouts up to\n  `opts.emfileWait` ms, which defaults to 1000.\n\n## options\n\n* unlink, chmod, stat, lstat, rmdir, readdir,\n  unlinkSync, chmodSync, statSync, lstatSync, rmdirSync, readdirSync\n\n    In order to use a custom file system library, you can override\n    specific fs functions on the options object.\n\n    If any of these functions are present on the options object, then\n    the supplied function will be used instead of the default fs\n    method.\n\n    Sync methods are only relevant for `rimraf.sync()`, of course.\n\n    For example:\n\n    ```javascript\n    var myCustomFS = require('some-custom-fs')\n\n    rimraf('some-thing', myCustomFS, callback)\n    ```\n\n* maxBusyTries\n\n    If an `EBUSY`, `ENOTEMPTY`, or `EPERM` error code is encountered\n    on Windows systems, then rimraf will retry with a linear backoff\n    wait of 100ms longer on each try.  The default maxBusyTries is 3.\n\n    Only relevant for async usage.\n\n* emfileWait\n\n    If an `EMFILE` error is encountered, then rimraf will retry\n    repeatedly with a linear backoff of 1ms longer on each try, until\n    the timeout counter hits this max.  The default limit is 1000.\n\n    If you repeatedly encounter `EMFILE` errors, then consider using\n    [graceful-fs](http://npm.im/graceful-fs) in your program.\n\n    Only relevant for async usage.\n\n* glob\n\n    Set to `false` to disable [glob](http://npm.im/glob) pattern\n    matching.\n\n    Set to an object to pass options to the glob module.  The default\n    glob options are `{ nosort: true, silent: true }`.\n\n    Glob version 6 is used in this module.\n\n    Relevant for both sync and async usage.\n\n* disableGlob\n\n    Set to any non-falsey value to disable globbing entirely.\n    (Equivalent to setting `glob: false`.)\n\n## rimraf.sync\n\nIt can remove stuff synchronously, too.  But that's not so good.  Use\nthe async API.  It's better.\n\n## CLI\n\nIf installed with `npm install rimraf -g` it can be used as a global\ncommand `rimraf <path> [<path> ...]` which is useful for cross platform support.\n\n## mkdirp\n\nIf you need to create a directory recursively, check out\n[mkdirp](https://github.com/substack/node-mkdirp).\n", "licenseText": "The ISC License\n\nCopyright (c) <PERSON> and Contributors\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/rimraf/-/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec", "type": "tarball", "reference": "https://registry.yarnpkg.com/rimraf/-/rimraf-2.7.1.tgz", "hash": "35797f13a7fdadc566142c29d4f07ccad483e3ec", "integrity": "sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==", "registry": "npm", "packageName": "<PERSON><PERSON><PERSON>", "cacheIntegrity": "sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w== sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w="}, "registry": "npm", "hash": "35797f13a7fdadc566142c29d4f07ccad483e3ec"}