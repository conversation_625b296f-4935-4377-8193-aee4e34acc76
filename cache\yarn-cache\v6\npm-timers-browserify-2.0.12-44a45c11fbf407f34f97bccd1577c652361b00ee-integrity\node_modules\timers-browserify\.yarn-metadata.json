{"manifest": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://convolv.es/"}, "name": "timers-browserify", "description": "timers module for browserify", "version": "2.0.12", "homepage": "https://github.com/jryans/timers-browserify", "bugs": {"url": "https://github.com/jryans/timers-browserify/issues"}, "repository": {"type": "git", "url": "git://github.com/jryans/timers-browserify.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "<PERSON>ut<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "taoqf", "email": "tao_qiu<PERSON>@126.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "wtgtybhertgeghgtwtg", "email": "<EMAIL>"}], "main": "main.js", "dependencies": {"setimmediate": "^1.0.4"}, "devDependencies": {"browserify": "~1.10.16", "connect": "~2.30.2"}, "optionalDependencies": {}, "engines": {"node": ">=0.6.0"}, "keywords": ["timers", "browserify", "browser"], "license": "MIT", "jspm": {"map": {"./main.js": {"node": "@node/timers"}}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-timers-browserify-2.0.12-44a45c11fbf407f34f97bccd1577c652361b00ee-integrity\\node_modules\\timers-browserify\\package.json", "readmeFilename": "README.md", "readme": "# Overview\n\nAdds support for the `timers` module to browserify.\n\n## Wait, isn't it already supported in the browser?\n\nThe public methods of the `timers` module are:\n\n* `setTimeout(callback, delay, [arg], [...])`\n* `clearTimeout(timeoutId)`\n* `setInterval(callback, delay, [arg], [...])`\n* `clearInterval(intervalId)`\n\nand indeed, browsers support these already.\n\n## So, why does this exist?\n\nThe `timers` module also includes some private methods used in other built-in\nNode.js modules:\n\n* `enroll(item, delay)`\n* `unenroll(item)`\n* `active(item)`\n\nThese are used to efficiently support a large quantity of timers with the same\ntimeouts by creating only a few timers under the covers.\n\nNode.js also offers the `immediate` APIs, which aren't yet available cross-browser, so we polyfill those:\n\n* `setImmediate(callback, [arg], [...])`\n* `clearImmediate(immediateId)`\n\n## I need lots of timers and want to use linked list timers as Node.js does.\n\nLinked lists are efficient when you have thousands (millions?) of timers with the same delay.\nTake a look at [timers-browserify-full](https://www.npmjs.com/package/timers-browserify-full) in this case.\n\n# License\n\n[MIT](http://jryans.mit-license.org/)\n", "licenseText": "# timers-browserify\n\nThis project uses the [MIT](http://jryans.mit-license.org/) license:\n\n    Copyright © 2012 <PERSON><PERSON> <PERSON> <<EMAIL>>\n\n    Permission is hereby granted, free of charge, to any person obtaining a\n    copy of this software and associated documentation files (the “Software”),\n    to deal in the Software without restriction, including without limitation\n    the rights to use, copy, modify, merge, publish, distribute, sublicense,\n    and/or sell copies of the Software, and to permit persons to whom the\n    Software is furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in\n    all copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n    DEALINGS IN THE SOFTWARE.\n\n# lib/node\n\nThe `lib/node` directory borrows files from joyent/node which uses the following license:\n\n    Copyright Joyent, Inc. and other Node contributors. All rights reserved.\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to\n    deal in the Software without restriction, including without limitation the\n    rights to use, copy, modify, merge, publish, distribute, sublicense, and/or\n    sell copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in\n    all copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\n    IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/timers-browserify/-/timers-browserify-2.0.12.tgz#44a45c11fbf407f34f97bccd1577c652361b00ee", "type": "tarball", "reference": "https://registry.yarnpkg.com/timers-browserify/-/timers-browserify-2.0.12.tgz", "hash": "44a45c11fbf407f34f97bccd1577c652361b00ee", "integrity": "sha512-9phl76Cqm6FhSX9Xe1ZUAMLtm1BLkKj2Qd5ApyWkXzsMRaA7dgr81kf4wJmQf/hAvg8EEyJxDo3du/0KlhPiKQ==", "registry": "npm", "packageName": "timers-browserify", "cacheIntegrity": "sha512-9phl76Cqm6FhSX9Xe1ZUAMLtm1BLkKj2Qd5ApyWkXzsMRaA7dgr81kf4wJmQf/hAvg8EEyJxDo3du/0KlhPiKQ== sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4="}, "registry": "npm", "hash": "44a45c11fbf407f34f97bccd1577c652361b00ee"}