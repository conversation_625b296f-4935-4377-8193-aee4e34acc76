{"name": "@types/koa", "version": "2.11.6", "description": "TypeScript definitions for Koa", "license": "MIT", "contributors": [{"name": "DavidCai1993", "url": "https://github.com/DavidCai1993", "githubUsername": "DavidCai1993"}, {"name": "j<PERSON><PERSON>", "url": "https://github.com/jkeylu", "githubUsername": "j<PERSON>lu"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/brikou", "githubUsername": "brikou"}, {"name": "harry<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/harryparkdotio", "githubUsername": "harry<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/chatoo2412", "githubUsername": "chatoo2412"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/koa"}, "scripts": {}, "dependencies": {"@types/accepts": "*", "@types/content-disposition": "*", "@types/cookies": "*", "@types/http-assert": "*", "@types/http-errors": "*", "@types/keygrip": "*", "@types/koa-compose": "*", "@types/node": "*"}, "typesPublisherContentHash": "557190fbae4bd7927b9ac54a1ff3a370ea131d37defaacc6f8e5e03a4cd10e86", "typeScriptVersion": "3.2"}