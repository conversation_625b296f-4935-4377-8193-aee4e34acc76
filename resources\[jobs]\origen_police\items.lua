
if Config.Framework == 'qbcore' and Config.autoSetItems then
    if GetResourceState('qb-core'):find('start') then 
        exports['qb-core']:AddItems({
            ['instant_camera']         = {['name'] = 'instant_camera',         ['label'] = 'Instant camera',      ['weight'] = 0,      ['type'] = 'item',    ['image'] = 'polaroid.png',          ['unique'] = true,     ['useable'] = true,  ['shouldClose'] = false,   ['combinable'] = nil,   ['description'] = 'A simple camera designed to take photos at a crime scene'},
            ['photo']   = {['name'] = 'photo',   ['label'] = 'Photo',      ['weight'] = 0,      ['type'] = 'item',    ['image'] = 'photos.png',       ['unique'] = true,     ['useable'] = true,  ['shouldClose'] = false,   ['combinable'] = nil,   ['description'] = 'An image'},
            ['evidence_a']   = {["name"] = "evidence_a",   ["label"] = "Evidence of bullet",     ["weight"] = 0,      ["type"] = "item",     ["image"] = "evidence_a.png",      ["unique"] = true,    ["useable"] = false,  ["shouldClose"] = false,   ["combinable"] = nil,   ["description"] = "Evidence obtained from a crime scene"},
            ['evidence_az']  = {["name"] = "evidence_az",   ["label"] = "Evidence",   ["weight"] = 0,      ["type"] = "item",     ["image"] = "evidence_az.png",      ["unique"] = true,    ["useable"] = false,  ["shouldClose"] = false,   ["combinable"] = nil,   ["description"] = "Evidence obtained from a crime scene"},
            ['evidence_b']   = {["name"] = "evidence_b",   ["label"] = "Vehicle evidence",     ["weight"] = 0,      ["type"] = "item",     ["image"] = "evidence_b.png",      ["unique"] = true,    ["useable"] = false,  ["shouldClose"] = false,   ["combinable"] = nil,   ["description"] = "Evidence obtained from a crime scene"},
            ['evidence_n']   = {["name"] = "evidence_n",   ["label"] = "Impact evidence",     ["weight"] = 0,      ["type"] = "item",     ["image"] = "evidence_n.png",      ["unique"] = true,    ["useable"] = false,  ["shouldClose"] = false,   ["combinable"] = nil,   ["description"] = "Evidence obtained from a crime scene"},
            ['evidence_ne']  = {["name"] = "evidence_ne",   ["label"] = "Footprint evidence",     ["weight"] = 0,      ["type"] = "item",     ["image"] = "evidence_ne.png",      ["unique"] = true,    ["useable"] = false,  ["shouldClose"] = false,   ["combinable"] = nil,   ["description"] = "Evidence obtained from a crime scene"},
            ['evidence_r']   = {["name"] = "evidence_r",   ["label"] = "Blood evidence",      ["weight"] = 0,      ["type"] = "item",     ["image"] = "evidence_r.png",      ["unique"] = true,    ["useable"] = false,  ["shouldClose"] = false,   ["combinable"] = nil,   ["description"] = "Evidence obtained from a crime scene"},
            ['evidence_ro']  = {["name"] = "evidence_ro",   ["label"] = "Evidence",   ["weight"] = 0,      ["type"] = "item",     ["image"] = "evidence_ro.png",      ["unique"] = true,    ["useable"] = false,  ["shouldClose"] = false,   ["combinable"] = nil,   ["description"] = "Evidence obtained from a crime scene"},
            ['evidence_v']   = {["name"] = "evidence_v",   ["label"] = "Drug evidence",      ["weight"] = 0,      ["type"] = "item",     ["image"] = "evidence_v.png",      ["unique"] = true,    ["useable"] = false,  ["shouldClose"] = false,   ["combinable"] = nil,   ["description"] = "Evidence obtained from a crime scene"},
            ['report_evidence']      = {["name"] = "report_evidence",  ["label"] = "Evidence report",     ["weight"] = 0,      ["type"] = "item",     ["image"] = "report_evidence.png",     ["unique"] = true,    ["useable"] = true,  ["shouldClose"] = false,   ["combinable"] = nil,   ["description"] = "Here there can be collected up to 4 pieces of evidence"},
            ['k9']      = {["name"] = "k9",     ["label"] = "K9 whistle",    ["weight"] = 0,      ["type"] = "item",     ["image"] = "whistle.png",   ["unique"] = true,    ["useable"] = true,  ["shouldClose"] = false,   ["combinable"] = nil,   ["description"] = "Use the whistle to call the K9 unit"},
            ['lspd_badge']    = {["name"] = "lspd_badge",   ["label"] = "Police badge",  ["weight"] = 0,      ["type"] = "item",     ["image"] = "lspd_badge.png",  ["unique"] = true,    ["useable"] = true,  ["shouldClose"] = false,   ["combinable"] = nil,   ["description"] = "Your identification as a police officer, includes your rank and badge number"},
            ['bcsd_badge']    = {["name"] = "bcsd_badge",   ["label"] = "Sheriff badge",  ["weight"] = 0,      ["type"] = "item",     ["image"] = "bcsd_badge.png",  ["unique"] = true,    ["useable"] = true,  ["shouldClose"] = false,   ["combinable"] = nil,   ["description"] = "Your identification as a sheriff's agent, including your rank and badge number"},
            ['police_cad']    = {["name"] = "police_cad",   ["label"] = "Police tablet",  ["weight"] = 0,      ["type"] = "item",     ["image"] = "tablet.png",  ["unique"] = true,    ["useable"] = true,  ["shouldClose"] = false,   ["combinable"] = nil,   ["description"] = "Your personal tablet with all the information of the San Andreas police"},
            ['megaphone']    = {["name"] = "megaphone",   ["label"] = "Megaphone",  ["weight"] = 0,      ["type"] = "item",     ["image"] = "megaphone.png",  ["unique"] = true,    ["useable"] = true,  ["shouldClose"] = false,   ["combinable"] = nil,   ["description"] = "A megaphone to make your voice heard"},
            ['fib_badge']    = {["name"] = "fib_badge",   ["label"] = "FIB badge",  ["weight"] = 0,      ["type"] = "item",     ["image"] = "fib_badge.png",  ["unique"] = true,    ["useable"] = true,  ["shouldClose"] = false,   ["combinable"] = nil,   ["description"] = "Your identification as a police officer, includes your rank and badge number"},
        })
    end
end