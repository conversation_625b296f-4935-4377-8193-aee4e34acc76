{"name": "@types/http-assert", "version": "1.5.1", "description": "TypeScript definitions for http-assert", "license": "MIT", "contributors": [{"name": "j<PERSON><PERSON>", "url": "https://github.com/jkeylu", "githubUsername": "j<PERSON>lu"}, {"name": "<PERSON>", "url": "https://github.com/stripedpajamas", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/sapfear", "githubUsername": "sapfear"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/http-assert"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "25e5ec6bb6a8c0e3ba83fc4a67c444744defd0d4d2707b09649fc09dbb271083", "typeScriptVersion": "2.3"}