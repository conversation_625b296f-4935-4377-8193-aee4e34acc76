{"manifest": {"name": "html-minifier", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "version": "3.5.21", "keywords": ["cli", "compress", "compressor", "css", "html", "htmlmin", "javascript", "min", "minification", "minifier", "minify", "optimize", "optimizer", "pack", "packer", "parse", "parser", "uglifier", "uglify"], "homepage": "https://kangax.github.io/html-minifier/", "author": {"name": "<PERSON><PERSON><PERSON> \"kangax\" <PERSON>"}, "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://perfectionkills.com/"}], "contributors": [{"name": "<PERSON>", "url": "https://github.com/gilmoreorless"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bin": {"html-minifier": "cli.js"}, "main": "src/htmlminifier.js", "repository": {"type": "git", "url": "git+https://github.com/kangax/html-minifier.git"}, "bugs": {"url": "https://github.com/kangax/html-minifier/issues"}, "engines": {"node": ">=4"}, "scripts": {"dist": "grunt dist", "test": "grunt test"}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.2.x", "commander": "2.17.x", "he": "1.2.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.4.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.3.x", "grunt-contrib-uglify": "3.4.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunit": "2.x"}, "benchmarkDependencies": {"brotli": "1.3.x", "chalk": "2.4.x", "cli-table": "0.3.x", "lzma": "2.3.x", "minimize": "2.2.x", "progress": "2.0.x"}, "files": ["src/*.js", "cli.js", "sample-cli-config-file.conf"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-html-minifier-3.5.21-d0040e054730e354db008463593194015212d20c-integrity\\node_modules\\html-minifier\\package.json", "readmeFilename": "README.md", "readme": "# HTMLMinifier\n\n[![NPM version](https://img.shields.io/npm/v/html-minifier.svg)](https://www.npmjs.com/package/html-minifier)\n[![Build Status](https://img.shields.io/travis/kangax/html-minifier.svg)](https://travis-ci.org/kangax/html-minifier)\n[![Dependency Status](https://img.shields.io/david/kangax/html-minifier.svg)](https://david-dm.org/kangax/html-minifier)\n\n[HTMLMinifier](https://kangax.github.io/html-minifier/) is a highly **configurable**, **well-tested**, JavaScript-based HTML minifier.\n\nSee [corresponding blog post](http://perfectionkills.com/experimenting-with-html-minifier/) for all the gory details of [how it works](http://perfectionkills.com/experimenting-with-html-minifier/#how_it_works), [description of each option](http://perfectionkills.com/experimenting-with-html-minifier/#options), [testing results](http://perfectionkills.com/experimenting-with-html-minifier/#field_testing) and [conclusions](http://perfectionkills.com/experimenting-with-html-minifier/#cost_and_benefits).\n\n[Test suite is available online](https://kangax.github.io/html-minifier/tests/).\n\nAlso see corresponding [Ruby wrapper](https://github.com/stereobooster/html_minifier), and for Node.js, [Grunt plugin](https://github.com/gruntjs/grunt-contrib-htmlmin), [Gulp module](https://github.com/jonschlinkert/gulp-htmlmin), [Koa middleware wrapper](https://github.com/koajs/html-minifier) and [Express middleware wrapper](https://github.com/melonmanchan/express-minify-html).\n\nFor lint-like capabilities take a look at [HTMLLint](https://github.com/kangax/html-lint).\n\n## Minification comparison\n\nHow does HTMLMinifier compare to other solutions — [HTML Minifier from Will Peavy](http://www.willpeavy.com/minifier/) (1st result in [Google search for \"html minifier\"](https://www.google.com/#q=html+minifier)) as well as [htmlcompressor.com](http://htmlcompressor.com) and [minimize](https://github.com/Swaagie/minimize)?\n\n| Site                                                                         | Original size *(KB)* | HTMLMinifier | minimize | Will Peavy | htmlcompressor.com |\n| ---------------------------------------------------------------------------- |:--------------------:| ------------:| --------:| ----------:| ------------------:|\n| [Google](https://www.google.com/)                                            | 48                   | **44**       | 48       | 49         | 48                 |\n| [HTMLMinifier](https://github.com/kangax/html-minifier)                      | 154                  | **117**      | 128      | 133        | 128                |\n| [Twitter](https://twitter.com/)                                              | 203                  | **162**      | 195      | 219        | 195                |\n| [Stack Overflow](https://stackoverflow.com/)                                 | 254                  | **196**      | 208      | 216        | 205                |\n| [Bootstrap CSS](https://getbootstrap.com/docs/3.3/css/)                      | 271                  | **260**      | 269      | 229        | 269                |\n| [BBC](https://www.bbc.co.uk/)                                                | 288                  | **230**      | 280      | 281        | 272                |\n| [Amazon](https://www.amazon.co.uk/)                                          | 508                  | **439**      | 495      | 501        | n/a                |\n| [Wikipedia](https://en.wikipedia.org/wiki/President_of_the_United_States)    | 533                  | **434**      | 517      | 536        | 517                |\n| [New York Times](https://mathiasbynens.be/_tmp/nyt.html)                     | 699                  | **619**      | 693      | 683        | n/a                |\n| [NBC](https://www.nbc.com/)                                                  | 700                  | **657**      | 698      | 699        | n/a                |\n| [Eloquent Javascript](https://eloquentjavascript.net/1st_edition/print.html) | 870                  | **815**      | 840      | 864        | n/a                |\n| [ES6 table](https://kangax.github.io/compat-table/es6/)                      | 5308                 | **4529**     | 5025     | n/a        | n/a                |\n| [ES draft](https://tc39.github.io/ecma262/)                                  | 6082                 | **5456**     | 5624     | n/a        | n/a                |\n\n## Options Quick Reference\n\nMost of the options are disabled by default.\n\n| Option                         | Description     | Default |\n|--------------------------------|-----------------|---------|\n| `caseSensitive`                | Treat attributes in case sensitive manner (useful for custom HTML tags) | `false` |\n| `collapseBooleanAttributes`    | [Omit attribute values from boolean attributes](http://perfectionkills.com/experimenting-with-html-minifier/#collapse_boolean_attributes) | `false` |\n| `collapseInlineTagWhitespace`  | Don't leave any spaces between `display:inline;` elements when collapsing. Must be used in conjunction with `collapseWhitespace=true` | `false` |\n| `collapseWhitespace`           | [Collapse white space that contributes to text nodes in a document tree](http://perfectionkills.com/experimenting-with-html-minifier/#collapse_whitespace) | `false` |\n| `conservativeCollapse`         | Always collapse to 1 space (never remove it entirely). Must be used in conjunction with `collapseWhitespace=true` | `false` |\n| `customAttrAssign`             | Arrays of regex'es that allow to support custom attribute assign expressions (e.g. `'<div flex?=\"{{mode != cover}}\"></div>'`) | `[ ]` |\n| `customAttrCollapse`           | Regex that specifies custom attribute to strip newlines from (e.g. `/ng-class/`) | |\n| `customAttrSurround`           | Arrays of regex'es that allow to support custom attribute surround expressions (e.g. `<input {{#if value}}checked=\"checked\"{{/if}}>`) | `[ ]` |\n| `customEventAttributes`        | Arrays of regex'es that allow to support custom event attributes for `minifyJS` (e.g. `ng-click`) | `[ /^on[a-z]{3,}$/ ]` |\n| `decodeEntities`               | Use direct Unicode characters whenever possible | `false` |\n| `html5`                        | Parse input according to HTML5 specifications | `true` |\n| `ignoreCustomComments`         | Array of regex'es that allow to ignore certain comments, when matched | `[ /^!/ ]` |\n| `ignoreCustomFragments`        | Array of regex'es that allow to ignore certain fragments, when matched (e.g. `<?php ... ?>`, `{{ ... }}`, etc.)  | `[ /<%[\\s\\S]*?%>/, /<\\?[\\s\\S]*?\\?>/ ]` |\n| `includeAutoGeneratedTags`     | Insert tags generated by HTML parser | `true` |\n| `keepClosingSlash`             | Keep the trailing slash on singleton elements | `false` |\n| `maxLineLength`                | Specify a maximum line length. Compressed output will be split by newlines at valid HTML split-points |\n| `minifyCSS`                    | Minify CSS in style elements and style attributes (uses [clean-css](https://github.com/jakubpawlowicz/clean-css)) | `false` (could be `true`, `Object`, `Function(text, type)`) |\n| `minifyJS`                     | Minify JavaScript in script elements and event attributes (uses [UglifyJS](https://github.com/mishoo/UglifyJS2)) | `false` (could be `true`, `Object`, `Function(text, inline)`) |\n| `minifyURLs`                   | Minify URLs in various attributes (uses [relateurl](https://github.com/stevenvachon/relateurl)) | `false` (could be `String`, `Object`, `Function(text)`) |\n| `preserveLineBreaks`           | Always collapse to 1 line break (never remove it entirely) when whitespace between tags include a line break. Must be used in conjunction with `collapseWhitespace=true` | `false` |\n| `preventAttributesEscaping`    | Prevents the escaping of the values of attributes | `false` |\n| `processConditionalComments`   | Process contents of conditional comments through minifier | `false` |\n| `processScripts`               | Array of strings corresponding to types of script elements to process through minifier (e.g. `text/ng-template`, `text/x-handlebars-template`, etc.) | `[ ]` |\n| `quoteCharacter`               | Type of quote to use for attribute values (' or \") | |\n| `removeAttributeQuotes`        | [Remove quotes around attributes when possible](http://perfectionkills.com/experimenting-with-html-minifier/#remove_attribute_quotes) | `false` |\n| `removeComments`               | [Strip HTML comments](http://perfectionkills.com/experimenting-with-html-minifier/#remove_comments) | `false` |\n| `removeEmptyAttributes`        | [Remove all attributes with whitespace-only values](http://perfectionkills.com/experimenting-with-html-minifier/#remove_empty_or_blank_attributes) | `false` (could be `true`, `Function(attrName, tag)`) |\n| `removeEmptyElements`          | [Remove all elements with empty contents](http://perfectionkills.com/experimenting-with-html-minifier/#remove_empty_elements) | `false` |\n| `removeOptionalTags`           | [Remove optional tags](http://perfectionkills.com/experimenting-with-html-minifier/#remove_optional_tags) | `false` |\n| `removeRedundantAttributes`    | [Remove attributes when value matches default.](http://perfectionkills.com/experimenting-with-html-minifier/#remove_redundant_attributes) | `false` |\n| `removeScriptTypeAttributes`   | Remove `type=\"text/javascript\"` from `script` tags. Other `type` attribute values are left intact | `false` |\n| `removeStyleLinkTypeAttributes`| Remove `type=\"text/css\"` from `style` and `link` tags. Other `type` attribute values are left intact | `false` |\n| `removeTagWhitespace`          | Remove space between attributes whenever possible. **Note that this will result in invalid HTML!** | `false` |\n| `sortAttributes`               | [Sort attributes by frequency](#sorting-attributes--style-classes) | `false` |\n| `sortClassName`                | [Sort style classes by frequency](#sorting-attributes--style-classes) | `false` |\n| `trimCustomFragments`          | Trim white space around `ignoreCustomFragments`. | `false` |\n| `useShortDoctype`              | [Replaces the `doctype` with the short (HTML5) doctype](http://perfectionkills.com/experimenting-with-html-minifier/#use_short_doctype) | `false` |\n\n### Sorting attributes / style classes\n\nMinifier options like `sortAttributes` and `sortClassName` won't impact the plain-text size of the output. However, they form long repetitive chains of characters that should improve compression ratio of gzip used in HTTP compression.\n\n## Special cases\n\n### Ignoring chunks of markup\n\nIf you have chunks of markup you would like preserved, you can wrap them `<!-- htmlmin:ignore -->`.\n\n### Preserving SVG tags\n\nSVG tags are automatically recognized, and when they are minified, both case-sensitivity and closing-slashes are preserved, regardless of the minification settings used for the rest of the file.\n\n### Working with invalid markup\n\nHTMLMinifier **can't work with invalid or partial chunks of markup**. This is because it parses markup into a tree structure, then modifies it (removing anything that was specified for removal, ignoring anything that was specified to be ignored, etc.), then it creates a markup out of that tree and returns it.\n\nInput markup (e.g. `<p id=\"\">foo`)\n\n↓\n\nInternal representation of markup in a form of tree (e.g. `{ tag: \"p\", attr: \"id\", children: [\"foo\"] }`)\n\n↓\n\nTransformation of internal representation (e.g. removal of `id` attribute)\n\n↓\n\nOutput of resulting markup (e.g. `<p>foo</p>`)\n\nHTMLMinifier can't know that original markup was only half of the tree; it does its best to try to parse it as a full tree and it loses information about tree being malformed or partial in the beginning. As a result, it can't create a partial/malformed tree at the time of the output.\n\n## Installation Instructions\n\nFrom NPM for use as a command line app:\n\n```shell\nnpm install html-minifier -g\n```\n\nFrom NPM for programmatic use:\n\n```shell\nnpm install html-minifier\n```\n\nFrom Git:\n\n```shell\ngit clone git://github.com/kangax/html-minifier.git\ncd html-minifier\nnpm link .\n```\n\n## Usage\n\nNote that almost all options are disabled by default. For command line usage please see `html-minifier --help` for a list of available options. Experiment and find what works best for you and your project.\n\n* **Sample command line:** ``html-minifier --collapse-whitespace --remove-comments --remove-optional-tags --remove-redundant-attributes --remove-script-type-attributes --remove-tag-whitespace --use-short-doctype --minify-css true --minify-js true``\n\n### Node.js\n\n```js\nvar minify = require('html-minifier').minify;\nvar result = minify('<p title=\"blah\" id=\"moo\">foo</p>', {\n  removeAttributeQuotes: true\n});\nresult; // '<p title=blah id=moo>foo</p>'\n```\n\n## Running benchmarks\n\nBenchmarks for minified HTML:\n\n```shell\nnode benchmark.js\n```\n", "licenseText": "Copyright (c) 2010-2018 <PERSON><PERSON><PERSON> \"kangax\" <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person\nobtaining a copy of this software and associated documentation\nfiles (the \"Software\"), to deal in the Software without\nrestriction, including without limitation the rights to use,\ncopy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the\nSoftware is furnished to do so, subject to the following\nconditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\nHOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\nWHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\nOTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/html-minifier/-/html-minifier-3.5.21.tgz#d0040e054730e354db008463593194015212d20c", "type": "tarball", "reference": "https://registry.yarnpkg.com/html-minifier/-/html-minifier-3.5.21.tgz", "hash": "d0040e054730e354db008463593194015212d20c", "integrity": "sha512-LKUKwuJDhxNa3uf/LPR/KVjm/l3rBqtYeCOAekvG8F1vItxMUpueGd94i/asDDr8/1u7InxzFA5EeGjhhG5mMA==", "registry": "npm", "packageName": "html-minifier", "cacheIntegrity": "sha512-LKUKwuJDhxNa3uf/LPR/KVjm/l3rBqtYeCOAekvG8F1vItxMUpueGd94i/asDDr8/1u7InxzFA5EeGjhhG5mMA== sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw="}, "registry": "npm", "hash": "d0040e054730e354db008463593194015212d20c"}