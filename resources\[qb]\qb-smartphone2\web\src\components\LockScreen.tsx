import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { usePhone } from '../contexts/PhoneContext';
import { useTheme } from '../contexts/ThemeContext';
import { fetchNui } from '../utils/fetchNui';
import { formatTime, formatDate } from '../utils/misc';
import './LockScreen.css';

const LockScreen: React.FC = () => {
  const { phoneData, unlockPhone } = usePhone();
  const { colors } = useTheme();
  const [pin, setPin] = useState('');
  const [showPinInput, setShowPinInput] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [isLocked, setIsLocked] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleUnlock = async (method: 'pin' | 'fingerprint') => {
    try {
      const result = await fetchNui('unlockPhone', { 
        method, 
        pin: method === 'pin' ? pin : undefined 
      });

      if (result.success) {
        unlockPhone();
        setPin('');
        setAttempts(0);
      } else {
        setAttempts(result.attempts || 0);
        setPin('');
        
        if (result.attempts >= 3) {
          setIsLocked(true);
          setTimeout(() => setIsLocked(false), 300000); // 5 minutes lockout
        }
      }
    } catch (error) {
      console.error('Unlock error:', error);
    }
  };

  const handlePinInput = (digit: string) => {
    if (pin.length < 4) {
      const newPin = pin + digit;
      setPin(newPin);
      
      if (newPin.length === 4) {
        setTimeout(() => handleUnlock('pin'), 100);
      }
    }
  };

  const handlePinDelete = () => {
    setPin(pin.slice(0, -1));
  };

  const handleFingerprintUnlock = () => {
    if (phoneData?.settings.fingerprint_enabled) {
      handleUnlock('fingerprint');
    }
  };

  const wallpaper = phoneData?.settings.wallpaper || 'default.jpg';
  const timeString = formatTime(currentTime);
  const dateString = formatDate(currentTime);

  return (
    <motion.div
      className="lock-screen"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      style={{
        backgroundImage: `url(/wallpapers/${wallpaper})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    >
      {/* Overlay */}
      <div className="lock-overlay" />
      
      {/* Time and Date */}
      <div className="lock-time-container">
        <motion.div
          className="lock-time"
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {timeString}
        </motion.div>
        <motion.div
          className="lock-date"
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          {dateString}
        </motion.div>
      </div>

      {/* Notifications preview */}
      <div className="lock-notifications">
        {/* TODO: Add notification previews */}
      </div>

      {/* Unlock area */}
      <div className="lock-unlock-area">
        {!showPinInput ? (
          <motion.div
            className="lock-unlock-prompt"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            <div className="unlock-text">Potáhni nahoru pro odemknutí</div>
            <div className="unlock-methods">
              {phoneData?.settings.pin_code && (
                <button
                  className="unlock-method-btn"
                  onClick={() => setShowPinInput(true)}
                  style={{ backgroundColor: colors.surface, color: colors.text }}
                >
                  <i className="fas fa-lock" />
                  PIN
                </button>
              )}
              {phoneData?.settings.fingerprint_enabled === 1 && (
                <button
                  className="unlock-method-btn"
                  onClick={handleFingerprintUnlock}
                  style={{ backgroundColor: colors.surface, color: colors.text }}
                >
                  <i className="fas fa-fingerprint" />
                  Otisk
                </button>
              )}
            </div>
          </motion.div>
        ) : (
          <motion.div
            className="pin-input-container"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
          >
            <div className="pin-title">Zadej PIN</div>
            
            {attempts > 0 && (
              <div className="pin-error">
                Nesprávný PIN ({attempts}/3)
              </div>
            )}
            
            {isLocked && (
              <div className="pin-locked">
                Telefon je zamčený na 5 minut
              </div>
            )}
            
            <div className="pin-dots">
              {[...Array(4)].map((_, index) => (
                <div
                  key={index}
                  className={`pin-dot ${index < pin.length ? 'filled' : ''}`}
                  style={{
                    backgroundColor: index < pin.length ? colors.primary : 'transparent',
                    borderColor: colors.text
                  }}
                />
              ))}
            </div>
            
            <div className="pin-keypad">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((digit) => (
                <button
                  key={digit}
                  className="pin-key"
                  onClick={() => handlePinInput(digit.toString())}
                  disabled={isLocked}
                  style={{ 
                    backgroundColor: colors.surface, 
                    color: colors.text,
                    opacity: isLocked ? 0.5 : 1
                  }}
                >
                  {digit}
                </button>
              ))}
              <button
                className="pin-key"
                onClick={() => setShowPinInput(false)}
                style={{ backgroundColor: colors.surface, color: colors.text }}
              >
                <i className="fas fa-times" />
              </button>
              <button
                className="pin-key"
                onClick={() => handlePinInput('0')}
                disabled={isLocked}
                style={{ 
                  backgroundColor: colors.surface, 
                  color: colors.text,
                  opacity: isLocked ? 0.5 : 1
                }}
              >
                0
              </button>
              <button
                className="pin-key"
                onClick={handlePinDelete}
                disabled={isLocked}
                style={{ 
                  backgroundColor: colors.surface, 
                  color: colors.text,
                  opacity: isLocked ? 0.5 : 1
                }}
              >
                <i className="fas fa-backspace" />
              </button>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default LockScreen;
