{"manifest": {"name": "tslib", "author": {"name": "Microsoft Corp."}, "homepage": "https://www.typescriptlang.org/", "version": "1.14.1", "license": "0BSD", "description": "Runtime library for TypeScript helper functions", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "repository": {"type": "git", "url": "https://github.com/Microsoft/tslib.git"}, "main": "tslib.js", "module": "tslib.es6.js", "jsnext:main": "tslib.es6.js", "typings": "tslib.d.ts", "sideEffects": false, "exports": {".": {"module": "./tslib.es6.js", "import": "./modules/index.js", "default": "./tslib.js"}, "./": "./"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-tslib-1.14.1-cf2d38bdc34a134bcaf1091c41f6619e2f672d00-integrity\\node_modules\\tslib\\package.json", "readmeFilename": "README.md", "readme": "# tslib\n\nThis is a runtime library for [TypeScript](http://www.typescriptlang.org/) that contains all of the TypeScript helper functions.\n\nThis library is primarily used by the `--importHelpers` flag in TypeScript.\nWhen using `--importHelpers`, a module that uses helper functions like `__extends` and `__assign` in the following emitted file:\n\n```ts\nvar __assign = (this && this.__assign) || Object.assign || function(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n        s = arguments[i];\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n            t[p] = s[p];\n    }\n    return t;\n};\nexports.x = {};\nexports.y = __assign({}, exports.x);\n\n```\n\nwill instead be emitted as something like the following:\n\n```ts\nvar tslib_1 = require(\"tslib\");\nexports.x = {};\nexports.y = tslib_1.__assign({}, exports.x);\n```\n\nBecause this can avoid duplicate declarations of things like `__extends`, `__assign`, etc., this means delivering users smaller files on average, as well as less runtime overhead.\nFor optimized bundles with TypeScript, you should absolutely consider using `tslib` and `--importHelpers`.\n\n# Installing\n\nFor the latest stable version, run:\n\n## npm\n\n```sh\n# TypeScript 2.3.3 or later\nnpm install tslib\n\n# TypeScript 2.3.2 or earlier\nnpm install tslib@1.6.1\n```\n\n## yarn\n\n```sh\n# TypeScript 2.3.3 or later\nyarn add tslib\n\n# TypeScript 2.3.2 or earlier\nyarn add tslib@1.6.1\n```\n\n## bower\n\n```sh\n# TypeScript 2.3.3 or later\nbower install tslib\n\n# TypeScript 2.3.2 or earlier\nbower install tslib@1.6.1\n```\n\n## JSPM\n\n```sh\n# TypeScript 2.3.3 or later\njspm install tslib\n\n# TypeScript 2.3.2 or earlier\njspm install tslib@1.6.1\n```\n\n# Usage\n\nSet the `importHelpers` compiler option on the command line:\n\n```\ntsc --importHelpers file.ts\n```\n\nor in your tsconfig.json:\n\n```json\n{\n    \"compilerOptions\": {\n        \"importHelpers\": true\n    }\n}\n```\n\n#### For bower and JSPM users\n\nYou will need to add a `paths` mapping for `tslib`, e.g. For Bower users:\n\n```json\n{\n    \"compilerOptions\": {\n        \"module\": \"amd\",\n        \"importHelpers\": true,\n        \"baseUrl\": \"./\",\n        \"paths\": {\n            \"tslib\" : [\"bower_components/tslib/tslib.d.ts\"]\n        }\n    }\n}\n```\n\nFor JSPM users:\n\n```json\n{\n    \"compilerOptions\": {\n        \"module\": \"system\",\n        \"importHelpers\": true,\n        \"baseUrl\": \"./\",\n        \"paths\": {\n            \"tslib\" : [\"jspm_packages/npm/tslib@1.[version].0/tslib.d.ts\"]\n        }\n    }\n}\n```\n\n\n# Contribute\n\nThere are many ways to [contribute](https://github.com/Microsoft/TypeScript/blob/master/CONTRIBUTING.md) to TypeScript.\n\n* [Submit bugs](https://github.com/Microsoft/TypeScript/issues) and help us verify fixes as they are checked in.\n* Review the [source code changes](https://github.com/Microsoft/TypeScript/pulls).\n* Engage with other TypeScript users and developers on [StackOverflow](http://stackoverflow.com/questions/tagged/typescript).\n* Join the [#typescript](http://twitter.com/#!/search/realtime/%23typescript) discussion on Twitter.\n* [Contribute bug fixes](https://github.com/Microsoft/TypeScript/blob/master/CONTRIBUTING.md).\n\n# Documentation\n\n* [Quick tutorial](http://www.typescriptlang.org/Tutorial)\n* [Programming handbook](http://www.typescriptlang.org/Handbook)\n* [Homepage](http://www.typescriptlang.org/)\n", "licenseText": "Copyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTAB<PERSON>ITY\nAND F<PERSON>NE<PERSON>. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/tslib/-/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00", "type": "tarball", "reference": "https://registry.yarnpkg.com/tslib/-/tslib-1.14.1.tgz", "hash": "cf2d38bdc34a134bcaf1091c41f6619e2f672d00", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "registry": "npm", "packageName": "tslib", "cacheIntegrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg== sha1-zy04vcNKE0vK8QkcQfZhni9nLQA="}, "registry": "npm", "hash": "cf2d38bdc34a134bcaf1091c41f6619e2f672d00"}