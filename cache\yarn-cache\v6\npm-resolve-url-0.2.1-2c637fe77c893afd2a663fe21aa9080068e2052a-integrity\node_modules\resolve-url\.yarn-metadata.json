{"manifest": {"name": "resolve-url", "version": "0.2.1", "description": "Like Node.js’ `path.resolve`/`url.resolve` for the browser.", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "resolve-url.js", "repository": {"type": "git", "url": "https://github.com/lydell/resolve-url.git"}, "keywords": ["resolve", "url"], "scripts": {"test": "jshint resolve-url.js test/ && testling -u"}, "devDependencies": {"testling": "~1.6.0", "jshint": "~2.4.3", "tape": "~2.5.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "chrome/latest", "firefox/latest", "opera/12", "opera/latest", "safari/5", "iphone/6", "android-browser/4"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-resolve-url-0.2.1-2c637fe77c893afd2a663fe21aa9080068e2052a-integrity\\node_modules\\resolve-url\\package.json", "readmeFilename": "readme.md", "readme": "Overview\n========\n\n[![browser support](https://ci.testling.com/lydell/resolve-url.png)](https://ci.testling.com/lydell/resolve-url)\n\nLike Node.js’ [`path.resolve`]/[`url.resolve`] for the browser.\n\n```js\nvar resolveUrl = require(\"resolve-url\")\n\nwindow.location\n// https://example.com/articles/resolving-urls/edit\n\nresolveUrl(\"remove\")\n// https://example.com/articles/resolving-urls/remove\n\nresolveUrl(\"/static/scripts/app.js\")\n// https://example.com/static/scripts/app.js\n\n// Imagine /static/scripts/app.js contains `//# sourceMappingURL=../source-maps/app.js.map`\nresolveUrl(\"/static/scripts/app.js\", \"../source-maps/app.js.map\")\n// https://example.com/static/source-maps/app.js.map\n\nresolveUrl(\"/static/scripts/app.js\", \"../source-maps/app.js.map\", \"../coffee/app.coffee\")\n// https://example.com/static/coffee/app.coffee\n\nresolveUrl(\"//cdn.example.com/jquery.js\")\n// https://cdn.example.com/jquery.js\n\nresolveUrl(\"http://foo.org/\")\n// http://foo.org/\n```\n\n\nInstallation\n============\n\n- `npm install resolve-url`\n- `bower install resolve-url`\n- `component install lydell/resolve-url`\n\nWorks with CommonJS, AMD and browser globals, through UMD.\n\n\nUsage\n=====\n\n### `resolveUrl(...urls)` ###\n\nPass one or more urls. Resolves the last one to an absolute url, using the\nprevious ones and `window.location`.\n\nIt’s like starting out on `window.location`, and then clicking links with the\nurls as `href` attributes in order, from left to right.\n\nUnlike Node.js’ [`path.resolve`], this function always goes through all of the\narguments, from left to right. `path.resolve` goes from right to left and only\nin the worst case goes through them all. Should that matter.\n\nActually, the function is _really_ like clicking a lot of links in series: An\nactual `<a>` gets its `href` attribute set for each url! This means that the\nurl resolution of the browser is used, which makes this module really\nlight-weight.\n\nAlso note that this functions deals with urls, not paths, so in that respect it\nhas more in common with Node.js’ [`url.resolve`]. But the arguments are more\nlike [`path.resolve`].\n\n[`path.resolve`]: http://nodejs.org/api/path.html#path_path_resolve_from_to\n[`url.resolve`]: http://nodejs.org/api/url.html#url_url_resolve_from_to\n\n\nTests\n=====\n\nRun `npm test`, which lints the code and then gives you a link to open in a\nbrowser of choice (using `testling`).\n\n\nLicense\n=======\n\n[The X11 (“MIT”) License](LICENSE).\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2013 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/resolve-url/-/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a", "type": "tarball", "reference": "https://registry.yarnpkg.com/resolve-url/-/resolve-url-0.2.1.tgz", "hash": "2c637fe77c893afd2a663fe21aa9080068e2052a", "integrity": "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=", "registry": "npm", "packageName": "resolve-url", "cacheIntegrity": "sha512-ZuF55hVUQaaczgOIwqWzkEcEidmlD/xl44x1UZnhOXcYuFN2S6+rcxpG+C1N3So0wvNI3DmJICUFfu2SxhBmvg== sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo="}, "registry": "npm", "hash": "2c637fe77c893afd2a663fe21aa9080068e2052a"}