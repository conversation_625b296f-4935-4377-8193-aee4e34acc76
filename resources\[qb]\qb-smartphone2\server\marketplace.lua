local QBCore = exports['qb-core']:GetCoreObject()

-- Kategorie pro marketplace
local MarketplaceCategories = {
    'vehicles', 'electronics', 'clothing', 'furniture', 'tools', 'weapons', 'drugs', 'services', 'real_estate', 'other'
}

-- Callback pro získání inzerátů
QBCore.Functions.CreateCallback('qb-smartphone2:server:getMarketplaceAds', function(source, cb, filters)
    local query = 'SELECT * FROM phone_marketplace WHERE status = "active"'
    local params = {}
    
    -- P<PERSON><PERSON><PERSON><PERSON> filtrů
    if filters then
        if filters.category and filters.category ~= 'all' then
            query = query .. ' AND category = ?'
            table.insert(params, filters.category)
        end
        
        if filters.minPrice then
            query = query .. ' AND price >= ?'
            table.insert(params, filters.minPrice)
        end
        
        if filters.maxPrice then
            query = query .. ' AND price <= ?'
            table.insert(params, filters.maxPrice)
        end
        
        if filters.search then
            query = query .. ' AND (title LIKE ? OR description LIKE ?)'
            table.insert(params, '%' .. filters.search .. '%')
            table.insert(params, '%' .. filters.search .. '%')
        end
    end
    
    query = query .. ' ORDER BY created_at DESC LIMIT 50'
    
    MySQL.Async.fetchAll(query, params, function(result)
        cb(result or {})
    end)
end)

-- Event pro vytvoření inzerátu
RegisterNetEvent('qb-smartphone2:server:createMarketplaceAd', function(adData)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    local phoneNumber = Player.PlayerData.charinfo.phone
    local sellerName = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname
    
    -- Kontrola limitu inzerátů
    MySQL.Async.fetchAll('SELECT COUNT(*) as count FROM phone_marketplace WHERE citizenid = ? AND status = "active"', {citizenid}, function(result)
        local adCount = result[1].count
        
        if adCount >= Config.MaxAds then
            TriggerClientEvent('QBCore:Notify', src, 'Dosáhl jsi maximálního počtu aktivních inzerátů!', 'error')
            return
        end
        
        -- Kontrola poplatku za inzerát
        if Config.AdFee > 0 then
            local playerMoney = Player.PlayerData.money.cash or 0
            if playerMoney < Config.AdFee then
                TriggerClientEvent('QBCore:Notify', src, 'Nemáš dostatek peněz na poplatek za inzerát!', 'error')
                return
            end
            
            Player.Functions.RemoveMoney('cash', Config.AdFee, 'marketplace-ad-fee')
        end
        
        -- Výpočet data vypršení
        local expiresAt = os.date('%Y-%m-%d %H:%M:%S', os.time() + (Config.AdDuration * 24 * 60 * 60))
        
        -- Vytvoření inzerátu
        MySQL.Async.execute([[
            INSERT INTO phone_marketplace 
            (citizenid, seller_name, seller_number, title, description, price, category, images, expires_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ]], {
            citizenid,
            sellerName,
            phoneNumber,
            adData.title,
            adData.description,
            adData.price,
            adData.category,
            json.encode(adData.images or {}),
            expiresAt
        }, function(insertId)
            if insertId then
                TriggerClientEvent('QBCore:Notify', src, 'Inzerát byl vytvořen!', 'success')
                TriggerClientEvent('qb-smartphone2:client:adCreated', src, insertId)
            else
                TriggerClientEvent('QBCore:Notify', src, 'Chyba při vytváření inzerátu!', 'error')
            end
        end)
    end)
end)

-- Event pro úpravu inzerátu
RegisterNetEvent('qb-smartphone2:server:editMarketplaceAd', function(adId, adData)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Kontrola vlastnictví inzerátu
    MySQL.Async.fetchAll('SELECT * FROM phone_marketplace WHERE id = ? AND citizenid = ?', {adId, citizenid}, function(result)
        if not result[1] then
            TriggerClientEvent('QBCore:Notify', src, 'Inzerát nenalezen!', 'error')
            return
        end
        
        -- Aktualizace inzerátu
        MySQL.Async.execute([[
            UPDATE phone_marketplace 
            SET title = ?, description = ?, price = ?, category = ?, images = ? 
            WHERE id = ? AND citizenid = ?
        ]], {
            adData.title,
            adData.description,
            adData.price,
            adData.category,
            json.encode(adData.images or {}),
            adId,
            citizenid
        }, function(affectedRows)
            if affectedRows > 0 then
                TriggerClientEvent('QBCore:Notify', src, 'Inzerát byl upraven!', 'success')
                TriggerClientEvent('qb-smartphone2:client:adUpdated', src, adId)
            else
                TriggerClientEvent('QBCore:Notify', src, 'Chyba při úpravě inzerátu!', 'error')
            end
        end)
    end)
end)

-- Event pro smazání inzerátu
RegisterNetEvent('qb-smartphone2:server:deleteMarketplaceAd', function(adId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Kontrola vlastnictví inzerátu
    MySQL.Async.fetchAll('SELECT * FROM phone_marketplace WHERE id = ? AND citizenid = ?', {adId, citizenid}, function(result)
        if not result[1] then
            TriggerClientEvent('QBCore:Notify', src, 'Inzerát nenalezen!', 'error')
            return
        end
        
        -- Smazání inzerátu
        MySQL.Async.execute('DELETE FROM phone_marketplace WHERE id = ? AND citizenid = ?', {adId, citizenid}, function(affectedRows)
            if affectedRows > 0 then
                TriggerClientEvent('QBCore:Notify', src, 'Inzerát byl smazán!', 'success')
                TriggerClientEvent('qb-smartphone2:client:adDeleted', src, adId)
            else
                TriggerClientEvent('QBCore:Notify', src, 'Chyba při mazání inzerátu!', 'error')
            end
        end)
    end)
end)

-- Event pro označení inzerátu jako prodaný
RegisterNetEvent('qb-smartphone2:server:markAdAsSold', function(adId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Kontrola vlastnictví inzerátu
    MySQL.Async.fetchAll('SELECT * FROM phone_marketplace WHERE id = ? AND citizenid = ?', {adId, citizenid}, function(result)
        if not result[1] then
            TriggerClientEvent('QBCore:Notify', src, 'Inzerát nenalezen!', 'error')
            return
        end
        
        -- Označení jako prodaný
        MySQL.Async.execute('UPDATE phone_marketplace SET status = "sold" WHERE id = ? AND citizenid = ?', {adId, citizenid}, function(affectedRows)
            if affectedRows > 0 then
                TriggerClientEvent('QBCore:Notify', src, 'Inzerát byl označen jako prodaný!', 'success')
                TriggerClientEvent('qb-smartphone2:client:adSold', src, adId)
            else
                TriggerClientEvent('QBCore:Notify', src, 'Chyba při označování inzerátu!', 'error')
            end
        end)
    end)
end)

-- Event pro kontaktování prodejce
RegisterNetEvent('qb-smartphone2:server:contactSeller', function(adId, message)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local buyerNumber = Player.PlayerData.charinfo.phone
    local buyerName = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname
    
    -- Získání informací o inzerátu
    MySQL.Async.fetchAll('SELECT * FROM phone_marketplace WHERE id = ?', {adId}, function(result)
        if not result[1] then
            TriggerClientEvent('QBCore:Notify', src, 'Inzerát nenalezen!', 'error')
            return
        end
        
        local ad = result[1]
        local sellerNumber = ad.seller_number
        
        -- Odeslání zprávy prodejci
        local fullMessage = 'Zájem o inzerát "' .. ad.title .. '": ' .. (message or 'Mám zájem o váš inzerát.')
        
        TriggerEvent('qb-smartphone2:server:sendMessage', {
            receiver = sellerNumber,
            message = fullMessage,
            attachments = {
                {
                    type = 'marketplace_ad',
                    adId = adId,
                    title = ad.title,
                    price = ad.price
                }
            }
        })
        
        TriggerClientEvent('QBCore:Notify', src, 'Zpráva byla odeslána prodejci!', 'success')
    end)
end)

-- Callback pro získání vlastních inzerátů
QBCore.Functions.CreateCallback('qb-smartphone2:server:getMyAds', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb({}) return end
    
    local citizenid = Player.PlayerData.citizenid
    
    MySQL.Async.fetchAll('SELECT * FROM phone_marketplace WHERE citizenid = ? ORDER BY created_at DESC', {citizenid}, function(result)
        cb(result or {})
    end)
end)

-- Callback pro získání kategorií
QBCore.Functions.CreateCallback('qb-smartphone2:server:getMarketplaceCategories', function(source, cb)
    cb(MarketplaceCategories)
end)

-- Callback pro získání detailu inzerátu
QBCore.Functions.CreateCallback('qb-smartphone2:server:getAdDetails', function(source, cb, adId)
    MySQL.Async.fetchAll('SELECT * FROM phone_marketplace WHERE id = ?', {adId}, function(result)
        if result[1] then
            local ad = result[1]
            ad.images = json.decode(ad.images or '[]')
            cb(ad)
        else
            cb(nil)
        end
    end)
end)

-- Automatické vymazání vypršených inzerátů
CreateThread(function()
    while true do
        Wait(3600000) -- Každou hodinu
        
        MySQL.Async.execute('UPDATE phone_marketplace SET status = "expired" WHERE expires_at < NOW() AND status = "active"', {}, function(affectedRows)
            if affectedRows > 0 then
                print('^3[QB-Smartphone2]^7 Označeno ' .. affectedRows .. ' inzerátů jako vypršené')
            end
        end)
    end
end)

-- Event pro obnovení inzerátu
RegisterNetEvent('qb-smartphone2:server:renewAd', function(adId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Kontrola vlastnictví inzerátu
    MySQL.Async.fetchAll('SELECT * FROM phone_marketplace WHERE id = ? AND citizenid = ?', {adId, citizenid}, function(result)
        if not result[1] then
            TriggerClientEvent('QBCore:Notify', src, 'Inzerát nenalezen!', 'error')
            return
        end
        
        -- Kontrola poplatku za obnovení
        if Config.AdFee > 0 then
            local playerMoney = Player.PlayerData.money.cash or 0
            if playerMoney < Config.AdFee then
                TriggerClientEvent('QBCore:Notify', src, 'Nemáš dostatek peněz na obnovení inzerátu!', 'error')
                return
            end
            
            Player.Functions.RemoveMoney('cash', Config.AdFee, 'marketplace-ad-renewal')
        end
        
        -- Nové datum vypršení
        local newExpiresAt = os.date('%Y-%m-%d %H:%M:%S', os.time() + (Config.AdDuration * 24 * 60 * 60))
        
        -- Obnovení inzerátu
        MySQL.Async.execute('UPDATE phone_marketplace SET status = "active", expires_at = ? WHERE id = ? AND citizenid = ?', {
            newExpiresAt, adId, citizenid
        }, function(affectedRows)
            if affectedRows > 0 then
                TriggerClientEvent('QBCore:Notify', src, 'Inzerát byl obnoven!', 'success')
                TriggerClientEvent('qb-smartphone2:client:adRenewed', src, adId)
            else
                TriggerClientEvent('QBCore:Notify', src, 'Chyba při obnovování inzerátu!', 'error')
            end
        end)
    end)
end)

-- Export funkce pro jiné scripty
exports('CreateMarketplaceAd', function(citizenid, adData)
    local p = promise.new()
    
    local expiresAt = os.date('%Y-%m-%d %H:%M:%S', os.time() + (Config.AdDuration * 24 * 60 * 60))
    
    MySQL.Async.execute([[
        INSERT INTO phone_marketplace 
        (citizenid, seller_name, seller_number, title, description, price, category, images, expires_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ]], {
        citizenid,
        adData.seller_name or 'System',
        adData.seller_number or '000-0000',
        adData.title,
        adData.description,
        adData.price,
        adData.category,
        json.encode(adData.images or {}),
        expiresAt
    }, function(insertId)
        p:resolve(insertId ~= nil)
    end)
    
    return Citizen.Await(p)
end)

exports('DeleteMarketplaceAd', function(adId)
    local p = promise.new()
    
    MySQL.Async.execute('DELETE FROM phone_marketplace WHERE id = ?', {adId}, function(affectedRows)
        p:resolve(affectedRows > 0)
    end)
    
    return Citizen.Await(p)
end)

exports('GetMarketplaceAds', function(filters)
    local p = promise.new()
    
    local query = 'SELECT * FROM phone_marketplace WHERE status = "active" ORDER BY created_at DESC LIMIT 50'
    
    MySQL.Async.fetchAll(query, {}, function(result)
        p:resolve(result or {})
    end)
    
    return Citizen.Await(p)
end)
