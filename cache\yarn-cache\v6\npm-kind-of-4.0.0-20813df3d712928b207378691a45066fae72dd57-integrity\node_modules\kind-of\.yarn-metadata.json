{"manifest": {"name": "kind-of", "description": "Get the native type of a value.", "version": "4.0.0", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "dependencies": {"is-buffer": "^1.1.5"}, "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^1.1.1", "browserify": "^14.3.0", "glob": "^7.1.1", "gulp-format-md": "^0.1.12", "mocha": "^3.4.1", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"related": {"list": ["is-glob", "is-number", "is-primitive"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-kind-of-4.0.0-20813df3d712928b207378691a45066fae72dd57-integrity\\node_modules\\kind-of\\package.json", "readmeFilename": "README.md", "readme": "# kind-of [![NPM version](https://img.shields.io/npm/v/kind-of.svg?style=flat)](https://www.npmjs.com/package/kind-of) [![NPM monthly downloads](https://img.shields.io/npm/dm/kind-of.svg?style=flat)](https://npmjs.org/package/kind-of) [![NPM total downloads](https://img.shields.io/npm/dt/kind-of.svg?style=flat)](https://npmjs.org/package/kind-of) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/kind-of.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/kind-of)\n\n> Get the native type of a value.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save kind-of\n```\n\nInstall with [bower](https://bower.io/)\n\n```sh\n$ bower install kind-of --save\n```\n\n## Usage\n\n> es5, browser and es6 ready\n\n```js\nvar kindOf = require('kind-of');\n\nkindOf(undefined);\n//=> 'undefined'\n\nkindOf(null);\n//=> 'null'\n\nkindOf(true);\n//=> 'boolean'\n\nkindOf(false);\n//=> 'boolean'\n\nkindOf(new Boolean(true));\n//=> 'boolean'\n\nkindOf(new Buffer(''));\n//=> 'buffer'\n\nkindOf(42);\n//=> 'number'\n\nkindOf(new Number(42));\n//=> 'number'\n\nkindOf('str');\n//=> 'string'\n\nkindOf(new String('str'));\n//=> 'string'\n\nkindOf(arguments);\n//=> 'arguments'\n\nkindOf({});\n//=> 'object'\n\nkindOf(Object.create(null));\n//=> 'object'\n\nkindOf(new Test());\n//=> 'object'\n\nkindOf(new Date());\n//=> 'date'\n\nkindOf([]);\n//=> 'array'\n\nkindOf([1, 2, 3]);\n//=> 'array'\n\nkindOf(new Array());\n//=> 'array'\n\nkindOf(/foo/);\n//=> 'regexp'\n\nkindOf(new RegExp('foo'));\n//=> 'regexp'\n\nkindOf(function () {});\n//=> 'function'\n\nkindOf(function * () {});\n//=> 'function'\n\nkindOf(new Function());\n//=> 'function'\n\nkindOf(new Map());\n//=> 'map'\n\nkindOf(new WeakMap());\n//=> 'weakmap'\n\nkindOf(new Set());\n//=> 'set'\n\nkindOf(new WeakSet());\n//=> 'weakset'\n\nkindOf(Symbol('str'));\n//=> 'symbol'\n\nkindOf(new Int8Array());\n//=> 'int8array'\n\nkindOf(new Uint8Array());\n//=> 'uint8array'\n\nkindOf(new Uint8ClampedArray());\n//=> 'uint8clampedarray'\n\nkindOf(new Int16Array());\n//=> 'int16array'\n\nkindOf(new Uint16Array());\n//=> 'uint16array'\n\nkindOf(new Int32Array());\n//=> 'int32array'\n\nkindOf(new Uint32Array());\n//=> 'uint32array'\n\nkindOf(new Float32Array());\n//=> 'float32array'\n\nkindOf(new Float64Array());\n//=> 'float64array'\n```\n\n## Benchmarks\n\nBenchmarked against [typeof](http://github.com/CodingFu/typeof) and [type-of](https://github.com/ForbesLindesay/type-of).\nNote that performaces is slower for es6 features `Map`, `WeakMap`, `Set` and `WeakSet`.\n\n```bash\n#1: array\n  current x 23,329,397 ops/sec ±0.82% (94 runs sampled)\n  lib-type-of x 4,170,273 ops/sec ±0.55% (94 runs sampled)\n  lib-typeof x 9,686,935 ops/sec ±0.59% (98 runs sampled)\n\n#2: boolean\n  current x 27,197,115 ops/sec ±0.85% (94 runs sampled)\n  lib-type-of x 3,145,791 ops/sec ±0.73% (97 runs sampled)\n  lib-typeof x 9,199,562 ops/sec ±0.44% (99 runs sampled)\n\n#3: date\n  current x 20,190,117 ops/sec ±0.86% (92 runs sampled)\n  lib-type-of x 5,166,970 ops/sec ±0.74% (94 runs sampled)\n  lib-typeof x 9,610,821 ops/sec ±0.50% (96 runs sampled)\n\n#4: function\n  current x 23,855,460 ops/sec ±0.60% (97 runs sampled)\n  lib-type-of x 5,667,740 ops/sec ±0.54% (100 runs sampled)\n  lib-typeof x 10,010,644 ops/sec ±0.44% (100 runs sampled)\n\n#5: null\n  current x 27,061,047 ops/sec ±0.97% (96 runs sampled)\n  lib-type-of x 13,965,573 ops/sec ±0.62% (97 runs sampled)\n  lib-typeof x 8,460,194 ops/sec ±0.61% (97 runs sampled)\n\n#6: number\n  current x 25,075,682 ops/sec ±0.53% (99 runs sampled)\n  lib-type-of x 2,266,405 ops/sec ±0.41% (98 runs sampled)\n  lib-typeof x 9,821,481 ops/sec ±0.45% (99 runs sampled)\n\n#7: object\n  current x 3,348,980 ops/sec ±0.49% (99 runs sampled)\n  lib-type-of x 3,245,138 ops/sec ±0.60% (94 runs sampled)\n  lib-typeof x 9,262,952 ops/sec ±0.59% (99 runs sampled)\n\n#8: regex\n  current x 21,284,827 ops/sec ±0.72% (96 runs sampled)\n  lib-type-of x 4,689,241 ops/sec ±0.43% (100 runs sampled)\n  lib-typeof x 8,957,593 ops/sec ±0.62% (98 runs sampled)\n\n#9: string\n  current x 25,379,234 ops/sec ±0.58% (96 runs sampled)\n  lib-type-of x 3,635,148 ops/sec ±0.76% (93 runs sampled)\n  lib-typeof x 9,494,134 ops/sec ±0.49% (98 runs sampled)\n\n#10: undef\n  current x 27,459,221 ops/sec ±1.01% (93 runs sampled)\n  lib-type-of x 14,360,433 ops/sec ±0.52% (99 runs sampled)\n  lib-typeof x 23,202,868 ops/sec ±0.59% (94 runs sampled)\n\n```\n\n## Release history\n\n### v4.0.0\n\n**Added**\n\n* `promise` support\n\n## Optimizations\n\nIn 7 out of 8 cases, this library is 2x-10x faster than other top libraries included in the benchmarks. There are a few things that lead to this performance advantage, none of them hard and fast rules, but all of them simple and repeatable in almost any code library:\n\n1. Optimize around the fastest and most common use cases first. Of course, this will change from project-to-project, but I took some time to understand how and why `typeof` checks were being used in my own libraries and other libraries I use a lot.\n2. Optimize around bottlenecks - In other words, the order in which conditionals are implemented is significant, because each check is only as fast as the failing checks that came before it. Here, the biggest bottleneck by far is checking for plain objects (an object that was created by the `Object` constructor). I opted to make this check happen by process of elimination rather than brute force up front (e.g. by using something like `val.constructor.name`), so that every other type check would not be penalized it.\n3. Don't do uneccessary processing - why do `.slice(8, -1).toLowerCase();` just to get the word `regex`? It's much faster to do `if (type === '[object RegExp]') return 'regex'`\n\n## About\n\n### Related projects\n\n* [is-glob](https://www.npmjs.com/package/is-glob): Returns `true` if the given string looks like a glob pattern or an extglob pattern… [more](https://github.com/jonschlinkert/is-glob) | [homepage](https://github.com/jonschlinkert/is-glob \"Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet\")\n* [is-number](https://www.npmjs.com/package/is-number): Returns true if the value is a number. comprehensive tests. | [homepage](https://github.com/jonschlinkert/is-number \"Returns true if the value is a number. comprehensive tests.\")\n* [is-primitive](https://www.npmjs.com/package/is-primitive): Returns `true` if the value is a primitive.  | [homepage](https://github.com/jonschlinkert/is-primitive \"Returns `true` if the value is a primitive. \")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 64 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 2 | [miguelmota](https://github.com/miguelmota) |\n| 1 | [dtothefp](https://github.com/dtothefp) |\n| 1 | [ksheedlo](https://github.com/ksheedlo) |\n| 1 | [pdehaan](https://github.com/pdehaan) |\n| 1 | [laggingreflex](https://github.com/laggingreflex) |\n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on May 19, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/kind-of/-/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57", "type": "tarball", "reference": "https://registry.yarnpkg.com/kind-of/-/kind-of-4.0.0.tgz", "hash": "20813df3d712928b207378691a45066fae72dd57", "integrity": "sha1-IIE989cSkosgc3hpGkUGb65y3Vc=", "registry": "npm", "packageName": "kind-of", "cacheIntegrity": "sha512-24XsCxmEbRwEDbz/qz3stgin8TTzZ1ESR56OMCN0ujYg+vRutNSiOj9bHH9u85DKgXguraugV5sFuvbD4FW/hw== sha1-IIE989cSkosgc3hpGkUGb65y3Vc="}, "registry": "npm", "hash": "20813df3d712928b207378691a45066fae72dd57"}