{"name": "fsevents", "version": "1.2.13", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1"}, "os": ["darwin"], "engines": {"node": ">= 4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node install.js"}, "repository": {"type": "git", "url": "https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents"}