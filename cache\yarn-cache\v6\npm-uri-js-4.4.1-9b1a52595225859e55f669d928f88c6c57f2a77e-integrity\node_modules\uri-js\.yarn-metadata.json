{"manifest": {"name": "uri-js", "version": "4.4.1", "description": "An RFC 3986/3987 compliant, scheme extendable URI/IRI parsing/validating/resolving library for JavaScript.", "main": "dist/es5/uri.all.js", "types": "dist/es5/uri.all.d.ts", "files": ["dist", "package.json", "yarn.lock", "README.md", "CHANGELOG", "LICENSE"], "scripts": {"build:esnext": "tsc", "build:es5": "rollup -c && cp dist/esnext/uri.d.ts dist/es5/uri.all.d.ts && npm run build:es5:fix-sourcemap", "build:es5:fix-sourcemap": "sorcery -i dist/es5/uri.all.js", "build:es5:min": "uglifyjs dist/es5/uri.all.js --support-ie8 --output dist/es5/uri.all.min.js --in-source-map dist/es5/uri.all.js.map --source-map uri.all.min.js.map --comments --compress --mangle --pure-funcs merge subexp && mv uri.all.min.js.map dist/es5/ && cp dist/es5/uri.all.d.ts dist/es5/uri.all.min.d.ts", "build": "npm run build:esnext && npm run build:es5 && npm run build:es5:min", "clean": "rm -rf dist", "test": "mocha -u mocha-qunit-ui dist/es5/uri.all.js tests/tests.js"}, "repository": {"type": "git", "url": "http://github.com/garycourt/uri-js"}, "keywords": ["URI", "IRI", "IDN", "URN", "UUID", "HTTP", "HTTPS", "WS", "WSS", "MAILTO", "RFC3986", "RFC3987", "RFC5891", "RFC2616", "RFC2818", "RFC2141", "RFC4122", "RFC4291", "RFC5952", "RFC6068", "RFC6455", "RFC6874"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/garycourt/uri-js/issues"}, "homepage": "https://github.com/garycourt/uri-js", "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.1", "mocha": "^8.2.1", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.8.1", "uglify-js": "^2.8.14"}, "dependencies": {"punycode": "^2.1.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-uri-js-4.4.1-9b1a52595225859e55f669d928f88c6c57f2a77e-integrity\\node_modules\\uri-js\\package.json", "readmeFilename": "README.md", "readme": "# URI.js\n\nURI.js is an [RFC 3986](http://www.ietf.org/rfc/rfc3986.txt) compliant, scheme extendable URI parsing/validating/resolving library for all JavaScript environments (browsers, Node.js, etc).\nIt is also compliant with the IRI ([RFC 3987](http://www.ietf.org/rfc/rfc3987.txt)), IDNA ([RFC 5890](http://www.ietf.org/rfc/rfc5890.txt)), IPv6 Address ([RFC 5952](http://www.ietf.org/rfc/rfc5952.txt)), IPv6 Zone Identifier ([RFC 6874](http://www.ietf.org/rfc/rfc6874.txt)) specifications.\n\nURI.js has an extensive test suite, and works in all (Node.js, web) environments. It weighs in at 6.4kb (gzipped, 17kb deflated).\n\n## API\n\n### Parsing\n\n\tURI.parse(\"uri://user:<EMAIL>:123/one/two.three?q1=a1&q2=a2#body\");\n\t//returns:\n\t//{\n\t//  scheme : \"uri\",\n\t//  userinfo : \"user:pass\",\n\t//  host : \"example.com\",\n\t//  port : 123,\n\t//  path : \"/one/two.three\",\n\t//  query : \"q1=a1&q2=a2\",\n\t//  fragment : \"body\"\n\t//}\n\n### Serializing\n\n\tURI.serialize({scheme : \"http\", host : \"example.com\", fragment : \"footer\"}) === \"http://example.com/#footer\"\n\n### Resolving\n\n\tURI.resolve(\"uri://a/b/c/d?q\", \"../../g\") === \"uri://a/g\"\n\n### Normalizing\n\n\tURI.normalize(\"HTTP://ABC.com:80/%7Esmith/home.html\") === \"http://abc.com/~smith/home.html\"\n\n### Comparison\n\n\tURI.equal(\"example://a/b/c/%7Bfoo%7D\", \"eXAMPLE://a/./b/../b/%63/%7bfoo%7d\") === true\n\n### IP Support\n\n\t//IPv4 normalization\n\tURI.normalize(\"//***************\") === \"//**********\"\n\n\t//IPv6 normalization\n\tURI.normalize(\"//[2001:0:0DB8::0:0001]\") === \"//[2001:0:db8::1]\"\n\n\t//IPv6 zone identifier support\n\tURI.parse(\"//[2001:db8::7%25en1]\");\n\t//returns:\n\t//{\n\t//  host : \"2001:db8::7%en1\"\n\t//}\n\n### IRI Support\n\n\t//convert IRI to URI\n\tURI.serialize(URI.parse(\"http://examplé.org/rosé\")) === \"http://xn--exampl-gva.org/ros%C3%A9\"\n\t//convert URI to IRI\n\tURI.serialize(URI.parse(\"http://xn--exampl-gva.org/ros%C3%A9\"), {iri:true}) === \"http://examplé.org/rosé\"\n\n### Options\n\nAll of the above functions can accept an additional options argument that is an object that can contain one or more of the following properties:\n\n*\t`scheme` (string)\n\n\tIndicates the scheme that the URI should be treated as, overriding the URI's normal scheme parsing behavior.\n\n*\t`reference` (string)\n\n\tIf set to `\"suffix\"`, it indicates that the URI is in the suffix format, and the validator will use the option's `scheme` property to determine the URI's scheme.\n\n*\t`tolerant` (boolean, false)\n\n\tIf set to `true`, the parser will relax URI resolving rules.\n\n*\t`absolutePath` (boolean, false)\n\n\tIf set to `true`, the serializer will not resolve a relative `path` component.\n\n*\t`iri` (boolean, false)\n\n\tIf set to `true`, the serializer will unescape non-ASCII characters as per [RFC 3987](http://www.ietf.org/rfc/rfc3987.txt).\n\n*\t`unicodeSupport` (boolean, false)\n\n\tIf set to `true`, the parser will unescape non-ASCII characters in the parsed output as per [RFC 3987](http://www.ietf.org/rfc/rfc3987.txt).\n\n*\t`domainHost` (boolean, false)\n\n\tIf set to `true`, the library will treat the `host` component as a domain name, and convert IDNs (International Domain Names) as per [RFC 5891](http://www.ietf.org/rfc/rfc5891.txt).\n\n## Scheme Extendable\n\nURI.js supports inserting custom [scheme](http://en.wikipedia.org/wiki/URI_scheme) dependent processing rules. Currently, URI.js has built in support for the following schemes:\n\n*\thttp \\[[RFC 2616](http://www.ietf.org/rfc/rfc2616.txt)\\]\n*\thttps \\[[RFC 2818](http://www.ietf.org/rfc/rfc2818.txt)\\]\n*\tws \\[[RFC 6455](http://www.ietf.org/rfc/rfc6455.txt)\\]\n*\twss \\[[RFC 6455](http://www.ietf.org/rfc/rfc6455.txt)\\]\n*\tmailto \\[[RFC 6068](http://www.ietf.org/rfc/rfc6068.txt)\\]\n*\turn \\[[RFC 2141](http://www.ietf.org/rfc/rfc2141.txt)\\]\n*\turn:uuid \\[[RFC 4122](http://www.ietf.org/rfc/rfc4122.txt)\\]\n\n### HTTP/HTTPS Support\n\n\tURI.equal(\"HTTP://ABC.COM:80\", \"http://abc.com/\") === true\n\tURI.equal(\"https://abc.com\", \"HTTPS://ABC.COM:443/\") === true\n\n### WS/WSS Support\n\n\tURI.parse(\"wss://example.com/foo?bar=baz\");\n\t//returns:\n\t//{\n\t//\tscheme : \"wss\",\n\t//\thost: \"example.com\",\n\t//\tresourceName: \"/foo?bar=baz\",\n\t//\tsecure: true,\n\t//}\n\n\tURI.equal(\"WS://ABC.COM:80/chat#one\", \"ws://abc.com/chat\") === true\n\n### Mailto Support\n\n\tURI.parse(\"mailto:<EMAIL>,<EMAIL>?subject=SUBSCRIBE&body=Sign%20me%20up!\");\n\t//returns:\n\t//{\n\t//\tscheme : \"mailto\",\n\t//\tto : [\"<EMAIL>\", \"<EMAIL>\"],\n\t//\tsubject : \"SUBSCRIBE\",\n\t//\tbody : \"Sign me up!\"\n\t//}\n\n\tURI.serialize({\n\t\tscheme : \"mailto\",\n\t\tto : [\"<EMAIL>\"],\n\t\tsubject : \"REMOVE\",\n\t\tbody : \"Please remove me\",\n\t\theaders : {\n\t\t\tcc : \"<EMAIL>\"\n\t\t}\n\t}) === \"mailto:<EMAIL>?cc=<EMAIL>&subject=REMOVE&body=Please%20remove%20me\"\n\n### URN Support\n\n\tURI.parse(\"urn:example:foo\");\n\t//returns:\n\t//{\n\t//\tscheme : \"urn\",\n\t//\tnid : \"example\",\n\t//\tnss : \"foo\",\n\t//}\n\n#### URN UUID Support\n\n\tURI.parse(\"urn:uuid:f81d4fae-7dec-11d0-a765-00a0c91e6bf6\");\n\t//returns:\n\t//{\n\t//\tscheme : \"urn\",\n\t//\tnid : \"uuid\",\n\t//\tuuid : \"f81d4fae-7dec-11d0-a765-00a0c91e6bf6\",\n\t//}\n\n## Usage\n\nTo load in a browser, use the following tag:\n\n\t<script type=\"text/javascript\" src=\"uri-js/dist/es5/uri.all.min.js\"></script>\n\nTo load in a CommonJS/Module environment, first install with npm/yarn by running on the command line:\n\n\tnpm install uri-js\n\t# OR\n\tyarn add uri-js\n\nThen, in your code, load it using:\n\n\tconst URI = require(\"uri-js\");\n\nIf you are writing your code in ES6+ (ESNEXT) or TypeScript, you would load it using:\n\n\timport * as URI from \"uri-js\";\n\nOr you can load just what you need using named exports:\n\n\timport { parse, serialize, resolve, resolveComponents, normalize, equal, removeDotSegments, pctEncChar, pctDecChars, escapeComponent, unescapeComponent } from \"uri-js\";\n\n## Breaking changes\n\n### Breaking changes from 3.x\n\nURN parsing has been completely changed to better align with the specification. Scheme is now always `urn`, but has two new properties: `nid` which contains the Namspace Identifier, and `nss` which contains the Namespace Specific String. The `nss` property will be removed by higher order scheme handlers, such as the UUID URN scheme handler.\n\nThe UUID of a URN can now be found in the `uuid` property.\n\n### Breaking changes from 2.x\n\nURI validation has been removed as it was slow, exposed a vulnerabilty, and was generally not useful.\n\n### Breaking changes from 1.x\n\nThe `errors` array on parsed components is now an `error` string.\n", "licenseText": "Copyright 2011 Gary Court. All rights reserved.\n\nRedistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n\n1.\tRedistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.\n\n2.\tRedistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.\n\nTHIS SOFTWARE IS PROVIDED BY GARY COURT \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF ME<PERSON><PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL GARY COURT OR <PERSON><PERSON><PERSON>BUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n\nThe views and conclusions contained in the software and documentation are those of the authors and should not be interpreted as representing official policies, either expressed or implied, of Gary Court.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/uri-js/-/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e", "type": "tarball", "reference": "https://registry.yarnpkg.com/uri-js/-/uri-js-4.4.1.tgz", "hash": "9b1a52595225859e55f669d928f88c6c57f2a77e", "integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==", "registry": "npm", "packageName": "uri-js", "cacheIntegrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg== sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34="}, "registry": "npm", "hash": "9b1a52595225859e55f669d928f88c6c57f2a77e"}