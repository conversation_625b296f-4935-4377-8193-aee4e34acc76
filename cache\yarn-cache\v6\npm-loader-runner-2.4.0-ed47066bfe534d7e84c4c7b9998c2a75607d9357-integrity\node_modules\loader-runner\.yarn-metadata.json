{"manifest": {"name": "loader-runner", "version": "2.4.0", "description": "Runs (webpack) loaders", "main": "lib/LoaderRunner.js", "scripts": {"lint": "eslint lib test", "pretest": "npm run lint", "test": "mocha --reporter spec", "precover": "npm run lint", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly"}, "repository": {"type": "git", "url": "git+https://github.com/webpack/loader-runner.git"}, "keywords": ["webpack", "loader"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "bugs": {"url": "https://github.com/webpack/loader-runner/issues"}, "homepage": "https://github.com/webpack/loader-runner#readme", "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}, "files": ["lib/", "bin/", "hot/", "web_modules/", "schemas/"], "devDependencies": {"codecov.io": "^0.1.6", "coveralls": "^2.11.6", "eslint": "^3.12.2", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "mocha": "^3.2.0", "should": "^8.0.2"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-loader-runner-2.4.0-ed47066bfe534d7e84c4c7b9998c2a75607d9357-integrity\\node_modules\\loader-runner\\package.json", "readmeFilename": "README.md", "readme": "# loader-runner\n\n``` js\nimport { runLoaders } from \"loader-runner\";\n\nrunLoaders({\n\tresource: \"/abs/path/to/file.txt?query\",\n\t// String: Absolute path to the resource (optionally including query string)\n\n\tloaders: [\"/abs/path/to/loader.js?query\"],\n\t// String[]: Absolute paths to the loaders (optionally including query string)\n\t// {loader, options}[]: Absolute paths to the loaders with options object\n\n\tcontext: { minimize: true },\n\t// Additional loader context which is used as base context\n\n\treadResource: fs.readFile.bind(fs)\n\t// A function to read the resource\n\t// Must have signature function(path, function(err, buffer))\n\n}, function(err, result) {\n\t// err: Error?\n\n\t// result.result: Buffer | String\n\t// The result\n\n\t// result.resourceBuffer: Buffer\n\t// The raw resource as Buffer (useful for SourceMaps)\n\n\t// result.cacheable: Bool\n\t// Is the result cacheable or do it require reexecution?\n\n\t// result.fileDependencies: String[]\n\t// An array of paths (files) on which the result depends on\n\n\t// result.contextDependencies: String[]\n\t// An array of paths (directories) on which the result depends on\n})\n```\n\nMore documentation following...\n\n", "licenseText": "The MIT License\n\nCopyright (c) <PERSON> @sokra\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/loader-runner/-/loader-runner-2.4.0.tgz#ed47066bfe534d7e84c4c7b9998c2a75607d9357", "type": "tarball", "reference": "https://registry.yarnpkg.com/loader-runner/-/loader-runner-2.4.0.tgz", "hash": "ed47066bfe534d7e84c4c7b9998c2a75607d9357", "integrity": "sha512-Jsmr89RcXGIwivFY21FcRrisYZfvLMTWx5kOLc+JTxtpBOG6xML0vzbc6SEQG2FO9/4Fc3wW4LVcB5DmGflaRw==", "registry": "npm", "packageName": "loader-runner", "cacheIntegrity": "sha512-Jsmr89RcXGIwivFY21FcRrisYZfvLMTWx5kOLc+JTxtpBOG6xML0vzbc6SEQG2FO9/4Fc3wW4LVcB5DmGflaRw== sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c="}, "registry": "npm", "hash": "ed47066bfe534d7e84c4c7b9998c2a75607d9357"}