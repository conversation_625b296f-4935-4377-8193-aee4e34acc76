{"name": "@types/http-errors", "version": "1.8.0", "description": "TypeScript definitions for http-errors", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/http-errors"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "61c68cc213bf1a21d07bfac01c18754adea53b40a66a043c89fb6a35957ac0c9", "typeScriptVersion": "3.0"}