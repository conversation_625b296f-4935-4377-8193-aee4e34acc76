{"manifest": {"name": "is-binary-path", "version": "1.0.1", "description": "Check if a filepath is a binary file", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/is-binary-path.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "path", "check", "detect", "is"], "dependencies": {"binary-extensions": "^1.0.0"}, "devDependencies": {"ava": "0.0.4"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-binary-path-1.0.1-75f16642b480f187a711c814161fd3a4a7655898-integrity\\node_modules\\is-binary-path\\package.json", "readmeFilename": "readme.md", "readme": "# is-binary-path [![Build Status](https://travis-ci.org/sindresorhus/is-binary-path.svg?branch=master)](https://travis-ci.org/sindresorhus/is-binary-path)\n\n> Check if a filepath is a binary file\n\n\n## Install\n\n```\n$ npm install --save is-binary-path\n```\n\n\n## Usage\n\n```js\nvar isBinaryPath = require('is-binary-path');\n\nisBinaryPath('src/unicorn.png');\n//=> true\n\nisBinaryPath('src/unicorn.txt');\n//=> false\n```\n\n\n## Related\n\n- [`binary-extensions`](https://github.com/sindresorhus/binary-extensions) - List of binary file extensions\n- [`is-text-path`](https://github.com/sindresorhus/is-text-path) - Check if a filepath is a text file\n\n\n## License\n\nMIT © [Sindre Sorhus](http://sindresorhus.com)\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND <PERSON><PERSON>NFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-binary-path/-/is-binary-path-1.0.1.tgz#75f16642b480f187a711c814161fd3a4a7655898", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-binary-path/-/is-binary-path-1.0.1.tgz", "hash": "75f16642b480f187a711c814161fd3a4a7655898", "integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "registry": "npm", "packageName": "is-binary-path", "cacheIntegrity": "sha512-9fRVlXc0uCxEDj1nQzaWONSpbTfx0FmJfzHF7pwlI8DkWGoHBBea4Pg5Ky0ojwwxQmnSifgbKkI06Qv0Ljgj+Q== sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg="}, "registry": "npm", "hash": "75f16642b480f187a711c814161fd3a4a7655898"}