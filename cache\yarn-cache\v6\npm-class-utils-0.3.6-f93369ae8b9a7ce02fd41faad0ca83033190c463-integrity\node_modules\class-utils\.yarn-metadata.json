{"manifest": {"name": "class-utils", "description": "Utils for working with JavaScript classes and prototype methods.", "version": "0.3.6", "homepage": "https://github.com/jonschlinkert/class-utils", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/class-utils.git"}, "bugs": {"url": "https://github.com/jonschlinkert/class-utils/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "devDependencies": {"gulp": "^3.9.1", "gulp-eslint": "^2.0.0", "gulp-format-md": "^0.1.7", "gulp-istanbul": "^0.10.3", "gulp-mocha": "^2.2.0", "mocha": "^2.4.5", "should": "^8.2.2", "through2": "^2.0.1"}, "keywords": ["array", "assign", "class", "copy", "ctor", "define", "delegate", "descriptor", "extend", "extends", "inherit", "inheritance", "merge", "method", "object", "prop", "properties", "property", "prototype", "util", "utils"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["define-property", "delegate-properties", "is-descriptor"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-class-utils-0.3.6-f93369ae8b9a7ce02fd41faad0ca83033190c463-integrity\\node_modules\\class-utils\\package.json", "readmeFilename": "README.md", "readme": "# class-utils [![NPM version](https://img.shields.io/npm/v/class-utils.svg?style=flat)](https://www.npmjs.com/package/class-utils) [![NPM monthly downloads](https://img.shields.io/npm/dm/class-utils.svg?style=flat)](https://npmjs.org/package/class-utils) [![NPM total downloads](https://img.shields.io/npm/dt/class-utils.svg?style=flat)](https://npmjs.org/package/class-utils) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/class-utils.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/class-utils)\n\n> Utils for working with JavaScript classes and prototype methods.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save class-utils\n```\n\n## Usage\n\n```js\nvar cu = require('class-utils');\n```\n\n## API\n\n### [.has](index.js#L43)\n\nReturns true if an array has any of the given elements, or an object has any of the give keys.\n\n**Params**\n\n* `obj` **{Object}**\n* `val` **{String|Array}**\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\ncu.has(['a', 'b', 'c'], 'c');\n//=> true\n\ncu.has(['a', 'b', 'c'], ['c', 'z']);\n//=> true\n\ncu.has({a: 'b', c: 'd'}, ['c', 'z']);\n//=> true\n```\n\n### [.hasAll](index.js#L90)\n\nReturns true if an array or object has all of the given values.\n\n**Params**\n\n* `val` **{Object|Array}**\n* `values` **{String|Array}**\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\ncu.hasAll(['a', 'b', 'c'], 'c');\n//=> true\n\ncu.hasAll(['a', 'b', 'c'], ['c', 'z']);\n//=> false\n\ncu.hasAll({a: 'b', c: 'd'}, ['c', 'z']);\n//=> false\n```\n\n### [.arrayify](index.js#L117)\n\nCast the given value to an array.\n\n**Params**\n\n* `val` **{String|Array}**\n* `returns` **{Array}**\n\n**Example**\n\n```js\ncu.arrayify('foo');\n//=> ['foo']\n\ncu.arrayify(['foo']);\n//=> ['foo']\n```\n\n### [.hasConstructor](index.js#L152)\n\nReturns true if a value has a `contructor`\n\n**Params**\n\n* `value` **{Object}**\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\ncu.hasConstructor({});\n//=> true\n\ncu.hasConstructor(Object.create(null));\n//=> false\n```\n\n### [.nativeKeys](index.js#L174)\n\nGet the native `ownPropertyNames` from the constructor of the given `object`. An empty array is returned if the object does not have a constructor.\n\n**Params**\n\n* `obj` **{Object}**: Object that has a `constructor`.\n* `returns` **{Array}**: Array of keys.\n\n**Example**\n\n```js\ncu.nativeKeys({a: 'b', b: 'c', c: 'd'})\n//=> ['a', 'b', 'c']\n\ncu.nativeKeys(function(){})\n//=> ['length', 'caller']\n```\n\n### [.getDescriptor](index.js#L208)\n\nReturns property descriptor `key` if it's an \"own\" property of the given object.\n\n**Params**\n\n* `obj` **{Object}**\n* `key` **{String}**\n* `returns` **{Object}**: Returns descriptor `key`\n\n**Example**\n\n```js\nfunction App() {}\nObject.defineProperty(App.prototype, 'count', {\n  get: function() {\n    return Object.keys(this).length;\n  }\n});\ncu.getDescriptor(App.prototype, 'count');\n// returns:\n// {\n//   get: [Function],\n//   set: undefined,\n//   enumerable: false,\n//   configurable: false\n// }\n```\n\n### [.copyDescriptor](index.js#L238)\n\nCopy a descriptor from one object to another.\n\n**Params**\n\n* `receiver` **{Object}**\n* `provider` **{Object}**\n* `name` **{String}**\n* `returns` **{Object}**\n\n**Example**\n\n```js\nfunction App() {}\nObject.defineProperty(App.prototype, 'count', {\n  get: function() {\n    return Object.keys(this).length;\n  }\n});\nvar obj = {};\ncu.copyDescriptor(obj, App.prototype, 'count');\n```\n\n### [.copy](index.js#L264)\n\nCopy static properties, prototype properties, and descriptors\nfrom one object to another.\n\n**Params**\n\n* `receiver` **{Object}**\n* `provider` **{Object}**\n* `omit` **{String|Array}**: One or more properties to omit\n* `returns` **{Object}**\n\n### [.inherit](index.js#L299)\n\nInherit the static properties, prototype properties, and descriptors\nfrom of an object.\n\n**Params**\n\n* `receiver` **{Object}**\n* `provider` **{Object}**\n* `omit` **{String|Array}**: One or more properties to omit\n* `returns` **{Object}**\n\n### [.extend](index.js#L343)\n\nReturns a function for extending the static properties, prototype properties, and descriptors from the `Parent` constructor onto `Child` constructors.\n\n**Params**\n\n* `Parent` **{Function}**: Parent ctor\n* `extend` **{Function}**: Optional extend function to handle custom extensions. Useful when updating methods that require a specific prototype.\n* `Child` **{Function}**: Child ctor\n* `proto` **{Object}**: Optionally pass additional prototype properties to inherit.\n* `returns` **{Object}**\n\n**Example**\n\n```js\nvar extend = cu.extend(Parent);\nParent.extend(Child);\n\n// optional methods\nParent.extend(Child, {\n  foo: function() {},\n  bar: function() {}\n});\n```\n\n### [.bubble](index.js#L356)\n\nBubble up events emitted from static methods on the Parent ctor.\n\n**Params**\n\n* `Parent` **{Object}**\n* `events` **{Array}**: Event names to bubble up\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [define-property](https://www.npmjs.com/package/define-property): Define a non-enumerable property on an object. Uses Reflect.defineProperty when available, otherwise Object.defineProperty. | [homepage](https://github.com/jonschlinkert/define-property \"Define a non-enumerable property on an object. Uses Reflect.defineProperty when available, otherwise Object.defineProperty.\")\n* [delegate-properties](https://www.npmjs.com/package/delegate-properties): Deep-clone properties from one object to another and make them non-enumerable, or make existing properties… [more](https://github.com/jonschlinkert/delegate-properties) | [homepage](https://github.com/jonschlinkert/delegate-properties \"Deep-clone properties from one object to another and make them non-enumerable, or make existing properties on an object non-enumerable.\")\n* [is-descriptor](https://www.npmjs.com/package/is-descriptor): Returns true if a value has the characteristics of a valid JavaScript descriptor. Works for… [more](https://github.com/jonschlinkert/is-descriptor) | [homepage](https://github.com/jonschlinkert/is-descriptor \"Returns true if a value has the characteristics of a valid JavaScript descriptor. Works for data descriptors and accessor descriptors.\")\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 34 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 8 | [doowb](https://github.com/doowb) |\n| 2 | [wtgtybhertgeghgtwtg](https://github.com/wtgtybhertgeghgtwtg) |\n\n### Author\n\n**Jon Schlinkert**\n\n* [linkedin/in/jonschlinkert](https://linkedin.com/in/jonschlinkert)\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on January 11, 2018._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015, 2017-2018, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/class-utils/-/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463", "type": "tarball", "reference": "https://registry.yarnpkg.com/class-utils/-/class-utils-0.3.6.tgz", "hash": "f93369ae8b9a7ce02fd41faad0ca83033190c463", "integrity": "sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg==", "registry": "npm", "packageName": "class-utils", "cacheIntegrity": "sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg== sha1-+TNprouafOAv1B+q0MqDAzGQxGM="}, "registry": "npm", "hash": "f93369ae8b9a7ce02fd41faad0ca83033190c463"}