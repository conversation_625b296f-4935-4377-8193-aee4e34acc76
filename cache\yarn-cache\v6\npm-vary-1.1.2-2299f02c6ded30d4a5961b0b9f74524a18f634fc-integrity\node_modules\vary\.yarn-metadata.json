{"manifest": {"name": "vary", "description": "Manipulate the HTTP Vary header", "version": "1.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["http", "res", "vary"], "repository": {"type": "git", "url": "https://github.com/jshttp/vary.git"}, "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "5.1.1", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "2.5.3", "supertest": "1.1.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-vary-1.1.2-2299f02c6ded30d4a5961b0b9f74524a18f634fc-integrity\\node_modules\\vary\\package.json", "readmeFilename": "README.md", "readme": "# vary\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nManipulate the HTTP Vary header\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally): \n\n```sh\n$ npm install vary\n```\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar vary = require('vary')\n```\n\n### vary(res, field)\n\nAdds the given header `field` to the `Vary` response header of `res`.\nThis can be a string of a single field, a string of a valid `Vary`\nheader, or an array of multiple fields.\n\nThis will append the header if not already listed, otherwise leaves\nit listed in the current location.\n\n<!-- eslint-disable no-undef -->\n\n```js\n// Append \"Origin\" to the Vary header of the response\nvary(res, 'Origin')\n```\n\n### vary.append(header, field)\n\nAdds the given header `field` to the `Vary` response header string `header`.\nThis can be a string of a single field, a string of a valid `Vary` header,\nor an array of multiple fields.\n\nThis will append the header if not already listed, otherwise leaves\nit listed in the current location. The new header string is returned.\n\n<!-- eslint-disable no-undef -->\n\n```js\n// Get header string appending \"Origin\" to \"Accept, User-Agent\"\nvary.append('Accept, User-Agent', 'Origin')\n```\n\n## Examples\n\n### Updating the Vary header when content is based on it\n\n```js\nvar http = require('http')\nvar vary = require('vary')\n\nhttp.createServer(function onRequest (req, res) {\n  // about to user-agent sniff\n  vary(res, 'User-Agent')\n\n  var ua = req.headers['user-agent'] || ''\n  var isMobile = /mobi|android|touch|mini/i.test(ua)\n\n  // serve site, depending on isMobile\n  res.setHeader('Content-Type', 'text/html')\n  res.end('You are (probably) ' + (isMobile ? '' : 'not ') + 'a mobile user')\n})\n```\n\n## Testing\n\n```sh\n$ npm test\n```\n\n## License\n\n[MIT](LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/vary.svg\n[npm-url]: https://npmjs.org/package/vary\n[node-version-image]: https://img.shields.io/node/v/vary.svg\n[node-version-url]: https://nodejs.org/en/download\n[travis-image]: https://img.shields.io/travis/jshttp/vary/master.svg\n[travis-url]: https://travis-ci.org/jshttp/vary\n[coveralls-image]: https://img.shields.io/coveralls/jshttp/vary/master.svg\n[coveralls-url]: https://coveralls.io/r/jshttp/vary\n[downloads-image]: https://img.shields.io/npm/dm/vary.svg\n[downloads-url]: https://npmjs.org/package/vary\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2014-2017 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/vary/-/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc", "type": "tarball", "reference": "https://registry.yarnpkg.com/vary/-/vary-1.1.2.tgz", "hash": "2299f02c6ded30d4a5961b0b9f74524a18f634fc", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "registry": "npm", "packageName": "vary", "cacheIntegrity": "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg== sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="}, "registry": "npm", "hash": "2299f02c6ded30d4a5961b0b9f74524a18f634fc"}