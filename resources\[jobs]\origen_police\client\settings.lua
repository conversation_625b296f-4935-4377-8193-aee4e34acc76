local L0_1, L1_1, L2_1, L3_1
function L0_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2
  L3_2 = math
  L3_2 = L3_2.abs
  L4_2 = A0_2 - A1_2
  L3_2 = L3_2(L4_2)
  L4_2 = A2_2 or L4_2
  if not A2_2 then
    L4_2 = 0.5
  end
  L3_2 = L3_2 < L4_2
  return L3_2
end
NumberEquals = L0_1
L0_1 = RegisterNUICallback
L1_1 = "GotoMarker"
function L2_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2
  L2_2 = vector3
  L3_2 = tonumber
  L4_2 = A0_2.x
  L3_2 = L3_2(L4_2)
  L4_2 = tonumber
  L5_2 = A0_2.y
  L4_2 = L4_2(L5_2)
  L5_2 = tonumber
  L6_2 = A0_2.z
  L5_2, L6_2 = L5_2(L6_2)
  L2_2 = L2_2(L3_2, L4_2, L5_2, L6_2)
  L3_2 = SetEntityCoords
  L4_2 = PlayerPedId
  L4_2 = L4_2()
  L5_2 = L2_2
  L3_2(L4_2, L5_2)
  L3_2 = A1_2
  L4_2 = true
  L3_2(L4_2)
end
L0_1(L1_1, L2_1)
L0_1 = RegisterNUICallback
L1_1 = "GetJobCategories"
function L2_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  L2_2 = Config
  L2_2 = L2_2.JobCategory
  L3_2 = {}
  L4_2 = pairs
  L5_2 = L2_2
  L4_2, L5_2, L6_2, L7_2 = L4_2(L5_2)
  for L8_2, L9_2 in L4_2, L5_2, L6_2, L7_2 do
    L10_2 = {}
    L3_2[L8_2] = L10_2
    L10_2 = pairs
    L11_2 = L9_2
    L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2)
    for L14_2, L15_2 in L10_2, L11_2, L12_2, L13_2 do
      L16_2 = L3_2[L8_2]
      L17_2 = L3_2[L8_2]
      L17_2 = #L17_2
      L17_2 = L17_2 + 1
      L18_2 = L15_2.name
      L16_2[L17_2] = L18_2
    end
  end
  L4_2 = A1_2
  L5_2 = L3_2
  L4_2(L5_2)
end
L0_1(L1_1, L2_1)
L0_1 = RegisterNUICallback
L1_1 = "SetMarkerPos"
function L2_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2
  L2_2 = SetNewPos
  L3_2 = A0_2.station
  L4_2 = A0_2.markerName
  L5_2 = A0_2.markerIndex
  L6_2 = A0_2.multiple
  L2_2(L3_2, L4_2, L5_2, L6_2)
  L2_2 = A1_2
  L3_2 = true
  L2_2(L3_2)
end
L0_1(L1_1, L2_1)
L0_1 = RegisterNUICallback
L1_1 = "CreateMarker"
function L2_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L2_2 = SetNewPos
  L3_2 = A0_2.station
  L4_2 = A0_2.markerName
  L5_2 = nil
  L6_2 = A0_2.multiple
  L7_2 = true
  L2_2(L3_2, L4_2, L5_2, L6_2, L7_2)
  L2_2 = A1_2
  L3_2 = true
  L2_2(L3_2)
end
L0_1(L1_1, L2_1)
function L0_1(A0_2, A1_2, A2_2, A3_2, A4_2)
  local sprite = 20
  local marker = MarkersList[A1_2] or PublicMarkerList[A1_2]
  if marker then
    sprite = marker.sprite
  end

  local markerCount = 0
  local markerData = {}

  Citizen.CreateThread(function()
    while true do
      Citizen.Wait(10)
      local hit, coords = GetScreenCoords(100)

      if Config.CustomNotify then
        ShowHelpNotification("E", markerCount >= 1 and "Place spawn position" or "Place marker position")
      elseif Config.Framework == "qbcore" then
        exports["qb-core"].DrawText("[E] - " .. (markerCount >= 1 and "Place spawn position" or "Place marker position"), "left")
      elseif Config.Framework == "esx" then
        Framework.ShowHelpNotification("~INPUT_PICKUP~ " .. (markerCount >= 1 and "Place spawn position" or "Place marker position"), true)
      end

      if hit then
        local playerCoords = GetEntityCoords(PlayerPedId())
        DrawLine(playerCoords.x, playerCoords.y, playerCoords.z, coords.x, coords.y, coords.z, 255, 255, 255, 100)
        DrawMarker(sprite, coords.x, coords.y, coords.z + 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.3, 0.3, 0.3, 0, 0, 0, 100, false, false, 0, true)

        if IsControlJustPressed(0, 38) then
          markerCount = markerCount + 1
          local markerType = markerCount == 1 and "coords" or "spawn"
          markerData[markerType] = {coords.x, coords.y, coords.z + 1.0, GetEntityHeading(PlayerPedId())}
          HideHelpNotification()

          if not A4_2 then
            FW_TriggerCallback("origen_police:callback:UpdateMarkerPos", function(response)
              Debuger("Update marker pos: " .. json.encode(response))
            end, {
              station = A0_2,
              markerName = A1_2,
              markerIndex = tonumber(A2_2) + 1,
              x = coords.x,
              y = coords.y,
              z = coords.z + 1.0,
              w = GetEntityHeading(PlayerPedId()),
              isMultiple = markerCount > 1
            })
          elseif markerCount == 1 or not A3_2 then
            FW_TriggerCallback("origen_police:callback:CreateMarker", function(response)
              Debuger("Create marker: " .. json.encode(response))
            end, {
              station = A0_2,
              markerName = A1_2,
              creatingData = markerData
            })
          end

          if markerCount > 1 then
            TogglePause(true)
            break
          end
        elseif IsControlJustPressed(0, 177) then
          TogglePause(true)
          break
        end
      end
    end
    HideHelpNotification()
  end)
end
SetNewPos = L0_1
function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L1_2 = pairs
  L2_2 = Config
  L2_2 = L2_2.Maps
  L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2)
  for L5_2, L6_2 in L1_2, L2_2, L3_2, L4_2 do
    if L6_2 == A0_2 then
      L7_2 = tonumber
      L8_2 = L5_2
      return L7_2(L8_2)
    end
  end
end
GetStationIndex = L0_1
L0_1 = RegisterNetEvent
L1_1 = "origen_police:client:UpdateMarkerPos"
function L2_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  L1_2 = A0_2.markerName
  if "BillsNPC" == L1_2 then
    L1_2 = ShowNotification
    L2_2 = "The NPC position will be updated after a script restart"
    return L1_2(L2_2)
  end
  L1_2 = PublicMarkerList
  L2_2 = A0_2.markerName
  L1_2 = L1_2[L2_2]
  L1_2 = nil ~= L1_2
  if L1_2 then
    L2_2 = Public
    L2_2 = L2_2.Markers
    if L2_2 then
      goto lbl_27
    end
  end
  L2_2 = Tables
  L2_2 = L2_2.Markers
  L3_2 = GetStationIndex
  L4_2 = A0_2.station
  L3_2 = L3_2(L4_2)
  L2_2 = L2_2[L3_2]
  ::lbl_27::
  if L1_2 then
    L3_2 = PublicMarkerList
    L4_2 = A0_2.markerName
    L3_2 = L3_2[L4_2]
    L3_2 = L3_2.event
    if L3_2 then
      goto lbl_39
    end
  end
  L3_2 = MarkersList
  L4_2 = A0_2.markerName
  L3_2 = L3_2[L4_2]
  L3_2 = L3_2.event
  ::lbl_39::
  L4_2 = pairs
  L5_2 = L2_2
  L4_2, L5_2, L6_2, L7_2 = L4_2(L5_2)
  for L8_2, L9_2 in L4_2, L5_2, L6_2, L7_2 do
    L10_2 = L9_2.event
    if L10_2 == L3_2 then
      L10_2 = A0_2.isMultiple
      if L10_2 then
        L10_2 = "spawn"
        if L10_2 then
          goto lbl_53
        end
      end
      L10_2 = "coords"
      ::lbl_53::
      L11_2 = vector4
      L12_2 = A0_2.x
      L13_2 = A0_2.y
      L14_2 = A0_2.z
      L15_2 = A0_2.w
      L11_2 = L11_2(L12_2, L13_2, L14_2, L15_2)
      L9_2[L10_2] = L11_2
      break
    end
  end
end
L0_1(L1_1, L2_1)
L0_1 = RegisterNetEvent
L1_1 = "origen_police:client:RemoveMarker"
function L2_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L1_2 = A0_2.markerName
  if "BillsNPC" == L1_2 then
    L1_2 = ShowNotification
    L2_2 = "The NPC will be removed after a script restart"
    return L1_2(L2_2)
  end
  L1_2 = PublicMarkerList
  L2_2 = A0_2.markerName
  L1_2 = L1_2[L2_2]
  L1_2 = nil ~= L1_2
  if L1_2 then
    L2_2 = Public
    L2_2 = L2_2.Markers
    if L2_2 then
      goto lbl_27
    end
  end
  L2_2 = Tables
  L2_2 = L2_2.Markers
  L3_2 = GetStationIndex
  L4_2 = A0_2.station
  L3_2 = L3_2(L4_2)
  L2_2 = L2_2[L3_2]
  ::lbl_27::
  if L1_2 then
    L3_2 = PublicMarkerList
    L4_2 = A0_2.markerName
    L3_2 = L3_2[L4_2]
    L3_2 = L3_2.event
    if L3_2 then
      goto lbl_39
    end
  end
  L3_2 = MarkersList
  L4_2 = A0_2.markerName
  L3_2 = L3_2[L4_2]
  L3_2 = L3_2.event
  ::lbl_39::
  L4_2 = pairs
  L5_2 = L2_2
  L4_2, L5_2, L6_2, L7_2 = L4_2(L5_2)
  for L8_2, L9_2 in L4_2, L5_2, L6_2, L7_2 do
    L10_2 = L9_2.event
    if L10_2 == L3_2 then
      L10_2 = NumberEquals
      L11_2 = L9_2.coords
      L11_2 = L11_2.x
      L12_2 = A0_2.coords
      L12_2 = L12_2[1]
      L10_2 = L10_2(L11_2, L12_2)
      if L10_2 then
        L10_2 = NumberEquals
        L11_2 = L9_2.coords
        L11_2 = L11_2.y
        L12_2 = A0_2.coords
        L12_2 = L12_2[2]
        L10_2 = L10_2(L11_2, L12_2)
        if L10_2 then
          L10_2 = NumberEquals
          L11_2 = L9_2.coords
          L11_2 = L11_2.z
          L12_2 = A0_2.coords
          L12_2 = L12_2[3]
          L10_2 = L10_2(L11_2, L12_2)
          if L10_2 then
            L10_2 = print
            L11_2 = "REMOVED MARKER: "
            L12_2 = A0_2.markerName
            L10_2(L11_2, L12_2)
            L10_2 = table
            L10_2 = L10_2.remove
            L11_2 = L2_2
            L12_2 = L8_2
            L10_2(L11_2, L12_2)
            break
          end
        end
      end
    end
  end
end
L0_1(L1_1, L2_1)
L0_1 = RegisterNetEvent
L1_1 = "origen_police:client:CreateMarker"
function L2_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L1_2 = A0_2.markerName
  if "BillsNPC" == L1_2 then
    L1_2 = ShowNotification
    L2_2 = "The NPC will be spawned after a script restart"
    return L1_2(L2_2)
  end
  L1_2 = PublicMarkerList
  L2_2 = A0_2.markerName
  L1_2 = L1_2[L2_2]
  L1_2 = nil ~= L1_2
  if L1_2 then
    L2_2 = PublicMarkerList
    L3_2 = A0_2.markerName
    L2_2 = L2_2[L3_2]
    if L2_2 then
      goto lbl_25
    end
  end
  L2_2 = MarkersList
  L3_2 = A0_2.markerName
  L2_2 = L2_2[L3_2]
  ::lbl_25::
  L3_2 = L2_2.event
  L4_2 = GetStationIndex
  L5_2 = A0_2.station
  L4_2 = L4_2(L5_2)
  if L1_2 then
    L5_2 = Public
    L5_2 = L5_2.Markers
    if L5_2 then
      goto lbl_38
    end
  end
  L5_2 = Tables
  L5_2 = L5_2.Markers
  L5_2 = L5_2[L4_2]
  ::lbl_38::
  if nil == L5_2 and not L1_2 then
    L6_2 = Tables
    L6_2 = L6_2.Markers
    L7_2 = {}
    L6_2[L4_2] = L7_2
    L6_2 = Tables
    L6_2 = L6_2.Markers
    L5_2 = L6_2[L4_2]
  end
  L6_2 = #L5_2
  L6_2 = L6_2 + 1
  L5_2[L6_2] = L2_2
  L7_2 = L5_2[L6_2]
  L8_2 = vector4
  L9_2 = A0_2.creatingData
  L9_2 = L9_2.coords
  L9_2 = L9_2[1]
  L10_2 = A0_2.creatingData
  L10_2 = L10_2.coords
  L10_2 = L10_2[2]
  L11_2 = A0_2.creatingData
  L11_2 = L11_2.coords
  L11_2 = L11_2[3]
  L12_2 = A0_2.creatingData
  L12_2 = L12_2.coords
  L12_2 = L12_2[4]
  L8_2 = L8_2(L9_2, L10_2, L11_2, L12_2)
  L7_2.coords = L8_2
  L7_2 = A0_2.creatingData
  L7_2 = L7_2.spawn
  if L7_2 then
    L7_2 = L5_2[L6_2]
    L8_2 = vector4
    L9_2 = A0_2.creatingData
    L9_2 = L9_2.spawn
    L9_2 = L9_2[1]
    L10_2 = A0_2.creatingData
    L10_2 = L10_2.spawn
    L10_2 = L10_2[2]
    L11_2 = A0_2.creatingData
    L11_2 = L11_2.spawn
    L11_2 = L11_2[3]
    L12_2 = A0_2.creatingData
    L12_2 = L12_2.coords
    L12_2 = L12_2[4]
    L8_2 = L8_2(L9_2, L10_2, L11_2, L12_2)
    L7_2.spawn = L8_2
  end
end
L0_1(L1_1, L2_1)
L0_1 = RegisterNetEvent
L1_1 = "origen_police:client:ActiveStation"
function L2_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L1_2 = A0_2.active
  if not L1_2 then
    L1_2 = pairs
    L2_2 = Config
    L2_2 = L2_2.Maps
    L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2)
    for L5_2, L6_2 in L1_2, L2_2, L3_2, L4_2 do
      L7_2 = A0_2.station
      if L6_2 == L7_2 then
        L7_2 = Config
        L7_2 = L7_2.Maps
        L7_2[L5_2] = nil
        break
      end
    end
    L1_2 = ReloadMarkers
    L1_2()
    return
  end
  L1_2 = Config
  L1_2 = L1_2.Maps
  L2_2 = Config
  L2_2 = L2_2.Maps
  L2_2 = #L2_2
  L2_2 = L2_2 + 1
  L3_2 = A0_2.station
  L1_2[L2_2] = L3_2
  L1_2 = ReloadMarkers
  L1_2()
end
L0_1(L1_1, L2_1)
L0_1 = false
L1_1 = RegisterNetEvent
L2_1 = "origen_police:client:OnPlayerLoaded"
function L3_1()
  local L0_2, L1_2, L2_2
  L0_2 = Config
  L0_2 = L0_2.IgnoreSettings
  if not L0_2 then
    L0_2 = L0_1
    if not L0_2 then
      goto lbl_9
    end
  end
  do return end
  ::lbl_9::
  L0_2 = true
  L0_1 = L0_2
  L0_2 = FW_TriggerCallback
  L1_2 = "origen_police:callback:GetActiveMaps"
  function L2_2(A0_3)
    local L1_3, L2_3
    L1_3 = Config
    L1_3.Maps = A0_3
    L1_3 = #A0_3
    if 0 == L1_3 then
      L1_3 = print
      L2_3 = "^3WARNING: NO ACTIVE MAPS, PLEASE ADD SOME IN THE SETTING TAB OF THE POLICE TABLET^0"
      L1_3(L2_3)
    end
    L1_3 = ReloadMarkers
    L1_3()
  end
  L0_2(L1_2, L2_2)
end
L1_1(L2_1, L3_1)
