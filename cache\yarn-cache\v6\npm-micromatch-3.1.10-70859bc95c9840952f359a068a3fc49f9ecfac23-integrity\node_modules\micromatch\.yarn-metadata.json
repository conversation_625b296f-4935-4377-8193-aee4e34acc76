{"manifest": {"name": "micromatch", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "version": "3.1.10", "homepage": "https://github.com/micromatch/micromatch", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "amilajack.com"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/TrySound"}, {"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "Devon Govett", "url": "http://badassjs.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://ultcombo.js.org"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "https://kolarik.sk"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://i.am.charlike.online"}, {"name": "<PERSON>", "url": "paulmillr.com"}, {"name": "<PERSON>", "url": "https://github.com/tomByrer"}, {"name": "<PERSON>", "url": "http://rumkin.com"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "repository": {"type": "git", "url": "https://github.com/micromatch/micromatch.git"}, "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "license": "MIT", "files": ["index.js", "lib"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.3", "gulp-mocha": "^5.0.0", "gulp-unused": "^0.2.1", "is-windows": "^1.0.2", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.3", "multimatch": "^2.1.0"}, "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "lintDeps": {"dependencies": {"options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": {"options": {"ignore": ["benchmark/**"]}}}}, "verb": {"toc": "collapsible", "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "helpers": ["./benchmark/helper.js"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "lint": {"reflinks": true}, "reflinks": ["expand-brackets", "extglob", "glob-object", "minimatch", "multimatch", "snapdragon"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-micromatch-3.1.10-70859bc95c9840952f359a068a3fc49f9ecfac23-integrity\\node_modules\\micromatch\\package.json", "readmeFilename": "README.md", "readme": "# micromatch [![NPM version](https://img.shields.io/npm/v/micromatch.svg?style=flat)](https://www.npmjs.com/package/micromatch) [![NPM monthly downloads](https://img.shields.io/npm/dm/micromatch.svg?style=flat)](https://npmjs.org/package/micromatch) [![NPM total downloads](https://img.shields.io/npm/dt/micromatch.svg?style=flat)](https://npmjs.org/package/micromatch) [![Linux Build Status](https://img.shields.io/travis/micromatch/micromatch.svg?style=flat&label=Travis)](https://travis-ci.org/micromatch/micromatch) [![Windows Build Status](https://img.shields.io/appveyor/ci/micromatch/micromatch.svg?style=flat&label=AppVeyor)](https://ci.appveyor.com/project/micromatch/micromatch)\n\n> Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Table of Contents\n\n<details>\n<summary><strong>Details</strong></summary>\n\n- [Install](#install)\n- [Quickstart](#quickstart)\n- [Why use micromatch?](#why-use-micromatch)\n  * [Matching features](#matching-features)\n- [Switching to micromatch](#switching-to-micromatch)\n  * [From minimatch](#from-minimatch)\n  * [From multimatch](#from-multimatch)\n- [API](#api)\n- [Options](#options)\n  * [options.basename](#optionsbasename)\n  * [options.bash](#optionsbash)\n  * [options.cache](#optionscache)\n  * [options.dot](#optionsdot)\n  * [options.failglob](#optionsfailglob)\n  * [options.ignore](#optionsignore)\n  * [options.matchBase](#optionsmatchbase)\n  * [options.nobrace](#optionsnobrace)\n  * [options.nocase](#optionsnocase)\n  * [options.nodupes](#optionsnodupes)\n  * [options.noext](#optionsnoext)\n  * [options.nonegate](#optionsnonegate)\n  * [options.noglobstar](#optionsnoglobstar)\n  * [options.nonull](#optionsnonull)\n  * [options.nullglob](#optionsnullglob)\n  * [options.snapdragon](#optionssnapdragon)\n  * [options.sourcemap](#optionssourcemap)\n  * [options.unescape](#optionsunescape)\n  * [options.unixify](#optionsunixify)\n- [Extended globbing](#extended-globbing)\n  * [extglobs](#extglobs)\n  * [braces](#braces)\n  * [regex character classes](#regex-character-classes)\n  * [regex groups](#regex-groups)\n  * [POSIX bracket expressions](#posix-bracket-expressions)\n- [Notes](#notes)\n  * [Bash 4.3 parity](#bash-43-parity)\n  * [Backslashes](#backslashes)\n- [Contributing](#contributing)\n- [Benchmarks](#benchmarks)\n  * [Running benchmarks](#running-benchmarks)\n  * [Latest results](#latest-results)\n- [About](#about)\n\n</details>\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save micromatch\n```\n\n## Quickstart\n\n```js\nvar mm = require('micromatch');\nmm(list, patterns[, options]);\n```\n\nThe [main export](#micromatch) takes a list of strings and one or more glob patterns:\n\n```js\nconsole.log(mm(['foo', 'bar', 'qux'], ['f*', 'b*'])); \n//=> ['foo', 'bar']\n```\n\nUse [.isMatch()](#ismatch) to get true/false:\n\n```js\nconsole.log(mm.isMatch('foo', 'f*'));  \n//=> true\n```\n\n[Switching](#switching-to-micromatch) from minimatch and multimatch is easy!\n\n## Why use micromatch?\n\n> micromatch is a [drop-in replacement](#switching-to-micromatch) for minimatch and multimatch\n\n* Supports all of the same matching features as [minimatch](https://github.com/isaacs/minimatch) and [multimatch](https://github.com/sindresorhus/multimatch)\n* Micromatch uses [snapdragon](https://github.com/jonschlinkert/snapdragon) for parsing and compiling globs, which provides granular control over the entire conversion process in a way that is easy to understand, reason about, and maintain.\n* More consistently accurate matching [than minimatch](https://github.com/yarnpkg/yarn/pull/3339), with more than 36,000 [test assertions](./test) to prove it.\n* More complete support for the Bash 4.3 specification than minimatch and multimatch. In fact, micromatch passes _all of the spec tests_ from bash, including some that bash still fails.\n* [Faster matching](#benchmarks), from a combination of optimized glob patterns, faster algorithms, and regex caching.\n* [Micromatch is safer](https://github.com/micromatch/braces#braces-is-safe), and is not subject to DoS with brace patterns, like minimatch and multimatch.\n* More reliable windows support than minimatch and multimatch.\n\n### Matching features\n\n* Support for multiple glob patterns (no need for wrappers like multimatch)\n* Wildcards (`**`, `*.js`)\n* Negation (`'!a/*.js'`, `'*!(b).js']`)\n* [extglobs](https://github.com/micromatch/extglob) (`+(x|y)`, `!(a|b)`)\n* [POSIX character classes](https://github.com/micromatch/expand-brackets) (`[[:alpha:][:digit:]]`)\n* [brace expansion](https://github.com/micromatch/braces) (`foo/{1..5}.md`, `bar/{a,b,c}.js`)\n* regex character classes (`foo-[1-5].js`)\n* regex logical \"or\" (`foo/(abc|xyz).js`)\n\nYou can mix and match these features to create whatever patterns you need!\n\n## Switching to micromatch\n\nThere is one notable difference between micromatch and minimatch in regards to how backslashes are handled. See [the notes about backslashes](#backslashes) for more information.\n\n### From minimatch\n\nUse [mm.isMatch()](#ismatch) instead of `minimatch()`:\n\n```js\nmm.isMatch('foo', 'b*');\n//=> false\n```\n\nUse [mm.match()](#match) instead of `minimatch.match()`:\n\n```js\nmm.match(['foo', 'bar'], 'b*');\n//=> 'bar'\n```\n\n### From multimatch\n\nSame signature:\n\n```js\nmm(['foo', 'bar', 'baz'], ['f*', '*z']);\n//=> ['foo', 'baz']\n```\n\n## API\n\n### [micromatch](index.js#L41)\n\nThe main function takes a list of strings and one or more glob patterns to use for matching.\n\n**Params**\n\n* `list` **{Array}**: A list of strings to match\n* `patterns` **{String|Array}**: One or more glob patterns to use for matching.\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Array}**: Returns an array of matches\n\n**Example**\n\n```js\nvar mm = require('micromatch');\nmm(list, patterns[, options]);\n\nconsole.log(mm(['a.js', 'a.txt'], ['*.js']));\n//=> [ 'a.js' ]\n```\n\n### [.match](index.js#L93)\n\nSimilar to the main function, but `pattern` must be a string.\n\n**Params**\n\n* `list` **{Array}**: Array of strings to match\n* `pattern` **{String}**: Glob pattern to use for matching.\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Array}**: Returns an array of matches\n\n**Example**\n\n```js\nvar mm = require('micromatch');\nmm.match(list, pattern[, options]);\n\nconsole.log(mm.match(['a.a', 'a.aa', 'a.b', 'a.c'], '*.a'));\n//=> ['a.a', 'a.aa']\n```\n\n### [.isMatch](index.js#L154)\n\nReturns true if the specified `string` matches the given glob `pattern`.\n\n**Params**\n\n* `string` **{String}**: String to match\n* `pattern` **{String}**: Glob pattern to use for matching.\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Boolean}**: Returns true if the string matches the glob pattern.\n\n**Example**\n\n```js\nvar mm = require('micromatch');\nmm.isMatch(string, pattern[, options]);\n\nconsole.log(mm.isMatch('a.a', '*.a'));\n//=> true\nconsole.log(mm.isMatch('a.b', '*.a'));\n//=> false\n```\n\n### [.some](index.js#L192)\n\nReturns true if some of the strings in the given `list` match any of the given glob `patterns`.\n\n**Params**\n\n* `list` **{String|Array}**: The string or array of strings to test. Returns as soon as the first match is found.\n* `patterns` **{String|Array}**: One or more glob patterns to use for matching.\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Boolean}**: Returns true if any patterns match `str`\n\n**Example**\n\n```js\nvar mm = require('micromatch');\nmm.some(list, patterns[, options]);\n\nconsole.log(mm.some(['foo.js', 'bar.js'], ['*.js', '!foo.js']));\n// true\nconsole.log(mm.some(['foo.js'], ['*.js', '!foo.js']));\n// false\n```\n\n### [.every](index.js#L228)\n\nReturns true if every string in the given `list` matches any of the given glob `patterns`.\n\n**Params**\n\n* `list` **{String|Array}**: The string or array of strings to test.\n* `patterns` **{String|Array}**: One or more glob patterns to use for matching.\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Boolean}**: Returns true if any patterns match `str`\n\n**Example**\n\n```js\nvar mm = require('micromatch');\nmm.every(list, patterns[, options]);\n\nconsole.log(mm.every('foo.js', ['foo.js']));\n// true\nconsole.log(mm.every(['foo.js', 'bar.js'], ['*.js']));\n// true\nconsole.log(mm.every(['foo.js', 'bar.js'], ['*.js', '!foo.js']));\n// false\nconsole.log(mm.every(['foo.js'], ['*.js', '!foo.js']));\n// false\n```\n\n### [.any](index.js#L260)\n\nReturns true if **any** of the given glob `patterns` match the specified `string`.\n\n**Params**\n\n* `str` **{String|Array}**: The string to test.\n* `patterns` **{String|Array}**: One or more glob patterns to use for matching.\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Boolean}**: Returns true if any patterns match `str`\n\n**Example**\n\n```js\nvar mm = require('micromatch');\nmm.any(string, patterns[, options]);\n\nconsole.log(mm.any('a.a', ['b.*', '*.a']));\n//=> true\nconsole.log(mm.any('a.a', 'b.*'));\n//=> false\n```\n\n### [.all](index.js#L308)\n\nReturns true if **all** of the given `patterns` match the specified string.\n\n**Params**\n\n* `str` **{String|Array}**: The string to test.\n* `patterns` **{String|Array}**: One or more glob patterns to use for matching.\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Boolean}**: Returns true if any patterns match `str`\n\n**Example**\n\n```js\nvar mm = require('micromatch');\nmm.all(string, patterns[, options]);\n\nconsole.log(mm.all('foo.js', ['foo.js']));\n// true\n\nconsole.log(mm.all('foo.js', ['*.js', '!foo.js']));\n// false\n\nconsole.log(mm.all('foo.js', ['*.js', 'foo.js']));\n// true\n\nconsole.log(mm.all('foo.js', ['*.js', 'f*', '*o*', '*o.js']));\n// true\n```\n\n### [.not](index.js#L340)\n\nReturns a list of strings that _**do not match any**_ of the given `patterns`.\n\n**Params**\n\n* `list` **{Array}**: Array of strings to match.\n* `patterns` **{String|Array}**: One or more glob pattern to use for matching.\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Array}**: Returns an array of strings that **do not match** the given patterns.\n\n**Example**\n\n```js\nvar mm = require('micromatch');\nmm.not(list, patterns[, options]);\n\nconsole.log(mm.not(['a.a', 'b.b', 'c.c'], '*.a'));\n//=> ['b.b', 'c.c']\n```\n\n### [.contains](index.js#L376)\n\nReturns true if the given `string` contains the given pattern. Similar to [.isMatch](#isMatch) but the pattern can match any part of the string.\n\n**Params**\n\n* `str` **{String}**: The string to match.\n* `patterns` **{String|Array}**: Glob pattern to use for matching.\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Boolean}**: Returns true if the patter matches any part of `str`.\n\n**Example**\n\n```js\nvar mm = require('micromatch');\nmm.contains(string, pattern[, options]);\n\nconsole.log(mm.contains('aa/bb/cc', '*b'));\n//=> true\nconsole.log(mm.contains('aa/bb/cc', '*d'));\n//=> false\n```\n\n### [.matchKeys](index.js#L432)\n\nFilter the keys of the given object with the given `glob` pattern and `options`. Does not attempt to match nested keys. If you need this feature, use [glob-object](https://github.com/jonschlinkert/glob-object) instead.\n\n**Params**\n\n* `object` **{Object}**: The object with keys to filter.\n* `patterns` **{String|Array}**: One or more glob patterns to use for matching.\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Object}**: Returns an object with only keys that match the given patterns.\n\n**Example**\n\n```js\nvar mm = require('micromatch');\nmm.matchKeys(object, patterns[, options]);\n\nvar obj = { aa: 'a', ab: 'b', ac: 'c' };\nconsole.log(mm.matchKeys(obj, '*b'));\n//=> { ab: 'b' }\n```\n\n### [.matcher](index.js#L461)\n\nReturns a memoized matcher function from the given glob `pattern` and `options`. The returned function takes a string to match as its only argument and returns true if the string is a match.\n\n**Params**\n\n* `pattern` **{String}**: Glob pattern\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed.\n* `returns` **{Function}**: Returns a matcher function.\n\n**Example**\n\n```js\nvar mm = require('micromatch');\nmm.matcher(pattern[, options]);\n\nvar isMatch = mm.matcher('*.!(*a)');\nconsole.log(isMatch('a.a'));\n//=> false\nconsole.log(isMatch('a.b'));\n//=> true\n```\n\n### [.capture](index.js#L536)\n\nReturns an array of matches captured by `pattern` in `string, or`null` if the pattern did not match.\n\n**Params**\n\n* `pattern` **{String}**: Glob pattern to use for matching.\n* `string` **{String}**: String to match\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Boolean}**: Returns an array of captures if the string matches the glob pattern, otherwise `null`.\n\n**Example**\n\n```js\nvar mm = require('micromatch');\nmm.capture(pattern, string[, options]);\n\nconsole.log(mm.capture('test/*.js', 'test/foo.js'));\n//=> ['foo']\nconsole.log(mm.capture('test/*.js', 'foo/bar.css'));\n//=> null\n```\n\n### [.makeRe](index.js#L571)\n\nCreate a regular expression from the given glob `pattern`.\n\n**Params**\n\n* `pattern` **{String}**: A glob pattern to convert to regex.\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed.\n* `returns` **{RegExp}**: Returns a regex created from the given pattern.\n\n**Example**\n\n```js\nvar mm = require('micromatch');\nmm.makeRe(pattern[, options]);\n\nconsole.log(mm.makeRe('*.js'));\n//=> /^(?:(\\.[\\\\\\/])?(?!\\.)(?=.)[^\\/]*?\\.js)$/\n```\n\n### [.braces](index.js#L618)\n\nExpand the given brace `pattern`.\n\n**Params**\n\n* `pattern` **{String}**: String with brace pattern to expand.\n* `options` **{Object}**: Any [options](#options) to change how expansion is performed. See the [braces](https://github.com/micromatch/braces) library for all available options.\n* `returns` **{Array}**\n\n**Example**\n\n```js\nvar mm = require('micromatch');\nconsole.log(mm.braces('foo/{a,b}/bar'));\n//=> ['foo/(a|b)/bar']\n\nconsole.log(mm.braces('foo/{a,b}/bar', {expand: true}));\n//=> ['foo/(a|b)/bar']\n```\n\n### [.create](index.js#L685)\n\nParses the given glob `pattern` and returns an array of abstract syntax trees (ASTs), with the compiled `output` and optional source `map` on each AST.\n\n**Params**\n\n* `pattern` **{String}**: Glob pattern to parse and compile.\n* `options` **{Object}**: Any [options](#options) to change how parsing and compiling is performed.\n* `returns` **{Object}**: Returns an object with the parsed AST, compiled string and optional source map.\n\n**Example**\n\n```js\nvar mm = require('micromatch');\nmm.create(pattern[, options]);\n\nconsole.log(mm.create('abc/*.js'));\n// [{ options: { source: 'string', sourcemap: true },\n//   state: {},\n//   compilers:\n//    { ... },\n//   output: '(\\\\.[\\\\\\\\\\\\/])?abc\\\\/(?!\\\\.)(?=.)[^\\\\/]*?\\\\.js',\n//   ast:\n//    { type: 'root',\n//      errors: [],\n//      nodes:\n//       [ ... ],\n//      dot: false,\n//      input: 'abc/*.js' },\n//   parsingErrors: [],\n//   map:\n//    { version: 3,\n//      sources: [ 'string' ],\n//      names: [],\n//      mappings: 'AAAA,GAAG,EAAC,kBAAC,EAAC,EAAE',\n//      sourcesContent: [ 'abc/*.js' ] },\n//   position: { line: 1, column: 28 },\n//   content: {},\n//   files: {},\n//   idx: 6 }]\n```\n\n### [.parse](index.js#L732)\n\nParse the given `str` with the given `options`.\n\n**Params**\n\n* `str` **{String}**\n* `options` **{Object}**\n* `returns` **{Object}**: Returns an AST\n\n**Example**\n\n```js\nvar mm = require('micromatch');\nmm.parse(pattern[, options]);\n\nvar ast = mm.parse('a/{b,c}/d');\nconsole.log(ast);\n// { type: 'root',\n//   errors: [],\n//   input: 'a/{b,c}/d',\n//   nodes:\n//    [ { type: 'bos', val: '' },\n//      { type: 'text', val: 'a/' },\n//      { type: 'brace',\n//        nodes:\n//         [ { type: 'brace.open', val: '{' },\n//           { type: 'text', val: 'b,c' },\n//           { type: 'brace.close', val: '}' } ] },\n//      { type: 'text', val: '/d' },\n//      { type: 'eos', val: '' } ] }\n```\n\n### [.compile](index.js#L780)\n\nCompile the given `ast` or string with the given `options`.\n\n**Params**\n\n* `ast` **{Object|String}**\n* `options` **{Object}**\n* `returns` **{Object}**: Returns an object that has an `output` property with the compiled string.\n\n**Example**\n\n```js\nvar mm = require('micromatch');\nmm.compile(ast[, options]);\n\nvar ast = mm.parse('a/{b,c}/d');\nconsole.log(mm.compile(ast));\n// { options: { source: 'string' },\n//   state: {},\n//   compilers:\n//    { eos: [Function],\n//      noop: [Function],\n//      bos: [Function],\n//      brace: [Function],\n//      'brace.open': [Function],\n//      text: [Function],\n//      'brace.close': [Function] },\n//   output: [ 'a/(b|c)/d' ],\n//   ast:\n//    { ... },\n//   parsingErrors: [] }\n```\n\n### [.clearCache](index.js#L801)\n\nClear the regex cache.\n\n**Example**\n\n```js\nmm.clearCache();\n```\n\n## Options\n\n* [basename](#optionsbasename)\n* [bash](#optionsbash)\n* [cache](#optionscache)\n* [dot](#optionsdot)\n* [failglob](#optionsfailglob)\n* [ignore](#optionsignore)\n* [matchBase](#optionsmatchBase)\n* [nobrace](#optionsnobrace)\n* [nocase](#optionsnocase)\n* [nodupes](#optionsnodupes)\n* [noext](#optionsnoext)\n* [noglobstar](#optionsnoglobstar)\n* [nonull](#optionsnonull)\n* [nullglob](#optionsnullglob)\n* [snapdragon](#optionssnapdragon)\n* [sourcemap](#optionssourcemap)\n* [unescape](#optionsunescape)\n* [unixify](#optionsunixify)\n\n### options.basename\n\nAllow glob patterns without slashes to match a file path based on its basename. Same behavior as [minimatch](https://github.com/isaacs/minimatch) option `matchBase`.\n\n**Type**: `Boolean`\n\n**Default**: `false`\n\n**Example**\n\n```js\nmm(['a/b.js', 'a/c.md'], '*.js');\n//=> []\n\nmm(['a/b.js', 'a/c.md'], '*.js', {matchBase: true});\n//=> ['a/b.js']\n```\n\n### options.bash\n\nEnabled by default, this option enforces bash-like behavior with stars immediately following a bracket expression. Bash bracket expressions are similar to regex character classes, but unlike regex, a star following a bracket expression **does not repeat the bracketed characters**. Instead, the star is treated the same as an other star.\n\n**Type**: `Boolean`\n\n**Default**: `true`\n\n**Example**\n\n```js\nvar files = ['abc', 'ajz'];\nconsole.log(mm(files, '[a-c]*'));\n//=> ['abc', 'ajz']\n\nconsole.log(mm(files, '[a-c]*', {bash: false}));\n```\n\n### options.cache\n\nDisable regex and function memoization.\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n### options.dot\n\nMatch dotfiles. Same behavior as [minimatch](https://github.com/isaacs/minimatch) option `dot`.\n\n**Type**: `Boolean`\n\n**Default**: `false`\n\n### options.failglob\n\nSimilar to the `--failglob` behavior in Bash, throws an error when no matches are found.\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n### options.ignore\n\nString or array of glob patterns to match files to ignore.\n\n**Type**: `String|Array`\n\n**Default**: `undefined`\n\n### options.matchBase\n\nAlias for [options.basename](#options-basename).\n\n### options.nobrace\n\nDisable expansion of brace patterns. Same behavior as [minimatch](https://github.com/isaacs/minimatch) option `nobrace`.\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\nSee [braces](https://github.com/micromatch/braces) for more information about extended brace expansion.\n\n### options.nocase\n\nUse a case-insensitive regex for matching files. Same behavior as [minimatch](https://github.com/isaacs/minimatch).\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n### options.nodupes\n\nRemove duplicate elements from the result array.\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n**Example**\n\nExample of using the `unescape` and `nodupes` options together:\n\n```js\nmm.match(['a/b/c', 'a/b/c'], 'a/b/c');\n//=> ['a/b/c', 'a/b/c']\n\nmm.match(['a/b/c', 'a/b/c'], 'a/b/c', {nodupes: true});\n//=> ['abc']\n```\n\n### options.noext\n\nDisable extglob support, so that extglobs are regarded as literal characters.\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n**Examples**\n\n```js\nmm(['a/z', 'a/b', 'a/!(z)'], 'a/!(z)');\n//=> ['a/b', 'a/!(z)']\n\nmm(['a/z', 'a/b', 'a/!(z)'], 'a/!(z)', {noext: true});\n//=> ['a/!(z)'] (matches only as literal characters)\n```\n\n### options.nonegate\n\nDisallow negation (`!`) patterns, and treat leading `!` as a literal character to match.\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n### options.noglobstar\n\nDisable matching with globstars (`**`).\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n```js\nmm(['a/b', 'a/b/c', 'a/b/c/d'], 'a/**');\n//=> ['a/b', 'a/b/c', 'a/b/c/d']\n\nmm(['a/b', 'a/b/c', 'a/b/c/d'], 'a/**', {noglobstar: true});\n//=> ['a/b']\n```\n\n### options.nonull\n\nAlias for [options.nullglob](#options-nullglob).\n\n### options.nullglob\n\nIf `true`, when no matches are found the actual (arrayified) glob pattern is returned instead of an empty array. Same behavior as [minimatch](https://github.com/isaacs/minimatch) option `nonull`.\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n### options.snapdragon\n\nPass your own instance of [snapdragon](https://github.com/jonschlinkert/snapdragon), to customize parsers or compilers.\n\n**Type**: `Object`\n\n**Default**: `undefined`\n\n### options.sourcemap\n\nGenerate a source map by enabling the `sourcemap` option with the `.parse`, `.compile`, or `.create` methods.\n\n_(Note that sourcemaps are currently not enabled for brace patterns)_\n\n**Examples**\n\n``` js\nvar mm = require('micromatch');\nvar pattern = '*(*(of*(a)x)z)';\n\nvar res = mm.create('abc/*.js', {sourcemap: true});\nconsole.log(res.map);\n// { version: 3,\n//   sources: [ 'string' ],\n//   names: [],\n//   mappings: 'AAAA,GAAG,EAAC,iBAAC,EAAC,EAAE',\n//   sourcesContent: [ 'abc/*.js' ] }\n\nvar ast = mm.parse('abc/**/*.js');\nvar res = mm.compile(ast, {sourcemap: true});\nconsole.log(res.map);\n// { version: 3,\n//   sources: [ 'string' ],\n//   names: [],\n//   mappings: 'AAAA,GAAG,EAAC,2BAAE,EAAC,iBAAC,EAAC,EAAE',\n//   sourcesContent: [ 'abc/**/*.js' ] }\n\nvar ast = mm.parse(pattern);\nvar res = mm.compile(ast, {sourcemap: true});\nconsole.log(res.map);\n// { version: 3,\n//   sources: [ 'string' ],\n//   names: [],\n//   mappings: 'AAAA,CAAE,CAAE,EAAE,CAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC',\n//   sourcesContent: [ '*(*(of*(a)x)z)' ] }\n```\n\n### options.unescape\n\nRemove backslashes from returned matches.\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n**Example**\n\nIn this example we want to match a literal `*`:\n\n```js\nmm.match(['abc', 'a\\\\*c'], 'a\\\\*c');\n//=> ['a\\\\*c']\n\nmm.match(['abc', 'a\\\\*c'], 'a\\\\*c', {unescape: true});\n//=> ['a*c']\n```\n\n### options.unixify\n\nConvert path separators on returned files to posix/unix-style forward slashes.\n\n**Type**: `Boolean`\n\n**Default**: `true` on windows, `false` everywhere else\n\n**Example**\n\n```js\nmm.match(['a\\\\b\\\\c'], 'a/**');\n//=> ['a/b/c']\n\nmm.match(['a\\\\b\\\\c'], {unixify: false});\n//=> ['a\\\\b\\\\c']\n```\n\n## Extended globbing\n\nMicromatch also supports extended globbing features.\n\n### extglobs\n\nExtended globbing, as described by the bash man page:\n\n| **pattern** | **regex equivalent** | **description** | \n| --- | --- | --- |\n| `?(pattern)` | `(pattern)?` | Matches zero or one occurrence of the given patterns |\n| `*(pattern)` | `(pattern)*` | Matches zero or more occurrences of the given patterns |\n| `+(pattern)` | `(pattern)+` | Matches one or more occurrences of the given patterns |\n| `@(pattern)` | `(pattern)` <sup>*</sup> | Matches one of the given patterns |\n| `!(pattern)` | N/A (equivalent regex is much more complicated) | Matches anything except one of the given patterns |\n\n<sup><strong>*</strong></sup> Note that `@` isn't a RegEx character.\n\nPowered by [extglob](https://github.com/micromatch/extglob). Visit that library for the full range of options or to report extglob related issues.\n\n### braces\n\nBrace patterns can be used to match specific ranges or sets of characters. For example, the pattern `*/{1..3}/*` would match any of following strings:\n\n```\nfoo/1/bar\nfoo/2/bar\nfoo/3/bar\nbaz/1/qux\nbaz/2/qux\nbaz/3/qux\n```\n\nVisit [braces](https://github.com/micromatch/braces) to see the full range of features and options related to brace expansion, or to create brace matching or expansion related issues.\n\n### regex character classes\n\nGiven the list: `['a.js', 'b.js', 'c.js', 'd.js', 'E.js']`:\n\n* `[ac].js`: matches both `a` and `c`, returning `['a.js', 'c.js']`\n* `[b-d].js`: matches from `b` to `d`, returning `['b.js', 'c.js', 'd.js']`\n* `[b-d].js`: matches from `b` to `d`, returning `['b.js', 'c.js', 'd.js']`\n* `a/[A-Z].js`: matches and uppercase letter, returning `['a/E.md']`\n\nLearn about [regex character classes](http://www.regular-expressions.info/charclass.html).\n\n### regex groups\n\nGiven `['a.js', 'b.js', 'c.js', 'd.js', 'E.js']`:\n\n* `(a|c).js`: would match either `a` or `c`, returning `['a.js', 'c.js']`\n* `(b|d).js`: would match either `b` or `d`, returning `['b.js', 'd.js']`\n* `(b|[A-Z]).js`: would match either `b` or an uppercase letter, returning `['b.js', 'E.js']`\n\nAs with regex, parens can be nested, so patterns like `((a|b)|c)/b` will work. Although brace expansion might be friendlier to use, depending on preference.\n\n### POSIX bracket expressions\n\nPOSIX brackets are intended to be more user-friendly than regex character classes. This of course is in the eye of the beholder.\n\n**Example**\n\n```js\nmm.isMatch('a1', '[[:alpha:][:digit:]]');\n//=> true\n\nmm.isMatch('a1', '[[:alpha:][:alpha:]]');\n//=> false\n```\n\nSee [expand-brackets](https://github.com/jonschlinkert/expand-brackets) for more information about bracket expressions.\n\n***\n\n## Notes\n\n### Bash 4.3 parity\n\nWhenever possible matching behavior is based on behavior Bash 4.3, which is mostly consistent with minimatch.\n\nHowever, it's suprising how many edge cases and rabbit holes there are with glob matching, and since there is no real glob specification, and micromatch is more accurate than both Bash and minimatch, there are cases where best-guesses were made for behavior. In a few cases where Bash had no answers, we used wildmatch (used by git) as a fallback.\n\n### Backslashes\n\nThere is an important, notable difference between minimatch and micromatch _in regards to how backslashes are handled_ in glob patterns.\n\n* Micromatch exclusively and explicitly reserves backslashes for escaping characters in a glob pattern, even on windows. This is consistent with bash behavior.\n* Minimatch converts all backslashes to forward slashes, which means you can't use backslashes to escape any characters in your glob patterns.\n\nWe made this decision for micromatch for a couple of reasons:\n\n* consistency with bash conventions.\n* glob patterns are not filepaths. They are a type of [regular language](https://en.wikipedia.org/wiki/Regular_language) that is converted to a JavaScript regular expression. Thus, when forward slashes are defined in a glob pattern, the resulting regular expression will match windows or POSIX path separators just fine.\n\n**A note about joining paths to globs**\n\nNote that when you pass something like `path.join('foo', '*')` to micromatch, you are creating a filepath and expecting it to still work as a glob pattern. This causes problems on windows, since the `path.sep` is `\\\\`.\n\nIn other words, since `\\\\` is reserved as an escape character in globs, on windows `path.join('foo', '*')` would result in `foo\\\\*`, which tells micromatch to match `*` as a literal character. This is the same behavior as bash.\n\n## Contributing\n\nAll contributions are welcome! Please read [the contributing guide](.github/contributing.md) to get started.\n\n**Bug reports**\n\nPlease create an issue if you encounter a bug or matching behavior that doesn't seem correct. If you find a matching-related issue, please:\n\n* [research existing issues first](../../issues) (open and closed)\n* visit the [GNU Bash documentation](https://www.gnu.org/software/bash/manual/) to see how Bash deals with the pattern\n* visit the [minimatch](https://github.com/isaacs/minimatch) documentation to cross-check expected behavior in node.js\n* if all else fails, since there is no real specification for globs we will probably need to discuss expected behavior and decide how to resolve it. which means any detail you can provide to help with this discussion would be greatly appreciated.\n\n**Platform issues**\n\nIt's important to us that micromatch work consistently on all platforms. If you encounter any platform-specific matching or path related issues, please let us know (pull requests are also greatly appreciated).\n\n## Benchmarks\n\n### Running benchmarks\n\nInstall dev dependencies:\n\n```bash\nnpm i -d && npm run benchmark\n```\n\n### Latest results\n\nAs of February 18, 2018 (longer bars are better):\n\n```sh\n# braces-globstar-large-list (485691 bytes)\n  micromatch ██████████████████████████████████████████████████ (517 ops/sec ±0.49%)\n  minimatch  █ (18.92 ops/sec ±0.54%)\n  multimatch █ (18.94 ops/sec ±0.62%)\n\n  micromatch is faster by an avg. of 2,733%\n\n# braces-multiple (3362 bytes)\n  micromatch ██████████████████████████████████████████████████ (33,625 ops/sec ±0.45%)\n  minimatch   (2.92 ops/sec ±3.26%)\n  multimatch  (2.90 ops/sec ±2.76%)\n\n  micromatch is faster by an avg. of 1,156,935%\n\n# braces-range (727 bytes)\n  micromatch █████████████████████████████████████████████████ (155,220 ops/sec ±0.56%)\n  minimatch  ██████ (20,186 ops/sec ±1.27%)\n  multimatch ██████ (19,809 ops/sec ±0.60%)\n\n  micromatch is faster by an avg. of 776%\n\n# braces-set (2858 bytes)\n  micromatch █████████████████████████████████████████████████ (24,354 ops/sec ±0.92%)\n  minimatch  █████ (2,566 ops/sec ±0.56%)\n  multimatch ████ (2,431 ops/sec ±1.25%)\n\n  micromatch is faster by an avg. of 975%\n\n# globstar-large-list (485686 bytes)\n  micromatch █████████████████████████████████████████████████ (504 ops/sec ±0.45%)\n  minimatch  ███ (33.36 ops/sec ±1.08%)\n  multimatch ███ (33.19 ops/sec ±1.35%)\n\n  micromatch is faster by an avg. of 1,514%\n\n# globstar-long-list (90647 bytes)\n  micromatch ██████████████████████████████████████████████████ (2,694 ops/sec ±1.08%)\n  minimatch  ████████████████ (870 ops/sec ±1.09%)\n  multimatch ████████████████ (862 ops/sec ±0.84%)\n\n  micromatch is faster by an avg. of 311%\n\n# globstar-short-list (182 bytes)\n  micromatch ██████████████████████████████████████████████████ (328,921 ops/sec ±1.06%)\n  minimatch  █████████ (64,808 ops/sec ±1.42%)\n  multimatch ████████ (57,991 ops/sec ±2.11%)\n\n  micromatch is faster by an avg. of 536%\n\n# no-glob (701 bytes)\n  micromatch █████████████████████████████████████████████████ (415,935 ops/sec ±0.36%)\n  minimatch  ███████████ (92,730 ops/sec ±1.44%)\n  multimatch █████████ (81,958 ops/sec ±2.13%)\n\n  micromatch is faster by an avg. of 476%\n\n# star-basename-long (12339 bytes)\n  micromatch █████████████████████████████████████████████████ (7,963 ops/sec ±0.36%)\n  minimatch  ███████████████████████████████ (5,072 ops/sec ±0.83%)\n  multimatch ███████████████████████████████ (5,028 ops/sec ±0.40%)\n\n  micromatch is faster by an avg. of 158%\n\n# star-basename-short (349 bytes)\n  micromatch ██████████████████████████████████████████████████ (269,552 ops/sec ±0.70%)\n  minimatch  ██████████████████████ (122,457 ops/sec ±1.39%)\n  multimatch ████████████████████ (110,788 ops/sec ±1.99%)\n\n  micromatch is faster by an avg. of 231%\n\n# star-folder-long (19207 bytes)\n  micromatch █████████████████████████████████████████████████ (3,806 ops/sec ±0.38%)\n  minimatch  ████████████████████████████ (2,204 ops/sec ±0.32%)\n  multimatch ██████████████████████████ (2,020 ops/sec ±1.07%)\n\n  micromatch is faster by an avg. of 180%\n\n# star-folder-short (551 bytes)\n  micromatch ██████████████████████████████████████████████████ (249,077 ops/sec ±0.40%)\n  minimatch  ███████████ (59,431 ops/sec ±1.67%)\n  multimatch ███████████ (55,569 ops/sec ±1.43%)\n\n  micromatch is faster by an avg. of 433%\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\nPlease read the [contributing guide](.github/contributing.md) for advice on opening issues, pull requests, and coding standards.\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [braces](https://www.npmjs.com/package/braces): Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support… [more](https://github.com/micromatch/braces) | [homepage](https://github.com/micromatch/braces \"Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.\")\n* [expand-brackets](https://www.npmjs.com/package/expand-brackets): Expand POSIX bracket expressions (character classes) in glob patterns. | [homepage](https://github.com/jonschlinkert/expand-brackets \"Expand POSIX bracket expressions (character classes) in glob patterns.\")\n* [extglob](https://www.npmjs.com/package/extglob): Extended glob support for JavaScript. Adds (almost) the expressive power of regular expressions to glob… [more](https://github.com/micromatch/extglob) | [homepage](https://github.com/micromatch/extglob \"Extended glob support for JavaScript. Adds (almost) the expressive power of regular expressions to glob patterns.\")\n* [fill-range](https://www.npmjs.com/package/fill-range): Fill in a range of numbers or letters, optionally passing an increment or `step` to… [more](https://github.com/jonschlinkert/fill-range) | [homepage](https://github.com/jonschlinkert/fill-range \"Fill in a range of numbers or letters, optionally passing an increment or `step` to use, or create a regex-compatible range with `options.toRegex`\")\n* [nanomatch](https://www.npmjs.com/package/nanomatch): Fast, minimal glob matcher for node.js. Similar to micromatch, minimatch and multimatch, but complete Bash… [more](https://github.com/micromatch/nanomatch) | [homepage](https://github.com/micromatch/nanomatch \"Fast, minimal glob matcher for node.js. Similar to micromatch, minimatch and multimatch, but complete Bash 4.3 wildcard support only (no support for exglobs, posix brackets or braces)\")\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 457 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 12 | [es128](https://github.com/es128) |\n| 8 | [doowb](https://github.com/doowb) |\n| 3 | [paulmillr](https://github.com/paulmillr) |\n| 2 | [TrySound](https://github.com/TrySound) |\n| 2 | [MartinKolarik](https://github.com/MartinKolarik) |\n| 2 | [charlike-old](https://github.com/charlike-old) |\n| 1 | [amilajack](https://github.com/amilajack) |\n| 1 | [mrmlnc](https://github.com/mrmlnc) |\n| 1 | [devongovett](https://github.com/devongovett) |\n| 1 | [DianeLooney](https://github.com/DianeLooney) |\n| 1 | [UltCombo](https://github.com/UltCombo) |\n| 1 | [tomByrer](https://github.com/tomByrer) |\n| 1 | [fidian](https://github.com/fidian) |\n\n### Author\n\n**Jon Schlinkert**\n\n* [linkedin/in/jonschlinkert](https://linkedin.com/in/jonschlinkert)\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on February 18, 2018._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2018, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/micromatch/-/micromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23", "type": "tarball", "reference": "https://registry.yarnpkg.com/micromatch/-/micromatch-3.1.10.tgz", "hash": "70859bc95c9840952f359a068a3fc49f9ecfac23", "integrity": "sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg==", "registry": "npm", "packageName": "micromatch", "cacheIntegrity": "sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg== sha1-cIWbyVyYQJUvNZoGij/En57PrCM="}, "registry": "npm", "hash": "70859bc95c9840952f359a068a3fc49f9ecfac23"}