{"manifest": {"name": "css-select", "version": "2.1.0", "description": "a CSS selector compiler/engine", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["css", "selector", "sizzle"], "repository": {"type": "git", "url": "git://github.com/fb55/css-select.git"}, "files": ["index.js", "index.d.ts", "lib"], "dependencies": {"boolbase": "^1.0.0", "css-what": "^3.2.1", "domutils": "^1.7.0", "nth-check": "^1.0.2"}, "devDependencies": {"cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^6.0.0", "expect.js": "^0.3.1", "htmlparser2": "^4.0.0", "istanbul": "^0.4.5", "mocha": "^6.0.0", "mocha-lcov-reporter": "^1.3.0"}, "scripts": {"test": "mocha && npm run lint", "lint": "eslint index.js lib/*.js test/*.js", "lcov": "istanbul cover _mocha --report lcovonly -- -R spec", "coveralls": "npm run lint && npm run lcov && (cat coverage/lcov.info | coveralls || exit 0)"}, "license": "BSD-2-<PERSON><PERSON>", "types": "index.d.ts", "prettier": {"tabWidth": 4}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-css-select-2.1.0-6a34653356635934a81baca68d0255432105dbef-integrity\\node_modules\\css-select\\package.json", "readmeFilename": "README.md", "readme": "# css-select [![NPM version](http://img.shields.io/npm/v/css-select.svg)](https://npmjs.org/package/css-select) [![Build Status](https://travis-ci.org/fb55/css-select.svg?branch=master)](http://travis-ci.org/fb55/css-select) [![Downloads](https://img.shields.io/npm/dm/css-select.svg)](https://npmjs.org/package/css-select) [![Coverage](https://coveralls.io/repos/fb55/css-select/badge.svg?branch=master)](https://coveralls.io/r/fb55/css-select)\n\na CSS selector compiler/engine\n\n## What?\n\ncss-select turns CSS selectors into functions that tests if elements match them. When searching for elements, testing is executed \"from the top\", similar to how browsers execute CSS selectors.\n\nIn its default configuration, css-select queries the DOM structure of the [`domhandler`](https://github.com/fb55/domhandler) module (also known as htmlparser2 DOM).\nIt uses [`domutils`](https://github.com/fb55/domutils) as its default adapter over the DOM structure. See Options below for details on querying alternative DOM structures.\n\n__Features:__\n\n- Full implementation of CSS3 selectors\n- Partial implementation of jQuery/Sizzle extensions\n- Very high test coverage\n- Pretty good performance\n\n## Why?\n\nThe traditional approach of executing CSS selectors, named left-to-right execution, is to execute every component of the selector in order, from left to right _(duh)_. The execution of the selector `a b` for example will first query for `a` elements, then search these for `b` elements. (That's the approach of eg. [`Sizzle`](https://github.com/jquery/sizzle), [`nwmatcher`](https://github.com/dperini/nwmatcher/) and [`qwery`](https://github.com/ded/qwery).)\n\nWhile this works, it has some downsides: Children of `a`s will be checked multiple times; first, to check if they are also `a`s, then, for every superior `a` once, if they are `b`s. Using [Big O notation](http://en.wikipedia.org/wiki/Big_O_notation), that would be `O(n^(k+1))`, where `k` is the number of descendant selectors (that's the space in the example above).\n\nThe far more efficient approach is to first look for `b` elements, then check if they have superior `a` elements: Using big O notation again, that would be `O(n)`. That's called right-to-left execution.\n\nAnd that's what css-select does – and why it's quite performant.\n\n## How does it work?\n\nBy building a stack of functions.\n\n_Wait, what?_\n\nOkay, so let's suppose we want to compile the selector `a b` again, for right-to-left execution. We start by _parsing_ the selector, which means we turn the selector into an array of the building-blocks of the selector, so we can distinguish them easily. That's what the [`css-what`](https://github.com/fb55/css-what) module is for, if you want to have a look.\n\nAnyway, after parsing, we end up with an array like this one:\n\n```js\n[\n  { type: 'tag', name: 'a' },\n  { type: 'descendant' },\n  { type: 'tag', name: 'b' }\n]\n```\n\nActually, this array is wrapped in another array, but that's another story (involving commas in selectors).\n\nNow that we know the meaning of every part of the selector, we can compile it. That's where it becomes interesting.\n\nThe basic idea is to turn every part of the selector into a function, which takes an element as its only argument. The function checks whether a passed element matches its part of the selector: If it does, the element is passed to the next turned-into-a-function part of the selector, which does the same. If an element is accepted by all parts of the selector, it _matches_ the selector and double rainbow ALL THE WAY.\n\nAs said before, we want to do right-to-left execution with all the big O improvements nonsense, so elements are passed from the rightmost part of the selector (`b` in our example) to the leftmost (~~which would be `c`~~ of course `a`).\n\n_//TODO: More in-depth description. Implementation details. Build a spaceship._\n\n## API\n\n```js\nconst CSSselect = require(\"css-select\");\n```\n\n__Note:__ css-select throws errors when invalid selectors are passed to it, contrary to the behavior in browsers, which swallow them. This is done to aid with writing css selectors, but can be unexpected when processing arbitrary strings.\n\n#### `CSSselect(query, elems, options)`\n\nQueries `elems`, returns an array containing all matches.\n\n- `query` can be either a CSS selector or a function.\n- `elems` can be either an array of elements, or a single element. If it is an element, its children will be queried.\n- `options` is described below.\n\nAliases: `CSSselect.selectAll(query, elems)`, `CSSselect.iterate(query, elems)`.\n\n#### `CSSselect.compile(query)`\n\nCompiles the query, returns a function.\n\n#### `CSSselect.is(elem, query, options)`\n\nTests whether or not an element is matched by `query`. `query` can be either a CSS selector or a function.\n\n#### `CSSselect.selectOne(query, elems, options)`\n\nArguments are the same as for `CSSselect(query, elems)`. Only returns the first match, or `null` if there was no match.\n\n### Options\n\n- `xmlMode`: When enabled, tag names will be case-sensitive. Default: `false`.\n- `strict`: Limits the module to only use CSS3 selectors. Default: `false`.\n- `rootFunc`: The last function in the stack, will be called with the last element that's looked at. Should return `true`.\n- `adapter`: The adapter to use when interacting with the backing DOM structure. By default it uses [`domutils`](https://github.com/fb55/domutils).\n\n#### Custom Adapters\n\nA custom adapter must implement the following functions:\n\n```\nisTag, existsOne, getAttributeValue, getChildren, getName, getParent,\ngetSiblings, getText, hasAttrib, removeSubsets, findAll, findOne\n```\n\nThe method signature notation used below should be fairly intuitive - if not,\nsee the [`rtype`](https://github.com/ericelliott/rtype) or\n[`TypeScript`](https://www.typescriptlang.org/) docs, as it is very similar to\nboth of those. You may also want to look at\n-[`domutils`](https://github.com/fb55/domutils) to see the default \n-implementation, or at \n-[`css-select-browser-adapter`](https://github.com/nrkn/css-select-browser-adapter/blob/master/index.js) \n-for an implementation backed by the DOM.\n\n```ts\n{\n  // is the node a tag?\n  isTag: ( node:Node ) => isTag:Boolean,\n\n  // does at least one of passed element nodes pass the test predicate?\n  existsOne: ( test:Predicate, elems:[ElementNode] ) => existsOne:Boolean,\n\n  // get the attribute value\n  getAttributeValue: ( elem:ElementNode, name:String ) => value:String,\n\n  // get the node's children\n  getChildren: ( node:Node ) => children:[Node],\n\n  // get the name of the tag\n  getName: ( elem:ElementNode ) => tagName:String,\n\n  // get the parent of the node\n  getParent: ( node:Node ) => parentNode:Node,\n\n  /*\n    get the siblings of the node. Note that unlike jQuery's `siblings` method,\n    this is expected to include the current node as well\n  */\n  getSiblings: ( node:Node ) => siblings:[Node],\n\n  // get the text content of the node, and its children if it has any\n  getText: ( node:Node ) => text:String,\n\n  // does the element have the named attribute?\n  hasAttrib: ( elem:ElementNode, name:String ) => hasAttrib:Boolean,\n\n  // takes an array of nodes, and removes any duplicates, as well as any nodes\n  // whose ancestors are also in the array\n  removeSubsets: ( nodes:[Node] ) => unique:[Node],\n\n  // finds all of the element nodes in the array that match the test predicate,\n  // as well as any of their children that match it\n  findAll: ( test:Predicate, nodes:[Node] ) => elems:[ElementNode],\n\n  // finds the first node in the array that matches the test predicate, or one\n  // of its children \n  findOne: ( test:Predicate, elems:[ElementNode] ) => findOne:ElementNode,\n\n  /*\n    The adapter can also optionally include an equals method, if your DOM\n    structure needs a custom equality test to compare two objects which refer\n    to the same underlying node. If not provided, `css-select` will fall back to\n    `a === b`.\n  */\n  equals: ( a:Node, b:Node ) => Boolean\n}\n```\n\n## Supported selectors\n\n_As defined by CSS 4 and / or jQuery._\n\n* Universal (`*`)\n* Tag (`<tagname>`)\n* Descendant (` `)\n* Child (`>`)\n* Parent (`<`) *\n* Sibling (`+`)\n* Adjacent (`~`)\n* Attribute (`[attr=foo]`), with supported comparisons:\n  * `[attr]` (existential)\n  * `=`\n  * `~=`\n  * `|=`\n  * `*=`\n  * `^=`\n  * `$=`\n  * `!=` *\n  * Also, `i` can be added after the comparison to make the comparison case-insensitive (eg. `[attr=foo i]`) *\n* Pseudos:\n  * `:not`\n  * `:contains` *\n  * `:icontains` * (case-insensitive version of `:contains`)\n  * `:has` *\n  * `:root`\n  * `:empty`\n  * `:parent` *\n  * `:[first|last]-child[-of-type]`\n  * `:only-of-type`, `:only-child`\n  * `:nth-[last-]child[-of-type]`\n  * `:link`\n  * `:visited`, `:hover`, `:active` * (these depend on optional Adapter methods, so these will work only if implemented in Adapter)\n  * `:selected` *, `:checked`\n  * `:enabled`, `:disabled`\n  * `:required`, `:optional`\n  * `:header`, `:button`, `:input`, `:text`, `:checkbox`, `:file`, `:password`, `:reset`, `:radio` etc. *\n  * `:matches` *\n\n__*__: Not part of CSS3\n\n---\n\nLicense: BSD-2-Clause\n\n## Security contact information\n\nTo report a security vulnerability, please use the [Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\n## `css-select` for enterprise\n\nAvailable as part of the Tidelift Subscription\n\nThe maintainers of `css-select` and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-css-select?utm_source=npm-css-select&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n", "licenseText": "Copyright (c) <PERSON>\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n\nRedistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.\n\nRedistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.\n\nTHIS IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERC<PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS,\nEVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/css-select/-/css-select-2.1.0.tgz#6a34653356635934a81baca68d0255432105dbef", "type": "tarball", "reference": "https://registry.yarnpkg.com/css-select/-/css-select-2.1.0.tgz", "hash": "6a34653356635934a81baca68d0255432105dbef", "integrity": "sha512-Dqk7LQKpwLoH3VovzZnkzegqNSuAziQyNZUcrdDM401iY+R5NkGBXGmtO05/yaXQziALuPogeG0b7UAgjnTJTQ==", "registry": "npm", "packageName": "css-select", "cacheIntegrity": "sha512-Dqk7LQKpwLoH3VovzZnkzegqNSuAziQyNZUcrdDM401iY+R5NkGBXGmtO05/yaXQziALuPogeG0b7UAgjnTJTQ== sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8="}, "registry": "npm", "hash": "6a34653356635934a81baca68d0255432105dbef"}