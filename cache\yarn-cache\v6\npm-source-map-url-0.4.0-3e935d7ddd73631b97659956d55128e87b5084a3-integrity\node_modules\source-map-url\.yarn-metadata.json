{"manifest": {"name": "source-map-url", "version": "0.4.0", "author": {"name": "<PERSON>"}, "license": "MIT", "description": "Tools for working with sourceMappingURL comments.", "keywords": ["source map", "sourceMappingURL", "comment", "annotation"], "main": "source-map-url.js", "repository": {"type": "git", "url": "https://github.com/lydell/source-map-url.git"}, "scripts": {"lint": "jshint source-map-url.js test/ ", "unit": "mocha", "test": "npm run lint && npm run unit"}, "devDependencies": {"mocha": "~1.17.1", "expect.js": "~0.3.1", "jshint": "~2.4.3"}, "testling": {"harness": "mocha", "files": "test/*.js", "browsers": ["ie/8..latest", "chrome/latest", "firefox/latest", "opera/12", "opera/latest", "safari/5", "iphone/6", "android-browser/4"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-source-map-url-0.4.0-3e935d7ddd73631b97659956d55128e87b5084a3-integrity\\node_modules\\source-map-url\\package.json", "readmeFilename": "readme.md", "readme": "Overview [![Build Status](https://travis-ci.org/lydell/source-map-url.png?branch=master)](https://travis-ci.org/lydell/source-map-url)\n========\n\n[![browser support](https://ci.testling.com/lydell/source-map-url.png)](https://ci.testling.com/lydell/source-map-url)\n\nTools for working with sourceMappingURL comments.\n\n```js\nvar sourceMappingURL = require(\"source-map-url\")\n\nvar code = [\n  \"!function(){...}();\",\n  \"/*# sourceMappingURL=foo.js.map */\"\n].join(\"\\n\")\n\nsourceMappingURL.existsIn(code)\n// true\n\nsourceMappingURL.getFrom(code)\n// foo.js.map\n\ncode = sourceMappingURL.insertBefore(code, \"// License: MIT\\n\")\n// !function(){...}();\n// // License: MIT\n// /*# sourceMappingURL=foo.js.map */\n\ncode = sourceMappingURL.removeFrom(code)\n// !function(){...}();\n// // License: MIT\n\nsourceMappingURL.existsIn(code)\n// false\n\nsourceMappingURL.getFrom(code)\n// null\n\ncode += \"//# sourceMappingURL=/other/file.js.map\"\n// !function(){...}();\n// // License: MIT\n// //# sourceMappingURL=/other/file.js.map\n```\n\n\nInstallation\n============\n\n- `npm install source-map-url`\n- `bower install source-map-url`\n- `component install lydell/source-map-url`\n\nWorks with CommonJS, AMD and browser globals, through UMD.\n\n\nUsage\n=====\n\n### `sourceMappingURL.getFrom(code)` ###\n\nReturns the url of the sourceMappingURL comment in `code`. Returns `null` if\nthere is no such comment.\n\n### `sourceMappingURL.existsIn(code)` ###\n\nReturns `true` if there is a sourceMappingURL comment in `code`, or `false`\notherwise.\n\n### `sourceMappingURL.removeFrom(code)` ###\n\nRemoves the sourceMappingURL comment in `code`. Does nothing if there is no\nsuch comment. Returns the updated `code`.\n\n### `sourceMappingURL.insertBefore(code, string)` ###\n\nInserts `string` before the sourceMappingURL comment in `code`. Appends\n`string` to `code` if there is no such comment.\n\nLets you append something to a file without worrying about burying the\nsourceMappingURL comment (by keeping it at the end of the file).\n\n### `sourceMappingURL.regex` ###\n\nThe regex that is used to match sourceMappingURL comments. It matches both `//`\nand `/**/` comments, thus supporting both JavaScript and CSS.\n\n\nTests\n=====\n\nStart by running `npm test`, which lints the code and runs the test suite in Node.js.\n\nTo run the tests in a browser, run `testling` (`npm install -g testling`) or `testling -u`.\n\n\nLicense\n=======\n\n[The X11 (“MIT”) License](LICENSE).\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/source-map-url/-/source-map-url-0.4.0.tgz#3e935d7ddd73631b97659956d55128e87b5084a3", "type": "tarball", "reference": "https://registry.yarnpkg.com/source-map-url/-/source-map-url-0.4.0.tgz", "hash": "3e935d7ddd73631b97659956d55128e87b5084a3", "integrity": "sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=", "registry": "npm", "packageName": "source-map-url", "cacheIntegrity": "sha512-liJwHPI9x9d9w5WSIjM58MqGmmb7XzNqwdUA3kSBQ4lmDngexlKwawGzK3J1mKXi6+sysoMDlpVyZh9sv5vRfw== sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM="}, "registry": "npm", "hash": "3e935d7ddd73631b97659956d55128e87b5084a3"}