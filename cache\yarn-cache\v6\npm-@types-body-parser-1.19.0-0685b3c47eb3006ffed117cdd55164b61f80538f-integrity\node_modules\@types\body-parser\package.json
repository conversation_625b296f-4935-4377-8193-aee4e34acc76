{"name": "@types/body-parser", "version": "1.19.0", "description": "TypeScript definitions for body-parser", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/santialbo", "githubUsername": "santial<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/vilic", "githubUsername": "vilic"}, {"name": "<PERSON>", "url": "https://github.com/dreampulse", "githubUsername": "dreampulse"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/blendsdk", "githubUsername": "blendsdk"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tlaziuk", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jwalton", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/body-parser"}, "scripts": {}, "dependencies": {"@types/connect": "*", "@types/node": "*"}, "typesPublisherContentHash": "4257cff3580f6064eb283c690c28aa3a5347cd3cae2a2e208b8f23c61705724a", "typeScriptVersion": "2.8"}