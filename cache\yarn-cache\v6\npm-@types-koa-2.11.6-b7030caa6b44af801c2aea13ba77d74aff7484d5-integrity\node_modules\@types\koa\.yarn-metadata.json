{"manifest": {"name": "@types/koa", "version": "2.11.6", "description": "TypeScript definitions for Koa", "license": "MIT", "contributors": [{"name": "DavidCai1993", "url": "https://github.com/DavidCai1993"}, {"name": "j<PERSON><PERSON>", "url": "https://github.com/jkeylu"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/brikou"}, {"name": "harry<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/harryparkdotio"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/chatoo2412"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/koa"}, "scripts": {}, "dependencies": {"@types/accepts": "*", "@types/content-disposition": "*", "@types/cookies": "*", "@types/http-assert": "*", "@types/http-errors": "*", "@types/keygrip": "*", "@types/koa-compose": "*", "@types/node": "*"}, "typesPublisherContentHash": "557190fbae4bd7927b9ac54a1ff3a370ea131d37defaacc6f8e5e03a4cd10e86", "typeScriptVersion": "3.2", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-koa-2.11.6-b7030caa6b44af801c2aea13ba77d74aff7484d5-integrity\\node_modules\\@types\\koa\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/koa`\n\n# Summary\nThis package contains type definitions for Koa (http://koajs.com).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/koa.\n\n### Additional Details\n * Last updated: Tu<PERSON>, 20 Oct 2020 14:53:22 GMT\n * Dependencies: [@types/accepts](https://npmjs.com/package/@types/accepts), [@types/cookies](https://npmjs.com/package/@types/cookies), [@types/http-assert](https://npmjs.com/package/@types/http-assert), [@types/http-errors](https://npmjs.com/package/@types/http-errors), [@types/keygrip](https://npmjs.com/package/@types/keygrip), [@types/koa-compose](https://npmjs.com/package/@types/koa-compose), [@types/content-disposition](https://npmjs.com/package/@types/content-disposition), [@types/node](https://npmjs.com/package/@types/node)\n * Global values: none\n\n# Credits\nThese definitions were written by [<PERSON><PERSON>ai1993](https://github.com/DavidCai1993), [j<PERSON>ey Lu](https://github.com/jkeylu), [Brice Bernard](https://github.com/brikou), [harryparkdotio](https://github.com/harryparkdotio), and [Wooram Jun](https://github.com/chatoo2412).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/koa/-/koa-2.11.6.tgz#b7030caa6b44af801c2aea13ba77d74aff7484d5", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/koa/-/koa-2.11.6.tgz", "hash": "b7030caa6b44af801c2aea13ba77d74aff7484d5", "integrity": "sha512-B<PERSON>rMj06eQkk04C97fovEDQMpLpd2IxCB4ecitaXwOKGq78Wi2tooaDOWOFGajPk8IkQOAtMppApgSVkYe1F/A==", "registry": "npm", "packageName": "@types/koa", "cacheIntegrity": "sha512-B<PERSON>rMj06eQkk04C97fovEDQMpLpd2IxCB4ecitaXwOKGq78Wi2tooaDOWOFGajPk8IkQOAtMppApgSVkYe1F/A== sha1-twMMqmtEr4AcKuoTunfXSv90hNU="}, "registry": "npm", "hash": "b7030caa6b44af801c2aea13ba77d74aff7484d5"}