[{"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[cfx-default]\\[system]\\[builders]\\webpack\\node_modules\\webpack\\buildin\\global.js", "stats": {"mtime": 1749054663461, "size": 472, "inode": 1688849860418594}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@citizenfx\\client\\index.d.ts", "stats": {"mtime": 1749054673865, "size": 3330, "inode": 1125899907005773}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@citizenfx\\client\\natives_universal.d.ts", "stats": {"mtime": 1749054673865, "size": 1576346, "inode": 1125899907005778}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@citizenfx\\server\\index.d.ts", "stats": {"mtime": 1749054673867, "size": 3327, "inode": 1125899907005789}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@citizenfx\\server\\natives_server.d.ts", "stats": {"mtime": 1749054673867, "size": 68153, "inode": 1125899907005794}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\async_hooks.d.ts", "stats": {"mtime": 1749054674460, "size": 9693, "inode": 1125899907005858}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\buffer.d.ts", "stats": {"mtime": 1749054674460, "size": 708, "inode": 1125899907005856}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\child_process.d.ts", "stats": {"mtime": 1749054674460, "size": 25084, "inode": 1125899907005857}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\cluster.d.ts", "stats": {"mtime": 1749054674460, "size": 16125, "inode": 1125899907005860}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\console.d.ts", "stats": {"mtime": 1749054674460, "size": 5923, "inode": 1125899907005859}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\constants.d.ts", "stats": {"mtime": 1749054674460, "size": 459, "inode": 1125899907005861}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\crypto.d.ts", "stats": {"mtime": 1749054674460, "size": 46334, "inode": 1125899907005863}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\dgram.d.ts", "stats": {"mtime": 1749054674460, "size": 7195, "inode": 1125899907005862}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\dns.d.ts", "stats": {"mtime": 1749054674460, "size": 16315, "inode": 1125899907005864}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\domain.d.ts", "stats": {"mtime": 1749054674460, "size": 715, "inode": 1125899907005865}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\events.d.ts", "stats": {"mtime": 1749054674460, "size": 3691, "inode": 1125899907005866}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\fs.d.ts", "stats": {"mtime": 1749054674460, "size": 117840, "inode": 1125899907005867}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\fs\\promises.d.ts", "stats": {"mtime": 1749054674460, "size": 30101, "inode": 1125899907008832}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\globals.d.ts", "stats": {"mtime": 1749054674460, "size": 23701, "inode": 1125899907005869}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\http.d.ts", "stats": {"mtime": 1749054674460, "size": 18871, "inode": 1125899907005871}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\http2.d.ts", "stats": {"mtime": 1749054674460, "size": 56595, "inode": 1125899907005873}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\https.d.ts", "stats": {"mtime": 1749054674460, "size": 1684, "inode": 1125899907005870}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\inspector.d.ts", "stats": {"mtime": 1749054674460, "size": 121933, "inode": 1125899907005875}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\module.d.ts", "stats": {"mtime": 1749054674460, "size": 1643, "inode": 1125899907005876}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\net.d.ts", "stats": {"mtime": 1749054674460, "size": 12897, "inode": 1125899907005878}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\os.d.ts", "stats": {"mtime": 1749054674460, "size": 8178, "inode": 1125899907005877}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\path.d.ts", "stats": {"mtime": 1749054674460, "size": 6309, "inode": 1125899907005881}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\perf_hooks.d.ts", "stats": {"mtime": 1749054674460, "size": 10616, "inode": 1125899907005880}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\process.d.ts", "stats": {"mtime": 1749054674460, "size": 20332, "inode": 1125899907005884}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\punycode.d.ts", "stats": {"mtime": 1749054674460, "size": 3159, "inode": 1125899907005882}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\querystring.d.ts", "stats": {"mtime": 1749054674460, "size": 1046, "inode": 1125899907005883}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\readline.d.ts", "stats": {"mtime": 1749054674460, "size": 7330, "inode": 1125899907005886}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\repl.d.ts", "stats": {"mtime": 1749054674460, "size": 18118, "inode": 1125899907005887}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\stream.d.ts", "stats": {"mtime": 1749054674460, "size": 19374, "inode": 1125899907005889}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\string_decoder.d.ts", "stats": {"mtime": 1749054674460, "size": 193, "inode": 1125899907005888}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\timers.d.ts", "stats": {"mtime": 1749054674460, "size": 826, "inode": 1125899907005890}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\tls.d.ts", "stats": {"mtime": 1749054674460, "size": 37760, "inode": 1125899907005891}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\trace_events.d.ts", "stats": {"mtime": 1749054674460, "size": 2113, "inode": 1125899907005892}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\ts3.4\\assert.d.ts", "stats": {"mtime": 1749054674460, "size": 4648, "inode": 1125899907008846}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\ts3.4\\base.d.ts", "stats": {"mtime": 1749054674460, "size": 2420, "inode": 1125899907008849}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\ts3.4\\globals.global.d.ts", "stats": {"mtime": 1749054674460, "size": 35, "inode": 1125899907008850}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\ts3.4\\index.d.ts", "stats": {"mtime": 1749054674460, "size": 429, "inode": 1125899907008851}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\tty.d.ts", "stats": {"mtime": 1749054674460, "size": 2442, "inode": 1125899907005893}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\url.d.ts", "stats": {"mtime": 1749054674460, "size": 4209, "inode": 1125899907005894}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\util.d.ts", "stats": {"mtime": 1749054674460, "size": 12753, "inode": 1125899907005898}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\v8.d.ts", "stats": {"mtime": 1749054674460, "size": 6769, "inode": 1125899907005896}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\vm.d.ts", "stats": {"mtime": 1749054674460, "size": 5900, "inode": 1125899907005900}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\worker_threads.d.ts", "stats": {"mtime": 1749054674460, "size": 11446, "inode": 1125899907005904}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\zlib.d.ts", "stats": {"mtime": 1749054674460, "size": 14327, "inode": 1125899907005901}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.collection.d.ts", "stats": {"mtime": 1749054681540, "size": 2845, "inode": 844424930300143}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.core.d.ts", "stats": {"mtime": 1749054681540, "size": 19608, "inode": 844424930300142}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.d.ts", "stats": {"mtime": 1749054681540, "size": 1250, "inode": 844424930300145}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.generator.d.ts", "stats": {"mtime": 1749054681540, "size": 2129, "inode": 844424930300144}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.iterable.d.ts", "stats": {"mtime": 1749054681540, "size": 14536, "inode": 844424930300147}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.promise.d.ts", "stats": {"mtime": 1749054681540, "size": 10652, "inode": 844424930300146}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.proxy.d.ts", "stats": {"mtime": 1749054681540, "size": 1961, "inode": 844424930300148}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.reflect.d.ts", "stats": {"mtime": 1749054681540, "size": 1913, "inode": 844424930300149}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.symbol.d.ts", "stats": {"mtime": 1749054681540, "size": 1657, "inode": 844424930300151}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.symbol.wellknown.d.ts", "stats": {"mtime": 1749054681540, "size": 10257, "inode": 844424930300150}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2016.array.include.d.ts", "stats": {"mtime": 1749054681540, "size": 4870, "inode": 844424930300153}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2016.d.ts", "stats": {"mtime": 1749054681540, "size": 937, "inode": 844424930300152}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.d.ts", "stats": {"mtime": 1749054681540, "size": 1092, "inode": 844424930300157}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.intl.d.ts", "stats": {"mtime": 1749054681540, "size": 1253, "inode": 844424930300158}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.object.d.ts", "stats": {"mtime": 1749054681540, "size": 2461, "inode": 844424930300156}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.sharedmemory.d.ts", "stats": {"mtime": 1749054681540, "size": 6148, "inode": 844424930300160}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.string.d.ts", "stats": {"mtime": 1749054681540, "size": 2387, "inode": 844424930300161}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.typedarrays.d.ts", "stats": {"mtime": 1749054681540, "size": 1434, "inode": 844424930300159}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2018.d.ts", "stats": {"mtime": 1749054681540, "size": 1006, "inode": 844424930300162}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2018.intl.d.ts", "stats": {"mtime": 1749054681540, "size": 1821, "inode": 844424930300165}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2018.promise.d.ts", "stats": {"mtime": 1749054681540, "size": 1361, "inode": 844424930300164}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2018.regexp.d.ts", "stats": {"mtime": 1749054681540, "size": 1236, "inode": 844424930300166}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es5.d.ts", "stats": {"mtime": 1749054681540, "size": 200360, "inode": 844424930300167}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.esnext.asynciterable.d.ts", "stats": {"mtime": 1749054681540, "size": 1544, "inode": 844424930300170}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.esnext.bigint.d.ts", "stats": {"mtime": 1749054681540, "size": 30733, "inode": 844424930300171}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.esnext.intl.d.ts", "stats": {"mtime": 1749054681540, "size": 1255, "inode": 844424930300174}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\src\\client\\client.ts", "stats": {"mtime": 1749053766311.6401, "size": 2093, "inode": 1970324837117913}}]