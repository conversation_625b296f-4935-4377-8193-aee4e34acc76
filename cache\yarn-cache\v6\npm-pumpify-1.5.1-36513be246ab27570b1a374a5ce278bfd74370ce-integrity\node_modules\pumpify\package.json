{"name": "pumpify", "version": "1.5.1", "description": "Combine an array of streams into a single duplex stream using pump and duplexify", "main": "index.js", "dependencies": {"duplexify": "^3.6.0", "inherits": "^2.0.3", "pump": "^2.0.0"}, "devDependencies": {"tape": "^4.8.0", "through2": "^2.0.3"}, "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "git://github.com/mafintosh/pumpify"}, "keywords": ["pump", "duplexify", "duplex", "streams", "stream", "pipeline", "combine"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/pumpify/issues"}, "homepage": "https://github.com/mafintosh/pumpify"}