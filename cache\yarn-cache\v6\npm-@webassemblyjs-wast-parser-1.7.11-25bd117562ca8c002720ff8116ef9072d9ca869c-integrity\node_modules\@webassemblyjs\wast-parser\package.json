{"name": "@webassemblyjs/wast-parser", "version": "1.7.11", "description": "WebAssembly text format parser", "keywords": ["webassembly", "javascript", "ast", "parser", "wat", "wast"], "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "mocha"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/floating-point-hex-parser": "1.7.11", "@webassemblyjs/helper-api-error": "1.7.11", "@webassemblyjs/helper-code-frame": "1.7.11", "@webassemblyjs/helper-fsm": "1.7.11", "@xtuc/long": "4.2.1"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.11", "mamacro": "^0.0.3"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909"}