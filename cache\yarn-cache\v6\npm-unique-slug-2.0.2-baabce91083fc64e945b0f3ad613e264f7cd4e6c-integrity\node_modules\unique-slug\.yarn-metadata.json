{"manifest": {"name": "unique-slug", "version": "2.0.2", "description": "Generate a unique character string suitible for use in files and URLs.", "main": "index.js", "scripts": {"test": "standard && tap --coverage test"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "devDependencies": {"standard": "^12.0.1", "tap": "^12.7.0"}, "repository": {"type": "git", "url": "git://github.com/iarna/unique-slug.git"}, "dependencies": {"imurmurhash": "^0.1.4"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-unique-slug-2.0.2-baabce91083fc64e945b0f3ad613e264f7cd4e6c-integrity\\node_modules\\unique-slug\\package.json", "readmeFilename": "README.md", "readme": "unique-slug\n===========\n\nGenerate a unique character string suitible for use in files and URLs.\n\n```\nvar uniqueSlug = require('unique-slug')\n\nvar randomSlug = uniqueSlug()\nvar fileSlug = uniqueSlug('/etc/passwd')\n```\n\n### uniqueSlug(*str*) → String (8 chars)\n\nIf *str* is passed in then the return value will be its murmur hash in\nhex.\n\nIf *str* is not passed in, it will be 4 randomly generated bytes\nconverted into 8 hexadecimal characters.\n", "licenseText": "The ISC License\n\nCopyright npm, Inc\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND <PERSON>ITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, <PERSON>ATA OR PROFITS, WHETHER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/unique-slug/-/unique-slug-2.0.2.tgz#baabce91083fc64e945b0f3ad613e264f7cd4e6c", "type": "tarball", "reference": "https://registry.yarnpkg.com/unique-slug/-/unique-slug-2.0.2.tgz", "hash": "baabce91083fc64e945b0f3ad613e264f7cd4e6c", "integrity": "sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w==", "registry": "npm", "packageName": "unique-slug", "cacheIntegrity": "sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w== sha1-uqvOkQg/xk6UWw861hPiZPfNTmw="}, "registry": "npm", "hash": "baabce91083fc64e945b0f3ad613e264f7cd4e6c"}