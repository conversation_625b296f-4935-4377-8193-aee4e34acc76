{"name": "@webassemblyjs/wast-printer", "version": "1.9.0", "description": "WebAssembly text format printer", "main": "lib/index.js", "module": "esm/index.js", "keywords": ["webassembly", "javascript", "ast", "compiler", "printer", "wast"], "scripts": {"test": "mocha"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/wast-parser": "1.9.0", "@xtuc/long": "4.2.2"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.9.0"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "gitHead": "0440b420888c1f7701eb9762ec657775506b87d8"}