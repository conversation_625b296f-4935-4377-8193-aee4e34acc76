{"manifest": {"name": "get-intrinsic", "version": "1.0.2", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"]}, "scripts": {"lint": "eslint --ext=.js,.mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/*'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/get-intrinsic.git"}, "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "homepage": "https://github.com/ljharb/get-intrinsic#readme", "devDependencies": {"@ljharb/eslint-config": "^17.3.0", "aud": "^1.1.3", "auto-changelog": "^2.2.1", "es-abstract": "^1.18.0-next.1", "es-value-fixtures": "^1.0.0", "eslint": "^7.15.0", "foreach": "^2.0.5", "has-bigints": "^1.0.1", "make-async-function": "^1.0.0", "make-async-generator-function": "^1.0.0", "make-generator-function": "^2.0.0", "nyc": "^10.3.2", "object-inspect": "^1.9.0", "tape": "^5.0.1"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-get-intrinsic-1.0.2-6820da226e50b24894e08859469dc68361545d49-integrity\\node_modules\\get-intrinsic\\package.json", "readmeFilename": "README.md", "readme": "# get-intrinsic\nGet and robustly cache all JS language-level intrinsics at first require time.\n", "licenseText": "MIT License\n\nCopyright (c) 2020 Jordan Harband\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/get-intrinsic/-/get-intrinsic-1.0.2.tgz#6820da226e50b24894e08859469dc68361545d49", "type": "tarball", "reference": "https://registry.yarnpkg.com/get-intrinsic/-/get-intrinsic-1.0.2.tgz", "hash": "6820da226e50b24894e08859469dc68361545d49", "integrity": "sha512-aeX0vrFm21ILl3+JpFFRNe9aUvp6VFZb2/CTbgLb8j75kOhvoNYjt9d8KA/tJG4gSo8nzEDedRl0h7vDmBYRVg==", "registry": "npm", "packageName": "get-intrinsic", "cacheIntegrity": "sha512-aeX0vrFm21ILl3+JpFFRNe9aUvp6VFZb2/CTbgLb8j75kOhvoNYjt9d8KA/tJG4gSo8nzEDedRl0h7vDmBYRVg== sha1-aCDaIm5QskiU4IhZRp3Gg2FUXUk="}, "registry": "npm", "hash": "6820da226e50b24894e08859469dc68361545d49"}