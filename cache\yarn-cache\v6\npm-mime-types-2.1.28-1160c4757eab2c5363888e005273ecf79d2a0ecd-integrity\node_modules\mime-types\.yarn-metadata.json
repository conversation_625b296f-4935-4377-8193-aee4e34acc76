{"manifest": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.28", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "1.45.0"}, "devDependencies": {"eslint": "7.17.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.22.1", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.1.0", "mocha": "8.2.1", "nyc": "15.1.0"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-mime-types-2.1.28-1160c4757eab2c5363888e005273ecf79d2a0ecd-integrity\\node_modules\\mime-types\\package.json", "readmeFilename": "README.md", "readme": "# mime-types\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][ci-image]][ci-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nThe ultimate javascript content-type utility.\n\nSimilar to [the `mime@1.x` module](https://www.npmjs.com/package/mime), except:\n\n- __No fallbacks.__ Instead of naively returning the first available type,\n  `mime-types` simply returns `false`, so do\n  `var type = mime.lookup('unrecognized') || 'application/octet-stream'`.\n- No `new Mime()` business, so you could do `var lookup = require('mime-types').lookup`.\n- No `.define()` functionality\n- Bug fixes for `.lookup(path)`\n\nOtherwise, the API is compatible with `mime` 1.x.\n\n## Install\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install mime-types\n```\n\n## Adding Types\n\nAll mime types are based on [mime-db](https://www.npmjs.com/package/mime-db),\nso open a PR there if you'd like to add mime types.\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar mime = require('mime-types')\n```\n\nAll functions return `false` if input is invalid or not found.\n\n### mime.lookup(path)\n\nLookup the content-type associated with a file.\n\n<!-- eslint-disable no-undef -->\n\n```js\nmime.lookup('json') // 'application/json'\nmime.lookup('.md') // 'text/markdown'\nmime.lookup('file.html') // 'text/html'\nmime.lookup('folder/file.js') // 'application/javascript'\nmime.lookup('folder/.htaccess') // false\n\nmime.lookup('cats') // false\n```\n\n### mime.contentType(type)\n\nCreate a full content-type header given a content-type or extension.\nWhen given an extension, `mime.lookup` is used to get the matching\ncontent-type, otherwise the given content-type is used. Then if the\ncontent-type does not already have a `charset` parameter, `mime.charset`\nis used to get the default charset and add to the returned content-type.\n\n<!-- eslint-disable no-undef -->\n\n```js\nmime.contentType('markdown') // 'text/x-markdown; charset=utf-8'\nmime.contentType('file.json') // 'application/json; charset=utf-8'\nmime.contentType('text/html') // 'text/html; charset=utf-8'\nmime.contentType('text/html; charset=iso-8859-1') // 'text/html; charset=iso-8859-1'\n\n// from a full path\nmime.contentType(path.extname('/path/to/file.json')) // 'application/json; charset=utf-8'\n```\n\n### mime.extension(type)\n\nGet the default extension for a content-type.\n\n<!-- eslint-disable no-undef -->\n\n```js\nmime.extension('application/octet-stream') // 'bin'\n```\n\n### mime.charset(type)\n\nLookup the implied default charset of a content-type.\n\n<!-- eslint-disable no-undef -->\n\n```js\nmime.charset('text/markdown') // 'UTF-8'\n```\n\n### var type = mime.types[extension]\n\nA map of content-types by extension.\n\n### [extensions...] = mime.extensions[type]\n\nA map of extensions by content-type.\n\n## License\n\n[MIT](LICENSE)\n\n[ci-image]: https://badgen.net/github/checks/jshttp/mime-types/master?label=ci\n[ci-url]: https://github.com/jshttp/mime-types/actions?query=workflow%3Aci\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/mime-types/master\n[coveralls-url]: https://coveralls.io/r/jshttp/mime-types?branch=master\n[node-version-image]: https://badgen.net/npm/node/mime-types\n[node-version-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/mime-types\n[npm-url]: https://npmjs.org/package/mime-types\n[npm-version-image]: https://badgen.net/npm/v/mime-types\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2014 <PERSON> <<EMAIL>>\nCopyright (c) 2015 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR CO<PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/mime-types/-/mime-types-2.1.28.tgz#1160c4757eab2c5363888e005273ecf79d2a0ecd", "type": "tarball", "reference": "https://registry.yarnpkg.com/mime-types/-/mime-types-2.1.28.tgz", "hash": "1160c4757eab2c5363888e005273ecf79d2a0ecd", "integrity": "sha512-0TO2yJ5YHYr7M2zzT7gDU1tbwHxEUWBCLt0lscSNpcdAfFyJOVEpRYNS7EXVcTLNj/25QO8gulHC5JtTzSE2UQ==", "registry": "npm", "packageName": "mime-types", "cacheIntegrity": "sha512-0TO2yJ5YHYr7M2zzT7gDU1tbwHxEUWBCLt0lscSNpcdAfFyJOVEpRYNS7EXVcTLNj/25QO8gulHC5JtTzSE2UQ== sha1-EWDEdX6rLFNjiI4AUnPs950qDs0="}, "registry": "npm", "hash": "1160c4757eab2c5363888e005273ecf79d2a0ecd"}