{"manifest": {"name": "is-date-object", "version": "1.0.2", "author": {"name": "<PERSON>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "Is this value a JS Date object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "node --harmony --es-staging test", "posttest": "npx aud", "coverage": "covert test/index.js", "lint": "eslint .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/ljharb/is-date-object.git"}, "keywords": ["Date", "ES6", "toStringTag", "@@toStringTag", "Date object"], "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^15.0.2", "auto-changelog": "^1.16.2", "covert": "^1.1.1", "eslint": "^6.7.2", "foreach": "^2.0.5", "indexof": "^0.0.1", "is": "^3.3.0", "safe-publish-latest": "^1.1.4", "tape": "^4.12.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-date-object-1.0.2-bda736f2cd8fd06d32844e7743bfa7494c3bfd7e-integrity\\node_modules\\is-date-object\\package.json", "readmeFilename": "README.md", "readme": "# is-date-object <sup>[![Version Badge][2]][1]</sup>\n\n[![Build Status][3]][4]\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\n[![browser support][9]][10]\n\nIs this value a JS Date object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.\n\n## Example\n\n```js\nvar isDate = require('is-date-object');\nvar assert = require('assert');\n\nassert.notOk(isDate(undefined));\nassert.notOk(isDate(null));\nassert.notOk(isDate(false));\nassert.notOk(isDate(true));\nassert.notOk(isDate(42));\nassert.notOk(isDate('foo'));\nassert.notOk(isDate(function () {}));\nassert.notOk(isDate([]));\nassert.notOk(isDate({}));\nassert.notOk(isDate(/a/g));\nassert.notOk(isDate(new RegExp('a', 'g')));\n\nassert.ok(isDate(new Date()));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[1]: https://npmjs.org/package/is-date-object\n[2]: http://versionbadg.es/ljharb/is-date-object.svg\n[3]: https://travis-ci.org/ljharb/is-date-object.svg\n[4]: https://travis-ci.org/ljharb/is-date-object\n[5]: https://david-dm.org/ljharb/is-date-object.svg\n[6]: https://david-dm.org/ljharb/is-date-object\n[7]: https://david-dm.org/ljharb/is-date-object/dev-status.svg\n[8]: https://david-dm.org/ljharb/is-date-object#info=devDependencies\n[9]: https://ci.testling.com/ljharb/is-date-object.png\n[10]: https://ci.testling.com/ljharb/is-date-object\n[11]: https://nodei.co/npm/is-date-object.png?downloads=true&stars=true\n[license-image]: http://img.shields.io/npm/l/is-date-object.svg\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/is-date-object.svg\n[downloads-url]: http://npm-stat.com/charts.html?package=is-date-object\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015 Jordan Harband\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-date-object/-/is-date-object-1.0.2.tgz#bda736f2cd8fd06d32844e7743bfa7494c3bfd7e", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-date-object/-/is-date-object-1.0.2.tgz", "hash": "bda736f2cd8fd06d32844e7743bfa7494c3bfd7e", "integrity": "sha512-USlDT524woQ08aoZFzh3/Z6ch9Y/EWXEHQ/AaRN0SkKq4t2Jw2R2339tSXmwuVoY7LLlBCbOIlx2myP/L5zk0g==", "registry": "npm", "packageName": "is-date-object", "cacheIntegrity": "sha512-USlDT524woQ08aoZFzh3/Z6ch9Y/EWXEHQ/AaRN0SkKq4t2Jw2R2339tSXmwuVoY7LLlBCbOIlx2myP/L5zk0g== sha1-vac28s2P0G0yhE53Q7+nSUw7/X4="}, "registry": "npm", "hash": "bda736f2cd8fd06d32844e7743bfa7494c3bfd7e"}