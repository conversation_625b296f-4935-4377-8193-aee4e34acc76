Core = nil
CoreName = nil
CoreReady = false
Tables = {}
Items = {}
QBShared = QBShared or {}
QBShared.Items = {}
ESXOxItems = {}


Citizen.CreateThread(function()
    for k, v in pairs(Cores) do
        if GetResourceState(v.ResourceName) == "starting" or GetResourceState(v.ResourceName) == "started" then
            CoreName = v.ResourceName
            Core = v.GetFramework()

            -- Wait for Core to be fully loaded
            while not Core do
                Citizen.Wait(100)
                Core = v.GetFramework()
            end

            CoreReady = true
            print("Inventory Core: " .. CoreName)

            if CoreName == "qb-core" or CoreName == "qbx_core" and Config.OldInventory=="ox" then
                for _, item in pairs(QBShared.Items) do
                        table.insert(Core.Shared.Items, item)
                end
            elseif CoreName == "es_extended" and Config.OldInventory=="ox" then
                for _, item in pairs(QBShared.Items) do
                    table.insert(ESXOxItems, item)
            end
            end
            break -- Exit loop once core is found
        end
    end
end)


function GetPlayer(source)
    if not CoreReady or not Core then
        print("GetPlayer: Core not ready")
        return nil
    end

    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(source)
        if not player then
            print("GetPlayer: Player not found for source " .. tostring(source))
            return nil
        end
        return player
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        if not player then
            print("GetPlayer: Player not found for source " .. tostring(source))
            return nil
        end
        return player
    end
    return nil
end

function GetPlayerData(source)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(source)
        return player.PlayerData
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        return player
    end
end

function GetMoney(source, type)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(source)
        return player.PlayerData.money[type]
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        local account = player.getAccount(type).money
        return account
    end
end

function GetName(source)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(source)
        if not player then
            print("GetName: Player not found for source " .. tostring(source))
            return "Unknown Player"
        end
        return player.PlayerData.charinfo.firstname .. " " .. player.PlayerData.charinfo.lastname
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        if not player then
            print("GetName: Player not found for source " .. tostring(source))
            return "Unknown Player"
        end
        return player.getName()
    end
end

-- function GetPlayerItems(source)
--     if CoreName == "qb-core" or CoreName == "qbx_core" then
--         local player = Core.Functions.GetPlayer(source)
--         return player.PlayerData.items
--     elseif CoreName == "es_extended" then
--         local player = Core.GetPlayerFromId(source)
--         return player.getInventory()
--     end
-- end

function GetIdentifier(source)
    if not CoreReady or not Core then
        print("GetIdentifier: Core not ready")
        return nil
    end

    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(source)
        if not player then
            print("GetIdentifier: Player not found for source " .. tostring(source))
            return nil
        end
        return player.PlayerData.citizenid
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        if not player then
            print("GetIdentifier: Player not found for source " .. tostring(source))
            return nil
        end
        return player.identifier
    end
    return nil
end

function GetPlayerName(source)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(source)
        if not player then
            print("GetPlayerName: Player not found for source " .. tostring(source))
            return "Unknown Player"
        end
        return player.PlayerData.charinfo.firstname .. " " .. player.PlayerData.charinfo.lastname
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        if not player then
            print("GetPlayerName: Player not found for source " .. tostring(source))
            return "Unknown Player"
        end
        return player.getName()
    end
end


function GetPlayerCid(source)
    if CoreName == "qb-core" then
        local player = Core.Functions.GetPlayer(source)
        return player.PlayerData.citizenid
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        return player.getIdentifier()
    end
end


function Notify(source, text, length, type)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        Core.Functions.Notify(source, text, length, type)
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        player.showNotification(text)
    end
end

function GetPlayerMoney(src, type)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(src)
        return player.PlayerData.money[type]
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(src)
        local acType = "bank"
        if type == "cash" then
            acType = "money"
        end
        local account = player.getAccount(acType).money
        return account
    end
end

function GetAllPlayers()
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        return Core.Functions.GetPlayers()
    elseif CoreName == "es_extended" then
        return Core.GetPlayers()
    end

end

function RemoveMoney(src, type, amount, description)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(src)
        player.Functions.RemoveMoney(type, amount, description)
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(src)
        if type == "bank" then
            player.removeAccountMoney("bank", amount, description)
        elseif type == "cash" then
            player.removeMoney(amount, description)
        end
    end
end

function AddMoney(src, type, amount, description)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(src)
        player.Functions.AddMoney(type, amount, description)
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(src)
        if type == "bank" then
            player.addAccountMoney("bank", amount, description)
        elseif type == "cash" then
            player.addMoney(amount, description)
        end
    end
end

function AddItem(source, name, amount, metadata)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(source)
        player.Functions.AddItem(name, amount, false, metadata)
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        local hasQs = GetResourceState('qs-inventory') == 'started'
        local hasEsx = GetResourceState('esx_inventoryhud') == 'started'
        local hasOx = GetResourceState('ox_inventory') == 'started'

        if hasQs then
            return exports['qs-inventory']:AddItem(source, name, amount, false, metadata)
        elseif hasEsx then
            return player.addInventoryItem(name, amount)
        elseif hasOx then
            return exports["ox_inventory"]:AddItem(source, name, amount, metadata)
        else
            --CUSTOM INVENTORY ADD ITEM FUNCTION HERE
        end

    end
end

function RemoveItem(source, name, amount, metadata)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(source)
        player.Functions.RemoveItem(name, amount, metadata)
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        local hasQs = GetResourceState('qs-inventory') == 'started'
        local hasEsx = GetResourceState('esx_inventoryhud') == 'started'
        local hasOx = GetResourceState('ox_inventory') == 'started'

        if hasQs then
            return exports['qs-inventory']:RemoveItem(source, name, amount, metadata)
        elseif hasEsx then
            return player.removeInventoryItem(name, amount)
        elseif hasOx then
            return exports["ox_inventory"]:RemoveItem(source, name, amount, metadata)
        else
            --CUSTOM INVENTORY REMOVE ITEM FUNCTION HERE
        end
    end
end

function GetItem(source, name)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(source)
        return player.Functions.GetItemByName(name)
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        local hasQs = GetResourceState('qs-inventory') == 'started'
        local hasEsx = GetResourceState('esx_inventoryhud') == 'started'
        local hasOx = GetResourceState('ox_inventory') == 'started'

        if hasQs then
            return exports['qs-inventory']:GetItem(source, name)
        elseif hasEsx then
            return player.getInventoryItem(name)
        elseif hasOx then
            return exports["ox_inventory"]:GetItem(source, name,nil, false)
        else
            --CUSTOM INVENTORY GET ITEM FUNCTION HERE
        end
    end
end

function RandomStr(length)
    local StringCharset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    if length <= 0 then return '' end
    return RandomStr(length - 1) .. string.sub(StringCharset, math.random(1, #StringCharset), math.random(1, #StringCharset))
end

function RandomInt(length)
    local NumberCharset = "0123456789"
    if length <= 0 then return '' end
    return RandomInt(length - 1) .. string.sub(NumberCharset, math.random(1, #NumberCharset), math.random(1, #NumberCharset))
end

function SharedItems(item)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        if not Core or not Core.Shared or not Core.Shared.Items then
            print("SharedItems: Core.Shared.Items not available")
            return nil
        end

        for k, v in pairs(Core.Shared.Items) do
            if item == nil then
                return Core.Shared.Items
            end

            if v.name == item then
                return v
            end
        end

    elseif CoreName == "es_extended" and Config.OldInventory == "ox" then

        for k  , v in pairs(ESXOxItems) do

            if item == nil then
                return ESXOxItems
            end
    
            if v.name == item then 
                return v
            end
        end
        
    end
end


function GetItemMetadata(source, name)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(source)
        return player.Functions.GetItemByName(name).info
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        local hasQs = GetResourceState('qs-inventory') == 'started'
        local hasEsx = GetResourceState('esx_inventoryhud') == 'started'
        local hasOx = GetResourceState('ox_inventory') == 'started'

        if hasQs then
            return exports['qs-inventory']:GetItem(source, name).metadata
        elseif hasEsx then
            return player.getInventoryItem(name).metadata
        elseif hasOx then
            return exports["ox_inventory"]:GetItem(source, name).metadata
        else
            --CUSTOM INVENTORY GET ITEM METADATA FUNCTION HERE
        end
    end
end

function GetItemCount (source,item)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local hasOx = GetResourceState('ox_inventory') == 'started'
        local player = Core.Functions.GetPlayer(source)

        if hasOx then
            return exports["ox_inventory"]:GetItem(source, item).amount
        else 
            return player.Functions.GetItemByName(item).amount
        end

    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        local hasQs = GetResourceState('qs-inventory') == 'started'
        local hasEsx = GetResourceState('esx_inventoryhud') == 'started'
        local hasOx = GetResourceState('ox_inventory') == 'started'
        
        if hasQs then
            return exports['qs-inventory']:GetItem(source, item).amount
        elseif hasOx or hasEsx then
            return player.getInventoryItem(item).count
        -- elseif hasOx then
        --     return exports["ox_inventory"]:GetItem(source, item).amount
        else
            --CUSTOM INVENTORY GET ITEM COUNT FUNCTION HERE
        end
    end
end


function GetItems(source, name)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(source)
        
        return player.Functions.GetItemsByName(name)
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        local hasQs = GetResourceState('qs-inventory') == 'started'
        local hasEsx = GetResourceState('esx_inventoryhud') == 'started'
        local hasOx = GetResourceState('ox_inventory') == 'started'

        if hasQs then
            return exports['qs-inventory']:GetItemsByName(source, name)
        elseif hasOx or hasEsx then
            return player.getInventoryItem(name)
        -- elseif hasOx then
        --     return exports["ox_inventory"]:GetItem(source,name, nil, false)
        else
            --CUSTOM INVENTORY GET ITEMS BY NAME FUNCTION HERE
        end
    end
end

-- function GetItemBySlot(source,slot)
--     if CoreName == "qb-core" or CoreName == "qbx_core" and Config.OldInventory ~= "ox" then
--         local player = Core.Functions.GetPlayer(source)
--         return player.PlayerData.items[source, slot]        
--     elseif Config.OldInventory == "ox"  then
        
--     end 
-- end

function GetPlayerJob(source) 
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(source)
        return player.PlayerData.job
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        return player.getJob()
    end
end

function GetPlayerMetadata(source, key, value)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(source)
        return player.PlayerData.metadata[key]
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        return player.getMeta(key)
    end
end

function SetPlayerMetadata(source, key, value)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(source)
        player.Functions.SetMetaData(key, value)
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        player.setMeta(key, value)
    end
end

function GetInventory(source)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(source)
        if not player then
            print("GetInventory: Player not found for source " .. tostring(source))
            return {}
        end
        return player.PlayerData.items or {}
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        if not player then
            print("GetInventory: Player not found for source " .. tostring(source))
            return {}
        end

        local hasQs = GetResourceState('qs-inventory') == 'started'
        local hasEsx = GetResourceState('esx_inventoryhud') == 'started'
        local hasOx = GetResourceState('ox_inventory') == 'started'

        if hasQs then
            return exports['qs-inventory']:GetPlayerInventory(source)
        elseif hasEsx then
            return player.getInventory()
        elseif hasOx then
            return exports["ox_inventory"]:GetPlayerInventory(source)
        else
            return player.getInventory()
            --CUSTOM INVENTORY GET INVENTORY FUNCTION HERE
        end
    end
    return {}
end

function ItemCheckForCraft(player,reqItem)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local items = player.PlayerData.items
        local tbl = {}

        for x, y in pairs(items) do
            for k, v in pairs(reqItem) do
                if y.name == v.requredItemName then
                    table.insert(tbl, {name = v.requredItemName, count = y.amount})
                end
            end
        end
        return tbl

    elseif CoreName == "es_extended" then
        local items = player.getInventory()
        local tbl = {}

        for x, y in pairs(items) do
            for k, v in pairs(reqItem) do
                if y.name == v.requredItemName then
                    table.insert(tbl, {name = v.requredItemName, count = y.count})
                end
            end
        end
        return tbl

    end
end


function ItemCountControl(source, name, amount)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(source)
        return player.Functions.GetItemByName(name).amount >= amount
    elseif CoreName == "es_extended" then
        local player = Core.GetPlayerFromId(source)
        local hasQs = GetResourceState('qs-inventory') == 'started'
        local hasEsx = GetResourceState('esx_inventoryhud') == 'started'
        local hasOx = GetResourceState('ox_inventory') == 'started'

        if hasQs then
            return exports['qs-inventory']:GetItem(source, name).amount >= amount
        elseif hasEsx then
            return player.getInventoryItem(name).count >= amount
        elseif hasOx then
            return exports["ox_inventory"]:GetItem(source, name).amount >= amount
        else
            --CUSTOM INVENTORY ITEM COUNT CONTROL FUNCTION HERE
        end
    end
end

function SplitStr(str, delimiter)
    local result = {}
    local from = 1
    local delim_from, delim_to = string.find(str, delimiter, from)
    while delim_from do
        result[#result + 1] = string.sub(str, from, delim_from - 1)
        from = delim_to + 1
        delim_from, delim_to = string.find(str, delimiter, from)
    end
    result[#result + 1] = string.sub(str, from)
    return result
end

function SetPlayerData(source,key, value)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        local player = Core.Functions.GetPlayer(source)
        player.Functions.SetMetaData(key, data)
    elseif CoreName == "es_extended" then
        Core.SetPlayerData(key, value)
    end
end

function AddPlayerMethod(source, methodName, handler)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        Core.Functions.AddPlayerMethod(playerId, methodName, handler)
    elseif CoreName == "es_extended" then
        for k, v in pairs(ids) do
            Core.RegisterServerCallback(methodName, function(source, cb, ...)
                if source == v then
                    handler(source, cb, ...)
                end
            end)
        end
    end
end 

function GetSpecialPlayers()
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        return Core.Functions.GetQBPlayers()
    elseif CoreName == "es_extended" then
        return Core.GetPlayers()
    end
end


function CanUseItem(item)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        return Core.Functions.CanUseItem(item)
    elseif CoreName == "es_extended" then
        return Core.CanUseItem(item)
    end
end

function AddCommand(command, cb)
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        Core.Commands.Add(command, cb)
    elseif CoreName == "es_extended" then
         Core.RegisterCommand(command, cb)
    end
end

function RegisterUseableItem(name)
    while CoreReady == false do Citizen.Wait(0) end
    if CoreName == "qb-core" or CoreName == "qbx_core" then
        Core.Functions.CreateUseableItem(name, function(source, item)
        end)
    elseif CoreName == "es_extended" then
        local hasQs = GetResourceState('qs-inventory') == 'started'
        if hasQs then
            Core.RegisterUsableItem(name, function(source, item)
            end)
            return
        end

        Core.RegisterUsableItem(name, function(source, _, item)
        end)
    end
end

Config.ServerCallbacks = {}
function CreateCallback(name, cb)
    Config.ServerCallbacks[name] = cb
end

function TriggerCallback(name, source, cb, ...)
    if not Config.ServerCallbacks[name] then return end
    Config.ServerCallbacks[name](source, cb, ...)
end

RegisterNetEvent('inventory:server:triggerCallback', function(name, ...)
    local src = source
    TriggerCallback(name, src, function(...)
        TriggerClientEvent('inventory:client:triggerCallback', src, name, ...)
    end, ...)
end)





local count = 0

local file = ('data/items.lua')
local datafile = LoadResourceFile(GetCurrentResourceName(),file)
local path = ('@@%s/%s'):format(GetCurrentResourceName(), file)
local func, err = load(datafile, path)

local itemlist = func()

for item, data in pairs(itemlist) do
	count += 1	
	QBShared.Items[item] = { name = item, label = data.label, weight = data.weight, type = data.type, image = data.client and (data.client.image and data.client.image) or item .. ".png", unique = not stackable, useable = false, shouldClose = data.close, combinable = nil, description = data.description }
end

file = ('data/weapons.lua')
datafile = LoadResourceFile(GetCurrentResourceName(),file)
path = ('@@%s/%s'):format(GetCurrentResourceName(), file)
func, err = load(datafile, path)

itemlist = func()

for weapon, data in pairs(itemlist.Weapons) do
	count += 1
	QBShared.Items[weapon:lower()] = { name = weapon:lower(), label = data.label, weight = data.weight, type = 'weapon', image = weapon:lower() .. '.png', unique = not weapon.stack, useable = false, description = '' }
end

for attach, data in pairs(itemlist.Components) do
	count += 1
	QBShared.Items[attach] = { name = attach, label = data.label, weight = data.weight, type = 'item', image = attach .. ".png", unique = false, useable = true, shouldClose = true, combinable = nil, description = '' }
end

for ammo, data in pairs(itemlist.Ammo) do
	count += 1
	QBShared.Items[ammo] = { name = ammo, label = data.label, weight = data.weight, type = 'item', image = ammo .. '.png', unique = false, useable = true, shouldClose = true, combinable = nil, description = '' }
end


