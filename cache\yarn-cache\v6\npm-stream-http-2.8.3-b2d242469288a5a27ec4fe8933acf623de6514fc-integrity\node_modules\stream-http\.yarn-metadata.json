{"manifest": {"name": "stream-http", "version": "2.8.3", "description": "Streaming http in the browser", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/jhiesey/stream-http.git"}, "scripts": {"test": "npm run test-node && ([ -n \"${TRAVIS_PULL_REQUEST}\" -a \"${TRAVIS_PULL_REQUEST}\" != 'false' ] || npm run test-browser)", "test-node": "tape test/node/*.js", "test-browser": "airtap --loopback airtap.local -- test/browser/*.js", "test-browser-local": "airtap --no-instrument --local 8080 -- test/browser/*.js"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jhiesey/stream-http/issues"}, "homepage": "https://github.com/jhiesey/stream-http#readme", "keywords": ["http", "stream", "streaming", "xhr", "http-browserify"], "dependencies": {"builtin-status-codes": "^3.0.0", "inherits": "^2.0.1", "readable-stream": "^2.3.6", "to-arraybuffer": "^1.0.0", "xtend": "^4.0.0"}, "devDependencies": {"airtap": "^0.0.5", "basic-auth": "^2.0.0", "brfs": "^1.6.1", "cookie-parser": "^1.4.3", "express": "^4.16.3", "tape": "^4.9.0", "ua-parser-js": "^0.7.18", "webworkify": "^1.5.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-stream-http-2.8.3-b2d242469288a5a27ec4fe8933acf623de6514fc-integrity\\node_modules\\stream-http\\package.json", "readmeFilename": "README.md", "readme": "# stream-http [![Build Status](https://travis-ci.org/jhiesey/stream-http.svg?branch=master)](https://travis-ci.org/jhiesey/stream-http)\n\n[![Sauce Test Status](https://saucelabs.com/browser-matrix/stream-http.svg)](https://saucelabs.com/u/stream-http)\n\nThis module is an implementation of Node's native `http` module for the browser.\nIt tries to match Node's API and behavior as closely as possible, but some features\naren't available, since browsers don't give nearly as much control over requests.\n\nThis is heavily inspired by, and intended to replace, [http-browserify](https://github.com/substack/http-browserify).\n\n## What does it do?\n\nIn accordance with its name, `stream-http` tries to provide data to its caller before\nthe request has completed whenever possible.\n\nBackpressure, allowing the browser to only pull data from the server as fast as it is\nconsumed, is supported in:\n* Chrome >= 58 (using `fetch` and `WritableStream`)\n\nThe following browsers support true streaming, where only a small amount of the request\nhas to be held in memory at once:\n* Chrome >= 43 (using the `fetch` API)\n* Firefox >= 9 (using `moz-chunked-arraybuffer` responseType with xhr)\n\nThe following browsers support pseudo-streaming, where the data is available before the\nrequest finishes, but the entire response must be held in memory:\n* Chrome\n* Safari >= 5, and maybe older\n* IE >= 10\n* Most other Webkit-based browsers, including the default Android browser\n\nAll browsers newer than IE8 support binary responses. All of the above browsers that\nsupport true streaming or pseudo-streaming support that for binary data as well\nexcept for IE10. Old (presto-based) Opera also does not support binary streaming either.\n\n### IE8 note:\nAs of version 2.0.0, IE8 support requires the user to supply polyfills for\n`Object.keys`, `Array.prototype.forEach`, and `Array.prototype.indexOf`. Example\nimplementations are provided in [ie8-polyfill.js](ie8-polyfill.js); alternately,\nyou may want to consider using [es5-shim](https://github.com/es-shims/es5-shim).\nAll browsers with full ES5 support shouldn't require any polyfills.\n\n## How do you use it?\n\nThe intent is to have the same API as the client part of the\n[Node HTTP module](https://nodejs.org/api/http.html). The interfaces are the same wherever\npractical, although limitations in browsers make an exact clone of the Node API impossible.\n\nThis module implements `http.request`, `http.get`, and most of `http.ClientRequest`\nand `http.IncomingMessage` in addition to `http.METHODS` and `http.STATUS_CODES`. See the\nNode docs for how these work.\n\n### Extra features compared to Node\n\n* The `message.url` property provides access to the final URL after all redirects. This\nis useful since the browser follows all redirects silently, unlike Node. It is available\nin Chrome 37 and newer, Firefox 32 and newer, and Safari 9 and newer.\n\n* The `options.withCredentials` boolean flag, used to indicate if the browser should send\ncookies or authentication information with a CORS request. Default false.\n\nThis module has to make some tradeoffs to support binary data and/or streaming. Generally,\nthe module can make a fairly good decision about which underlying browser features to use,\nbut sometimes it helps to get a little input from the developer.\n\n* The `options.mode` field passed into `http.request` or `http.get` can take on one of the\nfollowing values:\n  * 'default' (or any falsy value, including `undefined`): Try to provide partial data before\nthe request completes, but not at the cost of correctness for binary data or correctness of\nthe 'content-type' response header. This mode will also avoid slower code paths whenever\npossible, which is particularly useful when making large requests in a browser like Safari\nthat has a weaker JavaScript engine.\n  * 'allow-wrong-content-type': Provides partial data in more cases than 'default', but\nat the expense of causing the 'content-type' response header to be incorrectly reported\n(as 'text/plain; charset=x-user-defined') in some browsers, notably Safari and Chrome 42\nand older. Preserves binary data whenever possible. In some cases the implementation may\nalso be a bit slow. This was the default in versions of this module before 1.5.\n  * 'prefer-stream': Provide data before the request completes even if binary data (anything\nthat isn't a single-byte ASCII or UTF8 character) will be corrupted. Of course, this option\nis only safe for text data. May also cause the 'content-type' response header to be\nincorrectly reported (as 'text/plain; charset=x-user-defined').\n  * 'disable-fetch': Force the use of plain XHR regardless of the browser declaring a fetch\ncapability. Preserves the correctness of binary data and the 'content-type' response header.\n  * 'prefer-fast': Deprecated; now a synonym for 'default', which has the same performance\ncharacteristics as this mode did in versions before 1.5.\n\n* `options.requestTimeout` allows setting a timeout in millisecionds for XHR and fetch (if\nsupported by the browser). This is a limit on how long the entire process takes from\nbeginning to end. Note that this is not the same as the node `setTimeout` functions,\nwhich apply to pauses in data transfer over the underlying socket, or the node `timeout`\noption, which applies to opening the connection.\n\n### Features missing compared to Node\n\n* `http.Agent` is only a stub\n* The 'socket', 'connect', 'upgrade', and 'continue' events on `http.ClientRequest`.\n* Any operations, including `request.setTimeout`, that operate directly on the underlying\nsocket.\n* Any options that are disallowed for security reasons. This includes setting or getting\ncertain headers.\n* `message.httpVersion`\n* `message.rawHeaders` is modified by the browser, and may not quite match what is sent by\nthe server.\n* `message.trailers` and `message.rawTrailers` will remain empty.\n* Redirects are followed silently by the browser, so it isn't possible to access the 301/302\nredirect pages.\n* The `timeout` event/option and `setTimeout` functions, which operate on the underlying\nsocket, are not available. However, see `options.requestTimeout` above.\n\n## Example\n\n``` js\nhttp.get('/bundle.js', function (res) {\n\tvar div = document.getElementById('result');\n\tdiv.innerHTML += 'GET /beep<br>';\n\n\tres.on('data', function (buf) {\n\t\tdiv.innerHTML += buf;\n\t});\n\n\tres.on('end', function () {\n\t\tdiv.innerHTML += '<br>__END__';\n\t});\n})\n```\n\n## Running tests\n\nThere are two sets of tests: the tests that run in Node (found in `test/node`) and the tests\nthat run in the browser (found in `test/browser`). Normally the browser tests run on\n[Sauce Labs](http://saucelabs.com/).\n\nRunning `npm test` will run both sets of tests, but in order for the Sauce Labs tests to run\nyou will need to sign up for an account (free for open source projects) and put the\ncredentials in a [`.zuulrc` file](https://github.com/defunctzombie/zuul/wiki/zuulrc).\n\nTo run just the Node tests, run `npm run test-node`.\n\nTo run the browser tests locally, run `npm run test-browser-local` and point your browser to\n`http://localhost:8080/__zuul`\n\n## License\n\nMIT. Copyright (C) John Hiesey and other contributors.\n", "licenseText": "The MIT License\n\nCopyright (c) 2015 John <PERSON>\n\nPermission is hereby granted, free of charge, \nto any person obtaining a copy of this software and \nassociated documentation files (the \"Software\"), to \ndeal in the Software without restriction, including \nwithout limitation the rights to use, copy, modify, \nmerge, publish, distribute, sublicense, and/or sell \ncopies of the Software, and to permit persons to whom \nthe Software is furnished to do so, \nsubject to the following conditions:\n\nThe above copyright notice and this permission notice \nshall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, \nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES \nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. \nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR \nANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, \nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE \nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/stream-http/-/stream-http-2.8.3.tgz#b2d242469288a5a27ec4fe8933acf623de6514fc", "type": "tarball", "reference": "https://registry.yarnpkg.com/stream-http/-/stream-http-2.8.3.tgz", "hash": "b2d242469288a5a27ec4fe8933acf623de6514fc", "integrity": "sha512-+TSkfINHDo4J+ZobQLWiMouQYB+UVYFttRA94FpEzzJ7ZdqcL4uUUQ7WkdkI4DSozGmgBUE/a47L+38PenXhUw==", "registry": "npm", "packageName": "stream-http", "cacheIntegrity": "sha512-+TSkfINHDo4J+ZobQLWiMouQYB+UVYFttRA94FpEzzJ7ZdqcL4uUUQ7WkdkI4DSozGmgBUE/a47L+38PenXhUw== sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw="}, "registry": "npm", "hash": "b2d242469288a5a27ec4fe8933acf623de6514fc"}