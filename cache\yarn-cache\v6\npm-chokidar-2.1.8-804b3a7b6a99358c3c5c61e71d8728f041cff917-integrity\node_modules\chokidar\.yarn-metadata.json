{"manifest": {"name": "chokidar", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "version": "2.1.8", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "types": "./types/index.d.ts", "homepage": "https://github.com/paulmillr/chokidar", "author": {"name": "<PERSON>", "url": "https://paulmillr.com"}, "repository": {"type": "git", "url": "https://github.com/paulmillr/chokidar.git"}, "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "license": "MIT", "scripts": {"test": "nyc mocha --exit", "coveralls": "nyc report --reporter=text-lcov | coveralls", "dtslint": "dtslint types"}, "files": ["index.js", "lib/", "types/index.d.ts"], "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "devDependencies": {"@types/node": "^11.9.4", "chai": "^3.2.0", "coveralls": "^3.0.1", "dtslint": "0.4.1", "graceful-fs": "4.1.4", "mocha": "^5.2.0", "nyc": "^11.8.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-chokidar-2.1.8-804b3a7b6a99358c3c5c61e71d8728f041cff917-integrity\\node_modules\\chokidar\\package.json", "readmeFilename": "README.md", "readme": "# Chokidar [![Weekly downloads](https://img.shields.io/npm/dw/chokidar.svg)](https://github.com/paulmillr/chokidar) [![Yearly downloads](https://img.shields.io/npm/dy/chokidar.svg)](https://github.com/paulmillr/chokidar) [![Mac/Linux Build Status](https://img.shields.io/travis/paulmillr/chokidar/master.svg?label=Mac%20OSX%20%26%20Linux)](https://travis-ci.org/paulmillr/chokidar) [![Windows Build status](https://img.shields.io/appveyor/ci/paulmillr/chokidar/master.svg?label=Windows)](https://ci.appveyor.com/project/paulmillr/chokidar/branch/master) [![Coverage Status](https://coveralls.io/repos/paulmillr/chokidar/badge.svg)](https://coveralls.io/r/paulmillr/chokidar)\n\n> A neat wrapper around node.js fs.watch / fs.watchFile / FSEvents.\n\n[![NPM](https://nodei.co/npm/chokidar.png)](https://www.npmjs.com/package/chokidar)\n\n## Why?\nNode.js `fs.watch`:\n\n* Doesn't report filenames on MacOS.\n* Doesn't report events at all when using editors like Sublime on MacOS.\n* Often reports events twice.\n* Emits most changes as `rename`.\n* Has [a lot of other issues](https://github.com/nodejs/node/search?q=fs.watch&type=Issues)\n* Does not provide an easy way to recursively watch file trees.\n\nNode.js `fs.watchFile`:\n\n* Almost as bad at event handling.\n* Also does not provide any recursive watching.\n* Results in high CPU utilization.\n\nChokidar resolves these problems.\n\nInitially made for **[Brunch](http://brunch.io)** (an ultra-swift web app build tool), it is now used in\n[gulp](https://github.com/gulpjs/gulp/),\n[karma](http://karma-runner.github.io),\n[PM2](https://github.com/Unitech/PM2),\n[browserify](http://browserify.org/),\n[webpack](http://webpack.github.io/),\n[BrowserSync](http://www.browsersync.io/),\n[Microsoft's Visual Studio Code](https://github.com/microsoft/vscode),\nand [many others](https://www.npmjs.org/browse/depended/chokidar/).\nIt has proven itself in production environments.\n\n## How?\nChokidar does still rely on the Node.js core `fs` module, but when using\n`fs.watch` and `fs.watchFile` for watching, it normalizes the events it\nreceives, often checking for truth by getting file stats and/or dir contents.\n\nOn MacOS, chokidar by default uses a native extension exposing the Darwin\n`FSEvents` API. This provides very efficient recursive watching compared with\nimplementations like `kqueue` available on most \\*nix platforms. Chokidar still\ndoes have to do some work to normalize the events received that way as well.\n\nOn other platforms, the `fs.watch`-based implementation is the default, which\navoids polling and keeps CPU usage down. Be advised that chokidar will initiate\nwatchers recursively for everything within scope of the paths that have been\nspecified, so be judicious about not wasting system resources by watching much\nmore than needed.\n\n## Getting started\nInstall with npm:\n\n```sh\nnpm install chokidar\n```\n\nThen `require` and use it in your code:\n\n```javascript\nvar chokidar = require('chokidar');\n\n// One-liner for current directory, ignores .dotfiles\nchokidar.watch('.', {ignored: /(^|[\\/\\\\])\\../}).on('all', (event, path) => {\n  console.log(event, path);\n});\n```\n\n```javascript\n// Example of a more typical implementation structure:\n\n// Initialize watcher.\nvar watcher = chokidar.watch('file, dir, glob, or array', {\n  ignored: /(^|[\\/\\\\])\\../,\n  persistent: true\n});\n\n// Something to use when events are received.\nvar log = console.log.bind(console);\n// Add event listeners.\nwatcher\n  .on('add', path => log(`File ${path} has been added`))\n  .on('change', path => log(`File ${path} has been changed`))\n  .on('unlink', path => log(`File ${path} has been removed`));\n\n// More possible events.\nwatcher\n  .on('addDir', path => log(`Directory ${path} has been added`))\n  .on('unlinkDir', path => log(`Directory ${path} has been removed`))\n  .on('error', error => log(`Watcher error: ${error}`))\n  .on('ready', () => log('Initial scan complete. Ready for changes'))\n  .on('raw', (event, path, details) => {\n    log('Raw event info:', event, path, details);\n  });\n\n// 'add', 'addDir' and 'change' events also receive stat() results as second\n// argument when available: http://nodejs.org/api/fs.html#fs_class_fs_stats\nwatcher.on('change', (path, stats) => {\n  if (stats) console.log(`File ${path} changed size to ${stats.size}`);\n});\n\n// Watch new files.\nwatcher.add('new-file');\nwatcher.add(['new-file-2', 'new-file-3', '**/other-file*']);\n\n// Get list of actual paths being watched on the filesystem\nvar watchedPaths = watcher.getWatched();\n\n// Un-watch some files.\nwatcher.unwatch('new-file*');\n\n// Stop watching.\nwatcher.close();\n\n// Full list of options. See below for descriptions. (do not use this example)\nchokidar.watch('file', {\n  persistent: true,\n\n  ignored: '*.txt',\n  ignoreInitial: false,\n  followSymlinks: true,\n  cwd: '.',\n  disableGlobbing: false,\n\n  usePolling: true,\n  interval: 100,\n  binaryInterval: 300,\n  alwaysStat: false,\n  depth: 99,\n  awaitWriteFinish: {\n    stabilityThreshold: 2000,\n    pollInterval: 100\n  },\n\n  ignorePermissionErrors: false,\n  atomic: true // or a custom 'atomicity delay', in milliseconds (default 100)\n});\n\n```\n\n## API\n\n`chokidar.watch(paths, [options])`\n\n* `paths` (string or array of strings). Paths to files, dirs to be watched\nrecursively, or glob patterns.\n* `options` (object) Options object as defined below:\n\n#### Persistence\n\n* `persistent` (default: `true`). Indicates whether the process\nshould continue to run as long as files are being watched. If set to\n`false` when using `fsevents` to watch, no more events will be emitted\nafter `ready`, even if the process continues to run.\n\n#### Path filtering\n\n* `ignored` ([anymatch](https://github.com/es128/anymatch)-compatible definition)\nDefines files/paths to be ignored. The whole relative or absolute path is\ntested, not just filename. If a function with two arguments is provided, it\ngets called twice per path - once with a single argument (the path), second\ntime with two arguments (the path and the\n[`fs.Stats`](http://nodejs.org/api/fs.html#fs_class_fs_stats)\nobject of that path).\n* `ignoreInitial` (default: `false`). If set to `false` then `add`/`addDir` events are also emitted for matching paths while\ninstantiating the watching as chokidar discovers these file paths (before the `ready` event).\n* `followSymlinks` (default: `true`). When `false`, only the\nsymlinks themselves will be watched for changes instead of following\nthe link references and bubbling events through the link's path.\n* `cwd` (no default). The base directory from which watch `paths` are to be\nderived. Paths emitted with events will be relative to this.\n* `disableGlobbing` (default: `false`). If set to `true` then the strings passed to `.watch()` and `.add()` are treated as\nliteral path names, even if they look like globs.\n\n#### Performance\n\n* `usePolling` (default: `false`).\nWhether to use fs.watchFile (backed by polling), or fs.watch. If polling\nleads to high CPU utilization, consider setting this to `false`. It is\ntypically necessary to **set this to `true` to successfully watch files over\na network**, and it may be necessary to successfully watch files in other\nnon-standard situations. Setting to `true` explicitly on MacOS overrides the\n`useFsEvents` default. You may also set the CHOKIDAR_USEPOLLING env variable\nto true (1) or false (0) in order to override this option.\n* _Polling-specific settings_ (effective when `usePolling: true`)\n  * `interval` (default: `100`). Interval of file system polling. You may also\n    set the CHOKIDAR_INTERVAL env variable to override this option.\n  * `binaryInterval` (default: `300`). Interval of file system\n  polling for binary files.\n  ([see list of binary extensions](https://github.com/sindresorhus/binary-extensions/blob/master/binary-extensions.json))\n* `useFsEvents` (default: `true` on MacOS). Whether to use the\n`fsevents` watching interface if available. When set to `true` explicitly\nand `fsevents` is available this supercedes the `usePolling` setting. When\nset to `false` on MacOS, `usePolling: true` becomes the default.\n* `alwaysStat` (default: `false`). If relying upon the\n[`fs.Stats`](http://nodejs.org/api/fs.html#fs_class_fs_stats)\nobject that may get passed with `add`, `addDir`, and `change` events, set\nthis to `true` to ensure it is provided even in cases where it wasn't\nalready available from the underlying watch events.\n* `depth` (default: `undefined`). If set, limits how many levels of\nsubdirectories will be traversed.\n* `awaitWriteFinish` (default: `false`).\nBy default, the `add` event will fire when a file first appears on disk, before\nthe entire file has been written. Furthermore, in some cases some `change`\nevents will be emitted while the file is being written. In some cases,\nespecially when watching for large files there will be a need to wait for the\nwrite operation to finish before responding to a file creation or modification.\nSetting `awaitWriteFinish` to `true` (or a truthy value) will poll file size,\nholding its `add` and `change` events until the size does not change for a\nconfigurable amount of time. The appropriate duration setting is heavily\ndependent on the OS and hardware. For accurate detection this parameter should\nbe relatively high, making file watching much less responsive.\nUse with caution.\n  * *`options.awaitWriteFinish` can be set to an object in order to adjust\n  timing params:*\n  * `awaitWriteFinish.stabilityThreshold` (default: 2000). Amount of time in\n  milliseconds for a file size to remain constant before emitting its event.\n  * `awaitWriteFinish.pollInterval` (default: 100). File size polling interval.\n\n#### Errors\n* `ignorePermissionErrors` (default: `false`). Indicates whether to watch files\nthat don't have read permissions if possible. If watching fails due to `EPERM`\nor `EACCES` with this set to `true`, the errors will be suppressed silently.\n* `atomic` (default: `true` if `useFsEvents` and `usePolling` are `false`).\nAutomatically filters out artifacts that occur when using editors that use\n\"atomic writes\" instead of writing directly to the source file. If a file is\nre-added within 100 ms of being deleted, Chokidar emits a `change` event\nrather than `unlink` then `add`. If the default of 100 ms does not work well\nfor you, you can override it by setting `atomic` to a custom value, in\nmilliseconds.\n\n### Methods & Events\n\n`chokidar.watch()` produces an instance of `FSWatcher`. Methods of `FSWatcher`:\n\n* `.add(path / paths)`: Add files, directories, or glob patterns for tracking.\nTakes an array of strings or just one string.\n* `.on(event, callback)`: Listen for an FS event.\nAvailable events: `add`, `addDir`, `change`, `unlink`, `unlinkDir`, `ready`,\n`raw`, `error`.\nAdditionally `all` is available which gets emitted with the underlying event\nname and path for every event other than `ready`, `raw`, and `error`.\n* `.unwatch(path / paths)`: Stop watching files, directories, or glob patterns.\nTakes an array of strings or just one string.\n* `.close()`: Removes all listeners from watched files.\n* `.getWatched()`: Returns an object representing all the paths on the file\nsystem being watched by this `FSWatcher` instance. The object's keys are all the\ndirectories (using absolute paths unless the `cwd` option was used), and the\nvalues are arrays of the names of the items contained in each directory.\n\n## CLI\n\nIf you need a CLI interface for your file watching, check out\n[chokidar-cli](https://github.com/kimmobrunfeldt/chokidar-cli), allowing you to\nexecute a command on each change, or get a stdio stream of change events.\n\n## Install Troubleshooting\n\n* `npm WARN optional dep failed, continuing fsevents@n.n.n`\n  * This message is normal part of how `npm` handles optional dependencies and is\n    not indicative of a problem. Even if accompanied by other related error messages,\n    Chokidar should function properly.\n\n* `ERR! stack Error: Python executable \"python\" is v3.4.1, which is not supported by gyp.`\n  * You should be able to resolve this by installing python 2.7 and running:\n    `npm config set python python2.7`\n\n* `gyp ERR! stack Error: not found: make`\n  * On Mac, install the XCode command-line tools\n\n## License\n\nThe MIT License (MIT)\n\nCopyright (c) 2012-2019 Paul Miller (https://paulmillr.com) & Elan Shanker\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the “Software”), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/chokidar/-/chokidar-2.1.8.tgz#804b3a7b6a99358c3c5c61e71d8728f041cff917", "type": "tarball", "reference": "https://registry.yarnpkg.com/chokidar/-/chokidar-2.1.8.tgz", "hash": "804b3a7b6a99358c3c5c61e71d8728f041cff917", "integrity": "sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg==", "registry": "npm", "packageName": "chokidar", "cacheIntegrity": "sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg== sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc="}, "registry": "npm", "hash": "804b3a7b6a99358c3c5c61e71d8728f041cff917"}