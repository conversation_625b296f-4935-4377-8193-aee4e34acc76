{"manifest": {"name": "lower-case", "version": "1.1.4", "description": "Lowercase a string", "main": "lower-case.js", "typings": "lower-case.d.ts", "files": ["lower-case.js", "lower-case.d.ts", "LICENSE"], "scripts": {"lint": "standard", "test-std": "mocha -- -R spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail", "test": "npm run lint && npm run test-cov"}, "standard": {"ignore": ["coverage/**", "node_modules/**", "bower_components/**"]}, "repository": {"type": "git", "url": "git://github.com/blakeembrey/lower-case.git"}, "keywords": ["cases", "lower", "lowercase", "case"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/blakeembrey/lower-case/issues"}, "homepage": "https://github.com/blakeembrey/lower-case", "devDependencies": {"istanbul": "^0.3.5", "mocha": "^2.1.0", "pre-commit": "^1.0.2", "standard": "^2.4.5"}, "dependencies": {}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-lower-case-1.1.4-9a2cabd1b9e8e0ae993a4bf7d5875c39c42e8eac-integrity\\node_modules\\lower-case\\package.json", "readmeFilename": "README.md", "readme": "# Lower Case\n\n[![NPM version][npm-image]][npm-url]\n[![NPM downloads][downloads-image]][downloads-url]\n[![Build status][travis-image]][travis-url]\n[![Test coverage][coveralls-image]][coveralls-url]\n\nLower case a string.\n\nSupports Unicode (non-ASCII characters) and non-string entities, such as objects with a `toString` property, numbers and booleans. Empty values (`null` and `undefined`) will result in an empty string.\n\n## Installation\n\n```\nnpm install lower-case --save\n```\n\n## Usage\n\n```js\nvar lowerCase = require('lower-case')\n\nlowerCase(null)           //=> \"\"\nlowerCase('STRING')       //=> \"string\"\nlowerCase('STRING', 'tr') //=> \"strıng\"\n\nlowerCase({ toString: function () { return 'TEST' } }) //=> \"test\"\n```\n\n## Typings\n\nIncludes a [TypeScript definition](lower-case.d.ts).\n\n## License\n\nMIT\n\n[npm-image]: https://img.shields.io/npm/v/lower-case.svg?style=flat\n[npm-url]: https://npmjs.org/package/lower-case\n[downloads-image]: https://img.shields.io/npm/dm/lower-case.svg?style=flat\n[downloads-url]: https://npmjs.org/package/lower-case\n[travis-image]: https://img.shields.io/travis/blakeembrey/lower-case.svg?style=flat\n[travis-url]: https://travis-ci.org/blakeembrey/lower-case\n[coveralls-image]: https://img.shields.io/coveralls/blakeembrey/lower-case.svg?style=flat\n[coveralls-url]: https://coveralls.io/r/blakeembrey/lower-case?branch=master\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 <PERSON> (<EMAIL>)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/lower-case/-/lower-case-1.1.4.tgz#9a2cabd1b9e8e0ae993a4bf7d5875c39c42e8eac", "type": "tarball", "reference": "https://registry.yarnpkg.com/lower-case/-/lower-case-1.1.4.tgz", "hash": "9a2cabd1b9e8e0ae993a4bf7d5875c39c42e8eac", "integrity": "sha1-miyr0bno4K6ZOkv31YdcOcQujqw=", "registry": "npm", "packageName": "lower-case", "cacheIntegrity": "sha512-2Fgx1Ycm599x+WGpIYwJOvsjmXFzTSc34IwDWALRA/8AopUKAVPwfJ+h5+f85BCp0PWmmJcWzEpxOpoXycMpdA== sha1-miyr0bno4K6ZOkv31YdcOcQujqw="}, "registry": "npm", "hash": "9a2cabd1b9e8e0ae993a4bf7d5875c39c42e8eac"}