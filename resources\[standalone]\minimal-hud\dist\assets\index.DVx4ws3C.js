import{A as e,y as t,u as l,a,b as s,R as o,T as r,c as n,q as i,F as c,d,P as h,e as f,f as u,k as p,t as m,g as x,h as v,i as w,j as g,l as b,m as N,B as y,n as _,o as k,p as F,r as I,s as $,v as B,w as S,D as j}from"./vendor.CtzV0aEa.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const l of e)if("childList"===l.type)for(const e of l.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const C=()=>!window.invokeNative,M=()=>{},O=(l,a)=>{const s=e(M);t((()=>{s.current=a}),[a]),t((()=>{const e=e=>{const{action:t,data:a}=e.data;s.current&&t===l&&s.current(a)};return window.addEventListener("message",e),()=>window.removeEventListener("message",e)}),[l])},z=s({health:100,armor:100,hunger:50,thirst:100,oxygen:100,stamina:100,stress:0,voice:50,streetLabel:"Downtown Vinewood",areaLabel:"Vinewood Blvd",heading:"NW",isSeatbeltOn:!1,isInVehicle:!0,mic:!0}),A=()=>l(z),L=s({speed:222,rpm:50,engineState:!0,engineHealth:50,gears:6,currentGear:"N",fuel:50,nos:40,speedUnit:"MPH",headlights:50});let V=!1;const E=(...e)=>{C()},R=o.memo((function({speed:l=50,maxRpm:a=100,rpm:s=20,gears:o=8,currentGear:i,speedUnit:c}){const d=r((()=>s/a*100),[s,a]),h=e(null),f=(e,t,l,a)=>{const s=(a-90)*Math.PI/180;return{x:e+l*Math.cos(s),y:t+l*Math.sin(s)}},u=r((()=>(e,t,l,a,s)=>{const o=f(e,t,l,a),r=f(e,t,l,s),n=s-a<=180?"0":"1";return["M",o.x,o.y,"A",l,l,0,n,1,r.x,r.y].join(" ")}),[]),p=r((()=>(e,t,l,a,s)=>{const o=f(e,t,l,s),r=f(e,t,a,s);return`M ${o.x} ${o.y} L ${r.x} ${r.y}`}),[]);t((()=>{if(h.current){const e=h.current.getTotalLength(),t=e*(1-d/100);h.current.style.strokeDasharray=`${e} ${e}`,h.current.style.strokeDashoffset=`${t}`}}),[d]);const m=r((()=>[...Array(o)].map(((e,t)=>{const l=240*t/Math.max(o-1,1)-120,a=f(0,0,30,l);return n("g",{children:[n("path",{d:p(0,0,38,42,l),stroke:"#dee2e6",strokeWidth:"2.1",opacity:"100",strokeLinecap:"round"}),n("text",{x:a.x,y:a.y,textAnchor:"middle",alignmentBaseline:"middle",fill:"white",fontSize:"5",fontWeight:"bold",children:t+1})]},`gear-${t}`)}))),[o,p,f]);return n("div",{className:"w-60 2k:w-[15dvw] 2k:h-[21dvh] 4k:w-[10dvw] 4k:h-[20dvh] h-64 relative flex items-center justify-center -mb-20 z-0",children:[n("svg",{viewBox:"-50 -50 100 100",preserveAspectRatio:"xMidYMid meet",className:"w-full h-full",children:[n("defs",{children:n("filter",{id:"glow",children:[n("feGaussianBlur",{stdDeviation:"2.5",result:"coloredBlur"}),n("feMerge",{children:[n("feMergeNode",{in:"coloredBlur"}),n("feMergeNode",{in:"SourceGraphic"})]})]})}),n("g",{filter:"url(#glow)",children:[n("path",{d:u(0,0,40,-120,120),fill:"none",stroke:"#11181a27",strokeWidth:"4"}),n("path",{ref:h,d:u(0,0,40,-120,120),fill:"none",strokeWidth:"4",className:"transition-all duration-300 ease-in-out",style:{stroke:d>=90?"#fe2436":d>=85?"#FB8607":"#06CE6B"}})]}),m]}),n("div",{className:"absolute inset-0 flex items-center justify-center",children:n("div",{className:"text-center flex flex-col",children:[n("span",{className:"absolute -mt-5 left-1/2 transform -translate-x-1/2 text-[1vw] font-semibold text-gray-400 tabular-nums drop-shadow-[0_1.2px_1.2px_rgba(0,0,0,1)] ml-1",children:[" ",i," "]}),n("span",{className:"text-[2vw] font-bold text-white tabular-nums drop-shadow-[0_1.2px_1.2px_rgba(0,0,0,1)] ml-2",children:[" ",l," "]}),n("span",{className:"text-[1vw] -mt-1 font-semibold text-gray-400 drop-shadow-[0_1.2px_1.2px_rgba(0,0,0,1)] ml-4 uppercase",children:[" ",c," "]})]})})]})})),W=({value:e=50,icon:t,color:l="#06CE6B",iconSize:a="1.2vw",iconSpacing:s="12px",...o})=>{const i=r((()=>e<=20?"#FE2436":e<=50?"#FB8607":l),[l,e]);return n("div",{className:"flex flex-col items-center justify-center w-[2.1dvw] h-[4dvh]",...o,children:[n("div",{className:"flex items-center justify-center text-[1vw]",style:{height:s,fontSize:a,marginBottom:s,color:"rgba(255, 255, 255, 0.87)"},children:t}),n("div",{className:"relative w-[80%] bg-black/20 shadow h-[3.5px] 4k:h-[5px] 4k:mt-1 rounded-full drop-shadow-[0_1.2px_1.2px_rgba(0,0,0,1)]",children:n("div",{className:"absolute max-w-full transition-all rounded-full shadow left-0 h-full z-20",style:{width:`${e}%`,backgroundColor:i}})})]})},G=s(!1),J=s(20),P=()=>l(G),D=()=>l(J),U=o.memo((function(){const[e,t]=a(L),l=A(),s=P(),o=D(),m=i((e=>{t((t=>JSON.stringify(t)!==JSON.stringify(e)?e:t))}),[t]);return O("state::vehicle::set",m),r((()=>l.isInVehicle?n("div",{className:"absolute bottom-8 right-16 w-fit h-fit mb-4 flex-col items-center flex justify-center gap-2",style:s?{transform:`perspective(1000px) rotateY(-${o}deg)`,backfaceVisibility:"hidden",transformStyle:"preserve-3d",willChange:"transform"}:void 0,children:[n(R,{speed:e.speed,maxRpm:100,rpm:e.rpm,gears:e.gears,currentGear:e.currentGear,engineHealth:e.engineHealth,speedUnit:e.speedUnit}),n("div",{className:"flex gap-2 items-center 4k:-mt-14 mt-2.5 ml-2",children:n(p,{children:[n(W,{icon:n(c,{}),value:e.fuel,iconSize:"1.1vw"}),n(W,{icon:n(d,{}),value:e.nos}),n(W,{icon:n(h,{}),value:e.engineHealth}),n(W,{icon:n(f,{}),value:e.headlights}),n(W,{icon:n(u,{}),value:l.isSeatbeltOn?100:0,iconSize:"1.25vw"})]})})]}):(E(),null)),[l.isInVehicle,e,l.isSeatbeltOn,s])})),H=({Icon:e=x,label:t="NW",className:l="",textClassName:a="",iconClassName:s="",...o})=>n("div",{className:m("flex items-center h-[2.5dvh] justify-center text-y_white bg-black/30 rounded-[8px] p-[1px] min-w-[5dvw]",l),...o,children:[n(e,{className:m("mr-2 drop-shadow-[0_1.2px_1.2px_rgba(0,0,0,1)] text-[0.8vw]",s)}),n("p",{className:m("text-center text-y_white font-bold text-[0.7vw] drop-shadow-[0_1.2px_1.2px_rgba(0,0,0,1)]",a),style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:t})]}),Y=s("top"),T=s(!0),q=o.memo((()=>{const e=A(),t=l(Y);return l(T)||e.isInVehicle?n("div",{className:"bottom"===t?"flex absolute bottom-1 w-full h-fit items-center justify-center ":"flex w-full h-[10dvh] items-center justify-center",children:n("div",{className:"flex gap-3 items-center justify-center w-[50%]",children:[n(H,{label:e.heading,Icon:v}),n(H,{label:e.streetLabel,className:"min-w-[20%]",Icon:w}),n(H,{className:"px-3",label:e.areaLabel,Icon:g})]})}):null})),K=s({height:197.64,left:20.000002026557922,top:800.9999979734421,width:314.496}),Q=({Icon:e=N,value:t=20,maxValue:l=100,color:a="#F2F2F2",vertical:s=!1,iconColor:o,...i})=>{const c=r((()=>t/l*100),[t,l]);return n("div",{className:`flex ${s?"h-[3dvh]":"w-full"} items-center gap-1 4k:gap-2`,...i,children:[!s&&n(e,{className:"${finalIconColor} text-[1vw] "}),!s&&n("p",{className:"text-[0.6vw] drop-shadow-[0_1.2px_1.2px_rgba(0,0,0,1)] w-[20px] 4k:text-base 2k:text-sm text-center font-bold",style:{color:a},children:t}),n("div",{className:`relative drop-shadow-[0_1.2px_1.2px_rgba(0,0,0,1)] ${s?"h-full 2k:w-[6px] w-[4px] 4k:w-[8px] rounded-full":"w-full ml-1 h-2 2k:h-3 rounded-full"} bg-black/30 overflow-hidden`,children:n("div",{className:`absolute ${s?"bottom-0 w-full":"left-0 h-full"} transition-all bg-red-500 rounded-[1px] ease-in-out`,style:{backgroundColor:a,[s?"height":"width"]:`${c}%`,borderRadius:c<100?"50px":"9999px",overflow:"hidden"}})}),s&&n(e,{className:"${finalIconColor} drop-shadow-[0_1.2px_1.2px_rgba(0,0,0,1)] text-[0.8vw]"})]})},X=({Icon:e=N,value:t=20,color:l="#F2F2F2",...a})=>{const s=r((()=>Array.from({length:4},((e,l)=>t>=100*(l+1)/4?100:t>100*l/4?(t-100*l/4)/25*100:0))),[t,4,25]);return n("div",{className:"flex items-center gap-1 w-full 4k:gap-2",...a,children:[n(e,{className:"text-y_white text-[1vw] drop-shadow-[0_1.2px_1.2px_rgba(0,0,0,1)]"}),n("p",{className:"text-[0.6vw] drop-shadow-[0_1.2px_1.2px_rgba(0,0,0,1)] w-[20px] 4k:text-base 2k:text-sm text-center font-bold",style:{color:l},children:t}),n("div",{className:"relative flex gap-3 *:drop-shadow-[0_1.2px_1.2px_rgba(0,0,0,1)] w-full ml-1 h-[8px] 2k:h-3 rounded-[1px]",children:s.map(((e,t)=>n("svg",{width:"100%",height:"100%",className:"rounded-full ",viewBox:"0 0 100 24",preserveAspectRatio:"none",children:[n("rect",{x:"0",y:"0",width:"100",height:"24",className:"fill-black/30"}),n("rect",{x:"0",y:"0",width:e,height:"24",fill:l,className:"transition-all"})]},t)))})]})},Z=o.memo((()=>{const[e,t]=a(z),[l,s]=a(K),o=P(),c=D(),d=i((e=>{t((t=>JSON.stringify(t)!==JSON.stringify(e)?e:t))}),[t]);O("state::global::set",(e=>{d(e.player),s(e.minimap)}));const h=r((()=>void 0!==e.hunger||void 0!==e.thirst),[e]);return n(p,{children:n("div",{class:"absolute items-end justify-center z-20 flex",style:{top:l.top+"px",left:l.left+"px",width:2*l.width+"px",height:l.height+"px"},children:n("div",{className:"w-full h-full relative",style:o?{transform:`perspective(1000px) rotateY(${c}deg)`,backfaceVisibility:"hidden",transformStyle:"preserve-3d",willChange:"transform"}:void 0,children:n("div",{className:"absolute -bottom-12 w-full flex gap-3 items-center justify-start",children:[n("div",{className:"flex flex-col w-2/4 items-center justify-center gap-1",children:[n(X,{Icon:y,value:e.armor,color:"#2B78FC"}),n(Q,{Icon:N,value:e.health,color:"#06CE6B",maxValue:100})]}),h&&n(p,{children:n("div",{className:"w-2/4 flex gap-3",children:["boolean"==typeof e.mic&&!0===e.mic?n(Q,{Icon:_,value:e.mic?100:0,color:"#FFFF00",vertical:!0}):"number"==typeof e.voice?n(Q,{Icon:_,value:e.voice,color:"#ffffff",vertical:!0}):null,n(Q,{Icon:k,value:e.hunger,color:"#FB8607",vertical:!0}),n(Q,{Icon:F,value:e.thirst,color:"#2B78FC",vertical:!0}),e.oxygen<100&&n(Q,{Icon:I,value:e.oxygen,color:"#00d4ff",vertical:!0}),e.stamina<100&&n(Q,{Icon:$,value:e.stamina,color:"#63e6be",vertical:!0}),"number"==typeof e.stress&&e.stress>0&&n(Q,{Icon:B,value:e.stress,color:"#FE2436",vertical:!0})]})})]})})})})}));async function ee(e,t,l){if(C())return await new Promise((e=>e));const a={method:"post",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify(t)},s=window.GetParentResourceName?window.GetParentResourceName():"nui-frame-app",o=await fetch(`https://${s}/${e}`,a);return await o.json()}if(C()){const e=document.body;e.style.backgroundImage='url("https://images.hdqwalls.com/download/dodge-charger-srt-hellcat-enforcer-n1-3840x2400.jpg")',e.style.backgroundSize="cover",e.style.backgroundRepeat="no-repeat",E()}function te(){const[e,l]=S(!0),s=b(K),[o,r]=a(Y),[i,c]=a(T),[d,h]=a(G),[f,u]=a(J);return O("state::visibility::app::set",(t=>{const a="toggle"===t?!e:t;l(a),ee("state::visibility::app::sync",a),E()})),t((()=>{ee("APP_LOADED").then((e=>{var t;t=e.config.debug??!1,V=t,s(e.minimap),r(e.config.compassLocation),c(e.config.compassAlways),h(e.config.useSkewedStyle),u(e.config.skewAmount)})).catch((e=>{})).finally((()=>{E()}))}),[]),e?n(p,{children:[n(Z,{}),n(U,{}),"hidden"!==o&&n(p,{children:n(q,{})})]}):(E(),n(p,{}))}j(n(te,{}),document.getElementById("app"));
