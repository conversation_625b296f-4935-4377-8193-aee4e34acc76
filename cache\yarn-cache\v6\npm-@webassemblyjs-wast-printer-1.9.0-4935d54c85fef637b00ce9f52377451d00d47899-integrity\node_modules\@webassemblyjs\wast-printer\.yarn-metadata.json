{"manifest": {"name": "@webassemblyjs/wast-printer", "version": "1.9.0", "description": "WebAssembly text format printer", "main": "lib/index.js", "module": "esm/index.js", "keywords": ["webassembly", "javascript", "ast", "compiler", "printer", "wast"], "scripts": {"test": "mocha"}, "author": {"name": "<PERSON>"}, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/wast-parser": "1.9.0", "@xtuc/long": "4.2.2"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.9.0"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "gitHead": "0440b420888c1f7701eb9762ec657775506b87d8", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@webassemblyjs-wast-printer-1.9.0-4935d54c85fef637b00ce9f52377451d00d47899-integrity\\node_modules\\@webassemblyjs\\wast-printer\\package.json", "readmeFilename": "README.md", "readme": "# @webassemblyjs/wast-parser\n\n> WebAssembly text format printer\n\n## Installation\n\n```sh\nyarn add @webassemblyjs/wast-printer\n```\n\n## Usage\n\n```js\nimport { print } from \"@webassemblyjs/wast-printer\"\n\nconsole.log(print(ast));\n```\n", "licenseText": "MIT License\n\nCopyright (c) 2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@webassemblyjs/wast-printer/-/wast-printer-1.9.0.tgz#4935d54c85fef637b00ce9f52377451d00d47899", "type": "tarball", "reference": "https://registry.yarnpkg.com/@webassemblyjs/wast-printer/-/wast-printer-1.9.0.tgz", "hash": "4935d54c85fef637b00ce9f52377451d00d47899", "integrity": "sha512-2J0nE95rHXHyQ24cWjMKJ1tqB/ds8z/cyeOZxJhcb+rW+SQASVjuznUSmdz5GpVJTzU8JkhYut0D3siFDD6wsA==", "registry": "npm", "packageName": "@webassemblyjs/wast-printer", "cacheIntegrity": "sha512-2J0nE95rHXHyQ24cWjMKJ1tqB/ds8z/cyeOZxJhcb+rW+SQASVjuznUSmdz5GpVJTzU8JkhYut0D3siFDD6wsA== sha1-STXVTIX+9jewDOn1I3dFHQDUeJk="}, "registry": "npm", "hash": "4935d54c85fef637b00ce9f52377451d00d47899"}