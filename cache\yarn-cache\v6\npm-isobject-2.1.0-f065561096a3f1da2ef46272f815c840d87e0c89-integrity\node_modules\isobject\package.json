{"name": "isobject", "description": "Returns true if the value is an object and not an array or null.", "version": "2.1.0", "homepage": "https://github.com/jonschlinkert/isobject", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/isobject", "bugs": {"url": "https://github.com/jonschlinkert/isobject/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"isarray": "1.0.0"}, "devDependencies": {"gulp-format-md": "^0.1.9", "mocha": "^2.4.5"}, "keywords": ["check", "is", "is-object", "isobject", "kind", "kind-of", "kindof", "native", "object", "type", "typeof", "value"], "verb": {"related": {"list": ["merge-deep", "extend-shallow", "is-plain-object", "kind-of"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}}