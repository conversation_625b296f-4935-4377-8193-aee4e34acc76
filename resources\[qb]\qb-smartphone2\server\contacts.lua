local QBCore = exports['qb-core']:GetCoreObject()

-- Callback pro získ<PERSON>í kontaktů
QBCore.Functions.CreateCallback('qb-smartphone2:server:getContacts', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb({}) return end
    
    local citizenid = Player.PlayerData.citizenid
    
    MySQL.Async.fetchAll('SELECT * FROM phone_contacts WHERE citizenid = ? ORDER BY name ASC', {citizenid}, function(result)
        cb(result or {})
    end)
end)

-- Event pro přidání kontaktu
RegisterNetEvent('qb-smartphone2:server:addContact', function(contactData)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Kontrola limitu kontaktů
    MySQL.Async.fetchAll('SELECT COUNT(*) as count FROM phone_contacts WHERE citizenid = ?', {citizenid}, function(result)
        local contactCount = result[1].count
        
        if contactCount >= Config.MaxContacts then
            TriggerClientEvent('QBCore:Notify', src, '<PERSON><PERSON><PERSON><PERSON> jsi maximálního počtu kontaktů!', 'error')
            return
        end
        
        -- Kontrola, zda kontakt už neexistuje
        MySQL.Async.fetchAll('SELECT * FROM phone_contacts WHERE citizenid = ? AND number = ?', {citizenid, contactData.number}, function(existing)
            if existing[1] then
                TriggerClientEvent('QBCore:Notify', src, 'Tento kontakt už existuje!', 'error')
                return
            end
            
            -- Přidání kontaktu
            MySQL.Async.execute('INSERT INTO phone_contacts (citizenid, name, number, avatar, favorite) VALUES (?, ?, ?, ?, ?)', {
                citizenid,
                contactData.name,
                contactData.number,
                contactData.avatar or nil,
                contactData.favorite or 0
            }, function(insertId)
                if insertId then
                    TriggerClientEvent('QBCore:Notify', src, 'Kontakt byl přidán!', 'success')
                    TriggerClientEvent('qb-smartphone2:client:refreshContacts', src)
                else
                    TriggerClientEvent('QBCore:Notify', src, 'Chyba při přidávání kontaktu!', 'error')
                end
            end)
        end)
    end)
end)

-- Event pro úpravu kontaktu
RegisterNetEvent('qb-smartphone2:server:editContact', function(contactId, contactData)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Kontrola vlastnictví kontaktu
    MySQL.Async.fetchAll('SELECT * FROM phone_contacts WHERE id = ? AND citizenid = ?', {contactId, citizenid}, function(result)
        if not result[1] then
            TriggerClientEvent('QBCore:Notify', src, 'Kontakt nenalezen!', 'error')
            return
        end
        
        -- Aktualizace kontaktu
        MySQL.Async.execute('UPDATE phone_contacts SET name = ?, number = ?, avatar = ?, favorite = ? WHERE id = ? AND citizenid = ?', {
            contactData.name,
            contactData.number,
            contactData.avatar or nil,
            contactData.favorite or 0,
            contactId,
            citizenid
        }, function(affectedRows)
            if affectedRows > 0 then
                TriggerClientEvent('QBCore:Notify', src, 'Kontakt byl upraven!', 'success')
                TriggerClientEvent('qb-smartphone2:client:refreshContacts', src)
            else
                TriggerClientEvent('QBCore:Notify', src, 'Chyba při úpravě kontaktu!', 'error')
            end
        end)
    end)
end)

-- Event pro smazání kontaktu
RegisterNetEvent('qb-smartphone2:server:deleteContact', function(contactId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Kontrola vlastnictví kontaktu
    MySQL.Async.fetchAll('SELECT * FROM phone_contacts WHERE id = ? AND citizenid = ?', {contactId, citizenid}, function(result)
        if not result[1] then
            TriggerClientEvent('QBCore:Notify', src, 'Kontakt nenalezen!', 'error')
            return
        end
        
        -- Smazání kontaktu
        MySQL.Async.execute('DELETE FROM phone_contacts WHERE id = ? AND citizenid = ?', {contactId, citizenid}, function(affectedRows)
            if affectedRows > 0 then
                TriggerClientEvent('QBCore:Notify', src, 'Kontakt byl smazán!', 'success')
                TriggerClientEvent('qb-smartphone2:client:refreshContacts', src)
            else
                TriggerClientEvent('QBCore:Notify', src, 'Chyba při mazání kontaktu!', 'error')
            end
        end)
    end)
end)

-- Event pro označení/odznačení oblíbeného kontaktu
RegisterNetEvent('qb-smartphone2:server:toggleFavoriteContact', function(contactId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Získání současného stavu
    MySQL.Async.fetchAll('SELECT favorite FROM phone_contacts WHERE id = ? AND citizenid = ?', {contactId, citizenid}, function(result)
        if not result[1] then
            TriggerClientEvent('QBCore:Notify', src, 'Kontakt nenalezen!', 'error')
            return
        end
        
        local newFavoriteStatus = result[1].favorite == 1 and 0 or 1
        
        -- Aktualizace stavu oblíbeného
        MySQL.Async.execute('UPDATE phone_contacts SET favorite = ? WHERE id = ? AND citizenid = ?', {
            newFavoriteStatus, contactId, citizenid
        }, function(affectedRows)
            if affectedRows > 0 then
                local message = newFavoriteStatus == 1 and 'Kontakt přidán do oblíbených!' or 'Kontakt odebrán z oblíbených!'
                TriggerClientEvent('QBCore:Notify', src, message, 'success')
                TriggerClientEvent('qb-smartphone2:client:refreshContacts', src)
            end
        end)
    end)
end)

-- Event pro blokování/odblokování kontaktu
RegisterNetEvent('qb-smartphone2:server:toggleBlockContact', function(contactId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Získání současného stavu
    MySQL.Async.fetchAll('SELECT blocked FROM phone_contacts WHERE id = ? AND citizenid = ?', {contactId, citizenid}, function(result)
        if not result[1] then
            TriggerClientEvent('QBCore:Notify', src, 'Kontakt nenalezen!', 'error')
            return
        end
        
        local newBlockedStatus = result[1].blocked == 1 and 0 or 1
        
        -- Aktualizace stavu blokování
        MySQL.Async.execute('UPDATE phone_contacts SET blocked = ? WHERE id = ? AND citizenid = ?', {
            newBlockedStatus, contactId, citizenid
        }, function(affectedRows)
            if affectedRows > 0 then
                local message = newBlockedStatus == 1 and 'Kontakt byl zablokován!' or 'Kontakt byl odblokován!'
                TriggerClientEvent('QBCore:Notify', src, message, 'success')
                TriggerClientEvent('qb-smartphone2:client:refreshContacts', src)
            end
        end)
    end)
end)

-- Callback pro vyhledání kontaktu podle čísla
QBCore.Functions.CreateCallback('qb-smartphone2:server:getContactByNumber', function(source, cb, phoneNumber)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb(nil) return end
    
    local citizenid = Player.PlayerData.citizenid
    
    MySQL.Async.fetchAll('SELECT * FROM phone_contacts WHERE citizenid = ? AND number = ?', {citizenid, phoneNumber}, function(result)
        cb(result[1] or nil)
    end)
end)

-- Callback pro získání oblíbených kontaktů
QBCore.Functions.CreateCallback('qb-smartphone2:server:getFavoriteContacts', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb({}) return end
    
    local citizenid = Player.PlayerData.citizenid
    
    MySQL.Async.fetchAll('SELECT * FROM phone_contacts WHERE citizenid = ? AND favorite = 1 ORDER BY name ASC', {citizenid}, function(result)
        cb(result or {})
    end)
end)

-- Callback pro kontrola, zda je číslo blokované
QBCore.Functions.CreateCallback('qb-smartphone2:server:isNumberBlocked', function(source, cb, phoneNumber)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb(false) return end
    
    local citizenid = Player.PlayerData.citizenid
    
    MySQL.Async.fetchAll('SELECT blocked FROM phone_contacts WHERE citizenid = ? AND number = ?', {citizenid, phoneNumber}, function(result)
        if result[1] then
            cb(result[1].blocked == 1)
        else
            cb(false)
        end
    end)
end)

-- Funkce pro import kontaktů ze SIM karty (volitelné)
RegisterNetEvent('qb-smartphone2:server:importContacts', function(contacts)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    local importedCount = 0
    
    for _, contact in ipairs(contacts) do
        -- Kontrola, zda kontakt už neexistuje
        MySQL.Async.fetchAll('SELECT * FROM phone_contacts WHERE citizenid = ? AND number = ?', {citizenid, contact.number}, function(existing)
            if not existing[1] then
                MySQL.Async.execute('INSERT INTO phone_contacts (citizenid, name, number) VALUES (?, ?, ?)', {
                    citizenid,
                    contact.name,
                    contact.number
                }, function(insertId)
                    if insertId then
                        importedCount = importedCount + 1
                    end
                end)
            end
        end)
    end
    
    -- Počkej a pošli výsledek
    SetTimeout(2000, function()
        TriggerClientEvent('QBCore:Notify', src, 'Importováno ' .. importedCount .. ' kontaktů!', 'success')
        TriggerClientEvent('qb-smartphone2:client:refreshContacts', src)
    end)
end)

-- Export funkce pro jiné scripty
exports('GetPlayerContacts', function(source)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return {} end
    
    local citizenid = Player.PlayerData.citizenid
    local p = promise.new()
    
    MySQL.Async.fetchAll('SELECT * FROM phone_contacts WHERE citizenid = ? ORDER BY name ASC', {citizenid}, function(result)
        p:resolve(result or {})
    end)
    
    return Citizen.Await(p)
end)

exports('AddContactForPlayer', function(source, contactData)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return false end
    
    local citizenid = Player.PlayerData.citizenid
    local p = promise.new()
    
    MySQL.Async.execute('INSERT INTO phone_contacts (citizenid, name, number, avatar) VALUES (?, ?, ?, ?)', {
        citizenid,
        contactData.name,
        contactData.number,
        contactData.avatar or nil
    }, function(insertId)
        p:resolve(insertId ~= nil)
    end)
    
    return Citizen.Await(p)
end)
