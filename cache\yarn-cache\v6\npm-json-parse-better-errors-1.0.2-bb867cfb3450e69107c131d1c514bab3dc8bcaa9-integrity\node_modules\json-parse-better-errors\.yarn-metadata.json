{"manifest": {"name": "json-parse-better-errors", "version": "1.0.2", "description": "JSON.parse with context information on error", "main": "index.js", "files": ["*.js"], "scripts": {"prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "pretest": "standard", "release": "standard-version -s", "test": "tap -J --coverage test/*.js", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "repository": {"type": "git", "url": "https://github.com/zkat/json-parse-better-errors"}, "keywords": ["JSON", "parser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"nyc": "^10.3.2", "standard": "^9.0.2", "standard-version": "^4.1.0", "tap": "^10.3.3", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8"}, "config": {"nyc": {"exclude": ["node_modules/**", "test/**"]}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-json-parse-better-errors-1.0.2-bb867cfb3450e69107c131d1c514bab3dc8bcaa9-integrity\\node_modules\\json-parse-better-errors\\package.json", "readmeFilename": "README.md", "readme": "# json-parse-better-errors [![npm version](https://img.shields.io/npm/v/json-parse-better-errors.svg)](https://npm.im/json-parse-better-errors) [![license](https://img.shields.io/npm/l/json-parse-better-errors.svg)](https://npm.im/json-parse-better-errors) [![Travis](https://img.shields.io/travis/zkat/json-parse-better-errors.svg)](https://travis-ci.org/zkat/json-parse-better-errors) [![AppVeyor](https://ci.appveyor.com/api/projects/status/github/zkat/json-parse-better-errors?svg=true)](https://ci.appveyor.com/project/zkat/json-parse-better-errors) [![Coverage Status](https://coveralls.io/repos/github/zkat/json-parse-better-errors/badge.svg?branch=latest)](https://coveralls.io/github/zkat/json-parse-better-errors?branch=latest)\n\n[`json-parse-better-errors`](https://github.com/zkat/json-parse-better-errors) is a Node.js library for\ngetting nicer errors out of `JSON.parse()`, including context and position of the parse errors.\n\n## Install\n\n`$ npm install --save json-parse-better-errors`\n\n## Table of Contents\n\n* [Example](#example)\n* [Features](#features)\n* [Contributing](#contributing)\n* [API](#api)\n  * [`parse`](#parse)\n\n### Example\n\n```javascript\nconst parseJson = require('json-parse-better-errors')\n\nparseJson('\"foo\"')\nparseJson('garbage') // more useful error message\n```\n\n### Features\n\n* Like JSON.parse, but the errors are better.\n\n### Contributing\n\nThe npm team enthusiastically welcomes contributions and project participation! There's a bunch of things you can do if you want to contribute! The [Contributor Guide](CONTRIBUTING.md) has all the information you need for everything from reporting bugs to contributing entire new features. Please don't hesitate to jump in if you'd like to, or even ask us questions if something isn't clear.\n\nAll participants and maintainers in this project are expected to follow [Code of Conduct](CODE_OF_CONDUCT.md), and just generally be excellent to each other.\n\nPlease refer to the [Changelog](CHANGELOG.md) for project history details, too.\n\nHappy hacking!\n\n### API\n\n#### <a name=\"parse\"></a> `> parse(txt, ?reviver, ?context=20)`\n\nWorks just like `JSON.parse`, but will include a bit more information when an\nerror happens.\n", "licenseText": "Copyright 2017 Kat Marchán\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9", "type": "tarball", "reference": "https://registry.yarnpkg.com/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz", "hash": "bb867cfb3450e69107c131d1c514bab3dc8bcaa9", "integrity": "sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==", "registry": "npm", "packageName": "json-parse-better-errors", "cacheIntegrity": "sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw== sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk="}, "registry": "npm", "hash": "bb867cfb3450e69107c131d1c514bab3dc8bcaa9"}