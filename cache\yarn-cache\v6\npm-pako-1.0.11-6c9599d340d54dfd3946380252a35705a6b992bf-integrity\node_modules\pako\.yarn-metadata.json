{"manifest": {"name": "pako", "description": "zlib port to javascript - fast, modularized, with browser support", "version": "1.0.11", "keywords": ["zlib", "deflate", "inflate", "gzip"], "homepage": "https://github.com/nodeca/pako", "contributors": [{"name": "<PERSON>", "url": "https://github.com/andr83"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/puzrin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/dignifiedquire"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Kirill89"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}], "files": ["index.js", "dist/", "lib/"], "license": "(MIT AND Zlib)", "repository": {"type": "git", "url": "https://github.com/nodeca/pako.git"}, "scripts": {"test": "make test"}, "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "browserify": "^16.2.3", "buffer-from": "^1.1.1", "eslint": "^5.9.0", "istanbul": "^0.4.5", "mocha": "^5.2.0", "multiparty": "^4.1.3", "ndoc": "^5.0.1", "uglify-js": "=3.4.8", "zlibjs": "^0.3.1"}, "dependencies": {}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-pako-1.0.11-6c9599d340d54dfd3946380252a35705a6b992bf-integrity\\node_modules\\pako\\package.json", "readmeFilename": "README.md", "readme": "pako\n==========================================\n\n[![Build Status](https://travis-ci.org/nodeca/pako.svg?branch=master)](https://travis-ci.org/nodeca/pako)\n[![NPM version](https://img.shields.io/npm/v/pako.svg)](https://www.npmjs.org/package/pako)\n\n> zlib port to javascript, very fast!\n\n__Why pako is cool:__\n\n- Almost as fast in modern JS engines as C implementation (see benchmarks).\n- Works in browsers, you can browserify any separate component.\n- Chunking support for big blobs.\n- Results are binary equal to well known [zlib](http://www.zlib.net/) (now contains ported zlib v1.2.8).\n\nThis project was done to understand how fast JS can be and is it necessary to\ndevelop native C modules for CPU-intensive tasks. Enjoy the result!\n\n\n__Famous projects, using pako:__\n\n- [browserify](http://browserify.org/) (via [browserify-zlib](https://github.com/devongovett/browserify-zlib))\n- [JSZip](http://stuk.github.io/jszip/)\n- [mincer](https://github.com/nodeca/mincer)\n- [JS-Git](https://github.com/creationix/js-git) and\n  [Tedit](https://chrome.google.com/webstore/detail/tedit-development-environ/ooekdijbnbbjdfjocaiflnjgoohnblgf)\n  by [@creationix](https://github.com/creationix)\n\n\n__Benchmarks:__\n\n```\nnode v0.10.26, 1mb sample:\n\n   deflate-dankogai x 4.73 ops/sec ±0.82% (15 runs sampled)\n   deflate-gildas x 4.58 ops/sec ±2.33% (15 runs sampled)\n   deflate-imaya x 3.22 ops/sec ±3.95% (12 runs sampled)\n ! deflate-pako x 6.99 ops/sec ±0.51% (21 runs sampled)\n   deflate-pako-string x 5.89 ops/sec ±0.77% (18 runs sampled)\n   deflate-pako-untyped x 4.39 ops/sec ±1.58% (14 runs sampled)\n * deflate-zlib x 14.71 ops/sec ±4.23% (59 runs sampled)\n   inflate-dankogai x 32.16 ops/sec ±0.13% (56 runs sampled)\n   inflate-imaya x 30.35 ops/sec ±0.92% (53 runs sampled)\n ! inflate-pako x 69.89 ops/sec ±1.46% (71 runs sampled)\n   inflate-pako-string x 19.22 ops/sec ±1.86% (49 runs sampled)\n   inflate-pako-untyped x 17.19 ops/sec ±0.85% (32 runs sampled)\n * inflate-zlib x 70.03 ops/sec ±1.64% (81 runs sampled)\n\nnode v0.11.12, 1mb sample:\n\n   deflate-dankogai x 5.60 ops/sec ±0.49% (17 runs sampled)\n   deflate-gildas x 5.06 ops/sec ±6.00% (16 runs sampled)\n   deflate-imaya x 3.52 ops/sec ±3.71% (13 runs sampled)\n ! deflate-pako x 11.52 ops/sec ±0.22% (32 runs sampled)\n   deflate-pako-string x 9.53 ops/sec ±1.12% (27 runs sampled)\n   deflate-pako-untyped x 5.44 ops/sec ±0.72% (17 runs sampled)\n * deflate-zlib x 14.05 ops/sec ±3.34% (63 runs sampled)\n   inflate-dankogai x 42.19 ops/sec ±0.09% (56 runs sampled)\n   inflate-imaya x 79.68 ops/sec ±1.07% (68 runs sampled)\n ! inflate-pako x 97.52 ops/sec ±0.83% (80 runs sampled)\n   inflate-pako-string x 45.19 ops/sec ±1.69% (57 runs sampled)\n   inflate-pako-untyped x 24.35 ops/sec ±2.59% (40 runs sampled)\n * inflate-zlib x 60.32 ops/sec ±1.36% (69 runs sampled)\n```\n\nzlib's test is partially affected by marshalling (that make sense for inflate only).\nYou can change deflate level to 0 in benchmark source, to investigate details.\nFor deflate level 6 results can be considered as correct.\n\n__Install:__\n\nnode.js:\n\n```\nnpm install pako\n```\n\nbrowser:\n\n```\nbower install pako\n```\n\n\nExample & API\n-------------\n\nFull docs - http://nodeca.github.io/pako/\n\n```javascript\nvar pako = require('pako');\n\n// Deflate\n//\nvar input = new Uint8Array();\n//... fill input data here\nvar output = pako.deflate(input);\n\n// Inflate (simple wrapper can throw exception on broken stream)\n//\nvar compressed = new Uint8Array();\n//... fill data to uncompress here\ntry {\n  var result = pako.inflate(compressed);\n} catch (err) {\n  console.log(err);\n}\n\n//\n// Alternate interface for chunking & without exceptions\n//\n\nvar inflator = new pako.Inflate();\n\ninflator.push(chunk1, false);\ninflator.push(chunk2, false);\n...\ninflator.push(chunkN, true); // true -> last chunk\n\nif (inflator.err) {\n  console.log(inflator.msg);\n}\n\nvar output = inflator.result;\n\n```\n\nSometime you can wish to work with strings. For example, to send\nbig objects as json to server. Pako detects input data type. You can\nforce output to be string with option `{ to: 'string' }`.\n\n```javascript\nvar pako = require('pako');\n\nvar test = { my: 'super', puper: [456, 567], awesome: 'pako' };\n\nvar binaryString = pako.deflate(JSON.stringify(test), { to: 'string' });\n\n//\n// Here you can do base64 encode, make xhr requests and so on.\n//\n\nvar restored = JSON.parse(pako.inflate(binaryString, { to: 'string' }));\n```\n\n\nNotes\n-----\n\nPako does not contain some specific zlib functions:\n\n- __deflate__ -  methods `deflateCopy`, `deflateBound`, `deflateParams`,\n  `deflatePending`, `deflatePrime`, `deflateTune`.\n- __inflate__ - methods `inflateCopy`, `inflateMark`,\n  `inflatePrime`, `inflateGetDictionary`, `inflateSync`, `inflateSyncPoint`, `inflateUndermine`.\n- High level inflate/deflate wrappers (classes) may not support some flush\n  modes. Those should work: Z_NO_FLUSH, Z_FINISH, Z_SYNC_FLUSH.\n\n\npako for enterprise\n-------------------\n\nAvailable as part of the Tidelift Subscription\n\nThe maintainers of pako and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-pako?utm_source=npm-pako&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n\n\nAuthors\n-------\n\n- Andrey Tupitsin [@anrd83](https://github.com/andr83)\n- Vitaly Puzrin [@puzrin](https://github.com/puzrin)\n\nPersonal thanks to:\n\n- Vyacheslav Egorov ([@mraleph](https://github.com/mraleph)) for his awesome\n  tutorials about optimising JS code for v8, [IRHydra](http://mrale.ph/irhydra/)\n  tool and his advices.\n- David Duponchel ([@dduponchel](https://github.com/dduponchel)) for help with\n  testing.\n\nOriginal implementation (in C):\n\n- [zlib](http://zlib.net/) by Jean-loup Gailly and Mark Adler.\n\n\nLicense\n-------\n\n- MIT - all files, except `/lib/zlib` folder\n- ZLIB - `/lib/zlib` content\n", "licenseText": "(The MIT License)\n\nCopyright (C) 2014-2017 by <PERSON><PERSON> and <PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/pako/-/pako-1.0.11.tgz#6c9599d340d54dfd3946380252a35705a6b992bf", "type": "tarball", "reference": "https://registry.yarnpkg.com/pako/-/pako-1.0.11.tgz", "hash": "6c9599d340d54dfd3946380252a35705a6b992bf", "integrity": "sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==", "registry": "npm", "packageName": "pako", "cacheIntegrity": "sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw== sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8="}, "registry": "npm", "hash": "6c9599d340d54dfd3946380252a35705a6b992bf"}