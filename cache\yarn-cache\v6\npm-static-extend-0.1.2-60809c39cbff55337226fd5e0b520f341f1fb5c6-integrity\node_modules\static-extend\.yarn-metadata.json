{"manifest": {"name": "static-extend", "description": "Adds a static `extend` method to a class, to simplify inheritance. Extends the static properties, prototype properties, and descriptors from a `Parent` constructor onto `Child` constructors.", "version": "0.1.2", "homepage": "https://github.com/jonschlinkert/static-extend", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/static-extend.git"}, "bugs": {"url": "https://github.com/jonschlinkert/static-extend/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "devDependencies": {"gulp-format-md": "^0.1.9", "mocha": "^2.5.3"}, "keywords": ["class", "ctor", "descriptor", "extend", "extends", "inherit", "inheritance", "merge", "method", "prop", "properties", "property", "prototype"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "reflinks": ["verb", "verb-readme-generator"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-static-extend-0.1.2-60809c39cbff55337226fd5e0b520f341f1fb5c6-integrity\\node_modules\\static-extend\\package.json", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/static-extend/-/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6", "type": "tarball", "reference": "https://registry.yarnpkg.com/static-extend/-/static-extend-0.1.2.tgz", "hash": "60809c39cbff55337226fd5e0b520f341f1fb5c6", "integrity": "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=", "registry": "npm", "packageName": "static-extend", "cacheIntegrity": "sha512-72E9+uLc27Mt718pMHt9VMNiAL4LMsmDbBva8mxWUCkT07fSzEGMYUCk0XWY6lp0j6RBAG4cJ3mWuZv2OE3s0g== sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY="}, "registry": "npm", "hash": "60809c39cbff55337226fd5e0b520f341f1fb5c6"}