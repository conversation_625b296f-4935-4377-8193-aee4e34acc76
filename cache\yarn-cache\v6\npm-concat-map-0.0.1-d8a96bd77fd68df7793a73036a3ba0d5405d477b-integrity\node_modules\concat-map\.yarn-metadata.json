{"manifest": {"name": "concat-map", "description": "concatenative mapdashery", "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/substack/node-concat-map.git"}, "main": "index.js", "keywords": ["concat", "concatMap", "map", "functional", "higher-order"], "scripts": {"test": "tape test/*.js"}, "devDependencies": {"tape": "~2.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-concat-map-0.0.1-d8a96bd77fd68df7793a73036a3ba0d5405d477b-integrity\\node_modules\\concat-map\\package.json", "readmeFilename": "README.markdown", "readme": "concat-map\n==========\n\nConcatenative mapdashery.\n\n[![browser support](http://ci.testling.com/substack/node-concat-map.png)](http://ci.testling.com/substack/node-concat-map)\n\n[![build status](https://secure.travis-ci.org/substack/node-concat-map.png)](http://travis-ci.org/substack/node-concat-map)\n\nexample\n=======\n\n``` js\nvar concatMap = require('concat-map');\nvar xs = [ 1, 2, 3, 4, 5, 6 ];\nvar ys = concatMap(xs, function (x) {\n    return x % 2 ? [ x - 0.1, x, x + 0.1 ] : [];\n});\nconsole.dir(ys);\n```\n\n***\n\n```\n[ 0.9, 1, 1.1, 2.9, 3, 3.1, 4.9, 5, 5.1 ]\n```\n\nmethods\n=======\n\n``` js\nvar concatMap = require('concat-map')\n```\n\nconcatMap(xs, fn)\n-----------------\n\nReturn an array of concatenated elements by calling `fn(x, i)` for each element\n`x` and each index `i` in the array `xs`.\n\nWhen `fn(x, i)` returns an array, its result will be concatenated with the\nresult array. If `fn(x, i)` returns anything else, that value will be pushed\nonto the end of the result array.\n\ninstall\n=======\n\nWith [npm](http://npmjs.org) do:\n\n```\nnpm install concat-map\n```\n\nlicense\n=======\n\nMIT\n\nnotes\n=====\n\nThis module was written while sitting high above the ground in a tree.\n", "licenseText": "This software is released under the MIT license:\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\n<PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b", "type": "tarball", "reference": "https://registry.yarnpkg.com/concat-map/-/concat-map-0.0.1.tgz", "hash": "d8a96bd77fd68df7793a73036a3ba0d5405d477b", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "registry": "npm", "packageName": "concat-map", "cacheIntegrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg== sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="}, "registry": "npm", "hash": "d8a96bd77fd68df7793a73036a3ba0d5405d477b"}