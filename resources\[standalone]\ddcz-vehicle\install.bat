@echo off
echo ================================================
echo DDCZ-VEHICLE Installation Script
echo ================================================
echo.

echo [1/4] Checking files...
if not exist "fxmanifest.lua" (
    echo ERROR: fxmanifest.lua not found!
    pause
    exit /b 1
)

if not exist "config.lua" (
    echo ERROR: config.lua not found!
    pause
    exit /b 1
)

if not exist "client\main.lua" (
    echo ERROR: client\main.lua not found!
    pause
    exit /b 1
)

if not exist "server\main.lua" (
    echo ERROR: server\main.lua not found!
    pause
    exit /b 1
)

if not exist "nui\index.html" (
    echo ERROR: nui\index.html not found!
    pause
    exit /b 1
)

echo [2/4] Files check completed successfully!
echo.

echo [3/4] Installation instructions:
echo.
echo 1. Add "ensure ddcz-vehicle" to your server.cfg
echo 2. Make sure Q<PERSON><PERSON> is installed and running
echo 3. Configure jobs in config.lua if needed
echo 4. Restart your server
echo.

echo [4/4] Configuration options:
echo.
echo Default keybind: U (changeable in config.lua)
echo Commands: /vehicle, /vehiclemenu
echo Admin: /vehicleadmin reload, /vehicleadmin reset [id]
echo.

echo Restricted jobs (extras/livery access):
echo - police
echo - ambulance  
echo - fire
echo.

echo ================================================
echo Installation completed!
echo ================================================
echo.
echo Features:
echo - Modern animated vehicle control menu
echo - Door, light, and engine controls
echo - Extras and livery for police/EMS
echo - Real-time synchronization between players
echo - Fully configurable settings
echo.
echo Enjoy your new vehicle control system!
echo.
pause
