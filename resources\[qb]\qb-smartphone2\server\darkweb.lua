local QBCore = exports['qb-core']:GetCoreObject()

-- Globální proměnné pro DarkWeb
local DarkWebUsers = {}
local DarkWebItems = {
    {
        name = 'weapon_pistol',
        label = 'Pistole',
        price = 5000,
        currency = 'crypto',
        category = 'weapons',
        description = 'Neregistrovaná pistole',
        seller = 'GunDealer_001'
    },
    {
        name = 'weapon_knife',
        label = 'Nůž',
        price = 500,
        currency = 'crypto',
        category = 'weapons',
        description = 'Ostrý nůž',
        seller = 'BladeRunner'
    },
    {
        name = 'lockpick',
        label = 'Paklíč',
        price = 200,
        currency = 'crypto',
        category = 'tools',
        description = 'Pro otevírání zámků',
        seller = 'LockMaster'
    },
    {
        name = 'phone_hack_device',
        label = 'Hacking Device',
        price = 2500,
        currency = 'crypto',
        category = 'electronics',
        description = 'Pro hackování telefonů',
        seller = 'CyberGhost'
    },
    {
        name = 'fake_id',
        label = 'Falešné ID',
        price = 1500,
        currency = 'crypto',
        category = 'documents',
        description = 'Falešná identita',
        seller = 'IDForger'
    }
}

-- Callback pro kontrolu přístupu k DarkWeb
QBCore.Functions.CreateCallback('qb-smartphone2:server:checkDarkWebAccess', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb(false) return end
    
    local hasUSB = Player.Functions.GetItemByName(Config.DarkWebItem)
    cb(hasUSB ~= nil)
end)

-- Callback pro získání DarkWeb itemů
QBCore.Functions.CreateCallback('qb-smartphone2:server:getDarkWebItems', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb({}) return end
    
    local hasUSB = Player.Functions.GetItemByName(Config.DarkWebItem)
    if not hasUSB then
        cb({})
        return
    end
    
    -- Přidej dynamické itemy z databáze
    MySQL.Async.fetchAll('SELECT * FROM phone_darkweb WHERE status = "available" ORDER BY created_at DESC', {}, function(result)
        local allItems = {}
        
        -- Přidej statické itemy
        for _, item in ipairs(DarkWebItems) do
            table.insert(allItems, item)
        end
        
        -- Přidej dynamické itemy
        for _, dbItem in ipairs(result or {}) do
            table.insert(allItems, {
                id = dbItem.id,
                name = dbItem.item_name,
                label = dbItem.item_label,
                price = dbItem.price,
                currency = dbItem.currency,
                seller = dbItem.seller,
                category = 'user_items',
                description = 'Prodáváno hráčem'
            })
        end
        
        cb(allItems)
    end)
end)

-- Event pro nákup DarkWeb itemu
RegisterNetEvent('qb-smartphone2:server:buyDarkWebItem', function(itemData)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local hasUSB = Player.Functions.GetItemByName(Config.DarkWebItem)
    if not hasUSB then
        TriggerClientEvent('QBCore:Notify', src, 'Nemáš přístup k DarkWeb!', 'error')
        return
    end
    
    -- Kontrola měny
    local playerMoney = Player.PlayerData.money[itemData.currency] or 0
    if playerMoney < itemData.price then
        TriggerClientEvent('QBCore:Notify', src, 'Nemáš dostatek ' .. itemData.currency .. '!', 'error')
        return
    end
    
    -- Odebrání peněz
    Player.Functions.RemoveMoney(itemData.currency, itemData.price, 'darkweb-purchase')
    
    -- Přidání itemu
    Player.Functions.AddItem(itemData.name, 1, false, {
        description = 'Zakoupeno na DarkWeb od ' .. itemData.seller
    })
    
    -- Zaznamenání transakce
    MySQL.Async.execute('INSERT INTO phone_darkweb (citizenid, item_name, item_label, price, currency, seller, status) VALUES (?, ?, ?, ?, ?, ?, ?)', {
        Player.PlayerData.citizenid,
        itemData.name,
        itemData.label,
        itemData.price,
        itemData.currency,
        itemData.seller,
        'sold'
    })
    
    TriggerClientEvent('QBCore:Notify', src, 'Zakoupil jsi ' .. itemData.label .. ' za ' .. itemData.price .. ' ' .. itemData.currency, 'success')
    
    -- Pokud je to dynamický item, označ jako prodaný
    if itemData.id then
        MySQL.Async.execute('UPDATE phone_darkweb SET status = "sold" WHERE id = ?', {itemData.id})
    end
end)

-- Event pro prodej itemu na DarkWeb
RegisterNetEvent('qb-smartphone2:server:sellItemOnDarkWeb', function(sellData)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local hasUSB = Player.Functions.GetItemByName(Config.DarkWebItem)
    if not hasUSB then
        TriggerClientEvent('QBCore:Notify', src, 'Nemáš přístup k DarkWeb!', 'error')
        return
    end
    
    -- Kontrola, zda má hráč item
    local hasItem = Player.Functions.GetItemByName(sellData.itemName)
    if not hasItem or hasItem.amount < sellData.quantity then
        TriggerClientEvent('QBCore:Notify', src, 'Nemáš tento item!', 'error')
        return
    end
    
    -- Odebrání itemu
    Player.Functions.RemoveItem(sellData.itemName, sellData.quantity)
    
    -- Přidání na DarkWeb
    MySQL.Async.execute('INSERT INTO phone_darkweb (citizenid, item_name, item_label, price, currency, seller, status) VALUES (?, ?, ?, ?, ?, ?, ?)', {
        Player.PlayerData.citizenid,
        sellData.itemName,
        sellData.itemLabel,
        sellData.price,
        sellData.currency or 'crypto',
        GenerateDarkWebAlias(),
        'available'
    })
    
    TriggerClientEvent('QBCore:Notify', src, 'Item byl přidán na DarkWeb!', 'success')
end)

-- Callback pro získání DarkWeb chatu
QBCore.Functions.CreateCallback('qb-smartphone2:server:getDarkWebChat', function(source, cb, room)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb({}) return end
    
    local hasUSB = Player.Functions.GetItemByName(Config.DarkWebItem)
    if not hasUSB then
        cb({})
        return
    end
    
    MySQL.Async.fetchAll('SELECT * FROM phone_darkweb_chat WHERE room = ? ORDER BY created_at DESC LIMIT 50', {room or 'general'}, function(result)
        cb(result or {})
    end)
end)

-- Event pro odeslání zprávy do DarkWeb chatu
RegisterNetEvent('qb-smartphone2:server:sendDarkWebMessage', function(messageData)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local hasUSB = Player.Functions.GetItemByName(Config.DarkWebItem)
    if not hasUSB then
        TriggerClientEvent('QBCore:Notify', src, 'Nemáš přístup k DarkWeb!', 'error')
        return
    end
    
    local citizenid = Player.PlayerData.citizenid
    local alias = GetOrCreateDarkWebAlias(citizenid)
    
    -- Uložení zprávy
    MySQL.Async.execute('INSERT INTO phone_darkweb_chat (sender_id, sender_alias, message, room) VALUES (?, ?, ?, ?)', {
        citizenid,
        alias,
        messageData.message,
        messageData.room or 'general'
    }, function(insertId)
        if insertId then
            -- Odeslání zprávy všem online hráčům s přístupem
            local players = QBCore.Functions.GetQBPlayers()
            for _, player in pairs(players) do
                local hasAccess = player.Functions.GetItemByName(Config.DarkWebItem)
                if hasAccess then
                    TriggerClientEvent('qb-smartphone2:client:receiveDarkWebMessage', player.PlayerData.source, {
                        id = insertId,
                        sender_alias = alias,
                        message = messageData.message,
                        room = messageData.room or 'general',
                        created_at = os.date('%Y-%m-%d %H:%M:%S')
                    })
                end
            end
        end
    end)
end)

-- Event pro hackování telefonu
RegisterNetEvent('qb-smartphone2:server:hackPhone', function(targetNumber)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local hasHackDevice = Player.Functions.GetItemByName('phone_hack_device')
    if not hasHackDevice then
        TriggerClientEvent('QBCore:Notify', src, 'Nemáš hacking device!', 'error')
        return
    end
    
    local TargetPlayer = QBCore.Functions.GetPlayerByPhone(targetNumber)
    if not TargetPlayer then
        TriggerClientEvent('QBCore:Notify', src, 'Cílové číslo neexistuje!', 'error')
        return
    end
    
    -- Spuštění mini-hry pro hackování
    TriggerClientEvent('qb-smartphone2:client:startHackingMinigame', src, {
        targetNumber = targetNumber,
        difficulty = math.random(3, 7)
    })
end)

-- Event pro úspěšné hackování
RegisterNetEvent('qb-smartphone2:server:hackingSuccess', function(targetNumber)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local TargetPlayer = QBCore.Functions.GetPlayerByPhone(targetNumber)
    if not TargetPlayer then return end
    
    -- Získání dat z telefonu cíle
    local targetCitizenid = TargetPlayer.PlayerData.citizenid
    
    -- Získání kontaktů
    MySQL.Async.fetchAll('SELECT * FROM phone_contacts WHERE citizenid = ? LIMIT 10', {targetCitizenid}, function(contacts)
        -- Získání zpráv
        MySQL.Async.fetchAll('SELECT * FROM phone_messages WHERE sender = ? OR receiver = ? ORDER BY created_at DESC LIMIT 20', {
            targetNumber, targetNumber
        }, function(messages)
            -- Odeslání dat hackerovi
            TriggerClientEvent('qb-smartphone2:client:receiveHackedData', src, {
                targetNumber = targetNumber,
                contacts = contacts or {},
                messages = messages or {}
            })
            
            TriggerClientEvent('QBCore:Notify', src, 'Hackování úspěšné! Data získána.', 'success')
            
            -- Oznámení cíli (volitelné)
            if math.random(1, 100) <= 30 then -- 30% šance na detekci
                TriggerClientEvent('QBCore:Notify', TargetPlayer.PlayerData.source, 'Tvůj telefon byl možná hacknut!', 'error')
            end
        end)
    end)
end)

-- Funkce pro generování DarkWeb aliasu
function GenerateDarkWebAlias()
    local prefixes = {'Dark', 'Shadow', 'Ghost', 'Phantom', 'Cyber', 'Neo', 'Zero', 'Black', 'Silent', 'Hidden'}
    local suffixes = {'Wolf', 'Hawk', 'Viper', 'Raven', 'Fox', 'Spider', 'Reaper', 'Hunter', 'Blade', 'Storm'}
    
    local prefix = prefixes[math.random(#prefixes)]
    local suffix = suffixes[math.random(#suffixes)]
    local number = math.random(100, 999)
    
    return prefix .. suffix .. number
end

-- Funkce pro získání nebo vytvoření aliasu
function GetOrCreateDarkWebAlias(citizenid)
    if DarkWebUsers[citizenid] then
        return DarkWebUsers[citizenid]
    end
    
    local alias = GenerateDarkWebAlias()
    DarkWebUsers[citizenid] = alias
    return alias
end

-- Export funkce pro jiné scripty
exports('HasDarkWebAccess', function(source)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return false end
    
    local hasUSB = Player.Functions.GetItemByName(Config.DarkWebItem)
    return hasUSB ~= nil
end)

exports('AddDarkWebItem', function(itemData)
    table.insert(DarkWebItems, itemData)
end)

exports('RemoveDarkWebItem', function(itemName)
    for i, item in ipairs(DarkWebItems) do
        if item.name == itemName then
            table.remove(DarkWebItems, i)
            break
        end
    end
end)
