{"manifest": {"name": "acorn-dynamic-import", "description": "Support dynamic imports in acorn", "main": "lib/index.js", "homepage": "https://github.com/kesne/acorn-dynamic-import", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/kesne/acorn-dynamic-import"}, "license": "MIT", "scripts": {"build": "babel src --out-dir lib", "test": "npm run lint && npm run tests-only", "lint": "eslint .", "tests-only": "mocha", "prepublish": "in-publish && safe-publish-latest && npm run build || not-in-publish", "check-changelog": "expr $(git status --porcelain 2>/dev/null| grep \"^\\s*M.*CHANGELOG.md\" | wc -l) >/dev/null || (echo 'Please edit CHANGELOG.md' && exit 1)", "check-only-changelog-changed": "(expr $(git status --porcelain 2>/dev/null| grep -v \"CHANGELOG.md\" | wc -l) >/dev/null && echo 'Only CHANGELOG.md may have uncommitted changes' && exit 1) || exit 0", "version:major": "npm --no-git-tag-version version major", "version:minor": "npm --no-git-tag-version version minor", "version:patch": "npm --no-git-tag-version version patch", "postversion": "git commit package.json CHANGELOG.md -m \"v$npm_package_version\" && npm run tag && git push && git push --tags", "preversion": "npm run test && npm run check-changelog && npm run check-only-changelog-changed", "tag": "git tag v$npm_package_version"}, "dependencies": {"acorn": "^5.0.0"}, "devDependencies": {"babel-cli": "^6.18.0", "babel-eslint": "^7.1.1", "babel-preset-airbnb": "^2.1.1", "babel-register": "^6.18.0", "chai": "^3.0.0", "eslint": "^3.10.2", "eslint-config-airbnb-base": "^10.0.1", "eslint-plugin-import": "^2.2.0", "in-publish": "^2.0.0", "mocha": "^2.2.5", "rimraf": "^2.5.4", "safe-publish-latest": "^1.1.1"}, "version": "3.0.0", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-acorn-dynamic-import-3.0.0-901ceee4c7faaef7e07ad2a47e890675da50a278-integrity\\node_modules\\acorn-dynamic-import\\package.json", "readmeFilename": "README.md", "readme": "# Dynamic import support in acorn\n\nThis is plugin for [Acorn](http://marijnhaverbeke.nl/acorn/) - a tiny, fast JavaScript parser, written completely in JavaScript.\n\nFor more information, check out the [proposal repo](https://github.com/tc39/proposal-dynamic-import).\n\n## Usage\n\nYou can use this module directly in order to get Acorn instance with plugin installed:\n\n```js\nimport acorn from 'acorn-dynamic-import';\n// or...\nconst acorn = require('acorn-dynamic-import').default;\n```\n\nOr you can use `inject.js` for injecting plugin into your own version of Acorn like this:\n\n```js\nconst acorn = require('acorn-dynamic-import/lib/inject').default(require('./custom-acorn'));\n```\n\nThen, use the `plugins` option whenever you need to support dynamicImport while parsing:\n\n```js\nconst ast = acorn.parse(code, {\n  plugins: { dynamicImport: true }\n});\n```\n\nTo use the updated walk functionality the process is similar. You can require the default implementation as:\n\n```js\nimport walk from 'acorn-dynamic-import/lib/walk';\n// or...\nconst dynamicImportWalk = require('acorn-dynamic-import/lib/walk').default;\n```\n\nOr you can use the injectable version for injecting the new walk functionality into your own version of Acorn like this:\n\n```js\nimport { inject } from 'acorn-dynamic-import/lib/walk';\nimport acornWalk from 'acorn/dist/walk';\n\nconst walk = inject(acornWalk);\n\n``` \n\n## License\n\nThis plugin is issued under the [MIT license](./LICENSE).\n", "licenseText": "MIT License\n\nCopyright (c) 2016 Jordan Gensler\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/acorn-dynamic-import/-/acorn-dynamic-import-3.0.0.tgz#901ceee4c7faaef7e07ad2a47e890675da50a278", "type": "tarball", "reference": "https://registry.yarnpkg.com/acorn-dynamic-import/-/acorn-dynamic-import-3.0.0.tgz", "hash": "901ceee4c7faaef7e07ad2a47e890675da50a278", "integrity": "sha512-zVWV8Z8lislJoOKKqdNMOB+s6+XV5WERty8MnKBeFgwA+19XJjJHs2RP5dzM57FftIs+jQnRToLiWazKr6sSWg==", "registry": "npm", "packageName": "acorn-dynamic-import", "cacheIntegrity": "sha512-zVWV8Z8lislJoOKKqdNMOB+s6+XV5WERty8MnKBeFgwA+19XJjJHs2RP5dzM57FftIs+jQnRToLiWazKr6sSWg== sha1-kBzu5Mf6rvfgetKkfokGddpQong="}, "registry": "npm", "hash": "901ceee4c7faaef7e07ad2a47e890675da50a278"}