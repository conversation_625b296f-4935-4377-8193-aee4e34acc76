{"manifest": {"name": "define-properties", "version": "1.1.3", "author": {"name": "<PERSON>"}, "description": "Define multiple non-enumerable properties at once. Uses `Object.defineProperty` when available; falls back to standard assignment in older engines.", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "posttest": "npm run --silent security", "tests-only": "node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run --silent jscs && npm run --silent eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "security": "nsp check"}, "repository": {"type": "git", "url": "git://github.com/ljharb/define-properties.git"}, "keywords": ["Object.defineProperty", "Object.defineProperties", "object", "property descriptor", "descriptor", "define", "ES5"], "dependencies": {"object-keys": "^1.0.12"}, "devDependencies": {"@ljharb/eslint-config": "^13.0.0", "covert": "^1.1.0", "eslint": "^5.3.0", "jscs": "^3.0.7", "nsp": "^3.2.1", "tape": "^4.9.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-define-properties-1.1.3-cf88da6cbee26fe6db7094f61d870cbd84cee9f1-integrity\\node_modules\\define-properties\\package.json", "readmeFilename": "README.md", "readme": "#define-properties <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![Build Status][travis-svg]][travis-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\n[![browser support][testling-svg]][testling-url]\n\nDefine multiple non-enumerable properties at once. Uses `Object.defineProperty` when available; falls back to standard assignment in older engines.\nExisting properties are not overridden. Accepts a map of property names to a predicate that, when true, force-overrides.\n\n## Example\n\n```js\nvar define = require('define-properties');\nvar assert = require('assert');\n\nvar obj = define({ a: 1, b: 2 }, {\n\ta: 10,\n\tb: 20,\n\tc: 30\n});\nassert(obj.a === 1);\nassert(obj.b === 2);\nassert(obj.c === 30);\nif (define.supportsDescriptors) {\n\tassert.deepEqual(Object.keys(obj), ['a', 'b']);\n\tassert.deepEqual(Object.getOwnPropertyDescriptor(obj, 'c'), {\n\t\tconfigurable: true,\n\t\tenumerable: false,\n\t\tvalue: 30,\n\t\twritable: false\n\t});\n}\n```\n\nThen, with predicates:\n```js\nvar define = require('define-properties');\nvar assert = require('assert');\n\nvar obj = define({ a: 1, b: 2, c: 3 }, {\n\ta: 10,\n\tb: 20,\n\tc: 30\n}, {\n\ta: function () { return false; },\n\tb: function () { return true; }\n});\nassert(obj.a === 1);\nassert(obj.b === 20);\nassert(obj.c === 3);\nif (define.supportsDescriptors) {\n\tassert.deepEqual(Object.keys(obj), ['a', 'c']);\n\tassert.deepEqual(Object.getOwnPropertyDescriptor(obj, 'b'), {\n\t\tconfigurable: true,\n\t\tenumerable: false,\n\t\tvalue: 20,\n\t\twritable: false\n\t});\n}\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/define-properties\n[npm-version-svg]: http://versionbadg.es/ljharb/define-properties.svg\n[travis-svg]: https://travis-ci.org/ljharb/define-properties.svg\n[travis-url]: https://travis-ci.org/ljharb/define-properties\n[deps-svg]: https://david-dm.org/ljharb/define-properties.svg\n[deps-url]: https://david-dm.org/ljharb/define-properties\n[dev-deps-svg]: https://david-dm.org/ljharb/define-properties/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/define-properties#info=devDependencies\n[testling-svg]: https://ci.testling.com/ljharb/define-properties.png\n[testling-url]: https://ci.testling.com/ljharb/define-properties\n[npm-badge-png]: https://nodei.co/npm/define-properties.png?downloads=true&stars=true\n[license-image]: http://img.shields.io/npm/l/define-properties.svg\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/define-properties.svg\n[downloads-url]: http://npm-stat.com/charts.html?package=define-properties\n\n", "licenseText": "The MIT License (MIT)\n\nCopyright (C) 2015 Jordan Harband\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/define-properties/-/define-properties-1.1.3.tgz#cf88da6cbee26fe6db7094f61d870cbd84cee9f1", "type": "tarball", "reference": "https://registry.yarnpkg.com/define-properties/-/define-properties-1.1.3.tgz", "hash": "cf88da6cbee26fe6db7094f61d870cbd84cee9f1", "integrity": "sha512-3MqfYKj2lLzdMSf8ZIZE/V+Zuy+BgD6f164e8K2w7dgnpKArBDerGYpM46IYYcjnkdPNMjPk9A6VFB8+3SKlXQ==", "registry": "npm", "packageName": "define-properties", "cacheIntegrity": "sha512-3MqfYKj2lLzdMSf8ZIZE/V+Zuy+BgD6f164e8K2w7dgnpKArBDerGYpM46IYYcjnkdPNMjPk9A6VFB8+3SKlXQ== sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE="}, "registry": "npm", "hash": "cf88da6cbee26fe6db7094f61d870cbd84cee9f1"}