{"manifest": {"name": "fast-deep-equal", "version": "3.1.3", "description": "Fast deep equal", "main": "index.js", "scripts": {"eslint": "eslint *.js benchmark/*.js spec/*.js", "build": "node build", "benchmark": "npm i && npm run build && cd ./benchmark && npm i && node ./", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "nyc npm run test-spec", "test-ts": "tsc --target ES5 --noImplicitAny index.d.ts", "test": "npm run build && npm run eslint && npm run test-ts && npm run test-cov", "prepublish": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/fast-deep-equal.git"}, "keywords": ["fast", "equal", "deep-equal"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/fast-deep-equal/issues"}, "homepage": "https://github.com/epoberezkin/fast-deep-equal#readme", "devDependencies": {"coveralls": "^3.1.0", "dot": "^1.1.2", "eslint": "^7.2.0", "mocha": "^7.2.0", "nyc": "^15.1.0", "pre-commit": "^1.2.2", "react": "^16.12.0", "react-test-renderer": "^16.12.0", "sinon": "^9.0.2", "typescript": "^3.9.5"}, "nyc": {"exclude": ["**/spec/**", "node_modules"], "reporter": ["lcov", "text-summary"]}, "files": ["index.js", "index.d.ts", "react.js", "react.d.ts", "es6/"], "types": "index.d.ts", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-fast-deep-equal-3.1.3-****************************************-integrity\\node_modules\\fast-deep-equal\\package.json", "readmeFilename": "README.md", "readme": "# fast-deep-equal\nThe fastest deep equal with ES6 Map, Set and Typed arrays support.\n\n[![Build Status](https://travis-ci.org/epoberezkin/fast-deep-equal.svg?branch=master)](https://travis-ci.org/epoberezkin/fast-deep-equal)\n[![npm](https://img.shields.io/npm/v/fast-deep-equal.svg)](https://www.npmjs.com/package/fast-deep-equal)\n[![Coverage Status](https://coveralls.io/repos/github/epoberezkin/fast-deep-equal/badge.svg?branch=master)](https://coveralls.io/github/epoberezkin/fast-deep-equal?branch=master)\n\n\n## Install\n\n```bash\nnpm install fast-deep-equal\n```\n\n\n## Features\n\n- ES5 compatible\n- works in node.js (8+) and browsers (IE9+)\n- checks equality of Date and RegExp objects by value.\n\nES6 equal (`require('fast-deep-equal/es6')`) also supports:\n- Maps\n- Sets\n- Typed arrays\n\n\n## Usage\n\n```javascript\nvar equal = require('fast-deep-equal');\nconsole.log(equal({foo: 'bar'}, {foo: 'bar'})); // true\n```\n\nTo support ES6 Maps, Sets and Typed arrays equality use:\n\n```javascript\nvar equal = require('fast-deep-equal/es6');\nconsole.log(equal(Int16Array([1, 2]), Int16Array([1, 2]))); // true\n```\n\nTo use with React (avoiding the traversal of React elements' _owner\nproperty that contains circular references and is not needed when\ncomparing the elements - borrowed from [react-fast-compare](https://github.com/FormidableLabs/react-fast-compare)):\n\n```javascript\nvar equal = require('fast-deep-equal/react');\nvar equal = require('fast-deep-equal/es6/react');\n```\n\n\n## Performance benchmark\n\nNode.js v12.6.0:\n\n```\nfast-deep-equal x 261,950 ops/sec ±0.52% (89 runs sampled)\nfast-deep-equal/es6 x 212,991 ops/sec ±0.34% (92 runs sampled)\nfast-equals x 230,957 ops/sec ±0.83% (85 runs sampled)\nnano-equal x 187,995 ops/sec ±0.53% (88 runs sampled)\nshallow-equal-fuzzy x 138,302 ops/sec ±0.49% (90 runs sampled)\nunderscore.isEqual x 74,423 ops/sec ±0.38% (89 runs sampled)\nlodash.isEqual x 36,637 ops/sec ±0.72% (90 runs sampled)\ndeep-equal x 2,310 ops/sec ±0.37% (90 runs sampled)\ndeep-eql x 35,312 ops/sec ±0.67% (91 runs sampled)\nramda.equals x 12,054 ops/sec ±0.40% (91 runs sampled)\nutil.isDeepStrictEqual x 46,440 ops/sec ±0.43% (90 runs sampled)\nassert.deepStrictEqual x 456 ops/sec ±0.71% (88 runs sampled)\n\nThe fastest is fast-deep-equal\n```\n\nTo run benchmark (requires node.js 6+):\n\n```bash\nnpm run benchmark\n```\n\n__Please note__: this benchmark runs against the available test cases. To choose the most performant library for your application, it is recommended to benchmark against your data and to NOT expect this benchmark to reflect the performance difference in your application.\n\n\n## Enterprise support\n\nfast-deep-equal package is a part of [Tidelift enterprise subscription](https://tidelift.com/subscription/pkg/npm-fast-deep-equal?utm_source=npm-fast-deep-equal&utm_medium=referral&utm_campaign=enterprise&utm_term=repo) - it provides a centralised commercial support to open-source software users, in addition to the support provided by software maintainers.\n\n\n## Security contact\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure. Please do NOT report security vulnerability via GitHub issues.\n\n\n## License\n\n[MIT](https://github.com/epoberezkin/fast-deep-equal/blob/master/LICENSE)\n", "licenseText": "MIT License\n\nCopyright (c) 2017 <PERSON><PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz#****************************************", "type": "tarball", "reference": "https://registry.yarnpkg.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "hash": "****************************************", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==", "registry": "npm", "packageName": "fast-deep-equal", "cacheIntegrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q== sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU="}, "registry": "npm", "hash": "****************************************"}