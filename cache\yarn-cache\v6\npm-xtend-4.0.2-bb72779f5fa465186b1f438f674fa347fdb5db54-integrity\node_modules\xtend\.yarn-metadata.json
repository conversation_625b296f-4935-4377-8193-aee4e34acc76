{"manifest": {"name": "xtend", "version": "4.0.2", "description": "extend like a boss", "keywords": ["extend", "merge", "options", "opts", "object", "array"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/Raynos/xtend.git"}, "main": "immutable", "scripts": {"test": "node test"}, "dependencies": {}, "devDependencies": {"tape": "~1.1.0"}, "homepage": "https://github.com/Raynos/xtend", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "bugs": {"url": "https://github.com/Raynos/xtend/issues", "email": "<EMAIL>"}, "license": "MIT", "testling": {"files": "test.js", "browsers": ["ie/7..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "engines": {"node": ">=0.4"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-xtend-4.0.2-bb72779f5fa465186b1f438f674fa347fdb5db54-integrity\\node_modules\\xtend\\package.json", "readmeFilename": "README.md", "readme": "# xtend\n\n[![browser support][3]][4]\n\n[![locked](http://badges.github.io/stability-badges/dist/locked.svg)](http://github.com/badges/stability-badges)\n\nExtend like a boss\n\nxtend is a basic utility library which allows you to extend an object by appending all of the properties from each object in a list. When there are identical properties, the right-most property takes precedence.\n\n## Examples\n\n```js\nvar extend = require(\"xtend\")\n\n// extend returns a new object. Does not mutate arguments\nvar combination = extend({\n    a: \"a\",\n    b: \"c\"\n}, {\n    b: \"b\"\n})\n// { a: \"a\", b: \"b\" }\n```\n\n## Stability status: Locked\n\n## MIT Licensed \n\n\n  [3]: http://ci.testling.com/Raynos/xtend.png\n  [4]: http://ci.testling.com/Raynos/xtend\n", "licenseText": "The MIT License (MIT)\nCopyright (c) 2012-2014 Raynos.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/xtend/-/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54", "type": "tarball", "reference": "https://registry.yarnpkg.com/xtend/-/xtend-4.0.2.tgz", "hash": "bb72779f5fa465186b1f438f674fa347fdb5db54", "integrity": "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==", "registry": "npm", "packageName": "xtend", "cacheIntegrity": "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ== sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q="}, "registry": "npm", "hash": "bb72779f5fa465186b1f438f674fa347fdb5db54"}