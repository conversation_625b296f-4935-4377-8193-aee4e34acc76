{"manifest": {"name": "snapdragon-node", "description": "Snapdragon utility for creating a new AST node in custom code, such as plugins.", "version": "2.1.1", "homepage": "https://github.com/jonschlinkert/snapdragon-node", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/snapdragon-node.git"}, "bugs": {"url": "https://github.com/jonschlinkert/snapdragon-node/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "devDependencies": {"gulp": "^3.9.1", "gulp-eslint": "^4.0.0", "gulp-format-md": "^0.1.12", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "mocha": "^3.4.2", "snapdragon": "^0.11.0"}, "keywords": ["ast", "compile", "compiler", "convert", "node", "parse", "parser", "plugin", "render", "snapdragon", "snapdragonplugin", "token", "transform"], "verb": {"layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["breakdance", "snapdragon", "snapdragon-capture", "snapdragon-cheerio", "snapdragon-util"]}, "reflinks": ["verb", "verb-generate-readme"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-snapdragon-node-2.1.1-6c175f86ff14bdb0724563e8f3c1b021a286853b-integrity\\node_modules\\snapdragon-node\\package.json", "readmeFilename": "README.md", "readme": "# snapdragon-node [![NPM version](https://img.shields.io/npm/v/snapdragon-node.svg?style=flat)](https://www.npmjs.com/package/snapdragon-node) [![NPM monthly downloads](https://img.shields.io/npm/dm/snapdragon-node.svg?style=flat)](https://npmjs.org/package/snapdragon-node) [![NPM total downloads](https://img.shields.io/npm/dt/snapdragon-node.svg?style=flat)](https://npmjs.org/package/snapdragon-node) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/snapdragon-node.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/snapdragon-node)\n\n> Snapdragon utility for creating a new AST node in custom code, such as plugins.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save snapdragon-node\n```\n\n## Usage\n\nWith [snapdragon](https://github.com/jonschlinkert/snapdragon) v0.9.0 and higher you can use `this.node()` to create a new `Node`, whenever it makes sense.\n\n```js\nvar Node = require('snapdragon-node');\nvar Snapdragon = require('snapdragon');\nvar snapdragon = new Snapdragon();\n\n// example usage inside a parser visitor function\nsnapdragon.parser.set('foo', function() {\n  // get the current \"start\" position\n  var pos = this.position();\n\n  // returns the match if regex matches the substring \n  // at the current position on `parser.input`\n  var match = this.match(/foo/);\n  if (match) {\n    // call \"pos\" on the node, to set the start and end \n    // positions, and return the node to push it onto the AST\n    // (snapdragon will push the node onto the correct\n    // nodes array, based on the stack)\n    return pos(new Node({type: 'bar', val: match[0]}));\n  }\n});\n```\n\n## API\n\n### [Node](index.js#L22)\n\nCreate a new AST `Node` with the given `val` and `type`.\n\n**Params**\n\n* `val` **{String|Object}**: Pass a matched substring, or an object to merge onto the node.\n* `type` **{String}**: The node type to use when `val` is a string.\n* `returns` **{Object}**: node instance\n\n**Example**\n\n```js\nvar node = new Node('*', 'Star');\nvar node = new Node({type: 'star', val: '*'});\n```\n\n### [.isNode](index.js#L61)\n\nReturns true if the given value is a node.\n\n**Params**\n\n* `node` **{Object}**\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nvar Node = require('snapdragon-node');\nvar node = new Node({type: 'foo'});\nconsole.log(Node.isNode(node)); //=> true\nconsole.log(Node.isNode({})); //=> false\n```\n\n### [.define](index.js#L80)\n\nDefine a non-enumberable property on the node instance. Useful for adding properties that shouldn't be extended or visible during debugging.\n\n**Params**\n\n* `name` **{String}**\n* `val` **{any}**\n* `returns` **{Object}**: returns the node instance\n\n**Example**\n\n```js\nvar node = new Node();\nnode.define('foo', 'something non-enumerable');\n```\n\n### [.isEmpty](index.js#L100)\n\nReturns true if `node.val` is an empty string, or `node.nodes` does not contain any non-empty text nodes.\n\n**Params**\n\n* `fn` **{Function}**: (optional) Filter function that is called on `node` and/or child nodes. `isEmpty` will return false immediately when the filter function returns false on any nodes.\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nvar node = new Node({type: 'text'});\nnode.isEmpty(); //=> true\nnode.val = 'foo';\nnode.isEmpty(); //=> false\n```\n\n### [.push](index.js#L118)\n\nGiven node `foo` and node `bar`, push node `bar` onto `foo.nodes`, and set `foo` as `bar.parent`.\n\n**Params**\n\n* `node` **{Object}**\n* `returns` **{Number}**: Returns the length of `node.nodes`\n\n**Example**\n\n```js\nvar foo = new Node({type: 'foo'});\nvar bar = new Node({type: 'bar'});\nfoo.push(bar);\n```\n\n### [.unshift](index.js#L140)\n\nGiven node `foo` and node `bar`, unshift node `bar` onto `foo.nodes`, and set `foo` as `bar.parent`.\n\n**Params**\n\n* `node` **{Object}**\n* `returns` **{Number}**: Returns the length of `node.nodes`\n\n**Example**\n\n```js\nvar foo = new Node({type: 'foo'});\nvar bar = new Node({type: 'bar'});\nfoo.unshift(bar);\n```\n\n### [.pop](index.js#L167)\n\nPop a node from `node.nodes`.\n\n* `returns` **{Number}**: Returns the popped `node`\n\n**Example**\n\n```js\nvar node = new Node({type: 'foo'});\nnode.push(new Node({type: 'a'}));\nnode.push(new Node({type: 'b'}));\nnode.push(new Node({type: 'c'}));\nnode.push(new Node({type: 'd'}));\nconsole.log(node.nodes.length);\n//=> 4\nnode.pop();\nconsole.log(node.nodes.length);\n//=> 3\n```\n\n### [.shift](index.js#L190)\n\nShift a node from `node.nodes`.\n\n* `returns` **{Object}**: Returns the shifted `node`\n\n**Example**\n\n```js\nvar node = new Node({type: 'foo'});\nnode.push(new Node({type: 'a'}));\nnode.push(new Node({type: 'b'}));\nnode.push(new Node({type: 'c'}));\nnode.push(new Node({type: 'd'}));\nconsole.log(node.nodes.length);\n//=> 4\nnode.shift();\nconsole.log(node.nodes.length);\n//=> 3\n```\n\n### [.remove](index.js#L205)\n\nRemove `node` from `node.nodes`.\n\n**Params**\n\n* `node` **{Object}**\n* `returns` **{Object}**: Returns the removed node.\n\n**Example**\n\n```js\nnode.remove(childNode);\n```\n\n### [.find](index.js#L231)\n\nGet the first child node from `node.nodes` that matches the given `type`. If `type` is a number, the child node at that index is returned.\n\n**Params**\n\n* `type` **{String}**\n* `returns` **{Object}**: Returns a child node or undefined.\n\n**Example**\n\n```js\nvar child = node.find(1); //<= index of the node to get\nvar child = node.find('foo'); //<= node.type of a child node\nvar child = node.find(/^(foo|bar)$/); //<= regex to match node.type\nvar child = node.find(['foo', 'bar']); //<= array of node.type(s)\n```\n\n### [.isType](index.js#L249)\n\nReturn true if the node is the given `type`.\n\n**Params**\n\n* `type` **{String}**\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nvar node = new Node({type: 'bar'});\ncosole.log(node.isType('foo'));          // false\ncosole.log(node.isType(/^(foo|bar)$/));  // true\ncosole.log(node.isType(['foo', 'bar'])); // true\n```\n\n### [.hasType](index.js#L270)\n\nReturn true if the `node.nodes` has the given `type`.\n\n**Params**\n\n* `type` **{String}**\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nvar foo = new Node({type: 'foo'});\nvar bar = new Node({type: 'bar'});\nfoo.push(bar);\n\ncosole.log(foo.hasType('qux'));          // false\ncosole.log(foo.hasType(/^(qux|bar)$/));  // true\ncosole.log(foo.hasType(['qux', 'bar'])); // true\n```\n\n* `returns` **{Array}**\n\n**Example**\n\n```js\nvar foo = new Node({type: 'foo'});\nvar bar = new Node({type: 'bar'});\nvar baz = new Node({type: 'baz'});\nfoo.push(bar);\nfoo.push(baz);\n\nconsole.log(bar.siblings.length) // 2\nconsole.log(baz.siblings.length) // 2\n```\n\n* `returns` **{Number}**\n\n**Example**\n\n```js\nvar foo = new Node({type: 'foo'});\nvar bar = new Node({type: 'bar'});\nvar baz = new Node({type: 'baz'});\nvar qux = new Node({type: 'qux'});\nfoo.push(bar);\nfoo.push(baz);\nfoo.unshift(qux);\n\nconsole.log(bar.index) // 1\nconsole.log(baz.index) // 2\nconsole.log(qux.index) // 0\n```\n\n* `returns` **{Object}**\n\n**Example**\n\n```js\nvar foo = new Node({type: 'foo'});\nvar bar = new Node({type: 'bar'});\nvar baz = new Node({type: 'baz'});\nfoo.push(bar);\nfoo.push(baz);\n\nconsole.log(baz.prev.type) // 'bar'\n```\n\n* `returns` **{Object}**\n\n**Example**\n\n```js\nvar foo = new Node({type: 'foo'});\nvar bar = new Node({type: 'bar'});\nvar baz = new Node({type: 'baz'});\nfoo.push(bar);\nfoo.push(baz);\n\nconsole.log(bar.siblings.length) // 2\nconsole.log(baz.siblings.length) // 2\n```\n\n* `returns` **{Object}**: The first node, or undefiend\n\n**Example**\n\n```js\nvar foo = new Node({type: 'foo'});\nvar bar = new Node({type: 'bar'});\nvar baz = new Node({type: 'baz'});\nvar qux = new Node({type: 'qux'});\nfoo.push(bar);\nfoo.push(baz);\nfoo.push(qux);\n\nconsole.log(foo.first.type) // 'bar'\n```\n\n* `returns` **{Object}**: The last node, or undefiend\n\n**Example**\n\n```js\nvar foo = new Node({type: 'foo'});\nvar bar = new Node({type: 'bar'});\nvar baz = new Node({type: 'baz'});\nvar qux = new Node({type: 'qux'});\nfoo.push(bar);\nfoo.push(baz);\nfoo.push(qux);\n\nconsole.log(foo.last.type) // 'qux'\n```\n\n* `returns` **{Object}**: The last node, or undefiend\n\n**Example**\n\n```js\nvar foo = new Node({type: 'foo'});\nvar bar = new Node({type: 'bar'});\nvar baz = new Node({type: 'baz'});\nvar qux = new Node({type: 'qux'});\nfoo.push(bar);\nfoo.push(baz);\nfoo.push(qux);\n\nconsole.log(foo.last.type) // 'qux'\n```\n\n## Release history\n\nChangelog entries are classified using the following labels from [keep-a-changelog](https://github.com/olivierlacan/keep-a-changelog):\n\n* `added`: for new features\n* `changed`: for changes in existing functionality\n* `deprecated`: for once-stable features removed in upcoming releases\n* `removed`: for deprecated features removed in this release\n* `fixed`: for any bug fixes\n\nCustom labels used in this changelog:\n\n* `dependencies`: bumps dependencies\n* `housekeeping`: code re-organization, minor edits, or other changes that don't fit in one of the other categories.\n\n### [2.0.0] - 2017-05-01\n\n**Changed**\n\n* `.unshiftNode` was renamed to [.unshift](#unshift)\n* `.pushNode` was renamed to [.push](#push)\n* `.getNode` was renamed to [.find](#find)\n\n**Added**\n\n* [.isNode](#isNode)\n* [.isEmpty](#isEmpty)\n* [.pop](#pop)\n* [.shift](#shift)\n* [.remove](#remove)\n\n### [0.1.0]\n\nFirst release.\n\n## About\n\n### Related projects\n\n* [breakdance](https://www.npmjs.com/package/breakdance): Breakdance is a node.js library for converting HTML to markdown. Highly pluggable, flexible and easy… [more](http://breakdance.io) | [homepage](http://breakdance.io \"Breakdance is a node.js library for converting HTML to markdown. Highly pluggable, flexible and easy to use. It's time for your markup to get down.\")\n* [snapdragon-capture](https://www.npmjs.com/package/snapdragon-capture): Snapdragon plugin that adds a capture method to the parser instance. | [homepage](https://github.com/jonschlinkert/snapdragon-capture \"Snapdragon plugin that adds a capture method to the parser instance.\")\n* [snapdragon-cheerio](https://www.npmjs.com/package/snapdragon-cheerio): Snapdragon plugin for converting a cheerio AST to a snapdragon AST. | [homepage](https://github.com/jonschlinkert/snapdragon-cheerio \"Snapdragon plugin for converting a cheerio AST to a snapdragon AST.\")\n* [snapdragon-util](https://www.npmjs.com/package/snapdragon-util): Utilities for the snapdragon parser/compiler. | [homepage](https://github.com/jonschlinkert/snapdragon-util \"Utilities for the snapdragon parser/compiler.\")\n* [snapdragon](https://www.npmjs.com/package/snapdragon): Easy-to-use plugin system for creating powerful, fast and versatile parsers and compilers, with built-in source-map… [more](https://github.com/jonschlinkert/snapdragon) | [homepage](https://github.com/jonschlinkert/snapdragon \"Easy-to-use plugin system for creating powerful, fast and versatile parsers and compilers, with built-in source-map support.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\nPlease read the [contributing guide](.github/contributing.md) for advice on opening issues, pull requests, and coding standards.\n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on June 25, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2017, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/snapdragon-node/-/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b", "type": "tarball", "reference": "https://registry.yarnpkg.com/snapdragon-node/-/snapdragon-node-2.1.1.tgz", "hash": "6c175f86ff14bdb0724563e8f3c1b021a286853b", "integrity": "sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw==", "registry": "npm", "packageName": "snapdragon-node", "cacheIntegrity": "sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw== sha1-bBdfhv8UvbByRWPo88GwIaKGhTs="}, "registry": "npm", "hash": "6c175f86ff14bdb0724563e8f3c1b021a286853b"}