import React from 'react';
import { usePhone } from '../contexts/PhoneContext';
import { useTheme } from '../contexts/ThemeContext';

const CameraApp: React.FC = () => {
  const { closeApp } = usePhone();
  const { colors } = useTheme();

  return (
    <div className="camera-app">
      <div className="app-header" style={{ backgroundColor: colors.surface, borderColor: colors.border }}>
        <button className="app-back-btn" onClick={closeApp} style={{ color: colors.primary }}>
          <i className="fas fa-arrow-left" />
          Zpět
        </button>
        <div className="app-title" style={{ color: colors.text }}>Fotoaparát</div>
      </div>
      
      <div className="app-content" style={{ backgroundColor: colors.background }}>
        <div style={{ padding: '40px 20px', textAlign: 'center', color: colors.textSecondary }}>
          <i className="fas fa-camera" style={{ fontSize: '48px', marginBottom: '16px' }} />
          <p>Fotoaparát aplikace bude implementována</p>
        </div>
      </div>
    </div>
  );
};

export default CameraApp;
