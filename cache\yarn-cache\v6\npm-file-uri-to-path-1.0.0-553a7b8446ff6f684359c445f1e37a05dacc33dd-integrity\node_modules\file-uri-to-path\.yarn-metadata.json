{"manifest": {"name": "file-uri-to-path", "version": "1.0.0", "description": "Convert a file: URI to a file path", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/file-uri-to-path.git"}, "keywords": ["file", "uri", "convert", "path"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/file-uri-to-path/issues"}, "homepage": "https://github.com/TooTallNate/file-uri-to-path", "devDependencies": {"mocha": "3"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-file-uri-to-path-1.0.0-553a7b8446ff6f684359c445f1e37a05dacc33dd-integrity\\node_modules\\file-uri-to-path\\package.json", "readmeFilename": "README.md", "readme": "file-uri-to-path\n================\n### Convert a `file:` URI to a file path\n[![Build Status](https://travis-ci.org/TooTallNate/file-uri-to-path.svg?branch=master)](https://travis-ci.org/TooTallNate/file-uri-to-path)\n\nAccepts a `file:` URI and returns a regular file path suitable for use with the\n`fs` module functions.\n\n\nInstallation\n------------\n\nInstall with `npm`:\n\n``` bash\n$ npm install file-uri-to-path\n```\n\n\nExample\n-------\n\n``` js\nvar uri2path = require('file-uri-to-path');\n\nuri2path('file://localhost/c|/WINDOWS/clock.avi');\n// \"c:\\\\WINDOWS\\\\clock.avi\"\n\nuri2path('file:///c|/WINDOWS/clock.avi');\n// \"c:\\\\WINDOWS\\\\clock.avi\"\n\nuri2path('file://localhost/c:/WINDOWS/clock.avi');\n// \"c:\\\\WINDOWS\\\\clock.avi\"\n\nuri2path('file://hostname/path/to/the%20file.txt');\n// \"\\\\\\\\hostname\\\\path\\\\to\\\\the file.txt\"\n\nuri2path('file:///c:/path/to/the%20file.txt');\n// \"c:\\\\path\\\\to\\\\the file.txt\"\n```\n\n\nAPI\n---\n\n### fileUriToPath(String uri) → String\n\n\n\nLicense\n-------\n\n(The MIT License)\n\nCopyright (c) 2014 Nathan Rajlich &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "licenseText": "Copyright (c) 2014 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz#553a7b8446ff6f684359c445f1e37a05dacc33dd", "type": "tarball", "reference": "https://registry.yarnpkg.com/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz", "hash": "553a7b8446ff6f684359c445f1e37a05dacc33dd", "integrity": "sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==", "registry": "npm", "packageName": "file-uri-to-path", "cacheIntegrity": "sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw== sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90="}, "registry": "npm", "hash": "553a7b8446ff6f684359c445f1e37a05dacc33dd"}