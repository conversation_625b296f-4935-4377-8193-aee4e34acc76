{"manifest": {"name": "is-generator-function", "version": "1.0.8", "description": "Determine if a function is a native generator function.", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc npm run test:all", "test:all": "npm run test:index && npm run test:corejs && npm run test:uglified", "test:harmony": "node  --es-staging --harmony test && node  --es-staging --harmony test/corejs && node --es-staging --harmony test/uglified", "test:index": "node test", "test:corejs": "node test/corejs", "test:uglified": "node test/uglified", "posttest": "npx aud --production", "lint": "eslint .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/ljharb/is-generator-function.git"}, "keywords": ["generator", "generator function", "es6", "es2015", "yield", "function", "function*"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/is-generator-function/issues"}, "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^17.3.0", "aud": "^1.1.3", "auto-changelog": "^2.2.1", "core-js": "^2.6.5 || ^3", "eslint": "^7.14.0", "make-generator-function": "^2.0.0", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.0.1", "uglify-register": "^1.0.1"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-generator-function-1.0.8-dfb5c2b120e02b0a8d9d2c6806cd5621aa922f7b-integrity\\node_modules\\is-generator-function\\package.json", "readmeFilename": "README.md", "readme": "# is-generator-function <sup>[![Version Badge][2]][1]</sup>\n\n[![Build Status][3]][4]\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\n[![browser support][9]][10]\n\nIs this a native generator function?\n\n## Example\n\n```js\nvar isGeneratorFunction = require('is-generator-function');\nassert(!isGeneratorFunction(function () {}));\nassert(!isGeneratorFunction(null));\nassert(isGeneratorFunction(function* () { yield 42; return Infinity; }));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[1]: https://npmjs.org/package/is-generator-function\n[2]: http://versionbadg.es/ljharb/is-generator-function.svg\n[3]: https://travis-ci.org/ljharb/is-generator-function.svg\n[4]: https://travis-ci.org/ljharb/is-generator-function\n[5]: https://david-dm.org/ljharb/is-generator-function.svg\n[6]: https://david-dm.org/ljharb/is-generator-function\n[7]: https://david-dm.org/ljharb/is-generator-function/dev-status.svg\n[8]: https://david-dm.org/ljharb/is-generator-function#info=devDependencies\n[9]: https://ci.testling.com/ljharb/is-generator-function.png\n[10]: https://ci.testling.com/ljharb/is-generator-function\n[11]: https://nodei.co/npm/is-generator-function.png?downloads=true&stars=true\n[license-image]: http://img.shields.io/npm/l/is-generator-function.svg\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/is-generator-function.svg\n[downloads-url]: http://npm-stat.com/charts.html?package=is-generator-function\n\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 Jordan Harband\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-generator-function/-/is-generator-function-1.0.8.tgz#dfb5c2b120e02b0a8d9d2c6806cd5621aa922f7b", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-generator-function/-/is-generator-function-1.0.8.tgz", "hash": "dfb5c2b120e02b0a8d9d2c6806cd5621aa922f7b", "integrity": "sha512-2Omr/twNtufVZFr1GhxjOMFPAj2sjc/dKaIqBhvo4qciXfJmITGH6ZGd8eZYNHza8t1y0e01AuqRhJwfWp26WQ==", "registry": "npm", "packageName": "is-generator-function", "cacheIntegrity": "sha512-2Omr/twNtufVZFr1GhxjOMFPAj2sjc/dKaIqBhvo4qciXfJmITGH6ZGd8eZYNHza8t1y0e01AuqRhJwfWp26WQ== sha1-37XCsSDgKwqNnSxoBs1WIaqSL3s="}, "registry": "npm", "hash": "dfb5c2b120e02b0a8d9d2c6806cd5621aa922f7b"}