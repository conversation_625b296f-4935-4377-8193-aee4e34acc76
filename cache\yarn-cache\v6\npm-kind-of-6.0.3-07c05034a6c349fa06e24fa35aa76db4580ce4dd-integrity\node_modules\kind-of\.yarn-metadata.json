{"manifest": {"name": "kind-of", "description": "Get the native type of a value.", "version": "6.0.3", "homepage": "https://github.com/jonschlinkert/kind-of", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "url": "https://twitter.com/aretecode"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}, {"name": "tunnckoCore", "url": "https://i.am.charlike.online"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/kind-of.git"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "devDependencies": {"benchmarked": "^2.0.0", "browserify": "^14.4.0", "gulp-format-md": "^1.0.0", "mocha": "^4.0.1", "write": "^1.0.3"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["is-glob", "is-number", "is-primitive"]}, "reflinks": ["type-of", "typeof", "verb"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-kind-of-6.0.3-07c05034a6c349fa06e24fa35aa76db4580ce4dd-integrity\\node_modules\\kind-of\\package.json", "readmeFilename": "README.md", "readme": "# kind-of [![NPM version](https://img.shields.io/npm/v/kind-of.svg?style=flat)](https://www.npmjs.com/package/kind-of) [![NPM monthly downloads](https://img.shields.io/npm/dm/kind-of.svg?style=flat)](https://npmjs.org/package/kind-of) [![NPM total downloads](https://img.shields.io/npm/dt/kind-of.svg?style=flat)](https://npmjs.org/package/kind-of) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/kind-of.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/kind-of)\n\n> Get the native type of a value.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save kind-of\n```\n\nInstall with [bower](https://bower.io/)\n\n```sh\n$ bower install kind-of --save\n```\n\n## Why use this?\n\n1. [it's fast](#benchmarks) | [optimizations](#optimizations)\n2. [better type checking](#better-type-checking)\n\n## Usage\n\n> es5, es6, and browser ready\n\n```js\nvar kindOf = require('kind-of');\n\nkindOf(undefined);\n//=> 'undefined'\n\nkindOf(null);\n//=> 'null'\n\nkindOf(true);\n//=> 'boolean'\n\nkindOf(false);\n//=> 'boolean'\n\nkindOf(new Buffer(''));\n//=> 'buffer'\n\nkindOf(42);\n//=> 'number'\n\nkindOf('str');\n//=> 'string'\n\nkindOf(arguments);\n//=> 'arguments'\n\nkindOf({});\n//=> 'object'\n\nkindOf(Object.create(null));\n//=> 'object'\n\nkindOf(new Test());\n//=> 'object'\n\nkindOf(new Date());\n//=> 'date'\n\nkindOf([1, 2, 3]);\n//=> 'array'\n\nkindOf(/foo/);\n//=> 'regexp'\n\nkindOf(new RegExp('foo'));\n//=> 'regexp'\n\nkindOf(new Error('error'));\n//=> 'error'\n\nkindOf(function () {});\n//=> 'function'\n\nkindOf(function * () {});\n//=> 'generatorfunction'\n\nkindOf(Symbol('str'));\n//=> 'symbol'\n\nkindOf(new Map());\n//=> 'map'\n\nkindOf(new WeakMap());\n//=> 'weakmap'\n\nkindOf(new Set());\n//=> 'set'\n\nkindOf(new WeakSet());\n//=> 'weakset'\n\nkindOf(new Int8Array());\n//=> 'int8array'\n\nkindOf(new Uint8Array());\n//=> 'uint8array'\n\nkindOf(new Uint8ClampedArray());\n//=> 'uint8clampedarray'\n\nkindOf(new Int16Array());\n//=> 'int16array'\n\nkindOf(new Uint16Array());\n//=> 'uint16array'\n\nkindOf(new Int32Array());\n//=> 'int32array'\n\nkindOf(new Uint32Array());\n//=> 'uint32array'\n\nkindOf(new Float32Array());\n//=> 'float32array'\n\nkindOf(new Float64Array());\n//=> 'float64array'\n```\n\n## Benchmarks\n\nBenchmarked against [typeof](http://github.com/CodingFu/typeof) and [type-of](https://github.com/ForbesLindesay/type-of).\n\n```bash\n# arguments (32 bytes)\n  kind-of x 17,024,098 ops/sec ±1.90% (86 runs sampled)\n  lib-type-of x 11,926,235 ops/sec ±1.34% (83 runs sampled)\n  lib-typeof x 9,245,257 ops/sec ±1.22% (87 runs sampled)\n\n  fastest is kind-of (by 161% avg)\n\n# array (22 bytes)\n  kind-of x 17,196,492 ops/sec ±1.07% (88 runs sampled)\n  lib-type-of x 8,838,283 ops/sec ±1.02% (87 runs sampled)\n  lib-typeof x 8,677,848 ops/sec ±0.87% (87 runs sampled)\n\n  fastest is kind-of (by 196% avg)\n\n# boolean (24 bytes)\n  kind-of x 16,841,600 ops/sec ±1.10% (86 runs sampled)\n  lib-type-of x 8,096,787 ops/sec ±0.95% (87 runs sampled)\n  lib-typeof x 8,423,345 ops/sec ±1.15% (86 runs sampled)\n\n  fastest is kind-of (by 204% avg)\n\n# buffer (38 bytes)\n  kind-of x 14,848,060 ops/sec ±1.05% (86 runs sampled)\n  lib-type-of x 3,671,577 ops/sec ±1.49% (87 runs sampled)\n  lib-typeof x 8,360,236 ops/sec ±1.24% (86 runs sampled)\n\n  fastest is kind-of (by 247% avg)\n\n# date (30 bytes)\n  kind-of x 16,067,761 ops/sec ±1.58% (86 runs sampled)\n  lib-type-of x 8,954,436 ops/sec ±1.40% (87 runs sampled)\n  lib-typeof x 8,488,307 ops/sec ±1.51% (84 runs sampled)\n\n  fastest is kind-of (by 184% avg)\n\n# error (36 bytes)\n  kind-of x 9,634,090 ops/sec ±1.12% (89 runs sampled)\n  lib-type-of x 7,735,624 ops/sec ±1.32% (86 runs sampled)\n  lib-typeof x 7,442,160 ops/sec ±1.11% (90 runs sampled)\n\n  fastest is kind-of (by 127% avg)\n\n# function (34 bytes)\n  kind-of x 10,031,494 ops/sec ±1.27% (86 runs sampled)\n  lib-type-of x 9,502,757 ops/sec ±1.17% (89 runs sampled)\n  lib-typeof x 8,278,985 ops/sec ±1.08% (88 runs sampled)\n\n  fastest is kind-of (by 113% avg)\n\n# null (24 bytes)\n  kind-of x 18,159,808 ops/sec ±1.92% (86 runs sampled)\n  lib-type-of x 12,927,635 ops/sec ±1.01% (88 runs sampled)\n  lib-typeof x 7,958,234 ops/sec ±1.21% (89 runs sampled)\n\n  fastest is kind-of (by 174% avg)\n\n# number (22 bytes)\n  kind-of x 17,846,779 ops/sec ±0.91% (85 runs sampled)\n  lib-type-of x 3,316,636 ops/sec ±1.19% (86 runs sampled)\n  lib-typeof x 2,329,477 ops/sec ±2.21% (85 runs sampled)\n\n  fastest is kind-of (by 632% avg)\n\n# object-plain (47 bytes)\n  kind-of x 7,085,155 ops/sec ±1.05% (88 runs sampled)\n  lib-type-of x 8,870,930 ops/sec ±1.06% (83 runs sampled)\n  lib-typeof x 8,716,024 ops/sec ±1.05% (87 runs sampled)\n\n  fastest is lib-type-of (by 112% avg)\n\n# regex (25 bytes)\n  kind-of x 14,196,052 ops/sec ±1.65% (84 runs sampled)\n  lib-type-of x 9,554,164 ops/sec ±1.25% (88 runs sampled)\n  lib-typeof x 8,359,691 ops/sec ±1.07% (87 runs sampled)\n\n  fastest is kind-of (by 158% avg)\n\n# string (33 bytes)\n  kind-of x 16,131,428 ops/sec ±1.41% (85 runs sampled)\n  lib-type-of x 7,273,172 ops/sec ±1.05% (87 runs sampled)\n  lib-typeof x 7,382,635 ops/sec ±1.17% (85 runs sampled)\n\n  fastest is kind-of (by 220% avg)\n\n# symbol (34 bytes)\n  kind-of x 17,011,537 ops/sec ±1.24% (86 runs sampled)\n  lib-type-of x 3,492,454 ops/sec ±1.23% (89 runs sampled)\n  lib-typeof x 7,471,235 ops/sec ±2.48% (87 runs sampled)\n\n  fastest is kind-of (by 310% avg)\n\n# template-strings (36 bytes)\n  kind-of x 15,434,250 ops/sec ±1.46% (83 runs sampled)\n  lib-type-of x 7,157,907 ops/sec ±0.97% (87 runs sampled)\n  lib-typeof x 7,517,986 ops/sec ±0.92% (86 runs sampled)\n\n  fastest is kind-of (by 210% avg)\n\n# undefined (29 bytes)\n  kind-of x 19,167,115 ops/sec ±1.71% (87 runs sampled)\n  lib-type-of x 15,477,740 ops/sec ±1.63% (85 runs sampled)\n  lib-typeof x 19,075,495 ops/sec ±1.17% (83 runs sampled)\n\n  fastest is lib-typeof,kind-of\n\n```\n\n## Optimizations\n\nIn 7 out of 8 cases, this library is 2x-10x faster than other top libraries included in the benchmarks. There are a few things that lead to this performance advantage, none of them hard and fast rules, but all of them simple and repeatable in almost any code library:\n\n1. Optimize around the fastest and most common use cases first. Of course, this will change from project-to-project, but I took some time to understand how and why `typeof` checks were being used in my own libraries and other libraries I use a lot.\n2. Optimize around bottlenecks - In other words, the order in which conditionals are implemented is significant, because each check is only as fast as the failing checks that came before it. Here, the biggest bottleneck by far is checking for plain objects (an object that was created by the `Object` constructor). I opted to make this check happen by process of elimination rather than brute force up front (e.g. by using something like `val.constructor.name`), so that every other type check would not be penalized it.\n3. Don't do uneccessary processing - why do `.slice(8, -1).toLowerCase();` just to get the word `regex`? It's much faster to do `if (type === '[object RegExp]') return 'regex'`\n4. There is no reason to make the code in a microlib as terse as possible, just to win points for making it shorter. It's always better to favor performant code over terse code. You will always only be using a single `require()` statement to use the library anyway, regardless of how the code is written.\n\n## Better type checking\n\nkind-of seems to be more consistently \"correct\" than other type checking libs I've looked at. For example, here are some differing results from other popular libs:\n\n### [typeof](https://github.com/CodingFu/typeof) lib\n\nIncorrectly identifies instances of custom constructors (pretty common):\n\n```js\nvar typeOf = require('typeof');\nfunction Test() {}\nconsole.log(typeOf(new Test()));\n//=> 'test'\n```\n\nReturns `object` instead of `arguments`:\n\n```js\nfunction foo() {\n  console.log(typeOf(arguments)) //=> 'object'\n}\nfoo();\n```\n\n### [type-of](https://github.com/ForbesLindesay/type-of) lib\n\nIncorrectly returns `object` for generator functions, buffers, `Map`, `Set`, `WeakMap` and `WeakSet`:\n\n```js\nfunction * foo() {}\nconsole.log(typeOf(foo));\n//=> 'object'\nconsole.log(typeOf(new Buffer('')));\n//=> 'object'\nconsole.log(typeOf(new Map()));\n//=> 'object'\nconsole.log(typeOf(new Set()));\n//=> 'object'\nconsole.log(typeOf(new WeakMap()));\n//=> 'object'\nconsole.log(typeOf(new WeakSet()));\n//=> 'object'\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [is-glob](https://www.npmjs.com/package/is-glob): Returns `true` if the given string looks like a glob pattern or an extglob pattern… [more](https://github.com/micromatch/is-glob) | [homepage](https://github.com/micromatch/is-glob \"Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet\")\n* [is-number](https://www.npmjs.com/package/is-number): Returns true if a number or string value is a finite number. Useful for regex… [more](https://github.com/jonschlinkert/is-number) | [homepage](https://github.com/jonschlinkert/is-number \"Returns true if a number or string value is a finite number. Useful for regex matches, parsing, user input, etc.\")\n* [is-primitive](https://www.npmjs.com/package/is-primitive): Returns `true` if the value is a primitive.  | [homepage](https://github.com/jonschlinkert/is-primitive \"Returns `true` if the value is a primitive. \")\n\n### Contributors\n\n| **Commits** | **Contributor** |  \n| --- | --- |  \n| 102 | [jonschlinkert](https://github.com/jonschlinkert) |  \n| 3   | [aretecode](https://github.com/aretecode) |  \n| 2   | [miguelmota](https://github.com/miguelmota) |  \n| 1   | [doowb](https://github.com/doowb) |  \n| 1   | [dtothefp](https://github.com/dtothefp) |  \n| 1   | [ianstormtaylor](https://github.com/ianstormtaylor) |  \n| 1   | [ksheedlo](https://github.com/ksheedlo) |  \n| 1   | [pdehaan](https://github.com/pdehaan) |  \n| 1   | [laggingreflex](https://github.com/laggingreflex) |  \n| 1   | [tunnckoCore](https://github.com/tunnckoCore) |  \n| 1   | [xiaofen9](https://github.com/xiaofen9) |  \n\n### Author\n\n**Jon Schlinkert**\n\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n\n### License\n\nCopyright © 2020, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.8.0, on January 16, 2020._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/kind-of/-/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd", "type": "tarball", "reference": "https://registry.yarnpkg.com/kind-of/-/kind-of-6.0.3.tgz", "hash": "07c05034a6c349fa06e24fa35aa76db4580ce4dd", "integrity": "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==", "registry": "npm", "packageName": "kind-of", "cacheIntegrity": "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw== sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0="}, "registry": "npm", "hash": "07c05034a6c349fa06e24fa35aa76db4580ce4dd"}