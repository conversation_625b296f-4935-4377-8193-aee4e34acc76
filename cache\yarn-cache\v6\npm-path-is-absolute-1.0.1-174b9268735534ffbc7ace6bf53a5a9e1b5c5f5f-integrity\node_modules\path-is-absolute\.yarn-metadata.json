{"manifest": {"name": "path-is-absolute", "version": "1.0.1", "description": "Node.js 0.12 path.isAbsolute() ponyfill", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/path-is-absolute.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && node test.js"}, "files": ["index.js"], "keywords": ["path", "paths", "file", "dir", "absolute", "isabsolute", "is-absolute", "built-in", "util", "utils", "core", "ponyfill", "polyfill", "shim", "is", "detect", "check"], "devDependencies": {"xo": "^0.16.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-path-is-absolute-1.0.1-****************************************-integrity\\node_modules\\path-is-absolute\\package.json", "readmeFilename": "readme.md", "readme": "# path-is-absolute [![Build Status](https://travis-ci.org/sindresorhus/path-is-absolute.svg?branch=master)](https://travis-ci.org/sindresorhus/path-is-absolute)\n\n> Node.js 0.12 [`path.isAbsolute()`](http://nodejs.org/api/path.html#path_path_isabsolute_path) [ponyfill](https://ponyfill.com)\n\n\n## Install\n\n```\n$ npm install --save path-is-absolute\n```\n\n\n## Usage\n\n```js\nconst pathIsAbsolute = require('path-is-absolute');\n\n// Running on Linux\npathIsAbsolute('/home/<USER>');\n//=> true\npathIsAbsolute('C:/Users/<USER>');\n//=> false\n\n// Running on Windows\npathIsAbsolute('C:/Users/<USER>');\n//=> true\npathIsAbsolute('/home/<USER>');\n//=> false\n\n// Running on any OS\npathIsAbsolute.posix('/home/<USER>');\n//=> true\npathIsAbsolute.posix('C:/Users/<USER>');\n//=> false\npathIsAbsolute.win32('C:/Users/<USER>');\n//=> true\npathIsAbsolute.win32('/home/<USER>');\n//=> false\n```\n\n\n## API\n\nSee the [`path.isAbsolute()` docs](http://nodejs.org/api/path.html#path_path_isabsolute_path).\n\n### pathIsAbsolute(path)\n\n### pathIsAbsolute.posix(path)\n\nPOSIX specific version.\n\n### pathIsAbsolute.win32(path)\n\nWindows specific version.\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com)\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND <PERSON><PERSON>NFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz#****************************************", "type": "tarball", "reference": "https://registry.yarnpkg.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "hash": "****************************************", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "registry": "npm", "packageName": "path-is-absolute", "cacheIntegrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg== sha1-F0uSaHNVNP+8es5r9TpanhtcX18="}, "registry": "npm", "hash": "****************************************"}