{"manifest": {"name": "ansi-styles", "version": "3.2.1", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/chalk/ansi-styles.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava", "screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"color-convert": "^1.9.0"}, "devDependencies": {"ava": "*", "babel-polyfill": "^6.23.0", "svg-term-cli": "^2.1.1", "xo": "*"}, "ava": {"require": "babel-polyfill"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-ansi-styles-3.2.1-41fbb20243e50b12be0f04b8dedbf07520ce841d-integrity\\node_modules\\ansi-styles\\package.json", "readmeFilename": "readme.md", "readme": "# ansi-styles [![Build Status](https://travis-ci.org/chalk/ansi-styles.svg?branch=master)](https://travis-ci.org/chalk/ansi-styles)\n\n> [ANSI escape codes](http://en.wikipedia.org/wiki/ANSI_escape_code#Colors_and_Styles) for styling strings in the terminal\n\nYou probably want the higher-level [chalk](https://github.com/chalk/chalk) module for styling your strings.\n\n<img src=\"https://cdn.rawgit.com/chalk/ansi-styles/8261697c95bf34b6c7767e2cbe9941a851d59385/screenshot.svg\" width=\"900\">\n\n\n## Install\n\n```\n$ npm install ansi-styles\n```\n\n\n## Usage\n\n```js\nconst style = require('ansi-styles');\n\nconsole.log(`${style.green.open}Hello world!${style.green.close}`);\n\n\n// Color conversion between 16/256/truecolor\n// NOTE: If conversion goes to 16 colors or 256 colors, the original color\n//       may be degraded to fit that color palette. This means terminals\n//       that do not support 16 million colors will best-match the\n//       original color.\nconsole.log(style.bgColor.ansi.hsl(120, 80, 72) + 'Hello world!' + style.bgColor.close);\nconsole.log(style.color.ansi256.rgb(199, 20, 250) + 'Hello world!' + style.color.close);\nconsole.log(style.color.ansi16m.hex('#ABCDEF') + 'Hello world!' + style.color.close);\n```\n\n## API\n\nEach style has an `open` and `close` property.\n\n\n## Styles\n\n### Modifiers\n\n- `reset`\n- `bold`\n- `dim`\n- `italic` *(Not widely supported)*\n- `underline`\n- `inverse`\n- `hidden`\n- `strikethrough` *(Not widely supported)*\n\n### Colors\n\n- `black`\n- `red`\n- `green`\n- `yellow`\n- `blue`\n- `magenta`\n- `cyan`\n- `white`\n- `gray` (\"bright black\")\n- `redBright`\n- `greenBright`\n- `yellowBright`\n- `blueBright`\n- `magentaBright`\n- `cyanBright`\n- `whiteBright`\n\n### Background colors\n\n- `bgBlack`\n- `bgRed`\n- `bgGreen`\n- `bgYellow`\n- `bgBlue`\n- `bgMagenta`\n- `bgCyan`\n- `bgWhite`\n- `bgBlackBright`\n- `bgRedBright`\n- `bgGreenBright`\n- `bgYellowBright`\n- `bgBlueBright`\n- `bgMagentaBright`\n- `bgCyanBright`\n- `bgWhiteBright`\n\n\n## Advanced usage\n\nBy default, you get a map of styles, but the styles are also available as groups. They are non-enumerable so they don't show up unless you access them explicitly. This makes it easier to expose only a subset in a higher-level module.\n\n- `style.modifier`\n- `style.color`\n- `style.bgColor`\n\n###### Example\n\n```js\nconsole.log(style.color.green.open);\n```\n\nRaw escape codes (i.e. without the CSI escape prefix `\\u001B[` and render mode postfix `m`) are available under `style.codes`, which returns a `Map` with the open codes as keys and close codes as values.\n\n###### Example\n\n```js\nconsole.log(style.codes.get(36));\n//=> 39\n```\n\n\n## [256 / 16 million (TrueColor) support](https://gist.github.com/XVilka/8346728)\n\n`ansi-styles` uses the [`color-convert`](https://github.com/Qix-/color-convert) package to allow for converting between various colors and ANSI escapes, with support for 256 and 16 million colors.\n\nTo use these, call the associated conversion function with the intended output, for example:\n\n```js\nstyle.color.ansi.rgb(100, 200, 15); // RGB to 16 color ansi foreground code\nstyle.bgColor.ansi.rgb(100, 200, 15); // RGB to 16 color ansi background code\n\nstyle.color.ansi256.hsl(120, 100, 60); // HSL to 256 color ansi foreground code\nstyle.bgColor.ansi256.hsl(120, 100, 60); // HSL to 256 color ansi foreground code\n\nstyle.color.ansi16m.hex('#C0FFEE'); // Hex (RGB) to 16 million color foreground code\nstyle.bgColor.ansi16m.hex('#C0FFEE'); // Hex (RGB) to 16 million color background code\n```\n\n\n## Related\n\n- [ansi-escapes](https://github.com/sindresorhus/ansi-escapes) - ANSI escape codes for manipulating the terminal\n\n\n## Maintainers\n\n- [Sindre Sorhus](https://github.com/sindresorhus)\n- [Josh Junon](https://github.com/qix-)\n\n\n## License\n\nMIT\n", "licenseText": "MIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d", "type": "tarball", "reference": "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-3.2.1.tgz", "hash": "41fbb20243e50b12be0f04b8dedbf07520ce841d", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "registry": "npm", "packageName": "ansi-styles", "cacheIntegrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA== sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0="}, "registry": "npm", "hash": "41fbb20243e50b12be0f04b8dedbf07520ce841d"}