# has-values [![NPM version](https://img.shields.io/npm/v/has-values.svg?style=flat)](https://www.npmjs.com/package/has-values) [![NPM monthly downloads](https://img.shields.io/npm/dm/has-values.svg?style=flat)](https://npmjs.org/package/has-values) [![NPM total downloads](https://img.shields.io/npm/dt/has-values.svg?style=flat)](https://npmjs.org/package/has-values) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/has-values.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/has-values)

> Returns true if any values exist, false if empty. Works for booleans, functions, numbers, strings, nulls, objects and arrays.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save has-values
```

## Usage

```js
var hasValue = require('has-values');

hasValue('a');
//=> true

hasValue('');
//=> false

hasValue(1);
//=> true

hasValue(0);
//=> false

hasValue({a: 'a'}});
//=> true

hasValue({});
hasValue({foo: undefined});
//=> false

hasValue({foo: null});
//=> true

hasValue(['a']);
//=> true

hasValue([]);
hasValue([[], []]);
hasValue([[[]]]);
//=> false

hasValue(['foo']);
hasValue([0]);
//=> true

hasValue(function(foo) {}); 
//=> true

hasValue(function() {});
//=> true

hasValue(true);
//=> true

hasValue(false);
//=> true
```

## isEmpty

To test for empty values, do:

```js
function isEmpty(o, isZero) {
  return !hasValue(o, isZero);
}
```

## Release history

### v1.0.0

* `zero` always returns true
* `array` now recurses, so that an array of empty arrays will return `false`
* `null` now returns true

## About

### Related projects

* [has-value](https://www.npmjs.com/package/has-value): Returns true if a value exists, false if empty. Works with deeply nested values using… [more](https://github.com/jonschlinkert/has-value) | [homepage](https://github.com/jonschlinkert/has-value "Returns true if a value exists, false if empty. Works with deeply nested values using object paths.")
* [is-number](https://www.npmjs.com/package/is-number): Returns true if the value is a number. comprehensive tests. | [homepage](https://github.com/jonschlinkert/is-number "Returns true if the value is a number. comprehensive tests.")
* [is-plain-object](https://www.npmjs.com/package/is-plain-object): Returns true if an object was created by the `Object` constructor. | [homepage](https://github.com/jonschlinkert/is-plain-object "Returns true if an object was created by the `Object` constructor.")
* [isobject](https://www.npmjs.com/package/isobject): Returns true if the value is an object and not an array or null. | [homepage](https://github.com/jonschlinkert/isobject "Returns true if the value is an object and not an array or null.")
* [kind-of](https://www.npmjs.com/package/kind-of): Get the native type of a value. | [homepage](https://github.com/jonschlinkert/kind-of "Get the native type of a value.")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Building docs

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

### Running tests

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on May 19, 2017._