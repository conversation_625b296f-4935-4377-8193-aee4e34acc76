{"manifest": {"name": "@webassemblyjs/ast", "version": "1.7.11", "description": "AST utils for webassemblyjs", "keywords": ["webassembly", "javascript", "ast"], "main": "lib/index.js", "module": "esm/index.js", "author": {"name": "<PERSON>"}, "license": "MIT", "dependencies": {"@webassemblyjs/helper-module-context": "1.7.11", "@webassemblyjs/helper-wasm-bytecode": "1.7.11", "@webassemblyjs/wast-parser": "1.7.11"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.11", "array.prototype.flatmap": "^1.2.1", "dump-exports": "^0.1.0", "mamacro": "^0.0.3"}, "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@webassemblyjs-ast-1.7.11-b988582cafbb2b095e8b556526f30c90d057cace-integrity\\node_modules\\@webassemblyjs\\ast\\package.json", "readmeFilename": "README.md", "readme": "# @webassemblyjs/ast\n\n> AST utils for webassemblyjs\n\n## Installation\n\n```sh\nyarn add @webassemblyjs/ast\n```\n\n## Usage\n\n### Traverse\n\n```js\nimport { traverse } from \"@webassemblyjs/ast\";\n\ntraverse(ast, {\n  Module(path) {\n    console.log(path.node);\n  }\n});\n```\n\n### Instruction signatures\n\n```js\nimport { signatures } from \"@webassemblyjs/ast\";\n\nconsole.log(signatures);\n```\n\n### Path methods\n\n- `findParent: NodeLocator`\n- `replaceWith: Node => void`\n- `remove: () => void`\n- `insertBefore: Node => void`\n- `insertAfter: Node => void`\n- `stop: () => void`\n\n### AST utils\n\n- function `module(id, fields, metadata)`\n- function `moduleMetadata(sections, functionNames, localNames)`\n- function `moduleNameMetadata(value)`\n- function `functionNameMetadata(value, index)`\n- function `localNameMetadata(value, localIndex, functionIndex)`\n- function `binaryModule(id, blob)`\n- function `quoteModule(id, string)`\n- function `sectionMetadata(section, startOffset, size, vectorOfSize)`\n- function `loopInstruction(label, resulttype, instr)`\n- function `instruction(id, args, namedArgs)`\n- function `objectInstruction(id, object, args, namedArgs)`\n- function `ifInstruction(testLabel, test, result, consequent, alternate)`\n- function `stringLiteral(value)`\n- function `numberLiteralFromRaw(value, raw)`\n- function `longNumberLiteral(value, raw)`\n- function `floatLiteral(value, nan, inf, raw)`\n- function `elem(table, offset, funcs)`\n- function `indexInFuncSection(index)`\n- function `valtypeLiteral(name)`\n- function `typeInstruction(id, functype)`\n- function `start(index)`\n- function `globalType(valtype, mutability)`\n- function `leadingComment(value)`\n- function `blockComment(value)`\n- function `data(memoryIndex, offset, init)`\n- function `global(globalType, init, name)`\n- function `table(elementType, limits, name, elements)`\n- function `memory(limits, id)`\n- function `funcImportDescr(id, signature)`\n- function `moduleImport(module, name, descr)`\n- function `moduleExportDescr(exportType, id)`\n- function `moduleExport(name, descr)`\n- function `limit(min, max)`\n- function `signature(params, results)`\n- function `program(body)`\n- function `identifier(value, raw)`\n- function `blockInstruction(label, instr, result)`\n- function `callInstruction(index, instrArgs)`\n- function `callIndirectInstruction(signature, intrs)`\n- function `byteArray(values)`\n- function `func(name, signature, body, isExternal, metadata)`\n- Constant`isModule`\n- Constant`isModuleMetadata`\n- Constant`isModuleNameMetadata`\n- Constant`isFunctionNameMetadata`\n- Constant`isLocalNameMetadata`\n- Constant`isBinaryModule`\n- Constant`isQuoteModule`\n- Constant`isSectionMetadata`\n- Constant`isLoopInstruction`\n- Constant`isInstruction`\n- Constant`isObjectInstruction`\n- Constant`isIfInstruction`\n- Constant`isStringLiteral`\n- Constant`isNumberLiteral`\n- Constant`isLongNumberLiteral`\n- Constant`isFloatLiteral`\n- Constant`isElem`\n- Constant`isIndexInFuncSection`\n- Constant`isValtypeLiteral`\n- Constant`isTypeInstruction`\n- Constant`isStart`\n- Constant`isGlobalType`\n- Constant`isLeadingComment`\n- Constant`isBlockComment`\n- Constant`isData`\n- Constant`isGlobal`\n- Constant`isTable`\n- Constant`isMemory`\n- Constant`isFuncImportDescr`\n- Constant`isModuleImport`\n- Constant`isModuleExportDescr`\n- Constant`isModuleExport`\n- Constant`isLimit`\n- Constant`isSignature`\n- Constant`isProgram`\n- Constant`isIdentifier`\n- Constant`isBlockInstruction`\n- Constant`isCallInstruction`\n- Constant`isCallIndirectInstruction`\n- Constant`isByteArray`\n- Constant`isFunc`\n- Constant`assertModule`\n- Constant`assertModuleMetadata`\n- Constant`assertModuleNameMetadata`\n- Constant`assertFunctionNameMetadata`\n- Constant`assertLocalNameMetadata`\n- Constant`assertBinaryModule`\n- Constant`assertQuoteModule`\n- Constant`assertSectionMetadata`\n- Constant`assertLoopInstruction`\n- Constant`assertInstruction`\n- Constant`assertObjectInstruction`\n- Constant`assertIfInstruction`\n- Constant`assertStringLiteral`\n- Constant`assertNumberLiteral`\n- Constant`assertLongNumberLiteral`\n- Constant`assertFloatLiteral`\n- Constant`assertElem`\n- Constant`assertIndexInFuncSection`\n- Constant`assertValtypeLiteral`\n- Constant`assertTypeInstruction`\n- Constant`assertStart`\n- Constant`assertGlobalType`\n- Constant`assertLeadingComment`\n- Constant`assertBlockComment`\n- Constant`assertData`\n- Constant`assertGlobal`\n- Constant`assertTable`\n- Constant`assertMemory`\n- Constant`assertFuncImportDescr`\n- Constant`assertModuleImport`\n- Constant`assertModuleExportDescr`\n- Constant`assertModuleExport`\n- Constant`assertLimit`\n- Constant`assertSignature`\n- Constant`assertProgram`\n- Constant`assertIdentifier`\n- Constant`assertBlockInstruction`\n- Constant`assertCallInstruction`\n- Constant`assertCallIndirectInstruction`\n- Constant`assertByteArray`\n- Constant`assertFunc`\n\n", "licenseText": "MIT License\n\nCopyright (c) 2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@webassemblyjs/ast/-/ast-1.7.11.tgz#b988582cafbb2b095e8b556526f30c90d057cace", "type": "tarball", "reference": "https://registry.yarnpkg.com/@webassemblyjs/ast/-/ast-1.7.11.tgz", "hash": "b988582cafbb2b095e8b556526f30c90d057cace", "integrity": "sha512-ZEzy4vjvTzScC+SH8RBssQUawpaInUdMTYwYYLh54/s8TuT0gBLuyUnppKsVyZEi876VmmStKsUs28UxPgdvrA==", "registry": "npm", "packageName": "@webassemblyjs/ast", "cacheIntegrity": "sha512-ZEzy4vjvTzScC+SH8RBssQUawpaInUdMTYwYYLh54/s8TuT0gBLuyUnppKsVyZEi876VmmStKsUs28UxPgdvrA== sha1-uYhYLK+7Kwlei1VlJvMMkNBXys4="}, "registry": "npm", "hash": "b988582cafbb2b095e8b556526f30c90d057cace"}