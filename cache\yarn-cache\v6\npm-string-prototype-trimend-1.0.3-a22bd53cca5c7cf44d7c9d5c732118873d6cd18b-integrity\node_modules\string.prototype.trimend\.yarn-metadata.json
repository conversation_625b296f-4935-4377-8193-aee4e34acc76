{"manifest": {"name": "string.prototype.trimend", "version": "1.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "k<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ES2019 spec-compliant String.prototype.trimEnd shim.", "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "lint": "eslint .", "postlint": "es-shim-api --bound", "pretest": "npm run lint", "test": "npm run tests-only", "posttest": "aud --production", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/es-shims/String.prototype.trimEnd.git"}, "keywords": ["es6", "es7", "es8", "javascript", "prototype", "polyfill", "utility", "trim", "trimLeft", "trimRight", "trimStart", "trimEnd", "tc39"], "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^17.2.0", "aud": "^1.1.3", "auto-changelog": "^2.2.1", "eslint": "^7.14.0", "functions-have-names": "^1.2.1", "has-strict-mode": "^1.0.0", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.0.1"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "dependencies": {"call-bind": "^1.0.0", "define-properties": "^1.1.3"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-string-prototype-trimend-1.0.3-a22bd53cca5c7cf44d7c9d5c732118873d6cd18b-integrity\\node_modules\\string.prototype.trimend\\package.json", "readmeFilename": "README.md", "readme": "String.prototype.trimEnd <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![Build Status][travis-svg]][travis-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\n[![browser support][testling-svg]][testling-url]\n\nAn ES2019-spec-compliant `String.prototype.trimEnd` shim. Invoke its \"shim\" method to shim `String.prototype.trimEnd` if it is unavailable.\n\nThis package implements the [es-shim API](https://github.com/es-shims/api) interface. It works in an ES3-supported environment and complies with the [spec](http://www.ecma-international.org/ecma-262/6.0/#sec-object.assign). In an ES6 environment, it will also work properly with `Symbol`s.\n\nMost common usage:\n```js\nvar trimEnd = require('string.prototype.trimend');\n\nassert(trimEnd(' \\t\\na \\t\\n') === 'a \\t\\n');\n\nif (!String.prototype.trimEnd) {\n\ttrimEnd.shim();\n}\n\nassert(trimEnd(' \\t\\na \\t\\n ') === ' \\t\\na \\t\\n '.trimEnd());\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.com/package/string.prototype.trimend\n[npm-version-svg]: http://vb.teelaun.ch/es-shims/String.prototype.trimEnd.svg\n[travis-svg]: https://travis-ci.org/es-shims/String.prototype.trimEnd.svg\n[travis-url]: https://travis-ci.org/es-shims/String.prototype.trimEnd\n[deps-svg]: https://david-dm.org/es-shims/String.prototype.trimEnd.svg\n[deps-url]: https://david-dm.org/es-shims/String.prototype.trimEnd\n[dev-deps-svg]: https://david-dm.org/es-shims/String.prototype.trimEnd/dev-status.svg\n[dev-deps-url]: https://david-dm.org/es-shims/String.prototype.trimEnd#info=devDependencies\n[testling-svg]: https://ci.testling.com/es-shims/String.prototype.trimEnd.png\n[testling-url]: https://ci.testling.com/es-shims/String.prototype.trimEnd\n[npm-badge-png]: https://nodei.co/npm/string.prototype.trimend.png?downloads=true&stars=true\n[license-image]: http://img.shields.io/npm/l/string.prototype.trimend.svg\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/string.prototype.trimend.svg\n[downloads-url]: http://npm-stat.com/charts.html?package=string.prototype.trimend\n", "licenseText": "MIT License\n\nCopyright (c) 2017 <PERSON><PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/string.prototype.trimend/-/string.prototype.trimend-1.0.3.tgz#a22bd53cca5c7cf44d7c9d5c732118873d6cd18b", "type": "tarball", "reference": "https://registry.yarnpkg.com/string.prototype.trimend/-/string.prototype.trimend-1.0.3.tgz", "hash": "a22bd53cca5c7cf44d7c9d5c732118873d6cd18b", "integrity": "sha512-ayH0pB+uf0U28CtjlLvL7NaohvR1amUvVZk+y3DYb0Ey2PUV5zPkkKy9+U1ndVEIXO8hNg18eIv9Jntbii+dKw==", "registry": "npm", "packageName": "string.prototype.trimend", "cacheIntegrity": "sha512-ayH0pB+uf0U28CtjlLvL7NaohvR1amUvVZk+y3DYb0Ey2PUV5zPkkKy9+U1ndVEIXO8hNg18eIv9Jntbii+dKw== sha1-oivVPMpcfPRNfJ1ccyEYhz1s0Ys="}, "registry": "npm", "hash": "a22bd53cca5c7cf44d7c9d5c732118873d6cd18b"}