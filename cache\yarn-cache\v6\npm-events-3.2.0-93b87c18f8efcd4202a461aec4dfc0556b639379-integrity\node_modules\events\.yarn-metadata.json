{"manifest": {"name": "events", "version": "3.2.0", "description": "<PERSON>de's event emitter for all engines.", "keywords": ["events", "eventEmitter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "listeners"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://jeditoolkit.com"}, "repository": {"type": "git", "url": "git://github.com/Gozala/events.git", "web": "https://github.com/Gozala/events"}, "bugs": {"url": "http://github.com/Gozala/events/issues/"}, "main": "./events.js", "engines": {"node": ">=0.8.x"}, "devDependencies": {"airtap": "^1.0.0", "functions-have-names": "^1.2.1", "has-symbols": "^1.0.1", "isarray": "^2.0.5", "tape": "^5.0.0"}, "scripts": {"test": "node tests/index.js", "test:browsers": "airtap -- tests/index.js"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-events-3.2.0-93b87c18f8efcd4202a461aec4dfc0556b639379-integrity\\node_modules\\events\\package.json", "readmeFilename": "Readme.md", "readme": "# events [![Build Status](https://travis-ci.org/Gozala/events.png?branch=master)](https://travis-ci.org/Gozala/events)\n\n> Node's event emitter for all engines.\n\nThis implements the Node.js [`events`][node.js docs] module for environments that do not have it, like browsers.\n\n> `events` currently matches the **Node.js 11.13.0** API.\n\nNote that the `events` module uses ES5 features. If you need to support very old browsers like IE8, use a shim like [`es5-shim`](https://www.npmjs.com/package/es5-shim). You need both the shim and the sham versions of `es5-shim`.\n\nThis module is maintained, but only by very few people. If you'd like to help, let us know in the [Maintainer Needed](https://github.com/Gozala/events/issues/43) issue!\n\n## Install\n\nYou usually do not have to install `events` yourself! If your code runs in Node.js, `events` is built in. If your code runs in the browser, bundlers like [browserify](https://github.com/browserify/browserify) or [webpack](https://github.com/webpack/webpack) also include the `events` module.\n\nBut if none of those apply, with npm do:\n\n```\nnpm install events\n```\n\n## Usage\n\n```javascript\nvar EventEmitter = require('events')\n\nvar ee = new EventEmitter()\nee.on('message', function (text) {\n  console.log(text)\n})\nee.emit('message', 'hello world')\n```\n\n## API\n\nSee the [Node.js EventEmitter docs][node.js docs]. `events` currently matches the Node.js 11.13.0 API.\n\n## Contributing\n\nPRs are very welcome! The main way to contribute to `events` is by porting features, bugfixes and tests from Node.js. Ideally, code contributions to this module are copy-pasted from Node.js and transpiled to ES5, rather than reimplemented from scratch. Matching the Node.js code as closely as possible makes maintenance simpler when new changes land in Node.js.\nThis module intends to provide exactly the same API as Node.js, so features that are not available in the core `events` module will not be accepted. Feature requests should instead be directed at [nodejs/node](https://github.com/nodejs/node) and will be added to this module once they are implemented in Node.js.\n\nIf there is a difference in behaviour between Node.js's `events` module and this module, please open an issue!\n\n## License\n\n[MIT](./LICENSE)\n[node.js docs]: https://nodejs.org/dist/v11.13.0/docs/api/events.html\n", "licenseText": "MIT\n\nCopyright Joyent, Inc. and other Node contributors.\n\nPermission is hereby granted, free of charge, to any person obtaining a\ncopy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to permit\npersons to whom the Software is furnished to do so, subject to the\nfollowing conditions:\n\nThe above copyright notice and this permission notice shall be included\nin all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\nOR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\nNO EVENT SHALL THE AUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM,\nDAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\nOTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\nUSE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/events/-/events-3.2.0.tgz#93b87c18f8efcd4202a461aec4dfc0556b639379", "type": "tarball", "reference": "https://registry.yarnpkg.com/events/-/events-3.2.0.tgz", "hash": "93b87c18f8efcd4202a461aec4dfc0556b639379", "integrity": "sha512-/46HWwbfCX2xTawVfkKLGxMifJYQBWMwY1mjywRtb4c9x8l5NP3KoJtnIOiL1hfdRkIuYhETxQlo62IF8tcnlg==", "registry": "npm", "packageName": "events", "cacheIntegrity": "sha512-/46HWwbfCX2xTawVfkKLGxMifJYQBWMwY1mjywRtb4c9x8l5NP3KoJtnIOiL1hfdRkIuYhETxQlo62IF8tcnlg== sha1-k7h8GPjvzUICpGGuxN/AVWtjk3k="}, "registry": "npm", "hash": "93b87c18f8efcd4202a461aec4dfc0556b639379"}