local Translations = {
    error = {
        has_no_drugs = "Du har ingen stoffer på dig",
        not_enough_police = "Der er ikke nok betjente på arbejde (%{polices} påkrævet)",
        no_drugs_left = "Ingen stoffer at sælge",
        too_far_away = "Du gik for langt væk",
        offer_declined = "Tilbud blev afslået",
        no_player_nearby = "Ingen spiller i nærheden",
        pending_delivery = "Du mangler stadig at færdiggøre en opgave, gør den færdig først?!",
        item_unavailable = "Denne enhed er ikke længere eksiterende, du får en refundering",
        order_not_right = "Dette er ikke en del af ordren",
        too_late = "Du er for sent på den",
        dealer_already_exists = "En dealer er allerede navngivet med det navn",
        dealer_not_exists = "Denne dealer eksistere ikke",
        no_dealers = "Ingen dealer er blevet placeret",
        dealer_not_exists_command = "Dealeren %{dealerName}, eksistere ikke",
        in_vehicle = "Kan ikke sælge i et køretøj",
        delivery_fail = "",
    },
    success = {
        helped_player = "Du hjalp en person på benene igen",
        route_has_been_set = "Ruten til lokationen er på dit kort",
        teleported_to_dealer = "Du blev telepoteret til %{dealerName}",
        offer_accepted = "Tilbud accepteret",
        order_delivered = "Orderen er blevet afleveret",
        dealer_deleted = "Dealer %{dealerName} er blevet slettet"
    },
    info = {
        started_selling_drugs = "Du starter med at sælge stoffer",
        stopped_selling_drugs = "Du stoppede med at sælge stoffer",
        has_been_robbed = "Du blev røvet og mistede %{bags} pose(r) %{drugType}",
        suspicious_situation = "Mistænkelig adfærd",
        possible_drug_dealing = "Mulig narko handel",
        drug_offer = "[~g~E~w~] %{bags}x %{drugLabel} for %{randomPrice} DKK? / [~g~G~w~] Afvis tilbud",
        pick_up_button = "[~g~E~w~] Saml op",
        knock_button = "[~g~E~w~] Bank på",
        mystery_man_button = "[~g~E~w~] Køb / [~g~G~w~] Hjælp din fyr (5000 DKK)",
        other_dealers_button = "[~g~E~w~] Køb / [~g~G~w~] Start en mission",
        reviving_player = "Hjælper personen op...",
        dealer_name = "Dealer %{dealerName}",
        sending_delivery_email = "Der er produkterne, jeg vil opdatere dig på mail",
        mystery_man_knock_message = "Hej mit barn, hvad kan jeg gøre for dig?",
        treated_fred_bad = "Desværre, jeg laver ikke den form for arbejde længere ... Du skulle have behandlet mig bedre",
        fred_knock_message = "Yo %{firstName}! Hvad kan jeg gøre for dig?",
        no_one_home = "Det ser ud til at ingen er hjemme",
        delivery_info_email = "Her er alle informationerne til afleveringen, <br>Enheder: <br> %{itemAmount}x %{itemLabel}<br><br> vær der til tiden",
        deliver_items_button = "[~g~E~w~] %{itemAmount}x %{itemLabel} aflever",
        delivering_products = "Aflevere produkterne...",
        drug_deal_alert = "112: Narkosalg",
        perfect_delivery = "Du gjorde det godt, håber vi ses igen ;)<br><br>Hilsen, %{dealerName}",
        bad_delivery = "Jeg har modtaget klager over din levering, det skal ikke ske igen",
        late_delivery = "Du var der ikke til tiden. Havde du andre vigtigere ting end at lave penge?",
        police_message_server = "En mistænkelig situation på %{street}, muligvis narkohandel",
        drug_deal = "Narko handel",
        newdealer_command_desc = "Placér en dealer (Kun admin)",
        newdealer_command_help1_name = "navn",
        newdealer_command_help1_help = "Dealer navn",
        newdealer_command_help2_name = "min",
        newdealer_command_help2_help = "Minimum tid",
        newdealer_command_help3_name = "maks",
        newdealer_command_help3_help = "Maksimum Tid",
        deletedealer_command_desc = "Slet en dealer (Kun admin)",
        deletedealer_command_help1_name = "navn",
        deletedealer_command_help1_help = "Dealer navn",
        dealers_command_desc = "Se alle dealers (Kun admin)",
        dealergoto_command_desc = "Teleport til dealeren (Kun admin)",
        dealergoto_command_help1_name = "navn",
        dealergoto_command_help1_help = "Dealer navn",
        list_dealers_title = "Liste over alle dealers: ",
        list_dealers_name_prefix = "Navn: ",
        selling_to_ped = "Sælger stoffer...",
        delivery_search = "",
    }
}

if GetConvar('qb_locale', 'en') == 'da' then
    Lang = Locale:new({
        phrases = Translations,
        warnOnMissing = true,
        fallbackLang = Lang,
    })
end
