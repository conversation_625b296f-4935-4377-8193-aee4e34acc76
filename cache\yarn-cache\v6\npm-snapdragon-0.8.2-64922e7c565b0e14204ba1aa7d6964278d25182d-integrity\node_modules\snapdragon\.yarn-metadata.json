{"manifest": {"name": "snapdragon", "description": "Fast, pluggable and easy-to-use parser-renderer factory.", "version": "0.8.2", "homepage": "https://github.com/jonschlinkert/snapdragon", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://edwardbetts.com"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/snapdragon.git"}, "bugs": {"url": "https://github.com/jonschlinkert/snapdragon/issues"}, "license": "MIT", "files": ["index.js", "lib"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "devDependencies": {"gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.10", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.0", "mocha": "^3.0.2"}, "keywords": ["lexer", "snapdragon"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"description": "These libraries use snapdragon:", "list": ["braces", "expand-brackets", "extglob", "micromatch"]}, "reflinks": ["css", "pug", "verb", "verb-generate-readme"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-snapdragon-0.8.2-64922e7c565b0e14204ba1aa7d6964278d25182d-integrity\\node_modules\\snapdragon\\package.json", "readmeFilename": "README.md", "readme": "# snapdragon [![NPM version](https://img.shields.io/npm/v/snapdragon.svg?style=flat)](https://www.npmjs.com/package/snapdragon) [![NPM downloads](https://img.shields.io/npm/dm/snapdragon.svg?style=flat)](https://npmjs.org/package/snapdragon) [![Build Status](https://img.shields.io/travis/jonschlinkert/snapdragon.svg?style=flat)](https://travis-ci.org/jonschlinkert/snapdragon)\n\n> Fast, pluggable and easy-to-use parser-renderer factory.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save snapdragon\n```\n\nCreated by [jonschlink<PERSON>](https://github.com/jonschlinkert) and [doowb](https://github.com/doowb).\n\n**Features**\n\n* Bootstrap your own parser, get sourcemap support for free\n* All parsing and compiling is handled by simple, reusable middleware functions\n* Inspired by the parsers in [pug](http://jade-lang.com) and [css](https://github.com/reworkcss/css).\n\n## History\n\n### v0.5.0\n\n**Breaking changes**\n\nSubstantial breaking changes were made in v0.5.0! Most of these changes are part of a larger refactor that will be finished in 0.6.0, including the introduction of a `Lexer` class.\n\n* Renderer was renamed to `Compiler`\n* the `.render` method was renamed to `.compile`\n* Many other smaller changes. A more detailed overview will be provided in 0.6.0. If you don't have to time review code, I recommend you wait for the 0.6.0 release.\n\n## Usage examples\n\n```js\nvar Snapdragon = require('snapdragon');\nvar snapdragon = new Snapdragon();\n```\n\n**Parse**\n\n```js\nvar ast = snapdragon.parser('some string', options)\n  // parser middleware that can be called by other middleware\n  .set('foo', function () {})\n  // parser middleware, runs immediately in the order defined\n  .use(bar())\n  .use(baz())\n```\n\n**Render**\n\n```js\n// pass the `ast` from the parse method\nvar res = snapdragon.compiler(ast)\n  // compiler middleware, called when the name of the middleware\n  // matches the `node.type` (defined in a parser middleware)\n  .set('bar', function () {})\n  .set('baz', function () {})\n  .compile()\n```\n\nSee the [examples](./examples/).\n\n## Getting started\n\n**Parsers**\n\nParsers are middleware functions used for parsing a string into an ast node.\n\n```js\nvar ast = snapdragon.parser(str, options)\n  .use(function() {\n    var pos = this.position();\n    var m = this.match(/^\\./);\n    if (!m) return;\n    return pos({\n      // `type` specifies the compiler to use\n      type: 'dot',\n      val: m[0]\n    });\n  })\n```\n\n**AST node**\n\nWhen the parser finds a match, `pos()` is called, pushing a token for that node onto the ast that looks something like:\n\n```js\n{ type: 'dot',\n  val: '.',\n  position:\n   { start: { lineno: 1, column: 1 },\n     end: { lineno: 1, column: 2 } }}\n```\n\n**Renderers**\n\nRenderers are _named_ middleware functions that visit over an array of ast nodes to compile a string.\n\n```js\nvar res = snapdragon.compiler(ast)\n  .set('dot', function (node) {\n    console.log(node.val)\n    //=> '.'\n    return this.emit(node.val);\n  })\n```\n\n**Source maps**\n\nIf you want source map support, make sure to emit the position as well.\n\n```js\nvar res = snapdragon.compiler(ast)\n  .set('dot', function (node) {\n    return this.emit(node.val, node.position);\n  })\n```\n\n## Docs\n\n### Parser middleware\n\nA parser middleware is a function that returns an abject called a `token`. This token is pushed onto the AST as a node.\n\n**Example token**\n\n```js\n{ type: 'dot',\n  val: '.',\n  position:\n   { start: { lineno: 1, column: 1 },\n     end: { lineno: 1, column: 2 } }}\n```\n\n**Example parser middleware**\n\nMatch a single `.` in a string:\n\n1. Get the starting position by calling `this.position()`\n2. pass a regex for matching a single dot to the `.match` method\n3. if **no match** is found, return `undefined`\n4. if a **match** is found, `pos()` is called, which returns a token with:\n  - `type`: the name of the [compiler] to use\n  - `val`: The actual value captured by the regex. In this case, a `.`. Note that you can capture and return whatever will be needed by the corresponding [compiler].\n  - The ending position: automatically calculated by adding the length of the first capture group to the starting position.\n\n## Renderer middleware\n\nRenderers are run when the name of the compiler middleware matches the `type` defined on an ast `node` (which is defined in a parser).\n\n**Example**\n\nExercise: Parse a dot, then compile it as an escaped dot.\n\n```js\nvar ast = snapdragon.parser('.')\n  .use(function () {\n    var pos = this.position();\n    var m = this.match(/^\\./);\n    if (!m) return;\n    return pos({\n      // define the `type` of compiler to use\n      type: 'dot',\n      val: m[0]\n    })\n  })\n\nvar result = snapdragon.compiler(ast)\n  .set('dot', function (node) {\n    return this.emit('\\\\' + node.val);\n  })\n  .compile()\n\nconsole.log(result.output);\n//=> '\\.'\n```\n\n## API\n\n### [Parser](lib/parser.js#L19)\n\nCreate a new `Parser` with the given `input` and `options`.\n\n**Params**\n\n* `input` **{String}**\n* `options` **{Object}**\n\n### [.define](lib/parser.js#L103)\n\nDefine a non-enumberable property on the `Parser` instance.\n\n**Example**\n\n```js\nparser.define('foo', 'bar');\n```\n\n**Params**\n\n* `key` **{String}**: propery name\n* `val` **{any}**: property value\n* `returns` **{Object}**: Returns the Parser instance for chaining.\n\nSet parser `name` with the given `fn`\n\n**Params**\n\n* `name` **{String}**\n* `fn` **{Function}**\n\nGet parser `name`\n\n**Params**\n\n* `name` **{String}**\n\nPush a `token` onto the `type` stack.\n\n**Params**\n\n* `type` **{String}**\n* `returns` **{Object}** `token`\n\nPop a token off of the `type` stack\n\n**Params**\n\n* `type` **{String}**\n* `returns` **{Object}**: Returns a token\n\nReturn true if inside a `stack` node. Types are `braces`, `parens` or `brackets`.\n\n**Params**\n\n* `type` **{String}**\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nparser.isType(node, 'brace');\n```\n\n**Params**\n\n* `node` **{Object}**\n* `type` **{String}**\n* `returns` **{Boolean}**\n\n### [.define](lib/compiler.js#L71)\n\nDefine a non-enumberable property on the `Compiler` instance.\n\n**Example**\n\n```js\ncompiler.define('foo', 'bar');\n```\n\n**Params**\n\n* `key` **{String}**: propery name\n* `val` **{any}**: property value\n* `returns` **{Object}**: Returns the Compiler instance for chaining.\n\n## About\n\n### Related projects\n\n* [braces](https://www.npmjs.com/package/braces): Fastest brace expansion for node.js, with the most complete support for the Bash 4.3 braces… [more](https://github.com/jonschlinkert/braces) | [homepage](https://github.com/jonschlinkert/braces \"Fastest brace expansion for node.js, with the most complete support for the Bash 4.3 braces specification.\")\n* [expand-brackets](https://www.npmjs.com/package/expand-brackets): Expand POSIX bracket expressions (character classes) in glob patterns. | [homepage](https://github.com/jonschlinkert/expand-brackets \"Expand POSIX bracket expressions (character classes) in glob patterns.\")\n* [extglob](https://www.npmjs.com/package/extglob): Convert extended globs to regex-compatible strings. Add (almost) the expressive power of regular expressions to… [more](https://github.com/jonschlinkert/extglob) | [homepage](https://github.com/jonschlinkert/extglob \"Convert extended globs to regex-compatible strings. Add (almost) the expressive power of regular expressions to glob patterns.\")\n* [micromatch](https://www.npmjs.com/package/micromatch): Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. | [homepage](https://github.com/jonschlinkert/micromatch \"Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor**<br/> | \n| --- | --- |\n| 106 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 2 | [doowb](https://github.com/doowb) |\n\n### Building docs\n\n_(This document was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme) (a [verb](https://github.com/verbose/verb) generator), please don't edit the readme directly. Any changes to the readme must be made in [.verb.md](.verb.md).)_\n\nTo generate the readme and API documentation with [verb](https://github.com/verbose/verb):\n\n```sh\n$ npm install -g verb verb-generate-readme && verb\n```\n\n### Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm install -d && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT license](https://github.com/jonschlinkert/snapdragon/blob/master/LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.1.31, on October 10, 2016._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015-2016, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/snapdragon/-/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d", "type": "tarball", "reference": "https://registry.yarnpkg.com/snapdragon/-/snapdragon-0.8.2.tgz", "hash": "64922e7c565b0e14204ba1aa7d6964278d25182d", "integrity": "sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg==", "registry": "npm", "packageName": "snapdragon", "cacheIntegrity": "sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg== sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0="}, "registry": "npm", "hash": "64922e7c565b0e14204ba1aa7d6964278d25182d"}