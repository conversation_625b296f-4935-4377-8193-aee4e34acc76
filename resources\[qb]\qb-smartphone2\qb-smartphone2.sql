-- QB-Smartphone2 Database Tables

-- Kontakty
CREATE TABLE IF NOT EXISTS `phone_contacts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `citizenid` varchar(50) NOT NULL,
  `name` varchar(50) NOT NULL,
  `number` varchar(20) NOT NULL,
  `avatar` text DEFAULT NULL,
  `favorite` tinyint(1) DEFAULT 0,
  `blocked` tinyint(1) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `citizenid` (`citizenid`),
  KEY `number` (`number`)
) ENGINE=InnoDB AUTO_INCREMENT=1;

-- SMS zprávy
CREATE TABLE IF NOT EXISTS `phone_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sender` varchar(20) NOT NULL,
  `receiver` varchar(20) NOT NULL,
  `message` text NOT NULL,
  `attachments` text DEFAULT NULL,
  `read_status` tinyint(1) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `sender` (`sender`),
  KEY `receiver` (`receiver`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1;

-- Historie hovorů
CREATE TABLE IF NOT EXISTS `phone_calls` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `caller` varchar(20) NOT NULL,
  `receiver` varchar(20) NOT NULL,
  `duration` int(11) DEFAULT 0,
  `call_type` enum('incoming','outgoing','missed') NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `caller` (`caller`),
  KEY `receiver` (`receiver`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1;

-- Galerie fotek
CREATE TABLE IF NOT EXISTS `phone_gallery` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `citizenid` varchar(50) NOT NULL,
  `image` longtext NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `location` varchar(100) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `citizenid` (`citizenid`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1;

-- DarkWeb transakce
CREATE TABLE IF NOT EXISTS `phone_darkweb` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `citizenid` varchar(50) NOT NULL,
  `item_name` varchar(50) NOT NULL,
  `item_label` varchar(100) NOT NULL,
  `price` int(11) NOT NULL,
  `currency` varchar(20) DEFAULT 'crypto',
  `seller` varchar(50) DEFAULT 'anonymous',
  `status` enum('available','sold','pending') DEFAULT 'available',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `citizenid` (`citizenid`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1;

-- DarkWeb chat
CREATE TABLE IF NOT EXISTS `phone_darkweb_chat` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sender_id` varchar(50) NOT NULL,
  `sender_alias` varchar(50) NOT NULL,
  `message` text NOT NULL,
  `room` varchar(50) DEFAULT 'general',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `room` (`room`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1;

-- Sociální síť posty
CREATE TABLE IF NOT EXISTS `phone_social_posts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `citizenid` varchar(50) NOT NULL,
  `author_name` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `image` longtext DEFAULT NULL,
  `likes` int(11) DEFAULT 0,
  `comments_count` int(11) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `citizenid` (`citizenid`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1;

-- Sociální síť komentáře
CREATE TABLE IF NOT EXISTS `phone_social_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL,
  `citizenid` varchar(50) NOT NULL,
  `author_name` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `post_id` (`post_id`),
  KEY `citizenid` (`citizenid`),
  FOREIGN KEY (`post_id`) REFERENCES `phone_social_posts`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1;

-- Sociální síť liky
CREATE TABLE IF NOT EXISTS `phone_social_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL,
  `citizenid` varchar(50) NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_like` (`post_id`, `citizenid`),
  FOREIGN KEY (`post_id`) REFERENCES `phone_social_posts`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1;

-- Sociální síť sledování
CREATE TABLE IF NOT EXISTS `phone_social_follows` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `follower_id` varchar(50) NOT NULL,
  `following_id` varchar(50) NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_follow` (`follower_id`, `following_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1;

-- Marketplace inzeráty
CREATE TABLE IF NOT EXISTS `phone_marketplace` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `citizenid` varchar(50) NOT NULL,
  `seller_name` varchar(100) NOT NULL,
  `seller_number` varchar(20) NOT NULL,
  `title` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `price` int(11) NOT NULL,
  `category` varchar(50) NOT NULL,
  `images` text DEFAULT NULL,
  `status` enum('active','sold','expired') DEFAULT 'active',
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `citizenid` (`citizenid`),
  KEY `category` (`category`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1;

-- Nastavení telefonu
CREATE TABLE IF NOT EXISTS `phone_settings` (
  `citizenid` varchar(50) NOT NULL,
  `wallpaper` varchar(50) DEFAULT 'default.jpg',
  `theme` varchar(20) DEFAULT 'light',
  `ringtone` varchar(50) DEFAULT 'default',
  `notification_sound` tinyint(1) DEFAULT 1,
  `vibration` tinyint(1) DEFAULT 1,
  `battery_level` int(3) DEFAULT 100,
  `pin_code` varchar(10) DEFAULT NULL,
  `fingerprint_enabled` tinyint(1) DEFAULT 0,
  `bluetooth_enabled` tinyint(1) DEFAULT 1,
  `widgets` text DEFAULT NULL,
  `app_layout` text DEFAULT NULL,
  PRIMARY KEY (`citizenid`)
) ENGINE=InnoDB;

-- Email zprávy
CREATE TABLE IF NOT EXISTS `phone_emails` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sender` varchar(100) NOT NULL,
  `receiver` varchar(50) NOT NULL,
  `subject` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `attachments` text DEFAULT NULL,
  `read_status` tinyint(1) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `receiver` (`receiver`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1;

-- Kalendář události
CREATE TABLE IF NOT EXISTS `phone_calendar` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `citizenid` varchar(50) NOT NULL,
  `title` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `start_date` datetime NOT NULL,
  `end_date` datetime DEFAULT NULL,
  `reminder` tinyint(1) DEFAULT 0,
  `reminder_time` int(11) DEFAULT 15,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `citizenid` (`citizenid`),
  KEY `start_date` (`start_date`)
) ENGINE=InnoDB AUTO_INCREMENT=1;

-- Bluetooth chat
CREATE TABLE IF NOT EXISTS `phone_bluetooth_chat` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sender_id` varchar(50) NOT NULL,
  `receiver_id` varchar(50) NOT NULL,
  `message` text NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `sender_id` (`sender_id`),
  KEY `receiver_id` (`receiver_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1;

-- Herní skóre
CREATE TABLE IF NOT EXISTS `phone_game_scores` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `citizenid` varchar(50) NOT NULL,
  `game` varchar(50) NOT NULL,
  `score` int(11) NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `citizenid` (`citizenid`),
  KEY `game` (`game`),
  KEY `score` (`score`)
) ENGINE=InnoDB AUTO_INCREMENT=1;
