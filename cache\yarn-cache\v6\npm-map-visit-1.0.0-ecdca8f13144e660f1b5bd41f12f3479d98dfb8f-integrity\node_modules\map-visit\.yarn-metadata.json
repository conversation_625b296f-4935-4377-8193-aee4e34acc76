{"manifest": {"name": "map-visit", "description": "Map `visit` over an array of objects.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/map-visit", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/map-visit.git"}, "bugs": {"url": "https://github.com/jonschlinkert/map-visit/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"object-visit": "^1.0.0"}, "devDependencies": {"clone-deep": "^0.2.4", "extend-shallow": "^2.0.1", "gulp-format-md": "^0.1.12", "lodash": "^4.17.4", "mocha": "^3.2.0"}, "keywords": ["array", "arrays", "function", "helper", "invoke", "key", "map", "method", "object", "objects", "value", "visit", "visitor"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["collection-visit", "object-visit"]}, "reflinks": ["verb", "verb-generate-readme"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-map-visit-1.0.0-ecdca8f13144e660f1b5bd41f12f3479d98dfb8f-integrity\\node_modules\\map-visit\\package.json", "readmeFilename": "README.md", "readme": "# map-visit [![NPM version](https://img.shields.io/npm/v/map-visit.svg?style=flat)](https://www.npmjs.com/package/map-visit) [![NPM monthly downloads](https://img.shields.io/npm/dm/map-visit.svg?style=flat)](https://npmjs.org/package/map-visit)  [![NPM total downloads](https://img.shields.io/npm/dt/map-visit.svg?style=flat)](https://npmjs.org/package/map-visit) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/map-visit.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/map-visit)\n\n> Map `visit` over an array of objects.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save map-visit\n```\n\n## Usage\n\n```js\nvar mapVisit = require('map-visit');\n```\n\n## What does this do?\n\n**Assign/Merge/Extend vs. Visit**\n\nLet's say you want to add a `set` method to your application that will:\n\n* set key-value pairs on a `data` object\n* extend objects onto the `data` object\n* extend arrays of objects onto the data object\n\n**Example using `extend`**\n\nHere is one way to accomplish this using Lo-Dash's `extend` (comparable to `Object.assign`):\n\n```js\nvar _ = require('lodash');\n\nvar obj = {\n  data: {},\n  set: function (key, value) {\n    if (Array.isArray(key)) {\n      _.extend.apply(_, [obj.data].concat(key));\n    } else if (typeof key === 'object') {\n      _.extend(obj.data, key);\n    } else {\n      obj.data[key] = value;\n    }\n  }\n};\n\nobj.set('a', 'a');\nobj.set([{b: 'b'}, {c: 'c'}]);\nobj.set({d: {e: 'f'}});\n\nconsole.log(obj.data);\n//=> {a: 'a', b: 'b', c: 'c', d: { e: 'f' }}\n```\n\nThe above approach works fine for most use cases. However, **if you also want to emit an event** each time a property is added to the `data` object, or you want more control over what happens as the object is extended, a better approach would be to use `visit`.\n\n**Example using `visit`**\n\nIn this approach:\n\n* when an array is passed to `set`, the `mapVisit` library calls the `set` method on each object in the array.\n* when an object is passed, `visit` calls `set` on each property in the object.\n\nAs a result, the `data` event will be emitted every time a property is added to `data` (events are just an example, you can use this approach to perform any necessary logic every time the method is called).\n\n```js\nvar mapVisit = require('map-visit');\nvar visit = require('object-visit');\n\nvar obj = {\n  data: {},\n  set: function (key, value) {\n    if (Array.isArray(key)) {\n      mapVisit(obj, 'set', key);\n    } else if (typeof key === 'object') {\n      visit(obj, 'set', key);\n    } else {\n      // simulate an event-emitter\n      console.log('emit', key, value);\n      obj.data[key] = value;\n    }\n  }\n};\n\nobj.set('a', 'a');\nobj.set([{b: 'b'}, {c: 'c'}]);\nobj.set({d: {e: 'f'}});\nobj.set({g: 'h', i: 'j', k: 'l'});\n\nconsole.log(obj.data);\n//=> {a: 'a', b: 'b', c: 'c', d: { e: 'f' }, g: 'h', i: 'j', k: 'l'}\n\n// events would look something like:\n// emit a a\n// emit b b\n// emit c c\n// emit d { e: 'f' }\n// emit g h\n// emit i j\n// emit k l\n```\n\n## About\n\n### Related projects\n\n* [collection-visit](https://www.npmjs.com/package/collection-visit): Visit a method over the items in an object, or map visit over the objects… [more](https://github.com/jonschlinkert/collection-visit) | [homepage](https://github.com/jonschlinkert/collection-visit \"Visit a method over the items in an object, or map visit over the objects in an array.\")\n* [object-visit](https://www.npmjs.com/package/object-visit): Call a specified method on each value in the given object. | [homepage](https://github.com/jonschlinkert/object-visit \"Call a specified method on each value in the given object.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 15 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 7 | [doowb](https://github.com/doowb) |\n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.5.0, on April 09, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015-2017, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/map-visit/-/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f", "type": "tarball", "reference": "https://registry.yarnpkg.com/map-visit/-/map-visit-1.0.0.tgz", "hash": "ecdca8f13144e660f1b5bd41f12f3479d98dfb8f", "integrity": "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=", "registry": "npm", "packageName": "map-visit", "cacheIntegrity": "sha512-4y7uGv8bd2WdM9vpQsiQNo41Ln1NvhvDRuVt0k2JZQ+ezN2uaQes7lZeZ+QQUHOLQAtDaBJ+7wCbi+ab/KFs+w== sha1-7Nyo8TFE5mDxtb1B8S80edmN+48="}, "registry": "npm", "hash": "ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"}