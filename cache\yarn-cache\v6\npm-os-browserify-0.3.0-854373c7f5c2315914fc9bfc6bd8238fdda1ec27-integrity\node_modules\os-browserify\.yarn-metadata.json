{"manifest": {"name": "os-browserify", "version": "0.3.0", "author": {"name": "<PERSON>r<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "main": "main.js", "browser": "browser.js", "jspm": {"map": {"./main.js": {"node": "@node/os", "browser": "./browser.js"}}}, "repository": {"type": "git", "url": "http://github.com/CoderPuppy/os-browserify.git"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-os-browserify-0.3.0-854373c7f5c2315914fc9bfc6bd8238fdda1ec27-integrity\\node_modules\\os-browserify\\package.json", "readmeFilename": "README.md", "readme": "# os-browserify\n\nThe [os](https://nodejs.org/api/os.html) module from node.js, but for browsers.\n\nWhen you `require('os')` in [browserify](http://github.com/substack/node-browserify), this module will be loaded.\n", "description": "The [os](https://nodejs.org/api/os.html) module from node.js, but for browsers.", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2017 CoderPuppy\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/os-browserify/-/os-browserify-0.3.0.tgz#854373c7f5c2315914fc9bfc6bd8238fdda1ec27", "type": "tarball", "reference": "https://registry.yarnpkg.com/os-browserify/-/os-browserify-0.3.0.tgz", "hash": "854373c7f5c2315914fc9bfc6bd8238fdda1ec27", "integrity": "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=", "registry": "npm", "packageName": "os-browserify", "cacheIntegrity": "sha512-gjcpUc3clBf9+210TRaDWbf+rZZZEshZ+DlXMRCeAjp0xhTrnQsKHypIy1J3d5hKdUzj69t708EHtU8P6bUn0A== sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc="}, "registry": "npm", "hash": "854373c7f5c2315914fc9bfc6bd8238fdda1ec27"}