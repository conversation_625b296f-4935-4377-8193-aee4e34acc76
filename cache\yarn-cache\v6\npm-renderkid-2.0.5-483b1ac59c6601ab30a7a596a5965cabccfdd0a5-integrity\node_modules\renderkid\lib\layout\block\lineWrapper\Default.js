// Generated by CoffeeScript 1.9.3
var DefaultLineWrapper,
  extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
  hasProp = {}.hasOwnProperty;

module.exports = DefaultLineWrapper = (function(superClass) {
  extend(DefaultLineWrapper, superClass);

  function DefaultLineWrapper() {
    return DefaultLineWrapper.__super__.constructor.apply(this, arguments);
  }

  DefaultLineWrapper.prototype._render = function() {};

  return DefaultLineWrapper;

})(require('./_LineWrapper'));
