fx_version "bodacious"

game "gta5"
quiinooxgay "yes"
author "ANTIPORTAL"

-- https://discord.gg/KHf4HpQRFq

version "2.3.6"

shared_scripts {
    "config/_framework.lua",
    "config/police-stations/loader.lua",
    "config/translations/*.lua",
    "config/*.lua",
    "radio/config.lua",
    "custom/shared/*.lua",
}

-- https://discord.gg/KHf4HpQRFq

client_scripts {
    "utils/*.lua",
    "hooks/client.lua",
    "custom/framework/client/*.lua",
    "custom/client/actions.lua",
    "custom/client/clothing.lua",
    "custom/client/commands.lua",
    "custom/client/federal.lua",
    "custom/client/inventory.lua",
    "custom/client/menu.lua",
    "custom/client/rpol.lua",
    "custom/client/vehicles.lua",
    "custom/client/voice.lua",
    "custom/client/client_pay_bills.lua",
    "client/init.lua",
    "client/menu/*.lua",
    "client/*.lua",
    "radio/client/**/*.lua",
}

-- https://discord.gg/KHf4HpQRFq

server_scripts {
    'items.lua', -- qb auto add items
    "hooks/server.lua",
    "utils/FileManager.js",
    "server/middlewares/*.lua",
    "config/logs/logs.lua",
    "custom/server/ban.lua",
    "custom/server/database.lua",
    "custom/framework/server/*.lua",
    "custom/server/inventory.lua",
    "custom/server/rpol.lua",
    "custom/server/society.lua",
    "custom/server/vehicles.lua",
    "custom/server/menu/*.lua",
    "custom/server/federal.lua",
    "server/init.lua",
    "server/*.lua",
    "server/menu/*.lua",
    "radio/server/**/*.lua",
}

-- https://discord.gg/KHf4HpQRFq

ui_page 'html/index.html'

-- https://discord.gg/KHf4HpQRFq

files {
    "config/police-stations/*.json",
	'html/index.html',
    "html/apps/*.html",
    "html/components/*.html",
    'html/img/*.*',
    'html/img/webp/*.webp',
    'html/**/*.*',
    'html/sounds/*.*',
    "html/apps/*.html",
    "html/components/*.html",
    "html/css/*.css",
    "html/fonts/*.*",
    "html/js/*.js",
    "stream/*.ytyp",
}

-- https://discord.gg/KHf4HpQRFq

data_file 'DLC_ITYP_REQUEST' 'stream/*.ytyp'

-- https://discord.gg/KHf4HpQRFq

provide {
    'qb-policejob'
}

-- https://discord.gg/KHf4HpQRFq

-- Escrow
lua54 'yes'

-- https://discord.gg/KHf4HpQRFq

dependencies {
    "pma-voice"
}

-- https://discord.gg/KHf4HpQRFq

escrow_ignore {
    "utils/mugshot.lua",
    "config/*.lua",
    "config/translations/*.lua",
    "config/logs/logs.lua",
    "custom/client/*.lua",
    "custom/client/menu/*.lua",
    "custom/framework/**/*.lua",
    "custom/shared/*.lua",
    "custom/server/*.lua",
    "custom/server/menu/*.lua",
    "server/tables.lua",
    "radio/config.lua",
    'items.lua'
}

-- https://discord.gg/KHf4HpQRFq

dependency '/assetpacks'
dependency '/assetpacks'