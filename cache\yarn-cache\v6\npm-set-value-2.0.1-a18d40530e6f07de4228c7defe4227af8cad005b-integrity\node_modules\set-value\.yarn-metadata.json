{"manifest": {"name": "set-value", "description": "Create nested values and any intermediaries using dot notation (`'a.b.c'`) paths.", "version": "2.0.1", "homepage": "https://github.com/jonschlinkert/set-value", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://vadimdemedes.com"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/set-value.git"}, "bugs": {"url": "https://github.com/jonschlinkert/set-value/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "devDependencies": {"gulp-format-md": "^0.1.12", "mocha": "^3.4.2"}, "keywords": ["get", "has", "hasown", "key", "keys", "nested", "notation", "object", "prop", "properties", "property", "props", "set", "value", "values"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["assign-value", "get-value", "has-value", "merge-value", "omit-value", "set-value", "union-value", "unset-value"]}, "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-set-value-2.0.1-a18d40530e6f07de4228c7defe4227af8cad005b-integrity\\node_modules\\set-value\\package.json", "readmeFilename": "README.md", "readme": "# set-value [![NPM version](https://img.shields.io/npm/v/set-value.svg?style=flat)](https://www.npmjs.com/package/set-value) [![NPM monthly downloads](https://img.shields.io/npm/dm/set-value.svg?style=flat)](https://npmjs.org/package/set-value) [![NPM total downloads](https://img.shields.io/npm/dt/set-value.svg?style=flat)](https://npmjs.org/package/set-value) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/set-value.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/set-value)\n\n> Create nested values and any intermediaries using dot notation (`'a.b.c'`) paths.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save set-value\n```\n\n## Usage\n\n```js\nvar set = require('set-value');\nset(object, prop, value);\n```\n\n### Params\n\n* `object` **{object}**: The object to set `value` on\n* `prop` **{string}**: The property to set. Dot-notation may be used.\n* `value` **{any}**: The value to set on `object[prop]`\n\n## Examples\n\nUpdates and returns the given object:\n\n```js\nvar obj = {};\nset(obj, 'a.b.c', 'd');\nconsole.log(obj);\n//=> { a: { b: { c: 'd' } } }\n```\n\n### Escaping\n\n**Escaping with backslashes**\n\nPrevent set-value from splitting on a dot by prefixing it with backslashes:\n\n```js\nconsole.log(set({}, 'a\\\\.b.c', 'd'));\n//=> { 'a.b': { c: 'd' } }\n\nconsole.log(set({}, 'a\\\\.b\\\\.c', 'd'));\n//=> { 'a.b.c': 'd' }\n```\n\n**Escaping with double-quotes or single-quotes**\n\nWrap double or single quotes around the string, or part of the string, that should not be split by set-value:\n\n```js\nconsole.log(set({}, '\"a.b\".c', 'd'));\n//=> { 'a.b': { c: 'd' } }\n\nconsole.log(set({}, \"'a.b'.c\", \"d\"));\n//=> { 'a.b': { c: 'd' } }\n\nconsole.log(set({}, '\"this/is/a/.file.path\"', 'd'));\n//=> { 'this/is/a/file.path': 'd' }\n```\n\n### Bracket support\n\nset-value does not split inside brackets or braces:\n\n```js\nconsole.log(set({}, '[a.b].c', 'd'));\n//=> { '[a.b]': { c: 'd' } }\n\nconsole.log(set({}, \"(a.b).c\", \"d\"));\n//=> { '(a.b)': { c: 'd' } }\n\nconsole.log(set({}, \"<a.b>.c\", \"d\"));\n//=> { '<a.b>': { c: 'd' } }\n\nconsole.log(set({}, \"{a..b}.c\", \"d\"));\n//=> { '{a..b}': { c: 'd' } }\n```\n\n## History\n\n### v2.0.0\n\n* Adds support for escaping with double or single quotes. See [escaping](#escaping) for examples.\n* Will no longer split inside brackets or braces. See [bracket support](#bracket-support) for examples.\n\nIf there are any regressions please create a [bug report](../../issues/new). Thanks!\n\n## About\n\n### Related projects\n\n* [assign-value](https://www.npmjs.com/package/assign-value): Assign a value or extend a deeply nested property of an object using object path… [more](https://github.com/jonschlinkert/assign-value) | [homepage](https://github.com/jonschlinkert/assign-value \"Assign a value or extend a deeply nested property of an object using object path notation.\")\n* [get-value](https://www.npmjs.com/package/get-value): Use property paths (`a.b.c`) to get a nested value from an object. | [homepage](https://github.com/jonschlinkert/get-value \"Use property paths (`a.b.c`) to get a nested value from an object.\")\n* [has-value](https://www.npmjs.com/package/has-value): Returns true if a value exists, false if empty. Works with deeply nested values using… [more](https://github.com/jonschlinkert/has-value) | [homepage](https://github.com/jonschlinkert/has-value \"Returns true if a value exists, false if empty. Works with deeply nested values using object paths.\")\n* [merge-value](https://www.npmjs.com/package/merge-value): Similar to assign-value but deeply merges object values or nested values using object path/dot notation. | [homepage](https://github.com/jonschlinkert/merge-value \"Similar to assign-value but deeply merges object values or nested values using object path/dot notation.\")\n* [omit-value](https://www.npmjs.com/package/omit-value): Omit properties from an object or deeply nested property of an object using object path… [more](https://github.com/jonschlinkert/omit-value) | [homepage](https://github.com/jonschlinkert/omit-value \"Omit properties from an object or deeply nested property of an object using object path notation.\")\n* [set-value](https://www.npmjs.com/package/set-value): Create nested values and any intermediaries using dot notation (`'a.b.c'`) paths. | [homepage](https://github.com/jonschlinkert/set-value \"Create nested values and any intermediaries using dot notation (`'a.b.c'`) paths.\")\n* [union-value](https://www.npmjs.com/package/union-value): Set an array of unique values as the property of an object. Supports setting deeply… [more](https://github.com/jonschlinkert/union-value) | [homepage](https://github.com/jonschlinkert/union-value \"Set an array of unique values as the property of an object. Supports setting deeply nested properties using using object-paths/dot notation.\")\n* [unset-value](https://www.npmjs.com/package/unset-value): Delete nested properties from an object using dot notation. | [homepage](https://github.com/jonschlinkert/unset-value \"Delete nested properties from an object using dot notation.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 59 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 1 | [vadimdemedes](https://github.com/vadimdemedes) |\n| 1 | [wtgtybhertgeghgtwtg](https://github.com/wtgtybhertgeghgtwtg) |\n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on June 21, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/set-value/-/set-value-2.0.1.tgz#a18d40530e6f07de4228c7defe4227af8cad005b", "type": "tarball", "reference": "https://registry.yarnpkg.com/set-value/-/set-value-2.0.1.tgz", "hash": "a18d40530e6f07de4228c7defe4227af8cad005b", "integrity": "sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw==", "registry": "npm", "packageName": "set-value", "cacheIntegrity": "sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw== sha1-oY1AUw5vB95CKMfe/kInr4ytAFs="}, "registry": "npm", "hash": "a18d40530e6f07de4228c7defe4227af8cad005b"}