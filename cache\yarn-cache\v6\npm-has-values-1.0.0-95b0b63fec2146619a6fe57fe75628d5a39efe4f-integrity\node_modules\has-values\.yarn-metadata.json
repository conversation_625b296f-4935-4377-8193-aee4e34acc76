{"manifest": {"name": "has-values", "description": "Returns true if any values exist, false if empty. Works for booleans, functions, numbers, strings, nulls, objects and arrays. ", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/has-values", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/has-values.git"}, "bugs": {"url": "https://github.com/jonschlinkert/has-values/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "devDependencies": {"gulp-format-md": "^0.1.12", "mocha": "^3.4.1"}, "keywords": ["array", "boolean", "empty", "find", "function", "has", "hasOwn", "javascript", "js", "key", "keys", "node.js", "null", "number", "object", "properties", "property", "string", "type", "util", "utilities", "utility", "value", "values"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["has-value", "kind-of", "is-number", "is-plain-object", "isobject"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-has-values-1.0.0-95b0b63fec2146619a6fe57fe75628d5a39efe4f-integrity\\node_modules\\has-values\\package.json", "readmeFilename": "README.md", "readme": "# has-values [![NPM version](https://img.shields.io/npm/v/has-values.svg?style=flat)](https://www.npmjs.com/package/has-values) [![NPM monthly downloads](https://img.shields.io/npm/dm/has-values.svg?style=flat)](https://npmjs.org/package/has-values) [![NPM total downloads](https://img.shields.io/npm/dt/has-values.svg?style=flat)](https://npmjs.org/package/has-values) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/has-values.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/has-values)\n\n> Returns true if any values exist, false if empty. Works for booleans, functions, numbers, strings, nulls, objects and arrays.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save has-values\n```\n\n## Usage\n\n```js\nvar hasValue = require('has-values');\n\nhasValue('a');\n//=> true\n\nhasValue('');\n//=> false\n\nhasValue(1);\n//=> true\n\nhasValue(0);\n//=> false\n\nhasValue({a: 'a'}});\n//=> true\n\nhasValue({});\nhasValue({foo: undefined});\n//=> false\n\nhasValue({foo: null});\n//=> true\n\nhasValue(['a']);\n//=> true\n\nhasValue([]);\nhasValue([[], []]);\nhasValue([[[]]]);\n//=> false\n\nhasValue(['foo']);\nhasValue([0]);\n//=> true\n\nhasValue(function(foo) {}); \n//=> true\n\nhasValue(function() {});\n//=> true\n\nhasValue(true);\n//=> true\n\nhasValue(false);\n//=> true\n```\n\n## isEmpty\n\nTo test for empty values, do:\n\n```js\nfunction isEmpty(o, isZero) {\n  return !hasValue(o, isZero);\n}\n```\n\n## Release history\n\n### v1.0.0\n\n* `zero` always returns true\n* `array` now recurses, so that an array of empty arrays will return `false`\n* `null` now returns true\n\n## About\n\n### Related projects\n\n* [has-value](https://www.npmjs.com/package/has-value): Returns true if a value exists, false if empty. Works with deeply nested values using… [more](https://github.com/jonschlinkert/has-value) | [homepage](https://github.com/jonschlinkert/has-value \"Returns true if a value exists, false if empty. Works with deeply nested values using object paths.\")\n* [is-number](https://www.npmjs.com/package/is-number): Returns true if the value is a number. comprehensive tests. | [homepage](https://github.com/jonschlinkert/is-number \"Returns true if the value is a number. comprehensive tests.\")\n* [is-plain-object](https://www.npmjs.com/package/is-plain-object): Returns true if an object was created by the `Object` constructor. | [homepage](https://github.com/jonschlinkert/is-plain-object \"Returns true if an object was created by the `Object` constructor.\")\n* [isobject](https://www.npmjs.com/package/isobject): Returns true if the value is an object and not an array or null. | [homepage](https://github.com/jonschlinkert/isobject \"Returns true if the value is an object and not an array or null.\")\n* [kind-of](https://www.npmjs.com/package/kind-of): Get the native type of a value. | [homepage](https://github.com/jonschlinkert/kind-of \"Get the native type of a value.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on May 19, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/has-values/-/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f", "type": "tarball", "reference": "https://registry.yarnpkg.com/has-values/-/has-values-1.0.0.tgz", "hash": "95b0b63fec2146619a6fe57fe75628d5a39efe4f", "integrity": "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=", "registry": "npm", "packageName": "has-values", "cacheIntegrity": "sha512-ODYZC64uqzmtfGMEAX/FvZiRyWLpAC3vYnNunURUnkGVTS+mI0smVsWaPydRBsE3g+ok7h960jChO8mFcWlHaQ== sha1-lbC2P+whRmGab+V/51Yo1aOe/k8="}, "registry": "npm", "hash": "95b0b63fec2146619a6fe57fe75628d5a39efe4f"}