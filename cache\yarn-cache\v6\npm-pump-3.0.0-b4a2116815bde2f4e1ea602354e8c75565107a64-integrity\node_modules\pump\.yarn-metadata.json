{"manifest": {"name": "pump", "version": "3.0.0", "repository": {"type": "git", "url": "git://github.com/mafintosh/pump.git"}, "license": "MIT", "description": "pipe streams together and close all of them if one of them closes", "browser": {"fs": false}, "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}, "scripts": {"test": "node test-browser.js && node test-node.js"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-pump-3.0.0-b4a2116815bde2f4e1ea602354e8c75565107a64-integrity\\node_modules\\pump\\package.json", "readmeFilename": "README.md", "readme": "# pump\n\npump is a small node module that pipes streams together and destroys all of them if one of them closes.\n\n```\nnpm install pump\n```\n\n[![build status](http://img.shields.io/travis/mafintosh/pump.svg?style=flat)](http://travis-ci.org/mafintosh/pump)\n\n## What problem does it solve?\n\nWhen using standard `source.pipe(dest)` source will _not_ be destroyed if dest emits close or an error.\nYou are also not able to provide a callback to tell when then pipe has finished.\n\npump does these two things for you\n\n## Usage\n\nSimply pass the streams you want to pipe together to pump and add an optional callback\n\n``` js\nvar pump = require('pump')\nvar fs = require('fs')\n\nvar source = fs.createReadStream('/dev/random')\nvar dest = fs.createWriteStream('/dev/null')\n\npump(source, dest, function(err) {\n  console.log('pipe finished', err)\n})\n\nsetTimeout(function() {\n  dest.destroy() // when dest is closed pump will destroy source\n}, 1000)\n```\n\nYou can use pump to pipe more than two streams together as well\n\n``` js\nvar transform = someTransformStream()\n\npump(source, transform, anotherTransform, dest, function(err) {\n  console.log('pipe finished', err)\n})\n```\n\nIf `source`, `transform`, `anotherTransform` or `dest` closes all of them will be destroyed.\n\nSimilarly to `stream.pipe()`, `pump()` returns the last stream passed in, so you can do:\n\n```\nreturn pump(s1, s2) // returns s2\n```\n\nIf you want to return a stream that combines *both* s1 and s2 to a single stream use\n[pumpify](https://github.com/mafintosh/pumpify) instead.\n\n## License\n\nMIT\n\n## Related\n\n`pump` is part of the [mississippi stream utility collection](https://github.com/maxogden/mississippi) which includes more useful stream modules similar to this one.\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 Mathias Buus\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/pump/-/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64", "type": "tarball", "reference": "https://registry.yarnpkg.com/pump/-/pump-3.0.0.tgz", "hash": "b4a2116815bde2f4e1ea602354e8c75565107a64", "integrity": "sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==", "registry": "npm", "packageName": "pump", "cacheIntegrity": "sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww== sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ="}, "registry": "npm", "hash": "b4a2116815bde2f4e1ea602354e8c75565107a64"}