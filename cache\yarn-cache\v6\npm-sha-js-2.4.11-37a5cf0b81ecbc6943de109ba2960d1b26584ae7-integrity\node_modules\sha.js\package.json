{"name": "sha.js", "description": "Streamable SHA hashes in pure javascript", "version": "2.4.11", "homepage": "https://github.com/crypto-browserify/sha.js", "repository": {"type": "git", "url": "git://github.com/crypto-browserify/sha.js.git"}, "dependencies": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}, "devDependencies": {"buffer": "~2.3.2", "hash-test-vectors": "^1.3.1", "standard": "^10.0.2", "tape": "~2.3.2", "typedarray": "0.0.6"}, "bin": "./bin.js", "scripts": {"prepublish": "npm ls && npm run unit", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "set -e; for t in test/*.js; do node $t; done;"}, "author": "<PERSON> <<EMAIL>> (dominictarr.com)", "license": "(MIT AND BSD-3-<PERSON><PERSON>)"}