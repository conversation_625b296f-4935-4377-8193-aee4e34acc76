{"name": "console-browserify", "version": "1.2.0", "description": "Emulate console for all the browsers", "keywords": [], "author": "Raynos <<EMAIL>>", "repository": "git://github.com/browserify/console-browserify.git", "main": "index", "homepage": "https://github.com/browserify/console-browserify", "contributors": [{"name": "<PERSON><PERSON>"}], "bugs": {"url": "https://github.com/browserify/console-browserify/issues", "email": "<EMAIL>"}, "devDependencies": {"tape": "^2.12.3", "jsonify": "0.0.0", "tap-spec": "^0.1.8", "run-browser": "^1.3.0", "tap-dot": "^0.2.1"}, "licenses": [{"type": "MIT", "url": "http://github.com/browserify/console-browserify/raw/master/LICENSE"}], "scripts": {"test": "node ./test/index.js | tap-spec", "dot": "node ./test/index.js | tap-dot", "start": "node ./index.js", "cover": "istanbul cover --report none --print detail ./test/index.js", "view-cover": "istanbul report html && google-chrome ./coverage/index.html", "browser": "run-browser test/index.js", "phantom": "run-browser test/index.js -b | tap-spec", "build": "browserify test/index.js -o test/static/bundle.js", "testem": "testem"}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}}