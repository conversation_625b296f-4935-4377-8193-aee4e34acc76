{"manifest": {"name": "loader-utils", "version": "0.2.17", "author": {"name": "<PERSON> @sokra"}, "description": "utils for webpack loaders", "dependencies": {"big.js": "^3.1.3", "emojis-list": "^2.0.0", "json5": "^0.5.0", "object-assign": "^4.0.1"}, "scripts": {"test": "mocha", "travis": "npm run cover -- --report lcovonly", "cover": "istanbul cover -x *.runtime.js node_modules/mocha/bin/_mocha", "publish-patch": "mocha && npm version patch && git push && git push --tags && npm publish"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/webpack/loader-utils.git"}, "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.14", "mocha": "^1.21.4"}, "files": ["index.js"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-loader-utils-0.2.17-f86e6374d43205a6e6c60e9196f17c0299bfb348-integrity\\node_modules\\loader-utils\\package.json", "readmeFilename": "README.md", "readme": "# loader-utils\n\n## Methods\n\n### `getLoaderConfig`\n\nRecommended way to retrieve the loader config:\n\n```javascript\n// inside your loader\nconfig = loaderUtils.getLoaderConfig(this, \"myLoader\");\n```\n\nTries to read the loader config from the `webpack.config.js` under the given property name (`\"myLoader\"` in this case) and merges the result with the loader query. For example, if your `webpack.config.js` had this property...\n\n```javascript\ncheesecakeLoader: {\n\ttype: \"delicious\",\n\tslices: 4\n}\n```\n\n...and your loader was called with `?slices=8`, `getLoaderConfig(this, \"cheesecakeLoader\")` would return\n\n```javascript\n{\n\ttype: \"delicious\",\n\tslices: 8\n}\n```\n\nIt is recommended that you use the camelCased loader name as your default config property name.\n\n### `parseQuery`\n\n``` javascript\nvar query = loaderUtils.parseQuery(this.query);\nassert(typeof query == \"object\");\nif(query.flag)\n\t// ...\n```\n\n``` text\nnull                   -> {}\n?                      -> {}\n?flag                  -> { flag: true }\n?+flag                 -> { flag: true }\n?-flag                 -> { flag: false }\n?xyz=test              -> { xyz: \"test\" }\n?xyz[]=a               -> { xyz: [\"a\"] }\n?flag1&flag2           -> { flag1: true, flag2: true }\n?+flag1,-flag2         -> { flag1: true, flag2: false }\n?xyz[]=a,xyz[]=b       -> { xyz: [\"a\", \"b\"] }\n?a%2C%26b=c%2C%26d     -> { \"a,&b\": \"c,&d\" }\n?{json:5,data:{a:1}}   -> { json: 5, data: { a: 1 } }\n```\n\n### `stringifyRequest`\n\nTurns a request into a string that can be used inside `require()` or `import` while avoiding absolute paths.\nUse it instead of `JSON.stringify(...)` if you're generating code inside a loader.\n\n**Why is this necessary?** Since webpack calculates the hash before module paths are translated into module ids, we must avoid absolute paths to ensure\nconsistent hashes across different compilations.\n\nThis function:\n\n- resolves absolute requests into relative requests if the request and the module are on the same hard drive\n- replaces `\\` with `/` if the request and the module are on the same hard drive\n- won't change the path at all if the request and the module are on different hard drives\n- applies `JSON.stringify` to the result\n\n```javascript\nloaderUtils.stringifyRequest(this, \"./test.js\");\n// \"\\\"./test.js\\\"\"\n\nloaderUtils.stringifyRequest(this, \".\\\\test.js\");\n// \"\\\"./test.js\\\"\"\n\nloaderUtils.stringifyRequest(this, \"test\");\n// \"\\\"test\\\"\"\n\nloaderUtils.stringifyRequest(this, \"test/lib/index.js\");\n// \"\\\"test/lib/index.js\\\"\"\n\nloaderUtils.stringifyRequest(this, \"otherLoader?andConfig!test?someConfig\");\n// \"\\\"otherLoader?andConfig!test?someConfig\\\"\"\n\nloaderUtils.stringifyRequest(this, require.resolve(\"test\"));\n// \"\\\"../node_modules/some-loader/lib/test.js\\\"\"\n\nloaderUtils.stringifyRequest(this, \"C:\\\\module\\\\test.js\");\n// \"\\\"../../test.js\\\"\" (on Windows, in case the module and the request are on the same drive)\n\nloaderUtils.stringifyRequest(this, \"C:\\\\module\\\\test.js\");\n// \"\\\"C:\\\\module\\\\test.js\\\"\" (on Windows, in case the module and the request are on different drives)\n\nloaderUtils.stringifyRequest(this, \"\\\\\\\\network-drive\\\\test.js\");\n// \"\\\"\\\\\\\\network-drive\\\\\\\\test.js\\\"\" (on Windows, in case the module and the request are on different drives)\n```\n\n### `urlToRequest`\n\nConverts some resource URL to a webpack module request.\n\n```javascript\nvar url = \"path/to/module.js\";\nvar request = loaderUtils.urlToRequest(url); // \"./path/to/module.js\"\n```\n\n#### Module URLs\n\nAny URL containing a `~` will be interpreted as a module request. Anything after the `~` will be considered the request path.\n\n```javascript\nvar url = \"~path/to/module.js\";\nvar request = loaderUtils.urlToRequest(url); // \"path/to/module.js\"\n```\n\n#### Root-relative URLs\n\nURLs that are root-relative (start with `/`) can be resolved relative to some arbitrary path by using the `root` parameter:\n\n```javascript\nvar url = \"/path/to/module.js\";\nvar root = \"./root\";\nvar request = loaderUtils.urlToRequest(url, root); // \"./root/path/to/module.js\"\n```\n\nTo convert a root-relative URL into a module URL, specify a `root` value that starts with `~`:\n\n```javascript\nvar url = \"/path/to/module.js\";\nvar root = \"~\";\nvar request = loaderUtils.urlToRequest(url, root); // \"path/to/module.js\"\n```\n\n### `interpolateName`\n\nInterpolates a filename template using multiple placeholders and/or a regular expression.\nThe template and regular expression are set as query params called `name` and `regExp` on the current loader's context.\n\n```javascript\nvar interpolatedName = loaderUtils.interpolateName(loaderContext, name, options);\n```\n\nThe following tokens are replaced in the `name` parameter:\n\n* `[ext]` the extension of the resource\n* `[name]` the basename of the resource\n* `[path]` the path of the resource relative to the `context` query parameter or option.\n* `[folder]` the folder of the resource is in.\n* `[emoji]` a random emoji representation of `options.content`\n* `[emoji:<length>]` same as above, but with a customizable number of emojis\n* `[hash]` the hash of `options.content` (Buffer) (by default it's the hex digest of the md5 hash)\n* `[<hashType>:hash:<digestType>:<length>]` optionally one can configure\n  * other `hashType`s, i. e. `sha1`, `md5`, `sha256`, `sha512`\n  * other `digestType`s, i. e. `hex`, `base26`, `base32`, `base36`, `base49`, `base52`, `base58`, `base62`, `base64`\n  * and `length` the length in chars\n* `[N]` the N-th match obtained from matching the current file name against `options.regExp`\n\nExamples\n\n``` javascript\n// loaderContext.resourcePath = \"/app/js/javascript.js\"\nloaderUtils.interpolateName(loaderContext, \"js/[hash].script.[ext]\", { content: ... });\n// => js/9473fdd0d880a43c21b7778d34872157.script.js\n\n// loaderContext.resourcePath = \"/app/page.html\"\nloaderUtils.interpolateName(loaderContext, \"html-[hash:6].html\", { content: ... });\n// => html-9473fd.html\n\n// loaderContext.resourcePath = \"/app/flash.txt\"\nloaderUtils.interpolateName(loaderContext, \"[hash]\", { content: ... });\n// => c31e9820c001c9c4a86bce33ce43b679\n\n// loaderContext.resourcePath = \"/app/img/image.gif\"\nloaderUtils.interpolateName(loaderContext, \"[emoji]\", { content: ... });\n// => 👍\n\n// loaderContext.resourcePath = \"/app/img/image.gif\"\nloaderUtils.interpolateName(loaderContext, \"[emoji:4]\", { content: ... });\n// => 🙍🏢📤🐝\n\n// loaderContext.resourcePath = \"/app/img/image.png\"\nloaderUtils.interpolateName(loaderContext, \"[sha512:hash:base64:7].[ext]\", { content: ... });\n// => 2BKDTjl.png\n// use sha512 hash instead of md5 and with only 7 chars of base64\n\n// loaderContext.resourcePath = \"/app/img/myself.png\"\n// loaderContext.query.name =\nloaderUtils.interpolateName(loaderContext, \"picture.png\");\n// => picture.png\n\n// loaderContext.resourcePath = \"/app/dir/file.png\"\nloaderUtils.interpolateName(loaderContext, \"[path][name].[ext]?[hash]\", { content: ... });\n// => /app/dir/file.png?9473fdd0d880a43c21b7778d34872157\n\n// loaderContext.resourcePath = \"/app/js/page-home.js\"\nloaderUtils.interpolateName(loaderContext, \"script-[1].[ext]\", { regExp: \"page-(.*)\\\\.js\", content: ... });\n// => script-home.js\n```\n\n### `getHashDigest`\n\n``` javascript\nvar digestString = loaderUtils.getHashDigest(buffer, hashType, digestType, maxLength);\n```\n\n* `buffer` the content that should be hashed\n* `hashType` one of `sha1`, `md5`, `sha256`, `sha512` or any other node.js supported hash type\n* `digestType` one of `hex`, `base26`, `base32`, `base36`, `base49`, `base52`, `base58`, `base62`, `base64`\n* `maxLength` the maximum length in chars\n\n## License\n\nMIT (http://www.opensource.org/licenses/mit-license.php)\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2012 - 2015 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/loader-utils/-/loader-utils-0.2.17.tgz#f86e6374d43205a6e6c60e9196f17c0299bfb348", "type": "tarball", "reference": "https://registry.yarnpkg.com/loader-utils/-/loader-utils-0.2.17.tgz", "hash": "f86e6374d43205a6e6c60e9196f17c0299bfb348", "integrity": "sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g=", "registry": "npm", "packageName": "loader-utils", "cacheIntegrity": "sha512-tiv66G0SmiOx+pLWMtGEkfSEejxvb6N6uRrQjfWJIT79W9GMpgKeCAmm9aVBKtd4WEgntciI8CsGqjpDoCWJug== sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g="}, "registry": "npm", "hash": "f86e6374d43205a6e6c60e9196f17c0299bfb348"}