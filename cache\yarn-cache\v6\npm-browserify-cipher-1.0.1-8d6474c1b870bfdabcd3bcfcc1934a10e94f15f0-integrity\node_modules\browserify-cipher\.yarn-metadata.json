{"manifest": {"name": "browserify-cipher", "version": "1.0.1", "description": "ciphers for the browser", "main": "index.js", "dependencies": {"browserify-aes": "^1.0.4", "browserify-des": "^1.0.0", "evp_bytestokey": "^1.0.0"}, "browser": "browser.js", "devDependencies": {"standard": "^10.0.2", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "scripts": {"test": "standard && node test.js | tspec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "**************:crypto-browserify/browserify-cipher.git"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-browserify-cipher-1.0.1-8d6474c1b870bfdabcd3bcfcc1934a10e94f15f0-integrity\\node_modules\\browserify-cipher\\package.json", "readmeFilename": "README.md", "readme": "browserify-cipher\n===\n\n[![Build Status](https://travis-ci.org/crypto-browserify/browserify-cipher.svg)](https://travis-ci.org/crypto-browserify/browserify-cipher)\n\nProvides createCipher, createDecipher, createCipheriv, createDecipheriv and\ngetCiphers for the browserify.  Includes AES and DES ciphers.\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017 Calvin Metcalf & contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/browserify-cipher/-/browserify-cipher-1.0.1.tgz#8d6474c1b870bfdabcd3bcfcc1934a10e94f15f0", "type": "tarball", "reference": "https://registry.yarnpkg.com/browserify-cipher/-/browserify-cipher-1.0.1.tgz", "hash": "8d6474c1b870bfdabcd3bcfcc1934a10e94f15f0", "integrity": "sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w==", "registry": "npm", "packageName": "browserify-cipher", "cacheIntegrity": "sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w== sha1-jWR0wbhwv9q807z8wZNKEOlPFfA="}, "registry": "npm", "hash": "8d6474c1b870bfdabcd3bcfcc1934a10e94f15f0"}