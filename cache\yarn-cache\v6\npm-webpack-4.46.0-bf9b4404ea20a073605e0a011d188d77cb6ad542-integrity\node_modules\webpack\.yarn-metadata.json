{"manifest": {"name": "webpack", "version": "4.46.0", "author": {"name": "<PERSON> @sokra"}, "description": "Packs CommonJs/AMD modules for the browser. Allows to split your codebase into multiple bundles, which can be loaded on demand. Support loaders to preprocess files, i.e. json, jsx, es7, css, less, ... and your custom stuff.", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-module-context": "1.9.0", "@webassemblyjs/wasm-edit": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0", "acorn": "^6.4.1", "ajv": "^6.10.2", "ajv-keywords": "^3.4.1", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^4.5.0", "eslint-scope": "^4.0.3", "json-parse-better-errors": "^1.0.2", "loader-runner": "^2.4.0", "loader-utils": "^1.2.3", "memory-fs": "^0.4.1", "micromatch": "^3.1.10", "mkdirp": "^0.5.3", "neo-async": "^2.6.1", "node-libs-browser": "^2.2.1", "schema-utils": "^1.0.0", "tapable": "^1.1.3", "terser-webpack-plugin": "^1.4.3", "watchpack": "^1.7.4", "webpack-sources": "^1.4.1"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}, "webpack-command": {"optional": true}}, "devDependencies": {"@babel/core": "^7.7.2", "@types/node": "^10.12.21", "@types/tapable": "^1.0.1", "@types/webpack-sources": "^0.1.4", "@yarnpkg/lockfile": "^1.1.0", "babel-loader": "^8.0.6", "benchmark": "^2.1.1", "bundle-loader": "~0.5.0", "coffee-loader": "^0.9.0", "coffeescript": "^2.3.2", "coveralls": "^3.0.2", "css-loader": "^2.1.0", "es6-promise-polyfill": "^1.1.1", "eslint": "^5.8.0", "eslint-config-prettier": "^4.0.0", "eslint-plugin-jest": "^22.2.2", "eslint-plugin-jsdoc": "^15.3.2", "eslint-plugin-node": "^8.0.0", "eslint-plugin-prettier": "^3.0.0", "express": "~4.16.4", "file-loader": "^3.0.1", "glob": "^7.1.3", "husky": "^1.1.3", "i18n-webpack-plugin": "^1.0.0", "istanbul": "^0.4.5", "jest": "^24.9.0", "jest-junit": "^8.0.0", "json-loader": "^0.5.7", "json-schema-to-typescript": "^6.0.1", "less": "^3.9.0", "less-loader": "^4.0.3", "lint-staged": "^8.0.4", "lodash": "^4.17.4", "prettier": "^1.14.3", "pug": "^2.0.4", "pug-loader": "^2.4.0", "raw-loader": "^1.0.0", "react": "^16.8.0", "react-dom": "^16.8.0", "rimraf": "^2.6.2", "script-loader": "~0.7.0", "simple-git": "^1.65.0", "strip-ansi": "^5.2.0", "style-loader": "^0.23.1", "typescript": "^3.0.0-rc", "url-loader": "^1.1.2", "val-loader": "^1.0.2", "vm-browserify": "~1.1.0", "wast-loader": "^1.5.5", "webpack-dev-middleware": "^3.5.1", "webassembly-feature": "1.3.0", "worker-loader": "^2.0.0", "xxhashjs": "^0.2.1"}, "engines": {"node": ">=6.11.5"}, "repository": {"type": "git", "url": "https://github.com/webpack/webpack.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "homepage": "https://github.com/webpack/webpack", "main": "lib/webpack.js", "web": "lib/webpack.web.js", "bin": {"webpack": "bin\\webpack.js"}, "files": ["lib/", "bin/", "buildin/", "declarations/", "hot/", "web_modules/", "schemas/", "SECURITY.md"], "scripts": {"setup": "node ./setup/setup.js", "test": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest", "test:update-snapshots": "yarn jest -u", "test:integration": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.test.js\"", "test:basic": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/te{st/TestCasesNormal,st/StatsTestCases,st/ConfigTestCases}.test.js\"", "test:unit": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.unittest.js\"", "travis:integration": "yarn cover:integration --ci $JEST", "travis:basic": "yarn cover:basic --ci $JEST", "travis:lintunit": "yarn lint && yarn cover:unit --ci $JEST", "travis:benchmark": "yarn benchmark --ci", "appveyor:integration": "yarn cover:integration --ci %JEST%", "appveyor:unit": "yarn cover:unit --ci %JEST%", "appveyor:benchmark": "yarn benchmark --ci", "build:examples": "cd examples && node buildAll.js", "pretest": "yarn lint", "prelint": "yarn setup", "lint": "yarn code-lint && yarn jest-lint && yarn type-lint && yarn special-lint", "code-lint": "eslint . --ext '.js' --cache", "type-lint": "tsc --pretty", "special-lint": "node tooling/inherit-types && node tooling/format-schemas && node tooling/compile-to-definitions", "special-lint-fix": "node tooling/inherit-types --write --override && node tooling/format-schemas --write && node tooling/compile-to-definitions --write", "fix": "yarn code-lint --fix && yarn special-lint-fix", "pretty": "prettier --loglevel warn --write \"*.{ts,js,json,yml,yaml}\" \"{setup,lib,bin,hot,buildin,benchmark,tooling,schemas}/**/*.{js,json}\" \"test/*.js\" \"test/helpers/*.js\" \"test/{configCases,watchCases,statsCases,hotCases}/**/webpack.config.js\" \"examples/**/webpack.config.js\"", "jest-lint": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.lint.js\" --no-verbose", "benchmark": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.benchmark.js\" --runInBand", "cover": "yarn cover:all && yarn cover:report", "cover:all": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --coverage", "cover:basic": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/te{st/TestCasesNormal,st/StatsTestCases,st/ConfigTestCases}.test.js\" --coverage", "cover:integration": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.test.js\" --coverage", "cover:unit": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.unittest.js\" --coverage", "cover:report": "istanbul report"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js|{lib,setup,bin,hot,buildin,tooling,schemas}/**/*.js|test/*.js|{test,examples}/**/webpack.config.js}": ["eslint --cache"]}, "jest": {"forceExit": true, "setupFilesAfterEnv": ["<rootDir>/test/setupTestFramework.js"], "testMatch": ["<rootDir>/test/*.test.js", "<rootDir>/test/*.unittest.js"], "watchPathIgnorePatterns": ["<rootDir>/.git", "<rootDir>/node_modules", "<rootDir>/test/js", "<rootDir>/test/browsertest/js", "<rootDir>/test/fixtures/temp-cache-fixture", "<rootDir>/test/fixtures/temp-", "<rootDir>/benchmark", "<rootDir>/examples/*/dist", "<rootDir>/coverage", "<rootDir>/.eslintcache"], "modulePathIgnorePatterns": ["<rootDir>/.git", "<rootDir>/node_modules/webpack/node_modules", "<rootDir>/test/js", "<rootDir>/test/browsertest/js", "<rootDir>/test/fixtures/temp-cache-fixture", "<rootDir>/test/fixtures/temp-", "<rootDir>/benchmark", "<rootDir>/examples/*/dist", "<rootDir>/coverage", "<rootDir>/.eslintcache"], "transformIgnorePatterns": ["<rootDir>"], "coverageDirectory": "<rootDir>/coverage", "coveragePathIgnorePatterns": ["\\.runtime\\.js$", "<rootDir>/test", "<rootDir>/schemas", "<rootDir>/node_modules"], "testEnvironment": "node", "coverageReporters": ["json"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-webpack-4.46.0-bf9b4404ea20a073605e0a011d188d77cb6ad542-integrity\\node_modules\\webpack\\package.json", "readmeFilename": "README.md", "readme": "<div align=\"center\">\n  <a href=\"https://github.com/webpack/webpack\">\n    <img width=\"200\" height=\"200\" src=\"https://webpack.js.org/assets/icon-square-big.svg\">\n  </a>\n  <br>\n  <br>\n\n[![npm][npm]][npm-url]\n\n[![node][node]][node-url]\n[![deps][deps]][deps-url]\n[![tests][tests]][tests-url]\n[![builds][builds]][builds-url]\n[![builds2][builds2]][builds2-url]\n[![coverage][cover]][cover-url]\n[![licenses][licenses]][licenses-url]\n[![PR's welcome][prs]][prs-url]\n\n  <br>\n  <a href=\"https://dependabot.com/compatibility-score.html?dependency-name=webpack&package-manager=npm_and_yarn&new-version=latest\">\n    <img src=\"https://api.dependabot.com/badges/compatibility_score?dependency-name=webpack&package-manager=npm_and_yarn&version-scheme=semver&target-version=latest\">\n  </a>\n\t<a href=\"https://npmcharts.com/compare/webpack?minimal=true\">\n\t\t<img src=\"https://img.shields.io/npm/dm/webpack.svg\">\n\t</a>\n\t<a href=\"https://packagephobia.now.sh/result?p=webpack\">\n\t\t<img src=\"https://packagephobia.now.sh/badge?p=webpack\" alt=\"install size\">\n\t</a>\n\t<a href=\"https://opencollective.com/webpack#backer\">\n\t\t<img src=\"https://opencollective.com/webpack/backers/badge.svg\">\n\t</a>\n\t<a href=\"https://opencollective.com/webpack#sponsors\">\n\t\t<img src=\"https://opencollective.com/webpack/sponsors/badge.svg\">\n\t</a>\n\t<a href=\"https://github.com/webpack/webpack/graphs/contributors\">\n\t\t<img src=\"https://img.shields.io/github/contributors/webpack/webpack.svg\">\n\t</a>\n\t<a href=\"https://gitter.im/webpack/webpack\">\n\t\t<img src=\"https://badges.gitter.im/webpack/webpack.svg\">\n\t</a>\n  <h1>webpack</h1>\n  <p>\n    webpack is a module bundler. Its main purpose is to bundle JavaScript files for usage in a browser, yet it is also capable of transforming, bundling, or packaging just about any resource or asset.\n  </p>\n</div>\n\n## Table of Contents\n\n1. [Install](#install)\n2. [Introduction](#introduction)\n3. [Concepts](#concepts)\n4. [Contributing](#contributing)\n5. [Support](#support)\n6. [Core Team](#core-team)\n7. [Sponsoring](#sponsoring)\n8. [Premium Partners](#premium-partners)\n9. [Other Backers and Sponsors](#other-backers-and-sponsors)\n10. [Gold Sponsors](#gold-sponsors)\n11. [Silver Sponsors](#silver-sponsors)\n12. [Bronze Sponsors](#bronze-sponsors)\n13. [Backers](#backers)\n14. [Special Thanks](#special-thanks-to)\n\n<h2 align=\"center\">Install</h2>\n\nInstall with npm:\n\n```bash\nnpm install --save-dev webpack\n```\n\nInstall with yarn:\n\n```bash\nyarn add webpack --dev\n```\n\n<h2 align=\"center\">Introduction</h2>\n\nwebpack is a bundler for modules. The main purpose is to bundle JavaScript\nfiles for usage in a browser, yet it is also capable of transforming, bundling,\nor packaging just about any resource or asset.\n\n**TL;DR**\n\n* Bundles [ES Modules](http://www.2ality.com/2014/09/es6-modules-final.html), [CommonJS](http://wiki.commonjs.org/), and [AMD](https://github.com/amdjs/amdjs-api/wiki/AMD) modules (even combined).\n* Can create a single bundle or multiple chunks that are asynchronously loaded at runtime (to reduce initial loading time).\n* Dependencies are resolved during compilation, reducing the runtime size.\n* Loaders can preprocess files while compiling, e.g. TypeScript to JavaScript, Handlebars strings to compiled functions, images to Base64, etc.\n* Highly modular plugin system to do whatever else your application requires.\n\n### Get Started\n\nCheck out webpack's quick [**Get Started**](https://webpack.js.org/guides/getting-started) guide and the [other guides](https://webpack.js.org/guides/).\n\n### Browser Compatibility\n\nwebpack supports all browsers that are [ES5-compliant](http://kangax.github.io/compat-table/es5/) (IE8 and below are not supported).\nwebpack also needs `Promise` for `import()` and `require.ensure()`. If you want to support older browsers, you will need to [load a polyfill](https://webpack.js.org/guides/shimming/) before using these expressions.\n\n<h2 align=\"center\">Concepts</h2>\n\n### [Plugins](https://webpack.js.org/plugins/)\n\nwebpack has a [rich plugin\ninterface](https://webpack.js.org/plugins/). Most of the features\nwithin webpack itself use this plugin interface. This makes webpack very\n**flexible**.\n\n|Name|Status|Install Size|Description|\n|:--:|:----:|:----------:|:----------|\n|[mini-css-extract-plugin][mini-css]|![mini-css-npm]|![mini-css-size]|Extracts CSS into separate files. It creates a CSS file per JS file which contains CSS.|\n|[compression-webpack-plugin][compression]|![compression-npm]|![compression-size]|Prepares compressed versions of assets to serve them with Content-Encoding|\n|[i18n-webpack-plugin][i18n]|![i18n-npm]|![i18n-size]|Adds i18n support to your bundles|\n|[html-webpack-plugin][html-plugin]|![html-plugin-npm]|![html-plugin-size]| Simplifies creation of HTML files (`index.html`) to serve your bundles|\n|[extract-text-webpack-plugin][extract]|![extract-npm]|![extract-size]|Extract text from a bundle, or bundles, into a separate file|\n\n[common-npm]: https://img.shields.io/npm/v/webpack.svg\n[extract]: https://github.com/webpack/extract-text-webpack-plugin\n[extract-npm]: https://img.shields.io/npm/v/extract-text-webpack-plugin.svg\n[extract-size]: https://packagephobia.now.sh/badge?p=extract-text-webpack-plugin\n[mini-css]: https://github.com/webpack-contrib/mini-css-extract-plugin\n[mini-css-npm]: https://img.shields.io/npm/v/mini-css-extract-plugin.svg\n[mini-css-size]: https://packagephobia.now.sh/badge?p=mini-css-extract-plugin\n[component]: https://github.com/webpack/component-webpack-plugin\n[component-npm]: https://img.shields.io/npm/v/component-webpack-plugin.svg\n[component-size]: https://packagephobia.now.sh/badge?p=component-webpack-plugin\n[compression]: https://github.com/webpack/compression-webpack-plugin\n[compression-npm]: https://img.shields.io/npm/v/compression-webpack-plugin.svg\n[compression-size]: https://packagephobia.now.sh/badge?p=compression-webpack-plugin\n[i18n]: https://github.com/webpack/i18n-webpack-plugin\n[i18n-npm]: https://img.shields.io/npm/v/i18n-webpack-plugin.svg\n[i18n-size]: https://packagephobia.now.sh/badge?p=i18n-webpack-plugin\n[html-plugin]: https://github.com/ampedandwired/html-webpack-plugin\n[html-plugin-npm]: https://img.shields.io/npm/v/html-webpack-plugin.svg\n[html-plugin-size]: https://packagephobia.now.sh/badge?p=html-webpack-plugin\n\n### [Loaders](https://webpack.js.org/loaders/)\n\nwebpack enables use of loaders to preprocess files. This allows you to bundle\n**any static resource** way beyond JavaScript. You can easily [write your own\nloaders](https://webpack.js.org/api/loaders/) using Node.js.\n\nLoaders are activated by using `loadername!` prefixes in `require()` statements,\nor are automatically applied via regex from your webpack configuration.\n\n#### Files\n\n|Name|Status|Install Size|Description|\n|:--:|:----:|:----------:|:----------|\n|[raw-loader][raw]|![raw-npm]|![raw-size]|Loads raw content of a file (utf-8)|\n|[val-loader][val]|![val-npm]|![val-size]|Executes code as module and considers exports as JS code|\n|[url-loader][url]|![url-npm]|![url-size]|Works like the file loader, but can return a Data Url if the file is smaller than a limit|\n|[file-loader][file]|![file-npm]|![file-size]|Emits the file into the output folder and returns the (relative) url|\n\n\n[raw]: https://github.com/webpack/raw-loader\n[raw-npm]: https://img.shields.io/npm/v/raw-loader.svg\n[raw-size]: https://packagephobia.now.sh/badge?p=raw-loader\n[val]: https://github.com/webpack/val-loader\n[val-npm]: https://img.shields.io/npm/v/val-loader.svg\n[val-size]: https://packagephobia.now.sh/badge?p=val-loader\n[url]: https://github.com/webpack/url-loader\n[url-npm]: https://img.shields.io/npm/v/url-loader.svg\n[url-size]: https://packagephobia.now.sh/badge?p=url-loader\n[file]: https://github.com/webpack/file-loader\n[file-npm]: https://img.shields.io/npm/v/file-loader.svg\n[file-size]: https://packagephobia.now.sh/badge?p=file-loader\n\n#### JSON\n\n|Name|Status|Install Size|Description|\n|:--:|:----:|:----------:|:----------|\n|<a href=\"https://github.com/webpack/json-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/json.svg\"></a>|![json-npm]|![json-size]|Loads a JSON file (included by default)|\n|<a href=\"https://github.com/webpack/json5-loader\"><img width=\"48\" height=\"10.656\" src=\"https://cdn.rawgit.com/json5/json5-logo/master/json5-logo.svg\"></a>|![json5-npm]|![json5-size]|Loads and transpiles a JSON 5 file|\n|<a href=\"https://github.com/awnist/cson-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/coffeescript.svg\"></a>|![cson-npm]|![cson-size]|Loads and transpiles a CSON file|\n\n\n[json-npm]: https://img.shields.io/npm/v/json-loader.svg\n[json-size]: https://packagephobia.now.sh/badge?p=json-loader\n[json5-npm]: https://img.shields.io/npm/v/json5-loader.svg\n[json5-size]: https://packagephobia.now.sh/badge?p=json5-loader\n[cson-npm]: https://img.shields.io/npm/v/cson-loader.svg\n[cson-size]: https://packagephobia.now.sh/badge?p=cson-loader\n\n#### Transpiling\n\n|Name|Status|Install Size|Description|\n|:--:|:----:|:----------:|:----------|\n|<a href=\"https://github.com/webpack/script-loader\">`<script>`</a>|![script-npm]|![script-size]|Executes a JavaScript file once in global context (like in script tag), `require()`s are not parsed|\n|<a href=\"https://github.com/babel/babel-loader\"><img width=\"48\" height=\"48\" title=\"babel-loader\" src=\"https://worldvectorlogo.com/logos/babel-10.svg\"></a>|![babel-npm]|![babel-size]|Loads ES2015+ code and transpiles to ES5 using <a href=\"https://github.com/babel/babel\">Babel</a>|\n|<a href=\"https://github.com/jupl/traceur-loader\"><img width=\"48\" height=\"48\" src=\"https://google.github.com/traceur-compiler/logo/tc.svg\"></a>|![traceur-npm]|![traceur-size]|Loads ES2015+ code and transpiles to ES5 using [Traceur](https://github.com/google/traceur-compiler)|\n|<a href=\"https://github.com/TypeStrong/ts-loader\"><img width=\"48\" height=\"48\" src=\"https://cdn.rawgit.com/Microsoft/TypeScript/master/doc/logo.svg\"></a>|![type-npm]|![type-size]|Loads TypeScript like JavaScript|\n|[`awesome-typescript-loader`](https://github.com/s-panferov/awesome-typescript-loader)|![awesome-typescript-npm]|![awesome-typescript-size]|Awesome TypeScript loader for webpack|\n|<a href=\"https://github.com/webpack/coffee-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/coffeescript.svg\"></a>|![coffee-npm]|![coffee-size]|Loads CoffeeScript like JavaScript|\n\n\n[script-npm]: https://img.shields.io/npm/v/script-loader.svg\n[script-size]: https://packagephobia.now.sh/badge?p=script-loader\n[babel-npm]: https://img.shields.io/npm/v/babel-loader.svg\n[babel-size]: https://packagephobia.now.sh/badge?p=babel-loader\n[traceur-npm]: https://img.shields.io/npm/v/traceur-loader.svg\n[traceur-size]: https://packagephobia.now.sh/badge?p=traceur-loader\n[coffee-npm]: https://img.shields.io/npm/v/coffee-loader.svg\n[coffee-size]: https://packagephobia.now.sh/badge?p=coffee-loader\n[type-npm]: https://img.shields.io/npm/v/ts-loader.svg\n[type-size]: https://packagephobia.now.sh/badge?p=ts-loader\n[awesome-typescript-npm]: https://img.shields.io/npm/v/awesome-typescript-loader.svg\n[awesome-typescript-size]: https://packagephobia.now.sh/badge?p=awesome-typescript-loader\n\n#### Templating\n\n|Name|Status|Install Size|Description|\n|:--:|:----:|:----------:|:----------|\n|<a href=\"https://github.com/webpack/html-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/html5.svg\"></a>|![html-npm]|![html-size]|Exports HTML as string, requires references to static resources|\n|<a href=\"https://github.com/pugjs/pug-loader\"><img width=\"48\" height=\"48\" src=\"https://cdn.rawgit.com/pugjs/pug-logo/master/SVG/pug-final-logo-_-colour-128.svg\"></a>|![pug-npm]|![pug-size]|Loads Pug templates and returns a function|\n|<a href=\"https://github.com/webpack/jade-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/jade-3.svg\"></a>|![jade-npm]|![jade-size]|Loads Jade templates and returns a function|\n|<a href=\"https://github.com/peerigon/markdown-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/markdown.svg\"></a>|![md-npm]|![md-size]|Compiles Markdown to HTML|\n|<a href=\"https://github.com/posthtml/posthtml-loader\"><img width=\"48\" height=\"48\" src=\"http://posthtml.github.io/posthtml/logo.svg\"></a>|![posthtml-npm]|![posthtml-size]|Loads and transforms a HTML file using [PostHTML](https://github.com/posthtml/posthtml)|\n|<a href=\"https://github.com/altano/handlebars-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/handlebars-1.svg\"></a>|![hbs-npm]|![hbs-size]| Compiles Handlebars to HTML|\n\n\n[html-npm]: https://img.shields.io/npm/v/html-loader.svg\n[html-size]: https://packagephobia.now.sh/badge?p=html-loader\n[pug-npm]: https://img.shields.io/npm/v/pug-loader.svg\n[pug-size]: https://packagephobia.now.sh/badge?p=pug-loader\n[jade-npm]: https://img.shields.io/npm/v/jade-loader.svg\n[jade-size]: https://packagephobia.now.sh/badge?p=jade-loader\n[md-npm]: https://img.shields.io/npm/v/markdown-loader.svg\n[md-size]: https://packagephobia.now.sh/badge?p=markdown-loader\n[posthtml-npm]: https://img.shields.io/npm/v/posthtml-loader.svg\n[posthtml-size]: https://packagephobia.now.sh/badge?p=posthtml-loader\n[hbs-npm]: https://img.shields.io/npm/v/handlebars-loader.svg\n[hbs-size]: https://packagephobia.now.sh/badge?p=handlebars-loader\n\n#### Styling\n\n|Name|Status|Install Size|Description|\n|:--:|:----:|:----------:|:----------|\n|<a href=\"https://github.com/webpack/style-loader\">`<style>`</a>|![style-npm]|![style-size]|Add exports of a module as style to DOM|\n|<a href=\"https://github.com/webpack/css-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/css-3.svg\"></a>|![css-npm]|![css-size]|Loads CSS file with resolved imports and returns CSS code|\n|<a href=\"https://github.com/webpack/less-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/less-63.svg\"></a>|![less-npm]|![less-size]|Loads and compiles a LESS file|\n|<a href=\"https://github.com/jtangelder/sass-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/sass-1.svg\"></a>|![sass-npm]|![sass-size]|Loads and compiles a Sass/SCSS file|\n|<a href=\"https://github.com/shama/stylus-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/stylus.svg\"></a>|![stylus-npm]|![stylus-size]|Loads and compiles a Stylus file|\n|<a href=\"https://github.com/postcss/postcss-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/postcss.svg\"></a>|![postcss-npm]|![postcss-size]|Loads and transforms a CSS/SSS file using [PostCSS](http://postcss.org)|\n\n\n[style-npm]: https://img.shields.io/npm/v/style-loader.svg\n[style-size]: https://packagephobia.now.sh/badge?p=style-loader\n[css-npm]: https://img.shields.io/npm/v/css-loader.svg\n[css-size]: https://packagephobia.now.sh/badge?p=css-loader\n[less-npm]: https://img.shields.io/npm/v/less-loader.svg\n[less-size]: https://packagephobia.now.sh/badge?p=less-loader\n[sass-npm]: https://img.shields.io/npm/v/sass-loader.svg\n[sass-size]: https://packagephobia.now.sh/badge?p=sass-loader\n[stylus-npm]: https://img.shields.io/npm/v/stylus-loader.svg\n[stylus-size]: https://packagephobia.now.sh/badge?p=stylus-loader\n[postcss-npm]: https://img.shields.io/npm/v/postcss-loader.svg\n[postcss-size]: https://packagephobia.now.sh/badge?p=postcss-loader\n\n#### Linting & Testing\n\n|Name|Status|Install Size|Description|\n|:--:|:----:|:----------:|:----------|\n|<a href=\"https://github.com/webpack/mocha-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/mocha.svg\"></a>|![mocha-npm]|![mocha-size]|Tests with mocha (Browser/NodeJS)|\n|<a href=\"https://github.com/MoOx/eslint-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/eslint.svg\"></a>|![eslint-npm]|![eslint-size]|PreLoader for linting code using ESLint|\n|<a href=\"https://github.com/webpack-contrib/jshint-loader\"><img width=\"48\" height=\"20.64\" src=\"http://jshint.com/res/jshint-dark.png\"></a>|![jshint-npm]|![jshint-size]|PreLoader for linting code using JSHint|\n\n[mocha-npm]: https://img.shields.io/npm/v/mocha-loader.svg\n[mocha-size]: https://packagephobia.now.sh/badge?p=mocha-loader\n[eslint-npm]: https://img.shields.io/npm/v/eslint-loader.svg\n[eslint-size]: https://packagephobia.now.sh/badge?p=eslint-loader\n[jshint-npm]: https://img.shields.io/npm/v/jshint-loader.svg\n[jshint-size]: https://packagephobia.now.sh/badge?p=jshint-loader\n[jscs-npm]: https://img.shields.io/npm/v/jscs-loader.svg\n[jscs-size]: https://packagephobia.now.sh/badge?p=jscs-loader\n\n#### Frameworks\n\n|Name|Status|Install Size|Description|\n|:--:|:----:|:----------:|:----------|\n|<a href=\"https://github.com/vuejs/vue-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/vue-9.svg\"></a>|![vue-npm]|![vue-size]|Loads and compiles Vue Components|\n|<a href=\"https://github.com/webpack-contrib/polymer-webpack-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/polymer.svg\"></a>|![polymer-npm]|![polymer-size]|Process HTML & CSS with preprocessor of choice and `require()` Web Components like first-class modules|\n|<a href=\"https://github.com/TheLarkInn/angular2-template-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/angular-icon-1.svg\"></a>|![angular-npm]|![angular-size]| Loads and compiles Angular 2 Components|\n|<a href=\"https://github.com/riot/tag-loader\"><img width=\"48\" height=\"48\" src=\"https://worldvectorlogo.com/logos/riot.svg\"></a>|![riot-npm]|![riot-size]| Riot official webpack loader|\n\n\n\n[vue-npm]: https://img.shields.io/npm/v/vue-loader.svg\n[vue-size]: https://packagephobia.now.sh/badge?p=vue-loader\n[polymer-npm]: https://img.shields.io/npm/v/polymer-webpack-loader.svg\n[polymer-size]: https://packagephobia.now.sh/badge?p=polymer-webpack-loader\n[angular-npm]: https://img.shields.io/npm/v/angular2-template-loader.svg\n[angular-size]: https://packagephobia.now.sh/badge?p=angular2-template-loader\n[riot-npm]: https://img.shields.io/npm/v/riot-tag-loader.svg\n[riot-size]: https://packagephobia.now.sh/badge?p=riot-tag-loader\n\n### Performance\n\nwebpack uses async I/O and has multiple caching levels. This makes webpack fast\nand incredibly **fast** on incremental compilations.\n\n### Module Formats\n\nwebpack supports ES2015+, CommonJS and AMD modules **out of the box**. It performs clever static\nanalysis on the AST of your code. It even has an evaluation engine to evaluate\nsimple expressions. This allows you to **support most existing libraries** out of the box.\n\n### [Code Splitting](https://webpack.js.org/guides/code-splitting/)\n\nwebpack allows you to split your codebase into multiple chunks. Chunks are\nloaded asynchronously at runtime. This reduces the initial loading time.\n\n### [Optimizations](https://webpack.js.org/guides/production-build/)\n\nwebpack can do many optimizations to **reduce the output size of your\nJavaScript** by deduplicating frequently used modules, minifying, and giving\nyou full control of what is loaded initially and what is loaded at runtime\nthrough code splitting. It can also make your code chunks **cache\nfriendly** by using hashes.\n\n<h2 align=\"center\">Contributing</h2>\n\n**We want contributing to webpack to be fun, enjoyable, and educational for anyone, and everyone.** We have a [vibrant ecosystem](https://medium.com/webpack/contributors-guide/home) that spans beyond this single repo. We welcome you to check out any of the repositories in [our organization](http://github.com/webpack) or [webpack-contrib organization](http://github.com/webpack-contrib) which houses all of our loaders and plugins.\n\nContributions go far beyond pull requests and commits. Although we love giving you the opportunity to put your stamp on webpack, we also are thrilled to receive a variety of other contributions including:\n\n* [Documentation](https://github.com/webpack/webpack.js.org) updates, enhancements, designs, or bugfixes\n* Spelling or grammar fixes\n* README.md corrections or redesigns\n* Adding unit, or functional tests\n* Triaging GitHub issues -- especially determining whether an issue still persists or is reproducible.\n* [Searching #webpack on twitter](https://twitter.com/search?q=webpack) and helping someone else who needs help\n* Teaching others how to contribute to one of the many webpack's repos!\n* [Blogging, speaking about, or creating tutorials](https://github.com/webpack-contrib/awesome-webpack) about one of webpack's many features.\n* Helping others in our webpack [gitter channel](https://gitter.im/webpack/webpack).\n\nIf you are worried or don't know where to start, you can **always** reach out to [Sean Larkin (@TheLarkInn) on Twitter](https://twitter.com/thelarkinn) or simply submit an issue and a maintainer can help give you guidance!\n\nWe have also started a series on our [Medium Publication](https://medium.com/webpack) called [The Contributor's Guide to webpack](https://medium.com/webpack/contributors-guide/home). We welcome you to read it and post any questions or responses if you still need help.\n\n_Looking to speak about webpack?_ We'd **love** to review your talk abstract/CFP! You can email it to webpack [at] opencollective [dot] com and we can give pointers or tips!!!\n\n<h3 align=\"center\">Creating your own plugins and loaders</h3>\n\nIf you create a loader or plugin, we would <3 for you to open source it, and put it on npm. We follow the `x-loader`, `x-webpack-plugin` naming convention.\n\n<h2 align=\"center\">Support</h2>\n\nWe consider webpack to be a low-level tool used not only individually but also layered beneath other awesome tools. Because of its flexibility, webpack isn't always the _easiest_ entry-level solution, however we do believe it is the most powerful. That said, we're always looking for ways to improve and simplify the tool without compromising functionality. If you have any ideas on ways to accomplish this, we're all ears!\n\nIf you're just getting started, take a look at [our new docs and concepts page](https://webpack.js.org/concepts/). This has a high level overview that is great for beginners!!\n\nLooking for webpack 1 docs? Please check out the old [wiki](https://github.com/webpack/docs/wiki/contents), but note that this deprecated version is no longer supported.\n\nIf you want to discuss something or just need help, [here is our Gitter room](https://gitter.im/webpack/webpack) where there are always individuals looking to help out!\n\nIf you are still having difficulty, we would love for you to post\na question to [StackOverflow with the webpack tag](https://stackoverflow.com/tags/webpack). It is much easier to answer questions that include your webpack.config.js and relevant files! So if you can provide them, we'd be extremely grateful (and more likely to help you find the answer!)\n\nIf you are twitter savvy you can tweet #webpack with your question and someone should be able to reach out and help also.\n\nIf you have discovered a 🐜 or have a feature suggestion, feel free to create an issue on Github.\n\n### License\n\n[![FOSSA Status](https://app.fossa.io/api/projects/git%2Bhttps%3A%2F%2Fgithub.com%2Fwebpack%2Fwebpack.svg?type=large)](https://app.fossa.io/projects/git%2Bhttps%3A%2F%2Fgithub.com%2Fwebpack%2Fwebpack?ref=badge_large)\n\n<h2 align=\"center\">Core Team</h2>\n\n<table>\n  <tbody>\n    <tr>\n      <td align=\"center\" valign=\"top\">\n        <img width=\"150\" height=\"150\" src=\"https://github.com/sokra.png?s=150\">\n        <br>\n        <a href=\"https://github.com/sokra\">Tobias Koppers</a>\n        <p>Core</p>\n        <br>\n        <p>Founder of webpack</p>\n      </td>\n      <td align=\"center\" valign=\"top\">\n        <img width=\"150\" height=\"150\" src=\"https://github.com/jhnns.png?s=150\">\n        <br>\n        <a href=\"https://github.com/jhnns\">Johannes Ewald</a>\n        <p>Loaders &amp; Plugins</p>\n        <br>\n        <p>Early adopter of webpack</p>\n      </td>\n      <td align=\"center\" width=\"20%\" valign=\"top\">\n        <img width=\"150\" height=\"150\" src=\"https://github.com/TheLarkInn.png?s=150\">\n        <br>\n        <a href=\"https://github.com/TheLarkInn\">Sean T. Larkin</a>\n        <p>Public Relations</p>\n        <br>\n        <p>Founder of the core team</p>\n      </td>\n      <td align=\"center\" valign=\"top\">\n        <img width=\"150\" height=\"150\" src=\"https://github.com/spacek33z.png?s=150\">\n        <br>\n        <a href=\"https://github.com/spacek33z\">Kees Kluskens</a>\n        <p>Development</p>\n        <br>\n        <p>Sponsor</p>\n        <a href=\"https://codeyellow.nl/\">\n          <img height=\"15px\" src=\"https://cloud.githubusercontent.com/assets/1365881/20286583/ad62eb04-aac7-11e6-9c14-a0fef35b9b56.png\">\n        </a>\n\t\t<br>\n      </td>\n     </tr>\n  </tbody>\n</table>\n\n<h2 align=\"center\">Sponsoring</h2>\n\nMost of the core team members, webpack contributors and contributors in the ecosystem do this open source work in their free time. If you use webpack for a serious task, and you'd like us to invest more time on it, please donate. This project increases your income/productivity too. It makes development and applications faster and it reduces the required bandwidth.\n\nThis is how we use the donations:\n\n* Allow the core team to work on webpack\n* Thank contributors if they invested a large amount of time in contributing\n* Support projects in the ecosystem that are of great value for users\n* Support projects that are voted most (work in progress)\n* Infrastructure cost\n* Fees for money handling\n\n\n<h2 align=\"center\">Premium Partners</h2>\n\n<div align=\"center\">\n\n<a href=\"https://www.ag-grid.com/?utm_source=webpack&utm_medium=banner&utm_campaign=sponsorship\" target=\"_blank\"><img align=\"center\" src=\"https://raw.githubusercontent.com/webpack/media/2b399d58/horiz-banner-ad-ag-grid.png\">\n</a>\n\n</div>\n\n<h2 align=\"center\">Other Backers and Sponsors</h2>\n\nBefore we started using OpenCollective, donations were made anonymously. Now that we have made the switch, we would like to acknowledge these sponsors (and the ones who continue to donate using OpenCollective). If we've missed someone, please send us a PR, and we'll add you to this list.\n\n<div align=\"center\">\n\n[Google Angular Team](https://angular.io/), [Architects.io](http://architects.io/),\n<a href=\"https://moonmail.io\" target=\"_blank\" title=\"Email Marketing Software\"><img\nsrc=\"https://static.moonmail.io/moonmail-logo.svg\" height=\"30\" alt=\"MoonMail\"></a>\n<a href=\"https://monei.net\" target=\"_blank\" title=\"Best payment gateway rates\"><img\nsrc=\"https://static.monei.net/monei-logo.svg\" height=\"30\" alt=\"MONEI\"></a>\n\n</div>\n\n<h2 align=\"center\">Gold Sponsors</h2>\n\n[Become a gold sponsor](https://opencollective.com/webpack#sponsor) and get your logo on our README on Github with a link to your site.\n\n<div align=\"center\">\n\n<a href=\"https://opencollective.com/webpack/goldsponsor/0/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/0/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/1/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/1/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/2/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/2/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/3/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/3/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/4/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/4/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/5/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/5/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/6/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/6/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/7/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/7/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/8/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/8/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/9/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/9/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/10/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/10/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/11/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/11/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/12/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/12/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/13/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/13/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/14/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/14/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/15/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/15/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/16/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/16/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/17/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/17/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/18/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/18/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/19/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/19/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/20/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/20/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/21/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/21/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/22/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/22/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/23/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/23/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/24/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/24/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/25/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/25/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/26/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/26/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/27/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/27/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/28/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/28/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/goldsponsor/29/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/goldsponsor/29/avatar.svg?requireActive=false\"></a>\n\n</div>\n\n<h2 align=\"center\">Silver Sponsors</h2>\n\n[Become a silver sponsor](https://opencollective.com/webpack#sponsor) and get your logo on our README on Github with a link to your site.\n\n<div align=\"center\">\n\n<a href=\"https://opencollective.com/webpack/silversponsor/0/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/0/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/1/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/1/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/2/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/2/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/3/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/3/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/4/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/4/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/5/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/5/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/6/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/6/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/7/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/7/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/8/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/8/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/9/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/9/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/10/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/10/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/11/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/11/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/12/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/12/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/13/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/13/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/14/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/14/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/15/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/15/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/16/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/16/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/17/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/17/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/18/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/18/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/19/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/19/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/20/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/20/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/21/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/21/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/22/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/22/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/23/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/23/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/24/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/24/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/25/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/25/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/26/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/26/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/27/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/27/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/28/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/28/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/silversponsor/29/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/silversponsor/29/avatar.svg?requireActive=false\"></a>\n\n</div>\n\n<h2 align=\"center\">Bronze Sponsors</h2>\n\n[Become a bronze sponsor](https://opencollective.com/webpack#sponsor) and get your logo on our README on Github with a link to your site.\n\n<div align=\"center\">\n\n<a href=\"https://opencollective.com/webpack/sponsor/0/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/0/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/1/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/1/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/2/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/2/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/3/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/3/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/4/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/4/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/5/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/5/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/6/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/6/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/7/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/7/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/8/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/8/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/9/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/9/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/10/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/10/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/11/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/11/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/12/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/12/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/13/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/13/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/14/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/14/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/15/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/15/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/16/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/16/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/17/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/17/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/18/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/18/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/19/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/19/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/20/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/20/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/21/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/21/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/22/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/22/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/23/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/23/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/24/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/24/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/25/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/25/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/26/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/26/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/27/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/27/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/28/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/28/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/29/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/29/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/30/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/30/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/31/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/31/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/32/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/32/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/33/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/33/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/34/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/34/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/35/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/35/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/36/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/36/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/37/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/37/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/38/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/38/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/39/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/39/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/40/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/40/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/41/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/41/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/42/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/42/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/43/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/43/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/44/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/44/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/45/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/45/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/46/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/46/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/47/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/47/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/48/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/48/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/49/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/49/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/50/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/50/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/51/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/51/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/52/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/52/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/53/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/53/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/54/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/54/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/55/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/55/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/56/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/56/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/57/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/57/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/58/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/58/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/59/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/59/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/60/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/60/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/61/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/61/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/62/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/62/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/63/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/63/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/64/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/64/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/65/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/65/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/66/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/66/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/67/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/67/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/68/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/68/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/69/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/69/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/70/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/70/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/71/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/71/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/72/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/72/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/73/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/73/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/74/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/74/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/75/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/75/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/76/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/76/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/77/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/77/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/78/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/78/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/79/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/79/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/80/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/80/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/81/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/81/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/82/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/82/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/83/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/83/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/84/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/84/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/85/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/85/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/86/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/86/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/87/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/87/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/88/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/88/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/89/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/89/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/90/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/90/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/91/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/91/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/92/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/92/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/93/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/93/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/94/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/94/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/95/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/95/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/96/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/96/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/97/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/97/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/98/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/98/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/99/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/99/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/sponsor/100/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/sponsor/100/avatar.svg?requireActive=false\"></a>\n\n</div>\n\n<h2 align=\"center\">Backers</h2>\n\n[Become a backer](https://opencollective.com/webpack#backer) and get your image on our README on Github with a link to your site.\n\n<a href=\"https://opencollective.com/webpack/backer/0/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/0/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/1/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/1/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/2/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/2/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/3/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/3/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/4/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/4/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/5/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/5/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/6/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/6/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/7/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/7/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/8/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/8/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/9/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/9/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/10/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/10/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/11/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/11/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/12/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/12/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/13/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/13/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/14/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/14/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/15/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/15/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/16/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/16/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/17/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/17/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/18/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/18/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/19/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/19/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/20/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/20/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/21/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/21/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/22/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/22/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/23/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/23/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/24/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/24/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/25/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/25/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/26/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/26/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/27/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/27/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/28/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/28/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/29/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/29/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/30/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/30/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/31/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/31/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/32/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/32/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/33/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/33/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/34/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/34/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/35/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/35/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/36/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/36/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/37/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/37/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/38/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/38/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/39/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/39/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/40/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/40/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/41/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/41/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/42/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/42/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/43/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/43/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/44/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/44/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/45/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/45/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/46/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/46/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/47/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/47/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/48/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/48/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/49/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/49/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/50/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/50/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/51/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/51/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/52/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/52/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/53/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/53/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/54/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/54/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/55/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/55/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/56/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/56/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/57/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/57/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/58/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/58/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/59/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/59/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/60/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/60/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/61/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/61/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/62/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/62/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/63/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/63/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/64/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/64/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/65/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/65/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/66/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/66/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/67/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/67/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/68/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/68/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/69/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/69/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/70/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/70/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/71/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/71/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/72/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/72/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/73/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/73/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/74/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/74/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/75/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/75/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/76/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/76/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/77/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/77/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/78/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/78/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/79/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/79/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/80/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/80/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/81/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/81/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/82/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/82/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/83/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/83/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/84/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/84/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/85/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/85/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/86/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/86/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/87/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/87/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/88/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/88/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/89/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/89/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/90/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/90/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/91/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/91/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/92/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/92/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/93/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/93/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/94/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/94/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/95/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/95/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/96/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/96/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/97/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/97/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/98/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/98/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/99/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/99/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/webpack/backer/100/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/webpack/backer/100/avatar.svg?requireActive=false\"></a>\n\n<h2 align=\"center\">Special Thanks to</h2>\n<p align=\"center\">(In chronological order)</p>\n\n* @google for [Google Web Toolkit (GWT)](http://www.gwtproject.org/), which aims to compile Java to JavaScript. It features a similar [Code Splitting](http://www.gwtproject.org/doc/latest/DevGuideCodeSplitting.html) as webpack.\n* @medikoo for [modules-webmake](https://github.com/medikoo/modules-webmake), which is a similar project. webpack was born because I wanted Code Splitting for modules-webmake. Interestingly the [Code Splitting issue is still open](https://github.com/medikoo/modules-webmake/issues/7) (thanks also to @Phoscur for the discussion).\n* @substack for [browserify](http://browserify.org/), which is a similar project and source for many ideas.\n* @jrburke for [require.js](http://requirejs.org/), which is a similar project and source for many ideas.\n* @defunctzombie for the [browser-field spec](https://gist.github.com/defunctzombie/4339901), which makes modules available for node.js, browserify and webpack.\n* Every early webpack user, which contributed to webpack by writing issues or PRs. You influenced the direction...\n* @shama, @jhnns and @sokra for maintaining this project\n* Everyone who has written a loader for webpack. You are the ecosystem...\n* Everyone I forgot to mention here, but also influenced webpack.\n\n\n[npm]: https://img.shields.io/npm/v/webpack.svg\n[npm-url]: https://npmjs.com/package/webpack\n\n[node]: https://img.shields.io/node/v/webpack.svg\n[node-url]: https://nodejs.org\n\n[deps]: https://img.shields.io/david/webpack/webpack.svg\n[deps-url]: https://david-dm.org/webpack/webpack\n\n[tests]: https://img.shields.io/travis/webpack/webpack/master.svg\n[tests-url]: https://travis-ci.org/webpack/webpack\n\n[prs]: https://img.shields.io/badge/PRs-welcome-brightgreen.svg\n[prs-url]: https://webpack.js.org/contribute/\n\n[builds-url]: https://ci.appveyor.com/project/sokra/webpack/branch/master\n[builds]: https://ci.appveyor.com/api/projects/status/github/webpack/webpack?svg=true\n\n[builds2]: https://dev.azure.com/webpack/webpack/_apis/build/status/webpack.webpack\n[builds2-url]: https://dev.azure.com/webpack/webpack/_build/latest?definitionId=3\n\n[licenses-url]: https://app.fossa.io/projects/git%2Bhttps%3A%2F%2Fgithub.com%2Fwebpack%2Fwebpack?ref=badge_shield\n[licenses]: https://app.fossa.io/api/projects/git%2Bhttps%3A%2F%2Fgithub.com%2Fwebpack%2Fwebpack.svg?type=shield\n\n[cover]: https://img.shields.io/coveralls/webpack/webpack.svg\n[cover-url]: https://coveralls.io/r/webpack/webpack/\n", "licenseText": "Copyright JS Foundation and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/webpack/-/webpack-4.46.0.tgz#bf9b4404ea20a073605e0a011d188d77cb6ad542", "type": "tarball", "reference": "https://registry.yarnpkg.com/webpack/-/webpack-4.46.0.tgz", "hash": "bf9b4404ea20a073605e0a011d188d77cb6ad542", "integrity": "sha512-6jJuJjg8znb/xRItk7bkT0+Q7AHCYjjFnvKIWQPkNIOyRqoCGvkOs0ipeQzrqz4l5FtN5ZI/ukEHroeX/o1/5Q==", "registry": "npm", "packageName": "webpack", "cacheIntegrity": "sha512-6jJuJjg8znb/xRItk7bkT0+Q7AHCYjjFnvKIWQPkNIOyRqoCGvkOs0ipeQzrqz4l5FtN5ZI/ukEHroeX/o1/5Q== sha1-v5tEBOogoHNgXgoBHRiNd8tq1UI="}, "registry": "npm", "hash": "bf9b4404ea20a073605e0a011d188d77cb6ad542"}