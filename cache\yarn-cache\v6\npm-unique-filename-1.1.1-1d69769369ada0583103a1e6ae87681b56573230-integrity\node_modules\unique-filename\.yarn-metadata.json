{"manifest": {"name": "unique-filename", "version": "1.1.1", "description": "Generate a unique filename for use in temporary directories or caches.", "main": "index.js", "scripts": {"test": "standard && tap test"}, "repository": {"type": "git", "url": "https://github.com/iarna/unique-filename.git"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/unique-filename/issues"}, "homepage": "https://github.com/iarna/unique-filename", "devDependencies": {"standard": "^5.4.1", "tap": "^2.3.1"}, "dependencies": {"unique-slug": "^2.0.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-unique-filename-1.1.1-1d69769369ada0583103a1e6ae87681b56573230-integrity\\node_modules\\unique-filename\\package.json", "readmeFilename": "README.md", "readme": "unique-filename\n===============\n\nGenerate a unique filename for use in temporary directories or caches.\n\n```\nvar uniqueFilename = require('unique-filename')\n\n// returns something like: /tmp/912ec803b2ce49e4a541068d495ab570\nvar randomTmpfile = uniqueFilename(os.tmpdir())\n\n// returns something like: /tmp/my-test-912ec803b2ce49e4a541068d495ab570\nvar randomPrefixedTmpfile = uniqueFilename(os.tmpdir(), 'my-test')\n\nvar uniqueTmpfile = uniqueFilename('/tmp', 'testing', '/my/thing/to/uniq/on')\n```\n\n### uniqueFilename(*dir*, *fileprefix*, *uniqstr*) → String\n\nReturns the full path of a unique filename that looks like:\n`dir/prefix-7ddd44c0`\nor `dir/7ddd44c0`\n\n*dir* – The path you want the filename in. `os.tmpdir()` is a good choice for this.\n\n*fileprefix* – A string to append prior to the unique part of the filename.\nThe parameter is required if *uniqstr* is also passed in but is otherwise\noptional and can be `undefined`/`null`/`''`. If present and not empty\nthen this string plus a hyphen are prepended to the unique part.\n\n*uniqstr* – Optional, if not passed the unique part of the resulting\nfilename will be random.  If passed in it will be generated from this string\nin a reproducable way.\n", "licenseText": "Copyright npm, Inc\n\nPermission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING AL<PERSON> IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, <PERSON>ATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/unique-filename/-/unique-filename-1.1.1.tgz#1d69769369ada0583103a1e6ae87681b56573230", "type": "tarball", "reference": "https://registry.yarnpkg.com/unique-filename/-/unique-filename-1.1.1.tgz", "hash": "1d69769369ada0583103a1e6ae87681b56573230", "integrity": "sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ==", "registry": "npm", "packageName": "unique-filename", "cacheIntegrity": "sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ== sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA="}, "registry": "npm", "hash": "1d69769369ada0583103a1e6ae87681b56573230"}