{"manifest": {"name": "@citizenfx/three", "version": "0.100.0", "description": "JavaScript 3D library", "main": "build/three.js", "repository": {"type": "git", "url": "https://github.com/citizenfx/three.js.git"}, "jsnext:main": "build/three.module.js", "module": "build/three.module.js", "files": ["package.json", "LICENSE", "README.md", "build/three.js", "build/three.min.js", "build/three.module.js", "src", "examples/js", "examples/fonts"], "eslintConfig": {"extends": "mdcs", "plugins": ["html"]}, "scripts": {"build": "rollup -c", "build-test": "rollup -c test/rollup.unit.config.js", "build-closure": "rollup -c && java -jar node_modules/google-closure-compiler-java/compiler.jar --warning_level=VERBOSE --jscomp_off=globalThis --jscomp_off=checkTypes --externs utils/build/externs.js --language_in=ECMASCRIPT5_STRICT --js build/three.js --js_output_file build/three.min.js", "build-examples": "rollup -c rollup-examples.config.js", "dev": "concurrently --names \"R<PERSON><PERSON><PERSON>,HTTP\" -c \"bgBlue.bold,bgGreen.bold\" \"rollup -c -w -m inline\" \"http-server -c-1 -p 8080\"", "dev-test": "concurrently --names \"ROLLUP,ROLLUPTEST,HTTP\" -c \"bgBlue.bold,bgRed.bold,bgGreen.bold\" \"rollup -c -w -m inline\" \"rollup -c test/rollup.unit.config.js -w -m inline\" \"http-server -p 8080\"", "start": "npm run dev", "lint": "eslint src", "test": "npm run build-test && qunit test/unit/three.source.unit.js", "travis": "npm run lint && npm test", "editor": "electron ./editor/main.js"}, "keywords": ["three", "three.js", "3d", "webgl"], "author": {"name": "m<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mrdoob/three.js/issues"}, "homepage": "https://threejs.org/", "devDependencies": {"concurrently": "^4.1.0", "electron": "^4.0.0", "eslint": "^5.11.1", "eslint-config-mdcs": "^4.2.3", "eslint-plugin-html": "^5.0.0", "google-closure-compiler": "20181210.0.0", "http-server": "^0.11.1", "qunit": "^2.8.0", "rollup": "^1.0.0"}, "jspm": {"files": ["package.json", "LICENSE", "README.md", "build/three.js", "build/three.min.js", "build/three.module.js"], "directories": {}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@citizenfx-three-0.100.0-ac605a1f86863e25e5b3c50d9ae80a2ef2a2e361-integrity\\node_modules\\@citizenfx\\three\\package.json", "readmeFilename": "README.md", "readme": "three.js\n========\n\n[![NPM package][npm]][npm-url]\n[![Build Size][build-size]][build-size-url]\n[![Build Status][build-status]][build-status-url]\n[![Dependencies][dependencies]][dependencies-url]\n[![Dev Dependencies][dev-dependencies]][dev-dependencies-url]\n[![Language Grade][lgtm]][lgtm-url]\n\n#### JavaScript 3D library ####\n\nThe aim of the project is to create an easy to use, lightweight, 3D library. The library provides Canvas 2D, SVG, CSS3D and WebGL renderers.\n\n[Examples](http://threejs.org/examples/) &mdash;\n[Documentation](http://threejs.org/docs/) &mdash;\n[Wiki](https://github.com/mrdoob/three.js/wiki) &mdash;\n[Migrating](https://github.com/mrdoob/three.js/wiki/Migration-Guide) &mdash;\n[Questions](http://stackoverflow.com/questions/tagged/three.js) &mdash;\n[Forum](https://discourse.threejs.org/) &mdash;\n[Gitter](https://gitter.im/mrdoob/three.js) &mdash;\n[Slack](https://threejs-slack.herokuapp.com/)\n\n### Usage ###\n\nDownload the [minified library](http://threejs.org/build/three.min.js) and include it in your HTML, or install and import it as a [module](http://threejs.org/docs/#manual/introduction/Import-via-modules),\nAlternatively see [how to build the library yourself](https://github.com/mrdoob/three.js/wiki/Build-instructions).\n\n```html\n<script src=\"js/three.min.js\"></script>\n```\n\nThis code creates a scene, a camera, and a geometric cube, and it adds the cube to the scene. It then creates a `WebGL` renderer for the scene and camera, and it adds that viewport to the document.body element. Finally, it animates the cube within the scene for the camera.\n\n```javascript\nvar camera, scene, renderer;\nvar geometry, material, mesh;\n\ninit();\nanimate();\n\nfunction init() {\n\n\tcamera = new THREE.PerspectiveCamera( 70, window.innerWidth / window.innerHeight, 0.01, 10 );\n\tcamera.position.z = 1;\n\n\tscene = new THREE.Scene();\n\n\tgeometry = new THREE.BoxGeometry( 0.2, 0.2, 0.2 );\n\tmaterial = new THREE.MeshNormalMaterial();\n\n\tmesh = new THREE.Mesh( geometry, material );\n\tscene.add( mesh );\n\n\trenderer = new THREE.WebGLRenderer( { antialias: true } );\n\trenderer.setSize( window.innerWidth, window.innerHeight );\n\tdocument.body.appendChild( renderer.domElement );\n\n}\n\nfunction animate() {\n\n\trequestAnimationFrame( animate );\n\n\tmesh.rotation.x += 0.01;\n\tmesh.rotation.y += 0.02;\n\n\trenderer.render( scene, camera );\n\n}\n```\n\nIf everything went well you should see [this](https://jsfiddle.net/f2Lommf5/).\n\n### Change log ###\n\n[Releases](https://github.com/mrdoob/three.js/releases)\n\n\n[npm]: https://img.shields.io/npm/v/three.svg\n[npm-url]: https://www.npmjs.com/package/three\n[build-size]: https://badgen.net/bundlephobia/minzip/three\n[build-size-url]: https://bundlephobia.com/result?p=three\n[build-status]: https://travis-ci.org/mrdoob/three.js.svg?branch=dev\n[build-status-url]: https://travis-ci.org/mrdoob/three.js\n[dependencies]: https://img.shields.io/david/mrdoob/three.js.svg\n[dependencies-url]: https://david-dm.org/mrdoob/three.js\n[dev-dependencies]: https://img.shields.io/david/dev/mrdoob/three.js.svg\n[dev-dependencies-url]: https://david-dm.org/mrdoob/three.js#info=devDependencies\n[lgtm]: https://img.shields.io/lgtm/grade/javascript/g/mrdoob/three.js.svg?label=code%20quality\n[lgtm-url]: https://lgtm.com/projects/g/mrdoob/three.js/\n", "licenseText": "The MIT License\n\nCopyright © 2010-2019 three.js authors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@citizenfx/three/-/three-0.100.0.tgz#ac605a1f86863e25e5b3c50d9ae80a2ef2a2e361", "type": "tarball", "reference": "https://registry.yarnpkg.com/@citizenfx/three/-/three-0.100.0.tgz", "hash": "ac605a1f86863e25e5b3c50d9ae80a2ef2a2e361", "integrity": "sha512-qiJHVGNzhGfZP+poq4X3m9cDoi4H1mK4UgfbBTZoHmtSIiDRAIYEWsb93MHst+ADHtn0vJ8msMRQaAHC3horSA==", "registry": "npm", "packageName": "@citizenfx/three", "cacheIntegrity": "sha512-qiJHVGNzhGfZP+poq4X3m9cDoi4H1mK4UgfbBTZoHmtSIiDRAIYEWsb93MHst+ADHtn0vJ8msMRQaAHC3horSA== sha1-rGBaH4aGPiXls8UNmugKLvKi42E="}, "registry": "npm", "hash": "ac605a1f86863e25e5b3c50d9ae80a2ef2a2e361"}