function GetVehicleIdentifier(pVehicle)
   return exports['qb-vehicles']:GetVehicleIdentifier(pVehicle)
end

function HasVehicleKey(pVehicle)
   return exports['qb-vehicles']:HasVehicleKey(pVehicle)
end

function GetVehicleDegradation(pVehicle)
   return exports['qb-vehicles']:GetVehicleDegradation(pVehicle)
end

function GetVehicleFuel(pVehicle)
    return exports['qb-vehicles']:GetVehicleFuel(pVehicle)
end

function CurrentFuel(pVehicle)
   return exports['qb-vehicles']:CurrentFuel(pVehicle)
end

function IsOnParkingSpot(pEntity, pEntity, pRadius)
   return exports['qb-vehicles']:IsOnParkingSpot(pEntity, pEntity, pRadius)
end

function VehicleHasHarness(pVehicle)
   return exports['qb-vehicles']:VehicleHasHarness(pVehicle)
end

function GetHarnessLevel(pVehicle)
   return exports['qb-vehicles']:GetHarnessLevel(pVehicle)
end

function GetHarnessLevel(pVehicle)
   return exports['qb-vehicles']:GetHarnessLevel(pVehicle)
end

function IsUsingNitro()
   return exports['qb-vehicles']:IsUsingNitro()
end

function VehicleHasNitro(pVehicle)
   return exports['qb-vehicles']:VehicleHasNitro(pVehicle)
end

function GetNitroLevel(pVehicle)
   return exports['qb-vehicles']:GetNitroLevel(pVehicle)
end

function GetVehicleMetadata(pVehicle, pKey)
   return exports['qb-vehicles']:GetVehicleMetadata(pVehicle, pKey)
end

function TurnOnEngine(pVehicle, pInstant)
   return exports['qb-vehicles']:TurnOnEngine(pVehicle, pInstant)
end

function TurnOffEngine(pVehicle, pInstant)
   return exports['qb-vehicles']:TurnOffEngine(pVehicle, pInstant)
end

function SwapVehicleSeat(pSeat, pVehicle)
   return exports['qb-vehicles']:SwapVehicleSeat(pSeat, pVehicle)
end

function SwapVehicleSeat(pSeat, pVehicle)
   return exports['qb-vehicles']:SwapVehicleSeat(pSeat, pVehicle)
end

function IsVinScratched(pVehicle)
   return exports['qb-vehicles']:IsVinScratched(pVehicle)
end
