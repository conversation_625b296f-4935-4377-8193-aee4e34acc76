{"manifest": {"name": "koa", "version": "2.13.1", "description": "Koa web app framework", "main": "lib/application.js", "exports": {".": {"require": "./lib/application.js", "import": "./dist/koa.mjs"}, "./": "./"}, "scripts": {"test": "egg-bin test test", "test-cov": "egg-bin cov test", "lint": "eslint benchmarks lib test", "bench": "make -C benchmarks", "authors": "git log --format='%aN <%aE>' | sort -u > AUTHORS", "build": "gen-esm-wrapper . ./dist/koa.mjs", "prepare": "npm run build"}, "repository": {"type": "git", "url": "https://github.com/koajs/koa.git"}, "keywords": ["web", "app", "http", "application", "framework", "middleware", "rack"], "license": "MIT", "dependencies": {"accepts": "^1.3.5", "cache-content-type": "^1.0.0", "content-disposition": "~0.5.2", "content-type": "^1.0.4", "cookies": "~0.8.0", "debug": "~3.1.0", "delegates": "^1.0.0", "depd": "^2.0.0", "destroy": "^1.0.4", "encodeurl": "^1.0.2", "escape-html": "^1.0.3", "fresh": "~0.5.2", "http-assert": "^1.3.0", "http-errors": "^1.6.3", "is-generator-function": "^1.0.7", "koa-compose": "^4.1.0", "koa-convert": "^1.2.0", "on-finished": "^2.3.0", "only": "~0.0.2", "parseurl": "^1.3.2", "statuses": "^1.5.0", "type-is": "^1.6.16", "vary": "^1.1.2"}, "devDependencies": {"egg-bin": "^4.13.0", "eslint": "^6.5.1", "eslint-config-koa": "^2.0.0", "eslint-config-standard": "^14.1.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-node": "^10.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "gen-esm-wrapper": "^1.0.6", "mm": "^2.5.0", "supertest": "^3.1.0"}, "engines": {"node": "^4.8.4 || ^6.10.1 || ^7.10.1 || >= 8.1.4"}, "files": ["dist", "lib"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-koa-2.13.1-6275172875b27bcfe1d454356a5b6b9f5a9b1051-integrity\\node_modules\\koa\\package.json", "readmeFilename": "Readme.md", "readme": "<img src=\"/docs/logo.png\" alt=\"Koa middleware framework for nodejs\"/>\n\n  [![gitter][gitter-image]][gitter-url]\n  [![NPM version][npm-image]][npm-url]\n  [![build status][travis-image]][travis-url]\n  [![Test coverage][coveralls-image]][coveralls-url]\n  [![OpenCollective Backers][backers-image]](#backers)\n  [![OpenCollective Sponsors][sponsors-image]](#sponsors)\n  [![PR's Welcome][pr-welcoming-image]][pr-welcoming-url]\n\n  Expressive HTTP middleware framework for node.js to make web applications and APIs more enjoyable to write. Koa's middleware stack flows in a stack-like manner, allowing you to perform actions downstream then filter and manipulate the response upstream.\n\n  Only methods that are common to nearly all HTTP servers are integrated directly into Koa's small ~570 SLOC codebase. This\n  includes things like content negotiation, normalization of node inconsistencies, redirection, and a few others.\n\n  Koa is not bundled with any middleware.\n\n## Installation\n\nKoa requires __node v7.6.0__ or higher for ES2015 and async function support.\n\n```\n$ npm install koa\n```\n\n## Hello Koa\n\n```js\nconst Koa = require('koa');\nconst app = new Koa();\n\n// response\napp.use(ctx => {\n  ctx.body = 'Hello Koa';\n});\n\napp.listen(3000);\n```\n\n## Getting started\n\n - [Kick-Off-Koa](https://github.com/koajs/kick-off-koa) - An intro to Koa via a set of self-guided workshops.\n - [Workshop](https://github.com/koajs/workshop) - A workshop to learn the basics of Koa, Express' spiritual successor.\n - [Introduction Screencast](https://knowthen.com/episode-3-koajs-quickstart-guide/) - An introduction to installing and getting started with Koa\n\n\n## Middleware\n\nKoa is a middleware framework that can take two different kinds of functions as middleware:\n\n  * async function\n  * common function\n\nHere is an example of logger middleware with each of the different functions:\n\n### ___async___ functions (node v7.6+)\n\n```js\napp.use(async (ctx, next) => {\n  const start = Date.now();\n  await next();\n  const ms = Date.now() - start;\n  console.log(`${ctx.method} ${ctx.url} - ${ms}ms`);\n});\n```\n\n### Common function\n\n```js\n// Middleware normally takes two parameters (ctx, next), ctx is the context for one request,\n// next is a function that is invoked to execute the downstream middleware. It returns a Promise with a then function for running code after completion.\n\napp.use((ctx, next) => {\n  const start = Date.now();\n  return next().then(() => {\n    const ms = Date.now() - start;\n    console.log(`${ctx.method} ${ctx.url} - ${ms}ms`);\n  });\n});\n```\n\n### Koa v1.x Middleware Signature\n\nThe middleware signature changed between v1.x and v2.x.  The older signature is deprecated.\n\n**Old signature middleware support will be removed in v3**\n\nPlease see the [Migration Guide](docs/migration.md) for more information on upgrading from v1.x and\nusing v1.x middleware with v2.x.\n\n## Context, Request and Response\n\nEach middleware receives a Koa `Context` object that encapsulates an incoming\nhttp message and the corresponding response to that message.  `ctx` is often used\nas the parameter name for the context object.\n\n```js\napp.use(async (ctx, next) => { await next(); });\n```\n\nKoa provides a `Request` object as the `request` property of the `Context`.  \nKoa's `Request` object provides helpful methods for working with\nhttp requests which delegate to an [IncomingMessage](https://nodejs.org/api/http.html#http_class_http_incomingmessage)\nfrom the node `http` module.\n\nHere is an example of checking that a requesting client supports xml.\n\n```js\napp.use(async (ctx, next) => {\n  ctx.assert(ctx.request.accepts('xml'), 406);\n  // equivalent to:\n  // if (!ctx.request.accepts('xml')) ctx.throw(406);\n  await next();\n});\n```\n\nKoa provides a `Response` object as the `response` property of the `Context`.  \nKoa's `Response` object provides helpful methods for working with\nhttp responses which delegate to a [ServerResponse](https://nodejs.org/api/http.html#http_class_http_serverresponse)\n.  \n\nKoa's pattern of delegating to Node's request and response objects rather than extending them\nprovides a cleaner interface and reduces conflicts between different middleware and with Node\nitself as well as providing better support for stream handling.  The `IncomingMessage` can still be\ndirectly accessed as the `req` property on the `Context` and `ServerResponse` can be directly\naccessed as the `res` property on the `Context`.\n\nHere is an example using Koa's `Response` object to stream a file as the response body.\n\n```js\napp.use(async (ctx, next) => {\n  await next();\n  ctx.response.type = 'xml';\n  ctx.response.body = fs.createReadStream('really_large.xml');\n});\n```\n\nThe `Context` object also provides shortcuts for methods on its `request` and `response`.  In the prior\nexamples,  `ctx.type` can be used instead of `ctx.response.type` and `ctx.accepts` can be used\ninstead of `ctx.request.accepts`.\n\nFor more information on `Request`, `Response` and `Context`, see the [Request API Reference](docs/api/request.md),\n[Response API Reference](docs/api/response.md) and [Context API Reference](docs/api/context.md).\n\n## Koa Application\n\nThe object created when executing `new Koa()` is known as the Koa application object.\n\nThe application object is Koa's interface with node's http server and handles the registration\nof middleware, dispatching to the middleware from http, default error handling, as well as\nconfiguration of the context, request and response objects.\n\nLearn more about the application object in the [Application API Reference](docs/api/index.md).\n\n## Documentation\n\n - [Usage Guide](docs/guide.md)\n - [Error Handling](docs/error-handling.md)\n - [Koa for Express Users](docs/koa-vs-express.md)\n - [FAQ](docs/faq.md)\n - [API documentation](docs/api/index.md)\n\n## Troubleshooting\n\nCheck the [Troubleshooting Guide](docs/troubleshooting.md) or [Debugging Koa](docs/guide.md#debugging-koa) in\nthe general Koa guide.\n\n## Running tests\n\n```\n$ npm test\n```\n\n## Reporting vulnerabilities\n\nTo report a security vulnerability, please do not open an issue, as this notifies attackers of the vulnerability. Instead, please email [dead_horse](mailto:<EMAIL>), [jonathanong](mailto:<EMAIL>), and [niftylettuce](mailto:<EMAIL>) to disclose.\n\n## Authors\n\nSee [AUTHORS](AUTHORS).\n\n## Community\n\n - [Badgeboard](https://koajs.github.io/badgeboard) and list of official modules\n - [Examples](https://github.com/koajs/examples)\n - [Middleware](https://github.com/koajs/koa/wiki) list\n - [Wiki](https://github.com/koajs/koa/wiki)\n - [Reddit Community](https://www.reddit.com/r/koajs)\n - [Mailing list](https://groups.google.com/forum/#!forum/koajs)\n - [中文文档 v1.x](https://github.com/guo-yu/koa-guide)\n - [中文文档 v2.x](https://github.com/demopark/koa-docs-Zh-CN)\n - __[#koajs]__ on freenode\n\n## Job Board\n\nLooking for a career upgrade?\n\n<a href=\"https://astro.netlify.com/automattic\"><img src=\"https://astro.netlify.com/static/automattic.png\"></a>\n<a href=\"https://astro.netlify.com/segment\"><img src=\"https://astro.netlify.com/static/segment.png\"></a>\n<a href=\"https://astro.netlify.com/auth0\"><img src=\"https://astro.netlify.com/static/auth0.png\"/></a>\n\n## Backers\n\nSupport us with a monthly donation and help us continue our activities.\n\n<a href=\"https://opencollective.com/koajs/backer/0/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/0/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/1/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/1/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/2/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/2/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/3/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/3/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/4/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/4/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/5/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/5/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/6/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/6/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/7/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/7/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/8/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/8/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/9/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/9/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/10/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/10/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/11/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/11/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/12/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/12/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/13/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/13/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/14/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/14/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/15/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/15/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/16/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/16/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/17/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/17/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/18/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/18/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/19/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/19/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/20/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/20/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/21/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/21/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/22/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/22/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/23/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/23/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/24/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/24/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/25/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/25/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/26/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/26/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/27/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/27/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/28/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/28/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/backer/29/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/backer/29/avatar.svg\"></a>\n\n\n## Sponsors\n\nBecome a sponsor and get your logo on our README on Github with a link to your site.\n\n<a href=\"https://opencollective.com/koajs/sponsor/0/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/0/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/1/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/1/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/2/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/2/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/3/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/3/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/4/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/4/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/5/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/5/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/6/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/6/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/7/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/7/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/8/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/8/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/9/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/9/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/10/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/10/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/11/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/11/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/12/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/12/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/13/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/13/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/14/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/14/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/15/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/15/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/16/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/16/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/17/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/17/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/18/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/18/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/19/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/19/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/20/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/20/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/21/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/21/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/22/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/22/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/23/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/23/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/24/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/24/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/25/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/25/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/26/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/26/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/27/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/27/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/28/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/28/avatar.svg\"></a>\n<a href=\"https://opencollective.com/koajs/sponsor/29/website\" target=\"_blank\"><img src=\"https://opencollective.com/koajs/sponsor/29/avatar.svg\"></a>\n\n# License\n\n  [MIT](https://github.com/koajs/koa/blob/master/LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/koa.svg?style=flat-square\n[npm-url]: https://www.npmjs.com/package/koa\n[travis-image]: https://img.shields.io/travis/koajs/koa/master.svg?style=flat-square\n[travis-url]: https://travis-ci.org/koajs/koa\n[coveralls-image]: https://img.shields.io/codecov/c/github/koajs/koa.svg?style=flat-square\n[coveralls-url]: https://codecov.io/github/koajs/koa?branch=master\n[backers-image]: https://opencollective.com/koajs/backers/badge.svg?style=flat-square\n[sponsors-image]: https://opencollective.com/koajs/sponsors/badge.svg?style=flat-square\n[gitter-image]: https://img.shields.io/gitter/room/koajs/koa.svg?style=flat-square\n[gitter-url]: https://gitter.im/koajs/koa?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge&utm_content=badge\n[#koajs]: https://webchat.freenode.net/?channels=#koajs\n[pr-welcoming-image]: https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square\n[pr-welcoming-url]: https://github.com/koajs/koa/pull/new\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2019 Koa contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/koa/-/koa-2.13.1.tgz#6275172875b27bcfe1d454356a5b6b9f5a9b1051", "type": "tarball", "reference": "https://registry.yarnpkg.com/koa/-/koa-2.13.1.tgz", "hash": "6275172875b27bcfe1d454356a5b6b9f5a9b1051", "integrity": "sha512-Lb2Dloc72auj5vK4X4qqL7B5jyDPQaZucc9sR/71byg7ryoD1NCaCm63CShk9ID9quQvDEi1bGR/iGjCG7As3w==", "registry": "npm", "packageName": "koa", "cacheIntegrity": "sha512-Lb2Dloc72auj5vK4X4qqL7B5jyDPQaZucc9sR/71byg7ryoD1NCaCm63CShk9ID9quQvDEi1bGR/iGjCG7As3w== sha1-YnUXKHWye8/h1FQ1altrn1qbEFE="}, "registry": "npm", "hash": "6275172875b27bcfe1d454356a5b6b9f5a9b1051"}