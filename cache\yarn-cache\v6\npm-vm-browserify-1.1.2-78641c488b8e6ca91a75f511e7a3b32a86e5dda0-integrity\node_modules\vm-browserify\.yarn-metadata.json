{"manifest": {"name": "vm-browserify", "version": "1.1.2", "description": "vm module for the browser", "main": "index.js", "repository": {"type": "git", "url": "http://github.com/substack/vm-browserify.git"}, "keywords": ["vm", "browser", "eval"], "dependencies": {}, "devDependencies": {"browserify": "^16.1.1", "tape": "^4.11.0", "tape-run": "^6.0.1"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "scripts": {"test": "browserify test/vm.js | tape-run"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-vm-browserify-1.1.2-78641c488b8e6ca91a75f511e7a3b32a86e5dda0-integrity\\node_modules\\vm-browserify\\package.json", "readmeFilename": "readme.markdown", "readme": "# vm-browserify\n\nemulate node's vm module for the browser\n\n[![Build Status](https://travis-ci.org/browserify/vm-browserify.svg?branch=master)](https://travis-ci.org/browserify/vm-browserify)\n\n# example\n\nJust write some client-side javascript:\n\n``` js\nvar vm = require('vm');\n\nwindow.addEventListener('load', function () {\n    var res = vm.runInNewContext('a + 5', { a : 100 });\n    document.querySelector('#res').textContent = res;\n});\n```\n\ncompile it with [browserify](http://github.com/substack/node-browserify):\n\n```\nbrowserify entry.js -o bundle.js\n```\n\nthen whip up some html:\n\n``` html\n<html>\n  <head>\n    <script src=\"/bundle.js\"></script>\n  </head>\n  <body>\n    result = <span id=\"res\"></span>\n  </body>\n</html>\n```\n\nand when you load the page you should see:\n\n```\nresult = 105\n```\n\n# methods\n\n## vm.runInNewContext(code, context={})\n\nEvaluate some `code` in a new iframe with a `context`.\n\nContexts are like wrapping your code in a `with()` except slightly less terrible\nbecause the code is sandboxed into a new iframe.\n\n# install\n\nThis module is depended upon by browserify, so you should just be able to\n`require('vm')` and it will just work. However if you want to use this module\ndirectly you can install it with [npm](http://npmjs.org):\n\n```\nnpm install vm-browserify\n```\n\n# license\n\nMIT\n", "licenseText": "MIT License\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\n<PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/vm-browserify/-/vm-browserify-1.1.2.tgz#78641c488b8e6ca91a75f511e7a3b32a86e5dda0", "type": "tarball", "reference": "https://registry.yarnpkg.com/vm-browserify/-/vm-browserify-1.1.2.tgz", "hash": "78641c488b8e6ca91a75f511e7a3b32a86e5dda0", "integrity": "sha512-2ham8XPWTONajOR0ohOKOHXkm3+gaBmGut3SRuu75xLd/RRaY6vqgh8NBYYk7+RW3u5AtzPQZG8F10LHkl0lAQ==", "registry": "npm", "packageName": "vm-browserify", "cacheIntegrity": "sha512-2ham8XPWTONajOR0ohOKOHXkm3+gaBmGut3SRuu75xLd/RRaY6vqgh8NBYYk7+RW3u5AtzPQZG8F10LHkl0lAQ== sha1-eGQcSIuObKkadfUR56OzKobl3aA="}, "registry": "npm", "hash": "78641c488b8e6ca91a75f511e7a3b32a86e5dda0"}