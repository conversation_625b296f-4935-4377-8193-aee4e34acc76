<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QB Smartphone 2</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: transparent;
            overflow: hidden;
            user-select: none;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        #root {
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            perspective: 1000px;
        }

        /* iPhone 16 Pro Frame */
        .phone-container {
            width: 393px;
            height: 852px;
            background: linear-gradient(145deg, #1d1d1f, #000000);
            border-radius: 50px;
            padding: 3px;
            box-shadow:
                0 0 0 1px #2c2c2e,
                0 0 0 3px #1d1d1f,
                0 30px 60px rgba(0, 0, 0, 0.9),
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
            position: relative;
            overflow: hidden;
            display: none;
            transform-style: preserve-3d;
        }

        /* iPhone Screen */
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #000000;
            border-radius: 47px;
            display: flex;
            flex-direction: column;
            color: white;
            position: relative;
            overflow: hidden;
        }

        /* Dynamic Island - přesně jako iPhone 16 Pro */
        .dynamic-island {
            position: absolute;
            top: 11px;
            left: 50%;
            transform: translateX(-50%);
            width: 126px;
            height: 37px;
            background: #000000;
            border-radius: 19px;
            z-index: 1000;
            box-shadow:
                inset 0 0 0 1px rgba(255, 255, 255, 0.1),
                0 2px 10px rgba(0, 0, 0, 0.3);
        }

        /* Wallpaper Background */
        .wallpaper-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                #1e3c72 0%,
                #2a5298 25%,
                #667eea 50%,
                #764ba2 75%,
                #f093fb 100%);
            opacity: 1;
        }

        /* Status Bar - přesně jako iPhone */
        .status-bar {
            position: absolute;
            top: 59px;
            left: 44px;
            right: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 17px;
            font-weight: 600;
            z-index: 100;
            color: white;
        }

        .status-left {
            display: flex;
            align-items: center;
        }

        .time {
            font-weight: 700;
            letter-spacing: -0.3px;
            font-size: 17px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 15px;
        }

        /* Signal Strength */
        .signal-strength {
            display: flex;
            gap: 2px;
            align-items: flex-end;
            height: 12px;
        }

        .signal-bar {
            width: 3px;
            background: white;
            border-radius: 1px;
        }

        .signal-bar:nth-child(1) { height: 3px; }
        .signal-bar:nth-child(2) { height: 5px; }
        .signal-bar:nth-child(3) { height: 7px; }
        .signal-bar:nth-child(4) { height: 9px; }

        /* WiFi Icon */
        .wifi-icon {
            font-size: 15px;
        }

        /* Battery */
        .battery-container {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .battery-percentage {
            font-size: 15px;
            font-weight: 500;
        }

        .battery-icon {
            width: 24px;
            height: 12px;
            border: 1px solid white;
            border-radius: 2px;
            position: relative;
            background: transparent;
        }

        .battery-icon::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 2px;
            width: 2px;
            height: 8px;
            background: white;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            height: 100%;
            background: #30d158;
            border-radius: 1px;
            width: 80%;
            transition: all 0.3s ease;
        }

        /* Home Screen Container */
        .home-screen {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 100px 24px 34px;
            position: relative;
            z-index: 10;
        }

        /* Apps Grid - přesně jako iOS */
        .apps-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-top: 20px;
            padding: 0 8px;
        }

        /* App Container */
        .app-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        /* App Icons - realistické iOS ikony s pokročilými animacemi */
        .app-icon {
            width: 60px;
            height: 60px;
            border-radius: 13px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            box-shadow:
                0 2px 8px rgba(0, 0, 0, 0.15),
                0 8px 24px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            transform: translateZ(0);
            backface-visibility: hidden;
        }

        .app-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 50%);
            border-radius: 13px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .app-icon::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        /* Specific App Styles */
        .app-phone { background: linear-gradient(135deg, #4ade80, #22c55e); }
        .app-messages { background: linear-gradient(135deg, #06d6a0, #118ab2); }
        .app-contacts { background: linear-gradient(135deg, #8b5cf6, #a855f7); }
        .app-camera { background: linear-gradient(135deg, #64748b, #475569); }
        .app-gallery { background: linear-gradient(135deg, #f59e0b, #d97706); }
        .app-maps { background: linear-gradient(135deg, #10b981, #059669); }
        .app-browser { background: linear-gradient(135deg, #3b82f6, #2563eb); }
        .app-notes { background: linear-gradient(135deg, #fbbf24, #f59e0b); }
        .app-darkweb { background: linear-gradient(135deg, #1f2937, #111827); }
        .app-banking { background: linear-gradient(135deg, #ef4444, #dc2626); }
        .app-marketplace { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
        .app-settings { background: linear-gradient(135deg, #6b7280, #4b5563); }

        .app-icon:hover {
            transform: scale(1.1) translateY(-2px);
            box-shadow:
                0 4px 16px rgba(0, 0, 0, 0.3),
                0 12px 32px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .app-icon:hover::before {
            opacity: 1;
        }

        .app-icon:active {
            transform: scale(0.9) translateY(0);
            transition: all 0.1s ease;
        }

        .app-icon:active::after {
            width: 100px;
            height: 100px;
            opacity: 0;
        }

        /* Wiggle animation pro dlouhé stisknutí */
        @keyframes wiggle {
            0%, 100% { transform: rotate(0deg) scale(1.05); }
            25% { transform: rotate(-1deg) scale(1.05); }
            75% { transform: rotate(1deg) scale(1.05); }
        }

        .app-icon.wiggle {
            animation: wiggle 0.3s ease-in-out infinite;
        }

        /* App Labels */
        .app-label {
            font-size: 12px;
            font-weight: 400;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
            text-align: center;
            max-width: 70px;
            line-height: 1.2;
        }

        /* Dock - přesně jako iOS */
        .dock {
            position: absolute;
            bottom: 34px;
            left: 24px;
            right: 24px;
            height: 84px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(40px);
            -webkit-backdrop-filter: blur(40px);
            border-radius: 28px;
            display: flex;
            align-items: center;
            justify-content: space-evenly;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .dock .app-icon {
            width: 60px;
            height: 60px;
            font-size: 28px;
        }

        /* Home Indicator */
        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 3px;
        }

        /* Pokročilé animace */
        .phone-container.visible {
            display: block;
            animation: phoneAppear 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .phone-container.closing {
            animation: phoneDisappear 0.4s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
        }

        @keyframes phoneAppear {
            0% {
                opacity: 0;
                transform: scale(0.8) translateY(50px) rotateX(10deg);
                filter: blur(10px);
            }
            50% {
                opacity: 0.8;
                transform: scale(0.95) translateY(10px) rotateX(2deg);
                filter: blur(2px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0) rotateX(0deg);
                filter: blur(0px);
            }
        }

        @keyframes phoneDisappear {
            0% {
                opacity: 1;
                transform: scale(1) translateY(0) rotateX(0deg);
                filter: blur(0px);
            }
            100% {
                opacity: 0;
                transform: scale(0.8) translateY(30px) rotateX(-5deg);
                filter: blur(5px);
            }
        }

        /* Floating animation pro telefon */
        @keyframes phoneFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-3px); }
        }

        .phone-container.visible {
            animation: phoneAppear 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275),
                       phoneFloat 4s ease-in-out infinite 1s;
        }

        /* Page Dots */
        .page-dots {
            position: absolute;
            bottom: 130px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
        }

        .page-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
        }

        .page-dot.active {
            background: rgba(255, 255, 255, 0.9);
        }

        /* Responsive */
        @media (max-height: 900px) {
            .phone-container {
                transform: scale(0.85);
            }
        }

        @media (max-height: 800px) {
            .phone-container {
                transform: scale(0.75);
            }
        }

        /* App Loading Styles */
        .app-loading {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(20px);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        .loading-text {
            color: white;
            font-size: 16px;
            font-weight: 500;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* App Interface Styles */
        .app-interface {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #000000;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            z-index: 500;
            display: flex;
            flex-direction: column;
        }

        .app-header {
            height: 60px;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(20px);
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-top: 100px;
        }

        .back-btn {
            background: none;
            border: none;
            color: #007AFF;
            font-size: 18px;
            cursor: pointer;
            padding: 10px;
            margin-right: 10px;
        }

        .app-header h2 {
            color: white;
            font-size: 18px;
            font-weight: 600;
        }

        .app-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        /* Phone App Styles */
        .phone-keypad {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .number-display input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 15px;
            color: white;
            font-size: 18px;
            text-align: center;
        }

        .keypad-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            width: 100%;
            max-width: 250px;
        }

        .keypad-btn {
            width: 70px;
            height: 70px;
            border-radius: 35px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 24px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .keypad-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }

        .call-btn {
            width: 200px;
            height: 50px;
            background: #34C759;
            border: none;
            border-radius: 25px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .call-btn:hover {
            background: #30B855;
            transform: scale(1.05);
        }

        /* Messages App Styles */
        .messages-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .message-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .message-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .contact-avatar {
            width: 50px;
            height: 50px;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-right: 15px;
        }

        .message-preview {
            flex: 1;
        }

        .contact-name {
            color: white;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .last-message {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }

        .message-time {
            color: rgba(255, 255, 255, 0.5);
            font-size: 12px;
        }

        /* Camera App Styles */
        .camera-view {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .camera-preview {
            flex: 1;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .camera-placeholder {
            font-size: 64px;
            margin-bottom: 10px;
        }

        .camera-text {
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
        }

        .camera-controls {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .camera-btn {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .camera-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* App Placeholder Styles */
        .app-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
        }

        .placeholder-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .placeholder-text {
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
            max-width: 250px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="phone-container" id="phone">
            <div class="phone-screen">
                <!-- Dynamic Island -->
                <div class="dynamic-island"></div>

                <!-- Status Bar -->
                <div class="status-bar">
                    <div class="status-left">
                        <div class="time" id="time">9:41</div>
                    </div>
                    <div class="status-right">
                        <div class="signal-strength">
                            <div class="signal-bar"></div>
                            <div class="signal-bar"></div>
                            <div class="signal-bar"></div>
                            <div class="signal-bar"></div>
                        </div>
                        <div class="wifi-icon">📶</div>
                        <div class="battery-container">
                            <span class="battery-percentage" id="battery-percent">80%</span>
                            <div class="battery-icon">
                                <div class="battery-fill" id="battery-fill"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Wallpaper Background -->
                <div class="wallpaper-bg"></div>

                <!-- Home Screen -->
                <div class="home-screen">
                    <div class="apps-grid">
                        <div class="app-container">
                            <div class="app-icon app-phone" onclick="openApp('phone')">📞</div>
                            <div class="app-label">Telefon</div>
                        </div>
                        <div class="app-container">
                            <div class="app-icon app-messages" onclick="openApp('messages')">💬</div>
                            <div class="app-label">Zprávy</div>
                        </div>
                        <div class="app-container">
                            <div class="app-icon app-contacts" onclick="openApp('contacts')">👥</div>
                            <div class="app-label">Kontakty</div>
                        </div>
                        <div class="app-container">
                            <div class="app-icon app-camera" onclick="openApp('camera')">📷</div>
                            <div class="app-label">Fotoaparát</div>
                        </div>
                        <div class="app-container">
                            <div class="app-icon app-gallery" onclick="openApp('gallery')">🖼️</div>
                            <div class="app-label">Fotky</div>
                        </div>
                        <div class="app-container">
                            <div class="app-icon app-maps" onclick="openApp('maps')">🗺️</div>
                            <div class="app-label">Mapy</div>
                        </div>
                        <div class="app-container">
                            <div class="app-icon app-browser" onclick="openApp('browser')">🌐</div>
                            <div class="app-label">Safari</div>
                        </div>
                        <div class="app-container">
                            <div class="app-icon app-notes" onclick="openApp('notes')">📝</div>
                            <div class="app-label">Poznámky</div>
                        </div>
                        <div class="app-container">
                            <div class="app-icon app-darkweb" onclick="openApp('darkweb')">🕸️</div>
                            <div class="app-label">DarkWeb</div>
                        </div>
                        <div class="app-container">
                            <div class="app-icon app-banking" onclick="openApp('banking')">💳</div>
                            <div class="app-label">Banka</div>
                        </div>
                        <div class="app-container">
                            <div class="app-icon app-marketplace" onclick="openApp('marketplace')">🛒</div>
                            <div class="app-label">Obchod</div>
                        </div>
                        <div class="app-container">
                            <div class="app-icon app-settings" onclick="openApp('settings')">⚙️</div>
                            <div class="app-label">Nastavení</div>
                        </div>
                    </div>

                    <!-- Page Dots -->
                    <div class="page-dots">
                        <div class="page-dot active"></div>
                        <div class="page-dot"></div>
                    </div>

                    <!-- Dock -->
                    <div class="dock">
                        <div class="app-icon app-phone" onclick="openApp('phone')">📞</div>
                        <div class="app-icon app-messages" onclick="openApp('messages')">💬</div>
                        <div class="app-icon app-camera" onclick="openApp('camera')">📷</div>
                        <div class="app-icon app-browser" onclick="openApp('browser')">🌐</div>
                    </div>
                </div>

                <!-- Home Indicator -->
                <div class="home-indicator"></div>
            </div>
        </div>
    </div>
    
    <script>
        let isVisible = false;

        // Debug logging
        function debugLog(message, data = '') {
            console.log('[QB-Smartphone2 NUI]', message, data);
        }

        // Update time
        function updateTime() {
            const now = new Date();
            const timeStr = now.getHours().toString().padStart(2, '0') + ':' +
                           now.getMinutes().toString().padStart(2, '0');
            document.getElementById('time').textContent = timeStr;
        }

        // Update battery display
        function updateBattery(level) {
            const batteryPercent = document.getElementById('battery-percent');
            const batteryFill = document.getElementById('battery-fill');

            if (batteryPercent) batteryPercent.textContent = level + '%';
            if (batteryFill) batteryFill.style.width = level + '%';

            // Change color based on battery level
            if (batteryFill) {
                if (level > 50) {
                    batteryFill.style.background = '#30d158'; // Green
                } else if (level > 20) {
                    batteryFill.style.background = '#ff9f0a'; // Orange
                } else {
                    batteryFill.style.background = '#ff453a'; // Red
                }
            }
        }

        // Update time every second
        setInterval(updateTime, 1000);
        updateTime();

        // App functions s animacemi
        function openApp(appName) {
            debugLog('Opening app:', appName);

            // Najdi kliknutou ikonu a přidej animaci
            const clickedIcon = event.target.closest('.app-icon');
            if (clickedIcon) {
                clickedIcon.style.transform = 'scale(0.8)';
                setTimeout(() => {
                    clickedIcon.style.transform = '';
                }, 150);
            }

            // Zobraz loading efekt
            showAppLoading(appName);

            // Send to Lua
            fetch(`https://${GetParentResourceName()}/openApp`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json; charset=UTF-8',
                },
                body: JSON.stringify({ app: appName })
            }).then(() => {
                // Simulace otevření aplikace
                setTimeout(() => {
                    hideAppLoading();
                    showAppInterface(appName);
                }, 800);
            }).catch(err => {
                debugLog('Error opening app:', err);
                hideAppLoading();
            });
        }

        // Loading efekt pro aplikace
        function showAppLoading(appName) {
            const homeScreen = document.querySelector('.home-screen');
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'app-loading';
            loadingDiv.innerHTML = `
                <div class="loading-spinner"></div>
                <div class="loading-text">Otevírám ${getAppDisplayName(appName)}...</div>
            `;
            homeScreen.appendChild(loadingDiv);

            // Animace
            setTimeout(() => {
                loadingDiv.style.opacity = '1';
            }, 10);
        }

        function hideAppLoading() {
            const loading = document.querySelector('.app-loading');
            if (loading) {
                loading.style.opacity = '0';
                setTimeout(() => {
                    loading.remove();
                }, 300);
            }
        }

        function getAppDisplayName(appName) {
            const names = {
                'phone': 'Telefon',
                'messages': 'Zprávy',
                'contacts': 'Kontakty',
                'camera': 'Fotoaparát',
                'gallery': 'Fotky',
                'maps': 'Mapy',
                'browser': 'Safari',
                'notes': 'Poznámky',
                'darkweb': 'DarkWeb',
                'banking': 'Banku',
                'marketplace': 'Obchod',
                'settings': 'Nastavení'
            };
            return names[appName] || appName;
        }

        // Zobrazení rozhraní aplikace
        function showAppInterface(appName) {
            const homeScreen = document.querySelector('.home-screen');
            const appDiv = document.createElement('div');
            appDiv.className = 'app-interface';
            appDiv.innerHTML = getAppHTML(appName);
            homeScreen.appendChild(appDiv);

            // Animace
            setTimeout(() => {
                appDiv.style.transform = 'translateX(0)';
                appDiv.style.opacity = '1';
            }, 10);
        }

        function getAppHTML(appName) {
            switch(appName) {
                case 'phone':
                    return `
                        <div class="app-header">
                            <button onclick="closeApp()" class="back-btn">←</button>
                            <h2>Telefon</h2>
                        </div>
                        <div class="app-content">
                            <div class="phone-keypad">
                                <div class="number-display">
                                    <input type="text" id="phone-number" placeholder="Zadej číslo...">
                                </div>
                                <div class="keypad-grid">
                                    ${[1,2,3,4,5,6,7,8,9,'*',0,'#'].map(n =>
                                        `<button class="keypad-btn" onclick="addNumber('${n}')">${n}</button>`
                                    ).join('')}
                                </div>
                                <button class="call-btn" onclick="makeCall()">📞 Volat</button>
                            </div>
                        </div>
                    `;
                case 'messages':
                    return `
                        <div class="app-header">
                            <button onclick="closeApp()" class="back-btn">←</button>
                            <h2>Zprávy</h2>
                        </div>
                        <div class="app-content">
                            <div class="messages-list">
                                <div class="message-item">
                                    <div class="contact-avatar">👤</div>
                                    <div class="message-preview">
                                        <div class="contact-name">John Doe</div>
                                        <div class="last-message">Ahoj, jak se máš?</div>
                                    </div>
                                    <div class="message-time">12:34</div>
                                </div>
                            </div>
                        </div>
                    `;
                case 'camera':
                    return `
                        <div class="app-header">
                            <button onclick="closeApp()" class="back-btn">←</button>
                            <h2>Fotoaparát</h2>
                        </div>
                        <div class="app-content camera-view">
                            <div class="camera-preview">
                                <div class="camera-placeholder">📷</div>
                                <div class="camera-text">Kamera je připravena</div>
                            </div>
                            <div class="camera-controls">
                                <button class="camera-btn" onclick="takePhoto()">📸 Vyfotit</button>
                                <button class="camera-btn" onclick="recordVideo()">🎥 Video</button>
                            </div>
                        </div>
                    `;
                default:
                    return `
                        <div class="app-header">
                            <button onclick="closeApp()" class="back-btn">←</button>
                            <h2>${getAppDisplayName(appName)}</h2>
                        </div>
                        <div class="app-content">
                            <div class="app-placeholder">
                                <div class="placeholder-icon">${getAppIcon(appName)}</div>
                                <div class="placeholder-text">Aplikace ${getAppDisplayName(appName)} bude brzy dostupná!</div>
                            </div>
                        </div>
                    `;
            }
        }

        function getAppIcon(appName) {
            const icons = {
                'contacts': '👥', 'gallery': '🖼️', 'maps': '🗺️', 'browser': '🌐',
                'notes': '📝', 'darkweb': '🕸️', 'banking': '💳', 'marketplace': '🛒', 'settings': '⚙️'
            };
            return icons[appName] || '📱';
        }

        function closeApp() {
            const appInterface = document.querySelector('.app-interface');
            if (appInterface) {
                appInterface.style.transform = 'translateX(100%)';
                appInterface.style.opacity = '0';
                setTimeout(() => {
                    appInterface.remove();
                }, 300);
            }
        }

        // Telefon funkce
        function addNumber(num) {
            const input = document.getElementById('phone-number');
            if (input) input.value += num;
        }

        function makeCall() {
            const number = document.getElementById('phone-number').value;
            if (number) {
                fetch(`https://${GetParentResourceName()}/makeCall`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json; charset=UTF-8' },
                    body: JSON.stringify({ number: number })
                });
            }
        }

        function takePhoto() {
            fetch(`https://${GetParentResourceName()}/takePhoto`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json; charset=UTF-8' },
                body: JSON.stringify({})
            });
        }

        function recordVideo() {
            fetch(`https://${GetParentResourceName()}/recordVideo`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json; charset=UTF-8' },
                body: JSON.stringify({})
            });
        }

        // NUI Event listener
        window.addEventListener('message', function(event) {
            debugLog('Received message:', event.data);

            // Check if event.data exists and has action
            if (!event.data || typeof event.data !== 'object') {
                debugLog('Invalid message format:', event.data);
                return;
            }

            const { action, data } = event.data;

            if (!action) {
                debugLog('Message missing action property:', event.data);
                return;
            }

            switch (action) {
                case 'openPhone':
                    showPhone(data);
                    break;
                case 'closePhone':
                    hidePhone();
                    break;
                case 'updateBattery':
                    if (data && data.batteryLevel !== undefined) {
                        updateBattery(data.batteryLevel);
                    }
                    break;
                case 'newNotification':
                    // Handle notifications
                    debugLog('New notification:', data);
                    break;
                case 'waypointCleared':
                case 'bluetoothConnected':
                case 'bluetoothDisconnected':
                    // Ignore these actions silently
                    break;
                default:
                    debugLog('Unknown action:', action);
            }
        });

        function showPhone(data = {}) {
            debugLog('Showing phone with data:', data);
            const phone = document.getElementById('phone');
            phone.classList.add('visible');
            isVisible = true;

            // Update phone data if provided
            if (data && data.batteryLevel !== undefined) {
                updateBattery(data.batteryLevel);
                debugLog('Battery level updated:', data.batteryLevel);
            }

            // Add subtle vibration effect
            phone.style.animation = 'phoneAppear 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
        }

        function hidePhone() {
            debugLog('Hiding phone');
            const phone = document.getElementById('phone');

            // Přidej closing animaci
            phone.classList.add('closing');

            setTimeout(() => {
                phone.classList.remove('visible');
                phone.classList.remove('closing');
                isVisible = false;
            }, 400);
        }

        // ESC key to close
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && isVisible) {
                debugLog('ESC pressed, closing phone');
                closePhone();
            }
        });

        // Click outside to close
        document.addEventListener('click', function(event) {
            if (isVisible && !event.target.closest('.phone-container')) {
                debugLog('Clicked outside, closing phone');
                closePhone();
            }
        });

        function closePhone() {
            debugLog('Closing phone...');

            // Okamžitě skryj telefon
            hidePhone();

            // Pošli zprávu na server
            fetch(`https://${GetParentResourceName()}/closePhone`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json; charset=UTF-8',
                },
                body: JSON.stringify({})
            }).then(() => {
                debugLog('Close request sent successfully');
            }).catch(err => {
                debugLog('Error sending close request:', err);
            });
        }

        // Mock GetParentResourceName for browser testing
        if (!window.GetParentResourceName) {
            window.GetParentResourceName = () => 'qb-smartphone2';
        }

        // Initialize
        debugLog('NUI initialized successfully');

        // Test if we can communicate with Lua
        setTimeout(() => {
            debugLog('Testing Lua communication...');
        }, 1000);
    </script>
</body>
</html>
