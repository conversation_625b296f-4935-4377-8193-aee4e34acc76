{"manifest": {"name": "aproba", "version": "1.2.0", "description": "A ridiculously light-weight argument validator (now browser friendly)", "main": "index.js", "dependencies": {}, "devDependencies": {"standard": "^10.0.3", "tap": "^10.0.2"}, "files": ["index.js"], "scripts": {"test": "standard && tap -j3 test/*.js"}, "repository": {"type": "git", "url": "https://github.com/iarna/aproba"}, "keywords": ["argument", "validate"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/aproba/issues"}, "homepage": "https://github.com/iarna/aproba", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-aproba-1.2.0-6802e6264efd18c790a1b0d517f0f2627bf2c94a-integrity\\node_modules\\aproba\\package.json", "readmeFilename": "README.md", "readme": "aproba\n======\n\nA ridiculously light-weight function argument validator\n\n```\nvar validate = require(\"aproba\")\n\nfunction myfunc(a, b, c) {\n  // `a` must be a string, `b` a number, `c` a function\n  validate('SNF', arguments) // [a,b,c] is also valid\n}\n\nmyfunc('test', 23, function () {}) // ok\nmyfunc(123, 23, function () {}) // type error\nmyfunc('test', 23) // missing arg error\nmyfunc('test', 23, function () {}, true) // too many args error\n\n```\n\nValid types are:\n\n| type | description\n| :--: | :----------\n| *    | matches any type\n| A    | `Array.isArray` OR an `arguments` object\n| S    | typeof == string\n| N    | typeof == number\n| F    | typeof == function\n| O    | typeof == object and not type A and not type E\n| B    | typeof == boolean\n| E    | `instanceof Error` OR `null` **(special: see below)**\n| Z    | == `null`\n\nValidation failures throw one of three exception types, distinguished by a\n`code` property of `EMISSINGARG`, `EINVALIDTYPE` or `ETOOMANYARGS`.\n\nIf you pass in an invalid type then it will throw with a code of\n`EUNKNOWNTYPE`.\n\nIf an **error** argument is found and is not null then the remaining\narguments are optional.  That is, if you say `ESO` then that's like using a\nnon-magical `E` in: `E|ESO|ZSO`.\n\n### But I have optional arguments?!\n\nYou can provide more than one signature by separating them with pipes `|`.\nIf any signature matches the arguments then they'll be considered valid.\n\nSo for example, say you wanted to write a signature for\n`fs.createWriteStream`.  The docs for it describe it thusly:\n\n```\nfs.createWriteStream(path[, options])\n```\n\nThis would be a signature of `SO|S`.  That is, a string and and object, or\njust a string.\n\nNow, if you read the full `fs` docs, you'll see that actually path can ALSO\nbe a buffer.  And options can be a string, that is:\n```\npath <String> | <Buffer>\noptions <String> | <Object>\n```\n\nTo reproduce this you have to fully enumerate all of the possible\ncombinations and that implies a signature of `SO|SS|OO|OS|S|O`.  The\nawkwardness is a feature: It reminds you of the complexity you're adding to\nyour API when you do this sort of thing.\n\n\n### Browser support\n\nThis has no dependencies and should work in browsers, though you'll have\nnoisier stack traces.\n\n### Why this exists\n\nI wanted a very simple argument validator. It needed to do two things:\n\n1. Be more concise and easier to use than assertions\n\n2. Not encourage an infinite bikeshed of DSLs\n\nThis is why types are specified by a single character and there's no such\nthing as an optional argument. \n\nThis is not intended to validate user data. This is specifically about\nasserting the interface of your functions.\n\nIf you need greater validation, I encourage you to write them by hand or\nlook elsewhere.\n\n", "licenseText": "Copyright (c) 2015, <PERSON> <<EMAIL>>\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\nOR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/aproba/-/aproba-1.2.0.tgz#6802e6264efd18c790a1b0d517f0f2627bf2c94a", "type": "tarball", "reference": "https://registry.yarnpkg.com/aproba/-/aproba-1.2.0.tgz", "hash": "6802e6264efd18c790a1b0d517f0f2627bf2c94a", "integrity": "sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw==", "registry": "npm", "packageName": "aproba", "cacheIntegrity": "sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw== sha1-aALmJk79GMeQobDVF/DyYnvyyUo="}, "registry": "npm", "hash": "6802e6264efd18c790a1b0d517f0f2627bf2c94a"}