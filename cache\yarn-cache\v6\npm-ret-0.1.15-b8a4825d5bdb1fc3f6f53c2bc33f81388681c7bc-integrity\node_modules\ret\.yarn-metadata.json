{"manifest": {"name": "ret", "description": "Tokenizes a string that represents a regular expression.", "keywords": ["regex", "regexp", "regular expression", "parser", "tokenizer"], "version": "0.1.15", "repository": {"type": "git", "url": "git://github.com/fent/ret.js.git"}, "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/fent"}, "main": "./lib/index.js", "files": ["lib"], "scripts": {"test": "istanbul cover vows -- --spec test/*-test.js"}, "devDependencies": {"istanbul": "*", "vows": "*"}, "engines": {"node": ">=0.12"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-ret-0.1.15-b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc-integrity\\node_modules\\ret\\package.json", "readmeFilename": "README.md", "readme": "# Regular Expression Tokenizer\n\nTokenizes strings that represent a regular expressions.\n\n[![Build Status](https://secure.travis-ci.org/fent/ret.js.svg)](http://travis-ci.org/fent/ret.js)\n[![Dependency Status](https://david-dm.org/fent/ret.js.svg)](https://david-dm.org/fent/ret.js)\n[![codecov](https://codecov.io/gh/fent/ret.js/branch/master/graph/badge.svg)](https://codecov.io/gh/fent/ret.js)\n\n# Usage\n\n```js\nvar ret = require('ret');\n\nvar tokens = ret(/foo|bar/.source);\n```\n\n`tokens` will contain the following object\n\n```js\n{\n  \"type\": ret.types.ROOT\n  \"options\": [\n    [ { \"type\": ret.types.CHAR, \"value\", 102 },\n      { \"type\": ret.types.CHAR, \"value\", 111 },\n      { \"type\": ret.types.CHAR, \"value\", 111 } ],\n    [ { \"type\": ret.types.CHAR, \"value\",  98 },\n      { \"type\": ret.types.CHAR, \"value\",  97 },\n      { \"type\": ret.types.CHAR, \"value\", 114 } ]\n  ]\n}\n```\n\n# Token Types\n\n`ret.types` is a collection of the various token types exported by ret.\n\n### ROOT\n\nOnly used in the root of the regexp. This is needed due to the posibility of the root containing a pipe `|` character. In that case, the token will have an `options` key that will be an array of arrays of tokens. If not, it will contain a `stack` key that is an array of tokens.\n\n```js\n{\n  \"type\": ret.types.ROOT,\n  \"stack\": [token1, token2...],\n}\n```\n\n```js\n{\n  \"type\": ret.types.ROOT,\n  \"options\" [\n    [token1, token2...],\n    [othertoken1, othertoken2...]\n    ...\n  ],\n}\n```\n\n### GROUP\n\nGroups contain tokens that are inside of a parenthesis. If the group begins with `?` followed by another character, it's a special type of group. A ':' tells the group not to be remembered when `exec` is used. '=' means the previous token matches only if followed by this group, and '!' means the previous token matches only if NOT followed.\n\nLike root, it can contain an `options` key instead of `stack` if there is a pipe.\n\n```js\n{\n  \"type\": ret.types.GROUP,\n  \"remember\" true,\n  \"followedBy\": false,\n  \"notFollowedBy\": false,\n  \"stack\": [token1, token2...],\n}\n```\n\n```js\n{\n  \"type\": ret.types.GROUP,\n  \"remember\" true,\n  \"followedBy\": false,\n  \"notFollowedBy\": false,\n  \"options\" [\n    [token1, token2...],\n    [othertoken1, othertoken2...]\n    ...\n  ],\n}\n```\n\n### POSITION\n\n`\\b`, `\\B`, `^`, and `$` specify positions in the regexp.\n\n```js\n{\n  \"type\": ret.types.POSITION,\n  \"value\": \"^\",\n}\n```\n\n### SET\n\nContains a key `set` specifying what tokens are allowed and a key `not` specifying if the set should be negated. A set can contain other sets, ranges, and characters.\n\n```js\n{\n  \"type\": ret.types.SET,\n  \"set\": [token1, token2...],\n  \"not\": false,\n}\n```\n\n### RANGE\n\nUsed in set tokens to specify a character range. `from` and `to` are character codes.\n\n```js\n{\n  \"type\": ret.types.RANGE,\n  \"from\": 97,\n  \"to\": 122,\n}\n```\n\n### REPETITION\n\n```js\n{\n  \"type\": ret.types.REPETITION,\n  \"min\": 0,\n  \"max\": Infinity,\n  \"value\": token,\n}\n```\n\n### REFERENCE\n\nReferences a group token. `value` is 1-9.\n\n```js\n{\n  \"type\": ret.types.REFERENCE,\n  \"value\": 1,\n}\n```\n\n### CHAR\n\nRepresents a single character token. `value` is the character code. This might seem a bit cluttering instead of concatenating characters together. But since repetition tokens only repeat the last token and not the last clause like the pipe, it's simpler to do it this way.\n\n```js\n{\n  \"type\": ret.types.CHAR,\n  \"value\": 123,\n}\n```\n\n## Errors\n\nret.js will throw errors if given a string with an invalid regular expression. All possible errors are\n\n* Invalid group. When a group with an immediate `?` character is followed by an invalid character. It can only be followed by `!`, `=`, or `:`. Example: `/(?_abc)/`\n* Nothing to repeat. Thrown when a repetitional token is used as the first token in the current clause, as in right in the beginning of the regexp or group, or right after a pipe. Example: `/foo|?bar/`, `/{1,3}foo|bar/`, `/foo(+bar)/`\n* Unmatched ). A group was not opened, but was closed. Example: `/hello)2u/`\n* Unterminated group. A group was not closed. Example: `/(1(23)4/`\n* Unterminated character class. A custom character set was not closed. Example: `/[abc/`\n\n\n# Install\n\n    npm install ret\n\n\n# Tests\n\nTests are written with [vows](http://vowsjs.org/)\n\n```bash\nnpm test\n```\n\n# License\n\nMIT\n", "licenseText": "Copyright (C) 2011 by <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE. \n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/ret/-/ret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc", "type": "tarball", "reference": "https://registry.yarnpkg.com/ret/-/ret-0.1.15.tgz", "hash": "b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc", "integrity": "sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg==", "registry": "npm", "packageName": "ret", "cacheIntegrity": "sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg== sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w="}, "registry": "npm", "hash": "b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"}