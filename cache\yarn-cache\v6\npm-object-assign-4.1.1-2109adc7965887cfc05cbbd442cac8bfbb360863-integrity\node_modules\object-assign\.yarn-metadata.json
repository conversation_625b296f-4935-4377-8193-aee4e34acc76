{"manifest": {"name": "object-assign", "version": "4.1.1", "description": "ES2015 `Object.assign()` ponyfill", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/object-assign.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava", "bench": "matcha bench.js"}, "files": ["index.js"], "keywords": ["object", "assign", "extend", "properties", "es2015", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "devDependencies": {"ava": "^0.16.0", "lodash": "^4.16.4", "matcha": "^0.7.0", "xo": "^0.16.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-object-assign-4.1.1-2109adc7965887cfc05cbbd442cac8bfbb360863-integrity\\node_modules\\object-assign\\package.json", "readmeFilename": "readme.md", "readme": "# object-assign [![Build Status](https://travis-ci.org/sindresorhus/object-assign.svg?branch=master)](https://travis-ci.org/sindresorhus/object-assign)\n\n> ES2015 [`Object.assign()`](http://www.2ality.com/2014/01/object-assign.html) [ponyfill](https://ponyfill.com)\n\n\n## Use the built-in\n\nNode.js 4 and up, as well as every evergreen browser (Chrome, Edge, Firefox, Opera, Safari),\nsupport `Object.assign()` :tada:. If you target only those environments, then by all\nmeans, use `Object.assign()` instead of this package.\n\n\n## Install\n\n```\n$ npm install --save object-assign\n```\n\n\n## Usage\n\n```js\nconst objectAssign = require('object-assign');\n\nobjectAssign({foo: 0}, {bar: 1});\n//=> {foo: 0, bar: 1}\n\n// multiple sources\nobjectAssign({foo: 0}, {bar: 1}, {baz: 2});\n//=> {foo: 0, bar: 1, baz: 2}\n\n// overwrites equal keys\nobjectAssign({foo: 0}, {foo: 1}, {foo: 2});\n//=> {foo: 2}\n\n// ignores null and undefined sources\nobjectAssign({foo: 0}, null, {bar: 1}, undefined);\n//=> {foo: 0, bar: 1}\n```\n\n\n## API\n\n### objectAssign(target, [source, ...])\n\nAssigns enumerable own properties of `source` objects to the `target` object and returns the `target` object. Additional `source` objects will overwrite previous ones.\n\n\n## Resources\n\n- [ES2015 spec - Object.assign](https://people.mozilla.org/~jorendorff/es6-draft.html#sec-object.assign)\n\n\n## Related\n\n- [deep-assign](https://github.com/sindresorhus/deep-assign) - Recursive `Object.assign()`\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com)\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND <PERSON><PERSON>NFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863", "type": "tarball", "reference": "https://registry.yarnpkg.com/object-assign/-/object-assign-4.1.1.tgz", "hash": "2109adc7965887cfc05cbbd442cac8bfbb360863", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "registry": "npm", "packageName": "object-assign", "cacheIntegrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg== sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="}, "registry": "npm", "hash": "2109adc7965887cfc05cbbd442cac8bfbb360863"}