{"manifest": {"name": "strip-ansi", "version": "3.0.1", "description": "Strip ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/chalk/strip-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "jbna.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/qix-"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^2.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-strip-ansi-3.0.1-6a385fb8853d952d5ff05d0e8aaf94278dc63dcf-integrity\\node_modules\\strip-ansi\\package.json", "readmeFilename": "readme.md", "readme": "# strip-ansi [![Build Status](https://travis-ci.org/chalk/strip-ansi.svg?branch=master)](https://travis-ci.org/chalk/strip-ansi)\n\n> Strip [ANSI escape codes](http://en.wikipedia.org/wiki/ANSI_escape_code)\n\n\n## Install\n\n```\n$ npm install --save strip-ansi\n```\n\n\n## Usage\n\n```js\nvar stripAnsi = require('strip-ansi');\n\nstripAnsi('\\u001b[4mcake\\u001b[0m');\n//=> 'cake'\n```\n\n\n## Related\n\n- [strip-ansi-cli](https://github.com/chalk/strip-ansi-cli) - CLI for this module\n- [has-ansi](https://github.com/chalk/has-ansi) - Check if a string has ANSI escape codes\n- [ansi-regex](https://github.com/chalk/ansi-regex) - Regular expression for matching ANSI escape codes\n- [chalk](https://github.com/chalk/chalk) - Terminal string styling done right\n\n\n## License\n\nMIT © [Sindre Sorhus](http://sindresorhus.com)\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND <PERSON><PERSON>NFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf", "type": "tarball", "reference": "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-3.0.1.tgz", "hash": "6a385fb8853d952d5ff05d0e8aaf94278dc63dcf", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "registry": "npm", "packageName": "strip-ansi", "cacheIntegrity": "sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg== sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8="}, "registry": "npm", "hash": "6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"}