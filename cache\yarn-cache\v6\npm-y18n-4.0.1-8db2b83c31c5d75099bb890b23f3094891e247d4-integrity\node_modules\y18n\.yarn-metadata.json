{"manifest": {"name": "y18n", "version": "4.0.1", "description": "the bare-bones internationalization library used by yargs", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "repository": {"type": "git", "url": "**************:yargs/y18n.git"}, "files": ["index.js"], "keywords": ["i18n", "internationalization", "yargs"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "homepage": "https://github.com/yargs/y18n", "devDependencies": {"chai": "^4.0.1", "coveralls": "^3.0.0", "mocha": "^4.0.1", "nyc": "^11.0.1", "rimraf": "^2.5.0", "standard": "^10.0.0-beta.0", "standard-version": "^4.2.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-y18n-4.0.1-8db2b83c31c5d75099bb890b23f3094891e247d4-integrity\\node_modules\\y18n\\package.json", "readmeFilename": "README.md", "readme": "# y18n\n\n[![Build Status][travis-image]][travis-url]\n[![Coverage Status][coveralls-image]][coveralls-url]\n[![NPM version][npm-image]][npm-url]\n[![js-standard-style][standard-image]][standard-url]\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n\nThe bare-bones internationalization library used by yargs.\n\nInspired by [i18n](https://www.npmjs.com/package/i18n).\n\n## Examples\n\n_simple string translation:_\n\n```js\nvar __ = require('y18n').__\n\nconsole.log(__('my awesome string %s', 'foo'))\n```\n\noutput:\n\n`my awesome string foo`\n\n_using tagged template literals_\n\n```js\nvar __ = require('y18n').__\nvar str = 'foo'\n\nconsole.log(__`my awesome string ${str}`)\n```\n\noutput:\n\n`my awesome string foo`\n\n_pluralization support:_\n\n```js\nvar __n = require('y18n').__n\n\nconsole.log(__n('one fish %s', '%d fishes %s', 2, 'foo'))\n```\n\noutput:\n\n`2 fishes foo`\n\n## JSON Language Files\n\nThe JSON language files should be stored in a `./locales` folder.\nFile names correspond to locales, e.g., `en.json`, `pirate.json`.\n\nWhen strings are observed for the first time they will be\nadded to the JSON file corresponding to the current locale.\n\n## Methods\n\n### require('y18n')(config)\n\nCreate an instance of y18n with the config provided, options include:\n\n* `directory`: the locale directory, default `./locales`.\n* `updateFiles`: should newly observed strings be updated in file, default `true`.\n* `locale`: what locale should be used.\n* `fallbackToLanguage`: should fallback to a language-only file (e.g. `en.json`)\n  be allowed if a file matching the locale does not exist (e.g. `en_US.json`),\n  default `true`.\n\n### y18n.\\_\\_(str, arg, arg, arg)\n\nPrint a localized string, `%s` will be replaced with `arg`s.\n\nThis function can also be used as a tag for a template literal. You can use it\nlike this: <code>__&#96;hello ${'world'}&#96;</code>. This will be equivalent to\n`__('hello %s', 'world')`.\n\n### y18n.\\_\\_n(singularString, pluralString, count, arg, arg, arg)\n\nPrint a localized string with appropriate pluralization. If `%d` is provided\nin the string, the `count` will replace this placeholder.\n\n### y18n.setLocale(str)\n\nSet the current locale being used.\n\n### y18n.getLocale()\n\nWhat locale is currently being used?\n\n### y18n.updateLocale(obj)\n\nUpdate the current locale with the key value pairs in `obj`.\n\n## License\n\nISC\n\n[travis-url]: https://travis-ci.org/yargs/y18n\n[travis-image]: https://img.shields.io/travis/yargs/y18n.svg\n[coveralls-url]: https://coveralls.io/github/yargs/y18n\n[coveralls-image]: https://img.shields.io/coveralls/yargs/y18n.svg\n[npm-url]: https://npmjs.org/package/y18n\n[npm-image]: https://img.shields.io/npm/v/y18n.svg\n[standard-image]: https://img.shields.io/badge/code%20style-standard-brightgreen.svg\n[standard-url]: https://github.com/feross/standard\n", "licenseText": "Copyright (c) 2015, Contributors\n\nPermission to use, copy, modify, and/or distribute this software for any purpose\nwith or without fee is hereby granted, provided that the above copyright notice\nand this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING AL<PERSON> IMPLIED WARRANTIES OF MERCHANTABILITY AND\nFITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS\nOF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER\nTORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF\nTHIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/y18n/-/y18n-4.0.1.tgz#8db2b83c31c5d75099bb890b23f3094891e247d4", "type": "tarball", "reference": "https://registry.yarnpkg.com/y18n/-/y18n-4.0.1.tgz", "hash": "8db2b83c31c5d75099bb890b23f3094891e247d4", "integrity": "sha512-wNcy4NvjMYL8gogWWYAO7ZFWFfHcbdbE57tZO8e4cbpj8tfUcwrwqSl3ad8HxpYWCdXcJUCeKKZS62Av1affwQ==", "registry": "npm", "packageName": "y18n", "cacheIntegrity": "sha512-wNcy4NvjMYL8gogWWYAO7ZFWFfHcbdbE57tZO8e4cbpj8tfUcwrwqSl3ad8HxpYWCdXcJUCeKKZS62Av1affwQ== sha1-jbK4PDHF11CZu4kLI/MJSJHiR9Q="}, "registry": "npm", "hash": "8db2b83c31c5d75099bb890b23f3094891e247d4"}