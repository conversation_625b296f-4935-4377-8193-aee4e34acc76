{"manifest": {"name": "is-accessor-descriptor", "description": "Returns true if a value has the characteristics of a valid JavaScript accessor descriptor.", "version": "0.1.6", "homepage": "https://github.com/jonschlinkert/is-accessor-descriptor", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-accessor-descriptor.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-accessor-descriptor/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"kind-of": "^3.0.2"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["accessor", "check", "data", "descriptor", "get", "getter", "is", "keys", "object", "properties", "property", "set", "setter", "type", "valid", "value"], "verb": {"related": {"list": ["is-accessor-descriptor", "is-data-descriptor", "is-descriptor", "isobject"]}, "plugins": ["gulp-format-md"], "layout": "default"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-accessor-descriptor-0.1.6-a9e12cb3ae8d876727eeef3843f8a0897b5c98d6-integrity\\node_modules\\is-accessor-descriptor\\package.json", "readmeFilename": "README.md", "readme": "# is-accessor-descriptor [![NPM version](https://img.shields.io/npm/v/is-accessor-descriptor.svg)](https://www.npmjs.com/package/is-accessor-descriptor) [![Build Status](https://img.shields.io/travis/jonschlinkert/is-accessor-descriptor.svg)](https://travis-ci.org/jonschlinkert/is-accessor-descriptor)\n\n> Returns true if a value has the characteristics of a valid JavaScript accessor descriptor.\n\n- [Install](#install)\n- [Usage](#usage)\n- [Examples](#examples)\n- [API](#api)\n- [Related projects](#related-projects)\n- [Running tests](#running-tests)\n- [Contributing](#contributing)\n- [Author](#author)\n- [License](#license)\n\n_(TOC generated by [verb](https://github.com/verbose/verb) using [markdown-toc](https://github.com/jonschlinkert/markdown-toc))_\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm i is-accessor-descriptor --save\n```\n\n## Usage\n\n```js\nvar isAccessor = require('is-accessor-descriptor');\n\nisAccessor({get: function() {}});\n//=> true\n```\n\nYou may also pass an object and property name to check if the property is an accessor:\n\n```js\nisAccessor(foo, 'bar');\n```\n\n## Examples\n\n`false` when not an object\n\n```js\nisAccessor('a')\nisAccessor(null)\nisAccessor([])\n//=> false\n```\n\n`true` when the object has valid properties\n\nand the properties all have the correct JavaScript types:\n\n```js\nisAccessor({get: noop, set: noop})\nisAccessor({get: noop})\nisAccessor({set: noop})\n//=> true\n```\n\n`false` when the object has invalid properties\n\n```js\nisAccessor({get: noop, set: noop, bar: 'baz'})\nisAccessor({get: noop, writable: true})\nisAccessor({get: noop, value: true})\n//=> false\n```\n\n`false` when an accessor is not a function\n\n```js\nisAccessor({get: noop, set: 'baz'})\nisAccessor({get: 'foo', set: noop})\nisAccessor({get: 'foo', bar: 'baz'})\nisAccessor({get: 'foo', set: 'baz'})\n//=> false\n```\n\n`false` when a value is not the correct type\n\n```js\nisAccessor({get: noop, set: noop, enumerable: 'foo'})\nisAccessor({set: noop, configurable: 'foo'})\nisAccessor({get: noop, configurable: 'foo'})\n//=> false\n```\n\n## Related projects\n\n* [is-accessor-descriptor](https://www.npmjs.com/package/is-accessor-descriptor): Returns true if a value has the characteristics of a valid JavaScript accessor descriptor. | [homepage](https://github.com/jonschlinkert/is-accessor-descriptor)\n* [is-data-descriptor](https://www.npmjs.com/package/is-data-descriptor): Returns true if a value has the characteristics of a valid JavaScript data descriptor. | [homepage](https://github.com/jonschlinkert/is-data-descriptor)\n* [is-descriptor](https://www.npmjs.com/package/is-descriptor): Returns true if a value has the characteristics of a valid JavaScript descriptor. Works for… [more](https://www.npmjs.com/package/is-descriptor) | [homepage](https://github.com/jonschlinkert/is-descriptor)\n* [isobject](https://www.npmjs.com/package/isobject): Returns true if the value is an object and not an array or null. | [homepage](https://github.com/jonschlinkert/isobject)\n\n## Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm i -d && npm test\n```\n\n## Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/is-accessor-descriptor/issues/new).\n\n## Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n## License\n\nCopyright © 2015 [Jon Schlinkert](https://github.com/jonschlinkert)\nReleased under the MIT license.\n\n***\n\n_This file was generated by [verb](https://github.com/verbose/verb) on December 28, 2015._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz#a9e12cb3ae8d876727eeef3843f8a0897b5c98d6", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz", "hash": "a9e12cb3ae8d876727eeef3843f8a0897b5c98d6", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "registry": "npm", "packageName": "is-accessor-descriptor", "cacheIntegrity": "sha512-e1BM1qnDbMRG3ll2U9dSK0UMHuWOs3pY3AtcFsmvwPtKL3MML/Q86i+GilLfvqEs4GW+ExB91tQ3Ig9noDIZ+A== sha1-qeEss66Nh2cn7u84Q/igiXtcmNY="}, "registry": "npm", "hash": "a9e12cb3ae8d876727eeef3843f8a0897b5c98d6"}