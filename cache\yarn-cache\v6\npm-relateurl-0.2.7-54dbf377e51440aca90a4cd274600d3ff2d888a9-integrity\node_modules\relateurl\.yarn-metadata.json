{"manifest": {"name": "<PERSON><PERSON><PERSON>", "description": "Minify URLs by converting them from absolute to relative.", "version": "0.2.7", "license": "MIT", "homepage": "https://github.com/stevenvachon/relateurl", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.svachon.com/"}, "main": "lib", "repository": {"type": "git", "url": "git://github.com/stevenvachon/relateurl.git"}, "bugs": {"url": "https://github.com/stevenvachon/relateurl/issues"}, "devDependencies": {"browserify": "^13.0.1", "chai": "^3.5.0", "mocha": "^2.5.3", "uglify-js": "^2.7.0"}, "engines": {"node": ">= 0.10"}, "scripts": {"browserify": "browserify lib/ --standalone RelateUrl | uglifyjs --compress --mangle -o relateurl-browser.js", "test": "mocha test/ --bail --reporter spec --check-leaks"}, "files": ["lib", "license"], "keywords": ["uri", "url", "minifier", "minify", "lint", "relative", "absolute"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-relateurl-0.2.7-54dbf377e51440aca90a4cd274600d3ff2d888a9-integrity\\node_modules\\relateurl\\package.json", "readmeFilename": "README.md", "readme": "# relateurl [![NPM Version][npm-image]][npm-url] [![Build Status][travis-image]][travis-url] [![Dependency Status][david-image]][david-url]\n\n> Minify URLs by converting them from absolute to relative.\n\nIf you were to use this library on a website like `http://example.com/dir1/dir1-1/`, you would get results such as:\n\n| Before                                      | After                                |\n| :------------------------------------------ | :----------------------------------- |\n| `http://example.com/dir1/dir1-2/index.html` | `../dir1-2/`                         |\n| `http://example.com/dir2/dir2-1/`           | `/dir2/dir2-1/`                      |\n| `http://example.com/dir1/dir1-1/`           | ` `                                  |\n| `https://example.com/dir1/dir1-1/`          | `https://example.com/dir1/dir1-1/`   |\n| `http://google.com:80/dir/`                 | `//google.com/dir/`                  |\n| `../../../../../../../../#anchor`           | `/#anchor`                           |\n\n**All string parsing.** *No* directory browsing. It is thoroughly tested, very fast and lightweight with zero external dependencies.\n\n## Getting Started\n\nThis utility requires [Node.js](http://nodejs.org/) `>= 0.10`. To install, type this at the command line:\n```\nnpm install relateurl --save-dev\n```\n\n### Options\n\n#### options.defaultPorts\nType: `Object`   \nDefault value: `{ftp:21, http:80, https:443}`  \n\nExtend the list with any ports you need. Any URLs containing these default ports will have them removed. Example: `http://example.com:80/` will become `http://example.com/`.\n\n#### options.directoryIndexes\nType: `Array`   \nDefault value: `[\"index.html\"]`  \n\nExtend the list with any resources you need. Works with [`options.removeDirectoryIndexes`](#options.removeDirectoryIndexes).\n\n#### options.ignore_www\nType: `Boolean`  \nDefault value: `false`  \n\nThis will, for example, consider any domains containing `http://www.example.com/` to be related to any that contain `http://example.com/`.\n\n#### options.output\nType: constant or `String`  \nChoices: `RelateUrl.ABSOLUTE`,`RelateUrl.PATH_RELATIVE`,`RelateUrl.ROOT_RELATIVE`,`RelateUrl.SHORTEST`  \nChoices: `\"absolute\"`,`\"pathRelative\"`,`\"rootRelative\"`,`\"shortest\"`  \nDefault value: `RelateUrl.SHORTEST`  \n\n`RelateUrl.ABSOLUTE` will produce an absolute URL. Overrides [`options.schemeRelative`](#options.schemeRelative) with a value of `false`.  \n`RelateUrl.PATH_RELATIVE` will produce something like `../child-of-parent/etc/`.  \n`RelateUrl.ROOT_RELATIVE` will produce something like `/child-of-root/etc/`.  \n`RelateUrl.SHORTEST` will choose whichever is shortest between root- and path-relative.  \n\n#### options.rejectedSchemes\nType: `Array`   \nDefault value: `[\"data\",\"javascript\",\"mailto\"]`  \n\nExtend the list with any additional schemes. Example: `javascript:something` will not be modified.\n\n#### options.removeAuth\nType: `Boolean`   \nDefault value: `false`  \n\nRemove user authentication information from the output URL.\n\n#### options.removeDirectoryIndexes\nType: `Boolean`   \nDefault value: `true`  \n\nRemove any resources that match any found in [`options.directoryIndexes`](#options.directoryIndexes).\n\n#### options.removeEmptyQueries\nType: `Boolean`   \nDefault value: `false`  \n\nRemove empty query variables. Example: `http://domain.com/?var1&var2=&var3=asdf` will become `http://domain.com/?var3=adsf`. This does not apply to unrelated URLs (with other protocols, auths, hosts and/or ports).\n\n#### options.removeRootTrailingSlash\nType: `Boolean`   \nDefault value: `true`  \n\nRemove trailing slashes from root paths. Example: `http://domain.com/?var` will become `http://domain.com?var` while `http://domain.com/dir/?var` will not be modified.\n\n#### options.schemeRelative\nType: `Boolean`   \nDefault value: `true`  \n\nOutput URLs relative to the scheme. Example: `http://example.com/` will become `//example.com/`.\n\n#### options.site\nType: `String`   \nDefault value: `undefined`  \n\nAn options-based version of the [`from`](#examples) argument. If both are specified, `from` takes priority.\n\n#### options.slashesDenoteHost\nType: `Boolean`   \nDefault value: `true`  \n\nPassed to Node's [`url.parse`](http://nodejs.org/api/url.html#url_url_parse_urlstr_parsequerystring_slashesdenotehost).\n\n### Examples\nThis library can be used as a [function for single-use](#single-instance) or as a [class for multiple conversions](#reusable-instances).\n\nUpon successful conversion, a `String` will be returned. If an issue is encountered while parsing `from`, an error will be thrown.\n\n#### Single Instance\n```js\nvar RelateUrl = require(\"relateurl\");\n\nvar result = RelateUrl.relate(from, to, options);\n```\n\n#### Reusable Instances\n```js\nvar RelateUrl = require(\"relateurl\");\n\nvar instance = new RelateUrl(from, options);\n\nvar result1 = instance.relate(to1);\nvar result2 = instance.relate(to2, customOptions);\nvar result3 = instance.relate(to3);\n```\n\n## FAQ\n1. **Why bother writing/using this?**  \nTo aid in further minifying HTML, mainly for the purpose of faster page loads and SEO. It's been integrated into [HTMLMinifier](https://github.com/kangax/html-minifier).\n\n2. **Why not just use Node's `url.parse`, `url.resolve` and `path.relative`?**  \n`url.parse` *is* used, but `url.resolve` and `path.relative` are both slower and less powerful than this library.\n\n\n## Release History\n* 0.2.7 Node v6 support\n* 0.2.6 minor enhancements\n* 0.2.5 added `options.removeRootTrailingSlash`\n* 0.2.4 added `options.site`\n* 0.2.3 added browserify npm-script\n* 0.2.2 removed task runner\n* 0.2.1 shorten resource- and query-relative URLs, test variations list with other site URLs\n* 0.2.0 code cleanup, `options.removeEmptyQueries=true` only applied to unrelated URLs\n* 0.1.0 initial release\n\n\n## Roadmap\n* 0.2.8 check if queries are the same, regardless of param order\n* 0.2.8 possible [scheme exclusions](http://www.iana.org/assignments/uri-schemes/uri-schemes.xhtml) such as `tel:`\n* 0.2.8 decipher and return invalid input (special cases) to complete test suite\n* 0.3.0 test `options.slashesDenoteHost=false`, add something like `options.externalDirectoryIndexes=[]` for external sites\n\n\n[npm-image]: https://img.shields.io/npm/v/relateurl.svg\n[npm-url]: https://npmjs.org/package/relateurl\n[travis-image]: https://img.shields.io/travis/stevenvachon/relateurl.svg\n[travis-url]: https://travis-ci.org/stevenvachon/relateurl\n[david-image]: https://img.shields.io/david/stevenvachon/relateurl.svg\n[david-url]: https://david-dm.org/stevenvachon/relateurl\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) <PERSON> <<EMAIL>> (svachon.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/relateurl/-/relateurl-0.2.7.tgz#54dbf377e51440aca90a4cd274600d3ff2d888a9", "type": "tarball", "reference": "https://registry.yarnpkg.com/relateurl/-/relateurl-0.2.7.tgz", "hash": "54dbf377e51440aca90a4cd274600d3ff2d888a9", "integrity": "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=", "registry": "npm", "packageName": "<PERSON><PERSON><PERSON>", "cacheIntegrity": "sha512-G08Dxvm4iDN3MLM0EsP62EDV9IuhXPR6blNz6Utcp7zyV3tr4HVNINt6MpaRWbxoOHT3Q7YN2P+jaHX8vUbgog== sha1-VNvzd+UUQKypCkzSdGANP/LYiKk="}, "registry": "npm", "hash": "54dbf377e51440aca90a4cd274600d3ff2d888a9"}