{"manifest": {"name": "@xtuc/ieee754", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "version": "1.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "devDependencies": {"airtap": "0.0.7", "standard": "*", "tape": "^4.0.0", "@babel/cli": "^7.0.0-beta.54", "@babel/core": "^7.0.0-beta.54", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.54"}, "keywords": ["IEEE 754", "buffer", "convert", "floating point", "ieee754"], "license": "BSD-3-<PERSON><PERSON>", "main": "dist/index.cjs.js", "module": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "airtap -- test/*.js", "test-browser-local": "airtap --local -- test/*.js", "test-node": "tape test/*.js"}, "prepublish": "babel --plugins @babel/plugin-transform-modules-commonjs index.js -o dist/index.cjs.js", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@xtuc-ieee754-1.2.0-eef014a3145ae477a1cbc00cd1e552336dceb790-integrity\\node_modules\\@xtuc\\ieee754\\package.json", "readmeFilename": "README.md", "readme": "# ieee754 [![travis][travis-image]][travis-url] [![npm][npm-image]][npm-url] [![downloads][downloads-image]][downloads-url] [![javascript style guide][standard-image]][standard-url]\n\n[travis-image]: https://img.shields.io/travis/feross/ieee754/master.svg\n[travis-url]: https://travis-ci.org/feross/ieee754\n[npm-image]: https://img.shields.io/npm/v/ieee754.svg\n[npm-url]: https://npmjs.org/package/ieee754\n[downloads-image]: https://img.shields.io/npm/dm/ieee754.svg\n[downloads-url]: https://npmjs.org/package/ieee754\n[standard-image]: https://img.shields.io/badge/code_style-standard-brightgreen.svg\n[standard-url]: https://standardjs.com\n\n[![saucelabs][saucelabs-image]][saucelabs-url]\n\n[saucelabs-image]: https://saucelabs.com/browser-matrix/ieee754.svg\n[saucelabs-url]: https://saucelabs.com/u/ieee754\n\n### Read/write IEEE754 floating point numbers from/to a Buffer or array-like object.\n\n## install\n\n```\nnpm install ieee754\n```\n\n## methods\n\n`var ieee754 = require('ieee754')`\n\nThe `ieee754` object has the following functions:\n\n```\nieee754.read = function (buffer, offset, isLE, mLen, nBytes)\nieee754.write = function (buffer, value, offset, isLE, mLen, nBytes)\n```\n\nThe arguments mean the following:\n\n- buffer = the buffer\n- offset = offset into the buffer\n- value = value to set (only for `write`)\n- isLe = is little endian?\n- mLen = mantissa length\n- nBytes = number of bytes\n\n## what is ieee754?\n\nThe IEEE Standard for Floating-Point Arithmetic (IEEE 754) is a technical standard for floating-point computation. [Read more](http://en.wikipedia.org/wiki/IEEE_floating_point).\n\n## license\n\nBSD 3 Clause. Copyright (c) 2008, Fair Oaks Labs, Inc.\n", "licenseText": "Copyright (c) 2008, Fair Oaks Labs, Inc.\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are met:\n\n * Redistributions of source code must retain the above copyright notice,\n   this list of conditions and the following disclaimer.\n\n * Redistributions in binary form must reproduce the above copyright notice,\n   this list of conditions and the following disclaimer in the documentation\n   and/or other materials provided with the distribution.\n\n * Neither the name of Fair Oaks Labs, Inc. nor the names of its contributors\n   may be used to endorse or promote products derived from this software\n   without specific prior written permission.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\nAND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\nIMPLIED WARRANTIES OF ME<PERSON><PERSON><PERSON><PERSON><PERSON>ITY AND FITNESS FOR A PARTICULAR PURPOSE\nARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE\nLIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\nCO<PERSON>EQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n<PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\nINTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\nCONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\nARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\nPOSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@xtuc/ieee754/-/ieee754-1.2.0.tgz#eef014a3145ae477a1cbc00cd1e552336dceb790", "type": "tarball", "reference": "https://registry.yarnpkg.com/@xtuc/ieee754/-/ieee754-1.2.0.tgz", "hash": "eef014a3145ae477a1cbc00cd1e552336dceb790", "integrity": "sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==", "registry": "npm", "packageName": "@xtuc/ieee754", "cacheIntegrity": "sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA== sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A="}, "registry": "npm", "hash": "eef014a3145ae477a1cbc00cd1e552336dceb790"}