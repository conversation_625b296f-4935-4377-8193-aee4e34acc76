{"manifest": {"name": "memory-fs", "version": "0.4.1", "description": "A simple in-memory filesystem. Holds data in a javascript object.", "main": "lib/MemoryFileSystem.js", "files": ["lib/"], "scripts": {"test": "mocha", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly"}, "repository": {"type": "git", "url": "https://github.com/webpack/memory-fs.git"}, "keywords": ["fs", "memory"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "bugs": {"url": "https://github.com/webpack/memory-fs/issues"}, "homepage": "https://github.com/webpack/memory-fs", "devDependencies": {"bl": "^1.0.0", "codecov.io": "^0.1.4", "coveralls": "^2.11.2", "istanbul": "^0.2.13", "mocha": "^1.20.1", "should": "^4.0.4"}, "dependencies": {"errno": "^0.1.3", "readable-stream": "^2.0.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-memory-fs-0.4.1-3a9a20b8462523e447cfbc7e8bb80ed667bfc552-integrity\\node_modules\\memory-fs\\package.json", "readmeFilename": "README.md", "readme": "# memory-fs\n\nA simple in-memory filesystem. Holds data in a javascript object.\n\n``` javascript\nvar MemoryFileSystem = require(\"memory-fs\");\nvar fs = new MemoryFileSystem(); // Optionally pass a javascript object\n\nfs.mkdirpSync(\"/a/test/dir\");\nfs.writeFileSync(\"/a/test/dir/file.txt\", \"Hello World\");\nfs.readFileSync(\"/a/test/dir/file.txt\"); // returns Buffer(\"Hello World\")\n\n// Async variants too\nfs.unlink(\"/a/test/dir/file.txt\", function(err) {\n\t// ...\n});\n\nfs.readdirSync(\"/a/test\"); // returns [\"dir\"]\nfs.statSync(\"/a/test/dir\").isDirectory(); // returns true\nfs.rmdirSync(\"/a/test/dir\");\n\nfs.mkdirpSync(\"C:\\\\use\\\\windows\\\\style\\\\paths\");\n```\n\n## License\n\nCopyright (c) 2012-2014 <PERSON> (http://www.opensource.org/licenses/mit-license.php)\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/memory-fs/-/memory-fs-0.4.1.tgz#3a9a20b8462523e447cfbc7e8bb80ed667bfc552", "type": "tarball", "reference": "https://registry.yarnpkg.com/memory-fs/-/memory-fs-0.4.1.tgz", "hash": "3a9a20b8462523e447cfbc7e8bb80ed667bfc552", "integrity": "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=", "registry": "npm", "packageName": "memory-fs", "cacheIntegrity": "sha512-cda4JKCxReDXFXRqOHPQscuIYg1PvxbE2S2GP45rnwfEK+vZaXC8C1OFvdHIbgw0DLzowXGVoxLaAmlgRy14GQ== sha1-OpoguEYlI+RHz7x+i7gO1me/xVI="}, "registry": "npm", "hash": "3a9a20b8462523e447cfbc7e8bb80ed667bfc552"}