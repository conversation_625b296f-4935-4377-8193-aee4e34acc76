{"name": "bn.js", "version": "5.1.3", "description": "Big number implementation in pure javascript", "keywords": ["BN", "Big number", "BigNum", "<PERSON><PERSON><PERSON>", "<PERSON>"], "homepage": "https://github.com/indutny/bn.js", "bugs": {"url": "https://github.com/indutny/bn.js/issues"}, "repository": {"type": "git", "url": "**************:indutny/bn.js"}, "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>>", "files": ["lib/bn.js"], "main": "lib/bn.js", "browser": {"buffer": false}, "scripts": {"lint": "standardx", "test": "npm run lint && npm run unit", "unit": "mocha --reporter=spec test/*-test.js"}, "devDependencies": {"babel-eslint": "^10.0.3", "mocha": "^7.0.1", "standardx": "^5.0.0"}, "standardx": {"parser": "babel-es<PERSON>"}}