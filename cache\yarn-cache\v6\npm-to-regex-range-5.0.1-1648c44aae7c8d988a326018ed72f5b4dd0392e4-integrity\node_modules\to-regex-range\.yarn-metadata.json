{"manifest": {"name": "to-regex-range", "description": "Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.", "version": "5.0.1", "homepage": "https://github.com/micromatch/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}], "repository": {"type": "git", "url": "https://github.com/micromatch/to-regex-range.git"}, "bugs": {"url": "https://github.com/micromatch/to-regex-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=8.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^7.0.0"}, "devDependencies": {"fill-range": "^6.0.0", "gulp-format-md": "^2.0.0", "mocha": "^6.0.2", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "keywords": ["bash", "date", "expand", "expansion", "expression", "glob", "match", "match date", "match number", "match numbers", "match year", "matches", "matching", "number", "numbers", "numerical", "range", "ranges", "regex", "regexp", "regular", "regular expression", "sequence"], "verb": {"layout": "default", "toc": false, "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "helpers": {"examples": {"displayName": "examples"}}, "related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-to-regex-range-5.0.1-1648c44aae7c8d988a326018ed72f5b4dd0392e4-integrity\\node_modules\\to-regex-range\\package.json", "readmeFilename": "README.md", "readme": "# to-regex-range [![Donate](https://img.shields.io/badge/Donate-PayPal-green.svg)](https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=W8YFZ425KND68) [![NPM version](https://img.shields.io/npm/v/to-regex-range.svg?style=flat)](https://www.npmjs.com/package/to-regex-range) [![NPM monthly downloads](https://img.shields.io/npm/dm/to-regex-range.svg?style=flat)](https://npmjs.org/package/to-regex-range) [![NPM total downloads](https://img.shields.io/npm/dt/to-regex-range.svg?style=flat)](https://npmjs.org/package/to-regex-range) [![Linux Build Status](https://img.shields.io/travis/micromatch/to-regex-range.svg?style=flat&label=Travis)](https://travis-ci.org/micromatch/to-regex-range)\n\n> Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.\n\nPlease consider following this project's author, [Jon Schlinkert](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save to-regex-range\n```\n\n<details>\n<summary><strong>What does this do?</strong></summary>\n\n<br>\n\nThis libary generates the `source` string to be passed to `new RegExp()` for matching a range of numbers.\n\n**Example**\n\n```js\nconst toRegexRange = require('to-regex-range');\nconst regex = new RegExp(toRegexRange('15', '95'));\n```\n\nA string is returned so that you can do whatever you need with it before passing it to `new RegExp()` (like adding `^` or `$` boundaries, defining flags, or combining it another string).\n\n<br>\n\n</details>\n\n<details>\n<summary><strong>Why use this library?</strong></summary>\n\n<br>\n\n### Convenience\n\nCreating regular expressions for matching numbers gets deceptively complicated pretty fast.\n\nFor example, let's say you need a validation regex for matching part of a user-id, postal code, social security number, tax id, etc:\n\n* regex for matching `1` => `/1/` (easy enough)\n* regex for matching `1` through `5` => `/[1-5]/` (not bad...)\n* regex for matching `1` or `5` => `/(1|5)/` (still easy...)\n* regex for matching `1` through `50` => `/([1-9]|[1-4][0-9]|50)/` (uh-oh...)\n* regex for matching `1` through `55` => `/([1-9]|[1-4][0-9]|5[0-5])/` (no prob, I can do this...)\n* regex for matching `1` through `555` => `/([1-9]|[1-9][0-9]|[1-4][0-9]{2}|5[0-4][0-9]|55[0-5])/` (maybe not...)\n* regex for matching `0001` through `5555` => `/(0{3}[1-9]|0{2}[1-9][0-9]|0[1-9][0-9]{2}|[1-4][0-9]{3}|5[0-4][0-9]{2}|55[0-4][0-9]|555[0-5])/` (okay, I get the point!)\n\nThe numbers are contrived, but they're also really basic. In the real world you might need to generate a regex on-the-fly for validation.\n\n**Learn more**\n\nIf you're interested in learning more about [character classes](http://www.regular-expressions.info/charclass.html) and other regex features, I personally have always found [regular-expressions.info](http://www.regular-expressions.info/charclass.html) to be pretty useful.\n\n### Heavily tested\n\nAs of April 07, 2019, this library runs [>1m test assertions](./test/test.js) against generated regex-ranges to provide brute-force verification that results are correct.\n\nTests run in ~280ms on my MacBook Pro, 2.5 GHz Intel Core i7.\n\n### Optimized\n\nGenerated regular expressions are optimized:\n\n* duplicate sequences and character classes are reduced using quantifiers\n* smart enough to use `?` conditionals when number(s) or range(s) can be positive or negative\n* uses fragment caching to avoid processing the same exact string more than once\n\n<br>\n\n</details>\n\n## Usage\n\nAdd this library to your javascript application with the following line of code\n\n```js\nconst toRegexRange = require('to-regex-range');\n```\n\nThe main export is a function that takes two integers: the `min` value and `max` value (formatted as strings or numbers).\n\n```js\nconst source = toRegexRange('15', '95');\n//=> 1[5-9]|[2-8][0-9]|9[0-5]\n\nconst regex = new RegExp(`^${source}$`);\nconsole.log(regex.test('14')); //=> false\nconsole.log(regex.test('50')); //=> true\nconsole.log(regex.test('94')); //=> true\nconsole.log(regex.test('96')); //=> false\n```\n\n## Options\n\n### options.capture\n\n**Type**: `boolean`\n\n**Deafault**: `undefined`\n\nWrap the returned value in parentheses when there is more than one regex condition. Useful when you're dynamically generating ranges.\n\n```js\nconsole.log(toRegexRange('-10', '10'));\n//=> -[1-9]|-?10|[0-9]\n\nconsole.log(toRegexRange('-10', '10', { capture: true }));\n//=> (-[1-9]|-?10|[0-9])\n```\n\n### options.shorthand\n\n**Type**: `boolean`\n\n**Deafault**: `undefined`\n\nUse the regex shorthand for `[0-9]`:\n\n```js\nconsole.log(toRegexRange('0', '999999'));\n//=> [0-9]|[1-9][0-9]{1,5}\n\nconsole.log(toRegexRange('0', '999999', { shorthand: true }));\n//=> \\d|[1-9]\\d{1,5}\n```\n\n### options.relaxZeros\n\n**Type**: `boolean`\n\n**Default**: `true`\n\nThis option relaxes matching for leading zeros when when ranges are zero-padded.\n\n```js\nconst source = toRegexRange('-0010', '0010');\nconst regex = new RegExp(`^${source}$`);\nconsole.log(regex.test('-10')); //=> true\nconsole.log(regex.test('-010')); //=> true\nconsole.log(regex.test('-0010')); //=> true\nconsole.log(regex.test('10')); //=> true\nconsole.log(regex.test('010')); //=> true\nconsole.log(regex.test('0010')); //=> true\n```\n\nWhen `relaxZeros` is false, matching is strict:\n\n```js\nconst source = toRegexRange('-0010', '0010', { relaxZeros: false });\nconst regex = new RegExp(`^${source}$`);\nconsole.log(regex.test('-10')); //=> false\nconsole.log(regex.test('-010')); //=> false\nconsole.log(regex.test('-0010')); //=> true\nconsole.log(regex.test('10')); //=> false\nconsole.log(regex.test('010')); //=> false\nconsole.log(regex.test('0010')); //=> true\n```\n\n## Examples\n\n| **Range**                   | **Result**                                                                      | **Compile time** |\n| ---                         | ---                                                                             | ---              |\n| `toRegexRange(-10, 10)`     | `-[1-9]\\|-?10\\|[0-9]`                                                           | _132μs_          |\n| `toRegexRange(-100, -10)`   | `-1[0-9]\\|-[2-9][0-9]\\|-100`                                                    | _50μs_           |\n| `toRegexRange(-100, 100)`   | `-[1-9]\\|-?[1-9][0-9]\\|-?100\\|[0-9]`                                            | _42μs_           |\n| `toRegexRange(001, 100)`    | `0{0,2}[1-9]\\|0?[1-9][0-9]\\|100`                                                | _109μs_          |\n| `toRegexRange(001, 555)`    | `0{0,2}[1-9]\\|0?[1-9][0-9]\\|[1-4][0-9]{2}\\|5[0-4][0-9]\\|55[0-5]`                | _51μs_           |\n| `toRegexRange(0010, 1000)`  | `0{0,2}1[0-9]\\|0{0,2}[2-9][0-9]\\|0?[1-9][0-9]{2}\\|1000`                         | _31μs_           |\n| `toRegexRange(1, 50)`       | `[1-9]\\|[1-4][0-9]\\|50`                                                         | _24μs_           |\n| `toRegexRange(1, 55)`       | `[1-9]\\|[1-4][0-9]\\|5[0-5]`                                                     | _23μs_           |\n| `toRegexRange(1, 555)`      | `[1-9]\\|[1-9][0-9]\\|[1-4][0-9]{2}\\|5[0-4][0-9]\\|55[0-5]`                        | _30μs_           |\n| `toRegexRange(1, 5555)`     | `[1-9]\\|[1-9][0-9]{1,2}\\|[1-4][0-9]{3}\\|5[0-4][0-9]{2}\\|55[0-4][0-9]\\|555[0-5]` | _43μs_           |\n| `toRegexRange(111, 555)`    | `11[1-9]\\|1[2-9][0-9]\\|[2-4][0-9]{2}\\|5[0-4][0-9]\\|55[0-5]`                     | _38μs_           |\n| `toRegexRange(29, 51)`      | `29\\|[34][0-9]\\|5[01]`                                                          | _24μs_           |\n| `toRegexRange(31, 877)`     | `3[1-9]\\|[4-9][0-9]\\|[1-7][0-9]{2}\\|8[0-6][0-9]\\|87[0-7]`                       | _32μs_           |\n| `toRegexRange(5, 5)`        | `5`                                                                             | _8μs_            |\n| `toRegexRange(5, 6)`        | `5\\|6`                                                                          | _11μs_           |\n| `toRegexRange(1, 2)`        | `1\\|2`                                                                          | _6μs_            |\n| `toRegexRange(1, 5)`        | `[1-5]`                                                                         | _15μs_           |\n| `toRegexRange(1, 10)`       | `[1-9]\\|10`                                                                     | _22μs_           |\n| `toRegexRange(1, 100)`      | `[1-9]\\|[1-9][0-9]\\|100`                                                        | _25μs_           |\n| `toRegexRange(1, 1000)`     | `[1-9]\\|[1-9][0-9]{1,2}\\|1000`                                                  | _31μs_           |\n| `toRegexRange(1, 10000)`    | `[1-9]\\|[1-9][0-9]{1,3}\\|10000`                                                 | _34μs_           |\n| `toRegexRange(1, 100000)`   | `[1-9]\\|[1-9][0-9]{1,4}\\|100000`                                                | _36μs_           |\n| `toRegexRange(1, 1000000)`  | `[1-9]\\|[1-9][0-9]{1,5}\\|1000000`                                               | _42μs_           |\n| `toRegexRange(1, 10000000)` | `[1-9]\\|[1-9][0-9]{1,6}\\|10000000`                                              | _42μs_           |\n\n## Heads up!\n\n**Order of arguments**\n\nWhen the `min` is larger than the `max`, values will be flipped to create a valid range:\n\n```js\ntoRegexRange('51', '29');\n```\n\nIs effectively flipped to:\n\n```js\ntoRegexRange('29', '51');\n//=> 29|[3-4][0-9]|5[0-1]\n```\n\n**Steps / increments**\n\nThis library does not support steps (increments). A pr to add support would be welcome.\n\n## History\n\n### v2.0.0 - 2017-04-21\n\n**New features**\n\nAdds support for zero-padding!\n\n### v1.0.0\n\n**Optimizations**\n\nRepeating ranges are now grouped using quantifiers. rocessing time is roughly the same, but the generated regex is much smaller, which should result in faster matching.\n\n## Attribution\n\nInspired by the python library [range-regex](https://github.com/dimka665/range-regex).\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [expand-range](https://www.npmjs.com/package/expand-range): Fast, bash-like range expansion. Expand a range of numbers or letters, uppercase or lowercase. Used… [more](https://github.com/jonschlinkert/expand-range) | [homepage](https://github.com/jonschlinkert/expand-range \"Fast, bash-like range expansion. Expand a range of numbers or letters, uppercase or lowercase. Used by micromatch.\")\n* [fill-range](https://www.npmjs.com/package/fill-range): Fill in a range of numbers or letters, optionally passing an increment or `step` to… [more](https://github.com/jonschlinkert/fill-range) | [homepage](https://github.com/jonschlinkert/fill-range \"Fill in a range of numbers or letters, optionally passing an increment or `step` to use, or create a regex-compatible range with `options.toRegex`\")\n* [micromatch](https://www.npmjs.com/package/micromatch): Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. | [homepage](https://github.com/micromatch/micromatch \"Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.\")\n* [repeat-element](https://www.npmjs.com/package/repeat-element): Create an array by repeating the given value n times. | [homepage](https://github.com/jonschlinkert/repeat-element \"Create an array by repeating the given value n times.\")\n* [repeat-string](https://www.npmjs.com/package/repeat-string): Repeat the given string n times. Fastest implementation for repeating a string. | [homepage](https://github.com/jonschlinkert/repeat-string \"Repeat the given string n times. Fastest implementation for repeating a string.\")\n\n### Contributors\n\n| **Commits** | **Contributor** |  \n| --- | --- |  \n| 63 | [jonschlinkert](https://github.com/jonschlinkert) |  \n| 3  | [doowb](https://github.com/doowb) |  \n| 2  | [realityking](https://github.com/realityking) |  \n\n### Author\n\n**Jon Schlinkert**\n\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n\nPlease consider supporting me on Patreon, or [start your own Patreon page](https://patreon.com/invite/bxpbvm)!\n\n<a href=\"https://www.patreon.com/jonschlinkert\">\n<img src=\"https://c5.patreon.com/external/logo/<EMAIL>\" height=\"50\">\n</a>\n\n### License\n\nCopyright © 2019, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.8.0, on April 07, 2019._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015-present, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/to-regex-range/-/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4", "type": "tarball", "reference": "https://registry.yarnpkg.com/to-regex-range/-/to-regex-range-5.0.1.tgz", "hash": "1648c44aae7c8d988a326018ed72f5b4dd0392e4", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "registry": "npm", "packageName": "to-regex-range", "cacheIntegrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ== sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ="}, "registry": "npm", "hash": "1648c44aae7c8d988a326018ed72f5b4dd0392e4"}