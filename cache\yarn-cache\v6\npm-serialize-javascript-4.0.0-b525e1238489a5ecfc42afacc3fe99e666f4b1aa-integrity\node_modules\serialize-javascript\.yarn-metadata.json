{"manifest": {"name": "serialize-javascript", "version": "4.0.0", "description": "Serialize JavaScript to a superset of JSON that includes regular expressions and functions.", "main": "index.js", "scripts": {"benchmark": "node -v && node test/benchmark/serialize.js", "test": "nyc --reporter=lcov mocha test/unit"}, "repository": {"type": "git", "url": "git+https://github.com/yahoo/serialize-javascript.git"}, "keywords": ["serialize", "serialization", "javascript", "js", "json"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/yahoo/serialize-javascript/issues"}, "homepage": "https://github.com/yahoo/serialize-javascript", "devDependencies": {"benchmark": "^2.1.4", "chai": "^4.1.0", "mocha": "^7.0.0", "nyc": "^15.0.0"}, "dependencies": {"randombytes": "^2.1.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-serialize-javascript-4.0.0-b525e1238489a5ecfc42afacc3fe99e666f4b1aa-integrity\\node_modules\\serialize-javascript\\package.json", "readmeFilename": "README.md", "readme": "Serialize JavaScript\n====================\n\nSerialize JavaScript to a _superset_ of JSON that includes regular expressions, dates and functions.\n\n[![npm Version][npm-badge]][npm]\n[![Dependency Status][david-badge]][david]\n[![Build Status][travis-badge]][travis]\n\n## Overview\n\nThe code in this package began its life as an internal module to [express-state][]. To expand its usefulness, it now lives as `serialize-javascript` — an independent package on npm.\n\nYou're probably wondering: **What about `JSON.stringify()`!?** We've found that sometimes we need to serialize JavaScript **functions**, **regexps**, **dates**, **sets** or **maps**. A great example is a web app that uses client-side URL routing where the route definitions are regexps that need to be shared from the server to the client. But this module is also great for communicating between node processes.\n\nThe string returned from this package's single export function is literal JavaScript which can be saved to a `.js` file, or be embedded into an HTML document by making the content of a `<script>` element.\n\n> **HTML characters and JavaScript line terminators are escaped automatically.**\n\nPlease note that serialization for ES6 Sets & Maps requires support for `Array.from` (not available in IE or Node < 0.12), or an `Array.from` polyfill.\n\n## Installation\n\nInstall using npm:\n\n```shell\n$ npm install serialize-javascript\n```\n\n## Usage\n\n```js\nvar serialize = require('serialize-javascript');\n\nserialize({\n    str  : 'string',\n    num  : 0,\n    obj  : {foo: 'foo'},\n    arr  : [1, 2, 3],\n    bool : true,\n    nil  : null,\n    undef: undefined,\n    inf  : Infinity,\n    date : new Date(\"Thu, 28 Apr 2016 22:02:17 GMT\"),\n    map  : new Map([['hello', 'world']]),\n    set  : new Set([123, 456]),\n    fn   : function echo(arg) { return arg; },\n    re   : /([^\\s]+)/g,\n    big  : BigInt(10),\n});\n```\n\nThe above will produce the following string output:\n\n```js\n'{\"str\":\"string\",\"num\":0,\"obj\":{\"foo\":\"foo\"},\"arr\":[1,2,3],\"bool\":true,\"nil\":null,\"undef\":undefined,\"inf\":Infinity,\"date\":new Date(\"2016-04-28T22:02:17.000Z\"),\"map\":new Map([[\"hello\",\"world\"]]),\"set\":new Set([123,456]),\"fn\":function echo(arg) { return arg; },\"re\":new RegExp(\"([^\\\\\\\\s]+)\", \"g\"),\"big\":BigInt(\"10\")}'\n```\n\nNote: to produced a beautified string, you can pass an optional second argument to `serialize()` to define the number of spaces to be used for the indentation.\n\n### Automatic Escaping of HTML Characters\n\nA primary feature of this package is to serialize code to a string of literal JavaScript which can be embedded in an HTML document by adding it as the contents of the `<script>` element. In order to make this safe, HTML characters and JavaScript line terminators are escaped automatically.\n\n```js\nserialize({\n    haxorXSS: '</script>'\n});\n```\n\nThe above will produce the following string, HTML-escaped output which is safe to put into an HTML document as it will not cause the inline script element to terminate:\n\n```js\n'{\"haxorXSS\":\"\\\\u003C\\\\u002Fscript\\\\u003E\"}'\n```\n\n> You can pass an optional `unsafe` argument to `serialize()` for straight serialization.\n\n### Options\n\nThe `serialize()` function accepts an `options` object as its second argument. All options are being defaulted to `undefined`:\n\n#### `options.space`\n\nThis option is the same as the `space` argument that can be passed to [`JSON.stringify`][JSON.stringify]. It can be used to add whitespace and indentation to the serialized output to make it more readable.\n\n```js\nserialize(obj, {space: 2});\n```\n\n#### `options.isJSON`\n\nThis option is a signal to `serialize()` that the object being serialized does not contain any function or regexps values. This enables a hot-path that allows serialization to be over 3x faster. If you're serializing a lot of data, and know its pure JSON, then you can enable this option for a speed-up.\n\n**Note:** That when using this option, the output will still be escaped to protect against XSS.\n\n```js\nserialize(obj, {isJSON: true});\n```\n\n#### `options.unsafe`\n\nThis option is to signal `serialize()` that we want to do a straight conversion, without the XSS protection. This options needs to be explicitly set to `true`. HTML characters and JavaScript line terminators will not be escaped. You will have to roll your own.\n\n```js\nserialize(obj, {unsafe: true});\n```\n\n#### `options.ignoreFunction`\n\nThis option is to signal `serialize()` that we do not want serialize JavaScript function. \nJust treat function like `JSON.stringify` do, but other features will work as expected.\n\n```js\nserialize(obj, {ignoreFunction: true});\n```\n\n## Deserializing\n\nFor some use cases you might also need to deserialize the string. This is explicitly not part of this module. However, you can easily write it yourself:\n\n```js\nfunction deserialize(serializedJavascript){\n  return eval('(' + serializedJavascript + ')');\n}\n```\n\n**Note:** Don't forget the parentheses around the serialized javascript, as the opening bracket `{` will be considered to be the start of a body.\n\n## License\n\nThis software is free to use under the Yahoo! Inc. BSD license.\nSee the [LICENSE file][LICENSE] for license text and copyright information.\n\n\n[npm]: https://www.npmjs.org/package/serialize-javascript\n[npm-badge]: https://img.shields.io/npm/v/serialize-javascript.svg?style=flat-square\n[david]: https://david-dm.org/yahoo/serialize-javascript\n[david-badge]: https://img.shields.io/david/yahoo/serialize-javascript.svg?style=flat-square\n[travis]: https://travis-ci.org/yahoo/serialize-javascript\n[travis-badge]: https://img.shields.io/travis/yahoo/serialize-javascript.svg?style=flat-square\n[express-state]: https://github.com/yahoo/express-state\n[JSON.stringify]: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify\n[LICENSE]: https://github.com/yahoo/serialize-javascript/blob/master/LICENSE\n", "licenseText": "Copyright 2014 Yahoo! Inc.\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are met:\n\n    * Redistributions of source code must retain the above copyright\n      notice, this list of conditions and the following disclaimer.\n\n    * Redistributions in binary form must reproduce the above copyright\n      notice, this list of conditions and the following disclaimer in the\n      documentation and/or other materials provided with the distribution.\n\n    * Neither the name of the Yahoo! Inc. nor the\n      names of its contributors may be used to endorse or promote products\n      derived from this software without specific prior written permission.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\nANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\nWARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\nDISCLAIMED. IN NO EVENT SHALL YAHOO! INC. BE LIABLE FOR ANY\nDIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\nLOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\nON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\nSOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/serialize-javascript/-/serialize-javascript-4.0.0.tgz#b525e1238489a5ecfc42afacc3fe99e666f4b1aa", "type": "tarball", "reference": "https://registry.yarnpkg.com/serialize-javascript/-/serialize-javascript-4.0.0.tgz", "hash": "b525e1238489a5ecfc42afacc3fe99e666f4b1aa", "integrity": "sha512-GaNA54380uFefWghODBWEGisLZFj00nS5ACs6yHa9nLqlLpVLO8ChDGeKRjZnV4Nh4n0Qi7nhYZD/9fCPzEqkw==", "registry": "npm", "packageName": "serialize-javascript", "cacheIntegrity": "sha512-GaNA54380uFefWghODBWEGisLZFj00nS5ACs6yHa9nLqlLpVLO8ChDGeKRjZnV4Nh4n0Qi7nhYZD/9fCPzEqkw== sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao="}, "registry": "npm", "hash": "b525e1238489a5ecfc42afacc3fe99e666f4b1aa"}