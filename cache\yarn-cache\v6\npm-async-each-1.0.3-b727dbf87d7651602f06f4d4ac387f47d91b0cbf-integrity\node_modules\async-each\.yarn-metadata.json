{"manifest": {"name": "async-each", "description": "No-bullshit, ultra-simple, 35-lines-of-code async parallel forEach / map function for JavaScript.", "version": "1.0.3", "license": "MIT", "keywords": ["async", "for<PERSON>ach", "each", "map", "asynchronous", "iteration", "iterate", "loop", "parallel", "concurrent", "array", "flow", "control flow"], "files": ["index.js"], "homepage": "https://github.com/paulmillr/async-each/", "author": {"name": "<PERSON>", "url": "https://paulmillr.com/"}, "repository": {"type": "git", "url": "git://github.com/paulmillr/async-each.git"}, "main": "index.js", "dependencies": {}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-async-each-1.0.3-b727dbf87d7651602f06f4d4ac387f47d91b0cbf-integrity\\node_modules\\async-each\\package.json", "readmeFilename": "README.md", "readme": "# async-each\n\nNo-bullshit, ultra-simple, 35-lines-of-code async parallel forEach function for JavaScript.\n\nWe don't need junky 30K async libs. Really.\n\nFor browsers and node.js.\n\n## Installation\n* Just include async-each before your scripts.\n* `npm install async-each` if you’re using node.js.\n\n## Usage\n\n* `each(array, iterator, callback);` — `Array`, `Function`, `(optional) Function`\n* `iterator(item, next)` receives current item and a callback that will mark the item as done. `next` callback receives optional `error, transformedItem` arguments.\n* `callback(error, transformedArray)` optionally receives first error and transformed result `Array`.\n\n```javascript\nvar each = require('async-each');\neach(['a.js', 'b.js', 'c.js'], fs.readFile, function(error, contents) {\n  if (error) console.error(error);\n  console.log('Contents for a, b and c:', contents);\n});\n\n// Alternatively in browser:\nasyncEach(list, fn, callback);\n```\n\n## License\n\nThe MIT License (MIT)\n\nCopyright (c) 2016 <PERSON> [(paulmillr.com)](http://paulmillr.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the “Software”), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/async-each/-/async-each-1.0.3.tgz#b727dbf87d7651602f06f4d4ac387f47d91b0cbf", "type": "tarball", "reference": "https://registry.yarnpkg.com/async-each/-/async-each-1.0.3.tgz", "hash": "b727dbf87d7651602f06f4d4ac387f47d91b0cbf", "integrity": "sha512-z/WhQ5FPySLdvREByI2vZiTWwCnF0moMJ1hK9YQwDTHKh6I7/uSckMetoRGb5UBZPC1z0jlw+n/XCgjeH7y1AQ==", "registry": "npm", "packageName": "async-each", "cacheIntegrity": "sha512-z/WhQ5FPySLdvREByI2vZiTWwCnF0moMJ1hK9YQwDTHKh6I7/uSckMetoRGb5UBZPC1z0jlw+n/XCgjeH7y1AQ== sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8="}, "registry": "npm", "hash": "b727dbf87d7651602f06f4d4ac387f47d91b0cbf"}