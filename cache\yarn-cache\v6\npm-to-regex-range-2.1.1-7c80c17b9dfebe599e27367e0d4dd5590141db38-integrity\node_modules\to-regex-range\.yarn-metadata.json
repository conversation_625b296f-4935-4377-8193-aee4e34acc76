{"manifest": {"name": "to-regex-range", "description": "Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.", "version": "2.1.1", "homepage": "https://github.com/micromatch/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/micromatch/to-regex-range.git"}, "bugs": {"url": "https://github.com/micromatch/to-regex-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "devDependencies": {"fill-range": "^3.1.1", "gulp-format-md": "^0.1.12", "mocha": "^3.2.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "date", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sequence", "sh", "to", "year"], "verb": {"related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "helpers": ["./examples.js"], "reflinks": ["0-5", "0-9", "1-5", "1-9"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-to-regex-range-2.1.1-7c80c17b9dfebe599e27367e0d4dd5590141db38-integrity\\node_modules\\to-regex-range\\package.json", "readmeFilename": "README.md", "readme": "# to-regex-range [![NPM version](https://img.shields.io/npm/v/to-regex-range.svg?style=flat)](https://www.npmjs.com/package/to-regex-range) [![NPM monthly downloads](https://img.shields.io/npm/dm/to-regex-range.svg?style=flat)](https://npmjs.org/package/to-regex-range)  [![NPM total downloads](https://img.shields.io/npm/dt/to-regex-range.svg?style=flat)](https://npmjs.org/package/to-regex-range) [![Linux Build Status](https://img.shields.io/travis/micromatch/to-regex-range.svg?style=flat&label=Travis)](https://travis-ci.org/micromatch/to-regex-range)\n\n> Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save to-regex-range\n```\n\nInstall with [yarn](https://yarnpkg.com):\n\n```sh\n$ yarn add to-regex-range\n```\n\n<details>\n<summary><strong>What does this do?</strong></summary>\n\n<br>\n\nThis libary generates the `source` string to be passed to `new RegExp()` for matching a range of numbers.\n\n**Example**\n\n```js\nvar toRegexRange = require('to-regex-range');\nvar regex = new RegExp(toRegexRange('15', '95'));\n```\n\nA string is returned so that you can do whatever you need with it before passing it to `new RegExp()` (like adding `^` or `$` boundaries, defining flags, or combining it another string).\n\n<br>\n\n</details>\n\n<details>\n<summary><strong>Why use this library?</strong></summary>\n\n<br>\n\n### Convenience\n\nCreating regular expressions for matching numbers gets deceptively complicated pretty fast.\n\nFor example, let's say you need a validation regex for matching part of a user-id, postal code, social security number, tax id, etc:\n\n* regex for matching `1` => `/1/` (easy enough)\n* regex for matching `1` through `5` => `/[1-5]/` (not bad...)\n* regex for matching `1` or `5` => `/(1|5)/` (still easy...)\n* regex for matching `1` through `50` => `/([1-9]|[1-4][0-9]|50)/` (uh-oh...)\n* regex for matching `1` through `55` => `/([1-9]|[1-4][0-9]|5[0-5])/` (no prob, I can do this...)\n* regex for matching `1` through `555` => `/([1-9]|[1-9][0-9]|[1-4][0-9]{2}|5[0-4][0-9]|55[0-5])/` (maybe not...)\n* regex for matching `0001` through `5555` => `/(0{3}[1-9]|0{2}[1-9][0-9]|0[1-9][0-9]{2}|[1-4][0-9]{3}|5[0-4][0-9]{2}|55[0-4][0-9]|555[0-5])/` (okay, I get the point!)\n\nThe numbers are contrived, but they're also really basic. In the real world you might need to generate a regex on-the-fly for validation.\n\n**Learn more**\n\nIf you're interested in learning more about [character classes](http://www.regular-expressions.info/charclass.html) and other regex features, I personally have always found [regular-expressions.info](http://www.regular-expressions.info/charclass.html) to be pretty useful.\n\n### Heavily tested\n\nAs of April 27, 2017, this library runs [2,783,483 test assertions](./test/test.js) against generated regex-ranges to provide brute-force verification that results are indeed correct.\n\nTests run in ~870ms on my MacBook Pro, 2.5 GHz Intel Core i7.\n\n### Highly optimized\n\nGenerated regular expressions are highly optimized:\n\n* duplicate sequences and character classes are reduced using quantifiers\n* smart enough to use `?` conditionals when number(s) or range(s) can be positive or negative\n* uses fragment caching to avoid processing the same exact string more than once\n\n<br>\n\n</details>\n\n## Usage\n\nAdd this library to your javascript application with the following line of code\n\n```js\nvar toRegexRange = require('to-regex-range');\n```\n\nThe main export is a function that takes two integers: the `min` value and `max` value (formatted as strings or numbers).\n\n```js\nvar source = toRegexRange('15', '95');\n//=> 1[5-9]|[2-8][0-9]|9[0-5]\n\nvar re = new RegExp('^' + source + '$');\nconsole.log(re.test('14')); //=> false\nconsole.log(re.test('50')); //=> true\nconsole.log(re.test('94')); //=> true\nconsole.log(re.test('96')); //=> false\n```\n\n## Options\n\n### options.capture\n\n**Type**: `boolean`\n\n**Deafault**: `undefined`\n\nWrap the returned value in parentheses when there is more than one regex condition. Useful when you're dynamically generating ranges.\n\n```js\nconsole.log(toRegexRange('-10', '10'));\n//=> -[1-9]|-?10|[0-9]\n\nconsole.log(toRegexRange('-10', '10', {capture: true}));\n//=> (-[1-9]|-?10|[0-9])\n```\n\n### options.shorthand\n\n**Type**: `boolean`\n\n**Deafault**: `undefined`\n\nUse the regex shorthand for `[0-9]`:\n\n```js\nconsole.log(toRegexRange('0', '999999'));\n//=> [0-9]|[1-9][0-9]{1,5}\n\nconsole.log(toRegexRange('0', '999999', {shorthand: true}));\n//=> \\d|[1-9]\\d{1,5}\n```\n\n### options.relaxZeros\n\n**Type**: `boolean`\n\n**Default**: `true`\n\nThis option only applies to **negative zero-padded ranges**. By default, when a negative zero-padded range is defined, the number of leading zeros is relaxed using `-0*`.\n\n```js\nconsole.log(toRegexRange('-001', '100'));\n//=> -0*1|0{2}[0-9]|0[1-9][0-9]|100\n\nconsole.log(toRegexRange('-001', '100', {relaxZeros: false}));\n//=> -0{2}1|0{2}[0-9]|0[1-9][0-9]|100\n```\n\n<details>\n<summary><strong>Why are zeros relaxed for negative zero-padded ranges by default?</strong></summary>\n\nConsider the following.\n\n```js\nvar regex = toRegexRange('-001', '100');\n```\n\n_Note that `-001` and `100` are both three digits long_.\n\nIn most zero-padding implementations, only a single leading zero is enough to indicate that zero-padding should be applied. Thus, the leading zeros would be \"corrected\" on the negative range in the example to `-01`, instead of `-001`, to make total length of each string no greater than the length of the largest number in the range (in other words, `-001` is 4 digits, but `100` is only three digits).\n\nIf zeros were not relaxed by default, you might expect the resulting regex of the above pattern to match `-001` - given that it's defined that way in the arguments - _but it wouldn't_. It would, however, match `-01`. This gets even more ambiguous with large ranges, like `-01` to `1000000`.\n\nThus, we relax zeros by default to provide a more predictable experience for users.\n\n</details>\n\n## Examples\n\n| **Range** | **Result** | **Compile time** | \n| --- | --- | --- |\n| `toRegexRange('5, 5')` | `5` | _33μs_ |\n| `toRegexRange('5, 6')` | `5\\|6` | _53μs_ |\n| `toRegexRange('29, 51')` | `29\\|[34][0-9]\\|5[01]` | _699μs_ |\n| `toRegexRange('31, 877')` | `3[1-9]\\|[4-9][0-9]\\|[1-7][0-9]{2}\\|8[0-6][0-9]\\|87[0-7]` | _711μs_ |\n| `toRegexRange('111, 555')` | `11[1-9]\\|1[2-9][0-9]\\|[2-4][0-9]{2}\\|5[0-4][0-9]\\|55[0-5]` | _62μs_ |\n| `toRegexRange('-10, 10')` | `-[1-9]\\|-?10\\|[0-9]` | _74μs_ |\n| `toRegexRange('-100, -10')` | `-1[0-9]\\|-[2-9][0-9]\\|-100` | _49μs_ |\n| `toRegexRange('-100, 100')` | `-[1-9]\\|-?[1-9][0-9]\\|-?100\\|[0-9]` | _45μs_ |\n| `toRegexRange('001, 100')` | `0{2}[1-9]\\|0[1-9][0-9]\\|100` | _158μs_ |\n| `toRegexRange('0010, 1000')` | `0{2}1[0-9]\\|0{2}[2-9][0-9]\\|0[1-9][0-9]{2}\\|1000` | _61μs_ |\n| `toRegexRange('1, 2')` | `1\\|2` | _10μs_ |\n| `toRegexRange('1, 5')` | `[1-5]` | _24μs_ |\n| `toRegexRange('1, 10')` | `[1-9]\\|10` | _23μs_ |\n| `toRegexRange('1, 100')` | `[1-9]\\|[1-9][0-9]\\|100` | _30μs_ |\n| `toRegexRange('1, 1000')` | `[1-9]\\|[1-9][0-9]{1,2}\\|1000` | _52μs_ |\n| `toRegexRange('1, 10000')` | `[1-9]\\|[1-9][0-9]{1,3}\\|10000` | _47μs_ |\n| `toRegexRange('1, 100000')` | `[1-9]\\|[1-9][0-9]{1,4}\\|100000` | _44μs_ |\n| `toRegexRange('1, 1000000')` | `[1-9]\\|[1-9][0-9]{1,5}\\|1000000` | _49μs_ |\n| `toRegexRange('1, 10000000')` | `[1-9]\\|[1-9][0-9]{1,6}\\|10000000` | _63μs_ |\n\n## Heads up!\n\n**Order of arguments**\n\nWhen the `min` is larger than the `max`, values will be flipped to create a valid range:\n\n```js\ntoRegexRange('51', '29');\n```\n\nIs effectively flipped to:\n\n```js\ntoRegexRange('29', '51');\n//=> 29|[3-4][0-9]|5[0-1]\n```\n\n**Steps / increments**\n\nThis library does not support steps (increments). A pr to add support would be welcome.\n\n## History\n\n### v2.0.0 - 2017-04-21\n\n**New features**\n\nAdds support for zero-padding!\n\n### v1.0.0\n\n**Optimizations**\n\nRepeating ranges are now grouped using quantifiers. rocessing time is roughly the same, but the generated regex is much smaller, which should result in faster matching.\n\n## Attribution\n\nInspired by the python library [range-regex](https://github.com/dimka665/range-regex).\n\n## About\n\n### Related projects\n\n* [expand-range](https://www.npmjs.com/package/expand-range): Fast, bash-like range expansion. Expand a range of numbers or letters, uppercase or lowercase. See… [more](https://github.com/jonschlinkert/expand-range) | [homepage](https://github.com/jonschlinkert/expand-range \"Fast, bash-like range expansion. Expand a range of numbers or letters, uppercase or lowercase. See the benchmarks. Used by micromatch.\")\n* [fill-range](https://www.npmjs.com/package/fill-range): Fill in a range of numbers or letters, optionally passing an increment or `step` to… [more](https://github.com/jonschlinkert/fill-range) | [homepage](https://github.com/jonschlinkert/fill-range \"Fill in a range of numbers or letters, optionally passing an increment or `step` to use, or create a regex-compatible range with `options.toRegex`\")\n* [micromatch](https://www.npmjs.com/package/micromatch): Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. | [homepage](https://github.com/jonschlinkert/micromatch \"Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.\")\n* [repeat-element](https://www.npmjs.com/package/repeat-element): Create an array by repeating the given value n times. | [homepage](https://github.com/jonschlinkert/repeat-element \"Create an array by repeating the given value n times.\")\n* [repeat-string](https://www.npmjs.com/package/repeat-string): Repeat the given string n times. Fastest implementation for repeating a string. | [homepage](https://github.com/jonschlinkert/repeat-string \"Repeat the given string n times. Fastest implementation for repeating a string.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on April 27, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015-2017, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/to-regex-range/-/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38", "type": "tarball", "reference": "https://registry.yarnpkg.com/to-regex-range/-/to-regex-range-2.1.1.tgz", "hash": "7c80c17b9dfebe599e27367e0d4dd5590141db38", "integrity": "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=", "registry": "npm", "packageName": "to-regex-range", "cacheIntegrity": "sha512-ZZWNfCjUokXXDGXFpZehJIkZqq91BcULFq/Pi7M5i4JnxXdhMKAK682z8bCW3o8Hj1wuuzoKcW3DfVzaP6VuNg== sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg="}, "registry": "npm", "hash": "7c80c17b9dfebe599e27367e0d4dd5590141db38"}