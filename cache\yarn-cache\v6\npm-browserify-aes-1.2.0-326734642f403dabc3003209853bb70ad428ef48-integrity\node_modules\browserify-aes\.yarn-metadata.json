{"manifest": {"name": "browserify-aes", "version": "1.2.0", "description": "aes, for browserify", "browser": "browser.js", "main": "index.js", "scripts": {"standard": "standard", "unit": "node test/index.js | tspec", "test": "npm run standard && npm run unit"}, "repository": {"type": "git", "url": "git://github.com/crypto-browserify/browserify-aes.git"}, "keywords": ["aes", "crypto", "browserify"], "author": {}, "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/browserify-aes/issues"}, "homepage": "https://github.com/crypto-browserify/browserify-aes", "dependencies": {"buffer-xor": "^1.0.3", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.3", "inherits": "^2.0.1", "safe-buffer": "^5.0.1"}, "devDependencies": {"standard": "^9.0.0", "tap-spec": "^4.1.1", "tape": "^4.6.3"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-browserify-aes-1.2.0-326734642f403dabc3003209853bb70ad428ef48-integrity\\node_modules\\browserify-aes\\package.json", "readmeFilename": "README.md", "readme": "# browserify-aes\n[![Build Status](https://travis-ci.org/crypto-browserify/browserify-aes.svg)](https://travis-ci.org/crypto-browserify/browserify-aes)\n\nNode style aes for use in the browser.\nImplements:\n\n - createCipher\n - createCipheriv\n - createDecipher\n - createDecipheriv\n - getCiphers\n\nIn node.js, the `crypto` implementation is used, in browsers it falls back to a pure JavaScript implementation.\n\nMuch of this library has been taken from the aes implementation in [triplesec](https://github.com/keybase/triplesec),  a partial derivation of [crypto-js](https://code.google.com/p/crypto-js/).\n\n`EVP_BytesToKey` is a straight up port of the same function from OpenSSL as there is literally no documenation on it beyond it using 'undocumented extensions' for longer keys.\n\n## LICENSE [MIT](LICENSE)\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017 browserify-aes contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/browserify-aes/-/browserify-aes-1.2.0.tgz#326734642f403dabc3003209853bb70ad428ef48", "type": "tarball", "reference": "https://registry.yarnpkg.com/browserify-aes/-/browserify-aes-1.2.0.tgz", "hash": "326734642f403dabc3003209853bb70ad428ef48", "integrity": "sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA==", "registry": "npm", "packageName": "browserify-aes", "cacheIntegrity": "sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA== sha1-Mmc0ZC9APavDADIJhTu3CtQo70g="}, "registry": "npm", "hash": "326734642f403dabc3003209853bb70ad428ef48"}