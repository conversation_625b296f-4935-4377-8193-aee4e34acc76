{"manifest": {"name": "has-value", "description": "Returns true if a value exists, false if empty. Works with deeply nested values using object paths.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/has-value", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "https://linkedin.com/in/harrisonrm"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/has-value.git"}, "bugs": {"url": "https://github.com/jonschlinkert/has-value/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}, "devDependencies": {"gulp-format-md": "^0.1.12", "mocha": "^3.4.1"}, "keywords": ["array", "boolean", "empty", "find", "function", "has", "hasOwn", "javascript", "js", "key", "keys", "node.js", "null", "number", "object", "properties", "property", "string", "type", "util", "utilities", "utility", "value"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["define-property", "get-value", "set-value", "unset-value"]}, "reflinks": [], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-has-value-1.0.0-18b281da585b1c5c51def24c930ed29a0be6b177-integrity\\node_modules\\has-value\\package.json", "readmeFilename": "README.md", "readme": "# has-value [![NPM version](https://img.shields.io/npm/v/has-value.svg?style=flat)](https://www.npmjs.com/package/has-value) [![NPM monthly downloads](https://img.shields.io/npm/dm/has-value.svg?style=flat)](https://npmjs.org/package/has-value) [![NPM total downloads](https://img.shields.io/npm/dt/has-value.svg?style=flat)](https://npmjs.org/package/has-value) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/has-value.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/has-value)\n\n> Returns true if a value exists, false if empty. Works with deeply nested values using object paths.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save has-value\n```\n\n**Works for:**\n\n* booleans\n* functions\n* numbers\n* strings\n* nulls\n* object\n* arrays\n\n## Usage\n\nWorks with property values (supports object-path notation, like `foo.bar`) or a single value:\n\n```js\nvar hasValue = require('has-value');\n\nhasValue('foo');\nhasValue({foo: 'bar'}, 'foo');\nhasValue({a: {b: {c: 'foo'}}}, 'a.b.c');\n//=> true\n\nhasValue('');\nhasValue({foo: ''}, 'foo');\n//=> false\n\nhasValue(0);\nhasValue(1);\nhasValue({foo: 0}, 'foo');\nhasValue({foo: 1}, 'foo');\nhasValue({foo: null}, 'foo');\nhasValue({foo: {bar: 'a'}}}, 'foo');\nhasValue({foo: {bar: 'a'}}}, 'foo.bar');\n//=> true\n\nhasValue({foo: {}}}, 'foo');\nhasValue({foo: {bar: {}}}}, 'foo.bar');\nhasValue({foo: undefined}, 'foo');\n//=> false\n\nhasValue([]);\nhasValue([[]]);\nhasValue([[], []]);\nhasValue([undefined]);\nhasValue({foo: []}, 'foo');\n//=> false\n\nhasValue([0]);\nhasValue([null]);\nhasValue(['foo']);\nhasValue({foo: ['a']}, 'foo');\n//=> true\n\nhasValue(function() {})\nhasValue(function(foo) {})\nhasValue({foo: function(foo) {}}, 'foo'); \nhasValue({foo: function() {}}, 'foo');\n//=> true\n\nhasValue(true);\nhasValue(false);\nhasValue({foo: true}, 'foo');\nhasValue({foo: false}, 'foo');\n//=> true\n```\n\n## isEmpty\n\nTo do the opposite and test for empty values, do:\n\n```js\nfunction isEmpty(o) {\n  return !hasValue.apply(hasValue, arguments);\n}\n```\n\n## Release history\n\n### v1.0.0\n\n* `zero` always returns true\n* `array` now recurses, so that an array of empty arrays will return `false`\n* `null` now returns true\n\n## About\n\n### Related projects\n\n* [define-property](https://www.npmjs.com/package/define-property): Define a non-enumerable property on an object. | [homepage](https://github.com/jonschlinkert/define-property \"Define a non-enumerable property on an object.\")\n* [get-value](https://www.npmjs.com/package/get-value): Use property paths (`a.b.c`) to get a nested value from an object. | [homepage](https://github.com/jonschlinkert/get-value \"Use property paths (`a.b.c`) to get a nested value from an object.\")\n* [set-value](https://www.npmjs.com/package/set-value): Create nested values and any intermediaries using dot notation (`'a.b.c'`) paths. | [homepage](https://github.com/jonschlinkert/set-value \"Create nested values and any intermediaries using dot notation (`'a.b.c'`) paths.\")\n* [unset-value](https://www.npmjs.com/package/unset-value): Delete nested properties from an object using dot notation. | [homepage](https://github.com/jonschlinkert/unset-value \"Delete nested properties from an object using dot notation.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 17 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 2 | [rmharrison](https://github.com/rmharrison) |\n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on May 19, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/has-value/-/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177", "type": "tarball", "reference": "https://registry.yarnpkg.com/has-value/-/has-value-1.0.0.tgz", "hash": "18b281da585b1c5c51def24c930ed29a0be6b177", "integrity": "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=", "registry": "npm", "packageName": "has-value", "cacheIntegrity": "sha512-IBXk4GTsLYdQ7Rvt+GRBrFSVEkmuOUy4re0Xjd9kJSUQpnTrWR4/y9RpfexN9vkAPMFuQoeWKwqzPozRTlasGw== sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc="}, "registry": "npm", "hash": "18b281da585b1c5c51def24c930ed29a0be6b177"}