{"manifest": {"name": "is-descriptor", "description": "Returns true if a value has the characteristics of a valid JavaScript descriptor. Works for data descriptors and accessor descriptors.", "version": "1.0.2", "homepage": "https://github.com/jonschlinkert/is-descriptor", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-descriptor.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-descriptor/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["accessor", "check", "data", "descriptor", "get", "getter", "is", "keys", "object", "properties", "property", "set", "setter", "type", "valid", "value"], "verb": {"related": {"list": ["is-accessor-descriptor", "is-data-descriptor", "is-descriptor", "isobject"]}, "plugins": ["gulp-format-md"], "toc": false, "layout": "default", "tasks": ["readme"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-descriptor-1.0.2-3b159746a66604b04f8c81524ba365c5f14d86ec-integrity\\node_modules\\is-descriptor\\package.json", "readmeFilename": "README.md", "readme": "# is-descriptor [![NPM version](https://img.shields.io/npm/v/is-descriptor.svg?style=flat)](https://www.npmjs.com/package/is-descriptor) [![NPM monthly downloads](https://img.shields.io/npm/dm/is-descriptor.svg?style=flat)](https://npmjs.org/package/is-descriptor) [![NPM total downloads](https://img.shields.io/npm/dt/is-descriptor.svg?style=flat)](https://npmjs.org/package/is-descriptor) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/is-descriptor.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/is-descriptor)\n\n> Returns true if a value has the characteristics of a valid JavaScript descriptor. Works for data descriptors and accessor descriptors.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save is-descriptor\n```\n\n## Usage\n\n```js\nvar isDescriptor = require('is-descriptor');\n\nisDescriptor({value: 'foo'})\n//=> true\nisDescriptor({get: function(){}, set: function(){}})\n//=> true\nisDescriptor({get: 'foo', set: function(){}})\n//=> false\n```\n\nYou may also check for a descriptor by passing an object as the first argument and property name (`string`) as the second argument.\n\n```js\nvar obj = {};\nobj.foo = 'abc';\n\nObject.defineProperty(obj, 'bar', {\n  value: 'xyz'\n});\n\nisDescriptor(obj, 'foo');\n//=> true\nisDescriptor(obj, 'bar');\n//=> true\n```\n\n## Examples\n\n### value type\n\n`false` when not an object\n\n```js\nisDescriptor('a');\n//=> false\nisDescriptor(null);\n//=> false\nisDescriptor([]);\n//=> false\n```\n\n### data descriptor\n\n`true` when the object has valid properties with valid values.\n\n```js\nisDescriptor({value: 'foo'});\n//=> true\nisDescriptor({value: noop});\n//=> true\n```\n\n`false` when the object has invalid properties\n\n```js\nisDescriptor({value: 'foo', bar: 'baz'});\n//=> false\nisDescriptor({value: 'foo', bar: 'baz'});\n//=> false\nisDescriptor({value: 'foo', get: noop});\n//=> false\nisDescriptor({get: noop, value: noop});\n//=> false\n```\n\n`false` when a value is not the correct type\n\n```js\nisDescriptor({value: 'foo', enumerable: 'foo'});\n//=> false\nisDescriptor({value: 'foo', configurable: 'foo'});\n//=> false\nisDescriptor({value: 'foo', writable: 'foo'});\n//=> false\n```\n\n### accessor descriptor\n\n`true` when the object has valid properties with valid values.\n\n```js\nisDescriptor({get: noop, set: noop});\n//=> true\nisDescriptor({get: noop});\n//=> true\nisDescriptor({set: noop});\n//=> true\n```\n\n`false` when the object has invalid properties\n\n```js\nisDescriptor({get: noop, set: noop, bar: 'baz'});\n//=> false\nisDescriptor({get: noop, writable: true});\n//=> false\nisDescriptor({get: noop, value: true});\n//=> false\n```\n\n`false` when an accessor is not a function\n\n```js\nisDescriptor({get: noop, set: 'baz'});\n//=> false\nisDescriptor({get: 'foo', set: noop});\n//=> false\nisDescriptor({get: 'foo', bar: 'baz'});\n//=> false\nisDescriptor({get: 'foo', set: 'baz'});\n//=> false\n```\n\n`false` when a value is not the correct type\n\n```js\nisDescriptor({get: noop, set: noop, enumerable: 'foo'});\n//=> false\nisDescriptor({set: noop, configurable: 'foo'});\n//=> false\nisDescriptor({get: noop, configurable: 'foo'});\n//=> false\n```\n\n## About\n\n### Related projects\n\n* [is-accessor-descriptor](https://www.npmjs.com/package/is-accessor-descriptor): Returns true if a value has the characteristics of a valid JavaScript accessor descriptor. | [homepage](https://github.com/jonschlinkert/is-accessor-descriptor \"Returns true if a value has the characteristics of a valid JavaScript accessor descriptor.\")\n* [is-data-descriptor](https://www.npmjs.com/package/is-data-descriptor): Returns true if a value has the characteristics of a valid JavaScript data descriptor. | [homepage](https://github.com/jonschlinkert/is-data-descriptor \"Returns true if a value has the characteristics of a valid JavaScript data descriptor.\")\n* [is-descriptor](https://www.npmjs.com/package/is-descriptor): Returns true if a value has the characteristics of a valid JavaScript descriptor. Works for… [more](https://github.com/jonschlinkert/is-descriptor) | [homepage](https://github.com/jonschlinkert/is-descriptor \"Returns true if a value has the characteristics of a valid JavaScript descriptor. Works for data descriptors and accessor descriptors.\")\n* [isobject](https://www.npmjs.com/package/isobject): Returns true if the value is an object and not an array or null. | [homepage](https://github.com/jonschlinkert/isobject \"Returns true if the value is an object and not an array or null.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 24 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 1 | [doowb](https://github.com/doowb) |\n| 1 | [wtgtybhertgeghgtwtg](https://github.com/wtgtybhertgeghgtwtg) |\n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on July 22, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015-2017, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-descriptor/-/is-descriptor-1.0.2.tgz#3b159746a66604b04f8c81524ba365c5f14d86ec", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-descriptor/-/is-descriptor-1.0.2.tgz", "hash": "3b159746a66604b04f8c81524ba365c5f14d86ec", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "registry": "npm", "packageName": "is-descriptor", "cacheIntegrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg== sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw="}, "registry": "npm", "hash": "3b159746a66604b04f8c81524ba365c5f14d86ec"}