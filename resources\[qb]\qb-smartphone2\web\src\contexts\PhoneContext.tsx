import React, { createContext, useContext, useState, ReactNode } from 'react';

export interface PhoneSettings {
  wallpaper: string;
  theme: 'light' | 'dark';
  ringtone: string;
  notification_sound: number;
  vibration: number;
  battery_level: number;
  pin_code?: string;
  fingerprint_enabled: number;
  bluetooth_enabled: number;
  widgets?: any;
  app_layout?: any;
}

export interface PhoneData {
  phoneNumber: string;
  batteryLevel: number;
  isCharging: boolean;
  isLocked: boolean;
  settings: PhoneSettings;
  notifications: any[];
}

interface PhoneContextType {
  isVisible: boolean;
  isLocked: boolean;
  currentApp: string | null;
  phoneData: PhoneData | null;
  setIsVisible: (visible: boolean) => void;
  setIsLocked: (locked: boolean) => void;
  setCurrentApp: (app: string | null) => void;
  setPhoneData: (data: PhoneData | ((prev: PhoneData | null) => PhoneData | null)) => void;
  openApp: (appName: string) => void;
  closeApp: () => void;
  unlockPhone: () => void;
  lockPhone: () => void;
}

const PhoneContext = createContext<PhoneContextType | undefined>(undefined);

interface PhoneProviderProps {
  children: ReactNode;
}

export const PhoneProvider: React.FC<PhoneProviderProps> = ({ children }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLocked, setIsLocked] = useState(true);
  const [currentApp, setCurrentApp] = useState<string | null>(null);
  const [phoneData, setPhoneData] = useState<PhoneData | null>(null);

  const openApp = (appName: string) => {
    setCurrentApp(appName);
  };

  const closeApp = () => {
    setCurrentApp(null);
  };

  const unlockPhone = () => {
    setIsLocked(false);
  };

  const lockPhone = () => {
    setIsLocked(true);
    setCurrentApp(null);
  };

  const value: PhoneContextType = {
    isVisible,
    isLocked,
    currentApp,
    phoneData,
    setIsVisible,
    setIsLocked,
    setCurrentApp,
    setPhoneData,
    openApp,
    closeApp,
    unlockPhone,
    lockPhone,
  };

  return (
    <PhoneContext.Provider value={value}>
      {children}
    </PhoneContext.Provider>
  );
};

export const usePhone = (): PhoneContextType => {
  const context = useContext(PhoneContext);
  if (!context) {
    throw new Error('usePhone must be used within a PhoneProvider');
  }
  return context;
};
