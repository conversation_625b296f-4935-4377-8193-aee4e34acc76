{"manifest": {"name": "@types/content-disposition", "version": "0.5.3", "description": "TypeScript definitions for content-disposition", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/bomret"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/content-disposition"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "713abf3c213aa73aac34682a2a9f500cc7cdf72fdd23e8c027d63e42fbc3e494", "typeScriptVersion": "2.8", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-content-disposition-0.5.3-0aa116701955c2faa0717fc69cd1596095e49d96-integrity\\node_modules\\@types\\content-disposition\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/content-disposition`\n\n# Summary\nThis package contains type definitions for content-disposition (https://github.com/jshttp/content-disposition).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/content-disposition.\n\n### Additional Details\n * Last updated: <PERSON><PERSON>, 31 Mar 2020 22:24:18 GMT\n * Dependencies: none\n * Global values: none\n\n# Credits\nThese definitions were written by [<PERSON>](https://github.com/bomret), and [<PERSON><PERSON><PERSON>](https://github.com/peter<PERSON><PERSON><PERSON><PERSON>).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation. All rights reserved.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/content-disposition/-/content-disposition-0.5.3.tgz#0aa116701955c2faa0717fc69cd1596095e49d96", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/content-disposition/-/content-disposition-0.5.3.tgz", "hash": "0aa116701955c2faa0717fc69cd1596095e49d96", "integrity": "sha512-P1bffQfhD3O4LW0ioENXUhZ9OIa0Zn+P7M+pWgkCKaT53wVLSq0mrKksCID/FGHpFhRSxRGhgrQmfhRuzwtKdg==", "registry": "npm", "packageName": "@types/content-disposition", "cacheIntegrity": "sha512-P1bffQfhD3O4LW0ioENXUhZ9OIa0Zn+P7M+pWgkCKaT53wVLSq0mrKksCID/FGHpFhRSxRGhgrQmfhRuzwtKdg== sha1-CqEWcBlVwvqgcX/GnNFZYJXknZY="}, "registry": "npm", "hash": "0aa116701955c2faa0717fc69cd1596095e49d96"}