{"name": "is-regex", "version": "1.1.1", "description": "Is this value a JS regex? Works cross-realm/iframe, and despite ES6 @@toStringTag", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "node --harmony --es-staging test", "posttest": "npx aud --production", "coverage": "covert test/index.js", "lint": "eslint .", "eccheck": "eclint check *.js **/*.js > /dev/null", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/ljharb/is-regex.git"}, "bugs": {"url": "https://github.com/ljharb/is-regex/issues"}, "homepage": "https://github.com/ljharb/is-regex", "keywords": ["regex", "regexp", "is", "regular expression", "regular", "expression"], "dependencies": {"has-symbols": "^1.0.1"}, "devDependencies": {"@ljharb/eslint-config": "^17.1.0", "aud": "^1.1.2", "auto-changelog": "^2.2.0", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^7.6.0", "foreach": "^2.0.5", "safe-publish-latest": "^1.1.4", "tape": "^5.0.1"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}}