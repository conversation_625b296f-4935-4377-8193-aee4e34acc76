{"manifest": {"name": "@types/http-errors", "version": "1.8.0", "description": "TypeScript definitions for http-errors", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/http-errors"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "61c68cc213bf1a21d07bfac01c18754adea53b40a66a043c89fb6a35957ac0c9", "typeScriptVersion": "3.0", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-http-errors-1.8.0-682477dbbbd07cd032731cb3b0e7eaee3d026b69-integrity\\node_modules\\@types\\http-errors\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/http-errors`\n\n# Summary\nThis package contains type definitions for http-errors (https://github.com/jshttp/http-errors).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-errors.\n\n### Additional Details\n * Last updated: Fri, 10 Jul 2020 14:16:15 GMT\n * Dependencies: none\n * Global values: none\n\n# Credits\nThese definitions were written by [<PERSON><PERSON>](https://github.com/tkrotoff), and [BendingBender](https://github.com/BendingBender).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/http-errors/-/http-errors-1.8.0.tgz#682477dbbbd07cd032731cb3b0e7eaee3d026b69", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/http-errors/-/http-errors-1.8.0.tgz", "hash": "682477dbbbd07cd032731cb3b0e7eaee3d026b69", "integrity": "sha512-2aoSC4UUbHDj2uCsCxcG/vRMXey/m17bC7UwitVm5hn22nI8O8Y9iDpA76Orc+DWkQ4zZrOKEshCqR/jSuXAHA==", "registry": "npm", "packageName": "@types/http-errors", "cacheIntegrity": "sha512-2aoSC4UUbHDj2uCsCxcG/vRMXey/m17bC7UwitVm5hn22nI8O8Y9iDpA76Orc+DWkQ4zZrOKEshCqR/jSuXAHA== sha1-aCR327vQfNAycxyzsOfq7j0Ca2k="}, "registry": "npm", "hash": "682477dbbbd07cd032731cb3b0e7eaee3d026b69"}