{"/Users/<USER>/Dropbox/git/Object.getOwnPropertyDescriptors.git/auto.js": {"path": "/Users/<USER>/Dropbox/git/Object.getOwnPropertyDescriptors.git/auto.js", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 20}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Dropbox/git/Object.getOwnPropertyDescriptors.git/implementation.js": {"path": "/Users/<USER>/Dropbox/git/Object.getOwnPropertyDescriptors.git/implementation.js", "statementMap": {"0": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 71}}, "1": {"start": {"line": 4, "column": 17}, "end": {"line": 4, "column": 55}}, "2": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 79}}, "3": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 51}}, "4": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 46}}, "5": {"start": {"line": 9, "column": 12}, "end": {"line": 9, "column": 43}}, "6": {"start": {"line": 10, "column": 19}, "end": {"line": 10, "column": 45}}, "7": {"start": {"line": 11, "column": 18}, "end": {"line": 11, "column": 46}}, "8": {"start": {"line": 12, "column": 14}, "end": {"line": 12, "column": 49}}, "9": {"start": {"line": 13, "column": 14}, "end": {"line": 13, "column": 49}}, "10": {"start": {"line": 14, "column": 13}, "end": {"line": 16, "column": 16}}, "11": {"start": {"line": 15, "column": 1}, "end": {"line": 15, "column": 53}}, "12": {"start": {"line": 18, "column": 12}, "end": {"line": 18, "column": 57}}, "13": {"start": {"line": 20, "column": 0}, "end": {"line": 38, "column": 2}}, "14": {"start": {"line": 21, "column": 1}, "end": {"line": 21, "column": 31}}, "15": {"start": {"line": 22, "column": 1}, "end": {"line": 24, "column": 2}}, "16": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 92}}, "17": {"start": {"line": 26, "column": 9}, "end": {"line": 26, "column": 24}}, "18": {"start": {"line": 27, "column": 1}, "end": {"line": 37, "column": 3}}, "19": {"start": {"line": 30, "column": 20}, "end": {"line": 30, "column": 33}}, "20": {"start": {"line": 31, "column": 3}, "end": {"line": 33, "column": 4}}, "21": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 45}}, "22": {"start": {"line": 34, "column": 3}, "end": {"line": 34, "column": 14}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 14, "column": 27}, "end": {"line": 14, "column": 28}}, "loc": {"start": {"line": 14, "column": 42}, "end": {"line": 16, "column": 1}}, "line": 14}, "1": {"name": "getOwnPropertyDescriptors", "decl": {"start": {"line": 20, "column": 26}, "end": {"line": 20, "column": 51}}, "loc": {"start": {"line": 20, "column": 59}, "end": {"line": 38, "column": 1}}, "line": 20}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 3}}, "loc": {"start": {"line": 29, "column": 22}, "end": {"line": 35, "column": 3}}, "line": 29}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 13}, "end": {"line": 16, "column": 16}}, "type": "cond-expr", "locations": [{"start": {"line": 14, "column": 27}, "end": {"line": 16, "column": 1}}, {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 16}}], "line": 14}, "1": {"loc": {"start": {"line": 18, "column": 12}, "end": {"line": 18, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 18, "column": 12}, "end": {"line": 18, "column": 29}}, {"start": {"line": 18, "column": 33}, "end": {"line": 18, "column": 57}}], "line": 18}, "2": {"loc": {"start": {"line": 22, "column": 1}, "end": {"line": 24, "column": 2}}, "type": "if", "locations": [{"start": {"line": 22, "column": 1}, "end": {"line": 24, "column": 2}}, {"start": {"line": 22, "column": 1}, "end": {"line": 24, "column": 2}}], "line": 22}, "3": {"loc": {"start": {"line": 31, "column": 3}, "end": {"line": 33, "column": 4}}, "type": "if", "locations": [{"start": {"line": 31, "column": 3}, "end": {"line": 33, "column": 4}}, {"start": {"line": 31, "column": 3}, "end": {"line": 33, "column": 4}}], "line": 31}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "/Users/<USER>/Dropbox/git/Object.getOwnPropertyDescriptors.git/index.js": {"path": "/Users/<USER>/Dropbox/git/Object.getOwnPropertyDescriptors.git/index.js", "statementMap": {"0": {"start": {"line": 3, "column": 13}, "end": {"line": 3, "column": 41}}, "1": {"start": {"line": 4, "column": 15}, "end": {"line": 4, "column": 35}}, "2": {"start": {"line": 6, "column": 21}, "end": {"line": 6, "column": 48}}, "3": {"start": {"line": 7, "column": 18}, "end": {"line": 7, "column": 39}}, "4": {"start": {"line": 8, "column": 11}, "end": {"line": 8, "column": 28}}, "5": {"start": {"line": 10, "column": 12}, "end": {"line": 10, "column": 43}}, "6": {"start": {"line": 12, "column": 0}, "end": {"line": 16, "column": 3}}, "7": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 23}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {}, "b": {}}, "/Users/<USER>/Dropbox/git/Object.getOwnPropertyDescriptors.git/polyfill.js": {"path": "/Users/<USER>/Dropbox/git/Object.getOwnPropertyDescriptors.git/polyfill.js", "statementMap": {"0": {"start": {"line": 3, "column": 21}, "end": {"line": 3, "column": 48}}, "1": {"start": {"line": 5, "column": 0}, "end": {"line": 7, "column": 2}}, "2": {"start": {"line": 6, "column": 1}, "end": {"line": 6, "column": 115}}}, "fnMap": {"0": {"name": "getPolyfill", "decl": {"start": {"line": 5, "column": 26}, "end": {"line": 5, "column": 37}}, "loc": {"start": {"line": 5, "column": 40}, "end": {"line": 7, "column": 1}}, "line": 5}}, "branchMap": {"0": {"loc": {"start": {"line": 6, "column": 8}, "end": {"line": 6, "column": 114}}, "type": "cond-expr", "locations": [{"start": {"line": 6, "column": 65}, "end": {"line": 6, "column": 97}}, {"start": {"line": 6, "column": 100}, "end": {"line": 6, "column": 114}}], "line": 6}}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {"0": [0, 0]}}, "/Users/<USER>/Dropbox/git/Object.getOwnPropertyDescriptors.git/shim.js": {"path": "/Users/<USER>/Dropbox/git/Object.getOwnPropertyDescriptors.git/shim.js", "statementMap": {"0": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": 39}}, "1": {"start": {"line": 4, "column": 13}, "end": {"line": 4, "column": 41}}, "2": {"start": {"line": 6, "column": 0}, "end": {"line": 14, "column": 2}}, "3": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 29}}, "4": {"start": {"line": 8, "column": 1}, "end": {"line": 12, "column": 3}}, "5": {"start": {"line": 11, "column": 45}, "end": {"line": 11, "column": 98}}, "6": {"start": {"line": 13, "column": 1}, "end": {"line": 13, "column": 17}}}, "fnMap": {"0": {"name": "shimGetOwnPropertyDescriptors", "decl": {"start": {"line": 6, "column": 26}, "end": {"line": 6, "column": 55}}, "loc": {"start": {"line": 6, "column": 58}, "end": {"line": 14, "column": 1}}, "line": 6}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 11, "column": 31}, "end": {"line": 11, "column": 32}}, "loc": {"start": {"line": 11, "column": 43}, "end": {"line": 11, "column": 100}}, "line": 11}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {"0": 0, "1": 0}, "b": {}}}