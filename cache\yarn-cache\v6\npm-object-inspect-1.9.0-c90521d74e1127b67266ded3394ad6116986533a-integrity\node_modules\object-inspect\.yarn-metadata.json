{"manifest": {"name": "object-inspect", "version": "1.9.0", "description": "string representations of objects in node and the browser", "main": "index.js", "devDependencies": {"@ljharb/eslint-config": "^17.3.0", "aud": "^1.1.3", "core-js": "^2.6.12", "eslint": "^7.14.0", "for-each": "^0.3.3", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "string.prototype.repeat": "^1.0.0", "tape": "^5.0.1"}, "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint .", "test": "npm run tests-only", "tests-only": "nyc npm run tests-only:tape", "pretests-only:tape": "node test-core-js", "tests-only:tape": "tape 'test/*.js'", "posttest": "npx aud --production"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/object-inspect.git"}, "homepage": "https://github.com/inspect-js/object-inspect", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "browser": {"./util.inspect.js": false}, "greenkeeper": {"ignore": ["nyc", "core-js"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-object-inspect-1.9.0-c90521d74e1127b67266ded3394ad6116986533a-integrity\\node_modules\\object-inspect\\package.json", "readmeFilename": "readme.markdown", "readme": "# object-inspect\n\nstring representations of objects in node and the browser\n\n[![build status](https://secure.travis-ci.com/inspect-js/object-inspect.png)](https://travis-ci.com/inspect-js/object-inspect)\n\n# example\n\n## circular\n\n``` js\nvar inspect = require('object-inspect');\nvar obj = { a: 1, b: [3,4] };\nobj.c = obj;\nconsole.log(inspect(obj));\n```\n\n## dom element\n\n``` js\nvar inspect = require('object-inspect');\n\nvar d = document.createElement('div');\nd.setAttribute('id', 'beep');\nd.innerHTML = '<b>wooo</b><i>iiiii</i>';\n\nconsole.log(inspect([ d, { a: 3, b : 4, c: [5,6,[7,[8,[9]]]] } ]));\n```\n\noutput:\n\n```\n[ <div id=\"beep\">...</div>, { a: 3, b: 4, c: [ 5, 6, [ 7, [ 8, [ ... ] ] ] ] } ]\n```\n\n# methods\n\n``` js\nvar inspect = require('object-inspect')\n```\n\n## var s = inspect(obj, opts={})\n\nReturn a string `s` with the string representation of `obj` up to a depth of `opts.depth`.\n\nAdditional options:\n  - `quoteStyle`: must be \"single\" or \"double\", if present. Default `'single'` for strings, `'double'` for HTML elements.\n  - `maxStringLength`: must be `0`, a positive integer, `Infinity`, or `null`, if present. Default `Infinity`.\n  - `customInspect`: When `true`, a custom inspect method function will be invoked. Default `true`.\n  - `indent`: must be \"\\t\", `null`, or a positive integer. Default `null`.\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install object-inspect\n```\n\n# license\n\nMIT\n", "licenseText": "MIT License\n\nCopyright (c) 2013 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/object-inspect/-/object-inspect-1.9.0.tgz#c90521d74e1127b67266ded3394ad6116986533a", "type": "tarball", "reference": "https://registry.yarnpkg.com/object-inspect/-/object-inspect-1.9.0.tgz", "hash": "c90521d74e1127b67266ded3394ad6116986533a", "integrity": "sha512-i3Bp9iTqwhaLZBxGkRfo5ZbE07BQRT7MGu8+nNgwW9ItGp1TzCTw2DLEoWwjClxBjOFI/hWljTAmYGCEwmtnOw==", "registry": "npm", "packageName": "object-inspect", "cacheIntegrity": "sha512-i3Bp9iTqwhaLZBxGkRfo5ZbE07BQRT7MGu8+nNgwW9ItGp1TzCTw2DLEoWwjClxBjOFI/hWljTAmYGCEwmtnOw== sha1-yQUh104RJ7ZyZt7TOUrWEWmGUzo="}, "registry": "npm", "hash": "c90521d74e1127b67266ded3394ad6116986533a"}