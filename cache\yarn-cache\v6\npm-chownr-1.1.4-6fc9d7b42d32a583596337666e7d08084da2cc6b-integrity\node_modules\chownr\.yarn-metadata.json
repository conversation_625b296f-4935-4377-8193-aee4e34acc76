{"manifest": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "1.1.4", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "files": ["chownr.js"], "devDependencies": {"mkdirp": "0.3", "rimraf": "^2.7.1", "tap": "^14.10.6"}, "tap": {"check-coverage": true}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "license": "ISC", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-chownr-1.1.4-6fc9d7b42d32a583596337666e7d08084da2cc6b-integrity\\node_modules\\chownr\\package.json", "readmeFilename": "README.md", "readme": "Like `chown -R`.\n\nTakes the same arguments as `fs.chown()`\n", "licenseText": "The ISC License\n\nCopyright (c) <PERSON> and Contributors\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/chownr/-/chownr-1.1.4.tgz#6fc9d7b42d32a583596337666e7d08084da2cc6b", "type": "tarball", "reference": "https://registry.yarnpkg.com/chownr/-/chownr-1.1.4.tgz", "hash": "6fc9d7b42d32a583596337666e7d08084da2cc6b", "integrity": "sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==", "registry": "npm", "packageName": "chownr", "cacheIntegrity": "sha512-jJ<PERSON>bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg== sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs="}, "registry": "npm", "hash": "6fc9d7b42d32a583596337666e7d08084da2cc6b"}