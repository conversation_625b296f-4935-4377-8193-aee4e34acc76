# 📱 QB-SMARTPHONE2 - Moderní smartphone systém pro QBCore

Kompletní moderní smartphone systém s React frontend a všemi požadovanými funkcemi.

## 🚀 Instalace

### 1. Databáze
Importuj `qb-smartphone2.sql` do své MySQL databáze:
```sql
-- Spusť SQL soubor v phpMyAdmin nebo MySQL Workbench
```

### 2. Resource
1. Zkopíruj složku `qb-smartphone2` do `resources/[qb]/`
2. Přidej do `server.cfg`:
```
ensure qb-smartphone2
```

### 3. Frontend Build
1. Přejdi do `web/` složky:
```bash
cd resources/[qb]/qb-smartphone2/web/
```

2. Nainstaluj dependencies:
```bash
npm install
```

3. Build frontend:
```bash
npm run build
```

### 4. Restart serveru
Restartuj FiveM server nebo použij:
```
restart qb-smartphone2
```

## 🎮 Použití

### Základní ovládání
- **F1** - Otevření/zavření telefonu
- **ESC** - Zavření telefonu
- **Item**: `phone` - Použitelný item pro otevření telefonu

### Konfigurace
Všechna nastavení najdeš v `config.lua`:
- Klávesové zkratky
- Baterie nastavení
- Limity (kontakty, zprávy, fotky)
- Poplatky za inzeráty
- DarkWeb item

## 📱 Funkce

### ✅ Implementované
- **Základní systém**: Otevření/zavření, baterie, notifikace
- **Kontakty**: Přidávání, mazání, oblíbené, blokování
- **Volání**: RTC integrace (pma-voice/tokovoip)
- **SMS**: Posílání zpráv, historie, přílohy
- **GPS**: Navigace, sdílení lokace, uložené místa
- **Fotoaparát**: Pořizování fotek, uložení do galerie
- **DarkWeb**: Nákup věcí, anonymní chat, hackování
- **Sociální síť**: Posty, lajky, komentáře, sledování
- **Marketplace**: Inzeráty, kategorie, kontaktování
- **Banking**: Převody, QR platby, historie transakcí

### 🔄 V implementaci
- Kompletní React frontend pro všechny aplikace
- Email systém
- Kalendář s Discord integrací
- Minihry (Snake, TapTap, Memory)
- Widgety na homescreen

## 🛠️ Vývoj

### Frontend Development
Pro vývoj frontend v prohlížeči:
```bash
cd web/
npm start
```

### Struktura projektu
```
qb-smartphone2/
├── client/          # Client-side Lua scripty
├── server/          # Server-side Lua scripty
├── web/            # React frontend
│   ├── src/
│   │   ├── apps/   # Aplikace telefonu
│   │   ├── components/ # UI komponenty
│   │   ├── contexts/   # React kontexty
│   │   └── utils/      # Utility funkce
│   └── public/
├── config.lua      # Konfigurace
└── qb-smartphone2.sql # Databázové tabulky
```

## 🔧 API & Exporty

### Server Exporty
```lua
-- Odeslání notifikace
exports['qb-smartphone2']:SendNotification(phoneNumber, title, message, app, data)

-- Kontrola telefonu
exports['qb-smartphone2']:HasPhone(source)

-- Získání telefonního čísla
exports['qb-smartphone2']:GetPlayerPhoneNumber(source)

-- Odeslání zprávy
exports['qb-smartphone2']:SendMessage(senderNumber, receiverNumber, message, attachments)
```

### Client Exporty
```lua
-- Přehrání animace
exports['qb-smartphone2']:PlayPhoneAnimation(animationType, duration)

-- Nastavení GPS waypoint
exports['qb-smartphone2']:SetWaypoint(x, y, label)

-- Získání současné lokace
exports['qb-smartphone2']:GetCurrentLocation()
```

## 🎨 Témata & Přizpůsobení

### Wallpapery
Přidej vlastní wallpapery do `web/public/wallpapers/`:
- Podporované formáty: JPG, PNG
- Doporučené rozlišení: 375x812px

### Témata
Systém podporuje světlý a tmavý režim. Barvy lze upravit v:
- `web/src/contexts/ThemeContext.tsx`

## 🐛 Řešení problémů

### Telefon se neotevírá
1. Zkontroluj, zda máš item `phone`
2. Zkontroluj console pro chyby
3. Ověř, že je resource správně načten

### Frontend se nenačítá
1. Zkontroluj, zda je build složka vytvořena
2. Spusť `npm run build` v web/ složce
3. Restartuj resource

### Databázové chyby
1. Zkontroluj, zda jsou všechny tabulky vytvořeny
2. Ověř MySQL připojení
3. Zkontroluj oxmysql dependency

## 📞 Podpora

Pro podporu a hlášení chyb:
- GitHub Issues
- Discord: QB-Smartphone2 Support

## 📄 Licence

MIT License - viz LICENSE soubor

## 🤝 Přispívání

1. Fork repository
2. Vytvoř feature branch
3. Commit změny
4. Push do branch
5. Vytvoř Pull Request

---

**Vytvořeno s ❤️ pro QBCore komunitu**
