:root {
    --text-color: #000000;
    --background-color: #c0c0c0;
    --font-size: 0.7vh;
    --hover-color: #bdbdbd;
}

body {
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
    overflow: hidden;
    display: none;
    font-family: "Roboto", sans-serif;
    background: transparent;
    background-color: transparent;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

::-webkit-scrollbar {
    width: 0.2vw;
}

::-webkit-scrollbar-thumb {
    background: white;
}

::-webkit-scrollbar-track {
    background: #2d2e31;
}

#qbcore-inventory {
    width: 100%;
    height: 100%;
    background: radial-gradient(83% 83% at 50% 50%, rgba(12, 13, 18, 0.88), rgba(14, 15, 19, 1)) !important;
    display: none;
}

.inv-container {
    position: absolute;
    width: 100%;
    height: 100%;
}


#player-inv-label{
    color: #0ceebf;
    font-size: 1.5vh;
    position: absolute;
    top: 28%;
    left: 32%;
}

#other-inv-label{
    color: #0ceebf;
    font-size: 1.5vh;
    position: absolute;
    left: 29%;
    top: 27%;
    right: 30%;
}

#player-inv-weight-value{
    color: rgb(209 213 219 / 1);
    font-size: 1vh;
    line-height: 108.5%;
    position: absolute;
    top: -349%;
    left: 32.1%;
}

#other-inv-weight-value{
    color: rgb(209 213 219 / 1);
    font-size: 1vh;
    line-height: 108.5%;
    position: absolute;
    top: -623%;
    left: 48.3%;

}


#player-inv-progressbar {
    height: 5px;
    top: 25.5%;
    left: 14.2%;
    width: 27.8%;
    position: absolute;
    transition: width 0.2s;
    background-color: #2d2d2d;
}

.other-inv-info {
    position: absolute;
    top: 23%;
    right: 14.3%;
    width: 28%;
    height: fit-content;
    float: left;
    font-weight: bolder;
    color: white;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: 5px;
    justify-content: space-between;
    align-items: center;
}

#other-inv-progressbar {
    position: absolute;
    top: 25.5%;
    width: 27.8%;
    right: 14.4%;
    height: 5px;
    transition: width 0.2s;
    background-color: #2d2d2d;
}

.ui-progressbar.ui-widget.ui-widget-content {
    border: none;
}

.ui-progressbar.ui-widget-content {
    border: none;
}

.ui-progressbar .ui-widget-header {
    border: none;
}

.ui-progressbar-value {
    transition: width 0.5s;
    -webkit-transition: width 0.5s;
}

.ui-progressbar .ui-progressbar-value {
    background-color: green;
}

.ui-progressbar-medium .ui-progressbar-value {
    background-color: yellow;
}

.ui-progressbar-high .ui-progressbar-value {
    background-color: red;
}

/* .player-inventory {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-content: flex-start;
    position: absolute;
    top: 20%;
    left: 36.5%;
    width: 29%;
    height: 30%;
    float: left;
    overflow: hidden;
} */

.five-inventory{
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-content: flex-start;
    position: absolute;
    top: 20%;
    left: 5.5%;
    width: 6%;
    height: 56%;
    float: left;
    overflow: hidden;
}

.five-inventory .item-slot{
    height: 15%;
    width: 77%;
}


.alt-inventory{
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-content: flex-start;
    position: absolute;
    top: 131%;
    left: 0%;
    width: 90%;
    height: 97%;
    float: left;
    overflow-y: auto;
}

.alt-inventory .item-slot{
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0.5px;
    height: 29.275% !important;
    width: 17.765% !important;
    margin: 0.5vh;
    background: radial-gradient(83% 83% at 50% 50%,rgba(180,180,180,.15),rgba(120,120,120,.05));
}

.alt-alt-inventory{
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-content: flex-start;
    position: absolute;
    left: -34%;
    height: 187%;
    width: 22%;
    top: 0%;
    float: left;
    overflow: hidden;
}

.alt-alt-inventory .item-slot{
    height: 15% !important;
    width: 76% !important;
}


.alt-left-inventory{
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-content: flex-start;
    position: absolute;
    left: -115%;
    height: 187%;
    width: 22%;
    top: 0%;
    float: left;
    overflow: hidden;
}

.alt-left-inventory .item-slot{
    height: 15% !important;
    width: 76% !important;
}

.player-inventory{
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-content: flex-start;
    position: absolute;
    width: 28.7%;
    left: 37%;
    top: 20%;
    height: 30%;
    float: left;
    overflow:inherit;
}

.player-inventory .item-slot{
    height: 27.975%;
    width: 16.365%;
}


.altinv{
    top: 60%;
    overflow: hidden scroll;
}


.other-inventory {
    /* display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-content: flex-start;
    position: absolute;
    top: 20%;
    left: 67%;
    width: 26.42%;
    height: 28%;
    float: left;
    justify-content: flex-start;
    overflow: hidden scroll; */
    display: flex; 
    display: block;
    position: absolute;
    top: 20%;
    left: 67%;
    width: 26.42%;
    height: 28%;
    float: left;
    overflow: hidden scroll;
}

.other-inventory .item-slot{
    height: 29.975%;
    width: 17.365%;
}

.item-slot {
    position: relative;
    display: block;
    float: left;
    float: lfet;
    width: calc(20% - 1.5px);
    margin: 0.5px;
    margin: 0.5vh;
    background: radial-gradient(83% 83% at 50% 50%,rgba(180,180,180,.15),rgba(120,120,120,.05));

    /* position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: calc(20% - 1.5px);
    margin: 0.5px;
    height: 10vh;
    margin: 0.5vh;
    background: radial-gradient(83% 83% at 50% 50%,rgba(180,180,180,.15),rgba(120,120,120,.05)); */
}

.item-slot:hover{
    border: 0.1vh solid #0ceebf;
    background: radial-gradient(83% 83% at 50% 50%, rgba(180, 180, 180, 0.25), rgba(120, 120, 120, 0.15));
}

/* .item-slot:hover .item-hover{
    display: block;
} */

.item-slot .item-hover{
    display: block;
    width: 200%;
    height: 80%;
    position: absolute;
    border-radius: 0.1vw;
    background: radial-gradient(83% 83% at 50% 50%, rgb(85, 84, 84), rgb(54, 54, 54));
    left: 80%;
    top: 75%;
    z-index: 5;
}

.hover-desc{
    width: 100%;
    height: 150%;
    position: absolute;
    top: -150%;
    left: 0%;
    display: none;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    font-size: 1vh;
    color: white;
    border-radius: 0.1vw;
    background: radial-gradient(83% 83% at 50% 50%, rgb(85, 84, 84), rgb(54, 54, 54));
}

.hover-desc .hover-desc-text{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 70%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    font-size: 1vh;
    color: white;
}

.header{
    width: 100%;
    height: 20%;
    left: 5%;
    top: 10%;
    position: absolute;
    display: flex;
    font-size: 1vh;
    color: white;
}

.weight-bg{
    width: 23%;
    height: 32%;
    top: 54%;
    left: 30%;
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 1px;
}

.weight-bg .weight{
    position: absolute;
    top: 50%;
    left: 20%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 1.1vh;
}

.weight-bg .fa-weight-hanging{
    position: absolute;
    top: 0%;
    left: 16%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.65vh;
}

 .weight-bg .weight-text{
    position: absolute;
    top: 50%;
    left: 63%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.9vh;
}

 .durability-bg{
    width: 23%;
    height: 32%;
    top: 54%;
    left: 5%;
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 1px;
}

.durability-bg .durability-text{
    position: absolute;
    top: 50%;
    left: 65%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 1.1vh;
}

 .durability-bg .fa-wrench{
    position: absolute;
    top: 50%;
    left: 20%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 1.1vh;
}

.item-slot-img {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    z-index: 1;
}

.item-slot-img img {
    width: 50%;
    height: auto;
}


.emptyicon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    z-index: 5;
    left: 20% !important;
    top: 22% !important;
    font-size: 5vh !important;
}



.item-slot-key {
    display: none;
    font-weight: bolder;
    color: rgb(0 248 185);
    position: absolute;
    left: 0%;
    top: 0%;
    font-size: 0.35vw;
    width: 13px;
    height: 13px;
    background-color: #00f8b91a;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    /* box-shadow: inset 0 -2px 0 rgba(0, 0, 0, 0.1), 0 2px 2px rgba(0, 0, 0, 0.2); */
    /* background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1)); */
}

.item-slot-key p {
    margin-top: 124%;
} 

.item-slot-amount {
    display: none;
    font-size: var(--font-size);
    color: white;
    position: absolute;
    top: 5px;
    right: 5px;
    width: 100%;
    height: 20px;
    display: flex;
    justify-content: flex-end;
}

/* .item-slot-amount p {
    display: none;
} */

.zort-slot-label {
    display: flex;
    text-align: center;
    align-items: center;
    justify-content: center;
    position: absolute;
    white-space: nowrap;
    bottom: 0;
    height: 0.3vh;
    width: 100%;
    color: white;
    left: 0;
    border-bottom-left-radius: 0.2vh;
    border-bottom-right-radius: 0.2vh;
      
}

.zort-slot-label .durability-color{
    border-bottom-left-radius: 0.2vh;
    position: absolute;
    bottom: 0;
    height: 0.3vh;
    width: 30%;
    left: 0;
    background-color: #00f8b9;
}

.zort-slot-label p {
    margin: 0;
    font-size: 0.7vh;
    text-transform: uppercase;
    color: white;
    font-weight: bolder;
}


.item-slot-label {
    position: absolute;
    white-space: nowrap;
    bottom: 0;
    height: 0.3vh;
    width: 100%;
    color: white;
    left: 0;
    border-bottom-left-radius: 0.2vh;
    border-bottom-right-radius: 0.2vh;
      
}

.item-slot-label .durability-color{
    border-bottom-left-radius: 0.2vh;
    position: absolute;
    bottom: 0;
    height: 0.3vh;
    width: 30%;
    left: 0;
    background-color: #00f8b9;
}

.item-slot-label p {
    display: none;
    margin: 0;
    font-size: var(--font-size);
    text-transform: uppercase;
    color: white;
    font-weight: bolder;
}

.item-slot-quality {
    position: absolute;
    bottom: 0vh;
    width: 100%;
    height: 4px;
    background-color: #2d2d2d;
}

.item-slot-quality-bar {
    height: 4px;
}


.item-slot-quality-bar p{
    display: none;
}

.inv-options {
    position: absolute;
    width: 10%;
    top: 45%;
    left: 50%;
    z-index: 6;
    transform: translate(-50%, -50%);
}

#item-split{
    display: none;
    height: 5.7vw;
    width: 100%;
    height: 6.5vw;
    margin-top: 1vw;
    border-radius: 4px;
    font-size: 1.2vh;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-weight: bolder;
    color: white;
    cursor: pointer;
    transition: 0.2s linear;
    background: radial-gradient(83% 83% at 50% 50%, rgb(85, 84, 84,0.9), rgb(54, 54, 54,0.3));
}

.split-button{
    position: absolute;
    top: 74%;
    left: 12%;
    display: flex;
    font-weight: lighter;
    align-items: center;
    justify-content: center;
    font-size: 1.2vh;
    color: #ffffff;
    border-radius: 4px;
    padding: 1.3vh 5.7vh;
    width: 78%;
    height: 16%;
    background-color: rgb(12, 238, 191 ,0.1);
    transition: 0.2s linear;
}

.split-button:hover{
    background-color:rgb(12, 238, 191);
}

.inv-option-item .shortcut{
    position: absolute;
    top: 24.5%;
    left: 6.5%;
    display: flex;
    font-weight: bold;
    align-items: center;
    justify-content: center;
    font-size: 1.9vh;
    color: rgb(12, 238, 191);
    border-radius: 4px;
}

.inv-option-item .inv-option-item-text{
    position: absolute;
    top: 25%;
    font-weight: 400;
    left: 25%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    border-radius: 4px;

}

.amount-text{
    position: absolute;
    top: 30%;
    left: 9.5%;
    display: flex;
    font-weight: lighter;
    align-items: center;
    justify-content: center;
    font-size: 1.4vh;
    color: white;
    border-radius: 4px;
}

.amoun-value-input{
    position: absolute;
    top: 29%;
    left: 59%;
    display: flex;
    font-weight: bold;
    align-items: center;
    justify-content: center;
    font-size: 1.4vh;
    font-weight: lighter;
    color: white;
    border-radius: 4px;
    border:none;
    background: transparent;
    width: 6.5vh;
    height: 2.5vh;
    padding: 0.5vh;
    text-align: center;
}


.firstRange[type="range"] {
    left: 10%;
    width: 80%;
    height: 6%;
    border: 0.1vh solid gray;
    -webkit-appearance: none;
    /* margin-right: 15px; */
    top: 57%;
    position: absolute;
    background:  transparent;
    border-radius: 0.2vw;
    background-image: linear-gradient(#0ceebf, #0ceebf);
    background-size: 70% 100%;
    background-repeat: no-repeat;
  }
  
  .firstRange[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 0.7vw;
    width: 0.7vw;
    left: 5%;
    border-radius: 100%;
    background: #353535;
    cursor: ew-resize;
    transition: background .3s ease-in-out;
  }
  
  .firstRange[type="range"]::-webkit-slider-thumb:hover {
    background: #0ceebf;
  }
  
  .firstRange[type="range"]::-moz-range-thumb:hover {
    background: #0ceebf;
  }
  
  .firstRange[type="range"]::-ms-thumb:hover {
    background: #0ceebf;
  }
  
  .firstRange[type=range]::-webkit-slider-runnable-track  {
    -webkit-appearance: none;
    box-shadow: none;
    border: none;
    background: transparent;
  }
  
  .firstRange[type=range]::-moz-range-track {
    -webkit-appearance: none;
    box-shadow: none;
    border: none;
    background: transparent;
  }
  
  .firstRange[type="range"]::-ms-track {
    -webkit-appearance: none;
    box-shadow: none;
    border: none;
    background: transparent;
  }

.inv-options-list {
    top: 2.3vh;
    width: 100%;
    display: none;
    flex-direction: column;
    top: 100%;
    position: absolute;
}

.inv-option-item {
    top: 1.28vh;
    position: relative;
    width: 100%;
    height: 2.1vw;
    margin-top: 0.2vw;
    border-radius: 4px;
    display: flex;
    font-size: 1.2vh;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-weight: bolder;
    color: white;
    cursor: pointer;
    transition: 0.2s linear;
    background: radial-gradient(83% 83% at 50% 50%, rgb(85, 84, 84,0.9), rgb(54, 54, 54,0.3));
}

.inv-option-item:hover{
    background: #21ac8b;
}

.inv-option-item:hover .shortcut{
    color: white;
}

.inv-option-item:hover i{
    color: white;
}

.inv-option-item .icon{
    width: 1.5vw;
    height: 1.5vw;
    left: 5%;
    top: 13%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5vh;
    color: #0ceebf;
    border-radius: 4px;
    position: absolute;
}

.inv-option-item p{
    position: absolute;
    left: 25%;
}

#item-use p,
#item-give p,
#inv-close p,
#weapon-attachments p {
    margin: 0;
}

.ply-iteminfo-container {
    z-index: 555;
    display: none;
    justify-content: center;
    position: absolute;
    top: 67%;
    left: 50%;
    width: 10vw;
    height: 7%;
    transform: translate(-50%, -50%);
    align-items: center;
    text-align: center;
    background: radial-gradient(83% 83% at 50% 50%, rgb(85, 84, 84,0.9), rgb(54, 54, 54,0.3));
    border-radius: 3px;
}

.ply-iteminfo-container .item-info-title{
    width: 100%;
    height: 20%;
    left: 5%;
    top: 15%;
    position: absolute;
    display: flex;
    font-size: 1.2vh;
    color: white;
}

.item-info-description {
    z-index: 5555;
    top: 105%;
    position: absolute;
    width: 10vw; 
    padding: 10px; 
    overflow: hidden; 
    text-overflow: ellipsis;
    white-space: wrap;
    font-size: 1vh;
    display: flex;
    color: white;
    justify-content: left;
    align-items: left;
    text-align: left;
    border-radius: 3px;
    background: radial-gradient(83% 83% at 50% 50%, rgb(85, 84, 84,0.9), rgb(54, 54, 54,0.3));
}

.content {
    height: auto; 
}

.weapon-attachments-container {
    width: 100%;
    height: 100%;
    display: none;
}

.weapon-attachments-container-title {
    position: fixed;
    top: 30%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3vh;
}

.weapon-attachments-container-description {
    position: fixed;
    top: 35%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2vh;
}

.weapon-attachments-container-details {
    position: fixed;
    top: 65%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.weapon-attachments-container-detail-durability {
    height: 1.5vh;
    width: 10vh;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 1px;
}

.weapon-attachments-container-detail-durability-total {
    height: 100%;
    width: 50%;
    background-color: rgb(0 248 185);
}

.weapon-attachments-container-image {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.weapon-attachments-title {
    position: fixed;
    top: 85%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.weapon-attachments {
    display: flex;
    justify-content: center;
    position: fixed;
    bottom: 0;
    left: 50%;
    width: 30vw;
    transform: translateX(-50%);
    padding-bottom: 3vh;
}

.weapon-attachments-remove {
    position: fixed;
    top: 80%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--background-color);
}

.weapon-attachments-remove > i {
    color: var(--text-color);
    font-size: 2vh;
    padding: 1vh;
}

.weapon-attachments-remove:hover {
    background-color: var(--hover-color);
}

.weapon-attachments-back {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 10vh;
    height: 6vh;
    margin: 5vh;
    color: var(--text-color);
    text-align: center;
    line-height: 6vh;
    background-color: var(--background-color);
    transition: 0.05s linear;
}

.weapon-attachments-back:hover {
    background-color: var(--hover-color);
}

.combine-option-container {
    display: none;
    position: absolute;
    width: 10%;
    top: 58%;
    left: 50%;
    margin: 0;
    transform: translate(-50%, -50%);
}

.CombineItem p,
.SwitchItem p {
    margin: 0;
}

.itemboxes-container {
    display: flex;
    justify-content: center;
    position: fixed;
    bottom: 0;
    left: 50%;
    width: 30vw;
    transform: translateX(-50%);
    padding-bottom: 3vh;
}

.requiredItem-container {
    display: flex;
    justify-content: center;
    position: fixed;
    bottom: 0;
    left: 50%;
    width: 30vw;
    transform: translateX(-50%);
    padding-bottom: 3vh;
}

.z-hotbar-inventory {
    display: none;
    align-items: center;
    justify-content: center;
    position: fixed;
    bottom: 0;
    left: 50%;
    top: 87%;
    width: 30%;
    transform: translateX(-50%);
    padding-bottom: 3vh;
    text-align: center;
}

.z-hotbar-inventory .item-slot{
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;    
}

.z-hotbar-inventory .zort-slot-label{
    top: 73%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.z-hotbar-inventory .item-slot-img{
    display: flex;
    align-items: center;
    justify-content: center;   
    position: absolute; 
    top: -5% !important;
    left: 0% !important;
}

.z-hotbar-inventory .item-slot-img img{
    width: 50%;
    top: 44%;
    position: absolute;
    height: auto;
}


.notify{
    background: linear-gradient(to right, rgba(42,45,58,1), rgba(0,0,255,0));
    color: white;
    width: 10%;
    height: 7%;
    position: absolute;
    z-index: 1;
    top: 62%;
    left: -20%;
    display: none;
}

.notify .notify-svg{
    height: 80% !important;
    width: 80% !important;
    color: #0ceebf;
    border-radius: 0.2vh;
    position: absolute;
    z-index: 5;
    display: flex;
    align-items: start;
    justify-content: start;
    text-align: start;
    top: 10%;
    left: -20%;
}

.notify .item-name{
    position: absolute;
    top: 20%;
    left: 40%;
    font-size: 1.2vh;
    color: white;
    font-weight: 400;
}

.notify .item-type{
    position: absolute;
    top: 50%;
    left: 40%;
    font-size: 0.9vh;
    padding: 0.1vh 0.4vh; 
    background-color: #173d54;
    color: #298bc7;
    font-weight: bold;
    font-weight: 400;
}

.notify .item-count{
    position: absolute;
    top: 50%;
    left: 55.5%;
    font-size: 0.9vh;
    padding: 0.1vh 0.4vh; 
    background-color: #40444a;
    color: white;
    font-weight: bold;
    font-weight: 400;
}

.notify .item-img img{
    z-index: 5;
    height: 70% !important;
    width: 25% !important;
    color: #0ceebf;
    border-radius: 0.2vh;
    position: absolute;
    top: 16%;
    left: 8%;
    z-index: 15;
}







.char{
    position: absolute;
    top: 18%;
    left: 10%;
    width: 20%;
    height: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 5;
}


.character .wound {
    transition: all .2s ease-in-out;
    cursor: pointer;
}

.character {
    position: absolute;
    width: calc(0.092592592vh * 267.203);
    height: calc(0.092592592vh * 507);
    background-image: url(../images/character-769895e5.png);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-right: calc(0.092592592vh * 14);
    left: 10%;
    /* top: 23%; */
}

.status{
    position:absolute;
    display:flex;
    flex-direction:column;
    justify-content:flex-start;
    align-items:flex-start;
    pointer-events:none;
    color:#fff;
    text-align:center;
    font-size:calc(.092592592vh * 12);
    font-style:normal;
    font-weight:400;
    line-height:108.5%
}
.status.head{
    left: 55%;
    position: absolute;
    top: 5%;
}
.status.body{
    position: absolute;
    top: 35%;
    left: 47%;
}

.status.leftArm{
    position: absolute;
    top: 19%;
    left: 63%;
}

.status.rightArm{
    position: absolute;
    top: 28%;
    left: 6%;
}
.status.leftLeg{
    position: absolute;
    top: 84%;
    left: 56%;
}
.status.rightLeg{
    position: absolute;
    top: 84%;
    left: 11%;
}

.isRight{
    transform:scaleX(-1) translate(-50%)
}

.status .healthBar{
    width:calc(.092592592vh * 88);
    height:calc(.092592592vh * 10);
    flex-shrink:0;
    border-radius:calc(.092592592vh * 4);
    background:radial-gradient(83.13% 83.13% at 50% 50%,rgba(255,255,255,.15) 0%,rgba(255,255,255,0) 100%);
    display:flex;
}
.status .healthBar .bar{
    margin-top: 4%;
    margin-left: 6%;
    width:89%;
    height:calc(.092592592vh * 2);
    flex-shrink:0;
    border-radius:calc(.092592592vh * 1);
    background:rgba(255,255,255,.25);
    overflow:hidden;
    display:flex;
    flex-direction:row;
    justify-content:flex-start;
    align-items:center
}
.status .healthBar .bar .progress{
    width:20%;
    height:100%;
    flex-shrink:0;
    border-radius:calc(.092592592vh * 1);
    background:#F86969
}


.player-inv-info {
    position: absolute;
    top: 11.5%;
    left: 28.5%;
    width: 35%;
    height: 10%;
    float: left;
    font-weight: bolder;
    color: white;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: 5px;
    justify-content: space-between;
    align-items: center;
}

.altinfo{
    top: 52%;
}

.rightinfo{
    left: 60%;
}

.leftinfo{
    left: -4%;

}

.item-box-list{
    position: absolute;
    top: 50%;
    left: 73%;
    width: 100%;
    height: 30%;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 5;
}
.item-box-list .filter-item{
    float: left;
    position: relative;
    width: 5.5%;
    height: 70%;
}

.item-box-list .filter-item:hover .filter-text{
    display: flex;
}

.item-box-list .filter-item .filter-text{
    position: absolute;
    top: -120%;
    left: -0.5%;
    font-size: 0.8vh;
    background-color: #717475;
    font-weight: lighter;
    /* width: 5.5%;
    height: 70%; */
    padding: 0.53vh 0.8vh;
    display: none;
    text-align: center;
    justify-content: center;
    align-items: center;
    border-radius: 0.1vh;
}


.item-box-list svg{
    height: 1.55vw !important;
    width: 1.5vw !important;
    background-color: rgba(0, 248, 185, 0.1);
    padding: 0.5vh;
    margin: 0.1vh;
    color: #0ceebf;
    filter: brightness(0.8);
    border-radius: 0.2vh;
}



.invsearch-input{
    position: absolute;
    top: 20%;
    right: 1.6%;
    width: 25%;
    height: 6%;
    background-color: transparent;
    border: 0.1vh solid rgb(90, 90, 90);
    border-radius: 0vh;
    color: white;
    padding: 1.2vh 2.5vh;
    font-size: 1.5vh;
    z-index: 5;
}



.invsearch-input:focus{
    border: 0.1vh solid white;
    border-radius: 0.4vh;
}

.svg-search{
    position: absolute;
    top: 25%;
    right: 23%;
    color: gray;
    font-size: 1.2vh;
    width: 0.8vw;
    height: 0.8vw;
}

.fa-search{
    position: absolute;
    top: 28%;
    right: 23%;
    color: gray;
    font-size: 1.2vh;
}

.invsearch-input::placeholder {
    position: absolute;
    left: 20%;
    top: 15%;
    font-size: 1.2vh;
    color: rgb(102, 101, 101);
}

.polygon-img{
    position: absolute;
    top: 15%;
    right: 0%;
    left: 5%;
    bottom: 0%;
    z-index: 2;
    width: 45%;
    height: 45%;
}

.portrait-icon{
    color: #0ceebf;
    font-size: 1.85vh;
    position: absolute;
    top: 37.5%;
    right: 0%;
    left: 26.4%;
    bottom: 0%;
    z-index: 2;
    width: 5%;
    height: 40%;
}


.material-symbols-outlined {
  font-variation-settings:'FILL' 0,'wght' 400,'GRAD' 0,'opsz' 24;
  position: absolute;
  top : -5%;
  left: -12%;
}


.left-text-box{
    position: absolute;
    top: 72%;
    left: 5.5%;
    width: 28.441%;
    height: 26%;
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: start;
    z-index: 5;
}


.left-text-box .blue-box{
    position: absolute;
    top: 0%;
    left: 0%;
    width: 1.5%;
    height: 3%;
    box-shadow:  0px 0px 12px rgba(106, 154, 254, .55);
    background-color: rgb(0 248 185);
    border-radius: 0.1vh;
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: start;
}

.left-text-box .pers-text{
    position: absolute;
    top: -3%;
    left: 3.5%;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: start;
    font-size: 1.5vh;
    color: white;
    z-index: 5;
}

.left-text-box .text-alt-box{
    position: absolute;
    top: 10%;
    left: 0%;
    width: 40%;
    height: 20%;
}

.left-text-box .line{
    background-color: rgb(106, 154, 254); 
    box-shadow: rgb(106, 154, 254) 0px 0px 3px;
    border-radius: 0.1vh;
    width: 15%;
    height: 0.2vh;
    transform: rotate(90deg);
    position: absolute;
    top: 50%;
    left: 12%;
}

.left-text-box .text-head{
    position: absolute;
    top: 19%;
    left: 25%;
    width: 100%;
    font-size: 1vh;
    color: rgb(156 163 175);
    z-index: 5;
}

.left-text-box .text-alt{
    position: absolute;
    top: 45%;
    left: 25%;
    width: 100%;
    font-size: 1.2vh;
    color:white;
    z-index: 5;
}


.left-item-container{
    position: absolute;
    top: 20%;
    left: 5%;
    width: 9.441%;
    height: 49.94%;
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: start;
    z-index: 5;
}

.left-item-container .item-slot{
    width: 50%;
    margin: 0.45vw;
    height: 17%;
    border-radius: 4px;
    background: radial-gradient(83% 83% at 50% 50%,rgba(180,180,180,.15),rgba(120,120,120,.05));
}

.right-item-container{
    position: absolute;
    top: 20%;
    left: 28%;
    width: 9.441%;
    height: 49.94%;
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: start;
    z-index: 5;
}

.right-item-container .item-slot{
    width: 50%;
    margin: 0.45vw;
    height: 17%;
    border-radius: 4px;
    background: radial-gradient(83% 83% at 50% 50%,rgba(180,180,180,.15),rgba(120,120,120,.05));
}


.place-box{
    display: none;
    position: absolute;
    top: 92%;
    left: 43%;
    width: 15%;
    height: 15%;
    flex-direction: column;
    align-items: start;
    justify-content: start;
    z-index: 5;
}


.place-box .place-item{
    width: 50%;
    height: 17%;
    border-radius: 4px;
    position: relative;
    margin-top: 2%;
}

.place-box .place-item .place-key{
    position: absolute;
    top: 0%;
    left: 0%;
    width: 6%;
    height: 16%;
    padding: 1.2vh;
    border-radius: 0.3vh;
    box-shadow: 0px 0px 12px rgba(106, 154, 254, .55);
    border: 0.1vh solid #0ceebf;
    color: rgb(43 193 167);
    background-color: rgb(27 62 63);
    display: flex;
    flex-direction: column;
    text-align: center;
    align-items: center;
    justify-content: center;
    opacity: 0.9;
    animation: animkey 1s forwards !important ; 
}

@keyframes animkey {
    0% {
        top: 50%;
    }
    50%{
        top: 5%;
    }
    100% {
        top: 0%;
    }
}

.place-item:nth-child(2) .place-key {
    width: 25%; /* Örneğin, genişliği artırmak için yeni bir değer belirleyin */
}


.place-box .place-item .place-mini-box{
    position: absolute;
    top: 12%;
    left: 5%;
    width: 2.5%;
    height: 13.5%;
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: start;
    box-shadow: 0px 0px 12px #0ceebf;
    background-color: #0ceebf;
    z-index: 20;
}



@keyframes anim {
    0% {
        left: 50%;
    }
    100% {
        left: 22%;
    }
}

@keyframes animz {
    0% {
        left: 50%;
    }
    100% {
        left: 28%;
    }
}


.ab{
    animation: animz 1s forwards !important ; 
}
  

.place-box .place-item .place-text{
    position: absolute;
    top: 0%;
    left: 22%;
    width: 90%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 1.5vh;
    color: white;
    z-index: 5;
    border-radius: 0.3vh;
    background-color: rgb(36 ,36 ,49,0.9);
    animation: anim 1s forwards ; 
}

.char-info{
    display: none;
    position: absolute;
    top: 20%;
    left: 23%;
    width: 17%;
    height: 18%;
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: start;
    z-index: 15;
    filter: drop-shadow(rgb(112 112 112 / 30%) 0rem 0rem 12px);
    background-color: rgb(112 112 112 / 30%);
}


.char-info .char-header{
    position: absolute;
    top: 6%;
    left: 5%;
    width: 100%;
    height: 20%;
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: start;
    font-size: 1.5vh;
    color: white;
    z-index: 5;
    border-radius: 0.3vh;
}

.char-info .char-list-box{
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: start;
    position: absolute;
    top: 17%;
    left: 5%;
    z-index: 5;
    width: 90%;
    height: 80%;

}

.char-info .char-list-box .char-list{
    width: 100%;
    height: 18%;
    display: flex;
    flex-direction: row;
    align-items: start;
    justify-content: start;
    font-size: 1.5vh;
    color: white;
    z-index: 5;
    border-radius: 0.2vh;
    background-color: #686a6b;
    margin-top: 2%;
}

.char-info .char-list-box .char-list .info-name{
    left: 3%;
    margin-top: 0.5%;
    font-weight: 500;
    position: absolute;
}

.char-info .char-list-box .char-list .info-count{
    right: 0;
    font-weight: 500;
    position: absolute;
    width: 40%;
    height: 18%;
    /* padding: 0.65%; */
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    /* height: 18%; */
    background-color: #785858;
}

.alt-stash-inventory{
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-content: flex-start;
    position: absolute;
    height: 100%;
    width: 100%;
    top: 131%;
    left: 105%;
    float: left;
    overflow: inherit;
}


.alt-stash-inventory .item-slot{
    height: 29% !important;
    width: 16% !important;
}

.header-bg{
    display: none;
    position: absolute;
    top: 53%;
    left: 67.3%;
    width: 26.2%;
    height: 6%;
    flex-direction: column;
    align-items: start;
    justify-content: start;
    z-index: 5;
    background: radial-gradient(83% 83% at 50% 50%,rgba(180,180,180,.15),rgba(120,120,120,.05));
    border-radius: 0.3vh;
}

.header-bg .header-text{
    color: #0ceebf;
    font-size: 1.5vh;
    font-weight: bold;
    position: absolute;
    left: 15%;
    top: 25%;
    right: 30%;
}

.header-bg .kg-text{
    color: rgb(209 213 219 / 1);
    font-size: 1vh;
    line-height: 108.5%;
    position: absolute;
    left: 15%;
    top: 60%;
    right: 30%;
}

.header-bg .header-icon{
    color: #0ceebf;
    position: absolute;
    left: 12%;
    top: 36%;
    right: 30%;
}

.header-bg .polygon-img{
    position: absolute;
    top: 7%;
    right: 0%;
    left: -27.5%;
    bottom: 0%;
    z-index: 2;
    width: 70%;
    height: 70%;
}

.header-bg .left-background{
    position: absolute;
    top: 10%;
    /* left: 0%; */
    right: 3%;
    width: 6%;
    height: 80%;
    background-color: #203b3a;
    border-radius: 0.3vh;
    cursor: pointer;
}

.header-bg .left-background:hover{
    background-color: #0ceebf;
}


.header-bg .left-background span{
    position: absolute;
    top: 20%;
    left: 0%;
    transform: rotate(270deg);
    font-size: 3vh;
    color: #0ceebf;
    font-weight: lighter;
}

.health-system{
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 5;
    display: none;
    left: -50%;
}

 .bg{
    position: absolute;
    top: 0%;
    left: 0%;
    width: 100%;
    height: 100%;
    z-index: 5;
}

.health-system .ecg{
    position: absolute;
    top: 5%;
    left: 2%;
    font-size: 3vh;
    z-index: 5;
    color: #0ceebf;
    text-shadow: 0px 0px 12px #0ceebf;
}

.health-system .header{
    position: absolute;
    top: 4.5%;
    left: 4%;
    font-size: 1.7vh;
    font-weight: bold;
    z-index: 5;
    color: #0ceebf;
    text-shadow: 0px 0px 12px #0ceebf;
}

.health-system .desc{
    position: absolute;
    top: 6.5%;
    left: 4.1%;
    font-size: 1.2vh;
    font-weight: 400;
    z-index: 5;
    color: rgb(209 213 219 / 1);
}

.health-system .health-hr{
    position: absolute;
    top: 5%;
    left: 22%;
    width: 1.2%;
    height: 0.1vh;
    transform: rotate(110deg);
    background-color: #0ceebf;
    z-index: 5;
    box-shadow: 0px 0px 12px #0ceebf;
}

.health-system .first-circle{
    position: absolute;
    top: 10%;
    left: 2.3%;
    width: 19%;
    height: 50%;
    z-index: 5;
    border-radius: 0.2vh;
    border: 0.1vh solid rgb(146, 148, 150);
}

.health-system .first-circle .body-header{
    position: absolute;
    top: 3%;
    left: 5%;
    font-size: 1.2vh;
    font-weight: bold;
    z-index: 5;
    color: white;
}

.health-system .first-circle .body-desc{
    position: absolute;
    top: 6%;
    left: 5%;
    font-size: 1vh;
    font-weight: 400;
    z-index: 5;
    color: rgb(209 213 219 / 1);
}

.health-system .character{
    position: absolute;
    top: -20%;
    left: 66%;
    /* width: 20vh;
    height: 40vh; */
    z-index: 5;
    border-radius: 0.2vh;
}

.health-system .two-circle{
    position: absolute;
    top: 62%;
    left: 2.3%;
    width: 19%;
    height: 10%;
    z-index: 5;
    border-radius: 0.2vh;
    border: 0.1vh solid rgb(146, 148, 150);
}

.health-system .two-circle .body-header{
    position: absolute;
    top: 3%;
    left: 5%;
    font-size: 1.2vh;
    font-weight: bold;
    z-index: 5;
    color: white;
}

.health-system .two-circle .body-desc{
    position: absolute;
    top: 18%;
    left: 5%;
    font-size: 1vh;
    font-weight: 400;
    z-index: 5;
    color: rgb(209 213 219 / 1);
}

.health-system .injury-item{
    position: absolute;
    top: 50%;
    left: 13%;
    width: 80%;
    height: 30%;
    z-index: 5;
    border-radius: 0.3vh;
    background: radial-gradient(83% 83% at 50% 50%,rgb(102 110 109 / 65%),rgba(120,120,120,.05)); 
}

.health-system .injury-item .number{
    position: absolute;
    top: 23%;
    left: 92%;
    font-size: 1vh;
    font-weight: lighter;
    z-index: 5;
    color: rgb(75, 73, 73);
    width: 5%;
    height: 55%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #0ceebf;
    border-radius: 0.3vh;
    box-shadow: 0px 0px 5px #0ceebf;
}

.health-system .injury-item .text{
    position: absolute;
    top: 17%;
    left: 3%;
    font-size: 1.2vh;
    font-weight: bold;
    z-index: 5;
    color: #0ceebf;
}

.health-system .syringe{
    position: absolute;
    top: 50%;
    left: 5%;
    width: 7%;
    height: 30%;
    z-index: 5;
    border-radius: 0.3vh;
    background-color: #277e71;
    color: #0ceebf;
    border: 0.1vh solid #0ceebf;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 1.8vh;
}



.health-system .three-circle{
    position: absolute;
    top: 74%;
    left: 2.3%;
    width: 19%;
    height: 24%;
    z-index: 5;
    border-radius: 0.2vh;
    border: 0.1vh solid rgb(146, 148, 150);
}


.health-system .three-circle .body-header{
    position: absolute;
    top: 3%;
    left: 5%;
    font-size: 1.2vh;
    font-weight: bold;
    z-index: 5;
    color: white;
}

.health-system .three-circle .body-desc{
    position: absolute;
    top: 10%;
    left: 5%;
    font-size: 1vh;
    font-weight: 400;
    z-index: 5;
    color: rgb(209 213 219 / 1);
}

.health-system .three-circle .three-list{
    position: absolute;
    top: 20%;
    left: 2%;
    width: 95%;
    height: 70%;
    z-index: 5;
    border-radius: 0.3vh;
    overflow: hidden scroll;
}

.three-list::-webkit-scrollbar {
    display: none;
}

.health-system .three-circle .three-list .three-item{
    position: relative;
    top: 5%;
    left: 13%;
    width: 85%;
    height: 20%;
    z-index: 5;
    border-radius: 0.5vh;
    background: radial-gradient(83% 83% at 50% 50%,rgb(102 110 109 / 65%),rgba(120,120,120,.05)); 
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin-top: 2%;
}

.health-system .three-circle .three-list .three-item .three-text{
    position: absolute;
    top: 20%;
    left: 5%;
    font-size: 1.2vh;
    font-weight: bold;
    z-index: 5;
    color: #0ceebf;
}

.health-system .three-circle .three-list .three-item .three-count{
    position: absolute;
    top: 0%;
    right: 0%;
    font-size: 1.2vh;
    font-weight: bold;
    z-index: 5;
    background: radial-gradient(83% 83% at 50% 50%,rgb(12 238 191 / 65%),rgba(120,120,120,.05));
    width: 35%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #0ceebf;
    border-top-right-radius: 0.5vh;
    border-bottom-right-radius: 0.5vh;
}

.health-system .three-circle .syringe{
    position: absolute;
    top: 0%;
    left: -12%;
    width: 11%;
    height: 100%;
    z-index: 5;
    border-radius: 0.5vh;
    background-color: #277e71;
    color: #0ceebf;
    border: 0.1vh solid #0ceebf;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 1.8vh;
}


.settings-button{
    position: absolute;
    top: 93%;
    left: 5%;
    width: 5%;
    height: 2%;
    z-index: 5;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-weight: bold;
    font-size: 1vh;
    color: #777777;
    border-radius: 0.1vh;
    background-color: #212121;
    cursor: pointer;
}

.help-box{
    display: none;
    background-color: #262626;
    position: absolute;
    top: 42%;
    left: 5%;
    width: 28%;
    height: 45%;
    z-index: 9999;
}

.help-box .help-header{
    position: absolute;
    top: 0%;
    left: 0%;
    width: 100%;
    height: 10%;
    z-index: 5;
    color: #858585;
    font-size: 1.6vh;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    border-top-left-radius: 0.3vh;
    border-top-right-radius: 0.3vh;
}

.help-box .helpclose{
    position: absolute;
    top: 90%;
    left: 0%;
    width: 100%;
    height: 10%;
    z-index: 5;
    color: #858585;
    font-size: 1.6vh;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    border-top-left-radius: 0.3vh;
    border-top-right-radius: 0.3vh;
    cursor: pointer;
}

.help-box .help-hr{
    position: absolute;
    top: 7%;
    left: 0%;
    width: 100%;
    height: 0.001vh;
    z-index: 5;
    background-color: rgb(133, 133, 133,0.4);
}

.help-box ul{
    position: absolute;
    top: 18%;
    left: 4%;
    width: 100%;
    height: 90%;
    z-index: 5;
    color: #929292;
    font-size: 1.8vh;
    font-weight: 400;
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: start;
    text-align: start;
    padding: 1vh;
    overflow: auto;
    line-height: 1.5vh;
}

.help-box ul li{
    margin-top: 1vh;
    border-radius: 5.1vh;
    list-style-position: inside;
}

.help-box .help-list-header{
    position: absolute;
    top: 13%;
    left: 3%;
    z-index: 5;
    color: #929292;
    font-size: 1.8vh;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    border-top-left-radius: 0.3vh;
    border-top-right-radius: 0.3vh;
}

.exit-box{
    display: block;
    border: 0.05vh solid rgb(84, 85, 85,0.4);
    position: absolute;
    top: 3%;
    left: 4.6%;
    width: 5%;
    height: 2.5%;
    z-index: 9999;
    transition: all 0.3s;
}

.exit-box .exit{
    position: absolute;
    top: 0%;
    left: -25%;
    width: 100%;
    height: 100%;
    z-index: 5;
    color: #929292;
    font-size: 1.3vh;
    font-weight: 500;
    cursor: pointer;
    text-align: center;
    justify-content: center;
    align-items: center;
    display: flex;
}

.exit-box .escape{
    position: absolute;
    top: 0%;
    left: 50%;
    width: 50%;
    height: 100%;
    z-index: 5;
    text-align: center;
    justify-content: center;
    align-items: center;
    display: flex;
    background-color: rgb(33 33 32);
    color: #929292;
    font-size: 1.1vh;
    font-weight: 500;
    cursor: pointer;
}

.exit-box:hover {
    transform: scale(1.02);
}