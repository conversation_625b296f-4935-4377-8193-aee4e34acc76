{"manifest": {"name": "hmac-drbg", "version": "1.0.1", "description": "Deterministic random bit generator (hmac)", "main": "lib/hmac-drbg.js", "scripts": {"test": "mocha --reporter=spec test/*-test.js"}, "repository": {"type": "git", "url": "git+ssh://**************/indutny/hmac-drbg.git"}, "keywords": ["hmac", "drbg", "prng"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/indutny/hmac-drbg/issues"}, "homepage": "https://github.com/indutny/hmac-drbg#readme", "devDependencies": {"mocha": "^3.2.0"}, "dependencies": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-hmac-drbg-1.0.1-d2745701025a6c775a6c545793ed502fc0c649a1-integrity\\node_modules\\hmac-drbg\\package.json", "readmeFilename": "README.md", "readme": "# hmac-drbg\n[![Build Status](https://secure.travis-ci.org/indutny/hmac-drbg.svg)](http://travis-ci.org/indutny/hmac-drbg)\n[![NPM version](https://badge.fury.io/js/hmac-drbg.svg)](http://badge.fury.io/js/hmac-drbg)\n\nJS-only implementation of [HMAC DRBG][0].\n\n## Usage\n\n```js\nconst DRBG = require('hmac-drbg');\nconst hash = require('hash.js');\n\nconst d = new DRBG({\n  hash: hash.sha256,\n  entropy: '0123456789abcdef',\n  nonce: '0123456789abcdef',\n  pers: '0123456789abcdef' /* or `null` */\n});\n\nd.generate(32, 'hex');\n```\n\n#### LICENSE\n\nThis software is licensed under the MIT License.\n\nCopyright Fedor Indutny, 2017.\n\nPermission is hereby granted, free of charge, to any person obtaining a\ncopy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to permit\npersons to whom the Software is furnished to do so, subject to the\nfollowing conditions:\n\nThe above copyright notice and this permission notice shall be included\nin all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\nOR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\nNO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\nDAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\nOTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\nUSE OR OTHER DEALINGS IN THE SOFTWARE.\n\n[0]: http://csrc.nist.gov/groups/ST/toolkit/documents/rng/HashBlockCipherDRBG.pdf\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/hmac-drbg/-/hmac-drbg-1.0.1.tgz#d2745701025a6c775a6c545793ed502fc0c649a1", "type": "tarball", "reference": "https://registry.yarnpkg.com/hmac-drbg/-/hmac-drbg-1.0.1.tgz", "hash": "d2745701025a6c775a6c545793ed502fc0c649a1", "integrity": "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=", "registry": "npm", "packageName": "hmac-drbg", "cacheIntegrity": "sha512-Tti3gMqLdZfhOQY1Mzf/AanLiqh1WTiJgEj26ZuYQ9fbkLomzGchCws4FyrSd4VkpBfiNhaE1On+lOz894jvXg== sha1-0nRXAQJabHdabFRXk+1QL8DGSaE="}, "registry": "npm", "hash": "d2745701025a6c775a6c545793ed502fc0c649a1"}