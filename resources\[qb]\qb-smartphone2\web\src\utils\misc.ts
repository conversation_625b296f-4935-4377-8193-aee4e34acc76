/**
 * Will return whether the current environment is in a regular browser
 * and not CEF
 */
export const isEnvBrowser = (): boolean => !(window as any).invokeNative;

/**
 * Format phone number for display
 * @param phoneNumber - Raw phone number string
 * @returns Formatted phone number
 */
export const formatPhoneNumber = (phoneNumber: string): string => {
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  }
  
  if (cleaned.length === 7) {
    return `${cleaned.slice(0, 3)}-${cleaned.slice(3)}`;
  }
  
  return phoneNumber;
};

/**
 * Format time for display
 * @param timestamp - Unix timestamp or Date object
 * @returns Formatted time string
 */
export const formatTime = (timestamp: number | Date): string => {
  const date = typeof timestamp === 'number' ? new Date(timestamp * 1000) : timestamp;
  return date.toLocaleTimeString('cs-CZ', { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
};

/**
 * Format date for display
 * @param timestamp - Unix timestamp or Date object
 * @returns Formatted date string
 */
export const formatDate = (timestamp: number | Date): string => {
  const date = typeof timestamp === 'number' ? new Date(timestamp * 1000) : timestamp;
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  
  if (messageDate.getTime() === today.getTime()) {
    return 'Dnes';
  } else if (messageDate.getTime() === yesterday.getTime()) {
    return 'Včera';
  } else {
    return date.toLocaleDateString('cs-CZ');
  }
};

/**
 * Format relative time (e.g., "2 minutes ago")
 * @param timestamp - Unix timestamp or Date object
 * @returns Relative time string
 */
export const formatRelativeTime = (timestamp: number | Date): string => {
  const date = typeof timestamp === 'number' ? new Date(timestamp * 1000) : timestamp;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return 'Právě teď';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `Před ${minutes} min`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `Před ${hours} h`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `Před ${days} dny`;
  }
};

/**
 * Truncate text to specified length
 * @param text - Text to truncate
 * @param maxLength - Maximum length
 * @returns Truncated text
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength - 3) + '...';
};

/**
 * Generate random ID
 * @param length - Length of the ID
 * @returns Random ID string
 */
export const generateId = (length: number = 8): string => {
  return Math.random().toString(36).substr(2, length);
};

/**
 * Validate phone number
 * @param phoneNumber - Phone number to validate
 * @returns Whether the phone number is valid
 */
export const isValidPhoneNumber = (phoneNumber: string): boolean => {
  const cleaned = phoneNumber.replace(/\D/g, '');
  return cleaned.length >= 7 && cleaned.length <= 15;
};

/**
 * Validate email address
 * @param email - Email to validate
 * @returns Whether the email is valid
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Format currency
 * @param amount - Amount to format
 * @param currency - Currency symbol
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number, currency: string = '$'): string => {
  return `${currency}${amount.toLocaleString('cs-CZ')}`;
};

/**
 * Get battery color based on level
 * @param level - Battery level (0-100)
 * @returns Color string
 */
export const getBatteryColor = (level: number): string => {
  if (level > 50) return '#34C759';
  if (level > 20) return '#FF9500';
  return '#FF3B30';
};

/**
 * Get signal strength bars
 * @param strength - Signal strength (0-100)
 * @returns Number of bars (0-4)
 */
export const getSignalBars = (strength: number): number => {
  if (strength >= 75) return 4;
  if (strength >= 50) return 3;
  if (strength >= 25) return 2;
  if (strength > 0) return 1;
  return 0;
};

/**
 * Debounce function
 * @param func - Function to debounce
 * @param wait - Wait time in milliseconds
 * @returns Debounced function
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Throttle function
 * @param func - Function to throttle
 * @param limit - Time limit in milliseconds
 * @returns Throttled function
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};
