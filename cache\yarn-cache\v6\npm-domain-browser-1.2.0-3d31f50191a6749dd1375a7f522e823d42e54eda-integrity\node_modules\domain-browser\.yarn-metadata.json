{"manifest": {"name": "domain-browser", "version": "1.2.0", "description": "<PERSON>de's domain module for the web browser. This is merely an evented try...catch with the same API as node, nothing more.", "homepage": "https://github.com/bevry/domain-browser", "license": "MIT", "keywords": ["domain", "trycatch", "try", "catch", "node-compat", "ender.js", "component", "component.io", "umd", "amd", "require.js", "browser"], "badges": {"list": ["travis<PERSON>", "npmversion", "npmdownloads", "da<PERSON><PERSON><PERSON>", "<PERSON><PERSON>dm<PERSON>v", "---", "patreon", "opencollective", "gratipay", "flattr", "paypal", "bitcoin", "wishlist", "---", "slackin"], "config": {"patreonUsername": "bevry", "opencollectiveUsername": "bevry", "gratipayUsername": "bevry", "flattrUsername": "bal<PERSON><PERSON>", "paypalURL": "https://bevry.me/paypal", "bitcoinURL": "https://bevry.me/bitcoin", "wishlistURL": "https://bevry.me/wishlist", "slackinURL": "https://slack.bevry.me"}}, "author": {"name": "2013+ Bevry Pty Ltd", "email": "<EMAIL>", "url": "http://bevry.me"}, "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://balupton.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://balupton.com"}, {"name": "<PERSON>", "url": "http://evansolomon.me"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.neocities.org/"}, {"name": "<PERSON>", "email": "guy<PERSON><EMAIL>", "url": "twitter.com/guybedford"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TrySound"}], "bugs": {"url": "https://github.com/bevry/domain-browser/issues"}, "repository": {"type": "git", "url": "https://github.com/bevry/domain-browser.git"}, "engines": {"node": ">=0.4", "npm": ">=1.2"}, "editions": [{"description": "Source + ES5 + Require", "directory": "source", "entry": "index.js", "syntaxes": ["javascript", "es5", "require"]}], "main": "source/index.js", "browser": "source/index.js", "dependencies": {}, "devDependencies": {"assert-helpers": "^4.5.0", "eslint": "^4.16.0", "joe": "^2.0.2", "joe-reporter-console": "^2.0.1", "projectz": "^1.4.0"}, "scripts": {"our:setup": "npm run our:setup:npm", "our:setup:npm": "npm install", "our:clean": "rm -Rf ./docs ./es2015 ./es5 ./out", "our:compile": "echo no need for this project", "our:meta": "npm run our:meta:projectz", "our:meta:projectz": "projectz compile", "our:verify": "npm run our:verify:eslint", "our:verify:eslint": "eslint --fix ./source", "our:test": "npm run our:verify && npm test", "our:release": "npm run our:release:prepare && npm run our:release:check && npm run our:release:tag && npm run our:release:push", "our:release:prepare": "npm run our:clean && npm run our:compile && npm run our:test && npm run our:meta", "our:release:check": "npm run our:release:check:changelog && npm run our:release:check:dirty", "our:release:check:changelog": "cat ./HISTORY.md | grep v$npm_package_version || (echo add a changelog entry for v$npm_package_version && exit -1)", "our:release:check:dirty": "git diff --exit-code", "our:release:tag": "export MESSAGE=$(cat ./HISTORY.md | sed -n \"/## v$npm_package_version/,/##/p\" | sed 's/## //' | awk 'NR>1{print buf}{buf = $0}') && test \"$MESSAGE\" || (echo 'proper changelog entry not found' && exit -1) && git tag v$npm_package_version -am \"$MESSAGE\"", "our:release:push": "git push origin master && git push origin --tags", "test": "node --harmony source/test.js --joe-reporter=console"}, "jspm": {"map": {"source/index.js": {"node": "@node/domain"}}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-domain-browser-1.2.0-3d31f50191a6749dd1375a7f522e823d42e54eda-integrity\\node_modules\\domain-browser\\package.json", "readmeFilename": "README.md", "readme": "<!-- TITLE/ -->\n\n<h1>domain-browser</h1>\n\n<!-- /TITLE -->\n\n\n<!-- BADGES/ -->\n\n<span class=\"badge-travisci\"><a href=\"http://travis-ci.org/bevry/domain-browser\" title=\"Check this project's build status on TravisCI\"><img src=\"https://img.shields.io/travis/bevry/domain-browser/master.svg\" alt=\"Travis CI Build Status\" /></a></span>\n<span class=\"badge-npmversion\"><a href=\"https://npmjs.org/package/domain-browser\" title=\"View this project on NPM\"><img src=\"https://img.shields.io/npm/v/domain-browser.svg\" alt=\"NPM version\" /></a></span>\n<span class=\"badge-npmdownloads\"><a href=\"https://npmjs.org/package/domain-browser\" title=\"View this project on NPM\"><img src=\"https://img.shields.io/npm/dm/domain-browser.svg\" alt=\"NPM downloads\" /></a></span>\n<span class=\"badge-daviddm\"><a href=\"https://david-dm.org/bevry/domain-browser\" title=\"View the status of this project's dependencies on DavidDM\"><img src=\"https://img.shields.io/david/bevry/domain-browser.svg\" alt=\"Dependency Status\" /></a></span>\n<span class=\"badge-daviddmdev\"><a href=\"https://david-dm.org/bevry/domain-browser#info=devDependencies\" title=\"View the status of this project's development dependencies on DavidDM\"><img src=\"https://img.shields.io/david/dev/bevry/domain-browser.svg\" alt=\"Dev Dependency Status\" /></a></span>\n<br class=\"badge-separator\" />\n<span class=\"badge-patreon\"><a href=\"https://patreon.com/bevry\" title=\"Donate to this project using Patreon\"><img src=\"https://img.shields.io/badge/patreon-donate-yellow.svg\" alt=\"Patreon donate button\" /></a></span>\n<span class=\"badge-opencollective\"><a href=\"https://opencollective.com/bevry\" title=\"Donate to this project using Open Collective\"><img src=\"https://img.shields.io/badge/open%20collective-donate-yellow.svg\" alt=\"Open Collective donate button\" /></a></span>\n<span class=\"badge-gratipay\"><a href=\"https://www.gratipay.com/bevry\" title=\"Donate weekly to this project using Gratipay\"><img src=\"https://img.shields.io/badge/gratipay-donate-yellow.svg\" alt=\"Gratipay donate button\" /></a></span>\n<span class=\"badge-flattr\"><a href=\"https://flattr.com/profile/balupton\" title=\"Donate to this project using Flattr\"><img src=\"https://img.shields.io/badge/flattr-donate-yellow.svg\" alt=\"Flattr donate button\" /></a></span>\n<span class=\"badge-paypal\"><a href=\"https://bevry.me/paypal\" title=\"Donate to this project using Paypal\"><img src=\"https://img.shields.io/badge/paypal-donate-yellow.svg\" alt=\"PayPal donate button\" /></a></span>\n<span class=\"badge-bitcoin\"><a href=\"https://bevry.me/bitcoin\" title=\"Donate once-off to this project using Bitcoin\"><img src=\"https://img.shields.io/badge/bitcoin-donate-yellow.svg\" alt=\"Bitcoin donate button\" /></a></span>\n<span class=\"badge-wishlist\"><a href=\"https://bevry.me/wishlist\" title=\"Buy an item on our wishlist for us\"><img src=\"https://img.shields.io/badge/wishlist-donate-yellow.svg\" alt=\"Wishlist browse button\" /></a></span>\n<br class=\"badge-separator\" />\n<span class=\"badge-slackin\"><a href=\"https://slack.bevry.me\" title=\"Join this project's slack community\"><img src=\"https://slack.bevry.me/badge.svg\" alt=\"Slack community badge\" /></a></span>\n\n<!-- /BADGES -->\n\n\n<!-- DESCRIPTION/ -->\n\nNode's domain module for the web browser. This is merely an evented try...catch with the same API as node, nothing more.\n\n<!-- /DESCRIPTION -->\n\n\n<!-- INSTALL/ -->\n\n<h2>Install</h2>\n\n<a href=\"https://npmjs.com\" title=\"npm is a package manager for javascript\"><h3>NPM</h3></a><ul>\n<li>Install: <code>npm install --save domain-browser</code></li>\n<li>Module: <code>require('domain-browser')</code></li></ul>\n\n<a href=\"http://browserify.org\" title=\"Browserify lets you require('modules') in the browser by bundling up all of your dependencies\"><h3>Browserify</h3></a><ul>\n<li>Install: <code>npm install --save domain-browser</code></li>\n<li>Module: <code>require('domain-browser')</code></li>\n<li>CDN URL: <code>//wzrd.in/bundle/domain-browser@1.2.0</code></li></ul>\n\n<a href=\"http://enderjs.com\" title=\"Ender is a full featured package manager for your browser\"><h3>Ender</h3></a><ul>\n<li>Install: <code>ender add domain-browser</code></li>\n<li>Module: <code>require('domain-browser')</code></li></ul>\n\n<h3><a href=\"https://github.com/bevry/editions\" title=\"Editions are the best way to produce and consume packages you care about.\">Editions</a></h3>\n\n<p>This package is published with the following editions:</p>\n\n<ul><li><code>domain-browser</code> aliases <code>domain-browser/source/index.js</code></li>\n<li><code>domain-browser/source/index.js</code> is Source + ES5 + <a href=\"https://nodejs.org/dist/latest-v5.x/docs/api/modules.html\" title=\"Node/CJS Modules\">Require</a></li></ul>\n\n<!-- /INSTALL -->\n\n\n<!-- HISTORY/ -->\n\n<h2>History</h2>\n\n<a href=\"https://github.com/bevry/domain-browser/blob/master/HISTORY.md#files\">Discover the release history by heading on over to the <code>HISTORY.md</code> file.</a>\n\n<!-- /HISTORY -->\n\n\n<!-- BACKERS/ -->\n\n<h2>Backers</h2>\n\n<h3>Maintainers</h3>\n\nThese amazing people are maintaining this project:\n\n<ul><li><a href=\"http://balupton.com\">Benjamin Lupton</a> — <a href=\"https://github.com/bevry/domain-browser/commits?author=balupton\" title=\"View the GitHub contributions of Benjamin Lupton on repository bevry/domain-browser\">view contributions</a></li></ul>\n\n<h3>Sponsors</h3>\n\nNo sponsors yet! Will you be the first?\n\n<span class=\"badge-patreon\"><a href=\"https://patreon.com/bevry\" title=\"Donate to this project using Patreon\"><img src=\"https://img.shields.io/badge/patreon-donate-yellow.svg\" alt=\"Patreon donate button\" /></a></span>\n<span class=\"badge-opencollective\"><a href=\"https://opencollective.com/bevry\" title=\"Donate to this project using Open Collective\"><img src=\"https://img.shields.io/badge/open%20collective-donate-yellow.svg\" alt=\"Open Collective donate button\" /></a></span>\n<span class=\"badge-gratipay\"><a href=\"https://www.gratipay.com/bevry\" title=\"Donate weekly to this project using Gratipay\"><img src=\"https://img.shields.io/badge/gratipay-donate-yellow.svg\" alt=\"Gratipay donate button\" /></a></span>\n<span class=\"badge-flattr\"><a href=\"https://flattr.com/profile/balupton\" title=\"Donate to this project using Flattr\"><img src=\"https://img.shields.io/badge/flattr-donate-yellow.svg\" alt=\"Flattr donate button\" /></a></span>\n<span class=\"badge-paypal\"><a href=\"https://bevry.me/paypal\" title=\"Donate to this project using Paypal\"><img src=\"https://img.shields.io/badge/paypal-donate-yellow.svg\" alt=\"PayPal donate button\" /></a></span>\n<span class=\"badge-bitcoin\"><a href=\"https://bevry.me/bitcoin\" title=\"Donate once-off to this project using Bitcoin\"><img src=\"https://img.shields.io/badge/bitcoin-donate-yellow.svg\" alt=\"Bitcoin donate button\" /></a></span>\n<span class=\"badge-wishlist\"><a href=\"https://bevry.me/wishlist\" title=\"Buy an item on our wishlist for us\"><img src=\"https://img.shields.io/badge/wishlist-donate-yellow.svg\" alt=\"Wishlist browse button\" /></a></span>\n\n<h3>Contributors</h3>\n\nThese amazing people have contributed code to this project:\n\n<ul><li><a href=\"http://balupton.com\">Benjamin Lupton</a> — <a href=\"https://github.com/bevry/domain-browser/commits?author=balupton\" title=\"View the GitHub contributions of Benjamin Lupton on repository bevry/domain-browser\">view contributions</a></li>\n<li><a href=\"http://evansolomon.me\">Evan Solomon</a> — <a href=\"https://github.com/bevry/domain-browser/commits?author=evansolomon\" title=\"View the GitHub contributions of Evan Solomon on repository bevry/domain-browser\">view contributions</a></li>\n<li><a href=\"http://substack.neocities.org/\">James Halliday</a> — <a href=\"https://github.com/bevry/domain-browser/commits?author=substack\" title=\"View the GitHub contributions of James Halliday on repository bevry/domain-browser\">view contributions</a></li>\n<li><a href=\"twitter.com/guybedford\">Guy Bedford</a> — <a href=\"https://github.com/bevry/domain-browser/commits?author=guybedford\" title=\"View the GitHub contributions of Guy Bedford on repository bevry/domain-browser\">view contributions</a></li>\n<li><a href=\"https://github.com/TrySound\">Bogdan Chadkin</a> — <a href=\"https://github.com/bevry/domain-browser/commits?author=TrySound\" title=\"View the GitHub contributions of Bogdan Chadkin on repository bevry/domain-browser\">view contributions</a></li></ul>\n\n<a href=\"https://github.com/bevry/domain-browser/blob/master/CONTRIBUTING.md#files\">Discover how you can contribute by heading on over to the <code>CONTRIBUTING.md</code> file.</a>\n\n<!-- /BACKERS -->\n\n\n<!-- LICENSE/ -->\n\n<h2>License</h2>\n\nUnless stated otherwise all works are:\n\n<ul><li>Copyright &copy; 2013+ <a href=\"http://bevry.me\">Bevry Pty Ltd</a></li></ul>\n\nand licensed under:\n\n<ul><li><a href=\"http://spdx.org/licenses/MIT.html\">MIT License</a></li></ul>\n\n<!-- /LICENSE -->\n", "licenseText": "<!-- LICENSEFILE/ -->\n\n<h1>License</h1>\n\nUnless stated otherwise all works are:\n\n<ul><li>Copyright &copy; 2013+ <a href=\"http://bevry.me\">Bevry Pty Ltd</a></li></ul>\n\nand licensed under:\n\n<ul><li><a href=\"http://spdx.org/licenses/MIT.html\">MIT License</a></li></ul>\n\n<h2>MIT License</h2>\n\n<pre>\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n</pre>\n\n<!-- /LICENSEFILE -->\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/domain-browser/-/domain-browser-1.2.0.tgz#3d31f50191a6749dd1375a7f522e823d42e54eda", "type": "tarball", "reference": "https://registry.yarnpkg.com/domain-browser/-/domain-browser-1.2.0.tgz", "hash": "3d31f50191a6749dd1375a7f522e823d42e54eda", "integrity": "sha512-jnjyiM6eRyZl2H+W8Q/zLMA481hzi0eszAaBUzIVnmYVDBbnLxVNnfu1HgEBvCbL+71FrxMl3E6lpKH7Ge3OXA==", "registry": "npm", "packageName": "domain-browser", "cacheIntegrity": "sha512-jnjyiM6eRyZl2H+W8Q/zLMA481hzi0eszAaBUzIVnmYVDBbnLxVNnfu1HgEBvCbL+71FrxMl3E6lpKH7Ge3OXA== sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto="}, "registry": "npm", "hash": "3d31f50191a6749dd1375a7f522e823d42e54eda"}