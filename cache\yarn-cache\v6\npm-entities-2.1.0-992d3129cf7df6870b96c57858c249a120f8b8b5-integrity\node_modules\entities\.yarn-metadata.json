{"manifest": {"name": "entities", "version": "2.1.0", "description": "Encode & decode XML and HTML entities with ease", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": "https://github.com/fb55/entities?sponsor=1", "sideEffects": false, "keywords": ["entity", "decoding", "encoding", "html", "xml", "html entities"], "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib/**/*"], "devDependencies": {"@types/jest": "^26.0.0", "@types/node": "^14.11.8", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "coveralls": "*", "eslint": "^7.11.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-node": "^11.1.0", "jest": "^26.5.3", "prettier": "^2.0.5", "ts-jest": "^26.1.0", "typescript": "^4.0.2"}, "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint .", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc && cp -r src/maps lib", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/entities.git"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-entities-2.1.0-992d3129cf7df6870b96c57858c249a120f8b8b5-integrity\\node_modules\\entities\\package.json", "readmeFilename": "readme.md", "readme": "# entities [![NPM version](http://img.shields.io/npm/v/entities.svg)](https://npmjs.org/package/entities) [![Downloads](https://img.shields.io/npm/dm/entities.svg)](https://npmjs.org/package/entities) [![Build Status](http://img.shields.io/travis/fb55/entities.svg)](http://travis-ci.org/fb55/entities) [![Coverage](http://img.shields.io/coveralls/fb55/entities.svg)](https://coveralls.io/r/fb55/entities)\n\nEncode & decode HTML & XML entities with ease & speed.\n\n## How to…\n\n### …install `entities`\n\n    npm install entities\n\n### …use `entities`\n\n```javascript\nconst entities = require(\"entities\");\n\n//encoding\nentities.escape(\"&#38;\"); // \"&#x26;#38;\"\nentities.encodeXML(\"&#38;\"); // \"&amp;#38;\"\nentities.encodeHTML(\"&#38;\"); // \"&amp;&num;38&semi;\"\n\n//decoding\nentities.decodeXML(\"asdf &amp; &#xFF; &#xFC; &apos;\"); // \"asdf & ÿ ü '\"\nentities.decodeHTML(\"asdf &amp; &yuml; &uuml; &apos;\"); // \"asdf & ÿ ü '\"\n```\n\n## Performance\n\nThis is how `entities` compares to other libraries on a very basic benchmark (see `scripts/benchmark.ts`, for 10,000,000 iterations):\n\n| Library        | `decode` performance | `encode` performance | Bundle size                                                                |\n| -------------- | -------------------- | -------------------- | -------------------------------------------------------------------------- |\n| entities       | 10.809s              | 17.683s              | ![npm bundle size](https://img.shields.io/bundlephobia/min/entities)       |\n| html-entities  | 14.029s              | 22.670s              | ![npm bundle size](https://img.shields.io/bundlephobia/min/html-entities)  |\n| he             | 16.163s              | 44.010s              | ![npm bundle size](https://img.shields.io/bundlephobia/min/he)             |\n| parse-entities | 28.507s              | N/A                  | ![npm bundle size](https://img.shields.io/bundlephobia/min/parse-entities) |\n\n---\n\nLicense: BSD-2-Clause\n\n## Security contact information\n\nTo report a security vulnerability, please use the [Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\n## `entities` for enterprise\n\nAvailable as part of the Tidelift Subscription\n\nThe maintainers of `entities` and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-entities?utm_source=npm-entities&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n", "licenseText": "Copyright (c) <PERSON>\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n\nRedistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.\n\nRedistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.\n\nTHIS IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERC<PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS,\nEVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/entities/-/entities-2.1.0.tgz#992d3129cf7df6870b96c57858c249a120f8b8b5", "type": "tarball", "reference": "https://registry.yarnpkg.com/entities/-/entities-2.1.0.tgz", "hash": "992d3129cf7df6870b96c57858c249a120f8b8b5", "integrity": "sha512-hCx1oky9PFrJ611mf0ifBLBRW8lUUVRlFolb5gWRfIELabBlbp9xZvrqZLZAs+NxFnbfQoeGd8wDkygjg7U85w==", "registry": "npm", "packageName": "entities", "cacheIntegrity": "sha512-hCx1oky9PFrJ611mf0ifBLBRW8lUUVRlFolb5gWRfIELabBlbp9xZvrqZLZAs+NxFnbfQoeGd8wDkygjg7U85w== sha1-mS0xKc999ocLlsV4WMJJoSD4uLU="}, "registry": "npm", "hash": "992d3129cf7df6870b96c57858c249a120f8b8b5"}