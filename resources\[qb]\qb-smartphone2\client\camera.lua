local QBCore = exports['qb-core']:GetCoreObject()

-- Lokální proměnné pro kameru
local isCameraActive = false
local cameraScaleform = nil
local frontCam = false
local phone = nil
local phoneRotation = nil

-- NUI Callbacks pro kameru
RegisterNUICallback('openCamera', function(data, cb)
    OpenCamera()
    cb('ok')
end)

RegisterNUICallback('closeCamera', function(data, cb)
    CloseCamera()
    cb('ok')
end)

RegisterNUICallback('takePhoto', function(data, cb)
    TakePhoto(data.description or '')
    cb('ok')
end)

RegisterNUICallback('switchCamera', function(data, cb)
    SwitchCamera()
    cb('ok')
end)

-- Funkce pro otevření kamery
function OpenCamera()
    if isCameraActive then return end
    
    isCameraActive = true
    
    -- Vytvoření telefonu jako prop
    CreatePhoneProp()
    
    -- Nastavení kamery
    SetupCamera()
    
    -- Scaleform pro UI kamery
    CreateCameraScaleform()
    
    -- Hlavní smyčka kamery
    CreateThread(function()
        while isCameraActive do
            Wait(0)
            
            -- Vykreslení scaleform
            if cameraScaleform then
                DrawScaleformMovieFullscreen(cameraScaleform, 255, 255, 255, 255, 0)
            end
            
            -- Kontroly pro zavření kamery
            if IsControlJustPressed(0, 177) then -- BACKSPACE
                CloseCamera()
            end
            
            -- Pořízení fotky
            if IsControlJustPressed(0, 176) then -- ENTER
                TakePhoto('')
            end
            
            -- Přepnutí kamery
            if IsControlJustPressed(0, 178) then -- DELETE
                SwitchCamera()
            end
        end
    end)
    
    -- Oznámení NUI
    SendNUIMessage({
        action = 'cameraOpened'
    })
end

-- Funkce pro zavření kamery
function CloseCamera()
    if not isCameraActive then return end
    
    isCameraActive = false
    
    -- Vyčištění kamery
    DestroyMobilePhone()
    ClearFocus()
    RenderScriptCams(false, true, 500, true, true)
    DestroyCam(frontCam, false)
    
    -- Vyčištění scaleform
    if cameraScaleform then
        SetScaleformMovieAsNoLongerNeeded(cameraScaleform)
        cameraScaleform = nil
    end
    
    -- Vyčištění prop
    if phone then
        DeleteEntity(phone)
        phone = nil
    end
    
    -- Oznámení NUI
    SendNUIMessage({
        action = 'cameraClosed'
    })
end

-- Funkce pro vytvoření telefonu jako prop
function CreatePhoneProp()
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)

    -- Model telefonu
    local phoneModel = `prop_phone_01`
    RequestModel(phoneModel)
    while not HasModelLoaded(phoneModel) do
        Wait(0)
    end

    -- Vytvoření telefonu
    phone = CreateObject(phoneModel, coords.x, coords.y, coords.z, true, true, false)

    -- Připojení k ruce
    local boneIndex = GetPedBoneIndex(ped, 28422)
    AttachEntityToEntity(phone, ped, boneIndex, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, true, true, false, true, 1, true)

    SetModelAsNoLongerNeeded(phoneModel)
end

-- Funkce pro nastavení kamery
function SetupCamera()
    local ped = PlayerPedId()
    
    -- Vytvoření kamery
    frontCam = CreateCam('DEFAULT_SCRIPTED_FLY_CAMERA', true)
    
    -- Nastavení pozice kamery
    AttachCamToEntity(frontCam, ped, 0.0, 0.5, 0.65, true)
    SetCamRot(frontCam, 0.0, 0.0, GetEntityHeading(ped), 2)
    SetCamFov(frontCam, 110.0)
    RenderScriptCams(true, false, 0, true, false)
    
    -- Nastavení focus
    SetFocusEntity(ped)
end

-- Funkce pro přepnutí kamery (přední/zadní)
function SwitchCamera()
    local ped = PlayerPedId()
    
    if frontCam then
        DestroyCam(frontCam, false)
    end
    
    frontCam = CreateCam('DEFAULT_SCRIPTED_FLY_CAMERA', true)
    
    if not frontCamera then
        -- Zadní kamera
        AttachCamToEntity(frontCam, ped, 0.0, 0.5, 0.65, true)
        SetCamRot(frontCam, 0.0, 0.0, GetEntityHeading(ped), 2)
        frontCamera = true
    else
        -- Přední kamera (selfie)
        AttachCamToEntity(frontCam, ped, 0.0, -0.5, 0.65, true)
        SetCamRot(frontCam, 0.0, 0.0, GetEntityHeading(ped) + 180.0, 2)
        frontCamera = false
    end
    
    SetCamFov(frontCam, 110.0)
    RenderScriptCams(true, false, 0, true, false)
end

-- Funkce pro pořízení fotky
function TakePhoto(description)
    if not isCameraActive then return end
    
    -- Efekt blesku
    DoScreenFadeOut(100)
    Wait(100)
    DoScreenFadeIn(100)
    
    -- Zvuk spouště
    PlaySoundFrontend(-1, "Camera_Shoot", "Phone_SoundSet_Default", true)
    
    -- Získání screenshotu
    exports['screenshot-basic']:requestScreenshotUpload('https://discord.com/api/webhooks/YOUR_WEBHOOK_URL', 'files[]', function(data)
        local imageUrl = json.decode(data).attachments[1].proxy_url
        
        -- Uložení do galerie
        TriggerServerEvent('qb-smartphone2:server:savePhoto', {
            image = imageUrl,
            description = description,
            location = GetCurrentLocation()
        })
        
        QBCore.Functions.Notify('Fotka byla pořízena!', 'success')
    end)
end

-- Funkce pro vytvoření scaleform UI
function CreateCameraScaleform()
    cameraScaleform = RequestScaleformMovie('MOBILE_PHONE')
    
    while not HasScaleformMovieLoaded(cameraScaleform) do
        Wait(0)
    end
    
    -- Nastavení scaleform
    BeginScaleformMovieMethod(cameraScaleform, 'SET_BACKGROUND')
    ScaleformMovieMethodAddParamInt(1)
    EndScaleformMovieMethod()
end

-- Funkce pro získání současné lokace
function GetCurrentLocation()
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local streetHash, crossingHash = GetStreetNameAtCoord(coords.x, coords.y, coords.z)
    local streetName = GetStreetNameFromHashKey(streetHash)
    
    if crossingHash ~= 0 then
        local crossingName = GetStreetNameFromHashKey(crossingHash)
        return streetName .. ' / ' .. crossingName
    else
        return streetName
    end
end

-- Event handlery
RegisterNetEvent('qb-smartphone2:client:openCamera', function()
    OpenCamera()
end)

RegisterNetEvent('qb-smartphone2:client:closeCamera', function()
    CloseCamera()
end)

-- Cleanup při unload
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        if isCameraActive then
            CloseCamera()
        end
    end
end)
