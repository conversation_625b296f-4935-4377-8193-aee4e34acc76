{"name": "@types/express", "version": "4.17.11", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "51b7ca65fbad2f43fbcff8d74c47db7b8f36b31f71458c1fb328511d0075ac5a", "typeScriptVersion": "3.4"}