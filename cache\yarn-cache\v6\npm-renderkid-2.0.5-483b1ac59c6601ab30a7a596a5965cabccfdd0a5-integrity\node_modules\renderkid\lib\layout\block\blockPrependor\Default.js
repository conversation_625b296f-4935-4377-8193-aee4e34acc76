// Generated by CoffeeScript 1.9.3
var DefaultBlockPrependor, tools,
  extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
  hasProp = {}.hasOwnProperty;

tools = require('../../../tools');

module.exports = DefaultBlockPrependor = (function(superClass) {
  extend(DefaultBlockPrependor, superClass);

  function DefaultBlockPrependor() {
    return DefaultBlockPrependor.__super__.constructor.apply(this, arguments);
  }

  DefaultBlockPrependor.prototype._render = function(options) {
    return tools.repeatString("\n", this._config.amount);
  };

  return DefaultBlockPrependor;

})(require('./_BlockPrependor'));
