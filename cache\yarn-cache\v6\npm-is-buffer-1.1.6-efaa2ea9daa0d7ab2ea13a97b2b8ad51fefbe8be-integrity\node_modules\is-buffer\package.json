{"name": "is-buffer", "description": "Determine if an object is a Buffer", "version": "1.1.6", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "bugs": {"url": "https://github.com/feross/is-buffer/issues"}, "dependencies": {}, "devDependencies": {"standard": "*", "tape": "^4.0.0", "zuul": "^3.0.0"}, "keywords": ["buffer", "buffers", "type", "core buffer", "browser buffer", "browserify", "typed array", "uint32array", "int16array", "int32array", "float32array", "float64array", "browser", "arraybuffer", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/is-buffer.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js", "test-node": "tape test/*.js"}, "testling": {"files": "test/*.js"}}