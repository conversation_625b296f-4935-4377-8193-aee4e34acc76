# 🚗 DDCZ-VEHICLE - Pokročilé ovládání vozidel

Moderní animované menu pro kompletní ovlád<PERSON><PERSON> vozidel s podporou extras a livery pro policii/EMS.

## ✨ Funkce

### 🔧 Základní ovládání
- **Motor** - Zapnutí/vypnutí motoru
- **Zámky** - Zamčení/odemčení vozidla
- **Okna** - Ovládání všech oken najednou
- **Kufr** - Rychlé otevření/zavření kufru

### 🚪 Ovládání dveří
- Přední levé/pravé dveře
- Zadní levé/pravé dveře
- Kapota motoru
- Kufr vozidla

### 💡 Světelný systém
- Světlomety
- Interiérové osvětlení
- Levý/pravý blikač
- Výstražná světla
- Taxi světlo

### 🚔 Speciální funkce (Policie/EMS)
- **Extras** - Ovládán<PERSON> všech vehicle extras (1-12)
- **Livery** - Změna livery vozidla
- Pouze pro povolané profese

## 📋 Požadavky

- QBCore Framework
- FiveM Server

## 🚀 Instalace

### 1. Stažení a umístění
```bash
# Umístěte složku do resources/[standalone]/
resources/[standalone]/ddcz-vehicle/
```

### 2. Server.cfg
```cfg
# Přidejte do server.cfg
ensure ddcz-vehicle
```

### 3. Konfigurace
Upravte `config.lua` podle vašich potřeb:

```lua
-- Výchozí klávesa pro otevření menu
Config.DefaultKeybind = 'U'

-- Povolané profese s přístupem k extras/livery
Config.RestrictedJobs = {
    'police',
    'ambulance',
    'fire'
}
```

## 🎮 Použití

### Příkazy
- `/vehicle` - Otevře vehicle menu
- `/vehiclemenu` - Alternativní příkaz
- `U` - Výchozí klávesa (konfigurovatelná)

### Admin příkazy
- `/vehicleadmin reload` - Reload konfigurace
- `/vehicleadmin reset [playerid]` - Resetovat menu hráče

## ⚙️ Konfigurace

### Základní nastavení
```lua
-- Maximální vzdálenost od vozidla
Config.MaxDistance = 5.0

-- Automatické zavření při opuštění vozidla
Config.AutoCloseOnExit = true

-- Synchronizace změn mezi hráči
Config.SyncVehicleChanges = true
```

### Povolané profese
```lua
Config.RestrictedJobs = {
    'police',    -- Policie
    'ambulance', -- Záchranná služba
    'fire'       -- Hasiči
}
```

### Zprávy
```lua
Config.Messages = {
    no_vehicle = 'Nejste v žádném vozidle!',
    engine_on = 'Motor zapnut',
    engine_off = 'Motor vypnut',
    -- ... další zprávy
}
```

## 🎨 UI Funkce

### Moderní design
- Gradient pozadí s animacemi
- Smooth přechody a hover efekty
- Responzivní design pro různá rozlišení
- Neonové akcenty a glow efekty

### Animace
- Slide-in animace při otevření
- Pulse efekt při kliknutí
- Hover transformace tlačítek
- Smooth color transitions

### Interaktivní prvky
- Vizuální feedback pro všechny akce
- Real-time status indikátory
- Drag-free scrolling
- Keyboard shortcuts (ESC pro zavření)

## 🔧 Technické detaily

### Synchronizace
- Real-time synchronizace změn mezi všemi hráči
- Optimalizované network eventy
- Prevence duplikace akcí

### Bezpečnost
- Server-side validace všech akcí
- Permission kontrola pro restricted funkce
- Anti-spam ochrana

### Výkon
- Optimalizované NUI callbacks
- Lazy loading pro extras/livery
- Minimal resource usage

## 🐛 Řešení problémů

### Menu se neotevírá
- ✅ Zkontrolujte, zda jste v/blízko vozidla
- ✅ Ověřte správnou klávesu (výchozí: U)
- ✅ Restartujte resource

### Extras/Livery nefungují
- ✅ Zkontrolujte, zda máte správnou profesi
- ✅ Ověřte Config.RestrictedJobs
- ✅ Restartujte server

### Synchronizace nefunguje
- ✅ Zkontrolujte Config.SyncVehicleChanges = true
- ✅ Ověřte server konzoli pro chyby
- ✅ Restartujte resource

## 📝 Changelog

### v1.0.0
- ✅ Kompletní vehicle control systém
- ✅ Moderní animované NUI
- ✅ Extras a livery pro policii/EMS
- ✅ Real-time synchronizace
- ✅ Admin příkazy
- ✅ Plná konfigurovatelnost

## 👨‍💻 Autor

**DDCZ** - FiveM Script Developer

## 📄 Licence

Tento script je poskytován "jak je" bez jakýchkoli záruk.

---

**🚗 Užijte si pokročilé ovládání vozidel!**
