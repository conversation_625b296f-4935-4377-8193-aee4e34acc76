// Generated by CoffeeScript 1.9.3
var DefaultLinePrependor, SpecialString, tools,
  extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
  hasProp = {}.hasOwnProperty;

tools = require('../../../tools');

SpecialString = require('../../SpecialString');

module.exports = DefaultLinePrependor = (function(superClass) {
  var self;

  extend(DefaultLinePrependor, superClass);

  function DefaultLinePrependor() {
    return DefaultLinePrependor.__super__.constructor.apply(this, arguments);
  }

  self = DefaultLinePrependor;

  DefaultLinePrependor.pad = function(howMuch) {
    return tools.repeatString(" ", howMuch);
  };

  DefaultLinePrependor.prototype._render = function(inherited, options) {
    var addToLeft, addToRight, alignment, bullet, char, charLen, diff, left, output, space, toWrite;
    if (this._lineNo === 0 && (bullet = this._config.bullet)) {
      char = bullet.char;
      charLen = SpecialString(char).length;
      alignment = bullet.alignment;
      space = this._config.amount;
      toWrite = char;
      addToLeft = '';
      addToRight = '';
      if (space > charLen) {
        diff = space - charLen;
        if (alignment === 'right') {
          addToLeft = self.pad(diff);
        } else if (alignment === 'left') {
          addToRight = self.pad(diff);
        } else if (alignment === 'center') {
          left = Math.round(diff / 2);
          addToLeft = self.pad(left);
          addToRight = self.pad(diff - left);
        } else {
          throw Error("Unknown alignment `" + alignment + "`");
        }
      }
      output = addToLeft + char + addToRight;
    } else {
      output = self.pad(this._config.amount);
    }
    return inherited + output;
  };

  return DefaultLinePrependor;

})(require('./_LinePrependor'));
