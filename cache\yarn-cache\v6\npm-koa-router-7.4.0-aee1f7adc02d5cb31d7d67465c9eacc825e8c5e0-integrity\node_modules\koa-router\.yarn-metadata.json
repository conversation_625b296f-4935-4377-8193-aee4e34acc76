{"manifest": {"name": "koa-router", "description": "Router middleware for koa. Provides RESTful resource routing.", "repository": {"type": "git", "url": "https://github.com/alexmingoia/koa-router.git"}, "main": "lib/router.js", "files": ["lib"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "7.4.0", "keywords": ["koa", "middleware", "router", "route"], "dependencies": {"debug": "^3.1.0", "http-errors": "^1.3.1", "koa-compose": "^3.0.0", "methods": "^1.0.1", "path-to-regexp": "^1.1.1", "urijs": "^1.19.0"}, "devDependencies": {"expect.js": "^0.3.1", "gulp": "^3.8.11", "jsdoc-to-markdown": "^1.1.1", "koa": "2.2.0", "mocha": "^2.0.1", "should": "^6.0.3", "supertest": "^1.0.1"}, "scripts": {"test": "NODE_ENV=test mocha test/**/*.js", "docs": "NODE_ENV=test node node_modules/gulp/bin/gulp.js docs"}, "engines": {"node": ">= 4"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-koa-router-7.4.0-aee1f7adc02d5cb31d7d67465c9eacc825e8c5e0-integrity\\node_modules\\koa-router\\package.json", "readmeFilename": "README.md", "readme": "# koa-router\n\n[![NPM version](https://img.shields.io/npm/v/koa-router.svg?style=flat)](https://npmjs.org/package/koa-router) [![NPM Downloads](https://img.shields.io/npm/dm/koa-router.svg?style=flat)](https://npmjs.org/package/koa-router) [![Node.js Version](https://img.shields.io/node/v/koa-router.svg?style=flat)](http://nodejs.org/download/) [![Build Status](https://img.shields.io/travis/alexmingoia/koa-router.svg?style=flat)](http://travis-ci.org/alexmingoia/koa-router) [![Tips](https://img.shields.io/gratipay/alexmingoia.svg?style=flat)](https://www.gratipay.com/alexmingoia/) [![Gitter Chat](https://img.shields.io/badge/gitter-join%20chat-1dce73.svg?style=flat)](https://gitter.im/alexmingoia/koa-router/)\n\n> Router middleware for [koa](https://github.com/koajs/koa)\n\n* Express-style routing using `app.get`, `app.put`, `app.post`, etc.\n* Named URL parameters.\n* Named routes with URL generation.\n* Responds to `OPTIONS` requests with allowed methods.\n* Support for `405 Method Not Allowed` and `501 Not Implemented`.\n* Multiple route middleware.\n* Multiple routers.\n* Nestable routers.\n* ES7 async/await support.\n\n## Migrating to 7 / Koa 2\n\n- The API has changed to match the new promise-based middleware\n  signature of koa 2. See the\n  [koa 2.x readme](https://github.com/koajs/koa/tree/2.0.0-alpha.3) for more\n  information.\n- Middleware is now always run in the order declared by `.use()` (or `.get()`,\n  etc.), which matches Express 4 API.\n\n## Installation\n\nInstall using [npm](https://www.npmjs.org/):\n\n```sh\nnpm install koa-router\n```\n\n## API Reference\n  \n* [koa-router](#module_koa-router)\n    * [Router](#exp_module_koa-router--Router) ⏏\n        * [new Router([opts])](#new_module_koa-router--Router_new)\n        * _instance_\n            * [.get|put|post|patch|delete|del](#module_koa-router--Router+get|put|post|patch|delete|del) ⇒ <code>Router</code>\n            * [.routes](#module_koa-router--Router+routes) ⇒ <code>function</code>\n            * [.use([path], middleware)](#module_koa-router--Router+use) ⇒ <code>Router</code>\n            * [.prefix(prefix)](#module_koa-router--Router+prefix) ⇒ <code>Router</code>\n            * [.allowedMethods([options])](#module_koa-router--Router+allowedMethods) ⇒ <code>function</code>\n            * [.redirect(source, destination, [code])](#module_koa-router--Router+redirect) ⇒ <code>Router</code>\n            * [.route(name)](#module_koa-router--Router+route) ⇒ <code>Layer</code> &#124; <code>false</code>\n            * [.url(name, params, [options])](#module_koa-router--Router+url) ⇒ <code>String</code> &#124; <code>Error</code>\n            * [.param(param, middleware)](#module_koa-router--Router+param) ⇒ <code>Router</code>\n        * _static_\n            * [.url(path, params)](#module_koa-router--Router.url) ⇒ <code>String</code>\n\n<a name=\"exp_module_koa-router--Router\"></a>\n\n### Router ⏏\n**Kind**: Exported class  \n<a name=\"new_module_koa-router--Router_new\"></a>\n\n#### new Router([opts])\nCreate a new router.\n\n\n| Param | Type | Description |\n| --- | --- | --- |\n| [opts] | <code>Object</code> |  |\n| [opts.prefix] | <code>String</code> | prefix router paths |\n\n**Example**  \nBasic usage:\n\n```javascript\nvar Koa = require('koa');\nvar Router = require('koa-router');\n\nvar app = new Koa();\nvar router = new Router();\n\nrouter.get('/', (ctx, next) => {\n  // ctx.router available\n});\n\napp\n  .use(router.routes())\n  .use(router.allowedMethods());\n```\n<a name=\"module_koa-router--Router+get|put|post|patch|delete|del\"></a>\n\n#### router.get|put|post|patch|delete|del ⇒ <code>Router</code>\nCreate `router.verb()` methods, where *verb* is one of the HTTP verbs such\nas `router.get()` or `router.post()`.\n\nMatch URL patterns to callback functions or controller actions using `router.verb()`,\nwhere **verb** is one of the HTTP verbs such as `router.get()` or `router.post()`.\n\nAdditionaly, `router.all()` can be used to match against all methods.\n\n```javascript\nrouter\n  .get('/', (ctx, next) => {\n    ctx.body = 'Hello World!';\n  })\n  .post('/users', (ctx, next) => {\n    // ...\n  })\n  .put('/users/:id', (ctx, next) => {\n    // ...\n  })\n  .del('/users/:id', (ctx, next) => {\n    // ...\n  })\n  .all('/users/:id', (ctx, next) => {\n    // ...\n  });\n```\n\nWhen a route is matched, its path is available at `ctx._matchedRoute` and if named,\nthe name is available at `ctx._matchedRouteName`\n\nRoute paths will be translated to regular expressions using\n[path-to-regexp](https://github.com/pillarjs/path-to-regexp).\n\nQuery strings will not be considered when matching requests.\n\n#### Named routes\n\nRoutes can optionally have names. This allows generation of URLs and easy\nrenaming of URLs during development.\n\n```javascript\nrouter.get('user', '/users/:id', (ctx, next) => {\n // ...\n});\n\nrouter.url('user', 3);\n// => \"/users/3\"\n```\n\n#### Multiple middleware\n\nMultiple middleware may be given:\n\n```javascript\nrouter.get(\n  '/users/:id',\n  (ctx, next) => {\n    return User.findOne(ctx.params.id).then(function(user) {\n      ctx.user = user;\n      next();\n    });\n  },\n  ctx => {\n    console.log(ctx.user);\n    // => { id: 17, name: \"Alex\" }\n  }\n);\n```\n\n### Nested routers\n\nNesting routers is supported:\n\n```javascript\nvar forums = new Router();\nvar posts = new Router();\n\nposts.get('/', (ctx, next) => {...});\nposts.get('/:pid', (ctx, next) => {...});\nforums.use('/forums/:fid/posts', posts.routes(), posts.allowedMethods());\n\n// responds to \"/forums/123/posts\" and \"/forums/123/posts/123\"\napp.use(forums.routes());\n```\n\n#### Router prefixes\n\nRoute paths can be prefixed at the router level:\n\n```javascript\nvar router = new Router({\n  prefix: '/users'\n});\n\nrouter.get('/', ...); // responds to \"/users\"\nrouter.get('/:id', ...); // responds to \"/users/:id\"\n```\n\n#### URL parameters\n\nNamed route parameters are captured and added to `ctx.params`.\n\n```javascript\nrouter.get('/:category/:title', (ctx, next) => {\n  console.log(ctx.params);\n  // => { category: 'programming', title: 'how-to-node' }\n});\n```\n\nThe [path-to-regexp](https://github.com/pillarjs/path-to-regexp) module is\nused to convert paths to regular expressions.\n\n**Kind**: instance property of <code>[Router](#exp_module_koa-router--Router)</code>  \n\n| Param | Type | Description |\n| --- | --- | --- |\n| path | <code>String</code> |  |\n| [middleware] | <code>function</code> | route middleware(s) |\n| callback | <code>function</code> | route callback |\n\n<a name=\"module_koa-router--Router+routes\"></a>\n\n#### router.routes ⇒ <code>function</code>\nReturns router middleware which dispatches a route matching the request.\n\n**Kind**: instance property of <code>[Router](#exp_module_koa-router--Router)</code>  \n<a name=\"module_koa-router--Router+use\"></a>\n\n#### router.use([path], middleware) ⇒ <code>Router</code>\nUse given middleware.\n\nMiddleware run in the order they are defined by `.use()`. They are invoked\nsequentially, requests start at the first middleware and work their way\n\"down\" the middleware stack.\n\n**Kind**: instance method of <code>[Router](#exp_module_koa-router--Router)</code>  \n\n| Param | Type |\n| --- | --- |\n| [path] | <code>String</code> | \n| middleware | <code>function</code> | \n| [...] | <code>function</code> | \n\n**Example**  \n```javascript\n// session middleware will run before authorize\nrouter\n  .use(session())\n  .use(authorize());\n\n// use middleware only with given path\nrouter.use('/users', userAuth());\n\n// or with an array of paths\nrouter.use(['/users', '/admin'], userAuth());\n\napp.use(router.routes());\n```\n<a name=\"module_koa-router--Router+prefix\"></a>\n\n#### router.prefix(prefix) ⇒ <code>Router</code>\nSet the path prefix for a Router instance that was already initialized.\n\n**Kind**: instance method of <code>[Router](#exp_module_koa-router--Router)</code>  \n\n| Param | Type |\n| --- | --- |\n| prefix | <code>String</code> | \n\n**Example**  \n```javascript\nrouter.prefix('/things/:thing_id')\n```\n<a name=\"module_koa-router--Router+allowedMethods\"></a>\n\n#### router.allowedMethods([options]) ⇒ <code>function</code>\nReturns separate middleware for responding to `OPTIONS` requests with\nan `Allow` header containing the allowed methods, as well as responding\nwith `405 Method Not Allowed` and `501 Not Implemented` as appropriate.\n\n**Kind**: instance method of <code>[Router](#exp_module_koa-router--Router)</code>  \n\n| Param | Type | Description |\n| --- | --- | --- |\n| [options] | <code>Object</code> |  |\n| [options.throw] | <code>Boolean</code> | throw error instead of setting status and header |\n| [options.notImplemented] | <code>function</code> | throw the returned value in place of the default NotImplemented error |\n| [options.methodNotAllowed] | <code>function</code> | throw the returned value in place of the default MethodNotAllowed error |\n\n**Example**  \n```javascript\nvar Koa = require('koa');\nvar Router = require('koa-router');\n\nvar app = new Koa();\nvar router = new Router();\n\napp.use(router.routes());\napp.use(router.allowedMethods());\n```\n\n**Example with [Boom](https://github.com/hapijs/boom)**\n\n```javascript\nvar Koa = require('koa');\nvar Router = require('koa-router');\nvar Boom = require('boom');\n\nvar app = new Koa();\nvar router = new Router();\n\napp.use(router.routes());\napp.use(router.allowedMethods({\n  throw: true,\n  notImplemented: () => new Boom.notImplemented(),\n  methodNotAllowed: () => new Boom.methodNotAllowed()\n}));\n```\n<a name=\"module_koa-router--Router+redirect\"></a>\n\n#### router.redirect(source, destination, [code]) ⇒ <code>Router</code>\nRedirect `source` to `destination` URL with optional 30x status `code`.\n\nBoth `source` and `destination` can be route names.\n\n```javascript\nrouter.redirect('/login', 'sign-in');\n```\n\nThis is equivalent to:\n\n```javascript\nrouter.all('/login', ctx => {\n  ctx.redirect('/sign-in');\n  ctx.status = 301;\n});\n```\n\n**Kind**: instance method of <code>[Router](#exp_module_koa-router--Router)</code>  \n\n| Param | Type | Description |\n| --- | --- | --- |\n| source | <code>String</code> | URL or route name. |\n| destination | <code>String</code> | URL or route name. |\n| [code] | <code>Number</code> | HTTP status code (default: 301). |\n\n<a name=\"module_koa-router--Router+route\"></a>\n\n#### router.route(name) ⇒ <code>Layer</code> &#124; <code>false</code>\nLookup route with given `name`.\n\n**Kind**: instance method of <code>[Router](#exp_module_koa-router--Router)</code>  \n\n| Param | Type |\n| --- | --- |\n| name | <code>String</code> | \n\n<a name=\"module_koa-router--Router+url\"></a>\n\n#### router.url(name, params, [options]) ⇒ <code>String</code> &#124; <code>Error</code>\nGenerate URL for route. Takes a route name and map of named `params`.\n\n**Kind**: instance method of <code>[Router](#exp_module_koa-router--Router)</code>  \n\n| Param | Type | Description |\n| --- | --- | --- |\n| name | <code>String</code> | route name |\n| params | <code>Object</code> | url parameters |\n| [options] | <code>Object</code> | options parameter |\n| [options.query] | <code>Object</code> &#124; <code>String</code> | query options |\n\n**Example**  \n```javascript\nrouter.get('user', '/users/:id', (ctx, next) => {\n  // ...\n});\n\nrouter.url('user', 3);\n// => \"/users/3\"\n\nrouter.url('user', { id: 3 });\n// => \"/users/3\"\n\nrouter.use((ctx, next) => {\n  // redirect to named route\n  ctx.redirect(ctx.router.url('sign-in'));\n})\n\nrouter.url('user', { id: 3 }, { query: { limit: 1 } });\n// => \"/users/3?limit=1\"\n\nrouter.url('user', { id: 3 }, { query: \"limit=1\" });\n// => \"/users/3?limit=1\"\n```\n<a name=\"module_koa-router--Router+param\"></a>\n\n#### router.param(param, middleware) ⇒ <code>Router</code>\nRun middleware for named route parameters. Useful for auto-loading or\nvalidation.\n\n**Kind**: instance method of <code>[Router](#exp_module_koa-router--Router)</code>  \n\n| Param | Type |\n| --- | --- |\n| param | <code>String</code> | \n| middleware | <code>function</code> | \n\n**Example**  \n```javascript\nrouter\n  .param('user', (id, ctx, next) => {\n    ctx.user = users[id];\n    if (!ctx.user) return ctx.status = 404;\n    return next();\n  })\n  .get('/users/:user', ctx => {\n    ctx.body = ctx.user;\n  })\n  .get('/users/:user/friends', ctx => {\n    return ctx.user.getFriends().then(function(friends) {\n      ctx.body = friends;\n    });\n  })\n  // /users/3 => {\"id\": 3, \"name\": \"Alex\"}\n  // /users/3/friends => [{\"id\": 4, \"name\": \"TJ\"}]\n```\n<a name=\"module_koa-router--Router.url\"></a>\n\n#### Router.url(path, params) ⇒ <code>String</code>\nGenerate URL from url pattern and given `params`.\n\n**Kind**: static method of <code>[Router](#exp_module_koa-router--Router)</code>  \n\n| Param | Type | Description |\n| --- | --- | --- |\n| path | <code>String</code> | url pattern |\n| params | <code>Object</code> | url parameters |\n\n**Example**  \n```javascript\nvar url = Router.url('/users/:id', {id: 1});\n// => \"/users/1\"\n```\n## Contributing\n\nPlease submit all issues and pull requests to the [alexmingoia/koa-router](http://github.com/alexmingoia/koa-router) repository!\n\n## Tests\n\nRun tests using `npm test`.\n\n## Support\n\nIf you have any problem or suggestion please open an issue [here](https://github.com/alexmingoia/koa-router/issues).\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015 Alexander C. Mingoia\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/koa-router/-/koa-router-7.4.0.tgz#aee1f7adc02d5cb31d7d67465c9eacc825e8c5e0", "type": "tarball", "reference": "https://registry.yarnpkg.com/koa-router/-/koa-router-7.4.0.tgz", "hash": "aee1f7adc02d5cb31d7d67465c9eacc825e8c5e0", "integrity": "sha512-IWhaDXeAnfDBEpWS6hkGdZ1ablgr6Q6pGdXCyK38RbzuH4LkUOpPqPw+3f8l8aTDrQmBQ7xJc0bs2yV4dzcO+g==", "registry": "npm", "packageName": "koa-router", "cacheIntegrity": "sha512-IWhaDXeAnfDBEpWS6hkGdZ1ablgr6Q6pGdXCyK38RbzuH4LkUOpPqPw+3f8l8aTDrQmBQ7xJc0bs2yV4dzcO+g== sha1-ruH3rcAtXLMdfWdGXJ6syCXoxeA="}, "registry": "npm", "hash": "aee1f7adc02d5cb31d7d67465c9eacc825e8c5e0"}