var n0=Object.defineProperty;var r0=(e,t,n)=>t in e?n0(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Bu=(e,t,n)=>r0(e,typeof t!="symbol"?t+"":t,n);function a0(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const a in r)if(a!=="default"&&!(a in e)){const i=Object.getOwnPropertyDescriptor(r,a);i&&Object.defineProperty(e,a,i.get?i:{enumerable:!0,get:()=>r[a]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))r(a);new MutationObserver(a=>{for(const i of a)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(a){const i={};return a.integrity&&(i.integrity=a.integrity),a.referrerPolicy&&(i.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?i.credentials="include":a.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(a){if(a.ep)return;a.ep=!0;const i=n(a);fetch(a.href,i)}})();var jn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ji(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function i0(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var a=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,a.get?a:{enumerable:!0,get:function(){return e[r]}})}),n}var Qf={exports:{}},eo={},qf={exports:{}},V={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ea=Symbol.for("react.element"),o0=Symbol.for("react.portal"),l0=Symbol.for("react.fragment"),s0=Symbol.for("react.strict_mode"),u0=Symbol.for("react.profiler"),c0=Symbol.for("react.provider"),f0=Symbol.for("react.context"),d0=Symbol.for("react.forward_ref"),p0=Symbol.for("react.suspense"),m0=Symbol.for("react.memo"),h0=Symbol.for("react.lazy"),Vu=Symbol.iterator;function g0(e){return e===null||typeof e!="object"?null:(e=Vu&&e[Vu]||e["@@iterator"],typeof e=="function"?e:null)}var Kf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Xf=Object.assign,Zf={};function xr(e,t,n){this.props=e,this.context=t,this.refs=Zf,this.updater=n||Kf}xr.prototype.isReactComponent={};xr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};xr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Jf(){}Jf.prototype=xr.prototype;function ks(e,t,n){this.props=e,this.context=t,this.refs=Zf,this.updater=n||Kf}var Ss=ks.prototype=new Jf;Ss.constructor=ks;Xf(Ss,xr.prototype);Ss.isPureReactComponent=!0;var Wu=Array.isArray,ed=Object.prototype.hasOwnProperty,bs={current:null},td={key:!0,ref:!0,__self:!0,__source:!0};function nd(e,t,n){var r,a={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)ed.call(t,r)&&!td.hasOwnProperty(r)&&(a[r]=t[r]);var s=arguments.length-2;if(s===1)a.children=n;else if(1<s){for(var l=Array(s),u=0;u<s;u++)l[u]=arguments[u+2];a.children=l}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)a[r]===void 0&&(a[r]=s[r]);return{$$typeof:Ea,type:e,key:i,ref:o,props:a,_owner:bs.current}}function v0(e,t){return{$$typeof:Ea,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Es(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ea}function y0(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Gu=/\/+/g;function Co(e,t){return typeof e=="object"&&e!==null&&e.key!=null?y0(""+e.key):t.toString(36)}function si(e,t,n,r,a){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case Ea:case o0:o=!0}}if(o)return o=e,a=a(o),e=r===""?"."+Co(o,0):r,Wu(a)?(n="",e!=null&&(n=e.replace(Gu,"$&/")+"/"),si(a,t,n,"",function(u){return u})):a!=null&&(Es(a)&&(a=v0(a,n+(!a.key||o&&o.key===a.key?"":(""+a.key).replace(Gu,"$&/")+"/")+e)),t.push(a)),1;if(o=0,r=r===""?".":r+":",Wu(e))for(var s=0;s<e.length;s++){i=e[s];var l=r+Co(i,s);o+=si(i,t,n,l,a)}else if(l=g0(e),typeof l=="function")for(e=l.call(e),s=0;!(i=e.next()).done;)i=i.value,l=r+Co(i,s++),o+=si(i,t,n,l,a);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function Oa(e,t,n){if(e==null)return e;var r=[],a=0;return si(e,r,"","",function(i){return t.call(n,i,a++)}),r}function w0(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var je={current:null},ui={transition:null},x0={ReactCurrentDispatcher:je,ReactCurrentBatchConfig:ui,ReactCurrentOwner:bs};function rd(){throw Error("act(...) is not supported in production builds of React.")}V.Children={map:Oa,forEach:function(e,t,n){Oa(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Oa(e,function(){t++}),t},toArray:function(e){return Oa(e,function(t){return t})||[]},only:function(e){if(!Es(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};V.Component=xr;V.Fragment=l0;V.Profiler=u0;V.PureComponent=ks;V.StrictMode=s0;V.Suspense=p0;V.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=x0;V.act=rd;V.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Xf({},e.props),a=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=bs.current),t.key!==void 0&&(a=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(l in t)ed.call(t,l)&&!td.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&s!==void 0?s[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){s=Array(l);for(var u=0;u<l;u++)s[u]=arguments[u+2];r.children=s}return{$$typeof:Ea,type:e.type,key:a,ref:i,props:r,_owner:o}};V.createContext=function(e){return e={$$typeof:f0,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:c0,_context:e},e.Consumer=e};V.createElement=nd;V.createFactory=function(e){var t=nd.bind(null,e);return t.type=e,t};V.createRef=function(){return{current:null}};V.forwardRef=function(e){return{$$typeof:d0,render:e}};V.isValidElement=Es;V.lazy=function(e){return{$$typeof:h0,_payload:{_status:-1,_result:e},_init:w0}};V.memo=function(e,t){return{$$typeof:m0,type:e,compare:t===void 0?null:t}};V.startTransition=function(e){var t=ui.transition;ui.transition={};try{e()}finally{ui.transition=t}};V.unstable_act=rd;V.useCallback=function(e,t){return je.current.useCallback(e,t)};V.useContext=function(e){return je.current.useContext(e)};V.useDebugValue=function(){};V.useDeferredValue=function(e){return je.current.useDeferredValue(e)};V.useEffect=function(e,t){return je.current.useEffect(e,t)};V.useId=function(){return je.current.useId()};V.useImperativeHandle=function(e,t,n){return je.current.useImperativeHandle(e,t,n)};V.useInsertionEffect=function(e,t){return je.current.useInsertionEffect(e,t)};V.useLayoutEffect=function(e,t){return je.current.useLayoutEffect(e,t)};V.useMemo=function(e,t){return je.current.useMemo(e,t)};V.useReducer=function(e,t,n){return je.current.useReducer(e,t,n)};V.useRef=function(e){return je.current.useRef(e)};V.useState=function(e){return je.current.useState(e)};V.useSyncExternalStore=function(e,t,n){return je.current.useSyncExternalStore(e,t,n)};V.useTransition=function(){return je.current.useTransition()};V.version="18.3.1";qf.exports=V;var v=qf.exports;const Cs=Ji(v),A0=a0({__proto__:null,default:Cs},[v]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var k0=v,S0=Symbol.for("react.element"),b0=Symbol.for("react.fragment"),E0=Object.prototype.hasOwnProperty,C0=k0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,N0={key:!0,ref:!0,__self:!0,__source:!0};function ad(e,t,n){var r,a={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)E0.call(t,r)&&!N0.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)a[r]===void 0&&(a[r]=t[r]);return{$$typeof:S0,type:e,key:i,ref:o,props:a,_owner:C0.current}}eo.Fragment=b0;eo.jsx=ad;eo.jsxs=ad;Qf.exports=eo;var y=Qf.exports,cl={},id={exports:{}},Ve={},od={exports:{}},ld={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(N,O){var D=N.length;N.push(O);e:for(;0<D;){var H=D-1>>>1,K=N[H];if(0<a(K,O))N[H]=O,N[D]=K,D=H;else break e}}function n(N){return N.length===0?null:N[0]}function r(N){if(N.length===0)return null;var O=N[0],D=N.pop();if(D!==O){N[0]=D;e:for(var H=0,K=N.length,tt=K>>>1;H<tt;){var Ie=2*(H+1)-1,ft=N[Ie],He=Ie+1,De=N[He];if(0>a(ft,D))He<K&&0>a(De,ft)?(N[H]=De,N[He]=D,H=He):(N[H]=ft,N[Ie]=D,H=Ie);else if(He<K&&0>a(De,D))N[H]=De,N[He]=D,H=He;else break e}}return O}function a(N,O){var D=N.sortIndex-O.sortIndex;return D!==0?D:N.id-O.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,s=o.now();e.unstable_now=function(){return o.now()-s}}var l=[],u=[],f=1,c=null,d=3,h=!1,w=!1,A=!1,k=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(N){for(var O=n(u);O!==null;){if(O.callback===null)r(u);else if(O.startTime<=N)r(u),O.sortIndex=O.expirationTime,t(l,O);else break;O=n(u)}}function x(N){if(A=!1,g(N),!w)if(n(l)!==null)w=!0,q(S);else{var O=n(u);O!==null&&$(x,O.startTime-N)}}function S(N,O){w=!1,A&&(A=!1,m(_),_=-1),h=!0;var D=d;try{for(g(O),c=n(l);c!==null&&(!(c.expirationTime>O)||N&&!E());){var H=c.callback;if(typeof H=="function"){c.callback=null,d=c.priorityLevel;var K=H(c.expirationTime<=O);O=e.unstable_now(),typeof K=="function"?c.callback=K:c===n(l)&&r(l),g(O)}else r(l);c=n(l)}if(c!==null)var tt=!0;else{var Ie=n(u);Ie!==null&&$(x,Ie.startTime-O),tt=!1}return tt}finally{c=null,d=D,h=!1}}var C=!1,b=null,_=-1,R=5,T=-1;function E(){return!(e.unstable_now()-T<R)}function j(){if(b!==null){var N=e.unstable_now();T=N;var O=!0;try{O=b(!0,N)}finally{O?I():(C=!1,b=null)}}else C=!1}var I;if(typeof p=="function")I=function(){p(j)};else if(typeof MessageChannel<"u"){var Y=new MessageChannel,U=Y.port2;Y.port1.onmessage=j,I=function(){U.postMessage(null)}}else I=function(){k(j,0)};function q(N){b=N,C||(C=!0,I())}function $(N,O){_=k(function(){N(e.unstable_now())},O)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(N){N.callback=null},e.unstable_continueExecution=function(){w||h||(w=!0,q(S))},e.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):R=0<N?Math.floor(1e3/N):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(N){switch(d){case 1:case 2:case 3:var O=3;break;default:O=d}var D=d;d=O;try{return N()}finally{d=D}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(N,O){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var D=d;d=N;try{return O()}finally{d=D}},e.unstable_scheduleCallback=function(N,O,D){var H=e.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?H+D:H):D=H,N){case 1:var K=-1;break;case 2:K=250;break;case 5:K=**********;break;case 4:K=1e4;break;default:K=5e3}return K=D+K,N={id:f++,callback:O,priorityLevel:N,startTime:D,expirationTime:K,sortIndex:-1},D>H?(N.sortIndex=D,t(u,N),n(l)===null&&N===n(u)&&(A?(m(_),_=-1):A=!0,$(x,D-H))):(N.sortIndex=K,t(l,N),w||h||(w=!0,q(S))),N},e.unstable_shouldYield=E,e.unstable_wrapCallback=function(N){var O=d;return function(){var D=d;d=O;try{return N.apply(this,arguments)}finally{d=D}}}})(ld);od.exports=ld;var _0=od.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var P0=v,Be=_0;function P(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var sd=new Set,ea={};function Tn(e,t){dr(e,t),dr(e+"Capture",t)}function dr(e,t){for(ea[e]=t,e=0;e<t.length;e++)sd.add(t[e])}var Nt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),fl=Object.prototype.hasOwnProperty,j0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Hu={},Yu={};function T0(e){return fl.call(Yu,e)?!0:fl.call(Hu,e)?!1:j0.test(e)?Yu[e]=!0:(Hu[e]=!0,!1)}function R0(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function L0(e,t,n,r){if(t===null||typeof t>"u"||R0(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Te(e,t,n,r,a,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var Se={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Se[e]=new Te(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Se[t]=new Te(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Se[e]=new Te(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Se[e]=new Te(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Se[e]=new Te(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Se[e]=new Te(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Se[e]=new Te(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Se[e]=new Te(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Se[e]=new Te(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ns=/[\-:]([a-z])/g;function _s(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ns,_s);Se[t]=new Te(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ns,_s);Se[t]=new Te(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ns,_s);Se[t]=new Te(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Se[e]=new Te(e,1,!1,e.toLowerCase(),null,!1,!1)});Se.xlinkHref=new Te("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Se[e]=new Te(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ps(e,t,n,r){var a=Se.hasOwnProperty(t)?Se[t]:null;(a!==null?a.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(L0(t,n,a,r)&&(n=null),r||a===null?T0(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=n===null?a.type===3?!1:"":n:(t=a.attributeName,r=a.attributeNamespace,n===null?e.removeAttribute(t):(a=a.type,n=a===3||a===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Mt=P0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,za=Symbol.for("react.element"),Vn=Symbol.for("react.portal"),Wn=Symbol.for("react.fragment"),js=Symbol.for("react.strict_mode"),dl=Symbol.for("react.profiler"),ud=Symbol.for("react.provider"),cd=Symbol.for("react.context"),Ts=Symbol.for("react.forward_ref"),pl=Symbol.for("react.suspense"),ml=Symbol.for("react.suspense_list"),Rs=Symbol.for("react.memo"),Dt=Symbol.for("react.lazy"),fd=Symbol.for("react.offscreen"),Qu=Symbol.iterator;function Cr(e){return e===null||typeof e!="object"?null:(e=Qu&&e[Qu]||e["@@iterator"],typeof e=="function"?e:null)}var se=Object.assign,No;function zr(e){if(No===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);No=t&&t[1]||""}return`
`+No+e}var _o=!1;function Po(e,t){if(!e||_o)return"";_o=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var a=u.stack.split(`
`),i=r.stack.split(`
`),o=a.length-1,s=i.length-1;1<=o&&0<=s&&a[o]!==i[s];)s--;for(;1<=o&&0<=s;o--,s--)if(a[o]!==i[s]){if(o!==1||s!==1)do if(o--,s--,0>s||a[o]!==i[s]){var l=`
`+a[o].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=o&&0<=s);break}}}finally{_o=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?zr(e):""}function M0(e){switch(e.tag){case 5:return zr(e.type);case 16:return zr("Lazy");case 13:return zr("Suspense");case 19:return zr("SuspenseList");case 0:case 2:case 15:return e=Po(e.type,!1),e;case 11:return e=Po(e.type.render,!1),e;case 1:return e=Po(e.type,!0),e;default:return""}}function hl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Wn:return"Fragment";case Vn:return"Portal";case dl:return"Profiler";case js:return"StrictMode";case pl:return"Suspense";case ml:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case cd:return(e.displayName||"Context")+".Consumer";case ud:return(e._context.displayName||"Context")+".Provider";case Ts:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Rs:return t=e.displayName||null,t!==null?t:hl(e.type)||"Memo";case Dt:t=e._payload,e=e._init;try{return hl(e(t))}catch{}}return null}function O0(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return hl(t);case 8:return t===js?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function en(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function dd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function z0(e){var t=dd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var a=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ia(e){e._valueTracker||(e._valueTracker=z0(e))}function pd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=dd(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ki(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function gl(e,t){var n=t.checked;return se({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function qu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=en(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function md(e,t){t=t.checked,t!=null&&Ps(e,"checked",t,!1)}function vl(e,t){md(e,t);var n=en(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?yl(e,t.type,n):t.hasOwnProperty("defaultValue")&&yl(e,t.type,en(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ku(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function yl(e,t,n){(t!=="number"||ki(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Ir=Array.isArray;function ir(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+en(n),t=null,a=0;a<e.length;a++){if(e[a].value===n){e[a].selected=!0,r&&(e[a].defaultSelected=!0);return}t!==null||e[a].disabled||(t=e[a])}t!==null&&(t.selected=!0)}}function wl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(P(91));return se({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Xu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(P(92));if(Ir(n)){if(1<n.length)throw Error(P(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:en(n)}}function hd(e,t){var n=en(t.value),r=en(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Zu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function gd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function xl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?gd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Da,vd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,a){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,a)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Da=Da||document.createElement("div"),Da.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Da.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ta(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var $r={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},I0=["Webkit","ms","Moz","O"];Object.keys($r).forEach(function(e){I0.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),$r[t]=$r[e]})});function yd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||$r.hasOwnProperty(e)&&$r[e]?(""+t).trim():t+"px"}function wd(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,a=yd(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}var D0=se({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Al(e,t){if(t){if(D0[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(P(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(P(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(P(61))}if(t.style!=null&&typeof t.style!="object")throw Error(P(62))}}function kl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Sl=null;function Ls(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var bl=null,or=null,lr=null;function Ju(e){if(e=_a(e)){if(typeof bl!="function")throw Error(P(280));var t=e.stateNode;t&&(t=io(t),bl(e.stateNode,e.type,t))}}function xd(e){or?lr?lr.push(e):lr=[e]:or=e}function Ad(){if(or){var e=or,t=lr;if(lr=or=null,Ju(e),t)for(e=0;e<t.length;e++)Ju(t[e])}}function kd(e,t){return e(t)}function Sd(){}var jo=!1;function bd(e,t,n){if(jo)return e(t,n);jo=!0;try{return kd(e,t,n)}finally{jo=!1,(or!==null||lr!==null)&&(Sd(),Ad())}}function na(e,t){var n=e.stateNode;if(n===null)return null;var r=io(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(P(231,t,typeof n));return n}var El=!1;if(Nt)try{var Nr={};Object.defineProperty(Nr,"passive",{get:function(){El=!0}}),window.addEventListener("test",Nr,Nr),window.removeEventListener("test",Nr,Nr)}catch{El=!1}function F0(e,t,n,r,a,i,o,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(f){this.onError(f)}}var Br=!1,Si=null,bi=!1,Cl=null,U0={onError:function(e){Br=!0,Si=e}};function $0(e,t,n,r,a,i,o,s,l){Br=!1,Si=null,F0.apply(U0,arguments)}function B0(e,t,n,r,a,i,o,s,l){if($0.apply(this,arguments),Br){if(Br){var u=Si;Br=!1,Si=null}else throw Error(P(198));bi||(bi=!0,Cl=u)}}function Rn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Ed(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ec(e){if(Rn(e)!==e)throw Error(P(188))}function V0(e){var t=e.alternate;if(!t){if(t=Rn(e),t===null)throw Error(P(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(a===null)break;var i=a.alternate;if(i===null){if(r=a.return,r!==null){n=r;continue}break}if(a.child===i.child){for(i=a.child;i;){if(i===n)return ec(a),e;if(i===r)return ec(a),t;i=i.sibling}throw Error(P(188))}if(n.return!==r.return)n=a,r=i;else{for(var o=!1,s=a.child;s;){if(s===n){o=!0,n=a,r=i;break}if(s===r){o=!0,r=a,n=i;break}s=s.sibling}if(!o){for(s=i.child;s;){if(s===n){o=!0,n=i,r=a;break}if(s===r){o=!0,r=i,n=a;break}s=s.sibling}if(!o)throw Error(P(189))}}if(n.alternate!==r)throw Error(P(190))}if(n.tag!==3)throw Error(P(188));return n.stateNode.current===n?e:t}function Cd(e){return e=V0(e),e!==null?Nd(e):null}function Nd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Nd(e);if(t!==null)return t;e=e.sibling}return null}var _d=Be.unstable_scheduleCallback,tc=Be.unstable_cancelCallback,W0=Be.unstable_shouldYield,G0=Be.unstable_requestPaint,fe=Be.unstable_now,H0=Be.unstable_getCurrentPriorityLevel,Ms=Be.unstable_ImmediatePriority,Pd=Be.unstable_UserBlockingPriority,Ei=Be.unstable_NormalPriority,Y0=Be.unstable_LowPriority,jd=Be.unstable_IdlePriority,to=null,wt=null;function Q0(e){if(wt&&typeof wt.onCommitFiberRoot=="function")try{wt.onCommitFiberRoot(to,e,void 0,(e.current.flags&128)===128)}catch{}}var st=Math.clz32?Math.clz32:X0,q0=Math.log,K0=Math.LN2;function X0(e){return e>>>=0,e===0?32:31-(q0(e)/K0|0)|0}var Fa=64,Ua=4194304;function Dr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ci(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,a=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var s=o&~a;s!==0?r=Dr(s):(i&=o,i!==0&&(r=Dr(i)))}else o=n&~a,o!==0?r=Dr(o):i!==0&&(r=Dr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&a)&&(a=r&-r,i=t&-t,a>=i||a===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-st(t),a=1<<n,r|=e[n],t&=~a;return r}function Z0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function J0(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-st(i),s=1<<o,l=a[o];l===-1?(!(s&n)||s&r)&&(a[o]=Z0(s,t)):l<=t&&(e.expiredLanes|=s),i&=~s}}function Nl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Td(){var e=Fa;return Fa<<=1,!(Fa&4194240)&&(Fa=64),e}function To(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ca(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-st(t),e[t]=n}function eh(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-st(n),i=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~i}}function Os(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-st(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var Q=0;function Rd(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Ld,zs,Md,Od,zd,_l=!1,$a=[],Gt=null,Ht=null,Yt=null,ra=new Map,aa=new Map,Ut=[],th="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function nc(e,t){switch(e){case"focusin":case"focusout":Gt=null;break;case"dragenter":case"dragleave":Ht=null;break;case"mouseover":case"mouseout":Yt=null;break;case"pointerover":case"pointerout":ra.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":aa.delete(t.pointerId)}}function _r(e,t,n,r,a,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[a]},t!==null&&(t=_a(t),t!==null&&zs(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,a!==null&&t.indexOf(a)===-1&&t.push(a),e)}function nh(e,t,n,r,a){switch(t){case"focusin":return Gt=_r(Gt,e,t,n,r,a),!0;case"dragenter":return Ht=_r(Ht,e,t,n,r,a),!0;case"mouseover":return Yt=_r(Yt,e,t,n,r,a),!0;case"pointerover":var i=a.pointerId;return ra.set(i,_r(ra.get(i)||null,e,t,n,r,a)),!0;case"gotpointercapture":return i=a.pointerId,aa.set(i,_r(aa.get(i)||null,e,t,n,r,a)),!0}return!1}function Id(e){var t=gn(e.target);if(t!==null){var n=Rn(t);if(n!==null){if(t=n.tag,t===13){if(t=Ed(n),t!==null){e.blockedOn=t,zd(e.priority,function(){Md(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ci(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Pl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Sl=r,n.target.dispatchEvent(r),Sl=null}else return t=_a(n),t!==null&&zs(t),e.blockedOn=n,!1;t.shift()}return!0}function rc(e,t,n){ci(e)&&n.delete(t)}function rh(){_l=!1,Gt!==null&&ci(Gt)&&(Gt=null),Ht!==null&&ci(Ht)&&(Ht=null),Yt!==null&&ci(Yt)&&(Yt=null),ra.forEach(rc),aa.forEach(rc)}function Pr(e,t){e.blockedOn===t&&(e.blockedOn=null,_l||(_l=!0,Be.unstable_scheduleCallback(Be.unstable_NormalPriority,rh)))}function ia(e){function t(a){return Pr(a,e)}if(0<$a.length){Pr($a[0],e);for(var n=1;n<$a.length;n++){var r=$a[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Gt!==null&&Pr(Gt,e),Ht!==null&&Pr(Ht,e),Yt!==null&&Pr(Yt,e),ra.forEach(t),aa.forEach(t),n=0;n<Ut.length;n++)r=Ut[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Ut.length&&(n=Ut[0],n.blockedOn===null);)Id(n),n.blockedOn===null&&Ut.shift()}var sr=Mt.ReactCurrentBatchConfig,Ni=!0;function ah(e,t,n,r){var a=Q,i=sr.transition;sr.transition=null;try{Q=1,Is(e,t,n,r)}finally{Q=a,sr.transition=i}}function ih(e,t,n,r){var a=Q,i=sr.transition;sr.transition=null;try{Q=4,Is(e,t,n,r)}finally{Q=a,sr.transition=i}}function Is(e,t,n,r){if(Ni){var a=Pl(e,t,n,r);if(a===null)$o(e,t,r,_i,n),nc(e,r);else if(nh(a,e,t,n,r))r.stopPropagation();else if(nc(e,r),t&4&&-1<th.indexOf(e)){for(;a!==null;){var i=_a(a);if(i!==null&&Ld(i),i=Pl(e,t,n,r),i===null&&$o(e,t,r,_i,n),i===a)break;a=i}a!==null&&r.stopPropagation()}else $o(e,t,r,null,n)}}var _i=null;function Pl(e,t,n,r){if(_i=null,e=Ls(r),e=gn(e),e!==null)if(t=Rn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Ed(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return _i=e,null}function Dd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(H0()){case Ms:return 1;case Pd:return 4;case Ei:case Y0:return 16;case jd:return 536870912;default:return 16}default:return 16}}var Bt=null,Ds=null,fi=null;function Fd(){if(fi)return fi;var e,t=Ds,n=t.length,r,a="value"in Bt?Bt.value:Bt.textContent,i=a.length;for(e=0;e<n&&t[e]===a[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===a[i-r];r++);return fi=a.slice(e,1<r?1-r:void 0)}function di(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ba(){return!0}function ac(){return!1}function We(e){function t(n,r,a,i,o){this._reactName=n,this._targetInst=a,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(i):i[s]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Ba:ac,this.isPropagationStopped=ac,this}return se(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ba)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ba)},persist:function(){},isPersistent:Ba}),t}var Ar={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Fs=We(Ar),Na=se({},Ar,{view:0,detail:0}),oh=We(Na),Ro,Lo,jr,no=se({},Na,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Us,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==jr&&(jr&&e.type==="mousemove"?(Ro=e.screenX-jr.screenX,Lo=e.screenY-jr.screenY):Lo=Ro=0,jr=e),Ro)},movementY:function(e){return"movementY"in e?e.movementY:Lo}}),ic=We(no),lh=se({},no,{dataTransfer:0}),sh=We(lh),uh=se({},Na,{relatedTarget:0}),Mo=We(uh),ch=se({},Ar,{animationName:0,elapsedTime:0,pseudoElement:0}),fh=We(ch),dh=se({},Ar,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ph=We(dh),mh=se({},Ar,{data:0}),oc=We(mh),hh={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},gh={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},vh={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function yh(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=vh[e])?!!t[e]:!1}function Us(){return yh}var wh=se({},Na,{key:function(e){if(e.key){var t=hh[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=di(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?gh[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Us,charCode:function(e){return e.type==="keypress"?di(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?di(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),xh=We(wh),Ah=se({},no,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),lc=We(Ah),kh=se({},Na,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Us}),Sh=We(kh),bh=se({},Ar,{propertyName:0,elapsedTime:0,pseudoElement:0}),Eh=We(bh),Ch=se({},no,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Nh=We(Ch),_h=[9,13,27,32],$s=Nt&&"CompositionEvent"in window,Vr=null;Nt&&"documentMode"in document&&(Vr=document.documentMode);var Ph=Nt&&"TextEvent"in window&&!Vr,Ud=Nt&&(!$s||Vr&&8<Vr&&11>=Vr),sc=" ",uc=!1;function $d(e,t){switch(e){case"keyup":return _h.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Bd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Gn=!1;function jh(e,t){switch(e){case"compositionend":return Bd(t);case"keypress":return t.which!==32?null:(uc=!0,sc);case"textInput":return e=t.data,e===sc&&uc?null:e;default:return null}}function Th(e,t){if(Gn)return e==="compositionend"||!$s&&$d(e,t)?(e=Fd(),fi=Ds=Bt=null,Gn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ud&&t.locale!=="ko"?null:t.data;default:return null}}var Rh={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function cc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Rh[e.type]:t==="textarea"}function Vd(e,t,n,r){xd(r),t=Pi(t,"onChange"),0<t.length&&(n=new Fs("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Wr=null,oa=null;function Lh(e){ep(e,0)}function ro(e){var t=Qn(e);if(pd(t))return e}function Mh(e,t){if(e==="change")return t}var Wd=!1;if(Nt){var Oo;if(Nt){var zo="oninput"in document;if(!zo){var fc=document.createElement("div");fc.setAttribute("oninput","return;"),zo=typeof fc.oninput=="function"}Oo=zo}else Oo=!1;Wd=Oo&&(!document.documentMode||9<document.documentMode)}function dc(){Wr&&(Wr.detachEvent("onpropertychange",Gd),oa=Wr=null)}function Gd(e){if(e.propertyName==="value"&&ro(oa)){var t=[];Vd(t,oa,e,Ls(e)),bd(Lh,t)}}function Oh(e,t,n){e==="focusin"?(dc(),Wr=t,oa=n,Wr.attachEvent("onpropertychange",Gd)):e==="focusout"&&dc()}function zh(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ro(oa)}function Ih(e,t){if(e==="click")return ro(t)}function Dh(e,t){if(e==="input"||e==="change")return ro(t)}function Fh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ct=typeof Object.is=="function"?Object.is:Fh;function la(e,t){if(ct(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!fl.call(t,a)||!ct(e[a],t[a]))return!1}return!0}function pc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function mc(e,t){var n=pc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=pc(n)}}function Hd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Hd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Yd(){for(var e=window,t=ki();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ki(e.document)}return t}function Bs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Uh(e){var t=Yd(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Hd(n.ownerDocument.documentElement,n)){if(r!==null&&Bs(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var a=n.textContent.length,i=Math.min(r.start,a);r=r.end===void 0?i:Math.min(r.end,a),!e.extend&&i>r&&(a=r,r=i,i=a),a=mc(n,i);var o=mc(n,r);a&&o&&(e.rangeCount!==1||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(a.node,a.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var $h=Nt&&"documentMode"in document&&11>=document.documentMode,Hn=null,jl=null,Gr=null,Tl=!1;function hc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Tl||Hn==null||Hn!==ki(r)||(r=Hn,"selectionStart"in r&&Bs(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Gr&&la(Gr,r)||(Gr=r,r=Pi(jl,"onSelect"),0<r.length&&(t=new Fs("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Hn)))}function Va(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Yn={animationend:Va("Animation","AnimationEnd"),animationiteration:Va("Animation","AnimationIteration"),animationstart:Va("Animation","AnimationStart"),transitionend:Va("Transition","TransitionEnd")},Io={},Qd={};Nt&&(Qd=document.createElement("div").style,"AnimationEvent"in window||(delete Yn.animationend.animation,delete Yn.animationiteration.animation,delete Yn.animationstart.animation),"TransitionEvent"in window||delete Yn.transitionend.transition);function ao(e){if(Io[e])return Io[e];if(!Yn[e])return e;var t=Yn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Qd)return Io[e]=t[n];return e}var qd=ao("animationend"),Kd=ao("animationiteration"),Xd=ao("animationstart"),Zd=ao("transitionend"),Jd=new Map,gc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function on(e,t){Jd.set(e,t),Tn(t,[e])}for(var Do=0;Do<gc.length;Do++){var Fo=gc[Do],Bh=Fo.toLowerCase(),Vh=Fo[0].toUpperCase()+Fo.slice(1);on(Bh,"on"+Vh)}on(qd,"onAnimationEnd");on(Kd,"onAnimationIteration");on(Xd,"onAnimationStart");on("dblclick","onDoubleClick");on("focusin","onFocus");on("focusout","onBlur");on(Zd,"onTransitionEnd");dr("onMouseEnter",["mouseout","mouseover"]);dr("onMouseLeave",["mouseout","mouseover"]);dr("onPointerEnter",["pointerout","pointerover"]);dr("onPointerLeave",["pointerout","pointerover"]);Tn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Tn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Tn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Tn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Tn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Tn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Fr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Wh=new Set("cancel close invalid load scroll toggle".split(" ").concat(Fr));function vc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,B0(r,t,void 0,e),e.currentTarget=null}function ep(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var s=r[o],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==i&&a.isPropagationStopped())break e;vc(a,s,u),i=l}else for(o=0;o<r.length;o++){if(s=r[o],l=s.instance,u=s.currentTarget,s=s.listener,l!==i&&a.isPropagationStopped())break e;vc(a,s,u),i=l}}}if(bi)throw e=Cl,bi=!1,Cl=null,e}function ee(e,t){var n=t[zl];n===void 0&&(n=t[zl]=new Set);var r=e+"__bubble";n.has(r)||(tp(t,e,2,!1),n.add(r))}function Uo(e,t,n){var r=0;t&&(r|=4),tp(n,e,r,t)}var Wa="_reactListening"+Math.random().toString(36).slice(2);function sa(e){if(!e[Wa]){e[Wa]=!0,sd.forEach(function(n){n!=="selectionchange"&&(Wh.has(n)||Uo(n,!1,e),Uo(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Wa]||(t[Wa]=!0,Uo("selectionchange",!1,t))}}function tp(e,t,n,r){switch(Dd(t)){case 1:var a=ah;break;case 4:a=ih;break;default:a=Is}n=a.bind(null,t,n,e),a=void 0,!El||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(a=!0),r?a!==void 0?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):a!==void 0?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function $o(e,t,n,r,a){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var s=r.stateNode.containerInfo;if(s===a||s.nodeType===8&&s.parentNode===a)break;if(o===4)for(o=r.return;o!==null;){var l=o.tag;if((l===3||l===4)&&(l=o.stateNode.containerInfo,l===a||l.nodeType===8&&l.parentNode===a))return;o=o.return}for(;s!==null;){if(o=gn(s),o===null)return;if(l=o.tag,l===5||l===6){r=i=o;continue e}s=s.parentNode}}r=r.return}bd(function(){var u=i,f=Ls(n),c=[];e:{var d=Jd.get(e);if(d!==void 0){var h=Fs,w=e;switch(e){case"keypress":if(di(n)===0)break e;case"keydown":case"keyup":h=xh;break;case"focusin":w="focus",h=Mo;break;case"focusout":w="blur",h=Mo;break;case"beforeblur":case"afterblur":h=Mo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":h=ic;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":h=sh;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":h=Sh;break;case qd:case Kd:case Xd:h=fh;break;case Zd:h=Eh;break;case"scroll":h=oh;break;case"wheel":h=Nh;break;case"copy":case"cut":case"paste":h=ph;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":h=lc}var A=(t&4)!==0,k=!A&&e==="scroll",m=A?d!==null?d+"Capture":null:d;A=[];for(var p=u,g;p!==null;){g=p;var x=g.stateNode;if(g.tag===5&&x!==null&&(g=x,m!==null&&(x=na(p,m),x!=null&&A.push(ua(p,x,g)))),k)break;p=p.return}0<A.length&&(d=new h(d,w,null,n,f),c.push({event:d,listeners:A}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",h=e==="mouseout"||e==="pointerout",d&&n!==Sl&&(w=n.relatedTarget||n.fromElement)&&(gn(w)||w[_t]))break e;if((h||d)&&(d=f.window===f?f:(d=f.ownerDocument)?d.defaultView||d.parentWindow:window,h?(w=n.relatedTarget||n.toElement,h=u,w=w?gn(w):null,w!==null&&(k=Rn(w),w!==k||w.tag!==5&&w.tag!==6)&&(w=null)):(h=null,w=u),h!==w)){if(A=ic,x="onMouseLeave",m="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(A=lc,x="onPointerLeave",m="onPointerEnter",p="pointer"),k=h==null?d:Qn(h),g=w==null?d:Qn(w),d=new A(x,p+"leave",h,n,f),d.target=k,d.relatedTarget=g,x=null,gn(f)===u&&(A=new A(m,p+"enter",w,n,f),A.target=g,A.relatedTarget=k,x=A),k=x,h&&w)t:{for(A=h,m=w,p=0,g=A;g;g=$n(g))p++;for(g=0,x=m;x;x=$n(x))g++;for(;0<p-g;)A=$n(A),p--;for(;0<g-p;)m=$n(m),g--;for(;p--;){if(A===m||m!==null&&A===m.alternate)break t;A=$n(A),m=$n(m)}A=null}else A=null;h!==null&&yc(c,d,h,A,!1),w!==null&&k!==null&&yc(c,k,w,A,!0)}}e:{if(d=u?Qn(u):window,h=d.nodeName&&d.nodeName.toLowerCase(),h==="select"||h==="input"&&d.type==="file")var S=Mh;else if(cc(d))if(Wd)S=Dh;else{S=zh;var C=Oh}else(h=d.nodeName)&&h.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(S=Ih);if(S&&(S=S(e,u))){Vd(c,S,n,f);break e}C&&C(e,d,u),e==="focusout"&&(C=d._wrapperState)&&C.controlled&&d.type==="number"&&yl(d,"number",d.value)}switch(C=u?Qn(u):window,e){case"focusin":(cc(C)||C.contentEditable==="true")&&(Hn=C,jl=u,Gr=null);break;case"focusout":Gr=jl=Hn=null;break;case"mousedown":Tl=!0;break;case"contextmenu":case"mouseup":case"dragend":Tl=!1,hc(c,n,f);break;case"selectionchange":if($h)break;case"keydown":case"keyup":hc(c,n,f)}var b;if($s)e:{switch(e){case"compositionstart":var _="onCompositionStart";break e;case"compositionend":_="onCompositionEnd";break e;case"compositionupdate":_="onCompositionUpdate";break e}_=void 0}else Gn?$d(e,n)&&(_="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(_="onCompositionStart");_&&(Ud&&n.locale!=="ko"&&(Gn||_!=="onCompositionStart"?_==="onCompositionEnd"&&Gn&&(b=Fd()):(Bt=f,Ds="value"in Bt?Bt.value:Bt.textContent,Gn=!0)),C=Pi(u,_),0<C.length&&(_=new oc(_,e,null,n,f),c.push({event:_,listeners:C}),b?_.data=b:(b=Bd(n),b!==null&&(_.data=b)))),(b=Ph?jh(e,n):Th(e,n))&&(u=Pi(u,"onBeforeInput"),0<u.length&&(f=new oc("onBeforeInput","beforeinput",null,n,f),c.push({event:f,listeners:u}),f.data=b))}ep(c,t)})}function ua(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Pi(e,t){for(var n=t+"Capture",r=[];e!==null;){var a=e,i=a.stateNode;a.tag===5&&i!==null&&(a=i,i=na(e,n),i!=null&&r.unshift(ua(e,i,a)),i=na(e,t),i!=null&&r.push(ua(e,i,a))),e=e.return}return r}function $n(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function yc(e,t,n,r,a){for(var i=t._reactName,o=[];n!==null&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(l!==null&&l===r)break;s.tag===5&&u!==null&&(s=u,a?(l=na(n,i),l!=null&&o.unshift(ua(n,l,s))):a||(l=na(n,i),l!=null&&o.push(ua(n,l,s)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Gh=/\r\n?/g,Hh=/\u0000|\uFFFD/g;function wc(e){return(typeof e=="string"?e:""+e).replace(Gh,`
`).replace(Hh,"")}function Ga(e,t,n){if(t=wc(t),wc(e)!==t&&n)throw Error(P(425))}function ji(){}var Rl=null,Ll=null;function Ml(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ol=typeof setTimeout=="function"?setTimeout:void 0,Yh=typeof clearTimeout=="function"?clearTimeout:void 0,xc=typeof Promise=="function"?Promise:void 0,Qh=typeof queueMicrotask=="function"?queueMicrotask:typeof xc<"u"?function(e){return xc.resolve(null).then(e).catch(qh)}:Ol;function qh(e){setTimeout(function(){throw e})}function Bo(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&a.nodeType===8)if(n=a.data,n==="/$"){if(r===0){e.removeChild(a),ia(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=a}while(n);ia(t)}function Qt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Ac(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var kr=Math.random().toString(36).slice(2),vt="__reactFiber$"+kr,ca="__reactProps$"+kr,_t="__reactContainer$"+kr,zl="__reactEvents$"+kr,Kh="__reactListeners$"+kr,Xh="__reactHandles$"+kr;function gn(e){var t=e[vt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[_t]||n[vt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Ac(e);e!==null;){if(n=e[vt])return n;e=Ac(e)}return t}e=n,n=e.parentNode}return null}function _a(e){return e=e[vt]||e[_t],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Qn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(P(33))}function io(e){return e[ca]||null}var Il=[],qn=-1;function ln(e){return{current:e}}function ne(e){0>qn||(e.current=Il[qn],Il[qn]=null,qn--)}function Z(e,t){qn++,Il[qn]=e.current,e.current=t}var tn={},Ne=ln(tn),Me=ln(!1),Sn=tn;function pr(e,t){var n=e.type.contextTypes;if(!n)return tn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a={},i;for(i in n)a[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function Oe(e){return e=e.childContextTypes,e!=null}function Ti(){ne(Me),ne(Ne)}function kc(e,t,n){if(Ne.current!==tn)throw Error(P(168));Z(Ne,t),Z(Me,n)}function np(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var a in r)if(!(a in t))throw Error(P(108,O0(e)||"Unknown",a));return se({},n,r)}function Ri(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||tn,Sn=Ne.current,Z(Ne,e),Z(Me,Me.current),!0}function Sc(e,t,n){var r=e.stateNode;if(!r)throw Error(P(169));n?(e=np(e,t,Sn),r.__reactInternalMemoizedMergedChildContext=e,ne(Me),ne(Ne),Z(Ne,e)):ne(Me),Z(Me,n)}var St=null,oo=!1,Vo=!1;function rp(e){St===null?St=[e]:St.push(e)}function Zh(e){oo=!0,rp(e)}function sn(){if(!Vo&&St!==null){Vo=!0;var e=0,t=Q;try{var n=St;for(Q=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}St=null,oo=!1}catch(a){throw St!==null&&(St=St.slice(e+1)),_d(Ms,sn),a}finally{Q=t,Vo=!1}}return null}var Kn=[],Xn=0,Li=null,Mi=0,Qe=[],qe=0,bn=null,bt=1,Et="";function mn(e,t){Kn[Xn++]=Mi,Kn[Xn++]=Li,Li=e,Mi=t}function ap(e,t,n){Qe[qe++]=bt,Qe[qe++]=Et,Qe[qe++]=bn,bn=e;var r=bt;e=Et;var a=32-st(r)-1;r&=~(1<<a),n+=1;var i=32-st(t)+a;if(30<i){var o=a-a%5;i=(r&(1<<o)-1).toString(32),r>>=o,a-=o,bt=1<<32-st(t)+a|n<<a|r,Et=i+e}else bt=1<<i|n<<a|r,Et=e}function Vs(e){e.return!==null&&(mn(e,1),ap(e,1,0))}function Ws(e){for(;e===Li;)Li=Kn[--Xn],Kn[Xn]=null,Mi=Kn[--Xn],Kn[Xn]=null;for(;e===bn;)bn=Qe[--qe],Qe[qe]=null,Et=Qe[--qe],Qe[qe]=null,bt=Qe[--qe],Qe[qe]=null}var $e=null,Ue=null,ae=!1,ot=null;function ip(e,t){var n=Xe(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function bc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,$e=e,Ue=Qt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,$e=e,Ue=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=bn!==null?{id:bt,overflow:Et}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Xe(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,$e=e,Ue=null,!0):!1;default:return!1}}function Dl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Fl(e){if(ae){var t=Ue;if(t){var n=t;if(!bc(e,t)){if(Dl(e))throw Error(P(418));t=Qt(n.nextSibling);var r=$e;t&&bc(e,t)?ip(r,n):(e.flags=e.flags&-4097|2,ae=!1,$e=e)}}else{if(Dl(e))throw Error(P(418));e.flags=e.flags&-4097|2,ae=!1,$e=e}}}function Ec(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;$e=e}function Ha(e){if(e!==$e)return!1;if(!ae)return Ec(e),ae=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ml(e.type,e.memoizedProps)),t&&(t=Ue)){if(Dl(e))throw op(),Error(P(418));for(;t;)ip(e,t),t=Qt(t.nextSibling)}if(Ec(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(P(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ue=Qt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ue=null}}else Ue=$e?Qt(e.stateNode.nextSibling):null;return!0}function op(){for(var e=Ue;e;)e=Qt(e.nextSibling)}function mr(){Ue=$e=null,ae=!1}function Gs(e){ot===null?ot=[e]:ot.push(e)}var Jh=Mt.ReactCurrentBatchConfig;function Tr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(P(309));var r=n.stateNode}if(!r)throw Error(P(147,e));var a=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var s=a.refs;o===null?delete s[i]:s[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(P(284));if(!n._owner)throw Error(P(290,e))}return e}function Ya(e,t){throw e=Object.prototype.toString.call(t),Error(P(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Cc(e){var t=e._init;return t(e._payload)}function lp(e){function t(m,p){if(e){var g=m.deletions;g===null?(m.deletions=[p],m.flags|=16):g.push(p)}}function n(m,p){if(!e)return null;for(;p!==null;)t(m,p),p=p.sibling;return null}function r(m,p){for(m=new Map;p!==null;)p.key!==null?m.set(p.key,p):m.set(p.index,p),p=p.sibling;return m}function a(m,p){return m=Zt(m,p),m.index=0,m.sibling=null,m}function i(m,p,g){return m.index=g,e?(g=m.alternate,g!==null?(g=g.index,g<p?(m.flags|=2,p):g):(m.flags|=2,p)):(m.flags|=1048576,p)}function o(m){return e&&m.alternate===null&&(m.flags|=2),m}function s(m,p,g,x){return p===null||p.tag!==6?(p=Ko(g,m.mode,x),p.return=m,p):(p=a(p,g),p.return=m,p)}function l(m,p,g,x){var S=g.type;return S===Wn?f(m,p,g.props.children,x,g.key):p!==null&&(p.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Dt&&Cc(S)===p.type)?(x=a(p,g.props),x.ref=Tr(m,p,g),x.return=m,x):(x=wi(g.type,g.key,g.props,null,m.mode,x),x.ref=Tr(m,p,g),x.return=m,x)}function u(m,p,g,x){return p===null||p.tag!==4||p.stateNode.containerInfo!==g.containerInfo||p.stateNode.implementation!==g.implementation?(p=Xo(g,m.mode,x),p.return=m,p):(p=a(p,g.children||[]),p.return=m,p)}function f(m,p,g,x,S){return p===null||p.tag!==7?(p=kn(g,m.mode,x,S),p.return=m,p):(p=a(p,g),p.return=m,p)}function c(m,p,g){if(typeof p=="string"&&p!==""||typeof p=="number")return p=Ko(""+p,m.mode,g),p.return=m,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case za:return g=wi(p.type,p.key,p.props,null,m.mode,g),g.ref=Tr(m,null,p),g.return=m,g;case Vn:return p=Xo(p,m.mode,g),p.return=m,p;case Dt:var x=p._init;return c(m,x(p._payload),g)}if(Ir(p)||Cr(p))return p=kn(p,m.mode,g,null),p.return=m,p;Ya(m,p)}return null}function d(m,p,g,x){var S=p!==null?p.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return S!==null?null:s(m,p,""+g,x);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case za:return g.key===S?l(m,p,g,x):null;case Vn:return g.key===S?u(m,p,g,x):null;case Dt:return S=g._init,d(m,p,S(g._payload),x)}if(Ir(g)||Cr(g))return S!==null?null:f(m,p,g,x,null);Ya(m,g)}return null}function h(m,p,g,x,S){if(typeof x=="string"&&x!==""||typeof x=="number")return m=m.get(g)||null,s(p,m,""+x,S);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case za:return m=m.get(x.key===null?g:x.key)||null,l(p,m,x,S);case Vn:return m=m.get(x.key===null?g:x.key)||null,u(p,m,x,S);case Dt:var C=x._init;return h(m,p,g,C(x._payload),S)}if(Ir(x)||Cr(x))return m=m.get(g)||null,f(p,m,x,S,null);Ya(p,x)}return null}function w(m,p,g,x){for(var S=null,C=null,b=p,_=p=0,R=null;b!==null&&_<g.length;_++){b.index>_?(R=b,b=null):R=b.sibling;var T=d(m,b,g[_],x);if(T===null){b===null&&(b=R);break}e&&b&&T.alternate===null&&t(m,b),p=i(T,p,_),C===null?S=T:C.sibling=T,C=T,b=R}if(_===g.length)return n(m,b),ae&&mn(m,_),S;if(b===null){for(;_<g.length;_++)b=c(m,g[_],x),b!==null&&(p=i(b,p,_),C===null?S=b:C.sibling=b,C=b);return ae&&mn(m,_),S}for(b=r(m,b);_<g.length;_++)R=h(b,m,_,g[_],x),R!==null&&(e&&R.alternate!==null&&b.delete(R.key===null?_:R.key),p=i(R,p,_),C===null?S=R:C.sibling=R,C=R);return e&&b.forEach(function(E){return t(m,E)}),ae&&mn(m,_),S}function A(m,p,g,x){var S=Cr(g);if(typeof S!="function")throw Error(P(150));if(g=S.call(g),g==null)throw Error(P(151));for(var C=S=null,b=p,_=p=0,R=null,T=g.next();b!==null&&!T.done;_++,T=g.next()){b.index>_?(R=b,b=null):R=b.sibling;var E=d(m,b,T.value,x);if(E===null){b===null&&(b=R);break}e&&b&&E.alternate===null&&t(m,b),p=i(E,p,_),C===null?S=E:C.sibling=E,C=E,b=R}if(T.done)return n(m,b),ae&&mn(m,_),S;if(b===null){for(;!T.done;_++,T=g.next())T=c(m,T.value,x),T!==null&&(p=i(T,p,_),C===null?S=T:C.sibling=T,C=T);return ae&&mn(m,_),S}for(b=r(m,b);!T.done;_++,T=g.next())T=h(b,m,_,T.value,x),T!==null&&(e&&T.alternate!==null&&b.delete(T.key===null?_:T.key),p=i(T,p,_),C===null?S=T:C.sibling=T,C=T);return e&&b.forEach(function(j){return t(m,j)}),ae&&mn(m,_),S}function k(m,p,g,x){if(typeof g=="object"&&g!==null&&g.type===Wn&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case za:e:{for(var S=g.key,C=p;C!==null;){if(C.key===S){if(S=g.type,S===Wn){if(C.tag===7){n(m,C.sibling),p=a(C,g.props.children),p.return=m,m=p;break e}}else if(C.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Dt&&Cc(S)===C.type){n(m,C.sibling),p=a(C,g.props),p.ref=Tr(m,C,g),p.return=m,m=p;break e}n(m,C);break}else t(m,C);C=C.sibling}g.type===Wn?(p=kn(g.props.children,m.mode,x,g.key),p.return=m,m=p):(x=wi(g.type,g.key,g.props,null,m.mode,x),x.ref=Tr(m,p,g),x.return=m,m=x)}return o(m);case Vn:e:{for(C=g.key;p!==null;){if(p.key===C)if(p.tag===4&&p.stateNode.containerInfo===g.containerInfo&&p.stateNode.implementation===g.implementation){n(m,p.sibling),p=a(p,g.children||[]),p.return=m,m=p;break e}else{n(m,p);break}else t(m,p);p=p.sibling}p=Xo(g,m.mode,x),p.return=m,m=p}return o(m);case Dt:return C=g._init,k(m,p,C(g._payload),x)}if(Ir(g))return w(m,p,g,x);if(Cr(g))return A(m,p,g,x);Ya(m,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,p!==null&&p.tag===6?(n(m,p.sibling),p=a(p,g),p.return=m,m=p):(n(m,p),p=Ko(g,m.mode,x),p.return=m,m=p),o(m)):n(m,p)}return k}var hr=lp(!0),sp=lp(!1),Oi=ln(null),zi=null,Zn=null,Hs=null;function Ys(){Hs=Zn=zi=null}function Qs(e){var t=Oi.current;ne(Oi),e._currentValue=t}function Ul(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ur(e,t){zi=e,Hs=Zn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Le=!0),e.firstContext=null)}function Je(e){var t=e._currentValue;if(Hs!==e)if(e={context:e,memoizedValue:t,next:null},Zn===null){if(zi===null)throw Error(P(308));Zn=e,zi.dependencies={lanes:0,firstContext:e}}else Zn=Zn.next=e;return t}var vn=null;function qs(e){vn===null?vn=[e]:vn.push(e)}function up(e,t,n,r){var a=t.interleaved;return a===null?(n.next=n,qs(t)):(n.next=a.next,a.next=n),t.interleaved=n,Pt(e,r)}function Pt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Ft=!1;function Ks(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function cp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ct(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function qt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,W&2){var a=r.pending;return a===null?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Pt(e,n)}return a=r.interleaved,a===null?(t.next=t,qs(r)):(t.next=a.next,a.next=t),r.interleaved=t,Pt(e,n)}function pi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Os(e,n)}}function Nc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var a=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?a=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?a=i=t:i=i.next=t}else a=i=t;n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ii(e,t,n,r){var a=e.updateQueue;Ft=!1;var i=a.firstBaseUpdate,o=a.lastBaseUpdate,s=a.shared.pending;if(s!==null){a.shared.pending=null;var l=s,u=l.next;l.next=null,o===null?i=u:o.next=u,o=l;var f=e.alternate;f!==null&&(f=f.updateQueue,s=f.lastBaseUpdate,s!==o&&(s===null?f.firstBaseUpdate=u:s.next=u,f.lastBaseUpdate=l))}if(i!==null){var c=a.baseState;o=0,f=u=l=null,s=i;do{var d=s.lane,h=s.eventTime;if((r&d)===d){f!==null&&(f=f.next={eventTime:h,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var w=e,A=s;switch(d=t,h=n,A.tag){case 1:if(w=A.payload,typeof w=="function"){c=w.call(h,c,d);break e}c=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=A.payload,d=typeof w=="function"?w.call(h,c,d):w,d==null)break e;c=se({},c,d);break e;case 2:Ft=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,d=a.effects,d===null?a.effects=[s]:d.push(s))}else h={eventTime:h,lane:d,tag:s.tag,payload:s.payload,callback:s.callback,next:null},f===null?(u=f=h,l=c):f=f.next=h,o|=d;if(s=s.next,s===null){if(s=a.shared.pending,s===null)break;d=s,s=d.next,d.next=null,a.lastBaseUpdate=d,a.shared.pending=null}}while(!0);if(f===null&&(l=c),a.baseState=l,a.firstBaseUpdate=u,a.lastBaseUpdate=f,t=a.shared.interleaved,t!==null){a=t;do o|=a.lane,a=a.next;while(a!==t)}else i===null&&(a.shared.lanes=0);Cn|=o,e.lanes=o,e.memoizedState=c}}function _c(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(a!==null){if(r.callback=null,r=n,typeof a!="function")throw Error(P(191,a));a.call(r)}}}var Pa={},xt=ln(Pa),fa=ln(Pa),da=ln(Pa);function yn(e){if(e===Pa)throw Error(P(174));return e}function Xs(e,t){switch(Z(da,t),Z(fa,e),Z(xt,Pa),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:xl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=xl(t,e)}ne(xt),Z(xt,t)}function gr(){ne(xt),ne(fa),ne(da)}function fp(e){yn(da.current);var t=yn(xt.current),n=xl(t,e.type);t!==n&&(Z(fa,e),Z(xt,n))}function Zs(e){fa.current===e&&(ne(xt),ne(fa))}var oe=ln(0);function Di(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Wo=[];function Js(){for(var e=0;e<Wo.length;e++)Wo[e]._workInProgressVersionPrimary=null;Wo.length=0}var mi=Mt.ReactCurrentDispatcher,Go=Mt.ReactCurrentBatchConfig,En=0,le=null,me=null,ye=null,Fi=!1,Hr=!1,pa=0,e1=0;function be(){throw Error(P(321))}function eu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ct(e[n],t[n]))return!1;return!0}function tu(e,t,n,r,a,i){if(En=i,le=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,mi.current=e===null||e.memoizedState===null?a1:i1,e=n(r,a),Hr){i=0;do{if(Hr=!1,pa=0,25<=i)throw Error(P(301));i+=1,ye=me=null,t.updateQueue=null,mi.current=o1,e=n(r,a)}while(Hr)}if(mi.current=Ui,t=me!==null&&me.next!==null,En=0,ye=me=le=null,Fi=!1,t)throw Error(P(300));return e}function nu(){var e=pa!==0;return pa=0,e}function ht(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ye===null?le.memoizedState=ye=e:ye=ye.next=e,ye}function et(){if(me===null){var e=le.alternate;e=e!==null?e.memoizedState:null}else e=me.next;var t=ye===null?le.memoizedState:ye.next;if(t!==null)ye=t,me=e;else{if(e===null)throw Error(P(310));me=e,e={memoizedState:me.memoizedState,baseState:me.baseState,baseQueue:me.baseQueue,queue:me.queue,next:null},ye===null?le.memoizedState=ye=e:ye=ye.next=e}return ye}function ma(e,t){return typeof t=="function"?t(e):t}function Ho(e){var t=et(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=me,a=r.baseQueue,i=n.pending;if(i!==null){if(a!==null){var o=a.next;a.next=i.next,i.next=o}r.baseQueue=a=i,n.pending=null}if(a!==null){i=a.next,r=r.baseState;var s=o=null,l=null,u=i;do{var f=u.lane;if((En&f)===f)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var c={lane:f,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(s=l=c,o=r):l=l.next=c,le.lanes|=f,Cn|=f}u=u.next}while(u!==null&&u!==i);l===null?o=r:l.next=s,ct(r,t.memoizedState)||(Le=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){a=e;do i=a.lane,le.lanes|=i,Cn|=i,a=a.next;while(a!==e)}else a===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Yo(e){var t=et(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,i=t.memoizedState;if(a!==null){n.pending=null;var o=a=a.next;do i=e(i,o.action),o=o.next;while(o!==a);ct(i,t.memoizedState)||(Le=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function dp(){}function pp(e,t){var n=le,r=et(),a=t(),i=!ct(r.memoizedState,a);if(i&&(r.memoizedState=a,Le=!0),r=r.queue,ru(gp.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ye!==null&&ye.memoizedState.tag&1){if(n.flags|=2048,ha(9,hp.bind(null,n,r,a,t),void 0,null),xe===null)throw Error(P(349));En&30||mp(n,t,a)}return a}function mp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=le.updateQueue,t===null?(t={lastEffect:null,stores:null},le.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function hp(e,t,n,r){t.value=n,t.getSnapshot=r,vp(t)&&yp(e)}function gp(e,t,n){return n(function(){vp(t)&&yp(e)})}function vp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ct(e,n)}catch{return!0}}function yp(e){var t=Pt(e,1);t!==null&&ut(t,e,1,-1)}function Pc(e){var t=ht();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ma,lastRenderedState:e},t.queue=e,e=e.dispatch=r1.bind(null,le,e),[t.memoizedState,e]}function ha(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=le.updateQueue,t===null?(t={lastEffect:null,stores:null},le.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function wp(){return et().memoizedState}function hi(e,t,n,r){var a=ht();le.flags|=e,a.memoizedState=ha(1|t,n,void 0,r===void 0?null:r)}function lo(e,t,n,r){var a=et();r=r===void 0?null:r;var i=void 0;if(me!==null){var o=me.memoizedState;if(i=o.destroy,r!==null&&eu(r,o.deps)){a.memoizedState=ha(t,n,i,r);return}}le.flags|=e,a.memoizedState=ha(1|t,n,i,r)}function jc(e,t){return hi(8390656,8,e,t)}function ru(e,t){return lo(2048,8,e,t)}function xp(e,t){return lo(4,2,e,t)}function Ap(e,t){return lo(4,4,e,t)}function kp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Sp(e,t,n){return n=n!=null?n.concat([e]):null,lo(4,4,kp.bind(null,t,e),n)}function au(){}function bp(e,t){var n=et();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&eu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ep(e,t){var n=et();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&eu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Cp(e,t,n){return En&21?(ct(n,t)||(n=Td(),le.lanes|=n,Cn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Le=!0),e.memoizedState=n)}function t1(e,t){var n=Q;Q=n!==0&&4>n?n:4,e(!0);var r=Go.transition;Go.transition={};try{e(!1),t()}finally{Q=n,Go.transition=r}}function Np(){return et().memoizedState}function n1(e,t,n){var r=Xt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},_p(e))Pp(t,n);else if(n=up(e,t,n,r),n!==null){var a=Pe();ut(n,e,r,a),jp(n,t,r)}}function r1(e,t,n){var r=Xt(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(_p(e))Pp(t,a);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,s=i(o,n);if(a.hasEagerState=!0,a.eagerState=s,ct(s,o)){var l=t.interleaved;l===null?(a.next=a,qs(t)):(a.next=l.next,l.next=a),t.interleaved=a;return}}catch{}finally{}n=up(e,t,a,r),n!==null&&(a=Pe(),ut(n,e,r,a),jp(n,t,r))}}function _p(e){var t=e.alternate;return e===le||t!==null&&t===le}function Pp(e,t){Hr=Fi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function jp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Os(e,n)}}var Ui={readContext:Je,useCallback:be,useContext:be,useEffect:be,useImperativeHandle:be,useInsertionEffect:be,useLayoutEffect:be,useMemo:be,useReducer:be,useRef:be,useState:be,useDebugValue:be,useDeferredValue:be,useTransition:be,useMutableSource:be,useSyncExternalStore:be,useId:be,unstable_isNewReconciler:!1},a1={readContext:Je,useCallback:function(e,t){return ht().memoizedState=[e,t===void 0?null:t],e},useContext:Je,useEffect:jc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,hi(4194308,4,kp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return hi(4194308,4,e,t)},useInsertionEffect:function(e,t){return hi(4,2,e,t)},useMemo:function(e,t){var n=ht();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=ht();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=n1.bind(null,le,e),[r.memoizedState,e]},useRef:function(e){var t=ht();return e={current:e},t.memoizedState=e},useState:Pc,useDebugValue:au,useDeferredValue:function(e){return ht().memoizedState=e},useTransition:function(){var e=Pc(!1),t=e[0];return e=t1.bind(null,e[1]),ht().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=le,a=ht();if(ae){if(n===void 0)throw Error(P(407));n=n()}else{if(n=t(),xe===null)throw Error(P(349));En&30||mp(r,t,n)}a.memoizedState=n;var i={value:n,getSnapshot:t};return a.queue=i,jc(gp.bind(null,r,i,e),[e]),r.flags|=2048,ha(9,hp.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=ht(),t=xe.identifierPrefix;if(ae){var n=Et,r=bt;n=(r&~(1<<32-st(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=pa++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=e1++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},i1={readContext:Je,useCallback:bp,useContext:Je,useEffect:ru,useImperativeHandle:Sp,useInsertionEffect:xp,useLayoutEffect:Ap,useMemo:Ep,useReducer:Ho,useRef:wp,useState:function(){return Ho(ma)},useDebugValue:au,useDeferredValue:function(e){var t=et();return Cp(t,me.memoizedState,e)},useTransition:function(){var e=Ho(ma)[0],t=et().memoizedState;return[e,t]},useMutableSource:dp,useSyncExternalStore:pp,useId:Np,unstable_isNewReconciler:!1},o1={readContext:Je,useCallback:bp,useContext:Je,useEffect:ru,useImperativeHandle:Sp,useInsertionEffect:xp,useLayoutEffect:Ap,useMemo:Ep,useReducer:Yo,useRef:wp,useState:function(){return Yo(ma)},useDebugValue:au,useDeferredValue:function(e){var t=et();return me===null?t.memoizedState=e:Cp(t,me.memoizedState,e)},useTransition:function(){var e=Yo(ma)[0],t=et().memoizedState;return[e,t]},useMutableSource:dp,useSyncExternalStore:pp,useId:Np,unstable_isNewReconciler:!1};function at(e,t){if(e&&e.defaultProps){t=se({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function $l(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:se({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var so={isMounted:function(e){return(e=e._reactInternals)?Rn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Pe(),a=Xt(e),i=Ct(r,a);i.payload=t,n!=null&&(i.callback=n),t=qt(e,i,a),t!==null&&(ut(t,e,a,r),pi(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Pe(),a=Xt(e),i=Ct(r,a);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=qt(e,i,a),t!==null&&(ut(t,e,a,r),pi(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Pe(),r=Xt(e),a=Ct(n,r);a.tag=2,t!=null&&(a.callback=t),t=qt(e,a,r),t!==null&&(ut(t,e,r,n),pi(t,e,r))}};function Tc(e,t,n,r,a,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!la(n,r)||!la(a,i):!0}function Tp(e,t,n){var r=!1,a=tn,i=t.contextType;return typeof i=="object"&&i!==null?i=Je(i):(a=Oe(t)?Sn:Ne.current,r=t.contextTypes,i=(r=r!=null)?pr(e,a):tn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=so,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=i),t}function Rc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&so.enqueueReplaceState(t,t.state,null)}function Bl(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Ks(e);var i=t.contextType;typeof i=="object"&&i!==null?a.context=Je(i):(i=Oe(t)?Sn:Ne.current,a.context=pr(e,i)),a.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&($l(e,t,i,n),a.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof a.getSnapshotBeforeUpdate=="function"||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(t=a.state,typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount(),t!==a.state&&so.enqueueReplaceState(a,a.state,null),Ii(e,n,a,r),a.state=e.memoizedState),typeof a.componentDidMount=="function"&&(e.flags|=4194308)}function vr(e,t){try{var n="",r=t;do n+=M0(r),r=r.return;while(r);var a=n}catch(i){a=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:a,digest:null}}function Qo(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Vl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var l1=typeof WeakMap=="function"?WeakMap:Map;function Rp(e,t,n){n=Ct(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Bi||(Bi=!0,Jl=r),Vl(e,t)},n}function Lp(e,t,n){n=Ct(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){Vl(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Vl(e,t),typeof r!="function"&&(Kt===null?Kt=new Set([this]):Kt.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function Lc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new l1;var a=new Set;r.set(t,a)}else a=r.get(t),a===void 0&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=A1.bind(null,e,t,n),t.then(e,e))}function Mc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Oc(e,t,n,r,a){return e.mode&1?(e.flags|=65536,e.lanes=a,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Ct(-1,1),t.tag=2,qt(n,t,1))),n.lanes|=1),e)}var s1=Mt.ReactCurrentOwner,Le=!1;function _e(e,t,n,r){t.child=e===null?sp(t,null,n,r):hr(t,e.child,n,r)}function zc(e,t,n,r,a){n=n.render;var i=t.ref;return ur(t,a),r=tu(e,t,n,r,i,a),n=nu(),e!==null&&!Le?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,jt(e,t,a)):(ae&&n&&Vs(t),t.flags|=1,_e(e,t,r,a),t.child)}function Ic(e,t,n,r,a){if(e===null){var i=n.type;return typeof i=="function"&&!du(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Mp(e,t,i,r,a)):(e=wi(n.type,null,r,t,t.mode,a),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&a)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:la,n(o,r)&&e.ref===t.ref)return jt(e,t,a)}return t.flags|=1,e=Zt(i,r),e.ref=t.ref,e.return=t,t.child=e}function Mp(e,t,n,r,a){if(e!==null){var i=e.memoizedProps;if(la(i,r)&&e.ref===t.ref)if(Le=!1,t.pendingProps=r=i,(e.lanes&a)!==0)e.flags&131072&&(Le=!0);else return t.lanes=e.lanes,jt(e,t,a)}return Wl(e,t,n,r,a)}function Op(e,t,n){var r=t.pendingProps,a=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Z(er,Fe),Fe|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Z(er,Fe),Fe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,Z(er,Fe),Fe|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,Z(er,Fe),Fe|=r;return _e(e,t,a,n),t.child}function zp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Wl(e,t,n,r,a){var i=Oe(n)?Sn:Ne.current;return i=pr(t,i),ur(t,a),n=tu(e,t,n,r,i,a),r=nu(),e!==null&&!Le?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,jt(e,t,a)):(ae&&r&&Vs(t),t.flags|=1,_e(e,t,n,a),t.child)}function Dc(e,t,n,r,a){if(Oe(n)){var i=!0;Ri(t)}else i=!1;if(ur(t,a),t.stateNode===null)gi(e,t),Tp(t,n,r),Bl(t,n,r,a),r=!0;else if(e===null){var o=t.stateNode,s=t.memoizedProps;o.props=s;var l=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=Je(u):(u=Oe(n)?Sn:Ne.current,u=pr(t,u));var f=n.getDerivedStateFromProps,c=typeof f=="function"||typeof o.getSnapshotBeforeUpdate=="function";c||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(s!==r||l!==u)&&Rc(t,o,r,u),Ft=!1;var d=t.memoizedState;o.state=d,Ii(t,r,o,a),l=t.memoizedState,s!==r||d!==l||Me.current||Ft?(typeof f=="function"&&($l(t,n,f,r),l=t.memoizedState),(s=Ft||Tc(t,n,s,r,d,l,u))?(c||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),o.props=r,o.state=l,o.context=u,r=s):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,cp(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:at(t.type,s),o.props=u,c=t.pendingProps,d=o.context,l=n.contextType,typeof l=="object"&&l!==null?l=Je(l):(l=Oe(n)?Sn:Ne.current,l=pr(t,l));var h=n.getDerivedStateFromProps;(f=typeof h=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(s!==c||d!==l)&&Rc(t,o,r,l),Ft=!1,d=t.memoizedState,o.state=d,Ii(t,r,o,a);var w=t.memoizedState;s!==c||d!==w||Me.current||Ft?(typeof h=="function"&&($l(t,n,h,r),w=t.memoizedState),(u=Ft||Tc(t,n,u,r,d,w,l)||!1)?(f||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,w,l),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,w,l)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),o.props=r,o.state=w,o.context=l,r=u):(typeof o.componentDidUpdate!="function"||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return Gl(e,t,n,r,i,a)}function Gl(e,t,n,r,a,i){zp(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return a&&Sc(t,n,!1),jt(e,t,i);r=t.stateNode,s1.current=t;var s=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=hr(t,e.child,null,i),t.child=hr(t,null,s,i)):_e(e,t,s,i),t.memoizedState=r.state,a&&Sc(t,n,!0),t.child}function Ip(e){var t=e.stateNode;t.pendingContext?kc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&kc(e,t.context,!1),Xs(e,t.containerInfo)}function Fc(e,t,n,r,a){return mr(),Gs(a),t.flags|=256,_e(e,t,n,r),t.child}var Hl={dehydrated:null,treeContext:null,retryLane:0};function Yl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Dp(e,t,n){var r=t.pendingProps,a=oe.current,i=!1,o=(t.flags&128)!==0,s;if((s=o)||(s=e!==null&&e.memoizedState===null?!1:(a&2)!==0),s?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(a|=1),Z(oe,a&1),e===null)return Fl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=fo(o,r,0,null),e=kn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Yl(n),t.memoizedState=Hl,e):iu(t,o));if(a=e.memoizedState,a!==null&&(s=a.dehydrated,s!==null))return u1(e,t,o,r,s,a,n);if(i){i=r.fallback,o=t.mode,a=e.child,s=a.sibling;var l={mode:"hidden",children:r.children};return!(o&1)&&t.child!==a?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Zt(a,l),r.subtreeFlags=a.subtreeFlags&14680064),s!==null?i=Zt(s,i):(i=kn(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?Yl(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=Hl,r}return i=e.child,e=i.sibling,r=Zt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function iu(e,t){return t=fo({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Qa(e,t,n,r){return r!==null&&Gs(r),hr(t,e.child,null,n),e=iu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function u1(e,t,n,r,a,i,o){if(n)return t.flags&256?(t.flags&=-257,r=Qo(Error(P(422))),Qa(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,a=t.mode,r=fo({mode:"visible",children:r.children},a,0,null),i=kn(i,a,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&hr(t,e.child,null,o),t.child.memoizedState=Yl(o),t.memoizedState=Hl,i);if(!(t.mode&1))return Qa(e,t,o,null);if(a.data==="$!"){if(r=a.nextSibling&&a.nextSibling.dataset,r)var s=r.dgst;return r=s,i=Error(P(419)),r=Qo(i,r,void 0),Qa(e,t,o,r)}if(s=(o&e.childLanes)!==0,Le||s){if(r=xe,r!==null){switch(o&-o){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}a=a&(r.suspendedLanes|o)?0:a,a!==0&&a!==i.retryLane&&(i.retryLane=a,Pt(e,a),ut(r,e,a,-1))}return fu(),r=Qo(Error(P(421))),Qa(e,t,o,r)}return a.data==="$?"?(t.flags|=128,t.child=e.child,t=k1.bind(null,e),a._reactRetry=t,null):(e=i.treeContext,Ue=Qt(a.nextSibling),$e=t,ae=!0,ot=null,e!==null&&(Qe[qe++]=bt,Qe[qe++]=Et,Qe[qe++]=bn,bt=e.id,Et=e.overflow,bn=t),t=iu(t,r.children),t.flags|=4096,t)}function Uc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ul(e.return,t,n)}function qo(e,t,n,r,a){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=a)}function Fp(e,t,n){var r=t.pendingProps,a=r.revealOrder,i=r.tail;if(_e(e,t,r.children,n),r=oe.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Uc(e,n,t);else if(e.tag===19)Uc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Z(oe,r),!(t.mode&1))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;n!==null;)e=n.alternate,e!==null&&Di(e)===null&&(a=n),n=n.sibling;n=a,n===null?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),qo(t,!1,a,n,i);break;case"backwards":for(n=null,a=t.child,t.child=null;a!==null;){if(e=a.alternate,e!==null&&Di(e)===null){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}qo(t,!0,n,null,i);break;case"together":qo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function gi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function jt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Cn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(P(153));if(t.child!==null){for(e=t.child,n=Zt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Zt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function c1(e,t,n){switch(t.tag){case 3:Ip(t),mr();break;case 5:fp(t);break;case 1:Oe(t.type)&&Ri(t);break;case 4:Xs(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Z(Oi,r._currentValue),r._currentValue=a;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Z(oe,oe.current&1),t.flags|=128,null):n&t.child.childLanes?Dp(e,t,n):(Z(oe,oe.current&1),e=jt(e,t,n),e!==null?e.sibling:null);Z(oe,oe.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Fp(e,t,n);t.flags|=128}if(a=t.memoizedState,a!==null&&(a.rendering=null,a.tail=null,a.lastEffect=null),Z(oe,oe.current),r)break;return null;case 22:case 23:return t.lanes=0,Op(e,t,n)}return jt(e,t,n)}var Up,Ql,$p,Bp;Up=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Ql=function(){};$p=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,yn(xt.current);var i=null;switch(n){case"input":a=gl(e,a),r=gl(e,r),i=[];break;case"select":a=se({},a,{value:void 0}),r=se({},r,{value:void 0}),i=[];break;case"textarea":a=wl(e,a),r=wl(e,r),i=[];break;default:typeof a.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ji)}Al(n,r);var o;n=null;for(u in a)if(!r.hasOwnProperty(u)&&a.hasOwnProperty(u)&&a[u]!=null)if(u==="style"){var s=a[u];for(o in s)s.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(ea.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(s=a!=null?a[u]:void 0,r.hasOwnProperty(u)&&l!==s&&(l!=null||s!=null))if(u==="style")if(s){for(o in s)!s.hasOwnProperty(o)||l&&l.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in l)l.hasOwnProperty(o)&&s[o]!==l[o]&&(n||(n={}),n[o]=l[o])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,s=s?s.__html:void 0,l!=null&&s!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(ea.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&ee("scroll",e),i||s===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};Bp=function(e,t,n,r){n!==r&&(t.flags|=4)};function Rr(e,t){if(!ae)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ee(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;a!==null;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags&14680064,r|=a.flags&14680064,a.return=e,a=a.sibling;else for(a=e.child;a!==null;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function f1(e,t,n){var r=t.pendingProps;switch(Ws(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ee(t),null;case 1:return Oe(t.type)&&Ti(),Ee(t),null;case 3:return r=t.stateNode,gr(),ne(Me),ne(Ne),Js(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ha(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,ot!==null&&(ns(ot),ot=null))),Ql(e,t),Ee(t),null;case 5:Zs(t);var a=yn(da.current);if(n=t.type,e!==null&&t.stateNode!=null)$p(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(P(166));return Ee(t),null}if(e=yn(xt.current),Ha(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[vt]=t,r[ca]=i,e=(t.mode&1)!==0,n){case"dialog":ee("cancel",r),ee("close",r);break;case"iframe":case"object":case"embed":ee("load",r);break;case"video":case"audio":for(a=0;a<Fr.length;a++)ee(Fr[a],r);break;case"source":ee("error",r);break;case"img":case"image":case"link":ee("error",r),ee("load",r);break;case"details":ee("toggle",r);break;case"input":qu(r,i),ee("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},ee("invalid",r);break;case"textarea":Xu(r,i),ee("invalid",r)}Al(n,i),a=null;for(var o in i)if(i.hasOwnProperty(o)){var s=i[o];o==="children"?typeof s=="string"?r.textContent!==s&&(i.suppressHydrationWarning!==!0&&Ga(r.textContent,s,e),a=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(i.suppressHydrationWarning!==!0&&Ga(r.textContent,s,e),a=["children",""+s]):ea.hasOwnProperty(o)&&s!=null&&o==="onScroll"&&ee("scroll",r)}switch(n){case"input":Ia(r),Ku(r,i,!0);break;case"textarea":Ia(r),Zu(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=ji)}r=a,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=a.nodeType===9?a:a.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=gd(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[vt]=t,e[ca]=r,Up(e,t,!1,!1),t.stateNode=e;e:{switch(o=kl(n,r),n){case"dialog":ee("cancel",e),ee("close",e),a=r;break;case"iframe":case"object":case"embed":ee("load",e),a=r;break;case"video":case"audio":for(a=0;a<Fr.length;a++)ee(Fr[a],e);a=r;break;case"source":ee("error",e),a=r;break;case"img":case"image":case"link":ee("error",e),ee("load",e),a=r;break;case"details":ee("toggle",e),a=r;break;case"input":qu(e,r),a=gl(e,r),ee("invalid",e);break;case"option":a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=se({},r,{value:void 0}),ee("invalid",e);break;case"textarea":Xu(e,r),a=wl(e,r),ee("invalid",e);break;default:a=r}Al(n,a),s=a;for(i in s)if(s.hasOwnProperty(i)){var l=s[i];i==="style"?wd(e,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&vd(e,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&ta(e,l):typeof l=="number"&&ta(e,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(ea.hasOwnProperty(i)?l!=null&&i==="onScroll"&&ee("scroll",e):l!=null&&Ps(e,i,l,o))}switch(n){case"input":Ia(e),Ku(e,r,!1);break;case"textarea":Ia(e),Zu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+en(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?ir(e,!!r.multiple,i,!1):r.defaultValue!=null&&ir(e,!!r.multiple,r.defaultValue,!0);break;default:typeof a.onClick=="function"&&(e.onclick=ji)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ee(t),null;case 6:if(e&&t.stateNode!=null)Bp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(P(166));if(n=yn(da.current),yn(xt.current),Ha(t)){if(r=t.stateNode,n=t.memoizedProps,r[vt]=t,(i=r.nodeValue!==n)&&(e=$e,e!==null))switch(e.tag){case 3:Ga(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ga(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[vt]=t,t.stateNode=r}return Ee(t),null;case 13:if(ne(oe),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ae&&Ue!==null&&t.mode&1&&!(t.flags&128))op(),mr(),t.flags|=98560,i=!1;else if(i=Ha(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(P(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(P(317));i[vt]=t}else mr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Ee(t),i=!1}else ot!==null&&(ns(ot),ot=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||oe.current&1?he===0&&(he=3):fu())),t.updateQueue!==null&&(t.flags|=4),Ee(t),null);case 4:return gr(),Ql(e,t),e===null&&sa(t.stateNode.containerInfo),Ee(t),null;case 10:return Qs(t.type._context),Ee(t),null;case 17:return Oe(t.type)&&Ti(),Ee(t),null;case 19:if(ne(oe),i=t.memoizedState,i===null)return Ee(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)Rr(i,!1);else{if(he!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Di(e),o!==null){for(t.flags|=128,Rr(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Z(oe,oe.current&1|2),t.child}e=e.sibling}i.tail!==null&&fe()>yr&&(t.flags|=128,r=!0,Rr(i,!1),t.lanes=4194304)}else{if(!r)if(e=Di(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Rr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!ae)return Ee(t),null}else 2*fe()-i.renderingStartTime>yr&&n!==1073741824&&(t.flags|=128,r=!0,Rr(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=fe(),t.sibling=null,n=oe.current,Z(oe,r?n&1|2:n&1),t):(Ee(t),null);case 22:case 23:return cu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Fe&1073741824&&(Ee(t),t.subtreeFlags&6&&(t.flags|=8192)):Ee(t),null;case 24:return null;case 25:return null}throw Error(P(156,t.tag))}function d1(e,t){switch(Ws(t),t.tag){case 1:return Oe(t.type)&&Ti(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return gr(),ne(Me),ne(Ne),Js(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Zs(t),null;case 13:if(ne(oe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(P(340));mr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ne(oe),null;case 4:return gr(),null;case 10:return Qs(t.type._context),null;case 22:case 23:return cu(),null;case 24:return null;default:return null}}var qa=!1,Ce=!1,p1=typeof WeakSet=="function"?WeakSet:Set,M=null;function Jn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ue(e,t,r)}else n.current=null}function ql(e,t,n){try{n()}catch(r){ue(e,t,r)}}var $c=!1;function m1(e,t){if(Rl=Ni,e=Yd(),Bs(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var a=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,s=-1,l=-1,u=0,f=0,c=e,d=null;t:for(;;){for(var h;c!==n||a!==0&&c.nodeType!==3||(s=o+a),c!==i||r!==0&&c.nodeType!==3||(l=o+r),c.nodeType===3&&(o+=c.nodeValue.length),(h=c.firstChild)!==null;)d=c,c=h;for(;;){if(c===e)break t;if(d===n&&++u===a&&(s=o),d===i&&++f===r&&(l=o),(h=c.nextSibling)!==null)break;c=d,d=c.parentNode}c=h}n=s===-1||l===-1?null:{start:s,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ll={focusedElem:e,selectionRange:n},Ni=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var A=w.memoizedProps,k=w.memoizedState,m=t.stateNode,p=m.getSnapshotBeforeUpdate(t.elementType===t.type?A:at(t.type,A),k);m.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(P(163))}}catch(x){ue(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return w=$c,$c=!1,w}function Yr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var a=r=r.next;do{if((a.tag&e)===e){var i=a.destroy;a.destroy=void 0,i!==void 0&&ql(t,n,i)}a=a.next}while(a!==r)}}function uo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Kl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Vp(e){var t=e.alternate;t!==null&&(e.alternate=null,Vp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[vt],delete t[ca],delete t[zl],delete t[Kh],delete t[Xh])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Wp(e){return e.tag===5||e.tag===3||e.tag===4}function Bc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Wp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Xl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ji));else if(r!==4&&(e=e.child,e!==null))for(Xl(e,t,n),e=e.sibling;e!==null;)Xl(e,t,n),e=e.sibling}function Zl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Zl(e,t,n),e=e.sibling;e!==null;)Zl(e,t,n),e=e.sibling}var Ae=null,it=!1;function zt(e,t,n){for(n=n.child;n!==null;)Gp(e,t,n),n=n.sibling}function Gp(e,t,n){if(wt&&typeof wt.onCommitFiberUnmount=="function")try{wt.onCommitFiberUnmount(to,n)}catch{}switch(n.tag){case 5:Ce||Jn(n,t);case 6:var r=Ae,a=it;Ae=null,zt(e,t,n),Ae=r,it=a,Ae!==null&&(it?(e=Ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ae.removeChild(n.stateNode));break;case 18:Ae!==null&&(it?(e=Ae,n=n.stateNode,e.nodeType===8?Bo(e.parentNode,n):e.nodeType===1&&Bo(e,n),ia(e)):Bo(Ae,n.stateNode));break;case 4:r=Ae,a=it,Ae=n.stateNode.containerInfo,it=!0,zt(e,t,n),Ae=r,it=a;break;case 0:case 11:case 14:case 15:if(!Ce&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){a=r=r.next;do{var i=a,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&ql(n,t,o),a=a.next}while(a!==r)}zt(e,t,n);break;case 1:if(!Ce&&(Jn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){ue(n,t,s)}zt(e,t,n);break;case 21:zt(e,t,n);break;case 22:n.mode&1?(Ce=(r=Ce)||n.memoizedState!==null,zt(e,t,n),Ce=r):zt(e,t,n);break;default:zt(e,t,n)}}function Vc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new p1),t.forEach(function(r){var a=S1.bind(null,e,r);n.has(r)||(n.add(r),r.then(a,a))})}}function rt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var a=n[r];try{var i=e,o=t,s=o;e:for(;s!==null;){switch(s.tag){case 5:Ae=s.stateNode,it=!1;break e;case 3:Ae=s.stateNode.containerInfo,it=!0;break e;case 4:Ae=s.stateNode.containerInfo,it=!0;break e}s=s.return}if(Ae===null)throw Error(P(160));Gp(i,o,a),Ae=null,it=!1;var l=a.alternate;l!==null&&(l.return=null),a.return=null}catch(u){ue(a,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Hp(t,e),t=t.sibling}function Hp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(rt(t,e),pt(e),r&4){try{Yr(3,e,e.return),uo(3,e)}catch(A){ue(e,e.return,A)}try{Yr(5,e,e.return)}catch(A){ue(e,e.return,A)}}break;case 1:rt(t,e),pt(e),r&512&&n!==null&&Jn(n,n.return);break;case 5:if(rt(t,e),pt(e),r&512&&n!==null&&Jn(n,n.return),e.flags&32){var a=e.stateNode;try{ta(a,"")}catch(A){ue(e,e.return,A)}}if(r&4&&(a=e.stateNode,a!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,s=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{s==="input"&&i.type==="radio"&&i.name!=null&&md(a,i),kl(s,o);var u=kl(s,i);for(o=0;o<l.length;o+=2){var f=l[o],c=l[o+1];f==="style"?wd(a,c):f==="dangerouslySetInnerHTML"?vd(a,c):f==="children"?ta(a,c):Ps(a,f,c,u)}switch(s){case"input":vl(a,i);break;case"textarea":hd(a,i);break;case"select":var d=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!i.multiple;var h=i.value;h!=null?ir(a,!!i.multiple,h,!1):d!==!!i.multiple&&(i.defaultValue!=null?ir(a,!!i.multiple,i.defaultValue,!0):ir(a,!!i.multiple,i.multiple?[]:"",!1))}a[ca]=i}catch(A){ue(e,e.return,A)}}break;case 6:if(rt(t,e),pt(e),r&4){if(e.stateNode===null)throw Error(P(162));a=e.stateNode,i=e.memoizedProps;try{a.nodeValue=i}catch(A){ue(e,e.return,A)}}break;case 3:if(rt(t,e),pt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ia(t.containerInfo)}catch(A){ue(e,e.return,A)}break;case 4:rt(t,e),pt(e);break;case 13:rt(t,e),pt(e),a=e.child,a.flags&8192&&(i=a.memoizedState!==null,a.stateNode.isHidden=i,!i||a.alternate!==null&&a.alternate.memoizedState!==null||(su=fe())),r&4&&Vc(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(Ce=(u=Ce)||f,rt(t,e),Ce=u):rt(t,e),pt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!f&&e.mode&1)for(M=e,f=e.child;f!==null;){for(c=M=f;M!==null;){switch(d=M,h=d.child,d.tag){case 0:case 11:case 14:case 15:Yr(4,d,d.return);break;case 1:Jn(d,d.return);var w=d.stateNode;if(typeof w.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(A){ue(r,n,A)}}break;case 5:Jn(d,d.return);break;case 22:if(d.memoizedState!==null){Gc(c);continue}}h!==null?(h.return=d,M=h):Gc(c)}f=f.sibling}e:for(f=null,c=e;;){if(c.tag===5){if(f===null){f=c;try{a=c.stateNode,u?(i=a.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(s=c.stateNode,l=c.memoizedProps.style,o=l!=null&&l.hasOwnProperty("display")?l.display:null,s.style.display=yd("display",o))}catch(A){ue(e,e.return,A)}}}else if(c.tag===6){if(f===null)try{c.stateNode.nodeValue=u?"":c.memoizedProps}catch(A){ue(e,e.return,A)}}else if((c.tag!==22&&c.tag!==23||c.memoizedState===null||c===e)&&c.child!==null){c.child.return=c,c=c.child;continue}if(c===e)break e;for(;c.sibling===null;){if(c.return===null||c.return===e)break e;f===c&&(f=null),c=c.return}f===c&&(f=null),c.sibling.return=c.return,c=c.sibling}}break;case 19:rt(t,e),pt(e),r&4&&Vc(e);break;case 21:break;default:rt(t,e),pt(e)}}function pt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Wp(n)){var r=n;break e}n=n.return}throw Error(P(160))}switch(r.tag){case 5:var a=r.stateNode;r.flags&32&&(ta(a,""),r.flags&=-33);var i=Bc(e);Zl(e,i,a);break;case 3:case 4:var o=r.stateNode.containerInfo,s=Bc(e);Xl(e,s,o);break;default:throw Error(P(161))}}catch(l){ue(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function h1(e,t,n){M=e,Yp(e)}function Yp(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var a=M,i=a.child;if(a.tag===22&&r){var o=a.memoizedState!==null||qa;if(!o){var s=a.alternate,l=s!==null&&s.memoizedState!==null||Ce;s=qa;var u=Ce;if(qa=o,(Ce=l)&&!u)for(M=a;M!==null;)o=M,l=o.child,o.tag===22&&o.memoizedState!==null?Hc(a):l!==null?(l.return=o,M=l):Hc(a);for(;i!==null;)M=i,Yp(i),i=i.sibling;M=a,qa=s,Ce=u}Wc(e)}else a.subtreeFlags&8772&&i!==null?(i.return=a,M=i):Wc(e)}}function Wc(e){for(;M!==null;){var t=M;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ce||uo(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ce)if(n===null)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:at(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&_c(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}_c(t,o,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var f=u.memoizedState;if(f!==null){var c=f.dehydrated;c!==null&&ia(c)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(P(163))}Ce||t.flags&512&&Kl(t)}catch(d){ue(t,t.return,d)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function Gc(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function Hc(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{uo(4,t)}catch(l){ue(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var a=t.return;try{r.componentDidMount()}catch(l){ue(t,a,l)}}var i=t.return;try{Kl(t)}catch(l){ue(t,i,l)}break;case 5:var o=t.return;try{Kl(t)}catch(l){ue(t,o,l)}}}catch(l){ue(t,t.return,l)}if(t===e){M=null;break}var s=t.sibling;if(s!==null){s.return=t.return,M=s;break}M=t.return}}var g1=Math.ceil,$i=Mt.ReactCurrentDispatcher,ou=Mt.ReactCurrentOwner,Ze=Mt.ReactCurrentBatchConfig,W=0,xe=null,de=null,ke=0,Fe=0,er=ln(0),he=0,ga=null,Cn=0,co=0,lu=0,Qr=null,Re=null,su=0,yr=1/0,kt=null,Bi=!1,Jl=null,Kt=null,Ka=!1,Vt=null,Vi=0,qr=0,es=null,vi=-1,yi=0;function Pe(){return W&6?fe():vi!==-1?vi:vi=fe()}function Xt(e){return e.mode&1?W&2&&ke!==0?ke&-ke:Jh.transition!==null?(yi===0&&(yi=Td()),yi):(e=Q,e!==0||(e=window.event,e=e===void 0?16:Dd(e.type)),e):1}function ut(e,t,n,r){if(50<qr)throw qr=0,es=null,Error(P(185));Ca(e,n,r),(!(W&2)||e!==xe)&&(e===xe&&(!(W&2)&&(co|=n),he===4&&$t(e,ke)),ze(e,r),n===1&&W===0&&!(t.mode&1)&&(yr=fe()+500,oo&&sn()))}function ze(e,t){var n=e.callbackNode;J0(e,t);var r=Ci(e,e===xe?ke:0);if(r===0)n!==null&&tc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&tc(n),t===1)e.tag===0?Zh(Yc.bind(null,e)):rp(Yc.bind(null,e)),Qh(function(){!(W&6)&&sn()}),n=null;else{switch(Rd(r)){case 1:n=Ms;break;case 4:n=Pd;break;case 16:n=Ei;break;case 536870912:n=jd;break;default:n=Ei}n=tm(n,Qp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Qp(e,t){if(vi=-1,yi=0,W&6)throw Error(P(327));var n=e.callbackNode;if(cr()&&e.callbackNode!==n)return null;var r=Ci(e,e===xe?ke:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Wi(e,r);else{t=r;var a=W;W|=2;var i=Kp();(xe!==e||ke!==t)&&(kt=null,yr=fe()+500,An(e,t));do try{w1();break}catch(s){qp(e,s)}while(!0);Ys(),$i.current=i,W=a,de!==null?t=0:(xe=null,ke=0,t=he)}if(t!==0){if(t===2&&(a=Nl(e),a!==0&&(r=a,t=ts(e,a))),t===1)throw n=ga,An(e,0),$t(e,r),ze(e,fe()),n;if(t===6)$t(e,r);else{if(a=e.current.alternate,!(r&30)&&!v1(a)&&(t=Wi(e,r),t===2&&(i=Nl(e),i!==0&&(r=i,t=ts(e,i))),t===1))throw n=ga,An(e,0),$t(e,r),ze(e,fe()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(P(345));case 2:hn(e,Re,kt);break;case 3:if($t(e,r),(r&130023424)===r&&(t=su+500-fe(),10<t)){if(Ci(e,0)!==0)break;if(a=e.suspendedLanes,(a&r)!==r){Pe(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=Ol(hn.bind(null,e,Re,kt),t);break}hn(e,Re,kt);break;case 4:if($t(e,r),(r&4194240)===r)break;for(t=e.eventTimes,a=-1;0<r;){var o=31-st(r);i=1<<o,o=t[o],o>a&&(a=o),r&=~i}if(r=a,r=fe()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*g1(r/1960))-r,10<r){e.timeoutHandle=Ol(hn.bind(null,e,Re,kt),r);break}hn(e,Re,kt);break;case 5:hn(e,Re,kt);break;default:throw Error(P(329))}}}return ze(e,fe()),e.callbackNode===n?Qp.bind(null,e):null}function ts(e,t){var n=Qr;return e.current.memoizedState.isDehydrated&&(An(e,t).flags|=256),e=Wi(e,t),e!==2&&(t=Re,Re=n,t!==null&&ns(t)),e}function ns(e){Re===null?Re=e:Re.push.apply(Re,e)}function v1(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var a=n[r],i=a.getSnapshot;a=a.value;try{if(!ct(i(),a))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function $t(e,t){for(t&=~lu,t&=~co,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-st(t),r=1<<n;e[n]=-1,t&=~r}}function Yc(e){if(W&6)throw Error(P(327));cr();var t=Ci(e,0);if(!(t&1))return ze(e,fe()),null;var n=Wi(e,t);if(e.tag!==0&&n===2){var r=Nl(e);r!==0&&(t=r,n=ts(e,r))}if(n===1)throw n=ga,An(e,0),$t(e,t),ze(e,fe()),n;if(n===6)throw Error(P(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,hn(e,Re,kt),ze(e,fe()),null}function uu(e,t){var n=W;W|=1;try{return e(t)}finally{W=n,W===0&&(yr=fe()+500,oo&&sn())}}function Nn(e){Vt!==null&&Vt.tag===0&&!(W&6)&&cr();var t=W;W|=1;var n=Ze.transition,r=Q;try{if(Ze.transition=null,Q=1,e)return e()}finally{Q=r,Ze.transition=n,W=t,!(W&6)&&sn()}}function cu(){Fe=er.current,ne(er)}function An(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Yh(n)),de!==null)for(n=de.return;n!==null;){var r=n;switch(Ws(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ti();break;case 3:gr(),ne(Me),ne(Ne),Js();break;case 5:Zs(r);break;case 4:gr();break;case 13:ne(oe);break;case 19:ne(oe);break;case 10:Qs(r.type._context);break;case 22:case 23:cu()}n=n.return}if(xe=e,de=e=Zt(e.current,null),ke=Fe=t,he=0,ga=null,lu=co=Cn=0,Re=Qr=null,vn!==null){for(t=0;t<vn.length;t++)if(n=vn[t],r=n.interleaved,r!==null){n.interleaved=null;var a=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=a,r.next=o}n.pending=r}vn=null}return e}function qp(e,t){do{var n=de;try{if(Ys(),mi.current=Ui,Fi){for(var r=le.memoizedState;r!==null;){var a=r.queue;a!==null&&(a.pending=null),r=r.next}Fi=!1}if(En=0,ye=me=le=null,Hr=!1,pa=0,ou.current=null,n===null||n.return===null){he=1,ga=t,de=null;break}e:{var i=e,o=n.return,s=n,l=t;if(t=ke,s.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,f=s,c=f.tag;if(!(f.mode&1)&&(c===0||c===11||c===15)){var d=f.alternate;d?(f.updateQueue=d.updateQueue,f.memoizedState=d.memoizedState,f.lanes=d.lanes):(f.updateQueue=null,f.memoizedState=null)}var h=Mc(o);if(h!==null){h.flags&=-257,Oc(h,o,s,i,t),h.mode&1&&Lc(i,u,t),t=h,l=u;var w=t.updateQueue;if(w===null){var A=new Set;A.add(l),t.updateQueue=A}else w.add(l);break e}else{if(!(t&1)){Lc(i,u,t),fu();break e}l=Error(P(426))}}else if(ae&&s.mode&1){var k=Mc(o);if(k!==null){!(k.flags&65536)&&(k.flags|=256),Oc(k,o,s,i,t),Gs(vr(l,s));break e}}i=l=vr(l,s),he!==4&&(he=2),Qr===null?Qr=[i]:Qr.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var m=Rp(i,l,t);Nc(i,m);break e;case 1:s=l;var p=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Kt===null||!Kt.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var x=Lp(i,s,t);Nc(i,x);break e}}i=i.return}while(i!==null)}Zp(n)}catch(S){t=S,de===n&&n!==null&&(de=n=n.return);continue}break}while(!0)}function Kp(){var e=$i.current;return $i.current=Ui,e===null?Ui:e}function fu(){(he===0||he===3||he===2)&&(he=4),xe===null||!(Cn&268435455)&&!(co&268435455)||$t(xe,ke)}function Wi(e,t){var n=W;W|=2;var r=Kp();(xe!==e||ke!==t)&&(kt=null,An(e,t));do try{y1();break}catch(a){qp(e,a)}while(!0);if(Ys(),W=n,$i.current=r,de!==null)throw Error(P(261));return xe=null,ke=0,he}function y1(){for(;de!==null;)Xp(de)}function w1(){for(;de!==null&&!W0();)Xp(de)}function Xp(e){var t=em(e.alternate,e,Fe);e.memoizedProps=e.pendingProps,t===null?Zp(e):de=t,ou.current=null}function Zp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=d1(n,t),n!==null){n.flags&=32767,de=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{he=6,de=null;return}}else if(n=f1(n,t,Fe),n!==null){de=n;return}if(t=t.sibling,t!==null){de=t;return}de=t=e}while(t!==null);he===0&&(he=5)}function hn(e,t,n){var r=Q,a=Ze.transition;try{Ze.transition=null,Q=1,x1(e,t,n,r)}finally{Ze.transition=a,Q=r}return null}function x1(e,t,n,r){do cr();while(Vt!==null);if(W&6)throw Error(P(327));n=e.finishedWork;var a=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(P(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(eh(e,i),e===xe&&(de=xe=null,ke=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ka||(Ka=!0,tm(Ei,function(){return cr(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Ze.transition,Ze.transition=null;var o=Q;Q=1;var s=W;W|=4,ou.current=null,m1(e,n),Hp(n,e),Uh(Ll),Ni=!!Rl,Ll=Rl=null,e.current=n,h1(n),G0(),W=s,Q=o,Ze.transition=i}else e.current=n;if(Ka&&(Ka=!1,Vt=e,Vi=a),i=e.pendingLanes,i===0&&(Kt=null),Q0(n.stateNode),ze(e,fe()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Bi)throw Bi=!1,e=Jl,Jl=null,e;return Vi&1&&e.tag!==0&&cr(),i=e.pendingLanes,i&1?e===es?qr++:(qr=0,es=e):qr=0,sn(),null}function cr(){if(Vt!==null){var e=Rd(Vi),t=Ze.transition,n=Q;try{if(Ze.transition=null,Q=16>e?16:e,Vt===null)var r=!1;else{if(e=Vt,Vt=null,Vi=0,W&6)throw Error(P(331));var a=W;for(W|=4,M=e.current;M!==null;){var i=M,o=i.child;if(M.flags&16){var s=i.deletions;if(s!==null){for(var l=0;l<s.length;l++){var u=s[l];for(M=u;M!==null;){var f=M;switch(f.tag){case 0:case 11:case 15:Yr(8,f,i)}var c=f.child;if(c!==null)c.return=f,M=c;else for(;M!==null;){f=M;var d=f.sibling,h=f.return;if(Vp(f),f===u){M=null;break}if(d!==null){d.return=h,M=d;break}M=h}}}var w=i.alternate;if(w!==null){var A=w.child;if(A!==null){w.child=null;do{var k=A.sibling;A.sibling=null,A=k}while(A!==null)}}M=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,M=o;else e:for(;M!==null;){if(i=M,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Yr(9,i,i.return)}var m=i.sibling;if(m!==null){m.return=i.return,M=m;break e}M=i.return}}var p=e.current;for(M=p;M!==null;){o=M;var g=o.child;if(o.subtreeFlags&2064&&g!==null)g.return=o,M=g;else e:for(o=p;M!==null;){if(s=M,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:uo(9,s)}}catch(S){ue(s,s.return,S)}if(s===o){M=null;break e}var x=s.sibling;if(x!==null){x.return=s.return,M=x;break e}M=s.return}}if(W=a,sn(),wt&&typeof wt.onPostCommitFiberRoot=="function")try{wt.onPostCommitFiberRoot(to,e)}catch{}r=!0}return r}finally{Q=n,Ze.transition=t}}return!1}function Qc(e,t,n){t=vr(n,t),t=Rp(e,t,1),e=qt(e,t,1),t=Pe(),e!==null&&(Ca(e,1,t),ze(e,t))}function ue(e,t,n){if(e.tag===3)Qc(e,e,n);else for(;t!==null;){if(t.tag===3){Qc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Kt===null||!Kt.has(r))){e=vr(n,e),e=Lp(t,e,1),t=qt(t,e,1),e=Pe(),t!==null&&(Ca(t,1,e),ze(t,e));break}}t=t.return}}function A1(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Pe(),e.pingedLanes|=e.suspendedLanes&n,xe===e&&(ke&n)===n&&(he===4||he===3&&(ke&130023424)===ke&&500>fe()-su?An(e,0):lu|=n),ze(e,t)}function Jp(e,t){t===0&&(e.mode&1?(t=Ua,Ua<<=1,!(Ua&130023424)&&(Ua=4194304)):t=1);var n=Pe();e=Pt(e,t),e!==null&&(Ca(e,t,n),ze(e,n))}function k1(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Jp(e,n)}function S1(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;a!==null&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(P(314))}r!==null&&r.delete(t),Jp(e,n)}var em;em=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Me.current)Le=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Le=!1,c1(e,t,n);Le=!!(e.flags&131072)}else Le=!1,ae&&t.flags&1048576&&ap(t,Mi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;gi(e,t),e=t.pendingProps;var a=pr(t,Ne.current);ur(t,n),a=tu(null,t,r,e,a,n);var i=nu();return t.flags|=1,typeof a=="object"&&a!==null&&typeof a.render=="function"&&a.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Oe(r)?(i=!0,Ri(t)):i=!1,t.memoizedState=a.state!==null&&a.state!==void 0?a.state:null,Ks(t),a.updater=so,t.stateNode=a,a._reactInternals=t,Bl(t,r,e,n),t=Gl(null,t,r,!0,i,n)):(t.tag=0,ae&&i&&Vs(t),_e(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(gi(e,t),e=t.pendingProps,a=r._init,r=a(r._payload),t.type=r,a=t.tag=E1(r),e=at(r,e),a){case 0:t=Wl(null,t,r,e,n);break e;case 1:t=Dc(null,t,r,e,n);break e;case 11:t=zc(null,t,r,e,n);break e;case 14:t=Ic(null,t,r,at(r.type,e),n);break e}throw Error(P(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:at(r,a),Wl(e,t,r,a,n);case 1:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:at(r,a),Dc(e,t,r,a,n);case 3:e:{if(Ip(t),e===null)throw Error(P(387));r=t.pendingProps,i=t.memoizedState,a=i.element,cp(e,t),Ii(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){a=vr(Error(P(423)),t),t=Fc(e,t,r,n,a);break e}else if(r!==a){a=vr(Error(P(424)),t),t=Fc(e,t,r,n,a);break e}else for(Ue=Qt(t.stateNode.containerInfo.firstChild),$e=t,ae=!0,ot=null,n=sp(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(mr(),r===a){t=jt(e,t,n);break e}_e(e,t,r,n)}t=t.child}return t;case 5:return fp(t),e===null&&Fl(t),r=t.type,a=t.pendingProps,i=e!==null?e.memoizedProps:null,o=a.children,Ml(r,a)?o=null:i!==null&&Ml(r,i)&&(t.flags|=32),zp(e,t),_e(e,t,o,n),t.child;case 6:return e===null&&Fl(t),null;case 13:return Dp(e,t,n);case 4:return Xs(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=hr(t,null,r,n):_e(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:at(r,a),zc(e,t,r,a,n);case 7:return _e(e,t,t.pendingProps,n),t.child;case 8:return _e(e,t,t.pendingProps.children,n),t.child;case 12:return _e(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,i=t.memoizedProps,o=a.value,Z(Oi,r._currentValue),r._currentValue=o,i!==null)if(ct(i.value,o)){if(i.children===a.children&&!Me.current){t=jt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var s=i.dependencies;if(s!==null){o=i.child;for(var l=s.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=Ct(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var f=u.pending;f===null?l.next=l:(l.next=f.next,f.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),Ul(i.return,n,t),s.lanes|=n;break}l=l.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(P(341));o.lanes|=n,s=o.alternate,s!==null&&(s.lanes|=n),Ul(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}_e(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,ur(t,n),a=Je(a),r=r(a),t.flags|=1,_e(e,t,r,n),t.child;case 14:return r=t.type,a=at(r,t.pendingProps),a=at(r.type,a),Ic(e,t,r,a,n);case 15:return Mp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:at(r,a),gi(e,t),t.tag=1,Oe(r)?(e=!0,Ri(t)):e=!1,ur(t,n),Tp(t,r,a),Bl(t,r,a,n),Gl(null,t,r,!0,e,n);case 19:return Fp(e,t,n);case 22:return Op(e,t,n)}throw Error(P(156,t.tag))};function tm(e,t){return _d(e,t)}function b1(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Xe(e,t,n,r){return new b1(e,t,n,r)}function du(e){return e=e.prototype,!(!e||!e.isReactComponent)}function E1(e){if(typeof e=="function")return du(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ts)return 11;if(e===Rs)return 14}return 2}function Zt(e,t){var n=e.alternate;return n===null?(n=Xe(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function wi(e,t,n,r,a,i){var o=2;if(r=e,typeof e=="function")du(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Wn:return kn(n.children,a,i,t);case js:o=8,a|=8;break;case dl:return e=Xe(12,n,t,a|2),e.elementType=dl,e.lanes=i,e;case pl:return e=Xe(13,n,t,a),e.elementType=pl,e.lanes=i,e;case ml:return e=Xe(19,n,t,a),e.elementType=ml,e.lanes=i,e;case fd:return fo(n,a,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ud:o=10;break e;case cd:o=9;break e;case Ts:o=11;break e;case Rs:o=14;break e;case Dt:o=16,r=null;break e}throw Error(P(130,e==null?e:typeof e,""))}return t=Xe(o,n,t,a),t.elementType=e,t.type=r,t.lanes=i,t}function kn(e,t,n,r){return e=Xe(7,e,r,t),e.lanes=n,e}function fo(e,t,n,r){return e=Xe(22,e,r,t),e.elementType=fd,e.lanes=n,e.stateNode={isHidden:!1},e}function Ko(e,t,n){return e=Xe(6,e,null,t),e.lanes=n,e}function Xo(e,t,n){return t=Xe(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function C1(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=To(0),this.expirationTimes=To(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=To(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function pu(e,t,n,r,a,i,o,s,l){return e=new C1(e,t,n,s,l),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Xe(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ks(i),e}function N1(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Vn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function nm(e){if(!e)return tn;e=e._reactInternals;e:{if(Rn(e)!==e||e.tag!==1)throw Error(P(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Oe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(P(171))}if(e.tag===1){var n=e.type;if(Oe(n))return np(e,n,t)}return t}function rm(e,t,n,r,a,i,o,s,l){return e=pu(n,r,!0,e,a,i,o,s,l),e.context=nm(null),n=e.current,r=Pe(),a=Xt(n),i=Ct(r,a),i.callback=t??null,qt(n,i,a),e.current.lanes=a,Ca(e,a,r),ze(e,r),e}function po(e,t,n,r){var a=t.current,i=Pe(),o=Xt(a);return n=nm(n),t.context===null?t.context=n:t.pendingContext=n,t=Ct(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=qt(a,t,o),e!==null&&(ut(e,a,o,i),pi(e,a,o)),o}function Gi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function qc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function mu(e,t){qc(e,t),(e=e.alternate)&&qc(e,t)}function _1(){return null}var am=typeof reportError=="function"?reportError:function(e){console.error(e)};function hu(e){this._internalRoot=e}mo.prototype.render=hu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(P(409));po(e,t,null,null)};mo.prototype.unmount=hu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Nn(function(){po(null,e,null,null)}),t[_t]=null}};function mo(e){this._internalRoot=e}mo.prototype.unstable_scheduleHydration=function(e){if(e){var t=Od();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ut.length&&t!==0&&t<Ut[n].priority;n++);Ut.splice(n,0,e),n===0&&Id(e)}};function gu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ho(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Kc(){}function P1(e,t,n,r,a){if(a){if(typeof r=="function"){var i=r;r=function(){var u=Gi(o);i.call(u)}}var o=rm(t,r,e,0,null,!1,!1,"",Kc);return e._reactRootContainer=o,e[_t]=o.current,sa(e.nodeType===8?e.parentNode:e),Nn(),o}for(;a=e.lastChild;)e.removeChild(a);if(typeof r=="function"){var s=r;r=function(){var u=Gi(l);s.call(u)}}var l=pu(e,0,!1,null,null,!1,!1,"",Kc);return e._reactRootContainer=l,e[_t]=l.current,sa(e.nodeType===8?e.parentNode:e),Nn(function(){po(t,l,n,r)}),l}function go(e,t,n,r,a){var i=n._reactRootContainer;if(i){var o=i;if(typeof a=="function"){var s=a;a=function(){var l=Gi(o);s.call(l)}}po(t,o,e,a)}else o=P1(n,t,e,a,r);return Gi(o)}Ld=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Dr(t.pendingLanes);n!==0&&(Os(t,n|1),ze(t,fe()),!(W&6)&&(yr=fe()+500,sn()))}break;case 13:Nn(function(){var r=Pt(e,1);if(r!==null){var a=Pe();ut(r,e,1,a)}}),mu(e,1)}};zs=function(e){if(e.tag===13){var t=Pt(e,134217728);if(t!==null){var n=Pe();ut(t,e,134217728,n)}mu(e,134217728)}};Md=function(e){if(e.tag===13){var t=Xt(e),n=Pt(e,t);if(n!==null){var r=Pe();ut(n,e,t,r)}mu(e,t)}};Od=function(){return Q};zd=function(e,t){var n=Q;try{return Q=e,t()}finally{Q=n}};bl=function(e,t,n){switch(t){case"input":if(vl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=io(r);if(!a)throw Error(P(90));pd(r),vl(r,a)}}}break;case"textarea":hd(e,n);break;case"select":t=n.value,t!=null&&ir(e,!!n.multiple,t,!1)}};kd=uu;Sd=Nn;var j1={usingClientEntryPoint:!1,Events:[_a,Qn,io,xd,Ad,uu]},Lr={findFiberByHostInstance:gn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},T1={bundleType:Lr.bundleType,version:Lr.version,rendererPackageName:Lr.rendererPackageName,rendererConfig:Lr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Mt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Cd(e),e===null?null:e.stateNode},findFiberByHostInstance:Lr.findFiberByHostInstance||_1,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Xa=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Xa.isDisabled&&Xa.supportsFiber)try{to=Xa.inject(T1),wt=Xa}catch{}}Ve.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=j1;Ve.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!gu(t))throw Error(P(200));return N1(e,t,null,n)};Ve.createRoot=function(e,t){if(!gu(e))throw Error(P(299));var n=!1,r="",a=am;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(a=t.onRecoverableError)),t=pu(e,1,!1,null,null,n,!1,r,a),e[_t]=t.current,sa(e.nodeType===8?e.parentNode:e),new hu(t)};Ve.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(P(188)):(e=Object.keys(e).join(","),Error(P(268,e)));return e=Cd(t),e=e===null?null:e.stateNode,e};Ve.flushSync=function(e){return Nn(e)};Ve.hydrate=function(e,t,n){if(!ho(t))throw Error(P(200));return go(null,e,t,!0,n)};Ve.hydrateRoot=function(e,t,n){if(!gu(e))throw Error(P(405));var r=n!=null&&n.hydratedSources||null,a=!1,i="",o=am;if(n!=null&&(n.unstable_strictMode===!0&&(a=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=rm(t,null,e,1,n??null,a,!1,i,o),e[_t]=t.current,sa(e),r)for(e=0;e<r.length;e++)n=r[e],a=n._getVersion,a=a(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new mo(t)};Ve.render=function(e,t,n){if(!ho(t))throw Error(P(200));return go(null,e,t,!1,n)};Ve.unmountComponentAtNode=function(e){if(!ho(e))throw Error(P(40));return e._reactRootContainer?(Nn(function(){go(null,null,e,!1,function(){e._reactRootContainer=null,e[_t]=null})}),!0):!1};Ve.unstable_batchedUpdates=uu;Ve.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ho(n))throw Error(P(200));if(e==null||e._reactInternals===void 0)throw Error(P(38));return go(e,t,n,!1,r)};Ve.version="18.3.1-next-f1338f8080-20240426";function im(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(im)}catch(e){console.error(e)}}im(),id.exports=Ve;var R1=id.exports,Xc=R1;cl.createRoot=Xc.createRoot,cl.hydrateRoot=Xc.hydrateRoot;const At=()=>!window.invokeNative,L1=()=>{},vu=e=>{const t=[...e];let n=t.length,r;for(;n>0;)r=Math.floor(Math.random()*n),n--,[t[n],t[r]]=[t[r],t[n]];return t};function M1(e){function t(a){const i=["zero","one","two","three","four","five","six","seven","eight","nine","ten","eleven","twelve","thirteen","fourteen","fifteen","sixteen","seventeen","eighteen","nineteen"],o=["","","twenty","thirty","forty","fifty","sixty","seventy","eighty","ninety"];return a<20?i[a]:a<100?o[Math.floor(a/10)]+(a%10!==0?"-"+i[a%10]:""):a.toString()}const r=new Set(e).size;return t(r)}const un=(e,t)=>{const n=v.useRef(L1);v.useEffect(()=>{n.current=t},[t]),v.useEffect(()=>{const r=a=>{const{action:i,data:o}=a.data;n.current&&i===e&&n.current(o)};return window.addEventListener("message",r),()=>window.removeEventListener("message",r)},[e])},O1=v.createContext(null),z1=({children:e})=>{const[t,n]=v.useState(!1);return un("setVisible",n),v.useEffect(()=>{if(!t)return;const r=async a=>{["Escape"].includes(a.code)&&(At()?n(!t):console.log("Minigame isn't closable in game"))};return window.addEventListener("keydown",r),()=>{window.removeEventListener("keydown",r)}},[t]),y.jsx(O1.Provider,{value:{visible:t,setVisible:n},children:y.jsx("div",{style:{visibility:t?"visible":"hidden",height:"100%"},children:e})})};/**
 * @remix-run/router v1.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function va(){return va=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},va.apply(this,arguments)}var Wt;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Wt||(Wt={}));const Zc="popstate";function I1(e){e===void 0&&(e={});function t(r,a){let{pathname:i,search:o,hash:s}=r.location;return rs("",{pathname:i,search:o,hash:s},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:Hi(a)}return F1(t,n,null,e)}function pe(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function om(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function D1(){return Math.random().toString(36).substr(2,8)}function Jc(e,t){return{usr:e.state,key:e.key,idx:t}}function rs(e,t,n,r){return n===void 0&&(n=null),va({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Sr(t):t,{state:n,key:t&&t.key||r||D1()})}function Hi(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Sr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function F1(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:i=!1}=r,o=a.history,s=Wt.Pop,l=null,u=f();u==null&&(u=0,o.replaceState(va({},o.state,{idx:u}),""));function f(){return(o.state||{idx:null}).idx}function c(){s=Wt.Pop;let k=f(),m=k==null?null:k-u;u=k,l&&l({action:s,location:A.location,delta:m})}function d(k,m){s=Wt.Push;let p=rs(A.location,k,m);u=f()+1;let g=Jc(p,u),x=A.createHref(p);try{o.pushState(g,"",x)}catch(S){if(S instanceof DOMException&&S.name==="DataCloneError")throw S;a.location.assign(x)}i&&l&&l({action:s,location:A.location,delta:1})}function h(k,m){s=Wt.Replace;let p=rs(A.location,k,m);u=f();let g=Jc(p,u),x=A.createHref(p);o.replaceState(g,"",x),i&&l&&l({action:s,location:A.location,delta:0})}function w(k){let m=a.location.origin!=="null"?a.location.origin:a.location.href,p=typeof k=="string"?k:Hi(k);return p=p.replace(/ $/,"%20"),pe(m,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,m)}let A={get action(){return s},get location(){return e(a,o)},listen(k){if(l)throw new Error("A history only accepts one active listener");return a.addEventListener(Zc,c),l=k,()=>{a.removeEventListener(Zc,c),l=null}},createHref(k){return t(a,k)},createURL:w,encodeLocation(k){let m=w(k);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:d,replace:h,go(k){return o.go(k)}};return A}var ef;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(ef||(ef={}));function U1(e,t,n){return n===void 0&&(n="/"),$1(e,t,n,!1)}function $1(e,t,n,r){let a=typeof t=="string"?Sr(t):t,i=yu(a.pathname||"/",n);if(i==null)return null;let o=lm(e);B1(o);let s=null;for(let l=0;s==null&&l<o.length;++l){let u=J1(i);s=X1(o[l],u,r)}return s}function lm(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(i,o,s)=>{let l={relativePath:s===void 0?i.path||"":s,caseSensitive:i.caseSensitive===!0,childrenIndex:o,route:i};l.relativePath.startsWith("/")&&(pe(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=Jt([r,l.relativePath]),f=n.concat(l);i.children&&i.children.length>0&&(pe(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),lm(i.children,t,f,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:q1(u,i.index),routesMeta:f})};return e.forEach((i,o)=>{var s;if(i.path===""||!((s=i.path)!=null&&s.includes("?")))a(i,o);else for(let l of sm(i.path))a(i,o,l)}),t}function sm(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return a?[i,""]:[i];let o=sm(r.join("/")),s=[];return s.push(...o.map(l=>l===""?i:[i,l].join("/"))),a&&s.push(...o),s.map(l=>e.startsWith("/")&&l===""?"/":l)}function B1(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:K1(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const V1=/^:[\w-]+$/,W1=3,G1=2,H1=1,Y1=10,Q1=-2,tf=e=>e==="*";function q1(e,t){let n=e.split("/"),r=n.length;return n.some(tf)&&(r+=Q1),t&&(r+=G1),n.filter(a=>!tf(a)).reduce((a,i)=>a+(V1.test(i)?W1:i===""?H1:Y1),r)}function K1(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function X1(e,t,n){let{routesMeta:r}=e,a={},i="/",o=[];for(let s=0;s<r.length;++s){let l=r[s],u=s===r.length-1,f=i==="/"?t:t.slice(i.length)||"/",c=nf({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},f),d=l.route;if(!c&&u&&n&&!r[r.length-1].route.index&&(c=nf({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},f)),!c)return null;Object.assign(a,c.params),o.push({params:a,pathname:Jt([i,c.pathname]),pathnameBase:rg(Jt([i,c.pathnameBase])),route:d}),c.pathnameBase!=="/"&&(i=Jt([i,c.pathnameBase]))}return o}function nf(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Z1(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let i=a[0],o=i.replace(/(.)\/+$/,"$1"),s=a.slice(1);return{params:r.reduce((u,f,c)=>{let{paramName:d,isOptional:h}=f;if(d==="*"){let A=s[c]||"";o=i.slice(0,i.length-A.length).replace(/(.)\/+$/,"$1")}const w=s[c];return h&&!w?u[d]=void 0:u[d]=(w||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:o,pattern:e}}function Z1(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),om(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,s,l)=>(r.push({paramName:s,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function J1(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return om(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function yu(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function eg(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?Sr(e):e;return{pathname:n?n.startsWith("/")?n:tg(n,t):t,search:ag(r),hash:ig(a)}}function tg(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function Zo(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function ng(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function um(e,t){let n=ng(e);return t?n.map((r,a)=>a===e.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function cm(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=Sr(e):(a=va({},e),pe(!a.pathname||!a.pathname.includes("?"),Zo("?","pathname","search",a)),pe(!a.pathname||!a.pathname.includes("#"),Zo("#","pathname","hash",a)),pe(!a.search||!a.search.includes("#"),Zo("#","search","hash",a)));let i=e===""||a.pathname==="",o=i?"/":a.pathname,s;if(o==null)s=n;else{let c=t.length-1;if(!r&&o.startsWith("..")){let d=o.split("/");for(;d[0]==="..";)d.shift(),c-=1;a.pathname=d.join("/")}s=c>=0?t[c]:"/"}let l=eg(a,s),u=o&&o!=="/"&&o.endsWith("/"),f=(i||o===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||f)&&(l.pathname+="/"),l}const Jt=e=>e.join("/").replace(/\/\/+/g,"/"),rg=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),ag=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,ig=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function og(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const fm=["post","put","patch","delete"];new Set(fm);const lg=["get",...fm];new Set(lg);/**
 * React Router v6.24.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ya(){return ya=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ya.apply(this,arguments)}const wu=v.createContext(null),sg=v.createContext(null),Ln=v.createContext(null),vo=v.createContext(null),Mn=v.createContext({outlet:null,matches:[],isDataRoute:!1}),dm=v.createContext(null);function ug(e,t){let{relative:n}=t===void 0?{}:t;ja()||pe(!1);let{basename:r,navigator:a}=v.useContext(Ln),{hash:i,pathname:o,search:s}=mm(e,{relative:n}),l=o;return r!=="/"&&(l=o==="/"?r:Jt([r,o])),a.createHref({pathname:l,search:s,hash:i})}function ja(){return v.useContext(vo)!=null}function yo(){return ja()||pe(!1),v.useContext(vo).location}function pm(e){v.useContext(Ln).static||v.useLayoutEffect(e)}function cn(){let{isDataRoute:e}=v.useContext(Mn);return e?kg():cg()}function cg(){ja()||pe(!1);let e=v.useContext(wu),{basename:t,future:n,navigator:r}=v.useContext(Ln),{matches:a}=v.useContext(Mn),{pathname:i}=yo(),o=JSON.stringify(um(a,n.v7_relativeSplatPath)),s=v.useRef(!1);return pm(()=>{s.current=!0}),v.useCallback(function(u,f){if(f===void 0&&(f={}),!s.current)return;if(typeof u=="number"){r.go(u);return}let c=cm(u,JSON.parse(o),i,f.relative==="path");e==null&&t!=="/"&&(c.pathname=c.pathname==="/"?t:Jt([t,c.pathname])),(f.replace?r.replace:r.push)(c,f.state,f)},[t,r,o,i,e])}function mm(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=v.useContext(Ln),{matches:a}=v.useContext(Mn),{pathname:i}=yo(),o=JSON.stringify(um(a,r.v7_relativeSplatPath));return v.useMemo(()=>cm(e,JSON.parse(o),i,n==="path"),[e,o,i,n])}function fg(e,t){return dg(e,t)}function dg(e,t,n,r){ja()||pe(!1);let{navigator:a}=v.useContext(Ln),{matches:i}=v.useContext(Mn),o=i[i.length-1],s=o?o.params:{};o&&o.pathname;let l=o?o.pathnameBase:"/";o&&o.route;let u=yo(),f;if(t){var c;let k=typeof t=="string"?Sr(t):t;l==="/"||(c=k.pathname)!=null&&c.startsWith(l)||pe(!1),f=k}else f=u;let d=f.pathname||"/",h=d;if(l!=="/"){let k=l.replace(/^\//,"").split("/");h="/"+d.replace(/^\//,"").split("/").slice(k.length).join("/")}let w=U1(e,{pathname:h}),A=vg(w&&w.map(k=>Object.assign({},k,{params:Object.assign({},s,k.params),pathname:Jt([l,a.encodeLocation?a.encodeLocation(k.pathname).pathname:k.pathname]),pathnameBase:k.pathnameBase==="/"?l:Jt([l,a.encodeLocation?a.encodeLocation(k.pathnameBase).pathname:k.pathnameBase])})),i,n,r);return t&&A?v.createElement(vo.Provider,{value:{location:ya({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:Wt.Pop}},A):A}function pg(){let e=Ag(),t=og(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return v.createElement(v.Fragment,null,v.createElement("h2",null,"Unexpected Application Error!"),v.createElement("h3",{style:{fontStyle:"italic"}},t),n?v.createElement("pre",{style:a},n):null,null)}const mg=v.createElement(pg,null);class hg extends v.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?v.createElement(Mn.Provider,{value:this.props.routeContext},v.createElement(dm.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function gg(e){let{routeContext:t,match:n,children:r}=e,a=v.useContext(wu);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),v.createElement(Mn.Provider,{value:t},r)}function vg(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if((i=n)!=null&&i.errors)e=n.matches;else return null}let o=e,s=(a=n)==null?void 0:a.errors;if(s!=null){let f=o.findIndex(c=>c.route.id&&(s==null?void 0:s[c.route.id])!==void 0);f>=0||pe(!1),o=o.slice(0,Math.min(o.length,f+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let f=0;f<o.length;f++){let c=o[f];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(u=f),c.route.id){let{loaderData:d,errors:h}=n,w=c.route.loader&&d[c.route.id]===void 0&&(!h||h[c.route.id]===void 0);if(c.route.lazy||w){l=!0,u>=0?o=o.slice(0,u+1):o=[o[0]];break}}}return o.reduceRight((f,c,d)=>{let h,w=!1,A=null,k=null;n&&(h=s&&c.route.id?s[c.route.id]:void 0,A=c.route.errorElement||mg,l&&(u<0&&d===0?(w=!0,k=null):u===d&&(w=!0,k=c.route.hydrateFallbackElement||null)));let m=t.concat(o.slice(0,d+1)),p=()=>{let g;return h?g=A:w?g=k:c.route.Component?g=v.createElement(c.route.Component,null):c.route.element?g=c.route.element:g=f,v.createElement(gg,{match:c,routeContext:{outlet:f,matches:m,isDataRoute:n!=null},children:g})};return n&&(c.route.ErrorBoundary||c.route.errorElement||d===0)?v.createElement(hg,{location:n.location,revalidation:n.revalidation,component:A,error:h,children:p(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):p()},null)}var hm=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(hm||{}),Yi=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Yi||{});function yg(e){let t=v.useContext(wu);return t||pe(!1),t}function wg(e){let t=v.useContext(sg);return t||pe(!1),t}function xg(e){let t=v.useContext(Mn);return t||pe(!1),t}function gm(e){let t=xg(),n=t.matches[t.matches.length-1];return n.route.id||pe(!1),n.route.id}function Ag(){var e;let t=v.useContext(dm),n=wg(Yi.UseRouteError),r=gm(Yi.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function kg(){let{router:e}=yg(hm.UseNavigateStable),t=gm(Yi.UseNavigateStable),n=v.useRef(!1);return pm(()=>{n.current=!0}),v.useCallback(function(a,i){i===void 0&&(i={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,ya({fromRouteId:t},i)))},[e,t])}function mt(e){pe(!1)}function Sg(e){let{basename:t="/",children:n=null,location:r,navigationType:a=Wt.Pop,navigator:i,static:o=!1,future:s}=e;ja()&&pe(!1);let l=t.replace(/^\/*/,"/"),u=v.useMemo(()=>({basename:l,navigator:i,static:o,future:ya({v7_relativeSplatPath:!1},s)}),[l,s,i,o]);typeof r=="string"&&(r=Sr(r));let{pathname:f="/",search:c="",hash:d="",state:h=null,key:w="default"}=r,A=v.useMemo(()=>{let k=yu(f,l);return k==null?null:{location:{pathname:k,search:c,hash:d,state:h,key:w},navigationType:a}},[l,f,c,d,h,w,a]);return A==null?null:v.createElement(Ln.Provider,{value:u},v.createElement(vo.Provider,{children:n,value:A}))}function bg(e){let{children:t,location:n}=e;return fg(as(t),n)}new Promise(()=>{});function as(e,t){t===void 0&&(t=[]);let n=[];return v.Children.forEach(e,(r,a)=>{if(!v.isValidElement(r))return;let i=[...t,a];if(r.type===v.Fragment){n.push.apply(n,as(r.props.children,i));return}r.type!==mt&&pe(!1),!r.props.index||!r.props.children||pe(!1);let o={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=as(r.props.children,i)),n.push(o)}),n}/**
 * React Router DOM v6.24.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function is(){return is=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},is.apply(this,arguments)}function Eg(e,t){if(e==null)return{};var n={},r=Object.keys(e),a,i;for(i=0;i<r.length;i++)a=r[i],!(t.indexOf(a)>=0)&&(n[a]=e[a]);return n}function Cg(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Ng(e,t){return e.button===0&&(!t||t==="_self")&&!Cg(e)}const _g=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","unstable_viewTransition"],Pg="6";try{window.__reactRouterVersion=Pg}catch{}const jg="startTransition",rf=A0[jg];function Tg(e){let{basename:t,children:n,future:r,window:a}=e,i=v.useRef();i.current==null&&(i.current=I1({window:a,v5Compat:!0}));let o=i.current,[s,l]=v.useState({action:o.action,location:o.location}),{v7_startTransition:u}=r||{},f=v.useCallback(c=>{u&&rf?rf(()=>l(c)):l(c)},[l,u]);return v.useLayoutEffect(()=>o.listen(f),[o,f]),v.createElement(Sg,{basename:t,children:n,location:s.location,navigationType:s.action,navigator:o,future:r})}const Rg=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Lg=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,os=v.forwardRef(function(t,n){let{onClick:r,relative:a,reloadDocument:i,replace:o,state:s,target:l,to:u,preventScrollReset:f,unstable_viewTransition:c}=t,d=Eg(t,_g),{basename:h}=v.useContext(Ln),w,A=!1;if(typeof u=="string"&&Lg.test(u)&&(w=u,Rg))try{let g=new URL(window.location.href),x=u.startsWith("//")?new URL(g.protocol+u):new URL(u),S=yu(x.pathname,h);x.origin===g.origin&&S!=null?u=S+x.search+x.hash:A=!0}catch{}let k=ug(u,{relative:a}),m=Mg(u,{replace:o,state:s,target:l,preventScrollReset:f,relative:a,unstable_viewTransition:c});function p(g){r&&r(g),g.defaultPrevented||m(g)}return v.createElement("a",is({},d,{href:w||k,onClick:A||i?r:p,ref:n,target:l}))});var af;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(af||(af={}));var of;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(of||(of={}));function Mg(e,t){let{target:n,replace:r,state:a,preventScrollReset:i,relative:o,unstable_viewTransition:s}=t===void 0?{}:t,l=cn(),u=yo(),f=mm(e,{relative:o});return v.useCallback(c=>{if(Ng(c,n)){c.preventDefault();let d=r!==void 0?r:Hi(u)===Hi(f);l(e,{replace:d,state:a,preventScrollReset:i,relative:o,unstable_viewTransition:s})}},[u,l,f,r,a,n,e,i,o,s])}const Og=[{href:"/thermite",img:"https://github.com/MaximilianAdF/NoPixel-MiniGames-4.0/assets/********/0674273c-43da-4d39-a98e-1443517a4eac",title:"Thermite",description:"Replica of the Thermite hack that is triggered when disabling lasers inside the Maze Bank on NoPixel 4.0"},{href:"/roof-running",img:"https://github.com/MaximilianAdF/NoPixel-MiniGames-4.0/assets/********/e8387474-4a34-4f02-842f-195484160a60",title:"Roof Running",description:"Replica of the Roof Running hack that is triggered when robbing AC-Units on NoPixel 4.0"},{href:"/laundromat",img:"https://github.com/MaximilianAdF/NoPixel-MiniGames-4.0/assets/********/23371a81-fd85-49b5-819b-de1207a5a4f8",title:"Laundromat",description:"Replica of the Laundromat hack that is triggered when robbing the safe inside the south-side Laundromat on NoPixel 4.0"},{href:"/lockpick",img:"https://github.com/MaximilianAdF/NoPixel-MiniGames-4.0/assets/********/472d8447-c69b-4474-aaeb-474516b8f014",title:"LockPick",description:"Replica of the LockPick hack that is triggered when lockpicking vehicles, among other things, on NoPixel 4.0"},{href:"/repair-kit",img:"https://github.com/MaximilianAdF/NoPixel-MiniGames-4.0/assets/********/ac828de8-fb5d-4c9f-97ac-ee000702b630",title:"RepairKit",description:"Replica of the RepairKit hack that is triggered when repairing vehicles on NoPixel 4.0"},{href:"/word-memory",img:"https://github.com/MaximilianAdF/NoPixel-MiniGames-4.0/assets/********/613422a5-39ba-4cd8-bd64-c0a41427629d",title:"Word Memory",description:"Replica of the Word Memory hack that is triggered when hacking the Maze Bank on NoPixel 4.0"},{href:"/chopping",img:"https://github.com/MaximilianAdF/NoPixel-MiniGames-4.0/assets/********/815913c0-c159-4d17-b363-7136e08a1077",title:"Chopping",description:"Replica of the Chopping hack that is triggered when chopping vehicles on NoPixel 4.0"},{href:"/pincracker",img:"https://github.com/MaximilianAdF/NoPixel-MiniGames-4.0/assets/********/e3619d89-7059-45fe-b8e5-3d429151ff41",title:"PinCracker",description:"Replica of the PinCracker hack that is triggered when hacking the Maze Bank on NoPixel 4.0"},{href:"/color-picker",img:"https://github.com/MaximilianAdF/NoPixel-MiniGames-4.0/assets/********/e3619d89-7059-45fe-b8e5-3d429151ff41",title:"Color Picker",description:"Match the icons with their color in this memory game."}],zg=()=>y.jsxs("main",{className:"flex min-h-screen flex-col items-center p-5 gap-5 ",children:[y.jsxs("div",{className:"w-full max-w-2xl p-5 mx-auto flex items-center flex-col rounded-lg shadow bg-mirage-900/50",children:[y.jsx("h1",{className:"text-white text-5xl m-0 pb-5",children:"NoPixel Hacking Simulator"}),y.jsxs("p",{className:"text-white text-xl text-center *:text-spring-green-300 hover:*:text-aquamarine-300",children:["Hover over the below ",y.jsx("a",{children:"images"})," to discover the minigames that are currently available. For more information checkout the"," ",y.jsx(os,{to:"https://github.com/MaximilianAdF/NoPixel-MiniGames-4.0",children:"GitHub Repository"}),"."]})]}),y.jsx("div",{className:"flex flex-wrap justify-center max-w-4xl mx-auto",children:Og.map((e,t)=>y.jsx("div",{className:"flex-item m-2 p-2 rounded-lg text-center relative overflow-hidden bg-spring-green-300 shadow",children:y.jsxs(os,{to:e.href,className:"",children:[y.jsx("img",{src:e.img,alt:e.title,width:250,height:124,className:"rounded"}),y.jsxs("div",{className:"absolute top-0 left-0 w-full h-full opacity-0 flex justify-center flex-col items-center transition duration-500 ease-in-out bg-black/50 text-white text-center hover:opacity-100",children:[y.jsx("h2",{className:"text-3xl",children:e.title}),y.jsx("p",{className:"text-xs max-w-[80%]",children:e.description})]})]})},t))}),y.jsxs("div",{className:"w-full max-w-2xl p-5 mx-auto flex flex-col items-center shadow rounded-lg text-white text-center bg-mirage-900/50",children:[y.jsx("h2",{className:"text-5xl pb-5",children:"Highscores"}),y.jsx("p",{className:"text-xl max-w-[80%]",children:"Coming soon..."})]})]}),Ig="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='iso-8859-1'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20fill='%23a2d8fa'%20height='800px'%20width='800px'%20version='1.1'%20id='Capa_1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20viewBox='0%200%20264.725%20264.725'%20xml:space='preserve'%3e%3cpath%20d='M220.195,71.427c-0.589-7.654-9.135-15.619-17.979-16.209c-8.844-0.584-17.398,0.301-12.087,6.483%20c5.308,6.188,7.074,12.091,4.423,11.212c-2.66-0.896-13.267-7.08-45.104-2.066c-4.126,1.17-21.221-12.682-44.513-12.977%20c-23.283-0.295-40.381,6.346-64.85,72.296c-2.356,5.828-18.866,19.386-27.71,25.865C3.536,162.529,0.007,169.787,0,182.763%20c-0.018,18.158,25.934,27.187,81.648,26.889c55.715-0.292,85.195-9.388,85.195-9.388c-62.789,6.773-158.907,10.52-158.907-18.687%20c0-20.641,28.321-28.47,36.281-28.184c7.958,0.3,13.562,12.673,33.307,5.603c3.247-0.295,1.48,4.423-1.18,7.369%20c-2.651,2.942-0.586,6.487,9.73,6.487c10.315,0,41.183,0.295,47.707,0c6.531-0.299,11.839-11.792-9.384-12.68%20c-18.548,0.311,12.023-5.773,15.915-21.813c0.709-3.927,8.84-4.139,15.918-4.119c20.777,0.029,34.485,38.193,38.912,38.338%20c4.416,0.15,17.979,1.621,17.683-4.273c-0.292-5.897-11.491-3.241-13.854-6.487c-2.359-3.234-10.023-15.504-7.366-21.104%20c2.65-5.59,12.674-21.229,24.463-22.988c11.789-1.777,42.451,7.361,47.459,0c5.012-7.372-6.783-11.512-15.918-28.611%20C243.779,80.572,238.768,71.728,220.195,71.427z'/%3e%3c/svg%3e",Dg="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20fill='%23a2d8fa'%20height='800px'%20width='800px'%20version='1.1'%20id='_x32_'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20viewBox='0%200%20512%20512'%20xml:space='preserve'%3e%3cg%3e%3cpath%20class='st0'%20d='M477.364,394.541c-6.64,0-12.656,2.695-17.015,7.038l-28.156-7.038c0,0,24.093-54.21,51.194-117.46%20c21.36-49.827-6.336-92.256-33.124-96.373c-16.609-2.555-28.835,2.492-37.46,9.125c-4.687-30.093-15.062-57.648-29.406-81.194%20c19.062-9.282,32.226-28.781,32.226-51.414C415.622,25.616,390.014,0,358.405,0c-24.664,0-45.617,15.624-53.663,37.492%20c-15.688-6.71-32.14-10.382-48.742-10.382s-33.054,3.672-48.742,10.382C199.22,15.624,178.267,0,153.603,0%20c-31.601,0-57.226,25.616-57.226,57.226c0,22.624,13.172,42.132,32.234,51.414c-14.352,23.546-24.719,51.093-29.414,81.194%20c-8.616-6.633-20.851-11.68-37.452-9.125c-26.788,4.117-54.484,46.546-33.132,96.373c27.109,63.25,51.202,117.46,51.202,117.46%20l-28.164,7.038c-4.36-4.343-10.375-7.038-17.016-7.038c-13.305,0-24.093,10.788-24.093,24.093%20c0,13.304,10.788,24.093,24.093,24.093c9.539,0,17.703-5.586,21.609-13.625l83.804-20.703V307.198l15.062,13.554v91.858%20l-27.75,23.476c-3.406-1.844-7.258-2.992-11.405-2.992c-13.305,0-24.094,10.789-24.094,24.093%20c0,13.305,10.789,24.093,24.094,24.093c12.718,0,23.023-9.882,23.913-22.374l24.273-16.18v26.508%20c-5.469,4.413-9.031,11.093-9.031,18.672c0,13.304,10.789,24.093,24.093,24.093c13.305,0,24.093-10.79,24.093-24.093%20c0-8.898-4.875-16.578-12.046-20.758v-30.444l12.351,8.992c-0.133,1.008-0.305,2.008-0.305,3.054%20c0,13.305,10.79,24.094,24.093,24.094c13.305,0,24.094-10.789,24.094-24.094c0-13.304-10.789-24.093-24.094-24.093%20c-2.531,0-4.922,0.492-7.21,1.226l-19.898-16.28v-18.078h26.608h18.57h21.078h18.578h26.601v18.078l-19.891,16.28%20c-2.297-0.734-4.687-1.226-7.21-1.226c-13.313,0-24.101,10.789-24.101,24.093c0,13.305,10.788,24.094,24.101,24.094%20c13.304,0,24.093-10.789,24.093-24.094c0-1.046-0.18-2.046-0.305-3.054l12.351-8.992v30.444%20c-7.171,4.18-12.046,11.86-12.046,20.758c0,13.304,10.789,24.093,24.093,24.093c13.304,0,24.093-10.79,24.093-24.093%20c0-7.578-3.562-14.258-9.039-18.672v-26.508l24.273,16.18c0.89,12.492,11.203,22.374,23.922,22.374%20c13.304,0,24.094-10.788,24.094-24.093c0-13.304-10.79-24.093-24.094-24.093c-4.156,0-8,1.148-11.406,2.992l-27.75-23.476v-91.858%20l15.055-13.554V408.4l83.803,20.703c3.906,8.039,12.078,13.625,21.609,13.625c13.313,0,24.093-10.789,24.093-24.093%20C501.457,405.33,490.676,394.541,477.364,394.541z%20M130.869,57.226c0-12.554,10.18-22.718,22.734-22.718%20c12.562,0,22.726,10.164,22.726,22.718c0,12.555-10.164,22.742-22.726,22.742C141.049,79.968,130.869,69.78,130.869,57.226z%20M354.647,171.677H157.354c-1.508,0.281-2.829,0.367-3.75,0h3.75c14.874-2.774,56.718-34.132,98.646-34.132%20S339.78,168.904,354.647,171.677h3.758C357.475,172.044,356.154,171.958,354.647,171.677z%20M358.405,79.968%20c-12.554,0-22.734-10.187-22.734-22.742c0-12.554,10.18-22.718,22.734-22.718c12.562,0,22.726,10.164,22.726,22.718%20C381.131,69.78,370.967,79.968,358.405,79.968z'/%3e%3c/g%3e%3c/svg%3e",Fg="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='iso-8859-1'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20fill='%23a2d8fa'%20height='800px'%20width='800px'%20version='1.1'%20id='Capa_1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20viewBox='0%200%20341.874%20341.874'%20xml:space='preserve'%3e%3cpath%20d='M341.338,56.544c-0.865-1.748-2.654-2.834-4.669-2.834c-0.243,0-0.471,0.016-0.662,0.035l-13.201,0.573l10.858-10.302%20l0.248-0.132l2.895-3.426l-1.536-3.02c-0.887-1.743-2.67-2.826-4.654-2.826c-0.489,0-0.928,0.066-1.289,0.15l-35.572,6.485%20c-0.648-0.844-1.308-1.672-1.984-2.479c-12.529-14.959-29.409-22.865-48.814-22.865c-4.852,0-9.917,0.497-15.056,1.476%20c-36.111,6.882-53.736,34.2-72.396,63.122c-24.924,38.632-50.697,78.579-124.683,78.579c-6.337,0-13.028-0.301-19.886-0.896%20c-0.695-0.061-1.358-0.091-1.97-0.091c-3.851,0-6.544,1.251-8.006,3.717c-2.208,3.727-0.062,7.647,0.969,9.531l0.142,0.261%20c13.907,25.674,34.957,47.705,60.9,63.712c23.211,14.321,49.99,23.099,76.99,25.806v52.677c0,6.747,5.517,12.171,12.265,12.171%20h28.832c0.799,0,1.593-0.092,2.373-0.243c0.783,0.158,1.593,0.243,2.422,0.243h28.832c3.66,0,7.256-1.609,9.62-4.359%20c2.116-2.461,3.024-5.496,2.556-8.58c-1.294-8.509-12.61-11.532-22.768-11.532c-2.314,0-6.642,0.184-11.032,1.307l3.213-44.469%20c3.401-0.65,6.804-1.365,10.205-2.186c46.987-11.342,72.971-42.049,86.494-65.814c16.654-29.266,23.972-64.827,20.076-97.568%20c-0.326-2.739-0.727-5.427-1.202-8.063l27.382-21.343c1.025-0.608,1.824-1.513,2.262-2.59%20C342.051,59.4,341.991,57.852,341.338,56.544z%20M173.964,301.607c-1-0.067-2.282-0.101-3.326-0.101c-2.314,0-6.727,0.18-11.117,1.303%20l3.079-40.844c3.728-0.08,7.365-0.271,11.365-0.568V301.607z%20M137.724,201.387c-15.404,10.814-31.967,11.775-41.318-4.436%20c-10.372,3.407-21.528,2.202-26.284-7.327c-1.491-2.988,0.775-5.469,2.189-5.541c52.375-2.654,99.886-43.521,118.922-86.605%20c1.398-3.165,5.691-3.562,6.524-0.52C212.89,152.204,194.946,219.858,137.724,201.387z%20M242.354,87.651%20c-9.213,0-16.682-7.469-16.682-16.682s7.469-16.682,16.682-16.682c9.213,0,16.682,7.469,16.682,16.682%20S251.567,87.651,242.354,87.651z'/%3e%3c/svg%3e",Ug=""+new URL("check-beep-CDZhnwrK.mp3",import.meta.url).href,$g="data:audio/mpeg;base64,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",Bg=""+new URL("success-BAkgJFt2.mp3",import.meta.url).href,Vg=""+new URL("failed-DvCgm1Ng.mp3",import.meta.url).href;class wo{constructor(t){Bu(this,"audioElement",null);typeof window<"u"&&(this.audioElement=new Audio(t),this.audioElement.preload="auto")}play(){this.audioElement&&(this.audioElement.currentTime=0,this.audioElement.play().catch(t=>{console.error("Audio playback failed:",t)}))}}const vm=new wo(Ug),Wg=new wo($g),On=new wo(Bg),zn=new wo(Vg),ls=[{type:"short",distance:1,img:Ig},{type:"medium",distance:2,img:Dg},{type:"long",distance:3,img:Fg}],ve=[{timer:60,targetScore:24,rows:6,columns:6},{timer:60,targetScore:28,rows:6,columns:6}],Gg=new Array(ve[0].rows).fill(new Array(ve[0].columns).fill({piece:ls[0],status:"full",highlighted:!1})),ym=()=>ls[Math.floor(Math.random()*ls.length)],Hg=(e="full",t=!0)=>({piece:ym(),status:e,highlighted:t}),Yg=e=>{switch(e){case 0:case 1:return"";case 2:return zn.play(),"You suck at this :D";case 3:return On.play(),"Sometimes you win :O";case 4:return"Reset!";default:return`Error: Unknown game status ${e}`}},Za="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='iso-8859-1'?%3e%3csvg%20fill='%23FFFFFF'%20xmlns='http://www.w3.org/2000/svg'%20height='16'%20viewBox='0%20-960%20960%20960'%20width='16'%3e%3cpath%20d='m287-252-33-35%20192-193-192-195%2033-35%20194%20195%20192-195%2033%2035-192%20195%20192%20193-33%2035-192-195-194%20195Z'/%3e%3c/svg%3e",Qg=""+new URL("background-M-NnOTog.svg",import.meta.url).href;var wm={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/(function(e){(function(){var t={}.hasOwnProperty;function n(){for(var i="",o=0;o<arguments.length;o++){var s=arguments[o];s&&(i=a(i,r(s)))}return i}function r(i){if(typeof i=="string"||typeof i=="number")return i;if(typeof i!="object")return"";if(Array.isArray(i))return n.apply(null,i);if(i.toString!==Object.prototype.toString&&!i.toString.toString().includes("[native code]"))return i.toString();var o="";for(var s in i)t.call(i,s)&&i[s]&&(o=a(o,s));return o}function a(i,o){return o?i?i+" "+o:i+o:i}e.exports?(n.default=n,e.exports=n):window.classNames=n})()})(wm);var qg=wm.exports;const Ke=Ji(qg),wa=({color:e,label:t,icon:n,href:r,disabled:a,onClick:i,children:o})=>{const s={title:t||(o==null?void 0:o.toString()),className:Ke(`
                rounded-lg
                px-4 py-2
                text-xl
                font-medium
                transition-colors
                duration-100
                ease-in-out
                disabled:opacity-50
            `,e==="purple"?"bg-vivid-violet-600/25 text-vivid-violet-600 enabled:hover:bg-vivid-violet-600/50":e==="green"?"bg-turquoise-400/25 text-turquoise-400 enabled:hover:bg-turquoise-400/50":""),"aria-label":t||(o==null?void 0:o.toString()),disabled:a,onClick:i},l=y.jsxs(y.Fragment,{children:[n&&n,o&&o]});return r?y.jsx(os,{to:r,...s,children:l}):y.jsx("button",{...s,children:l})},Kg=(e,t)=>{const n=v.useRef(),r=v.useRef(),a=v.useCallback(()=>{function i(){var o;(o=n.current)==null||o.call(n)}return clearTimeout(r.current),r.current=setTimeout(i,t),()=>clearTimeout(r.current)},[t]);return v.useEffect(()=>{n.current=e},[e]),a},Xg=(e,t,n)=>{const r=v.useRef();v.useEffect(()=>{r.current=e},[e]),v.useEffect(()=>{function a(){var i;(i=r.current)==null||i.call(r)}if(t!==null&&!n){const i=setInterval(a,t);return()=>clearInterval(i)}},[t,n])},Zg=(e,t,n=50)=>{const r=v.useRef(),[a,i]=v.useState(),[o,s]=v.useState(0),[l,u]=v.useState(!1);v.useEffect(()=>{r.current=e},[e]);const f=v.useCallback(()=>{var A;if(a===void 0||l)return;const h=Date.now()-a,w=Math.max(Math.min(h/t,1),0)*100;w<=100&&Wg.play(),w===100&&((A=r.current)==null||A.call(r),console.log("finished ticks")),s(w)},[t,l,a]),c=()=>{s(0),i(Date.now()),u(!1)},d=()=>{f(),u(!0)};return Xg(f,n,o===100),v.useEffect(c,[]),[o,c,d]},Jg=e=>t=>{e(+t.target.value)},we=({title:e,value:t,setValue:n,min:r,max:a})=>y.jsxs("div",{className:`
            text-xl
            flex flex-col
            gap-1
            items-center justify-center
            pb-[45px]
        `,children:[y.jsx("div",{className:`
                relative w-[calc(100%-20px)]
            `,children:y.jsx("span",{className:`
                    absolute text-center
                    size-[25px]
                    text-[rgb(22_40_52/97.9)]
                    top-[60px]
                    [font-size:15px]
                    [transform:translateX(-50%)]
                    [line-height:45px]
                    z-[2]

                    after:box-content
                    after:absolute after:text-center
                    after:size-[25px]
                    after:left-[50%]
                    after:top-[30%]
                    after:[font-size:15px]
                    after:[line-height:45px]
                    after:content-['']
                    after:bg-spring-green-300
                    after:[transform:translateX(-50%)_rotate(45deg)]
                    after:[border:3px_solid_rgb(22_40_52/0.651)]
                    after:rounded-b-[70%] after:rounded-tr-[70%]
                    after:z-[-1]
                    `,style:{left:`${(t-r)/(a-r)*100}%`},children:t})}),y.jsx("label",{htmlFor:`settings-range-${e}`,className:"text-[rgb(94_93_93)]",children:e}),y.jsx("input",{className:Ke(`
                    appearance-none
                    w-full h-5
                    bg-transparent
                    outline-none
                    cursor-pointer
                    rounded-full
                    [box-shadow:0_0_2px_rgb(162_216_250)]
                    transition-all duration-300
                    `,"accent-spring-green-300",`
                    [&::-webkit-slider-thumb]:box-border
                    [&::-webkit-slider-thumb]:bg-spring-green-300
                    [&::-webkit-slider-thumb]:rounded-full
                    [&::-webkit-slider-thumb]:appearance-none
                    [&::-webkit-slider-thumb]:size-5
                    [&::-webkit-slider-thumb]:[border:1px_solid_rgb(22_40_52/0.651)]
                    `,`
                    [&::-moz-range-thumb]:box-border
                    [&::-moz-range-thumb]:bg-spring-green-300
                    [&::-moz-range-thumb]:rounded-full
                    [&::-moz-range-thumb]:appearance-none
                    [&::-moz-range-thumb]:size-5
                    [&::-moz-range-thumb]:[border:1px_solid_rgb(22_40_52/0.651)]
                    `),type:"range",id:`settings-range-${e}`,min:r,max:a,value:t,onChange:Jg(n)})]}),ev=({children:e,handleReset:t,handleSave:n,visible:r,setVisible:a})=>y.jsxs(y.Fragment,{children:[y.jsx("div",{className:Ke(`
                        fixed w-full h-full
                        top-0 left-0
                        bg-black/50
                        z-30
                    `,r?"":"hidden"),onClick:()=>a(!1)}),y.jsxs("div",{className:Ke(`
                    absolute
                    w-[640px] max-w-[90%]
                    p-4 gap-5
                    top-[300px]
                    left-[50%]
                    my-5
                    -translate-x-1/2
                    flex flex-col
                    bg-radient-circle-c
                    from-[rgb(15_27_33/0.781)]
                    to-[rgb(15_27_33)]
                    [outline:3px_solid_rgb(84_255_164)]
                    rounded-lg
                    z-40
                    bg-[rgb(15_27_33/0.781)]
                `,r?"":"hidden"),children:[y.jsxs("div",{className:`
                    mb-5
                    h-10
                    flex items-center justify-between
                    gap-4
                `,children:[y.jsx("h2",{className:`
                        text-lg
                        sm:text-2xl
                        text-spring-green-300
                        [text-shadow:0_0_40px_rgb(162_216_250)]
                    `,children:"Settings"}),y.jsx("button",{onClick:()=>a(!1),children:y.jsx("i",{className:`
                                fas fa-xmark
                                text-lg sm:text-2xl
                                text-[rgb(94_93_93)]
                                aspect-square
                            `})})]}),e,y.jsxs("div",{className:"flex w-full gap-1 *:flex-1",children:[y.jsx(wa,{color:"green",onClick:()=>{a(!1),n()},children:"Save"}),y.jsx(wa,{color:"purple",onClick:()=>{a(!1),t()},children:"Reset"})]})]})]}),tv="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='iso-8859-1'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20height='800px'%20width='800px'%20version='1.1'%20id='Capa_1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20viewBox='0%200%2053.626%2053.626'%20xml:space='preserve'%3e%3cg%3e%3cpath%20style='fill:%235e5d5d;'%20d='M48.831,15.334c-7.083-11.637-17.753-3.541-17.753-3.541c-0.692,0.523-1.968,0.953-2.835,0.955%20l-2.858,0.002c-0.867,0.001-2.143-0.429-2.834-0.952c0,0-10.671-8.098-17.755,3.539C-2.286,26.97,0.568,39.639,0.568,39.639%20c0.5,3.102,2.148,5.172,5.258,4.912c3.101-0.259,9.832-8.354,9.832-8.354c0.556-0.667,1.721-1.212,2.586-1.212l17.134-0.003%20c0.866,0,2.03,0.545,2.585,1.212c0,0,6.732,8.095,9.838,8.354c3.106,0.26,4.758-1.812,5.255-4.912%20C53.055,39.636,55.914,26.969,48.831,15.334z%20M20.374,24.806H16.7v3.541c0,0-0.778,0.594-1.982,0.579%20c-1.202-0.018-1.746-0.648-1.746-0.648v-3.471h-3.47c0,0-0.433-0.444-0.549-1.613c-0.114-1.169,0.479-2.114,0.479-2.114h3.675%20v-3.674c0,0,0.756-0.405,1.843-0.374c1.088,0.034,1.885,0.443,1.885,0.443l-0.015,3.604h3.47c0,0,0.606,0.778,0.656,1.718%20C20.996,23.738,20.374,24.806,20.374,24.806z%20M37.226,28.842c-1.609,0-2.906-1.301-2.906-2.908c0-1.61,1.297-2.908,2.906-2.908%20c1.602,0,2.909,1.298,2.909,2.908C40.135,27.542,38.828,28.842,37.226,28.842z%20M37.226,20.841c-1.609,0-2.906-1.3-2.906-2.907%20c0-1.61,1.297-2.908,2.906-2.908c1.602,0,2.909,1.298,2.909,2.908C40.135,19.542,38.828,20.841,37.226,20.841z%20M44.468,25.136%20c-1.609,0-2.906-1.3-2.906-2.908c0-1.609,1.297-2.908,2.906-2.908c1.602,0,2.909,1.299,2.909,2.908%20C47.377,23.836,46.07,25.136,44.468,25.136z'/%3e%3c/g%3e%3c/svg%3e";function lf(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),n.push.apply(n,r)}return n}function L(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?lf(Object(n),!0).forEach(function(r){ge(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):lf(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function Qi(e){"@babel/helpers - typeof";return Qi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qi(e)}function nv(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function rv(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function av(e,t,n){return t&&rv(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function ge(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function xu(e,t){return ov(e)||sv(e,t)||xm(e,t)||cv()}function Ta(e){return iv(e)||lv(e)||xm(e)||uv()}function iv(e){if(Array.isArray(e))return ss(e)}function ov(e){if(Array.isArray(e))return e}function lv(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function sv(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var r=[],a=!0,i=!1,o,s;try{for(n=n.call(e);!(a=(o=n.next()).done)&&(r.push(o.value),!(t&&r.length===t));a=!0);}catch(l){i=!0,s=l}finally{try{!a&&n.return!=null&&n.return()}finally{if(i)throw s}}return r}}function xm(e,t){if(e){if(typeof e=="string")return ss(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ss(e,t)}}function ss(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function uv(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function cv(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var sf=function(){},Au={},Am={},km=null,Sm={mark:sf,measure:sf};try{typeof window<"u"&&(Au=window),typeof document<"u"&&(Am=document),typeof MutationObserver<"u"&&(km=MutationObserver),typeof performance<"u"&&(Sm=performance)}catch{}var fv=Au.navigator||{},uf=fv.userAgent,cf=uf===void 0?"":uf,nn=Au,re=Am,ff=km,Ja=Sm;nn.document;var Ot=!!re.documentElement&&!!re.head&&typeof re.addEventListener=="function"&&typeof re.createElement=="function",bm=~cf.indexOf("MSIE")||~cf.indexOf("Trident/"),ei,ti,ni,ri,ai,Tt="___FONT_AWESOME___",us=16,Em="fa",Cm="svg-inline--fa",_n="data-fa-i2svg",cs="data-fa-pseudo-element",dv="data-fa-pseudo-element-pending",ku="data-prefix",Su="data-icon",df="fontawesome-i2svg",pv="async",mv=["HTML","HEAD","STYLE","SCRIPT"],Nm=function(){try{return!0}catch{return!1}}(),te="classic",ce="sharp",bu=[te,ce];function Ra(e){return new Proxy(e,{get:function(n,r){return r in n?n[r]:n[te]}})}var xa=Ra((ei={},ge(ei,te,{fa:"solid",fas:"solid","fa-solid":"solid",far:"regular","fa-regular":"regular",fal:"light","fa-light":"light",fat:"thin","fa-thin":"thin",fad:"duotone","fa-duotone":"duotone",fab:"brands","fa-brands":"brands",fak:"kit",fakd:"kit","fa-kit":"kit","fa-kit-duotone":"kit"}),ge(ei,ce,{fa:"solid",fass:"solid","fa-solid":"solid",fasr:"regular","fa-regular":"regular",fasl:"light","fa-light":"light",fast:"thin","fa-thin":"thin"}),ei)),Aa=Ra((ti={},ge(ti,te,{solid:"fas",regular:"far",light:"fal",thin:"fat",duotone:"fad",brands:"fab",kit:"fak"}),ge(ti,ce,{solid:"fass",regular:"fasr",light:"fasl",thin:"fast"}),ti)),ka=Ra((ni={},ge(ni,te,{fab:"fa-brands",fad:"fa-duotone",fak:"fa-kit",fal:"fa-light",far:"fa-regular",fas:"fa-solid",fat:"fa-thin"}),ge(ni,ce,{fass:"fa-solid",fasr:"fa-regular",fasl:"fa-light",fast:"fa-thin"}),ni)),hv=Ra((ri={},ge(ri,te,{"fa-brands":"fab","fa-duotone":"fad","fa-kit":"fak","fa-light":"fal","fa-regular":"far","fa-solid":"fas","fa-thin":"fat"}),ge(ri,ce,{"fa-solid":"fass","fa-regular":"fasr","fa-light":"fasl","fa-thin":"fast"}),ri)),gv=/fa(s|r|l|t|d|b|k|ss|sr|sl|st)?[\-\ ]/,_m="fa-layers-text",vv=/Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp|Kit)?.*/i,yv=Ra((ai={},ge(ai,te,{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"}),ge(ai,ce,{900:"fass",400:"fasr",300:"fasl",100:"fast"}),ai)),Pm=[1,2,3,4,5,6,7,8,9,10],wv=Pm.concat([11,12,13,14,15,16,17,18,19,20]),xv=["class","data-prefix","data-icon","data-fa-transform","data-fa-mask"],wn={GROUP:"duotone-group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},Sa=new Set;Object.keys(Aa[te]).map(Sa.add.bind(Sa));Object.keys(Aa[ce]).map(Sa.add.bind(Sa));var Av=[].concat(bu,Ta(Sa),["2xs","xs","sm","lg","xl","2xl","beat","border","fade","beat-fade","bounce","flip-both","flip-horizontal","flip-vertical","flip","fw","inverse","layers-counter","layers-text","layers","li","pull-left","pull-right","pulse","rotate-180","rotate-270","rotate-90","rotate-by","shake","spin-pulse","spin-reverse","spin","stack-1x","stack-2x","stack","ul",wn.GROUP,wn.SWAP_OPACITY,wn.PRIMARY,wn.SECONDARY]).concat(Pm.map(function(e){return"".concat(e,"x")})).concat(wv.map(function(e){return"w-".concat(e)})),Kr=nn.FontAwesomeConfig||{};function kv(e){var t=re.querySelector("script["+e+"]");if(t)return t.getAttribute(e)}function Sv(e){return e===""?!0:e==="false"?!1:e==="true"?!0:e}if(re&&typeof re.querySelector=="function"){var bv=[["data-family-prefix","familyPrefix"],["data-css-prefix","cssPrefix"],["data-family-default","familyDefault"],["data-style-default","styleDefault"],["data-replacement-class","replacementClass"],["data-auto-replace-svg","autoReplaceSvg"],["data-auto-add-css","autoAddCss"],["data-auto-a11y","autoA11y"],["data-search-pseudo-elements","searchPseudoElements"],["data-observe-mutations","observeMutations"],["data-mutate-approach","mutateApproach"],["data-keep-original-source","keepOriginalSource"],["data-measure-performance","measurePerformance"],["data-show-missing-icons","showMissingIcons"]];bv.forEach(function(e){var t=xu(e,2),n=t[0],r=t[1],a=Sv(kv(n));a!=null&&(Kr[r]=a)})}var jm={styleDefault:"solid",familyDefault:"classic",cssPrefix:Em,replacementClass:Cm,autoReplaceSvg:!0,autoAddCss:!0,autoA11y:!0,searchPseudoElements:!1,observeMutations:!0,mutateApproach:"async",keepOriginalSource:!0,measurePerformance:!1,showMissingIcons:!0};Kr.familyPrefix&&(Kr.cssPrefix=Kr.familyPrefix);var wr=L(L({},jm),Kr);wr.autoReplaceSvg||(wr.observeMutations=!1);var z={};Object.keys(jm).forEach(function(e){Object.defineProperty(z,e,{enumerable:!0,set:function(n){wr[e]=n,Xr.forEach(function(r){return r(z)})},get:function(){return wr[e]}})});Object.defineProperty(z,"familyPrefix",{enumerable:!0,set:function(t){wr.cssPrefix=t,Xr.forEach(function(n){return n(z)})},get:function(){return wr.cssPrefix}});nn.FontAwesomeConfig=z;var Xr=[];function Ev(e){return Xr.push(e),function(){Xr.splice(Xr.indexOf(e),1)}}var It=us,yt={size:16,x:0,y:0,rotate:0,flipX:!1,flipY:!1};function Cv(e){if(!(!e||!Ot)){var t=re.createElement("style");t.setAttribute("type","text/css"),t.innerHTML=e;for(var n=re.head.childNodes,r=null,a=n.length-1;a>-1;a--){var i=n[a],o=(i.tagName||"").toUpperCase();["STYLE","LINK"].indexOf(o)>-1&&(r=i)}return re.head.insertBefore(t,r),e}}var Nv="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";function ba(){for(var e=12,t="";e-- >0;)t+=Nv[Math.random()*62|0];return t}function br(e){for(var t=[],n=(e||[]).length>>>0;n--;)t[n]=e[n];return t}function Eu(e){return e.classList?br(e.classList):(e.getAttribute("class")||"").split(" ").filter(function(t){return t})}function Tm(e){return"".concat(e).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function _v(e){return Object.keys(e||{}).reduce(function(t,n){return t+"".concat(n,'="').concat(Tm(e[n]),'" ')},"").trim()}function xo(e){return Object.keys(e||{}).reduce(function(t,n){return t+"".concat(n,": ").concat(e[n].trim(),";")},"")}function Cu(e){return e.size!==yt.size||e.x!==yt.x||e.y!==yt.y||e.rotate!==yt.rotate||e.flipX||e.flipY}function Pv(e){var t=e.transform,n=e.containerWidth,r=e.iconWidth,a={transform:"translate(".concat(n/2," 256)")},i="translate(".concat(t.x*32,", ").concat(t.y*32,") "),o="scale(".concat(t.size/16*(t.flipX?-1:1),", ").concat(t.size/16*(t.flipY?-1:1),") "),s="rotate(".concat(t.rotate," 0 0)"),l={transform:"".concat(i," ").concat(o," ").concat(s)},u={transform:"translate(".concat(r/2*-1," -256)")};return{outer:a,inner:l,path:u}}function jv(e){var t=e.transform,n=e.width,r=n===void 0?us:n,a=e.height,i=a===void 0?us:a,o=e.startCentered,s=o===void 0?!1:o,l="";return s&&bm?l+="translate(".concat(t.x/It-r/2,"em, ").concat(t.y/It-i/2,"em) "):s?l+="translate(calc(-50% + ".concat(t.x/It,"em), calc(-50% + ").concat(t.y/It,"em)) "):l+="translate(".concat(t.x/It,"em, ").concat(t.y/It,"em) "),l+="scale(".concat(t.size/It*(t.flipX?-1:1),", ").concat(t.size/It*(t.flipY?-1:1),") "),l+="rotate(".concat(t.rotate,"deg) "),l}var Tv=`:root, :host {
  --fa-font-solid: normal 900 1em/1 "Font Awesome 6 Solid";
  --fa-font-regular: normal 400 1em/1 "Font Awesome 6 Regular";
  --fa-font-light: normal 300 1em/1 "Font Awesome 6 Light";
  --fa-font-thin: normal 100 1em/1 "Font Awesome 6 Thin";
  --fa-font-duotone: normal 900 1em/1 "Font Awesome 6 Duotone";
  --fa-font-sharp-solid: normal 900 1em/1 "Font Awesome 6 Sharp";
  --fa-font-sharp-regular: normal 400 1em/1 "Font Awesome 6 Sharp";
  --fa-font-sharp-light: normal 300 1em/1 "Font Awesome 6 Sharp";
  --fa-font-sharp-thin: normal 100 1em/1 "Font Awesome 6 Sharp";
  --fa-font-brands: normal 400 1em/1 "Font Awesome 6 Brands";
}

svg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {
  overflow: visible;
  box-sizing: content-box;
}

.svg-inline--fa {
  display: var(--fa-display, inline-block);
  height: 1em;
  overflow: visible;
  vertical-align: -0.125em;
}
.svg-inline--fa.fa-2xs {
  vertical-align: 0.1em;
}
.svg-inline--fa.fa-xs {
  vertical-align: 0em;
}
.svg-inline--fa.fa-sm {
  vertical-align: -0.0714285705em;
}
.svg-inline--fa.fa-lg {
  vertical-align: -0.2em;
}
.svg-inline--fa.fa-xl {
  vertical-align: -0.25em;
}
.svg-inline--fa.fa-2xl {
  vertical-align: -0.3125em;
}
.svg-inline--fa.fa-pull-left {
  margin-right: var(--fa-pull-margin, 0.3em);
  width: auto;
}
.svg-inline--fa.fa-pull-right {
  margin-left: var(--fa-pull-margin, 0.3em);
  width: auto;
}
.svg-inline--fa.fa-li {
  width: var(--fa-li-width, 2em);
  top: 0.25em;
}
.svg-inline--fa.fa-fw {
  width: var(--fa-fw-width, 1.25em);
}

.fa-layers svg.svg-inline--fa {
  bottom: 0;
  left: 0;
  margin: auto;
  position: absolute;
  right: 0;
  top: 0;
}

.fa-layers-counter, .fa-layers-text {
  display: inline-block;
  position: absolute;
  text-align: center;
}

.fa-layers {
  display: inline-block;
  height: 1em;
  position: relative;
  text-align: center;
  vertical-align: -0.125em;
  width: 1em;
}
.fa-layers svg.svg-inline--fa {
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.fa-layers-text {
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.fa-layers-counter {
  background-color: var(--fa-counter-background-color, #ff253a);
  border-radius: var(--fa-counter-border-radius, 1em);
  box-sizing: border-box;
  color: var(--fa-inverse, #fff);
  line-height: var(--fa-counter-line-height, 1);
  max-width: var(--fa-counter-max-width, 5em);
  min-width: var(--fa-counter-min-width, 1.5em);
  overflow: hidden;
  padding: var(--fa-counter-padding, 0.25em 0.5em);
  right: var(--fa-right, 0);
  text-overflow: ellipsis;
  top: var(--fa-top, 0);
  -webkit-transform: scale(var(--fa-counter-scale, 0.25));
          transform: scale(var(--fa-counter-scale, 0.25));
  -webkit-transform-origin: top right;
          transform-origin: top right;
}

.fa-layers-bottom-right {
  bottom: var(--fa-bottom, 0);
  right: var(--fa-right, 0);
  top: auto;
  -webkit-transform: scale(var(--fa-layers-scale, 0.25));
          transform: scale(var(--fa-layers-scale, 0.25));
  -webkit-transform-origin: bottom right;
          transform-origin: bottom right;
}

.fa-layers-bottom-left {
  bottom: var(--fa-bottom, 0);
  left: var(--fa-left, 0);
  right: auto;
  top: auto;
  -webkit-transform: scale(var(--fa-layers-scale, 0.25));
          transform: scale(var(--fa-layers-scale, 0.25));
  -webkit-transform-origin: bottom left;
          transform-origin: bottom left;
}

.fa-layers-top-right {
  top: var(--fa-top, 0);
  right: var(--fa-right, 0);
  -webkit-transform: scale(var(--fa-layers-scale, 0.25));
          transform: scale(var(--fa-layers-scale, 0.25));
  -webkit-transform-origin: top right;
          transform-origin: top right;
}

.fa-layers-top-left {
  left: var(--fa-left, 0);
  right: auto;
  top: var(--fa-top, 0);
  -webkit-transform: scale(var(--fa-layers-scale, 0.25));
          transform: scale(var(--fa-layers-scale, 0.25));
  -webkit-transform-origin: top left;
          transform-origin: top left;
}

.fa-1x {
  font-size: 1em;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

.fa-4x {
  font-size: 4em;
}

.fa-5x {
  font-size: 5em;
}

.fa-6x {
  font-size: 6em;
}

.fa-7x {
  font-size: 7em;
}

.fa-8x {
  font-size: 8em;
}

.fa-9x {
  font-size: 9em;
}

.fa-10x {
  font-size: 10em;
}

.fa-2xs {
  font-size: 0.625em;
  line-height: 0.1em;
  vertical-align: 0.225em;
}

.fa-xs {
  font-size: 0.75em;
  line-height: 0.0833333337em;
  vertical-align: 0.125em;
}

.fa-sm {
  font-size: 0.875em;
  line-height: 0.0714285718em;
  vertical-align: 0.0535714295em;
}

.fa-lg {
  font-size: 1.25em;
  line-height: 0.05em;
  vertical-align: -0.075em;
}

.fa-xl {
  font-size: 1.5em;
  line-height: 0.0416666682em;
  vertical-align: -0.125em;
}

.fa-2xl {
  font-size: 2em;
  line-height: 0.03125em;
  vertical-align: -0.1875em;
}

.fa-fw {
  text-align: center;
  width: 1.25em;
}

.fa-ul {
  list-style-type: none;
  margin-left: var(--fa-li-margin, 2.5em);
  padding-left: 0;
}
.fa-ul > li {
  position: relative;
}

.fa-li {
  left: calc(var(--fa-li-width, 2em) * -1);
  position: absolute;
  text-align: center;
  width: var(--fa-li-width, 2em);
  line-height: inherit;
}

.fa-border {
  border-color: var(--fa-border-color, #eee);
  border-radius: var(--fa-border-radius, 0.1em);
  border-style: var(--fa-border-style, solid);
  border-width: var(--fa-border-width, 0.08em);
  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);
}

.fa-pull-left {
  float: left;
  margin-right: var(--fa-pull-margin, 0.3em);
}

.fa-pull-right {
  float: right;
  margin-left: var(--fa-pull-margin, 0.3em);
}

.fa-beat {
  -webkit-animation-name: fa-beat;
          animation-name: fa-beat;
  -webkit-animation-delay: var(--fa-animation-delay, 0s);
          animation-delay: var(--fa-animation-delay, 0s);
  -webkit-animation-direction: var(--fa-animation-direction, normal);
          animation-direction: var(--fa-animation-direction, normal);
  -webkit-animation-duration: var(--fa-animation-duration, 1s);
          animation-duration: var(--fa-animation-duration, 1s);
  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
          animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  -webkit-animation-timing-function: var(--fa-animation-timing, ease-in-out);
          animation-timing-function: var(--fa-animation-timing, ease-in-out);
}

.fa-bounce {
  -webkit-animation-name: fa-bounce;
          animation-name: fa-bounce;
  -webkit-animation-delay: var(--fa-animation-delay, 0s);
          animation-delay: var(--fa-animation-delay, 0s);
  -webkit-animation-direction: var(--fa-animation-direction, normal);
          animation-direction: var(--fa-animation-direction, normal);
  -webkit-animation-duration: var(--fa-animation-duration, 1s);
          animation-duration: var(--fa-animation-duration, 1s);
  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
          animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));
          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));
}

.fa-fade {
  -webkit-animation-name: fa-fade;
          animation-name: fa-fade;
  -webkit-animation-delay: var(--fa-animation-delay, 0s);
          animation-delay: var(--fa-animation-delay, 0s);
  -webkit-animation-direction: var(--fa-animation-direction, normal);
          animation-direction: var(--fa-animation-direction, normal);
  -webkit-animation-duration: var(--fa-animation-duration, 1s);
          animation-duration: var(--fa-animation-duration, 1s);
  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
          animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
}

.fa-beat-fade {
  -webkit-animation-name: fa-beat-fade;
          animation-name: fa-beat-fade;
  -webkit-animation-delay: var(--fa-animation-delay, 0s);
          animation-delay: var(--fa-animation-delay, 0s);
  -webkit-animation-direction: var(--fa-animation-direction, normal);
          animation-direction: var(--fa-animation-direction, normal);
  -webkit-animation-duration: var(--fa-animation-duration, 1s);
          animation-duration: var(--fa-animation-duration, 1s);
  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
          animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
}

.fa-flip {
  -webkit-animation-name: fa-flip;
          animation-name: fa-flip;
  -webkit-animation-delay: var(--fa-animation-delay, 0s);
          animation-delay: var(--fa-animation-delay, 0s);
  -webkit-animation-direction: var(--fa-animation-direction, normal);
          animation-direction: var(--fa-animation-direction, normal);
  -webkit-animation-duration: var(--fa-animation-duration, 1s);
          animation-duration: var(--fa-animation-duration, 1s);
  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
          animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  -webkit-animation-timing-function: var(--fa-animation-timing, ease-in-out);
          animation-timing-function: var(--fa-animation-timing, ease-in-out);
}

.fa-shake {
  -webkit-animation-name: fa-shake;
          animation-name: fa-shake;
  -webkit-animation-delay: var(--fa-animation-delay, 0s);
          animation-delay: var(--fa-animation-delay, 0s);
  -webkit-animation-direction: var(--fa-animation-direction, normal);
          animation-direction: var(--fa-animation-direction, normal);
  -webkit-animation-duration: var(--fa-animation-duration, 1s);
          animation-duration: var(--fa-animation-duration, 1s);
  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
          animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  -webkit-animation-timing-function: var(--fa-animation-timing, linear);
          animation-timing-function: var(--fa-animation-timing, linear);
}

.fa-spin {
  -webkit-animation-name: fa-spin;
          animation-name: fa-spin;
  -webkit-animation-delay: var(--fa-animation-delay, 0s);
          animation-delay: var(--fa-animation-delay, 0s);
  -webkit-animation-direction: var(--fa-animation-direction, normal);
          animation-direction: var(--fa-animation-direction, normal);
  -webkit-animation-duration: var(--fa-animation-duration, 2s);
          animation-duration: var(--fa-animation-duration, 2s);
  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
          animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  -webkit-animation-timing-function: var(--fa-animation-timing, linear);
          animation-timing-function: var(--fa-animation-timing, linear);
}

.fa-spin-reverse {
  --fa-animation-direction: reverse;
}

.fa-pulse,
.fa-spin-pulse {
  -webkit-animation-name: fa-spin;
          animation-name: fa-spin;
  -webkit-animation-direction: var(--fa-animation-direction, normal);
          animation-direction: var(--fa-animation-direction, normal);
  -webkit-animation-duration: var(--fa-animation-duration, 1s);
          animation-duration: var(--fa-animation-duration, 1s);
  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
          animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  -webkit-animation-timing-function: var(--fa-animation-timing, steps(8));
          animation-timing-function: var(--fa-animation-timing, steps(8));
}

@media (prefers-reduced-motion: reduce) {
  .fa-beat,
.fa-bounce,
.fa-fade,
.fa-beat-fade,
.fa-flip,
.fa-pulse,
.fa-shake,
.fa-spin,
.fa-spin-pulse {
    -webkit-animation-delay: -1ms;
            animation-delay: -1ms;
    -webkit-animation-duration: 1ms;
            animation-duration: 1ms;
    -webkit-animation-iteration-count: 1;
            animation-iteration-count: 1;
    -webkit-transition-delay: 0s;
            transition-delay: 0s;
    -webkit-transition-duration: 0s;
            transition-duration: 0s;
  }
}
@-webkit-keyframes fa-beat {
  0%, 90% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  45% {
    -webkit-transform: scale(var(--fa-beat-scale, 1.25));
            transform: scale(var(--fa-beat-scale, 1.25));
  }
}
@keyframes fa-beat {
  0%, 90% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  45% {
    -webkit-transform: scale(var(--fa-beat-scale, 1.25));
            transform: scale(var(--fa-beat-scale, 1.25));
  }
}
@-webkit-keyframes fa-bounce {
  0% {
    -webkit-transform: scale(1, 1) translateY(0);
            transform: scale(1, 1) translateY(0);
  }
  10% {
    -webkit-transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);
            transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);
  }
  30% {
    -webkit-transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));
            transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));
  }
  50% {
    -webkit-transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);
            transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);
  }
  57% {
    -webkit-transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));
            transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));
  }
  64% {
    -webkit-transform: scale(1, 1) translateY(0);
            transform: scale(1, 1) translateY(0);
  }
  100% {
    -webkit-transform: scale(1, 1) translateY(0);
            transform: scale(1, 1) translateY(0);
  }
}
@keyframes fa-bounce {
  0% {
    -webkit-transform: scale(1, 1) translateY(0);
            transform: scale(1, 1) translateY(0);
  }
  10% {
    -webkit-transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);
            transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);
  }
  30% {
    -webkit-transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));
            transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));
  }
  50% {
    -webkit-transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);
            transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);
  }
  57% {
    -webkit-transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));
            transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));
  }
  64% {
    -webkit-transform: scale(1, 1) translateY(0);
            transform: scale(1, 1) translateY(0);
  }
  100% {
    -webkit-transform: scale(1, 1) translateY(0);
            transform: scale(1, 1) translateY(0);
  }
}
@-webkit-keyframes fa-fade {
  50% {
    opacity: var(--fa-fade-opacity, 0.4);
  }
}
@keyframes fa-fade {
  50% {
    opacity: var(--fa-fade-opacity, 0.4);
  }
}
@-webkit-keyframes fa-beat-fade {
  0%, 100% {
    opacity: var(--fa-beat-fade-opacity, 0.4);
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    opacity: 1;
    -webkit-transform: scale(var(--fa-beat-fade-scale, 1.125));
            transform: scale(var(--fa-beat-fade-scale, 1.125));
  }
}
@keyframes fa-beat-fade {
  0%, 100% {
    opacity: var(--fa-beat-fade-opacity, 0.4);
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    opacity: 1;
    -webkit-transform: scale(var(--fa-beat-fade-scale, 1.125));
            transform: scale(var(--fa-beat-fade-scale, 1.125));
  }
}
@-webkit-keyframes fa-flip {
  50% {
    -webkit-transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));
            transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));
  }
}
@keyframes fa-flip {
  50% {
    -webkit-transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));
            transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));
  }
}
@-webkit-keyframes fa-shake {
  0% {
    -webkit-transform: rotate(-15deg);
            transform: rotate(-15deg);
  }
  4% {
    -webkit-transform: rotate(15deg);
            transform: rotate(15deg);
  }
  8%, 24% {
    -webkit-transform: rotate(-18deg);
            transform: rotate(-18deg);
  }
  12%, 28% {
    -webkit-transform: rotate(18deg);
            transform: rotate(18deg);
  }
  16% {
    -webkit-transform: rotate(-22deg);
            transform: rotate(-22deg);
  }
  20% {
    -webkit-transform: rotate(22deg);
            transform: rotate(22deg);
  }
  32% {
    -webkit-transform: rotate(-12deg);
            transform: rotate(-12deg);
  }
  36% {
    -webkit-transform: rotate(12deg);
            transform: rotate(12deg);
  }
  40%, 100% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
}
@keyframes fa-shake {
  0% {
    -webkit-transform: rotate(-15deg);
            transform: rotate(-15deg);
  }
  4% {
    -webkit-transform: rotate(15deg);
            transform: rotate(15deg);
  }
  8%, 24% {
    -webkit-transform: rotate(-18deg);
            transform: rotate(-18deg);
  }
  12%, 28% {
    -webkit-transform: rotate(18deg);
            transform: rotate(18deg);
  }
  16% {
    -webkit-transform: rotate(-22deg);
            transform: rotate(-22deg);
  }
  20% {
    -webkit-transform: rotate(22deg);
            transform: rotate(22deg);
  }
  32% {
    -webkit-transform: rotate(-12deg);
            transform: rotate(-12deg);
  }
  36% {
    -webkit-transform: rotate(12deg);
            transform: rotate(12deg);
  }
  40%, 100% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
}
@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
.fa-rotate-90 {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}

.fa-rotate-180 {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}

.fa-rotate-270 {
  -webkit-transform: rotate(270deg);
          transform: rotate(270deg);
}

.fa-flip-horizontal {
  -webkit-transform: scale(-1, 1);
          transform: scale(-1, 1);
}

.fa-flip-vertical {
  -webkit-transform: scale(1, -1);
          transform: scale(1, -1);
}

.fa-flip-both,
.fa-flip-horizontal.fa-flip-vertical {
  -webkit-transform: scale(-1, -1);
          transform: scale(-1, -1);
}

.fa-rotate-by {
  -webkit-transform: rotate(var(--fa-rotate-angle, 0));
          transform: rotate(var(--fa-rotate-angle, 0));
}

.fa-stack {
  display: inline-block;
  vertical-align: middle;
  height: 2em;
  position: relative;
  width: 2.5em;
}

.fa-stack-1x,
.fa-stack-2x {
  bottom: 0;
  left: 0;
  margin: auto;
  position: absolute;
  right: 0;
  top: 0;
  z-index: var(--fa-stack-z-index, auto);
}

.svg-inline--fa.fa-stack-1x {
  height: 1em;
  width: 1.25em;
}
.svg-inline--fa.fa-stack-2x {
  height: 2em;
  width: 2.5em;
}

.fa-inverse {
  color: var(--fa-inverse, #fff);
}

.sr-only,
.fa-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.sr-only-focusable:not(:focus),
.fa-sr-only-focusable:not(:focus) {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.svg-inline--fa .fa-primary {
  fill: var(--fa-primary-color, currentColor);
  opacity: var(--fa-primary-opacity, 1);
}

.svg-inline--fa .fa-secondary {
  fill: var(--fa-secondary-color, currentColor);
  opacity: var(--fa-secondary-opacity, 0.4);
}

.svg-inline--fa.fa-swap-opacity .fa-primary {
  opacity: var(--fa-secondary-opacity, 0.4);
}

.svg-inline--fa.fa-swap-opacity .fa-secondary {
  opacity: var(--fa-primary-opacity, 1);
}

.svg-inline--fa mask .fa-primary,
.svg-inline--fa mask .fa-secondary {
  fill: black;
}

.fad.fa-inverse,
.fa-duotone.fa-inverse {
  color: var(--fa-inverse, #fff);
}`;function Rm(){var e=Em,t=Cm,n=z.cssPrefix,r=z.replacementClass,a=Tv;if(n!==e||r!==t){var i=new RegExp("\\.".concat(e,"\\-"),"g"),o=new RegExp("\\--".concat(e,"\\-"),"g"),s=new RegExp("\\.".concat(t),"g");a=a.replace(i,".".concat(n,"-")).replace(o,"--".concat(n,"-")).replace(s,".".concat(r))}return a}var pf=!1;function Jo(){z.autoAddCss&&!pf&&(Cv(Rm()),pf=!0)}var Rv={mixout:function(){return{dom:{css:Rm,insertCss:Jo}}},hooks:function(){return{beforeDOMElementCreation:function(){Jo()},beforeI2svg:function(){Jo()}}}},Rt=nn||{};Rt[Tt]||(Rt[Tt]={});Rt[Tt].styles||(Rt[Tt].styles={});Rt[Tt].hooks||(Rt[Tt].hooks={});Rt[Tt].shims||(Rt[Tt].shims=[]);var lt=Rt[Tt],Lm=[],Lv=function e(){re.removeEventListener("DOMContentLoaded",e),qi=1,Lm.map(function(t){return t()})},qi=!1;Ot&&(qi=(re.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(re.readyState),qi||re.addEventListener("DOMContentLoaded",Lv));function Mv(e){Ot&&(qi?setTimeout(e,0):Lm.push(e))}function La(e){var t=e.tag,n=e.attributes,r=n===void 0?{}:n,a=e.children,i=a===void 0?[]:a;return typeof e=="string"?Tm(e):"<".concat(t," ").concat(_v(r),">").concat(i.map(La).join(""),"</").concat(t,">")}function mf(e,t,n){if(e&&e[t]&&e[t][n])return{prefix:t,iconName:n,icon:e[t][n]}}var el=function(t,n,r,a){var i=Object.keys(t),o=i.length,s=n,l,u,f;for(r===void 0?(l=1,f=t[i[0]]):(l=0,f=r);l<o;l++)u=i[l],f=s(f,t[u],u,t);return f};function Ov(e){for(var t=[],n=0,r=e.length;n<r;){var a=e.charCodeAt(n++);if(a>=55296&&a<=56319&&n<r){var i=e.charCodeAt(n++);(i&64512)==56320?t.push(((a&1023)<<10)+(i&1023)+65536):(t.push(a),n--)}else t.push(a)}return t}function fs(e){var t=Ov(e);return t.length===1?t[0].toString(16):null}function zv(e,t){var n=e.length,r=e.charCodeAt(t),a;return r>=55296&&r<=56319&&n>t+1&&(a=e.charCodeAt(t+1),a>=56320&&a<=57343)?(r-55296)*1024+a-56320+65536:r}function hf(e){return Object.keys(e).reduce(function(t,n){var r=e[n],a=!!r.icon;return a?t[r.iconName]=r.icon:t[n]=r,t},{})}function ds(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=n.skipHooks,a=r===void 0?!1:r,i=hf(t);typeof lt.hooks.addPack=="function"&&!a?lt.hooks.addPack(e,hf(t)):lt.styles[e]=L(L({},lt.styles[e]||{}),i),e==="fas"&&ds("fa",t)}var ii,oi,li,tr=lt.styles,Iv=lt.shims,Dv=(ii={},ge(ii,te,Object.values(ka[te])),ge(ii,ce,Object.values(ka[ce])),ii),Nu=null,Mm={},Om={},zm={},Im={},Dm={},Fv=(oi={},ge(oi,te,Object.keys(xa[te])),ge(oi,ce,Object.keys(xa[ce])),oi);function Uv(e){return~Av.indexOf(e)}function $v(e,t){var n=t.split("-"),r=n[0],a=n.slice(1).join("-");return r===e&&a!==""&&!Uv(a)?a:null}var Fm=function(){var t=function(i){return el(tr,function(o,s,l){return o[l]=el(s,i,{}),o},{})};Mm=t(function(a,i,o){if(i[3]&&(a[i[3]]=o),i[2]){var s=i[2].filter(function(l){return typeof l=="number"});s.forEach(function(l){a[l.toString(16)]=o})}return a}),Om=t(function(a,i,o){if(a[o]=o,i[2]){var s=i[2].filter(function(l){return typeof l=="string"});s.forEach(function(l){a[l]=o})}return a}),Dm=t(function(a,i,o){var s=i[2];return a[o]=o,s.forEach(function(l){a[l]=o}),a});var n="far"in tr||z.autoFetchSvg,r=el(Iv,function(a,i){var o=i[0],s=i[1],l=i[2];return s==="far"&&!n&&(s="fas"),typeof o=="string"&&(a.names[o]={prefix:s,iconName:l}),typeof o=="number"&&(a.unicodes[o.toString(16)]={prefix:s,iconName:l}),a},{names:{},unicodes:{}});zm=r.names,Im=r.unicodes,Nu=Ao(z.styleDefault,{family:z.familyDefault})};Ev(function(e){Nu=Ao(e.styleDefault,{family:z.familyDefault})});Fm();function _u(e,t){return(Mm[e]||{})[t]}function Bv(e,t){return(Om[e]||{})[t]}function xn(e,t){return(Dm[e]||{})[t]}function Um(e){return zm[e]||{prefix:null,iconName:null}}function Vv(e){var t=Im[e],n=_u("fas",e);return t||(n?{prefix:"fas",iconName:n}:null)||{prefix:null,iconName:null}}function rn(){return Nu}var Pu=function(){return{prefix:null,iconName:null,rest:[]}};function Ao(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.family,r=n===void 0?te:n,a=xa[r][e],i=Aa[r][e]||Aa[r][a],o=e in lt.styles?e:null;return i||o||null}var gf=(li={},ge(li,te,Object.keys(ka[te])),ge(li,ce,Object.keys(ka[ce])),li);function ko(e){var t,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=n.skipLookups,a=r===void 0?!1:r,i=(t={},ge(t,te,"".concat(z.cssPrefix,"-").concat(te)),ge(t,ce,"".concat(z.cssPrefix,"-").concat(ce)),t),o=null,s=te;(e.includes(i[te])||e.some(function(u){return gf[te].includes(u)}))&&(s=te),(e.includes(i[ce])||e.some(function(u){return gf[ce].includes(u)}))&&(s=ce);var l=e.reduce(function(u,f){var c=$v(z.cssPrefix,f);if(tr[f]?(f=Dv[s].includes(f)?hv[s][f]:f,o=f,u.prefix=f):Fv[s].indexOf(f)>-1?(o=f,u.prefix=Ao(f,{family:s})):c?u.iconName=c:f!==z.replacementClass&&f!==i[te]&&f!==i[ce]&&u.rest.push(f),!a&&u.prefix&&u.iconName){var d=o==="fa"?Um(u.iconName):{},h=xn(u.prefix,u.iconName);d.prefix&&(o=null),u.iconName=d.iconName||h||u.iconName,u.prefix=d.prefix||u.prefix,u.prefix==="far"&&!tr.far&&tr.fas&&!z.autoFetchSvg&&(u.prefix="fas")}return u},Pu());return(e.includes("fa-brands")||e.includes("fab"))&&(l.prefix="fab"),(e.includes("fa-duotone")||e.includes("fad"))&&(l.prefix="fad"),!l.prefix&&s===ce&&(tr.fass||z.autoFetchSvg)&&(l.prefix="fass",l.iconName=xn(l.prefix,l.iconName)||l.iconName),(l.prefix==="fa"||o==="fa")&&(l.prefix=rn()||"fas"),l}var Wv=function(){function e(){nv(this,e),this.definitions={}}return av(e,[{key:"add",value:function(){for(var n=this,r=arguments.length,a=new Array(r),i=0;i<r;i++)a[i]=arguments[i];var o=a.reduce(this._pullDefinitions,{});Object.keys(o).forEach(function(s){n.definitions[s]=L(L({},n.definitions[s]||{}),o[s]),ds(s,o[s]);var l=ka[te][s];l&&ds(l,o[s]),Fm()})}},{key:"reset",value:function(){this.definitions={}}},{key:"_pullDefinitions",value:function(n,r){var a=r.prefix&&r.iconName&&r.icon?{0:r}:r;return Object.keys(a).map(function(i){var o=a[i],s=o.prefix,l=o.iconName,u=o.icon,f=u[2];n[s]||(n[s]={}),f.length>0&&f.forEach(function(c){typeof c=="string"&&(n[s][c]=u)}),n[s][l]=u}),n}}]),e}(),vf=[],nr={},fr={},Gv=Object.keys(fr);function Hv(e,t){var n=t.mixoutsTo;return vf=e,nr={},Object.keys(fr).forEach(function(r){Gv.indexOf(r)===-1&&delete fr[r]}),vf.forEach(function(r){var a=r.mixout?r.mixout():{};if(Object.keys(a).forEach(function(o){typeof a[o]=="function"&&(n[o]=a[o]),Qi(a[o])==="object"&&Object.keys(a[o]).forEach(function(s){n[o]||(n[o]={}),n[o][s]=a[o][s]})}),r.hooks){var i=r.hooks();Object.keys(i).forEach(function(o){nr[o]||(nr[o]=[]),nr[o].push(i[o])})}r.provides&&r.provides(fr)}),n}function ps(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),a=2;a<n;a++)r[a-2]=arguments[a];var i=nr[e]||[];return i.forEach(function(o){t=o.apply(null,[t].concat(r))}),t}function Pn(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var a=nr[e]||[];a.forEach(function(i){i.apply(null,n)})}function Lt(){var e=arguments[0],t=Array.prototype.slice.call(arguments,1);return fr[e]?fr[e].apply(null,t):void 0}function ms(e){e.prefix==="fa"&&(e.prefix="fas");var t=e.iconName,n=e.prefix||rn();if(t)return t=xn(n,t)||t,mf($m.definitions,n,t)||mf(lt.styles,n,t)}var $m=new Wv,Yv=function(){z.autoReplaceSvg=!1,z.observeMutations=!1,Pn("noAuto")},Qv={i2svg:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return Ot?(Pn("beforeI2svg",t),Lt("pseudoElements2svg",t),Lt("i2svg",t)):Promise.reject("Operation requires a DOM of some kind.")},watch:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=t.autoReplaceSvgRoot;z.autoReplaceSvg===!1&&(z.autoReplaceSvg=!0),z.observeMutations=!0,Mv(function(){Kv({autoReplaceSvgRoot:n}),Pn("watch",t)})}},qv={icon:function(t){if(t===null)return null;if(Qi(t)==="object"&&t.prefix&&t.iconName)return{prefix:t.prefix,iconName:xn(t.prefix,t.iconName)||t.iconName};if(Array.isArray(t)&&t.length===2){var n=t[1].indexOf("fa-")===0?t[1].slice(3):t[1],r=Ao(t[0]);return{prefix:r,iconName:xn(r,n)||n}}if(typeof t=="string"&&(t.indexOf("".concat(z.cssPrefix,"-"))>-1||t.match(gv))){var a=ko(t.split(" "),{skipLookups:!0});return{prefix:a.prefix||rn(),iconName:xn(a.prefix,a.iconName)||a.iconName}}if(typeof t=="string"){var i=rn();return{prefix:i,iconName:xn(i,t)||t}}}},Ge={noAuto:Yv,config:z,dom:Qv,parse:qv,library:$m,findIconDefinition:ms,toHtml:La},Kv=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=t.autoReplaceSvgRoot,r=n===void 0?re:n;(Object.keys(lt.styles).length>0||z.autoFetchSvg)&&Ot&&z.autoReplaceSvg&&Ge.dom.i2svg({node:r})};function So(e,t){return Object.defineProperty(e,"abstract",{get:t}),Object.defineProperty(e,"html",{get:function(){return e.abstract.map(function(r){return La(r)})}}),Object.defineProperty(e,"node",{get:function(){if(Ot){var r=re.createElement("div");return r.innerHTML=e.html,r.children}}}),e}function Xv(e){var t=e.children,n=e.main,r=e.mask,a=e.attributes,i=e.styles,o=e.transform;if(Cu(o)&&n.found&&!r.found){var s=n.width,l=n.height,u={x:s/l/2,y:.5};a.style=xo(L(L({},i),{},{"transform-origin":"".concat(u.x+o.x/16,"em ").concat(u.y+o.y/16,"em")}))}return[{tag:"svg",attributes:a,children:t}]}function Zv(e){var t=e.prefix,n=e.iconName,r=e.children,a=e.attributes,i=e.symbol,o=i===!0?"".concat(t,"-").concat(z.cssPrefix,"-").concat(n):i;return[{tag:"svg",attributes:{style:"display: none;"},children:[{tag:"symbol",attributes:L(L({},a),{},{id:o}),children:r}]}]}function ju(e){var t=e.icons,n=t.main,r=t.mask,a=e.prefix,i=e.iconName,o=e.transform,s=e.symbol,l=e.title,u=e.maskId,f=e.titleId,c=e.extra,d=e.watchable,h=d===void 0?!1:d,w=r.found?r:n,A=w.width,k=w.height,m=a==="fak",p=[z.replacementClass,i?"".concat(z.cssPrefix,"-").concat(i):""].filter(function(R){return c.classes.indexOf(R)===-1}).filter(function(R){return R!==""||!!R}).concat(c.classes).join(" "),g={children:[],attributes:L(L({},c.attributes),{},{"data-prefix":a,"data-icon":i,class:p,role:c.attributes.role||"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(A," ").concat(k)})},x=m&&!~c.classes.indexOf("fa-fw")?{width:"".concat(A/k*16*.0625,"em")}:{};h&&(g.attributes[_n]=""),l&&(g.children.push({tag:"title",attributes:{id:g.attributes["aria-labelledby"]||"title-".concat(f||ba())},children:[l]}),delete g.attributes.title);var S=L(L({},g),{},{prefix:a,iconName:i,main:n,mask:r,maskId:u,transform:o,symbol:s,styles:L(L({},x),c.styles)}),C=r.found&&n.found?Lt("generateAbstractMask",S)||{children:[],attributes:{}}:Lt("generateAbstractIcon",S)||{children:[],attributes:{}},b=C.children,_=C.attributes;return S.children=b,S.attributes=_,s?Zv(S):Xv(S)}function yf(e){var t=e.content,n=e.width,r=e.height,a=e.transform,i=e.title,o=e.extra,s=e.watchable,l=s===void 0?!1:s,u=L(L(L({},o.attributes),i?{title:i}:{}),{},{class:o.classes.join(" ")});l&&(u[_n]="");var f=L({},o.styles);Cu(a)&&(f.transform=jv({transform:a,startCentered:!0,width:n,height:r}),f["-webkit-transform"]=f.transform);var c=xo(f);c.length>0&&(u.style=c);var d=[];return d.push({tag:"span",attributes:u,children:[t]}),i&&d.push({tag:"span",attributes:{class:"sr-only"},children:[i]}),d}function Jv(e){var t=e.content,n=e.title,r=e.extra,a=L(L(L({},r.attributes),n?{title:n}:{}),{},{class:r.classes.join(" ")}),i=xo(r.styles);i.length>0&&(a.style=i);var o=[];return o.push({tag:"span",attributes:a,children:[t]}),n&&o.push({tag:"span",attributes:{class:"sr-only"},children:[n]}),o}var tl=lt.styles;function hs(e){var t=e[0],n=e[1],r=e.slice(4),a=xu(r,1),i=a[0],o=null;return Array.isArray(i)?o={tag:"g",attributes:{class:"".concat(z.cssPrefix,"-").concat(wn.GROUP)},children:[{tag:"path",attributes:{class:"".concat(z.cssPrefix,"-").concat(wn.SECONDARY),fill:"currentColor",d:i[0]}},{tag:"path",attributes:{class:"".concat(z.cssPrefix,"-").concat(wn.PRIMARY),fill:"currentColor",d:i[1]}}]}:o={tag:"path",attributes:{fill:"currentColor",d:i}},{found:!0,width:t,height:n,icon:o}}var ey={found:!1,width:512,height:512};function ty(e,t){!Nm&&!z.showMissingIcons&&e&&console.error('Icon with name "'.concat(e,'" and prefix "').concat(t,'" is missing.'))}function gs(e,t){var n=t;return t==="fa"&&z.styleDefault!==null&&(t=rn()),new Promise(function(r,a){if(Lt("missingIconAbstract"),n==="fa"){var i=Um(e)||{};e=i.iconName||e,t=i.prefix||t}if(e&&t&&tl[t]&&tl[t][e]){var o=tl[t][e];return r(hs(o))}ty(e,t),r(L(L({},ey),{},{icon:z.showMissingIcons&&e?Lt("missingIconAbstract")||{}:{}}))})}var wf=function(){},vs=z.measurePerformance&&Ja&&Ja.mark&&Ja.measure?Ja:{mark:wf,measure:wf},Ur='FA "6.5.2"',ny=function(t){return vs.mark("".concat(Ur," ").concat(t," begins")),function(){return Bm(t)}},Bm=function(t){vs.mark("".concat(Ur," ").concat(t," ends")),vs.measure("".concat(Ur," ").concat(t),"".concat(Ur," ").concat(t," begins"),"".concat(Ur," ").concat(t," ends"))},Tu={begin:ny,end:Bm},xi=function(){};function xf(e){var t=e.getAttribute?e.getAttribute(_n):null;return typeof t=="string"}function ry(e){var t=e.getAttribute?e.getAttribute(ku):null,n=e.getAttribute?e.getAttribute(Su):null;return t&&n}function ay(e){return e&&e.classList&&e.classList.contains&&e.classList.contains(z.replacementClass)}function iy(){if(z.autoReplaceSvg===!0)return Ai.replace;var e=Ai[z.autoReplaceSvg];return e||Ai.replace}function oy(e){return re.createElementNS("http://www.w3.org/2000/svg",e)}function ly(e){return re.createElement(e)}function Vm(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.ceFn,r=n===void 0?e.tag==="svg"?oy:ly:n;if(typeof e=="string")return re.createTextNode(e);var a=r(e.tag);Object.keys(e.attributes||[]).forEach(function(o){a.setAttribute(o,e.attributes[o])});var i=e.children||[];return i.forEach(function(o){a.appendChild(Vm(o,{ceFn:r}))}),a}function sy(e){var t=" ".concat(e.outerHTML," ");return t="".concat(t,"Font Awesome fontawesome.com "),t}var Ai={replace:function(t){var n=t[0];if(n.parentNode)if(t[1].forEach(function(a){n.parentNode.insertBefore(Vm(a),n)}),n.getAttribute(_n)===null&&z.keepOriginalSource){var r=re.createComment(sy(n));n.parentNode.replaceChild(r,n)}else n.remove()},nest:function(t){var n=t[0],r=t[1];if(~Eu(n).indexOf(z.replacementClass))return Ai.replace(t);var a=new RegExp("".concat(z.cssPrefix,"-.*"));if(delete r[0].attributes.id,r[0].attributes.class){var i=r[0].attributes.class.split(" ").reduce(function(s,l){return l===z.replacementClass||l.match(a)?s.toSvg.push(l):s.toNode.push(l),s},{toNode:[],toSvg:[]});r[0].attributes.class=i.toSvg.join(" "),i.toNode.length===0?n.removeAttribute("class"):n.setAttribute("class",i.toNode.join(" "))}var o=r.map(function(s){return La(s)}).join(`
`);n.setAttribute(_n,""),n.innerHTML=o}};function Af(e){e()}function Wm(e,t){var n=typeof t=="function"?t:xi;if(e.length===0)n();else{var r=Af;z.mutateApproach===pv&&(r=nn.requestAnimationFrame||Af),r(function(){var a=iy(),i=Tu.begin("mutate");e.map(a),i(),n()})}}var Ru=!1;function Gm(){Ru=!0}function ys(){Ru=!1}var Ki=null;function kf(e){if(ff&&z.observeMutations){var t=e.treeCallback,n=t===void 0?xi:t,r=e.nodeCallback,a=r===void 0?xi:r,i=e.pseudoElementsCallback,o=i===void 0?xi:i,s=e.observeMutationsRoot,l=s===void 0?re:s;Ki=new ff(function(u){if(!Ru){var f=rn();br(u).forEach(function(c){if(c.type==="childList"&&c.addedNodes.length>0&&!xf(c.addedNodes[0])&&(z.searchPseudoElements&&o(c.target),n(c.target)),c.type==="attributes"&&c.target.parentNode&&z.searchPseudoElements&&o(c.target.parentNode),c.type==="attributes"&&xf(c.target)&&~xv.indexOf(c.attributeName))if(c.attributeName==="class"&&ry(c.target)){var d=ko(Eu(c.target)),h=d.prefix,w=d.iconName;c.target.setAttribute(ku,h||f),w&&c.target.setAttribute(Su,w)}else ay(c.target)&&a(c.target)})}}),Ot&&Ki.observe(l,{childList:!0,attributes:!0,characterData:!0,subtree:!0})}}function uy(){Ki&&Ki.disconnect()}function cy(e){var t=e.getAttribute("style"),n=[];return t&&(n=t.split(";").reduce(function(r,a){var i=a.split(":"),o=i[0],s=i.slice(1);return o&&s.length>0&&(r[o]=s.join(":").trim()),r},{})),n}function fy(e){var t=e.getAttribute("data-prefix"),n=e.getAttribute("data-icon"),r=e.innerText!==void 0?e.innerText.trim():"",a=ko(Eu(e));return a.prefix||(a.prefix=rn()),t&&n&&(a.prefix=t,a.iconName=n),a.iconName&&a.prefix||(a.prefix&&r.length>0&&(a.iconName=Bv(a.prefix,e.innerText)||_u(a.prefix,fs(e.innerText))),!a.iconName&&z.autoFetchSvg&&e.firstChild&&e.firstChild.nodeType===Node.TEXT_NODE&&(a.iconName=e.firstChild.data)),a}function dy(e){var t=br(e.attributes).reduce(function(a,i){return a.name!=="class"&&a.name!=="style"&&(a[i.name]=i.value),a},{}),n=e.getAttribute("title"),r=e.getAttribute("data-fa-title-id");return z.autoA11y&&(n?t["aria-labelledby"]="".concat(z.replacementClass,"-title-").concat(r||ba()):(t["aria-hidden"]="true",t.focusable="false")),t}function py(){return{iconName:null,title:null,titleId:null,prefix:null,transform:yt,symbol:!1,mask:{iconName:null,prefix:null,rest:[]},maskId:null,extra:{classes:[],styles:{},attributes:{}}}}function Sf(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{styleParser:!0},n=fy(e),r=n.iconName,a=n.prefix,i=n.rest,o=dy(e),s=ps("parseNodeAttributes",{},e),l=t.styleParser?cy(e):[];return L({iconName:r,title:e.getAttribute("title"),titleId:e.getAttribute("data-fa-title-id"),prefix:a,transform:yt,mask:{iconName:null,prefix:null,rest:[]},maskId:null,symbol:!1,extra:{classes:i,styles:l,attributes:o}},s)}var my=lt.styles;function Hm(e){var t=z.autoReplaceSvg==="nest"?Sf(e,{styleParser:!1}):Sf(e);return~t.extra.classes.indexOf(_m)?Lt("generateLayersText",e,t):Lt("generateSvgReplacementMutation",e,t)}var an=new Set;bu.map(function(e){an.add("fa-".concat(e))});Object.keys(xa[te]).map(an.add.bind(an));Object.keys(xa[ce]).map(an.add.bind(an));an=Ta(an);function bf(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(!Ot)return Promise.resolve();var n=re.documentElement.classList,r=function(c){return n.add("".concat(df,"-").concat(c))},a=function(c){return n.remove("".concat(df,"-").concat(c))},i=z.autoFetchSvg?an:bu.map(function(f){return"fa-".concat(f)}).concat(Object.keys(my));i.includes("fa")||i.push("fa");var o=[".".concat(_m,":not([").concat(_n,"])")].concat(i.map(function(f){return".".concat(f,":not([").concat(_n,"])")})).join(", ");if(o.length===0)return Promise.resolve();var s=[];try{s=br(e.querySelectorAll(o))}catch{}if(s.length>0)r("pending"),a("complete");else return Promise.resolve();var l=Tu.begin("onTree"),u=s.reduce(function(f,c){try{var d=Hm(c);d&&f.push(d)}catch(h){Nm||h.name==="MissingIcon"&&console.error(h)}return f},[]);return new Promise(function(f,c){Promise.all(u).then(function(d){Wm(d,function(){r("active"),r("complete"),a("pending"),typeof t=="function"&&t(),l(),f()})}).catch(function(d){l(),c(d)})})}function hy(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;Hm(e).then(function(n){n&&Wm([n],t)})}function gy(e){return function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=(t||{}).icon?t:ms(t||{}),a=n.mask;return a&&(a=(a||{}).icon?a:ms(a||{})),e(r,L(L({},n),{},{mask:a}))}}var vy=function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=n.transform,a=r===void 0?yt:r,i=n.symbol,o=i===void 0?!1:i,s=n.mask,l=s===void 0?null:s,u=n.maskId,f=u===void 0?null:u,c=n.title,d=c===void 0?null:c,h=n.titleId,w=h===void 0?null:h,A=n.classes,k=A===void 0?[]:A,m=n.attributes,p=m===void 0?{}:m,g=n.styles,x=g===void 0?{}:g;if(t){var S=t.prefix,C=t.iconName,b=t.icon;return So(L({type:"icon"},t),function(){return Pn("beforeDOMElementCreation",{iconDefinition:t,params:n}),z.autoA11y&&(d?p["aria-labelledby"]="".concat(z.replacementClass,"-title-").concat(w||ba()):(p["aria-hidden"]="true",p.focusable="false")),ju({icons:{main:hs(b),mask:l?hs(l.icon):{found:!1,width:null,height:null,icon:{}}},prefix:S,iconName:C,transform:L(L({},yt),a),symbol:o,title:d,maskId:f,titleId:w,extra:{attributes:p,styles:x,classes:k}})})}},yy={mixout:function(){return{icon:gy(vy)}},hooks:function(){return{mutationObserverCallbacks:function(n){return n.treeCallback=bf,n.nodeCallback=hy,n}}},provides:function(t){t.i2svg=function(n){var r=n.node,a=r===void 0?re:r,i=n.callback,o=i===void 0?function(){}:i;return bf(a,o)},t.generateSvgReplacementMutation=function(n,r){var a=r.iconName,i=r.title,o=r.titleId,s=r.prefix,l=r.transform,u=r.symbol,f=r.mask,c=r.maskId,d=r.extra;return new Promise(function(h,w){Promise.all([gs(a,s),f.iconName?gs(f.iconName,f.prefix):Promise.resolve({found:!1,width:512,height:512,icon:{}})]).then(function(A){var k=xu(A,2),m=k[0],p=k[1];h([n,ju({icons:{main:m,mask:p},prefix:s,iconName:a,transform:l,symbol:u,maskId:c,title:i,titleId:o,extra:d,watchable:!0})])}).catch(w)})},t.generateAbstractIcon=function(n){var r=n.children,a=n.attributes,i=n.main,o=n.transform,s=n.styles,l=xo(s);l.length>0&&(a.style=l);var u;return Cu(o)&&(u=Lt("generateAbstractTransformGrouping",{main:i,transform:o,containerWidth:i.width,iconWidth:i.width})),r.push(u||i.icon),{children:r,attributes:a}}}},wy={mixout:function(){return{layer:function(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},a=r.classes,i=a===void 0?[]:a;return So({type:"layer"},function(){Pn("beforeDOMElementCreation",{assembler:n,params:r});var o=[];return n(function(s){Array.isArray(s)?s.map(function(l){o=o.concat(l.abstract)}):o=o.concat(s.abstract)}),[{tag:"span",attributes:{class:["".concat(z.cssPrefix,"-layers")].concat(Ta(i)).join(" ")},children:o}]})}}}},xy={mixout:function(){return{counter:function(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},a=r.title,i=a===void 0?null:a,o=r.classes,s=o===void 0?[]:o,l=r.attributes,u=l===void 0?{}:l,f=r.styles,c=f===void 0?{}:f;return So({type:"counter",content:n},function(){return Pn("beforeDOMElementCreation",{content:n,params:r}),Jv({content:n.toString(),title:i,extra:{attributes:u,styles:c,classes:["".concat(z.cssPrefix,"-layers-counter")].concat(Ta(s))}})})}}}},Ay={mixout:function(){return{text:function(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},a=r.transform,i=a===void 0?yt:a,o=r.title,s=o===void 0?null:o,l=r.classes,u=l===void 0?[]:l,f=r.attributes,c=f===void 0?{}:f,d=r.styles,h=d===void 0?{}:d;return So({type:"text",content:n},function(){return Pn("beforeDOMElementCreation",{content:n,params:r}),yf({content:n,transform:L(L({},yt),i),title:s,extra:{attributes:c,styles:h,classes:["".concat(z.cssPrefix,"-layers-text")].concat(Ta(u))}})})}}},provides:function(t){t.generateLayersText=function(n,r){var a=r.title,i=r.transform,o=r.extra,s=null,l=null;if(bm){var u=parseInt(getComputedStyle(n).fontSize,10),f=n.getBoundingClientRect();s=f.width/u,l=f.height/u}return z.autoA11y&&!a&&(o.attributes["aria-hidden"]="true"),Promise.resolve([n,yf({content:n.innerHTML,width:s,height:l,transform:i,title:a,extra:o,watchable:!0})])}}},ky=new RegExp('"',"ug"),Ef=[1105920,1112319];function Sy(e){var t=e.replace(ky,""),n=zv(t,0),r=n>=Ef[0]&&n<=Ef[1],a=t.length===2?t[0]===t[1]:!1;return{value:fs(a?t[0]:t),isSecondary:r||a}}function Cf(e,t){var n="".concat(dv).concat(t.replace(":","-"));return new Promise(function(r,a){if(e.getAttribute(n)!==null)return r();var i=br(e.children),o=i.filter(function(b){return b.getAttribute(cs)===t})[0],s=nn.getComputedStyle(e,t),l=s.getPropertyValue("font-family").match(vv),u=s.getPropertyValue("font-weight"),f=s.getPropertyValue("content");if(o&&!l)return e.removeChild(o),r();if(l&&f!=="none"&&f!==""){var c=s.getPropertyValue("content"),d=~["Sharp"].indexOf(l[2])?ce:te,h=~["Solid","Regular","Light","Thin","Duotone","Brands","Kit"].indexOf(l[2])?Aa[d][l[2].toLowerCase()]:yv[d][u],w=Sy(c),A=w.value,k=w.isSecondary,m=l[0].startsWith("FontAwesome"),p=_u(h,A),g=p;if(m){var x=Vv(A);x.iconName&&x.prefix&&(p=x.iconName,h=x.prefix)}if(p&&!k&&(!o||o.getAttribute(ku)!==h||o.getAttribute(Su)!==g)){e.setAttribute(n,g),o&&e.removeChild(o);var S=py(),C=S.extra;C.attributes[cs]=t,gs(p,h).then(function(b){var _=ju(L(L({},S),{},{icons:{main:b,mask:Pu()},prefix:h,iconName:g,extra:C,watchable:!0})),R=re.createElementNS("http://www.w3.org/2000/svg","svg");t==="::before"?e.insertBefore(R,e.firstChild):e.appendChild(R),R.outerHTML=_.map(function(T){return La(T)}).join(`
`),e.removeAttribute(n),r()}).catch(a)}else r()}else r()})}function by(e){return Promise.all([Cf(e,"::before"),Cf(e,"::after")])}function Ey(e){return e.parentNode!==document.head&&!~mv.indexOf(e.tagName.toUpperCase())&&!e.getAttribute(cs)&&(!e.parentNode||e.parentNode.tagName!=="svg")}function Nf(e){if(Ot)return new Promise(function(t,n){var r=br(e.querySelectorAll("*")).filter(Ey).map(by),a=Tu.begin("searchPseudoElements");Gm(),Promise.all(r).then(function(){a(),ys(),t()}).catch(function(){a(),ys(),n()})})}var Cy={hooks:function(){return{mutationObserverCallbacks:function(n){return n.pseudoElementsCallback=Nf,n}}},provides:function(t){t.pseudoElements2svg=function(n){var r=n.node,a=r===void 0?re:r;z.searchPseudoElements&&Nf(a)}}},_f=!1,Ny={mixout:function(){return{dom:{unwatch:function(){Gm(),_f=!0}}}},hooks:function(){return{bootstrap:function(){kf(ps("mutationObserverCallbacks",{}))},noAuto:function(){uy()},watch:function(n){var r=n.observeMutationsRoot;_f?ys():kf(ps("mutationObserverCallbacks",{observeMutationsRoot:r}))}}}},Pf=function(t){var n={size:16,x:0,y:0,flipX:!1,flipY:!1,rotate:0};return t.toLowerCase().split(" ").reduce(function(r,a){var i=a.toLowerCase().split("-"),o=i[0],s=i.slice(1).join("-");if(o&&s==="h")return r.flipX=!0,r;if(o&&s==="v")return r.flipY=!0,r;if(s=parseFloat(s),isNaN(s))return r;switch(o){case"grow":r.size=r.size+s;break;case"shrink":r.size=r.size-s;break;case"left":r.x=r.x-s;break;case"right":r.x=r.x+s;break;case"up":r.y=r.y-s;break;case"down":r.y=r.y+s;break;case"rotate":r.rotate=r.rotate+s;break}return r},n)},_y={mixout:function(){return{parse:{transform:function(n){return Pf(n)}}}},hooks:function(){return{parseNodeAttributes:function(n,r){var a=r.getAttribute("data-fa-transform");return a&&(n.transform=Pf(a)),n}}},provides:function(t){t.generateAbstractTransformGrouping=function(n){var r=n.main,a=n.transform,i=n.containerWidth,o=n.iconWidth,s={transform:"translate(".concat(i/2," 256)")},l="translate(".concat(a.x*32,", ").concat(a.y*32,") "),u="scale(".concat(a.size/16*(a.flipX?-1:1),", ").concat(a.size/16*(a.flipY?-1:1),") "),f="rotate(".concat(a.rotate," 0 0)"),c={transform:"".concat(l," ").concat(u," ").concat(f)},d={transform:"translate(".concat(o/2*-1," -256)")},h={outer:s,inner:c,path:d};return{tag:"g",attributes:L({},h.outer),children:[{tag:"g",attributes:L({},h.inner),children:[{tag:r.icon.tag,children:r.icon.children,attributes:L(L({},r.icon.attributes),h.path)}]}]}}}},nl={x:0,y:0,width:"100%",height:"100%"};function jf(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return e.attributes&&(e.attributes.fill||t)&&(e.attributes.fill="black"),e}function Py(e){return e.tag==="g"?e.children:[e]}var jy={hooks:function(){return{parseNodeAttributes:function(n,r){var a=r.getAttribute("data-fa-mask"),i=a?ko(a.split(" ").map(function(o){return o.trim()})):Pu();return i.prefix||(i.prefix=rn()),n.mask=i,n.maskId=r.getAttribute("data-fa-mask-id"),n}}},provides:function(t){t.generateAbstractMask=function(n){var r=n.children,a=n.attributes,i=n.main,o=n.mask,s=n.maskId,l=n.transform,u=i.width,f=i.icon,c=o.width,d=o.icon,h=Pv({transform:l,containerWidth:c,iconWidth:u}),w={tag:"rect",attributes:L(L({},nl),{},{fill:"white"})},A=f.children?{children:f.children.map(jf)}:{},k={tag:"g",attributes:L({},h.inner),children:[jf(L({tag:f.tag,attributes:L(L({},f.attributes),h.path)},A))]},m={tag:"g",attributes:L({},h.outer),children:[k]},p="mask-".concat(s||ba()),g="clip-".concat(s||ba()),x={tag:"mask",attributes:L(L({},nl),{},{id:p,maskUnits:"userSpaceOnUse",maskContentUnits:"userSpaceOnUse"}),children:[w,m]},S={tag:"defs",children:[{tag:"clipPath",attributes:{id:g},children:Py(d)},x]};return r.push(S,{tag:"rect",attributes:L({fill:"currentColor","clip-path":"url(#".concat(g,")"),mask:"url(#".concat(p,")")},nl)}),{children:r,attributes:a}}}},Ty={provides:function(t){var n=!1;nn.matchMedia&&(n=nn.matchMedia("(prefers-reduced-motion: reduce)").matches),t.missingIconAbstract=function(){var r=[],a={fill:"currentColor"},i={attributeType:"XML",repeatCount:"indefinite",dur:"2s"};r.push({tag:"path",attributes:L(L({},a),{},{d:"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z"})});var o=L(L({},i),{},{attributeName:"opacity"}),s={tag:"circle",attributes:L(L({},a),{},{cx:"256",cy:"364",r:"28"}),children:[]};return n||s.children.push({tag:"animate",attributes:L(L({},i),{},{attributeName:"r",values:"28;14;28;28;14;28;"})},{tag:"animate",attributes:L(L({},o),{},{values:"1;0;1;1;0;1;"})}),r.push(s),r.push({tag:"path",attributes:L(L({},a),{},{opacity:"1",d:"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z"}),children:n?[]:[{tag:"animate",attributes:L(L({},o),{},{values:"1;0;0;0;0;1;"})}]}),n||r.push({tag:"path",attributes:L(L({},a),{},{opacity:"0",d:"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z"}),children:[{tag:"animate",attributes:L(L({},o),{},{values:"0;0;1;1;0;0;"})}]}),{tag:"g",attributes:{class:"missing"},children:r}}}},Ry={hooks:function(){return{parseNodeAttributes:function(n,r){var a=r.getAttribute("data-fa-symbol"),i=a===null?!1:a===""?!0:a;return n.symbol=i,n}}}},Ly=[Rv,yy,wy,xy,Ay,Cy,Ny,_y,jy,Ty,Ry];Hv(Ly,{mixoutsTo:Ge});Ge.noAuto;Ge.config;Ge.library;Ge.dom;var ws=Ge.parse;Ge.findIconDefinition;Ge.toHtml;var My=Ge.icon;Ge.layer;Ge.text;Ge.counter;var Ym={exports:{}},Oy="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",zy=Oy,Iy=zy;function Qm(){}function qm(){}qm.resetWarningCache=Qm;var Dy=function(){function e(r,a,i,o,s,l){if(l!==Iy){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}e.isRequired=e;function t(){return e}var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:qm,resetWarningCache:Qm};return n.PropTypes=n,n};Ym.exports=Dy();var Fy=Ym.exports;const B=Ji(Fy);function Tf(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),n.push.apply(n,r)}return n}function gt(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Tf(Object(n),!0).forEach(function(r){rr(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Tf(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function Xi(e){"@babel/helpers - typeof";return Xi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xi(e)}function rr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Uy(e,t){if(e==null)return{};var n={},r=Object.keys(e),a,i;for(i=0;i<r.length;i++)a=r[i],!(t.indexOf(a)>=0)&&(n[a]=e[a]);return n}function $y(e,t){if(e==null)return{};var n=Uy(e,t),r,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)r=i[a],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function xs(e){return By(e)||Vy(e)||Wy(e)||Gy()}function By(e){if(Array.isArray(e))return As(e)}function Vy(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Wy(e,t){if(e){if(typeof e=="string")return As(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return As(e,t)}}function As(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Gy(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Hy(e){var t,n=e.beat,r=e.fade,a=e.beatFade,i=e.bounce,o=e.shake,s=e.flash,l=e.spin,u=e.spinPulse,f=e.spinReverse,c=e.pulse,d=e.fixedWidth,h=e.inverse,w=e.border,A=e.listItem,k=e.flip,m=e.size,p=e.rotation,g=e.pull,x=(t={"fa-beat":n,"fa-fade":r,"fa-beat-fade":a,"fa-bounce":i,"fa-shake":o,"fa-flash":s,"fa-spin":l,"fa-spin-reverse":f,"fa-spin-pulse":u,"fa-pulse":c,"fa-fw":d,"fa-inverse":h,"fa-border":w,"fa-li":A,"fa-flip":k===!0,"fa-flip-horizontal":k==="horizontal"||k==="both","fa-flip-vertical":k==="vertical"||k==="both"},rr(t,"fa-".concat(m),typeof m<"u"&&m!==null),rr(t,"fa-rotate-".concat(p),typeof p<"u"&&p!==null&&p!==0),rr(t,"fa-pull-".concat(g),typeof g<"u"&&g!==null),rr(t,"fa-swap-opacity",e.swapOpacity),t);return Object.keys(x).map(function(S){return x[S]?S:null}).filter(function(S){return S})}function Yy(e){return e=e-0,e===e}function Km(e){return Yy(e)?e:(e=e.replace(/[\-_\s]+(.)?/g,function(t,n){return n?n.toUpperCase():""}),e.substr(0,1).toLowerCase()+e.substr(1))}var Qy=["style"];function qy(e){return e.charAt(0).toUpperCase()+e.slice(1)}function Ky(e){return e.split(";").map(function(t){return t.trim()}).filter(function(t){return t}).reduce(function(t,n){var r=n.indexOf(":"),a=Km(n.slice(0,r)),i=n.slice(r+1).trim();return a.startsWith("webkit")?t[qy(a)]=i:t[a]=i,t},{})}function Xm(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(typeof t=="string")return t;var r=(t.children||[]).map(function(l){return Xm(e,l)}),a=Object.keys(t.attributes||{}).reduce(function(l,u){var f=t.attributes[u];switch(u){case"class":l.attrs.className=f,delete t.attributes.class;break;case"style":l.attrs.style=Ky(f);break;default:u.indexOf("aria-")===0||u.indexOf("data-")===0?l.attrs[u.toLowerCase()]=f:l.attrs[Km(u)]=f}return l},{attrs:{}}),i=n.style,o=i===void 0?{}:i,s=$y(n,Qy);return a.attrs.style=gt(gt({},a.attrs.style),o),e.apply(void 0,[t.tag,gt(gt({},a.attrs),s)].concat(xs(r)))}var Zm=!1;try{Zm=!0}catch{}function Xy(){if(!Zm&&console&&typeof console.error=="function"){var e;(e=console).error.apply(e,arguments)}}function Rf(e){if(e&&Xi(e)==="object"&&e.prefix&&e.iconName&&e.icon)return e;if(ws.icon)return ws.icon(e);if(e===null)return null;if(e&&Xi(e)==="object"&&e.prefix&&e.iconName)return e;if(Array.isArray(e)&&e.length===2)return{prefix:e[0],iconName:e[1]};if(typeof e=="string")return{prefix:"fas",iconName:e}}function rl(e,t){return Array.isArray(t)&&t.length>0||!Array.isArray(t)&&t?rr({},e,t):{}}var Lf={border:!1,className:"",mask:null,maskId:null,fixedWidth:!1,inverse:!1,flip:!1,icon:null,listItem:!1,pull:null,pulse:!1,rotation:null,size:null,spin:!1,spinPulse:!1,spinReverse:!1,beat:!1,fade:!1,beatFade:!1,bounce:!1,shake:!1,symbol:!1,title:"",titleId:null,transform:null,swapOpacity:!1},Lu=Cs.forwardRef(function(e,t){var n=gt(gt({},Lf),e),r=n.icon,a=n.mask,i=n.symbol,o=n.className,s=n.title,l=n.titleId,u=n.maskId,f=Rf(r),c=rl("classes",[].concat(xs(Hy(n)),xs((o||"").split(" ")))),d=rl("transform",typeof n.transform=="string"?ws.transform(n.transform):n.transform),h=rl("mask",Rf(a)),w=My(f,gt(gt(gt(gt({},c),d),h),{},{symbol:i,title:s,titleId:l,maskId:u}));if(!w)return Xy("Could not find icon",f),null;var A=w.abstract,k={ref:t};return Object.keys(n).forEach(function(m){Lf.hasOwnProperty(m)||(k[m]=n[m])}),Zy(A[0],k)});Lu.displayName="FontAwesomeIcon";Lu.propTypes={beat:B.bool,border:B.bool,beatFade:B.bool,bounce:B.bool,className:B.string,fade:B.bool,flash:B.bool,mask:B.oneOfType([B.object,B.array,B.string]),maskId:B.string,fixedWidth:B.bool,inverse:B.bool,flip:B.oneOf([!0,!1,"horizontal","vertical","both"]),icon:B.oneOfType([B.object,B.array,B.string]),listItem:B.bool,pull:B.oneOf(["right","left"]),pulse:B.bool,rotation:B.oneOf([0,90,180,270]),shake:B.bool,size:B.oneOf(["2xs","xs","sm","lg","xl","2xl","1x","2x","3x","4x","5x","6x","7x","8x","9x","10x"]),spin:B.bool,spinPulse:B.bool,spinReverse:B.bool,symbol:B.oneOfType([B.bool,B.string]),title:B.string,titleId:B.string,transform:B.oneOfType([B.string,B.object]),swapOpacity:B.bool};var Zy=Xm.bind(null,Cs.createElement),Jy={prefix:"fas",iconName:"gear",icon:[512,512,[9881,"cog"],"f013","M495.9 166.6c3.2 8.7 .5 18.4-6.4 24.6l-43.3 39.4c1.1 8.3 1.7 16.8 1.7 25.4s-.6 17.1-1.7 25.4l43.3 39.4c6.9 6.2 9.6 15.9 6.4 24.6c-4.4 11.9-9.7 23.3-15.8 34.3l-4.7 8.1c-6.6 11-14 21.4-22.1 31.2c-5.9 7.2-15.7 9.6-24.5 6.8l-55.7-17.7c-13.4 10.3-28.2 18.9-44 25.4l-12.5 57.1c-2 9.1-9 16.3-18.2 17.8c-13.8 2.3-28 3.5-42.5 3.5s-28.7-1.2-42.5-3.5c-9.2-1.5-16.2-8.7-18.2-17.8l-12.5-57.1c-15.8-6.5-30.6-15.1-44-25.4L83.1 425.9c-8.8 2.8-18.6 .3-24.5-6.8c-8.1-9.8-15.5-20.2-22.1-31.2l-4.7-8.1c-6.1-11-11.4-22.4-15.8-34.3c-3.2-8.7-.5-18.4 6.4-24.6l43.3-39.4C64.6 273.1 64 264.6 64 256s.6-17.1 1.7-25.4L22.4 191.2c-6.9-6.2-9.6-15.9-6.4-24.6c4.4-11.9 9.7-23.3 15.8-34.3l4.7-8.1c6.6-11 14-21.4 22.1-31.2c5.9-7.2 15.7-9.6 24.5-6.8l55.7 17.7c13.4-10.3 28.2-18.9 44-25.4l12.5-57.1c2-9.1 9-16.3 18.2-17.8C227.3 1.2 241.5 0 256 0s28.7 1.2 42.5 3.5c9.2 1.5 16.2 8.7 18.2 17.8l12.5 57.1c15.8 6.5 30.6 15.1 44 25.4l55.7-17.7c8.8-2.8 18.6-.3 24.5 6.8c8.1 9.8 15.5 20.2 22.1 31.2l4.7 8.1c6.1 11 11.4 22.4 15.8 34.3zM256 336a80 80 0 1 0 0-160 80 80 0 1 0 0 160z"]};const In=({children:e,title:t,description:n,buttons:r,countdownDuration:a,resetCallback:i,resetDelay:o,status:s,setStatus:l,statusMessage:u,settings:f,score:c,targetScore:d,...h})=>{const A=Kg(()=>{i(),p()},o);v.useEffect(()=>{s!==1&&s!==0&&A()},[A,s]);const k=()=>{l(2)},[m,p,g]=Zg(k,a,1e3);v.useEffect(()=>{s!==1&&s!==0&&g()},[g,s]);const x=()=>{let b=100;return s===1&&(b-=m,b-=Math.max(Math.min(1e3/a,1),0)*100,b=Math.max(Math.min(b,100),0)),b},[S,C]=v.useState(!1);return y.jsxs(y.Fragment,{children:[f&&y.jsx(ev,{handleReset:f.handleReset,handleSave:f.handleSave,visible:S,setVisible:C,children:f.children}),y.jsxs("div",{className:Ke(`
                    max-h-full max-w-full
                    rounded-lg
                    overflow-hidden
                `,h.className),children:[y.jsxs("div",{className:`
                    max-h-full max-w-full
                    relative
                    p-3
                    flex flex-col justify-center
                    bg-[rgb(7_19_32)]
                `,children:[y.jsxs("div",{className:`
                        grid
                        grid-cols-[auto_min-content]
                        mb-4
                    `,children:[y.jsxs("div",{className:`
                            flex items-center
                            gap-4
                        `,children:[y.jsx("img",{className:"w-8 sm:w-10",src:tv}),y.jsx("h2",{className:`
                                text-lg
                                sm:text-2xl
                                text-spring-green-300
                                [text-shadow:0_0_40px_rgb(162_216_250)]
                            `,children:t}),y.jsx("p",{className:`
                                text-xs
                                sm:text-base
                                text-[rgb(142_142_142)]`,children:n})]}),f&&At()&&y.jsx("div",{className:"h-full flex aspect-square justify-center items-center p-1 mr-7",children:y.jsx(Lu,{icon:Jy,className:`
                                    size-full
                                    text-gray-500
                                    hover:rotate-90 hover:scale-110 hover:cursor-pointer
                                    transition-transform
                                `,onClick:()=>C(!0),title:"Open Settings"})}),d&&y.jsxs("div",{className:`
                                col-span-full
                                text-center
                                text-white
                                text-lg
                            `,children:[c,"/",d]})]}),u!==""&&y.jsx("div",{className:Ke(`
                            gap-2.5
                            absolute
                            text-white
                            rounded
                            flex items-center justify-center
                            w-full h-full left-0 top-0 bg-[rgb(0_0_0/0.7)] z-20
                        `),children:y.jsxs("div",{className:Ke("flex items-center justify-center rounded-sm px-5 p-2 gap-2 ",s===2?"bg-[rgb(56_13_23)]":s===3?"bg-[rgb(23_95_88)]":s===4?"bg-[rgb(118_128_37)]":"hidden"),children:[y.jsx("i",{className:Ke("fa-solid ",s===2?"fa-circle-xmark text-[rgb(255_84_84)] text-2xl":s===3?"fa-circle-check text-[rgb(84_255_164)] text-2xl":s===0?"fa-hourglass-start text-[rgb(118_128_37)] text-2xl":"hidden")}),y.jsx("p",{className:"text-xl font-medium",children:u})]})}),y.jsx("div",{className:"w-full pb-2 flex-1",children:e}),y.jsx("div",{className:"flex flex-col w-full gap-1",children:r.map((b,_)=>y.jsx("div",{className:"flex gap-1 *:flex-1",children:b.map((R,T)=>y.jsx(wa,{onClick:R.callback,color:R.color,disabled:R.disabled,children:R.label},T))},_))})]}),y.jsx("div",{className:"bg-[rgb(15_27_33)] flex w-full h-2.5",children:y.jsx("div",{className:Ke("bg-[#38a2e5] w-full h-full [transition:width_linear]"),style:{transitionDuration:s!==1?"0ms":"1000ms",width:`${x()}%`}})})]})]})},Dn=(e,t)=>{const[n,r]=v.useState(0),a=v.useCallback(i=>{console.log("Handling update",i),r(i),t(i)},[t]);return v.useEffect(()=>{n===0&&a(1)},[n,a]),[n,a]};async function e2(e,t,n){const r={method:"post",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify(t)};if(At()&&n)return n;const a=window.GetParentResourceName?window.GetParentResourceName():"nui-frame-app";return await(await fetch(`https://${a}/${e}`,r)).json()}const Er=async e=>await e2("finishedMinigame",e,!0),t2=()=>{const[e,t]=v.useState(Gg),[n,r]=v.useState(0),[a,i]=v.useState(0),[o,s]=v.useState(-1),[l,u]=v.useState(0),[f,c]=v.useState(!1),[d,h]=v.useState(!1),[w,A]=v.useState(!1),[k,m]=v.useState(0),[p,g]=v.useState(ve[k].timer),[x,S]=v.useState(ve[k].targetScore),[C,b]=v.useState(ve[k].rows),[_,R]=v.useState(ve[k].columns),[T,E]=v.useState(!1),j=cn(),I=v.useCallback(()=>{const ie=[];for(let J=0;J<C;J++){const X=[];for(let F=0;F<_;F++)X.push(Hg());ie.push(X)}t(ie),r(0),i(0),s(-1),u(0),A(!1)},[_,C]),Y=v.useCallback(ie=>{switch(ie){case 1:c(!1),I();break;default:c(!0);break}},[I]),[U,q]=Dn(p*1e3,Y);v.useEffect(()=>{T&&(q(1),E(!1))},[T,q]),un("playMinigame",ie=>{if(ie.minigame!=="thermite")return;const J=ie.data;S(J.targetScore),b(J.rows),R(J.columns),g(J.timer),E(!0)});const $=v.useCallback(async()=>{At()?q(1):(await Er(U===3),j("/"))},[q,U,j]),N=v.useCallback((ie,J)=>{const X=[...e],[F,G]=ie;X[F][G]={...X[F][G],...J},t(X)},[e]),O=v.useCallback(ie=>{const[J,X]=ie;return e[J][X]},[e]),D=v.useCallback((ie,J,X)=>{const[F,G]=ie,[nt,Ye]=J;return G%X.distance===Ye%X.distance&&F%X.distance===nt%X.distance&&Math.abs(G-Ye)<=X.distance&&Math.abs(F-nt)<=X.distance},[]),H=v.useCallback(ie=>{if(U!==1)return;const J=O(ie);if(!J.highlighted||J.status==="empty")return;let X=0;const[F,G]=ie,nt=[...e].map((Ye,dn)=>Ye.map((pn,Uu)=>{if(F==dn&&G==Uu||pn.status==="empty")return pn;const $u=D([dn,Uu],ie,J.piece);return $u&&X++,pn.highlighted=$u,pn}));if(t(nt),X===0){A(!0),q(2);return}if(J.status==="half"){let Ye=a,dn=n;dn++;const pn=Date.now();if(Ye===0||pn-o<=1e3?Ye++:Ye=0,s(pn),Ye>=3?(dn+=Math.pow(2,l),i(0),u(l+1),h(!0)):i(Ye),r(dn),dn>=x){q(3);return}}else i(0);N(ie,{status:J.status==="full"?"half":"empty",piece:ym(),highlighted:!1})},[e,a,U,O,D,o,n,q,N,x,l]),[K,tt]=v.useState(k),[Ie,ft]=v.useState(p),[He,De]=v.useState(x),[dt,Un]=v.useState(C),[Ma,fn]=v.useState(_);v.useEffect(()=>{tt(k),ft(p),De(x),Un(C),fn(_)},[k,p,x,C,_]);const Eo={handleSave:()=>{m(K),g(Ie),S(He),b(dt),R(Ma),q(4)},handleReset:()=>{m(k),g(ve[k].timer),S(ve[k].targetScore),b(ve[k].rows),R(ve[k].columns),q(4)},children:y.jsxs(v.Fragment,{children:[y.jsxs("div",{className:"w-full flex items-center gap-2 *:flex-1",children:[y.jsx(we,{title:"Target score",value:He,setValue:De,min:10,max:75}),y.jsx(we,{title:"Timer",value:Ie,setValue:ft,min:30,max:180})]}),y.jsxs("div",{className:"w-full flex items-center gap-2 *:flex-1",children:[y.jsx(we,{title:"Rows",value:dt,setValue:Un,min:5,max:9}),y.jsx(we,{title:"Columns",value:Ma,setValue:fn,min:5,max:9})]}),y.jsxs("div",{children:[y.jsx("p",{className:"w-full px-8 text-[rgb(94_93_93)] text-xl",children:"Presets"}),y.jsxs("div",{className:"w-full flex items-center gap-2 px-8 *:flex-1",children:[y.jsx(wa,{label:"Maze Bank - Sewer",color:"green",disabled:K===0,onClick:()=>{tt(0),ft(ve[0].timer),De(ve[0].targetScore),Un(ve[0].rows),fn(ve[0].columns)},children:"Maze Bank - Sewer"}),y.jsx(wa,{label:"Maze Bank - Vault",color:"green",disabled:K===1,onClick:()=>{tt(1),ft(ve[1].timer),De(ve[1].targetScore),Un(ve[1].rows),fn(ve[1].columns)},children:"Maze Bank - Vault"})]})]})]})};return y.jsx(In,{title:"Mazer",description:"Decrypt the required number of bytes",buttons:[],countdownDuration:p*1e3,resetCallback:$,resetDelay:3e3,status:U,setStatus:q,statusMessage:Yg(U),settings:Eo,score:n,targetScore:x,children:y.jsx("div",{className:"thermite"+(U===0||U===4?" blur":""),style:{width:"100%",height:"100%",gridTemplateRows:`repeat(${C}, minmax(0, 1fr))`,gridTemplateColumns:`repeat(${_}, minmax(0, 1fr))`},children:y.jsxs(v.Fragment,{children:[e.map((ie,J)=>y.jsx(v.Fragment,{children:ie.map((X,F)=>y.jsxs("div",{className:"square","data-piece":X.piece,"data-status":w&&X.status!=="empty"?"fail":X.status,"data-highlighted":X.highlighted,onClick:()=>H([J,F]),children:[y.jsx("span",{className:"piece",children:y.jsx("img",{src:X.piece.img,alt:"",width:75,height:75})}),y.jsxs("div",{className:"crosses",children:[y.jsx("img",{src:Za,alt:"",width:16,height:16}),y.jsx("img",{src:Za,alt:"",width:16,height:16}),y.jsx("img",{src:Za,alt:"",width:16,height:16}),y.jsx("img",{src:Za,alt:"",width:16,height:16})]}),y.jsx("div",{className:"highlight",style:{animationName:f?"none":"highlight"}})]},`${J}_${F}`))},J)),y.jsx("div",{className:"notice",children:y.jsx("span",{style:{animationName:d?"notice":"none"},onAnimationEnd:()=>h(!1),children:"CRC Bypassed!"})}),y.jsx("img",{src:Qg,alt:"",className:"background-image"})]})})})},Zr=(e,t)=>{const n=v.useCallback(r=>{t.some(i=>r.key===i)&&(r.preventDefault(),e(r.key))},[e,t]);v.useEffect(()=>(document.addEventListener("keydown",n),()=>{document.removeEventListener("keydown",n)}),[n])},Bn=["0","1","2","3","4","5","6","7","8","9"],al=20,n2=e=>{switch(e){case 0:return"";case 1:return"";case 2:return zn.play(),"You suck at this :D";case 3:return"Sometimes you win :O";case 4:return"Reset!";default:return`Error: Unknown game status ${e}`}},r2=()=>{const[e,t]=v.useState(al),[n,r]=v.useState(al),[a,i]=v.useState(0),[o,s]=v.useState(!0),[l,u]=v.useState(4),[f,c]=v.useState(),[d,h]=v.useState(!1),w=cn(),A=E=>{switch(E){case 1:s(!1),C();break}},[k,m]=Dn(e*1e3,A);v.useEffect(()=>{d&&(m(1),h(!1))},[d,m]),un("playMinigame",E=>{if(E.minigame!=="pincracker")return;const j=E.data;u(j.pinLength),t(j.timer),h(!0)});const p=()=>{if(a<l)console.log("Incomplete pin");else{const E=document.querySelectorAll(".wrapper"),j=document.querySelectorAll(".marker"),I=document.querySelectorAll(".digit"),Y=Array.from(I).map(U=>U.innerHTML);s(!1);for(let U=0;U<l;U++)setTimeout(()=>{vm.play(),U>0&&E[U-1].classList.remove("bg-gradient-radial","from-spring-green-300","to-turquoise-900/50"),E[U].classList.add("bg-gradient-radial","from-spring-green-300","to-turquoise-900/50"),j[U].classList.remove("bg-slate-400"),j[U].classList.remove("bg-green-400"),j[U].classList.remove("bg-yellow-400"),j[U].classList.remove("bg-red-400"),f&&Y[U]===f[U]?j[U].classList.add("bg-green-400"):f&&f.includes(Y[U])?j[U].classList.add("bg-yellow-400"):j[U].classList.add("bg-red-400"),setTimeout(()=>{E[U].classList.remove("bg-gradient-radial","from-spring-green-300","to-turquoise-900/50")},250)},U*250);setTimeout(()=>{f&&Y.join("")===f.join("")&&m(3),i(0),g(250)},1e3)}},g=E=>{const j=document.querySelectorAll(".digit");for(let I=l-1;I>-1;I--)setTimeout(()=>{j[I].innerHTML=""},(l-I)*E);setTimeout(()=>{s(!0)},E*l)},x=()=>{const E=document.querySelectorAll(".marker");for(let j=0;j<E.length;j++)E[j].classList.remove("bg-green-400"),E[j].classList.remove("bg-yellow-400"),E[j].classList.remove("bg-red-400"),E[j].classList.add("bg-slate-400")};function S(){for(let j=Bn.length-1;j>0;j--){const I=Math.floor(Math.random()*(j+1));[Bn[j],Bn[I]]=[Bn[I],Bn[j]]}const E=Bn.slice(0,l);c(E)}const C=()=>{console.log(`Resetting cracker with ${e} seconds`),i(0),S(),x(),g(0),s(!0)},b=async()=>{At()?m(1):(await Er(k===3),w("/"))},_=E=>{const j=document.querySelectorAll(".digit");j[E].innerHTML=""},R=E=>{if(E==="Enter")p();else if(E==="Backspace")i(Math.max(a-1,0)),_(Math.max(a-1,0));else if(a<l){const j=document.querySelectorAll(".digit");j[a].innerHTML=E.toString(),i(a+1)}};Zr(E=>{o&&E&&k==1&&R(E)},["1","2","3","4","5","6","7","8","9","0","Backspace","Enter"]),v.useEffect(()=>{k===3&&On.play()},[k]);const T={handleSave:()=>{t(n),m(4)},handleReset:()=>{r(al),m(4)},children:y.jsxs("div",{className:"flex flex-col items-center",children:[y.jsx(we,{title:"Pin Length",min:2,max:6,value:l,setValue:u}),y.jsx(we,{title:"Duration (seconds)",min:5,max:30,value:n,setValue:r})]})};return y.jsx(In,{title:"PinCracker",description:"Decode digits of the pin code",buttons:[[{label:"Crack",color:"green",callback:p,disabled:k!==1}]],countdownDuration:e*1e3,resetCallback:b,resetDelay:3e3,status:k,setStatus:m,statusMessage:n2(k),settings:T,children:y.jsx("div",{className:`
            h-32 w-[600px] max-w-full
            rounded-lg
            bg-[rgb(22_40_52)]
            flex items-center justify-between
            text-white text-5xl
          `,children:[...Array(l)].map((E,j)=>y.jsxs("div",{className:"flex flex-col items-center justify-center w-3/12 h-full gap-3 rounded-md wrapper",children:[y.jsx("div",{className:"h-[50px] digit"}),y.jsx("div",{className:"px-5 h-1 bg-slate-400 marker"})]},j))})})},Mf=["Q","W","E","R","A","S","D"],a2=e=>{switch(e){case 0:return"";case 1:return"";case 2:return zn.play(),"You suck at this :D";case 3:return On.play(),"Sometimes you win :O";case 4:return"Reset!";default:return`Error: Unknown game status ${e}`}},i2=()=>Mf[Math.floor(Math.random()*Mf.length)],Mr=15,il=7,Or=6,o2=()=>{const[e,t]=v.useState(il),[n,r]=v.useState(Mr),[a,i]=v.useState(0),[o,s]=v.useState(new Array(Mr)),[l,u]=v.useState(new Array(Mr).fill("")),f=cn(),[c,d]=v.useState(!1),h=E=>{switch(E){case 1:console.log("Reset game"),k();break}},[w,A]=Dn(e*1e3,h);v.useEffect(()=>{c&&(u(new Array(n).fill("")),A(1),d(!1))},[n,c,A]),un("playMinigame",E=>{if(E.minigame!=="chopping")return;const j=E.data;t(j.timer),r(j.letters),d(!0)});const k=()=>{const E=[];console.log(`Resetting board with ${n} letters`);for(let I=0;I<n;I++)E.push(i2());s(E),i(0);const j=new Array(n).fill("");u(j)},m=async()=>{At()?A(1):(await Er(w===3),f("/"))},p=E=>{console.log(`Win: ${E}`),A(3)},g=E=>{console.log(`Lose: ${E}`),A(2)},x=E=>{E[E.length-1]==="done"&&p("All letters pressed successfully"),E[a]==="fail"&&g(`Letter ${o==null?void 0:o[a]} failed`)},S=E=>{const j=[...o],I=[...l];E.toUpperCase()===o[a]?(I[a]="done",i(a+1)):I[a]="fail",vm.play(),s(j),u(I),x(I)};Zr(E=>{E&&w==1&&S(E)},["Q","q","W","w","E","e","R","r","A","a","S","s","D","d"]);const[C,b]=v.useState(Mr),[_,R]=v.useState(il);v.useEffect(()=>{b(n),R(e)},[n,e]);const T={handleSave:()=>{r(C),t(_),A(4)},handleReset:()=>{b(Mr),R(il),A(4)},children:y.jsxs("div",{className:"flex flex-col items-center",children:[y.jsx(we,{title:"Number of letters",min:13,max:18,value:C,setValue:b}),y.jsx(we,{title:"Duration (seconds)",min:5,max:30,value:_,setValue:R})]})};return y.jsx(In,{title:"Alphabet",description:"Tap the letters in order",buttons:[],countdownDuration:e*1e3,resetCallback:m,resetDelay:3e3,status:w,setStatus:A,statusMessage:a2(w),settings:T,children:y.jsx("div",{className:`
                h-max w-max max-w-full
                rounded-lg
                bg-[rgb(22_40_52)]
                flex items-center justify-center
                text-white text-5xl
                p-2
            `,children:y.jsx("div",{className:"game-grid",children:Array.from({length:Math.ceil(n/Or)}).map((E,j)=>y.jsx("div",{className:"game-grid-row",style:{gridTemplateColumns:`repeat(${Math.min(n-j*Or,Or)}, min-content)`},children:Array.from({length:Or}).map((I,Y)=>{const U=j*Or+Y;if(U<n){const q=o[U],$=U===a,N=l[U]==="done",O=l[U]==="fail",D=Ke("letter",{"letter-active":$,done:N,fail:O});return y.jsx("div",{className:D,style:{justifySelf:"center"},children:q},Y)}else return null})},j))})})})},Jm=30,ar=360/Jm,Of=["red","yellow","blue"],l2=e=>{switch(e){case 0:return"";case 1:return"";case 2:return zn.play(),"The lockpick bent out of shape.";case 3:return On.play(),"The lock was picked successfully.";case 4:return"Reset!";default:return`Error: Unknown game status ${e}`}},zf=(e,t)=>{const n=vu(e);return n.length=t,n},If=(e,t=ar)=>{for(;e<0;)e+=t;return e%t},s2=(e="normal")=>{const t=[],n=[];for(let u=0;u<ar;u++)t.push(u),n.push(Of[Math.floor(Math.random()*Of.length)]);const r=Math.floor(Math.random()*(ar+1-5)+5),a=Math.floor(Math.random()*(ar-4-4)+4);let i,o;const s=vu(t);switch(e){case"normal":i=s.slice(0,r),o=s.slice(0,a);break;case"hard":i=zf(t,r),o=zf(t,a);break}const l=Math.floor(Math.random()*ar);return{color:n,balls:i,slots:o,rotation:l}},Df=5,Ff=30,u2=()=>{const[e,t]=v.useState("Lockpick"),[n,r]=v.useState(Df),[a,i]=v.useState(Ff),[o,s]=v.useState([]),[l,u]=v.useState(0),[f,c]=v.useState(0),d=cn(),[h,w]=v.useState(!1),A=$=>{const N=[];switch($){case 1:console.log("Reset game");for(let O=0;O<n;O++)N.push(s2());s(N),c(0),u(N[0].rotation);break}},[k,m]=Dn(a*1e3,A);v.useEffect(()=>{h&&(m(1),w(!1))},[h,m]),un("playMinigame",$=>{if($.minigame!=="lockpick")return;const N=$.data;t(N.title),r(N.levels),i(N.timer),w(!0)});const p=async()=>{At()?m(1):(await Er(k===3),d("/"))},g=async $=>{console.log(`Win: ${$}`),m(3)},x=async $=>{console.log(`Lose: ${$}`),m(2)},S=()=>{if(f>=n-1)g("All levels completed");else{const $=f+1;c($),u(o[$].rotation)}},C=$=>{const N=l+$;u(N);const O=o;O[f].rotation=N,s(O)},b=()=>{C(-1)},_=()=>{C(1)},R=()=>{for(const $ of o[f].slots){const N=If($-If(l));if(o[f].balls.includes(N)&&o[f].color[N]!==o[f].color[$]){x(`Mismatch on level ${f} with rotation ${l}. Slot ${$} (${o[f].color[$]}) does not match ball ${N} (${o[f].color[N]})`);return}}S()},T=$=>()=>{k===1&&$()};Zr(T(b),["ArrowLeft","a","A"]),Zr(T(_),["ArrowRight","d","D"]),Zr(T(R),["Enter"," "]);const E=50*(n*2+1),[j,I]=v.useState(n),[Y,U]=v.useState(a);v.useEffect(()=>{I(n),U(a)},[n,a,k]);const q={handleSave:()=>{r(j),i(Y),m(4)},handleReset:()=>{r(Df),i(Ff),m(4)},children:y.jsx(y.Fragment,{children:y.jsxs("div",{className:"flex w-full gap-2 *:flex-1 flex-col sm:flex-row",children:[y.jsx(we,{title:"Levels",value:j,setValue:I,min:2,max:10}),y.jsx(we,{title:"Timer",value:Y,setValue:U,min:5,max:100})]})})};return y.jsx(In,{title:e,description:"Unlock each lock",buttons:[[{label:"Rotate Left",color:"purple",callback:b,disabled:k!==1},{label:"Rotate Right",color:"purple",callback:_,disabled:k!==1}],[{label:"Unlock",color:"green",callback:R,disabled:k!==1}]],countdownDuration:a*1e3,resetCallback:p,resetDelay:3e3,status:k,setStatus:m,statusMessage:l2(k),settings:q,children:y.jsx("div",{className:Ke(`
                    aspect-square
                    max-h-full max-w-full
                    rounded-lg
                    bg-[rgb(22_40_52)]
                    flex items-center justify-center
                    relative
                `,k===0||k===4?"blur":""),style:{maxWidth:"calc(100vh - 298px)",width:"calc(100vw - 64px)"},children:y.jsx("div",{className:`
                    aspect-square
                    flex items-center justify-center
                    size-full
                    absolute
                `,children:y.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",version:"1.1",className:`
                            size-full aspect-square

                            *:origin-center

                            data-[stroke=gray]:*:stroke-[rgb(173_173_173)]
                            data-[stroke=fail]:*:stroke-[rgb(255_84_84)]
                            data-[stroke=win]:*:stroke-[rgb(48_221_189/0.816)]

                            *:*:origin-center

                            data-[stroke=blue]:*:*:stroke-[rgb(46_134_213)]
                            data-[stroke=yellow]:*:*:stroke-[rgb(239_181_17)]
                            data-[stroke=red]:*:*:stroke-[rgb(202_39_97)]
                            data-[stroke=fail]:*:*:stroke-[rgb(255_84_84)]
                            data-[stroke=win]:*:*:stroke-[rgb(48_221_189/0.816)]

                            data-[fill=blue]:*:*:fill-[rgb(46_134_213)]
                            data-[fill=yellow]:*:*:fill-[rgb(239_181_17)]
                            data-[fill=red]:*:*:fill-[rgb(202_39_97)]
                            data-[fill=fail]:*:*:fill-[rgb(255_84_84)]
                            data-[fill=win]:*:*:fill-[rgb(48_221_189/0.816)]
                        `,viewBox:`0 0 ${E} ${E}`,children:[y.jsx("g",{className:`
                                    *:stroke-[1.2px] *:origin-center
                                    *:stroke-[rgb(142_142_142)]
                                `,children:[...Array(ar/2)].map(($,N)=>y.jsx("line",{x1:35,x2:E-35,y1:E/2,y2:E/2,transform:`rotate(${N*Jm})`},N))}),o.map(($,N)=>{const O=(N+1)*50-10,D=O+15;return y.jsxs(y.Fragment,{children:[y.jsx("circle",{className:`
                                            fill-none stroke-[3px]
                                        `,"data-stroke":f>N||k===3?"win":k===2&&f==N?"fail":"gray",cx:"50%",cy:"50%",r:O},N*3),y.jsx("g",{className:`
                                            *:ease-in-out *:transition-transform *:duration-200
                                        `,children:$.balls.map((H,K)=>y.jsx("circle",{style:{transform:`rotate(${(H+$.rotation)*30}deg) translateX(${O}px)`},"data-fill":f>N||k===3?"win":k===2&&f==N?"fail":$.color[H],cx:"50%",cy:"50%",r:"8.5px"},K))},N*3+1),y.jsx("g",{className:`
                                            *:fill-none *:stroke-[5px]
                                        `,children:$.slots.map((H,K)=>y.jsx("circle",{"data-r-px":D,cx:"50%",cy:"50%",r:D,"data-stroke":f>N||k===3?"win":k===2&&f==N?"fail":$.color[H],style:{transform:`rotate(${-15+H*30}deg)`,strokeDasharray:`${2*D*Math.PI}`,strokeDashoffset:`${11*(2*D*Math.PI)/12}`}},K))},N*3+2)]})})]})})})})},Jr=["red","green","blue"],c2=(e,t,n)=>{const r=[...e];for(let a=0;a<n;a++){let i=0;for(let o=t-1;o>=0;o--)r[o*n+a]==="empty"?i++:i>0&&(r[(o+i)*n+a]=r[o*n+a],r[o*n+a]="empty")}return r},f2=(e,t,n)=>{const r=[...e];let a=0;for(let i=0;i<n;i++){let o=!0;for(let s=0;s<t;s++)if(r[s*n+i]!=="empty"){o=!1;break}if(o)a++;else if(a>0)for(let s=0;s<t;s++)r[s*n+i-a]=r[s*n+i],r[s*n+i]="empty"}return r},d2=(e,t,n,r)=>{const a=e[t],i=[],o=new Array(n*r).fill(!1);function s(l){o[l]=!0,i.push(l);const u=Math.floor(l/r),f=l%r,c=[l-r,l+r,l-1,l+1];for(const d of c)d>=0&&d<n*r&&(Math.floor(d/r)===u||d%r===f)&&!o[d]&&e[d]===a&&s(d)}return s(t),i},p2=e=>{switch(e){case 0:return"";case 1:return"";case 2:return zn.play(),"You suck at this :D";case 3:return On.play(),"Sometimes you win :O";case 4:return"Reset!";default:return`Error: Unknown game status ${e}`}},m2=()=>Jr[Math.floor(Math.random()*Jr.length)],Uf=8,$f=11,Bf=25,h2=()=>{const[e,t]=v.useState(Bf),[n,r]=v.useState(Uf),[a,i]=v.useState($f),[o,s]=v.useState(new Array(n*a).fill("empty")),[l,u]=v.useState(!1),f=cn(),c=E=>{switch(E){case 1:console.log("Reset game"),w();break}},[d,h]=Dn(e*1e3,c);v.useEffect(()=>{l&&(s(new Array(n*a).fill("empty")),h(1),u(!1))},[l,h,n,a]),un("playMinigame",E=>{if(E.minigame!=="roof-running")return;const j=E.data;r(j.rows),i(j.columns),t(j.timer),u(!0)});const w=()=>{const E=[];console.log(`generating new ${n}x${a} board`);for(let j=0;j<n*a;j++)E.push(m2());s(E)},A=async()=>{At()?h(1):(await Er(d===3),f("/"))},k=E=>{console.log(`Win: ${E}`),h(3)},m=E=>{console.log(`Lose: ${E}`),h(2)},p=E=>{E.every(j=>j==="empty")&&k("All tiles cleared");for(let j=0;j<Jr.length;j++)E.filter(I=>I===Jr[j]).length===1&&m(`Unsolvable: 1 ${Jr[j]} tile remaining`)},g=E=>{if(d!==1)return;const j=d2(o,E,n,a);if(j.length>1){let I=[...o];j.forEach(Y=>{I[Y]="empty"}),I=c2(I,n,a),I=f2(I,n,a),s(I),p(I)}},[x,S]=v.useState(n),[C,b]=v.useState(a),[_,R]=v.useState(e);v.useEffect(()=>{S(n),b(a),R(e)},[n,a,e]);const T={handleSave:()=>{r(x),i(C),t(_),h(4)},handleReset:()=>{r(Uf),i($f),t(Bf),h(4)},children:y.jsxs("div",{className:"flex flex-col items-center",children:[y.jsxs("div",{className:"flex w-full gap-2 *:flex-1 flex-row",children:[y.jsx(we,{title:"Rows",value:x,setValue:S,min:5,max:10}),y.jsx(we,{title:"Columns",value:C,setValue:b,min:5,max:15})]}),y.jsx("div",{className:"flex items-center justify-center aspect-[15/10] w-[50%] mt-6 h-auto ",children:y.jsxs("div",{className:`
                            [outline:2px_solid_rgb(94_93_93)]
                            bg-radient-circle-c
                            from-[rgb(22_40_52/0.651)] to-[rgb(22_40_52)]

                            *:whitespace-nowrap
                            *:absolute
                            *:text-base
                            *:text-[rgb(84_255_164)]
                            *:[text-shadow:0_0_2.1px_rgb(127_255_191)]
                        `,style:{height:`${100/10*x}%`,width:`${100/15*C}%`},children:[y.jsx("div",{className:"left-1/2 -translate-y-full -translate-x-1/2",children:"Columns"}),y.jsx("div",{className:"[writing-mode:vertical-lr] top-1/2 -translate-x-full -rotate-180",children:"Rows"})]})}),y.jsx("div",{className:"flex w-full gap-2 *:flex-1 flex-col sm:flex-row",children:y.jsx(we,{title:"Timer",value:_,setValue:R,min:5,max:100})})]})};return y.jsx(In,{title:"Same Game",description:"Click on matching groups of blocks",buttons:[],countdownDuration:e*1e3,resetCallback:A,resetDelay:3e3,status:d,setStatus:h,statusMessage:p2(d),settings:T,children:y.jsx("div",{className:Ke(`
                    grid
                    gap-x-0.5 gap-y-1
    
                    mx-auto
    
                    *:aspect-square
                    *:bg-gradient-to-b
    
                    data-[color=red]:*:from-[#f30308]
                    data-[color=red]:*:to-[#92393b]
                    data-[color=red]:*:[box-shadow:0px_5px_0px_#5c2829]
    
                    data-[color=green]:*:from-[#8ab357]
                    data-[color=green]:*:to-[#668a3d]
                    data-[color=green]:*:[box-shadow:0px_5px_0px_#48612f]
    
                    data-[color=blue]:*:from-[#5490b2]
                    data-[color=blue]:*:to-[#3a7494]
                    data-[color=blue]:*:[box-shadow:0px_5px_0px_#345066]
    
                    *:overflow-hidden
    
                    *:*:size-full
                    *:*:opacity-50
                    *:*:overflow-visible
    
                    *:data-[color=empty]:*:hidden
                `,d===0||d===4?"blur":""),style:{maxWidth:`calc(calc(calc(calc(calc(100vh - 208px) - ${4*(n-1)}px) / ${n}) * ${a}) + ${2*(a-1)}px)`,width:"calc(100vw - 64px)",gridTemplateRows:`repeat(${n}, minmax(0, 1fr))`,gridTemplateColumns:`repeat(${a}, minmax(0, 1fr))`},children:o.map((E,j)=>y.jsx("div",{"data-color":E,onClick:()=>g(j),children:y.jsxs("svg",{viewBox:"0 0 100 100",style:{padding:"0.5px"},children:[y.jsx("rect",{width:100,height:100,style:{fill:"none",stroke:"white",strokeWidth:"2"}}),y.jsx("path",{d:"M5 25 V5 H25 M75 5 H95 V25 M95 75 V95 H75 M25 95 H5 V75",style:{fill:"none",stroke:"white",strokeWidth:"1.5"}})]})},j))})})};var Mu={exports:{}};Mu.exports;(function(e){(function(t,n,r){function a(l){var u=this,f=s();u.next=function(){var c=2091639*u.s0+u.c*23283064365386963e-26;return u.s0=u.s1,u.s1=u.s2,u.s2=c-(u.c=c|0)},u.c=1,u.s0=f(" "),u.s1=f(" "),u.s2=f(" "),u.s0-=f(l),u.s0<0&&(u.s0+=1),u.s1-=f(l),u.s1<0&&(u.s1+=1),u.s2-=f(l),u.s2<0&&(u.s2+=1),f=null}function i(l,u){return u.c=l.c,u.s0=l.s0,u.s1=l.s1,u.s2=l.s2,u}function o(l,u){var f=new a(l),c=u&&u.state,d=f.next;return d.int32=function(){return f.next()*4294967296|0},d.double=function(){return d()+(d()*2097152|0)*11102230246251565e-32},d.quick=d,c&&(typeof c=="object"&&i(c,f),d.state=function(){return i(f,{})}),d}function s(){var l=4022871197,u=function(f){f=String(f);for(var c=0;c<f.length;c++){l+=f.charCodeAt(c);var d=.02519603282416938*l;l=d>>>0,d-=l,d*=l,l=d>>>0,d-=l,l+=d*4294967296}return(l>>>0)*23283064365386963e-26};return u}n&&n.exports?n.exports=o:this.alea=o})(jn,e)})(Mu);var g2=Mu.exports,Ou={exports:{}};Ou.exports;(function(e){(function(t,n,r){function a(s){var l=this,u="";l.x=0,l.y=0,l.z=0,l.w=0,l.next=function(){var c=l.x^l.x<<11;return l.x=l.y,l.y=l.z,l.z=l.w,l.w^=l.w>>>19^c^c>>>8},s===(s|0)?l.x=s:u+=s;for(var f=0;f<u.length+64;f++)l.x^=u.charCodeAt(f)|0,l.next()}function i(s,l){return l.x=s.x,l.y=s.y,l.z=s.z,l.w=s.w,l}function o(s,l){var u=new a(s),f=l&&l.state,c=function(){return(u.next()>>>0)/4294967296};return c.double=function(){do var d=u.next()>>>11,h=(u.next()>>>0)/4294967296,w=(d+h)/(1<<21);while(w===0);return w},c.int32=u.next,c.quick=c,f&&(typeof f=="object"&&i(f,u),c.state=function(){return i(u,{})}),c}n&&n.exports?n.exports=o:this.xor128=o})(jn,e)})(Ou);var v2=Ou.exports,zu={exports:{}};zu.exports;(function(e){(function(t,n,r){function a(s){var l=this,u="";l.next=function(){var c=l.x^l.x>>>2;return l.x=l.y,l.y=l.z,l.z=l.w,l.w=l.v,(l.d=l.d+362437|0)+(l.v=l.v^l.v<<4^(c^c<<1))|0},l.x=0,l.y=0,l.z=0,l.w=0,l.v=0,s===(s|0)?l.x=s:u+=s;for(var f=0;f<u.length+64;f++)l.x^=u.charCodeAt(f)|0,f==u.length&&(l.d=l.x<<10^l.x>>>4),l.next()}function i(s,l){return l.x=s.x,l.y=s.y,l.z=s.z,l.w=s.w,l.v=s.v,l.d=s.d,l}function o(s,l){var u=new a(s),f=l&&l.state,c=function(){return(u.next()>>>0)/4294967296};return c.double=function(){do var d=u.next()>>>11,h=(u.next()>>>0)/4294967296,w=(d+h)/(1<<21);while(w===0);return w},c.int32=u.next,c.quick=c,f&&(typeof f=="object"&&i(f,u),c.state=function(){return i(u,{})}),c}n&&n.exports?n.exports=o:this.xorwow=o})(jn,e)})(zu);var y2=zu.exports,Iu={exports:{}};Iu.exports;(function(e){(function(t,n,r){function a(s){var l=this;l.next=function(){var f=l.x,c=l.i,d,h;return d=f[c],d^=d>>>7,h=d^d<<24,d=f[c+1&7],h^=d^d>>>10,d=f[c+3&7],h^=d^d>>>3,d=f[c+4&7],h^=d^d<<7,d=f[c+7&7],d=d^d<<13,h^=d^d<<9,f[c]=h,l.i=c+1&7,h};function u(f,c){var d,h=[];if(c===(c|0))h[0]=c;else for(c=""+c,d=0;d<c.length;++d)h[d&7]=h[d&7]<<15^c.charCodeAt(d)+h[d+1&7]<<13;for(;h.length<8;)h.push(0);for(d=0;d<8&&h[d]===0;++d);for(d==8?h[7]=-1:h[d],f.x=h,f.i=0,d=256;d>0;--d)f.next()}u(l,s)}function i(s,l){return l.x=s.x.slice(),l.i=s.i,l}function o(s,l){s==null&&(s=+new Date);var u=new a(s),f=l&&l.state,c=function(){return(u.next()>>>0)/4294967296};return c.double=function(){do var d=u.next()>>>11,h=(u.next()>>>0)/4294967296,w=(d+h)/(1<<21);while(w===0);return w},c.int32=u.next,c.quick=c,f&&(f.x&&i(f,u),c.state=function(){return i(u,{})}),c}n&&n.exports?n.exports=o:this.xorshift7=o})(jn,e)})(Iu);var w2=Iu.exports,Du={exports:{}};Du.exports;(function(e){(function(t,n,r){function a(s){var l=this;l.next=function(){var f=l.w,c=l.X,d=l.i,h,w;return l.w=f=f+1640531527|0,w=c[d+34&127],h=c[d=d+1&127],w^=w<<13,h^=h<<17,w^=w>>>15,h^=h>>>12,w=c[d]=w^h,l.i=d,w+(f^f>>>16)|0};function u(f,c){var d,h,w,A,k,m=[],p=128;for(c===(c|0)?(h=c,c=null):(c=c+"\0",h=0,p=Math.max(p,c.length)),w=0,A=-32;A<p;++A)c&&(h^=c.charCodeAt((A+32)%c.length)),A===0&&(k=h),h^=h<<10,h^=h>>>15,h^=h<<4,h^=h>>>13,A>=0&&(k=k+1640531527|0,d=m[A&127]^=h+k,w=d==0?w+1:0);for(w>=128&&(m[(c&&c.length||0)&127]=-1),w=127,A=4*128;A>0;--A)h=m[w+34&127],d=m[w=w+1&127],h^=h<<13,d^=d<<17,h^=h>>>15,d^=d>>>12,m[w]=h^d;f.w=k,f.X=m,f.i=w}u(l,s)}function i(s,l){return l.i=s.i,l.w=s.w,l.X=s.X.slice(),l}function o(s,l){s==null&&(s=+new Date);var u=new a(s),f=l&&l.state,c=function(){return(u.next()>>>0)/4294967296};return c.double=function(){do var d=u.next()>>>11,h=(u.next()>>>0)/4294967296,w=(d+h)/(1<<21);while(w===0);return w},c.int32=u.next,c.quick=c,f&&(f.X&&i(f,u),c.state=function(){return i(u,{})}),c}n&&n.exports?n.exports=o:this.xor4096=o})(jn,e)})(Du);var x2=Du.exports,Fu={exports:{}};Fu.exports;(function(e){(function(t,n,r){function a(s){var l=this,u="";l.next=function(){var c=l.b,d=l.c,h=l.d,w=l.a;return c=c<<25^c>>>7^d,d=d-h|0,h=h<<24^h>>>8^w,w=w-c|0,l.b=c=c<<20^c>>>12^d,l.c=d=d-h|0,l.d=h<<16^d>>>16^w,l.a=w-c|0},l.a=0,l.b=0,l.c=-1640531527,l.d=1367130551,s===Math.floor(s)?(l.a=s/4294967296|0,l.b=s|0):u+=s;for(var f=0;f<u.length+20;f++)l.b^=u.charCodeAt(f)|0,l.next()}function i(s,l){return l.a=s.a,l.b=s.b,l.c=s.c,l.d=s.d,l}function o(s,l){var u=new a(s),f=l&&l.state,c=function(){return(u.next()>>>0)/4294967296};return c.double=function(){do var d=u.next()>>>11,h=(u.next()>>>0)/4294967296,w=(d+h)/(1<<21);while(w===0);return w},c.int32=u.next,c.quick=c,f&&(typeof f=="object"&&i(f,u),c.state=function(){return i(u,{})}),c}n&&n.exports?n.exports=o:this.tychei=o})(jn,e)})(Fu);var A2=Fu.exports,e0={exports:{}};const k2={},S2=Object.freeze(Object.defineProperty({__proto__:null,default:k2},Symbol.toStringTag,{value:"Module"})),b2=i0(S2);(function(e){(function(t,n,r){var a=256,i=6,o=52,s="random",l=r.pow(a,i),u=r.pow(2,o),f=u*2,c=a-1,d;function h(x,S,C){var b=[];S=S==!0?{entropy:!0}:S||{};var _=m(k(S.entropy?[x,g(n)]:x??p(),3),b),R=new w(b),T=function(){for(var E=R.g(i),j=l,I=0;E<u;)E=(E+I)*a,j*=a,I=R.g(1);for(;E>=f;)E/=2,j/=2,I>>>=1;return(E+I)/j};return T.int32=function(){return R.g(4)|0},T.quick=function(){return R.g(4)/4294967296},T.double=T,m(g(R.S),n),(S.pass||C||function(E,j,I,Y){return Y&&(Y.S&&A(Y,R),E.state=function(){return A(R,{})}),I?(r[s]=E,j):E})(T,_,"global"in S?S.global:this==r,S.state)}function w(x){var S,C=x.length,b=this,_=0,R=b.i=b.j=0,T=b.S=[];for(C||(x=[C++]);_<a;)T[_]=_++;for(_=0;_<a;_++)T[_]=T[R=c&R+x[_%C]+(S=T[_])],T[R]=S;(b.g=function(E){for(var j,I=0,Y=b.i,U=b.j,q=b.S;E--;)j=q[Y=c&Y+1],I=I*a+q[c&(q[Y]=q[U=c&U+j])+(q[U]=j)];return b.i=Y,b.j=U,I})(a)}function A(x,S){return S.i=x.i,S.j=x.j,S.S=x.S.slice(),S}function k(x,S){var C=[],b=typeof x,_;if(S&&b=="object")for(_ in x)try{C.push(k(x[_],S-1))}catch{}return C.length?C:b=="string"?x:x+"\0"}function m(x,S){for(var C=x+"",b,_=0;_<C.length;)S[c&_]=c&(b^=S[c&_]*19)+C.charCodeAt(_++);return g(S)}function p(){try{var x;return d&&(x=d.randomBytes)?x=x(a):(x=new Uint8Array(a),(t.crypto||t.msCrypto).getRandomValues(x)),g(x)}catch{var S=t.navigator,C=S&&S.plugins;return[+new Date,t,C,t.screen,g(n)]}}function g(x){return String.fromCharCode.apply(0,x)}if(m(r.random(),n),e.exports){e.exports=h;try{d=b2}catch{}}else r["seed"+s]=h})(typeof self<"u"?self:jn,[],Math)})(e0);var E2=e0.exports,C2=g2,N2=v2,_2=y2,P2=w2,j2=x2,T2=A2,Fn=E2;Fn.alea=C2;Fn.xor128=N2;Fn.xorwow=_2;Fn.xorshift7=P2;Fn.xor4096=j2;Fn.tychei=T2;var R2=Fn;const L2=Ji(R2),Zi=["ability","able","aboard","about","above","accept","accident","according","account","accurate","acres","across","act","action","active","activity","actual","actually","add","addition","additional","adjective","adult","adventure","advice","affect","afraid","after","afternoon","again","against","age","ago","agree","ahead","aid","air","airplane","alike","alive","all","allow","almost","alone","along","aloud","alphabet","already","also","although","am","among","amount","ancient","angle","angry","animal","announced","another","answer","ants","any","anybody","anyone","anything","anyway","anywhere","apart","apartment","appearance","apple","applied","appropriate","are","area","arm","army","around","arrange","arrangement","arrive","arrow","art","article","as","aside","ask","asleep","at","ate","atmosphere","atom","atomic","attached","attack","attempt","attention","audience","author","automobile","available","average","avoid","aware","away","baby","back","bad","badly","bag","balance","ball","balloon","band","bank","bar","bare","bark","barn","base","baseball","basic","basis","basket","bat","battle","be","bean","bear","beat","beautiful","beauty","became","because","become","becoming","bee","been","before","began","beginning","begun","behavior","behind","being","believed","bell","belong","below","belt","bend","beneath","bent","beside","best","bet","better","between","beyond","bicycle","bigger","biggest","bill","birds","birth","birthday","bit","bite","black","blank","blanket","blew","blind","block","blood","blow","blue","board","boat","body","bone","book","border","born","both","bottle","bottom","bound","bow","bowl","box","boy","brain","branch","brass","brave","bread","break","breakfast","breath","breathe","breathing","breeze","brick","bridge","brief","bright","bring","broad","broke","broken","brother","brought","brown","brush","buffalo","build","building","built","buried","burn","burst","bus","bush","business","busy","but","butter","buy","by","cabin","cage","cake","call","calm","came","camera","camp","can","canal","cannot","cap","capital","captain","captured","car","carbon","card","care","careful","carefully","carried","carry","case","cast","castle","cat","catch","cattle","caught","cause","cave","cell","cent","center","central","century","certain","certainly","chain","chair","chamber","chance","change","changing","chapter","character","characteristic","charge","chart","check","cheese","chemical","chest","chicken","chief","child","children","choice","choose","chose","chosen","church","circle","circus","citizen","city","class","classroom","claws","clay","clean","clear","clearly","climate","climb","clock","close","closely","closer","cloth","clothes","clothing","cloud","club","coach","coal","coast","coat","coffee","cold","collect","college","colony","color","column","combination","combine","come","comfortable","coming","command","common","community","company","compare","compass","complete","completely","complex","composed","composition","compound","concerned","condition","congress","connected","consider","consist","consonant","constantly","construction","contain","continent","continued","contrast","control","conversation","cook","cookies","cool","copper","copy","corn","corner","correct","correctly","cost","cotton","could","count","country","couple","courage","course","court","cover","cow","cowboy","crack","cream","create","creature","crew","crop","cross","crowd","cry","cup","curious","current","curve","customs","cut","cutting","daily","damage","dance","danger","dangerous","dark","darkness","date","daughter","dawn","day","dead","deal","dear","death","decide","declared","deep","deeply","deer","definition","degree","depend","depth","describe","desert","design","desk","detail","determine","develop","development","diagram","diameter","did","die","differ","difference","different","difficult","difficulty","dig","dinner","direct","direction","directly","dirt","dirty","disappear","discover","discovery","discuss","discussion","disease","dish","distance","distant","divide","division","do","doctor","does","dog","doing","doll","dollar","done","donkey","door","dot","double","doubt","down","dozen","draw","drawn","dream","dress","drew","dried","drink","drive","driven","driver","driving","drop","dropped","drove","dry","duck","due","dug","dull","during","dust","duty","each","eager","ear","earlier","early","earn","earth","easier","easily","east","easy","eat","eaten","edge","education","effect","effort","egg","eight","either","electric","electricity","element","elephant","eleven","else","empty","end","enemy","energy","engine","engineer","enjoy","enough","enter","entire","entirely","environment","equal","equally","equator","equipment","escape","especially","essential","establish","even","evening","event","eventually","ever","every","everybody","everyone","everything","everywhere","evidence","exact","exactly","examine","example","excellent","except","exchange","excited","excitement","exciting","exclaimed","exercise","exist","expect","experience","experiment","explain","explanation","explore","express","expression","extra","eye","face","facing","fact","factor","factory","failed","fair","fairly","fall","fallen","familiar","family","famous","far","farm","farmer","farther","fast","fastened","faster","fat","father","favorite","fear","feathers","feature","fed","feed","feel","feet","fell","fellow","felt","fence","few","fewer","field","fierce","fifteen","fifth","fifty","fight","fighting","figure","fill","film","final","finally","find","fine","finest","finger","finish","fire","fireplace","firm","first","fish","five","fix","flag","flame","flat","flew","flies","flight","floating","floor","flow","flower","fly","fog","folks","follow","food","foot","football","for","force","foreign","forest","forget","forgot","forgotten","form","former","fort","forth","forty","forward","fought","found","four","fourth","fox","frame","free","freedom","frequently","fresh","friend","friendly","frighten","frog","from","front","frozen","fruit","fuel","full","fully","fun","function","funny","fur","furniture","further","future","gain","game","garage","garden","gas","gasoline","gate","gather","gave","general","generally","gentle","gently","get","getting","giant","gift","girl","give","given","giving","glad","glass","globe","go","goes","gold","golden","gone","good","goose","got","government","grabbed","grade","gradually","grain","grandfather","grandmother","graph","grass","gravity","gray","great","greater","greatest","greatly","green","grew","ground","group","grow","grown","growth","guard","guess","guide","gulf","gun","habit","had","hair","half","halfway","hall","hand","handle","handsome","hang","happen","happened","happily","happy","harbor","hard","harder","hardly","has","hat","have","having","hay","he","headed","heading","health","heard","hearing","heart","heat","heavy","height","held","hello","help","helpful","her","herd","here","herself","hidden","hide","high","higher","highest","highway","hill","him","himself","his","history","hit","hold","hole","hollow","home","honor","hope","horn","horse","hospital","hot","hour","house","how","however","huge","human","hundred","hung","hungry","hunt","hunter","hurried","hurry","hurt","husband","ice","idea","identity","if","ill","image","imagine","immediately","importance","important","impossible","improve","in","inch","include","including","income","increase","indeed","independent","indicate","individual","industrial","industry","influence","information","inside","instance","instant","instead","instrument","interest","interior","into","introduced","invented","involved","iron","is","island","it","its","itself","jack","jar","jet","job","join","joined","journey","joy","judge","jump","jungle","just","keep","kept","key","kids","kill","kind","kitchen","knew","knife","know","knowledge","known","label","labor","lack","lady","laid","lake","lamp","land","language","large","larger","largest","last","late","later","laugh","law","lay","layers","lead","leader","leaf","learn","least","leather","leave","leaving","led","left","leg","length","lesson","let","letter","level","library","lie","life","lift","light","like","likely","limited","line","lion","lips","liquid","list","listen","little","live","living","load","local","locate","location","log","lonely","long","longer","look","loose","lose","loss","lost","lot","loud","love","lovely","low","lower","luck","lucky","lunch","lungs","lying","machine","machinery","mad","made","magic","magnet","mail","main","mainly","major","make","making","man","managed","manner","manufacturing","many","map","mark","market","married","mass","massage","master","material","mathematics","matter","may","maybe","me","meal","mean","means","meant","measure","meat","medicine","meet","melted","member","memory","men","mental","merely","met","metal","method","mice","middle","might","mighty","mile","military","milk","mill","mind","mine","minerals","minute","mirror","missing","mission","mistake","mix","mixture","model","modern","molecular","moment","money","monkey","month","mood","moon","more","morning","most","mostly","mother","motion","motor","mountain","mouse","mouth","move","movement","movie","moving","mud","muscle","music","musical","must","my","myself","mysterious","nails","name","nation","national","native","natural","naturally","nature","near","nearby","nearer","nearest","nearly","necessary","neck","needed","needle","needs","negative","neighbor","neighborhood","nervous","nest","never","new","news","newspaper","next","nice","night","nine","no","nobody","nodded","noise","none","noon","nor","north","nose","not","note","noted","nothing","notice","noun","now","number","numeral","nuts","object","observe","obtain","occasionally","occur","ocean","of","off","offer","office","officer","official","oil","old","older","oldest","on","once","one","only","onto","open","operation","opinion","opportunity","opposite","or","orange","orbit","order","ordinary","organization","organized","origin","original","other","ought","our","ourselves","out","outer","outline","outside","over","own","owner","oxygen","pack","package","page","paid","pain","paint","pair","palace","pale","pan","paper","paragraph","parallel","parent","park","part","particles","particular","particularly","partly","parts","party","pass","passage","past","path","pattern","pay","peace","pen","pencil","people","per","percent","perfect","perfectly","perhaps","period","person","personal","pet","phrase","physical","piano","pick","picture","pictured","pie","piece","pig","pile","pilot","pine","pink","pipe","pitch","place","plain","plan","plane","planet","planned","planning","plant","plastic","plate","plates","play","pleasant","please","pleasure","plenty","plural","plus","pocket","poem","poet","poetry","point","pole","police","policeman","political","pond","pony","pool","poor","popular","population","porch","port","position","positive","possible","possibly","post","pot","potatoes","pound","pour","powder","power","powerful","practical","practice","prepare","present","president","press","pressure","pretty","prevent","previous","price","pride","primitive","principal","principle","printed","private","prize","probably","problem","process","produce","product","production","program","progress","promised","proper","properly","property","protection","proud","prove","provide","public","pull","pupil","pure","purple","purpose","push","put","putting","quarter","queen","question","quick","quickly","quiet","quietly","quite","rabbit","race","radio","railroad","rain","raise","ran","ranch","range","rapidly","rate","rather","raw","rays","reach","read","reader","ready","real","realize","rear","reason","recall","receive","recent","recently","recognize","record","red","refer","refused","region","regular","related","relationship","religious","remain","remarkable","remember","remove","repeat","replace","replied","report","represent","require","research","respect","rest","result","return","review","rhyme","rhythm","rice","rich","ride","riding","right","ring","rise","rising","river","road","roar","rock","rocket","rocky","rod","roll","roof","room","root","rope","rose","rough","round","route","row","rubbed","rubber","rule","ruler","run","running","rush","sad","saddle","safe","safety","said","sail","sale","salmon","salt","same","sand","sang","sat","satellites","satisfied","save","saved","saw","say","scale","scared","scene","school","science","scientific","scientist","score","screen","sea","search","season","seat","second","secret","section","see","seed","seeing","seems","seen","seldom","select","selection","sell","send","sense","sent","sentence","separate","series","serious","serve","service","sets","setting","settle","settlers","seven","several","shade","shadow","shake","shaking","shall","shallow","shape","share","sharp","she","sheep","sheet","shelf","shells","shelter","shine","shinning","ship","shirt","shoe","shoot","shop","shore","short","shorter","shot","should","shoulder","shout","show","shown","shut","sick","sides","sight","sign","signal","silence","silent","silk","silly","silver","similar","simple","simplest","simply","since","sing","single","sink","sister","sit","sitting","situation","six","size","skill","skin","sky","slabs","slave","sleep","slept","slide","slight","slightly","slip","slipped","slope","slow","slowly","small","smaller","smallest","smell","smile","smoke","smooth","snake","snow","so","soap","social","society","soft","softly","soil","solar","sold","soldier","solid","solution","solve","some","somebody","somehow","someone","something","sometime","somewhere","son","song","soon","sort","sound","source","south","southern","space","speak","special","species","specific","speech","speed","spell","spend","spent","spider","spin","spirit","spite","split","spoken","sport","spread","spring","square","stage","stairs","stand","standard","star","stared","start","state","statement","station","stay","steady","steam","steel","steep","stems","step","stepped","stick","stiff","still","stock","stomach","stone","stood","stop","stopped","store","storm","story","stove","straight","strange","stranger","straw","stream","street","strength","stretch","strike","string","strip","strong","stronger","struck","structure","struggle","stuck","student","studied","studying","subject","substance","success","successful","such","sudden","suddenly","sugar","suggest","suit","sum","summer","sun","sunlight","supper","supply","support","suppose","sure","surface","surprise","surrounded","swam","sweet","swept","swim","swimming","swing","swung","syllable","symbol","system","table","tail","take","taken","tales","talk","tall","tank","tape","task","taste","taught","tax","tea","teach","teacher","team","tears","teeth","telephone","television","tell","temperature","ten","tent","term","terrible","test","than","thank","that","thee","them","themselves","then","theory","there","therefore","these","they","thick","thin","thing","think","third","thirty","this","those","thou","though","thought","thousand","thread","three","threw","throat","through","throughout","throw","thrown","thumb","thus","thy","tide","tie","tight","tightly","till","time","tin","tiny","tip","tired","title","to","tobacco","today","together","told","tomorrow","tone","tongue","tonight","too","took","tool","top","topic","torn","total","touch","toward","tower","town","toy","trace","track","trade","traffic","trail","train","transportation","trap","travel","treated","tree","triangle","tribe","trick","tried","trip","troops","tropical","trouble","truck","trunk","truth","try","tube","tune","turn","twelve","twenty","twice","two","type","typical","uncle","under","underline","understanding","unhappy","union","unit","universe","unknown","unless","until","unusual","up","upon","upper","upward","us","use","useful","using","usual","usually","valley","valuable","value","vapor","variety","various","vast","vegetable","verb","vertical","very","vessels","victory","view","village","visit","visitor","voice","volume","vote","vowel","voyage","wagon","wait","walk","wall","want","war","warm","warn","was","wash","waste","watch","water","wave","way","we","weak","wealth","wear","weather","week","weigh","weight","welcome","well","went","were","west","western","wet","whale","what","whatever","wheat","wheel","when","whenever","where","wherever","whether","which","while","whispered","whistle","white","who","whole","whom","whose","why","wide","widely","wife","wild","will","willing","win","wind","window","wing","winter","wire","wise","wish","with","within","without","wolf","women","won","wonder","wonderful","wood","wooden","wool","word","wore","work","worker","world","worried","worry","worse","worth","would","wrapped","write","writer","writing","written","wrong","wrote","yard","year","yellow","yes","yesterday","yet","you","young","younger","your","yourself","youth","zero","zebra","zipper","zoo","zulu"],ol=Zi.reduce((e,t)=>t.length<e.length?t:e).length,ll=Zi.reduce((e,t)=>t.length>e.length?t:e).length;function M2(e){const t=e!=null&&e.seed?new L2(e.seed):null,{minLength:n,maxLength:r,...a}=e||{};function i(){let h=typeof n!="number"?ol:s(n);const w=typeof r!="number"?ll:s(r);h>w&&(h=w);let A=!1,k;for(;!A;)k=o(),A=k.length<=w&&k.length>=h;return k}function o(){return Zi[l(Zi.length)]}function s(h){return h<ol&&(h=ol),h>ll&&(h=ll),h}function l(h){const w=t?t():Math.random();return Math.floor(w*h)}if(e===void 0)return i();if(typeof e=="number")e={exactly:e};else if(Object.keys(a).length===0)return i();e.exactly&&(e.min=e.exactly,e.max=e.exactly),typeof e.wordsPerString!="number"&&(e.wordsPerString=1),typeof e.formatter!="function"&&(e.formatter=h=>h),typeof e.separator!="string"&&(e.separator=" ");const u=e.min+l(e.max+1-e.min);let f=[],c="",d=0;for(let h=0;h<u*e.wordsPerString;h++)d===e.wordsPerString-1?c+=e.formatter(i(),d):c+=e.formatter(i(),d)+e.separator,d++,(h+1)%e.wordsPerString===0&&(f.push(c),c="",d=0);return typeof e.join=="string"&&(f=f.join(e.join)),f}const O2=e=>{switch(e){case 0:return"Reset!";case 1:return"";case 2:return zn.play(),"You suck at this :D";case 3:return On.play(),"Succeeded!";default:return`Error: Unknown game status ${e}`}};function z2(){const n=S=>{switch(S){case 1:console.log("Reset game"),w(),s(0),c([]),h(r());break}},r=()=>M2(12.5),[a,i]=Dn(25,n),[o,s]=v.useState(0),[l,u]=v.useState(),[f,c]=v.useState([]),[d,h]=v.useState(r),w=v.useCallback(()=>{c(S=>S.concat([l])),u(d[Math.floor(Math.random()*d.length)])},[d,l]),A=()=>{i(1)},k=S=>{console.log(`Win: ${S}`),i(3)},m=S=>{console.log(`Lose: ${S}`),i(2)},p=()=>{o>=25?k("All rounds completed"):(s(S=>S+1),w())},g=()=>{f.includes(l)?p():m(`${l} not seen yet (${f})`)},x=()=>{f.includes(l)?m(`${l} already seen (${f})`):p()};return y.jsxs(In,{title:"Word Memory",description:"Memorize the words seen",buttons:[[{label:"Seen",color:"purple",callback:g,disabled:a!==1},{label:"New",color:"green",callback:x,disabled:a!==1}]],countdownDuration:25*1e3,resetCallback:A,resetDelay:3e3,status:a,setStatus:i,statusMessage:O2(a),children:[y.jsxs("p",{className:"text-white text-2xl text-center w-full",children:[o,"/",25]}),y.jsx("div",{className:`
                h-32 w-[750px] max-w-full
                rounded-lg
                bg-[rgb(22_40_52)]
                flex items-center justify-center
                text-white text-5xl
            `,children:y.jsx("p",{children:l})})]})}/**
 * @license lucide-react v0.400.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I2=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),t0=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.400.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var D2={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.400.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const F2=v.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:a="",children:i,iconNode:o,...s},l)=>v.createElement("svg",{ref:l,...D2,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:t0("lucide",a),...s},[...o.map(([u,f])=>v.createElement(u,f)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.400.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bo=(e,t)=>{const n=v.forwardRef(({className:r,...a},i)=>v.createElement(F2,{ref:i,iconNode:t,className:t0(`lucide-${I2(e)}`,r),...a}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.400.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U2=bo("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.400.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $2=bo("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.400.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B2=bo("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);/**
 * @license lucide-react v0.400.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V2=bo("Triangle",[["path",{d:"M13.73 4a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"14u9p9"}]]),W2=e=>{switch(e){case 0:return"";case 1:return"";case 2:return zn.play(),"You suck at this :D";case 3:return On.play(),"Sometimes you win :O";case 4:return"Reset!";default:return`Error: Unknown game status ${e}`}},Vf=["red","blue","yellow","purple","orange","green"],Wf=["square","circle","triangle"],G2=(e,t)=>{const n=[];for(let r=0;r<e;r++){for(let a=0;a<t;a++)n.push([r.toString(),a.toString()]);n.push([r.toString(),"color"]),n.push([r.toString(),"#"])}return n},H2=(e,t)=>{const n=[];for(;n.length<e*t;)n.push(Wf[Math.floor(Math.random()*Wf.length)]);const r=[];for(;n.length;)r.push({color:Vf[Math.floor(Math.random()*Vf.length)],shapes:n.splice(0,t)});return r},Y2=(e,t)=>{const n=[];return t.forEach(r=>{r[1]==="color"?n.push({q:`Sequence ${Number(r[0])+1} Color`,a:`${e[Number(r[0])].color}`}):r[1]==="#"?n.push({q:`Sequence ${Number(r[0])+1} # of unique shapes`,a:`${M1(e[Number(r[0])].shapes)}`}):n.push({q:`Sequence ${Number(r[0])+1} Shape ${Number(r[1])+1}`,a:`${e[Number(r[0])].shapes[Number(r[1])]}`})}),n},Q2=(e,t)=>{let n=!0;return t.forEach((r,a)=>{r.a!==e[a].a&&(n=!1)}),n},Gf=4,Hf=2,sl=12,ul=30,Yf=3,q2=()=>{const[e,t]=v.useState(Gf),[n,r]=v.useState(Hf),[a,i]=v.useState(sl+ul),[o,s]=v.useState(sl),[l,u]=v.useState(ul),[f,c]=v.useState(Yf),d=v.useRef(null),[h,w]=v.useState(""),[A,k]=v.useState(100),[m,p]=v.useState(0),[g,x]=v.useState(""),[S,C]=v.useState([]),[b,_]=v.useState([]),[R,T]=v.useState([]),[E,j]=v.useState(!0),[I,Y]=v.useState(Gf),[U,q]=v.useState(Hf),[$,N]=v.useState(sl),[O,D]=v.useState(ul),[H,K]=v.useState(Yf),[tt,Ie]=v.useState(!1),ft=cn(),He=F=>{switch(F){case 1:console.log("Reset game"),fn();break}},[De,dt]=Dn(a*1e3,He);v.useEffect(()=>{tt&&(dt(1),Ie(!1))},[tt,dt]),un("playMinigame",F=>{if(F.minigame!=="laptop-terminal")return;const G=F.data;t(G.rows),r(G.columns),s(G.viewTime),u(G.typeTime),c(G.answersNeeded),i(G.viewTime+G.typeTime),Ie(!0)}),v.useEffect(()=>{Y(e),q(n),N(o),D(l),i(o+l),c(f)},[e,n,o,l,f]);const Un={handleSave:()=>{t(I),r(U),s($),u(O),i($+O),c(H)},handleReset:()=>{Y(e),q(n),N(o),D(l),K(f),dt(4)},children:y.jsxs("div",{className:"flex flex-col items-center",children:[y.jsx(we,{title:"Number of Rows",min:2,max:4,value:I,setValue:Y}),y.jsx(we,{title:"Number of Columns",min:2,max:5,value:U,setValue:q}),y.jsx(we,{title:"View Time",min:5,max:60,value:$,setValue:N}),y.jsx(we,{title:"Type Time",min:5,max:60,value:O,setValue:D}),y.jsx(we,{title:"Answers Needed",min:1,max:5,value:O,setValue:D})]})};v.useEffect(()=>{At()&&fn()},[]);const Ma=async()=>{At()?dt(1):(await Er(De===3),ft("/"))};v.useEffect(()=>{if(m>0&&h==="inprogress"){const F=m-.1;setTimeout(()=>{p(F),k(F/a*100)},100),m<=l&&j(!1)}},[m,h,n,a,A,l]),v.useEffect(()=>{var F;E||(T([{q:b[0].q,a:""}]),(F=d.current)==null||F.focus())},[E,b]);const fn=()=>{const F=H2(e,n);C(F);const G=vu(G2(e,n)).slice(0,f);_(Y2(F,G)),j(!0),T([]),w("inprogress"),p(a)},Eo=F=>{F.key==="Enter"&&X()},ie=F=>{console.log(`Win: ${F}`),dt(3)},J=F=>{console.log(`Lose: ${F}`),dt(2)},X=()=>{if(h!=="inprogress"||E)return;const F=R[R.length-1];F.a=g;const G=[...R];G.splice(G.length-1,1,F);const nt=Q2(b,G);nt&&G.length===b.length?(ie("S U C C E S S !"),T(G),x("")):nt?(T([...G,{q:b[G.length].q,a:""}]),x("")):(J("F A I L E D !"),T(G),x(""))};return y.jsx(In,{title:"Terminal",description:"Replicate the sequence",buttons:[],countdownDuration:a*1e3,resetCallback:Ma,resetDelay:3e3,status:De,setStatus:dt,statusMessage:W2(De),settings:Un,className:"w-2/3",children:y.jsxs("div",{className:"flex flex-col items-start border-muted w-full h-[60vh] gap-2 bg-[rgb(0_0_0/0.8)] rounded-lg p-5",children:[E&&S.map((F,G)=>y.jsxs("div",{className:"relative flex flex-col h-full items-start gap-2 text-sm font-bold",children:[y.jsx("span",{className:"text-teal-400",children:`SEQUENCE  ${G+1}`}),y.jsx("div",{className:"flex flex-row items-center gap-8",children:F.shapes.map((nt,Ye)=>y.jsxs("div",{className:"flex w-24 h-24 items-center justify-center",children:[nt==="square"&&y.jsx(B2,{height:96,width:96,color:F.color}),nt==="circle"&&y.jsx($2,{height:96,width:96,color:F.color}),nt==="triangle"&&y.jsx(V2,{height:96,width:96,color:F.color})]},`cell-${G}-${Ye}`))})]},`row-${G}`)),!E&&y.jsx("div",{className:"relative h-full w-full flex flex-col",children:R.map((F,G)=>y.jsxs("div",{className:"relative h-fit flex flex-col gap-2 fon-semibold text-lg",children:[y.jsx("div",{className:"text-teal-400",children:F.q}),F.a&&y.jsxs("div",{className:"text-white text-2xl",children:["> ",F.a]})]},G))}),y.jsxs("div",{className:"relative flex w-full h-[50px] flex-row items-start space-x-2",children:[y.jsx("input",{type:"text",placeholder:"Type  answer  here",value:g,onChange:F=>x(F.target.value.toLowerCase()),onKeyUp:F=>Eo(F),className:"relative border-muted-foreground w-full h-[50px] border-2 border-[rgb(255_255_255/0.7)] rounded-md p-2 text-white outline-none cursor-auto bg-[rgb(255_255_255/0.1)] font-sans text-md",ref:d}),y.jsx("button",{className:"border-muted-foreground hover:text-secondary hover:bg-primary w-[50px] h-[50px] flex items-center justify-center rounded-md text-[#38a2e5] bg-[rgb(56_163_229/0.4)]",onClick:X,children:y.jsx(U2,{className:"h-8 w-8"})})]})]})})},K2=()=>{const e=cn();return un("navigateMinigame",t=>{e(t.minigame)}),y.jsx("div",{className:"h-full w-full p-5 m-auto flex items-center justify-center",children:y.jsx(bg,{children:y.jsxs(mt,{path:"/",children:[y.jsx(mt,{index:!0,element:y.jsx(zg,{})}),y.jsx(mt,{path:"/thermite",element:y.jsx(t2,{})}),y.jsx(mt,{path:"/pincracker",element:y.jsx(r2,{})}),y.jsx(mt,{path:"/chopping",element:y.jsx(o2,{})}),y.jsx(mt,{path:"/lockpick",element:y.jsx(u2,{})}),y.jsx(mt,{path:"/roof-running",element:y.jsx(h2,{})}),y.jsx(mt,{path:"/word-memory",element:y.jsx(z2,{})}),y.jsx(mt,{path:"/laptop-terminal",element:y.jsx(q2,{})})]})})})};cl.createRoot(document.getElementById("root")).render(y.jsx(z1,{children:y.jsx(Tg,{children:y.jsx(K2,{})})}));
