-- Database management for Weather Health System

local QBCore = exports['qb-core']:GetCoreObject()

-- Database utility functions
Database = {}

-- Initialize all database tables
function Database.Initialize()
    -- Create weather_player_health table
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `weather_player_health` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `citizenid` varchar(50) NOT NULL,
            `health_status` int(11) DEFAULT 100,
            `disease_type` varchar(50) DEFAULT NULL,
            `disease_start_time` timestamp NULL DEFAULT NULL,
            `disease_severity` int(11) DEFAULT 0,
            `last_weather_check` timestamp DEFAULT CURRENT_TIMESTAMP,
            `temperature_comfort` varchar(20) DEFAULT 'comfortable',
            `clothing_warmth` int(11) DEFAULT 0,
            `immunity_level` int(11) DEFAULT 50,
            `treatment_history` text DEFAULT NULL,
            `last_treatment_time` timestamp NULL DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `citizenid` (`citizenid`),
            KEY `health_status` (`health_status`),
            KEY `disease_type` (`disease_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]])
    
    -- Create weather_npc_data table
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `weather_npc_data` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `npc_id` varchar(50) NOT NULL,
            `model` varchar(50) NOT NULL,
            `position_x` float NOT NULL,
            `position_y` float NOT NULL,
            `position_z` float NOT NULL,
            `heading` float NOT NULL,
            `health_status` int(11) DEFAULT 100,
            `disease_type` varchar(50) DEFAULT NULL,
            `disease_start_time` timestamp NULL DEFAULT NULL,
            `current_outfit` text DEFAULT NULL,
            `last_weather_check` timestamp DEFAULT CURRENT_TIMESTAMP,
            `temperature_tolerance` int(11) DEFAULT 0,
            `is_dead` tinyint(1) DEFAULT 0,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `npc_id` (`npc_id`),
            KEY `health_status` (`health_status`),
            KEY `is_dead` (`is_dead`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]])
    
    -- Create weather_logs table
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `weather_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `weather_type` varchar(20) NOT NULL,
            `temperature` int(11) NOT NULL,
            `time_hour` int(11) NOT NULL,
            `affected_players` int(11) DEFAULT 0,
            `affected_npcs` int(11) DEFAULT 0,
            `health_incidents` int(11) DEFAULT 0,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `weather_type` (`weather_type`),
            KEY `created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]])
    
    -- Create weather_treatments table
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `weather_treatments` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `citizenid` varchar(50) NOT NULL,
            `treatment_type` varchar(50) NOT NULL,
            `item_used` varchar(50) DEFAULT NULL,
            `disease_treated` varchar(50) DEFAULT NULL,
            `success_rate` float DEFAULT 0.0,
            `treatment_result` varchar(20) NOT NULL,
            `health_before` int(11) NOT NULL,
            `health_after` int(11) NOT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `citizenid` (`citizenid`),
            KEY `treatment_type` (`treatment_type`),
            KEY `created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]])
    
    -- Create weather_clothing_stats table
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `weather_clothing_stats` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `citizenid` varchar(50) NOT NULL,
            `clothing_component` varchar(20) NOT NULL,
            `component_id` int(11) NOT NULL,
            `warmth_value` int(11) NOT NULL,
            `usage_count` int(11) DEFAULT 1,
            `effectiveness_rating` float DEFAULT 1.0,
            `last_used` timestamp DEFAULT CURRENT_TIMESTAMP,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `citizenid` (`citizenid`),
            KEY `clothing_component` (`clothing_component`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]])
    
    print("^2[WeatherHealth]^7 Database tables initialized")
end

-- Player health data functions
function Database.SavePlayerHealth(citizenid, healthData)
    MySQL.insert('INSERT INTO weather_player_health (citizenid, health_status, disease_type, disease_start_time, disease_severity, temperature_comfort, clothing_warmth, immunity_level, last_weather_check) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW()) ON DUPLICATE KEY UPDATE health_status = VALUES(health_status), disease_type = VALUES(disease_type), disease_start_time = VALUES(disease_start_time), disease_severity = VALUES(disease_severity), temperature_comfort = VALUES(temperature_comfort), clothing_warmth = VALUES(clothing_warmth), immunity_level = VALUES(immunity_level), last_weather_check = VALUES(last_weather_check), updated_at = NOW()', {
        citizenid,
        healthData.health or 100,
        healthData.diseaseType,
        healthData.diseaseStartTime and os.date('%Y-%m-%d %H:%M:%S', healthData.diseaseStartTime) or nil,
        healthData.diseaseSeverity or 0,
        healthData.comfort or 'comfortable',
        healthData.clothingWarmth or 0,
        healthData.immunityLevel or 50
    })
end

function Database.LoadPlayerHealth(citizenid)
    local result = MySQL.single.await('SELECT * FROM weather_player_health WHERE citizenid = ?', {citizenid})
    
    if result then
        return {
            health = result.health_status or 100,
            diseaseType = result.disease_type,
            diseaseStartTime = result.disease_start_time and os.time(os.date("*t", result.disease_start_time)) or nil,
            diseaseSeverity = result.disease_severity or 0,
            comfort = result.temperature_comfort or 'comfortable',
            clothingWarmth = result.clothing_warmth or 0,
            immunityLevel = result.immunity_level or 50,
            lastUpdate = os.time()
        }
    else
        return {
            health = 100,
            diseaseType = nil,
            diseaseStartTime = nil,
            diseaseSeverity = 0,
            comfort = 'comfortable',
            clothingWarmth = 0,
            immunityLevel = 50,
            lastUpdate = os.time()
        }
    end
end

-- NPC data functions
function Database.SaveNPCData(npcId, npcData)
    MySQL.insert('INSERT INTO weather_npc_data (npc_id, model, position_x, position_y, position_z, heading, health_status, disease_type, disease_start_time, current_outfit, temperature_tolerance, is_dead, last_weather_check) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW()) ON DUPLICATE KEY UPDATE model = VALUES(model), position_x = VALUES(position_x), position_y = VALUES(position_y), position_z = VALUES(position_z), heading = VALUES(heading), health_status = VALUES(health_status), disease_type = VALUES(disease_type), disease_start_time = VALUES(disease_start_time), current_outfit = VALUES(current_outfit), temperature_tolerance = VALUES(temperature_tolerance), is_dead = VALUES(is_dead), last_weather_check = VALUES(last_weather_check), updated_at = NOW()', {
        npcId,
        npcData.model or '',
        npcData.position and npcData.position.x or 0,
        npcData.position and npcData.position.y or 0,
        npcData.position and npcData.position.z or 0,
        npcData.heading or 0,
        npcData.health or 100,
        npcData.diseaseType,
        npcData.diseaseStartTime and os.date('%Y-%m-%d %H:%M:%S', npcData.diseaseStartTime) or nil,
        npcData.currentOutfit and json.encode(npcData.currentOutfit) or nil,
        npcData.temperatureTolerance or 0,
        npcData.isDead and 1 or 0
    })
end

function Database.LoadNPCData(npcId)
    local result = MySQL.single.await('SELECT * FROM weather_npc_data WHERE npc_id = ?', {npcId})
    
    if result then
        return {
            model = result.model,
            position = {
                x = result.position_x,
                y = result.position_y,
                z = result.position_z
            },
            heading = result.heading,
            health = result.health_status or 100,
            diseaseType = result.disease_type,
            diseaseStartTime = result.disease_start_time and os.time(os.date("*t", result.disease_start_time)) or nil,
            currentOutfit = result.current_outfit and json.decode(result.current_outfit) or nil,
            temperatureTolerance = result.temperature_tolerance or 0,
            isDead = result.is_dead == 1,
            lastUpdate = os.time()
        }
    else
        return nil
    end
end

-- Treatment functions
function Database.RecordTreatment(citizenid, treatmentType, itemUsed, diseaseTreated, result, healthBefore, healthAfter)
    MySQL.insert('INSERT INTO weather_treatments (citizenid, treatment_type, item_used, disease_treated, treatment_result, health_before, health_after, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())', {
        citizenid,
        treatmentType,
        itemUsed,
        diseaseTreated,
        result,
        healthBefore,
        healthAfter
    })
end

function Database.GetTreatmentHistory(citizenid, limit)
    limit = limit or 10
    local result = MySQL.query.await('SELECT * FROM weather_treatments WHERE citizenid = ? ORDER BY created_at DESC LIMIT ?', {citizenid, limit})
    return result or {}
end

-- Clothing stats functions
function Database.UpdateClothingStats(citizenid, component, componentId, warmthValue)
    MySQL.insert('INSERT INTO weather_clothing_stats (citizenid, clothing_component, component_id, warmth_value, usage_count, last_used) VALUES (?, ?, ?, ?, 1, NOW()) ON DUPLICATE KEY UPDATE usage_count = usage_count + 1, last_used = NOW()', {
        citizenid,
        component,
        componentId,
        warmthValue
    })
end

function Database.GetClothingStats(citizenid)
    local result = MySQL.query.await('SELECT * FROM weather_clothing_stats WHERE citizenid = ?', {citizenid})
    return result or {}
end

-- Weather logging functions
function Database.LogWeatherEvent(weatherType, temperature, hour, affectedPlayers, affectedNPCs, healthIncidents)
    MySQL.insert('INSERT INTO weather_logs (weather_type, temperature, time_hour, affected_players, affected_npcs, health_incidents, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())', {
        weatherType,
        temperature,
        hour,
        affectedPlayers or 0,
        affectedNPCs or 0,
        healthIncidents or 0
    })
end

function Database.GetWeatherStats(days)
    days = days or 7
    local result = MySQL.query.await('SELECT weather_type, AVG(temperature) as avg_temp, SUM(affected_players) as total_affected_players, SUM(affected_npcs) as total_affected_npcs, SUM(health_incidents) as total_incidents FROM weather_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) GROUP BY weather_type', {days})
    return result or {}
end

-- Cleanup functions
function Database.CleanupOldData(days)
    days = days or 30
    
    -- Clean old logs
    MySQL.execute('DELETE FROM weather_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)', {days})
    
    -- Clean old treatments
    MySQL.execute('DELETE FROM weather_treatments WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)', {days})
    
    -- Clean old NPC data for dead NPCs
    MySQL.execute('DELETE FROM weather_npc_data WHERE is_dead = 1 AND updated_at < DATE_SUB(NOW(), INTERVAL ? DAY)', {days})
    
    print(string.format("^2[WeatherHealth]^7 Cleaned up data older than %d days", days))
end

-- Backup functions
function Database.BackupPlayerData()
    local timestamp = os.date('%Y%m%d_%H%M%S')
    MySQL.execute(string.format('CREATE TABLE weather_player_health_backup_%s AS SELECT * FROM weather_player_health', timestamp))
    print(string.format("^2[WeatherHealth]^7 Player data backed up to weather_player_health_backup_%s", timestamp))
end

-- Statistics functions
function Database.GetSystemStats()
    local stats = {}
    
    -- Player stats
    local playerStats = MySQL.single.await('SELECT COUNT(*) as total, SUM(CASE WHEN disease_type IS NOT NULL THEN 1 ELSE 0 END) as sick, AVG(health_status) as avg_health, AVG(immunity_level) as avg_immunity FROM weather_player_health')
    stats.players = playerStats or {}
    
    -- NPC stats
    local npcStats = MySQL.single.await('SELECT COUNT(*) as total, SUM(CASE WHEN disease_type IS NOT NULL THEN 1 ELSE 0 END) as sick, SUM(CASE WHEN is_dead = 1 THEN 1 ELSE 0 END) as dead, AVG(health_status) as avg_health FROM weather_npc_data')
    stats.npcs = npcStats or {}
    
    -- Treatment stats
    local treatmentStats = MySQL.single.await('SELECT COUNT(*) as total, SUM(CASE WHEN treatment_result = "success" THEN 1 ELSE 0 END) as successful, AVG(CASE WHEN treatment_result = "success" THEN 1 ELSE 0 END) * 100 as success_rate FROM weather_treatments WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)')
    stats.treatments = treatmentStats or {}
    
    return stats
end

-- Export database functions
exports('initializeDatabase', Database.Initialize)
exports('savePlayerHealth', Database.SavePlayerHealth)
exports('loadPlayerHealth', Database.LoadPlayerHealth)
exports('saveNPCData', Database.SaveNPCData)
exports('loadNPCData', Database.LoadNPCData)
exports('recordTreatment', Database.RecordTreatment)
exports('getTreatmentHistory', Database.GetTreatmentHistory)
exports('updateClothingStats', Database.UpdateClothingStats)
exports('getClothingStats', Database.GetClothingStats)
exports('logWeatherEvent', Database.LogWeatherEvent)
exports('getWeatherStats', Database.GetWeatherStats)
exports('cleanupOldData', Database.CleanupOldData)
exports('backupPlayerData', Database.BackupPlayerData)
exports('getSystemStats', Database.GetSystemStats)
