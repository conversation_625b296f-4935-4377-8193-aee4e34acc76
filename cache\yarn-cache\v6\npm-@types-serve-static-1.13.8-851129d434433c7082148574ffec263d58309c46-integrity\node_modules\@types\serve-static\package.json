{"name": "@types/serve-static", "version": "1.13.8", "description": "TypeScript definitions for serve-static", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/urossmolnik", "githubUsername": "urossmolnik"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LinusU", "githubUsername": "LinusU"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/devanshj", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-static"}, "scripts": {}, "dependencies": {"@types/mime": "*", "@types/node": "*"}, "typesPublisherContentHash": "c80d90d1e2e13bd1f7faaa45b3d6a85a48ff07783977ae9dc956309579b066dd", "typeScriptVersion": "3.2"}