{"manifest": {"name": "parallel-transform", "version": "1.2.0", "repository": {"type": "git", "url": "git://github.com/mafintosh/parallel-transform"}, "license": "MIT", "description": "Transform stream that allows you to run your transforms in parallel without changing the order", "keywords": ["transform", "stream", "parallel", "preserve", "order"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"cyclist": "^1.0.1", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-parallel-transform-1.2.0-9049ca37d6cb2182c3b1d2c720be94d14a5814fc-integrity\\node_modules\\parallel-transform\\package.json", "readmeFilename": "README.md", "readme": "# parallel-transform\n\n[Transform stream](http://nodejs.org/api/stream.html#stream_class_stream_transform_1) for Node.js that allows you to run your transforms\nin parallel without changing the order of the output.\n\n\tnpm install parallel-transform\n\nIt is easy to use\n\n``` js\nvar transform = require('parallel-transform');\n\nvar stream = transform(10, function(data, callback) { // 10 is the parallism level\n\tsetTimeout(function() {\n\t\tcallback(null, data);\n\t}, Math.random() * 1000);\n});\n\nfor (var i = 0; i < 10; i++) {\n\tstream.write(''+i);\n}\nstream.end();\n\nstream.on('data', function(data) {\n\tconsole.log(data); // prints 0,1,2,...\n});\nstream.on('end', function() {\n\tconsole.log('stream has ended');\n});\n```\n\nIf you run the above example you'll notice that it runs in parallel\n(does not take ~1 second between each print) and that the order is preserved\n\n## Stream options\n\nAll transforms are Node 0.10 streams. Per default they are created with the options `{objectMode:true}`.\nIf you want to use your own stream options pass them as the second parameter\n\n``` js\nvar stream = transform(10, {objectMode:false}, function(data, callback) {\n\t// data is now a buffer\n\tcallback(null, data);\n});\n\nfs.createReadStream('filename').pipe(stream).pipe(process.stdout);\n```\n\n### Unordered\nPassing the option `{ordered:false}` will output the data as soon as it's processed by a transform, without waiting to respect the order.\n\n## License\n\nMIT", "licenseText": "Copyright 2013 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/parallel-transform/-/parallel-transform-1.2.0.tgz#9049ca37d6cb2182c3b1d2c720be94d14a5814fc", "type": "tarball", "reference": "https://registry.yarnpkg.com/parallel-transform/-/parallel-transform-1.2.0.tgz", "hash": "9049ca37d6cb2182c3b1d2c720be94d14a5814fc", "integrity": "sha512-P2vSmIu38uIlvdcU7fDkyrxj33gTUy/ABO5ZUbGowxNCopBq/OoD42bP4UmMrJoPyk4Uqf0mu3mtWBhHCZD8yg==", "registry": "npm", "packageName": "parallel-transform", "cacheIntegrity": "sha512-P2vSmIu38uIlvdcU7fDkyrxj33gTUy/ABO5ZUbGowxNCopBq/OoD42bP4UmMrJoPyk4Uqf0mu3mtWBhHCZD8yg== sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw="}, "registry": "npm", "hash": "9049ca37d6cb2182c3b1d2c720be94d14a5814fc"}