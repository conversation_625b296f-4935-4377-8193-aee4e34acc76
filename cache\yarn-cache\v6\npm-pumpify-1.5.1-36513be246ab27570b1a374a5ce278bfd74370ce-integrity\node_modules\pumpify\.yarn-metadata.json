{"manifest": {"name": "pumpify", "version": "1.5.1", "description": "Combine an array of streams into a single duplex stream using pump and duplexify", "main": "index.js", "dependencies": {"duplexify": "^3.6.0", "inherits": "^2.0.3", "pump": "^2.0.0"}, "devDependencies": {"tape": "^4.8.0", "through2": "^2.0.3"}, "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "git://github.com/mafintosh/pumpify"}, "keywords": ["pump", "duplexify", "duplex", "streams", "stream", "pipeline", "combine"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/pumpify/issues"}, "homepage": "https://github.com/mafintosh/pumpify", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-pumpify-1.5.1-36513be246ab27570b1a374a5ce278bfd74370ce-integrity\\node_modules\\pumpify\\package.json", "readmeFilename": "README.md", "readme": "# pumpify\n\nCombine an array of streams into a single duplex stream using [pump](https://github.com/mafintosh/pump) and [duplexify](https://github.com/mafintosh/duplexify).\nIf one of the streams closes/errors all streams in the pipeline will be destroyed.\n\n```\nnpm install pumpify\n```\n\n[![build status](http://img.shields.io/travis/mafintosh/pumpify.svg?style=flat)](http://travis-ci.org/mafintosh/pumpify)\n\n## Usage\n\nPass the streams you want to pipe together to pumpify `pipeline = pumpify(s1, s2, s3, ...)`.\n`pipeline` is a duplex stream that writes to the first streams and reads from the last one.\nStreams are piped together using [pump](https://github.com/mafintosh/pump) so if one of them closes\nall streams will be destroyed.\n\n``` js\nvar pumpify = require('pumpify')\nvar tar = require('tar-fs')\nvar zlib = require('zlib')\nvar fs = require('fs')\n\nvar untar = pumpify(zlib.createGunzip(), tar.extract('output-folder'))\n// you can also pass an array instead\n// var untar = pumpify([zlib.createGunzip(), tar.extract('output-folder')])\n\nfs.createReadStream('some-gzipped-tarball.tgz').pipe(untar)\n```\n\nIf you are pumping object streams together use `pipeline = pumpify.obj(s1, s2, ...)`.\nCall `pipeline.destroy()` to destroy the pipeline (including the streams passed to pumpify).\n\n### Using `setPipeline(s1, s2, ...)`\n\nSimilar to [duplexify](https://github.com/mafintosh/duplexify) you can also define the pipeline asynchronously using `setPipeline(s1, s2, ...)`\n\n``` js\nvar untar = pumpify()\n\nsetTimeout(function() {\n  // will start draining the input now\n  untar.setPipeline(zlib.createGunzip(), tar.extract('output-folder'))\n}, 1000)\n\nfs.createReadStream('some-gzipped-tarball.tgz').pipe(untar)\n```\n\n## License\n\nMIT\n\n## Related\n\n`pumpify` is part of the [mississippi stream utility collection](https://github.com/maxogden/mississippi) which includes more useful stream modules similar to this one.\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 Mathias Buus\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/pumpify/-/pumpify-1.5.1.tgz#36513be246ab27570b1a374a5ce278bfd74370ce", "type": "tarball", "reference": "https://registry.yarnpkg.com/pumpify/-/pumpify-1.5.1.tgz", "hash": "36513be246ab27570b1a374a5ce278bfd74370ce", "integrity": "sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ==", "registry": "npm", "packageName": "pumpify", "cacheIntegrity": "sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ== sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4="}, "registry": "npm", "hash": "36513be246ab27570b1a374a5ce278bfd74370ce"}