{"manifest": {"name": "figgy-pudding", "version": "3.5.2", "description": "Delicious, festive, cascading config/opts definitions", "main": "index.js", "files": ["*.js", "lib"], "scripts": {"prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "pretest": "standard", "release": "standard-version -s", "test": "tap -J --100 --coverage test/*.js"}, "repository": {"type": "git", "url": "https://github.com/npm/figgy-pudding"}, "keywords": ["config", "options", "yummy"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "dependencies": {}, "devDependencies": {"standard": "^11.0.1", "standard-version": "^4.4.0", "tap": "^12.0.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-figgy-pudding-3.5.2-b4eee8148abb01dcf1d1ac34367d59e12fa61d6e-integrity\\node_modules\\figgy-pudding\\package.json", "readmeFilename": "README.md", "readme": "# Note: pending imminent deprecation\n\n**This module will be deprecated once npm v7 is released.  Please do not rely\non it more than absolutely necessary (ie, only if you are depending on\nit for use with npm v6 internal dependencies).**\n\n----\n\n# figgy-pudding [![npm version](https://img.shields.io/npm/v/figgy-pudding.svg)](https://npm.im/figgy-pudding) [![license](https://img.shields.io/npm/l/figgy-pudding.svg)](https://npm.im/figgy-pudding) [![Travis](https://img.shields.io/travis/npm/figgy-pudding.svg)](https://travis-ci.org/npm/figgy-pudding) [![Coverage Status](https://coveralls.io/repos/github/npm/figgy-pudding/badge.svg?branch=latest)](https://coveralls.io/github/npm/figgy-pudding?branch=latest)\n\n[`figgy-pudding`](https://github.com/npm/figgy-pudding) is a small JavaScript\nlibrary for managing and composing cascading options objects -- hiding what\nneeds to be hidden from each layer, without having to do a lot of manual munging\nand passing of options.\n\n### The God Object is Dead!\n### Now Bring Us Some Figgy Pudding!\n\n## Install\n\n`$ npm install figgy-pudding`\n\n## Table of Contents\n\n* [Example](#example)\n* [Features](#features)\n* [API](#api)\n  * [`figgyPudding(spec)`](#figgy-pudding)\n  * [`PuddingFactory(values)`](#pudding-factory)\n    * [`opts.get()`](#opts-get)\n    * [`opts.concat()`](#opts-concat)\n    * [`opts.toJSON()`](#opts-to-json)\n    * [`opts.forEach()`](#opts-for-each)\n    * [`opts[Symbol.iterator]()`](#opts-symbol-iterator)\n    * [`opts.entries()`](#opts-entries)\n    * [`opts.keys()`](#opts-keys)\n    * [`opts.value()`](#opts-values)\n\n### Example\n\n```javascript\n// print-package.js\nconst fetch = require('./fetch.js')\nconst puddin = require('figgy-pudding')\n\nconst PrintOpts = puddin({\n  json: { default: false }\n})\n\nasync function printPkg (name, opts) {\n  // Expected pattern is to call this in every interface function. If `opts` is\n  // not passed in, it will automatically create an (empty) object for it.\n  opts = PrintOpts(opts)\n  const uri = `https://registry.npmjs.com/${name}`\n  const res = await fetch(uri, opts.concat({\n    // Add or override any passed-in configs and pass them down.\n    log: customLogger\n  }))\n  // The following would throw an error, because it's not in PrintOpts:\n  // console.log(opts.log)\n  if (opts.json) {\n    return res.json()\n  } else {\n    return res.text()\n  }\n}\n\nconsole.log(await printPkg('figgy', {\n  // Pass in *all* configs at the toplevel, as a regular object.\n  json: true,\n  cache: './tmp-cache'\n}))\n```\n\n```javascript\n// fetch.js\nconst puddin = require('figgy-pudding')\n\nconst FetchOpts = puddin({\n  log: { default: require('npmlog') },\n  cache: {}\n})\n\nmodule.exports = async function (..., opts) {\n  opts = FetchOpts(opts)\n}\n```\n\n### Features\n\n* hide options from layer that didn't ask for it\n* shared multi-layer options\n* make sure `opts` argument is available\n* transparent key access like normal keys, through a Proxy. No need for`.get()`!\n* default values\n* key aliases\n* arbitrary key filter functions\n* key/value iteration\n* serialization\n* 100% test coverage using `tap --100`\n\n### API\n\n#### <a name=\"figgy-pudding\"></a> `> figgyPudding({ key: { default: val } | String }, [opts]) -> PuddingFactory`\n\nDefines an Options constructor that can be used to collect only the needed\noptions.\n\nAn optional `default` property for specs can be used to specify default values\nif nothing was passed in.\n\nIf the value for a spec is a string, it will be treated as an alias to that\nother key.\n\n##### Example\n\n```javascript\nconst MyAppOpts = figgyPudding({\n  lg: 'log',\n  log: {\n    default: () => require('npmlog')\n  },\n  cache: {}\n})\n```\n\n#### <a name=\"pudding-factory\"></a> `> PuddingFactory(...providers) -> FiggyPudding{}`\n\nInstantiates an options object defined by `figgyPudding()`, which uses\n`providers`, in order, to find requested properties.\n\nEach provider can be either a plain object, a `Map`-like object (that is, one\nwith a `.get()` method) or another figgyPudding `Opts` object.\n\nWhen nesting `Opts` objects, their properties will not become available to the\nnew object, but any further nested `Opts` that reference that property _will_ be\nable to read from their grandparent, as long as they define that key. Default\nvalues for nested `Opts` parents will be used, if found.\n\n##### Example\n\n```javascript\nconst ReqOpts = figgyPudding({\n  follow: {}\n})\n\nconst opts = ReqOpts({\n  follow: true,\n  log: require('npmlog')\n})\n\nopts.follow // => true\nopts.log // => Error: ReqOpts does not define `log`\n\nconst MoreOpts = figgyPudding({\n  log: {}\n})\nMoreOpts(opts).log // => npmlog object (passed in from original plain obj)\nMoreOpts(opts).follow // => Error: MoreOpts does not define `follow`\n```\n\n#### <a name=\"opts-get\"></a> `> opts.get(key) -> Value`\n\nGets a value from the options object.\n\n##### Example\n\n```js\nconst opts = MyOpts(config)\nopts.get('foo') // value of `foo`\nopts.foo // Proxy-based access through `.get()`\n```\n\n#### <a name=\"opts-concat\"></a> `> opts.concat(...moreProviders) -> FiggyPudding{}`\n\nCreates a new opts object of the same type as `opts` with additional providers.\nProviders further to the right shadow providers to the left, with properties in\nthe original `opts` being shadows by the new providers.\n\n##### Example\n\n```js\nconst opts = MyOpts({x: 1})\nopts.get('x') // 1\nopts.concat({x: 2}).get('x') // 2\nopts.get('x') // 1 (original opts object left intact)\n```\n\n#### <a name=\"opts-to-json\"></a> `> opts.toJSON() -> Value`\n\nConverts `opts` to a plain, JSON-stringifiable JavaScript value. Used internally\nby JavaScript to get `JSON.stringify()` working.\n\nOnly keys that are readable by the current pudding type will be serialized.\n\n##### Example\n\n```js\nconst opts = MyOpts({x: 1})\nopts.toJSON() // {x: 1}\nJSON.stringify(opts) // '{\"x\":1}'\n```\n\n#### <a name=\"opts-for-each\"></a> `> opts.forEach((value, key, opts) => {}, thisArg) -> undefined`\n\nIterates over the values of `opts`, limited to the keys readable by the current\npudding type. `thisArg` will be used to set the `this` argument when calling the\n`fn`.\n\n##### Example\n\n```js\nconst opts = MyOpts({x: 1, y: 2})\nopts.forEach((value, key) => console.log(key, '=', value))\n```\n\n#### <a name=\"opts-entries\"></a> `> opts.entries() -> Iterator<[[key, value], ...]>`\n\nReturns an iterator that iterates over the keys and values in `opts`, limited to\nthe keys readable by the current pudding type. Each iteration returns an array\nof `[key, value]`.\n\n##### Example\n\n```js\nconst opts = MyOpts({x: 1, y: 2})\n[...opts({x: 1, y: 2}).entries()] // [['x', 1], ['y', 2]]\n```\n\n#### <a name=\"opts-symbol-iterator\"></a> `> opts[Symbol.iterator]() -> Iterator<[[key, value], ...]>`\n\nReturns an iterator that iterates over the keys and values in `opts`, limited to\nthe keys readable by the current pudding type. Each iteration returns an array\nof `[key, value]`. Makes puddings work natively with JS iteration mechanisms.\n\n##### Example\n\n```js\nconst opts = MyOpts({x: 1, y: 2})\n[...opts({x: 1, y: 2})] // [['x', 1], ['y', 2]]\nfor (let [key, value] of opts({x: 1, y: 2})) {\n  console.log(key, '=', value)\n}\n```\n\n#### <a name=\"opts-keys\"></a> `> opts.keys() -> Iterator<[key, ...]>`\n\nReturns an iterator that iterates over the keys in `opts`, limited to the keys\nreadable by the current pudding type.\n\n##### Example\n\n```js\nconst opts = MyOpts({x: 1, y: 2})\n[...opts({x: 1, y: 2}).keys()] // ['x', 'y']\n```\n\n#### <a name=\"opts-values\"></a> `> opts.values() -> Iterator<[value, ...]>`\n\nReturns an iterator that iterates over the values in `opts`, limited to the keys\nreadable by the current pudding type.\n\n##### Example\n'\n```js\nconst opts = MyOpts({x: 1, y: 2})\n[...opts({x: 1, y: 2}).values()] // [1, 2]\n```\n", "licenseText": "ISC License\n\nCopyright (c) npm, Inc.\n\nPermission to use, copy, modify, and/or distribute this software for\nany purpose with or without fee is hereby granted, provided that the\nabove copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE COPYRIGHT HOLDER DISCLAIMS\nALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED\nWARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE\nCOPYRIGHT HOLDER BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR\nCONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS\nOF USE, <PERSON><PERSON><PERSON> OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE\nOR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE\nUSE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/figgy-pudding/-/figgy-pudding-3.5.2.tgz#b4eee8148abb01dcf1d1ac34367d59e12fa61d6e", "type": "tarball", "reference": "https://registry.yarnpkg.com/figgy-pudding/-/figgy-pudding-3.5.2.tgz", "hash": "b4eee8148abb01dcf1d1ac34367d59e12fa61d6e", "integrity": "sha512-0btnI/H8f2pavGMN8w40mlSKOfTK2SVJmBfBeVIj3kNw0swwgzyRq0d5TJVOwodFmtvpPeWPN/MCcfuWF0Ezbw==", "registry": "npm", "packageName": "figgy-pudding", "cacheIntegrity": "sha512-0btnI/H8f2pavGMN8w40mlSKOfTK2SVJmBfBeVIj3kNw0swwgzyRq0d5TJVOwodFmtvpPeWPN/MCcfuWF0Ezbw== sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4="}, "registry": "npm", "hash": "b4eee8148abb01dcf1d1ac34367d59e12fa61d6e"}