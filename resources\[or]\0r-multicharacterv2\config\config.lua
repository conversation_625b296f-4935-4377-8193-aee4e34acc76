-- ██╗    ██╗███████╗██╗      ██████╗ ██████╗ ███╗   ███╗███████╗    ████████╗ ██████╗      ██████╗ ██████╗ ███╗   ██╗███████╗██╗ ██████╗ 
-- ██║    ██║██╔════╝██║     ██╔════╝██╔═══██╗████╗ ████║██╔════╝    ╚══██╔══╝██╔═══██╗    ██╔════╝██╔═══██╗████╗  ██║██╔════╝██║██╔════╝ 
-- ██║ █╗ ██║█████╗  ██║     ██║     ██║   ██║██╔████╔██║█████╗         ██║   ██║   ██║    ██║     ██║   ██║██╔██╗ ██║█████╗  ██║██║  ███╗
-- ██║███╗██║██╔══╝  ██║     ██║     ██║   ██║██║╚██╔╝██║██╔══╝         ██║   ██║   ██║    ██║     ██║   ██║██║╚██╗██║██╔══╝  ██║██║   ██║
-- ╚███╔███╔╝███████╗███████╗╚██████╗╚██████╔╝██║ ╚═╝ ██║███████╗       ██║   ╚██████╔╝    ╚██████╗╚██████╔╝██║ ╚████║██║     ██║╚██████╔╝
--  ╚══╝╚══╝ ╚══════╝╚══════╝ ╚═════╝ ╚═════╝ ╚═╝     ╚═╝╚══════╝       ╚═╝    ╚═════╝      ╚═════╝ ╚═════╝ ╚═╝  ╚═══╝╚═╝     ╚═╝ ╚═════╝ 
        
--[[
    Hello Dear Customer!

    Thank you for purchasing the 0Resmon Multicharacter Version 2 script!

    Here you can easily change many settings and customise it to make it compatible with your own server.

    If you encounter any problems, you can open a ticket via https://discord.gg/0resmon
]]


Config = {}

-- ██╗      ██████╗  ██████╗ █████╗ ██╗     ███████╗
-- ██║     ██╔═══██╗██╔════╝██╔══██╗██║     ██╔════╝
-- ██║     ██║   ██║██║     ███████║██║     █████╗  
-- ██║     ██║   ██║██║     ██╔══██║██║     ██╔══╝  
-- ███████╗╚██████╔╝╚██████╗██║  ██║███████╗███████╗
-- ╚══════╝ ╚═════╝  ╚═════╝╚═╝  ╚═╝╚══════╝╚══════╝

--[[
    There are currently 2 languages that support our script, these are;

    - ar (Arabic) - NEW
    - en (English)
    - tr (Turkish)
    - pt (Portuguese) - NEW

    By default ‘en’ is used, if you want us to support your language, send us the files via our discord server!
]]

Config.Locale = 'en'

-- ██╗      ██████╗  ██████╗ █████╗ ████████╗██╗ ██████╗ ███╗   ██╗
-- ██║     ██╔═══██╗██╔════╝██╔══██╗╚══██╔══╝██║██╔═══██╗████╗  ██║
-- ██║     ██║   ██║██║     ███████║   ██║   ██║██║   ██║██╔██╗ ██║
-- ██║     ██║   ██║██║     ██╔══██║   ██║   ██║██║   ██║██║╚██╗██║
-- ███████╗╚██████╔╝╚██████╗██║  ██║   ██║   ██║╚██████╔╝██║ ╚████║
-- ╚══════╝ ╚═════╝  ╚═════╝╚═╝  ╚═╝   ╚═╝   ╚═╝ ╚═════╝ ╚═╝  ╚═══╝

--[[
    Here you can change certain settings by entering your country, this setting changes the currency language in the UI interface
]]

Config.Location = 'en-US'

--  ██████╗██╗   ██╗██████╗ ██████╗ ███████╗███╗   ██╗ ██████╗██╗   ██╗
-- ██╔════╝██║   ██║██╔══██╗██╔══██╗██╔════╝████╗  ██║██╔════╝╚██╗ ██╔╝
-- ██║     ██║   ██║██████╔╝██████╔╝█████╗  ██╔██╗ ██║██║      ╚████╔╝ 
-- ██║     ██║   ██║██╔══██╗██╔══██╗██╔══╝  ██║╚██╗██║██║       ╚██╔╝  
-- ╚██████╗╚██████╔╝██║  ██║██║  ██║███████╗██║ ╚████║╚██████╗   ██║   
--  ╚═════╝ ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═══╝ ╚═════╝   ╚═╝   

--[[
    Here you can change the currency in the UI by specifying your currency directly.
]]

Config.Currency = 'EUR'

-- ██╗  ██╗██╗██████╗ ███████╗    ██████╗  █████╗ ██████╗  █████╗ ██████╗ 
-- ██║  ██║██║██╔══██╗██╔════╝    ██╔══██╗██╔══██╗██╔══██╗██╔══██╗██╔══██╗
-- ███████║██║██║  ██║█████╗      ██████╔╝███████║██║  ██║███████║██████╔╝
-- ██╔══██║██║██║  ██║██╔══╝      ██╔══██╗██╔══██║██║  ██║██╔══██║██╔══██╗
-- ██║  ██║██║██████╔╝███████╗    ██║  ██║██║  ██║██████╔╝██║  ██║██║  ██║
-- ╚═╝  ╚═╝╚═╝╚═════╝ ╚══════╝    ╚═╝  ╚═╝╚═╝  ╚═╝╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝

--[[
    If your hud does not hide the map when you first login, set this to true
--]]

Config.HideRadar = false

-- ██╗      ██████╗  ██████╗ ███████╗
-- ██║     ██╔═══██╗██╔════╝ ██╔════╝
-- ██║     ██║   ██║██║  ███╗███████╗
-- ██║     ██║   ██║██║   ██║╚════██║
-- ███████╗╚██████╔╝╚██████╔╝███████║
-- ╚══════╝ ╚═════╝  ╚═════╝ ╚══════╝

--[[
    If you are using qb-logs, set this to true
]]

Config.Logs = {}
Config.Logs.Status = false      --  true (optional)
Config.Logs.Logger = 'discord'  -- 'discord', 'fivemerr', 'ox' (ox support: fivemanage, datadog, grafana loki logging )

-- ██████╗ ███████╗███████╗ █████╗ ██╗   ██╗██╗  ████████╗
-- ██╔══██╗██╔════╝██╔════╝██╔══██╗██║   ██║██║  ╚══██╔══╝
-- ██║  ██║█████╗  █████╗  ███████║██║   ██║██║     ██║   
-- ██║  ██║██╔══╝  ██╔══╝  ██╔══██║██║   ██║██║     ██║   
-- ██████╔╝███████╗██║     ██║  ██║╚██████╔╝███████╗██║   
-- ╚═════╝ ╚══════╝╚═╝     ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝   

--[[
    Here you can change the default settings for the player, such as the number of slots, the bucket, and the settings.
]]

Config.Default = {}
Config.Default.Slots = 1
Config.Default.Bucket = 0
Config.Default.Settings = {
    ['streamerMode'] = false,
    ['mutedMusic'] = false,
    ['focusPlayer'] = true,
    ['clearMode'] = true,
    ['filter'] = {
        ['status'] = false,
        ['type'] = 'default',
    },
    ['particle'] = {
        ['status'] = false,
        ['type'] = {
            ['pName'] = 'scr_xs_celebration',
            ['pNameChild'] = 'scr_xs_confetti_burst'
        },
    },
    ['color'] = {
        ['name'] = "dark-green",
        ['hex'] = "#04CD96",
        ['shadow'] = "rgba(4, 205, 150, 0.72)",
    },
    ['time'] = {
        ['status'] = false,
        ['hour'] = 12,
    },
    ['weather'] = {
        ['status'] = false,
        ['type'] = 'EXTRASUNNY',
    },
    ['coords'] = {
        ['id'] = 19,
        ['pedCoords'] = vector4(2967.99, 5321.21, 100.68, 139.24),
        ['scenario'] = 'PROP_HUMAN_SEAT_CHAIR_DRINK',
        ['cameraCoords'] = vector3(0.3, 2.5, 0.3)
    },
    ['animations'] = {
        ['status'] = false,
        ['single'] = 'uwu',
        ['scenario'] = {
            ['status'] = false,
            ['single'] = 'WORLD_HUMAN_SMOKING',
        },
        ['camera'] = 'front', -- front, back, left, right, front-left, front-right, back-left, back-right, up-front-right, up-front-left, up-back-right, up-back-left
        ['entrance'] = 'top-and-bottom', -- top-and-bottom, left-and-right
    }
}
Config.Default.Spawn = {}
Config.Default.Spawn.Single = vector4(-1037.11, -2736.96, 20.17, 323.76)

--  ██████╗ ██████╗ ██╗      ██████╗ ██████╗ ███████╗
-- ██╔════╝██╔═══██╗██║     ██╔═══██╗██╔══██╗██╔════╝
-- ██║     ██║   ██║██║     ██║   ██║██████╔╝███████╗
-- ██║     ██║   ██║██║     ██║   ██║██╔══██╗╚════██║
-- ╚██████╗╚██████╔╝███████╗╚██████╔╝██║  ██║███████║
--  ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚═╝  ╚═╝╚══════╝

--[[
    Here you can change the players' interface colours, but remember that players can change this from within the game, so it will only be visible for new players
]]

Config.Colors = {
    {
        name = "dark-green",
        hex = "#04CD96",
        shadow = "rgba(4, 205, 150, 0.33)",
    },
    {
        name = "green",
        hex = "#20EFB1",
        shadow = "rgba(32, 239, 177, 0.33)",
    },
    {
        name = "cyan",
        hex = "#20EFEF",
        shadow = "rgba(32, 239, 239, 0.33)",
    },
    {
        name = "blue",
        hex = "#20AEEF",
        shadow = "rgba(32, 174, 239, 0.33)",
    },
    {
        name = "red",
        hex = "#EF2020",
        shadow = "rgba(239, 32, 32, 0.33)",
    },
    {
        name = "yellow",
        hex = "#EFEF20",
        shadow = "rgba(239, 239, 32, 0.33)",
    },
    {
        name = "purple",
        hex = "#A620EF",
        shadow = "rgba(166, 32, 239, 0.33)",
    },
    {
        name = "orange",
        hex = "#EF7A20",
        shadow = "rgba(239, 122, 32, 0.33)",
    },
    {
        name = "pink",
        hex = "#EF20A6",
        shadow = "rgba(239, 32, 166, 0.33)",
    },
    {
        name = "white",
        hex = "#FFFFFF",
        shadow = "rgba(255, 255, 255, 0.33)",
    },
    {
        name = "lemon",
        hex = "#C7EA46",
        shadow = "rgba(199, 234, 70, 0.33)",
    },
    {
        name = "gray",
        hex = "#FFFFFF80",
        shadow = "rgba(47, 47, 47, 0.33)",
    },
    {
        name = "light-pink",
        hex = "#FFA9F6",
        shadow = "rgba(255, 169, 246, 0.33)",
    },
}

--  ██████╗██╗      ██████╗ ████████╗██╗  ██╗██╗███╗   ██╗ ██████╗ 
-- ██╔════╝██║     ██╔═══██╗╚══██╔══╝██║  ██║██║████╗  ██║██╔════╝ 
-- ██║     ██║     ██║   ██║   ██║   ███████║██║██╔██╗ ██║██║  ███╗
-- ██║     ██║     ██║   ██║   ██║   ██╔══██║██║██║╚██╗██║██║   ██║
-- ╚██████╗███████╗╚██████╔╝   ██║   ██║  ██║██║██║ ╚████║╚██████╔╝
--  ╚═════╝╚══════╝ ╚═════╝    ╚═╝   ╚═╝  ╚═╝╚═╝╚═╝  ╚═══╝ ╚═════╝ 

--[[
    From here you should set the outfit script we support dealer outfit scripts;

    - illenium-appearance
    - bl_appearance
    - fivem-appearance
    - crm-appearance
    - qb-clothing
    - rcore_clothing
    - 0r-appearance - NEW
    - custom
]]

Config.Clothing = '0r-appearance'

--  █████╗ ███╗   ██╗██╗███╗   ███╗ █████╗ ████████╗██╗ ██████╗ ███╗   ██╗    ███╗   ███╗███████╗███╗   ██╗██╗   ██╗
-- ██╔══██╗████╗  ██║██║████╗ ████║██╔══██╗╚══██╔══╝██║██╔═══██╗████╗  ██║    ████╗ ████║██╔════╝████╗  ██║██║   ██║
-- ███████║██╔██╗ ██║██║██╔████╔██║███████║   ██║   ██║██║   ██║██╔██╗ ██║    ██╔████╔██║█████╗  ██╔██╗ ██║██║   ██║
-- ██╔══██║██║╚██╗██║██║██║╚██╔╝██║██╔══██║   ██║   ██║██║   ██║██║╚██╗██║    ██║╚██╔╝██║██╔══╝  ██║╚██╗██║██║   ██║
-- ██║  ██║██║ ╚████║██║██║ ╚═╝ ██║██║  ██║   ██║   ██║╚██████╔╝██║ ╚████║    ██║ ╚═╝ ██║███████╗██║ ╚████║╚██████╔╝
-- ╚═╝  ╚═╝╚═╝  ╚═══╝╚═╝╚═╝     ╚═╝╚═╝  ╚═╝   ╚═╝   ╚═╝ ╚═════╝ ╚═╝  ╚═══╝    ╚═╝     ╚═╝╚══════╝╚═╝  ╚═══╝ ╚═════╝ 

--[[
    From here, if you want to use animation instead of a script, you must specify an animation menu, here are some of the scripts we support;

    - rpemotes
    - scully
    - other
]]

Config.AnimationMenu = 'other'

--  ██████╗██╗      ██████╗ ████████╗██╗  ██╗██╗███╗   ██╗ ██████╗     ███████╗██╗  ██╗██████╗  ██████╗ ██████╗ ████████╗███████╗
-- ██╔════╝██║     ██╔═══██╗╚══██╔══╝██║  ██║██║████╗  ██║██╔════╝     ██╔════╝╚██╗██╔╝██╔══██╗██╔═══██╗██╔══██╗╚══██╔══╝██╔════╝
-- ██║     ██║     ██║   ██║   ██║   ███████║██║██╔██╗ ██║██║  ███╗    █████╗   ╚███╔╝ ██████╔╝██║   ██║██████╔╝   ██║   ███████╗
-- ██║     ██║     ██║   ██║   ██║   ██╔══██║██║██║╚██╗██║██║   ██║    ██╔══╝   ██╔██╗ ██╔═══╝ ██║   ██║██╔══██╗   ██║   ╚════██║
-- ╚██████╗███████╗╚██████╔╝   ██║   ██║  ██║██║██║ ╚████║╚██████╔╝    ███████╗██╔╝ ██╗██║     ╚██████╔╝██║  ██║   ██║   ███████║
--  ╚═════╝╚══════╝ ╚═════╝    ╚═╝   ╚═╝  ╚═╝╚═╝╚═╝  ╚═══╝ ╚═════╝     ╚══════╝╚═╝  ╚═╝╚═╝      ╚═════╝ ╚═╝  ╚═╝   ╚═╝   ╚══════╝

Config.ClothingExports = function(cacheped, skinData)
    if Config.Clothing == 'illenium-appearance' then
        exports['illenium-appearance']:setPedAppearance(cacheped, skinData)
    elseif Config.Clothing == 'bl_appearance' then
        exports.bl_appearance:SetPlayerPedAppearance(skinData)
    elseif Config.Clothing == 'fivem-appearance' then
        exports['fivem-appearance']:setPedAppearance(cacheped, skinData)
    elseif Config.Clothing == 'qb-clothing' then
        TriggerEvent('qb-clothing:client:loadPlayerClothing', skinData, cacheped)
    elseif Config.Clothing == 'crm-appearance' then
        exports['crm-appearance']:crm_set_ped_appearance(cacheped, skinData)
    elseif Config.Clothing == 'rcore_clothing' then
        exports.rcore_clothing:setPedSkin(cacheped, skinData)
    elseif Config.Clothing == '0r-appearance' then
        TriggerEvent('qb-clothing:client:loadPlayerClothing', skinData, cacheped)
    elseif Config.Clothing == 'custom' then
        -- Example: exports['myclothing']:setPedAppearance(cacheped, skinData)
    end
end

Config.CreateCharacterExports = function()
    if Config.Clothing == "illenium-appearance" then
        TriggerEvent('qb-clothes:client:CreateFirstCharacter')
    elseif Config.Clothing == "fivem-appearance" then
        local config = {
            ped = true,
            headBlend = true,
            faceFeatures = true,
            headOverlays = true,
            components = true,
            props = true,
            allowExit = true,
            tattoos = true
        }
        
        exports['fivem-appearance']:startPlayerCustomization(function (appearance)
            TriggerServerEvent('0r-multicharacterv2:server:changePlayerBucket', false)
        end, config)
    elseif Config.Clothing == "crm-appearance" then
        TriggerEvent('crm-appearance:init-new-character', 'crm-male', function() 
            TriggerServerEvent('0r-multicharacterv2:server:changePlayerBucket', false)
        end) 
    elseif Config.Clothing == "bl_appearance" then
        TriggerEvent('qb-clothes:client:CreateFirstCharacter')
    elseif Config.Clothing == "qb-clothing" then
        TriggerEvent('qb-clothes:client:CreateFirstCharacter')
    elseif Config.Clothing == "rcore_clothing" then
        TriggerEvent('qb-clothes:client:CreateFirstCharacter')
    elseif Config.Clothing == '0r-appearance' then
        TriggerEvent('qb-clothes:client:CreateFirstCharacter')
    elseif Config.Clothing == "custom" then
        -- TriggerEvent('qb-clothes:client:CreateFirstCharacter')
    end
end

--  ██████╗██╗   ██╗███████╗████████╗ ██████╗ ███╗   ███╗    ██╗  ██╗██╗   ██╗██████╗ 
-- ██╔════╝██║   ██║██╔════╝╚══██╔══╝██╔═══██╗████╗ ████║    ██║  ██║██║   ██║██╔══██╗
-- ██║     ██║   ██║███████╗   ██║   ██║   ██║██╔████╔██║    ███████║██║   ██║██║  ██║
-- ██║     ██║   ██║╚════██║   ██║   ██║   ██║██║╚██╔╝██║    ██╔══██║██║   ██║██║  ██║
-- ╚██████╗╚██████╔╝███████║   ██║   ╚██████╔╝██║ ╚═╝ ██║    ██║  ██║╚██████╔╝██████╔╝
--  ╚═════╝ ╚═════╝ ╚══════╝   ╚═╝    ╚═════╝ ╚═╝     ╚═╝    ╚═╝  ╚═╝ ╚═════╝ ╚═════╝ 

Config.CustomHud = function(bool)
    if bool then
        -- Example: exports['myhud']:SetDisplay(false)
    else
        -- Example: exports['myhud']:SetDisplay(true)
    end
end

-- ███████╗████████╗ █████╗ ██████╗ ████████╗███████╗██████╗     ██╗████████╗███████╗███╗   ███╗███████╗
-- ██╔════╝╚══██╔══╝██╔══██╗██╔══██╗╚══██╔══╝██╔════╝██╔══██╗    ██║╚══██╔══╝██╔════╝████╗ ████║██╔════╝
-- ███████╗   ██║   ███████║██████╔╝   ██║   █████╗  ██████╔╝    ██║   ██║   █████╗  ██╔████╔██║███████╗
-- ╚════██║   ██║   ██╔══██║██╔══██╗   ██║   ██╔══╝  ██╔══██╗    ██║   ██║   ██╔══╝  ██║╚██╔╝██║╚════██║
-- ███████║   ██║   ██║  ██║██║  ██║   ██║   ███████╗██║  ██║    ██║   ██║   ███████╗██║ ╚═╝ ██║███████║
-- ╚══════╝   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝   ╚══════╝╚═╝  ╚═╝    ╚═╝   ╚═╝   ╚══════╝╚═╝     ╚═╝╚══════╝

--[[
    You can specify the items to be given to new players joining the server here
]]

Config.StarterItems = {
    { item = 'phone',          amount = 1 },
    { item = 'id_card',        amount = 1, customExport = false },
    { item = 'driver_license', amount = 1, customExport = false },
}

-- ██████╗  ██████╗ ██████╗ 
-- ██╔══██╗██╔═══██╗██╔══██╗
-- ██║  ██║██║   ██║██████╔╝
-- ██║  ██║██║   ██║██╔══██╗
-- ██████╔╝╚██████╔╝██████╔╝
-- ╚═════╝  ╚═════╝ ╚═════╝ 

Config.Dob = {}
Config.Dob.Lowest = 2006
Config.Dob.Highest = 1900
Config.Dob.Message = 'You must be at least 18 years old to play on this server.'

-- ███████╗██████╗  █████╗ ██╗    ██╗███╗   ██╗    ███████╗███████╗██╗     ███████╗ ██████╗████████╗ ██████╗ ██████╗ 
-- ██╔════╝██╔══██╗██╔══██╗██║    ██║████╗  ██║    ██╔════╝██╔════╝██║     ██╔════╝██╔════╝╚══██╔══╝██╔═══██╗██╔══██╗
-- ███████╗██████╔╝███████║██║ █╗ ██║██╔██╗ ██║    ███████╗█████╗  ██║     █████╗  ██║        ██║   ██║   ██║██████╔╝
-- ╚════██║██╔═══╝ ██╔══██║██║███╗██║██║╚██╗██║    ╚════██║██╔══╝  ██║     ██╔══╝  ██║        ██║   ██║   ██║██╔══██╗
-- ███████║██║     ██║  ██║╚███╔███╔╝██║ ╚████║    ███████║███████╗███████╗███████╗╚██████╗   ██║   ╚██████╔╝██║  ██║
-- ╚══════╝╚═╝     ╚═╝  ╚═╝ ╚══╝╚══╝ ╚═╝  ╚═══╝    ╚══════╝╚══════╝╚══════╝╚══════╝ ╚═════╝   ╚═╝    ╚═════╝ ╚═╝  ╚═╝

Config.SpawnSelector = {}

Config.SpawnSelector.Use0Resmon = true
Config.SpawnSelector.Locations = {
    {
        id = 'mission-row-police-department',
        icon = 'fa-solid fa-shield-halved',
        name = 'Mission Row',
        subname = 'Police Department',
        coords = vector4(427.85, -979.66, 30.71, 93.31),
    },
    {
        id = 'del-perro-pier',
        icon = 'fa-solid fa-martini-glass',
        name = 'Del Perro',
        subname = 'Pier',
        coords = vector4(-1746.69, -1112.4, 13.02, 229.54),
    },
    {
        id = 'vespucci-beach',
        icon = 'fa-solid fa-umbrella-beach',
        name = 'Vespucci',
        subname = 'Beach',
        coords = vector4(-1217.84, -1534.73, 4.35, 116.36),
    },
    {
        id = 'paleto-bay',
        icon = 'fa-solid fa-tree',
        name = 'Paleto',
        subname = 'Bay',
        coords = vector4(150.91, 6577.92, 31.85, 221.89),
    },
    {
        id = 'sandy-shores',
        icon = 'fa-solid fa-umbrella-beach',
        name = 'Sandy',
        subname = 'Shores',
        coords = vector4(1848.37, 3670.41, 33.69, 208.57),
    },
    {
        id = 'los-santos-international-airport',
        icon = 'fa-solid fa-plane',
        name = 'Los Santos',
        subname = 'International Airport',
        coords = vector4(-1037.59, -2737.66, 20.17, 329.67),
    },
    {
        id = 'mirror-park',
        icon = 'fa-solid fa-tree',
        name = 'Mirror',
        subname = 'Park',
        coords = vector4(1128.55, -646.51, 56.83, 278.87),
    },
}
Config.SpawnSelector.Skip = false
Config.SpawnSelector.OpenUI = function(source, lastLocation)
    TriggerClientEvent('qb-spawn:client:openUI', source, true)
end

--  █████╗ ██████╗  █████╗ ██████╗ ████████╗███╗   ███╗███████╗███╗   ██╗████████╗    ███████╗████████╗ █████╗ ██████╗ ████████╗
-- ██╔══██╗██╔══██╗██╔══██╗██╔══██╗╚══██╔══╝████╗ ████║██╔════╝████╗  ██║╚══██╔══╝    ██╔════╝╚══██╔══╝██╔══██╗██╔══██╗╚══██╔══╝
-- ███████║██████╔╝███████║██████╔╝   ██║   ██╔████╔██║█████╗  ██╔██╗ ██║   ██║       ███████╗   ██║   ███████║██████╔╝   ██║   
-- ██╔══██║██╔═══╝ ██╔══██║██╔══██╗   ██║   ██║╚██╔╝██║██╔══╝  ██║╚██╗██║   ██║       ╚════██║   ██║   ██╔══██║██╔══██╗   ██║   
-- ██║  ██║██║     ██║  ██║██║  ██║   ██║   ██║ ╚═╝ ██║███████╗██║ ╚████║   ██║       ███████║   ██║   ██║  ██║██║  ██║   ██║   
-- ╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝   ╚═╝     ╚═╝╚══════╝╚═╝  ╚═══╝   ╚═╝       ╚══════╝   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝   

Config.ApartmentStart = false

-- ██████╗ ███████╗██╗     ███████╗████████╗███████╗    ██████╗ ██╗   ██╗████████╗████████╗ ██████╗ ███╗   ██╗
-- ██╔══██╗██╔════╝██║     ██╔════╝╚══██╔══╝██╔════╝    ██╔══██╗██║   ██║╚══██╔══╝╚══██╔══╝██╔═══██╗████╗  ██║
-- ██║  ██║█████╗  ██║     █████╗     ██║   █████╗      ██████╔╝██║   ██║   ██║      ██║   ██║   ██║██╔██╗ ██║
-- ██║  ██║██╔══╝  ██║     ██╔══╝     ██║   ██╔══╝      ██╔══██╗██║   ██║   ██║      ██║   ██║   ██║██║╚██╗██║
-- ██████╔╝███████╗███████╗███████╗   ██║   ███████╗    ██████╔╝╚██████╔╝   ██║      ██║   ╚██████╔╝██║ ╚████║
-- ╚═════╝ ╚══════╝╚══════╝╚══════╝   ╚═╝   ╚══════╝    ╚═════╝  ╚═════╝    ╚═╝      ╚═╝    ╚═════╝ ╚═╝  ╚═══╝

Config.DeleteButton = true

-- ██████╗  █████╗  ██████╗██╗  ██╗ ██████╗ ██████╗  ██████╗ ██╗   ██╗███╗   ██╗██████╗     ███╗   ███╗██╗   ██╗███████╗██╗ ██████╗
-- ██╔══██╗██╔══██╗██╔════╝██║ ██╔╝██╔════╝ ██╔══██╗██╔═══██╗██║   ██║████╗  ██║██╔══██╗    ████╗ ████║██║   ██║██╔════╝██║██╔════╝
-- ██████╔╝███████║██║     █████╔╝ ██║  ███╗██████╔╝██║   ██║██║   ██║██╔██╗ ██║██║  ██║    ██╔████╔██║██║   ██║███████╗██║██║     
-- ██╔══██╗██╔══██║██║     ██╔═██╗ ██║   ██║██╔══██╗██║   ██║██║   ██║██║╚██╗██║██║  ██║    ██║╚██╔╝██║██║   ██║╚════██║██║██║     
-- ██████╔╝██║  ██║╚██████╗██║  ██╗╚██████╔╝██║  ██║╚██████╔╝╚██████╔╝██║ ╚████║██████╔╝    ██║ ╚═╝ ██║╚██████╔╝███████║██║╚██████╗
-- ╚═════╝ ╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝ ╚═════╝ ╚═╝  ╚═╝ ╚═════╝  ╚═════╝ ╚═╝  ╚═══╝╚═════╝     ╚═╝     ╚═╝ ╚═════╝ ╚══════╝╚═╝ ╚═════╝

Config.BackgroundMusic = {}
Config.BackgroundMusic.Status = true
Config.BackgroundMusic.Name = 'bgmusic.mp3'
Config.BackgroundMusic.Volume = 0.2

--  ██████╗ ██████╗  ██████╗ ██████╗ ██████╗ ███████╗
-- ██╔════╝██╔═══██╗██╔═══██╗██╔══██╗██╔══██╗██╔════╝
-- ██║     ██║   ██║██║   ██║██████╔╝██║  ██║███████╗
-- ██║     ██║   ██║██║   ██║██╔══██╗██║  ██║╚════██║
-- ╚██████╗╚██████╔╝╚██████╔╝██║  ██║██████╔╝███████║
--  ╚═════╝ ╚═════╝  ╚═════╝ ╚═╝  ╚═╝╚═════╝ ╚══════╝

Config.Coords = {}
Config.Coords.List = {
    [1] = { 
        id = 1, 
        pedCoords = vector4(2934.43, 5325.41, 100.62, 128.05) 
    },
    [2] = { 
        id = 2, 
        pedCoords = vector4(3321.66, 5171.31, 18.42, 98.12) 
    },
    [3] = { 
        id = 3, 
        pedCoords = vector4(2528.89, 4208.06, 40.03, 323.25) 
    },
    [4] = { 
        id = 4, 
        pedCoords = vector4(1965.92, 4636.62, 40.82, 36.75) 
    },
    [5] = { 
        id = 5, 
        pedCoords = vector4(-1588.18, -1126.39, 2.57, 268.58) 
    },
    [6] = { 
        id = 6, 
        pedCoords = vector4(-1683.34, -1129.57, 13.13, 102.49) 
    },
    [7] = { 
        id = 7, 
        pedCoords = vector4(-1631.62, 194.38, 60.63, 111.56) 
    },
    [8] = { 
        id = 8, 
        pedCoords = vector4(-1739.52, -1114.48, 13.07, 91.75) 
    },
    [9] = { 
        id = 9, 
        pedCoords = vector4(-1387.03, 6741.98, 11.98, 67.8) 
    },
    [10] = { 
        id = 10, 
        pedCoords = vector4(-830.52, -102.67, 28.19, 297.36) 
    },
    [11] = { 
        id = 11, 
        pedCoords = vector4(26.96, 200.78, 105.97, 334.61) 
    },
    [12] = { 
        id = 12, 
        pedCoords = vector4(657.27, -1497.52, 10.68, 191.41) 
    },
    [13] = { 
        id = 13, 
        pedCoords = vector4(-74.14, -1455.51, 32.16, 115.75) 
    },
    [14] = { 
        id = 14, 
        pedCoords = vector4(-558.8, -849.07, 27.52, 358.68), 
        scenario = 'PROP_HUMAN_SEAT_CHAIR_DRINK_BEER' 
    },
    [15] = { 
        id = 15, 
        pedCoords = vector4(1978.67, 3830.5, 32.52, 227.04), 
        scenario = 'PROP_HUMAN_SEAT_CHAIR_FOOD' 
    },
    [16] = { 
        id = 16, 
        pedCoords = vector4(-30.43, -1978.88, 4.90, 339.19), 
        scenario = 'WORLD_HUMAN_SMOKING_POT' 
    },
    [17] = { 
        id = 17, 
        pedCoords = vector4(-519.76, 4420.22, 81.41, 271.8), 
        scenario = 'PROP_HUMAN_SEAT_CHAIR_DRINK',
        cameraCoords = vector3(0.0, 1.0, -.4)
    },
    [18] = { 
        id = 18, 
        pedCoords = vector4(148.16, -2201.9, 4.69, 86.71) 
    },
    [19] = {
        id = 19,
        pedCoords = vector4(2967.99, 5321.21, 100.68, 139.24),
        scenario = 'PROP_HUMAN_SEAT_CHAIR_DRINK',
        cameraCoords = vector3(0.3, 2.5, 0.3)
    },
}

-- ███████╗███████╗███████╗███████╗ ██████╗████████╗███████╗
-- ██╔════╝██╔════╝██╔════╝██╔════╝██╔════╝╚══██╔══╝██╔════╝
-- █████╗  █████╗  █████╗  █████╗  ██║        ██║   ███████╗
-- ██╔══╝  ██╔══╝  ██╔══╝  ██╔══╝  ██║        ██║   ╚════██║
-- ███████╗██║     ██║     ███████╗╚██████╗   ██║   ███████║
-- ╚══════╝╚═╝     ╚═╝     ╚══════╝ ╚═════╝   ╚═╝   ╚══════╝

Config.Effects = {}
Config.Effects.List = {
    { pName = 'core',                   pNameChild = 'ent_dst_gen_gobstop' },
    { pName = 'proj_indep_firework_v2', pNameChild = 'scr_firework_indep_repeat_burst_rwb' },
    { pName = 'scr_powerplay',          pNameChild = 'scr_powerplay_beast_vanish' },
    { pName = 'scr_rcbarry2',           pNameChild = 'scr_exp_clown' },
    { pName = 'scr_rcbarry2',           pNameChild = 'scr_clown_bul' },
    { pName = 'scr_xs_celebration',     pNameChild = 'scr_xs_confetti_burst' },
}

--  █████╗ ███╗   ██╗██╗███╗   ███╗ █████╗ ████████╗██╗ ██████╗ ███╗   ██╗
-- ██╔══██╗████╗  ██║██║████╗ ████║██╔══██╗╚══██╔══╝██║██╔═══██╗████╗  ██║
-- ███████║██╔██╗ ██║██║██╔████╔██║███████║   ██║   ██║██║   ██║██╔██╗ ██║
-- ██╔══██║██║╚██╗██║██║██║╚██╔╝██║██╔══██║   ██║   ██║██║   ██║██║╚██╗██║
-- ██║  ██║██║ ╚████║██║██║ ╚═╝ ██║██║  ██║   ██║   ██║╚██████╔╝██║ ╚████║
-- ╚═╝  ╚═╝╚═╝  ╚═══╝╚═╝╚═╝     ╚═╝╚═╝  ╚═╝   ╚═╝   ╚═╝ ╚═════╝ ╚═╝  ╚═══╝

Config.Animation = {}
Config.Animation.List = {
    "uwu",
    "smoke2",
    "foldarms2",
    "bookc",
    "dancedrink2",
    "sipshaked"
}

Config.Animation.Scenario = {}
Config.Animation.Scenario.List = {
    "WORLD_HUMAN_SMOKING",
    "WORLD_HUMAN_SMOKING_POT",
    "WORLD_HUMAN_DRINKING",
    "WORLD_HUMAN_GOLF_PLAYER",
}

Config.Animation.Export = function(emoteName)
    Wait(100)
    local animMenu = Config.AnimationMenu
    if animMenu == 'rpemotes' then
        exports["rpemotes"]:EmoteCommand(emoteName)
    elseif animMenu == 'scully' then
        exports.scully_emotemenu:playEmoteByCommand(emoteName)
    else
        ExecuteCommand(('e %s'):format(emoteName))
    end
end

Config.Animation.Stop = function()
    local animMenu = Config.AnimationMenu
    if animMenu == 'rpemotes' then
        exports["rpemotes"]:EmoteCancel(true)
    elseif animMenu == 'scully' then
        exports.scully_emotemenu:cancelEmote()
    else
        ClearPedTasks(PlayerPedId())
    end
end

Config.Camera = {}
Config.Camera.Filters = {
    'default',
    'ArenaEMP',
    'ArenaEMP_Blend',
    'BeastLaunch01',
    'BeastLaunch02',
    'BikerFormFlash',
    'BikersSPLASH',
    'BombCamFlash',
    'casino_mainfloor',
    'cinema',
    'CrossLine01',
    'CrossLine02',
    'Dont_tazeme_bro',
    'dont_tazeme_bro_b',
    'drug_flying_base',
    'FIB_A',
    'Forest',
    'hud_def_flash',
    'InchOrange01',
    'InchOrange02',
    'LectroDark',
}

-- ██████╗ ███████╗██╗     ███████╗████████╗███████╗    ████████╗ █████╗ ██████╗ ██╗     ███████╗
-- ██╔══██╗██╔════╝██║     ██╔════╝╚══██╔══╝██╔════╝    ╚══██╔══╝██╔══██╗██╔══██╗██║     ██╔════╝
-- ██║  ██║█████╗  ██║     █████╗     ██║   █████╗         ██║   ███████║██████╔╝██║     █████╗  
-- ██║  ██║██╔══╝  ██║     ██╔══╝     ██║   ██╔══╝         ██║   ██╔══██║██╔══██╗██║     ██╔══╝  
-- ██████╔╝███████╗███████╗███████╗   ██║   ███████╗       ██║   ██║  ██║██████╔╝███████╗███████╗
-- ╚═════╝ ╚══════╝╚══════╝╚══════╝   ╚═╝   ╚══════╝       ╚═╝   ╚═╝  ╚═╝╚═════╝ ╚══════╝╚══════╝

--[[
    Sql table and data to be deleted when a player deletes his own character or when officials try to delete a player's character

    properties for qbx-core
]]

Config.DeleteTable = {
    -- {
    --     table = 'properties',
    --     column = 'owner',
    --     type = 'citizenid' -- steam, license, discord, citizenid
    -- },
    {
        table = 'players',
        column = 'citizenid',
        type = 'citizenid' -- steam, license, discord, citizenid
    },
    {
        table = 'apartments',
        column = 'citizenid',
        type = 'citizenid' -- steam, license, discord, citizenid
    },
    {
        table = 'bank_accounts',
        column = 'citizenid',
        type = 'citizenid' -- steam, license, discord, citizenid
    },
    --{
      --  table = 'crypto_transactions',
       -- column = 'citizenid',
       -- type = 'citizenid' -- steam, license, discord, citizenid
    --},
    {
        table = 'phone_invoices',
        column = 'citizenid',
        type = 'citizenid' -- steam, license, discord, citizenid
    },
    {
        table = 'phone_messages',
        column = 'citizenid',
        type = 'citizenid' -- steam, license, discord, citizenid
    },
    {
        table = 'playerskins',
        column = 'citizenid',
        type = 'citizenid' -- steam, license, discord, citizenid
    },
    {
        table = 'player_contacts',
        column = 'citizenid',
        type = 'citizenid' -- steam, license, discord, citizenid
    },
    {
        table = 'player_houses',
        column = 'citizenid',
        type = 'citizenid' -- steam, license, discord, citizenid
    },
    {
        table = 'player_mails',
        column = 'citizenid',
        type = 'citizenid' -- steam, license, discord, citizenid
    },
    {
        table = 'player_outfits',
        column = 'citizenid',
        type = 'citizenid' -- steam, license, discord, citizenid
    },
    {
        table = 'player_vehicles',
        column = 'citizenid',
        type = 'citizenid' -- steam, license, discord, citizenid
    }
}

-- ███████╗██╗      ██████╗ ████████╗███████╗
-- ██╔════╝██║     ██╔═══██╗╚══██╔══╝██╔════╝
-- ███████╗██║     ██║   ██║   ██║   ███████╗
-- ╚════██║██║     ██║   ██║   ██║   ╚════██║
-- ███████║███████╗╚██████╔╝   ██║   ███████║
-- ╚══════╝╚══════╝ ╚═════╝    ╚═╝   ╚══════╝

Config.Slots = {}
Config.Slots.Package = {
    {
        id = 1,
        totalSlot = 1
    }
}

Config.Slots.List = {
    {
        license = "license", 
        slot = 1
    },
}

Config.Slots.DiscordPerm = {}
Config.Slots.DiscordPerm.Status = false
Config.Slots.DiscordPerm.Role = {
    { 
        id = "roleid",
        slot = 1
    },
}