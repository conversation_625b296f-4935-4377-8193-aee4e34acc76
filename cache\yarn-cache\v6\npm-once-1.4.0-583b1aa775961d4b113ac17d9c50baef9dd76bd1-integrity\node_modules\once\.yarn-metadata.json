{"manifest": {"name": "once", "version": "1.4.0", "description": "Run a function exactly one time", "main": "once.js", "dependencies": {"wrappy": "1"}, "devDependencies": {"tap": "^7.0.1"}, "scripts": {"test": "tap test/*.js"}, "files": ["once.js"], "repository": {"type": "git", "url": "git://github.com/isaacs/once"}, "keywords": ["once", "function", "one", "single"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-once-1.4.0-583b1aa775961d4b113ac17d9c50baef9dd76bd1-integrity\\node_modules\\once\\package.json", "readmeFilename": "README.md", "readme": "# once\n\nOnly call a function once.\n\n## usage\n\n```javascript\nvar once = require('once')\n\nfunction load (file, cb) {\n  cb = once(cb)\n  loader.load('file')\n  loader.once('load', cb)\n  loader.once('error', cb)\n}\n```\n\nOr add to the Function.prototype in a responsible way:\n\n```javascript\n// only has to be done once\nrequire('once').proto()\n\nfunction load (file, cb) {\n  cb = cb.once()\n  loader.load('file')\n  loader.once('load', cb)\n  loader.once('error', cb)\n}\n```\n\nIronically, the prototype feature makes this module twice as\ncomplicated as necessary.\n\nTo check whether you function has been called, use `fn.called`. Once the\nfunction is called for the first time the return value of the original\nfunction is saved in `fn.value` and subsequent calls will continue to\nreturn this value.\n\n```javascript\nvar once = require('once')\n\nfunction load (cb) {\n  cb = once(cb)\n  var stream = createStream()\n  stream.once('data', cb)\n  stream.once('end', function () {\n    if (!cb.called) cb(new Error('not found'))\n  })\n}\n```\n\n## `once.strict(func)`\n\nThrow an error if the function is called twice.\n\nSome functions are expected to be called only once. Using `once` for them would\npotentially hide logical errors.\n\nIn the example below, the `greet` function has to call the callback only once:\n\n```javascript\nfunction greet (name, cb) {\n  // return is missing from the if statement\n  // when no name is passed, the callback is called twice\n  if (!name) cb('Hello anonymous')\n  cb('Hello ' + name)\n}\n\nfunction log (msg) {\n  console.log(msg)\n}\n\n// this will print 'Hello anonymous' but the logical error will be missed\ngreet(null, once(msg))\n\n// once.strict will print 'Hello anonymous' and throw an error when the callback will be called the second time\ngreet(null, once.strict(msg))\n```\n", "licenseText": "The ISC License\n\nCopyright (c) <PERSON> and Contributors\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1", "type": "tarball", "reference": "https://registry.yarnpkg.com/once/-/once-1.4.0.tgz", "hash": "583b1aa775961d4b113ac17d9c50baef9dd76bd1", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "registry": "npm", "packageName": "once", "cacheIntegrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w== sha1-WDsap3WWHUsROsF9nFC6753Xa9E="}, "registry": "npm", "hash": "583b1aa775961d4b113ac17d9c50baef9dd76bd1"}