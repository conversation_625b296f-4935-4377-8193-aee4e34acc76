local Translations = {
    error = {
        blips_deactivated = 'Blips desativados',
        names_deactivated = 'Nomes desativados',
        changed_perm_failed = 'Escolhe um grupo!',
        missing_reason = 'Deves informar um motivo!',
        invalid_reason_length_ban = 'Deves informar um motivo e definir um prazo para o ban  !',
        no_store_vehicle_garage = 'Não podes guardar este veículo na tua garagem..',
        no_vehicle = 'Não está num veículo..',
        no_weapon = 'Não tens uma arma nas tuas mãos..',
        no_free_seats = 'O veículo não têm assentos livres!',
        failed_vehicle_owner = 'Este veículo já é teu..',
        not_online = 'Este jogador não está online',
        no_receive_report = 'Não está a receber relatórios',
        failed_set_speed = 'Não definiste uma velocidade.. (`fast` para super corrida, `normal` para normal)',
        failed_set_model = 'Não definiste um modelo..',
        failed_entity_copy = 'Nenhuma informação de entidade de mira livre para copiar para a área de transferência!',
    },
    success = {
        blips_activated = 'Blips ativados',
        names_activated = 'Nomes ativados',
        coords_copied = 'Coordenadas copiadas para a área de transferência!',
        heading_copied = 'Direção copiada para a área de transferência!',
        changed_perm = 'Grupo de autoridade alterado',
        entered_vehicle = 'Entraste no veículo',
        success_vehicle_owner = 'O veículo agora é teu!',
        receive_reports = 'Estás a receber relatórios',
        entity_copy = 'Informações da entidade de mira livre copiadas para a área de transferência!',
        spawn_weapon = 'Geraste uma arma',
        noclip_enabled = 'No-clip ativado',
        noclip_disabled = 'No-clip desativado',
    },
    info = {
        ped_coords            = 'Coordenadas Ped:',
        vehicle_dev_data      = 'Dados do desenvolvedor do veículo:',
        ent_id                = 'ID de Entidade:',
        net_id                = 'ID de Rede:',
        net_id_not_registered = 'Não registrado',
        model                 = 'Modelo',
        hash                  = 'Hash',
        eng_health            = 'Estado do motor:',
        body_health           = 'Estado do chassi:',
        go_to                 = 'Ir para',
        remove                = 'Remover',
        confirm               = 'Confirmar',
        reason_title          = 'Motivo',
        length                = 'Comprimento',
        options               = 'Opções',
        position              = 'Posição',
        your_position         = 'Para sua posição',
        open                  = 'Abrir',
        inventories           = 'Inventários',
        reason                = 'Motivo',
        give                  = 'Entregar',
        id                    = 'ID:',
        player_name           = 'Nome do Jogador',
        obj                   = 'Obj',
        ammoforthe            = '+%{valor} Munição para %{arma}',
        kicked_server         = 'Foste expulso do servidor',
        check_discord         = '🔸 Aparece no nosso Discord para mais informações: ',
        banned                = 'Foste banido:',
        ban_perm              = '\n\nO teu ban é permanente.\n🔸 Entra no nosso Discord para mais informações: ',
        ban_expires           = '\n\nO ban expira em: ',
        rank_level            = 'Seu nível de permissão é agora ',
        admin_report          = 'Relatório de administração - ',
        staffchat             = 'STAFFCHAT - ',
        warning_chat_message  = '^8WARNING ^7 Foi avisado por',
        warning_staff_message = '^8WARNING ^7 Avisou ',
        no_reason_specified   = 'Nenhum motivo especificado',
        server_restart        = 'Reinício do servidor, verifica o nosso Discord para mais informações: ',
        entity_view_distance  = 'Distância de visualização da entidade definida como: %{distance} metros',
        entity_view_info      = 'Informações da entidade',
        entity_view_title     = 'Modo de mira livre de entidade',
        entity_freeaim_delete = 'Excluir entidade',
        entity_freeaim_freeze = 'Congelar Entidade',
        entity_frozen         = 'Congelado',
        entity_unfrozen       = 'Descongelado',
        model_hash            = 'Modelo de hash:',
        obj_name              = 'Nome do objeto:',
        ent_owner             = 'Proprietário da entidade:',
        cur_health            = 'Estado atual:',
        max_health            = 'Percentual máximo:',
        armour                = 'Armadura:',
        rel_group             = 'Grupo de Relacionamento:',
        rel_to_player         = 'Relação com o jogador:',
        rel_group_custom      = 'Relacionamento personalizado',
        veh_acceleration      = 'Aceleração:',
        veh_cur_gear          = 'Marcha atual:',
        veh_speed_kph         = 'km/h:',
        veh_speed_mph         = 'mp/h:',
        veh_rpm               = 'Rpm:',
        dist_to_obj           = 'Dist:',
        obj_heading           = 'Direção:',
        obj_coords            = 'Coordenadas:',
        obj_rot               = 'Rotação:',
        obj_velocity          = 'Velocidade:',
        obj_unknown           = 'Desconhecido',
        you_have              = 'Tens ',
        freeaim_entity        = 'A entidade de mira livre',
        entity_del            = 'Entidade excluída',
        entity_del_error      = 'Erro ao excluir entidade',
    },
    menu = {
        admin_menu = 'Menu de Admin',
        admin_options = 'Opções do Admin',
        online_players = 'Jogadores online',
        manage_server = 'Gerir servidor',
        weather_conditions = 'Opções de clima disponíveis',
        dealer_list = 'Lista de vendedores',
        ban = 'Banir',
        kick = 'Expulsar',
        permissions = 'Permissões',
        developer_options = 'Opções de desenvolvedor',
        vehicle_options = 'Opções de veículo',
        vehicle_categories = 'Categorias de veículos',
        vehicle_models = 'Modelos de veículos',
        player_management = 'Gestão de jogadores',
        server_management = 'Gestão do Servidor',
        vehicles = 'Veículos',
        noclip = 'No-clip',
        revive = 'Reviver',
        invisible = 'Invisível',
        god = 'Modo Deus',
        names = 'Nomes',
        blips = 'Blips',
        weather_options = 'Opções de clima',
        server_time = 'Horário do servidor',
        time = 'Tempo',
        copy_vector3 = 'Copiar vector3',
        copy_vector4 = 'Copiar vector4',
        display_coords = 'Exibir Coordenadas',
        copy_heading = 'Copiar direção',
        vehicle_dev_mode = 'Modo de desenvolvimento do veículo',
        spawn_vehicle = 'Criar Veículo',
        fix_vehicle = 'Consertar Veículo',
        buy = 'Comprar',
        remove_vehicle = 'Remover veículo',
        edit_dealer = 'Editar vendedor ',
        dealer_name = 'Nome do vendedor',
        category_name = 'Nome da Categoria',
        kill = 'Matar',
        freeze = 'Congelar',
        spectate = 'Olhar',
        bring = 'Trazer',
        sit_in_vehicle = 'Senta-te no veículo',
        open_inv = 'Inventário aberto',
        give_clothing_menu = 'Menu de Roupas',
        hud_dev_mode = 'Modo de desenvolvimento (qb-hud)',
        entity_view_options = 'Modo de visualização de entidade',
        entity_view_distance = 'Definir distância de visualização',
        entity_view_freeaim = 'Modo de mira livre',
        entity_view_peds = 'Exibir Peds',
        entity_view_vehicles = 'Exibir Veículos',
        entity_view_objects = 'Exibir Objetos',
        entity_view_freeaim_copy = 'Copiar Informações da Entidade em Mira Livre',
        spawn_weapons = 'Criar armas',
        max_mods = 'Mods máximos do carro',
    },
    desc = {
        admin_options_desc = 'Opções diversas de admin',
        player_management_desc = 'Ver lista de jogadores',
        server_management_desc = 'Opções diversas do servidor',
        vehicles_desc = 'Opções de veículos',
        dealer_desc = 'Lista de vendedores existentes',
        noclip_desc = 'Habilitar/Desabilitar NoClip',
        revive_desc = 'Reviver-se',
        invisible_desc = 'Ativar/Desativar Invisibilidade',
        god_desc = 'Ativar/Desativar Modo Deus',
        names_desc = 'Ativar/desativar nomes sobre a cabeça',
        blips_desc = 'Ativar/Desativar Blips para jogadores em mapas',
        weather_desc = 'Muda o clima',
        developer_desc = 'Opções diversas de desenvolvedor',
        vector3_desc = 'Copiar vector3 para a área de transferência',
        vector4_desc = 'Copiar vector4 para a área de transferência',
        display_coords_desc = 'Mostrar coordenadas na tela',
        copy_heading_desc = 'Copiar direção para a área de transferência',
        vehicle_dev_mode_desc = 'Exibir informações do veículo',
        delete_laser_desc = 'Habilitar/Desabilitar Laser',
        spawn_vehicle_desc = 'Criar um veículo',
        fix_vehicle_desc = 'Conserte o veículo em que você está',
        buy_desc = 'Compre o veículo gratuitamente',
        remove_vehicle_desc = 'Remover o veículo mais próximo',
        dealergoto_desc = 'Ir para vendedor',
        dealerremove_desc = 'Remover vendedor',
        kick_reason = 'Razão da expulsão',
        confirm_kick = 'Confirmar expulsão',
        ban_reason = 'Motivo do ban',
        confirm_ban = 'Confirmar ban',
        sit_in_veh_desc = 'Sente-te',
        sit_in_veh_desc2 = '(proprietário do veículo)',
        clothing_menu_desc = 'Dê o menu de Roupa para',
        hud_dev_mode_desc = 'Ativar/desativar o modo de desenvolvedor',
        entity_view_desc = 'Ver informações sobre entidades',
        entity_view_freeaim_desc = 'Ativar/desativar informações da entidade via mira livre',
        entity_view_peds_desc = 'Ativar/Desativar informações de ped no mundo',
        entity_view_vehicles_desc = 'Ativar/desativar informações do veículo no mundo',
        entity_view_objects_desc = 'Ativar/desativar informações do objeto no mundo',
        entity_view_freeaim_copy_desc = 'Copia as informações da entidade em mira livre para a área de transferência',
        spawn_weapons_desc = 'Criar qualquer arma.',
        max_mod_desc = 'Mods máximos em seu veículo atual',
    },
    time = {
        ban_length = 'Duração do banimento',
        onehour = '1 hora',
        sixhour = '6 horas',
        twelvehour = '12 horas',
        oneday = '1 Dia',
        threeday = '3 Dias',
        oneweek = '1 Semana',
        onemonth = '1 Mês',
        threemonth = '3 Mêses',
        sixmonth = '6 Mêses',
        oneyear = '1 Ano',
        permanent = 'Permanente',
        self = 'Auto',
        changed = 'A hora mudou para %{time} hr 00 min',
    },
    weather = {
        extra_sunny = 'Extra ensolarado',
        extra_sunny_desc = 'Estou derretendo!',
        clear = 'Limpo',
        clear_desc = 'O dia perfeito!',
        neutral = 'Neutro',
        neutral_desc = 'Apenas um dia qualquer!',
        smog = 'Nevoeiro',
        smog_desc = 'Máquina de fumaça!',
        foggy = 'Neboluso',
        foggy_desc = 'Máquina de fumaça x2!',
        overcast = 'Nublado',
        overcast_desc = 'Não muito ensolarado!',
        clouds = 'Nuvens',
        clouds_desc = 'Cadê o sol?',
        clearing = 'Parcialmente nublado',
        clearing_desc = 'Nuvens começam a sair!',
        rain = 'Chuva',
        rain_desc = 'Faça chover!',
        thunder = 'Trovão',
        thunder_desc = 'Corra e se esconda!',
        snow = 'Neve',
        snow_desc = 'Está frio aqui fora?',
        blizzard = 'Nevasca',
        blizzed_desc = 'Máquina de neve?',
        light_snow = 'Pouca neve',
        light_snow_desc = 'Começando a sentir como o Natal!',
        heavy_snow = 'Neve pesada (NATAL)',
        heavy_snow_desc = 'Guerra de bolas de neve!',
        halloween = 'Halloween',
        halloween_desc = 'O que foi aquele barulho?!',
        weather_changed = 'Clima alterado para: %{value}',
    },
    commands = {
        blips_for_player = 'Mostrar blips para jogadores (somente administrador)',
        player_name_overhead = 'Mostrar nome do jogador na cabeça (somente administrador)',
        coords_dev_command = 'Ativar exibição de coordenadas para material de desenvolvimento (somente administrador)',
        toogle_noclip = 'Alternar noclip (somente administrador)',
        save_vehicle_garage = 'Salvar veículo em sua garagem (somente administrador)',
        make_announcement = 'Faça um anúncio (somente administrador)',
        open_admin = 'Abrir menu de administração (somente administrador)',
        staffchat_message = 'Envie uma mensagem para toda a Staff (somente administrador)',
        nui_focus = 'Dá NUI Focus a um jogador (somente administrador)',
        warn_a_player = 'Avisar um jogador (somente administrador)',
        check_player_warning = 'Verifica os avisos do jogador (somente administrador)',
        delete_player_warning = 'Excluir avisos de jogadores (somente administrador)',
        reply_to_report = 'Responder a um relatório (somente administrador)',
        change_ped_model = 'Alterar modelo de Ped (somente administrador)',
        set_player_foot_speed = 'Definir velocidade a pé do jogador (somente administrador)',
        report_toggle = 'Alterar relatórios de entrada (somente administrador)',
        kick_all = 'Expulsar todos os jogadores',
        ammo_amount_set = 'Define a tua quantidade de munição (somente administrador)',
    }
}

if GetConvar('qb_locale', 'en') == 'pt' then
    Lang = Locale:new({
        phrases = Translations,
        warnOnMissing = true,
        fallbackLang = Lang,
    })
end
