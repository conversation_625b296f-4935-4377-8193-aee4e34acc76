{"manifest": {"name": "ylru", "description": "Extends LRU base on hashlru", "version": "1.2.1", "homepage": "https://github.com/node-modules/ylru", "repository": {"type": "git", "url": "git://github.com/node-modules/ylru.git"}, "dependencies": {}, "devDependencies": {"beautify-benchmark": "^0.2.4", "benchmark": "^2.1.3", "egg-bin": "^1.10.0", "eslint": "^3.12.2", "eslint-config-egg": "^3.2.0", "hashlru": "^1.0.3", "ko-sleep": "^1.0.2", "lru-cache": "^4.0.2"}, "main": "index.js", "files": ["index.js"], "scripts": {"lint": "eslint test *.js", "test": "npm run lint -- --fix && npm run test-local", "test-local": "egg-bin test", "cov": "egg-bin cov", "ci": "npm run lint && npm run cov", "autod": "autod"}, "author": {"name": "fengmk2"}, "engines": {"node": ">= 4.0.0"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-ylru-1.2.1-f576b63341547989c1de7ba288760923b27fe84f-integrity\\node_modules\\ylru\\package.json", "readmeFilename": "README.md", "readme": "# ylru\n\n[![NPM version][npm-image]][npm-url]\n[![build status][travis-image]][travis-url]\n[![Test coverage][codecov-image]][codecov-url]\n[![<PERSON> de<PERSON>][david-image]][david-url]\n[![Known Vulnerabilities][snyk-image]][snyk-url]\n[![npm download][download-image]][download-url]\n\n[npm-image]: https://img.shields.io/npm/v/ylru.svg?style=flat-square\n[npm-url]: https://npmjs.org/package/ylru\n[travis-image]: https://img.shields.io/travis/node-modules/ylru.svg?style=flat-square\n[travis-url]: https://travis-ci.org/node-modules/ylru\n[codecov-image]: https://img.shields.io/codecov/c/github/node-modules/ylru.svg?style=flat-square\n[codecov-url]: https://codecov.io/github/node-modules/ylru?branch=master\n[david-image]: https://img.shields.io/david/node-modules/ylru.svg?style=flat-square\n[david-url]: https://david-dm.org/node-modules/ylru\n[snyk-image]: https://snyk.io/test/npm/ylru/badge.svg?style=flat-square\n[snyk-url]: https://snyk.io/test/npm/ylru\n[download-image]: https://img.shields.io/npm/dm/ylru.svg?style=flat-square\n[download-url]: https://npmjs.org/package/ylru\n\n**hashlru inspired**\n\n[hashlru](https://github.com/dominictarr/hashlru) is the **Simpler, faster LRU cache algorithm.**\nPlease checkout [algorithm](https://github.com/dominictarr/hashlru#algorithm) and [complexity](https://github.com/dominictarr/hashlru#complexity) on hashlru.\n\nylru extends some features base on hashlru:\n\n- cache value can be **expired**.\n- cache value can be **empty value**, e.g.: `null`, `undefined`, `''`, `0`\n\n## Usage\n\n```js\nconst LRU = require('ylru');\n\nconst lru = new LRU(100);\nlru.set(key, value);\nlru.get(key);\n\n// value2 will be expired after 5000ms\nlru.set(key2, value2, { maxAge: 5000 });\n// get key and update expired\nlru.get(key2, { maxAge: 5000 });\n```\n\n### API\n\n## LRU(max) => lru\n\ninitialize a lru object.\n\n### lru.get(key[, options]) => value | null\n\n- `{Number} options.maxAge`: update expire time when get, value will become `undefined` after `maxAge` pass.\n\nReturns the value in the cache.\n\n### lru.set(key, value[, options])\n\n- `{Number} options.maxAge`: value will become `undefined` after `maxAge` pass.\nIf `maxAge` not set, value will be never expired.\n\nSet the value for key.\n\n### lru.keys()\n\nGet all unexpired cache keys from lru, due to the strategy of ylru, the `keys`' length may greater than `max`.\n\n```js\nconst lru = new LRU(3);\nlru.set('key 1', 'value 1');\nlru.set('key 2', 'value 2');\nlru.set('key 3', 'value 3');\nlru.set('key 4', 'value 4');\n\nlru.keys(); // [ 'key 4', 'key 1', 'key 2', 'key 3']\n// cache: {\n//   'key 4': 'value 4',\n// }\n// _cache: {\n//   'key 1': 'value 1',\n//   'key 2': 'value 2',\n//   'key 3': 'value 3',\n// }\n```\n\n## License\n\n[MIT](LICENSE)\n", "licenseText": "Copyright (c) 2016 node-modules\nCopyright (c) 2016 '<PERSON>'\n\nPermission is hereby granted, free of charge,\nto any person obtaining a copy of this software and\nassociated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify,\nmerge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom\nthe Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice\nshall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR\nANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/ylru/-/ylru-1.2.1.tgz#f576b63341547989c1de7ba288760923b27fe84f", "type": "tarball", "reference": "https://registry.yarnpkg.com/ylru/-/ylru-1.2.1.tgz", "hash": "f576b63341547989c1de7ba288760923b27fe84f", "integrity": "sha512-faQrqNMzcPCHGVC2aaOINk13K+aaBDUPjGWl0teOXywElLjyVAB6Oe2jj62jHYtwsU49jXhScYbvPENK+6zAvQ==", "registry": "npm", "packageName": "ylru", "cacheIntegrity": "sha512-faQrqNMzcPCHGVC2aaOINk13K+aaBDUPjGWl0teOXywElLjyVAB6Oe2jj62jHYtwsU49jXhScYbvPENK+6zAvQ== sha1-9Xa2M0FUeYnB3nuiiHYJI7J/6E8="}, "registry": "npm", "hash": "f576b63341547989c1de7ba288760923b27fe84f"}