local QBCore = exports['qb-core']:GetCoreObject()

-- Callback pro získání fotek z galerie
QBCore.Functions.CreateCallback('qb-smartphone2:server:getGallery', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb({}) return end
    
    local citizenid = Player.PlayerData.citizenid
    
    MySQL.Async.fetchAll('SELECT * FROM phone_gallery WHERE citizenid = ? ORDER BY created_at DESC LIMIT ?', {
        citizenid, Config.MaxPhotos
    }, function(result)
        cb(result or {})
    end)
end)

-- Event pro uložení fotky
RegisterNetEvent('qb-smartphone2:server:savePhoto', function(photoData)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Kontrola limitu fotek
    MySQL.Async.fetchAll('SELECT COUNT(*) as count FROM phone_gallery WHERE citizenid = ?', {citizenid}, function(result)
        local photoCount = result[1].count
        
        if photoCount >= Config.MaxPhotos then
            -- <PERSON><PERSON><PERSON> nejstarší fotku
            MySQL.Async.execute('DELETE FROM phone_gallery WHERE citizenid = ? ORDER BY created_at ASC LIMIT 1', {citizenid})
        end
        
        -- Uložení nové fotky
        MySQL.Async.execute('INSERT INTO phone_gallery (citizenid, image, description, location) VALUES (?, ?, ?, ?)', {
            citizenid,
            photoData.image,
            photoData.description or '',
            photoData.location or ''
        }, function(insertId)
            if insertId then
                TriggerClientEvent('QBCore:Notify', src, 'Fotka byla uložena do galerie!', 'success')
                TriggerClientEvent('qb-smartphone2:client:photoSaved', src, {
                    id = insertId,
                    image = photoData.image,
                    description = photoData.description,
                    location = photoData.location,
                    created_at = os.date('%Y-%m-%d %H:%M:%S')
                })
            else
                TriggerClientEvent('QBCore:Notify', src, 'Chyba při ukládání fotky!', 'error')
            end
        end)
    end)
end)

-- Event pro smazání fotky
RegisterNetEvent('qb-smartphone2:server:deletePhoto', function(photoId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Kontrola vlastnictví fotky
    MySQL.Async.fetchAll('SELECT * FROM phone_gallery WHERE id = ? AND citizenid = ?', {photoId, citizenid}, function(result)
        if not result[1] then
            TriggerClientEvent('QBCore:Notify', src, 'Fotka nenalezena!', 'error')
            return
        end
        
        -- Smazání fotky
        MySQL.Async.execute('DELETE FROM phone_gallery WHERE id = ? AND citizenid = ?', {photoId, citizenid}, function(affectedRows)
            if affectedRows > 0 then
                TriggerClientEvent('QBCore:Notify', src, 'Fotka byla smazána!', 'success')
                TriggerClientEvent('qb-smartphone2:client:photoDeleted', src, photoId)
            else
                TriggerClientEvent('QBCore:Notify', src, 'Chyba při mazání fotky!', 'error')
            end
        end)
    end)
end)

-- Event pro sdílení fotky
RegisterNetEvent('qb-smartphone2:server:sharePhoto', function(photoId, shareData)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Získání fotky
    MySQL.Async.fetchAll('SELECT * FROM phone_gallery WHERE id = ? AND citizenid = ?', {photoId, citizenid}, function(result)
        if not result[1] then
            TriggerClientEvent('QBCore:Notify', src, 'Fotka nenalezena!', 'error')
            return
        end
        
        local photo = result[1]
        
        if shareData.type == 'sms' then
            -- Sdílení přes SMS
            local senderNumber = Player.PlayerData.charinfo.phone
            
            TriggerEvent('qb-smartphone2:server:sendMessage', {
                receiver = shareData.phoneNumber,
                message = shareData.message or 'Sdílená fotka',
                attachments = {
                    {
                        type = 'image',
                        url = photo.image,
                        description = photo.description
                    }
                }
            })
            
            TriggerClientEvent('QBCore:Notify', src, 'Fotka byla sdílena!', 'success')
            
        elseif shareData.type == 'social' then
            -- Sdílení na sociální síť
            TriggerEvent('qb-smartphone2:server:createSocialPost', {
                content = shareData.message or photo.description or 'Sdílená fotka',
                image = photo.image
            })
            
            TriggerClientEvent('QBCore:Notify', src, 'Fotka byla sdílena na sociální síť!', 'success')
        end
    end)
end)

-- Event pro úpravu popisku fotky
RegisterNetEvent('qb-smartphone2:server:editPhotoDescription', function(photoId, newDescription)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Kontrola vlastnictví fotky
    MySQL.Async.fetchAll('SELECT * FROM phone_gallery WHERE id = ? AND citizenid = ?', {photoId, citizenid}, function(result)
        if not result[1] then
            TriggerClientEvent('QBCore:Notify', src, 'Fotka nenalezena!', 'error')
            return
        end
        
        -- Aktualizace popisku
        MySQL.Async.execute('UPDATE phone_gallery SET description = ? WHERE id = ? AND citizenid = ?', {
            newDescription, photoId, citizenid
        }, function(affectedRows)
            if affectedRows > 0 then
                TriggerClientEvent('QBCore:Notify', src, 'Popisek byl upraven!', 'success')
                TriggerClientEvent('qb-smartphone2:client:photoDescriptionUpdated', src, photoId, newDescription)
            else
                TriggerClientEvent('QBCore:Notify', src, 'Chyba při úpravě popisku!', 'error')
            end
        end)
    end)
end)

-- Event pro hromadné smazání fotek
RegisterNetEvent('qb-smartphone2:server:bulkDeletePhotos', function(photoIds)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    if #photoIds == 0 then
        TriggerClientEvent('QBCore:Notify', src, 'Žádné fotky k smazání!', 'error')
        return
    end
    
    local placeholders = string.rep('?,', #photoIds):sub(1, -2)
    local query = string.format('DELETE FROM phone_gallery WHERE id IN (%s) AND citizenid = ?', placeholders)
    
    local params = {}
    for _, id in ipairs(photoIds) do
        table.insert(params, id)
    end
    table.insert(params, citizenid)
    
    MySQL.Async.execute(query, params, function(affectedRows)
        if affectedRows > 0 then
            TriggerClientEvent('QBCore:Notify', src, 'Smazáno ' .. affectedRows .. ' fotek!', 'success')
            TriggerClientEvent('qb-smartphone2:client:photosDeleted', src, photoIds)
        else
            TriggerClientEvent('QBCore:Notify', src, 'Chyba při mazání fotek!', 'error')
        end
    end)
end)

-- Callback pro vyhledání fotek
QBCore.Functions.CreateCallback('qb-smartphone2:server:searchPhotos', function(source, cb, searchTerm)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb({}) return end
    
    local citizenid = Player.PlayerData.citizenid
    
    MySQL.Async.fetchAll([[
        SELECT * FROM phone_gallery 
        WHERE citizenid = ? AND (description LIKE ? OR location LIKE ?) 
        ORDER BY created_at DESC 
        LIMIT 50
    ]], {citizenid, '%' .. searchTerm .. '%', '%' .. searchTerm .. '%'}, function(result)
        cb(result or {})
    end)
end)

-- Callback pro získání statistik galerie
QBCore.Functions.CreateCallback('qb-smartphone2:server:getGalleryStats', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb({}) return end
    
    local citizenid = Player.PlayerData.citizenid
    
    MySQL.Async.fetchAll([[
        SELECT 
            COUNT(*) as total_photos,
            COUNT(CASE WHEN description != '' THEN 1 END) as photos_with_description,
            COUNT(CASE WHEN location != '' THEN 1 END) as photos_with_location,
            MIN(created_at) as oldest_photo,
            MAX(created_at) as newest_photo
        FROM phone_gallery 
        WHERE citizenid = ?
    ]], {citizenid}, function(result)
        cb(result[1] or {})
    end)
end)

-- Export funkce pro jiné scripty
exports('SavePhoto', function(source, photoData)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return false end
    
    local citizenid = Player.PlayerData.citizenid
    local p = promise.new()
    
    MySQL.Async.execute('INSERT INTO phone_gallery (citizenid, image, description, location) VALUES (?, ?, ?, ?)', {
        citizenid,
        photoData.image,
        photoData.description or '',
        photoData.location or ''
    }, function(insertId)
        p:resolve(insertId ~= nil)
    end)
    
    return Citizen.Await(p)
end)

exports('GetPlayerGallery', function(source)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return {} end
    
    local citizenid = Player.PlayerData.citizenid
    local p = promise.new()
    
    MySQL.Async.fetchAll('SELECT * FROM phone_gallery WHERE citizenid = ? ORDER BY created_at DESC', {citizenid}, function(result)
        p:resolve(result or {})
    end)
    
    return Citizen.Await(p)
end)

exports('DeletePhoto', function(source, photoId)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return false end
    
    local citizenid = Player.PlayerData.citizenid
    local p = promise.new()
    
    MySQL.Async.execute('DELETE FROM phone_gallery WHERE id = ? AND citizenid = ?', {photoId, citizenid}, function(affectedRows)
        p:resolve(affectedRows > 0)
    end)
    
    return Citizen.Await(p)
end)
