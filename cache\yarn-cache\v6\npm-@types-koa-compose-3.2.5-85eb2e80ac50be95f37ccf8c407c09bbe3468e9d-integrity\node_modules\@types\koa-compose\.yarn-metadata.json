{"manifest": {"name": "@types/koa-compose", "version": "3.2.5", "description": "TypeScript definitions for koa-compose", "license": "MIT", "contributors": [{"name": "j<PERSON><PERSON>", "url": "https://github.com/jkeylu"}, {"name": "<PERSON>", "url": "https://github.com/astashov"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/koa-compose"}, "scripts": {}, "dependencies": {"@types/koa": "*"}, "typesPublisherContentHash": "a5fb98541fe6f08d4799ff1ca630573727c2aaf2ecaeabcdef672cde7eb1c72d", "typeScriptVersion": "2.8", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-koa-compose-3.2.5-85eb2e80ac50be95f37ccf8c407c09bbe3468e9d-integrity\\node_modules\\@types\\koa-compose\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/koa-compose`\n\n# Summary\nThis package contains type definitions for koa-compose (https://github.com/koajs/compose).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/koa-compose\n\nAdditional Details\n * Last updated: Thu, 07 Nov 2019 17:55:21 GMT\n * Dependencies: @types/koa\n * Global values: none\n\n# Credits\nThese definitions were written by <PERSON><PERSON><PERSON> <https://github.com/jkeylu>, and <PERSON> <https://github.com/astashov>.\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation. All rights reserved.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/koa-compose/-/koa-compose-3.2.5.tgz#85eb2e80ac50be95f37ccf8c407c09bbe3468e9d", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/koa-compose/-/koa-compose-3.2.5.tgz", "hash": "85eb2e80ac50be95f37ccf8c407c09bbe3468e9d", "integrity": "sha512-B8nG/OoE1ORZqCkBVsup/AKcvjdgoHnfi4pZMn5UwAPCbhk/96xyv284eBYW8JlQbQ7zDmnpFr68I/40mFoIBQ==", "registry": "npm", "packageName": "@types/koa-compose", "cacheIntegrity": "sha512-B8nG/OoE1ORZqCkBVsup/AKcvjdgoHnfi4pZMn5UwAPCbhk/96xyv284eBYW8JlQbQ7zDmnpFr68I/40mFoIBQ== sha1-hesugKxQvpXzfM+MQHwJu+NGjp0="}, "registry": "npm", "hash": "85eb2e80ac50be95f37ccf8c407c09bbe3468e9d"}