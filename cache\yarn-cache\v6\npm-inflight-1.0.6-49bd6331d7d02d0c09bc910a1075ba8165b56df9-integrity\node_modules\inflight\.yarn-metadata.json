{"manifest": {"name": "inflight", "version": "1.0.6", "description": "Add callbacks to requests in flight to avoid async duplication", "main": "inflight.js", "files": ["inflight.js"], "dependencies": {"once": "^1.3.0", "wrappy": "1"}, "devDependencies": {"tap": "^7.1.2"}, "scripts": {"test": "tap test.js --100"}, "repository": {"type": "git", "url": "https://github.com/npm/inflight.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/inflight/issues"}, "homepage": "https://github.com/isaacs/inflight", "license": "ISC", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-inflight-1.0.6-49bd6331d7d02d0c09bc910a1075ba8165b56df9-integrity\\node_modules\\inflight\\package.json", "readmeFilename": "README.md", "readme": "# inflight\n\nAdd callbacks to requests in flight to avoid async duplication\n\n## USAGE\n\n```javascript\nvar inflight = require('inflight')\n\n// some request that does some stuff\nfunction req(key, callback) {\n  // key is any random string.  like a url or filename or whatever.\n  //\n  // will return either a falsey value, indicating that the\n  // request for this key is already in flight, or a new callback\n  // which when called will call all callbacks passed to inflightk\n  // with the same key\n  callback = inflight(key, callback)\n\n  // If we got a falsey value back, then there's already a req going\n  if (!callback) return\n\n  // this is where you'd fetch the url or whatever\n  // callback is also once()-ified, so it can safely be assigned\n  // to multiple events etc.  First call wins.\n  setTimeout(function() {\n    callback(null, key)\n  }, 100)\n}\n\n// only assigns a single setTimeout\n// when it dings, all cbs get called\nreq('foo', cb1)\nreq('foo', cb2)\nreq('foo', cb3)\nreq('foo', cb4)\n```\n", "licenseText": "The ISC License\n\nCopyright (c) Isaac <PERSON>\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, <PERSON><PERSON><PERSON><PERSON><PERSON> IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9", "type": "tarball", "reference": "https://registry.yarnpkg.com/inflight/-/inflight-1.0.6.tgz", "hash": "49bd6331d7d02d0c09bc910a1075ba8165b56df9", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "registry": "npm", "packageName": "inflight", "cacheIntegrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA== sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk="}, "registry": "npm", "hash": "49bd6331d7d02d0c09bc910a1075ba8165b56df9"}