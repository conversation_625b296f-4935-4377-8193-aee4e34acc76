{"manifest": {"name": "htmlparser2", "description": "Fast & forgiving HTML/XML/RSS parser", "version": "3.10.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["html", "parser", "streams", "xml", "dom", "rss", "feed", "atom"], "repository": {"type": "git", "url": "git://github.com/fb55/htmlparser2.git"}, "bugs": {"mail": "<EMAIL>", "url": "http://github.com/fb55/htmlparser2/issues"}, "main": "lib/index.js", "files": ["lib"], "scripts": {"lcov": "istanbul cover _mocha --report lcovonly -- -R spec", "coveralls": "npm run lint && npm run lcov && (cat coverage/lcov.info | coveralls || exit 0)", "test": "mocha && npm run lint", "lint": "eslint lib test"}, "dependencies": {"domelementtype": "^1.3.1", "domhandler": "^2.3.0", "domutils": "^1.5.1", "entities": "^1.1.1", "inherits": "^2.0.1", "readable-stream": "^3.1.1"}, "devDependencies": {"coveralls": "^3.0.1", "eslint": "^5.13.0", "istanbul": "^0.4.3", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0"}, "browser": {"readable-stream": false}, "license": "MIT", "prettier": {"tabWidth": 4}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-htmlparser2-3.10.1-bd679dc3f59897b6a34bb10749c855bb53a9392f-integrity\\node_modules\\htmlparser2\\package.json", "readmeFilename": "README.md", "readme": "# htmlparser2\n\n[![NPM version](http://img.shields.io/npm/v/htmlparser2.svg?style=flat)](https://npmjs.org/package/htmlparser2)\n[![Downloads](https://img.shields.io/npm/dm/htmlparser2.svg?style=flat)](https://npmjs.org/package/htmlparser2)\n[![Build Status](http://img.shields.io/travis/fb55/htmlparser2/master.svg?style=flat)](http://travis-ci.org/fb55/htmlparser2)\n[![Coverage](http://img.shields.io/coveralls/fb55/htmlparser2.svg?style=flat)](https://coveralls.io/r/fb55/htmlparser2)\n\nA forgiving HTML/XML/RSS parser. The parser can handle streams and provides a callback interface.\n\n## Installation\n\tnpm install htmlparser2\n\nA live demo of htmlparser2 is available [here](https://astexplorer.net/#/2AmVrGuGVJ).\n\n## Usage\n\n```javascript\nvar htmlparser = require(\"htmlparser2\");\nvar parser = new htmlparser.Parser({\n\tonopentag: function(name, attribs){\n\t\tif(name === \"script\" && attribs.type === \"text/javascript\"){\n\t\t\tconsole.log(\"JS! Hooray!\");\n\t\t}\n\t},\n\tontext: function(text){\n\t\tconsole.log(\"-->\", text);\n\t},\n\tonclosetag: function(tagname){\n\t\tif(tagname === \"script\"){\n\t\t\tconsole.log(\"That's it?!\");\n\t\t}\n\t}\n}, {decodeEntities: true});\nparser.write(\"Xyz <script type='text/javascript'>var foo = '<<bar>>';</ script>\");\nparser.end();\n```\n\nOutput (simplified):\n\n```\n--> Xyz\nJS! Hooray!\n--> var foo = '<<bar>>';\nThat's it?!\n```\n\n## Documentation\n\nRead more about the parser and its options in the [wiki](https://github.com/fb55/htmlparser2/wiki/Parser-options).\n\n## Get a DOM\nThe `DomHandler` (known as `DefaultHandler` in the original `htmlparser` module) produces a DOM (document object model) that can be manipulated using the [`DomUtils`](https://github.com/fb55/DomUtils) helper.\n\nThe `DomHandler`, while still bundled with this module, was moved to its [own module](https://github.com/fb55/domhandler). Have a look at it for further information.\n\n## Parsing RSS/RDF/Atom Feeds\n\n```javascript\nnew htmlparser.FeedHandler(function(<error> error, <object> feed){\n    ...\n});\n```\n\nNote: While the provided feed handler works for most feeds, you might want to use  [danmactough/node-feedparser](https://github.com/danmactough/node-feedparser), which is much better tested and actively maintained.\n\n## Performance\n\nAfter having some artificial benchmarks for some time, __@AndreasMadsen__ published his [`htmlparser-benchmark`](https://github.com/AndreasMadsen/htmlparser-benchmark), which benchmarks HTML parses based on real-world websites.\n\nAt the time of writing, the latest versions of all supported parsers show the following performance characteristics on [Travis CI](https://travis-ci.org/AndreasMadsen/htmlparser-benchmark/builds/10805007) (please note that Travis doesn't guarantee equal conditions for all tests):\n\n```\ngumbo-parser   : 34.9208 ms/file ± 21.4238\nhtml-parser    : 24.8224 ms/file ± 15.8703\nhtml5          : 419.597 ms/file ± 264.265\nhtmlparser     : 60.0722 ms/file ± 384.844\nhtmlparser2-dom: 12.0749 ms/file ± 6.49474\nhtmlparser2    : 7.49130 ms/file ± 5.74368\nhubbub         : 30.4980 ms/file ± 16.4682\nlibxmljs       : 14.1338 ms/file ± 18.6541\nparse5         : 22.0439 ms/file ± 15.3743\nsax            : 49.6513 ms/file ± 26.6032\n```\n\n## How does this module differ from [node-htmlparser](https://github.com/tautologistics/node-htmlparser)?\n\nThis is a fork of the `htmlparser` module. The main difference is that this is intended to be used only with node (it runs on other platforms using [browserify](https://github.com/substack/node-browserify)). `htmlparser2` was rewritten multiple times and, while it maintains an API that's compatible with `htmlparser` in most cases, the projects don't share any code anymore.\n\nThe parser now provides a callback interface close to [sax.js](https://github.com/isaacs/sax-js) (originally targeted at [readabilitySAX](https://github.com/fb55/readabilitysax)). As a result, old handlers won't work anymore.\n\nThe `DefaultHandler` and the `RssHandler` were renamed to clarify their purpose (to `DomHandler` and `FeedHandler`). The old names are still available when requiring `htmlparser2`, your code should work as expected.\n", "licenseText": "Copyright 2010, 2011, <PERSON> <<EMAIL>>. All rights reserved.\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n \nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n \nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/htmlparser2/-/htmlparser2-3.10.1.tgz#bd679dc3f59897b6a34bb10749c855bb53a9392f", "type": "tarball", "reference": "https://registry.yarnpkg.com/htmlparser2/-/htmlparser2-3.10.1.tgz", "hash": "bd679dc3f59897b6a34bb10749c855bb53a9392f", "integrity": "sha512-IgieNijUMbkDovyoKObU1DUhm1iwNYE/fuifEoEHfd1oZKZDaONBSkal7Y01shxsM49R4XaMdGez3WnF9UfiCQ==", "registry": "npm", "packageName": "htmlparser2", "cacheIntegrity": "sha512-IgieNijUMbkDovyoKObU1DUhm1iwNYE/fuifEoEHfd1oZKZDaONBSkal7Y01shxsM49R4XaMdGez3WnF9UfiCQ== sha1-vWedw/WYl7ajS7EHSchVu1OpOS8="}, "registry": "npm", "hash": "bd679dc3f59897b6a34bb10749c855bb53a9392f"}