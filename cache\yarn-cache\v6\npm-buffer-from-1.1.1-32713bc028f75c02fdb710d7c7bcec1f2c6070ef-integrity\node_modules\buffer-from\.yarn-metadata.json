{"manifest": {"name": "buffer-from", "version": "1.1.1", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/LinusU/buffer-from.git"}, "files": ["index.js"], "scripts": {"test": "standard && node test"}, "devDependencies": {"standard": "^7.1.2"}, "keywords": ["buffer", "buffer from"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-buffer-from-1.1.1-32713bc028f75c02fdb710d7c7bcec1f2c6070ef-integrity\\node_modules\\buffer-from\\package.json", "readmeFilename": "readme.md", "readme": "# Buffer From\n\nA [ponyfill](https://ponyfill.com) for `Buffer.from`, uses native implementation if available.\n\n## Installation\n\n```sh\nnpm install --save buffer-from\n```\n\n## Usage\n\n```js\nconst bufferFrom = require('buffer-from')\n\nconsole.log(bufferFrom([1, 2, 3, 4]))\n//=> <Buffer 01 02 03 04>\n\nconst arr = new Uint8Array([1, 2, 3, 4])\nconsole.log(bufferFrom(arr.buffer, 1, 2))\n//=> <Buffer 02 03>\n\nconsole.log(bufferFrom('test', 'utf8'))\n//=> <Buffer 74 65 73 74>\n\nconst buf = bufferFrom('test')\nconsole.log(bufferFrom(buf))\n//=> <Buffer 74 65 73 74>\n```\n\n## API\n\n### bufferFrom(array)\n\n- `array` &lt;Array&gt;\n\nAllocates a new `Buffer` using an `array` of octets.\n\n### bufferFrom(arrayBuffer[, byteOffset[, length]])\n\n- `arrayBuffer` &lt;ArrayBuffer&gt; The `.buffer` property of a TypedArray or ArrayBuffer\n- `byteOffset` &lt;Integer&gt; Where to start copying from `arrayBuffer`. **Default:** `0`\n- `length` &lt;Integer&gt; How many bytes to copy from `arrayBuffer`. **Default:** `arrayBuffer.length - byteOffset`\n\nWhen passed a reference to the `.buffer` property of a TypedArray instance, the\nnewly created `Buffer` will share the same allocated memory as the TypedArray.\n\nThe optional `byteOffset` and `length` arguments specify a memory range within\nthe `arrayBuffer` that will be shared by the `Buffer`.\n\n### bufferFrom(buffer)\n\n- `buffer` &lt;Buffer&gt; An existing `Buffer` to copy data from\n\nCopies the passed `buffer` data onto a new `Buffer` instance.\n\n### bufferFrom(string[, encoding])\n\n- `string` &lt;String&gt; A string to encode.\n- `encoding` &lt;String&gt; The encoding of `string`. **Default:** `'utf8'`\n\nCreates a new `Buffer` containing the given JavaScript string `string`. If\nprovided, the `encoding` parameter identifies the character encoding of\n`string`.\n\n## See also\n\n- [buffer-alloc](https://github.com/LinusU/buffer-alloc) A ponyfill for `Buffer.alloc`\n- [buffer-alloc-unsafe](https://github.com/LinusU/buffer-alloc-unsafe) A ponyfill for `Buffer.allocUnsafe`\n", "description": "A [ponyfill](https://ponyfill.com) for `Buffer.from`, uses native implementation if available.", "licenseText": "MIT License\n\nCopyright (c) 2016, 2018 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/buffer-from/-/buffer-from-1.1.1.tgz#32713bc028f75c02fdb710d7c7bcec1f2c6070ef", "type": "tarball", "reference": "https://registry.yarnpkg.com/buffer-from/-/buffer-from-1.1.1.tgz", "hash": "32713bc028f75c02fdb710d7c7bcec1f2c6070ef", "integrity": "sha512-MQcXEUbCKtEo7bhqEs6560Hyd4XaovZlO/k9V3hjVUF/zwW7KBVdSK4gIt/bzwS9MbR5qob+F5jusZsb0YQK2A==", "registry": "npm", "packageName": "buffer-from", "cacheIntegrity": "sha512-MQc<PERSON>EUbCKtEo7bhqEs6560Hyd4XaovZlO/k9V3hjVUF/zwW7KBVdSK4gIt/bzwS9MbR5qob+F5jusZsb0YQK2A== sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8="}, "registry": "npm", "hash": "32713bc028f75c02fdb710d7c7bcec1f2c6070ef"}