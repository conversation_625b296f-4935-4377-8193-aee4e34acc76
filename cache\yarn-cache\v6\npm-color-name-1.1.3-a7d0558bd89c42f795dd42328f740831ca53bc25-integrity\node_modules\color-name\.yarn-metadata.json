{"manifest": {"name": "color-name", "version": "1.1.3", "description": "A list of color names and its values", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "**************:dfcreative/color-name.git"}, "keywords": ["color-name", "color", "color-keyword", "keyword"], "author": {"name": "DY", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/dfcreative/color-name/issues"}, "homepage": "https://github.com/dfcreative/color-name", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-color-name-1.1.3-a7d0558bd89c42f795dd42328f740831ca53bc25-integrity\\node_modules\\color-name\\package.json", "readmeFilename": "README.md", "readme": "A JSON with color names and its values. Based on http://dev.w3.org/csswg/css-color/#named-colors.\n\n[![NPM](https://nodei.co/npm/color-name.png?mini=true)](https://nodei.co/npm/color-name/)\n\n\n```js\nvar colors = require('color-name');\ncolors.red //[255,0,0]\n```\n\n<a href=\"LICENSE\"><img src=\"https://upload.wikimedia.org/wikipedia/commons/0/0c/MIT_logo.svg\" width=\"120\"/></a>\n", "licenseText": "The MIT License (MIT)\nCopyright (c) 2015 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/color-name/-/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25", "type": "tarball", "reference": "https://registry.yarnpkg.com/color-name/-/color-name-1.1.3.tgz", "hash": "a7d0558bd89c42f795dd42328f740831ca53bc25", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "registry": "npm", "packageName": "color-name", "cacheIntegrity": "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw== sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="}, "registry": "npm", "hash": "a7d0558bd89c42f795dd42328f740831ca53bc25"}