[{"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-weatherhealth/@qb-core/shared/locale.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-weatherhealth/client/animations.lua", "mt": **********, "s": 9326, "i": "s1wBAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-weatherhealth/client/main.lua", "mt": **********, "s": 11841, "i": "WuQAAAAAEgAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-weatherhealth/client/npc.lua", "mt": **********, "s": 17836, "i": "W+QAAAAAEgAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-weatherhealth/client/player.lua", "mt": **********, "s": 15237, "i": "XeQAAAAAEwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-weatherhealth/config.lua", "mt": **********, "s": 14415, "i": "yuMAAAAADQAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-weatherhealth/fxmanifest.lua", "mt": **********, "s": 630, "i": "xuMAAAAAEQAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-weatherhealth/locales/en.lua", "mt": **********, "s": 3593, "i": "E+QAAAAAKwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-weatherhealth/shared/utils.lua", "mt": **********, "s": 6377, "i": "0uMAAAAADwAAAAAAAAAAAA=="}]