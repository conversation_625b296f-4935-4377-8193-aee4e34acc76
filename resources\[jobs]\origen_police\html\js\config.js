const teclas = {
	/* NUMPAD */
	96: 'NUMPAD0',
	97: 'NUMPAD1',
	98: 'NUMPAD2',
	99: 'NUMPAD3',
	100: 'NUMPAD4',
	101: 'NUMPAD5',
	102: 'NUMPAD6',
	103: 'NUMPAD7',
	104: 'NUMPAD8',
	105: 'NUMPAD9',

	45: 'NUMPAD0',
	35: 'NUMPAD1',
	40: 'NUMPAD2',
	34: 'NUMPAD3',
	37: 'NUMPAD4',
	12: 'NUMPAD5',
	39: 'NUMPAD6',
	36: 'NUMPAD7',
	38: 'NUMPAD8',
	33: 'NUMPAD9',
	144: 'NUMLOCK',
	111: 'DIVIDE',
	106: 'MULTIPLY',
	109: 'SUBTRACT',
	107: 'ADD',
	13: 'NUMPADENTER',
	/* Fs */
	112: 'F1',
	113: 'F2',
	114: 'F3',
	115: 'F4',
	116: 'F5',
	117: 'F6',
	118: 'F7',
	119: 'F8',
	120: 'F9',
	121: 'F10',
	122: 'F11',
	123: 'F12',
	/* Especiales */
	8: 'BACK',
	9: 'TAB',
	19: 'PAUSE',
	20: 'CAPITAL',
	27: 'ESCAPE',
	32: 'SPACE',
	17: 'RMENU',
	187: 'PLUS',
	188: 'COMMA',
	189: 'MINUS',
	190: 'PERIOD',
	186: 'GRAVE',
	219: 'APOSTROPHE'
};

const codes = [
	'10.8',
	'10.10',
	'Cod 7',
	'254-V',
	'487-V',
	'Cod 2',
	'10.22',
	'10.98',
	'6-Adam',
	'Veh 488',
	'Veh 487',
	'Veh Alt',
	'10.6',
	'10-20',
	'QRR'
];

const ReferenceSprite = {
	38: 'https://docs.fivem.net/blips/radar_raceflag.png',
	43: 'https://docs.fivem.net/blips/radar_police_heli.png',
	67: 'https://docs.fivem.net/blips/radar_security_van.png',
	110: 'https://docs.fivem.net/blips/radar_gun_shop.png',
	226: 'https://docs.fivem.net/blips/radar_gang_vehicle_bikers.png',
	496: 'https://docs.fivem.net/blips/radar_production_weed.png',
	780: 'https://docs.fivem.net/blips/radar_docks_export.png',
	502: 'https://docs.fivem.net/blips/radar_capture_1.png',
	503: 'https://docs.fivem.net/blips/radar_capture_2.png',
	504: 'https://docs.fivem.net/blips/radar_capture_3.png',
	505: 'https://docs.fivem.net/blips/radar_capture_4.png',
	102: 'https://docs.fivem.net/blips/radar_comedy_club.png',
	669: 'https://docs.fivem.net/blips/radar_arena_zr380.png',
	623: 'https://docs.fivem.net/blips/radar_pickup_dtb_blast_decrease.png',
	134: 'https://docs.fivem.net/blips/radar_crim_cuff_keys.png'
};

const ReferenceColor = {
	1: '#e03232',
	76: '#782323',
	3: '#5db6e5',
	12: '#70a8ae',
	15: '#6ac4bf',
	18: '#97cae9',
	26: '#7ac3fe',
	29: '#4561ab',
	30: '#29a5b8',
	2: '#71cb71',
	11: '#8dcea7',
	24: '#bbd65b',
	25: '#0c7b56',
	82: '#a4ccaa',
	28: '#cda80c',
	5: '#eec64e',
	46: '#ecf029',
	34: '#ed8ca1',
	41: '#f29d9d',
	40: '#4c4c4c',
	85: '#3d3d3d',
	50: '#896ce2'
};

const MarkerBlips = {
	radar: './img/icons/6oQyhmQ.png'
};

const BlacklistedTranslations = [
	"OutDuty"
]