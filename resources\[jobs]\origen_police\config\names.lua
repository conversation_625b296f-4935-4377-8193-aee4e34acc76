Config.RandomNames = {
    { -- <PERSON><PERSON><PERSON>
        ["male"] = {
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON><PERSON>",
            "Maverick",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "Mark",
            "Donald",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
        },
        ["female"] = {
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
        } 
    },
    { -- <PERSON><PERSON><PERSON><PERSON>
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON> <PERSON>",
        "<PERSON> <PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "Davis",
        "Rodríguez",
        "Martínez",
        "Hernández",
        "López",
        "González",
        "Pérez",
        "Taylor",
        "Anderson",
        "Wilson",
        "Jackson",
        "Moore",
        "Martin",
        "Lee",
        "Pérez",
        "Harris",
        "Clark",
        "Lewis",
        "Robinson",
        "Walker",
        "Hall",
        "Allen",
        "King",
        "Nabosky"
    }
}

function GetRandomName(m_o_f)
    if not m_o_f or (m_o_f ~= "male" and m_o_f ~= "female") then
        m_o_f = "male"
    end
    return Config.RandomNames[1][m_o_f][math.random(1, #Config.RandomNames[1][m_o_f])] .. " " .. Config.RandomNames[2][math.random(1, #Config.RandomNames[2])]
end

exports("GetRandomName", GetRandomName)