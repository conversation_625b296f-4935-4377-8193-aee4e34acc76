# 🚀 QUICK START - Weather Health System

## ⚡ Rych<PERSON><PERSON> instalace (2 minuty)

### 1. <PERSON><PERSON><PERSON><PERSON>
```sql
-- Spusťte v MySQL/phpMyAdmin:
-- Zkopírujte obsah souboru sql/install.sql a spusťte
```

### 2. Restart serveru
```
restart qb-core
ensure qb-weatherhealth
```

### 3. Test systému
```
/giveweatheritems
/weatherhealth status
/testweatherscenario cold
```

## ✅ Hotovo!

Systém je nyní plně funkční!

---

## 🎮 Základní použití

### Pro hráče:
- **Oblékejte se podle počasí** (teplé oblečení v zimě, lehké v létě)
- **Používejte léčebné items** kdy<PERSON> onemocníte
- **Sledujte notifikace** o zdravotním stavu

### Pro adminy:
- `/checkhealthstatus [id]` - zkontroluje zdraví hráče
- `/givedisease [id] cold` - dá hráči nachlazení
- `/curedisease [id]` - vyl<PERSON><PERSON><PERSON> hráče
- `/weatherstats` - zobrazí statistiky systému

---

## 🧪 Testovací scénář

1. **Dejte si items**: `/giveweatheritems`
2. **Změňte počasí**: `/testweatherscenario cold`
3. **Oblečte se lehce** (tričko, šortky)
4. **Počkejte 5 minut** - dostanete varování
5. **Použijte heatpack** nebo se oblečte teple
6. **Zkontrolujte stav**: `/healthstatus`

---

## ❗ Řešení problémů

### Systém nefunguje:
```
restart qb-core
ensure qb-weatherhealth
```

### Items nefungují:
```
/giveweatheritems
```

### Chyby v console:
- Zkontrolujte databázové připojení
- Ověřte, že qb-weathersync běží

---

## 📞 Podpora

- Zkontrolujte `README.md` pro podrobnosti
- Použijte `install.md` pro řešení problémů
- Zkontrolujte `items_info.txt` pro informace o items

**Systém je připraven k použití! 🎉**
