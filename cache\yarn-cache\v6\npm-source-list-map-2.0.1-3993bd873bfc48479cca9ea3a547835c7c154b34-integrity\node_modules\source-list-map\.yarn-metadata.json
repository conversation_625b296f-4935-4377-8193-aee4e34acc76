{"manifest": {"name": "source-list-map", "version": "2.0.1", "description": "Fast line to line SourceMap generator.", "author": {"name": "<PERSON> @sokra"}, "main": "lib/index.js", "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "https://github.com/webpack/source-list-map.git"}, "keywords": ["source-map"], "files": ["lib"], "license": "MIT", "bugs": {"url": "https://github.com/webpack/source-list-map/issues"}, "homepage": "https://github.com/webpack/source-list-map", "devDependencies": {"mocha": "^2.2.1", "should": "^5.2.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-source-list-map-2.0.1-****************************************-integrity\\node_modules\\source-list-map\\package.json", "readmeFilename": "README.md", "readme": "# source-list-map\n\n## API\n\n### Example\n\n``` js\nvar SourceListMap = require(\"source-list-map\").SourceListMap;\n\n// Create a new map\nvar map = new SourceListMap();\n\n// Add generated code that is map line to line to some soure\nmap.add(\"Generated\\ncode1\\n\", \"source-code.js\", \"Orginal\\nsource\");\n\n// Add generated code that isn't mapped\nmap.add(\"Generated\\ncode2\\n\");\n\n// Get SourceMap and generated source\nmap.toStringWithSourceMap({ file: \"generated-code.js\" });\n// {\n//   source: 'Generated\\ncode1\\nGenerated\\ncode2\\n',\n//   map: {\n//      version: 3,\n//      file: 'generated-code.js',\n//      sources: [ 'source-code.js' ],\n//      sourcesContent: [ 'Orginal\\nsource' ],\n//      mappings: 'AAAA;AACA;;;'\n//    }\n// }\n\n// Convert existing SourceMap into SourceListMap\n// (Only the first mapping per line is preserved)\nvar fromStringWithSourceMap = require(\"source-list-map\").fromStringWithSourceMap;\nvar map = fromStringWithSourceMap(\"Generated\\ncode\", { version: 3, ... });\n\n```\n\n### `new SourceListMap()`\n\n### `SourceListMap.prototype.add`\n\n``` js\nSourceListMap.prototype.add(generatedCode: string)\nSourceListMap.prototype.add(generatedCode: string, source: string, originalSource: string)\nSourceListMap.prototype.add(sourceListMap: SourceListMap)\n```\n\nAppend some stuff.\n\n### `SourceListMap.prototype.prepend`\n\n``` js\nSourceListMap.prototype.prepend(generatedCode: string)\nSourceListMap.prototype.prepend(generatedCode: string, source: string, originalSource: string)\nSourceListMap.prototype.prepend(sourceListMap: SourceListMap)\n```\n\nPrepend some stuff.\n\n### `SourceListMap.prototype.toString()`\n\nGet generated code.\n\n### `SourceListMap.prototype.toStringWithSourceMap`\n\n``` js\nSourceListMap.prototype.toStringWithSourceMap(options: object)\n```\n\nGet generated code and SourceMap. `options` can contains `file` property which defines the `file` property of the SourceMap.\n\n### `SourceListMap.prototype.mapGeneratedCode`\n\n``` js\nSourceListMap.prototype.mapGeneratedCode(fn: function) : SourceListMap\n```\n\nApplies `fn` to each generated code block (per line). The returned value is set as new generated code. Returns a new SourceListMap.\n\nRemoving and adding lines is supported. The SourceMap complexity will increase when doing this.\n\n## Test\n\n[![Build Status](https://travis-ci.org/webpack/source-list-map.svg)](https://travis-ci.org/webpack/source-list-map)\n\n## License\n\nCopyright (c) 2017 JS Foundation\n\nMIT (http://www.opensource.org/licenses/mit-license.php)", "licenseText": "Copyright 2017 JS Foundation\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/source-list-map/-/source-list-map-2.0.1.tgz#****************************************", "type": "tarball", "reference": "https://registry.yarnpkg.com/source-list-map/-/source-list-map-2.0.1.tgz", "hash": "****************************************", "integrity": "sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw==", "registry": "npm", "packageName": "source-list-map", "cacheIntegrity": "sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw== sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ="}, "registry": "npm", "hash": "****************************************"}