{"manifest": {"name": "@xtuc/long", "version": "4.2.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A Long class for representing a 64-bit two's-complement integer value.", "main": "src/long.js", "repository": {"type": "git", "url": "https://github.com/dcodeIO/long.js.git"}, "bugs": {"url": "https://github.com/dcodeIO/long.js/issues"}, "keywords": ["math"], "dependencies": {}, "devDependencies": {"webpack": "^3.10.0"}, "license": "Apache-2.0", "scripts": {"build": "webpack", "test": "node tests"}, "files": ["index.js", "LICENSE", "README.md", "src/long.js", "dist/long.js", "dist/long.js.map", "index.d.ts"], "types": "index.d.ts", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@xtuc-long-4.2.2-d291c6a4e97989b5c61d9acf396ae4fe133a718d-integrity\\node_modules\\@xtuc\\long\\package.json", "readmeFilename": "README.md", "readme": "long.js\n=======\n\nA Long class for representing a 64 bit two's-complement integer value derived from the [Closure Library](https://github.com/google/closure-library)\nfor stand-alone use and extended with unsigned support.\n\n[![npm](https://img.shields.io/npm/v/long.svg)](https://www.npmjs.com/package/long) [![Build Status](https://travis-ci.org/dcodeIO/long.js.svg)](https://travis-ci.org/dcodeIO/long.js)\n\nBackground\n----------\n\nAs of [ECMA-262 5th Edition](http://ecma262-5.com/ELS5_HTML.htm#Section_8.5), \"all the positive and negative integers\nwhose magnitude is no greater than 2<sup>53</sup> are representable in the Number type\", which is \"representing the\ndoubleprecision 64-bit format IEEE 754 values as specified in the IEEE Standard for Binary Floating-Point Arithmetic\".\nThe [maximum safe integer](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/MAX_SAFE_INTEGER)\nin JavaScript is 2<sup>53</sup>-1.\n\nExample: 2<sup>64</sup>-1 is 1844674407370955**1615** but in JavaScript it evaluates to 1844674407370955**2000**.\n\nFurthermore, bitwise operators in JavaScript \"deal only with integers in the range −2<sup>31</sup> through\n2<sup>31</sup>−1, inclusive, or in the range 0 through 2<sup>32</sup>−1, inclusive. These operators accept any value of\nthe Number type but first convert each such value to one of 2<sup>32</sup> integer values.\"\n\nIn some use cases, however, it is required to be able to reliably work with and perform bitwise operations on the full\n64 bits. This is where long.js comes into play.\n\nUsage\n-----\n\nThe class is compatible with CommonJS and AMD loaders and is exposed globally as `Long` if neither is available.\n\n```javascript\nvar Long = require(\"long\");\n\nvar longVal = new Long(0xFFFFFFFF, 0x7FFFFFFF);\n\nconsole.log(longVal.toString());\n...\n```\n\nAPI\n---\n\n### Constructor\n\n* new **Long**(low: `number`, high: `number`, unsigned?: `boolean`)<br />\n  Constructs a 64 bit two's-complement integer, given its low and high 32 bit values as *signed* integers. See the from* functions below for more convenient ways of constructing Longs.\n\n### Fields\n\n* Long#**low**: `number`<br />\n  The low 32 bits as a signed value.\n\n* Long#**high**: `number`<br />\n  The high 32 bits as a signed value.\n\n* Long#**unsigned**: `boolean`<br />\n  Whether unsigned or not.\n\n### Constants\n\n* Long.**ZERO**: `Long`<br />\n  Signed zero.\n\n* Long.**ONE**: `Long`<br />\n  Signed one.\n\n* Long.**NEG_ONE**: `Long`<br />\n  Signed negative one.\n\n* Long.**UZERO**: `Long`<br />\n  Unsigned zero.\n\n* Long.**UONE**: `Long`<br />\n  Unsigned one.\n\n* Long.**MAX_VALUE**: `Long`<br />\n  Maximum signed value.\n\n* Long.**MIN_VALUE**: `Long`<br />\n  Minimum signed value.\n\n* Long.**MAX_UNSIGNED_VALUE**: `Long`<br />\n  Maximum unsigned value.\n\n### Utility\n\n* Long.**isLong**(obj: `*`): `boolean`<br />\n  Tests if the specified object is a Long.\n\n* Long.**fromBits**(lowBits: `number`, highBits: `number`, unsigned?: `boolean`): `Long`<br />\n  Returns a Long representing the 64 bit integer that comes by concatenating the given low and high bits. Each is assumed to use 32 bits.\n\n* Long.**fromBytes**(bytes: `number[]`, unsigned?: `boolean`, le?: `boolean`): `Long`<br />\n  Creates a Long from its byte representation.\n\n* Long.**fromBytesLE**(bytes: `number[]`, unsigned?: `boolean`): `Long`<br />\n  Creates a Long from its little endian byte representation.\n\n* Long.**fromBytesBE**(bytes: `number[]`, unsigned?: `boolean`): `Long`<br />\n  Creates a Long from its big endian byte representation.\n\n* Long.**fromInt**(value: `number`, unsigned?: `boolean`): `Long`<br />\n  Returns a Long representing the given 32 bit integer value.\n\n* Long.**fromNumber**(value: `number`, unsigned?: `boolean`): `Long`<br />\n  Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.\n\n* Long.**fromString**(str: `string`, unsigned?: `boolean`, radix?: `number`)<br />\n  Long.**fromString**(str: `string`, radix: `number`)<br />\n  Returns a Long representation of the given string, written using the specified radix.\n\n* Long.**fromValue**(val: `*`, unsigned?: `boolean`): `Long`<br />\n  Converts the specified value to a Long using the appropriate from* function for its type.\n\n### Methods\n\n* Long#**add**(addend: `Long | number | string`): `Long`<br />\n  Returns the sum of this and the specified Long.\n\n* Long#**and**(other: `Long | number | string`): `Long`<br />\n  Returns the bitwise AND of this Long and the specified.\n\n* Long#**compare**/**comp**(other: `Long | number | string`): `number`<br />\n  Compares this Long's value with the specified's. Returns `0` if they are the same, `1` if the this is greater and `-1` if the given one is greater.\n\n* Long#**divide**/**div**(divisor: `Long | number | string`): `Long`<br />\n  Returns this Long divided by the specified.\n\n* Long#**equals**/**eq**(other: `Long | number | string`): `boolean`<br />\n  Tests if this Long's value equals the specified's.\n\n* Long#**getHighBits**(): `number`<br />\n  Gets the high 32 bits as a signed integer.\n\n* Long#**getHighBitsUnsigned**(): `number`<br />\n  Gets the high 32 bits as an unsigned integer.\n\n* Long#**getLowBits**(): `number`<br />\n  Gets the low 32 bits as a signed integer.\n\n* Long#**getLowBitsUnsigned**(): `number`<br />\n  Gets the low 32 bits as an unsigned integer.\n\n* Long#**getNumBitsAbs**(): `number`<br />\n  Gets the number of bits needed to represent the absolute value of this Long.\n\n* Long#**greaterThan**/**gt**(other: `Long | number | string`): `boolean`<br />\n  Tests if this Long's value is greater than the specified's.\n\n* Long#**greaterThanOrEqual**/**gte**/**ge**(other: `Long | number | string`): `boolean`<br />\n  Tests if this Long's value is greater than or equal the specified's.\n\n* Long#**isEven**(): `boolean`<br />\n  Tests if this Long's value is even.\n\n* Long#**isNegative**(): `boolean`<br />\n  Tests if this Long's value is negative.\n\n* Long#**isOdd**(): `boolean`<br />\n  Tests if this Long's value is odd.\n\n* Long#**isPositive**(): `boolean`<br />\n  Tests if this Long's value is positive.\n\n* Long#**isZero**/**eqz**(): `boolean`<br />\n  Tests if this Long's value equals zero.\n\n* Long#**lessThan**/**lt**(other: `Long | number | string`): `boolean`<br />\n  Tests if this Long's value is less than the specified's.\n\n* Long#**lessThanOrEqual**/**lte**/**le**(other: `Long | number | string`): `boolean`<br />\n  Tests if this Long's value is less than or equal the specified's.\n\n* Long#**modulo**/**mod**/**rem**(divisor: `Long | number | string`): `Long`<br />\n  Returns this Long modulo the specified.\n\n* Long#**multiply**/**mul**(multiplier: `Long | number | string`): `Long`<br />\n  Returns the product of this and the specified Long.\n\n* Long#**negate**/**neg**(): `Long`<br />\n  Negates this Long's value.\n\n* Long#**not**(): `Long`<br />\n  Returns the bitwise NOT of this Long.\n\n* Long#**notEquals**/**neq**/**ne**(other: `Long | number | string`): `boolean`<br />\n  Tests if this Long's value differs from the specified's.\n\n* Long#**or**(other: `Long | number | string`): `Long`<br />\n  Returns the bitwise OR of this Long and the specified.\n\n* Long#**shiftLeft**/**shl**(numBits: `Long | number | string`): `Long`<br />\n  Returns this Long with bits shifted to the left by the given amount.\n\n* Long#**shiftRight**/**shr**(numBits: `Long | number | string`): `Long`<br />\n  Returns this Long with bits arithmetically shifted to the right by the given amount.\n\n* Long#**shiftRightUnsigned**/**shru**/**shr_u**(numBits: `Long | number | string`): `Long`<br />\n  Returns this Long with bits logically shifted to the right by the given amount.\n\n* Long#**rotateLeft**/**rotl**(numBits: `Long | number | string`): `Long`<br />\n  Returns this Long with bits rotated to the left by the given amount.\n\n* Long#**rotateRight**/**rotr**(numBits: `Long | number | string`): `Long`<br />\n  Returns this Long with bits rotated to the right by the given amount.\n\n* Long#**subtract**/**sub**(subtrahend: `Long | number | string`): `Long`<br />\n  Returns the difference of this and the specified Long.\n\n* Long#**toBytes**(le?: `boolean`): `number[]`<br />\n  Converts this Long to its byte representation.\n\n* Long#**toBytesLE**(): `number[]`<br />\n  Converts this Long to its little endian byte representation.\n\n* Long#**toBytesBE**(): `number[]`<br />\n  Converts this Long to its big endian byte representation.\n\n* Long#**toInt**(): `number`<br />\n  Converts the Long to a 32 bit integer, assuming it is a 32 bit integer.\n\n* Long#**toNumber**(): `number`<br />\n  Converts the Long to a the nearest floating-point representation of this value (double, 53 bit mantissa).\n\n* Long#**toSigned**(): `Long`<br />\n  Converts this Long to signed.\n\n* Long#**toString**(radix?: `number`): `string`<br />\n  Converts the Long to a string written in the specified radix.\n\n* Long#**toUnsigned**(): `Long`<br />\n  Converts this Long to unsigned.\n\n* Long#**xor**(other: `Long | number | string`): `Long`<br />\n  Returns the bitwise XOR of this Long and the given one.\n\nWebAssembly support\n-------------------\n\n[WebAssembly](http://webassembly.org) supports 64-bit integer arithmetic out of the box, hence a [tiny WebAssembly module](./src/wasm.wat) is used to compute operations like multiplication, division and remainder more efficiently (slow operations like division are around twice as fast), falling back to floating point based computations in JavaScript where WebAssembly is not yet supported, e.g., in older versions of node.\n\nBuilding\n--------\n\nTo build an UMD bundle to `dist/long.js`, run:\n\n```\n$> npm install\n$> npm run build\n```\n\nRunning the [tests](./tests):\n\n```\n$> npm test\n```\n", "licenseText": "\n                                 Apache License\n                           Version 2.0, January 2004\n                        http://www.apache.org/licenses/\n\n   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n   1. Definitions.\n\n      \"License\" shall mean the terms and conditions for use, reproduction,\n      and distribution as defined by Sections 1 through 9 of this document.\n\n      \"Licensor\" shall mean the copyright owner or entity authorized by\n      the copyright owner that is granting the License.\n\n      \"Legal Entity\" shall mean the union of the acting entity and all\n      other entities that control, are controlled by, or are under common\n      control with that entity. For the purposes of this definition,\n      \"control\" means (i) the power, direct or indirect, to cause the\n      direction or management of such entity, whether by contract or\n      otherwise, or (ii) ownership of fifty percent (50%) or more of the\n      outstanding shares, or (iii) beneficial ownership of such entity.\n\n      \"You\" (or \"Your\") shall mean an individual or Legal Entity\n      exercising permissions granted by this License.\n\n      \"Source\" form shall mean the preferred form for making modifications,\n      including but not limited to software source code, documentation\n      source, and configuration files.\n\n      \"Object\" form shall mean any form resulting from mechanical\n      transformation or translation of a Source form, including but\n      not limited to compiled object code, generated documentation,\n      and conversions to other media types.\n\n      \"Work\" shall mean the work of authorship, whether in Source or\n      Object form, made available under the License, as indicated by a\n      copyright notice that is included in or attached to the work\n      (an example is provided in the Appendix below).\n\n      \"Derivative Works\" shall mean any work, whether in Source or Object\n      form, that is based on (or derived from) the Work and for which the\n      editorial revisions, annotations, elaborations, or other modifications\n      represent, as a whole, an original work of authorship. For the purposes\n      of this License, Derivative Works shall not include works that remain\n      separable from, or merely link (or bind by name) to the interfaces of,\n      the Work and Derivative Works thereof.\n\n      \"Contribution\" shall mean any work of authorship, including\n      the original version of the Work and any modifications or additions\n      to that Work or Derivative Works thereof, that is intentionally\n      submitted to Licensor for inclusion in the Work by the copyright owner\n      or by an individual or Legal Entity authorized to submit on behalf of\n      the copyright owner. For the purposes of this definition, \"submitted\"\n      means any form of electronic, verbal, or written communication sent\n      to the Licensor or its representatives, including but not limited to\n      communication on electronic mailing lists, source code control systems,\n      and issue tracking systems that are managed by, or on behalf of, the\n      Licensor for the purpose of discussing and improving the Work, but\n      excluding communication that is conspicuously marked or otherwise\n      designated in writing by the copyright owner as \"Not a Contribution.\"\n\n      \"Contributor\" shall mean Licensor and any individual or Legal Entity\n      on behalf of whom a Contribution has been received by Licensor and\n      subsequently incorporated within the Work.\n\n   2. Grant of Copyright License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      copyright license to reproduce, prepare Derivative Works of,\n      publicly display, publicly perform, sublicense, and distribute the\n      Work and such Derivative Works in Source or Object form.\n\n   3. Grant of Patent License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      (except as stated in this section) patent license to make, have made,\n      use, offer to sell, sell, import, and otherwise transfer the Work,\n      where such license applies only to those patent claims licensable\n      by such Contributor that are necessarily infringed by their\n      Contribution(s) alone or by combination of their Contribution(s)\n      with the Work to which such Contribution(s) was submitted. If You\n      institute patent litigation against any entity (including a\n      cross-claim or counterclaim in a lawsuit) alleging that the Work\n      or a Contribution incorporated within the Work constitutes direct\n      or contributory patent infringement, then any patent licenses\n      granted to You under this License for that Work shall terminate\n      as of the date such litigation is filed.\n\n   4. Redistribution. You may reproduce and distribute copies of the\n      Work or Derivative Works thereof in any medium, with or without\n      modifications, and in Source or Object form, provided that You\n      meet the following conditions:\n\n      (a) You must give any other recipients of the Work or\n          Derivative Works a copy of this License; and\n\n      (b) You must cause any modified files to carry prominent notices\n          stating that You changed the files; and\n\n      (c) You must retain, in the Source form of any Derivative Works\n          that You distribute, all copyright, patent, trademark, and\n          attribution notices from the Source form of the Work,\n          excluding those notices that do not pertain to any part of\n          the Derivative Works; and\n\n      (d) If the Work includes a \"NOTICE\" text file as part of its\n          distribution, then any Derivative Works that You distribute must\n          include a readable copy of the attribution notices contained\n          within such NOTICE file, excluding those notices that do not\n          pertain to any part of the Derivative Works, in at least one\n          of the following places: within a NOTICE text file distributed\n          as part of the Derivative Works; within the Source form or\n          documentation, if provided along with the Derivative Works; or,\n          within a display generated by the Derivative Works, if and\n          wherever such third-party notices normally appear. The contents\n          of the NOTICE file are for informational purposes only and\n          do not modify the License. You may add Your own attribution\n          notices within Derivative Works that You distribute, alongside\n          or as an addendum to the NOTICE text from the Work, provided\n          that such additional attribution notices cannot be construed\n          as modifying the License.\n\n      You may add Your own copyright statement to Your modifications and\n      may provide additional or different license terms and conditions\n      for use, reproduction, or distribution of Your modifications, or\n      for any such Derivative Works as a whole, provided Your use,\n      reproduction, and distribution of the Work otherwise complies with\n      the conditions stated in this License.\n\n   5. Submission of Contributions. Unless You explicitly state otherwise,\n      any Contribution intentionally submitted for inclusion in the Work\n      by You to the Licensor shall be under the terms and conditions of\n      this License, without any additional terms or conditions.\n      Notwithstanding the above, nothing herein shall supersede or modify\n      the terms of any separate license agreement you may have executed\n      with Licensor regarding such Contributions.\n\n   6. Trademarks. This License does not grant permission to use the trade\n      names, trademarks, service marks, or product names of the Licensor,\n      except as required for reasonable and customary use in describing the\n      origin of the Work and reproducing the content of the NOTICE file.\n\n   7. Disclaimer of Warranty. Unless required by applicable law or\n      agreed to in writing, Licensor provides the Work (and each\n      Contributor provides its Contributions) on an \"AS IS\" BASIS,\n      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or\n      implied, including, without limitation, any warranties or conditions\n      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A\n      PARTICULAR PURPOSE. You are solely responsible for determining the\n      appropriateness of using or redistributing the Work and assume any\n      risks associated with Your exercise of permissions under this License.\n\n   8. Limitation of Liability. In no event and under no legal theory,\n      whether in tort (including negligence), contract, or otherwise,\n      unless required by applicable law (such as deliberate and grossly\n      negligent acts) or agreed to in writing, shall any Contributor be\n      liable to You for damages, including any direct, indirect, special,\n      incidental, or consequential damages of any character arising as a\n      result of this License or out of the use or inability to use the\n      Work (including but not limited to damages for loss of goodwill,\n      work stoppage, computer failure or malfunction, or any and all\n      other commercial damages or losses), even if such Contributor\n      has been advised of the possibility of such damages.\n\n   9. Accepting Warranty or Additional Liability. While redistributing\n      the Work or Derivative Works thereof, You may choose to offer,\n      and charge a fee for, acceptance of support, warranty, indemnity,\n      or other liability obligations and/or rights consistent with this\n      License. However, in accepting such obligations, You may act only\n      on Your own behalf and on Your sole responsibility, not on behalf\n      of any other Contributor, and only if You agree to indemnify,\n      defend, and hold each Contributor harmless for any liability\n      incurred by, or claims asserted against, such Contributor by reason\n      of your accepting any such warranty or additional liability.\n\n   END OF TERMS AND CONDITIONS\n\n   APPENDIX: How to apply the Apache License to your work.\n\n      To apply the Apache License to your work, attach the following\n      boilerplate notice, with the fields enclosed by brackets \"[]\"\n      replaced with your own identifying information. (Don't include\n      the brackets!)  The text should be enclosed in the appropriate\n      comment syntax for the file format. We also recommend that a\n      file or class name and description of purpose be included on the\n      same \"printed page\" as the copyright notice for easier\n      identification within third-party archives.\n\n   Copyright [yyyy] [name of copyright owner]\n\n   Licensed under the Apache License, Version 2.0 (the \"License\");\n   you may not use this file except in compliance with the License.\n   You may obtain a copy of the License at\n\n       http://www.apache.org/licenses/LICENSE-2.0\n\n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an \"AS IS\" BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   See the License for the specific language governing permissions and\n   limitations under the License.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@xtuc/long/-/long-4.2.2.tgz#d291c6a4e97989b5c61d9acf396ae4fe133a718d", "type": "tarball", "reference": "https://registry.yarnpkg.com/@xtuc/long/-/long-4.2.2.tgz", "hash": "d291c6a4e97989b5c61d9acf396ae4fe133a718d", "integrity": "sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==", "registry": "npm", "packageName": "@xtuc/long", "cacheIntegrity": "sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ== sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0="}, "registry": "npm", "hash": "d291c6a4e97989b5c61d9acf396ae4fe133a718d"}