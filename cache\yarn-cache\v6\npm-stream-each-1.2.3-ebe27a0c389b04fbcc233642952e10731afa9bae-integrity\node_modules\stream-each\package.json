{"name": "stream-each", "version": "1.2.3", "description": "Iterate all the data in a stream", "main": "index.js", "dependencies": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}, "devDependencies": {"ndjson": "^1.5.0", "standard": "^5.3.1", "tape": "^4.2.1", "through2": "^2.0.0"}, "scripts": {"test": "standard && tape test.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/stream-each.git"}, "author": "<PERSON> (@mafintosh)", "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/stream-each/issues"}, "homepage": "https://github.com/mafintosh/stream-each"}