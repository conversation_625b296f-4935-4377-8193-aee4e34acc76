fx_version 'cerulean'
game {'gta5'}
lua54 'yes'

author 'vezironi'
description 'A multicharacter that everyone can customize and looks modern'

scriptname '0r-multicharacterv2'
version '1.1.4'

shared_scripts {
    '@ox_lib/init.lua',
    'config/*.lua',
    'locales/*.lua',
}

client_scripts {
    'client/*.lua',
}

server_scripts {
    '@oxmysql/lib/MySQL.lua',
    'server/*.lua',
}

escrow_ignore {
    'config/*.lua',
    'locales/*.lua',
    'server/*.lua',
    'client/*.lua',
}

files {
    'ui/index.html',
    'ui/index.css',
    'ui/index.js',
    'ui/assets/**/*.js',
    'ui/assets/**/*.otf',
    'ui/assets/**/*.png',
    'ui/assets/**/*.svg',
    'ui/assets/**/*.mp3',
}

ui_page 'ui/index.html'

dependencies {
    'ox_lib',
}
dependency '/assetpacks'