{"manifest": {"name": "is-extendable", "description": "Returns true if a value is any of the object types: array, regexp, plain object, function or date. This is useful for determining if a value can be extended, e.g. \"can the value have keys?\"", "version": "0.1.1", "homepage": "https://github.com/jonschlinkert/is-extendable", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-extendable.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-extendable/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*"}, "keywords": ["array", "assign", "check", "date", "extend", "extensible", "function", "is", "object", "regex", "test"], "verbiage": {"related": {"list": ["isobject", "is-plain-object", "kind-of", "is-extendable", "is-equal-shallow", "extend-shallow", "assign-deep"]}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-extendable-0.1.1-62b110e289a471418e3ec36a617d472e301dfc89-integrity\\node_modules\\is-extendable\\package.json", "readmeFilename": "README.md", "readme": "# is-extendable [![NPM version](https://badge.fury.io/js/is-extendable.svg)](http://badge.fury.io/js/is-extendable)\n\n> Returns true if a value is any of the object types: array, regexp, plain object, function or date. This is useful for determining if a value can be extended, e.g. \"can the value have keys?\"\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/)\n\n```sh\n$ npm i is-extendable --save\n```\n\n## Usage\n\n```js\nvar isExtendable = require('is-extendable');\n```\n\nReturns true if the value is any of the following:\n\n* `array`\n* `regexp`\n* `plain object`\n* `function`\n* `date`\n* `error`\n\n## Notes\n\nAll objects in JavaScript can have keys, but it's a pain to check for this, since we ether need to verify that the value is not `null` or `undefined` and:\n\n* the value is not a primitive, or\n* that the object is an `object`, `function`\n\nAlso note that an `extendable` object is not the same as an [extensible object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/isExtensible), which is one that (in es6) is not sealed, frozen, or marked as non-extensible using `preventExtensions`.\n\n## Related projects\n\n* [assign-deep](https://github.com/jonschlinkert/assign-deep): Deeply assign the enumerable properties of source objects to a destination object.\n* [extend-shallow](https://github.com/jonschlinkert/extend-shallow): Extend an object with the properties of additional objects. node.js/javascript util.\n* [isobject](https://github.com/jonschlinkert/isobject): Returns true if the value is an object and not an array or null.\n* [is-plain-object](https://github.com/jonschlinkert/is-plain-object): Returns true if an object was created by the `Object` constructor.\n* [is-equal-shallow](https://github.com/jonschlinkert/is-equal-shallow): Does a shallow comparison of two objects, returning false if the keys or values differ.\n* [kind-of](https://github.com/jonschlinkert/kind-of): Get the native type of a value.\n\n## Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm i -d && npm test\n```\n\n## Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/is-extendable/issues/new)\n\n## Author\n\n**Jon Schlinkert**\n\n+ [github/jonschlinkert](https://github.com/jonschlinkert)\n+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n## License\n\nCopyright © 2015 Jon Schlinkert\nReleased under the MIT license.\n\n***\n\n_This file was generated by [verb-cli](https://github.com/assemble/verb-cli) on July 04, 2015._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-extendable/-/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-extendable/-/is-extendable-0.1.1.tgz", "hash": "62b110e289a471418e3ec36a617d472e301dfc89", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "registry": "npm", "packageName": "is-extendable", "cacheIntegrity": "sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw== sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="}, "registry": "npm", "hash": "62b110e289a471418e3ec36a617d472e301dfc89"}