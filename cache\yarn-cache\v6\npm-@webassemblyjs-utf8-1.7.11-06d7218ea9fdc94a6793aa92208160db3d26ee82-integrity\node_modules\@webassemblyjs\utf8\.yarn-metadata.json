{"manifest": {"name": "@webassemblyjs/utf8", "version": "1.7.11", "description": "UTF8 encoder/decoder for WASM", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@webassemblyjs-utf8-1.7.11-06d7218ea9fdc94a6793aa92208160db3d26ee82-integrity\\node_modules\\@webassemblyjs\\utf8\\package.json", "licenseText": "MIT License\n\nCopyright (c) 2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@webassemblyjs/utf8/-/utf8-1.7.11.tgz#06d7218ea9fdc94a6793aa92208160db3d26ee82", "type": "tarball", "reference": "https://registry.yarnpkg.com/@webassemblyjs/utf8/-/utf8-1.7.11.tgz", "hash": "06d7218ea9fdc94a6793aa92208160db3d26ee82", "integrity": "sha512-C6GFkc7aErQIAH+BMrIdVSmW+6HSe20wg57HEC1uqJP8E/xpMjXqQUxkQw07MhNDSDcGpxI9G5JSNOQCqJk4sA==", "registry": "npm", "packageName": "@webassemblyjs/utf8", "cacheIntegrity": "sha512-C6GFkc7aErQIAH+BMrIdVSmW+6HSe20wg57HEC1uqJP8E/xpMjXqQUxkQw07MhNDSDcGpxI9G5JSNOQCqJk4sA== sha1-Btchjqn9yUpnk6qSIIFg2z0m7oI="}, "registry": "npm", "hash": "06d7218ea9fdc94a6793aa92208160db3d26ee82"}