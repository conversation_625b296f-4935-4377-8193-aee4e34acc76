local Translations = {
    error = {
        blips_deactivated             = 'Blips deaktiviert',
        names_deactivated             = 'Namen deaktiviert',
        changed_perm_failed           = 'Wähle eine Gruppe aus!',
        missing_reason                = 'Du musst einen Grund angeben!',
        invalid_reason_length_ban     = 'Du musst einen Grund und die Länge des Bans festlegen!',
        no_store_vehicle_garage       = 'Du kannst dieses Auto nicht in der Garage parken.',
        no_vehicle                    = 'Du bist nicht in einem Fahrzeug.',
        no_weapon                     = 'Du hast keine Waffe in deiner Hand.',
        no_free_seats                 = 'Dieses Fahrzeug hat keinen freien Sitz!',
        failed_vehicle_owner          = 'Das Fahrzeug gehört bereits dir.',
        not_online                    = 'Dieser Spieler ist nicht online.',
        no_receive_report             = 'Du erhältst keine Reports.',
        failed_set_speed              = 'Du hast keine Geschwindigkeit festgelegt.. (`schnell` für Super-Run, `normal` für normal)',
        failed_set_model              = 'Du hast kein Modell festgelegt.',
        failed_entity_copy            = 'Keine Freeaim-Entitätsinfo zum Kopieren in die Zwischenablage!',
    },
    success = {
        blips_activated               = 'Blips aktiviert',
        names_activated               = 'Namen aktiviert',
        coords_copied                 = 'Koordinaten in die Zwischenablage kopiert!',
        heading_copied                = 'Ausrichtung in die Zwischenablage kopiert!',
        changed_perm                  = 'Gruppenrechte geändert',
        entered_vehicle               = 'Ins Fahrzeug eingestiegen',
        success_vehicle_owner         = 'Das Fahrzeug gehört dir jetzt!',
        receive_reports               = 'Du erhältst Reports',
        entity_copy                   = 'Freeaim-Entitätsinfo in die Zwischenablage kopiert!',
        spawn_weapon                  = 'Du hast eine Waffe gespawnt',
        noclip_enabled                = 'No-clip aktiviert',
        noclip_disabled               = 'No-clip deaktiviert',
    },
    info = {
        ped_coords                    = 'Ped-Koordinaten:',
        vehicle_dev_data              = 'Fahrzeug-Entwickler-Daten:',
        ent_id                        = 'Entitäts-ID:',
        net_id                        = 'Netz-ID:',
        net_id_not_registered         = 'Nicht registriert',
        model                         = 'Modell',
        hash                          = 'Hash',
        eng_health                    = 'Motorzustand:',
        body_health                   = 'Karosseriezustand:',
        go_to                         = 'Gehe zu',
        remove                        = 'Entfernen',
        confirm                       = 'Bestätigen',
        reason_title                  = 'Grund',
        length                        = 'Länge',
        options                       = 'Optionen',
        position                      = 'Position',
        your_position                 = 'zu deiner Position',
        open                          = 'Öffnen',
        inventories                   = 'Inventare',
        reason                        = 'Du musst einen Grund angeben',
        give                          = 'Geben',
        id                            = 'ID:',
        player_name                   = 'Spielername',
        obj                           = 'Objekt',
        ammoforthe                    = '+%{value} Munition für die %{weapon}',
        kicked_server                 = 'Du wurdest vom Server gekickt',
        check_discord                 = '🔸 Prüfe unseren Discord für mehr Informationen: ',
        banned                        = 'Du wurdest gebannt:',
        ban_perm                      = '\n\nDein Ban ist permanent.\n🔸 Prüfe unseren Discord für mehr Informationen: ',
        ban_expires                   = '\n\nDer Ban läuft ab: ',
        rank_level                    = 'Dein Berechtigungslevel ist jetzt ',
        admin_report                  = 'Admin-Bericht - ',
        staffchat                     = 'STAFFCHAT - ',
        warning_chat_message          = '^8WARNUNG ^7 Du wurdest gewarnt von',
        warning_staff_message         = '^8WARNUNG ^7 Du hast jemanden gewarnt ',
        no_reason_specified           = 'Kein Grund angegeben',
        server_restart                = 'Server-Neustart, prüfe unseren Discord für mehr Informationen: ',
        entity_view_distance          = 'Sichtweite der Entität auf: %{distance} Meter gesetzt',
        entity_view_info              = 'Entitätsinformationen',
        entity_view_title             = 'Freeaim-Modus',
        entity_freeaim_delete         = 'Entität löschen',
        entity_freeaim_freeze         = 'Entität einfrieren',
        entity_freeaim_coords         = 'Vec3 kopieren',
        coords_copied                 = 'Koordinaten kopiert',
        entity_frozen                 = 'Eingefroren',
        entity_unfrozen               = 'Aufgetaut',
        model_hash                    = 'Modell-Hash:',
        obj_name                      = 'Objektname:',
        ent_owner                     = 'Eigentümer der Entität:',
        cur_health                    = 'Aktuelle Gesundheit:',
        max_health                    = 'Max. Gesundheit:',
        armour                        = 'Rüstung:',
        rel_group                     = 'Beziehungsgruppe:',
        rel_to_player                 = 'Beziehung zum Spieler:',
        rel_group_custom              = 'Benutzerdefinierte Beziehung',
        veh_acceleration              = 'Beschleunigung:',
        veh_cur_gear                  = 'Aktueller Gang:',
        veh_speed_kph                 = 'Geschwindigkeit (km/h):',
        veh_speed_mph                 = 'Geschwindigkeit (mph):',
        veh_rpm                       = 'Drehzahl (U/min):',
        dist_to_obj                   = 'Abstand:',
        obj_heading                   = 'Ausrichtung:',
        obj_coords                    = 'Koordinaten:',
        obj_rot                       = 'Rotation:',
        obj_velocity                  = 'Geschwindigkeit:',
        obj_unknown                   = 'Unbekannt',
        you_have                      = 'Du hast ',
        freeaim_entity                = ' die Freeaim-Entität',
        entity_del                    = 'Entität gelöscht',
        entity_del_error              = 'Fehler beim Löschen der Entität',
    },
    menu = {
        admin_menu                    = 'Admin Menü',
        admin_options                 = 'Admin Optionen',
        online_players                = 'Online Spieler',
        manage_server                 = 'Server verwalten',
        weather_conditions            = 'Vorhandene Wetteroptionen',
        dealer_list                   = 'Dealer Liste',
        ban                           = 'Ban',
        kick                          = 'Kick',
        permissions                    = 'Rechte',
        developer_options             = 'Entwickler Optionen',
        vehicle_options               = 'Fahrzeug Optionen',
        vehicle_categories            = 'Fahrzeugkategorien',
        vehicle_models                = 'Fahrzeugmodelle',
        player_management             = 'Spieler-Management',
        server_management             = 'Server-Management',
        vehicles                      = 'Fahrzeuge',
        noclip                        = 'NoClip',
        revive                        = 'Wiederbeleben',
        invisible                     = 'Unsichtbar',
        god                           = 'Godmode',
        names                         = 'Namen',
        blips                         = 'Blips',
        weather_options               = 'Wetteroptionen',
        server_time                   = 'Serverzeit',
        time                          = 'Zeit',
        copy_vector3                  = 'Kopiere Vector3',
        copy_vector4                  = 'Kopiere Vector4',
        display_coords                = 'Zeige Koordinaten',
        copy_heading                  = 'Kopiere Heading',
        vehicle_dev_mode              = 'Fahrzeug Dev Modus',
        spawn_vehicle                 = 'Fahrzeug spawnen',
        fix_vehicle                   = 'Fahrzeug reparieren',
        buy                           = 'Kaufen',
        remove_vehicle                = 'Fahrzeug entfernen',
        edit_dealer                   = 'Dealer bearbeiten',
        dealer_name                   = 'Dealer Name',
        category_name                 = 'Kategoriename',
        kill                          = 'Töten',
        freeze                        = 'Einfrieren',
        spectate                      = 'Beobachten',
        bring                         = 'Herbringen',
        sit_in_vehicle                = 'Ins Fahrzeug setzen',
        open_inv                      = 'Inventar öffnen',
        give_clothing_menu            = 'Kleidungsmenü geben',
        hud_dev_mode                  = 'Dev Modus (qb-hud)',
        entity_view_options           = 'Entitätsansichtsmodus',
        entity_view_distance          = 'Sichtweite festlegen',
        entity_view_freeaim           = 'Freeaim-Modus',
        entity_view_peds              = 'Fußgänger anzeigen',
        entity_view_vehicles          = 'Fahrzeuge anzeigen',
        entity_view_objects           = 'Objekte anzeigen',
        entity_view_freeaim_copy      = 'Freeaim-Entitätsinformationen kopieren',
        spawn_weapons                 = 'Waffen spawnen',
        max_mods                      = 'Maximale Fahrzeug-Mods',
    },
    desc = {
        admin_options_desc            = 'Verschiedene Admin-Optionen',
        player_management_desc        = 'Siehe Spieler-Optionen',
        server_management_desc        = 'Verschiedene Server-Optionen',
        vehicles_desc                 = 'Fahrzeug-Optionen',
        dealer_desc                   = 'Liste existierender Dealer',
        noclip_desc                   = 'NoClip an/aus',
        revive_desc                   = 'Belebe dich selbst wieder',
        invisible_desc                = 'Unsichtbarkeit an/aus',
        god_desc                      = 'God-Mode an/aus',
        names_desc                    = 'Namen über den Köpfen an/aus',
        blips_desc                    = 'Blips für Spieler auf der Karte an/aus',
        weather_desc                  = 'Wetter ändern',
        developer_desc                = 'Verschiedene Entwickler-Optionen',
        vector3_desc                  = 'Kopiere Vector3 in die Zwischenablage',
        vector4_desc                  = 'Kopiere Vector4 in die Zwischenablage',
        display_coords_desc           = 'Zeige Koordinaten auf dem Bildschirm',
        copy_heading_desc             = 'Kopiere Heading in die Zwischenablage',
        vehicle_dev_mode_desc         = 'Zeige Fahrzeuginformationen',
        delete_laser_desc             = 'Laser an/aus',
        spawn_vehicle_desc            = 'Spawne ein Fahrzeug',
        fix_vehicle_desc              = 'Repariere das Fahrzeug, in dem du sitzt',
        buy_desc                      = 'Kaufe das Fahrzeug kostenlos',
        remove_vehicle_desc           = 'Entferne das nächste Fahrzeug',
        dealergoto_desc               = 'Gehe zum Dealer',
        dealerremove_desc             = 'Entferne den Dealer',
        kick_reason                   = 'Kick-Grund',
        confirm_kick                  = 'Kick bestätigen',
        ban_reason                    = 'Ban-Grund',
        confirm_ban                   = 'Ban bestätigen',
        sit_in_veh_desc               = 'Setze in',
        sit_in_veh_desc2              = "'s Fahrzeug",
        clothing_menu_desc            = 'Gebe das Kleidungsmenü aus',
        hud_dev_mode_desc             = 'Entwickler-Modus aktivieren/deaktivieren',
        entity_view_desc              = 'Informationen über Entitäten anzeigen',
        entity_view_freeaim_desc      = 'Aktiviere/Deaktiviere Entitätsinformationen über Freeaim',
        entity_view_peds_desc         = 'Informationen über Fußgänger in der Welt an/aus',
        entity_view_vehicles_desc     = 'Fahrzeuginformationen in der Welt an/aus',
        entity_view_objects_desc      = 'Objektinformationen in der Welt an/aus',
        entity_view_freeaim_copy_desc = 'Kopiere die Free-Aim-Entitätsinfo in die Zwischenablage',
        spawn_weapons_desc            = 'Spawne eine beliebige Waffe',
        max_mod_desc                  = 'Maximiere die Mods des aktuellen Fahrzeugs',
    },
    time = {
        ban_length                    = 'Ban-Länge',
        onehour                       = '1 Stunde',
        sixhour                       = '6 Stunden',
        twelvehour                    = '12 Stunden',
        oneday                        = '1 Tag',
        threeday                      = '3 Tage',
        oneweek                       = '1 Woche',
        onemonth                      = '1 Monat',
        threemonth                    = '3 Monate',
        sixmonth                      = '6 Monate',
        oneyear                       = '1 Jahr',
        permanent                     = 'Permanent',
        self                          = 'Selbst',
        changed                       = 'Zeit geändert zu %{time} Std 00 Min',
    },
    weather = {
        extra_sunny                   = 'Extra sonnig',
        extra_sunny_desc              = 'Ich schmelze!',
        clear                         = 'Klar',
        clear_desc                    = 'Ein perfekter Tag!',
        neutral                       = 'Neutral',
        neutral_desc                  = 'Nur ein normaler Tag!',
        smog                          = 'Smog',
        smog_desc                     = 'Rauchmaschine!',
        foggy                         = 'Nebel',
        foggy_desc                    = 'Rauchmaschine x2!',
        overcast                      = 'Bedeckt',
        overcast_desc                 = 'Nicht zu sonnig!',
        clouds                        = 'Wolken',
        clouds_desc                   = 'Wo ist die Sonne?',
        clearing                      = 'Aufklarend',
        clearing_desc                 = 'Die Wolken verziehen sich!',
        rain                          = 'Regen',
        rain_desc                     = 'Lass es regnen!',
        thunder                       = 'Gewitter',
        thunder_desc                  = 'Lauf und versteck dich!',
        snow                          = 'Schnee',
        snow_desc                     = 'Ist es kalt hier?',
        blizzard                      = 'Blizzard',
        blizzed_desc                  = 'Schneemaschine?',
        light_snow                    = 'Leichter Schnee',
        light_snow_desc               = 'Weihnachtsgefühle kommen auf!',
        heavy_snow                    = 'Starker Schneefall (XMAS)',
        heavy_snow_desc               = 'Schneeballschlacht!',
        halloween                     = 'Halloween',
        halloween_desc                = 'Was war das für ein Geräusch?!',
        weather_changed               = 'Wetter gewechselt zu: %{value}',
    },
    commands = {
        blips_for_player              = 'Zeige Blips für Spieler (nur Admin)',
        player_name_overhead          = 'Zeige Spielernamen über dem Kopf (nur Admin)',
        coords_dev_command            = 'Aktiviere Koordinatenanzeige für Entwickler (nur Admin)',
        toogle_noclip                 = 'Wechsle in den NoClip-Modus (nur Admin)',
        save_vehicle_garage           = 'Speichere Fahrzeug in deiner Garage (nur Admin)',
        make_announcement             = 'Mach eine Ansage (nur Admin)',
        open_admin                    = 'Öffne Admin-Menü (nur Admin)',
        staffchat_message             = 'Sende Nachricht an alle Staff-Mitglieder (nur Admin)',
        nui_focus                     = 'Gib einem Spieler NUI-Fokus (nur Admin)',
        warn_a_player                 = 'Verwarne einen Spieler (nur Admin)',
        check_player_warning          = 'Prüfe Spielerwarnungen (nur Admin)',
        delete_player_warning         = 'Lösche Spielerwarnungen (nur Admin)',
        reply_to_report               = 'Antworte auf einen Report (nur Admin)',
        change_ped_model              = 'Ändere Ped-Modell (nur Admin)',
        set_player_foot_speed         = 'Setze Spielergeschwindigkeit zu Fuß (nur Admin)',
        report_toggle                 = 'Durchsuche eingehende Reports (nur Admin)',
        kick_all                      = 'Kicke alle Spieler',
        ammo_amount_set               = 'Setze Munitionsstückzahl (nur Admin)',
    }
}

if GetConvar('qb_locale', 'en') == 'de' then
    Lang = Locale:new({
        phrases = Translations,
        warnOnMissing = true,
        fallbackLang = Lang,
    })
end
