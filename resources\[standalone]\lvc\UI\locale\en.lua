local Translations = {
	warning = {
		warning = 'Warning',
		-- WRONG BRANCH NOTIFIER --
		wrong_branch_warning = '~b~LVC~w~: ~o~Warning~w~: This is the development branch (master)',
		wrong_branch_info = '~y~THIS VERSION IS IN DEVELOPMENT AND IS NOT RECOMMENDED\nFOR PRODUCTION USE. IF THIS WAS A MISTAKE DOWNLOAD THE\nLATEST STABLE RELEASE AT:\n~g~github.com/TrevorBarns/luxart-vehicle-control~p~~h~/releases~h~',
		wrong_branch_mute = '~b~TO MUTE THIS~w~: Set CONVAR "~o~experimental~w~" to "~o~true~w~" in fxmanifest.',
		profile_missing = '^3LVC(%{ver}) WARNING: "DEFAULT" table missing from %{tbl} table. Using empty table for %{model}.',
		-- SIREN CONTROLLER --
		too_few_tone_frontend = '~b~LVC ~y~Warning %{code}:~s~ too little sirens assigned.',
		too_few_tone_console = '^3LVC(%{ver}) Warning %{code}: too little sirens assigned. Minimum %{req_tone_count} tones required. (UTIL:SetToneByPos(%{tone_string}, %{pos})',
		tone_position_nil_frontend = '~b~LVC ~y~Warning %{code}:~s~ attempted to set tone but, was unable to locate approved_tones. See console.',
		tone_position_nil_console = '^3LVC(%{ver}) Warning %{code}: attempted to set tone "%{tone_string}" but, was unable to locate approved_tones table. (UTIL:SetToneByPos(%{tone_string}, %{pos}). Try factory resetting as this may occur after siren tone assignments change.',
		tone_id_nil_frontend = '~b~LVC ~y~Warning %{code}:~s~ attempted to set tone but, was unable to locate in approved_tones. See console.',
		tone_id_nil_console = '^3LVC(%{ver}) Warning %{code}: attempted to set tone %{tone_string} but, was unable to locate position: %{tone_id} in approved_tones. (UTIL:SetToneByPos(%{tone_string}, %{pos}). Try factory resetting as this may occur after siren tone assignments change.',
		factory_reset = 'Are you sure you want to delete all saved LVC data and Factory Reset?',
		facory_reset_options= '~g~No: Escape \t ~r~Yes: Enter',
	},
	error = {
		-- CONFIG ERRORS -- 
		missing_community_id_frontend = '~b~~h~LVC~h~ ~r~~h~CONFIG ERROR~h~~s~: COMMUNITY ID MISSING. SEE LOGS. CONTACT SERVER DEVELOPER.',
		missing_community_id_console = '^1CONFIG ERROR: COMMUNITY ID NOT SET, THIS IS REQUIRED TO PREVENT CONFLICTS FOR PLAYERS WHO PLAY ON MULTIPLE SERVERS WITH LVC. PLEASE SET THIS IN SETTINGS.LUA.',
		invalid_resource_name_frontend = '~b~~h~LVC~h~ ~r~~h~CONFIG ERROR~h~~s~: INVALID RESOURCE NAME. SEE LOGS. CONTACT SERVER DEVELOPER.',
		invalid_resource_name_console = '^1CONFIG ERROR: INVALID RESOURCE NAME. PLEASE VERIFY RESOURCE FOLDER NAME READS "^3lvc^1" (CASE-SENSITIVE). THIS IS REQUIRED FOR PROPER SAVE / LOAD FUNCTIONALITY. PLEASE RENAME, REFRESH, AND ENSURE.',
		resource_conflict_frontend = '~b~~h~LVC~h~ ~r~~h~CONFLICT ERROR~h~~s~: RESOURCE CONFLICT. SEE CONSOLE.',
		resource_conflict_console = '^1LVC ERROR: DETECTED "lux_vehcontrol" RUNNING, THIS CONFLICTS WITH LVC. PLEASE STOP "lux_vehcontrol" AND RESTART LVC.',
		profile_none_found_frontend = '~b~~h~LVC~h~ ~r~~h~CONFIG ERROR~h~~s~: DEFAULT TABLE MISSING. SEE LOGS. CONTACT SERVER DEVELOPER.',
		profile_none_found_console = '^1CONFIG ERROR: UNABLE TO FIND A PROFILE FOR \'^3%{game_name}^1\', AND REQUIRED FALLBACK TABLE \'DEFAULT\' IS NOT PRESENT. (https://bit.ly/LVC-CSATS)',

		-- FRONTEND ERRORS --
		reg_keymap_nil_1 = '~b~~h~LVC~h~ ~r~~h~ERROR 2~h~~s~: Nil value caught.\ndetails: (%{i}, %{proposed_tone}, %{profile_name})',
		reg_keymap_nil_2 = '~b~~h~LVC~h~ ~r~~h~ERROR 2~h~~s~: Try switching vehicles and switching back OR loading profile settings (if save present).',
		profile_nil_table_frontend = '~b~~h~LVC~h~ ~r~ERROR: %{tbl} attempted to get profile from nil table. See console.',
		profile_nil_table_console = '^1LVC(%{ver}) ERROR: %{tbl} attempted to get profile from nil table. This is typically caused by an invalid character or missing { } brace in SIRENS.lua. (https://git.io/JDVhK)',
	},
	info = {
		locked = 'Siren Control Box: ~r~Locked',
		unlocked = 'Siren Control Box: ~g~Unlocked',
		debug_mode_frontend = '~y~~h~Info:~h~ ~s~debug mode set to %{state}. See console.',
		debug_mode_console = '^3LVC Info: debug mode set to %{state} temporarily. Debug_mode resets after resource restart unless set in fxmanifest. Make sure to run "refresh" to see fxmanifest changes.',
		profile_found = '^4LVC(%{ver}) ^5%{tbl}: ^7profile %{profile} found for %{model}.',
		disabled_profile = '^4LVC(%{ver}) ^5%{tbl}: ^1LVC disabled for %{model} using profile %{profile}.',
		profile_default_console = '^4LVC(%{ver}) ^5%{tbl}: ^7using default profile for %{model}.',
		profile_default_frontend = '~b~LVC~s~: Using ~b~DEFAULT~s~ profile for \'~o~ %{model} ~s~\'.',
		extra_on = '^4LVC: ^7Toggling %{extra} on',
		extra_off = '^4LVC: ^7Toggling %{extra} off',
		unknown = 'unknown',
		unable_to_disable = '~y~~h~Info:~h~ ~s~Luxart Vehicle Control\nAction prohibited, cannot disable all sirens.',
		factory_reset_success_console = 'Success: cleared all save data.',
		factory_reset_success_frontend = '~g~Success~s~: You have deleted all save data and reset LVC.',
	},
	control = {
		siren_control_desc = 'LVC Siren: %{ord_num}',
		lock_desc = 'LVC: Lock out controls',
		menu_desc = 'LVC: Open Menu',
	},
	command = {
		lock_command = 'lvclock',
		lock_desc = 'Toggle Luxart Vehicle Control Key Binding Lockout.',
		debug_command = 'lvcdebug',
		
	},
	menu = {
		-- MENU SUBTITLES --
		main = 'Main Menu',
		siren = 'Main Siren Settings',
		siren_desc = 'Change which/how each available primary tone is used.',
		hud = 'HUD Settings',
		hud_desc = 'Open HUD settings menu.',
		audio = 'Audio Settings',
		audio_desc = 'Open audio settings menu.',
		plugins = 'Plugins',
		plugins_desc = 'Open Plugins Menu.',
		storage = 'Storage Management',
		storage_desc = 'Save / Load vehicle profiles.',
		copy = 'Copy Profile Settings',
		more_info = 'More Information',
		more_info_desc = 'Learn more about Luxart Vehicle Control.',
		--------------------
		airhorn_interrupt = 'Airhorn Interrupt Mode',
		airhorn_interrupt_desc = 'Toggles whether the airhorn interrupts main siren.',
		reset_standby = 'Reset to Standby',
		reset_standby_desc = '~g~Enabled~s~, the primary siren will reset to 1st siren on siren toggle. ~r~Disabled~s~, the last played tone will resume on siren toggle.',
		--------------------
		primary_manu = 'Primary Manual Tone',
		primary_manu_desc =  'Change your primary manual tone.',
		secondary_manu = 'Secondary Manual Tone',
		secondary_manu_desc =  'Change your secondary manual tone.',
		aux_tone = 'Auxiliary Siren Tone',
		aux_tone_desc = 'Change your auxiliary/dual siren tone.',
		siren_park_kill ='Siren Park Kill',
		siren_park_kill_desc = 'Toggles whether your sirens turn off automatically when you exit your vehicle.',
		--------------------
		siren_settings_seperator = 'Siren Settings',
		other_settings_seperator = 'Other Settings',
		misc_settings_seperator = 'Miscellaneous',
		tone_options_seperator = 'Tone Options',
		audio_sfx_separator = 'SoundFX Settings',
		advanced_separator = 'Advanced Settings',
		--------------------
		cycle_button = 'Cycle & Button', 
		cycle_only = 'Cycle Only',
		button_only = 'Button Only', 
		enabled = 'Enabled',
		disabled = 'Disabled',
		tone_options_desc = '~g~Cycle:~s~ play as you cycle through sirens.\n~g~Button:~s~ play when registered key is pressed.\n~b~Select to rename siren tones.',
		--------------------
		hud_enabled_desc = 'Toggles whether HUD is displayed. Requires GTA V HUD to be enabled.',
		hud_move_mode = 'Move Mode',
		hud_move_mode_desc = 'Move HUD position on screen. To exit ~r~right-click~s~ or hit "~r~Esc~s~".',
		hud_scale = 'Scale',
		hud_scale_desc = 'Change scale of the HUD.',
		hud_backlight = 'Backlight',
		hud_backlight_auto = 'Auto', 
		hud_backlight_off = 'Off',
		hud_backlight_on = 'On',
		hud_backlight_desc = 'Changes HUD backlight behavior. ~b~Auto~s~ is determined by headlight state.',
		hud_reset = 'Reset',
		hud_reset_desc =  'Reset HUD position to default.',
		--------------------
		audio_radio = 'Radio Controls',
		audio_radio_desc = 'When enabled, the tilde key will act as a radio wheel key.',
		audio_scheme = 'Siren Box Scheme',
		audio_scheme_desc = 'Change what SFX to use for siren box clicks.',
		audio_manu_sfx = 'Manual Button Clicks',
		audio_manu_sfx_desc = 'When enabled, your manual tone button will activate the upgrade SFX.',
		audio_horn_sfx = 'Airhorn Button Clicks',
		audio_horn_sfx_desc = 'When enabled, your airhorn button will activate the upgrade SFX.',
		audio_activity_reminder = 'Activity Reminder',
		audio_activity_reminder_desc = 'Receive reminder tone that your lights are on. Options are in minutes. Timer (sec): %{timer}',
		audio_volumes = 'Adjust Volumes',
		audio_volumes_desc = 'Open volume settings menu.',
		on_volume = 'On Volume',
		on_volume_desc = 'Set volume of light slider / button. Plays when lights are turned ~g~on~s~. Press Enter to play the sound.',
		off_volume = 'Off Volume',
		off_volume_desc = 'Set volume of light slider / button. Plays when lights are turned ~r~off~s~. Press Enter to play the sound.',
		upgrade_volume = 'Upgrade Volume',
		upgrade_volume_desc = 'Set volume of siren button. Plays when siren is turned ~g~on~s~. Press Enter to play the sound.',
		downgrade_volume = 'Downgrade Volume',
		downgrade_volume_desc = 'Set volume of siren button. Plays when siren is turned ~r~off~s~. Press Enter to play the sound.',
		reminder_volume = 'Activity Reminder Volume',
		reminder_volume_desc = 'Set volume of activity reminder tone. Plays when lights are ~g~on~s~, siren is ~r~off~s~, and timer is has finished. Press Enter to play the sound.',
		hazards_volume = 'Hazards Volume',
		hazards_volume_desc = 'Set volume of hazards button. Plays when hazards are toggled. Press Enter to play the sound.',
		lock_volume = 'Lock Volume',
		lock_volume_desc = 'Set volume of lock notification sound. Plays when siren box lockout is toggled. Press Enter to play the sound.',
		lock_reminder_volume = 'Lock Reminder Volume',
		lock_reminder_volume_desc = 'Set volume of lock reminder sound. Plays when locked out keys are pressed repeatedly. Press Enter to play the sound.',
		--------------------
		confirm = 'Are you sure?',
		save = 'Save Settings',
		save_desc = 'Save LVC settings.',
		save_success = '~g~Success~s~: Your settings have been saved.',
		save_override = 'Are you sure?',
		save_override_desc =  '~r~This will override any existing save data for this vehicle profile (%{profile}).',
		load = 'Load Settings',
		load_desc =  'Load LVC settings.',
		load_success = '~g~Success~s~: Your settings have been loaded.',
		load_override = '~r~This will override any unsaved settings.',
		copy = 'Copy Settings',
		copy_desc = 'Copy profile settings from another vehicle.',
		reset = 'Reset Settings',
		reset_desc = '~r~Reset LVC to it\'s default state, preserves existing saves. Will override any unsaved settings.',
		reset_success = '~g~Success~s~: Settings have been reset.',
		factory_reset = 'Factory Reset',
		factory_reset_desc = '~r~Permanently delete any saves, resetting LVC to its default state.',
		load_copy = 'Load',
		load_copy_desc = 'Attempt to load settings from profile \'~b~%{profile}~s~\'.',
		--------------------
		storage_default_profile_msg = 'Using ~b~DEFAULT~s~ profile for \'~b~%{veh}~s~\'.',
		rename_tone = 'Enter new tone name for %{tone_string}:',
		--------------------
		current_version = 'Current Version',
		version_string = 'This server is running %{ver}%{ver_desc}',
		latest_version = 'Latest Version',
		latest_update_desc =  'The latest update is %{ver}.',
		latest_version_desc = ', the latest version.',
		old_version_desc = ', an out-of-date version.',
		experimental_version_desc = ', an ~y~experimental~s~ version.',
		unknown_version_desc = ', the latest version could not be determined.',
		about_credits = 'About / Credits',
		about_credits_desc = 'Originally designed and created by ~b~Lt. Caine~s~. ELS sound effects by ~b~Faction~s~. Version 3 expansion by ~b~Trevor Barns~s~.\n\nSpecial thanks to all contributors (see GitHub), the RageUI team, and everyone else who helped beta test, this would not have been possible without you all!',
	},
	plugins ={
		menu_tkd = 'Takedown Settings',
		menu_tkd_desc = 'Open takedown lights menu. (takedowns)',
		menu_ei = 'Extra Integration Settings',
		menu_ei_desc = 'Open extra integration menu. (extra_integration)',
		menu_ta = 'Traffic Advisor Settings',
		menu_ta_desc = 'Open traffic advisor menu. (traffic_advisor)',
		menu_ts = 'Trailer Support Settings',
		menu_ts_desc = 'Open trailer support settings menu. (trailer_support)',
		menu_ec = 'Extra Controls Settings',
		menu_ec_desc = 'Open extra controls settings menu. (extra_controls)',
		-------------------
		ec_shortcuts_separator = 'Shortcuts',
		ec_shortcut_prefix_change = 'Change',
		ec_shortcut_prefix_view = 'View',
		ec_shortcut_desc = '%{prefix} shortcut settings.',
		ec_enabled_desc = 'Toggle extra controls functionality.',
		ec_save = 'Save Profile Controls',
		ec_save_desc = 'Store new controls to client-side storage (KVP).',
		ec_load = 'Load Profile Controls',
		ec_load_desc = 'Load saved controls from client-side storage (KVP).',
		ec_reset = 'Reset Profile Controls',
		ec_reset_desc = '~r~Reset this profiles controls to default, preserves existing saves. Will override any unsaved settings.',
		ec_factory_reset = 'Delete All Profile Controls',
		ec_factory_reset_desc = '~r~Delete all Extra Controls saved data from client-side storage (KVP).',
		ec_factory_reset_success_console = 'Success: cleared all extra controls data.',
		ec_factory_reset_success_frontend = '~g~Success~s~: You have deleted all extra controls data and reset the plugin.',
		ec_no_shortcuts = '(None)',
		ec_no_shortcuts_desc = 'No shortcuts found.',
		ec_combo = 'Combo',
		ec_combo_desc = 'Control that needs to be pressed in addition to key to toggle extras. ~m~Format: (KEYBOARD | CONTROLLER)',
		ec_key = 'Key',
		ec_key_desc = 'Control that needs to be pressed in addition to combo-key to toggle extras. ~m~Format: (KEYBOARD | CONTROLLER)',
		ec_controller_support = 'Controller Support',
		ec_controller_support_desc = 'When disabled, LVC ignores controller button input. Requires combo-key to be None',
		ec_not_approved_console = '^3LVC Warning P404: attempted to use control %{control} but, was unable to locate CONTROLS.%{type} table. Try factory resetting or report to server developer.',
		ec_not_approved_frontend = '~b~LVC ~y~Warning P404:~s~ attempted to use control but was not approved. See console.',
		ec_fail_load_console = '^3LVC Warning:  The saved control for \'${name}\' is no longer permitted by server developer. Reverting to default. Re-save control profile to remove this error. CONTROL: %{control}',
		ec_fail_load_frontend = '~b~LVC ~y~Warning: Unable to load control for \'%{name}\'. See console.',
		ec_save_not_used = '^3LVC Info: found save data that did not align with current Extra Controls configuration. Likely old data that has since been changed by a server developer. You can delete this by re-saving.',
		-------------------
		ei_blackout = 'Blackout',
		ei_blackout_desc = 'Disabled auto brake lights on stop.',
		ei_auto_park = 'Auto Park Mode',
		ei_auto_park_desc = 'How long after being stopped to disable auto brake lights and put vehicle in "park". Options are in minutes. Timer (sec): %{timer}',
		ei_control_desc = 'LVC Toggle Blackout',
		ei_command_desc = 'Toggle LVC Blackout Mode.',
		ei_invalid_extra = '^3LVC Info: EXTRA_INTEGRATION table contains non-existent extra: %{extra} for %{profile}.',
		-------------------
		tkd_integration = 'Integration',
		tkd_integration_desc = 'Determines whether high-beams will auto toggle take-downs or visa versa.',
		tkd_integration_off = 'Off',
		tkd_integration_set_highbeam = 'TKDs Set High-beams', 
		tkd_integration_highbeam_set_tkd = 'High-beams Set TKDs',
		tkd_position = 'Position',
		tkd_position_desc = 'Position',
		tkd_intensity = 'Intensity',
		tkd_intensity_desc = 'Set brightness/intensity of take-downs.',
		tkd_radius = 'Radius',
		tkd_radius_desc = 'Set width of take-downs.',
		tkd_distance = 'Distance',
		tkd_distance_desc = 'Set the max distance the take-downs can travel.',
		tkd_falloff = 'Falloff',
		tkd_falloff_desc = 'Set how fast light "falls off" or appears dim.',
		-------------------
		ta_pattern = 'TA HUD Pattern',
		ta_pattern_desc = 'Change pattern displayed on HUD traffic advisor indicators.',
		ta_save = 'Save TA State',
		ta_save_desc = 'Preserves traffic advisor state on lights toggling. Unchecking this will turn TA extras off when lights are turned off.',
		ta_sync = 'Sync TA State',
		ta_sync_desc = '~o~Coming Soon~c ~ When able, sync TA state to nearby vehicles.',
		ta_control_desc_left = 'LVC Toggle Left TA',
		ta_control_desc_right = 'LVC Toggle Right TA',
		ta_control_desc_middle = 'LVC Toggle Middle TA',
		-------------------
		ts_menu_extras = 'Trailer Extras',
		ts_menu_doors = 'Trailer Doors',
		ts_menu_extras_button = 'Extras Menu',
		ts_menu_extras_desc = 'Open menu to toggle trailer extra states.',
		ts_menu_doors_button = 'Doors Menu',
		ts_menu_doors_desc = 'Open menu to open / close doors.',
		ts_door_fl = 'Left Front Door', 
		ts_door_fr = 'Right Front Door',
		ts_door_rl = 'Left Rear Door', 
		ts_door_rr = 'Right Rear Door', 
		ts_door_hood = 'Hood', 
		ts_door_trunk = 'Trunk',
		ts_door_extra1 = 'Extra #1', 
		ts_door_extra2 = 'Extra #2', 
		ts_door_bombbay = 'Bomb Bay', 
		ts_current = 'Current Trailer',
		ts_current_desc = 'Current detected trailer attached.',
		ts_shortcut_desc = 'Trigger shortcut %{shortcut}',
		ts_shortcut_separator = 'Shortcuts',
		ts_submenus_separator = 'Submenus',
		ts_truck_separator = 'Cab/Truck',
		ts_trailer_separator = 'Trailer',
		ts_extra = 'Extra #%{extra}',
		ts_extra_desc = 'Toggle extra #%{extra}',
		ts_door_desc = 'Open / close %{door}.',
		ts_not_found = 'NOT FOUND',
	},
}

Lang = Locale:new({
	phrases = Translations,
	warnOnMissing = true
})