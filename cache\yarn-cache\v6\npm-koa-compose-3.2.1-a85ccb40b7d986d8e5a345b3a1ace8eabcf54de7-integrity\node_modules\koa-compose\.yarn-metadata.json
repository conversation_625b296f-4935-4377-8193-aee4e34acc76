{"manifest": {"name": "koa-compose", "description": "compose Koa middleware", "repository": {"type": "git", "url": "https://github.com/koajs/compose.git"}, "version": "3.2.1", "keywords": ["koa", "middleware", "compose"], "files": ["index.js"], "dependencies": {"any-promise": "^1.1.0"}, "devDependencies": {"co": "^4.6.0", "istanbul": "^0.4.2", "matcha": "^0.7.0", "mocha": "^3.1.2", "should": "^2.0.0", "standard": "^8.4.0"}, "scripts": {"bench": "matcha bench/bench.js", "lint": "standard index.js test/*.js", "test": "mocha --require should --reporter spec", "test-cov": "node ./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --require should", "test-travis": "node ./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --require should"}, "publishConfig": {"tag": "next"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-koa-compose-3.2.1-a85ccb40b7d986d8e5a345b3a1ace8eabcf54de7-integrity\\node_modules\\koa-compose\\package.json", "readmeFilename": "Readme.md", "readme": "\n# koa-compose\n\n[![NPM version][npm-image]][npm-url]\n[![Build status][travis-image]][travis-url]\n[![Test coverage][codecov-image]][codecov-url]\n[![Dependency Status][david-image]][david-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n Compose middleware.\n\n## Installation\n\n```js\n$ npm install koa-compose\n```\n\n## API\n\n### compose([a, b, c, ...])\n\n  Compose the given middleware and return middleware.\n\n## License\n\n  MIT\n\n[npm-image]: https://img.shields.io/npm/v/koa-compose.svg?style=flat-square\n[npm-url]: https://npmjs.org/package/koa-compose\n[travis-image]: https://img.shields.io/travis/koajs/compose/next.svg?style=flat-square\n[travis-url]: https://travis-ci.org/koajs/compose\n[codecov-image]: https://img.shields.io/codecov/c/github/koajs/compose/next.svg?style=flat-square\n[codecov-url]: https://codecov.io/github/koajs/compose\n[david-image]: http://img.shields.io/david/koajs/compose.svg?style=flat-square\n[david-url]: https://david-dm.org/koajs/compose\n[license-image]: http://img.shields.io/npm/l/koa-compose.svg?style=flat-square\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/koa-compose.svg?style=flat-square\n[downloads-url]: https://npmjs.org/package/koa-compose\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/koa-compose/-/koa-compose-3.2.1.tgz#a85ccb40b7d986d8e5a345b3a1ace8eabcf54de7", "type": "tarball", "reference": "https://registry.yarnpkg.com/koa-compose/-/koa-compose-3.2.1.tgz", "hash": "a85ccb40b7d986d8e5a345b3a1ace8eabcf54de7", "integrity": "sha1-qFzLQLfZhtjlo0Wzoazo6rz1Tec=", "registry": "npm", "packageName": "koa-compose", "cacheIntegrity": "sha512-8gen2cvKHIZ35eDEik5WOo8zbVp9t4cP8p4hW4uE55waxolLRexKKrqfCpwhGVppnB40jWeF8bZeTVg99eZgPw== sha1-qFzLQLfZhtjlo0Wzoazo6rz1Tec="}, "registry": "npm", "hash": "a85ccb40b7d986d8e5a345b3a1ace8eabcf54de7"}