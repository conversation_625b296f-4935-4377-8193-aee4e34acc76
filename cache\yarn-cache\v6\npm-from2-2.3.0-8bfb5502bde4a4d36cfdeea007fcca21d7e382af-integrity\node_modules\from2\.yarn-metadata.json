{"manifest": {"name": "from2", "description": "Convenience wrapper for ReadableStream, with an API lifted from \"from\" and \"through2\"", "version": "2.3.0", "main": "index.js", "scripts": {"test": "node test"}, "dependencies": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}, "devDependencies": {"tape": "^4.0.0"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://hughsk.io/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/hughsk/from2"}, "bugs": {"url": "https://github.com/hughsk/from2/issues"}, "homepage": "https://github.com/hughsk/from2", "keywords": ["from", "stream", "readable", "pull", "convenience", "wrapper"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-from2-2.3.0-8bfb5502bde4a4d36cfdeea007fcca21d7e382af-integrity\\node_modules\\from2\\package.json", "readmeFilename": "README.md", "readme": "# from2 [![Flattr this!](https://api.flattr.com/button/flattr-badge-large.png)](https://flattr.com/submit/auto?user_id=hughskennedy&url=http://github.com/hughsk/from2&title=from2&description=hughsk/from2%20on%20GitHub&language=en_GB&tags=flattr,github,javascript&category=software)[![experimental](http://hughsk.github.io/stability-badges/dist/experimental.svg)](http://github.com/hughsk/stability-badges) #\n\n`from2` is a high-level module for creating readable streams that properly handle backpressure.\n\nConvience wrapper for\n[readable-stream](http://github.com/isaacs/readable-stream)'s `ReadableStream`\nbase class, with an API lifted from\n[from](http://github.com/dominictarr/from) and\n[through2](http://github.com/rvagg/through2).\n\n## Usage ##\n\n[![from2](https://nodei.co/npm/from2.png?mini=true)](https://nodei.co/npm/from2)\n\n### `stream = from2([opts], read)` ###\n\nWhere `opts` are the options to pass on to the `ReadableStream` constructor,\nand `read(size, next)` is called when data is requested from the stream.\n\n* `size` is the recommended amount of data (in bytes) to retrieve.\n* `next(err)` should be called when you're ready to emit more data.\n\nFor example, here's a readable stream that emits the contents of a given\nstring:\n\n``` javascript\nvar from = require('from2')\n\nfunction fromString(string) {\n  return from(function(size, next) {\n    // if there's no more content\n    // left in the string, close the stream.\n    if (string.length <= 0) return next(null, null)\n\n    // Pull in a new chunk of text,\n    // removing it from the string.\n    var chunk = string.slice(0, size)\n    string = string.slice(size)\n\n    // Emit \"chunk\" from the stream.\n    next(null, chunk)\n  })\n}\n\n// pipe \"hello world\" out\n// to stdout.\nfromString('hello world').pipe(process.stdout)\n```\n\n### `stream = from2.obj([opts], read)` ###\n\nShorthand for `from2({ objectMode: true }, read)`.\n\n### `createStream = from2.ctor([opts], read)` ###\n\nIf you're creating similar streams in quick succession you can improve\nperformance by generating a stream **constructor** that you can reuse instead\nof creating one-off streams on each call.\n\nTakes the same options as `from2`, instead returning a constructor which you\ncan use to create new streams.\n\n### See Also\n\n- [from2-array](https://github.com/binocarlos/from2-array) - Create a from2 stream based on an array of source values.\n- [from2-string](https://github.com/yoshuawuyts/from2-string) - Create a stream from a string. Sugary wrapper around from2.\n\n## License ##\n\nMIT. See [LICENSE.md](http://github.com/hughsk/from2/blob/master/LICENSE.md) for details.\n", "licenseText": "## The MIT License (MIT) ##\n\nCopyright (c) 2014 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/from2/-/from2-2.3.0.tgz#8bfb5502bde4a4d36cfdeea007fcca21d7e382af", "type": "tarball", "reference": "https://registry.yarnpkg.com/from2/-/from2-2.3.0.tgz", "hash": "8bfb5502bde4a4d36cfdeea007fcca21d7e382af", "integrity": "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=", "registry": "npm", "packageName": "from2", "cacheIntegrity": "sha512-OMcX/4IC/uqEPVgGeyfN22LJk6AZrMkRZHxcHBMBvHScDGgwTm2GT2Wkgtocyd3JfZffjj2kYUDXXII0Fk9W0g== sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8="}, "registry": "npm", "hash": "8bfb5502bde4a4d36cfdeea007fcca21d7e382af"}