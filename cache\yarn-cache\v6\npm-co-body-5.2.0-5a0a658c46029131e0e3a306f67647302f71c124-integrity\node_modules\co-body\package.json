{"name": "co-body", "version": "5.2.0", "repository": "cojs/co-body", "description": "request body parsing for co", "keywords": ["request", "parse", "parser", "json", "co", "generators", "u<PERSON><PERSON><PERSON>"], "dependencies": {"inflation": "^2.0.0", "qs": "^6.4.0", "raw-body": "^2.2.0", "type-is": "^1.6.14"}, "devDependencies": {"istanbul": "^0.4.5", "koa": "^1.2.5", "mocha": "^3.2.0", "safe-qs": "^6.0.1", "should": "^11.2.0", "supertest": "^1.0.1"}, "license": "MIT", "scripts": {"test": "make test", "test-cov": "make test-cov"}, "files": ["index.js", "lib/"]}