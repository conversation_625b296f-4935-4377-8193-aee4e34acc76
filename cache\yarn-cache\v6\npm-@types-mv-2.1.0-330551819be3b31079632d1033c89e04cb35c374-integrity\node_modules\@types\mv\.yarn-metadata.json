{"manifest": {"name": "@types/mv", "version": "2.1.0", "description": "TypeScript definitions for mv", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/nenadalm"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "c8b5cd5c60404bcc346fa5a9fa1728c9575a9fed35f3af18ffba476dfa770cc4", "typeScriptVersion": "2.0", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-mv-2.1.0-330551819be3b31079632d1033c89e04cb35c374-integrity\\node_modules\\@types\\mv\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/mv`\n\n# Summary\nThis package contains type definitions for mv (https://github.com/andrewrk/node-mv).\n\n# Details\nFiles were exported from https://www.github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mv\n\nAdditional Details\n * Last updated: Tue, 29 Aug 2017 21:31:37 GMT\n * Dependencies: none\n * Global values: none\n\n# Credits\nThese definitions were written by <PERSON><PERSON> <https://github.com/nenadalm>.\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation. All rights reserved.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/mv/-/mv-2.1.0.tgz#330551819be3b31079632d1033c89e04cb35c374", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/mv/-/mv-2.1.0.tgz", "hash": "330551819be3b31079632d1033c89e04cb35c374", "integrity": "sha512-9uDB9lojfIQipxfI308//b4c5isHs6uMo3kIMzv73FPdXmMnAk6iELHGI849cuuDPHy6aXBwN/q9gMzjRyhJ+w==", "registry": "npm", "packageName": "@types/mv", "cacheIntegrity": "sha512-9uDB9lojfIQipxfI308//b4c5isHs6uMo3kIMzv73FPdXmMnAk6iELHGI849cuuDPHy6aXBwN/q9gMzjRyhJ+w== sha1-MwVRgZvjsxB5Yy0QM8ieBMs1w3Q="}, "registry": "npm", "hash": "330551819be3b31079632d1033c89e04cb35c374"}