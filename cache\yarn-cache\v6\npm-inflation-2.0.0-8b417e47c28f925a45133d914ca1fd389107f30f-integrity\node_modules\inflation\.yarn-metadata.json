{"manifest": {"name": "inflation", "description": "Easily unzip an HTTP stream", "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/stream-utils/inflation.git"}, "keywords": ["decompress", "unzip", "inflate", "zlib", "gunzip"], "devDependencies": {"istanbul": "0.2.10", "mocha": "~1.20.1", "readable-stream": "~1.0.27", "should": "4.0.4"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "files": ["index.js"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-inflation-2.0.0-8b417e47c28f925a45133d914ca1fd389107f30f-integrity\\node_modules\\inflation\\package.json", "readmeFilename": "README.md", "readme": "# inflation\n\n[![NPM version](https://badge.fury.io/js/inflation.svg)](http://badge.fury.io/js/inflation)\n[![Build Status](https://travis-ci.org/stream-utils/inflation.svg?branch=master)](https://travis-ci.org/stream-utils/inflation)\n[![Coverage Status](https://img.shields.io/coveralls/stream-utils/inflation.svg?branch=master)](https://coveralls.io/r/stream-utils/inflation)\n\nAutomatically unzip an HTTP stream.\n\n## API\n\n```js\nvar inflate = require('inflation')\n```\n\n### inflate(stream, options)\n\nReturns a stream that emits inflated data from the given stream.\n\nOptions:\n\n- `encoding` - The encoding of the stream (`gzip` or `deflate`).\n  If not given, will look in `stream.headers['content-encoding']`.\n\n## Example\n\n```js\nvar inflate = require('inflation')\nvar raw     = require('raw-body')\n\nhttp.createServer(function (req, res) {\n  raw(inflate(req), 'utf-8', function (err, string) {\n    console.dir(string)\n  })\n})\n```\n", "licenseText": "\nThe MIT License (MIT)\n\nCopyright (c) 2014 <NAME_EMAIL>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/inflation/-/inflation-2.0.0.tgz#8b417e47c28f925a45133d914ca1fd389107f30f", "type": "tarball", "reference": "https://registry.yarnpkg.com/inflation/-/inflation-2.0.0.tgz", "hash": "8b417e47c28f925a45133d914ca1fd389107f30f", "integrity": "sha1-i0F+R8KPklpFEz2RTKH9OJEH8w8=", "registry": "npm", "packageName": "inflation", "cacheIntegrity": "sha512-m3xv4hJYR2oXw4o4Y5l6P5P16WYmazYof+el6Al3f+YlggGj6qT9kImBAnzDelRALnP5d3h4jGBPKzYCizjZZw== sha1-i0F+R8KPklpFEz2RTKH9OJEH8w8="}, "registry": "npm", "hash": "8b417e47c28f925a45133d914ca1fd389107f30f"}