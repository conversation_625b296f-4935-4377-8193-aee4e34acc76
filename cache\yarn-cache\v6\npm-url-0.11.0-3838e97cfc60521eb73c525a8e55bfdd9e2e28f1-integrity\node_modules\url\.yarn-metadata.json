{"manifest": {"name": "url", "description": "The core `url` packaged standalone for use with Browserify.", "version": "0.11.0", "dependencies": {"punycode": "1.3.2", "querystring": "0.2.0"}, "main": "./url.js", "devDependencies": {"assert": "1.1.1", "mocha": "1.18.2", "zuul": "3.3.0"}, "scripts": {"test": "mocha --ui qunit test.js && zuul -- test.js", "test-local": "zuul --local -- test.js"}, "repository": {"type": "git", "url": "https://github.com/defunctzombie/node-url.git"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-url-0.11.0-3838e97cfc60521eb73c525a8e55bfdd9e2e28f1-integrity\\node_modules\\url\\package.json", "readmeFilename": "README.md", "readme": "# node-url\n\n[![Build Status](https://travis-ci.org/defunctzombie/node-url.svg?branch=master)](https://travis-ci.org/defunctzombie/node-url)\n\nThis module has utilities for URL resolution and parsing meant to have feature parity with node.js core [url](http://nodejs.org/api/url.html) module.\n\n```js\nvar url = require('url');\n```\n\n## api\n\nParsed URL objects have some or all of the following fields, depending on\nwhether or not they exist in the URL string. Any parts that are not in the URL\nstring will not be in the parsed object. Examples are shown for the URL\n\n`'http://user:<EMAIL>:8080/p/a/t/h?query=string#hash'`\n\n* `href`: The full URL that was originally parsed. Both the protocol and host are lowercased.\n\n    Example: `'http://user:<EMAIL>:8080/p/a/t/h?query=string#hash'`\n\n* `protocol`: The request protocol, lowercased.\n\n    Example: `'http:'`\n\n* `host`: The full lowercased host portion of the URL, including port\n  information.\n\n    Example: `'host.com:8080'`\n\n* `auth`: The authentication information portion of a URL.\n\n    Example: `'user:pass'`\n\n* `hostname`: Just the lowercased hostname portion of the host.\n\n    Example: `'host.com'`\n\n* `port`: The port number portion of the host.\n\n    Example: `'8080'`\n\n* `pathname`: The path section of the URL, that comes after the host and\n  before the query, including the initial slash if present.\n\n    Example: `'/p/a/t/h'`\n\n* `search`: The 'query string' portion of the URL, including the leading\n  question mark.\n\n    Example: `'?query=string'`\n\n* `path`: Concatenation of `pathname` and `search`.\n\n    Example: `'/p/a/t/h?query=string'`\n\n* `query`: Either the 'params' portion of the query string, or a\n  querystring-parsed object.\n\n    Example: `'query=string'` or `{'query':'string'}`\n\n* `hash`: The 'fragment' portion of the URL including the pound-sign.\n\n    Example: `'#hash'`\n\nThe following methods are provided by the URL module:\n\n### url.parse(urlStr, [parseQueryString], [slashesDenoteHost])\n\nTake a URL string, and return an object.\n\nPass `true` as the second argument to also parse\nthe query string using the `querystring` module.\nDefaults to `false`.\n\nPass `true` as the third argument to treat `//foo/bar` as\n`{ host: 'foo', pathname: '/bar' }` rather than\n`{ pathname: '//foo/bar' }`. Defaults to `false`.\n\n### url.format(urlObj)\n\nTake a parsed URL object, and return a formatted URL string.\n\n* `href` will be ignored.\n* `protocol` is treated the same with or without the trailing `:` (colon).\n  * The protocols `http`, `https`, `ftp`, `gopher`, `file` will be\n    postfixed with `://` (colon-slash-slash).\n  * All other protocols `mailto`, `xmpp`, `aim`, `sftp`, `foo`, etc will\n    be postfixed with `:` (colon)\n* `auth` will be used if present.\n* `hostname` will only be used if `host` is absent.\n* `port` will only be used if `host` is absent.\n* `host` will be used in place of `hostname` and `port`\n* `pathname` is treated the same with or without the leading `/` (slash)\n* `search` will be used in place of `query`\n* `query` (object; see `querystring`) will only be used if `search` is absent.\n* `search` is treated the same with or without the leading `?` (question mark)\n* `hash` is treated the same with or without the leading `#` (pound sign, anchor)\n\n### url.resolve(from, to)\n\nTake a base URL, and a href URL, and resolve them as a browser would for\nan anchor tag.  Examples:\n\n    url.resolve('/one/two/three', 'four')         // '/one/two/four'\n    url.resolve('http://example.com/', '/one')    // 'http://example.com/one'\n    url.resolve('http://example.com/one', '/two') // 'http://example.com/two'\n", "licenseText": "The MIT License (MIT)\n\nCopyright Joyent, Inc. and other Node contributors.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/url/-/url-0.11.0.tgz#3838e97cfc60521eb73c525a8e55bfdd9e2e28f1", "type": "tarball", "reference": "https://registry.yarnpkg.com/url/-/url-0.11.0.tgz", "hash": "3838e97cfc60521eb73c525a8e55bfdd9e2e28f1", "integrity": "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=", "registry": "npm", "packageName": "url", "cacheIntegrity": "sha512-k<PERSON>ilJa29QrtXnxgq+DdCEGlbTeYM2eJUxsz6vjZavrCYPMIFHMKQmSKYAIuUK2i7hgPm28a8piX5NTUtM/LKQ== sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE="}, "registry": "npm", "hash": "3838e97cfc60521eb73c525a8e55bfdd9e2e28f1"}