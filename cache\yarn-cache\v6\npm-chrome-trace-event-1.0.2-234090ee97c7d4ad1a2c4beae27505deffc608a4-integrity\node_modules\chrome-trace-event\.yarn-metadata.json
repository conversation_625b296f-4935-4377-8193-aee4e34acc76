{"manifest": {"name": "chrome-trace-event", "description": "A library to create a trace of your node app per Google's Trace Event format.", "license": "MIT", "version": "1.0.2", "author": {"name": "<PERSON>, <PERSON>"}, "keywords": ["trace-event", "trace", "event", "trace-viewer", "google"], "repository": {"type": "git", "url": "github.com:samccone/chrome-trace-event"}, "main": "./dist/trace-event.js", "typings": "./dist/trace-event.d.ts", "dependencies": {"tslib": "^1.9.0"}, "devDependencies": {"@types/node": "^9.6.5", "prettier": "^1.12.1", "tape": "4.8.0", "typescript": "^2.8.1"}, "engines": {"node": ">=6.0"}, "scripts": {"build": "tsc", "check_format": "prettier -l lib/** test/** examples/**", "test": "tape test/*.test.js"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-chrome-trace-event-1.0.2-234090ee97c7d4ad1a2c4beae27505deffc608a4-integrity\\node_modules\\chrome-trace-event\\package.json", "readmeFilename": "README.md", "readme": "[![Build Status](https://travis-ci.org/samccone/chrome-trace-event.svg?branch=master)](https://travis-ci.org/samccone/chrome-trace-event)\n\nchrome-trace-event: A node library for creating trace event logs of program\nexecution according to [Google's Trace Event\nformat](https://docs.google.com/document/d/1CvAClvFfyA5R-PhYUmn5OOQtYMH4h6I0nSsKchNAySU).\nThese logs can then be visualized with\n[trace-viewer](https://github.com/google/trace-viewer) or chrome devtools to grok one's programs.\n\n# Install\n\n    npm install chrome-trace-event\n\n# Usage\n\n```javascript\nconst Trace = require(\"chrome-trace-event\").Tracer;\nconst trace = new Trace({\n    noStream: true\n});\ntrace.pipe(fs.createWriteStream(outPath));\ntrace.flush();\n```\n\n# Links\n\n* https://github.com/google/trace-viewer/wiki\n* https://docs.google.com/document/d/1CvAClvFfyA5R-PhYUmn5OOQtYMH4h6I0nSsKchNAySU\n\n# License\n\nMIT. See LICENSE.txt.\n", "licenseText": "# This is the MIT license\n\nCopyright (c) 2015 Joyent Inc. All rights reserved.\n\nPermission is hereby granted, free of charge, to any person obtaining a\ncopy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be included\nin all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\nOR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/chrome-trace-event/-/chrome-trace-event-1.0.2.tgz#234090ee97c7d4ad1a2c4beae27505deffc608a4", "type": "tarball", "reference": "https://registry.yarnpkg.com/chrome-trace-event/-/chrome-trace-event-1.0.2.tgz", "hash": "234090ee97c7d4ad1a2c4beae27505deffc608a4", "integrity": "sha512-9e/zx1jw7B4CO+c/RXoCsfg/x1AfUBioy4owYH0bJprEYAx5hRFLRhWBqHAG57D0ZM4H7vxbP7bPe0VwhQRYDQ==", "registry": "npm", "packageName": "chrome-trace-event", "cacheIntegrity": "sha512-9e/zx1jw7B4CO+c/RXoCsfg/x1AfUBioy4owYH0bJprEYAx5hRFLRhWBqHAG57D0ZM4H7vxbP7bPe0VwhQRYDQ== sha1-I0CQ7pfH1K0aLEvq4nUF3v/GCKQ="}, "registry": "npm", "hash": "234090ee97c7d4ad1a2c4beae27505deffc608a4"}