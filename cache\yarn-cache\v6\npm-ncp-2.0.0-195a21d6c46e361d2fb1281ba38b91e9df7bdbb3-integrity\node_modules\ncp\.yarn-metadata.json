{"manifest": {"name": "ncp", "version": "2.0.0", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "bin\\ncp"}, "devDependencies": {"mocha": "1.15.x", "rimraf": "1.0.x", "read-dir-files": "0.0.x"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "https://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.10"}, "scripts": {"test": "mocha -R spec"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-ncp-2.0.0-****************************************-integrity\\node_modules\\ncp\\package.json", "readmeFilename": "README.md", "readme": "# ncp - Asynchronous recursive file & directory copying\n\n[![Build Status](https://secure.travis-ci.org/AvianFlu/ncp.png)](http://travis-ci.org/AvianFlu/ncp)\n\nThink `cp -r`, but pure node, and asynchronous.  `ncp` can be used both as a CLI tool and programmatically.\n\n## Command Line usage\n\nUsage is simple: `ncp [source] [dest] [--limit=concurrency limit]\n[--filter=filter] --stopOnErr`\n\nThe 'filter' is a Regular Expression - matched files will be copied.\n\nThe 'concurrency limit' is an integer that represents how many pending file system requests `ncp` has at a time.\n\n'stoponerr' is a boolean flag that will tell `ncp` to stop immediately if any\nerrors arise, rather than attempting to continue while logging errors. The default behavior is to complete as many copies as possible, logging errors along the way.\n\nIf there are no errors, `ncp` will output `done.` when complete.  If there are errors, the error messages will be logged to `stdout` and to `./ncp-debug.log`, and the copy operation will attempt to continue.\n\n## Programmatic usage\n\nProgrammatic usage of `ncp` is just as simple.  The only argument to the completion callback is a possible error.  \n\n```javascript\nvar ncp = require('ncp').ncp;\n\nncp.limit = 16;\n\nncp(source, destination, function (err) {\n if (err) {\n   return console.error(err);\n }\n console.log('done!');\n});\n```\n\nYou can also call ncp like `ncp(source, destination, options, callback)`. \n`options` should be a dictionary. Currently, such options are available:\n\n  * `options.filter` - a `RegExp` instance, against which each file name is\n  tested to determine whether to copy it or not, or a function taking single\n  parameter: copied file name, returning `true` or `false`, determining\n  whether to copy file or not.\n\n  * `options.transform` - a function: `function (read, write) { read.pipe(write) }`\n  used to apply streaming transforms while copying.\n\n  * `options.clobber` - boolean=true. if set to false, `ncp` will not overwrite \n  destination files that already exist.\n\n  * `options.dereference` - boolean=false. If set to true, `ncp` will follow symbolic\n  links. For example, a symlink in the source tree pointing to a regular file\n  will become a regular file in the destination tree. Broken symlinks will result in\n  errors.\n\n  * `options.stopOnErr` - boolean=false.  If set to true, `ncp` will behave like `cp -r`,\n  and stop on the first error it encounters. By default, `ncp` continues copying, logging all\n  errors and returning an array.\n\n  * `options.errs` - stream. If `options.stopOnErr` is `false`, a stream can be provided, and errors will be written to this stream.\n\nPlease open an issue if any bugs arise.  As always, I accept (working) pull requests, and refunds are available at `/dev/null`.\n", "licenseText": "# MIT License\n\n###Copyright (C) 2011 by <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/ncp/-/ncp-2.0.0.tgz#****************************************", "type": "tarball", "reference": "https://registry.yarnpkg.com/ncp/-/ncp-2.0.0.tgz", "hash": "****************************************", "integrity": "sha1-GVoh1sRuNh0vsSgbo4uR6d9727M=", "registry": "npm", "packageName": "ncp", "cacheIntegrity": "sha512-zIdGUrPRFTUELUvr3Gmc7KZ2Sw/h1PiVM0Af/oHB6zgnV1ikqSfRk+TOufi79aHYCW3NiOXmr1BP5nWbzojLaA== sha1-GVoh1sRuNh0vsSgbo4uR6d9727M="}, "registry": "npm", "hash": "****************************************"}