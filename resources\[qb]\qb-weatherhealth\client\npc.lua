-- NPC Management for Weather Health System

local QBCore = exports['qb-core']:GetCoreObject()

-- NPC tracking variables
local trackedNPCs = {}
local npcUpdateTimer = 0
local npcBehaviorTimer = 0

-- NPC behavior states
local NPCBehaviorStates = {
    NORMAL = 'normal',
    SEEKING_SHELTER = 'seeking_shelter',
    SEEKING_WARMTH = 'seeking_warmth',
    SICK = 'sick',
    DYING = 'dying',
    DEAD = 'dead'
}

-- Initialize NPC management
CreateThread(function()
    while not QBCore do
        Wait(100)
    end
    
    StartNPCBehaviorLoop()
    StartNPCHealthLoop()
end)

-- Main NPC behavior loop
function StartNPCBehaviorLoop()
    CreateThread(function()
        while true do
            local gameTime = GetGameTimer()
            
            if gameTime - npcBehaviorTimer > 30000 then -- Every 30 seconds
                UpdateNPCBehaviors()
                npcBehaviorTimer = gameTime
            end
            
            Wait(1000)
        end
    end)
end

-- NPC health monitoring loop
function StartNPCHealthLoop()
    CreateThread(function()
        while true do
            local gameTime = GetGameTimer()
            
            if gameTime - npcUpdateTimer > 60000 then -- Every minute
                UpdateNPCHealth()
                npcUpdateTimer = gameTime
            end
            
            Wait(5000)
        end
    end)
end

-- Update NPC behaviors based on weather and time
function UpdateNPCBehaviors()
    local playerPed = PlayerPedId()
    local playerPos = GetEntityCoords(playerPed)
    local currentWeather = exports['qb-weatherhealth']:getCurrentWeather()
    local currentHour = GetClockHours()
    
    -- Get all peds in range
    local peds = GetGamePool('CPed')
    
    for _, ped in ipairs(peds) do
        if DoesEntityExist(ped) and not IsPedAPlayer(ped) and not IsPedInAnyVehicle(ped, false) then
            local pedPos = GetEntityCoords(ped)
            local distance = #(playerPos - pedPos)
            
            if distance <= Config.MaxNPCDistance then
                ProcessNPCBehavior(ped, currentWeather, currentHour)
            end
        end
    end
end

-- Process individual NPC behavior
function ProcessNPCBehavior(ped, weather, hour)
    local pedId = tostring(ped)
    
    -- Initialize NPC data if not exists
    if not trackedNPCs[pedId] then
        trackedNPCs[pedId] = {
            entity = ped,
            lastUpdate = GetGameTimer(),
            behaviorState = NPCBehaviorStates.NORMAL,
            health = 100,
            diseaseType = nil,
            lastOutfitChange = 0,
            lastWeather = nil,
            originalTask = nil,
            lastAnimationTime = 0
        }
    end
    
    local npcData = trackedNPCs[pedId]
    
    -- Update NPC outfit based on weather
    UpdateNPCOutfit(ped, weather, npcData)
    
    -- Update NPC behavior based on weather and time
    UpdateNPCWeatherBehavior(ped, weather, hour, npcData)
    
    -- Update NPC activity based on time
    UpdateNPCTimeBasedBehavior(ped, hour, npcData)

    -- Update NPC weather animations
    UpdateNPCWeatherAnimations(ped, weather, npcData)
end

-- Update NPC outfit for weather
function UpdateNPCOutfit(ped, weather, npcData)
    local gameTime = GetGameTimer()

    -- Check if weather changed - if so, update immediately
    local weatherChanged = npcData.lastWeather ~= weather

    -- Only update outfit every 2 minutes OR when weather changes
    if not weatherChanged and gameTime - npcData.lastOutfitChange < 120000 then
        return
    end

    local gender = IsPedMale(ped) and 'male' or 'female'
    local outfit = Utils.GetRandomNPCOutfit(weather, gender)

    if outfit then
        local currentClothing = Utils.GetPlayerClothing(ped)

        -- Check if outfit change is needed
        if weatherChanged or currentClothing.torso ~= outfit.torso or
           currentClothing.legs ~= outfit.legs then

            Utils.SetNPCClothing(ped, outfit)
            npcData.lastOutfitChange = gameTime
            npcData.lastWeather = weather

            Utils.Debug("Updated NPC %s outfit for weather %s (weather changed: %s)", tostring(ped), weather, tostring(weatherChanged))
        end
    end
end

-- Update NPC behavior based on weather
function UpdateNPCWeatherBehavior(ped, weather, hour, npcData)
    local needsShelter = Utils.NPCNeedsShelter(weather)
    local needsWarmth = Utils.NPCNeedsWarmth(weather)
    
    if needsShelter and npcData.behaviorState ~= NPCBehaviorStates.SEEKING_SHELTER then
        StartSeekingShelter(ped, npcData)
    elseif needsWarmth and npcData.behaviorState ~= NPCBehaviorStates.SEEKING_WARMTH then
        StartSeekingWarmth(ped, npcData)
    elseif not needsShelter and not needsWarmth and 
           (npcData.behaviorState == NPCBehaviorStates.SEEKING_SHELTER or 
            npcData.behaviorState == NPCBehaviorStates.SEEKING_WARMTH) then
        ReturnToNormalBehavior(ped, npcData)
    end
end

-- Update NPC behavior based on time
function UpdateNPCTimeBasedBehavior(ped, hour, npcData)
    local timeCategory = Utils.GetTimeCategory()
    local activityFactor = Utils.GetActivityFactor()

    -- Reduce NPC activity during night
    if timeCategory == 'night' and activityFactor < 0.5 then
        if math.random() < 0.3 then -- 30% chance to go idle during night
            MakeNPCIdle(ped, npcData)
        end
    end
end

-- Update NPC weather-based animations
function UpdateNPCWeatherAnimations(ped, weather, npcData)
    local gameTime = GetGameTimer()

    -- Only play animations every 30 seconds
    if gameTime - npcData.lastAnimationTime < 30000 then
        return
    end

    -- Skip if NPC is busy with other tasks
    if npcData.behaviorState ~= NPCBehaviorStates.NORMAL then
        return
    end

    local temp = Config.WeatherTemperature[weather] or 20
    local animCategory = 'normal'

    -- Determine animation category based on weather
    if temp < 10 then
        animCategory = 'cold'
    elseif temp > 25 then
        animCategory = 'hot'
    elseif weather == 'RAIN' or weather == 'THUNDER' then
        animCategory = 'rain'
    end

    local animations = Config.Animations.npc_weather[animCategory]
    if not animations then return end

    -- Try to play a random animation
    for _, animData in ipairs(animations) do
        if math.random(1, 100) <= animData.chance then
            PlayNPCWeatherAnimation(ped, animData)
            npcData.lastAnimationTime = gameTime
            Utils.Debug("NPC %s playing %s animation for %s weather", tostring(ped), animCategory, weather)
            break -- Only play one animation at a time
        end
    end
end

-- Play weather animation for NPC
function PlayNPCWeatherAnimation(ped, animData)
    if not DoesEntityExist(ped) then return end

    RequestAnimDict(animData.dict)
    while not HasAnimDictLoaded(animData.dict) do
        Wait(100)
    end

    -- Clear current task and play animation
    ClearPedTasks(ped)
    TaskPlayAnim(ped, animData.dict, animData.anim, 8.0, -8.0,
                animData.duration or 10000, 0, 0, false, false, false)

    RemoveAnimDict(animData.dict)
end

-- Make NPC seek shelter
function StartSeekingShelter(ped, npcData)
    npcData.behaviorState = NPCBehaviorStates.SEEKING_SHELTER
    
    -- Clear current task
    ClearPedTasks(ped)
    
    -- Find nearest building or shelter
    local pedPos = GetEntityCoords(ped)
    local shelterPos = FindNearestShelter(pedPos)
    
    if shelterPos then
        TaskGoToCoordAnyMeans(ped, shelterPos.x, shelterPos.y, shelterPos.z, 1.0, 0, 0, 786603, 0xbf800000)
        Utils.Debug("NPC %s seeking shelter", tostring(ped))
    else
        -- If no shelter found, make NPC stand still and play shelter animation
        TaskStandStill(ped, 30000) -- Stand still for 30 seconds
        PlayNPCAnimation(ped, 'shelter')
    end
end

-- Make NPC seek warmth
function StartSeekingWarmth(ped, npcData)
    npcData.behaviorState = NPCBehaviorStates.SEEKING_WARMTH
    
    -- Clear current task
    ClearPedTasks(ped)
    
    -- Find nearest warmth source
    local pedPos = GetEntityCoords(ped)
    local warmthPos = FindNearestWarmthSource(pedPos)
    
    if warmthPos then
        TaskGoToCoordAnyMeans(ped, warmthPos.x, warmthPos.y, warmthPos.z, 1.0, 0, 0, 786603, 0xbf800000)
        Utils.Debug("NPC %s seeking warmth", tostring(ped))
    else
        -- If no warmth source found, make NPC shiver and seek building
        TaskStandStill(ped, 30000)
        PlayNPCAnimation(ped, 'shiver')
    end
end

-- Return NPC to normal behavior
function ReturnToNormalBehavior(ped, npcData)
    npcData.behaviorState = NPCBehaviorStates.NORMAL
    
    -- Clear current task
    ClearPedTasks(ped)
    
    -- Resume normal wandering
    TaskWanderStandard(ped, 10.0, 10)
    
    Utils.Debug("NPC %s returned to normal behavior", tostring(ped))
end

-- Make NPC idle (for night time)
function MakeNPCIdle(ped, npcData)
    if npcData.behaviorState == NPCBehaviorStates.NORMAL then
        ClearPedTasks(ped)
        
        -- Random idle animations
        local idleAnims = {
            'WORLD_HUMAN_SMOKING',
            'WORLD_HUMAN_STAND_IMPATIENT',
            'WORLD_HUMAN_LEANING',
            'WORLD_HUMAN_HANG_OUT_STREET'
        }
        
        local randomAnim = idleAnims[math.random(#idleAnims)]
        TaskStartScenarioInPlace(ped, randomAnim, 0, true)
    end
end

-- Find nearest shelter
function FindNearestShelter(position)
    -- This is a simplified version - in a real implementation,
    -- you would have predefined shelter locations or use building detection
    local shelters = {
        vector3(213.0, -810.0, 31.0),  -- Hospital
        vector3(295.0, -895.0, 29.0),  -- Police Station
        vector3(-1037.0, -2738.0, 20.0), -- Airport
        vector3(1138.0, -982.0, 46.0),  -- Mirror Park
    }
    
    local nearestShelter = nil
    local nearestDistance = math.huge
    
    for _, shelter in ipairs(shelters) do
        local distance = #(position - shelter)
        if distance < nearestDistance then
            nearestDistance = distance
            nearestShelter = shelter
        end
    end
    
    return nearestDistance < 500.0 and nearestShelter or nil
end

-- Find nearest warmth source
function FindNearestWarmthSource(position)
    -- Simplified warmth sources - fires, heaters, etc.
    local warmthSources = {
        vector3(213.0, -810.0, 31.0),  -- Hospital (heated)
        vector3(295.0, -895.0, 29.0),  -- Police Station (heated)
        vector3(-1037.0, -2738.0, 20.0), -- Airport (heated)
    }
    
    local nearestSource = nil
    local nearestDistance = math.huge
    
    for _, source in ipairs(warmthSources) do
        local distance = #(position - source)
        if distance < nearestDistance then
            nearestDistance = distance
            nearestSource = source
        end
    end
    
    return nearestDistance < 300.0 and nearestSource or nil
end

-- Update NPC health status
function UpdateNPCHealth()
    for pedId, npcData in pairs(trackedNPCs) do
        if DoesEntityExist(npcData.entity) then
            ProcessNPCHealth(npcData)
        else
            -- Remove dead/despawned NPCs
            trackedNPCs[pedId] = nil
        end
    end
end

-- Process individual NPC health
function ProcessNPCHealth(npcData)
    local ped = npcData.entity
    local currentWeather = exports['qb-weatherhealth']:getCurrentWeather()
    
    -- Get NPC clothing and calculate comfort
    local clothing = Utils.GetPlayerClothing(ped)
    local comfort = Utils.CalculateTemperatureComfort(currentWeather, clothing)
    
    -- Apply health changes based on comfort
    local healthChange = Config.HealthEffects.healthChangeRate[comfort] or 0
    
    if healthChange < 0 then
        npcData.health = math.max(0, npcData.health + healthChange)
        
        -- Check for disease contraction
        if npcData.health < 80 and not npcData.diseaseType then
            ContractNPCDisease(npcData, comfort)
        end
        
        -- Check for death
        if npcData.health <= 0 then
            KillNPC(npcData)
        end
    elseif healthChange > 0 and npcData.health < 100 then
        npcData.health = math.min(100, npcData.health + healthChange)
    end
    
    -- Play symptom animations if sick
    if npcData.diseaseType and math.random() < 0.2 then -- 20% chance
        PlayDiseaseSymptom(npcData)
    end
end

-- Contract disease for NPC
function ContractNPCDisease(npcData, comfort)
    local diseases = {'cold', 'flu'}
    
    if comfort == 'critical' then
        local temp = Config.WeatherTemperature[exports['qb-weatherhealth']:getCurrentWeather()] or 20
        if temp > 30 then
            table.insert(diseases, 'heatstroke')
        elseif temp < 0 then
            table.insert(diseases, 'hypothermia')
        end
    end
    
    local randomDisease = diseases[math.random(#diseases)]
    npcData.diseaseType = randomDisease
    npcData.behaviorState = NPCBehaviorStates.SICK
    
    Utils.Debug("NPC %s contracted %s", tostring(npcData.entity), randomDisease)
end

-- Play disease symptom for NPC
function PlayDiseaseSymptom(npcData)
    local disease = Config.HealthEffects.diseases[npcData.diseaseType]
    if disease and disease.symptoms then
        local randomSymptom = disease.symptoms[math.random(#disease.symptoms)]
        PlayNPCAnimation(npcData.entity, randomSymptom)
    end
end

-- Kill NPC due to illness
function KillNPC(npcData)
    local ped = npcData.entity
    npcData.behaviorState = NPCBehaviorStates.DEAD
    npcData.health = 0
    
    -- Apply death
    SetEntityHealth(ped, 0)
    
    Utils.Debug("NPC %s died from illness", tostring(ped))
    
    -- Notify nearby players
    local playerPed = PlayerPedId()
    local pedPos = GetEntityCoords(ped)
    local playerPos = GetEntityCoords(playerPed)
    
    if #(playerPos - pedPos) < 50.0 then
        QBCore.Functions.Notify(Lang:t('npc.dead_npc'), 'error')
    end
end

-- Play NPC animation
function PlayNPCAnimation(ped, animationType)
    if not DoesEntityExist(ped) then return end
    
    local animConfig = Config.Animations.symptoms[animationType] or Config.Animations.weather[animationType]
    
    if animConfig then
        RequestAnimDict(animConfig.dict)
        while not HasAnimDictLoaded(animConfig.dict) do
            Wait(100)
        end
        
        TaskPlayAnim(ped, animConfig.dict, animConfig.anim, 8.0, -8.0, 
                    animConfig.duration or -1, animConfig.loop and 1 or 0, 0, false, false, false)
        
        RemoveAnimDict(animConfig.dict)
    end
end

-- Weather change event handler
RegisterNetEvent('weatherhealth:client:weatherChanged', function(newWeather, oldWeather)
    Utils.Debug("Weather changed from %s to %s - updating all NPC outfits", oldWeather, newWeather)

    -- Force update all tracked NPCs immediately
    for pedId, npcData in pairs(trackedNPCs) do
        if DoesEntityExist(npcData.entity) then
            -- Reset last outfit change time to force immediate update
            npcData.lastOutfitChange = 0
            npcData.lastWeather = oldWeather -- This will trigger weatherChanged flag

            local gender = IsPedMale(npcData.entity) and 'male' or 'female'
            local outfit = Utils.GetRandomNPCOutfit(newWeather, gender)

            if outfit then
                Utils.SetNPCClothing(npcData.entity, outfit)
                npcData.lastOutfitChange = GetGameTimer()
                npcData.lastWeather = newWeather
                Utils.Debug("Force updated NPC %s outfit for new weather %s", pedId, newWeather)
            end
        end
    end
end)

-- Cleanup function
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        -- Clear all NPC tasks
        for _, npcData in pairs(trackedNPCs) do
            if DoesEntityExist(npcData.entity) then
                ClearPedTasks(npcData.entity)
            end
        end
        trackedNPCs = {}
    end
end)

-- Event handler for checking nearby NPC clothing
RegisterNetEvent('weatherhealth:client:checkNearbyNPCClothing', function()
    local playerPed = PlayerPedId()
    local playerPos = GetEntityCoords(playerPed)
    local currentWeather = exports['qb-weatherhealth']:getCurrentWeather()

    local peds = GetGamePool('CPed')
    local npcCount = 0
    local clothingInfo = {}

    for _, ped in ipairs(peds) do
        if DoesEntityExist(ped) and not IsPedAPlayer(ped) then
            local pedPos = GetEntityCoords(ped)
            local distance = #(playerPos - pedPos)

            if distance <= 50.0 then -- Check NPCs within 50 meters
                npcCount = npcCount + 1
                local clothing = Utils.GetPlayerClothing(ped)
                local gender = IsPedMale(ped) and 'male' or 'female'
                local warmth = Utils.CalculateClothingWarmth(clothing)

                table.insert(clothingInfo, string.format(
                    "NPC %d (%s): Torso=%d, Legs=%d, Shoes=%d, Hat=%d, Warmth=%d",
                    npcCount, gender, clothing.torso, clothing.legs, clothing.shoes, clothing.hat, warmth
                ))

                if npcCount >= 5 then break end -- Limit to 5 NPCs for readability
            end
        end
    end

    if #clothingInfo > 0 then
        local message = string.format("Weather: %s\nNearby NPCs clothing:\n%s",
                                    currentWeather, table.concat(clothingInfo, "\n"))
        QBCore.Functions.Notify(message, 'primary', 10000)
    else
        QBCore.Functions.Notify("No NPCs found nearby", 'primary')
    end
end)

-- Export functions
exports('getNPCData', function(ped)
    return trackedNPCs[tostring(ped)]
end)

exports('getTrackedNPCs', function()
    return trackedNPCs
end)
