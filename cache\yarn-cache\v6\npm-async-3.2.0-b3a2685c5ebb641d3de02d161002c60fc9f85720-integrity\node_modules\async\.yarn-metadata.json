{"manifest": {"name": "async", "description": "Higher-order functions and common patterns for asynchronous code", "version": "3.2.0", "main": "dist/async.js", "author": {"name": "<PERSON><PERSON>"}, "homepage": "https://caolan.github.io/async/", "repository": {"type": "git", "url": "https://github.com/caolan/async.git"}, "bugs": {"url": "https://github.com/caolan/async/issues"}, "keywords": ["async", "callback", "module", "utility"], "dependencies": {}, "devDependencies": {"babel-core": "^6.26.3", "babel-eslint": "^8.2.6", "babel-minify": "^0.5.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-istanbul": "^5.1.4", "babel-plugin-syntax-async-generators": "^6.13.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "babel-register": "^6.26.0", "babelify": "^8.0.0", "benchmark": "^2.1.1", "bluebird": "^3.4.6", "browserify": "^16.2.3", "chai": "^4.2.0", "cheerio": "^0.22.0", "coveralls": "^3.0.4", "es6-promise": "^2.3.0", "eslint": "^6.0.1", "eslint-plugin-prefer-arrow": "^1.1.5", "fs-extra": "^0.26.7", "jsdoc": "^3.6.2", "karma": "^4.1.0", "karma-browserify": "^5.3.0", "karma-edge-launcher": "^0.4.2", "karma-firefox-launcher": "^1.1.0", "karma-junit-reporter": "^1.2.0", "karma-mocha": "^1.2.0", "karma-mocha-reporter": "^2.2.0", "karma-safari-launcher": "^1.0.0", "mocha": "^6.1.4", "mocha-junit-reporter": "^1.18.0", "native-promise-only": "^0.8.0-a", "nyc": "^14.1.1", "rimraf": "^2.5.0", "rollup": "^0.63.4", "rollup-plugin-node-resolve": "^2.0.0", "rollup-plugin-npm": "^2.0.0", "rsvp": "^3.0.18", "semver": "^5.5.0", "yargs": "^11.0.0"}, "scripts": {"coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "coveralls": "npm run coverage && nyc report --reporter=text-lcov | coveralls", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "lint": "eslint --fix lib/ test/ perf/memory.js perf/suites.js perf/benchmark.js support/build/ support/*.js karma.conf.js", "mocha-browser-test": "karma start", "mocha-node-test": "mocha", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "test": "npm run lint && npm run mocha-node-test"}, "license": "MIT", "nyc": {"exclude": ["test"]}, "module": "dist/async.mjs", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-async-3.2.0-b3a2685c5ebb641d3de02d161002c60fc9f85720-integrity\\node_modules\\async\\package.json", "readmeFilename": "README.md", "readme": "![Async Logo](https://raw.githubusercontent.com/caolan/async/master/logo/async-logo_readme.jpg)\n\n[![Build Status via Travis CI](https://travis-ci.org/caolan/async.svg?branch=master)](https://travis-ci.org/caolan/async)\n[![Build Status via Azure Pipelines](https://dev.azure.com/caolanmcmahon/async/_apis/build/status/caolan.async?branchName=master)](https://dev.azure.com/caolanmcmahon/async/_build/latest?definitionId=1&branchName=master)\n[![NPM version](https://img.shields.io/npm/v/async.svg)](https://www.npmjs.com/package/async)\n[![Coverage Status](https://coveralls.io/repos/caolan/async/badge.svg?branch=master)](https://coveralls.io/r/caolan/async?branch=master)\n[![Join the chat at https://gitter.im/caolan/async](https://badges.gitter.im/Join%20Chat.svg)](https://gitter.im/caolan/async?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge&utm_content=badge)\n[![jsDelivr Hits](https://data.jsdelivr.com/v1/package/npm/async/badge?style=rounded)](https://www.jsdelivr.com/package/npm/async)\n\n<!--\n|Linux|Windows|MacOS|\n|-|-|-|\n|[![Linux Build Status](https://dev.azure.com/caolanmcmahon/async/_apis/build/status/caolan.async?branchName=master&jobName=Linux&configuration=Linux%20node_10_x)](https://dev.azure.com/caolanmcmahon/async/_build/latest?definitionId=1&branchName=master) | [![Windows Build Status](https://dev.azure.com/caolanmcmahon/async/_apis/build/status/caolan.async?branchName=master&jobName=Windows&configuration=Windows%20node_10_x)](https://dev.azure.com/caolanmcmahon/async/_build/latest?definitionId=1&branchName=master) | [![MacOS Build Status](https://dev.azure.com/caolanmcmahon/async/_apis/build/status/caolan.async?branchName=master&jobName=OSX&configuration=OSX%20node_10_x)](https://dev.azure.com/caolanmcmahon/async/_build/latest?definitionId=1&branchName=master)| -->\n\nAsync is a utility module which provides straight-forward, powerful functions for working with [asynchronous JavaScript](http://caolan.github.io/async/v3/global.html). Although originally designed for use with [Node.js](https://nodejs.org/) and installable via `npm i async`, it can also be used directly in the browser.  A ESM/MJS version is included in the main `async` package that should automatically be used with compatible bundlers such as Webpack and Rollup.\n\nA pure ESM version of Async is available as [`async-es`](https://www.npmjs.com/package/async-es).\n\nFor Documentation, visit <https://caolan.github.io/async/>\n\n*For Async v1.5.x documentation, go [HERE](https://github.com/caolan/async/blob/v1.5.2/README.md)*\n\n\n```javascript\n// for use with Node-style callbacks...\nvar async = require(\"async\");\n\nvar obj = {dev: \"/dev.json\", test: \"/test.json\", prod: \"/prod.json\"};\nvar configs = {};\n\nasync.forEachOf(obj, (value, key, callback) => {\n    fs.readFile(__dirname + value, \"utf8\", (err, data) => {\n        if (err) return callback(err);\n        try {\n            configs[key] = JSON.parse(data);\n        } catch (e) {\n            return callback(e);\n        }\n        callback();\n    });\n}, err => {\n    if (err) console.error(err.message);\n    // configs is now a map of JSON data\n    doSomethingWith(configs);\n});\n```\n\n```javascript\nvar async = require(\"async\");\n\n// ...or ES2017 async functions\nasync.mapLimit(urls, 5, async function(url) {\n    const response = await fetch(url)\n    return response.body\n}, (err, results) => {\n    if (err) throw err\n    // results is now an array of the response bodies\n    console.log(results)\n})\n```\n", "licenseText": "Copyright (c) 2010-2018 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/async/-/async-3.2.0.tgz#b3a2685c5ebb641d3de02d161002c60fc9f85720", "type": "tarball", "reference": "https://registry.yarnpkg.com/async/-/async-3.2.0.tgz", "hash": "b3a2685c5ebb641d3de02d161002c60fc9f85720", "integrity": "sha512-TR2mEZFVOj2pLStYxLht7TyfuRzaydfpxr3k9RpHIzMgw7A64dzsdqCxH1WJyQdoe8T10nDXd9wnEigmiuHIZw==", "registry": "npm", "packageName": "async", "cacheIntegrity": "sha512-TR2mEZFVOj2pLStYxLht7TyfuRzaydfpxr3k9RpHIzMgw7A64dzsdqCxH1WJyQdoe8T10nDXd9wnEigmiuHIZw== sha1-s6JoXF67ZB094C0WEALGD8n4VyA="}, "registry": "npm", "hash": "b3a2685c5ebb641d3de02d161002c60fc9f85720"}