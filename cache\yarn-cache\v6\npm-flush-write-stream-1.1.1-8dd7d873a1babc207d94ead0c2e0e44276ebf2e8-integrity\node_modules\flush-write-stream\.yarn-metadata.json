{"manifest": {"name": "flush-write-stream", "version": "1.1.1", "description": "A write stream constructor that supports a flush function that is called before finish is emitted", "main": "index.js", "dependencies": {"inherits": "^2.0.3", "readable-stream": "^2.3.6"}, "devDependencies": {"tape": "^4.2.2"}, "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/flush-write-stream.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/flush-write-stream/issues"}, "homepage": "https://github.com/mafintosh/flush-write-stream", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-flush-write-stream-1.1.1-8dd7d873a1babc207d94ead0c2e0e44276ebf2e8-integrity\\node_modules\\flush-write-stream\\package.json", "readmeFilename": "README.md", "readme": "# flush-write-stream\n\nA write stream constructor that supports a flush function that is called before `finish` is emitted\n\n```\nnpm install flush-write-stream\n```\n\n[![build status](http://img.shields.io/travis/mafintosh/flush-write-stream.svg?style=flat)](http://travis-ci.org/mafintosh/flush-write-stream)\n\n## Usage\n\n``` js\nvar writer = require('flush-write-stream')\n\nvar ws = writer(write, flush)\n\nws.on('finish', function () {\n  console.log('finished')\n})\n\nws.write('hello')\nws.write('world')\nws.end()\n\nfunction write (data, enc, cb) {\n  // i am your normal ._write method\n  console.log('writing', data.toString())\n  cb()\n}\n\nfunction flush (cb) {\n  // i am called before finish is emitted\n  setTimeout(cb, 1000) // wait 1 sec\n}\n```\n\nIf you run the above it will produce the following output\n\n```\nwriting hello\nwriting world\n(nothing happens for 1 sec)\nfinished\n```\n\n## API\n\n#### `var ws = writer([options], write, [flush])`\n\nCreate a new writable stream. Options are forwarded to the stream constructor.\n\n#### `var ws = writer.obj([options], write, [flush])`\n\nSame as the above except `objectMode` is set to `true` per default.\n\n## License\n\nMIT\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015 Mathias Buus\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/flush-write-stream/-/flush-write-stream-1.1.1.tgz#8dd7d873a1babc207d94ead0c2e0e44276ebf2e8", "type": "tarball", "reference": "https://registry.yarnpkg.com/flush-write-stream/-/flush-write-stream-1.1.1.tgz", "hash": "8dd7d873a1babc207d94ead0c2e0e44276ebf2e8", "integrity": "sha512-3Z4XhFZ3992uIq0XOqb9AreonueSYphE6oYbpt5+3u06JWklbsPkNv3ZKkP9Bz/r+1MWCaMoSQ28P85+1Yc77w==", "registry": "npm", "packageName": "flush-write-stream", "cacheIntegrity": "sha512-3Z4XhFZ3992uIq0XOqb9AreonueSYphE6oYbpt5+3u06JWklbsPkNv3ZKkP9Bz/r+1MWCaMoSQ28P85+1Yc77w== sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug="}, "registry": "npm", "hash": "8dd7d873a1babc207d94ead0c2e0e44276ebf2e8"}