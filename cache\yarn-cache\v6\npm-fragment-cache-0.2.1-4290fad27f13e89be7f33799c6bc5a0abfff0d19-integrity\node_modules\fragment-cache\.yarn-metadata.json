{"manifest": {"name": "fragment-cache", "description": "A cache for managing namespaced sub-caches", "version": "0.2.1", "homepage": "https://github.com/jonschlinkert/fragment-cache", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/fragment-cache.git"}, "bugs": {"url": "https://github.com/jonschlinkert/fragment-cache/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"map-cache": "^0.2.2"}, "devDependencies": {"gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.11", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.1", "mocha": "^3.2.0"}, "keywords": ["cache", "fragment"], "verb": {"plugins": ["gulp-format-md"], "reflinks": ["map-cache", "verb"], "related": {"list": ["base", "map-cache"]}, "layout": "default", "toc": false, "tasks": ["readme"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-fragment-cache-0.2.1-4290fad27f13e89be7f33799c6bc5a0abfff0d19-integrity\\node_modules\\fragment-cache\\package.json", "readmeFilename": "README.md", "readme": "# fragment-cache [![NPM version](https://img.shields.io/npm/v/fragment-cache.svg?style=flat)](https://www.npmjs.com/package/fragment-cache) [![NPM downloads](https://img.shields.io/npm/dm/fragment-cache.svg?style=flat)](https://npmjs.org/package/fragment-cache) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/fragment-cache.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/fragment-cache)\n\n> A cache for managing namespaced sub-caches\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save fragment-cache\n```\n\n## Usage\n\n```js\nvar Fragment = require('fragment-cache');\nvar fragment = new Fragment();\n```\n\n## API\n\n### [FragmentCache](index.js#L24)\n\nCreate a new `FragmentCache` with an optional object to use for `caches`.\n\n**Example**\n\n```js\nvar fragment = new FragmentCache();\n```\n\n**Params**\n\n* `cacheName` **{String}**\n* `returns` **{Object}**: Returns the [map-cache](https://github.com/jonschlinkert/map-cache) instance.\n\n### [.cache](index.js#L49)\n\nGet cache `name` from the `fragment.caches` object. Creates a new `MapCache` if it doesn't already exist.\n\n**Example**\n\n```js\nvar cache = fragment.cache('files');\nconsole.log(fragment.caches.hasOwnProperty('files'));\n//=> true\n```\n\n**Params**\n\n* `cacheName` **{String}**\n* `returns` **{Object}**: Returns the [map-cache](https://github.com/jonschlinkert/map-cache) instance.\n\n### [.set](index.js#L67)\n\nSet a value for property `key` on cache `name`\n\n**Example**\n\n```js\nfragment.set('files', 'somefile.js', new File({path: 'somefile.js'}));\n```\n\n**Params**\n\n* `name` **{String}**\n* `key` **{String}**: Property name to set\n* `val` **{any}**: The value of `key`\n* `returns` **{Object}**: The cache instance for chaining\n\n### [.has](index.js#L93)\n\nReturns true if a non-undefined value is set for `key` on fragment cache `name`.\n\n**Example**\n\n```js\nvar cache = fragment.cache('files');\ncache.set('somefile.js');\n\nconsole.log(cache.has('somefile.js'));\n//=> true\n\nconsole.log(cache.has('some-other-file.js'));\n//=> false\n```\n\n**Params**\n\n* `name` **{String}**: Cache name\n* `key` **{String}**: Optionally specify a property to check for on cache `name`\n* `returns` **{Boolean}**\n\n### [.get](index.js#L115)\n\nGet `name`, or if specified, the value of `key`. Invokes the [cache](#cache) method, so that cache `name` will be created it doesn't already exist. If `key` is not passed, the entire cache (`name`) is returned.\n\n**Example**\n\n```js\nvar Vinyl = require('vinyl');\nvar cache = fragment.cache('files');\ncache.set('somefile.js', new Vinyl({path: 'somefile.js'}));\nconsole.log(cache.get('somefile.js'));\n//=> <File \"somefile.js\">\n```\n\n**Params**\n\n* `name` **{String}**\n* `returns` **{Object}**: Returns cache `name`, or the value of `key` if specified\n\n## About\n\n### Related projects\n\n* [base](https://www.npmjs.com/package/base): base is the foundation for creating modular, unit testable and highly pluggable node.js applications, starting… [more](https://github.com/node-base/base) | [homepage](https://github.com/node-base/base \"base is the foundation for creating modular, unit testable and highly pluggable node.js applications, starting with a handful of common methods, like `set`, `get`, `del` and `use`.\")\n* [map-cache](https://www.npmjs.com/package/map-cache): Basic cache object for storing key-value pairs. | [homepage](https://github.com/jonschlinkert/map-cache \"Basic cache object for storing key-value pairs.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Building docs\n\n_(This document was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme) (a [verb](https://github.com/verbose/verb) generator), please don't edit the readme directly. Any changes to the readme must be made in [.verb.md](.verb.md).)_\n\nTo generate the readme and API documentation with [verb](https://github.com/verbose/verb):\n\n```sh\n$ npm install -g verb verb-generate-readme && verb\n```\n\n### Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm install -d && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT license](https://github.com/jonschlinkert/fragment-cache/blob/master/LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.2.0, on October 17, 2016._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016-2017, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/fragment-cache/-/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19", "type": "tarball", "reference": "https://registry.yarnpkg.com/fragment-cache/-/fragment-cache-0.2.1.tgz", "hash": "4290fad27f13e89be7f33799c6bc5a0abfff0d19", "integrity": "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=", "registry": "npm", "packageName": "fragment-cache", "cacheIntegrity": "sha512-GMBAbW9antB8iZRHLoGw0b3HANt57diZYFO/HL1JGIC1MjKrdmhxvrJbupnVvpys0zsz7yBApXdQyfepKly2kA== sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk="}, "registry": "npm", "hash": "4290fad27f13e89be7f33799c6bc5a0abfff0d19"}