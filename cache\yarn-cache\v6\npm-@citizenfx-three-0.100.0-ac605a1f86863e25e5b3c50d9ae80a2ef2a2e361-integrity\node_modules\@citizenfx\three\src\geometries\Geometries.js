export { WireframeGeometry } from './WireframeGeometry.js';
export { ParametricGeometry, ParametricBufferGeometry } from './ParametricGeometry.js';
export { TetrahedronGeometry, TetrahedronBufferGeometry } from './TetrahedronGeometry.js';
export { OctahedronGeometry, OctahedronBufferGeometry } from './OctahedronGeometry.js';
export { IcosahedronGeometry, IcosahedronBufferGeometry } from './IcosahedronGeometry.js';
export { DodecahedronGeometry, DodecahedronBufferGeometry } from './DodecahedronGeometry.js';
export { PolyhedronGeometry, PolyhedronBufferGeometry } from './PolyhedronGeometry.js';
export { TubeGeometry, TubeBufferGeometry } from './TubeGeometry.js';
export { TorusKnotGeometry, TorusKnotBufferGeometry } from './TorusKnotGeometry.js';
export { TorusGeometry, TorusBufferGeometry } from './TorusGeometry.js';
export { TextGeometry, TextBufferGeometry } from './TextGeometry.js';
export { SphereGeometry, SphereBufferGeometry } from './SphereGeometry.js';
export { RingGeometry, RingBufferGeometry } from './RingGeometry.js';
export { PlaneGeometry, PlaneBufferGeometry } from './PlaneGeometry.js';
export { LatheGeometry, LatheBufferGeometry } from './LatheGeometry.js';
export { ShapeGeometry, ShapeBufferGeometry } from './ShapeGeometry.js';
export { ExtrudeGeometry, ExtrudeBufferGeometry } from './ExtrudeGeometry.js';
export { EdgesGeometry } from './EdgesGeometry.js';
export { ConeGeometry, ConeBufferGeometry } from './ConeGeometry.js';
export { CylinderGeometry, CylinderBufferGeometry } from './CylinderGeometry.js';
export { CircleGeometry, CircleBufferGeometry } from './CircleGeometry.js';
export { BoxGeometry, BoxBufferGeometry } from './BoxGeometry.js';
