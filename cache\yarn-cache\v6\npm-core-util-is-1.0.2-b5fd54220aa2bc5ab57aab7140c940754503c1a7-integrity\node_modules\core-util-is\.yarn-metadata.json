{"manifest": {"name": "core-util-is", "version": "1.0.2", "description": "The `util.is*` functions introduced in Node v0.12.", "main": "lib/util.js", "repository": {"type": "git", "url": "git://github.com/isaacs/core-util-is"}, "keywords": ["util", "<PERSON><PERSON><PERSON><PERSON>", "isArray", "isNumber", "isString", "isRegExp", "isThis", "isThat", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "bugs": {"url": "https://github.com/isaacs/core-util-is/issues"}, "scripts": {"test": "tap test.js"}, "devDependencies": {"tap": "^2.3.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-core-util-is-1.0.2-b5fd54220aa2bc5ab57aab7140c940754503c1a7-integrity\\node_modules\\core-util-is\\package.json", "readmeFilename": "README.md", "readme": "# core-util-is\n\nThe `util.is*` functions introduced in Node v0.12.\n", "licenseText": "Copyright Node.js contributors. All rights reserved.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/core-util-is/-/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7", "type": "tarball", "reference": "https://registry.yarnpkg.com/core-util-is/-/core-util-is-1.0.2.tgz", "hash": "b5fd54220aa2bc5ab57aab7140c940754503c1a7", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=", "registry": "npm", "packageName": "core-util-is", "cacheIntegrity": "sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ== sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="}, "registry": "npm", "hash": "b5fd54220aa2bc5ab57aab7140c940754503c1a7"}