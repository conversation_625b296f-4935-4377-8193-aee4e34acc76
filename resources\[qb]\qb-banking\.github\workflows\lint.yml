name: <PERSON><PERSON>
on: [push, pull_request_target]
jobs:
  lint:
    name: Lint Resource
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          ref: ${{ github.event.pull_request.head.sha }}
      - name: <PERSON><PERSON>
        uses: iLLeniumStudios/fivem-lua-lint-action@v2
        with:
          capture: "junit.xml"
          args: "-t --formatter JUnit"
          extra_libs: mysql+polyzone+qblocales
      - name: Generate Lint Report
        if: always()
        uses: mikepenz/action-junit-report@v3
        with:
          report_paths: "**/junit.xml"
          check_name: Linting Report
          fail_on_failure: false
