{"name": "@types/content-disposition", "version": "0.5.3", "description": "TypeScript definitions for content-disposition", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/bomret", "githubUsername": "bomret"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/content-disposition"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "713abf3c213aa73aac34682a2a9f500cc7cdf72fdd23e8c027d63e42fbc3e494", "typeScriptVersion": "2.8"}