{"manifest": {"name": "he", "version": "1.2.0", "description": "A robust HTML entities encoder/decoder with full Unicode support.", "homepage": "https://mths.be/he", "main": "he.js", "bin": {"he": "bin\\he"}, "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/he.git"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "files": ["LICENSE-MIT.txt", "he.js", "bin/", "man/"], "scripts": {"test": "node tests/tests.js", "build": "grunt build"}, "devDependencies": {"codecov.io": "^0.1.6", "grunt": "^0.4.5", "grunt-cli": "^1.3.1", "grunt-shell": "^1.1.1", "grunt-template": "^0.2.3", "istanbul": "^0.4.2", "jsesc": "^1.0.0", "lodash": "^4.8.2", "qunit-extras": "^1.4.5", "qunitjs": "~1.11.0", "regenerate": "^1.2.1", "regexgen": "^1.3.0", "requirejs": "^2.1.22", "sort-object": "^3.0.2"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-he-1.2.0-84ae65fa7eafb165fddb61566ae14baf05664f0f-integrity\\node_modules\\he\\package.json", "readmeFilename": "README.md", "readme": "# he [![Build status](https://travis-ci.org/mathiasbynens/he.svg?branch=master)](https://travis-ci.org/mathiasbynens/he) [![Code coverage status](https://codecov.io/github/mathiasbynens/he/coverage.svg?branch=master)](https://codecov.io/github/mathiasbynens/he?branch=master) [![Dependency status](https://gemnasium.com/mathiasbynens/he.svg)](https://gemnasium.com/mathiasbynens/he)\n\n_he_ (for “HTML entities”) is a robust HTML entity encoder/decoder written in JavaScript. It supports [all standardized named character references as per HTML](https://html.spec.whatwg.org/multipage/syntax.html#named-character-references), handles [ambiguous ampersands](https://mathiasbynens.be/notes/ambiguous-ampersands) and other edge cases [just like a browser would](https://html.spec.whatwg.org/multipage/syntax.html#tokenizing-character-references), has an extensive test suite, and — contrary to many other JavaScript solutions — _he_ handles astral Unicode symbols just fine. [An online demo is available.](https://mothereff.in/html-entities)\n\n## Installation\n\nVia [npm](https://www.npmjs.com/):\n\n```bash\nnpm install he\n```\n\nVia [Bower](http://bower.io/):\n\n```bash\nbower install he\n```\n\nVia [Component](https://github.com/component/component):\n\n```bash\ncomponent install mathiasbynens/he\n```\n\nIn a browser:\n\n```html\n<script src=\"he.js\"></script>\n```\n\nIn [Node.js](https://nodejs.org/), [io.js](https://iojs.org/), [Narwhal](http://narwhaljs.org/), and [RingoJS](http://ringojs.org/):\n\n```js\nvar he = require('he');\n```\n\nIn [Rhino](http://www.mozilla.org/rhino/):\n\n```js\nload('he.js');\n```\n\nUsing an AMD loader like [RequireJS](http://requirejs.org/):\n\n```js\nrequire(\n  {\n    'paths': {\n      'he': 'path/to/he'\n    }\n  },\n  ['he'],\n  function(he) {\n    console.log(he);\n  }\n);\n```\n\n## API\n\n### `he.version`\n\nA string representing the semantic version number.\n\n### `he.encode(text, options)`\n\nThis function takes a string of text and encodes (by default) any symbols that aren’t printable ASCII symbols and `&`, `<`, `>`, `\"`, `'`, and `` ` ``, replacing them with character references.\n\n```js\nhe.encode('foo © bar ≠ baz 𝌆 qux');\n// → 'foo &#xA9; bar &#x2260; baz &#x1D306; qux'\n```\n\nAs long as the input string contains [allowed code points](https://html.spec.whatwg.org/multipage/parsing.html#preprocessing-the-input-stream) only, the return value of this function is always valid HTML. Any [(invalid) code points that cannot be represented using a character reference](https://html.spec.whatwg.org/multipage/syntax.html#table-charref-overrides) in the input are not encoded:\n\n```js\nhe.encode('foo \\0 bar');\n// → 'foo \\0 bar'\n```\n\nHowever, enabling [the `strict` option](https://github.com/mathiasbynens/he#strict) causes invalid code points to throw an exception. With `strict` enabled, `he.encode` either throws (if the input contains invalid code points) or returns a string of valid HTML.\n\nThe `options` object is optional. It recognizes the following properties:\n\n#### `useNamedReferences`\n\nThe default value for the `useNamedReferences` option is `false`. This means that `encode()` will not use any named character references (e.g. `&copy;`) in the output — hexadecimal escapes (e.g. `&#xA9;`) will be used instead. Set it to `true` to enable the use of named references.\n\n**Note that if compatibility with older browsers is a concern, this option should remain disabled.**\n\n```js\n// Using the global default setting (defaults to `false`):\nhe.encode('foo © bar ≠ baz 𝌆 qux');\n// → 'foo &#xA9; bar &#x2260; baz &#x1D306; qux'\n\n// Passing an `options` object to `encode`, to explicitly disallow named references:\nhe.encode('foo © bar ≠ baz 𝌆 qux', {\n  'useNamedReferences': false\n});\n// → 'foo &#xA9; bar &#x2260; baz &#x1D306; qux'\n\n// Passing an `options` object to `encode`, to explicitly allow named references:\nhe.encode('foo © bar ≠ baz 𝌆 qux', {\n  'useNamedReferences': true\n});\n// → 'foo &copy; bar &ne; baz &#x1D306; qux'\n```\n\n#### `decimal`\n\nThe default value for the `decimal` option is `false`. If the option is enabled, `encode` will generally use decimal escapes (e.g. `&#169;`) rather than hexadecimal escapes (e.g. `&#xA9;`). Beside of this replacement, the basic behavior remains the same when combined with other options. For example: if both options `useNamedReferences` and `decimal` are enabled, named references (e.g. `&copy;`) are used over decimal escapes. HTML entities without a named reference are encoded using decimal escapes.\n\n```js\n// Using the global default setting (defaults to `false`):\nhe.encode('foo © bar ≠ baz 𝌆 qux');\n// → 'foo &#xA9; bar &#x2260; baz &#x1D306; qux'\n\n// Passing an `options` object to `encode`, to explicitly disable decimal escapes:\nhe.encode('foo © bar ≠ baz 𝌆 qux', {\n  'decimal': false\n});\n// → 'foo &#xA9; bar &#x2260; baz &#x1D306; qux'\n\n// Passing an `options` object to `encode`, to explicitly enable decimal escapes:\nhe.encode('foo © bar ≠ baz 𝌆 qux', {\n  'decimal': true\n});\n// → 'foo &#169; bar &#8800; baz &#119558; qux'\n\n// Passing an `options` object to `encode`, to explicitly allow named references and decimal escapes:\nhe.encode('foo © bar ≠ baz 𝌆 qux', {\n  'useNamedReferences': true,\n  'decimal': true\n});\n// → 'foo &copy; bar &ne; baz &#119558; qux'\n```\n\n#### `encodeEverything`\n\nThe default value for the `encodeEverything` option is `false`. This means that `encode()` will not use any character references for printable ASCII symbols that don’t need escaping. Set it to `true` to encode every symbol in the input string. When set to `true`, this option takes precedence over `allowUnsafeSymbols` (i.e. setting the latter to `true` in such a case has no effect).\n\n```js\n// Using the global default setting (defaults to `false`):\nhe.encode('foo © bar ≠ baz 𝌆 qux');\n// → 'foo &#xA9; bar &#x2260; baz &#x1D306; qux'\n\n// Passing an `options` object to `encode`, to explicitly encode all symbols:\nhe.encode('foo © bar ≠ baz 𝌆 qux', {\n  'encodeEverything': true\n});\n// → '&#x66;&#x6F;&#x6F;&#x20;&#xA9;&#x20;&#x62;&#x61;&#x72;&#x20;&#x2260;&#x20;&#x62;&#x61;&#x7A;&#x20;&#x1D306;&#x20;&#x71;&#x75;&#x78;'\n\n// This setting can be combined with the `useNamedReferences` option:\nhe.encode('foo © bar ≠ baz 𝌆 qux', {\n  'encodeEverything': true,\n  'useNamedReferences': true\n});\n// → '&#x66;&#x6F;&#x6F;&#x20;&copy;&#x20;&#x62;&#x61;&#x72;&#x20;&ne;&#x20;&#x62;&#x61;&#x7A;&#x20;&#x1D306;&#x20;&#x71;&#x75;&#x78;'\n```\n\n#### `strict`\n\nThe default value for the `strict` option is `false`. This means that `encode()` will encode any HTML text content you feed it, even if it contains any symbols that cause [parse errors](https://html.spec.whatwg.org/multipage/parsing.html#preprocessing-the-input-stream). To throw an error when such invalid HTML is encountered, set the `strict` option to `true`. This option makes it possible to use _he_ as part of HTML parsers and HTML validators.\n\n```js\n// Using the global default setting (defaults to `false`, i.e. error-tolerant mode):\nhe.encode('\\x01');\n// → '&#x1;'\n\n// Passing an `options` object to `encode`, to explicitly enable error-tolerant mode:\nhe.encode('\\x01', {\n  'strict': false\n});\n// → '&#x1;'\n\n// Passing an `options` object to `encode`, to explicitly enable strict mode:\nhe.encode('\\x01', {\n  'strict': true\n});\n// → Parse error\n```\n\n#### `allowUnsafeSymbols`\n\nThe default value for the `allowUnsafeSymbols` option is `false`. This means that characters that are unsafe for use in HTML content (`&`, `<`, `>`, `\"`, `'`, and `` ` ``) will be encoded. When set to `true`, only non-ASCII characters will be encoded. If the `encodeEverything` option is set to `true`, this option will be ignored.\n\n```js\nhe.encode('foo © and & ampersand', {\n  'allowUnsafeSymbols': true\n});\n// → 'foo &#xA9; and & ampersand'\n```\n\n#### Overriding default `encode` options globally\n\nThe global default setting can be overridden by modifying the `he.encode.options` object. This saves you from passing in an `options` object for every call to `encode` if you want to use the non-default setting.\n\n```js\n// Read the global default setting:\nhe.encode.options.useNamedReferences;\n// → `false` by default\n\n// Override the global default setting:\nhe.encode.options.useNamedReferences = true;\n\n// Using the global default setting, which is now `true`:\nhe.encode('foo © bar ≠ baz 𝌆 qux');\n// → 'foo &copy; bar &ne; baz &#x1D306; qux'\n```\n\n### `he.decode(html, options)`\n\nThis function takes a string of HTML and decodes any named and numerical character references in it using [the algorithm described in section ********* of the HTML spec](https://html.spec.whatwg.org/multipage/syntax.html#tokenizing-character-references).\n\n```js\nhe.decode('foo &copy; bar &ne; baz &#x1D306; qux');\n// → 'foo © bar ≠ baz 𝌆 qux'\n```\n\nThe `options` object is optional. It recognizes the following properties:\n\n#### `isAttributeValue`\n\nThe default value for the `isAttributeValue` option is `false`. This means that `decode()` will decode the string as if it were used in [a text context in an HTML document](https://html.spec.whatwg.org/multipage/syntax.html#data-state). HTML has different rules for [parsing character references in attribute values](https://html.spec.whatwg.org/multipage/syntax.html#character-reference-in-attribute-value-state) — set this option to `true` to treat the input string as if it were used as an attribute value.\n\n```js\n// Using the global default setting (defaults to `false`, i.e. HTML text context):\nhe.decode('foo&ampbar');\n// → 'foo&bar'\n\n// Passing an `options` object to `decode`, to explicitly assume an HTML text context:\nhe.decode('foo&ampbar', {\n  'isAttributeValue': false\n});\n// → 'foo&bar'\n\n// Passing an `options` object to `decode`, to explicitly assume an HTML attribute value context:\nhe.decode('foo&ampbar', {\n  'isAttributeValue': true\n});\n// → 'foo&ampbar'\n```\n\n#### `strict`\n\nThe default value for the `strict` option is `false`. This means that `decode()` will decode any HTML text content you feed it, even if it contains any entities that cause [parse errors](https://html.spec.whatwg.org/multipage/syntax.html#tokenizing-character-references). To throw an error when such invalid HTML is encountered, set the `strict` option to `true`. This option makes it possible to use _he_ as part of HTML parsers and HTML validators.\n\n```js\n// Using the global default setting (defaults to `false`, i.e. error-tolerant mode):\nhe.decode('foo&ampbar');\n// → 'foo&bar'\n\n// Passing an `options` object to `decode`, to explicitly enable error-tolerant mode:\nhe.decode('foo&ampbar', {\n  'strict': false\n});\n// → 'foo&bar'\n\n// Passing an `options` object to `decode`, to explicitly enable strict mode:\nhe.decode('foo&ampbar', {\n  'strict': true\n});\n// → Parse error\n```\n\n#### Overriding default `decode` options globally\n\nThe global default settings for the `decode` function can be overridden by modifying the `he.decode.options` object. This saves you from passing in an `options` object for every call to `decode` if you want to use a non-default setting.\n\n```js\n// Read the global default setting:\nhe.decode.options.isAttributeValue;\n// → `false` by default\n\n// Override the global default setting:\nhe.decode.options.isAttributeValue = true;\n\n// Using the global default setting, which is now `true`:\nhe.decode('foo&ampbar');\n// → 'foo&ampbar'\n```\n\n### `he.escape(text)`\n\nThis function takes a string of text and escapes it for use in text contexts in XML or HTML documents. Only the following characters are escaped: `&`, `<`, `>`, `\"`, `'`, and `` ` ``.\n\n```js\nhe.escape('<img src=\\'x\\' onerror=\"prompt(1)\">');\n// → '&lt;img src=&#x27;x&#x27; onerror=&quot;prompt(1)&quot;&gt;'\n```\n\n### `he.unescape(html, options)`\n\n`he.unescape` is an alias for `he.decode`. It takes a string of HTML and decodes any named and numerical character references in it.\n\n### Using the `he` binary\n\nTo use the `he` binary in your shell, simply install _he_ globally using npm:\n\n```bash\nnpm install -g he\n```\n\nAfter that you will be able to encode/decode HTML entities from the command line:\n\n```bash\n$ he --encode 'föo ♥ bår 𝌆 baz'\nf&#xF6;o &#x2665; b&#xE5;r &#x1D306; baz\n\n$ he --encode --use-named-refs 'föo ♥ bår 𝌆 baz'\nf&ouml;o &hearts; b&aring;r &#x1D306; baz\n\n$ he --decode 'f&ouml;o &hearts; b&aring;r &#x1D306; baz'\nföo ♥ bår 𝌆 baz\n```\n\nRead a local text file, encode it for use in an HTML text context, and save the result to a new file:\n\n```bash\n$ he --encode < foo.txt > foo-escaped.html\n```\n\nOr do the same with an online text file:\n\n```bash\n$ curl -sL \"http://git.io/HnfEaw\" | he --encode > escaped.html\n```\n\nOr, the opposite — read a local file containing a snippet of HTML in a text context, decode it back to plain text, and save the result to a new file:\n\n```bash\n$ he --decode < foo-escaped.html > foo.txt\n```\n\nOr do the same with an online HTML snippet:\n\n```bash\n$ curl -sL \"http://git.io/HnfEaw\" | he --decode > decoded.txt\n```\n\nSee `he --help` for the full list of options.\n\n## Support\n\n_he_ has been tested in at least:\n\n* Chrome 27-50\n* Firefox 3-45\n* Safari 4-9\n* Opera 10-12, 15–37\n* IE 6–11\n* Edge\n* Narwhal 0.3.2\n* Node.js v0.10, v0.12, v4, v5\n* PhantomJS 1.9.0\n* Rhino 1.7RC4\n* RingoJS 0.8-0.11\n\n## Unit tests & code coverage\n\nAfter cloning this repository, run `npm install` to install the dependencies needed for he development and testing. You may want to install Istanbul _globally_ using `npm install istanbul -g`.\n\nOnce that’s done, you can run the unit tests in Node using `npm test` or `node tests/tests.js`. To run the tests in Rhino, Ringo, Narwhal, and web browsers as well, use `grunt test`.\n\nTo generate the code coverage report, use `grunt cover`.\n\n## Acknowledgements\n\nThanks to [Simon Pieters](https://simon.html5.org/) ([@zcorpan](https://twitter.com/zcorpan)) for the many suggestions.\n\n## Author\n\n| [![twitter/mathias](https://gravatar.com/avatar/24e08a9ea84deb17ae121074d0f17125?s=70)](https://twitter.com/mathias \"Follow @mathias on Twitter\") |\n|---|\n| [Mathias Bynens](https://mathiasbynens.be/) |\n\n## License\n\n_he_ is available under the [MIT](https://mths.be/mit) license.\n", "man": ["man\\he.1"]}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/he/-/he-1.2.0.tgz#84ae65fa7eafb165fddb61566ae14baf05664f0f", "type": "tarball", "reference": "https://registry.yarnpkg.com/he/-/he-1.2.0.tgz", "hash": "84ae65fa7eafb165fddb61566ae14baf05664f0f", "integrity": "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==", "registry": "npm", "packageName": "he", "cacheIntegrity": "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw== sha1-hK5l+n6vsWX922FWauFLrwVmTw8="}, "registry": "npm", "hash": "84ae65fa7eafb165fddb61566ae14baf05664f0f"}