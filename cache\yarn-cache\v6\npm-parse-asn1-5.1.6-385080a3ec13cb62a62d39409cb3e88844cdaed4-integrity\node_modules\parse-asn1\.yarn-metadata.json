{"manifest": {"name": "parse-asn1", "version": "5.1.6", "description": "utility library for parsing asn1 files for use with browserify-sign.", "main": "index.js", "files": ["asn1.js", "aesid.json", "certificate.js", "fixProc.js", "index.js"], "scripts": {"unit": "node ./test", "standard": "standard", "test": "npm run standard && npm run unit"}, "repository": {"type": "git", "url": "git://github.com/crypto-browserify/parse-asn1.git"}, "author": {}, "license": "ISC", "dependencies": {"asn1.js": "^5.2.0", "browserify-aes": "^1.0.0", "evp_bytestokey": "^1.0.0", "pbkdf2": "^3.0.3", "safe-buffer": "^5.1.1"}, "devDependencies": {"standard": "^14.3.4", "tape": "^5.0.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-parse-asn1-5.1.6-385080a3ec13cb62a62d39409cb3e88844cdaed4-integrity\\node_modules\\parse-asn1\\package.json", "readmeFilename": "README.md", "readme": "# parse-asn1\n\n[![TRAVIS](https://secure.travis-ci.org/crypto-browserify/parse-asn1.png)](http://travis-ci.org/crypto-browserify/parse-asn1)\n[![NPM](http://img.shields.io/npm/v/parse-asn1.svg)](https://www.npmjs.org/package/parse-asn1)\n\n[![js-standard-style](https://cdn.rawgit.com/feross/standard/master/badge.svg)](https://github.com/feross/standard)\n\nutility library for parsing asn1 files for use with browserify-sign.\n", "licenseText": "Copyright (c) 2017, crypto-browserify contributors\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON><PERSON><PERSON> IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\nOR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/parse-asn1/-/parse-asn1-5.1.6.tgz#385080a3ec13cb62a62d39409cb3e88844cdaed4", "type": "tarball", "reference": "https://registry.yarnpkg.com/parse-asn1/-/parse-asn1-5.1.6.tgz", "hash": "385080a3ec13cb62a62d39409cb3e88844cdaed4", "integrity": "sha512-RnZRo1EPU6JBnra2vGHj0yhp6ebyjBZpmUCLHWiFhxlzvBCCpAuZ7elsBp1PVAbQN0/04VD/19rfzlBSwLstMw==", "registry": "npm", "packageName": "parse-asn1", "cacheIntegrity": "sha512-RnZRo1EPU6JBnra2vGHj0yhp6ebyjBZpmUCLHWiFhxlzvBCCpAuZ7elsBp1PVAbQN0/04VD/19rfzlBSwLstMw== sha1-OFCAo+wTy2KmLTlAnLPoiETNrtQ="}, "registry": "npm", "hash": "385080a3ec13cb62a62d39409cb3e88844cdaed4"}