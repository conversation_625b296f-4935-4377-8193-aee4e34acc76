{"manifest": {"name": "p-try", "version": "2.2.0", "description": "`Start a promise chain", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/p-try.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["promise", "try", "resolve", "function", "catch", "async", "await", "promises", "settled", "ponyfill", "polyfill", "shim", "bluebird"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-p-try-2.2.0-cb2868540e313d61de58fafbe35ce9004d5540e6-integrity\\node_modules\\p-try\\package.json", "readmeFilename": "readme.md", "readme": "# p-try [![Build Status](https://travis-ci.org/sindresorhus/p-try.svg?branch=master)](https://travis-ci.org/sindresorhus/p-try)\n\n> Start a promise chain\n\n[How is it useful?](http://cryto.net/~joepie91/blog/2016/05/11/what-is-promise-try-and-why-does-it-matter/)\n\n\n## Install\n\n```\n$ npm install p-try\n```\n\n\n## Usage\n\n```js\nconst pTry = require('p-try');\n\n(async () => {\n\ttry {\n\t\tconst value = await pTry(() => {\n\t\t\treturn synchronousFunctionThatMightThrow();\n\t\t});\n\t\tconsole.log(value);\n\t} catch (error) {\n\t\tconsole.error(error);\n\t}\n})();\n```\n\n\n## API\n\n### pTry(fn, ...arguments)\n\nReturns a `Promise` resolved with the value of calling `fn(...arguments)`. If the function throws an error, the returned `Promise` will be rejected with that error.\n\nSupport for passing arguments on to the `fn` is provided in order to be able to avoid creating unnecessary closures. You probably don't need this optimization unless you're pushing a *lot* of functions.\n\n#### fn\n\nThe function to run to start the promise chain.\n\n#### arguments\n\nArguments to pass to `fn`.\n\n\n## Related\n\n- [p-finally](https://github.com/sindresorhus/p-finally) - `Promise#finally()` ponyfill - Invoked when the promise is settled regardless of outcome\n- [More…](https://github.com/sindresorhus/promise-fun)\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com)\n", "licenseText": "MIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/p-try/-/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6", "type": "tarball", "reference": "https://registry.yarnpkg.com/p-try/-/p-try-2.2.0.tgz", "hash": "cb2868540e313d61de58fafbe35ce9004d5540e6", "integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==", "registry": "npm", "packageName": "p-try", "cacheIntegrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ== sha1-yyhoVA4xPWHeWPr741zpAE1VQOY="}, "registry": "npm", "hash": "cb2868540e313d61de58fafbe35ce9004d5540e6"}