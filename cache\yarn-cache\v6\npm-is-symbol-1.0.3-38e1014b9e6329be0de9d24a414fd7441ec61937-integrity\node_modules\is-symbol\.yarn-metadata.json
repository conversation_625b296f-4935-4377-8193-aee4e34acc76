{"manifest": {"name": "is-symbol", "version": "1.0.3", "description": "Determine if a value is an ES6 Symbol or not.", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "tests-only": "node --es-staging --harmony test", "test": "npm run tests-only", "posttest": "npx aud", "coverage": "covert test", "lint": "eslint .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-symbol.git"}, "keywords": ["symbol", "es6", "is", "Symbol"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-symbol/issues"}, "dependencies": {"has-symbols": "^1.0.1"}, "devDependencies": {"@ljharb/eslint-config": "^15.0.2", "auto-changelog": "^1.16.2", "covert": "^1.1.1", "eslint": "^6.6.0", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "semver": "^6.3.0", "tape": "^4.11.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-symbol-1.0.3-38e1014b9e6329be0de9d24a414fd7441ec61937-integrity\\node_modules\\is-symbol\\package.json", "readmeFilename": "README.md", "readme": "#is-symbol <sup>[![Version Badge][2]][1]</sup>\n\n[![Build Status][3]][4]\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\n[![browser support][9]][10]\n\nIs this an ES6 Symbol value?\n\n## Example\n\n```js\nvar isSymbol = require('is-symbol');\nassert(!isSymbol(function () {}));\nassert(!isSymbol(null));\nassert(!isSymbol(function* () { yield 42; return Infinity; });\n\nassert(isSymbol(Symbol.iterator));\nassert(isSymbol(Symbol('foo')));\nassert(isSymbol(Symbol.for('foo')));\nassert(isSymbol(Object(Symbol('foo'))));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[1]: https://npmjs.org/package/is-symbol\n[2]: http://versionbadg.es/inspect-js/is-symbol.svg\n[3]: https://travis-ci.org/inspect-js/is-symbol.svg\n[4]: https://travis-ci.org/inspect-js/is-symbol\n[5]: https://david-dm.org/inspect-js/is-symbol.svg\n[6]: https://david-dm.org/inspect-js/is-symbol\n[7]: https://david-dm.org/inspect-js/is-symbol/dev-status.svg\n[8]: https://david-dm.org/inspect-js/is-symbol#info=devDependencies\n[11]: https://nodei.co/npm/is-symbol.png?downloads=true&stars=true\n[license-image]: http://img.shields.io/npm/l/is-symbol.svg\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/is-symbol.svg\n[downloads-url]: http://npm-stat.com/charts.html?package=is-symbol\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015 Jordan Harband\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-symbol/-/is-symbol-1.0.3.tgz#38e1014b9e6329be0de9d24a414fd7441ec61937", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-symbol/-/is-symbol-1.0.3.tgz", "hash": "38e1014b9e6329be0de9d24a414fd7441ec61937", "integrity": "sha512-OwijhaRSgqvhm/0ZdAcXNZt9lYdKFpcRDT5ULUuYXPoT794UNOdU+gpT6Rzo7b4V2HUl/op6GqY894AZwv9faQ==", "registry": "npm", "packageName": "is-symbol", "cacheIntegrity": "sha512-OwijhaRSgqvhm/0ZdAcXNZt9lYdKFpcRDT5ULUuYXPoT794UNOdU+gpT6Rzo7b4V2HUl/op6GqY894AZwv9faQ== sha1-OOEBS55jKb4N6dJKQU/XRB7GGTc="}, "registry": "npm", "hash": "38e1014b9e6329be0de9d24a414fd7441ec61937"}