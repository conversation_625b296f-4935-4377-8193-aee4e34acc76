{"manifest": {"name": "end-of-stream", "version": "1.4.4", "description": "Call a callback when a readable/writable/duplex stream has completed or failed.", "repository": {"type": "git", "url": "git://github.com/mafintosh/end-of-stream.git"}, "dependencies": {"once": "^1.4.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["stream", "streams", "callback", "finish", "close", "end", "wait"], "bugs": {"url": "https://github.com/mafintosh/end-of-stream/issues"}, "homepage": "https://github.com/mafintosh/end-of-stream", "main": "index.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "^4.11.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-end-of-stream-1.4.4-5ae64a5f45057baf3626ec14da0ca5e4b2431eb0-integrity\\node_modules\\end-of-stream\\package.json", "readmeFilename": "README.md", "readme": "# end-of-stream\n\nA node module that calls a callback when a readable/writable/duplex stream has completed or failed.\n\n\tnpm install end-of-stream\n\n[![Build status](https://travis-ci.org/mafintosh/end-of-stream.svg?branch=master)](https://travis-ci.org/mafintosh/end-of-stream)\n\n## Usage\n\nSimply pass a stream and a callback to the `eos`.\nBoth legacy streams, streams2 and stream3 are supported.\n\n``` js\nvar eos = require('end-of-stream');\n\neos(readableStream, function(err) {\n  // this will be set to the stream instance\n\tif (err) return console.log('stream had an error or closed early');\n\tconsole.log('stream has ended', this === readableStream);\n});\n\neos(writableStream, function(err) {\n\tif (err) return console.log('stream had an error or closed early');\n\tconsole.log('stream has finished', this === writableStream);\n});\n\neos(duplexStream, function(err) {\n\tif (err) return console.log('stream had an error or closed early');\n\tconsole.log('stream has ended and finished', this === duplexStream);\n});\n\neos(duplexStream, {readable:false}, function(err) {\n\tif (err) return console.log('stream had an error or closed early');\n\tconsole.log('stream has finished but might still be readable');\n});\n\neos(duplexStream, {writable:false}, function(err) {\n\tif (err) return console.log('stream had an error or closed early');\n\tconsole.log('stream has ended but might still be writable');\n});\n\neos(readableStream, {error:false}, function(err) {\n\t// do not treat emit('error', err) as a end-of-stream\n});\n```\n\n## License\n\nMIT\n\n## Related\n\n`end-of-stream` is part of the [mississippi stream utility collection](https://github.com/maxogden/mississippi) which includes more useful stream modules similar to this one.\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 Mathias Buus\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/end-of-stream/-/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0", "type": "tarball", "reference": "https://registry.yarnpkg.com/end-of-stream/-/end-of-stream-1.4.4.tgz", "hash": "5ae64a5f45057baf3626ec14da0ca5e4b2431eb0", "integrity": "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==", "registry": "npm", "packageName": "end-of-stream", "cacheIntegrity": "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q== sha1-WuZKX0UFe682JuwU2gyl5LJDHrA="}, "registry": "npm", "hash": "5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"}