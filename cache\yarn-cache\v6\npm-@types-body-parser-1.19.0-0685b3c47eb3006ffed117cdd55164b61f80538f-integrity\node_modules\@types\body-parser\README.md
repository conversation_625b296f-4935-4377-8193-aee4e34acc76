# Installation
> `npm install --save @types/body-parser`

# Summary
This package contains type definitions for body-parser (https://github.com/expressjs/body-parser).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/body-parser.

### Additional Details
 * Last updated: Mon, 10 Feb 2020 21:19:04 GMT
 * Dependencies: [@types/connect](https://npmjs.com/package/@types/connect), [@types/node](https://npmjs.com/package/@types/node)
 * Global values: none

# Credits
These definitions were written by <PERSON><PERSON> (https://github.com/santialbo), <PERSON><PERSON><PERSON> (https://github.com/vilic), <PERSON> (https://github.com/dreampulse), <PERSON><PERSON><PERSON> (https://github.com/blendsdk), <PERSON><PERSON> (https://github.com/t<PERSON><PERSON><PERSON>), <PERSON> (https://github.com/j<PERSON><PERSON>), and <PERSON><PERSON><PERSON> (https://github.com/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>).
