{"manifest": {"name": "anymatch", "version": "2.0.0", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "files": ["index.js"], "author": {"name": "<PERSON><PERSON>", "url": "http://github.com/es128"}, "license": "ISC", "homepage": "https://github.com/micromatch/anymatch", "repository": {"type": "git", "url": "https://github.com/micromatch/anymatch"}, "bugs": {"url": "https://github.com/micromatch/anymatch/issues"}, "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "scripts": {"test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "dependencies": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}, "devDependencies": {"coveralls": "^2.7.0", "istanbul": "^0.4.5", "mocha": "^3.0.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-anymatch-2.0.0-bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb-integrity\\node_modules\\anymatch\\package.json", "readmeFilename": "README.md", "readme": "anymatch [![Build Status](https://travis-ci.org/micromatch/anymatch.svg?branch=master)](https://travis-ci.org/micromatch/anymatch) [![Coverage Status](https://img.shields.io/coveralls/micromatch/anymatch.svg?branch=master)](https://coveralls.io/r/micromatch/anymatch?branch=master)\n======\nJavascript module to match a string against a regular expression, glob, string,\nor function that takes the string as an argument and returns a truthy or falsy\nvalue. The matcher can also be an array of any or all of these. Useful for\nallowing a very flexible user-defined config to define things like file paths.\n\n__Note: This module has Bash-parity, please be aware that Windows-style backslashes are not supported as separators. See https://github.com/micromatch/micromatch#backslashes for more information.__\n\n[![NPM](https://nodei.co/npm/anymatch.png?downloads=true&downloadRank=true&stars=true)](https://nodei.co/npm/anymatch/)\n[![NPM](https://nodei.co/npm-dl/anymatch.png?height=3&months=9)](https://nodei.co/npm-dl/anymatch/)\n\nUsage\n-----\n```sh\nnpm install anymatch --save\n```\n\n#### anymatch (matchers, testString, [returnIndex], [startIndex], [endIndex])\n* __matchers__: (_Array|String|RegExp|Function_)\nString to be directly matched, string with glob patterns, regular expression\ntest, function that takes the testString as an argument and returns a truthy\nvalue if it should be matched, or an array of any number and mix of these types.\n* __testString__: (_String|Array_) The string to test against the matchers. If\npassed as an array, the first element of the array will be used as the\n`testString` for non-function matchers, while the entire array will be applied\nas the arguments for function matchers.\n* __returnIndex__: (_Boolean [optional]_) If true, return the array index of\nthe first matcher that that testString matched, or -1 if no match, instead of a\nboolean result.\n* __startIndex, endIndex__: (_Integer [optional]_) Can be used to define a\nsubset out of the array of provided matchers to test against. Can be useful\nwith bound matcher functions (see below). When used with `returnIndex = true`\npreserves original indexing. Behaves the same as `Array.prototype.slice` (i.e.\nincludes array members up to, but not including endIndex).\n\n```js\nvar anymatch = require('anymatch');\n\nvar matchers = [\n\t'path/to/file.js',\n\t'path/anyjs/**/*.js',\n\t/foo\\.js$/,\n\tfunction (string) {\n\t\treturn string.indexOf('bar') !== -1 && string.length > 10\n\t}\n];\n\nanymatch(matchers, 'path/to/file.js'); // true\nanymatch(matchers, 'path/anyjs/baz.js'); // true\nanymatch(matchers, 'path/to/foo.js'); // true\nanymatch(matchers, 'path/to/bar.js'); // true\nanymatch(matchers, 'bar.js'); // false\n\n// returnIndex = true\nanymatch(matchers, 'foo.js', true); // 2\nanymatch(matchers, 'path/anyjs/foo.js', true); // 1\n\n// skip matchers\nanymatch(matchers, 'path/to/file.js', false, 1); // false\nanymatch(matchers, 'path/anyjs/foo.js', true, 2, 3); // 2\nanymatch(matchers, 'path/to/bar.js', true, 0, 3); // -1\n\n// using globs to match directories and their children\nanymatch('node_modules', 'node_modules'); // true\nanymatch('node_modules', 'node_modules/somelib/index.js'); // false\nanymatch('node_modules/**', 'node_modules/somelib/index.js'); // true\nanymatch('node_modules/**', '/absolute/path/to/node_modules/somelib/index.js'); // false\nanymatch('**/node_modules/**', '/absolute/path/to/node_modules/somelib/index.js'); // true\n```\n\n#### anymatch (matchers)\nYou can also pass in only your matcher(s) to get a curried function that has\nalready been bound to the provided matching criteria. This can be used as an\n`Array.prototype.filter` callback.\n\n```js\nvar matcher = anymatch(matchers);\n\nmatcher('path/to/file.js'); // true\nmatcher('path/anyjs/baz.js', true); // 1\nmatcher('path/anyjs/baz.js', true, 2); // -1\n\n['foo.js', 'bar.js'].filter(matcher); // ['foo.js']\n```\n\nChange Log\n----------\n[See release notes page on GitHub](https://github.com/micromatch/anymatch/releases)\n\nNOTE: As of v2.0.0, [micromatch](https://github.com/jonschlinkert/micromatch) moves away from minimatch-parity and inline with Bash. This includes handling backslashes differently (see https://github.com/micromatch/micromatch#backslashes for more information).\n\nNOTE: As of v1.2.0, anymatch uses [micromatch](https://github.com/jonschlinkert/micromatch)\nfor glob pattern matching. Issues with glob pattern matching should be\nreported directly to the [micromatch issue tracker](https://github.com/jonschlinkert/micromatch/issues).\n\nLicense\n-------\n[ISC](https://raw.github.com/micromatch/anymatch/master/LICENSE)\n", "licenseText": "The ISC License\n\nCopyright (c) 2014 <PERSON><PERSON>\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SO<PERSON>WARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON><PERSON><PERSON> IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/anymatch/-/anymatch-2.0.0.tgz#bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb", "type": "tarball", "reference": "https://registry.yarnpkg.com/anymatch/-/anymatch-2.0.0.tgz", "hash": "bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb", "integrity": "sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw==", "registry": "npm", "packageName": "anymatch", "cacheIntegrity": "sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw== sha1-vLJLTzeTTZqnrBe0ra+J58du8us="}, "registry": "npm", "hash": "bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb"}