{"manifest": {"name": "ajv", "version": "6.12.6", "description": "Another JSON Schema Validator", "main": "lib/ajv.js", "typings": "lib/ajv.d.ts", "files": ["lib/", "dist/", "scripts/", "LICENSE", ".tonic_example.js"], "scripts": {"eslint": "eslint lib/{compile/,}*.js spec/{**/,}*.js scripts --ignore-pattern spec/JSON-Schema-Test-Suite", "jshint": "jshint lib/{compile/,}*.js", "lint": "npm run jshint && npm run eslint", "test-spec": "mocha spec/{**/,}*.spec.js -R spec", "test-fast": "AJV_FAST_TEST=true npm run test-spec", "test-debug": "npm run test-spec -- --inspect-brk", "test-cov": "nyc npm run test-spec", "test-ts": "tsc --target ES5 --noImplicitAny --noEmit spec/typescript/index.ts", "bundle": "del-cli dist && node ./scripts/bundle.js . Ajv pure_getters", "bundle-beautify": "node ./scripts/bundle.js js-beautify", "build": "del-cli lib/dotjs/*.js \"!lib/dotjs/index.js\" && node scripts/compile-dots.js", "test-karma": "karma start", "test-browser": "del-cli .browser && npm run bundle && scripts/prepare-tests && npm run test-karma", "test-all": "npm run test-cov && if-node-version 10 npm run test-browser", "test": "npm run lint && npm run build && npm run test-all", "prepublish": "npm run build && npm run bundle", "watch": "watch \"npm run build\" ./lib/dot"}, "nyc": {"exclude": ["**/spec/**", "node_modules"], "reporter": ["lcov", "text-summary"]}, "repository": {"type": "git", "url": "https://github.com/ajv-validator/ajv.git"}, "keywords": ["JSON", "schema", "validator", "validation", "jsonschema", "json-schema", "json-schema-validator", "json-schema-validation"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/ajv-validator/ajv/issues"}, "homepage": "https://github.com/ajv-validator/ajv", "tonicExampleFilename": ".tonic_example.js", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "devDependencies": {"ajv-async": "^1.0.0", "bluebird": "^3.5.3", "brfs": "^2.0.0", "browserify": "^16.2.0", "chai": "^4.0.1", "coveralls": "^3.0.1", "del-cli": "^3.0.0", "dot": "^1.0.3", "eslint": "^7.3.1", "gh-pages-generator": "^0.2.3", "glob": "^7.0.0", "if-node-version": "^1.0.0", "js-beautify": "^1.7.3", "jshint": "^2.10.2", "json-schema-test": "^2.0.0", "karma": "^5.0.0", "karma-chrome-launcher": "^3.0.0", "karma-mocha": "^2.0.0", "karma-sauce-launcher": "^4.1.3", "mocha": "^8.0.1", "nyc": "^15.0.0", "pre-commit": "^1.1.1", "require-globify": "^1.3.0", "typescript": "^3.9.5", "uglify-js": "^3.6.9", "watch": "^1.0.0"}, "collective": {"type": "opencollective", "url": "https://opencollective.com/ajv"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-ajv-6.12.6-baf5a62e802b07d977034586f8c3baf5adf26df4-integrity\\node_modules\\ajv\\package.json", "readmeFilename": "README.md", "readme": "<img align=\"right\" alt=\"Ajv logo\" width=\"160\" src=\"https://ajv.js.org/images/ajv_logo.png\">\n\n# Ajv: Another JSON Schema Validator\n\nThe fastest JSON Schema validator for Node.js and browser. Supports draft-04/06/07.\n\n[![Build Status](https://travis-ci.org/ajv-validator/ajv.svg?branch=master)](https://travis-ci.org/ajv-validator/ajv)\n[![npm](https://img.shields.io/npm/v/ajv.svg)](https://www.npmjs.com/package/ajv)\n[![npm (beta)](https://img.shields.io/npm/v/ajv/beta)](https://www.npmjs.com/package/ajv/v/7.0.0-beta.0)\n[![npm downloads](https://img.shields.io/npm/dm/ajv.svg)](https://www.npmjs.com/package/ajv)\n[![Coverage Status](https://coveralls.io/repos/github/ajv-validator/ajv/badge.svg?branch=master)](https://coveralls.io/github/ajv-validator/ajv?branch=master)\n[![Gitter](https://img.shields.io/gitter/room/ajv-validator/ajv.svg)](https://gitter.im/ajv-validator/ajv)\n[![GitHub Sponsors](https://img.shields.io/badge/$-sponsors-brightgreen)](https://github.com/sponsors/epoberezkin)\n\n\n## Ajv v7 beta is released\n\n[Ajv version 7.0.0-beta.0](https://github.com/ajv-validator/ajv/tree/v7-beta) is released with these changes:\n\n- to reduce the mistakes in JSON schemas and unexpected validation results, [strict mode](./docs/strict-mode.md) is added - it prohibits ignored or ambiguous JSON Schema elements.\n- to make code injection from untrusted schemas impossible, [code generation](./docs/codegen.md) is fully re-written to be safe.\n- to simplify Ajv extensions, the new keyword API that is used by pre-defined keywords is available to user-defined keywords - it is much easier to define any keywords now, especially with subschemas.\n- schemas are compiled to ES6 code (ES5 code generation is supported with an option).\n- to improve reliability and maintainability the code is migrated to TypeScript.\n\n**Please note**:\n\n- the support for JSON-Schema draft-04 is removed - if you have schemas using \"id\" attributes you have to replace them with \"\\$id\" (or continue using version 6 that will be supported until 02/28/2021).\n- all formats are separated to ajv-formats package - they have to be explicitely added if you use them.\n\nSee [release notes](https://github.com/ajv-validator/ajv/releases/tag/v7.0.0-beta.0) for the details.\n\nTo install the new version:\n\n```bash\nnpm install ajv@beta\n```\n\nSee [Getting started with v7](https://github.com/ajv-validator/ajv/tree/v7-beta#usage) for code example.\n\n\n## Mozilla MOSS grant and OpenJS Foundation\n\n[<img src=\"https://www.poberezkin.com/images/mozilla.png\" width=\"240\" height=\"68\">](https://www.mozilla.org/en-US/moss/) &nbsp;&nbsp;&nbsp; [<img src=\"https://www.poberezkin.com/images/openjs.png\" width=\"220\" height=\"68\">](https://openjsf.org/blog/2020/08/14/ajv-joins-openjs-foundation-as-an-incubation-project/)\n\nAjv has been awarded a grant from Mozilla’s [Open Source Support (MOSS) program](https://www.mozilla.org/en-US/moss/) in the “Foundational Technology” track! It will sponsor the development of Ajv support of [JSON Schema version 2019-09](https://tools.ietf.org/html/draft-handrews-json-schema-02) and of [JSON Type Definition](https://tools.ietf.org/html/draft-ucarion-json-type-definition-04).\n\nAjv also joined [OpenJS Foundation](https://openjsf.org/) – having this support will help ensure the longevity and stability of Ajv for all its users.\n\nThis [blog post](https://www.poberezkin.com/posts/2020-08-14-ajv-json-validator-mozilla-open-source-grant-openjs-foundation.html) has more details.\n\nI am looking for the long term maintainers of Ajv – working with [ReadySet](https://www.thereadyset.co/), also sponsored by Mozilla, to establish clear guidelines for the role of a \"maintainer\" and the contribution standards, and to encourage a wider, more inclusive, contribution from the community.\n\n\n## Please [sponsor Ajv development](https://github.com/sponsors/epoberezkin)\n\nSince I asked to support Ajv development 40 people and 6 organizations contributed via GitHub and OpenCollective - this support helped receiving the MOSS grant!\n\nYour continuing support is very important - the funds will be used to develop and maintain Ajv once the next major version is released.\n\nPlease sponsor Ajv via:\n- [GitHub sponsors page](https://github.com/sponsors/epoberezkin) (GitHub will match it)\n- [Ajv Open Collective️](https://opencollective.com/ajv)\n\nThank you.\n\n\n#### Open Collective sponsors\n\n<a href=\"https://opencollective.com/ajv\"><img src=\"https://opencollective.com/ajv/individuals.svg?width=890\"></a>\n\n<a href=\"https://opencollective.com/ajv/organization/0/website\"><img src=\"https://opencollective.com/ajv/organization/0/avatar.svg\"></a>\n<a href=\"https://opencollective.com/ajv/organization/1/website\"><img src=\"https://opencollective.com/ajv/organization/1/avatar.svg\"></a>\n<a href=\"https://opencollective.com/ajv/organization/2/website\"><img src=\"https://opencollective.com/ajv/organization/2/avatar.svg\"></a>\n<a href=\"https://opencollective.com/ajv/organization/3/website\"><img src=\"https://opencollective.com/ajv/organization/3/avatar.svg\"></a>\n<a href=\"https://opencollective.com/ajv/organization/4/website\"><img src=\"https://opencollective.com/ajv/organization/4/avatar.svg\"></a>\n<a href=\"https://opencollective.com/ajv/organization/5/website\"><img src=\"https://opencollective.com/ajv/organization/5/avatar.svg\"></a>\n<a href=\"https://opencollective.com/ajv/organization/6/website\"><img src=\"https://opencollective.com/ajv/organization/6/avatar.svg\"></a>\n<a href=\"https://opencollective.com/ajv/organization/7/website\"><img src=\"https://opencollective.com/ajv/organization/7/avatar.svg\"></a>\n<a href=\"https://opencollective.com/ajv/organization/8/website\"><img src=\"https://opencollective.com/ajv/organization/8/avatar.svg\"></a>\n<a href=\"https://opencollective.com/ajv/organization/9/website\"><img src=\"https://opencollective.com/ajv/organization/9/avatar.svg\"></a>\n\n\n## Using version 6\n\n[JSON Schema draft-07](http://json-schema.org/latest/json-schema-validation.html) is published.\n\n[Ajv version 6.0.0](https://github.com/ajv-validator/ajv/releases/tag/v6.0.0) that supports draft-07 is released. It may require either migrating your schemas or updating your code (to continue using draft-04 and v5 schemas, draft-06 schemas will be supported without changes).\n\n__Please note__: To use Ajv with draft-06 schemas you need to explicitly add the meta-schema to the validator instance:\n\n```javascript\najv.addMetaSchema(require('ajv/lib/refs/json-schema-draft-06.json'));\n```\n\nTo use Ajv with draft-04 schemas in addition to explicitly adding meta-schema you also need to use option schemaId:\n\n```javascript\nvar ajv = new Ajv({schemaId: 'id'});\n// If you want to use both draft-04 and draft-06/07 schemas:\n// var ajv = new Ajv({schemaId: 'auto'});\najv.addMetaSchema(require('ajv/lib/refs/json-schema-draft-04.json'));\n```\n\n\n## Contents\n\n- [Performance](#performance)\n- [Features](#features)\n- [Getting started](#getting-started)\n- [Frequently Asked Questions](https://github.com/ajv-validator/ajv/blob/master/FAQ.md)\n- [Using in browser](#using-in-browser)\n  - [Ajv and Content Security Policies (CSP)](#ajv-and-content-security-policies-csp)\n- [Command line interface](#command-line-interface)\n- Validation\n  - [Keywords](#validation-keywords)\n  - [Annotation keywords](#annotation-keywords)\n  - [Formats](#formats)\n  - [Combining schemas with $ref](#ref)\n  - [$data reference](#data-reference)\n  - NEW: [$merge and $patch keywords](#merge-and-patch-keywords)\n  - [Defining custom keywords](#defining-custom-keywords)\n  - [Asynchronous schema compilation](#asynchronous-schema-compilation)\n  - [Asynchronous validation](#asynchronous-validation)\n- [Security considerations](#security-considerations)\n  - [Security contact](#security-contact)\n  - [Untrusted schemas](#untrusted-schemas)\n  - [Circular references in objects](#circular-references-in-javascript-objects)\n  - [Trusted schemas](#security-risks-of-trusted-schemas)\n  - [ReDoS attack](#redos-attack)\n- Modifying data during validation\n  - [Filtering data](#filtering-data)\n  - [Assigning defaults](#assigning-defaults)\n  - [Coercing data types](#coercing-data-types)\n- API\n  - [Methods](#api)\n  - [Options](#options)\n  - [Validation errors](#validation-errors)\n- [Plugins](#plugins)\n- [Related packages](#related-packages)\n- [Some packages using Ajv](#some-packages-using-ajv)\n- [Tests, Contributing, Changes history](#tests)\n- [Support, Code of conduct, License](#open-source-software-support)\n\n\n## Performance\n\nAjv generates code using [doT templates](https://github.com/olado/doT) to turn JSON Schemas into super-fast validation functions that are efficient for v8 optimization.\n\nCurrently Ajv is the fastest and the most standard compliant validator according to these benchmarks:\n\n- [json-schema-benchmark](https://github.com/ebdrup/json-schema-benchmark) - 50% faster than the second place\n- [jsck benchmark](https://github.com/pandastrike/jsck#benchmarks) - 20-190% faster\n- [z-schema benchmark](https://rawgit.com/zaggino/z-schema/master/benchmark/results.html)\n- [themis benchmark](https://cdn.rawgit.com/playlyfe/themis/master/benchmark/results.html)\n\n\nPerformance of different validators by [json-schema-benchmark](https://github.com/ebdrup/json-schema-benchmark):\n\n[![performance](https://chart.googleapis.com/chart?chxt=x,y&cht=bhs&chco=76A4FB&chls=2.0&chbh=32,4,1&chs=600x416&chxl=-1:|djv|ajv|json-schema-validator-generator|jsen|is-my-json-valid|themis|z-schema|jsck|skeemas|json-schema-library|tv4&chd=t:100,98,72.1,66.8,50.1,15.1,6.1,3.8,1.2,0.7,0.2)](https://github.com/ebdrup/json-schema-benchmark/blob/master/README.md#performance)\n\n\n## Features\n\n- Ajv implements full JSON Schema [draft-06/07](http://json-schema.org/) and draft-04 standards:\n  - all validation keywords (see [JSON Schema validation keywords](https://github.com/ajv-validator/ajv/blob/master/KEYWORDS.md))\n  - full support of remote refs (remote schemas have to be added with `addSchema` or compiled to be available)\n  - support of circular references between schemas\n  - correct string lengths for strings with unicode pairs (can be turned off)\n  - [formats](#formats) defined by JSON Schema draft-07 standard and custom formats (can be turned off)\n  - [validates schemas against meta-schema](#api-validateschema)\n- supports [browsers](#using-in-browser) and Node.js 0.10-14.x\n- [asynchronous loading](#asynchronous-schema-compilation) of referenced schemas during compilation\n- \"All errors\" validation mode with [option allErrors](#options)\n- [error messages with parameters](#validation-errors) describing error reasons to allow creating custom error messages\n- i18n error messages support with [ajv-i18n](https://github.com/ajv-validator/ajv-i18n) package\n- [filtering data](#filtering-data) from additional properties\n- [assigning defaults](#assigning-defaults) to missing properties and items\n- [coercing data](#coercing-data-types) to the types specified in `type` keywords\n- [custom keywords](#defining-custom-keywords)\n- draft-06/07 keywords `const`, `contains`, `propertyNames` and `if/then/else`\n- draft-06 boolean schemas (`true`/`false` as a schema to always pass/fail).\n- keywords `switch`, `patternRequired`, `formatMaximum` / `formatMinimum` and `formatExclusiveMaximum` / `formatExclusiveMinimum` from [JSON Schema extension proposals](https://github.com/json-schema/json-schema/wiki/v5-Proposals) with [ajv-keywords](https://github.com/ajv-validator/ajv-keywords) package\n- [$data reference](#data-reference) to use values from the validated data as values for the schema keywords\n- [asynchronous validation](#asynchronous-validation) of custom formats and keywords\n\n\n## Install\n\n```\nnpm install ajv\n```\n\n\n## <a name=\"usage\"></a>Getting started\n\nTry it in the Node.js REPL: https://tonicdev.com/npm/ajv\n\n\nThe fastest validation call:\n\n```javascript\n// Node.js require:\nvar Ajv = require('ajv');\n// or ESM/TypeScript import\nimport Ajv from 'ajv';\n\nvar ajv = new Ajv(); // options can be passed, e.g. {allErrors: true}\nvar validate = ajv.compile(schema);\nvar valid = validate(data);\nif (!valid) console.log(validate.errors);\n```\n\nor with less code\n\n```javascript\n// ...\nvar valid = ajv.validate(schema, data);\nif (!valid) console.log(ajv.errors);\n// ...\n```\n\nor\n\n```javascript\n// ...\nvar valid = ajv.addSchema(schema, 'mySchema')\n               .validate('mySchema', data);\nif (!valid) console.log(ajv.errorsText());\n// ...\n```\n\nSee [API](#api) and [Options](#options) for more details.\n\nAjv compiles schemas to functions and caches them in all cases (using schema serialized with [fast-json-stable-stringify](https://github.com/epoberezkin/fast-json-stable-stringify) or a custom function as a key), so that the next time the same schema is used (not necessarily the same object instance) it won't be compiled again.\n\nThe best performance is achieved when using compiled functions returned by `compile` or `getSchema` methods (there is no additional function call).\n\n__Please note__: every time a validation function or `ajv.validate` are called `errors` property is overwritten. You need to copy `errors` array reference to another variable if you want to use it later (e.g., in the callback). See [Validation errors](#validation-errors)\n\n__Note for TypeScript users__: `ajv` provides its own TypeScript declarations\nout of the box, so you don't need to install the deprecated `@types/ajv`\nmodule.\n\n\n## Using in browser\n\nYou can require Ajv directly from the code you browserify - in this case Ajv will be a part of your bundle.\n\nIf you need to use Ajv in several bundles you can create a separate UMD bundle using `npm run bundle` script (thanks to [siddo420](https://github.com/siddo420)).\n\nThen you need to load Ajv in the browser:\n```html\n<script src=\"ajv.min.js\"></script>\n```\n\nThis bundle can be used with different module systems; it creates global `Ajv` if no module system is found.\n\nThe browser bundle is available on [cdnjs](https://cdnjs.com/libraries/ajv).\n\nAjv is tested with these browsers:\n\n[![Sauce Test Status](https://saucelabs.com/browser-matrix/epoberezkin.svg)](https://saucelabs.com/u/epoberezkin)\n\n__Please note__: some frameworks, e.g. Dojo, may redefine global require in such way that is not compatible with CommonJS module format. In such case Ajv bundle has to be loaded before the framework and then you can use global Ajv (see issue [#234](https://github.com/ajv-validator/ajv/issues/234)).\n\n\n### Ajv and Content Security Policies (CSP)\n\nIf you're using Ajv to compile a schema (the typical use) in a browser document that is loaded with a Content Security Policy (CSP), that policy will require a `script-src` directive that includes the value `'unsafe-eval'`.\n:warning: NOTE, however, that `unsafe-eval` is NOT recommended in a secure CSP[[1]](https://developer.chrome.com/extensions/contentSecurityPolicy#relaxing-eval), as it has the potential to open the document to cross-site scripting (XSS) attacks.\n\nIn order to make use of Ajv without easing your CSP, you can [pre-compile a schema using the CLI](https://github.com/ajv-validator/ajv-cli#compile-schemas). This will transpile the schema JSON into a JavaScript file that exports a `validate` function that works simlarly to a schema compiled at runtime.\n\nNote that pre-compilation of schemas is performed using [ajv-pack](https://github.com/ajv-validator/ajv-pack) and there are [some limitations to the schema features it can compile](https://github.com/ajv-validator/ajv-pack#limitations). A successfully pre-compiled schema is equivalent to the same schema compiled at runtime.\n\n\n## Command line interface\n\nCLI is available as a separate npm package [ajv-cli](https://github.com/ajv-validator/ajv-cli). It supports:\n\n- compiling JSON Schemas to test their validity\n- BETA: generating standalone module exporting a validation function to be used without Ajv (using [ajv-pack](https://github.com/ajv-validator/ajv-pack))\n- migrate schemas to draft-07 (using [json-schema-migrate](https://github.com/epoberezkin/json-schema-migrate))\n- validating data file(s) against JSON Schema\n- testing expected validity of data against JSON Schema\n- referenced schemas\n- custom meta-schemas\n- files in JSON, JSON5, YAML, and JavaScript format\n- all Ajv options\n- reporting changes in data after validation in [JSON-patch](https://tools.ietf.org/html/rfc6902) format\n\n\n## Validation keywords\n\nAjv supports all validation keywords from draft-07 of JSON Schema standard:\n\n- [type](https://github.com/ajv-validator/ajv/blob/master/KEYWORDS.md#type)\n- [for numbers](https://github.com/ajv-validator/ajv/blob/master/KEYWORDS.md#keywords-for-numbers) - maximum, minimum, exclusiveMaximum, exclusiveMinimum, multipleOf\n- [for strings](https://github.com/ajv-validator/ajv/blob/master/KEYWORDS.md#keywords-for-strings) - maxLength, minLength, pattern, format\n- [for arrays](https://github.com/ajv-validator/ajv/blob/master/KEYWORDS.md#keywords-for-arrays) - maxItems, minItems, uniqueItems, items, additionalItems, [contains](https://github.com/ajv-validator/ajv/blob/master/KEYWORDS.md#contains)\n- [for objects](https://github.com/ajv-validator/ajv/blob/master/KEYWORDS.md#keywords-for-objects) - maxProperties, minProperties, required, properties, patternProperties, additionalProperties, dependencies, [propertyNames](https://github.com/ajv-validator/ajv/blob/master/KEYWORDS.md#propertynames)\n- [for all types](https://github.com/ajv-validator/ajv/blob/master/KEYWORDS.md#keywords-for-all-types) - enum, [const](https://github.com/ajv-validator/ajv/blob/master/KEYWORDS.md#const)\n- [compound keywords](https://github.com/ajv-validator/ajv/blob/master/KEYWORDS.md#compound-keywords) - not, oneOf, anyOf, allOf, [if/then/else](https://github.com/ajv-validator/ajv/blob/master/KEYWORDS.md#ifthenelse)\n\nWith [ajv-keywords](https://github.com/ajv-validator/ajv-keywords) package Ajv also supports validation keywords from [JSON Schema extension proposals](https://github.com/json-schema/json-schema/wiki/v5-Proposals) for JSON Schema standard:\n\n- [patternRequired](https://github.com/ajv-validator/ajv/blob/master/KEYWORDS.md#patternrequired-proposed) - like `required` but with patterns that some property should match.\n- [formatMaximum, formatMinimum, formatExclusiveMaximum, formatExclusiveMinimum](https://github.com/ajv-validator/ajv/blob/master/KEYWORDS.md#formatmaximum--formatminimum-and-exclusiveformatmaximum--exclusiveformatminimum-proposed) - setting limits for date, time, etc.\n\nSee [JSON Schema validation keywords](https://github.com/ajv-validator/ajv/blob/master/KEYWORDS.md) for more details.\n\n\n## Annotation keywords\n\nJSON Schema specification defines several annotation keywords that describe schema itself but do not perform any validation.\n\n- `title` and `description`: information about the data represented by that schema\n- `$comment` (NEW in draft-07): information for developers. With option `$comment` Ajv logs or passes the comment string to the user-supplied function. See [Options](#options).\n- `default`: a default value of the data instance, see [Assigning defaults](#assigning-defaults).\n- `examples` (NEW in draft-06): an array of data instances. Ajv does not check the validity of these instances against the schema.\n- `readOnly` and `writeOnly` (NEW in draft-07): marks data-instance as read-only or write-only in relation to the source of the data (database, api, etc.).\n- `contentEncoding`: [RFC 2045](https://tools.ietf.org/html/rfc2045#section-6.1 ), e.g., \"base64\".\n- `contentMediaType`: [RFC 2046](https://tools.ietf.org/html/rfc2046), e.g., \"image/png\".\n\n__Please note__:  Ajv does not implement validation of the keywords `examples`, `contentEncoding` and `contentMediaType` but it reserves them. If you want to create a plugin that implements some of them, it should remove these keywords from the instance.\n\n\n## Formats\n\nAjv implements formats defined by JSON Schema specification and several other formats. It is recommended NOT to use \"format\" keyword implementations with untrusted data, as they use potentially unsafe regular expressions - see [ReDoS attack](#redos-attack).\n\n__Please note__: if you need to use \"format\" keyword to validate untrusted data, you MUST assess their suitability and safety for your validation scenarios.\n\nThe following formats are implemented for string validation with \"format\" keyword:\n\n- _date_: full-date according to [RFC3339](http://tools.ietf.org/html/rfc3339#section-5.6).\n- _time_: time with optional time-zone.\n- _date-time_: date-time from the same source (time-zone is mandatory). `date`, `time` and `date-time` validate ranges in `full` mode and only regexp in `fast` mode (see [options](#options)).\n- _uri_: full URI.\n- _uri-reference_: URI reference, including full and relative URIs.\n- _uri-template_: URI template according to [RFC6570](https://tools.ietf.org/html/rfc6570)\n- _url_ (deprecated): [URL record](https://url.spec.whatwg.org/#concept-url).\n- _email_: email address.\n- _hostname_: host name according to [RFC1034](http://tools.ietf.org/html/rfc1034#section-3.5).\n- _ipv4_: IP address v4.\n- _ipv6_: IP address v6.\n- _regex_: tests whether a string is a valid regular expression by passing it to RegExp constructor.\n- _uuid_: Universally Unique IDentifier according to [RFC4122](http://tools.ietf.org/html/rfc4122).\n- _json-pointer_: JSON-pointer according to [RFC6901](https://tools.ietf.org/html/rfc6901).\n- _relative-json-pointer_: relative JSON-pointer according to [this draft](http://tools.ietf.org/html/draft-luff-relative-json-pointer-00).\n\n__Please note__: JSON Schema draft-07 also defines formats `iri`, `iri-reference`, `idn-hostname` and `idn-email` for URLs, hostnames and emails with international characters. Ajv does not implement these formats. If you create Ajv plugin that implements them please make a PR to mention this plugin here.\n\nThere are two modes of format validation: `fast` and `full`. This mode affects formats `date`, `time`, `date-time`, `uri`, `uri-reference`, and `email`. See [Options](#options) for details.\n\nYou can add additional formats and replace any of the formats above using [addFormat](#api-addformat) method.\n\nThe option `unknownFormats` allows changing the default behaviour when an unknown format is encountered. In this case Ajv can either fail schema compilation (default) or ignore it (default in versions before 5.0.0). You also can allow specific format(s) that will be ignored. See [Options](#options) for details.\n\nYou can find regular expressions used for format validation and the sources that were used in [formats.js](https://github.com/ajv-validator/ajv/blob/master/lib/compile/formats.js).\n\n\n## <a name=\"ref\"></a>Combining schemas with $ref\n\nYou can structure your validation logic across multiple schema files and have schemas reference each other using `$ref` keyword.\n\nExample:\n\n```javascript\nvar schema = {\n  \"$id\": \"http://example.com/schemas/schema.json\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"foo\": { \"$ref\": \"defs.json#/definitions/int\" },\n    \"bar\": { \"$ref\": \"defs.json#/definitions/str\" }\n  }\n};\n\nvar defsSchema = {\n  \"$id\": \"http://example.com/schemas/defs.json\",\n  \"definitions\": {\n    \"int\": { \"type\": \"integer\" },\n    \"str\": { \"type\": \"string\" }\n  }\n};\n```\n\nNow to compile your schema you can either pass all schemas to Ajv instance:\n\n```javascript\nvar ajv = new Ajv({schemas: [schema, defsSchema]});\nvar validate = ajv.getSchema('http://example.com/schemas/schema.json');\n```\n\nor use `addSchema` method:\n\n```javascript\nvar ajv = new Ajv;\nvar validate = ajv.addSchema(defsSchema)\n                  .compile(schema);\n```\n\nSee [Options](#options) and [addSchema](#api) method.\n\n__Please note__:\n- `$ref` is resolved as the uri-reference using schema $id as the base URI (see the example).\n- References can be recursive (and mutually recursive) to implement the schemas for different data structures (such as linked lists, trees, graphs, etc.).\n- You don't have to host your schema files at the URIs that you use as schema $id. These URIs are only used to identify the schemas, and according to JSON Schema specification validators should not expect to be able to download the schemas from these URIs.\n- The actual location of the schema file in the file system is not used.\n- You can pass the identifier of the schema as the second parameter of `addSchema` method or as a property name in `schemas` option. This identifier can be used instead of (or in addition to) schema $id.\n- You cannot have the same $id (or the schema identifier) used for more than one schema - the exception will be thrown.\n- You can implement dynamic resolution of the referenced schemas using `compileAsync` method. In this way you can store schemas in any system (files, web, database, etc.) and reference them without explicitly adding to Ajv instance. See [Asynchronous schema compilation](#asynchronous-schema-compilation).\n\n\n## $data reference\n\nWith `$data` option you can use values from the validated data as the values for the schema keywords. See [proposal](https://github.com/json-schema-org/json-schema-spec/issues/51) for more information about how it works.\n\n`$data` reference is supported in the keywords: const, enum, format, maximum/minimum, exclusiveMaximum / exclusiveMinimum, maxLength / minLength, maxItems / minItems, maxProperties / minProperties, formatMaximum / formatMinimum, formatExclusiveMaximum / formatExclusiveMinimum, multipleOf, pattern, required, uniqueItems.\n\nThe value of \"$data\" should be a [JSON-pointer](https://tools.ietf.org/html/rfc6901) to the data (the root is always the top level data object, even if the $data reference is inside a referenced subschema) or a [relative JSON-pointer](http://tools.ietf.org/html/draft-luff-relative-json-pointer-00) (it is relative to the current point in data; if the $data reference is inside a referenced subschema it cannot point to the data outside of the root level for this subschema).\n\nExamples.\n\nThis schema requires that the value in property `smaller` is less or equal than the value in the property larger:\n\n```javascript\nvar ajv = new Ajv({$data: true});\n\nvar schema = {\n  \"properties\": {\n    \"smaller\": {\n      \"type\": \"number\",\n      \"maximum\": { \"$data\": \"1/larger\" }\n    },\n    \"larger\": { \"type\": \"number\" }\n  }\n};\n\nvar validData = {\n  smaller: 5,\n  larger: 7\n};\n\najv.validate(schema, validData); // true\n```\n\nThis schema requires that the properties have the same format as their field names:\n\n```javascript\nvar schema = {\n  \"additionalProperties\": {\n    \"type\": \"string\",\n    \"format\": { \"$data\": \"0#\" }\n  }\n};\n\nvar validData = {\n  'date-time': '1963-06-19T08:30:06.283185Z',\n  email: '<EMAIL>'\n}\n```\n\n`$data` reference is resolved safely - it won't throw even if some property is undefined. If `$data` resolves to `undefined` the validation succeeds (with the exclusion of `const` keyword). If `$data` resolves to incorrect type (e.g. not \"number\" for maximum keyword) the validation fails.\n\n\n## $merge and $patch keywords\n\nWith the package [ajv-merge-patch](https://github.com/ajv-validator/ajv-merge-patch) you can use the keywords `$merge` and `$patch` that allow extending JSON Schemas with patches using formats [JSON Merge Patch (RFC 7396)](https://tools.ietf.org/html/rfc7396) and [JSON Patch (RFC 6902)](https://tools.ietf.org/html/rfc6902).\n\nTo add keywords `$merge` and `$patch` to Ajv instance use this code:\n\n```javascript\nrequire('ajv-merge-patch')(ajv);\n```\n\nExamples.\n\nUsing `$merge`:\n\n```json\n{\n  \"$merge\": {\n    \"source\": {\n      \"type\": \"object\",\n      \"properties\": { \"p\": { \"type\": \"string\" } },\n      \"additionalProperties\": false\n    },\n    \"with\": {\n      \"properties\": { \"q\": { \"type\": \"number\" } }\n    }\n  }\n}\n```\n\nUsing `$patch`:\n\n```json\n{\n  \"$patch\": {\n    \"source\": {\n      \"type\": \"object\",\n      \"properties\": { \"p\": { \"type\": \"string\" } },\n      \"additionalProperties\": false\n    },\n    \"with\": [\n      { \"op\": \"add\", \"path\": \"/properties/q\", \"value\": { \"type\": \"number\" } }\n    ]\n  }\n}\n```\n\nThe schemas above are equivalent to this schema:\n\n```json\n{\n  \"type\": \"object\",\n  \"properties\": {\n    \"p\": { \"type\": \"string\" },\n    \"q\": { \"type\": \"number\" }\n  },\n  \"additionalProperties\": false\n}\n```\n\nThe properties `source` and `with` in the keywords `$merge` and `$patch` can use absolute or relative `$ref` to point to other schemas previously added to the Ajv instance or to the fragments of the current schema.\n\nSee the package [ajv-merge-patch](https://github.com/ajv-validator/ajv-merge-patch) for more information.\n\n\n## Defining custom keywords\n\nThe advantages of using custom keywords are:\n\n- allow creating validation scenarios that cannot be expressed using JSON Schema\n- simplify your schemas\n- help bringing a bigger part of the validation logic to your schemas\n- make your schemas more expressive, less verbose and closer to your application domain\n- implement custom data processors that modify your data (`modifying` option MUST be used in keyword definition) and/or create side effects while the data is being validated\n\nIf a keyword is used only for side-effects and its validation result is pre-defined, use option `valid: true/false` in keyword definition to simplify both generated code (no error handling in case of `valid: true`) and your keyword functions (no need to return any validation result).\n\nThe concerns you have to be aware of when extending JSON Schema standard with custom keywords are the portability and understanding of your schemas. You will have to support these custom keywords on other platforms and to properly document these keywords so that everybody can understand them in your schemas.\n\nYou can define custom keywords with [addKeyword](#api-addkeyword) method. Keywords are defined on the `ajv` instance level - new instances will not have previously defined keywords.\n\nAjv allows defining keywords with:\n- validation function\n- compilation function\n- macro function\n- inline compilation function that should return code (as string) that will be inlined in the currently compiled schema.\n\nExample. `range` and `exclusiveRange` keywords using compiled schema:\n\n```javascript\najv.addKeyword('range', {\n  type: 'number',\n  compile: function (sch, parentSchema) {\n    var min = sch[0];\n    var max = sch[1];\n\n    return parentSchema.exclusiveRange === true\n            ? function (data) { return data > min && data < max; }\n            : function (data) { return data >= min && data <= max; }\n  }\n});\n\nvar schema = { \"range\": [2, 4], \"exclusiveRange\": true };\nvar validate = ajv.compile(schema);\nconsole.log(validate(2.01)); // true\nconsole.log(validate(3.99)); // true\nconsole.log(validate(2)); // false\nconsole.log(validate(4)); // false\n```\n\nSeveral custom keywords (typeof, instanceof, range and propertyNames) are defined in [ajv-keywords](https://github.com/ajv-validator/ajv-keywords) package - they can be used for your schemas and as a starting point for your own custom keywords.\n\nSee [Defining custom keywords](https://github.com/ajv-validator/ajv/blob/master/CUSTOM.md) for more details.\n\n\n## Asynchronous schema compilation\n\nDuring asynchronous compilation remote references are loaded using supplied function. See `compileAsync` [method](#api-compileAsync) and `loadSchema` [option](#options).\n\nExample:\n\n```javascript\nvar ajv = new Ajv({ loadSchema: loadSchema });\n\najv.compileAsync(schema).then(function (validate) {\n  var valid = validate(data);\n  // ...\n});\n\nfunction loadSchema(uri) {\n  return request.json(uri).then(function (res) {\n    if (res.statusCode >= 400)\n      throw new Error('Loading error: ' + res.statusCode);\n    return res.body;\n  });\n}\n```\n\n__Please note__: [Option](#options) `missingRefs` should NOT be set to `\"ignore\"` or `\"fail\"` for asynchronous compilation to work.\n\n\n## Asynchronous validation\n\nExample in Node.js REPL: https://tonicdev.com/esp/ajv-asynchronous-validation\n\nYou can define custom formats and keywords that perform validation asynchronously by accessing database or some other service. You should add `async: true` in the keyword or format definition (see [addFormat](#api-addformat), [addKeyword](#api-addkeyword) and [Defining custom keywords](#defining-custom-keywords)).\n\nIf your schema uses asynchronous formats/keywords or refers to some schema that contains them it should have `\"$async\": true` keyword so that Ajv can compile it correctly. If asynchronous format/keyword or reference to asynchronous schema is used in the schema without `$async` keyword Ajv will throw an exception during schema compilation.\n\n__Please note__: all asynchronous subschemas that are referenced from the current or other schemas should have `\"$async\": true` keyword as well, otherwise the schema compilation will fail.\n\nValidation function for an asynchronous custom format/keyword should return a promise that resolves with `true` or `false` (or rejects with `new Ajv.ValidationError(errors)` if you want to return custom errors from the keyword function).\n\nAjv compiles asynchronous schemas to [es7 async functions](http://tc39.github.io/ecmascript-asyncawait/) that can optionally be transpiled with [nodent](https://github.com/MatAtBread/nodent). Async functions are supported in Node.js 7+ and all modern browsers. You can also supply any other transpiler as a function via `processCode` option. See [Options](#options).\n\nThe compiled validation function has `$async: true` property (if the schema is asynchronous), so you can differentiate these functions if you are using both synchronous and asynchronous schemas.\n\nValidation result will be a promise that resolves with validated data or rejects with an exception `Ajv.ValidationError` that contains the array of validation errors in `errors` property.\n\n\nExample:\n\n```javascript\nvar ajv = new Ajv;\n// require('ajv-async')(ajv);\n\najv.addKeyword('idExists', {\n  async: true,\n  type: 'number',\n  validate: checkIdExists\n});\n\n\nfunction checkIdExists(schema, data) {\n  return knex(schema.table)\n  .select('id')\n  .where('id', data)\n  .then(function (rows) {\n    return !!rows.length; // true if record is found\n  });\n}\n\nvar schema = {\n  \"$async\": true,\n  \"properties\": {\n    \"userId\": {\n      \"type\": \"integer\",\n      \"idExists\": { \"table\": \"users\" }\n    },\n    \"postId\": {\n      \"type\": \"integer\",\n      \"idExists\": { \"table\": \"posts\" }\n    }\n  }\n};\n\nvar validate = ajv.compile(schema);\n\nvalidate({ userId: 1, postId: 19 })\n.then(function (data) {\n  console.log('Data is valid', data); // { userId: 1, postId: 19 }\n})\n.catch(function (err) {\n  if (!(err instanceof Ajv.ValidationError)) throw err;\n  // data is invalid\n  console.log('Validation errors:', err.errors);\n});\n```\n\n### Using transpilers with asynchronous validation functions.\n\n[ajv-async](https://github.com/ajv-validator/ajv-async) uses [nodent](https://github.com/MatAtBread/nodent) to transpile async functions. To use another transpiler you should separately install it (or load its bundle in the browser).\n\n\n#### Using nodent\n\n```javascript\nvar ajv = new Ajv;\nrequire('ajv-async')(ajv);\n// in the browser if you want to load ajv-async bundle separately you can:\n// window.ajvAsync(ajv);\nvar validate = ajv.compile(schema); // transpiled es7 async function\nvalidate(data).then(successFunc).catch(errorFunc);\n```\n\n\n#### Using other transpilers\n\n```javascript\nvar ajv = new Ajv({ processCode: transpileFunc });\nvar validate = ajv.compile(schema); // transpiled es7 async function\nvalidate(data).then(successFunc).catch(errorFunc);\n```\n\nSee [Options](#options).\n\n\n## Security considerations\n\nJSON Schema, if properly used, can replace data sanitisation. It doesn't replace other API security considerations. It also introduces additional security aspects to consider.\n\n\n##### Security contact\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure. Please do NOT report security vulnerabilities via GitHub issues.\n\n\n##### Untrusted schemas\n\nAjv treats JSON schemas as trusted as your application code. This security model is based on the most common use case, when the schemas are static and bundled together with the application.\n\nIf your schemas are received from untrusted sources (or generated from untrusted data) there are several scenarios you need to prevent:\n- compiling schemas can cause stack overflow (if they are too deep)\n- compiling schemas can be slow (e.g. [#557](https://github.com/ajv-validator/ajv/issues/557))\n- validating certain data can be slow\n\nIt is difficult to predict all the scenarios, but at the very least it may help to limit the size of untrusted schemas (e.g. limit JSON string length) and also the maximum schema object depth (that can be high for relatively small JSON strings). You also may want to mitigate slow regular expressions in `pattern` and `patternProperties` keywords.\n\nRegardless the measures you take, using untrusted schemas increases security risks.\n\n\n##### Circular references in JavaScript objects\n\nAjv does not support schemas and validated data that have circular references in objects. See [issue #802](https://github.com/ajv-validator/ajv/issues/802).\n\nAn attempt to compile such schemas or validate such data would cause stack overflow (or will not complete in case of asynchronous validation). Depending on the parser you use, untrusted data can lead to circular references.\n\n\n##### Security risks of trusted schemas\n\nSome keywords in JSON Schemas can lead to very slow validation for certain data. These keywords include (but may be not limited to):\n\n- `pattern` and `format` for large strings - in some cases using `maxLength` can help mitigate it, but certain regular expressions can lead to exponential validation time even with relatively short strings (see [ReDoS attack](#redos-attack)).\n- `patternProperties` for large property names - use `propertyNames` to mitigate, but some regular expressions can have exponential evaluation time as well.\n- `uniqueItems` for large non-scalar arrays - use `maxItems` to mitigate\n\n__Please note__: The suggestions above to prevent slow validation would only work if you do NOT use `allErrors: true` in production code (using it would continue validation after validation errors).\n\nYou can validate your JSON schemas against [this meta-schema](https://github.com/ajv-validator/ajv/blob/master/lib/refs/json-schema-secure.json) to check that these recommendations are followed:\n\n```javascript\nconst isSchemaSecure = ajv.compile(require('ajv/lib/refs/json-schema-secure.json'));\n\nconst schema1 = {format: 'email'};\nisSchemaSecure(schema1); // false\n\nconst schema2 = {format: 'email', maxLength: MAX_LENGTH};\nisSchemaSecure(schema2); // true\n```\n\n__Please note__: following all these recommendation is not a guarantee that validation of untrusted data is safe - it can still lead to some undesirable results.\n\n\n##### Content Security Policies (CSP)\nSee [Ajv and Content Security Policies (CSP)](#ajv-and-content-security-policies-csp)\n\n\n## ReDoS attack\n\nCertain regular expressions can lead to the exponential evaluation time even with relatively short strings.\n\nPlease assess the regular expressions you use in the schemas on their vulnerability to this attack - see [safe-regex](https://github.com/substack/safe-regex), for example.\n\n__Please note__: some formats that Ajv implements use [regular expressions](https://github.com/ajv-validator/ajv/blob/master/lib/compile/formats.js) that can be vulnerable to ReDoS attack, so if you use Ajv to validate data from untrusted sources __it is strongly recommended__ to consider the following:\n\n- making assessment of \"format\" implementations in Ajv.\n- using `format: 'fast'` option that simplifies some of the regular expressions (although it does not guarantee that they are safe).\n- replacing format implementations provided by Ajv with your own implementations of \"format\" keyword that either uses different regular expressions or another approach to format validation. Please see [addFormat](#api-addformat) method.\n- disabling format validation by ignoring \"format\" keyword with option `format: false`\n\nWhatever mitigation you choose, please assume all formats provided by Ajv as potentially unsafe and make your own assessment of their suitability for your validation scenarios.\n\n\n## Filtering data\n\nWith [option `removeAdditional`](#options) (added by [andyscott](https://github.com/andyscott)) you can filter data during the validation.\n\nThis option modifies original data.\n\nExample:\n\n```javascript\nvar ajv = new Ajv({ removeAdditional: true });\nvar schema = {\n  \"additionalProperties\": false,\n  \"properties\": {\n    \"foo\": { \"type\": \"number\" },\n    \"bar\": {\n      \"additionalProperties\": { \"type\": \"number\" },\n      \"properties\": {\n        \"baz\": { \"type\": \"string\" }\n      }\n    }\n  }\n}\n\nvar data = {\n  \"foo\": 0,\n  \"additional1\": 1, // will be removed; `additionalProperties` == false\n  \"bar\": {\n    \"baz\": \"abc\",\n    \"additional2\": 2 // will NOT be removed; `additionalProperties` != false\n  },\n}\n\nvar validate = ajv.compile(schema);\n\nconsole.log(validate(data)); // true\nconsole.log(data); // { \"foo\": 0, \"bar\": { \"baz\": \"abc\", \"additional2\": 2 }\n```\n\nIf `removeAdditional` option in the example above were `\"all\"` then both `additional1` and `additional2` properties would have been removed.\n\nIf the option were `\"failing\"` then property `additional1` would have been removed regardless of its value and property `additional2` would have been removed only if its value were failing the schema in the inner `additionalProperties` (so in the example above it would have stayed because it passes the schema, but any non-number would have been removed).\n\n__Please note__: If you use `removeAdditional` option with `additionalProperties` keyword inside `anyOf`/`oneOf` keywords your validation can fail with this schema, for example:\n\n```json\n{\n  \"type\": \"object\",\n  \"oneOf\": [\n    {\n      \"properties\": {\n        \"foo\": { \"type\": \"string\" }\n      },\n      \"required\": [ \"foo\" ],\n      \"additionalProperties\": false\n    },\n    {\n      \"properties\": {\n        \"bar\": { \"type\": \"integer\" }\n      },\n      \"required\": [ \"bar\" ],\n      \"additionalProperties\": false\n    }\n  ]\n}\n```\n\nThe intention of the schema above is to allow objects with either the string property \"foo\" or the integer property \"bar\", but not with both and not with any other properties.\n\nWith the option `removeAdditional: true` the validation will pass for the object `{ \"foo\": \"abc\"}` but will fail for the object `{\"bar\": 1}`. It happens because while the first subschema in `oneOf` is validated, the property `bar` is removed because it is an additional property according to the standard (because it is not included in `properties` keyword in the same schema).\n\nWhile this behaviour is unexpected (issues [#129](https://github.com/ajv-validator/ajv/issues/129), [#134](https://github.com/ajv-validator/ajv/issues/134)), it is correct. To have the expected behaviour (both objects are allowed and additional properties are removed) the schema has to be refactored in this way:\n\n```json\n{\n  \"type\": \"object\",\n  \"properties\": {\n    \"foo\": { \"type\": \"string\" },\n    \"bar\": { \"type\": \"integer\" }\n  },\n  \"additionalProperties\": false,\n  \"oneOf\": [\n    { \"required\": [ \"foo\" ] },\n    { \"required\": [ \"bar\" ] }\n  ]\n}\n```\n\nThe schema above is also more efficient - it will compile into a faster function.\n\n\n## Assigning defaults\n\nWith [option `useDefaults`](#options) Ajv will assign values from `default` keyword in the schemas of `properties` and `items` (when it is the array of schemas) to the missing properties and items.\n\nWith the option value `\"empty\"` properties and items equal to `null` or `\"\"` (empty string) will be considered missing and assigned defaults.\n\nThis option modifies original data.\n\n__Please note__: the default value is inserted in the generated validation code as a literal, so the value inserted in the data will be the deep clone of the default in the schema.\n\n\nExample 1 (`default` in `properties`):\n\n```javascript\nvar ajv = new Ajv({ useDefaults: true });\nvar schema = {\n  \"type\": \"object\",\n  \"properties\": {\n    \"foo\": { \"type\": \"number\" },\n    \"bar\": { \"type\": \"string\", \"default\": \"baz\" }\n  },\n  \"required\": [ \"foo\", \"bar\" ]\n};\n\nvar data = { \"foo\": 1 };\n\nvar validate = ajv.compile(schema);\n\nconsole.log(validate(data)); // true\nconsole.log(data); // { \"foo\": 1, \"bar\": \"baz\" }\n```\n\nExample 2 (`default` in `items`):\n\n```javascript\nvar schema = {\n  \"type\": \"array\",\n  \"items\": [\n    { \"type\": \"number\" },\n    { \"type\": \"string\", \"default\": \"foo\" }\n  ]\n}\n\nvar data = [ 1 ];\n\nvar validate = ajv.compile(schema);\n\nconsole.log(validate(data)); // true\nconsole.log(data); // [ 1, \"foo\" ]\n```\n\n`default` keywords in other cases are ignored:\n\n- not in `properties` or `items` subschemas\n- in schemas inside `anyOf`, `oneOf` and `not` (see [#42](https://github.com/ajv-validator/ajv/issues/42))\n- in `if` subschema of `switch` keyword\n- in schemas generated by custom macro keywords\n\nThe [`strictDefaults` option](#options) customizes Ajv's behavior for the defaults that Ajv ignores (`true` raises an error, and `\"log\"` outputs a warning).\n\n\n## Coercing data types\n\nWhen you are validating user inputs all your data properties are usually strings. The option `coerceTypes` allows you to have your data types coerced to the types specified in your schema `type` keywords, both to pass the validation and to use the correctly typed data afterwards.\n\nThis option modifies original data.\n\n__Please note__: if you pass a scalar value to the validating function its type will be coerced and it will pass the validation, but the value of the variable you pass won't be updated because scalars are passed by value.\n\n\nExample 1:\n\n```javascript\nvar ajv = new Ajv({ coerceTypes: true });\nvar schema = {\n  \"type\": \"object\",\n  \"properties\": {\n    \"foo\": { \"type\": \"number\" },\n    \"bar\": { \"type\": \"boolean\" }\n  },\n  \"required\": [ \"foo\", \"bar\" ]\n};\n\nvar data = { \"foo\": \"1\", \"bar\": \"false\" };\n\nvar validate = ajv.compile(schema);\n\nconsole.log(validate(data)); // true\nconsole.log(data); // { \"foo\": 1, \"bar\": false }\n```\n\nExample 2 (array coercions):\n\n```javascript\nvar ajv = new Ajv({ coerceTypes: 'array' });\nvar schema = {\n  \"properties\": {\n    \"foo\": { \"type\": \"array\", \"items\": { \"type\": \"number\" } },\n    \"bar\": { \"type\": \"boolean\" }\n  }\n};\n\nvar data = { \"foo\": \"1\", \"bar\": [\"false\"] };\n\nvar validate = ajv.compile(schema);\n\nconsole.log(validate(data)); // true\nconsole.log(data); // { \"foo\": [1], \"bar\": false }\n```\n\nThe coercion rules, as you can see from the example, are different from JavaScript both to validate user input as expected and to have the coercion reversible (to correctly validate cases where different types are defined in subschemas of \"anyOf\" and other compound keywords).\n\nSee [Coercion rules](https://github.com/ajv-validator/ajv/blob/master/COERCION.md) for details.\n\n\n## API\n\n##### new Ajv(Object options) -&gt; Object\n\nCreate Ajv instance.\n\n\n##### .compile(Object schema) -&gt; Function&lt;Object data&gt;\n\nGenerate validating function and cache the compiled schema for future use.\n\nValidating function returns a boolean value. This function has properties `errors` and `schema`. Errors encountered during the last validation are assigned to `errors` property (it is assigned `null` if there was no errors). `schema` property contains the reference to the original schema.\n\nThe schema passed to this method will be validated against meta-schema unless `validateSchema` option is false. If schema is invalid, an error will be thrown. See [options](#options).\n\n\n##### <a name=\"api-compileAsync\"></a>.compileAsync(Object schema [, Boolean meta] [, Function callback]) -&gt; Promise\n\nAsynchronous version of `compile` method that loads missing remote schemas using asynchronous function in `options.loadSchema`. This function returns a Promise that resolves to a validation function. An optional callback passed to `compileAsync` will be called with 2 parameters: error (or null) and validating function. The returned promise will reject (and the callback will be called with an error) when:\n\n- missing schema can't be loaded (`loadSchema` returns a Promise that rejects).\n- a schema containing a missing reference is loaded, but the reference cannot be resolved.\n- schema (or some loaded/referenced schema) is invalid.\n\nThe function compiles schema and loads the first missing schema (or meta-schema) until all missing schemas are loaded.\n\nYou can asynchronously compile meta-schema by passing `true` as the second parameter.\n\nSee example in [Asynchronous compilation](#asynchronous-schema-compilation).\n\n\n##### .validate(Object schema|String key|String ref, data) -&gt; Boolean\n\nValidate data using passed schema (it will be compiled and cached).\n\nInstead of the schema you can use the key that was previously passed to `addSchema`, the schema id if it was present in the schema or any previously resolved reference.\n\nValidation errors will be available in the `errors` property of Ajv instance (`null` if there were no errors).\n\n__Please note__: every time this method is called the errors are overwritten so you need to copy them to another variable if you want to use them later.\n\nIf the schema is asynchronous (has `$async` keyword on the top level) this method returns a Promise. See [Asynchronous validation](#asynchronous-validation).\n\n\n##### .addSchema(Array&lt;Object&gt;|Object schema [, String key]) -&gt; Ajv\n\nAdd schema(s) to validator instance. This method does not compile schemas (but it still validates them). Because of that dependencies can be added in any order and circular dependencies are supported. It also prevents unnecessary compilation of schemas that are containers for other schemas but not used as a whole.\n\nArray of schemas can be passed (schemas should have ids), the second parameter will be ignored.\n\nKey can be passed that can be used to reference the schema and will be used as the schema id if there is no id inside the schema. If the key is not passed, the schema id will be used as the key.\n\n\nOnce the schema is added, it (and all the references inside it) can be referenced in other schemas and used to validate data.\n\nAlthough `addSchema` does not compile schemas, explicit compilation is not required - the schema will be compiled when it is used first time.\n\nBy default the schema is validated against meta-schema before it is added, and if the schema does not pass validation the exception is thrown. This behaviour is controlled by `validateSchema` option.\n\n__Please note__: Ajv uses the [method chaining syntax](https://en.wikipedia.org/wiki/Method_chaining) for all methods with the prefix `add*` and `remove*`.\nThis allows you to do nice things like the following.\n\n```javascript\nvar validate = new Ajv().addSchema(schema).addFormat(name, regex).getSchema(uri);\n```\n\n##### .addMetaSchema(Array&lt;Object&gt;|Object schema [, String key]) -&gt; Ajv\n\nAdds meta schema(s) that can be used to validate other schemas. That function should be used instead of `addSchema` because there may be instance options that would compile a meta schema incorrectly (at the moment it is `removeAdditional` option).\n\nThere is no need to explicitly add draft-07 meta schema (http://json-schema.org/draft-07/schema) - it is added by default, unless option `meta` is set to `false`. You only need to use it if you have a changed meta-schema that you want to use to validate your schemas. See `validateSchema`.\n\n\n##### <a name=\"api-validateschema\"></a>.validateSchema(Object schema) -&gt; Boolean\n\nValidates schema. This method should be used to validate schemas rather than `validate` due to the inconsistency of `uri` format in JSON Schema standard.\n\nBy default this method is called automatically when the schema is added, so you rarely need to use it directly.\n\nIf schema doesn't have `$schema` property, it is validated against draft 6 meta-schema (option `meta` should not be false).\n\nIf schema has `$schema` property, then the schema with this id (that should be previously added) is used to validate passed schema.\n\nErrors will be available at `ajv.errors`.\n\n\n##### .getSchema(String key) -&gt; Function&lt;Object data&gt;\n\nRetrieve compiled schema previously added with `addSchema` by the key passed to `addSchema` or by its full reference (id). The returned validating function has `schema` property with the reference to the original schema.\n\n\n##### .removeSchema([Object schema|String key|String ref|RegExp pattern]) -&gt; Ajv\n\nRemove added/cached schema. Even if schema is referenced by other schemas it can be safely removed as dependent schemas have local references.\n\nSchema can be removed using:\n- key passed to `addSchema`\n- it's full reference (id)\n- RegExp that should match schema id or key (meta-schemas won't be removed)\n- actual schema object that will be stable-stringified to remove schema from cache\n\nIf no parameter is passed all schemas but meta-schemas will be removed and the cache will be cleared.\n\n\n##### <a name=\"api-addformat\"></a>.addFormat(String name, String|RegExp|Function|Object format) -&gt; Ajv\n\nAdd custom format to validate strings or numbers. It can also be used to replace pre-defined formats for Ajv instance.\n\nStrings are converted to RegExp.\n\nFunction should return validation result as `true` or `false`.\n\nIf object is passed it should have properties `validate`, `compare` and `async`:\n\n- _validate_: a string, RegExp or a function as described above.\n- _compare_: an optional comparison function that accepts two strings and compares them according to the format meaning. This function is used with keywords `formatMaximum`/`formatMinimum` (defined in [ajv-keywords](https://github.com/ajv-validator/ajv-keywords) package). It should return `1` if the first value is bigger than the second value, `-1` if it is smaller and `0` if it is equal.\n- _async_: an optional `true` value if `validate` is an asynchronous function; in this case it should return a promise that resolves with a value `true` or `false`.\n- _type_: an optional type of data that the format applies to. It can be `\"string\"` (default) or `\"number\"` (see https://github.com/ajv-validator/ajv/issues/291#issuecomment-259923858). If the type of data is different, the validation will pass.\n\nCustom formats can be also added via `formats` option.\n\n\n##### <a name=\"api-addkeyword\"></a>.addKeyword(String keyword, Object definition) -&gt; Ajv\n\nAdd custom validation keyword to Ajv instance.\n\nKeyword should be different from all standard JSON Schema keywords and different from previously defined keywords. There is no way to redefine keywords or to remove keyword definition from the instance.\n\nKeyword must start with a letter, `_` or `$`, and may continue with letters, numbers, `_`, `$`, or `-`.\nIt is recommended to use an application-specific prefix for keywords to avoid current and future name collisions.\n\nExample Keywords:\n- `\"xyz-example\"`: valid, and uses prefix for the xyz project to avoid name collisions.\n- `\"example\"`: valid, but not recommended as it could collide with future versions of JSON Schema etc.\n- `\"3-example\"`: invalid as numbers are not allowed to be the first character in a keyword\n\nKeyword definition is an object with the following properties:\n\n- _type_: optional string or array of strings with data type(s) that the keyword applies to. If not present, the keyword will apply to all types.\n- _validate_: validating function\n- _compile_: compiling function\n- _macro_: macro function\n- _inline_: compiling function that returns code (as string)\n- _schema_: an optional `false` value used with \"validate\" keyword to not pass schema\n- _metaSchema_: an optional meta-schema for keyword schema\n- _dependencies_: an optional list of properties that must be present in the parent schema - it will be checked during schema compilation\n- _modifying_: `true` MUST be passed if keyword modifies data\n- _statements_: `true` can be passed in case inline keyword generates statements (as opposed to expression)\n- _valid_: pass `true`/`false` to pre-define validation result, the result returned from validation function will be ignored. This option cannot be used with macro keywords.\n- _$data_: an optional `true` value to support [$data reference](#data-reference) as the value of custom keyword. The reference will be resolved at validation time. If the keyword has meta-schema it would be extended to allow $data and it will be used to validate the resolved value. Supporting $data reference requires that keyword has validating function (as the only option or in addition to compile, macro or inline function).\n- _async_: an optional `true` value if the validation function is asynchronous (whether it is compiled or passed in _validate_ property); in this case it should return a promise that resolves with a value `true` or `false`. This option is ignored in case of \"macro\" and \"inline\" keywords.\n- _errors_: an optional boolean or string `\"full\"` indicating whether keyword returns errors. If this property is not set Ajv will determine if the errors were set in case of failed validation.\n\n_compile_, _macro_ and _inline_ are mutually exclusive, only one should be used at a time. _validate_ can be used separately or in addition to them to support $data reference.\n\n__Please note__: If the keyword is validating data type that is different from the type(s) in its definition, the validation function will not be called (and expanded macro will not be used), so there is no need to check for data type inside validation function or inside schema returned by macro function (unless you want to enforce a specific type and for some reason do not want to use a separate `type` keyword for that). In the same way as standard keywords work, if the keyword does not apply to the data type being validated, the validation of this keyword will succeed.\n\nSee [Defining custom keywords](#defining-custom-keywords) for more details.\n\n\n##### .getKeyword(String keyword) -&gt; Object|Boolean\n\nReturns custom keyword definition, `true` for pre-defined keywords and `false` if the keyword is unknown.\n\n\n##### .removeKeyword(String keyword) -&gt; Ajv\n\nRemoves custom or pre-defined keyword so you can redefine them.\n\nWhile this method can be used to extend pre-defined keywords, it can also be used to completely change their meaning - it may lead to unexpected results.\n\n__Please note__: schemas compiled before the keyword is removed will continue to work without changes. To recompile schemas use `removeSchema` method and compile them again.\n\n\n##### .errorsText([Array&lt;Object&gt; errors [, Object options]]) -&gt; String\n\nReturns the text with all errors in a String.\n\nOptions can have properties `separator` (string used to separate errors, \", \" by default) and `dataVar` (the variable name that dataPaths are prefixed with, \"data\" by default).\n\n\n## Options\n\nDefaults:\n\n```javascript\n{\n  // validation and reporting options:\n  $data:            false,\n  allErrors:        false,\n  verbose:          false,\n  $comment:         false, // NEW in Ajv version 6.0\n  jsonPointers:     false,\n  uniqueItems:      true,\n  unicode:          true,\n  nullable:         false,\n  format:           'fast',\n  formats:          {},\n  unknownFormats:   true,\n  schemas:          {},\n  logger:           undefined,\n  // referenced schema options:\n  schemaId:         '$id',\n  missingRefs:      true,\n  extendRefs:       'ignore', // recommended 'fail'\n  loadSchema:       undefined, // function(uri: string): Promise {}\n  // options to modify validated data:\n  removeAdditional: false,\n  useDefaults:      false,\n  coerceTypes:      false,\n  // strict mode options\n  strictDefaults:   false,\n  strictKeywords:   false,\n  strictNumbers:    false,\n  // asynchronous validation options:\n  transpile:        undefined, // requires ajv-async package\n  // advanced options:\n  meta:             true,\n  validateSchema:   true,\n  addUsedSchema:    true,\n  inlineRefs:       true,\n  passContext:      false,\n  loopRequired:     Infinity,\n  ownProperties:    false,\n  multipleOfPrecision: false,\n  errorDataPath:    'object', // deprecated\n  messages:         true,\n  sourceCode:       false,\n  processCode:      undefined, // function (str: string, schema: object): string {}\n  cache:            new Cache,\n  serialize:        undefined\n}\n```\n\n##### Validation and reporting options\n\n- _$data_: support [$data references](#data-reference). Draft 6 meta-schema that is added by default will be extended to allow them. If you want to use another meta-schema you need to use $dataMetaSchema method to add support for $data reference. See [API](#api).\n- _allErrors_: check all rules collecting all errors. Default is to return after the first error.\n- _verbose_: include the reference to the part of the schema (`schema` and `parentSchema`) and validated data in errors (false by default).\n- _$comment_ (NEW in Ajv version 6.0): log or pass the value of `$comment` keyword to a function. Option values:\n  - `false` (default): ignore $comment keyword.\n  - `true`: log the keyword value to console.\n  - function: pass the keyword value, its schema path and root schema to the specified function\n- _jsonPointers_: set `dataPath` property of errors using [JSON Pointers](https://tools.ietf.org/html/rfc6901) instead of JavaScript property access notation.\n- _uniqueItems_: validate `uniqueItems` keyword (true by default).\n- _unicode_: calculate correct length of strings with unicode pairs (true by default). Pass `false` to use `.length` of strings that is faster, but gives \"incorrect\" lengths of strings with unicode pairs - each unicode pair is counted as two characters.\n- _nullable_: support keyword \"nullable\" from [Open API 3 specification](https://swagger.io/docs/specification/data-models/data-types/).\n- _format_: formats validation mode. Option values:\n  - `\"fast\"` (default) - simplified and fast validation (see [Formats](#formats) for details of which formats are available and affected by this option).\n  - `\"full\"` - more restrictive and slow validation. E.g., 25:00:00 and 2015/14/33 will be invalid time and date in 'full' mode but it will be valid in 'fast' mode.\n  - `false` - ignore all format keywords.\n- _formats_: an object with custom formats. Keys and values will be passed to `addFormat` method.\n- _keywords_: an object with custom keywords. Keys and values will be passed to `addKeyword` method.\n- _unknownFormats_: handling of unknown formats. Option values:\n  - `true` (default) - if an unknown format is encountered the exception is thrown during schema compilation. If `format` keyword value is [$data reference](#data-reference) and it is unknown the validation will fail.\n  - `[String]` - an array of unknown format names that will be ignored. This option can be used to allow usage of third party schemas with format(s) for which you don't have definitions, but still fail if another unknown format is used. If `format` keyword value is [$data reference](#data-reference) and it is not in this array the validation will fail.\n  - `\"ignore\"` - to log warning during schema compilation and always pass validation (the default behaviour in versions before 5.0.0). This option is not recommended, as it allows to mistype format name and it won't be validated without any error message. This behaviour is required by JSON Schema specification.\n- _schemas_: an array or object of schemas that will be added to the instance. In case you pass the array the schemas must have IDs in them. When the object is passed the method `addSchema(value, key)` will be called for each schema in this object.\n- _logger_: sets the logging method. Default is the global `console` object that should have methods `log`, `warn` and `error`. See [Error logging](#error-logging). Option values:\n  - custom logger - it should have methods `log`, `warn` and `error`. If any of these methods is missing an exception will be thrown.\n  - `false` - logging is disabled.\n\n\n##### Referenced schema options\n\n- _schemaId_: this option defines which keywords are used as schema URI. Option value:\n  - `\"$id\"` (default) - only use `$id` keyword as schema URI (as specified in JSON Schema draft-06/07), ignore `id` keyword (if it is present a warning will be logged).\n  - `\"id\"` - only use `id` keyword as schema URI (as specified in JSON Schema draft-04), ignore `$id` keyword (if it is present a warning will be logged).\n  - `\"auto\"` - use both `$id` and `id` keywords as schema URI. If both are present (in the same schema object) and different the exception will be thrown during schema compilation.\n- _missingRefs_: handling of missing referenced schemas. Option values:\n  - `true` (default) - if the reference cannot be resolved during compilation the exception is thrown. The thrown error has properties `missingRef` (with hash fragment) and `missingSchema` (without it). Both properties are resolved relative to the current base id (usually schema id, unless it was substituted).\n  - `\"ignore\"` - to log error during compilation and always pass validation.\n  - `\"fail\"` - to log error and successfully compile schema but fail validation if this rule is checked.\n- _extendRefs_: validation of other keywords when `$ref` is present in the schema. Option values:\n  - `\"ignore\"` (default) - when `$ref` is used other keywords are ignored (as per [JSON Reference](https://tools.ietf.org/html/draft-pbryan-zyp-json-ref-03#section-3) standard). A warning will be logged during the schema compilation.\n  - `\"fail\"` (recommended) - if other validation keywords are used together with `$ref` the exception will be thrown when the schema is compiled. This option is recommended to make sure schema has no keywords that are ignored, which can be confusing.\n  - `true` - validate all keywords in the schemas with `$ref` (the default behaviour in versions before 5.0.0).\n- _loadSchema_: asynchronous function that will be used to load remote schemas when `compileAsync` [method](#api-compileAsync) is used and some reference is missing (option `missingRefs` should NOT be 'fail' or 'ignore'). This function should accept remote schema uri as a parameter and return a Promise that resolves to a schema. See example in [Asynchronous compilation](#asynchronous-schema-compilation).\n\n\n##### Options to modify validated data\n\n- _removeAdditional_: remove additional properties - see example in [Filtering data](#filtering-data). This option is not used if schema is added with `addMetaSchema` method. Option values:\n  - `false` (default) - not to remove additional properties\n  - `\"all\"` - all additional properties are removed, regardless of `additionalProperties` keyword in schema (and no validation is made for them).\n  - `true` - only additional properties with `additionalProperties` keyword equal to `false` are removed.\n  - `\"failing\"` - additional properties that fail schema validation will be removed (where `additionalProperties` keyword is `false` or schema).\n- _useDefaults_: replace missing or undefined properties and items with the values from corresponding `default` keywords. Default behaviour is to ignore `default` keywords. This option is not used if schema is added with `addMetaSchema` method. See examples in [Assigning defaults](#assigning-defaults). Option values:\n  - `false` (default) - do not use defaults\n  - `true` - insert defaults by value (object literal is used).\n  - `\"empty\"` - in addition to missing or undefined, use defaults for properties and items that are equal to `null` or `\"\"` (an empty string).\n  - `\"shared\"` (deprecated) - insert defaults by reference. If the default is an object, it will be shared by all instances of validated data. If you modify the inserted default in the validated data, it will be modified in the schema as well.\n- _coerceTypes_: change data type of data to match `type` keyword. See the example in [Coercing data types](#coercing-data-types) and [coercion rules](https://github.com/ajv-validator/ajv/blob/master/COERCION.md). Option values:\n  - `false` (default) - no type coercion.\n  - `true` - coerce scalar data types.\n  - `\"array\"` - in addition to coercions between scalar types, coerce scalar data to an array with one element and vice versa (as required by the schema).\n\n\n##### Strict mode options\n\n- _strictDefaults_: report ignored `default` keywords in schemas. Option values:\n  - `false` (default) - ignored defaults are not reported\n  - `true` - if an ignored default is present, throw an error\n  - `\"log\"` - if an ignored default is present, log warning\n- _strictKeywords_: report unknown keywords in schemas. Option values:\n  - `false` (default) - unknown keywords are not reported\n  - `true` - if an unknown keyword is present, throw an error\n  - `\"log\"` - if an unknown keyword is present, log warning\n- _strictNumbers_: validate numbers strictly, failing validation for NaN and Infinity. Option values:\n  - `false` (default) - NaN or Infinity will pass validation for numeric types\n  - `true` - NaN or Infinity will not pass validation for numeric types\n\n##### Asynchronous validation options\n\n- _transpile_: Requires [ajv-async](https://github.com/ajv-validator/ajv-async) package. It determines whether Ajv transpiles compiled asynchronous validation function. Option values:\n  - `undefined` (default) - transpile with [nodent](https://github.com/MatAtBread/nodent) if async functions are not supported.\n  - `true` - always transpile with nodent.\n  - `false` - do not transpile; if async functions are not supported an exception will be thrown.\n\n\n##### Advanced options\n\n- _meta_: add [meta-schema](http://json-schema.org/documentation.html) so it can be used by other schemas (true by default). If an object is passed, it will be used as the default meta-schema for schemas that have no `$schema` keyword. This default meta-schema MUST have `$schema` keyword.\n- _validateSchema_: validate added/compiled schemas against meta-schema (true by default). `$schema` property in the schema can be http://json-schema.org/draft-07/schema or absent (draft-07 meta-schema will be used) or can be a reference to the schema previously added with `addMetaSchema` method. Option values:\n  - `true` (default) -  if the validation fails, throw the exception.\n  - `\"log\"` - if the validation fails, log error.\n  - `false` - skip schema validation.\n- _addUsedSchema_: by default methods `compile` and `validate` add schemas to the instance if they have `$id` (or `id`) property that doesn't start with \"#\". If `$id` is present and it is not unique the exception will be thrown. Set this option to `false` to skip adding schemas to the instance and the `$id` uniqueness check when these methods are used. This option does not affect `addSchema` method.\n- _inlineRefs_: Affects compilation of referenced schemas. Option values:\n  - `true` (default) - the referenced schemas that don't have refs in them are inlined, regardless of their size - that substantially improves performance at the cost of the bigger size of compiled schema functions.\n  - `false` - to not inline referenced schemas (they will be compiled as separate functions).\n  - integer number - to limit the maximum number of keywords of the schema that will be inlined.\n- _passContext_: pass validation context to custom keyword functions. If this option is `true` and you pass some context to the compiled validation function with `validate.call(context, data)`, the `context` will be available as `this` in your custom keywords. By default `this` is Ajv instance.\n- _loopRequired_: by default `required` keyword is compiled into a single expression (or a sequence of statements in `allErrors` mode). In case of a very large number of properties in this keyword it may result in a very big validation function. Pass integer to set the number of properties above which `required` keyword will be validated in a loop - smaller validation function size but also worse performance.\n- _ownProperties_: by default Ajv iterates over all enumerable object properties; when this option is `true` only own enumerable object properties (i.e. found directly on the object rather than on its prototype) are iterated. Contributed by @mbroadst.\n- _multipleOfPrecision_: by default `multipleOf` keyword is validated by comparing the result of division with parseInt() of that result. It works for dividers that are bigger than 1. For small dividers such as 0.01 the result of the division is usually not integer (even when it should be integer, see issue [#84](https://github.com/ajv-validator/ajv/issues/84)). If you need to use fractional dividers set this option to some positive integer N to have `multipleOf` validated using this formula: `Math.abs(Math.round(division) - division) < 1e-N` (it is slower but allows for float arithmetics deviations).\n- _errorDataPath_ (deprecated): set `dataPath` to point to 'object' (default) or to 'property' when validating keywords `required`, `additionalProperties` and `dependencies`.\n- _messages_: Include human-readable messages in errors. `true` by default. `false` can be passed when custom messages are used (e.g. with [ajv-i18n](https://github.com/ajv-validator/ajv-i18n)).\n- _sourceCode_: add `sourceCode` property to validating function (for debugging; this code can be different from the result of toString call).\n- _processCode_: an optional function to process generated code before it is passed to Function constructor. It can be used to either beautify (the validating function is generated without line-breaks) or to transpile code. Starting from version 5.0.0 this option replaced options:\n  - `beautify` that formatted the generated function using [js-beautify](https://github.com/beautify-web/js-beautify). If you want to beautify the generated code pass a function calling `require('js-beautify').js_beautify` as `processCode: code => js_beautify(code)`.\n  - `transpile` that transpiled asynchronous validation function. You can still use `transpile` option with [ajv-async](https://github.com/ajv-validator/ajv-async) package. See [Asynchronous validation](#asynchronous-validation) for more information.\n- _cache_: an optional instance of cache to store compiled schemas using stable-stringified schema as a key. For example, set-associative cache [sacjs](https://github.com/epoberezkin/sacjs) can be used. If not passed then a simple hash is used which is good enough for the common use case (a limited number of statically defined schemas). Cache should have methods `put(key, value)`, `get(key)`, `del(key)` and `clear()`.\n- _serialize_: an optional function to serialize schema to cache key. Pass `false` to use schema itself as a key (e.g., if WeakMap used as a cache). By default [fast-json-stable-stringify](https://github.com/epoberezkin/fast-json-stable-stringify) is used.\n\n\n## Validation errors\n\nIn case of validation failure, Ajv assigns the array of errors to `errors` property of validation function (or to `errors` property of Ajv instance when `validate` or `validateSchema` methods were called). In case of [asynchronous validation](#asynchronous-validation), the returned promise is rejected with exception `Ajv.ValidationError` that has `errors` property.\n\n\n### Error objects\n\nEach error is an object with the following properties:\n\n- _keyword_: validation keyword.\n- _dataPath_: the path to the part of the data that was validated. By default `dataPath` uses JavaScript property access notation (e.g., `\".prop[1].subProp\"`). When the option `jsonPointers` is true (see [Options](#options)) `dataPath` will be set using JSON pointer standard (e.g., `\"/prop/1/subProp\"`).\n- _schemaPath_: the path (JSON-pointer as a URI fragment) to the schema of the keyword that failed validation.\n- _params_: the object with the additional information about error that can be used to create custom error messages (e.g., using [ajv-i18n](https://github.com/ajv-validator/ajv-i18n) package). See below for parameters set by all keywords.\n- _message_: the standard error message (can be excluded with option `messages` set to false).\n- _schema_: the schema of the keyword (added with `verbose` option).\n- _parentSchema_: the schema containing the keyword (added with `verbose` option)\n- _data_: the data validated by the keyword (added with `verbose` option).\n\n__Please note__: `propertyNames` keyword schema validation errors have an additional property `propertyName`, `dataPath` points to the object. After schema validation for each property name, if it is invalid an additional error is added with the property `keyword` equal to `\"propertyNames\"`.\n\n\n### Error parameters\n\nProperties of `params` object in errors depend on the keyword that failed validation.\n\n- `maxItems`, `minItems`, `maxLength`, `minLength`, `maxProperties`, `minProperties` - property `limit` (number, the schema of the keyword).\n- `additionalItems` - property `limit` (the maximum number of allowed items in case when `items` keyword is an array of schemas and `additionalItems` is false).\n- `additionalProperties` - property `additionalProperty` (the property not used in `properties` and `patternProperties` keywords).\n- `dependencies` - properties:\n  - `property` (dependent property),\n  - `missingProperty` (required missing dependency - only the first one is reported currently)\n  - `deps` (required dependencies, comma separated list as a string),\n  - `depsCount` (the number of required dependencies).\n- `format` - property `format` (the schema of the keyword).\n- `maximum`, `minimum` - properties:\n  - `limit` (number, the schema of the keyword),\n  - `exclusive` (boolean, the schema of `exclusiveMaximum` or `exclusiveMinimum`),\n  - `comparison` (string, comparison operation to compare the data to the limit, with the data on the left and the limit on the right; can be \"<\", \"<=\", \">\", \">=\")\n- `multipleOf` - property `multipleOf` (the schema of the keyword)\n- `pattern` - property `pattern` (the schema of the keyword)\n- `required` - property `missingProperty` (required property that is missing).\n- `propertyNames` - property `propertyName` (an invalid property name).\n- `patternRequired` (in ajv-keywords) - property `missingPattern` (required pattern that did not match any property).\n- `type` - property `type` (required type(s), a string, can be a comma-separated list)\n- `uniqueItems` - properties `i` and `j` (indices of duplicate items).\n- `const` - property `allowedValue` pointing to the value (the schema of the keyword).\n- `enum` - property `allowedValues` pointing to the array of values (the schema of the keyword).\n- `$ref` - property `ref` with the referenced schema URI.\n- `oneOf` - property `passingSchemas` (array of indices of passing schemas, null if no schema passes).\n- custom keywords (in case keyword definition doesn't create errors) - property `keyword` (the keyword name).\n\n\n### Error logging\n\nUsing the `logger` option when initiallizing Ajv will allow you to define custom logging. Here you can build upon the exisiting logging. The use of other logging packages is supported as long as the package or its associated wrapper exposes the required methods. If any of the required methods are missing an exception will be thrown.\n- **Required Methods**: `log`, `warn`, `error`\n\n```javascript\nvar otherLogger = new OtherLogger();\nvar ajv = new Ajv({\n  logger: {\n    log: console.log.bind(console),\n    warn: function warn() {\n      otherLogger.logWarn.apply(otherLogger, arguments);\n    },\n    error: function error() {\n      otherLogger.logError.apply(otherLogger, arguments);\n      console.error.apply(console, arguments);\n    }\n  }\n});\n```\n\n\n## Plugins\n\nAjv can be extended with plugins that add custom keywords, formats or functions to process generated code. When such plugin is published as npm package it is recommended that it follows these conventions:\n\n- it exports a function\n- this function accepts ajv instance as the first parameter and returns the same instance to allow chaining\n- this function can accept an optional configuration as the second parameter\n\nIf you have published a useful plugin please submit a PR to add it to the next section.\n\n\n## Related packages\n\n- [ajv-async](https://github.com/ajv-validator/ajv-async) - plugin to configure async validation mode\n- [ajv-bsontype](https://github.com/BoLaMN/ajv-bsontype) - plugin to validate mongodb's bsonType formats\n- [ajv-cli](https://github.com/jessedc/ajv-cli) - command line interface\n- [ajv-errors](https://github.com/ajv-validator/ajv-errors) - plugin for custom error messages\n- [ajv-i18n](https://github.com/ajv-validator/ajv-i18n) - internationalised error messages\n- [ajv-istanbul](https://github.com/ajv-validator/ajv-istanbul) - plugin to instrument generated validation code to measure test coverage of your schemas\n- [ajv-keywords](https://github.com/ajv-validator/ajv-keywords) - plugin with custom validation keywords (select, typeof, etc.)\n- [ajv-merge-patch](https://github.com/ajv-validator/ajv-merge-patch) - plugin with keywords $merge and $patch\n- [ajv-pack](https://github.com/ajv-validator/ajv-pack) - produces a compact module exporting validation functions\n- [ajv-formats-draft2019](https://github.com/luzlab/ajv-formats-draft2019) - format validators for draft2019 that aren't already included in ajv (ie. `idn-hostname`, `idn-email`, `iri`, `iri-reference` and `duration`).\n\n## Some packages using Ajv\n\n- [webpack](https://github.com/webpack/webpack) - a module bundler. Its main purpose is to bundle JavaScript files for usage in a browser\n- [jsonscript-js](https://github.com/JSONScript/jsonscript-js) - the interpreter for [JSONScript](http://www.jsonscript.org) - scripted processing of existing endpoints and services\n- [osprey-method-handler](https://github.com/mulesoft-labs/osprey-method-handler) - Express middleware for validating requests and responses based on a RAML method object, used in [osprey](https://github.com/mulesoft/osprey) - validating API proxy generated from a RAML definition\n- [har-validator](https://github.com/ahmadnassri/har-validator) - HTTP Archive (HAR) validator\n- [jsoneditor](https://github.com/josdejong/jsoneditor) - a web-based tool to view, edit, format, and validate JSON http://jsoneditoronline.org\n- [JSON Schema Lint](https://github.com/nickcmaynard/jsonschemalint) - a web tool to validate JSON/YAML document against a single JSON Schema http://jsonschemalint.com\n- [objection](https://github.com/vincit/objection.js) - SQL-friendly ORM for Node.js\n- [table](https://github.com/gajus/table) - formats data into a string table\n- [ripple-lib](https://github.com/ripple/ripple-lib) - a JavaScript API for interacting with [Ripple](https://ripple.com) in Node.js and the browser\n- [restbase](https://github.com/wikimedia/restbase) - distributed storage with REST API & dispatcher for backend services built to provide a low-latency & high-throughput API for Wikipedia / Wikimedia content\n- [hippie-swagger](https://github.com/CacheControl/hippie-swagger) - [Hippie](https://github.com/vesln/hippie) wrapper that provides end to end API testing with swagger validation\n- [react-form-controlled](https://github.com/seeden/react-form-controlled) - React controlled form components with validation\n- [rabbitmq-schema](https://github.com/tjmehta/rabbitmq-schema) - a schema definition module for RabbitMQ graphs and messages\n- [@query/schema](https://www.npmjs.com/package/@query/schema) - stream filtering with a URI-safe query syntax parsing to JSON Schema\n- [chai-ajv-json-schema](https://github.com/peon374/chai-ajv-json-schema) - chai plugin to us JSON Schema with expect in mocha tests\n- [grunt-jsonschema-ajv](https://github.com/SignpostMarv/grunt-jsonschema-ajv) - Grunt plugin for validating files against JSON Schema\n- [extract-text-webpack-plugin](https://github.com/webpack-contrib/extract-text-webpack-plugin) - extract text from bundle into a file\n- [electron-builder](https://github.com/electron-userland/electron-builder) - a solution to package and build a ready for distribution Electron app\n- [addons-linter](https://github.com/mozilla/addons-linter) - Mozilla Add-ons Linter\n- [gh-pages-generator](https://github.com/epoberezkin/gh-pages-generator) - multi-page site generator converting markdown files to GitHub pages\n- [ESLint](https://github.com/eslint/eslint) - the pluggable linting utility for JavaScript and JSX\n\n\n## Tests\n\n```\nnpm install\ngit submodule update --init\nnpm test\n```\n\n## Contributing\n\nAll validation functions are generated using doT templates in [dot](https://github.com/ajv-validator/ajv/tree/master/lib/dot) folder. Templates are precompiled so doT is not a run-time dependency.\n\n`npm run build` - compiles templates to [dotjs](https://github.com/ajv-validator/ajv/tree/master/lib/dotjs) folder.\n\n`npm run watch` - automatically compiles templates when files in dot folder change\n\nPlease see [Contributing guidelines](https://github.com/ajv-validator/ajv/blob/master/CONTRIBUTING.md)\n\n\n## Changes history\n\nSee https://github.com/ajv-validator/ajv/releases\n\n__Please note__: [Changes in version 7.0.0-beta](https://github.com/ajv-validator/ajv/releases/tag/v7.0.0-beta.0)\n\n[Version 6.0.0](https://github.com/ajv-validator/ajv/releases/tag/v6.0.0).\n\n## Code of conduct\n\nPlease review and follow the [Code of conduct](https://github.com/ajv-validator/ajv/blob/master/CODE_OF_CONDUCT.md).\n\nPlease report any unacceptable <NAME_EMAIL> - it will be reviewed by the project team.\n\n\n## Open-source software support\n\nAjv is a part of [Tidelift subscription](https://tidelift.com/subscription/pkg/npm-ajv?utm_source=npm-ajv&utm_medium=referral&utm_campaign=readme) - it provides a centralised support to open-source software users, in addition to the support provided by software maintainers.\n\n\n## License\n\n[MIT](https://github.com/ajv-validator/ajv/blob/master/LICENSE)\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015-2017 <PERSON><PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/ajv/-/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4", "type": "tarball", "reference": "https://registry.yarnpkg.com/ajv/-/ajv-6.12.6.tgz", "hash": "baf5a62e802b07d977034586f8c3baf5adf26df4", "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==", "registry": "npm", "packageName": "ajv", "cacheIntegrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g== sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ="}, "registry": "npm", "hash": "baf5a62e802b07d977034586f8c3baf5adf26df4"}