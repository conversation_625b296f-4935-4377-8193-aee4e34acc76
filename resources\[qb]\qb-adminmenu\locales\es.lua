local Translations = {
    error = {
        blips_deactivated = 'Blips desactivados',
        names_deactivated = 'Nombres desactivados',
        changed_perm_failed = 'Elige un grupo!',
        missing_reason = '¡Debes proporcionar una razón!',
        invalid_reason_length_ban = '¡Debes dar una razón y establecer una duración para el Ban!',
        no_store_vehicle_garage = 'No puede guardar este vehículo en su garaje..',
        no_vehicle = 'No estás en un vehículo..',
        no_weapon = 'No tienes un arma en tus manos..',
        no_free_seats = 'The vehicle has no free seats!',
        failed_vehicle_owner = 'Este vehículo ya es tuyo..',
        not_online = 'Este jugador no está en línea.',
        no_receive_report = 'No estás recibiendo reportes',
        failed_set_speed = 'No pusiste una velocidad.. (`fast` para super-rapido, `normal` para normal)',
        failed_set_model = 'No estableciste un modelo..',
        failed_entity_copy = '¡No hay información de entidad de freeaim para copiar al portapapeles!',
    },
    success = {
        blips_activated = 'Blips activados',
        names_activated = 'Nombres activados',
        coords_copied = '¡Coordenadas copiadas al portapapeles!',
        heading_copied = '¡Encabezado copiado al portapapeles!',
        changed_perm = 'Grupo de autoridad cambiado',
        entered_vehicle = 'Vehículo ingresado',
        success_vehicle_owner = '¡El vehículo ahora es tuyo!',
        receive_reports = 'Estás recibiendo reports',
        entity_copy = '¡Información de la entidad Freeaim copiada al portapapeles!',
        spawn_weapon = 'Has generado un arma ',
        noclip_enabled = 'No-clip activado',
        noclip_disabled = 'No-clip desactivado',
    },
    info = {
        ped_coords = 'Coordenadas de Ped:',
        vehicle_dev_data = 'Datos del desarrollador del vehículo:',
        ent_id = 'ID de la entidad:',
        net_id = 'ID Net:',
        net_id_not_registered = 'No registrado',
        model = 'Modelo: ',
        hash = 'Hash',
        eng_health = 'Integridad del motor:',
        body_health = 'Integridad estrucutural:',
        go_to = 'Ir a',
        remove = 'Eliminar',
        confirm = 'Confirmar',
        reason_title = 'Razón',
        length = 'Duración',
        options = 'Opciones',
        position = 'Posición',
        your_position = 'a tu posición',
        open = 'Abrir',
        inventories = 'inventarios',
        reason = 'Tienes que dar una razón',
        give = 'dar',
        id = 'ID:',
        player_name = 'Nombre del jugador',
        obj = 'Obj',
        ammoforthe = '+%{value} Munición para %{weapon}',
        kicked_server = 'Te han kickeado del servidor.',
        check_discord = '🔸 Revisa nuestro Discord para más información: ',
        banned = 'Has sido baneado:',
        ban_perm = '\n\nTu banneo es permanente.\n🔸 Revisa nuestro Discord para más información: ',
        ban_expires = '\n\n Tu Ban expira en: ',
        rank_level = 'Su nivel de permiso es ahora ',
        admin_report = 'Reporte de administración - ',
        staffchat = 'STAFFCHAT - ',
        warning_chat_message = '^8ADVERTENCIA ^7 Has sido advertido por',
        warning_staff_message = '^8ADVERTENCIA ^7 Has advertido ',
        no_reason_specified = 'Sin motivo especificado',
        server_restart = 'Reinicio del servidor, Revisa nuestro Discord para más información: ',
        entity_view_distance = 'Distancia de visualización de la entidad establecida en: %{distance} metros',
        entity_view_info = 'Información de la entidad',
        entity_view_title = 'Modo de puntería libre de la entidad',
        entity_freeaim_delete = 'Eliminar entidad',
        entity_freeaim_freeze = 'Congelar entidad',
        entity_frozen = 'Congelado',
        entity_unfrozen = 'Descongelado',
        model_hash = 'Modelo hash:',
        obj_name = 'Nombre del objeto:',
        ent_owner = 'Propietario de la entidad:',
        cur_health = 'Salud actual:',
        max_health = 'Salud máxima:',
        armadura = 'Armadura:',
        rel_group = 'Grupo de Relación:',
        rel_to_player = 'Relación con el jugador:',
        rel_group_custom = 'Relación personalizada',
        veh_acceleration = 'Aceleración:',
        veh_cur_gear = 'Velocidad actual:',
        veh_speed_kph = 'Kph:',
        veh_speed_mph = 'Mph:',
        veh_rpm = 'Rpm:',
        dist_to_obj = 'Dist:',
        obj_heading = 'Título:',
        obj_coords = 'Coordenadas:',
        obj_rot = 'Rotación:',
        obj_velocity = 'Velocidad:',
        obj_unknown = 'Desconocido',
        you_have = 'Tienes',
        freeaim_entity = 'la entidad freeaim',
        entity_del = 'Entidad eliminada',
        entity_del_error = 'Error al eliminar la entidad',
    },
    menu = {
        admin_menu = 'Menú de administración',
        admin_options = 'Opciones de administración',
        online_players = 'Jugadores en línea',
        manage_server = 'Administrar servidor',
        weather_conditions = 'Opciones meteorológicas disponibles',
        dealer_list = 'Lista de distribuidores',
        ban = 'Ban',
        kick = 'Kick',
        permissions = 'Permisos',
        developer_options = 'Opciones de desarrollador',
        vehicle_options = 'Opciones de vehículos',
        vehicle_categories = 'Categorías de vehículos',
        vehicle_models = 'Modelos de vehículos',
        player_management = 'Gestión de jugadores',
        server_management = 'Administración del servidor',
        vehicles = 'Vehículos',
        noclip = 'NoClip',
        revive = 'Revivir',
        invisible = 'Invisible',
        god = 'Modo Dios',
        names = 'Nombres',
        blips = 'Blips',
        weather_options = 'Opciones meteorológicas',
        server_time = 'Hora del Servidor',
        time = 'Hora',
        copy_vector3 = 'Copiar vector3',
        copy_vector4 = 'Copiar vector4',
        display_coords = 'Mostrar coordenadas',
        copy_heading = 'Copiar Encabezado',
        vehicle_dev_mode = 'Modo de desarrollo de vehículos',
        spawn_vehicle = 'Generación de Vehículo',
        fix_vehicle = 'Arreglar vehículo',
        buy = 'Comprar',
        remove_vehicle = 'Eliminar vehículo',
        edit_dealer = 'Editar distribuidor ',
        dealer_name = 'Nombre del distribuidor',
        category_name = 'Nombre de la categoría',
        kill = 'Matar',
        freeze = 'Congelar',
        spectate = 'Espectar',
        bring = 'Traer',
        sit_in_vehicle = 'Sentar en vehículo',
        open_inv = 'Abrir el inventario',
        give_clothing_menu = 'Dar menú de ropa',
        hud_dev_mode = 'Modo de desarrollo (qb-hud)',
        entity_view_options = 'Modo de vista de entidad',
        entity_view_distance = 'Establecer distancia de visualización',
        entity_view_freeaim = 'Modo de puntería libre',
        entity_view_peds = 'Mostrar peds',
        entity_view_vehicles = 'Mostrar vehículos',
        entity_view_objects = 'Mostrar objetos',
        entity_view_freeaim_copy = 'Copiar información de la entidad Freeaim',
        spawn_weapons = 'Generar arma',
        max_mods = 'Máximas modificaciones de vehículo',
    },
    desc = {
        admin_options_desc = 'Varias opciones de administración',
        player_management_desc = 'Ver lista de jugadores',
        server_management_desc = 'Varias opciones del servidor',
        vehicles_desc = 'Opciones de vehículos',
        dealer_desc = 'Lista de distribuidores existentes',
        noclip_desc = 'Habilitar/Deshabilitar NoClip',
        revive_desc = 'Revivirte a ti mismo',
        invisible_desc = 'Habilitar/Deshabilitar Invisibilidad',
        god_desc = 'Habilitar/Deshabilitar Modo Dios',
        names_desc = 'Habilitar/Deshabilitar nombres sobre la cabeza',
        blips_desc = 'Habilitar/Deshabilitar Blips para jugadores en mapas',
        weather_desc = 'Cambiar el clima',
        developer_desc = 'Varias opciones de desarrollo',
        vector3_desc = 'Copiar vector3 Al portapapeles',
        vector4_desc = 'Copiar vector4 Al portapapeles',
        display_coords_desc = 'Mostrar coordenadas en pantalla',
        copy_heading_desc = 'Copiar encabezado al Portapapeles',
        vehicle_dev_mode_desc = 'Mostrar información del vehículo',
        delete_laser_desc = 'Habilitar/Deshabilitar Laser',
        spawn_vehicle_desc = 'Generar un vehículo',
        fix_vehicle_desc = 'Reparar el vehículo en el que se encuentra',
        buy_desc = 'Compra el vehículo gratis',
        remove_vehicle_desc = 'Eliminar vehículo más cercano',
        dealergoto_desc = 'Ir al distribuidor',
        dealerremove_desc = 'Eliminar distribuidor',
        kick_reason = 'Razón del Kick',
        confirm_kick = 'Confirmar el Kick',
        ban_reason = 'Razón del Ban',
        confirm_ban = 'Confirmar el ban',
        sit_in_veh_desc = 'Sentar en',
        sit_in_veh_desc2 = "'s vehículo",
        clothing_menu_desc = 'Dale el menú de Ropa a',
        hud_dev_mode_desc = 'Habilitar/Deshabilitar Modo desarrollador',
        entity_view_desc = 'Ver información sobre las entidades',
        entity_view_freeaim_desc = 'Habilitar/Deshabilitar la información de la entidad a través de freeaim',
        entity_view_peds_desc = 'Habilitar/Deshabilitar información de ped en el mundo',
        entity_view_vehicles_desc = 'Habilitar/Deshabilitar información de vehículos en el mundo',
        entity_view_objects_desc = 'Habilitar/Deshabilitar la información del objeto en el mundo',
        entity_view_freeaim_copy_desc = 'Copia la información de la entidad Free Aim al portapapeles',
        spawn_weapons_desc = 'Genera cualquier arma.',
        max_mod_desc = 'Modificaciones del vehículo actual al máximo',
    },
    time = {
        ban_length = 'Duración del Ban',
        onehour = '1 hora',
        sixhour = '6 horas',
        twelvehour = '12 horas',
        oneday = '1 Dia',
        threeday = '3 Días',
        oneweek = '1 Semana',
        onemonth = '1 Mes',
        threemonth = '3 Meses',
        sixmonth = '6 Meses',
        oneyear = '1 Año',
        permanent = 'Permanente',
        self = 'Uno mismo',
        changed = 'El tiempo cambió a %{time} hrs 00 min',
    },
    weather = {
        extra_sunny = 'Extra soleado',
        extra_sunny_desc = '¡Me estoy derritiendo!',
        clear = 'Claro',
        clear_desc = '¡El día perfecto!',
        neutral = 'Neutral',
        neutral_desc = '¡Solo un día normal!',
        smog = 'Niebla',
        smog_desc = '¡Maquina de humo!',
        foggy = 'Neblinoso',
        foggy_desc = '¡Maquina de humo x2!',
        overcast = 'Nublado',
        overcast_desc = '¡No demasiado soleado!',
        clouds = 'Nubes',
        clouds_desc = '¿Dónde está el sol?',
        clearing = 'Despejado',
        clearing_desc = '¡Las nubes comienzan a despejarse!',
        rain = 'Lluvia',
        rain_desc = '¡Haz que llueva!',
        thunder = 'Truenos',
        thunder_desc = '¡Corre a esconderte!',
        snow = 'Nieve',
        snow_desc = '¿Hace frío aquí?',
        blizzard = 'Tormenta de nieve',
        blizzed_desc = '¿Maquina de nieve?',
        light_snow = 'Nieve ligera',
        light_snow_desc = '¡Empezando a sentirse como la Navidad!',
        heavy_snow = 'Fuertes nevadas (NAVIDAD)',
        heavy_snow_desc = '¡Guerra de nieve!',
        halloween = 'Halloween',
        halloween_desc = '¡¿Que fue ese ruido?!',
        weather_changed = 'Clima cambiado a: %{value}',
    },
    commands = {
        blips_for_player = 'Mostrar blips para los jugadores (solo administrador)',
        player_name_overhead = 'Mostrar el nombre del jugador en la parte superior (solo administrador)',
        coords_dev_command = 'Habilitar visualización de coordenadas para material de desarrollo (solo administrador)',
        toogle_noclip = 'Alternar noclip (solo administrador)',
        save_vehicle_garage = 'Guarde el vehículo en su garaje (solo administrador)',
        make_announcement = 'Hacer un anuncio (solo administrador)',
        open_admin = 'Abrir menú de administración (solo administrador)',
        staffchat_message = 'Enviar un mensaje a todo el personal (solo administrador)',
        nui_focus = 'Give A Player NUI Focus (solo administrador)',
        warn_a_player = 'Advertir a un jugador (solo administrador)',
        check_player_warning = 'Comprobar las advertencias del jugador (solo administrador)',
        delete_player_warning = 'Eliminar advertencias de los jugadores (solo administrador)',
        reply_to_report = 'Responder a un Reporte (solo administrador)',
        change_ped_model = 'Cambiar modelo de Ped (solo administrador)',
        set_player_foot_speed = 'Establecer la velocidad del pie del jugador (solo administrador)',
        report_toggle = 'Alternar reportes entrantes (solo administrador)',
        kick_all = 'Kick todos los jugadores',
        ammo_amount_set = 'Establezca su cantidad de munición (solo administrador)',
    }
}

if GetConvar('qb_locale', 'en') == 'es' then
    Lang = Locale:new({
        phrases = Translations,
        warnOnMissing = true,
        fallbackLang = Lang,
    })
end
