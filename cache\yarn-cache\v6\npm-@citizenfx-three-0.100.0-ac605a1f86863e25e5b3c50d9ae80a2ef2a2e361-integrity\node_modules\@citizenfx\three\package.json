{"name": "@citizenfx/three", "version": "0.100.0", "description": "JavaScript 3D library", "main": "build/three.js", "repository": "citizenfx/three.js", "jsnext:main": "build/three.module.js", "module": "build/three.module.js", "files": ["package.json", "LICENSE", "README.md", "build/three.js", "build/three.min.js", "build/three.module.js", "src", "examples/js", "examples/fonts"], "directories": {"doc": "docs", "example": "examples", "test": "test"}, "eslintConfig": {"extends": "mdcs", "plugins": ["html"]}, "scripts": {"build": "rollup -c", "build-test": "rollup -c test/rollup.unit.config.js", "build-closure": "rollup -c && java -jar node_modules/google-closure-compiler-java/compiler.jar --warning_level=VERBOSE --jscomp_off=globalThis --jscomp_off=checkTypes --externs utils/build/externs.js --language_in=ECMASCRIPT5_STRICT --js build/three.js --js_output_file build/three.min.js", "build-examples": "rollup -c rollup-examples.config.js", "dev": "concurrently --names \"R<PERSON><PERSON><PERSON>,HTTP\" -c \"bgBlue.bold,bgGreen.bold\" \"rollup -c -w -m inline\" \"http-server -c-1 -p 8080\"", "dev-test": "concurrently --names \"ROLLUP,ROLLUPTEST,HTTP\" -c \"bgBlue.bold,bgRed.bold,bgGreen.bold\" \"rollup -c -w -m inline\" \"rollup -c test/rollup.unit.config.js -w -m inline\" \"http-server -p 8080\"", "start": "npm run dev", "lint": "eslint src", "test": "npm run build-test && qunit test/unit/three.source.unit.js", "travis": "npm run lint && npm test", "editor": "electron ./editor/main.js"}, "keywords": ["three", "three.js", "3d", "webgl"], "author": "m<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/mrdoob/three.js/issues"}, "homepage": "https://threejs.org/", "devDependencies": {"concurrently": "^4.1.0", "electron": "^4.0.0", "eslint": "^5.11.1", "eslint-config-mdcs": "^4.2.3", "eslint-plugin-html": "^5.0.0", "google-closure-compiler": "20181210.0.0", "http-server": "^0.11.1", "qunit": "^2.8.0", "rollup": "^1.0.0"}, "jspm": {"files": ["package.json", "LICENSE", "README.md", "build/three.js", "build/three.min.js", "build/three.module.js"], "directories": {}}}