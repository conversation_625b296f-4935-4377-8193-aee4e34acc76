{"definitions": {"HandlerFunction": {"description": "Function that executes for every progress step", "instanceof": "Function", "tsType": "((percentage: number, msg: string, ...args: string[]) => void)"}, "ProgressPluginOptions": {"type": "object", "additionalProperties": false, "properties": {"activeModules": {"description": "Show active modules count and one active module in progress message", "type": "boolean"}, "entries": {"description": "Show entries count in progress message", "type": "boolean"}, "handler": {"description": "Function that executes for every progress step", "anyOf": [{"$ref": "#/definitions/HandlerFunction"}]}, "modules": {"description": "Show modules count in progress message", "type": "boolean"}, "modulesCount": {"description": "Minimum modules count to start with. Only for mode=modules. Default: 500", "type": "number"}, "profile": {"description": "Collect profile data for progress steps. Default: false", "enum": [true, false, null]}}}}, "title": "ProgressPluginArgument", "oneOf": [{"$ref": "#/definitions/ProgressPluginOptions"}, {"$ref": "#/definitions/HandlerFunction"}]}