-- Admin commands for Weather Health System

local QBCore = exports['qb-core']:GetCoreObject()

-- Admin command to check player health status
QBCore.Commands.Add('checkhealthstatus', 'Check player health status (Admin Only)', {{name = 'id', help = 'Player ID'}}, true, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Check admin permission
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local targetId = tonumber(args[1])
    local TargetPlayer = QBCore.Functions.GetPlayer(targetId)
    
    if not TargetPlayer then
        TriggerClientEvent('QBCore:Notify', source, 'Player not found', 'error')
        return
    end
    
    local citizenid = TargetPlayer.PlayerData.citizenid
    local healthData = exports['qb-weatherhealth']:getPlayerHealthData(citizenid)
    
    if healthData then
        local status = string.format(
            "Player: %s\nHealth: %d/100\nDisease: %s\nSeverity: %d\nComfort: %s\nImmunity: %d",
            TargetPlayer.PlayerData.name,
            healthData.health or 100,
            healthData.diseaseType or 'None',
            healthData.diseaseSeverity or 0,
            healthData.comfort or 'Unknown',
            healthData.immunityLevel or 50
        )
        TriggerClientEvent('QBCore:Notify', source, status, 'primary', 10000)
    else
        TriggerClientEvent('QBCore:Notify', source, 'No health data found for player', 'error')
    end
end)

-- Admin command to cure player disease
QBCore.Commands.Add('curedisease', 'Cure player disease (Admin Only)', {{name = 'id', help = 'Player ID'}}, true, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Check admin permission
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local targetId = tonumber(args[1])
    local TargetPlayer = QBCore.Functions.GetPlayer(targetId)
    
    if not TargetPlayer then
        TriggerClientEvent('QBCore:Notify', source, 'Player not found', 'error')
        return
    end
    
    local citizenid = TargetPlayer.PlayerData.citizenid
    local healthData = exports['qb-weatherhealth']:getPlayerHealthData(citizenid)
    
    if healthData and healthData.diseaseType then
        local diseaseType = healthData.diseaseType
        healthData.diseaseType = nil
        healthData.diseaseStartTime = nil
        healthData.diseaseSeverity = 0
        healthData.health = 100
        
        TriggerClientEvent('weatherhealth:client:cureDisease', TargetPlayer.PlayerData.source, diseaseType)
        TriggerClientEvent('QBCore:Notify', source, string.format('Cured %s of %s', TargetPlayer.PlayerData.name, diseaseType), 'success')
        TriggerClientEvent('QBCore:Notify', TargetPlayer.PlayerData.source, 'You have been cured by an administrator', 'success')
    else
        TriggerClientEvent('QBCore:Notify', source, 'Player is not sick', 'error')
    end
end)

-- Admin command to give player disease
QBCore.Commands.Add('givedisease', 'Give player disease (Admin Only)', {{name = 'id', help = 'Player ID'}, {name = 'disease', help = 'Disease type (cold/flu/heatstroke)'}}, true, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Check admin permission
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local targetId = tonumber(args[1])
    local diseaseType = args[2]
    local TargetPlayer = QBCore.Functions.GetPlayer(targetId)
    
    if not TargetPlayer then
        TriggerClientEvent('QBCore:Notify', source, 'Player not found', 'error')
        return
    end
    
    if not Config.HealthEffects.diseases[diseaseType] then
        TriggerClientEvent('QBCore:Notify', source, 'Invalid disease type. Available: cold, flu, heatstroke, hypothermia', 'error')
        return
    end
    
    local citizenid = TargetPlayer.PlayerData.citizenid
    local healthData = exports['qb-weatherhealth']:getPlayerHealthData(citizenid)
    
    if healthData then
        healthData.diseaseType = diseaseType
        healthData.diseaseStartTime = os.time()
        healthData.diseaseSeverity = 1
        
        TriggerClientEvent('weatherhealth:client:contractDisease', TargetPlayer.PlayerData.source, diseaseType, 1)
        TriggerClientEvent('QBCore:Notify', source, string.format('Gave %s %s', TargetPlayer.PlayerData.name, diseaseType), 'success')
        TriggerClientEvent('QBCore:Notify', TargetPlayer.PlayerData.source, string.format('You have contracted %s', diseaseType), 'error')
    end
end)

-- Admin command to get weather health statistics
QBCore.Commands.Add('weatherstats', 'Get weather health statistics (Admin Only)', {}, false, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Check admin permission
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local stats = exports['qb-weatherhealth']:getWeatherStats()
    local currentWeather = exports['qb-weatherhealth']:getCurrentWeather()
    
    local message = string.format(
        "Weather Health System Stats:\nCurrent Weather: %s\nTotal Players: %d\nAffected Players: %d\nSick Players: %d\nTotal NPCs: %d\nSick NPCs: %d\nDead NPCs: %d",
        currentWeather,
        stats.totalPlayers,
        stats.affectedPlayers,
        stats.sickPlayers,
        stats.totalNPCs,
        stats.sickNPCs,
        stats.deadNPCs
    )
    
    TriggerClientEvent('QBCore:Notify', source, message, 'primary', 15000)
end)

-- Admin command to reset player health data
QBCore.Commands.Add('resethealthdata', 'Reset player health data (Admin Only)', {{name = 'id', help = 'Player ID'}}, true, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Check admin permission
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local targetId = tonumber(args[1])
    local TargetPlayer = QBCore.Functions.GetPlayer(targetId)
    
    if not TargetPlayer then
        TriggerClientEvent('QBCore:Notify', source, 'Player not found', 'error')
        return
    end
    
    local citizenid = TargetPlayer.PlayerData.citizenid
    
    -- Reset health data in database
    MySQL.execute('DELETE FROM weather_player_health WHERE citizenid = ?', {citizenid})
    
    -- Reset in memory
    local healthData = exports['qb-weatherhealth']:getPlayerHealthData(citizenid)
    if healthData then
        healthData.health = 100
        healthData.diseaseType = nil
        healthData.diseaseStartTime = nil
        healthData.diseaseSeverity = 0
        healthData.comfort = 'comfortable'
        healthData.clothingWarmth = 0
        healthData.immunityLevel = 50
    end
    
    TriggerClientEvent('QBCore:Notify', source, string.format('Reset health data for %s', TargetPlayer.PlayerData.name), 'success')
    TriggerClientEvent('QBCore:Notify', TargetPlayer.PlayerData.source, 'Your health data has been reset by an administrator', 'primary')
end)

-- Admin command to toggle weather health system
QBCore.Commands.Add('toggleweatherhealth', 'Toggle weather health system (Admin Only)', {}, false, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Check admin permission
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'You do not have permission to use this command', 'error')
        return
    end
    
    -- Toggle system (this would need to be implemented in main.lua)
    TriggerEvent('weatherhealth:server:toggleSystem')
    TriggerClientEvent('QBCore:Notify', source, 'Weather health system toggled', 'primary')
end)

-- Admin command to cleanup old data
QBCore.Commands.Add('cleanuphealthdata', 'Cleanup old health data (Admin Only)', {{name = 'days', help = 'Days to keep (default: 30)'}}, false, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Check admin permission
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local days = tonumber(args[1]) or 30
    
    exports['qb-weatherhealth']:cleanupOldData(days)
    TriggerClientEvent('QBCore:Notify', source, string.format('Cleaned up health data older than %d days', days), 'success')
end)

-- Admin command to backup player data
QBCore.Commands.Add('backuphealthdata', 'Backup player health data (Admin Only)', {}, false, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Check admin permission
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'You do not have permission to use this command', 'error')
        return
    end
    
    exports['qb-weatherhealth']:backupPlayerData()
    TriggerClientEvent('QBCore:Notify', source, 'Player health data backed up successfully', 'success')
end)

-- Admin command to get detailed system statistics
QBCore.Commands.Add('detailedweatherstats', 'Get detailed weather health statistics (Admin Only)', {}, false, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Check admin permission
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local stats = exports['qb-weatherhealth']:getSystemStats()
    
    local message = string.format(
        "Detailed Weather Health Stats:\n\nPlayers:\n- Total: %d\n- Sick: %d\n- Avg Health: %.1f\n- Avg Immunity: %.1f\n\nNPCs:\n- Total: %d\n- Sick: %d\n- Dead: %d\n- Avg Health: %.1f\n\nTreatments (7 days):\n- Total: %d\n- Successful: %d\n- Success Rate: %.1f%%",
        stats.players.total or 0,
        stats.players.sick or 0,
        stats.players.avg_health or 0,
        stats.players.avg_immunity or 0,
        stats.npcs.total or 0,
        stats.npcs.sick or 0,
        stats.npcs.dead or 0,
        stats.npcs.avg_health or 0,
        stats.treatments.total or 0,
        stats.treatments.successful or 0,
        stats.treatments.success_rate or 0
    )
    
    TriggerClientEvent('QBCore:Notify', source, message, 'primary', 20000)
end)

print("^2[WeatherHealth]^7 Admin commands loaded")
