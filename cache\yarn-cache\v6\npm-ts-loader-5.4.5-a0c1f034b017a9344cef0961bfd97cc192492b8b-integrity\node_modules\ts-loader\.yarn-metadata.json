{"manifest": {"name": "ts-loader", "version": "5.4.5", "description": "TypeScript loader for webpack", "main": "index.js", "types": "dist/types/index.d.ts", "scripts": {"build": "tsc --version && tsc --project \"./src\"", "lint": "tslint --project \"./src\"", "comparison-tests": "tsc --project \"./test/comparison-tests\" && npm link ./test/comparison-tests/testLib && node test/comparison-tests/run-tests.js", "comparison-tests-generate": "node test/comparison-tests/stub-new-version.js", "execution-tests": "node test/execution-tests/run-tests.js", "test": "node test/run-tests.js", "docker:build": "docker build -t ts-loader .", "postdocker:build": "docker run -it ts-loader yarn test"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{ts,md}": ["prettier --write", "git add"]}, "repository": {"type": "git", "url": "https://github.com/TypeStrong/ts-loader.git"}, "keywords": ["ts-loader", "typescript-loader", "webpack", "loader", "typescript", "ts"], "engines": {"node": ">=6.11.5"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://blog.johnnyreilly.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://blog.johnnyreilly.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.jbrantly.com/"}], "license": "MIT", "bugs": {"url": "https://github.com/TypeStrong/ts-loader/issues"}, "homepage": "https://github.com/TypeStrong/ts-loader", "dependencies": {"chalk": "^2.3.0", "enhanced-resolve": "^4.0.0", "loader-utils": "^1.0.2", "micromatch": "^3.1.4", "semver": "^5.0.1"}, "devDependencies": {"@types/micromatch": "^3.1.0", "@types/node": "*", "@types/semver": "^5.4.0", "@types/webpack": "^4.4.29", "babel": "^6.0.0", "babel-core": "^6.0.0", "babel-loader": "^7.0.0", "babel-polyfill": "^6.16.0", "babel-preset-es2015": "^6.0.0", "babel-preset-es2016": "^6.16.0", "babel-preset-react": "^6.0.0", "escape-string-regexp": "^1.0.3", "fs-extra": "^7.0.0", "glob": "^7.1.1", "html-webpack-plugin": "^3.2.0", "husky": "^1.0.0", "jasmine-core": "^3.0.0", "karma": "^3.0.0", "karma-chrome-launcher": "^2.2.0", "karma-jasmine": "^2.0.0", "karma-mocha-reporter": "^2.0.0", "karma-sourcemap-loader": "^0.3.6", "karma-webpack": "^4.0.0-rc.5", "lint-staged": "^7.0.0", "mkdirp": "^0.5.1", "mocha": "^5.0.0", "prettier": "^1.11.1", "rimraf": "^2.6.2", "tslint": "^5.11.0", "tslint-config-prettier": "^1.15.0", "typescript": "^3.1.1", "webpack": "^4.5.0", "webpack-cli": "^3.1.1"}, "peerDependencies": {"typescript": "*"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-ts-loader-5.4.5-a0c1f034b017a9344cef0961bfd97cc192492b8b-integrity\\node_modules\\ts-loader\\package.json", "readmeFilename": "README.md", "readme": "# TypeScript loader for webpack\n\n[![npm version](https://img.shields.io/npm/v/ts-loader.svg)](https://www.npmjs.com/package/ts-loader)\n[![Linux Build Status](https://travis-ci.org/TypeStrong/ts-loader.svg?branch=master)](https://travis-ci.org/TypeStrong/ts-loader)\n[![Windows Build Status](https://ci.appveyor.com/api/projects/status/bjh0r0d4ckspgkh9/branch/master?svg=true)](https://ci.appveyor.com/project/JohnReilly/ts-loader/branch/master)\n[![Downloads](http://img.shields.io/npm/dm/ts-loader.svg)](https://npmjs.org/package/ts-loader)\n[![node version](https://img.shields.io/node/v/ts-loader.svg)](https://www.npmjs.com/package/ts-loader)\n[![code style: prettier](https://img.shields.io/badge/code_style-prettier-ff69b4.svg)](https://github.com/prettier/prettier)\n[![Join the chat at https://gitter.im/TypeStrong/ts-loader](https://img.shields.io/badge/gitter-join%20chat-brightgreen.svg)](https://gitter.im/TypeStrong/ts-loader)\n\nThis is the typescript loader for webpack.\n\n## Getting Started\n\n### Examples\n\nWe have a number of example setups to accomodate different workflows. Our examples can be found [here](examples/).\n\nWe probably have more examples than we need.  That said, here's a good way to get started:\n\n- I want the simplest setup going.  Use \"[vanilla](examples/vanilla)\" ts-loader\n- I want the fastest compilation that's available.  Use [fork-ts-checker-webpack-plugin](https://github.com/Realytics/fork-ts-checker-webpack-plugin).  It performs type checking in a separate process with `ts-loader` just handling transpilation.\n\n### Faster Builds\n\nAs your project becomes bigger, compilation time increases linearly. It's because typescript's semantic checker has to inspect all files on every rebuild. The simple solution is to disable it by using the `transpileOnly: true` option, but doing so leaves you without type checking and *will not output declaration files*.\n\nYou probably don't want to give up type checking; that's rather the point of TypeScript. So what you can do is use the [fork-ts-checker-webpack-plugin](https://github.com/Realytics/fork-ts-checker-webpack-plugin). It runs the type checker on a separate process, so your build remains fast thanks to `transpileOnly: true` but you still have the type checking. Also, the plugin has several optimizations to make incremental type checking faster (AST cache, multiple workers).\n\nIf you'd like to see a simple setup take a look at [our simple example](examples/fork-ts-checker-webpack-plugin/). For a more complex setup take a look at our [more involved example](examples/react-babel-karma-gulp).\n\n### Yarn Plug’n’Play \n\n`ts-loader` supports [Yarn Plug’n’Play](https://yarnpkg.com/en/docs/pnp).  The recommended way to integrate is using the [pnp-webpack-plugin](https://github.com/arcanis/pnp-webpack-plugin#ts-loader-integration).\n\n### Babel\n\nts-loader works very well in combination with [babel](https://babeljs.io/) and [babel-loader](https://github.com/babel/babel-loader). There is an [example](https://github.com/Microsoft/TypeScriptSamples/tree/master/react-flux-babel-karma) of this in the official [TypeScript Samples](https://github.com/Microsoft/TypeScriptSamples). Alternatively take a look at our own [example](examples/react-babel-karma-gulp).\n\n### Parallelising Builds\n\nIt's possible to parallelise your builds. Historically this was useful from a performance perspective with webpack 2 / 3.  [With webpack 4+ there appears to be significantly less benefit and perhaps even cost.](https://blog.johnnyreilly.com/2018/12/you-might-not-need-thread-loader.html)\n\nBut if that's what you want to do, there's two ways to achieve this: [happypack](https://github.com/amireh/happypack) and [thread-loader](https://github.com/webpack-contrib/thread-loader). Both should be used in combination with [fork-ts-checker-webpack-plugin](https://github.com/Realytics/fork-ts-checker-webpack-plugin) for typechecking.) \n\nTo read more, look at [this post](https://medium.com/webpack/typescript-webpack-super-pursuit-mode-83cc568dea79) by [@johnny_reilly](https://twitter.com/johnny_reilly) on the webpack publication channel.\n\nIf you'd like find out further ways to improve your build using the watch API then take a look at [this post](https://medium.com/@kenneth_chau/speeding-up-webpack-typescript-incremental-builds-by-7x-3912ba4c1d15) by [@kenneth_chau](https://twitter.com/kenneth_chau).\n\n### Installation\n\n```\nyarn add ts-loader --dev\n```\n\nor\n\n```\nnpm install ts-loader --save-dev\n```\n\nYou will also need to install TypeScript if you have not already.\n\n```\nyarn add typescript --dev\n```\n\nor\n\n```\nnpm install typescript --save-dev\n```\n\n### Running\n\nUse webpack like normal, including `webpack --watch` and `webpack-dev-server`, or through another\nbuild system using the [Node.js API](http://webpack.github.io/docs/node.js-api.html).\n\n### Compatibility\n\n* TypeScript: 2.4.1+\n* webpack: 4.x+ (please use ts-loader 3.x if you need webpack 2 or 3 support)\n* node: 6.11.5 minimum (aligned with webpack 4)\n\nA full test suite runs each night (and on each pull request). It runs both on [Linux](https://travis-ci.org/TypeStrong/ts-loader) and [Windows](https://ci.appveyor.com/project/JohnReilly/ts-loader), testing ts-loader against major releases of TypeScript. The test suite also runs against TypeScript@next (because we want to use it as much as you do).\n\nIf you become aware of issues not caught by the test suite then please let us know. Better yet, write a test and submit it in a PR!\n\n### Configuration\n\n1. Create or update `webpack.config.js` like so:\n\n   ```javascript\n   module.exports = {\n     mode: \"development\",\n     devtool: \"inline-source-map\",\n     entry: \"./app.ts\",\n     output: {\n       filename: \"bundle.js\"\n     },\n     resolve: {\n       // Add `.ts` and `.tsx` as a resolvable extension.\n       extensions: [\".ts\", \".tsx\", \".js\"]\n     },\n     module: {\n       rules: [\n         // all files with a `.ts` or `.tsx` extension will be handled by `ts-loader`\n         { test: /\\.tsx?$/, loader: \"ts-loader\" }\n       ]\n     }\n   };\n   ```\n\n2. Add a [`tsconfig.json`](https://www.typescriptlang.org/docs/handbook/tsconfig-json.html) file. (The one below is super simple; but you can tweak this to your hearts desire)\n\n   ```json\n   {\n     \"compilerOptions\": {\n       \"sourceMap\": true\n     }\n   }\n   ```\n\nThe [tsconfig.json](http://www.typescriptlang.org/docs/handbook/tsconfig-json.html) file controls\nTypeScript-related options so that your IDE, the `tsc` command, and this loader all share the\nsame options.\n\n#### `devtool` / sourcemaps\n\nIf you want to be able to debug your original source then you can thanks to the magic of sourcemaps. There are 2 steps to getting this set up with ts-loader and webpack.\n\nFirst, for ts-loader to produce **sourcemaps**, you will need to set the [tsconfig.json](http://www.typescriptlang.org/docs/handbook/tsconfig-json.html) option as `\"sourceMap\": true`.\n\nSecond, you need to set the `devtool` option in your `webpack.config.js` to support the type of sourcemaps you want. To make your choice have a read of the [`devtool` webpack docs](https://webpack.js.org/configuration/devtool/). You may be somewhat daunted by the choice available. You may also want to vary the sourcemap strategy depending on your build environment. Here are some example strategies for different environments:\n\n* `devtool: 'inline-source-map'` - Solid sourcemap support; the best \"all-rounder\". Works well with karma-webpack (not all strategies do)\n* `devtool: 'cheap-module-eval-source-map'` - Best support for sourcemaps whilst debugging.\n* `devtool: 'source-map'` - Approach that plays well with UglifyJsPlugin; typically you might use this in Production\n\n### Code Splitting and Loading Other Resources\n\nLoading css and other resources is possible but you will need to make sure that\nyou have defined the `require` function in a [declaration file](https://www.typescriptlang.org/docs/handbook/writing-declaration-files.html).\n\n```typescript\ndeclare var require: {\n  <T>(path: string): T;\n  (paths: string[], callback: (...modules: any[]) => void): void;\n  ensure: (\n    paths: string[],\n    callback: (require: <T>(path: string) => T) => void\n  ) => void;\n};\n```\n\nThen you can simply require assets or chunks per the [webpack documentation](https://webpack.js.org/guides/code-splitting/).\n\n```javascript\nrequire(\"!style!css!./style.css\");\n```\n\nThe same basic process is required for code splitting. In this case, you `import` modules you need but you\ndon't directly use them. Instead you require them at [split points](http://webpack.github.io/docs/code-splitting.html#defining-a-split-point). See [this example](test/comparison-tests/codeSplitting) and [this example](test/comparison-tests/es6codeSplitting) for more details.\n\n[TypeScript 2.4 provides support for ECMAScript's new `import()` calls. These calls import a module and return a promise to that module.](https://blogs.msdn.microsoft.com/typescript/2017/06/12/announcing-typescript-2-4-rc/) This is also supported in webpack - details on usage can be found [here](https://webpack.js.org/guides/code-splitting-async/#dynamic-import-import-). Happy code splitting!\n\n### Declarations (.d.ts)\n\nTo output a built .d.ts file, you can set `\"declaration\": true` in your tsconfig, and use the [DeclarationBundlerPlugin](https://www.npmjs.com/package/declaration-bundler-webpack-plugin) in your webpack config.\n\n### Failing the build on TypeScript compilation error\n\nThe build **should** fail on TypeScript compilation errors as of webpack 2. If for some reason it does not, you can use the [webpack-fail-plugin](https://www.npmjs.com/package/webpack-fail-plugin).\n\nFor more background have a read of [this issue](https://github.com/TypeStrong/ts-loader/issues/108).\n\n### `baseUrl` / `paths` module resolution\n\nIf you want to resolve modules according to `baseUrl` and `paths` in your `tsconfig.json` then you can use the [tsconfig-paths-webpack-plugin](https://www.npmjs.com/package/tsconfig-paths-webpack-plugin) package. For details about this functionality, see the [module resolution documentation](https://www.typescriptlang.org/docs/handbook/module-resolution.html#base-url).\n\nThis feature requires webpack 2.1+ and TypeScript 2.0+. Use the config below or check the [package](https://github.com/dividab/tsconfig-paths-webpack-plugin/blob/master/README.md) for more information on usage.\n\n```javascript\nconst TsconfigPathsPlugin = require('tsconfig-paths-webpack-plugin');\n\nmodule.exports = {\n  ...\n  resolve: {\n    plugins: [new TsconfigPathsPlugin({ /*configFile: \"./path/to/tsconfig.json\" */ })]\n  }\n  ...\n}\n```\n\n### Options\n\nThere are two types of options: TypeScript options (aka \"compiler options\") and loader options. TypeScript options should be set using a tsconfig.json file. Loader options can be specified through the `options` property in the webpack configuration:\n\n```javascript\nmodule.exports = {\n  ...\n  module: {\n    rules: [\n      {\n        test: /\\.tsx?$/,\n        use: [\n          {\n            loader: 'ts-loader',\n            options: {\n              transpileOnly: true\n            }\n          }\n        ]\n      }\n    ]\n  }\n}\n```\n\n### Loader Options\n\n#### transpileOnly _(boolean) (default=false)_\n\nIf you want to speed up compilation significantly you can set this flag.\nHowever, many of the benefits you get from static type checking between\ndifferent dependencies in your application will be lost.\n\nIt's advisable to use `transpileOnly` alongside the [fork-ts-checker-webpack-plugin](https://github.com/Realytics/fork-ts-checker-webpack-plugin) to get full type checking again. To see what this looks like in practice then either take a look at [our simple example](examples/fork-ts-checker-webpack-plugin). For a more complex setup take a look at our [more involved example](examples/react-babel-karma-gulp).\n\nIf you enable this option, webpack 4 will give you \"export not found\" warnings any time you re-export a type:\n\n```\nWARNING in ./src/bar.ts\n1:0-34 \"export 'IFoo' was not found in './foo'\n @ ./src/bar.ts\n @ ./src/index.ts\n```\n\nThe reason this happens is that when typescript doesn't do a full type check, it does not have enough information to determine whether an imported name is a type or not, so when the name is then exported, typescript has no choice but to emit the export. Fortunately, the extraneous export should not be harmful, so you can just suppress these warnings:\n\n```javascript\nmodule.exports = {\n  ...\n  stats: {\n    warningsFilter: /export .* was not found in/\n  }\n}\n```\n\n#### happyPackMode _(boolean) (default=false)_\n\nIf you're using [HappyPack](https://github.com/amireh/happypack) or [thread-loader](https://github.com/webpack-contrib/thread-loader) to parallise your builds then you'll need to set this to `true`. This implicitly sets `*transpileOnly*` to `true` and **WARNING!** stops registering **_all_** errors to webpack.\n\nIt's advisable to use this with the [fork-ts-checker-webpack-plugin](https://github.com/Realytics/fork-ts-checker-webpack-plugin) to get full type checking again. To see what this looks like in practice then either take a look at [our simple HappyPack example](examples/happypack) / [our simple thread-loader example](examples/thread-loader). For a more complex setup take a look at our [more involved HappyPack example](examples/react-babel-karma-gulp-happypack) / [more involved thread-loader example](examples/react-babel-karma-gulp-thread-loader). **_IMPORTANT_**: If you are using fork-ts-checker-webpack-plugin alongside HappyPack or thread-loader then ensure you set the `checkSyntacticErrors` option like so:\n\n```javascript\n        new ForkTsCheckerWebpackPlugin({ checkSyntacticErrors: true })\n```\n\nThis will ensure that the plugin checks for both syntactic errors (eg `const array = [{} {}];`) and semantic errors (eg `const x: number = '1';`). By default the plugin only checks for semantic errors (as when used with ts-loader in `transpileOnly` mode, ts-loader will still report syntactic errors).\n\nAlso, if you are using `thread-loader` in watch mode, remember to set `poolTimeout: Infinity` so workers don't die.\n\n#### resolveModuleName and resolveTypeReferenceDirective:\n\nThese options should be functions which will be used to resolve the import statements and the `<reference types=\"...\">` directives instead of the default TypeScript implementation. It's not intended that these will typically be used by a user of `ts-loader` - they exist to facilitate functionality such as [Yarn Plug’n’Play](https://yarnpkg.com/en/docs/pnp).\n\n#### getCustomTransformers _( (program: Program) => { before?: TransformerFactory<SourceFile>[]; after?: TransformerFactory<SourceFile>[]; } )_\n\nProvide custom transformers - only compatible with TypeScript 2.3+ (and 2.4 if using `transpileOnly` mode). For example usage take a look at [typescript-plugin-styled-components](https://github.com/Igorbek/typescript-plugin-styled-components) or our [test](test/comparison-tests/customTransformer).\n\nYou can also pass a path string to locate a js module file which exports the function described above, this useful especially in `happyPackMode`. (Because forked processes cannot serialize functions see more at [related issue](https://github.com/Igorbek/typescript-plugin-styled-components/issues/6#issue-303387183))\n\n#### logInfoToStdOut _(boolean) (default=false)_\n\nThis is important if you read from stdout or stderr and for proper error handling.\nThe default value ensures that you can read from stdout e.g. via pipes or you use webpack -j to generate json output.\n\n#### logLevel _(string) (default=warn)_\n\nCan be `info`, `warn` or `error` which limits the log output to the specified log level.\nBeware of the fact that errors are written to stderr and everything else is written to stderr (or stdout if logInfoToStdOut is true).\n\n#### silent _(boolean) (default=false)_\n\nIf `true`, no console.log messages will be emitted. Note that most error\nmessages are emitted via webpack which is not affected by this flag.\n\n#### ignoreDiagnostics _(number[]) (default=[])_\n\nYou can squelch certain TypeScript errors by specifying an array of diagnostic\ncodes to ignore.\n\n#### reportFiles _(string[]) (default=[])_\n\nOnly report errors on files matching these glob patterns.\n\n```javascript\n  // in webpack.config.js\n  {\n    test: /\\.ts$/,\n    loader: 'ts-loader',\n    options: { reportFiles: ['src/**/*.{ts,tsx}', '!src/skip.ts'] }\n  }\n```\n\nThis can be useful when certain types definitions have errors that are not fatal to your application.\n\n#### compiler _(string) (default='typescript')_\n\nAllows use of TypeScript compilers other than the official one. Should be\nset to the NPM name of the compiler, eg [`ntypescript`](https://github.com/basarat/ntypescript).\n\n#### configFile _(string) (default='tsconfig.json')_\n\nAllows you to specify where to find the TypeScript configuration file.\n\nYou may provide\n\n* just a file name. The loader then will search for the config file of each entry point in the respective entry point's containing folder. If a config file cannot be found there, it will travel up the parent directory chain and look for the config file in those folders.\n* a relative path to the configuration file. It will be resolved relative to the respective `.ts` entry file.\n* an absolute path to the configuration file.\n\nPlease note, that if the configuration file is outside of your project directory, you might need to set the `context` option to avoid TypeScript issues (like TS18003).\nIn this case the `configFile` should point to the `tsconfig.json` and `context` to the project root.\n\n#### colors _(boolean) (default=true)_\n\nIf `false`, disables built-in colors in logger messages.\n\n#### errorFormatter _((message: ErrorInfo, colors: boolean) => string) (default=undefined)_\n\nBy default ts-loader formats TypeScript compiler output for an error or a warning in the style:\n\n```\n[tsl] ERROR in myFile.ts(3,14)\n      TS4711: you did something very wrong\n```\n\nIf that format is not to your taste you can supply your own formatter using the `errorFormatter` option. Below is a template for a custom error formatter. Please note that the `colors` parameter is an instance of [`chalk`](https://github.com/chalk/chalk) which you can use to color your output. (This instance will respect the `colors` option.)\n\n```javascript\nfunction customErrorFormatter(error, colors) {\n  const messageColor =\n    error.severity === \"warning\" ? colors.bold.yellow : colors.bold.red;\n  return (\n    \"Does not compute.... \" +\n    messageColor(Object.keys(error).map(key => `${key}: ${error[key]}`))\n  );\n}\n```\n\nIf the above formatter received an error like this:\n\n```json\n{\n  \"code\":2307,\n  \"severity\": \"error\",\n  \"content\": \"Cannot find module 'components/myComponent2'.\",\n  \"file\":\"/.test/errorFormatter/app.ts\",\n  \"line\":2,\n  \"character\":31\n}\n```\n\nIt would produce an error message that said:\n\n```\nDoes not compute.... code: 2307,severity: error,content: Cannot find module 'components/myComponent2'.,file: /.test/errorFormatter/app.ts,line: 2,character: 31\n```\n\nAnd the bit after \"Does not compute.... \" would be red.\n\n#### compilerOptions _(object) (default={})_\n\nAllows overriding TypeScript options. Should be specified in the same format\nas you would do for the `compilerOptions` property in tsconfig.json.\n\n#### instance _(string)_\n\nAdvanced option to force files to go through different instances of the\nTypeScript compiler. Can be used to force segregation between different parts\nof your code.\n\n#### appendTsSuffixTo _(RegExp[]) (default=[])_\n\n#### appendTsxSuffixTo _(RegExp[]) (default=[])_\n\nA list of regular expressions to be matched against filename. If filename matches one of the regular expressions, a `.ts` or `.tsx` suffix will be appended to that filename.\n\nThis is useful for `*.vue` [file format](https://vuejs.org/v2/guide/single-file-components.html) for now. (Probably will benefit from the new single file format in the future.)\n\nExample:\n\nwebpack.config.js:\n\n```javascript\nmodule.exports = {\n  entry: \"./index.vue\",\n  output: { filename: \"bundle.js\" },\n  resolve: {\n    extensions: [\".ts\", \".vue\"]\n  },\n  module: {\n    rules: [\n      { test: /\\.vue$/, loader: \"vue-loader\" },\n      {\n        test: /\\.ts$/,\n        loader: \"ts-loader\",\n        options: { appendTsSuffixTo: [/\\.vue$/] }\n      }\n    ]\n  }\n};\n```\n\nindex.vue\n\n```vue\n<template><p>hello {{msg}}</p></template>\n<script lang=\"ts\">\nexport default {\n  data(): Object {\n    return {\n      msg: \"world\"\n    };\n  }\n};\n</script>\n```\n\nWe can handle `.tsx` by quite similar way:\n\nwebpack.config.js:\n\n```javascript\nmodule.exports = {\n    entry: './index.vue',\n    output: { filename: 'bundle.js' },\n    resolve: {\n        extensions: ['.ts', '.tsx', '.vue', '.vuex']\n    },\n    module: {\n        rules: [\n            { test: /\\.vue$/, loader: 'vue-loader',\n              options: {\n                loaders: {\n                  ts: 'ts-loader',\n                  tsx: 'babel-loader!ts-loader',\n                }\n              }\n            },\n            { test: /\\.ts$/, loader: 'ts-loader', options: { appendTsSuffixTo: [/TS\\.vue$/] } }\n            { test: /\\.tsx$/, loader: 'babel-loader!ts-loader', options: { appendTsxSuffixTo: [/TSX\\.vue$/] } }\n        ]\n    }\n}\n```\n\ntsconfig.json (set `jsx` option to `preserve` to let babel handle jsx)\n\n```json\n{\n  \"compilerOptions\": {\n    \"jsx\": \"preserve\"\n  }\n}\n```\n\nindex.vue\n\n```vue\n<script lang=\"tsx\">\nexport default {\n  functional: true,\n  render(h, c) {\n    return (<div>Content</div>);\n  }\n}\n</script>\n```\n\nOr if you want to use only tsx, just use the `appendTsxSuffixTo` option only:\n\n```javascript\n            { test: /\\.ts$/, loader: 'ts-loader' }\n            { test: /\\.tsx$/, loader: 'babel-loader!ts-loader', options: { appendTsxSuffixTo: [/\\.vue$/] } }\n```\n\n#### onlyCompileBundledFiles _(boolean) (default=false)_\n\nThe default behavior of ts-loader is to act as a drop-in replacement for the `tsc` command,\nso it respects the `include`, `files`, and `exclude` options in your `tsconfig.json`, loading\nany files specified by those options. The `onlyCompileBundledFiles` option modifies this behavior,\nloading only those files that are actually bundled by webpack, as well as any `.d.ts` files included\nby the `tsconfig.json` settings. `.d.ts` files are still included because they may be needed for\ncompilation without being explicitly imported, and therefore not picked up by webpack.\n\n#### allowTsInNodeModules _(boolean) (default=false)_\n\nBy default, ts-loader will not compile `.ts` files in `node_modules`.\nYou should not need to recompile `.ts` files there, but if you really want to, use this option.\nNote that this option acts as a *whitelist* - any modules you desire to import must be included in\nthe `\"files\"` or `\"include\"` block of your project's `tsconfig.json`.\n\nSee: [https://github.com/Microsoft/TypeScript/issues/12358](https://github.com/Microsoft/TypeScript/issues/12358)\n\n```javascript\n  // in webpack.config.js\n  {\n    test: /\\.ts$/,\n    loader: 'ts-loader',\n    options: { allowTsInNodeModules: true }\n  }\n```\n\nAnd in your `tsconfig.json`:\n\n```json\n  {\n    \"include\": [\n      \"node_modules/whitelisted_module.ts\"\n    ],\n    \"files\": [\n      \"node_modules/my_module/whitelisted_file.ts\"\n    ]\n  }\n```\n\n#### context _(string) (default=undefined)_\n\nIf set, will parse the TypeScript configuration file with given **absolute path** as base path.\nPer default the directory of the configuration file is used as base path. Relative paths in the configuration\nfile are resolved with respect to the base path when parsed. Option `context` allows to set option\n`configFile` to a path other than the project root (e.g. a NPM package), while the base path for `ts-loader`\ncan remain the project root.\n\nKeep in mind that **not** having a `tsconfig.json` in your project root can cause different behaviour between `ts-loader` and `tsc`.\nWhen using editors like `VS Code` it is advised to add a `tsconfig.json` file to the root of the project and extend the config file\nreferenced in option `configFile`. For more information please [read the PR](https://github.com/TypeStrong/ts-loader/pull/681) that\nis the base and [read the PR](https://github.com/TypeStrong/ts-loader/pull/688) that contributed this option.\n\nwebpack:\n\n```javascript\n{\n  loader: require.resolve('ts-loader'),\n  options: {\n    context: __dirname,\n    configFile: require.resolve('ts-config-react-app')\n  }\n}\n```\n\nExtending `tsconfig.json`:\n\n```json\n{ \"extends\": \"./node_modules/ts-config-react-app/index\" }\n```\n\nNote that changes in the extending file while not be respected by `ts-loader`. Its purpose is to satisfy the code editor.\n\n#### experimentalFileCaching _(boolean) (default=true)_\n\nBy default whenever the TypeScript compiler needs to check that a file/directory exists or resolve symlinks it makes syscalls. It does not cache the result of these operations and this may result in many syscalls with the same arguments ([see comment](https://github.com/TypeStrong/ts-loader/issues/825#issue-354725524) with example).\nIn some cases it may produce performance degradation.\n\nThis flag enables caching for some FS-functions like `fileExists`, `realpath` and `directoryExists` for TypeScript compiler. Note that caches are cleared between compilations.\n\n#### projectReferences _(boolean) (default=false)_\n\n**TL;DR:** Using project references currently requires building referenced projects outside of ts-loader. We don’t want to keep it that way, but we’re releasing what we’ve got now. To try it out, you’ll need to pass `projectReferences: true` to `loaderOptions`. You’ll also probably need to use TypeScript 3.1.1 or later (which, as of this writing, means `typescript@next`).\n\nts-loader has partial support for [project references](https://www.typescriptlang.org/docs/handbook/project-references.html) in that it will _load_ dependent composite projects that are already built, but will not currently _build/rebuild_ those upstream projects. The best way to explain exactly what this means is through an example. Say you have a project with a project reference pointing to the `lib/` directory:\n\n```\ntsconfig.json\napp.ts\nlib/\n  tsconfig.json\n  niftyUtil.ts\n```\n\nAnd we’ll assume that the root `tsconfig.json` has `{ \"references\": { \"path\": \"lib\" } }`, which means that any import of a file that’s part of the `lib` sub-project is treated as a reference to another project, not just a reference to a TypeScript file. Before discussing how ts-loader handles this, it’s helpful to review at a really basic level what `tsc` itself does here. If you were to run `tsc` on this tiny example project, the build would fail with the error:\n\n```\nerror TS6305: Output file 'lib/niftyUtil.d.ts' has not been built from source file 'lib/niftyUtil.ts'.\n```\n\nUsing project references actually instructs `tsc` _not_ to build anything that’s part of another project from source, but rather to look for any `.d.ts` and `.js` files that have already been generated from a previous build. Since we’ve never built the project in `lib` before, those files don’t exist, so building the root project fails. Still just thinking about how `tsc` works, there are two options to make the build succeed: either run `tsc -p lib/tsconfig.json` _first_, or simply run `tsc --build`, which will figure out that `lib` hasn’t been built and build it first for you.\n\nOk, so how is that relevant to ts-loader? Because the best way to think about what ts-loader does with project references is that it acts like `tsc`, but _not_ like `tsc --build`. If you run ts-loader on a project that’s using project references, and any upstream project hasn’t been built, you’ll get the exact same `error TS6305` that you would get with `tsc`. If you modify a source file in an upstream project and don’t rebuild that project, `ts-loader` won’t have any idea that you’ve changed anything—it will still be looking at the output from the last time you _built_ that file.\n\n**“Hey, don’t you think that sounds kind of useless and terrible?”** Well, sort of. You can consider it a work-in-progress. It’s true that on its own, as of today, ts-loader doesn’t have everything you need to take advantage of project references in webpack. In practice, though, _consuming_ upstream projects and _building_ upstream projects are somewhat separate concerns. Building them will likely come in a future release. For background, see the [original issue](https://github.com/TypeStrong/ts-loader/issues/815).\n\n**`outDir` Windows problemo.** At the moment, composite projects built using the [`outDir` compiler option](https://www.typescriptlang.org/docs/handbook/compiler-options.html) cannot be consumed using ts-loader on Windows. If you try to, ts-loader throws a \"has not been built from source file\" error.  We don't know why yet; it's possible there's a bug in `tsc`. It's more likely there's a bug in `ts-loader`. Hopefully it's going to get solved at some point. (Hey, maybe you're the one to solve it!) Either way, we didn't want to hold back from releasing. So if you're building on Windows then avoid building `composite` projects using `outDir`.\n\n**TypeScript version compatibility.** As a final caveat, [this commit to TypeScript](https://github.com/Microsoft/TypeScript/commit/d519e3f21ec36274726c44dab25c9eb48e34953f) is necessary for the `include` or `exclude` options of a project-referenced tsconfig file to work. It should be released in TypeScript 3.1.1 according to the tags. To use an earlier version of TypeScript, referenced project configuration files must specify `files`, and not `include`.\n\n### Usage with webpack watch\n\nBecause TS will generate .js and .d.ts files, you should ignore these files, otherwise watchers may go into an infinite watch loop. For example, when using webpack, you may wish to add this to your webpack.conf.js file:\n\n```javascript\n plugins: [\n   new webpack.WatchIgnorePlugin([\n     /\\.js$/,\n     /\\.d\\.ts$/\n   ])\n ],\n```\n\nIt's worth noting that use of the `LoaderOptionsPlugin` is [only supposed to be a stopgap measure](https://webpack.js.org/plugins/loader-options-plugin/). You may want to look at removing it entirely.\n\n### Hot Module replacement\n\nTo enable `webpack-dev-server` HMR, you need to follow the official [webpack HMR guide](https://webpack.js.org/guides/hot-module-replacement/), then tweak a few config options for `ts-loader`. The required configuration is as follows:\n\n1. Set `transpileOnly` to `true` (see [transpileOnly](#transpileonly-boolean-defaultfalse) for config details and recommendations above).\n2. Inside your HMR acceptance callback function, you must re-require the module that was replaced.\n\nFor a boilerplate HMR project using React, check out the [react-hot-boilerplate example](./examples/react-hot-boilerplate/).\n\nFor a minimal HMR TypeScript setup, go to the [hot-module-replacement example](./examples/hot-module-replacement/).\n\n## Contributing\n\nThis is your TypeScript loader! We want you to help make it even better. Please feel free to contribute; see the [contributor's guide](CONTRIBUTING.md) to get started.\n\n## License\n\nMIT License\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015 TypeStrong\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/ts-loader/-/ts-loader-5.4.5.tgz#a0c1f034b017a9344cef0961bfd97cc192492b8b", "type": "tarball", "reference": "https://registry.yarnpkg.com/ts-loader/-/ts-loader-5.4.5.tgz", "hash": "a0c1f034b017a9344cef0961bfd97cc192492b8b", "integrity": "sha512-XYsjfnRQCBum9AMRZpk2rTYSVpdZBpZK+kDh0TeT3kxmQNBDVIeUjdPjY5RZry4eIAb8XHc4gYSUiUWPYvzSRw==", "registry": "npm", "packageName": "ts-loader", "cacheIntegrity": "sha512-XYsjfnRQCBum9AMRZpk2rTYSVpdZBpZK+kDh0TeT3kxmQNBDVIeUjdPjY5RZry4eIAb8XHc4gYSUiUWPYvzSRw== sha1-oMHwNLAXqTRM7wlhv9l8wZJJK4s="}, "registry": "npm", "hash": "a0c1f034b017a9344cef0961bfd97cc192492b8b"}