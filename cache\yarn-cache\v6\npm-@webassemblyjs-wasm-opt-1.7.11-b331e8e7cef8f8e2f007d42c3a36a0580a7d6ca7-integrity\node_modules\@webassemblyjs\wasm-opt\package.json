{"name": "@webassemblyjs/wasm-opt", "version": "1.7.11", "description": "", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/helper-buffer": "1.7.11", "@webassemblyjs/wasm-gen": "1.7.11", "@webassemblyjs/wasm-parser": "1.7.11"}, "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909"}