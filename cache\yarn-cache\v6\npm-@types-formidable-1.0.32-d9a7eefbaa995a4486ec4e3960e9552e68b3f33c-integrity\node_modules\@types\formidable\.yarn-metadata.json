{"manifest": {"name": "@types/formidable", "version": "1.0.32", "description": "TypeScript definitions for Formidable", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/Nemo157"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/formidable"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "65cf49d0399f3ddf9174c07da587cef04ccacdfc77f62841a5c68516491a3ce1", "typeScriptVersion": "3.3", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-formidable-1.0.32-d9a7eefbaa995a4486ec4e3960e9552e68b3f33c-integrity\\node_modules\\@types\\formidable\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/formidable`\n\n# Summary\nThis package contains type definitions for Formidable (https://github.com/felixge/node-formidable/).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/formidable.\n\n### Additional Details\n * Last updated: <PERSON><PERSON>, 29 Dec 2020 09:20:24 GMT\n * Dependencies: [@types/node](https://npmjs.com/package/@types/node)\n * Global values: none\n\n# Credits\nThese definitions were written by [<PERSON><PERSON>](https://github.com/Nemo157).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/formidable/-/formidable-1.0.32.tgz#d9a7eefbaa995a4486ec4e3960e9552e68b3f33c", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/formidable/-/formidable-1.0.32.tgz", "hash": "d9a7eefbaa995a4486ec4e3960e9552e68b3f33c", "integrity": "sha512-jOAB5+GFW+C+2xdvUcpd/CnYg2rD5xCyagJLBJU+9PB4a/DKmsAqS9yZI3j/Q9zwvM7ztPHaAIH1ijzp4cezdQ==", "registry": "npm", "packageName": "@types/formidable", "cacheIntegrity": "sha512-jOAB5+GFW+C+2xdvUcpd/CnYg2rD5xCyagJLBJU+9PB4a/DKmsAqS9yZI3j/Q9zwvM7ztPHaAIH1ijzp4cezdQ== sha1-2afu+6qZWkSG7E45YOlVLmiz8zw="}, "registry": "npm", "hash": "d9a7eefbaa995a4486ec4e3960e9552e68b3f33c"}