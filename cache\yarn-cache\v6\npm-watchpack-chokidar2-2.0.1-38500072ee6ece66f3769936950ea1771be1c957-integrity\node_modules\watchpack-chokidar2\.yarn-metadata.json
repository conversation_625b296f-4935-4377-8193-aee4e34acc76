{"manifest": {"name": "watchpack-chokidar2", "version": "2.0.1", "repository": {"type": "git", "url": "https://github.com/webpack/watchpack.git"}, "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "bugs": {"url": "https://github.com/webpack/watchpack/issues"}, "homepage": "https://github.com/webpack/watchpack", "dependencies": {"chokidar": "^2.1.8"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-watchpack-chokidar2-2.0.1-38500072ee6ece66f3769936950ea1771be1c957-integrity\\node_modules\\watchpack-chokidar2\\package.json"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/watchpack-chokidar2/-/watchpack-chokidar2-2.0.1.tgz#38500072ee6ece66f3769936950ea1771be1c957", "type": "tarball", "reference": "https://registry.yarnpkg.com/watchpack-chokidar2/-/watchpack-chokidar2-2.0.1.tgz", "hash": "38500072ee6ece66f3769936950ea1771be1c957", "integrity": "sha512-nCFfBIPKr5Sh61s4LPpy1Wtfi0HE8isJ3d2Yb5/Ppw2P2B/3eVSEBjKfN0fmHJSK14+31KwMKmcrzs2GM4P0Ww==", "registry": "npm", "packageName": "watchpack-chokidar2", "cacheIntegrity": "sha512-nCFfBIPKr5Sh61s4LPpy1Wtfi0HE8isJ3d2Yb5/Ppw2P2B/3eVSEBjKfN0fmHJSK14+31KwMKmcrzs2GM4P0Ww== sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc="}, "registry": "npm", "hash": "38500072ee6ece66f3769936950ea1771be1c957"}