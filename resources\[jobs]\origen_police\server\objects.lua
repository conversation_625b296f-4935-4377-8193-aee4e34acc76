local L0_1, L1_1, L2_1, L3_1
L0_1 = {}
L1_1 = RegisterServerEvent
L2_1 = "origen_police:server:placeobj"
function L3_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L4_2 = CreateObject
  L5_2 = GetHashKey
  L6_2 = A0_2
  L5_2 = L5_2(L6_2)
  L6_2 = A1_2
  L7_2 = true
  L8_2 = true
  L9_2 = true
  L4_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
  L5_2 = SetEntityHeading
  L6_2 = L4_2
  L7_2 = A2_2
  L5_2(L6_2, L7_2)
  L5_2 = FreezeEntityPosition
  L6_2 = L4_2
  L7_2 = A3_2
  L5_2(L6_2, L7_2)
  L5_2 = L0_1
  L6_2 = {}
  L6_2.coords = A1_2
  L6_2.model = A0_2
  L5_2[L4_2] = L6_2
end
L1_1(L2_1, L3_1)
L1_1 = RegisterServerEvent
L2_1 = "origen_police:server:placeradar"
function L3_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  L1_2 = table
  L1_2 = L1_2.insert
  L2_2 = Public
  L2_2 = L2_2.Radars
  L3_2 = A0_2
  L1_2(L2_2, L3_2)
  L1_2 = TriggerClientEvent
  L2_2 = "origen_police:client:placeradar"
  L3_2 = -1
  L4_2 = A0_2
  L1_2(L2_2, L3_2, L4_2)
  A0_2.icon = "radar"
  L1_2 = pairs
  L2_2 = CentralSuscribeds
  L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2)
  for L5_2, L6_2 in L1_2, L2_2, L3_2, L4_2 do
    L7_2 = TriggerClientEvent
    L8_2 = "origen_police:client:AddCentralMark"
    L9_2 = L5_2
    L10_2 = "Radar_"
    L11_2 = Public
    L11_2 = L11_2.Radars
    L11_2 = #L11_2
    L10_2 = L10_2 .. L11_2
    L11_2 = json
    L11_2 = L11_2.decode
    L12_2 = json
    L12_2 = L12_2.encode
    L13_2 = A0_2
    L12_2, L13_2 = L12_2(L13_2)
    L11_2, L12_2, L13_2 = L11_2(L12_2, L13_2)
    L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
  end
end
L1_1(L2_1, L3_1)
L1_1 = FW_CreateCallback
L2_1 = "origen_police:callback:rmveobj"
function L3_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2
  L4_2 = NetworkGetEntityFromNetworkId
  L5_2 = A2_2
  L4_2 = L4_2(L5_2)
  L5_2 = L0_1
  L5_2 = L5_2[L4_2]
  if L5_2 then
    L5_2 = A1_2
    L6_2 = true
    L5_2(L6_2)
    L5_2 = Citizen
    L5_2 = L5_2.Wait
    L6_2 = 1000
    L5_2(L6_2)
    L5_2 = L0_1
    L5_2 = L5_2[L4_2]
    A3_2 = L5_2.coords
    L5_2 = DeleteEntity
    L6_2 = L4_2
    L5_2(L6_2)
    L5_2 = L0_1
    L5_2[L4_2] = nil
  else
    L5_2 = nil
    L6_2 = pairs
    L7_2 = L0_1
    L6_2, L7_2, L8_2, L9_2 = L6_2(L7_2)
    for L10_2, L11_2 in L6_2, L7_2, L8_2, L9_2 do
      L12_2 = L11_2.model
      if "prop_cctv_pole_01a" ~= L12_2 then
        L12_2 = 2
        if L12_2 then
          goto lbl_36
        end
      end
      L12_2 = 7.17
      ::lbl_36::
      L13_2 = L11_2.coords
      L13_2 = A3_2 - L13_2
      L13_2 = #L13_2
      if L12_2 > L13_2 then
        if L5_2 then
          L13_2 = L11_2.coords
          L13_2 = A3_2 - L13_2
          L13_2 = #L13_2
          L14_2 = L0_1
          L14_2 = L14_2[L5_2]
          L14_2 = L14_2.coords
          L14_2 = A3_2 - L14_2
          L14_2 = #L14_2
          if not (L13_2 < L14_2) then
            goto lbl_57
          end
        end
        L5_2 = L10_2
      end
      ::lbl_57::
    end
    if L5_2 then
      L6_2 = A1_2
      L7_2 = true
      L6_2(L7_2)
      L6_2 = Citizen
      L6_2 = L6_2.Wait
      L7_2 = 1000
      L6_2(L7_2)
      L6_2 = L0_1
      L6_2 = L6_2[L5_2]
      A3_2 = L6_2.coords
      L6_2 = DeleteEntity
      L7_2 = L5_2
      L6_2(L7_2)
      L6_2 = L0_1
      L6_2[L5_2] = nil
    else
      L6_2 = A1_2
      L7_2 = false
      L6_2(L7_2)
    end
  end
  L5_2 = 1
  L6_2 = Public
  L6_2 = L6_2.Radars
  L6_2 = #L6_2
  L7_2 = 1
  for L8_2 = L5_2, L6_2, L7_2 do
    L9_2 = Public
    L9_2 = L9_2.Radars
    L9_2 = L9_2[L8_2]
    L9_2 = L9_2.objectCoords
    L9_2 = A3_2 - L9_2
    L9_2 = #L9_2
    if L9_2 < 1 then
      L9_2 = table
      L9_2 = L9_2.remove
      L10_2 = Public
      L10_2 = L10_2.Radars
      L11_2 = L8_2
      L9_2(L10_2, L11_2)
      L9_2 = TriggerClientEvent
      L10_2 = "origen_police:client:removeradar"
      L11_2 = -1
      L12_2 = L8_2
      L9_2(L10_2, L11_2, L12_2)
      L9_2 = pairs
      L10_2 = CentralSuscribeds
      L9_2, L10_2, L11_2, L12_2 = L9_2(L10_2)
      for L13_2, L14_2 in L9_2, L10_2, L11_2, L12_2 do
        L15_2 = TriggerClientEvent
        L16_2 = "origen_police:client:RemoveCentralMark"
        L17_2 = L13_2
        L18_2 = "Radar_"
        L19_2 = L8_2
        L18_2 = L18_2 .. L19_2
        L15_2(L16_2, L17_2, L18_2)
      end
      break
    end
  end
end
L1_1(L2_1, L3_1)
