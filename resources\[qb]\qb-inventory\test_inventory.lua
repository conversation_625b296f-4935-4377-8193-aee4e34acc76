-- Test script pro qb-inventory
-- Tento soubor s<PERSON> pouze pro testování a měl by b<PERSON><PERSON> <PERSON> po opravě

RegisterCommand('testinv', function(source, args, rawCommand)
    local src = source
    print("=== TESTING INVENTORY FUNCTIONS ===")
    
    -- Test GetPlayer
    local player = GetPlayer(src)
    if player then
        print("✓ GetPlayer: SUCCESS - Player found")
    else
        print("✗ GetPlayer: FAILED - Player not found")
        return
    end
    
    -- Test GetIdentifier
    local identifier = GetIdentifier(src)
    if identifier then
        print("✓ GetIdentifier: SUCCESS - " .. tostring(identifier))
    else
        print("✗ GetIdentifier: FAILED - Identifier not found")
        return
    end
    
    -- Test GetPlayerName
    local playerName = GetPlayerName(src)
    if playerName then
        print("✓ GetPlayerName: SUCCESS - " .. tostring(playerName))
    else
        print("✗ GetPlayerName: FAILED - Name not found")
    end
    
    -- Test GetName
    local name = GetName(src)
    if name then
        print("✓ GetName: SUCCESS - " .. tostring(name))
    else
        print("✗ GetName: FAILED - Name not found")
    end
    
    -- Test SaveInventory
    print("Testing SaveInventory...")
    SaveInventory(src, player.PlayerData, false)
    print("✓ SaveInventory: Completed (check for errors above)")
    
    print("=== TEST COMPLETED ===")
end, true)

RegisterCommand('loadinv', function(source, args, rawCommand)
    local src = source
    local identifier = GetIdentifier(src)
    if identifier then
        print("Loading inventory for: " .. identifier)
        local inventory = LoadInventory(src, identifier)
        print("Loaded " .. (inventory and #inventory or 0) .. " items")
    else
        print("Could not get identifier for player")
    end
end, true)
