-- Test script pro qb-inventory
-- Tento soubor slou<PERSON> pouze pro testování a měl by b<PERSON><PERSON> s<PERSON> po opravě

RegisterCommand('testinv', function(source, args, rawCommand)
    local src = source
    print("=== TESTING INVENTORY FUNCTIONS ===")

    -- Test Core initialization
    if not CoreReady then
        print("✗ Core: NOT READY - Core is not initialized")
        return
    else
        print("✓ Core: READY - " .. tostring(CoreName))
    end

    -- Test GetPlayer
    local player = GetPlayer(src)
    if player then
        print("✓ GetPlayer: SUCCESS - Player found")
        print("  - Player source: " .. tostring(player.PlayerData.source))
        print("  - Player citizenid: " .. tostring(player.PlayerData.citizenid))
    else
        print("✗ GetPlayer: FAILED - Player not found")
        return
    end

    -- Test GetIdentifier
    local identifier = GetIdentifier(src)
    if identifier then
        print("✓ GetIdentifier: SUCCESS - " .. tostring(identifier))
    else
        print("✗ GetIdentifier: FAILED - Identifier not found")
        return
    end

    -- Test GetPlayerName
    local playerName = GetPlayerName(src)
    if playerName then
        print("✓ GetPlayerName: SUCCESS - " .. tostring(playerName))
    else
        print("✗ GetPlayerName: FAILED - Name not found")
    end

    -- Test GetName
    local name = GetName(src)
    if name then
        print("✓ GetName: SUCCESS - " .. tostring(name))
    else
        print("✗ GetName: FAILED - Name not found")
    end

    -- Test GetInventory
    local inventory = GetInventory(src)
    if inventory then
        print("✓ GetInventory: SUCCESS - Found " .. tostring(#inventory) .. " items")
    else
        print("✗ GetInventory: FAILED - No inventory found")
    end

    -- Test SaveInventory
    print("Testing SaveInventory...")
    SaveInventory(src, player.PlayerData, false)
    print("✓ SaveInventory: Completed (check for errors above)")

    print("=== TEST COMPLETED ===")
end, true)

RegisterCommand('loadinv', function(source, args, rawCommand)
    local src = source
    local identifier = GetIdentifier(src)
    if identifier then
        print("Loading inventory for: " .. identifier)
        local inventory = LoadInventory(src, identifier)
        if inventory then
            local itemCount = 0
            for _ in pairs(inventory) do
                itemCount = itemCount + 1
            end
            print("Loaded " .. itemCount .. " items")
        else
            print("No inventory data found")
        end
    else
        print("Could not get identifier for player")
    end
end, true)

RegisterCommand('testcore', function(source, args, rawCommand)
    print("=== CORE STATUS ===")
    print("CoreReady: " .. tostring(CoreReady))
    print("CoreName: " .. tostring(CoreName))
    print("Core exists: " .. tostring(Core ~= nil))
    if Core then
        print("Core.Functions exists: " .. tostring(Core.Functions ~= nil))
        if Core.Functions then
            print("Core.Functions.GetPlayer exists: " .. tostring(Core.Functions.GetPlayer ~= nil))
        end
    end
end, true)
