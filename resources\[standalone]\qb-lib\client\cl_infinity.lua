function IsPlayerActive(pServerId)
    return exports['qb-infinity']:IsPlayerActive(pServerId)
end

function DoesPlayerExist(pServerId)
    return exports['qb-infinity']:DoesPlayerExist(pServerId)
end

function GetPlayerCoords(pServerId)
    return exports['qb-infinity']:GetPlayerCoords(pServerId)
end

function GetNetworkedCoords(pType, pNetId)
    return exports['qb-infinity']:GetNetworkedCoords(pType, pNetId)
end

function GetLocalEntity(pType, pNetId)
    return exports['qb-infinity']:GetLocalEntity(pType, pNetId)
end