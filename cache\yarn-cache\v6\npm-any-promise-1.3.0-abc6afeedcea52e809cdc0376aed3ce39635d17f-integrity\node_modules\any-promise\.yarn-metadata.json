{"manifest": {"name": "any-promise", "version": "1.3.0", "description": "Resolve any installed ES6 compatible promise", "main": "index.js", "typings": "index.d.ts", "browser": {"./register.js": "./register-shim.js"}, "scripts": {"test": "ava"}, "repository": {"type": "git", "url": "https://github.com/kevinbeaty/any-promise"}, "keywords": ["promise", "es6"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/kevinbeaty/any-promise/issues"}, "homepage": "http://github.com/kevinbeaty/any-promise", "dependencies": {}, "devDependencies": {"ava": "^0.14.0", "bluebird": "^3.0.0", "es6-promise": "^3.0.0", "is-promise": "^2.0.0", "lie": "^3.0.0", "mocha": "^2.0.0", "native-promise-only": "^0.8.0", "phantomjs-prebuilt": "^2.0.0", "pinkie": "^2.0.0", "promise": "^7.0.0", "q": "^1.0.0", "rsvp": "^3.0.0", "vow": "^0.4.0", "when": "^3.0.0", "zuul": "^3.0.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-any-promise-1.3.0-abc6afeedcea52e809cdc0376aed3ce39635d17f-integrity\\node_modules\\any-promise\\package.json", "readmeFilename": "README.md", "readme": "## Any Promise\n\n[![Build Status](https://secure.travis-ci.org/kevinbeaty/any-promise.svg)](http://travis-ci.org/kevinbeaty/any-promise)\n\nLet your library support any ES 2015 (ES6) compatible `Promise` and leave the choice to application authors. The application can *optionally* register its preferred `Promise` implementation and it will be exported when requiring `any-promise` from library code.\n\nIf no preference is registered, defaults to the global `Promise` for newer Node.js versions. The browser version defaults to the window `Promise`, so polyfill or register as necessary.\n\n### Usage with global Promise:\n\nAssuming the global `Promise` is the desired implementation:\n\n```bash\n# Install any libraries depending on any-promise\n$ npm install mz\n```\n\nThe installed libraries will use global Promise by default.\n\n```js\n// in library\nvar Promise = require('any-promise')  // the global Promise\n\nfunction promiseReturningFunction(){\n    return new Promise(function(resolve, reject){...})\n}\n```\n\n### Usage with registration:\n\nAssuming `bluebird` is the desired Promise implementation:\n\n```bash\n# Install preferred promise library\n$ npm install bluebird\n# Install any-promise to allow registration\n$ npm install any-promise\n# Install any libraries you would like to use depending on any-promise\n$ npm install mz\n```\n\nRegister your preference in the application entry point before any other `require` of packages that load `any-promise`:\n\n```javascript\n// top of application index.js or other entry point\nrequire('any-promise/register/bluebird')\n\n// -or- Equivalent to above, but allows customization of Promise library\nrequire('any-promise/register')('bluebird', {Promise: require('bluebird')})\n```\n\nNow that the implementation is registered, you can use any package depending on `any-promise`:\n\n\n```javascript\nvar fsp = require('mz/fs') // mz/fs will use registered bluebird promises\nvar Promise = require('any-promise')  // the registered bluebird promise \n```\n\nIt is safe to call `register` multiple times, but it must always be with the same implementation.\n\nAgain, registration is *optional*. It should only be called by the application user if overriding the global `Promise` implementation is desired.\n\n### Optional Application Registration\n\nAs an application author, you can *optionally* register a preferred `Promise` implementation on application startup (before any call to `require('any-promise')`:\n\nYou must register your preference before any call to `require('any-promise')` (by you or required packages), and only one implementation can be registered. Typically, this registration would occur at the top of the application entry point.\n\n\n#### Registration shortcuts\n\nIf you are using a known `Promise` implementation, you can register your preference with a shortcut:\n\n\n```js\nrequire('any-promise/register/bluebird')\n// -or-\nimport 'any-promise/register/q';\n```\n\nShortcut registration is the preferred registration method as it works in the browser and Node.js. It is also convenient for using with `import` and many test runners, that offer a `--require` flag:\n\n```\n$ ava --require=any-promise/register/bluebird test.js\n```\n\nCurrent known implementations include `bluebird`, `q`, `when`, `rsvp`, `es6-promise`, `promise`, `native-promise-only`, `pinkie`, `vow` and `lie`. If you are not using a known implementation, you can use another registration method described below.\n\n\n#### Basic Registration\n\nAs an alternative to registration shortcuts, you can call the `register` function with the preferred `Promise` implementation. The benefit of this approach is that a `Promise` library can be required by name without being a known implementation.  This approach does NOT work in the browser. To use `any-promise` in the browser use either registration shortcuts or specify the `Promise` constructor using advanced registration (see below).\n\n```javascript\nrequire('any-promise/register')('when')\n// -or- require('any-promise/register')('any other ES6 compatible library (known or otherwise)')\n```\n\nThis registration method will try to detect the `Promise` constructor from requiring the specified implementation.  If you would like to specify your own constructor, see advanced registration.\n\n\n#### Advanced Registration\n\nTo use the browser version, you should either install a polyfill or explicitly register the `Promise` constructor:\n\n```javascript\nrequire('any-promise/register')('bluebird', {Promise: require('bluebird')})\n```\n\nThis could also be used for registering a custom `Promise` implementation or subclass.\n\nYour preference will be registered globally, allowing a single registration even if multiple versions of `any-promise` are installed in the NPM dependency tree or are using multiple bundled JavaScript files in the browser. You can bypass this global registration in options:\n\n\n```javascript\nrequire('../register')('es6-promise', {Promise: require('es6-promise').Promise, global: false})\n```\n\n### Library Usage\n\nTo use any `Promise` constructor, simply require it:\n\n```javascript\nvar Promise = require('any-promise');\n\nreturn Promise\n  .all([xf, f, init, coll])\n  .then(fn);\n\n\nreturn new Promise(function(resolve, reject){\n  try {\n    resolve(item);\n  } catch(e){\n    reject(e);\n  }\n});\n\n```\n\nExcept noted below, libraries using `any-promise` should only use [documented](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) functions as there is no guarantee which implementation will be chosen by the application author.  Libraries should never call `register`, only the application user should call if desired.\n\n\n#### Advanced Library Usage\n\nIf your library needs to branch code based on the registered implementation, you can retrieve it using `var impl = require('any-promise/implementation')`, where `impl` will be the package name (`\"bluebird\"`, `\"when\"`, etc.) if registered, `\"global.Promise\"` if using the global version on Node.js, or `\"window.Promise\"` if using the browser version. You should always include a default case, as there is no guarantee what package may be registered.\n\n\n### Support for old Node.js versions\n\nNode.js versions prior to `v0.12` may have contained buggy versions of the global `Promise`. For this reason, the global `Promise` is not loaded automatically for these old versions.  If using `any-promise` in Node.js versions versions `<= v0.12`, the user should register a desired implementation.\n\nIf an implementation is not registered, `any-promise` will attempt to discover an installed `Promise` implementation.  If no implementation can be found, an error will be thrown on `require('any-promise')`.  While the auto-discovery usually avoids errors, it is non-deterministic. It is recommended that the user always register a preferred implementation for older Node.js versions.\n\nThis auto-discovery is only available for Node.jS versions prior to `v0.12`. Any newer versions will always default to the global `Promise` implementation.\n\n### Related\n\n- [any-observable](https://github.com/sindresorhus/any-observable) - `any-promise` for Observables.\n\n", "licenseText": "Copyright (C) 2014-2016 <PERSON>\n\nPer<PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/any-promise/-/any-promise-1.3.0.tgz#abc6afeedcea52e809cdc0376aed3ce39635d17f", "type": "tarball", "reference": "https://registry.yarnpkg.com/any-promise/-/any-promise-1.3.0.tgz", "hash": "abc6afeedcea52e809cdc0376aed3ce39635d17f", "integrity": "sha1-q8av7tzqUugJzcA3au0845Y10X8=", "registry": "npm", "packageName": "any-promise", "cacheIntegrity": "sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A== sha1-q8av7tzqUugJzcA3au0845Y10X8="}, "registry": "npm", "hash": "abc6afeedcea52e809cdc0376aed3ce39635d17f"}