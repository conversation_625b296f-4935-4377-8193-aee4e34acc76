{"manifest": {"name": "buffer-xor", "version": "1.0.3", "description": "A simple module for bitwise-xor on buffers", "main": "index.js", "scripts": {"standard": "standard", "test": "npm run-script unit", "unit": "mocha"}, "repository": {"type": "git", "url": "https://github.com/crypto-browserify/buffer-xor.git"}, "bugs": {"url": "https://github.com/crypto-browserify/buffer-xor/issues"}, "homepage": "https://github.com/crypto-browserify/buffer-xor", "keywords": ["bits", "bitwise", "buffer", "buffer-xor", "crypto", "inline", "math", "memory", "performance", "xor"], "author": {"name": "<PERSON>"}, "license": "MIT", "devDependencies": {"mocha": "*", "standard": "*"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-buffer-xor-1.0.3-26e61ed1422fb70dd42e6e36729ed51d855fe8d9-integrity\\node_modules\\buffer-xor\\package.json", "readmeFilename": "README.md", "readme": "# buffer-xor\n\n[![TRAVIS](https://secure.travis-ci.org/crypto-browserify/buffer-xor.png)](http://travis-ci.org/crypto-browserify/buffer-xor)\n[![NPM](http://img.shields.io/npm/v/buffer-xor.svg)](https://www.npmjs.org/package/buffer-xor)\n\n[![js-standard-style](https://cdn.rawgit.com/feross/standard/master/badge.svg)](https://github.com/feross/standard)\n\nA simple module for bitwise-xor on buffers.\n\n\n## Examples\n\n``` javascript\nvar xor = require(\"buffer-xor\")\nvar a = new Buffer('00ff0f', 'hex')\nvar b = new Buffer('f0f0', 'hex')\n\nconsole.log(xor(a, b))\n// => <Buffer f0 0f>\n```\n\n\nOr for those seeking those few extra cycles, perform the operation in place:\n\n``` javascript\nvar xorInplace = require(\"buffer-xor/inplace\")\nvar a = new Buffer('00ff0f', 'hex')\nvar b = new Buffer('f0f0', 'hex')\n\nconsole.log(xorInplace(a, b))\n// => <Buffer f0 0f>\n// NOTE: xorInplace will return the shorter slice of its parameters\n\n// See that a has been mutated\nconsole.log(a)\n// => <Buffer f0 0f 0f>\n```\n\n\n## License [MIT](LICENSE)\n\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/buffer-xor/-/buffer-xor-1.0.3.tgz#26e61ed1422fb70dd42e6e36729ed51d855fe8d9", "type": "tarball", "reference": "https://registry.yarnpkg.com/buffer-xor/-/buffer-xor-1.0.3.tgz", "hash": "26e61ed1422fb70dd42e6e36729ed51d855fe8d9", "integrity": "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=", "registry": "npm", "packageName": "buffer-xor", "cacheIntegrity": "sha512-571s0T7nZWK6vB67HI5dyUF7wXiNcfaPPPTl6zYCNApANjIvYJTg7hlud/+cJpdAhS7dVzqMLmfhfHR3rAcOjQ== sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk="}, "registry": "npm", "hash": "26e61ed1422fb70dd42e6e36729ed51d855fe8d9"}