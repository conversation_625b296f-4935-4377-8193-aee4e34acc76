{"manifest": {"name": "prr", "description": "A better Object.defineProperty()", "version": "1.0.1", "homepage": "https://github.com/rvagg/prr", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/rvagg"}, "keywords": ["property", "properties", "defineProperty", "ender"], "main": "./prr.js", "repository": {"type": "git", "url": "https://github.com/rvagg/prr.git"}, "dependencies": {}, "devDependencies": {"tap": "*"}, "scripts": {"test": "node ./test.js"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-prr-1.0.1-d3fc114ba06995a45ec6893f484ceb1d78f5f476-integrity\\node_modules\\prr\\package.json", "readmeFilename": "README.md", "readme": "# prr [![Build Status](https://secure.travis-ci.org/rvagg/prr.png)](http://travis-ci.org/rvagg/prr)\n\nAn sensible alternative to `Object.defineProperty()`. Available in npm and Ender as **prr**.\n\n## Usage\n\nSet the property `'foo'` (`obj.foo`) to have the value `'bar'` with default options (`'enumerable'`, `'configurable'` and `'writable'` are all `false`):\n\n```js\nprr(obj, 'foo', 'bar')\n```\n\nAdjust the default options:\n\n```js\nprr(obj, 'foo', 'bar', { enumerable: true, writable: true })\n```\n\nDo the same operation for multiple properties:\n\n```js\nprr(obj, { one: 'one', two: 'two' })\n// or with options:\nprr(obj, { one: 'one', two: 'two' }, { enumerable: true, writable: true })\n```\n\n### Simplify!\n\nBut obviously, having to write out the full options object makes it nearly as bad as the original `Object.defineProperty()` so we can simplify.\n\nAs an alternative method we can use an options string where each character represents a option: `'e'=='enumerable'`, `'c'=='configurable'` and `'w'=='writable'`:\n\n```js\nprr(obj, 'foo', 'bar', 'ew') // enumerable and writable but not configurable\n// muliple properties:\nprr(obj, { one: 'one', two: 'two' }, 'ewc') // configurable too\n```\n\n## Where can I use it?\n\nAnywhere! For pre-ES5 environments *prr* will simply fall-back to an `object[property] = value` so you can get close to what you want.\n\n*prr* is Ender-compatible so you can include it in your Ender build and `$.prr(...)` or `var prr = require('prr'); prr(...)`.\n\n## Licence\n\nprr is Copyright (c) 2013 Rod Vagg [@rvagg](https://twitter.com/rvagg) and licensed under the MIT licence. All rights not explicitly granted in the MIT license are reserved. See the included LICENSE.md file for more details.\n", "licenseText": "The MIT License (MIT)\n=====================\n\nCopyright (c) 2014 Rod Vagg\n---------------------------\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/prr/-/prr-1.0.1.tgz#d3fc114ba06995a45ec6893f484ceb1d78f5f476", "type": "tarball", "reference": "https://registry.yarnpkg.com/prr/-/prr-1.0.1.tgz", "hash": "d3fc114ba06995a45ec6893f484ceb1d78f5f476", "integrity": "sha1-0/wRS6BplaRexok/SEzrHXj19HY=", "registry": "npm", "packageName": "prr", "cacheIntegrity": "sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw== sha1-0/wRS6BplaRexok/SEzrHXj19HY="}, "registry": "npm", "hash": "d3fc114ba06995a45ec6893f484ceb1d78f5f476"}