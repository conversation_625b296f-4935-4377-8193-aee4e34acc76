{"manifest": {"name": "querystring-es3", "id": "querystring-es3", "version": "0.2.1", "description": "Node's querystring module for all engines. (ES3 compat fork)", "keywords": ["commonjs", "query", "querystring"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/mike-spainhower/querystring.git", "web": "https://github.com/mike-spainhower/querystring"}, "bugs": {"url": "http://github.com/mike-spainhower/querystring/issues/"}, "devDependencies": {"test": "~0.x.0", "phantomify": "~0.x.0", "retape": "~0.x.0", "tape": "~0.1.5"}, "engines": {"node": ">=0.4.x"}, "scripts": {"test": "npm run test-node && npm run test-browser && npm run test-tap", "test-browser": "node ./node_modules/phantomify/bin/cmd.js ./test/common-index.js", "test-node": "node ./test/common-index.js", "test-tap": "node ./test/tap-index.js"}, "testling": {"files": "test/tap-index.js", "browsers": {"iexplore": [9, 10], "chrome": [16, 20, 25, "canary"], "firefox": [10, 15, 16, 17, 18, "nightly"], "safari": [5, 6], "opera": [12]}}, "licenses": [{"type": "MIT", "url": "https://github.com/Gozala/enchain/License.md"}], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-querystring-es3-0.2.1-9ec61f79049875707d69414596fd907a4d711e73-integrity\\node_modules\\querystring-es3\\package.json", "readmeFilename": "Readme.md", "readme": "# querystring\n\n[![Build Status](https://secure.travis-ci.org/mike-spainhower/querystring.png)](http://travis-ci.org/mike-spainhower/querystring)\n\n\n[![Browser support](http://ci.testling.com/mike-spainhower/querystring.png)](http://ci.testling.com/mike-spainhower/querystring)\n\n\n\nNode's querystring module for all engines.\n\n## Install ##\n\n    npm install querystring\n\n", "license": "MIT", "licenseText": "\nCopyright 2012 <PERSON><PERSON><PERSON>. All rights reserved.\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/querystring-es3/-/querystring-es3-0.2.1.tgz#9ec61f79049875707d69414596fd907a4d711e73", "type": "tarball", "reference": "https://registry.yarnpkg.com/querystring-es3/-/querystring-es3-0.2.1.tgz", "hash": "9ec61f79049875707d69414596fd907a4d711e73", "integrity": "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=", "registry": "npm", "packageName": "querystring-es3", "cacheIntegrity": "sha512-773xhDQnZBMFobEiztv8LIl70ch5MSF/jUQVlhwFyBILqq96anmoctVIYz+ZRp0qbCKATTn6ev02M3r7Ga5vqA== sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM="}, "registry": "npm", "hash": "9ec61f79049875707d69414596fd907a4d711e73"}