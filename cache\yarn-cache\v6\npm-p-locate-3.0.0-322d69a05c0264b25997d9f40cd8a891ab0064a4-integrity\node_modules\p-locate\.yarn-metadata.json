{"manifest": {"name": "p-locate", "version": "3.0.0", "description": "Get the first fulfilled promise that satisfies the provided testing function", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/p-locate.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "locate", "find", "finder", "search", "searcher", "test", "array", "collection", "iterable", "iterator", "race", "fulfilled", "fastest", "async", "await", "promises", "bluebird"], "dependencies": {"p-limit": "^2.0.0"}, "devDependencies": {"ava": "*", "delay": "^3.0.0", "in-range": "^1.0.0", "time-span": "^2.0.0", "xo": "*"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-p-locate-3.0.0-322d69a05c0264b25997d9f40cd8a891ab0064a4-integrity\\node_modules\\p-locate\\package.json", "readmeFilename": "readme.md", "readme": "# p-locate [![Build Status](https://travis-ci.org/sindresorhus/p-locate.svg?branch=master)](https://travis-ci.org/sindresorhus/p-locate)\n\n> Get the first fulfilled promise that satisfies the provided testing function\n\nThink of it like an async version of [`Array#find`](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Array/find).\n\n\n## Install\n\n```\n$ npm install p-locate\n```\n\n\n## Usage\n\nHere we find the first file that exists on disk, in array order.\n\n```js\nconst pathExists = require('path-exists');\nconst pLocate = require('p-locate');\n\nconst files = [\n\t'unicorn.png',\n\t'rainbow.png', // Only this one actually exists on disk\n\t'pony.png'\n];\n\n(async () => {\n\tconst foundPath = await pLocate(files, file => pathExists(file));\n\n\tconsole.log(foundPath);\n\t//=> 'rainbow'\n})();\n```\n\n*The above is just an example. Use [`locate-path`](https://github.com/sindresorhus/locate-path) if you need this.*\n\n\n## API\n\n### pLocate(input, tester, [options])\n\nReturns a `Promise` that is fulfilled when `tester` resolves to `true` or the iterable is done, or rejects if any of the promises reject. The fulfilled value is the current iterable value or `undefined` if `tester` never resolved to `true`.\n\n#### input\n\nType: `Iterable<Promise|any>`\n\n#### tester(element)\n\nType: `Function`\n\nExpected to return a `Promise<boolean>` or boolean.\n\n#### options\n\nType: `Object`\n\n##### concurrency\n\nType: `number`<br>\nDefault: `Infinity`<br>\nMinimum: `1`\n\nNumber of concurrently pending promises returned by `tester`.\n\n##### preserveOrder\n\nType: `boolean`<br>\nDefault: `true`\n\nPreserve `input` order when searching.\n\nDisable this to improve performance if you don't care about the order.\n\n\n## Related\n\n- [p-map](https://github.com/sindresorhus/p-map) - Map over promises concurrently\n- [p-filter](https://github.com/sindresorhus/p-filter) - Filter promises concurrently\n- [p-any](https://github.com/sindresorhus/p-any) - Wait for any promise to be fulfilled\n- [More…](https://github.com/sindresorhus/promise-fun)\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com)\n", "licenseText": "MIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/p-locate/-/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4", "type": "tarball", "reference": "https://registry.yarnpkg.com/p-locate/-/p-locate-3.0.0.tgz", "hash": "322d69a05c0264b25997d9f40cd8a891ab0064a4", "integrity": "sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==", "registry": "npm", "packageName": "p-locate", "cacheIntegrity": "sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ== sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ="}, "registry": "npm", "hash": "322d69a05c0264b25997d9f40cd8a891ab0064a4"}