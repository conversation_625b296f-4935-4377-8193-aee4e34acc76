{"manifest": {"name": "delegates", "version": "1.0.0", "repository": {"type": "git", "url": "https://github.com/visionmedia/node-delegates.git"}, "description": "delegate methods and accessors to another property", "keywords": ["delegate", "delegation"], "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-delegates-1.0.0-84c6e159b81904fdca59a0ef44cd870d31250f9a-integrity\\node_modules\\delegates\\package.json", "readmeFilename": "Readme.md", "readme": "\n# delegates\n\n  Node method and accessor delegation utilty.\n\n## Installation\n\n```\n$ npm install delegates\n```\n\n## Example\n\n```js\nvar delegate = require('delegates');\n\n...\n\ndelegate(proto, 'request')\n  .method('acceptsLanguages')\n  .method('acceptsEncodings')\n  .method('acceptsCharsets')\n  .method('accepts')\n  .method('is')\n  .access('querystring')\n  .access('idempotent')\n  .access('socket')\n  .access('length')\n  .access('query')\n  .access('search')\n  .access('status')\n  .access('method')\n  .access('path')\n  .access('body')\n  .access('host')\n  .access('url')\n  .getter('subdomains')\n  .getter('protocol')\n  .getter('header')\n  .getter('stale')\n  .getter('fresh')\n  .getter('secure')\n  .getter('ips')\n  .getter('ip')\n```\n\n# API\n\n## Delegate(proto, prop)\n\nCreates a delegator instance used to configure using the `prop` on the given\n`proto` object. (which is usually a prototype)\n\n## Delegate#method(name)\n\nAllows the given method `name` to be accessed on the host.\n\n## Delegate#getter(name)\n\nCreates a \"getter\" for the property with the given `name` on the delegated\nobject.\n\n## Delegate#setter(name)\n\nCreates a \"setter\" for the property with the given `name` on the delegated\nobject.\n\n## Delegate#access(name)\n\nCreates an \"accessor\" (ie: both getter *and* setter) for the property with the\ngiven `name` on the delegated object.\n\n## Delegate#fluent(name)\n\nA unique type of \"accessor\" that works for a \"fluent\" API. When called as a\ngetter, the method returns the expected value. However, if the method is called\nwith a value, it will return itself so it can be chained. For example:\n\n```js\ndelegate(proto, 'request')\n  .fluent('query')\n\n// getter\nvar q = request.query();\n\n// setter (chainable)\nrequest\n  .query({ a: 1 })\n  .query({ b: 2 });\n```\n\n# License\n\n  MIT\n", "licenseText": "Copyright (c) 2015 <PERSON><PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/delegates/-/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a", "type": "tarball", "reference": "https://registry.yarnpkg.com/delegates/-/delegates-1.0.0.tgz", "hash": "84c6e159b81904fdca59a0ef44cd870d31250f9a", "integrity": "sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=", "registry": "npm", "packageName": "delegates", "cacheIntegrity": "sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ== sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o="}, "registry": "npm", "hash": "84c6e159b81904fdca59a0ef44cd870d31250f9a"}