@font-face {
    font-family: 'Satoshi-Bold';
    src: url('./assets/fonts/Satoshi-Bold.otf') format('opentype');
}

@font-face {
    font-family: 'Satoshi-Medium';
    src: url('./assets/fonts/Satoshi-Medium.otf') format('opentype');
}

@font-face {
    font-family: 'Satoshi-Regular';
    src: url('./assets/fonts/Satoshi-Regular.otf') format('opentype');
}

@font-face {
    font-family: 'Satoshi-Light';
    src: url('./assets/fonts/Satoshi-Light.otf') format('opentype');
}

body,
html {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    user-select: none;
}

*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    user-select: none;
    scroll-behavior: smooth;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

::-webkit-scrollbar {
    width: .2604vw;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(22, 22, 22, 0.9);
    border-radius: .2604vw;
}

::-webkit-scrollbar-track {
    background-color: rgba(22, 22, 22, 0.4);
}

input[type="date"]::-webkit-inner-spin-button,
input[type="date"]::-webkit-calendar-picker-indicator {
    display: none;
    -webkit-appearance: none;
}

.animation-left-out {
    animation: left-out 1s ease-in-out forwards;
}

.animation-right-out {
    animation: right-out 1s ease-in-out forwards;
}

.animation-top-out {
    animation: top-out 1s ease-in-out forwards;
}

.notify-out {
    animation: notify-out .3s ease-in-out forwards;
}

.animation-bottom-out {
    animation: bottom-out 1s ease-in-out forwards;
}

.animation-top-all-out {
    animation: top-all-out 1s ease-in-out forwards;
}

.animation-bottom-all-out {
    animation: bottom-all-out 1s ease-in-out forwards;
}

.animation-fade-out {
    animation: fade-out 1s ease-in-out forwards;
}

.animation-zoom-out {
    animation: zoom-out .5s ease-in-out forwards;
}

.animation-left-in {
    animation: left-in 0.5s ease-in-out forwards;
}

.animation-right-in {
    animation: right-in 0.5s ease-in-out forwards;
}

.animation-top-in {
    animation: top-in 1s ease-in-out forwards;
}

.notify-in {
    animation: notify-in .3s ease-in-out forwards;
}

.animation-bottom-in {
    animation: bottom-in 1s ease-in-out forwards;
}

.animation-fade-in {
    animation: fade-in .5s ease-in-out forwards;
}

.animation-zoom-in {
    animation: zoom-in .5s ease-in-out forwards;
}

@keyframes left-out {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-100vw);
    }
}

@keyframes right-out {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(100vw);
    }
}

@keyframes top-out {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-100vh);
    }
}

@keyframes bottom-out {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(100vh);
    }
}

@keyframes top-all-out {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-100%);
    }
}

@keyframes bottom-all-out {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(100%);
    }
}

@keyframes fade-out {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

@keyframes zoom-out {
    0% {
        opacity: 1;
        transform: translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateY(-1.7708vw);
    }
}

@keyframes left-in {
    0% {
        transform: translateX(-50vw);
    }
    100% {
        transform: translateX(0);
    }
}

@keyframes right-in {
    0% {
        transform: translateX(50vw);
    }
    100% {
        transform: translateX(0);
    }
}

@keyframes top-in {
    0% {
        transform: translateY(-100vh);
    }
    100% {
        transform: translateY(0);
    }
}

@keyframes bottom-in {
    0% {
        transform: translateY(100vh);
    }
    100% {
        transform: translateY(0);
    }
}

@keyframes fade-in {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes zoom-in {
    0% {
        opacity: 0;
        transform: translateY(-1.7708vw);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes notify-out {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-50vw);
    }
}

@keyframes notify-in {
    0% {
        transform: translateY(-50vw);
    }
    100% {
        transform: translateY(0);
    }
}

.input-label {
    opacity: 0;
    transition: .3s;
}

.input-item {
    padding-top: 0;
    transition: .3s;
}

.input-item:focus,
.input-item:not(:placeholder-shown).input-item:not(:focus) {
    padding-top: .625vw;
    transition: .3s;
}

.input-item::placeholder {
    opacity: 1;
    transition: .3s;
}
.input-item:focus::placeholder {
    opacity: 0;
    transition: .3s;
}

.input-item:focus + .input-label,
.input-item:not(:placeholder-shown).input-item:not(:focus) + .input-label {
    opacity: 1;
    transition: .3s;
}