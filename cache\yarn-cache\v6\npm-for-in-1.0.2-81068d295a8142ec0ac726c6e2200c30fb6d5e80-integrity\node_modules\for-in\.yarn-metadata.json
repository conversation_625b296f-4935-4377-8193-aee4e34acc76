{"manifest": {"name": "for-in", "description": "Iterate over the own and inherited enumerable properties of an object, and return an object with properties that evaluate to true from the callback. Exit early by returning `false`. JavaScript/Node.js", "version": "1.0.2", "homepage": "https://github.com/jonschlinkert/for-in", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "http://paulirish.com"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/for-in.git"}, "bugs": {"url": "https://github.com/jonschlinkert/for-in/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "keywords": ["for", "for-in", "for-own", "has", "has-own", "hasOwn", "in", "key", "keys", "object", "own", "value"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["arr-flatten", "collection-map", "for-own"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-for-in-1.0.2-81068d295a8142ec0ac726c6e2200c30fb6d5e80-integrity\\node_modules\\for-in\\package.json", "readmeFilename": "README.md", "readme": "# for-in [![NPM version](https://img.shields.io/npm/v/for-in.svg?style=flat)](https://www.npmjs.com/package/for-in) [![NPM monthly downloads](https://img.shields.io/npm/dm/for-in.svg?style=flat)](https://npmjs.org/package/for-in)  [![NPM total downloads](https://img.shields.io/npm/dt/for-in.svg?style=flat)](https://npmjs.org/package/for-in) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/for-in.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/for-in)\n\n> Iterate over the own and inherited enumerable properties of an object, and return an object with properties that evaluate to true from the callback. Exit early by returning `false`. JavaScript/Node.js\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save for-in\n```\n\n## Usage\n\n```js\nvar forIn = require('for-in');\n\nvar obj = {a: 'foo', b: 'bar', c: 'baz'};\nvar values = [];\nvar keys = [];\n\nforIn(obj, function (value, key, o) {\n  keys.push(key);\n  values.push(value);\n});\n\nconsole.log(keys);\n//=> ['a', 'b', 'c'];\n\nconsole.log(values);\n//=> ['foo', 'bar', 'baz'];\n```\n\n## About\n\n### Related projects\n\n* [arr-flatten](https://www.npmjs.com/package/arr-flatten): Recursively flatten an array or arrays. This is the fastest implementation of array flatten. | [homepage](https://github.com/jonschlinkert/arr-flatten \"Recursively flatten an array or arrays. This is the fastest implementation of array flatten.\")\n* [collection-map](https://www.npmjs.com/package/collection-map): Returns an array of mapped values from an array or object. | [homepage](https://github.com/jonschlinkert/collection-map \"Returns an array of mapped values from an array or object.\")\n* [for-own](https://www.npmjs.com/package/for-own): Iterate over the own enumerable properties of an object, and return an object with properties… [more](https://github.com/jonschlinkert/for-own) | [homepage](https://github.com/jonschlinkert/for-own \"Iterate over the own enumerable properties of an object, and return an object with properties that evaluate to true from the callback. Exit early by returning `false`. JavaScript/Node.js.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 16 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 2 | [paulirish](https://github.com/paulirish) |\n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.4.2, on February 28, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/for-in/-/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80", "type": "tarball", "reference": "https://registry.yarnpkg.com/for-in/-/for-in-1.0.2.tgz", "hash": "81068d295a8142ec0ac726c6e2200c30fb6d5e80", "integrity": "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=", "registry": "npm", "packageName": "for-in", "cacheIntegrity": "sha512-7EwmXrOjyL+ChxMhmG5lnW9MPt1aIeZEwKhQzoBUdTV0N3zuwWDZYVJatDvZ2OyzPUvdIAZDsCetk3coyMfcnQ== sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="}, "registry": "npm", "hash": "81068d295a8142ec0ac726c6e2200c30fb6d5e80"}