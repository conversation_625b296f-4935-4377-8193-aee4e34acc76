{"manifest": {"name": "@types/serve-static", "version": "1.13.8", "description": "TypeScript definitions for serve-static", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/urossmolnik"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LinusU"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/devanshj"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-static"}, "scripts": {}, "dependencies": {"@types/mime": "*", "@types/node": "*"}, "typesPublisherContentHash": "c80d90d1e2e13bd1f7faaa45b3d6a85a48ff07783977ae9dc956309579b066dd", "typeScriptVersion": "3.2", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-serve-static-1.13.8-851129d434433c7082148574ffec263d58309c46-integrity\\node_modules\\@types\\serve-static\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/serve-static`\n\n# Summary\nThis package contains type definitions for serve-static (https://github.com/expressjs/serve-static).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-static.\n\n### Additional Details\n * Last updated: Wed, 18 Nov 2020 00:19:43 GMT\n * Dependencies: [@types/mime](https://npmjs.com/package/@types/mime), [@types/node](https://npmjs.com/package/@types/node)\n * Global values: none\n\n# Credits\nThese definitions were written by [<PERSON><PERSON> Smolnik](https://github.com/urossmolnik), [<PERSON><PERSON>](https://github.com/LinusU), and [<PERSON><PERSON><PERSON> Jethmalani](https://github.com/devanshj).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/serve-static/-/serve-static-1.13.8.tgz#851129d434433c7082148574ffec263d58309c46", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/serve-static/-/serve-static-1.13.8.tgz", "hash": "851129d434433c7082148574ffec263d58309c46", "integrity": "sha512-MoJhSQreaVoL+/hurAZzIm8wafFR6ajiTM1m4A0kv6AGeVBl4r4pOV8bGFrjjq1sGxDTnCoF8i22o0/aE5XCyA==", "registry": "npm", "packageName": "@types/serve-static", "cacheIntegrity": "sha512-MoJhSQreaVoL+/hurAZzIm8wafFR6ajiTM1m4A0kv6AGeVBl4r4pOV8bGFrjjq1sGxDTnCoF8i22o0/aE5XCyA== sha1-hREp1DRDPHCCFIV0/+wmPVgwnEY="}, "registry": "npm", "hash": "851129d434433c7082148574ffec263d58309c46"}