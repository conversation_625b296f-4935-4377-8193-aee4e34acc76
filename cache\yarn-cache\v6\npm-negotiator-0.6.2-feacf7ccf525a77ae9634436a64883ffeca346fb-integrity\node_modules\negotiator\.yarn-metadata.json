{"manifest": {"name": "negotiator", "description": "HTTP content negotiation", "version": "0.6.2", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}], "license": "MIT", "keywords": ["http", "content negotiation", "accept", "accept-language", "accept-encoding", "accept-charset"], "repository": {"type": "git", "url": "https://github.com/jshttp/negotiator.git"}, "devDependencies": {"eslint": "5.16.0", "eslint-plugin-markdown": "1.0.0", "mocha": "6.1.4", "nyc": "14.0.0"}, "files": ["lib/", "HISTORY.md", "LICENSE", "index.js", "README.md"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-negotiator-0.6.2-feacf7ccf525a77ae9634436a64883ffeca346fb-integrity\\node_modules\\negotiator\\package.json", "readmeFilename": "README.md", "readme": "# negotiator\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nAn HTTP content negotiator for Node.js\n\n## Installation\n\n```sh\n$ npm install negotiator\n```\n\n## API\n\n```js\nvar Negotiator = require('negotiator')\n```\n\n### Accept Negotiation\n\n```js\navailableMediaTypes = ['text/html', 'text/plain', 'application/json']\n\n// The negotiator constructor receives a request object\nnegotiator = new Negotiator(request)\n\n// Let's say Accept header is 'text/html, application/*;q=0.2, image/jpeg;q=0.8'\n\nnegotiator.mediaTypes()\n// -> ['text/html', 'image/jpeg', 'application/*']\n\nnegotiator.mediaTypes(availableMediaTypes)\n// -> ['text/html', 'application/json']\n\nnegotiator.mediaType(availableMediaTypes)\n// -> 'text/html'\n```\n\nYou can check a working example at `examples/accept.js`.\n\n#### Methods\n\n##### mediaType()\n\nReturns the most preferred media type from the client.\n\n##### mediaType(availableMediaType)\n\nReturns the most preferred media type from a list of available media types.\n\n##### mediaTypes()\n\nReturns an array of preferred media types ordered by the client preference.\n\n##### mediaTypes(availableMediaTypes)\n\nReturns an array of preferred media types ordered by priority from a list of\navailable media types.\n\n### Accept-Language Negotiation\n\n```js\nnegotiator = new Negotiator(request)\n\navailableLanguages = ['en', 'es', 'fr']\n\n// Let's say Accept-Language header is 'en;q=0.8, es, pt'\n\nnegotiator.languages()\n// -> ['es', 'pt', 'en']\n\nnegotiator.languages(availableLanguages)\n// -> ['es', 'en']\n\nlanguage = negotiator.language(availableLanguages)\n// -> 'es'\n```\n\nYou can check a working example at `examples/language.js`.\n\n#### Methods\n\n##### language()\n\nReturns the most preferred language from the client.\n\n##### language(availableLanguages)\n\nReturns the most preferred language from a list of available languages.\n\n##### languages()\n\nReturns an array of preferred languages ordered by the client preference.\n\n##### languages(availableLanguages)\n\nReturns an array of preferred languages ordered by priority from a list of\navailable languages.\n\n### Accept-Charset Negotiation\n\n```js\navailableCharsets = ['utf-8', 'iso-8859-1', 'iso-8859-5']\n\nnegotiator = new Negotiator(request)\n\n// Let's say Accept-Charset header is 'utf-8, iso-8859-1;q=0.8, utf-7;q=0.2'\n\nnegotiator.charsets()\n// -> ['utf-8', 'iso-8859-1', 'utf-7']\n\nnegotiator.charsets(availableCharsets)\n// -> ['utf-8', 'iso-8859-1']\n\nnegotiator.charset(availableCharsets)\n// -> 'utf-8'\n```\n\nYou can check a working example at `examples/charset.js`.\n\n#### Methods\n\n##### charset()\n\nReturns the most preferred charset from the client.\n\n##### charset(availableCharsets)\n\nReturns the most preferred charset from a list of available charsets.\n\n##### charsets()\n\nReturns an array of preferred charsets ordered by the client preference.\n\n##### charsets(availableCharsets)\n\nReturns an array of preferred charsets ordered by priority from a list of\navailable charsets.\n\n### Accept-Encoding Negotiation\n\n```js\navailableEncodings = ['identity', 'gzip']\n\nnegotiator = new Negotiator(request)\n\n// Let's say Accept-Encoding header is 'gzip, compress;q=0.2, identity;q=0.5'\n\nnegotiator.encodings()\n// -> ['gzip', 'identity', 'compress']\n\nnegotiator.encodings(availableEncodings)\n// -> ['gzip', 'identity']\n\nnegotiator.encoding(availableEncodings)\n// -> 'gzip'\n```\n\nYou can check a working example at `examples/encoding.js`.\n\n#### Methods\n\n##### encoding()\n\nReturns the most preferred encoding from the client.\n\n##### encoding(availableEncodings)\n\nReturns the most preferred encoding from a list of available encodings.\n\n##### encodings()\n\nReturns an array of preferred encodings ordered by the client preference.\n\n##### encodings(availableEncodings)\n\nReturns an array of preferred encodings ordered by priority from a list of\navailable encodings.\n\n## See Also\n\nThe [accepts](https://npmjs.org/package/accepts#readme) module builds on\nthis module and provides an alternative interface, mime type validation,\nand more.\n\n## License\n\n[MIT](LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/negotiator.svg\n[npm-url]: https://npmjs.org/package/negotiator\n[node-version-image]: https://img.shields.io/node/v/negotiator.svg\n[node-version-url]: https://nodejs.org/en/download/\n[travis-image]: https://img.shields.io/travis/jshttp/negotiator/master.svg\n[travis-url]: https://travis-ci.org/jshttp/negotiator\n[coveralls-image]: https://img.shields.io/coveralls/jshttp/negotiator/master.svg\n[coveralls-url]: https://coveralls.io/r/jshttp/negotiator?branch=master\n[downloads-image]: https://img.shields.io/npm/dm/negotiator.svg\n[downloads-url]: https://npmjs.org/package/negotiator\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2012-2014 <PERSON>\nCopyright (c) 2012-2014 <PERSON>\nCopyright (c) 2014-2015 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR CO<PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/negotiator/-/negotiator-0.6.2.tgz#feacf7ccf525a77ae9634436a64883ffeca346fb", "type": "tarball", "reference": "https://registry.yarnpkg.com/negotiator/-/negotiator-0.6.2.tgz", "hash": "feacf7ccf525a77ae9634436a64883ffeca346fb", "integrity": "sha512-hZXc7K2e+PgeI1eDBe/10Ard4ekbfrrqG8Ep+8Jmf4JID2bNg7NvCPOZN+kfF574pFQI7mum2AUqDidoKqcTOw==", "registry": "npm", "packageName": "negotiator", "cacheIntegrity": "sha512-hZXc7K2e+PgeI1eDBe/10Ard4ekbfrrqG8Ep+8Jmf4JID2bNg7NvCPOZN+kfF574pFQI7mum2AUqDidoKqcTOw== sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs="}, "registry": "npm", "hash": "feacf7ccf525a77ae9634436a64883ffeca346fb"}