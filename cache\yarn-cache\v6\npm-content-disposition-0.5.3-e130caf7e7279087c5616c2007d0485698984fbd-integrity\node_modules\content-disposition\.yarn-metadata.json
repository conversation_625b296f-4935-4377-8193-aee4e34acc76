{"manifest": {"name": "content-disposition", "description": "Create and parse Content-Disposition header", "version": "0.5.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["content-disposition", "http", "rfc6266", "res"], "repository": {"type": "git", "url": "https://github.com/jshttp/content-disposition.git"}, "dependencies": {"safe-buffer": "5.1.2"}, "devDependencies": {"deep-equal": "1.0.1", "eslint": "5.10.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-markdown": "1.0.0-rc.1", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "istanbul": "0.4.5", "mocha": "5.2.0"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-content-disposition-0.5.3-e130caf7e7279087c5616c2007d0485698984fbd-integrity\\node_modules\\content-disposition\\package.json", "readmeFilename": "README.md", "readme": "# content-disposition\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nCreate and parse HTTP `Content-Disposition` header\n\n## Installation\n\n```sh\n$ npm install content-disposition\n```\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar contentDisposition = require('content-disposition')\n```\n\n### contentDisposition(filename, options)\n\nCreate an attachment `Content-Disposition` header value using the given file name,\nif supplied. The `filename` is optional and if no file name is desired, but you\nwant to specify `options`, set `filename` to `undefined`.\n\n<!-- eslint-disable no-undef -->\n\n```js\nres.setHeader('Content-Disposition', contentDisposition('∫ maths.pdf'))\n```\n\n**note** HTTP headers are of the ISO-8859-1 character set. If you are writing this\nheader through a means different from `setHeader` in Node.js, you'll want to specify\nthe `'binary'` encoding in Node.js.\n\n#### Options\n\n`contentDisposition` accepts these properties in the options object.\n\n##### fallback\n\nIf the `filename` option is outside ISO-8859-1, then the file name is actually\nstored in a supplemental field for clients that support Unicode file names and\na ISO-8859-1 version of the file name is automatically generated.\n\nThis specifies the ISO-8859-1 file name to override the automatic generation or\ndisables the generation all together, defaults to `true`.\n\n  - A string will specify the ISO-8859-1 file name to use in place of automatic\n    generation.\n  - `false` will disable including a ISO-8859-1 file name and only include the\n    Unicode version (unless the file name is already ISO-8859-1).\n  - `true` will enable automatic generation if the file name is outside ISO-8859-1.\n\nIf the `filename` option is ISO-8859-1 and this option is specified and has a\ndifferent value, then the `filename` option is encoded in the extended field\nand this set as the fallback field, even though they are both ISO-8859-1.\n\n##### type\n\nSpecifies the disposition type, defaults to `\"attachment\"`. This can also be\n`\"inline\"`, or any other value (all values except inline are treated like\n`attachment`, but can convey additional information if both parties agree to\nit). The type is normalized to lower-case.\n\n### contentDisposition.parse(string)\n\n<!-- eslint-disable no-undef, no-unused-vars -->\n\n```js\nvar disposition = contentDisposition.parse('attachment; filename=\"EURO rates.txt\"; filename*=UTF-8\\'\\'%e2%82%ac%20rates.txt')\n```\n\nParse a `Content-Disposition` header string. This automatically handles extended\n(\"Unicode\") parameters by decoding them and providing them under the standard\nparameter name. This will return an object with the following properties (examples\nare shown for the string `'attachment; filename=\"EURO rates.txt\"; filename*=UTF-8\\'\\'%e2%82%ac%20rates.txt'`):\n\n - `type`: The disposition type (always lower case). Example: `'attachment'`\n\n - `parameters`: An object of the parameters in the disposition (name of parameter\n   always lower case and extended versions replace non-extended versions). Example:\n   `{filename: \"€ rates.txt\"}`\n\n## Examples\n\n### Send a file for download\n\n```js\nvar contentDisposition = require('content-disposition')\nvar destroy = require('destroy')\nvar fs = require('fs')\nvar http = require('http')\nvar onFinished = require('on-finished')\n\nvar filePath = '/path/to/public/plans.pdf'\n\nhttp.createServer(function onRequest (req, res) {\n  // set headers\n  res.setHeader('Content-Type', 'application/pdf')\n  res.setHeader('Content-Disposition', contentDisposition(filePath))\n\n  // send file\n  var stream = fs.createReadStream(filePath)\n  stream.pipe(res)\n  onFinished(res, function () {\n    destroy(stream)\n  })\n})\n```\n\n## Testing\n\n```sh\n$ npm test\n```\n\n## References\n\n- [RFC 2616: Hypertext Transfer Protocol -- HTTP/1.1][rfc-2616]\n- [RFC 5987: Character Set and Language Encoding for Hypertext Transfer Protocol (HTTP) Header Field Parameters][rfc-5987]\n- [RFC 6266: Use of the Content-Disposition Header Field in the Hypertext Transfer Protocol (HTTP)][rfc-6266]\n- [Test Cases for HTTP Content-Disposition header field (RFC 6266) and the Encodings defined in RFCs 2047, 2231 and 5987][tc-2231]\n\n[rfc-2616]: https://tools.ietf.org/html/rfc2616\n[rfc-5987]: https://tools.ietf.org/html/rfc5987\n[rfc-6266]: https://tools.ietf.org/html/rfc6266\n[tc-2231]: http://greenbytes.de/tech/tc2231/\n\n## License\n\n[MIT](LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/content-disposition.svg\n[npm-url]: https://npmjs.org/package/content-disposition\n[node-version-image]: https://img.shields.io/node/v/content-disposition.svg\n[node-version-url]: https://nodejs.org/en/download\n[travis-image]: https://img.shields.io/travis/jshttp/content-disposition.svg\n[travis-url]: https://travis-ci.org/jshttp/content-disposition\n[coveralls-image]: https://img.shields.io/coveralls/jshttp/content-disposition.svg\n[coveralls-url]: https://coveralls.io/r/jshttp/content-disposition?branch=master\n[downloads-image]: https://img.shields.io/npm/dm/content-disposition.svg\n[downloads-url]: https://npmjs.org/package/content-disposition\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2014-2017 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/content-disposition/-/content-disposition-0.5.3.tgz#e130caf7e7279087c5616c2007d0485698984fbd", "type": "tarball", "reference": "https://registry.yarnpkg.com/content-disposition/-/content-disposition-0.5.3.tgz", "hash": "e130caf7e7279087c5616c2007d0485698984fbd", "integrity": "sha512-ExO0774ikEObIAEV9kDo50o+79VCUdEB6n6lzKgGwupcVeRlhrj3qGAfwq8G6uBJjkqLrhT0qEYFcWng8z1z0g==", "registry": "npm", "packageName": "content-disposition", "cacheIntegrity": "sha512-ExO0774ikEObIAEV9kDo50o+79VCUdEB6n6lzKgGwupcVeRlhrj3qGAfwq8G6uBJjkqLrhT0qEYFcWng8z1z0g== sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70="}, "registry": "npm", "hash": "e130caf7e7279087c5616c2007d0485698984fbd"}