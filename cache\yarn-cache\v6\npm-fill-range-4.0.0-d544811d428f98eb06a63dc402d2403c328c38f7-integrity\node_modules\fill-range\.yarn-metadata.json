{"manifest": {"name": "fill-range", "description": "Fill in a range of numbers or letters, optionally passing an increment or `step` to use, or create a regex-compatible range with `options.toRegex`", "version": "4.0.0", "homepage": "https://github.com/jonschlinkert/fill-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"email": "<EMAIL>", "url": "https://github.com/wtgtybhertgeghgtwtg"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "edo.rivai.nl"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "paulmillr.com"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/fill-range.git"}, "bugs": {"url": "https://github.com/jonschlinkert/fill-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^1.0.0", "gulp-format-md": "^0.1.12", "minimist": "^1.2.0", "mocha": "^3.2.0"}, "keywords": ["alpha", "alphabetical", "array", "bash", "brace", "expand", "expansion", "fill", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sh"], "verb": {"related": {"list": ["braces", "expand-range", "micromatch", "to-regex-range"]}, "toc": true, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-fill-range-4.0.0-d544811d428f98eb06a63dc402d2403c328c38f7-integrity\\node_modules\\fill-range\\package.json", "readmeFilename": "README.md", "readme": "# fill-range [![NPM version](https://img.shields.io/npm/v/fill-range.svg?style=flat)](https://www.npmjs.com/package/fill-range) [![NPM monthly downloads](https://img.shields.io/npm/dm/fill-range.svg?style=flat)](https://npmjs.org/package/fill-range)  [![NPM total downloads](https://img.shields.io/npm/dt/fill-range.svg?style=flat)](https://npmjs.org/package/fill-range) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/fill-range.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/fill-range)\n\n> Fill in a range of numbers or letters, optionally passing an increment or `step` to use, or create a regex-compatible range with `options.toRegex`\n\n## Table of Contents\n\n- [Install](#install)\n- [Usage](#usage)\n- [Examples](#examples)\n- [Options](#options)\n  * [options.step](#optionsstep)\n  * [options.strictRanges](#optionsstrictranges)\n  * [options.stringify](#optionsstringify)\n  * [options.toRegex](#optionstoregex)\n  * [options.transform](#optionstransform)\n- [About](#about)\n\n_(TOC generated by [verb](https://github.com/verbose/verb) using [markdown-toc](https://github.com/jonschlinkert/markdown-toc))_\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save fill-range\n```\n\nInstall with [yarn](https://yarnpkg.com):\n\n```sh\n$ yarn add fill-range\n```\n\n## Usage\n\nExpands numbers and letters, optionally using a `step` as the last argument. _(Numbers may be defined as JavaScript numbers or strings)_.\n\n```js\nvar fill = require('fill-range');\nfill(from, to[, step, options]);\n\n// examples\nconsole.log(fill('1', '10'));                  //=> '[ '1', '2', '3', '4', '5', '6', '7', '8', '9', '10' ]'\nconsole.log(fill('1', '10', {toRegex: true})); //=> [1-9]|10\n```\n\n**Params**\n\n* `from`: **{String|Number}** the number or letter to start with\n* `to`: **{String|Number}** the number or letter to end with\n* `step`: **{String|Number|Object|Function}** Optionally pass a [step](#optionsstep) to use.\n* `options`: **{Object|Function}**: See all available [options](#options)\n\n## Examples\n\nBy default, an array of values is returned.\n\n**Alphabetical ranges**\n\n```js\nconsole.log(fill('a', 'e')); //=> ['a', 'b', 'c', 'd', 'e']\nconsole.log(fill('A', 'E')); //=> [ 'A', 'B', 'C', 'D', 'E' ]\n```\n\n**Numerical ranges**\n\nNumbers can be defined as actual numbers or strings.\n\n```js\nconsole.log(fill(1, 5));     //=> [ 1, 2, 3, 4, 5 ]\nconsole.log(fill('1', '5')); //=> [ 1, 2, 3, 4, 5 ]\n```\n\n**Negative ranges**\n\nNumbers can be defined as actual numbers or strings.\n\n```js\nconsole.log(fill('-5', '-1')); //=> [ '-5', '-4', '-3', '-2', '-1' ]\nconsole.log(fill('-5', '5')); //=> [ '-5', '-4', '-3', '-2', '-1', '0', '1', '2', '3', '4', '5' ]\n```\n\n**Steps (increments)**\n\n```js\n// numerical ranges with increments\nconsole.log(fill('0', '25', 4)); //=> [ '0', '4', '8', '12', '16', '20', '24' ]\nconsole.log(fill('0', '25', 5)); //=> [ '0', '5', '10', '15', '20', '25' ]\nconsole.log(fill('0', '25', 6)); //=> [ '0', '6', '12', '18', '24' ]\n\n// alphabetical ranges with increments\nconsole.log(fill('a', 'z', 4)); //=> [ 'a', 'e', 'i', 'm', 'q', 'u', 'y' ]\nconsole.log(fill('a', 'z', 5)); //=> [ 'a', 'f', 'k', 'p', 'u', 'z' ]\nconsole.log(fill('a', 'z', 6)); //=> [ 'a', 'g', 'm', 's', 'y' ]\n```\n\n## Options\n\n### options.step\n\n**Type**: `number` (formatted as a string or number)\n\n**Default**: `undefined`\n\n**Description**: The increment to use for the range. Can be used with letters or numbers.\n\n**Example(s)**\n\n```js\n// numbers\nconsole.log(fill('1', '10', 2)); //=> [ '1', '3', '5', '7', '9' ]\nconsole.log(fill('1', '10', 3)); //=> [ '1', '4', '7', '10' ]\nconsole.log(fill('1', '10', 4)); //=> [ '1', '5', '9' ]\n\n// letters\nconsole.log(fill('a', 'z', 5)); //=> [ 'a', 'f', 'k', 'p', 'u', 'z' ]\nconsole.log(fill('a', 'z', 7)); //=> [ 'a', 'h', 'o', 'v' ]\nconsole.log(fill('a', 'z', 9)); //=> [ 'a', 'j', 's' ]\n```\n\n### options.strictRanges\n\n**Type**: `boolean`\n\n**Default**: `false`\n\n**Description**: By default, `null` is returned when an invalid range is passed. Enable this option to throw a `RangeError` on invalid ranges.\n\n**Example(s)**\n\nThe following are all invalid:\n\n```js\nfill('1.1', '2');   // decimals not supported in ranges\nfill('a', '2');     // incompatible range values\nfill(1, 10, 'foo'); // invalid \"step\" argument\n```\n\n### options.stringify\n\n**Type**: `boolean`\n\n**Default**: `undefined`\n\n**Description**: Cast all returned values to strings. By default, integers are returned as numbers.\n\n**Example(s)**\n\n```js\nconsole.log(fill(1, 5));                    //=> [ 1, 2, 3, 4, 5 ]\nconsole.log(fill(1, 5, {stringify: true})); //=> [ '1', '2', '3', '4', '5' ]\n```\n\n### options.toRegex\n\n**Type**: `boolean`\n\n**Default**: `undefined`\n\n**Description**: Create a regex-compatible source string, instead of expanding values to an array.\n\n**Example(s)**\n\n```js\n// alphabetical range\nconsole.log(fill('a', 'e', {toRegex: true})); //=> '[a-e]'\n// alphabetical with step\nconsole.log(fill('a', 'z', 3, {toRegex: true})); //=> 'a|d|g|j|m|p|s|v|y'\n// numerical range\nconsole.log(fill('1', '100', {toRegex: true})); //=> '[1-9]|[1-9][0-9]|100'\n// numerical range with zero padding\nconsole.log(fill('000001', '100000', {toRegex: true}));\n//=> '0{5}[1-9]|0{4}[1-9][0-9]|0{3}[1-9][0-9]{2}|0{2}[1-9][0-9]{3}|0[1-9][0-9]{4}|100000'\n```\n\n### options.transform\n\n**Type**: `function`\n\n**Default**: `undefined`\n\n**Description**: Customize each value in the returned array (or [string](#optionstoRegex)). _(you can also pass this function as the last argument to `fill()`)_.\n\n**Example(s)**\n\n```js\n// increase padding by two\nvar arr = fill('01', '05', function(val, a, b, step, idx, arr, options) {\n  return repeat('0', (options.maxLength + 2) - val.length) + val;\n});\n\nconsole.log(arr);\n//=> ['0001', '0002', '0003', '0004', '0005']\n```\n\n## About\n\n### Related projects\n\n* [braces](https://www.npmjs.com/package/braces): Fast, comprehensive, bash-like brace expansion implemented in JavaScript. Complete support for the Bash 4.3 braces… [more](https://github.com/jonschlinkert/braces) | [homepage](https://github.com/jonschlinkert/braces \"Fast, comprehensive, bash-like brace expansion implemented in JavaScript. Complete support for the Bash 4.3 braces specification, without sacrificing speed.\")\n* [expand-range](https://www.npmjs.com/package/expand-range): Fast, bash-like range expansion. Expand a range of numbers or letters, uppercase or lowercase. See… [more](https://github.com/jonschlinkert/expand-range) | [homepage](https://github.com/jonschlinkert/expand-range \"Fast, bash-like range expansion. Expand a range of numbers or letters, uppercase or lowercase. See the benchmarks. Used by micromatch.\")\n* [micromatch](https://www.npmjs.com/package/micromatch): Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. | [homepage](https://github.com/jonschlinkert/micromatch \"Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.\")\n* [to-regex-range](https://www.npmjs.com/package/to-regex-range): Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than… [more](https://github.com/jonschlinkert/to-regex-range) | [homepage](https://github.com/jonschlinkert/to-regex-range \"Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.87 million test assertions.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor** |  \n| --- | --- |  \n| 103 | [jonschlinkert](https://github.com/jonschlinkert) |  \n| 2   | [paulmillr](https://github.com/paulmillr) |  \n| 1   | [edorivai](https://github.com/edorivai) |  \n| 1   | [wtgtybhertgeghgtwtg](https://github.com/wtgtybhertgeghgtwtg) |  \n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.5.0, on April 23, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/fill-range/-/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7", "type": "tarball", "reference": "https://registry.yarnpkg.com/fill-range/-/fill-range-4.0.0.tgz", "hash": "d544811d428f98eb06a63dc402d2403c328c38f7", "integrity": "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=", "registry": "npm", "packageName": "fill-range", "cacheIntegrity": "sha512-VcpLTWqWDiTerugjj8e3+esbg+skS3M9e54UuR3iCeIDMXCLTsAH8hTSzDQU/X6/6t3eYkOKoZSef2PlU6U1XQ== sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc="}, "registry": "npm", "hash": "d544811d428f98eb06a63dc402d2403c328c38f7"}