{"manifest": {"name": "fs.realpath", "version": "1.0.0", "description": "Use node's fs.realpath, but fall back to the JS implementation if the native one fails", "main": "index.js", "dependencies": {}, "devDependencies": {}, "scripts": {"test": "tap test/*.js --cov"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/fs.realpath.git"}, "keywords": ["realpath", "fs", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["old.js", "index.js"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-fs-realpath-1.0.0-1504ad2523158caa40db4a2787cb01411994ea4f-integrity\\node_modules\\fs.realpath\\package.json", "readmeFilename": "README.md", "readme": "# fs.realpath\n\nA backwards-compatible fs.realpath for Node v6 and above\n\nIn Node v6, the JavaScript implementation of fs.realpath was replaced\nwith a faster (but less resilient) native implementation.  That raises\nnew and platform-specific errors and cannot handle long or excessively\nsymlink-looping paths.\n\nThis module handles those cases by detecting the new errors and\nfalling back to the JavaScript implementation.  On versions of Node\nprior to v6, it has no effect.\n\n## USAGE\n\n```js\nvar rp = require('fs.realpath')\n\n// async version\nrp.realpath(someLongAndLoopingPath, function (er, real) {\n  // the ELOOP was handled, but it was a bit slower\n})\n\n// sync version\nvar real = rp.realpathSync(someLongAndLoopingPath)\n\n// monkeypatch at your own risk!\n// This replaces the fs.realpath/fs.realpathSync builtins\nrp.monkeypatch()\n\n// un-do the monkeypatching\nrp.unmonkeypatch()\n```\n", "licenseText": "The ISC License\n\nCopyright (c) <PERSON> and Contributors\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n\n----\n\nThis library bundles a version of the `fs.realpath` and `fs.realpathSync`\nmethods from Node.js v0.10 under the terms of the Node.js MIT license.\n\nNode's license follows, also included at the header of `old.js` which contains\nthe licensed code:\n\n  Copyright Joyent, Inc. and other Node contributors.\n\n  Permission is hereby granted, free of charge, to any person obtaining a\n  copy of this software and associated documentation files (the \"Software\"),\n  to deal in the Software without restriction, including without limitation\n  the rights to use, copy, modify, merge, publish, distribute, sublicense,\n  and/or sell copies of the Software, and to permit persons to whom the\n  Software is furnished to do so, subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be included in\n  all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n  DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f", "type": "tarball", "reference": "https://registry.yarnpkg.com/fs.realpath/-/fs.realpath-1.0.0.tgz", "hash": "1504ad2523158caa40db4a2787cb01411994ea4f", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "registry": "npm", "packageName": "fs.realpath", "cacheIntegrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw== sha1-FQStJSMVjKpA20onh8sBQRmU6k8="}, "registry": "npm", "hash": "1504ad2523158caa40db4a2787cb01411994ea4f"}