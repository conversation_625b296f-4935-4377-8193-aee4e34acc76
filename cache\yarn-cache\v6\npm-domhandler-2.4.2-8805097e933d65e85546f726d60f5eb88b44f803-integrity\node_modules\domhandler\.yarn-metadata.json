{"manifest": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "2.4.2", "description": "handler for htmlparser2 that turns pages into a dom", "main": "index.js", "scripts": {"test": "mocha -R list && jshint index.js test/"}, "repository": {"type": "git", "url": "git://github.com/fb55/DomHandler.git"}, "keywords": ["dom", "htmlparser2"], "dependencies": {"domelementtype": "1"}, "devDependencies": {"htmlparser2": "^3.9.0", "mocha": "^3.0.2", "jshint": "^2.9.1"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "jshintConfig": {"quotmark": "double", "trailing": true, "unused": true, "undef": true, "node": true, "proto": true, "globals": {"it": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-domhandler-2.4.2-8805097e933d65e85546f726d60f5eb88b44f803-integrity\\node_modules\\domhandler\\package.json", "readmeFilename": "readme.md", "readme": "# domhandler [![Build Status](https://travis-ci.org/fb55/domhandler.svg?branch=master)](https://travis-ci.org/fb55/domhandler)\n\nThe DOM handler (formally known as DefaultHandler) creates a tree containing all nodes of a page. The tree may be manipulated using the [domutils](https://github.com/fb55/domutils) library.\n\n## Usage\n```javascript\nvar handler = new DomHandler([ <func> callback(err, dom), ] [ <obj> options ]);\n// var parser = new Parser(handler[, options]);\n```\n\nAvailable options are described below.\n\n## Example\n```javascript\nvar htmlparser = require(\"htmlparser2\");\nvar rawHtml = \"Xyz <script language= javascript>var foo = '<<bar>>';< /  script><!--<!-- Waah! -- -->\";\nvar handler = new htmlparser.DomHandler(function (error, dom) {\n    if (error)\n    \t[...do something for errors...]\n    else\n    \t[...parsing done, do something...]\n        console.log(dom);\n});\nvar parser = new htmlparser.Parser(handler);\nparser.write(rawHtml);\nparser.end();\n```\n\nOutput:\n\n```javascript\n[{\n    data: 'Xyz ',\n    type: 'text'\n}, {\n    type: 'script',\n    name: 'script',\n    attribs: {\n    \tlanguage: 'javascript'\n    },\n    children: [{\n    \tdata: 'var foo = \\'<bar>\\';<',\n    \ttype: 'text'\n    }]\n}, {\n    data: '<!-- Waah! -- ',\n    type: 'comment'\n}]\n```\n\n## Option: normalizeWhitespace\nIndicates whether the whitespace in text nodes should be normalized (= all whitespace should be replaced with single spaces). The default value is \"false\". \n\nThe following HTML will be used:\n\n```html\n<font>\n\t<br>this is the text\n<font>\n```\n\n### Example: true\n\n```javascript\n[{\n    type: 'tag',\n    name: 'font',\n    children: [{\n    \tdata: ' ',\n    \ttype: 'text'\n    }, {\n    \ttype: 'tag',\n    \tname: 'br'\n    }, {\n    \tdata: 'this is the text ',\n    \ttype: 'text'\n    }, {\n    \ttype: 'tag',\n    \tname: 'font'\n    }]\n}]\n```\n\n### Example: false\n\n```javascript\n[{\n    type: 'tag',\n    name: 'font',\n    children: [{\n    \tdata: '\\n\\t',\n    \ttype: 'text'\n    }, {\n    \ttype: 'tag',\n    \tname: 'br'\n    }, {\n    \tdata: 'this is the text\\n',\n    \ttype: 'text'\n    }, {\n    \ttype: 'tag',\n    \tname: 'font'\n    }]\n}]\n```\n\n## Option: withDomLvl1\n\nAdds DOM level 1 properties to all elements.\n\n<!-- TODO: description -->\n\n## Option: withStartIndices\nIndicates whether a `startIndex` property will be added to nodes. When the parser is used in a non-streaming fashion, `startIndex` is an integer indicating the position of the start of the node in the document. The default value is \"false\".\n\n## Option: withEndIndices\nIndicates whether a `endIndex` property will be added to nodes. When the parser is used in a non-streaming fashion, `endIndex` is an integer indicating the position of the end of the node in the document. The default value is \"false\".\n", "licenseText": "Copyright (c) <PERSON>\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n\nRedistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.\n\nRedistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.\n\nTHIS IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERC<PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS,\nEVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/domhandler/-/domhandler-2.4.2.tgz#8805097e933d65e85546f726d60f5eb88b44f803", "type": "tarball", "reference": "https://registry.yarnpkg.com/domhandler/-/domhandler-2.4.2.tgz", "hash": "8805097e933d65e85546f726d60f5eb88b44f803", "integrity": "sha512-JiK04h0Ht5u/80fdLMCEmV4zkNh2BcoMFBmZ/91WtYZ8qVXSKjiw7fXMgFPnHcSZgOo3XdinHvmnDUeMf5R4wA==", "registry": "npm", "packageName": "<PERSON><PERSON><PERSON><PERSON>", "cacheIntegrity": "sha512-JiK04h0Ht5u/80fdLMCEmV4zkNh2BcoMFBmZ/91WtYZ8qVXSKjiw7fXMgFPnHcSZgOo3XdinHvmnDUeMf5R4wA== sha1-iAUJfpM9ZehVRvcm1g9euItE+AM="}, "registry": "npm", "hash": "8805097e933d65e85546f726d60f5eb88b44f803"}