{"manifest": {"name": "p-limit", "version": "2.3.0", "description": "Run multiple promise-returning & async functions with limited concurrency", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/p-limit.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd-check"}, "files": ["index.js", "index.d.ts"], "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "dependencies": {"p-try": "^2.0.0"}, "devDependencies": {"ava": "^1.2.1", "delay": "^4.1.0", "in-range": "^1.0.0", "random-int": "^1.0.0", "time-span": "^2.0.0", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-p-limit-2.3.0-3dd33c647a214fdfffd835933eb086da0dc21db1-integrity\\node_modules\\p-limit\\package.json", "readmeFilename": "readme.md", "readme": "# p-limit [![Build Status](https://travis-ci.org/sindresorhus/p-limit.svg?branch=master)](https://travis-ci.org/sindresorhus/p-limit)\n\n> Run multiple promise-returning & async functions with limited concurrency\n\n## Install\n\n```\n$ npm install p-limit\n```\n\n## Usage\n\n```js\nconst pLimit = require('p-limit');\n\nconst limit = pLimit(1);\n\nconst input = [\n\tlimit(() => fetchSomething('foo')),\n\tlimit(() => fetchSomething('bar')),\n\tlimit(() => doSomething())\n];\n\n(async () => {\n\t// Only one promise is run at once\n\tconst result = await Promise.all(input);\n\tconsole.log(result);\n})();\n```\n\n## API\n\n### pLimit(concurrency)\n\nReturns a `limit` function.\n\n#### concurrency\n\nType: `number`\\\nMinimum: `1`\\\nDefault: `Infinity`\n\nConcurrency limit.\n\n### limit(fn, ...args)\n\nReturns the promise returned by calling `fn(...args)`.\n\n#### fn\n\nType: `Function`\n\nPromise-returning/async function.\n\n#### args\n\nAny arguments to pass through to `fn`.\n\nSupport for passing arguments on to the `fn` is provided in order to be able to avoid creating unnecessary closures. You probably don't need this optimization unless you're pushing a *lot* of functions.\n\n### limit.activeCount\n\nThe number of promises that are currently running.\n\n### limit.pendingCount\n\nThe number of promises that are waiting to run (i.e. their internal `fn` was not called yet).\n\n### limit.clearQueue()\n\nDiscard pending promises that are waiting to run.\n\nThis might be useful if you want to teardown the queue at the end of your program's lifecycle or discard any function calls referencing an intermediary state of your app.\n\nNote: This does not cancel promises that are already running.\n\n## FAQ\n\n### How is this different from the [`p-queue`](https://github.com/sindresorhus/p-queue) package?\n\nThis package is only about limiting the number of concurrent executions, while `p-queue` is a fully featured queue implementation with lots of different options, introspection, and ability to pause the queue.\n\n## Related\n\n- [p-queue](https://github.com/sindresorhus/p-queue) - Promise queue with concurrency control\n- [p-throttle](https://github.com/sindresorhus/p-throttle) - Throttle promise-returning & async functions\n- [p-debounce](https://github.com/sindresorhus/p-debounce) - Debounce promise-returning & async functions\n- [p-all](https://github.com/sindresorhus/p-all) - Run promise-returning & async functions concurrently with optional limited concurrency\n- [More…](https://github.com/sindresorhus/promise-fun)\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-p-limit?utm_source=npm-p-limit&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "licenseText": "MIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/p-limit/-/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1", "type": "tarball", "reference": "https://registry.yarnpkg.com/p-limit/-/p-limit-2.3.0.tgz", "hash": "3dd33c647a214fdfffd835933eb086da0dc21db1", "integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==", "registry": "npm", "packageName": "p-limit", "cacheIntegrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w== sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE="}, "registry": "npm", "hash": "3dd33c647a214fdfffd835933eb086da0dc21db1"}