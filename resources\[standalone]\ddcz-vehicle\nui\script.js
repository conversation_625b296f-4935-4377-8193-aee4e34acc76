// ================================================
// DDCZ-VEHICLE NUI JavaScript
// ================================================

let isMenuOpen = false;
let currentVehicleData = {};
let currentLivery = 0;
let maxLivery = 0;

// DOM Elements
const vehicleContainer = document.getElementById('vehicle-container');
const closeBtn = document.getElementById('close-btn');
const vehicleName = document.getElementById('vehicle-name');
const vehicleModel = document.getElementById('vehicle-model');
const vehiclePlate = document.getElementById('vehicle-plate');
const engineStatus = document.getElementById('engine-status');
const restrictedSection = document.getElementById('restricted-section');
const extrasGrid = document.getElementById('extras-grid');
const currentLiverySpan = document.getElementById('current-livery');
const maxLiverySpan = document.getElementById('max-livery');
const liveryPrevBtn = document.getElementById('livery-prev');
const liveryNextBtn = document.getElementById('livery-next');
const saveBtn = document.getElementById('save-btn');
const resetBtn = document.getElementById('reset-btn');

// ================================================
// Event Listeners
// ================================================

// Close button
closeBtn.addEventListener('click', closeMenu);

// Control buttons
document.querySelectorAll('.control-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const action = this.dataset.action;
        if (action) {
            handleControlAction(action);
        }
    });
});

// Door buttons
document.querySelectorAll('.door-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const doorId = parseInt(this.dataset.door);
        toggleDoor(doorId);
    });
});

// Light buttons
document.querySelectorAll('.light-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const lightType = this.dataset.light;
        toggleLight(lightType);
    });
});

// Livery navigation
liveryPrevBtn.addEventListener('click', () => changeLivery(-1));
liveryNextBtn.addEventListener('click', () => changeLivery(1));

// Action buttons
saveBtn.addEventListener('click', saveVehicleSettings);
resetBtn.addEventListener('click', resetVehicleSettings);

// Keyboard events
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && isMenuOpen) {
        closeMenu();
    }
});

// ================================================
// NUI Message Handler
// ================================================

window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.type) {
        case 'openMenu':
            openMenu(data.vehicleData);
            break;
        case 'closeMenu':
            closeMenu();
            break;
        case 'updateVehicleData':
            updateVehicleData(data.vehicleData);
            break;
        case 'updateExtras':
            updateExtras(data.extras);
            break;
        case 'updateLivery':
            updateLiveryDisplay(data.current, data.max);
            break;
    }
});

// ================================================
// Main Functions
// ================================================

function openMenu(vehicleData) {
    currentVehicleData = vehicleData;
    updateVehicleInfo(vehicleData);
    updateRestrictedSection(vehicleData.hasRestrictedAccess);
    
    vehicleContainer.classList.remove('hidden');
    isMenuOpen = true;
    
    // Add opening animation
    setTimeout(() => {
        vehicleContainer.style.opacity = '1';
    }, 10);
}

function closeMenu() {
    vehicleContainer.classList.add('hidden');
    isMenuOpen = false;
    
    // Send close callback to client
    sendNUICallback('closeMenu', {});
}

function updateVehicleInfo(data) {
    vehicleName.textContent = data.displayName || 'Neznámé vozidlo';
    vehicleModel.textContent = data.model || 'N/A';
    vehiclePlate.textContent = data.plate || 'N/A';
    
    // Update engine status
    const engineStatusElement = document.getElementById('engine-status');
    if (data.engineOn) {
        engineStatusElement.textContent = 'Zapnut';
        engineStatusElement.className = 'status-indicator engine-on';
    } else {
        engineStatusElement.textContent = 'Vypnut';
        engineStatusElement.className = 'status-indicator engine-off';
    }
}

function updateRestrictedSection(hasAccess) {
    if (hasAccess) {
        restrictedSection.classList.remove('hidden');
        generateExtrasButtons();
    } else {
        restrictedSection.classList.add('hidden');
    }
}

function generateExtrasButtons() {
    extrasGrid.innerHTML = '';
    
    for (let i = 1; i <= 12; i++) {
        const extraBtn = document.createElement('button');
        extraBtn.className = 'extra-btn';
        extraBtn.dataset.extra = i;
        extraBtn.innerHTML = `
            <span class="extra-icon">⚙️</span>
            <span class="extra-label">Extra ${i}</span>
        `;
        
        extraBtn.addEventListener('click', function() {
            toggleExtra(i);
        });
        
        extrasGrid.appendChild(extraBtn);
    }
}

// ================================================
// Control Actions
// ================================================

function handleControlAction(action) {
    const button = document.querySelector(`[data-action="${action}"]`);
    
    // Add visual feedback
    button.classList.add('pulse');
    setTimeout(() => button.classList.remove('pulse'), 600);
    
    // Send action to client
    sendNUICallback('vehicleAction', { action: action });
}

function toggleDoor(doorId) {
    const doorBtn = document.querySelector(`[data-door="${doorId}"]`);
    const isOpen = doorBtn.classList.contains('open');
    
    // Toggle visual state
    if (isOpen) {
        doorBtn.classList.remove('open');
    } else {
        doorBtn.classList.add('open');
    }
    
    // Send to client
    sendNUICallback('toggleDoor', { doorId: doorId, open: !isOpen });
}

function toggleLight(lightType) {
    const lightBtn = document.querySelector(`[data-light="${lightType}"]`);
    const isActive = lightBtn.classList.contains('active');
    
    // Toggle visual state
    if (isActive) {
        lightBtn.classList.remove('active');
    } else {
        lightBtn.classList.add('active');
    }
    
    // Send to client
    sendNUICallback('toggleLight', { lightType: lightType, active: !isActive });
}

function toggleExtra(extraId) {
    const extraBtn = document.querySelector(`[data-extra="${extraId}"]`);
    const isActive = extraBtn.classList.contains('active');
    
    // Toggle visual state
    if (isActive) {
        extraBtn.classList.remove('active');
    } else {
        extraBtn.classList.add('active');
    }
    
    // Send to client
    sendNUICallback('toggleExtra', { extraId: extraId, active: !isActive });
}

function changeLivery(direction) {
    const newLivery = currentLivery + direction;
    
    if (newLivery >= 0 && newLivery <= maxLivery) {
        currentLivery = newLivery;
        updateLiveryDisplay(currentLivery, maxLivery);
        
        // Send to client
        sendNUICallback('changeLivery', { livery: currentLivery });
    }
}

function updateLiveryDisplay(current, max) {
    currentLivery = current;
    maxLivery = max;
    
    currentLiverySpan.textContent = current;
    maxLiverySpan.textContent = max;
    
    // Update button states
    liveryPrevBtn.disabled = current <= 0;
    liveryNextBtn.disabled = current >= max;
}

function saveVehicleSettings() {
    sendNUICallback('saveSettings', {});
    showNotification('Nastavení uloženo!', 'success');
}

function resetVehicleSettings() {
    sendNUICallback('resetSettings', {});
    showNotification('Nastavení resetováno!', 'info');
    
    // Reset visual states
    document.querySelectorAll('.door-btn.open').forEach(btn => {
        btn.classList.remove('open');
    });
    
    document.querySelectorAll('.light-btn.active').forEach(btn => {
        btn.classList.remove('active');
    });
    
    document.querySelectorAll('.extra-btn.active').forEach(btn => {
        btn.classList.remove('active');
    });
}

// ================================================
// Utility Functions
// ================================================

function sendNUICallback(action, data) {
    fetch(`https://${GetParentResourceName()}/${action}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify(data)
    }).catch(error => {
        console.error('NUI Callback Error:', error);
    });
}

function GetParentResourceName() {
    return window.location.hostname;
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
        ${type === 'success' ? 'background: linear-gradient(145deg, #27ae60, #2ecc71);' : ''}
        ${type === 'error' ? 'background: linear-gradient(145deg, #e74c3c, #c0392b);' : ''}
        ${type === 'info' ? 'background: linear-gradient(145deg, #3498db, #2980b9);' : ''}
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 10);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// ================================================
// Initialize
// ================================================

document.addEventListener('DOMContentLoaded', function() {
    console.log('DDCZ-VEHICLE NUI Loaded');
});
