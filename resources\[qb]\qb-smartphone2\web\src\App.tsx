import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';
import Phone from './components/Phone';
import LockScreen from './components/LockScreen';
import HomeScreen from './components/HomeScreen';
import StatusBar from './components/StatusBar';
import NotificationCenter from './components/NotificationCenter';
import { usePhone } from './contexts/PhoneContext';
import { useNotifications } from './contexts/NotificationContext';
import { debugData } from './utils/debugData';
import { fetchNui } from './utils/fetchNui';
import { isEnvBrowser } from './utils/misc';

// Import aplikací
import ContactsApp from './apps/ContactsApp';
import MessagesApp from './apps/MessagesApp';
import CallsApp from './apps/CallsApp';
import CameraApp from './apps/CameraApp';
import GalleryApp from './apps/GalleryApp';
import GPSApp from './apps/GPSApp';
import SettingsApp from './apps/SettingsApp';
import DarkWebApp from './apps/DarkWebApp';
import SocialApp from './apps/SocialApp';
import MarketplaceApp from './apps/MarketplaceApp';
import BankingApp from './apps/BankingApp';
import EmailApp from './apps/EmailApp';
import CalendarApp from './apps/CalendarApp';
import GamesApp from './apps/GamesApp';

import './App.css';

function App() {
  const { 
    isVisible, 
    isLocked, 
    currentApp, 
    phoneData,
    setIsVisible,
    setPhoneData,
    setCurrentApp
  } = usePhone();
  
  const { addNotification } = useNotifications();
  const [isLoading, setIsLoading] = useState(true);

  // NUI Event listeners
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const { action, data } = event.data;

      switch (action) {
        case 'openPhone':
          setIsVisible(true);
          if (data) {
            setPhoneData(data);
          }
          break;
          
        case 'closePhone':
          setIsVisible(false);
          setCurrentApp(null);
          break;
          
        case 'updateBattery':
          setPhoneData(prev => ({
            ...prev,
            batteryLevel: data.batteryLevel
          }));
          break;
          
        case 'newNotification':
          addNotification(data.notification);
          break;
          
        case 'cameraOpened':
          setCurrentApp('camera');
          break;
          
        case 'cameraClosed':
          setCurrentApp(null);
          break;
          
        default:
          break;
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [setIsVisible, setPhoneData, setCurrentApp, addNotification]);

  // Inicializace dat telefonu
  useEffect(() => {
    const initializePhone = async () => {
      try {
        if (isEnvBrowser()) {
          // Debug data pro vývoj v prohlížeči
          debugData([
            {
              action: 'openPhone',
              data: {
                phoneNumber: '555-0123',
                batteryLevel: 85,
                isCharging: false,
                isLocked: true,
                settings: {
                  wallpaper: 'default.jpg',
                  theme: 'dark',
                  ringtone: 'default',
                  notification_sound: 1,
                  vibration: 1,
                  pin_code: '1234',
                  fingerprint_enabled: 1
                },
                notifications: []
              }
            }
          ]);
        } else {
          // Načtení dat ze serveru
          const data = await fetchNui('getPhoneData');
          if (data.success) {
            setPhoneData(data.data);
          }
        }
      } catch (error) {
        console.error('Chyba při inicializaci telefonu:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializePhone();
  }, [setPhoneData]);

  // Klávesové zkratky
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isVisible) {
        handleClosePhone();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isVisible]);

  const handleClosePhone = () => {
    fetchNui('closePhone');
    setIsVisible(false);
    setCurrentApp(null);
  };

  const renderCurrentApp = () => {
    switch (currentApp) {
      case 'contacts':
        return <ContactsApp />;
      case 'messages':
        return <MessagesApp />;
      case 'calls':
        return <CallsApp />;
      case 'camera':
        return <CameraApp />;
      case 'gallery':
        return <GalleryApp />;
      case 'gps':
        return <GPSApp />;
      case 'settings':
        return <SettingsApp />;
      case 'darkweb':
        return <DarkWebApp />;
      case 'social':
        return <SocialApp />;
      case 'marketplace':
        return <MarketplaceApp />;
      case 'banking':
        return <BankingApp />;
      case 'email':
        return <EmailApp />;
      case 'calendar':
        return <CalendarApp />;
      case 'games':
        return <GamesApp />;
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center w-full h-full">
        <div className="text-white">Načítání...</div>
      </div>
    );
  }

  return (
    <Router>
      <div className="App">
        <AnimatePresence>
          {isVisible && (
            <Phone onClose={handleClosePhone}>
              <StatusBar />
              <NotificationCenter />
              
              <div className="phone-content">
                <AnimatePresence mode="wait">
                  {currentApp ? (
                    <div key={currentApp} className="app-screen">
                      {renderCurrentApp()}
                    </div>
                  ) : isLocked ? (
                    <LockScreen key="lockscreen" />
                  ) : (
                    <HomeScreen key="homescreen" />
                  )}
                </AnimatePresence>
              </div>
            </Phone>
          )}
        </AnimatePresence>
      </div>
    </Router>
  );
}

export default App;
