{"name": "parallel-transform", "version": "1.2.0", "repository": "git://github.com/mafintosh/parallel-transform", "license": "MIT", "description": "Transform stream that allows you to run your transforms in parallel without changing the order", "keywords": ["transform", "stream", "parallel", "preserve", "order"], "author": "<PERSON> <<EMAIL>>", "dependencies": {"cyclist": "^1.0.1", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}}