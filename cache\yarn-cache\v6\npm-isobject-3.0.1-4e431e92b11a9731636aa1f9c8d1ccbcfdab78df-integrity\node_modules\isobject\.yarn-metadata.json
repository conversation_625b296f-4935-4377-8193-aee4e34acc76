{"manifest": {"name": "isobject", "description": "Returns true if the value is an object and not an array or null.", "version": "3.0.1", "homepage": "https://github.com/jonschlinkert/isobject", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"url": "https://github.com/LeSuisse"}, {"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/magnudae"}, {"name": "<PERSON>", "url": "https://macwright.org"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/isobject.git"}, "bugs": {"url": "https://github.com/jonschlinkert/isobject/issues"}, "license": "MIT", "files": ["index.d.ts", "index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {}, "devDependencies": {"gulp-format-md": "^0.1.9", "mocha": "^2.4.5"}, "keywords": ["check", "is", "is-object", "isobject", "kind", "kind-of", "kindof", "native", "object", "type", "typeof", "value"], "types": "index.d.ts", "verb": {"related": {"list": ["extend-shallow", "is-plain-object", "kind-of", "merge-deep"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-isobject-3.0.1-4e431e92b11a9731636aa1f9c8d1ccbcfdab78df-integrity\\node_modules\\isobject\\package.json", "readmeFilename": "README.md", "readme": "# isobject [![NPM version](https://img.shields.io/npm/v/isobject.svg?style=flat)](https://www.npmjs.com/package/isobject) [![NPM monthly downloads](https://img.shields.io/npm/dm/isobject.svg?style=flat)](https://npmjs.org/package/isobject)  [![NPM total downloads](https://img.shields.io/npm/dt/isobject.svg?style=flat)](https://npmjs.org/package/isobject) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/isobject.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/isobject)\n\n> Returns true if the value is an object and not an array or null.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save isobject\n```\n\nInstall with [yarn](https://yarnpkg.com):\n\n```sh\n$ yarn add isobject\n```\n\nUse [is-plain-object](https://github.com/jonschlinkert/is-plain-object) if you want only objects that are created by the `Object` constructor.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install isobject\n```\nInstall with [bower](https://bower.io/)\n\n```sh\n$ bower install isobject\n```\n\n## Usage\n\n```js\nvar isObject = require('isobject');\n```\n\n**True**\n\nAll of the following return `true`:\n\n```js\nisObject({});\nisObject(Object.create({}));\nisObject(Object.create(Object.prototype));\nisObject(Object.create(null));\nisObject({});\nisObject(new Foo);\nisObject(/foo/);\n```\n\n**False**\n\nAll of the following return `false`:\n\n```js\nisObject();\nisObject(function () {});\nisObject(1);\nisObject([]);\nisObject(undefined);\nisObject(null);\n```\n\n## About\n\n### Related projects\n\n* [extend-shallow](https://www.npmjs.com/package/extend-shallow): Extend an object with the properties of additional objects. node.js/javascript util. | [homepage](https://github.com/jonschlinkert/extend-shallow \"Extend an object with the properties of additional objects. node.js/javascript util.\")\n* [is-plain-object](https://www.npmjs.com/package/is-plain-object): Returns true if an object was created by the `Object` constructor. | [homepage](https://github.com/jonschlinkert/is-plain-object \"Returns true if an object was created by the `Object` constructor.\")\n* [kind-of](https://www.npmjs.com/package/kind-of): Get the native type of a value. | [homepage](https://github.com/jonschlinkert/kind-of \"Get the native type of a value.\")\n* [merge-deep](https://www.npmjs.com/package/merge-deep): Recursively merge values in a javascript object. | [homepage](https://github.com/jonschlinkert/merge-deep \"Recursively merge values in a javascript object.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor** |  \n| --- | --- |  \n| 29 | [jonschlinkert](https://github.com/jonschlinkert) |  \n| 4  | [doowb](https://github.com/doowb) |  \n| 1  | [magnudae](https://github.com/magnudae) |  \n| 1  | [LeSuisse](https://github.com/LeSuisse) |  \n| 1  | [tmcw](https://github.com/tmcw) |  \n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on June 30, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/isobject/-/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df", "type": "tarball", "reference": "https://registry.yarnpkg.com/isobject/-/isobject-3.0.1.tgz", "hash": "4e431e92b11a9731636aa1f9c8d1ccbcfdab78df", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8=", "registry": "npm", "packageName": "isobject", "cacheIntegrity": "sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg== sha1-TkMekrEalzFjaqH5yNHMvP2reN8="}, "registry": "npm", "hash": "4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"}