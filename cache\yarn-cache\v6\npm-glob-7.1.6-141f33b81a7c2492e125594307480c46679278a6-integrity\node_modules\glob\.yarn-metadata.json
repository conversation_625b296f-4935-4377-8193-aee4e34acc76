{"manifest": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "glob", "description": "a little globber", "version": "7.1.6", "repository": {"type": "git", "url": "git://github.com/isaacs/node-glob.git"}, "main": "glob.js", "files": ["glob.js", "sync.js", "common.js"], "engines": {"node": "*"}, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"mkdirp": "0", "rimraf": "^2.2.8", "tap": "^12.0.1", "tick": "0.0.6"}, "scripts": {"prepublish": "npm run benchclean", "profclean": "rm -f v8.log profile.txt", "test": "tap test/*.js --cov", "test-regen": "npm run profclean && TEST_REGEN=1 node test/00-setup.js", "bench": "bash benchmark.sh", "prof": "bash prof.sh && cat profile.txt", "benchclean": "node benchclean.js"}, "license": "ISC", "funding": {"url": "https://github.com/sponsors/isaacs"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-glob-7.1.6-141f33b81a7c2492e125594307480c46679278a6-integrity\\node_modules\\glob\\package.json", "readmeFilename": "README.md", "readme": "# Glob\n\nMatch files using the patterns the shell uses, like stars and stuff.\n\n[![Build Status](https://travis-ci.org/isaacs/node-glob.svg?branch=master)](https://travis-ci.org/isaacs/node-glob/) [![Build Status](https://ci.appveyor.com/api/projects/status/kd7f3yftf7unxlsx?svg=true)](https://ci.appveyor.com/project/isaacs/node-glob) [![Coverage Status](https://coveralls.io/repos/isaacs/node-glob/badge.svg?branch=master&service=github)](https://coveralls.io/github/isaacs/node-glob?branch=master)\n\nThis is a glob implementation in JavaScript.  It uses the `minimatch`\nlibrary to do its matching.\n\n![](logo/glob.png)\n\n## Usage\n\nInstall with npm\n\n```\nnpm i glob\n```\n\n```javascript\nvar glob = require(\"glob\")\n\n// options is optional\nglob(\"**/*.js\", options, function (er, files) {\n  // files is an array of filenames.\n  // If the `nonull` option is set, and nothing\n  // was found, then files is [\"**/*.js\"]\n  // er is an error object or null.\n})\n```\n\n## Glob Primer\n\n\"Globs\" are the patterns you type when you do stuff like `ls *.js` on\nthe command line, or put `build/*` in a `.gitignore` file.\n\nBefore parsing the path part patterns, braced sections are expanded\ninto a set.  Braced sections start with `{` and end with `}`, with any\nnumber of comma-delimited sections within.  Braced sections may contain\nslash characters, so `a{/b/c,bcd}` would expand into `a/b/c` and `abcd`.\n\nThe following characters have special magic meaning when used in a\npath portion:\n\n* `*` Matches 0 or more characters in a single path portion\n* `?` Matches 1 character\n* `[...]` Matches a range of characters, similar to a RegExp range.\n  If the first character of the range is `!` or `^` then it matches\n  any character not in the range.\n* `!(pattern|pattern|pattern)` Matches anything that does not match\n  any of the patterns provided.\n* `?(pattern|pattern|pattern)` Matches zero or one occurrence of the\n  patterns provided.\n* `+(pattern|pattern|pattern)` Matches one or more occurrences of the\n  patterns provided.\n* `*(a|b|c)` Matches zero or more occurrences of the patterns provided\n* `@(pattern|pat*|pat?erN)` Matches exactly one of the patterns\n  provided\n* `**` If a \"globstar\" is alone in a path portion, then it matches\n  zero or more directories and subdirectories searching for matches.\n  It does not crawl symlinked directories.\n\n### Dots\n\nIf a file or directory path portion has a `.` as the first character,\nthen it will not match any glob pattern unless that pattern's\ncorresponding path part also has a `.` as its first character.\n\nFor example, the pattern `a/.*/c` would match the file at `a/.b/c`.\nHowever the pattern `a/*/c` would not, because `*` does not start with\na dot character.\n\nYou can make glob treat dots as normal characters by setting\n`dot:true` in the options.\n\n### Basename Matching\n\nIf you set `matchBase:true` in the options, and the pattern has no\nslashes in it, then it will seek for any file anywhere in the tree\nwith a matching basename.  For example, `*.js` would match\n`test/simple/basic.js`.\n\n### Empty Sets\n\nIf no matching files are found, then an empty array is returned.  This\ndiffers from the shell, where the pattern itself is returned.  For\nexample:\n\n    $ echo a*s*d*f\n    a*s*d*f\n\nTo get the bash-style behavior, set the `nonull:true` in the options.\n\n### See Also:\n\n* `man sh`\n* `man bash` (Search for \"Pattern Matching\")\n* `man 3 fnmatch`\n* `man 5 gitignore`\n* [minimatch documentation](https://github.com/isaacs/minimatch)\n\n## glob.hasMagic(pattern, [options])\n\nReturns `true` if there are any special characters in the pattern, and\n`false` otherwise.\n\nNote that the options affect the results.  If `noext:true` is set in\nthe options object, then `+(a|b)` will not be considered a magic\npattern.  If the pattern has a brace expansion, like `a/{b/c,x/y}`\nthen that is considered magical, unless `nobrace:true` is set in the\noptions.\n\n## glob(pattern, [options], cb)\n\n* `pattern` `{String}` Pattern to be matched\n* `options` `{Object}`\n* `cb` `{Function}`\n  * `err` `{Error | null}`\n  * `matches` `{Array<String>}` filenames found matching the pattern\n\nPerform an asynchronous glob search.\n\n## glob.sync(pattern, [options])\n\n* `pattern` `{String}` Pattern to be matched\n* `options` `{Object}`\n* return: `{Array<String>}` filenames found matching the pattern\n\nPerform a synchronous glob search.\n\n## Class: glob.Glob\n\nCreate a Glob object by instantiating the `glob.Glob` class.\n\n```javascript\nvar Glob = require(\"glob\").Glob\nvar mg = new Glob(pattern, options, cb)\n```\n\nIt's an EventEmitter, and starts walking the filesystem to find matches\nimmediately.\n\n### new glob.Glob(pattern, [options], [cb])\n\n* `pattern` `{String}` pattern to search for\n* `options` `{Object}`\n* `cb` `{Function}` Called when an error occurs, or matches are found\n  * `err` `{Error | null}`\n  * `matches` `{Array<String>}` filenames found matching the pattern\n\nNote that if the `sync` flag is set in the options, then matches will\nbe immediately available on the `g.found` member.\n\n### Properties\n\n* `minimatch` The minimatch object that the glob uses.\n* `options` The options object passed in.\n* `aborted` Boolean which is set to true when calling `abort()`.  There\n  is no way at this time to continue a glob search after aborting, but\n  you can re-use the statCache to avoid having to duplicate syscalls.\n* `cache` Convenience object.  Each field has the following possible\n  values:\n  * `false` - Path does not exist\n  * `true` - Path exists\n  * `'FILE'` - Path exists, and is not a directory\n  * `'DIR'` - Path exists, and is a directory\n  * `[file, entries, ...]` - Path exists, is a directory, and the\n    array value is the results of `fs.readdir`\n* `statCache` Cache of `fs.stat` results, to prevent statting the same\n  path multiple times.\n* `symlinks` A record of which paths are symbolic links, which is\n  relevant in resolving `**` patterns.\n* `realpathCache` An optional object which is passed to `fs.realpath`\n  to minimize unnecessary syscalls.  It is stored on the instantiated\n  Glob object, and may be re-used.\n\n### Events\n\n* `end` When the matching is finished, this is emitted with all the\n  matches found.  If the `nonull` option is set, and no match was found,\n  then the `matches` list contains the original pattern.  The matches\n  are sorted, unless the `nosort` flag is set.\n* `match` Every time a match is found, this is emitted with the specific\n  thing that matched. It is not deduplicated or resolved to a realpath.\n* `error` Emitted when an unexpected error is encountered, or whenever\n  any fs error occurs if `options.strict` is set.\n* `abort` When `abort()` is called, this event is raised.\n\n### Methods\n\n* `pause` Temporarily stop the search\n* `resume` Resume the search\n* `abort` Stop the search forever\n\n### Options\n\nAll the options that can be passed to Minimatch can also be passed to\nGlob to change pattern matching behavior.  Also, some have been added,\nor have glob-specific ramifications.\n\nAll options are false by default, unless otherwise noted.\n\nAll options are added to the Glob object, as well.\n\nIf you are running many `glob` operations, you can pass a Glob object\nas the `options` argument to a subsequent operation to shortcut some\n`stat` and `readdir` calls.  At the very least, you may pass in shared\n`symlinks`, `statCache`, `realpathCache`, and `cache` options, so that\nparallel glob operations will be sped up by sharing information about\nthe filesystem.\n\n* `cwd` The current working directory in which to search.  Defaults\n  to `process.cwd()`.\n* `root` The place where patterns starting with `/` will be mounted\n  onto.  Defaults to `path.resolve(options.cwd, \"/\")` (`/` on Unix\n  systems, and `C:\\` or some such on Windows.)\n* `dot` Include `.dot` files in normal matches and `globstar` matches.\n  Note that an explicit dot in a portion of the pattern will always\n  match dot files.\n* `nomount` By default, a pattern starting with a forward-slash will be\n  \"mounted\" onto the root setting, so that a valid filesystem path is\n  returned.  Set this flag to disable that behavior.\n* `mark` Add a `/` character to directory matches.  Note that this\n  requires additional stat calls.\n* `nosort` Don't sort the results.\n* `stat` Set to true to stat *all* results.  This reduces performance\n  somewhat, and is completely unnecessary, unless `readdir` is presumed\n  to be an untrustworthy indicator of file existence.\n* `silent` When an unusual error is encountered when attempting to\n  read a directory, a warning will be printed to stderr.  Set the\n  `silent` option to true to suppress these warnings.\n* `strict` When an unusual error is encountered when attempting to\n  read a directory, the process will just continue on in search of\n  other matches.  Set the `strict` option to raise an error in these\n  cases.\n* `cache` See `cache` property above.  Pass in a previously generated\n  cache object to save some fs calls.\n* `statCache` A cache of results of filesystem information, to prevent\n  unnecessary stat calls.  While it should not normally be necessary\n  to set this, you may pass the statCache from one glob() call to the\n  options object of another, if you know that the filesystem will not\n  change between calls.  (See \"Race Conditions\" below.)\n* `symlinks` A cache of known symbolic links.  You may pass in a\n  previously generated `symlinks` object to save `lstat` calls when\n  resolving `**` matches.\n* `sync` DEPRECATED: use `glob.sync(pattern, opts)` instead.\n* `nounique` In some cases, brace-expanded patterns can result in the\n  same file showing up multiple times in the result set.  By default,\n  this implementation prevents duplicates in the result set.  Set this\n  flag to disable that behavior.\n* `nonull` Set to never return an empty set, instead returning a set\n  containing the pattern itself.  This is the default in glob(3).\n* `debug` Set to enable debug logging in minimatch and glob.\n* `nobrace` Do not expand `{a,b}` and `{1..3}` brace sets.\n* `noglobstar` Do not match `**` against multiple filenames.  (Ie,\n  treat it as a normal `*` instead.)\n* `noext` Do not match `+(a|b)` \"extglob\" patterns.\n* `nocase` Perform a case-insensitive match.  Note: on\n  case-insensitive filesystems, non-magic patterns will match by\n  default, since `stat` and `readdir` will not raise errors.\n* `matchBase` Perform a basename-only match if the pattern does not\n  contain any slash characters.  That is, `*.js` would be treated as\n  equivalent to `**/*.js`, matching all js files in all directories.\n* `nodir` Do not match directories, only files.  (Note: to match\n  *only* directories, simply put a `/` at the end of the pattern.)\n* `ignore` Add a pattern or an array of glob patterns to exclude matches.\n  Note: `ignore` patterns are *always* in `dot:true` mode, regardless\n  of any other settings.\n* `follow` Follow symlinked directories when expanding `**` patterns.\n  Note that this can result in a lot of duplicate references in the\n  presence of cyclic links.\n* `realpath` Set to true to call `fs.realpath` on all of the results.\n  In the case of a symlink that cannot be resolved, the full absolute\n  path to the matched entry is returned (though it will usually be a\n  broken symlink)\n* `absolute` Set to true to always receive absolute paths for matched\n  files.  Unlike `realpath`, this also affects the values returned in\n  the `match` event.\n\n## Comparisons to other fnmatch/glob implementations\n\nWhile strict compliance with the existing standards is a worthwhile\ngoal, some discrepancies exist between node-glob and other\nimplementations, and are intentional.\n\nThe double-star character `**` is supported by default, unless the\n`noglobstar` flag is set.  This is supported in the manner of bsdglob\nand bash 4.3, where `**` only has special significance if it is the only\nthing in a path part.  That is, `a/**/b` will match `a/x/y/b`, but\n`a/**b` will not.\n\nNote that symlinked directories are not crawled as part of a `**`,\nthough their contents may match against subsequent portions of the\npattern.  This prevents infinite loops and duplicates and the like.\n\nIf an escaped pattern has no matches, and the `nonull` flag is set,\nthen glob returns the pattern as-provided, rather than\ninterpreting the character escapes.  For example,\n`glob.match([], \"\\\\*a\\\\?\")` will return `\"\\\\*a\\\\?\"` rather than\n`\"*a?\"`.  This is akin to setting the `nullglob` option in bash, except\nthat it does not resolve escaped pattern characters.\n\nIf brace expansion is not disabled, then it is performed before any\nother interpretation of the glob pattern.  Thus, a pattern like\n`+(a|{b),c)}`, which would not be valid in bash or zsh, is expanded\n**first** into the set of `+(a|b)` and `+(a|c)`, and those patterns are\nchecked for validity.  Since those two are valid, matching proceeds.\n\n### Comments and Negation\n\nPreviously, this module let you mark a pattern as a \"comment\" if it\nstarted with a `#` character, or a \"negated\" pattern if it started\nwith a `!` character.\n\nThese options were deprecated in version 5, and removed in version 6.\n\nTo specify things that should not match, use the `ignore` option.\n\n## Windows\n\n**Please only use forward-slashes in glob expressions.**\n\nThough windows uses either `/` or `\\` as its path separator, only `/`\ncharacters are used by this glob implementation.  You must use\nforward-slashes **only** in glob expressions.  Back-slashes will always\nbe interpreted as escape characters, not path separators.\n\nResults from absolute patterns such as `/foo/*` are mounted onto the\nroot setting using `path.join`.  On windows, this will by default result\nin `/foo/*` matching `C:\\foo\\bar.txt`.\n\n## Race Conditions\n\nGlob searching, by its very nature, is susceptible to race conditions,\nsince it relies on directory walking and such.\n\nAs a result, it is possible that a file that exists when glob looks for\nit may have been deleted or modified by the time it returns the result.\n\nAs part of its internal implementation, this program caches all stat\nand readdir calls that it makes, in order to cut down on system\noverhead.  However, this also makes it even more susceptible to races,\nespecially if the cache or statCache objects are reused between glob\ncalls.\n\nUsers are thus advised not to use a glob result as a guarantee of\nfilesystem state in the face of rapid changes.  For the vast majority\nof operations, this is never a problem.\n\n## Glob Logo\nGlob's logo was created by [Tanya Brassie](http://tanyabrassie.com/). Logo files can be found [here](https://github.com/isaacs/node-glob/tree/master/logo).\n\nThe logo is licensed under a [Creative Commons Attribution-ShareAlike 4.0 International License](https://creativecommons.org/licenses/by-sa/4.0/).\n\n## Contributing\n\nAny change to behavior (including bugfixes) must come with a test.\n\nPatches that fail tests or reduce performance will be rejected.\n\n```\n# to run tests\nnpm test\n\n# to re-generate test fixtures\nnpm run test-regen\n\n# to benchmark against bash/zsh\nnpm run bench\n\n# to profile javascript\nnpm run prof\n```\n\n![](oh-my-glob.gif)\n", "licenseText": "The ISC License\n\nCopyright (c) <PERSON> and Contributors\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n\n## Glob Logo\n\nGlob's logo created by <PERSON> <http://tanyabrassie.com/>, licensed\nunder a Creative Commons Attribution-ShareAlike 4.0 International License\nhttps://creativecommons.org/licenses/by-sa/4.0/\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/glob/-/glob-7.1.6.tgz#141f33b81a7c2492e125594307480c46679278a6", "type": "tarball", "reference": "https://registry.yarnpkg.com/glob/-/glob-7.1.6.tgz", "hash": "141f33b81a7c2492e125594307480c46679278a6", "integrity": "sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA==", "registry": "npm", "packageName": "glob", "cacheIntegrity": "sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA== sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY="}, "registry": "npm", "hash": "141f33b81a7c2492e125594307480c46679278a6"}