{"name": "@types/qs", "version": "6.9.5", "description": "TypeScript definitions for qs", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/RWander", "githubUsername": "R<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/leonyu", "githubUsername": "leonyu"}, {"name": "<PERSON>", "url": "https://github.com/tehbelinda", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/zyml", "githubUsername": "zyml"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/artursvonda", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dpsmith3", "githubUsername": "dpsmith3"}, {"name": "<PERSON>", "url": "https://github.com/hperrin", "githubUsername": "<PERSON><PERSON>rin"}, {"name": "<PERSON>", "url": "https://github.com/ljharb", "githubUsername": "lj<PERSON>b"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/qs"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "1a8820a6aece2344fa333148c105b71a132db5e68f839c47934a78889cd44574", "typeScriptVersion": "3.2"}