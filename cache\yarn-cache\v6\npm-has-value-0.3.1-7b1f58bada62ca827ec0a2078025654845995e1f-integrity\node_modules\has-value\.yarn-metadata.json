{"manifest": {"name": "has-value", "version": "0.3.1", "description": "Returns true if a value exists, false if empty. Works with deeply nested values using object paths.", "homepage": "https://github.com/jonschlinkert/has-value", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/has-value.git"}, "bugs": {"url": "https://github.com/jonschlinkert/has-value/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "devDependencies": {"gulp-format-md": "^0.1.7", "mocha": "^2.4.5"}, "keywords": ["array", "boolean", "empty", "find", "function", "has", "hasOwn", "javascript", "js", "key", "keys", "node.js", "null", "number", "object", "properties", "property", "string", "type", "util", "utilities", "utility", "value"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["get-object", "get-property", "get-value", "set-value"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-has-value-0.3.1-7b1f58bada62ca827ec0a2078025654845995e1f-integrity\\node_modules\\has-value\\package.json", "readmeFilename": "README.md", "readme": "# has-value [![NPM version](https://img.shields.io/npm/v/has-value.svg?style=flat)](https://www.npmjs.com/package/has-value) [![NPM downloads](https://img.shields.io/npm/dm/has-value.svg?style=flat)](https://npmjs.org/package/has-value) [![Build Status](https://img.shields.io/travis/jonschlinkert/has-value.svg?style=flat)](https://travis-ci.org/jonschlinkert/has-value)\n\n> Returns true if a value exists, false if empty. Works with deeply nested values using object paths.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install has-value --save\n```\n\n**Works for:**\n\n* booleans\n* functions\n* numbers (pass `true` as the last arg to treat zero as a value instead of falsey)\n* strings\n* nulls\n* object\n* arrays\n\n## Usage\n\nWorks with nested object paths or a single value:\n\n```js\nvar hasValue = require('has-value');\n\nhasValue({a: {b: {c: 'foo'}}} 'a.b.c');\n//=> true\n\nhasValue('a');\n//=> true\n\nhasValue('');\n//=> false\n\nhasValue(1);\n//=> true\n\nhasValue(0);\n//=> false\n\nhasValue(0, true); // pass `true` as the last arg to treat zero as a value\n//=> true\n\nhasValue({a: 'a'}});\n//=> true\n\nhasValue({}});\n//=> false\n\nhasValue(['a']);\n//=> true\n\nhasValue([]);\n//=> false\n\nhasValue(function(foo) {}); // function length/arity\n//=> true\n\nhasValue(function() {});\n//=> false\n\nhasValue(true);\nhasValue(false);\n//=> true\n```\n\n## isEmpty\n\nTo do the opposite and test for empty values, do:\n\n```js\nfunction isEmpty(o, isZero) {\n  return !hasValue.apply(hasValue, arguments);\n}\n```\n\n## Related projects\n\nYou might also be interested in these projects:\n\n* [get-object](https://www.npmjs.com/package/get-object): Get a property from an object using dot (object path) notation. | [homepage](https://github.com/jonschlinkert/get-object)\n* [get-property](https://www.npmjs.com/package/get-property): Get a nested property or its value from an object using simple `a.b.c` paths. | [homepage](https://github.com/jonschlinkert/get-property)\n* [get-value](https://www.npmjs.com/package/get-value): Use property paths (`a.b.c`) to get a nested value from an object. | [homepage](https://github.com/jonschlinkert/get-value)\n* [set-value](https://www.npmjs.com/package/set-value): Create nested values and any intermediaries using dot notation (`'a.b.c'`) paths. | [homepage](https://github.com/jonschlinkert/set-value)\n\n## Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/has-value/issues/new).\n\n## Building docs\n\nGenerate readme and API documentation with [verb](https://github.com/verbose/verb):\n\n```sh\n$ npm install verb && npm run docs\n```\n\nOr, if [verb](https://github.com/verbose/verb) is installed globally:\n\n```sh\n$ verb\n```\n\n## Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm install -d && npm test\n```\n\n## Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n## License\n\nCopyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT license](https://github.com/jonschlinkert/has-value/blob/master/LICENSE).\n\n***\n\n_This file was generated by [verb](https://github.com/verbose/verb), v, on March 27, 2016._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2016, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/has-value/-/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f", "type": "tarball", "reference": "https://registry.yarnpkg.com/has-value/-/has-value-0.3.1.tgz", "hash": "7b1f58bada62ca827ec0a2078025654845995e1f", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "registry": "npm", "packageName": "has-value", "cacheIntegrity": "sha512-gpG936j8/MzaeID5Yif+577c17TxaDmhuyVgSwtnL/q8UUTySg8Mecb+8Cf1otgLoD7DDH75axp86ER7LFsf3Q== sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8="}, "registry": "npm", "hash": "7b1f58bada62ca827ec0a2078025654845995e1f"}