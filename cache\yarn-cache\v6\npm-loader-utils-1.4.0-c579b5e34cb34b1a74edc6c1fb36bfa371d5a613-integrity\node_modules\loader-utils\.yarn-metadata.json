{"manifest": {"name": "loader-utils", "version": "1.4.0", "author": {"name": "<PERSON> @sokra"}, "description": "utils for webpack loaders", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^1.0.1"}, "scripts": {"lint": "eslint lib test", "pretest": "yarn lint", "test": "jest", "test:ci": "jest --coverage", "release": "yarn test && standard-version"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/webpack/loader-utils.git"}, "engines": {"node": ">=4.0.0"}, "devDependencies": {"coveralls": "^3.0.2", "eslint": "^5.11.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-prettier": "^3.0.0", "jest": "^21.2.1", "prettier": "^1.19.1", "standard-version": "^4.0.0"}, "main": "lib/index.js", "files": ["lib"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-loader-utils-1.4.0-c579b5e34cb34b1a74edc6c1fb36bfa371d5a613-integrity\\node_modules\\loader-utils\\package.json", "readmeFilename": "README.md", "readme": "# loader-utils\n\n## Methods\n\n### `getOptions`\n\nRecommended way to retrieve the options of a loader invocation:\n\n```javascript\n// inside your loader\nconst options = loaderUtils.getOptions(this);\n```\n\n1. If `this.query` is a string:\n\t- Tries to parse the query string and returns a new object\n\t- Throws if it's not a valid query string\n2. If `this.query` is object-like, it just returns `this.query`\n3. In any other case, it just returns `null`\n\n**Please note:** The returned `options` object is *read-only*. It may be re-used across multiple invocations.\nIf you pass it on to another library, make sure to make a *deep copy* of it:\n\n```javascript\nconst options = Object.assign(\n\t{},\n\tdefaultOptions,\n\tloaderUtils.getOptions(this) // it is safe to pass null to Object.assign()\n);\n// don't forget nested objects or arrays\noptions.obj = Object.assign({}, options.obj); \noptions.arr = options.arr.slice();\nsomeLibrary(options);\n```\n\n[clone](https://www.npmjs.com/package/clone) is a good library to make a deep copy of the options.\n\n#### Options as query strings\n\nIf the loader options have been passed as loader query string (`loader?some&params`), the string is parsed by using [`parseQuery`](#parsequery).\n\n### `parseQuery`\n\nParses a passed string (e.g. `loaderContext.resourceQuery`) as a query string, and returns an object.\n\n``` javascript\nconst params = loaderUtils.parseQuery(this.resourceQuery); // resource: `file?param1=foo`\nif (params.param1 === \"foo\") {\n\t// do something\n}\n```\n\nThe string is parsed like this:\n\n``` text\n                             -> Error\n?                            -> {}\n?flag                        -> { flag: true }\n?+flag                       -> { flag: true }\n?-flag                       -> { flag: false }\n?xyz=test                    -> { xyz: \"test\" }\n?xyz=1                       -> { xyz: \"1\" } // numbers are NOT parsed\n?xyz[]=a                     -> { xyz: [\"a\"] }\n?flag1&flag2                 -> { flag1: true, flag2: true }\n?+flag1,-flag2               -> { flag1: true, flag2: false }\n?xyz[]=a,xyz[]=b             -> { xyz: [\"a\", \"b\"] }\n?a%2C%26b=c%2C%26d           -> { \"a,&b\": \"c,&d\" }\n?{data:{a:1},isJSON5:true}   -> { data: { a: 1 }, isJSON5: true }\n```\n\n### `stringifyRequest`\n\nTurns a request into a string that can be used inside `require()` or `import` while avoiding absolute paths.\nUse it instead of `JSON.stringify(...)` if you're generating code inside a loader.\n\n**Why is this necessary?** Since webpack calculates the hash before module paths are translated into module ids, we must avoid absolute paths to ensure\nconsistent hashes across different compilations.\n\nThis function:\n\n- resolves absolute requests into relative requests if the request and the module are on the same hard drive\n- replaces `\\` with `/` if the request and the module are on the same hard drive\n- won't change the path at all if the request and the module are on different hard drives\n- applies `JSON.stringify` to the result\n\n```javascript\nloaderUtils.stringifyRequest(this, \"./test.js\");\n// \"\\\"./test.js\\\"\"\n\nloaderUtils.stringifyRequest(this, \".\\\\test.js\");\n// \"\\\"./test.js\\\"\"\n\nloaderUtils.stringifyRequest(this, \"test\");\n// \"\\\"test\\\"\"\n\nloaderUtils.stringifyRequest(this, \"test/lib/index.js\");\n// \"\\\"test/lib/index.js\\\"\"\n\nloaderUtils.stringifyRequest(this, \"otherLoader?andConfig!test?someConfig\");\n// \"\\\"otherLoader?andConfig!test?someConfig\\\"\"\n\nloaderUtils.stringifyRequest(this, require.resolve(\"test\"));\n// \"\\\"../node_modules/some-loader/lib/test.js\\\"\"\n\nloaderUtils.stringifyRequest(this, \"C:\\\\module\\\\test.js\");\n// \"\\\"../../test.js\\\"\" (on Windows, in case the module and the request are on the same drive)\n\nloaderUtils.stringifyRequest(this, \"C:\\\\module\\\\test.js\");\n// \"\\\"C:\\\\module\\\\test.js\\\"\" (on Windows, in case the module and the request are on different drives)\n\nloaderUtils.stringifyRequest(this, \"\\\\\\\\network-drive\\\\test.js\");\n// \"\\\"\\\\\\\\network-drive\\\\\\\\test.js\\\"\" (on Windows, in case the module and the request are on different drives)\n```\n\n### `urlToRequest`\n\nConverts some resource URL to a webpack module request.\n\n> i Before call `urlToRequest` you need call `isUrlRequest` to ensure it is requestable url\n\n```javascript\nconst url = \"path/to/module.js\";\n\nif (loaderUtils.isUrlRequest(url)) {\n  // Logic for requestable url\n  const request = loaderUtils.urlToRequest(url);\n} else {\n  // Logic for not requestable url\n}\n```\n\nSimple example:\n\n```javascript\nconst url = \"path/to/module.js\";\nconst request = loaderUtils.urlToRequest(url); // \"./path/to/module.js\"\n```\n\n#### Module URLs\n\nAny URL containing a `~` will be interpreted as a module request. Anything after the `~` will be considered the request path.\n\n```javascript\nconst url = \"~path/to/module.js\";\nconst request = loaderUtils.urlToRequest(url); // \"path/to/module.js\"\n```\n\n#### Root-relative URLs\n\nURLs that are root-relative (start with `/`) can be resolved relative to some arbitrary path by using the `root` parameter:\n\n```javascript\nconst url = \"/path/to/module.js\";\nconst root = \"./root\";\nconst request = loaderUtils.urlToRequest(url, root); // \"./root/path/to/module.js\"\n```\n\nTo convert a root-relative URL into a module URL, specify a `root` value that starts with `~`:\n\n```javascript\nconst url = \"/path/to/module.js\";\nconst root = \"~\";\nconst request = loaderUtils.urlToRequest(url, root); // \"path/to/module.js\"\n```\n\n### `interpolateName`\n\nInterpolates a filename template using multiple placeholders and/or a regular expression.\nThe template and regular expression are set as query params called `name` and `regExp` on the current loader's context.\n\n```javascript\nconst interpolatedName = loaderUtils.interpolateName(loaderContext, name, options);\n```\n\nThe following tokens are replaced in the `name` parameter:\n\n* `[ext]` the extension of the resource\n* `[name]` the basename of the resource\n* `[path]` the path of the resource relative to the `context` query parameter or option.\n* `[folder]` the folder the resource is in\n* `[query]` the queryof the resource, i.e. `?foo=bar`\n* `[emoji]` a random emoji representation of `options.content`\n* `[emoji:<length>]` same as above, but with a customizable number of emojis\n* `[contenthash]` the hash of `options.content` (Buffer) (by default it's the hex digest of the md5 hash)\n* `[<hashType>:contenthash:<digestType>:<length>]` optionally one can configure\n  * other `hashType`s, i. e. `sha1`, `md5`, `sha256`, `sha512`\n  * other `digestType`s, i. e. `hex`, `base26`, `base32`, `base36`, `base49`, `base52`, `base58`, `base62`, `base64`\n  * and `length` the length in chars\n* `[hash]` the hash of `options.content` (Buffer) (by default it's the hex digest of the md5 hash)\n* `[<hashType>:hash:<digestType>:<length>]` optionally one can configure\n  * other `hashType`s, i. e. `sha1`, `md5`, `sha256`, `sha512`\n  * other `digestType`s, i. e. `hex`, `base26`, `base32`, `base36`, `base49`, `base52`, `base58`, `base62`, `base64`\n  * and `length` the length in chars\n* `[N]` the N-th match obtained from matching the current file name against `options.regExp`\n\nIn loader context `[hash]` and `[contenthash]` are the same, but we recommend using `[contenthash]` for avoid misleading.\n\nExamples\n\n``` javascript\n// loaderContext.resourcePath = \"/absolute/path/to/app/js/javascript.js\"\nloaderUtils.interpolateName(loaderContext, \"js/[hash].script.[ext]\", { content: ... });\n// => js/9473fdd0d880a43c21b7778d34872157.script.js\n\n// loaderContext.resourcePath = \"/absolute/path/to/app/js/javascript.js\"\n// loaderContext.resourceQuery = \"?foo=bar\"\nloaderUtils.interpolateName(loaderContext, \"js/[hash].script.[ext][query]\", { content: ... });\n// => js/9473fdd0d880a43c21b7778d34872157.script.js?foo=bar\n\n// loaderContext.resourcePath = \"/absolute/path/to/app/js/javascript.js\"\nloaderUtils.interpolateName(loaderContext, \"js/[contenthash].script.[ext]\", { content: ... });\n// => js/9473fdd0d880a43c21b7778d34872157.script.js\n\n// loaderContext.resourcePath = \"/absolute/path/to/app/page.html\"\nloaderUtils.interpolateName(loaderContext, \"html-[hash:6].html\", { content: ... });\n// => html-9473fd.html\n\n// loaderContext.resourcePath = \"/absolute/path/to/app/flash.txt\"\nloaderUtils.interpolateName(loaderContext, \"[hash]\", { content: ... });\n// => c31e9820c001c9c4a86bce33ce43b679\n\n// loaderContext.resourcePath = \"/absolute/path/to/app/img/image.gif\"\nloaderUtils.interpolateName(loaderContext, \"[emoji]\", { content: ... });\n// => 👍\n\n// loaderContext.resourcePath = \"/absolute/path/to/app/img/image.gif\"\nloaderUtils.interpolateName(loaderContext, \"[emoji:4]\", { content: ... });\n// => 🙍🏢📤🐝\n\n// loaderContext.resourcePath = \"/absolute/path/to/app/img/image.png\"\nloaderUtils.interpolateName(loaderContext, \"[sha512:hash:base64:7].[ext]\", { content: ... });\n// => 2BKDTjl.png\n// use sha512 hash instead of md5 and with only 7 chars of base64\n\n// loaderContext.resourcePath = \"/absolute/path/to/app/img/myself.png\"\n// loaderContext.query.name =\nloaderUtils.interpolateName(loaderContext, \"picture.png\");\n// => picture.png\n\n// loaderContext.resourcePath = \"/absolute/path/to/app/dir/file.png\"\nloaderUtils.interpolateName(loaderContext, \"[path][name].[ext]?[hash]\", { content: ... });\n// => /app/dir/file.png?9473fdd0d880a43c21b7778d34872157\n\n// loaderContext.resourcePath = \"/absolute/path/to/app/js/page-home.js\"\nloaderUtils.interpolateName(loaderContext, \"script-[1].[ext]\", { regExp: \"page-(.*)\\\\.js\", content: ... });\n// => script-home.js\n\n// loaderContext.resourcePath = \"/absolute/path/to/app/js/javascript.js\"\n// loaderContext.resourceQuery = \"?foo=bar\"\nloaderUtils.interpolateName(\n  loaderContext, \n  (resourcePath, resourceQuery) => { \n    // resourcePath - `/app/js/javascript.js`\n    // resourceQuery - `?foo=bar`\n\n    return \"js/[hash].script.[ext]\"; \n  }, \n  { content: ... }\n);\n// => js/9473fdd0d880a43c21b7778d34872157.script.js\n```\n\n### `getHashDigest`\n\n``` javascript\nconst digestString = loaderUtils.getHashDigest(buffer, hashType, digestType, maxLength);\n```\n\n* `buffer` the content that should be hashed\n* `hashType` one of `sha1`, `md5`, `sha256`, `sha512` or any other node.js supported hash type\n* `digestType` one of `hex`, `base26`, `base32`, `base36`, `base49`, `base52`, `base58`, `base62`, `base64`\n* `maxLength` the maximum length in chars\n\n## License\n\nMIT (http://www.opensource.org/licenses/mit-license.php)\n", "licenseText": "Copyright JS Foundation and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/loader-utils/-/loader-utils-1.4.0.tgz#c579b5e34cb34b1a74edc6c1fb36bfa371d5a613", "type": "tarball", "reference": "https://registry.yarnpkg.com/loader-utils/-/loader-utils-1.4.0.tgz", "hash": "c579b5e34cb34b1a74edc6c1fb36bfa371d5a613", "integrity": "sha512-qH0WSMBtn/oHuwjy/NucEgbx5dbxxnxup9s4PVXJUDHZBQY+s0NWA9rJf53RBnQZxfch7euUui7hpoAPvALZdA==", "registry": "npm", "packageName": "loader-utils", "cacheIntegrity": "sha512-qH0WSMBtn/oHuwjy/NucEgbx5dbxxnxup9s4PVXJUDHZBQY+s0NWA9rJf53RBnQZxfch7euUui7hpoAPvALZdA== sha1-xXm140yzSxp07cbB+za/o3HVphM="}, "registry": "npm", "hash": "c579b5e34cb34b1a74edc6c1fb36bfa371d5a613"}