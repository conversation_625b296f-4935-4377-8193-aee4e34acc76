{"manifest": {"name": "json5", "version": "0.5.1", "description": "JSON for the ES5 era.", "keywords": ["json", "es5"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "main": "lib/json5.js", "bin": {"json5": "lib\\cli.js"}, "files": ["lib/"], "dependencies": {}, "devDependencies": {"gulp": "^3.9.1", "gulp-jshint": "^2.0.1", "jshint": "^2.9.3", "jshint-stylish": "^2.2.1", "mocha": "^3.1.0"}, "scripts": {"build": "node ./lib/cli.js -c package.json5", "test": "mocha --ui exports --reporter spec"}, "homepage": "http://json5.org/", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/aseemk/json5.git"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-json5-0.5.1-1eade7acc012034ad84e2396767ead9fa5495821-integrity\\node_modules\\json5\\package.json", "readmeFilename": "README.md", "readme": "# JSON5 – Modern JSON\n\n[![Build Status](https://travis-ci.org/json5/json5.svg)](https://travis-ci.org/json5/json5)\n\nJSON is an excellent data format, but we think it can be better.\n\n**JSON5 is a proposed extension to JSON** that aims to make it easier for\n*humans to write and maintain* by hand. It does this by adding some minimal\nsyntax features directly from ECMAScript 5.\n\nJSON5 remains a **strict subset of JavaScript**, adds **no new data types**,\nand **works with all existing JSON content**.\n\nJSON5 is *not* an official successor to JSON, and JSON5 content may *not*\nwork with existing JSON parsers. For this reason, JSON5 files use a new .json5\nextension. *(TODO: new MIME type needed too.)*\n\nThe code here is a **reference JavaScript implementation** for both Node.js\nand all browsers. It’s based directly off of <PERSON>’s own [JSON\nimplementation][json_parse.js], and it’s both robust and secure.\n\n\n## Why\n\nJSON isn’t the friendliest to *write*. Keys need to be quoted, objects and\narrays can’t have trailing commas, and comments aren’t allowed — even though\nnone of these are the case with regular JavaScript today.\n\nThat was fine when JSON’s goal was to be a great data format, but JSON’s usage\nhas expanded beyond *machines*. JSON is now used for writing [configs][ex1],\n[manifests][ex2], even [tests][ex3] — all by *humans*.\n\n[ex1]: http://plovr.com/docs.html\n[ex2]: https://www.npmjs.org/doc/files/package.json.html\n[ex3]: http://code.google.com/p/fuzztester/wiki/JSONFileFormat\n\nThere are other formats that are human-friendlier, like YAML, but changing\nfrom JSON to a completely different format is undesirable in many cases.\nJSON5’s aim is to remain close to JSON and JavaScript.\n\n\n## Features\n\nThe following is the exact list of additions to JSON’s syntax introduced by\nJSON5. **All of these are optional**, and **all of these come from ES5**.\n\n### Objects\n\n- Object keys can be unquoted if they’re valid [identifiers][mdn_variables].\n  Yes, even reserved keywords (like `default`) are valid unquoted keys in ES5\n  [[§11.1.5](http://es5.github.com/#x11.1.5), [§7.6](http://es5.github.com/#x7.6)].\n  ([More info](https://mathiasbynens.be/notes/javascript-identifiers))\n\n  *(TODO: Unicode characters and escape sequences aren’t yet supported in this\n  implementation.)*\n  \n- Object keys can also be single-quoted.\n\n- Objects can have trailing commas.\n\n[mdn_variables]: https://developer.mozilla.org/en/Core_JavaScript_1.5_Guide/Core_Language_Features#Variables\n\n### Arrays\n\n- Arrays can have trailing commas.\n\n### Strings\n\n- Strings can be single-quoted.\n\n- Strings can be split across multiple lines; just prefix each newline with a\n  backslash. [ES5 [§7.8.4](http://es5.github.com/#x7.8.4)]\n\n### Numbers\n\n- Numbers can be hexadecimal (base 16).\n\n- Numbers can begin or end with a (leading or trailing) decimal point.\n\n- Numbers can include `Infinity`, `-Infinity`,  `NaN`, and `-NaN`.\n\n- Numbers can begin with an explicit plus sign.\n\n### Comments\n\n- Both inline (single-line) and block (multi-line) comments are allowed.\n\n\n## Example\n\nThe following is a contrived example, but it illustrates most of the features:\n\n```js\n{\n    foo: 'bar',\n    while: true,\n\n    this: 'is a \\\nmulti-line string',\n\n    // this is an inline comment\n    here: 'is another', // inline comment\n\n    /* this is a block comment\n       that continues on another line */\n\n    hex: 0xDEADbeef,\n    half: .5,\n    delta: +10,\n    to: Infinity,   // and beyond!\n\n    finally: 'a trailing comma',\n    oh: [\n        \"we shouldn't forget\",\n        'arrays can have',\n        'trailing commas too',\n    ],\n}\n```\n\nThis implementation’s own [package.json5](package.json5) is more realistic:\n\n```js\n// This file is written in JSON5 syntax, naturally, but npm needs a regular\n// JSON file, so compile via `npm run build`. Be sure to keep both in sync!\n\n{\n    name: 'json5',\n    version: '0.5.0',\n    description: 'JSON for the ES5 era.',\n    keywords: ['json', 'es5'],\n    author: 'Aseem Kishore <<EMAIL>>',\n    contributors: [\n        // TODO: Should we remove this section in favor of GitHub's list?\n        // https://github.com/aseemk/json5/contributors\n        'Max Nanasy <<EMAIL>>',\n        'Andrew Eisenberg <<EMAIL>>',\n        'Jordan Tucker <<EMAIL>>',\n    ],\n    main: 'lib/json5.js',\n    bin: 'lib/cli.js',\n    files: [\"lib/\"],\n    dependencies: {},\n    devDependencies: {\n        gulp: \"^3.9.1\",\n        'gulp-jshint': \"^2.0.0\",\n        jshint: \"^2.9.1\",\n        'jshint-stylish': \"^2.1.0\",\n        mocha: \"^2.4.5\"\n    },\n    scripts: {\n        build: 'node ./lib/cli.js -c package.json5',\n        test: 'mocha --ui exports --reporter spec',\n            // TODO: Would it be better to define these in a mocha.opts file?\n    },\n    homepage: 'http://json5.org/',\n    license: 'MIT',\n    repository: {\n        type: 'git',\n        url: 'https://github.com/aseemk/json5.git',\n    },\n}\n```\n\n\n## Community\n\nJoin the [Google Group](http://groups.google.com/group/json5) if you’re\ninterested in JSON5 news, updates, and general discussion.\nDon’t worry, it’s very low-traffic.\n\nThe [GitHub wiki](https://github.com/aseemk/json5/wiki) is a good place to track\nJSON5 support and usage. Contribute freely there!\n\n[GitHub Issues](https://github.com/aseemk/json5/issues) is the place to\nformally propose feature requests and report bugs. Questions and general\nfeedback are better directed at the Google Group.\n\n\n## Usage\n\nThis JavaScript implementation of JSON5 simply provides a `JSON5` object just\nlike the native ES5 `JSON` object.\n\nTo use from Node:\n\n```sh\nnpm install json5\n```\n\n```js\nvar JSON5 = require('json5');\n```\n\nTo use in the browser (adds the `JSON5` object to the global namespace):\n\n```html\n<script src=\"json5.js\"></script>\n```\n\nThen in both cases, you can simply replace native `JSON` calls with `JSON5`:\n\n```js\nvar obj = JSON5.parse('{unquoted:\"key\",trailing:\"comma\",}');\nvar str = JSON5.stringify(obj);\n```\n\n`JSON5.parse` supports all of the JSON5 features listed above (*TODO: except\nUnicode*), as well as the native [`reviver` argument][json-parse].\n\n[json-parse]: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse\n\n`JSON5.stringify` mainly avoids quoting keys where possible, but we hope to\nkeep expanding it in the future (e.g. to also output trailing commas).\nIt supports the native [`replacer` and `space` arguments][json-stringify],\nas well. *(TODO: Any implemented `toJSON` methods aren’t used today.)*\n\n[json-stringify]: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify\n\n\n### Extras\n\nIf you’re running this on Node, you can also register a JSON5 `require()` hook\nto let you `require()` `.json5` files just like you can `.json` files:\n\n```js\nrequire('json5/lib/require');\nrequire('./path/to/foo');   // tries foo.json5 after foo.js, foo.json, etc.\nrequire('./path/to/bar.json5');\n```\n\nThis module also provides a `json5` executable (requires Node) for converting\nJSON5 files to JSON:\n\n```sh\njson5 -c path/to/foo.json5    # generates path/to/foo.json\n```\n\n\n## Development\n\n```sh\ngit clone git://github.com/aseemk/json5.git\ncd json5\nnpm install\nnpm test\n```\n\nAs the `package.json5` file states, be sure to run `npm run build` on changes\nto `package.json5`, since npm requires `package.json`.\n\nFeel free to [file issues](https://github.com/aseemk/json5/issues) and submit\n[pull requests](https://github.com/aseemk/json5/pulls) — contributions are\nwelcome. If you do submit a pull request, please be sure to add or update the\ntests, and ensure that `npm test` continues to pass.\n\n\n## License\n\nMIT. See [LICENSE.md](./LICENSE.md) for details.\n\n\n## Credits\n\n[Michael Bolin](http://bolinfest.com/) independently arrived at and published\nsome of these same ideas with awesome explanations and detail.\nRecommended reading:\n[Suggested Improvements to JSON](http://bolinfest.com/essays/json.html)\n\n[Douglas Crockford](http://www.crockford.com/) of course designed and built\nJSON, but his state machine diagrams on the [JSON website](http://json.org/),\nas cheesy as it may sound, gave me motivation and confidence that building a\nnew parser to implement these ideas this was within my reach!\nThis code is also modeled directly off of Doug’s open-source [json_parse.js][]\nparser. I’m super grateful for that clean and well-documented code.\n\n[json_parse.js]: https://github.com/douglascrockford/JSON-js/blob/master/json_parse.js\n\n[Max Nanasy](https://github.com/MaxNanasy) has been an early and prolific\nsupporter, contributing multiple patches and ideas. Thanks Max!\n\n[Andrew Eisenberg](https://github.com/aeisenberg) has contributed the\n`stringify` method.\n\n[Jordan Tucker](https://github.com/jordanbtucker) has aligned JSON5 more closely\nwith ES5 and is actively maintaining this project.\n", "licenseText": "MIT License\n\nCopyright (c) 2012-2016 <PERSON><PERSON><PERSON>, and [others](https://github.com/aseemk/json5/contributors).\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/json5/-/json5-0.5.1.tgz#1eade7acc012034ad84e2396767ead9fa5495821", "type": "tarball", "reference": "https://registry.yarnpkg.com/json5/-/json5-0.5.1.tgz", "hash": "1eade7acc012034ad84e2396767ead9fa5495821", "integrity": "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=", "registry": "npm", "packageName": "json5", "cacheIntegrity": "sha512-4xrs1aW+6N5DalkqSVA8fxh458CXvR99WU8WLKmq4v8eWAL86Xo3BVqyd3SkA9wEVjCMqyvvRRkshAdOnBp5rw== sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE="}, "registry": "npm", "hash": "1eade7acc012034ad84e2396767ead9fa5495821"}