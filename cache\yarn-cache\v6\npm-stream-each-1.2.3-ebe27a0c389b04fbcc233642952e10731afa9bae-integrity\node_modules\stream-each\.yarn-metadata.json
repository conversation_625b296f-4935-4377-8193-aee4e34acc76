{"manifest": {"name": "stream-each", "version": "1.2.3", "description": "Iterate all the data in a stream", "main": "index.js", "dependencies": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}, "devDependencies": {"ndjson": "^1.5.0", "standard": "^5.3.1", "tape": "^4.2.1", "through2": "^2.0.0"}, "scripts": {"test": "standard && tape test.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/stream-each.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/stream-each/issues"}, "homepage": "https://github.com/mafintosh/stream-each", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-stream-each-1.2.3-ebe27a0c389b04fbcc233642952e10731afa9bae-integrity\\node_modules\\stream-each\\package.json", "readmeFilename": "README.md", "readme": "# stream-each\n\nIterate all the data in a stream\n\n```\nnpm install stream-each\n```\n\n[![build status](http://img.shields.io/travis/mafintosh/stream-each.svg?style=flat)](http://travis-ci.org/mafintosh/stream-each)\n\n## Usage\n\n``` js\nvar each = require('stream-each')\n\neach(stream, function (data, next) {\n  console.log('data from stream', data)\n  // when ready to consume next chunk\n  next()\n}, function (err) {\n  console.log('no more data')\n})\n```\n\n## API\n\n#### `each(stream, iterator, cb)`\n\nIterate the data in the stream by calling the iterator function with `(data, next)`\nwhere data is a data chunk and next is a callback. Call next when you are ready to\nconsume the next chunk. Optionally you can call next with an error to destroy the stream\n\nWhen the stream ends/errors the callback is called if provided\n\n## License\n\nMIT\n\n## Related\n\n`stream-each` is part of the [mississippi stream utility collection](https://github.com/maxogden/mississippi) which includes more useful stream modules similar to this one.\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015 Mathias Buus\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/stream-each/-/stream-each-1.2.3.tgz#ebe27a0c389b04fbcc233642952e10731afa9bae", "type": "tarball", "reference": "https://registry.yarnpkg.com/stream-each/-/stream-each-1.2.3.tgz", "hash": "ebe27a0c389b04fbcc233642952e10731afa9bae", "integrity": "sha512-vlMC2f8I2u/bZGqkdfLQW/13Zihpej/7PmSiMQsbYddxuTsJp8vRe2x2FvVExZg7FaOds43ROAuFJwPR4MTZLw==", "registry": "npm", "packageName": "stream-each", "cacheIntegrity": "sha512-vlMC2f8I2u/bZGqkdfLQW/13Zihpej/7PmSiMQsbYddxuTsJp8vRe2x2FvVExZg7FaOds43ROAuFJwPR4MTZLw== sha1-6+J6DDibBPvMIzZClS4Qcxr6m64="}, "registry": "npm", "hash": "ebe27a0c389b04fbcc233642952e10731afa9bae"}