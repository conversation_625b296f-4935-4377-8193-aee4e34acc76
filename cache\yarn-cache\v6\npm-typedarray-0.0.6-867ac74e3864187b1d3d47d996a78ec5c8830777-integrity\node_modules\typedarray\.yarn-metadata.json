{"manifest": {"name": "typedarray", "version": "0.0.6", "description": "TypedArray polyfill for old browsers", "main": "index.js", "devDependencies": {"tape": "~2.3.2"}, "scripts": {"test": "tape test/*.js test/server/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/typedarray.git"}, "homepage": "https://github.com/substack/typedarray", "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "typed", "array", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-typedarray-0.0.6-867ac74e3864187b1d3d47d996a78ec5c8830777-integrity\\node_modules\\typedarray\\package.json", "readmeFilename": "readme.markdown", "readme": "# typedarray\n\nTypedArray polyfill ripped from [this\nmodule](https://raw.github.com/inexorabletash/polyfill).\n\n[![build status](https://secure.travis-ci.org/substack/typedarray.png)](http://travis-ci.org/substack/typedarray)\n\n[![testling badge](https://ci.testling.com/substack/typedarray.png)](https://ci.testling.com/substack/typedarray)\n\n# example\n\n``` js\nvar Uint8Array = require('typedarray').Uint8Array;\nvar ua = new Uint8Array(5);\nua[1] = 256 + 55;\nconsole.log(ua[1]);\n```\n\noutput:\n\n```\n55\n```\n\n# methods\n\n``` js\nvar TA = require('typedarray')\n```\n\nThe `TA` object has the following constructors:\n\n* TA.ArrayBuffer\n* TA.DataView\n* TA.Float32Array\n* TA.Float64Array\n* TA.Int8Array\n* TA.Int16Array\n* TA.Int32Array\n* TA.Uint8Array\n* TA.Uint8ClampedArray\n* TA.Uint16Array\n* TA.Uint32Array\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install typedarray\n```\n\nTo use this module in the browser, compile with\n[browserify](http://browserify.org)\nor download a UMD build from browserify CDN:\n\nhttp://wzrd.in/standalone/typedarray@latest\n\n# license\n\nMIT\n", "licenseText": "/*\n Copyright (c) 2010, Linden Research, Inc.\n Copyright (c) 2012, <PERSON>\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to deal\n in the Software without restriction, including without limitation the rights\n to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n copies of the Software, and to permit persons to whom the Software is\n furnished to do so, subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n THE SOFTWARE.\n $/LicenseInfo$\n */\n\n// Original can be found at:\n//   https://bitbucket.org/lindenlab/llsd\n// Modifications by <NAME_EMAIL>\n//   https://github.com/inexorabletash/polyfill\n\n// ES3/ES5 implementation of the Krhonos Typed Array Specification\n//   Ref: http://www.khronos.org/registry/typedarray/specs/latest/\n//   Date: 2011-02-01\n//\n// Variations:\n//  * Allows typed_array.get/set() as alias for subscripts (typed_array[])\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/typedarray/-/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777", "type": "tarball", "reference": "https://registry.yarnpkg.com/typedarray/-/typedarray-0.0.6.tgz", "hash": "867ac74e3864187b1d3d47d996a78ec5c8830777", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=", "registry": "npm", "packageName": "typedarray", "cacheIntegrity": "sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA== sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c="}, "registry": "npm", "hash": "867ac74e3864187b1d3d47d996a78ec5c8830777"}