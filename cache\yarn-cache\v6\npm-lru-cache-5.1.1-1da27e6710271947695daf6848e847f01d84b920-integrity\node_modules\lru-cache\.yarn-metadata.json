{"manifest": {"name": "lru-cache", "description": "A cache object that deletes the least-recently-used items.", "version": "5.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["mru", "lru", "cache"], "scripts": {"test": "tap test/*.js --100 -J", "snap": "TAP_SNAPSHOT=1 tap test/*.js -J", "coveragerport": "tap --coverage-report=html", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "main": "index.js", "repository": {"type": "git", "url": "git://github.com/isaacs/node-lru-cache.git"}, "devDependencies": {"benchmark": "^2.1.4", "tap": "^12.1.0"}, "license": "ISC", "dependencies": {"yallist": "^3.0.2"}, "files": ["index.js"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-lru-cache-5.1.1-1da27e6710271947695daf6848e847f01d84b920-integrity\\node_modules\\lru-cache\\package.json", "readmeFilename": "README.md", "readme": "# lru cache\n\nA cache object that deletes the least-recently-used items.\n\n[![Build Status](https://travis-ci.org/isaacs/node-lru-cache.svg?branch=master)](https://travis-ci.org/isaacs/node-lru-cache) [![Coverage Status](https://coveralls.io/repos/isaacs/node-lru-cache/badge.svg?service=github)](https://coveralls.io/github/isaacs/node-lru-cache)\n\n## Installation:\n\n```javascript\nnpm install lru-cache --save\n```\n\n## Usage:\n\n```javascript\nvar LRU = require(\"lru-cache\")\n  , options = { max: 500\n              , length: function (n, key) { return n * 2 + key.length }\n              , dispose: function (key, n) { n.close() }\n              , maxAge: 1000 * 60 * 60 }\n  , cache = new LRU(options)\n  , otherCache = new LRU(50) // sets just the max size\n\ncache.set(\"key\", \"value\")\ncache.get(\"key\") // \"value\"\n\n// non-string keys ARE fully supported\n// but note that it must be THE SAME object, not\n// just a JSON-equivalent object.\nvar someObject = { a: 1 }\ncache.set(someObject, 'a value')\n// Object keys are not toString()-ed\ncache.set('[object Object]', 'a different value')\nassert.equal(cache.get(someObject), 'a value')\n// A similar object with same keys/values won't work,\n// because it's a different object identity\nassert.equal(cache.get({ a: 1 }), undefined)\n\ncache.reset()    // empty the cache\n```\n\nIf you put more stuff in it, then items will fall out.\n\nIf you try to put an oversized thing in it, then it'll fall out right\naway.\n\n## Options\n\n* `max` The maximum size of the cache, checked by applying the length\n  function to all values in the cache.  Not setting this is kind of\n  silly, since that's the whole purpose of this lib, but it defaults\n  to `Infinity`.  Setting it to a non-number or negative number will\n  throw a `TypeError`.  Setting it to 0 makes it be `Infinity`.\n* `maxAge` Maximum age in ms.  Items are not pro-actively pruned out\n  as they age, but if you try to get an item that is too old, it'll\n  drop it and return undefined instead of giving it to you.\n  Setting this to a negative value will make everything seem old!\n  Setting it to a non-number will throw a `TypeError`.\n* `length` Function that is used to calculate the length of stored\n  items.  If you're storing strings or buffers, then you probably want\n  to do something like `function(n, key){return n.length}`.  The default is\n  `function(){return 1}`, which is fine if you want to store `max`\n  like-sized things.  The item is passed as the first argument, and\n  the key is passed as the second argumnet.\n* `dispose` Function that is called on items when they are dropped\n  from the cache.  This can be handy if you want to close file\n  descriptors or do other cleanup tasks when items are no longer\n  accessible.  Called with `key, value`.  It's called *before*\n  actually removing the item from the internal cache, so if you want\n  to immediately put it back in, you'll have to do that in a\n  `nextTick` or `setTimeout` callback or it won't do anything.\n* `stale` By default, if you set a `maxAge`, it'll only actually pull\n  stale items out of the cache when you `get(key)`.  (That is, it's\n  not pre-emptively doing a `setTimeout` or anything.)  If you set\n  `stale:true`, it'll return the stale value before deleting it.  If\n  you don't set this, then it'll return `undefined` when you try to\n  get a stale entry, as if it had already been deleted.\n* `noDisposeOnSet` By default, if you set a `dispose()` method, then\n  it'll be called whenever a `set()` operation overwrites an existing\n  key.  If you set this option, `dispose()` will only be called when a\n  key falls out of the cache, not when it is overwritten.\n* `updateAgeOnGet` When using time-expiring entries with `maxAge`,\n  setting this to `true` will make each item's effective time update\n  to the current time whenever it is retrieved from cache, causing it\n  to not expire.  (It can still fall out of cache based on recency of\n  use, of course.)\n\n## API\n\n* `set(key, value, maxAge)`\n* `get(key) => value`\n\n    Both of these will update the \"recently used\"-ness of the key.\n    They do what you think. `maxAge` is optional and overrides the\n    cache `maxAge` option if provided.\n\n    If the key is not found, `get()` will return `undefined`.\n\n    The key and val can be any value.\n\n* `peek(key)`\n\n    Returns the key value (or `undefined` if not found) without\n    updating the \"recently used\"-ness of the key.\n\n    (If you find yourself using this a lot, you *might* be using the\n    wrong sort of data structure, but there are some use cases where\n    it's handy.)\n\n* `del(key)`\n\n    Deletes a key out of the cache.\n\n* `reset()`\n\n    Clear the cache entirely, throwing away all values.\n\n* `has(key)`\n\n    Check if a key is in the cache, without updating the recent-ness\n    or deleting it for being stale.\n\n* `forEach(function(value,key,cache), [thisp])`\n\n    Just like `Array.prototype.forEach`.  Iterates over all the keys\n    in the cache, in order of recent-ness.  (Ie, more recently used\n    items are iterated over first.)\n\n* `rforEach(function(value,key,cache), [thisp])`\n\n    The same as `cache.forEach(...)` but items are iterated over in\n    reverse order.  (ie, less recently used items are iterated over\n    first.)\n\n* `keys()`\n\n    Return an array of the keys in the cache.\n\n* `values()`\n\n    Return an array of the values in the cache.\n\n* `length`\n\n    Return total length of objects in cache taking into account\n    `length` options function.\n\n* `itemCount`\n\n    Return total quantity of objects currently in cache. Note, that\n    `stale` (see options) items are returned as part of this item\n    count.\n\n* `dump()`\n\n    Return an array of the cache entries ready for serialization and usage\n    with 'destinationCache.load(arr)`.\n\n* `load(cacheEntriesArray)`\n\n    Loads another cache entries array, obtained with `sourceCache.dump()`,\n    into the cache. The destination cache is reset before loading new entries\n\n* `prune()`\n\n    Manually iterates over the entire cache proactively pruning old entries\n", "licenseText": "The ISC License\n\nCopyright (c) <PERSON> and Contributors\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/lru-cache/-/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920", "type": "tarball", "reference": "https://registry.yarnpkg.com/lru-cache/-/lru-cache-5.1.1.tgz", "hash": "1da27e6710271947695daf6848e847f01d84b920", "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "registry": "npm", "packageName": "lru-cache", "cacheIntegrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w== sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA="}, "registry": "npm", "hash": "1da27e6710271947695daf6848e847f01d84b920"}