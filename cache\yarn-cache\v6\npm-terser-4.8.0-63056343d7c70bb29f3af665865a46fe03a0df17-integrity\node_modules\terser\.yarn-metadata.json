{"manifest": {"name": "terser", "description": "JavaScript parser, mangler/compressor and beautifier toolkit for ES6+", "homepage": "https://terser.org/", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://lisperator.net/"}, "license": "BSD-2-<PERSON><PERSON>", "version": "4.8.0", "engines": {"node": ">=6.0.0"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/terser/terser"}, "main": "dist/bundle.min.js", "types": "tools/terser.d.ts", "bin": {"terser": "bin\\terser"}, "files": ["bin", "dist", "tools", "LICENSE", "README.md", "CHANGELOG.md", "PATRONS.md"], "dependencies": {"commander": "^2.20.0", "source-map": "~0.6.1", "source-map-support": "~0.5.12"}, "devDependencies": {"acorn": "^7.1.1", "astring": "^1.4.1", "eslint": "^6.3.0", "eslump": "^2.0.0", "mocha": "^7.1.2", "mochallel": "^2.0.0", "pre-commit": "^1.2.2", "rimraf": "^3.0.0", "rollup": "2.0.6", "rollup-plugin-terser": "5.3.0", "semver": "^7.1.3"}, "scripts": {"test": "npm run build -- --configTest && node test/run-tests.js", "test:compress": "npm run build -- --configTest && node test/compress.js", "test:mocha": "npm run build -- --configTest && node test/mocha.js", "lint": "eslint lib", "lint-fix": "eslint --fix lib", "build": "rimraf dist/* && rollup --config --silent", "prepare": "npm run build", "postversion": "echo 'Remember to update the changelog!'"}, "keywords": ["uglify", "terser", "uglify-es", "uglify-js", "minify", "minifier", "javascript", "ecmascript", "es5", "es6", "es7", "es8", "es2015", "es2016", "es2017", "async", "await"], "eslintConfig": {"parserOptions": {"sourceType": "module"}, "env": {"es6": true}, "globals": {"describe": false, "it": false, "require": false, "global": false, "process": false}, "rules": {"brace-style": ["error", "1tbs", {"allowSingleLine": true}], "quotes": ["error", "double", "avoid-escape"], "no-debugger": "error", "no-undef": "error", "no-unused-vars": ["error", {"varsIgnorePattern": "^_$"}], "no-tabs": "error", "semi": ["error", "always"], "no-extra-semi": "error", "no-irregular-whitespace": "error", "space-before-blocks": ["error", "always"]}}, "pre-commit": ["lint-fix", "test"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-terser-4.8.0-63056343d7c70bb29f3af665865a46fe03a0df17-integrity\\node_modules\\terser\\package.json", "readmeFilename": "README.md", "readme": "<h1><img src=\"https://terser.org/img/terser-banner-logo.png\" alt=\"Terser\" width=\"400\"></h1>\n\n  [![NPM Version][npm-image]][npm-url]\n  [![NPM Downloads][downloads-image]][downloads-url]\n  [![Travis Build][travis-image]][travis-url]\n  [![Opencollective financial contributors][opencollective-contributors]][opencollective-url]\n\nA JavaScript parser and mangler/compressor toolkit for ES6+.\n\n*note*: You can support this project on patreon: <a target=\"_blank\" rel=\"nofollow\" href=\"https://www.patreon.com/fabiosantoscode\"><img src=\"https://c5.patreon.com/external/logo/<EMAIL>\" alt=\"patron\" width=\"100px\" height=\"auto\"></a>. Check out [PATRONS.md](https://github.com/terser/terser/blob/master/PATRONS.md) for our first-tier patrons.\n\nTerser recommends you use RollupJS to bundle your modules, as that produces smaller code overall.\n\n*Beautification* has been undocumented and is *being removed* from terser, we recommend you use [prettier](https://npmjs.com/package/prettier).\n\nFind the changelog in [CHANGELOG.md](https://github.com/terser/terser/blob/master/CHANGELOG.md)\n\n\n\n[npm-image]: https://img.shields.io/npm/v/terser.svg\n[npm-url]: https://npmjs.org/package/terser\n[downloads-image]: https://img.shields.io/npm/dm/terser.svg\n[downloads-url]: https://npmjs.org/package/terser\n[travis-image]: https://img.shields.io/travis/terser/terser/master.svg\n[travis-url]: https://travis-ci.org/terser/terser\n[opencollective-contributors]: https://opencollective.com/terser/tiers/badge.svg\n[opencollective-url]: https://opencollective.com/terser\n\nWhy choose terser?\n------------------\n\n`uglify-es` is [no longer maintained](https://github.com/mishoo/UglifyJS2/issues/3156#issuecomment-392943058) and `uglify-js` does not support ES6+.\n\n**`terser`** is a fork of `uglify-es` that mostly retains API and CLI compatibility\nwith `uglify-es` and `uglify-js@3`.\n\nInstall\n-------\n\nFirst make sure you have installed the latest version of [node.js](http://nodejs.org/)\n(You may need to restart your computer after this step).\n\nFrom NPM for use as a command line app:\n\n    npm install terser -g\n\nFrom NPM for programmatic use:\n\n    npm install terser\n\n# Command line usage\n\n    terser [input files] [options]\n\nTerser can take multiple input files.  It's recommended that you pass the\ninput files first, then pass the options.  Terser will parse input files\nin sequence and apply any compression options.  The files are parsed in the\nsame global scope, that is, a reference from a file to some\nvariable/function declared in another file will be matched properly.\n\nIf no input file is specified, Terser will read from STDIN.\n\nIf you wish to pass your options before the input files, separate the two with\na double dash to prevent input files being used as option arguments:\n\n    terser --compress --mangle -- input.js\n\n### Command line options\n\n```\n    -h, --help                  Print usage information.\n                                `--help options` for details on available options.\n    -V, --version               Print version number.\n    -p, --parse <options>       Specify parser options:\n                                `acorn`  Use Acorn for parsing.\n                                `bare_returns`  Allow return outside of functions.\n                                                Useful when minifying CommonJS\n                                                modules and Userscripts that may\n                                                be anonymous function wrapped (IIFE)\n                                                by the .user.js engine `caller`.\n                                `expression`  Parse a single expression, rather than\n                                              a program (for parsing JSON).\n                                `spidermonkey`  Assume input files are SpiderMonkey\n                                                AST format (as JSON).\n    -c, --compress [options]    Enable compressor/specify compressor options:\n                                `pure_funcs`  List of functions that can be safely\n                                              removed when their return values are\n                                              not used.\n    -m, --mangle [options]      Mangle names/specify mangler options:\n                                `reserved`  List of names that should not be mangled.\n    --mangle-props [options]    Mangle properties/specify mangler options:\n                                `builtins`  Mangle property names that overlaps\n                                            with standard JavaScript globals and DOM\n                                            API props.\n                                `debug`  Add debug prefix and suffix.\n                                `keep_quoted`  Only mangle unquoted properties, quoted\n                                               properties are automatically reserved.\n                                               `strict` disables quoted properties\n                                               being automatically reserved.\n                                `regex`  Only mangle matched property names.\n                                `reserved`  List of names that should not be mangled.\n    -b, --beautify [options]     Specify output options:\n                                `preamble`  Preamble to prepend to the output. You\n                                            can use this to insert a comment, for\n                                            example for licensing information.\n                                            This will not be parsed, but the source\n                                            map will adjust for its presence.\n                                `quote_style`  Quote style:\n                                               0 - auto\n                                               1 - single\n                                               2 - double\n                                               3 - original\n                                `wrap_iife`  Wrap IIFEs in parenthesis. Note: you may\n                                             want to disable `negate_iife` under\n                                             compressor options.\n                                `wrap_func_args`  Wrap function arguments in parenthesis.\n    -o, --output <file>         Output file path (default STDOUT). Specify `ast` or\n                                `spidermonkey` to write Terser or SpiderMonkey AST\n                                as JSON to STDOUT respectively.\n    --comments [filter]         Preserve copyright comments in the output. By\n                                default this works like Google Closure, keeping\n                                JSDoc-style comments that contain \"@license\" or\n                                \"@preserve\". You can optionally pass one of the\n                                following arguments to this flag:\n                                - \"all\" to keep all comments\n                                - `false` to omit comments in the output\n                                - a valid JS RegExp like `/foo/` or `/^!/` to\n                                keep only matching comments.\n                                Note that currently not *all* comments can be\n                                kept when compression is on, because of dead\n                                code removal or cascading statements into\n                                sequences.\n    --config-file <file>        Read `minify()` options from JSON file.\n    -d, --define <expr>[=value] Global definitions.\n    --ecma <version>            Specify ECMAScript release: 5, 2015, 2016, etc.\n    -e, --enclose [arg[:value]] Embed output in a big function with configurable\n                                arguments and values.\n    --ie8                       Support non-standard Internet Explorer 8.\n                                Equivalent to setting `ie8: true` in `minify()`\n                                for `compress`, `mangle` and `output` options.\n                                By default Terser will not try to be IE-proof.\n    --keep-classnames           Do not mangle/drop class names.\n    --keep-fnames               Do not mangle/drop function names.  Useful for\n                                code relying on Function.prototype.name.\n    --module                    Input is an ES6 module. If `compress` or `mangle` is\n                                enabled then the `toplevel` option will be enabled.\n    --name-cache <file>         File to hold mangled name mappings.\n    --safari10                  Support non-standard Safari 10/11.\n                                Equivalent to setting `safari10: true` in `minify()`\n                                for `mangle` and `output` options.\n                                By default `terser` will not work around\n                                Safari 10/11 bugs.\n    --source-map [options]      Enable source map/specify source map options:\n                                `base`  Path to compute relative paths from input files.\n                                `content`  Input source map, useful if you're compressing\n                                           JS that was generated from some other original\n                                           code. Specify \"inline\" if the source map is\n                                           included within the sources.\n                                `filename`  Name and/or location of the output source.\n                                `includeSources`  Pass this flag if you want to include\n                                                  the content of source files in the\n                                                  source map as sourcesContent property.\n                                `root`  Path to the original source to be included in\n                                        the source map.\n                                `url`  If specified, path to the source map to append in\n                                       `//# sourceMappingURL`.\n    --timings                   Display operations run time on STDERR.\n    --toplevel                  Compress and/or mangle variables in top level scope.\n    --verbose                   Print diagnostic messages.\n    --warn                      Print warning messages.\n    --wrap <name>               Embed everything in a big function, making the\n                                “exports” and “global” variables available. You\n                                need to pass an argument to this option to\n                                specify the name that your module will take\n                                when included in, say, a browser.\n```\n\nSpecify `--output` (`-o`) to declare the output file.  Otherwise the output\ngoes to STDOUT.\n\n## CLI source map options\n\nTerser can generate a source map file, which is highly useful for\ndebugging your compressed JavaScript.  To get a source map, pass\n`--source-map --output output.js` (source map will be written out to\n`output.js.map`).\n\nAdditional options:\n\n- `--source-map \"filename='<NAME>'\"` to specify the name of the source map.\n\n- `--source-map \"root='<URL>'\"` to pass the URL where the original files can be found.\n\n- `--source-map \"url='<URL>'\"` to specify the URL where the source map can be found.\n  Otherwise Terser assumes HTTP `X-SourceMap` is being used and will omit the\n  `//# sourceMappingURL=` directive.\n\nFor example:\n\n    terser js/file1.js js/file2.js \\\n             -o foo.min.js -c -m \\\n             --source-map \"root='http://foo.com/src',url='foo.min.js.map'\"\n\nThe above will compress and mangle `file1.js` and `file2.js`, will drop the\noutput in `foo.min.js` and the source map in `foo.min.js.map`.  The source\nmapping will refer to `http://foo.com/src/js/file1.js` and\n`http://foo.com/src/js/file2.js` (in fact it will list `http://foo.com/src`\nas the source map root, and the original files as `js/file1.js` and\n`js/file2.js`).\n\n### Composed source map\n\nWhen you're compressing JS code that was output by a compiler such as\nCoffeeScript, mapping to the JS code won't be too helpful.  Instead, you'd\nlike to map back to the original code (i.e. CoffeeScript).  Terser has an\noption to take an input source map.  Assuming you have a mapping from\nCoffeeScript → compiled JS, Terser can generate a map from CoffeeScript →\ncompressed JS by mapping every token in the compiled JS to its original\nlocation.\n\nTo use this feature pass `--source-map \"content='/path/to/input/source.map'\"`\nor `--source-map \"content=inline\"` if the source map is included inline with\nthe sources.\n\n## CLI compress options\n\nYou need to pass `--compress` (`-c`) to enable the compressor.  Optionally\nyou can pass a comma-separated list of [compress options](#compress-options).\n\nOptions are in the form `foo=bar`, or just `foo` (the latter implies\na boolean option that you want to set `true`; it's effectively a\nshortcut for `foo=true`).\n\nExample:\n\n    terser file.js -c toplevel,sequences=false\n\n## CLI mangle options\n\nTo enable the mangler you need to pass `--mangle` (`-m`).  The following\n(comma-separated) options are supported:\n\n- `toplevel` (default `false`) -- mangle names declared in the top level scope.\n\n- `eval` (default `false`) -- mangle names visible in scopes where `eval` or `with` are used.\n\nWhen mangling is enabled but you want to prevent certain names from being\nmangled, you can declare those names with `--mangle reserved` — pass a\ncomma-separated list of names.  For example:\n\n    terser ... -m reserved=['$','require','exports']\n\nto prevent the `require`, `exports` and `$` names from being changed.\n\n### CLI mangling property names (`--mangle-props`)\n\n**Note:** THIS **WILL** BREAK YOUR CODE. A good rule of thumb is not to use this unless you know exactly what you're doing and how this works and read this section until the end.\n\nMangling property names is a separate step, different from variable name mangling.  Pass\n`--mangle-props` to enable it. The least dangerous\nway to use this is to use the `regex` option like so:\n\n```\nterser example.js -c -m --mangle-props regex=/_$/\n```\n\nThis will mangle all properties that end with an\nunderscore. So you can use it to mangle internal methods.\n\nBy default, it will mangle all properties in the\ninput code with the exception of built in DOM properties and properties\nin core JavaScript classes, which is what will break your code if you don't:\n\n1. Control all the code you're mangling\n2. Avoid using a module bundler, as they usually will call Terser on each file individually, making it impossible to pass mangled objects between modules.\n3. Avoid calling functions like `defineProperty` or `hasOwnProperty`, because they refer to object properties using strings and will break your code if you don't know what you are doing.\n\nAn example:\n\n```javascript\n// example.js\nvar x = {\n    baz_: 0,\n    foo_: 1,\n    calc: function() {\n        return this.foo_ + this.baz_;\n    }\n};\nx.bar_ = 2;\nx[\"baz_\"] = 3;\nconsole.log(x.calc());\n```\nMangle all properties (except for JavaScript `builtins`) (**very** unsafe):\n```bash\n$ terser example.js -c passes=2 -m --mangle-props\n```\n```javascript\nvar x={o:3,t:1,i:function(){return this.t+this.o},s:2};console.log(x.i());\n```\nMangle all properties except for `reserved` properties (still very unsafe):\n```bash\n$ terser example.js -c passes=2 -m --mangle-props reserved=[foo_,bar_]\n```\n```javascript\nvar x={o:3,foo_:1,t:function(){return this.foo_+this.o},bar_:2};console.log(x.t());\n```\nMangle all properties matching a `regex` (not as unsafe but still unsafe):\n```bash\n$ terser example.js -c passes=2 -m --mangle-props regex=/_$/\n```\n```javascript\nvar x={o:3,t:1,calc:function(){return this.t+this.o},i:2};console.log(x.calc());\n```\n\nCombining mangle properties options:\n```bash\n$ terser example.js -c passes=2 -m --mangle-props regex=/_$/,reserved=[bar_]\n```\n```javascript\nvar x={o:3,t:1,calc:function(){return this.t+this.o},bar_:2};console.log(x.calc());\n```\n\nIn order for this to be of any use, we avoid mangling standard JS names and DOM\nAPI properties by default (`--mangle-props builtins` to override).\n\nA regular expression can be used to define which property names should be\nmangled.  For example, `--mangle-props regex=/^_/` will only mangle property\nnames that start with an underscore.\n\nWhen you compress multiple files using this option, in order for them to\nwork together in the end we need to ensure somehow that one property gets\nmangled to the same name in all of them.  For this, pass `--name-cache filename.json`\nand Terser will maintain these mappings in a file which can then be reused.\nIt should be initially empty.  Example:\n\n```bash\n$ rm -f /tmp/cache.json  # start fresh\n$ terser file1.js file2.js --mangle-props --name-cache /tmp/cache.json -o part1.js\n$ terser file3.js file4.js --mangle-props --name-cache /tmp/cache.json -o part2.js\n```\n\nNow, `part1.js` and `part2.js` will be consistent with each other in terms\nof mangled property names.\n\nUsing the name cache is not necessary if you compress all your files in a\nsingle call to Terser.\n\n### Mangling unquoted names (`--mangle-props keep_quoted`)\n\nUsing quoted property name (`o[\"foo\"]`) reserves the property name (`foo`)\nso that it is not mangled throughout the entire script even when used in an\nunquoted style (`o.foo`). Example:\n\n```javascript\n// stuff.js\nvar o = {\n    \"foo\": 1,\n    bar: 3\n};\no.foo += o.bar;\nconsole.log(o.foo);\n```\n```bash\n$ terser stuff.js --mangle-props keep_quoted -c -m\n```\n```javascript\nvar o={foo:1,o:3};o.foo+=o.o,console.log(o.foo);\n```\n\n### Debugging property name mangling\n\nYou can also pass `--mangle-props debug` in order to mangle property names\nwithout completely obscuring them. For example the property `o.foo`\nwould mangle to `o._$foo$_` with this option. This allows property mangling\nof a large codebase while still being able to debug the code and identify\nwhere mangling is breaking things.\n\n```bash\n$ terser stuff.js --mangle-props debug -c -m\n```\n```javascript\nvar o={_$foo$_:1,_$bar$_:3};o._$foo$_+=o._$bar$_,console.log(o._$foo$_);\n```\n\nYou can also pass a custom suffix using `--mangle-props debug=XYZ`. This would then\nmangle `o.foo` to `o._$foo$XYZ_`. You can change this each time you compile a\nscript to identify how a property got mangled. One technique is to pass a\nrandom number on every compile to simulate mangling changing with different\ninputs (e.g. as you update the input script with new properties), and to help\nidentify mistakes like writing mangled keys to storage.\n\n\n# API Reference\n\nAssuming installation via NPM, you can load Terser in your application\nlike this:\n```javascript\nvar Terser = require(\"terser\");\n```\nBrowser loading is also supported:\n```html\n<script src=\"node_modules/source-map/dist/source-map.min.js\"></script>\n<script src=\"dist/bundle.min.js\"></script>\n```\n\nThere is a single high level function, **`minify(code, options)`**,\nwhich will perform all minification [phases](#minify-options) in a configurable\nmanner. By default `minify()` will enable the options [`compress`](#compress-options)\nand [`mangle`](#mangle-options). Example:\n```javascript\nvar code = \"function add(first, second) { return first + second; }\";\nvar result = Terser.minify(code);\nconsole.log(result.error); // runtime error, or `undefined` if no error\nconsole.log(result.code);  // minified output: function add(n,d){return n+d}\n```\n\nYou can `minify` more than one JavaScript file at a time by using an object\nfor the first argument where the keys are file names and the values are source\ncode:\n```javascript\nvar code = {\n    \"file1.js\": \"function add(first, second) { return first + second; }\",\n    \"file2.js\": \"console.log(add(1 + 2, 3 + 4));\"\n};\nvar result = Terser.minify(code);\nconsole.log(result.code);\n// function add(d,n){return d+n}console.log(add(3,7));\n```\n\nThe `toplevel` option:\n```javascript\nvar code = {\n    \"file1.js\": \"function add(first, second) { return first + second; }\",\n    \"file2.js\": \"console.log(add(1 + 2, 3 + 4));\"\n};\nvar options = { toplevel: true };\nvar result = Terser.minify(code, options);\nconsole.log(result.code);\n// console.log(3+7);\n```\n\nThe `nameCache` option:\n```javascript\nvar options = {\n    mangle: {\n        toplevel: true,\n    },\n    nameCache: {}\n};\nvar result1 = Terser.minify({\n    \"file1.js\": \"function add(first, second) { return first + second; }\"\n}, options);\nvar result2 = Terser.minify({\n    \"file2.js\": \"console.log(add(1 + 2, 3 + 4));\"\n}, options);\nconsole.log(result1.code);\n// function n(n,r){return n+r}\nconsole.log(result2.code);\n// console.log(n(3,7));\n```\n\nYou may persist the name cache to the file system in the following way:\n```javascript\nvar cacheFileName = \"/tmp/cache.json\";\nvar options = {\n    mangle: {\n        properties: true,\n    },\n    nameCache: JSON.parse(fs.readFileSync(cacheFileName, \"utf8\"))\n};\nfs.writeFileSync(\"part1.js\", Terser.minify({\n    \"file1.js\": fs.readFileSync(\"file1.js\", \"utf8\"),\n    \"file2.js\": fs.readFileSync(\"file2.js\", \"utf8\")\n}, options).code, \"utf8\");\nfs.writeFileSync(\"part2.js\", Terser.minify({\n    \"file3.js\": fs.readFileSync(\"file3.js\", \"utf8\"),\n    \"file4.js\": fs.readFileSync(\"file4.js\", \"utf8\")\n}, options).code, \"utf8\");\nfs.writeFileSync(cacheFileName, JSON.stringify(options.nameCache), \"utf8\");\n```\n\nAn example of a combination of `minify()` options:\n```javascript\nvar code = {\n    \"file1.js\": \"function add(first, second) { return first + second; }\",\n    \"file2.js\": \"console.log(add(1 + 2, 3 + 4));\"\n};\nvar options = {\n    toplevel: true,\n    compress: {\n        global_defs: {\n            \"@console.log\": \"alert\"\n        },\n        passes: 2\n    },\n    output: {\n        beautify: false,\n        preamble: \"/* minified */\"\n    }\n};\nvar result = Terser.minify(code, options);\nconsole.log(result.code);\n// /* minified */\n// alert(10);\"\n```\n\nTo produce warnings:\n```javascript\nvar code = \"function f(){ var u; return 2 + 3; }\";\nvar options = { warnings: true };\nvar result = Terser.minify(code, options);\nconsole.log(result.error);    // runtime error, `undefined` in this case\nconsole.log(result.warnings); // [ 'Dropping unused variable u [0:1,18]' ]\nconsole.log(result.code);     // function f(){return 5}\n```\n\nAn error example:\n```javascript\nvar result = Terser.minify({\"foo.js\" : \"if (0) else console.log(1);\"});\nconsole.log(JSON.stringify(result.error));\n// {\"message\":\"Unexpected token: keyword (else)\",\"filename\":\"foo.js\",\"line\":1,\"col\":7,\"pos\":7}\n```\nNote: unlike `uglify-js@2.x`, the Terser API does not throw errors.\nTo achieve a similar effect one could do the following:\n```javascript\nvar result = Terser.minify(code, options);\nif (result.error) throw result.error;\n```\n\n## Minify options\n\n- `ecma` (default `undefined`) - pass `5`, `2015`, `2016`, etc to override `parse`,\n  `compress` and `output`'s `ecma` options.\n\n- `warnings` (default `false`) — pass `true` to return compressor warnings\n  in `result.warnings`. Use the value `\"verbose\"` for more detailed warnings.\n\n- `parse` (default `{}`) — pass an object if you wish to specify some\n  additional [parse options](#parse-options).\n\n- `compress` (default `{}`) — pass `false` to skip compressing entirely.\n  Pass an object to specify custom [compress options](#compress-options).\n\n- `mangle` (default `true`) — pass `false` to skip mangling names, or pass\n  an object to specify [mangle options](#mangle-options) (see below).\n\n  - `mangle.properties` (default `false`) — a subcategory of the mangle option.\n    Pass an object to specify custom [mangle property options](#mangle-properties-options).\n\n- `module` (default `false`) — Use when minifying an ES6 module. \"use strict\"\n  is implied and names can be mangled on the top scope. If `compress` or\n  `mangle` is enabled then the `toplevel` option will be enabled.\n\n- `output` (default `null`) — pass an object if you wish to specify\n  additional [output options](#output-options).  The defaults are optimized\n  for best compression.\n\n- `sourceMap` (default `false`) - pass an object if you wish to specify\n  [source map options](#source-map-options).\n\n- `toplevel` (default `false`) - set to `true` if you wish to enable top level\n  variable and function name mangling and to drop unused variables and functions.\n\n- `nameCache` (default `null`) - pass an empty object `{}` or a previously\n  used `nameCache` object if you wish to cache mangled variable and\n  property names across multiple invocations of `minify()`. Note: this is\n  a read/write property. `minify()` will read the name cache state of this\n  object and update it during minification so that it may be\n  reused or externally persisted by the user.\n\n- `ie8` (default `false`) - set to `true` to support IE8.\n\n- `keep_classnames` (default: `undefined`) - pass `true` to prevent discarding or mangling\n  of class names. Pass a regular expression to only keep class names matching that regex.\n\n- `keep_fnames` (default: `false`) - pass `true` to prevent discarding or mangling\n  of function names. Pass a regular expression to only keep class names matching that regex.\n  Useful for code relying on `Function.prototype.name`. If the top level minify option\n  `keep_classnames` is `undefined` it will be overridden with the value of the top level\n  minify option `keep_fnames`.\n\n- `safari10` (default: `false`) - pass `true` to work around Safari 10/11 bugs in\n  loop scoping and `await`. See `safari10` options in [`mangle`](#mangle-options)\n  and [`output`](#output-options) for details.\n\n## Minify options structure\n\n```javascript\n{\n    parse: {\n        // parse options\n    },\n    compress: {\n        // compress options\n    },\n    mangle: {\n        // mangle options\n\n        properties: {\n            // mangle property options\n        }\n    },\n    output: {\n        // output options\n    },\n    sourceMap: {\n        // source map options\n    },\n    ecma: 5, // specify one of: 5, 2015, 2016, etc.\n    keep_classnames: false,\n    keep_fnames: false,\n    ie8: false,\n    module: false,\n    nameCache: null, // or specify a name cache object\n    safari10: false,\n    toplevel: false,\n    warnings: false,\n}\n```\n\n### Source map options\n\nTo generate a source map:\n```javascript\nvar result = Terser.minify({\"file1.js\": \"var a = function() {};\"}, {\n    sourceMap: {\n        filename: \"out.js\",\n        url: \"out.js.map\"\n    }\n});\nconsole.log(result.code); // minified output\nconsole.log(result.map);  // source map\n```\n\nNote that the source map is not saved in a file, it's just returned in\n`result.map`.  The value passed for `sourceMap.url` is only used to set\n`//# sourceMappingURL=out.js.map` in `result.code`. The value of\n`filename` is only used to set `file` attribute (see [the spec][sm-spec])\nin source map file.\n\nYou can set option `sourceMap.url` to be `\"inline\"` and source map will\nbe appended to code.\n\nYou can also specify sourceRoot property to be included in source map:\n```javascript\nvar result = Terser.minify({\"file1.js\": \"var a = function() {};\"}, {\n    sourceMap: {\n        root: \"http://example.com/src\",\n        url: \"out.js.map\"\n    }\n});\n```\n\nIf you're compressing compiled JavaScript and have a source map for it, you\ncan use `sourceMap.content`:\n```javascript\nvar result = Terser.minify({\"compiled.js\": \"compiled code\"}, {\n    sourceMap: {\n        content: \"content from compiled.js.map\",\n        url: \"minified.js.map\"\n    }\n});\n// same as before, it returns `code` and `map`\n```\n\nIf you're using the `X-SourceMap` header instead, you can just omit `sourceMap.url`.\n\nIf you happen to need the source map as a raw object, set `sourceMap.asObject` to `true`.\n\n## Parse options\n\n- `bare_returns` (default `false`) -- support top level `return` statements\n\n- `ecma` (default: `2017`) -- specify one of `5`, `2015`, `2016` or `2017`. Note: this setting\n  is not presently enforced except for ES8 optional trailing commas in function\n  parameter lists and calls with `ecma` `2017`.\n\n- `html5_comments` (default `true`)\n\n- `shebang` (default `true`) -- support `#!command` as the first line\n\n## Compress options\n\n- `defaults` (default: `true`) -- Pass `false` to disable most default\n  enabled `compress` transforms. Useful when you only want to enable a few\n  `compress` options while disabling the rest.\n\n- `arrows` (default: `true`) -- Class and object literal methods are converted\n  will also be converted to arrow expressions if the resultant code is shorter:\n  `m(){return x}` becomes `m:()=>x`. To do this to regular ES5 functions which\n  don't use `this` or `arguments`, see `unsafe_arrows`.\n\n- `arguments` (default: `false`) -- replace `arguments[index]` with function\n  parameter name whenever possible.\n\n- `booleans` (default: `true`) -- various optimizations for boolean context,\n  for example `!!a ? b : c → a ? b : c`\n\n- `booleans_as_integers` (default: `false`) -- Turn booleans into 0 and 1, also\n  makes comparisons with booleans use `==` and `!=` instead of `===` and `!==`.\n\n- `collapse_vars` (default: `true`) -- Collapse single-use non-constant variables,\n  side effects permitting.\n\n- `comparisons` (default: `true`) -- apply certain optimizations to binary nodes,\n  e.g. `!(a <= b) → a > b` (only when `unsafe_comps`), attempts to negate binary\n  nodes, e.g. `a = !b && !c && !d && !e → a=!(b||c||d||e)` etc.\n\n- `computed_props` (default: `true`) -- Transforms constant computed properties\n  into regular ones: `{[\"computed\"]: 1}` is converted to `{computed: 1}`.\n\n- `conditionals` (default: `true`) -- apply optimizations for `if`-s and conditional\n  expressions\n\n- `dead_code` (default: `true`) -- remove unreachable code\n\n- `directives` (default: `true`) -- remove redundant or non-standard directives\n\n- `drop_console` (default: `false`) -- Pass `true` to discard calls to\n  `console.*` functions. If you wish to drop a specific function call\n  such as `console.info` and/or retain side effects from function arguments\n  after dropping the function call then use `pure_funcs` instead.\n\n- `drop_debugger` (default: `true`) -- remove `debugger;` statements\n\n- `ecma` (default: `5`) -- Pass `2015` or greater to enable `compress` options that\n  will transform ES5 code into smaller ES6+ equivalent forms.\n\n- `evaluate` (default: `true`) -- attempt to evaluate constant expressions\n\n- `expression` (default: `false`) -- Pass `true` to preserve completion values\n  from terminal statements without `return`, e.g. in bookmarklets.\n\n- `global_defs` (default: `{}`) -- see [conditional compilation](#conditional-compilation)\n\n- `hoist_funs` (default: `false`) -- hoist function declarations\n\n- `hoist_props` (default: `true`) -- hoist properties from constant object and\n  array literals into regular variables subject to a set of constraints. For example:\n  `var o={p:1, q:2}; f(o.p, o.q);` is converted to `f(1, 2);`. Note: `hoist_props`\n  works best with `mangle` enabled, the `compress` option `passes` set to `2` or higher,\n  and the `compress` option `toplevel` enabled.\n\n- `hoist_vars` (default: `false`) -- hoist `var` declarations (this is `false`\n  by default because it seems to increase the size of the output in general)\n\n- `if_return` (default: `true`) -- optimizations for if/return and if/continue\n\n- `inline` (default: `true`) -- inline calls to function with simple/`return` statement:\n  - `false` -- same as `0`\n  - `0` -- disabled inlining\n  - `1` -- inline simple functions\n  - `2` -- inline functions with arguments\n  - `3` -- inline functions with arguments and variables\n  - `true` -- same as `3`\n\n- `join_vars` (default: `true`) -- join consecutive `var` statements\n\n- `keep_classnames` (default: `false`) -- Pass `true` to prevent the compressor from\n  discarding class names. Pass a regular expression to only keep class names matching\n  that regex. See also: the `keep_classnames` [mangle option](#mangle).\n\n- `keep_fargs` (default: `true`) -- Prevents the compressor from discarding unused\n  function arguments.  You need this for code which relies on `Function.length`.\n\n- `keep_fnames` (default: `false`) -- Pass `true` to prevent the\n  compressor from discarding function names. Pass a regular expression to only keep\n  function names matching that regex. Useful for code relying on `Function.prototype.name`.\n  See also: the `keep_fnames` [mangle option](#mangle).\n\n- `keep_infinity` (default: `false`) -- Pass `true` to prevent `Infinity` from\n  being compressed into `1/0`, which may cause performance issues on Chrome.\n\n- `loops` (default: `true`) -- optimizations for `do`, `while` and `for` loops\n  when we can statically determine the condition.\n\n- `module` (default `false`) -- Pass `true` when compressing an ES6 module. Strict\n  mode is implied and the `toplevel` option as well.\n\n- `negate_iife` (default: `true`) -- negate \"Immediately-Called Function Expressions\"\n  where the return value is discarded, to avoid the parens that the\n  code generator would insert.\n\n- `passes` (default: `1`) -- The maximum number of times to run compress.\n  In some cases more than one pass leads to further compressed code.  Keep in\n  mind more passes will take more time.\n\n- `properties` (default: `true`) -- rewrite property access using the dot notation, for\n  example `foo[\"bar\"] → foo.bar`\n\n- `pure_funcs` (default: `null`) -- You can pass an array of names and\n  Terser will assume that those functions do not produce side\n  effects.  DANGER: will not check if the name is redefined in scope.\n  An example case here, for instance `var q = Math.floor(a/b)`.  If\n  variable `q` is not used elsewhere, Terser will drop it, but will\n  still keep the `Math.floor(a/b)`, not knowing what it does.  You can\n  pass `pure_funcs: [ 'Math.floor' ]` to let it know that this\n  function won't produce any side effect, in which case the whole\n  statement would get discarded.  The current implementation adds some\n  overhead (compression will be slower).\n\n- `pure_getters` (default: `\"strict\"`) -- If you pass `true` for\n  this, Terser will assume that object property access\n  (e.g. `foo.bar` or `foo[\"bar\"]`) doesn't have any side effects.\n  Specify `\"strict\"` to treat `foo.bar` as side-effect-free only when\n  `foo` is certain to not throw, i.e. not `null` or `undefined`.\n\n- `reduce_funcs` (legacy option, safely ignored for backwards compatibility).\n\n- `reduce_vars` (default: `true`) -- Improve optimization on variables assigned with and\n  used as constant values.\n\n- `sequences` (default: `true`) -- join consecutive simple statements using the\n  comma operator.  May be set to a positive integer to specify the maximum number\n  of consecutive comma sequences that will be generated. If this option is set to\n  `true` then the default `sequences` limit is `200`. Set option to `false` or `0`\n  to disable. The smallest `sequences` length is `2`. A `sequences` value of `1`\n  is grandfathered to be equivalent to `true` and as such means `200`. On rare\n  occasions the default sequences limit leads to very slow compress times in which\n  case a value of `20` or less is recommended.\n\n- `side_effects` (default: `true`) -- Pass `false` to disable potentially dropping\n  function calls marked as \"pure\".  A function call is marked as \"pure\" if a comment\n  annotation `/*@__PURE__*/` or `/*#__PURE__*/` immediately precedes the call. For\n  example: `/*@__PURE__*/foo();`\n\n- `switches` (default: `true`) -- de-duplicate and remove unreachable `switch` branches\n\n- `toplevel` (default: `false`) -- drop unreferenced functions (`\"funcs\"`) and/or\n  variables (`\"vars\"`) in the top level scope (`false` by default, `true` to drop\n  both unreferenced functions and variables)\n\n- `top_retain` (default: `null`) -- prevent specific toplevel functions and\n  variables from `unused` removal (can be array, comma-separated, RegExp or\n  function. Implies `toplevel`)\n\n- `typeofs` (default: `true`) -- Transforms `typeof foo == \"undefined\"` into\n  `foo === void 0`.  Note: recommend to set this value to `false` for IE10 and\n  earlier versions due to known issues.\n\n- `unsafe` (default: `false`) -- apply \"unsafe\" transformations\n  ([details](#the-unsafe-compress-option)).\n\n- `unsafe_arrows` (default: `false`) -- Convert ES5 style anonymous function\n  expressions to arrow functions if the function body does not reference `this`.\n  Note: it is not always safe to perform this conversion if code relies on the\n  the function having a `prototype`, which arrow functions lack.\n  This transform requires that the `ecma` compress option is set to `2015` or greater.\n\n- `unsafe_comps` (default: `false`) -- Reverse `<` and `<=` to `>` and `>=` to\n  allow improved compression. This might be unsafe when an at least one of two\n  operands is an object with computed values due the use of methods like `get`,\n  or `valueOf`. This could cause change in execution order after operands in the\n  comparison are switching. Compression only works if both `comparisons` and\n  `unsafe_comps` are both set to true.\n\n- `unsafe_Function` (default: `false`) -- compress and mangle `Function(args, code)`\n  when both `args` and `code` are string literals.\n\n- `unsafe_math` (default: `false`) -- optimize numerical expressions like\n  `2 * x * 3` into `6 * x`, which may give imprecise floating point results.\n\n- `unsafe_symbols` (default: `false`) -- removes keys from native Symbol \n  declarations, e.g `Symbol(\"kDog\")` becomes `Symbol()`.\n\n- `unsafe_methods` (default: false) -- Converts `{ m: function(){} }` to\n  `{ m(){} }`. `ecma` must be set to `6` or greater to enable this transform.\n  If `unsafe_methods` is a RegExp then key/value pairs with keys matching the\n  RegExp will be converted to concise methods.\n  Note: if enabled there is a risk of getting a \"`<method name>` is not a\n  constructor\" TypeError should any code try to `new` the former function.\n\n- `unsafe_proto` (default: `false`) -- optimize expressions like\n  `Array.prototype.slice.call(a)` into `[].slice.call(a)`\n\n- `unsafe_regexp` (default: `false`) -- enable substitutions of variables with\n  `RegExp` values the same way as if they are constants.\n\n- `unsafe_undefined` (default: `false`) -- substitute `void 0` if there is a\n  variable named `undefined` in scope (variable name will be mangled, typically\n  reduced to a single character)\n\n- `unused` (default: `true`) -- drop unreferenced functions and variables (simple\n  direct variable assignments do not count as references unless set to `\"keep_assign\"`)\n\n- `warnings` (default: `false`) -- display warnings when dropping unreachable\n  code or unused declarations etc.\n\n## Mangle options\n\n- `eval` (default `false`) -- Pass `true` to mangle names visible in scopes\n  where `eval` or `with` are used.\n\n- `keep_classnames` (default `false`) -- Pass `true` to not mangle class names.\n  Pass a regular expression to only keep class names matching that regex.\n  See also: the `keep_classnames` [compress option](#compress-options).\n\n- `keep_fnames` (default `false`) -- Pass `true` to not mangle function names.\n  Pass a regular expression to only keep class names matching that regex.\n  Useful for code relying on `Function.prototype.name`. See also: the `keep_fnames`\n  [compress option](#compress-options).\n\n- `module` (default `false`) -- Pass `true` an ES6 modules, where the toplevel\n  scope is not the global scope. Implies `toplevel`.\n\n- `reserved` (default `[]`) -- Pass an array of identifiers that should be\n  excluded from mangling. Example: `[\"foo\", \"bar\"]`.\n\n- `toplevel` (default `false`) -- Pass `true` to mangle names declared in the\n  top level scope.\n\n- `safari10` (default `false`) -- Pass `true` to work around the Safari 10 loop\n  iterator [bug](https://bugs.webkit.org/show_bug.cgi?id=171041)\n  \"Cannot declare a let variable twice\".\n  See also: the `safari10` [output option](#output-options).\n\nExamples:\n\n```javascript\n// test.js\nvar globalVar;\nfunction funcName(firstLongName, anotherLongName) {\n    var myVariable = firstLongName +  anotherLongName;\n}\n```\n```javascript\nvar code = fs.readFileSync(\"test.js\", \"utf8\");\n\nTerser.minify(code).code;\n// 'function funcName(a,n){}var globalVar;'\n\nTerser.minify(code, { mangle: { reserved: ['firstLongName'] } }).code;\n// 'function funcName(firstLongName,a){}var globalVar;'\n\nTerser.minify(code, { mangle: { toplevel: true } }).code;\n// 'function n(n,a){}var a;'\n```\n\n### Mangle properties options\n\n- `builtins` (default: `false`) — Use `true` to allow the mangling of builtin\n  DOM properties. Not recommended to override this setting.\n\n- `debug` (default: `false`) — Mangle names with the original name still present.\n  Pass an empty string `\"\"` to enable, or a non-empty string to set the debug suffix.\n\n- `keep_quoted` (default: `false`) — Only mangle unquoted property names.\n  - `true` -- Quoted property names are automatically reserved and any unquoted\n    property names will not be mangled.\n  - `\"strict\"` -- Advanced, all unquoted property names are mangled unless\n    explicitly reserved.\n\n- `regex` (default: `null`) — Pass a [RegExp literal or pattern string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp) to only mangle property matching the regular expression.\n\n- `reserved` (default: `[]`) — Do not mangle property names listed in the\n  `reserved` array.\n\n- `undeclared` (default: `false`) - Mangle those names when they are accessed\n  as properties of known top level variables but their declarations are never\n  found in input code. May be useful when only minifying parts of a project.\n  See [#397](https://github.com/terser/terser/issues/397) for more details.\n\n## Output options\n\nThe code generator tries to output shortest code possible by default.  In\ncase you want beautified output, pass `--beautify` (`-b`).  Optionally you\ncan pass additional arguments that control the code output:\n\n- `ascii_only` (default `false`) -- escape Unicode characters in strings and\n  regexps (affects directives with non-ascii characters becoming invalid)\n\n- `beautify` (default `true`) -- whether to actually beautify the output.\n  Passing `-b` will set this to true, but you might need to pass `-b` even\n  when you want to generate minified code, in order to specify additional\n  arguments, so you can use `-b beautify=false` to override it.\n\n- `braces` (default `false`) -- always insert braces in `if`, `for`,\n  `do`, `while` or `with` statements, even if their body is a single\n  statement.\n\n- `comments` (default `\"some\"`) -- by default it keeps JSDoc-style comments\n  that contain \"@license\" or \"@preserve\", pass `true` or `\"all\"` to preserve all\n  comments, `false` to omit comments in the output, a regular expression string\n  (e.g. `/^!/`) or a function.\n\n- `ecma` (default `5`) -- set output printing mode. Set `ecma` to `2015` or\n  greater to emit shorthand object properties - i.e.: `{a}` instead of `{a: a}`.\n  The `ecma` option will only change the output in direct control of the\n  beautifier. Non-compatible features in the abstract syntax tree will still\n  be output as is. For example: an `ecma` setting of `5` will **not** convert\n  ES6+ code to ES5.\n\n- `indent_level` (default `4`)\n\n- `indent_start` (default `0`) -- prefix all lines by that many spaces\n\n- `inline_script` (default `true`) -- escape HTML comments and the slash in\n  occurrences of `</script>` in strings\n\n- `keep_numbers` (default `false`) -- keep number literals as it was in original code\n (disables optimizations like converting `1000000` into `1e6`)\n\n- `keep_quoted_props` (default `false`) -- when turned on, prevents stripping\n  quotes from property names in object literals.\n\n- `max_line_len` (default `false`) -- maximum line length (for minified code)\n\n- `preamble` (default `null`) -- when passed it must be a string and\n  it will be prepended to the output literally.  The source map will\n  adjust for this text.  Can be used to insert a comment containing\n  licensing information, for example.\n\n- `quote_keys` (default `false`) -- pass `true` to quote all keys in literal\n  objects\n\n- `quote_style` (default `0`) -- preferred quote style for strings (affects\n  quoted property names and directives as well):\n  - `0` -- prefers double quotes, switches to single quotes when there are\n    more double quotes in the string itself. `0` is best for gzip size.\n  - `1` -- always use single quotes\n  - `2` -- always use double quotes\n  - `3` -- always use the original quotes\n\n- `preserve_annotations` -- (default `false`) -- Preserve [Terser annotations](#annotations) in the output.\n\n- `safari10` (default `false`) -- set this option to `true` to work around\n  the [Safari 10/11 await bug](https://bugs.webkit.org/show_bug.cgi?id=176685).\n  See also: the `safari10` [mangle option](#mangle-options).\n\n- `semicolons` (default `true`) -- separate statements with semicolons.  If\n  you pass `false` then whenever possible we will use a newline instead of a\n  semicolon, leading to more readable output of minified code (size before\n  gzip could be smaller; size after gzip insignificantly larger).\n\n- `shebang` (default `true`) -- preserve shebang `#!` in preamble (bash scripts)\n\n- `webkit` (default `false`) -- enable workarounds for WebKit bugs.\n  PhantomJS users should set this option to `true`.\n\n- `wrap_iife` (default `false`) -- pass `true` to wrap immediately invoked\n  function expressions. See\n  [#640](https://github.com/mishoo/UglifyJS2/issues/640) for more details.\n\n- `wrap_func_args` (default `true`) -- pass `false` if you do not want to wrap\n  function expressions that are passed as arguments, in parenthesis. See\n  [OptimizeJS](https://github.com/nolanlawson/optimize-js) for more details.\n\n# Miscellaneous\n\n### Keeping copyright notices or other comments\n\nYou can pass `--comments` to retain certain comments in the output.  By\ndefault it will keep JSDoc-style comments that contain \"@preserve\",\n\"@license\" or \"@cc_on\" (conditional compilation for IE).  You can pass\n`--comments all` to keep all the comments, or a valid JavaScript regexp to\nkeep only comments that match this regexp.  For example `--comments /^!/`\nwill keep comments like `/*! Copyright Notice */`.\n\nNote, however, that there might be situations where comments are lost.  For\nexample:\n```javascript\nfunction f() {\n    /** @preserve Foo Bar */\n    function g() {\n        // this function is never called\n    }\n    return something();\n}\n```\n\nEven though it has \"@preserve\", the comment will be lost because the inner\nfunction `g` (which is the AST node to which the comment is attached to) is\ndiscarded by the compressor as not referenced.\n\nThe safest comments where to place copyright information (or other info that\nneeds to be kept in the output) are comments attached to toplevel nodes.\n\n### The `unsafe` `compress` option\n\nIt enables some transformations that *might* break code logic in certain\ncontrived cases, but should be fine for most code.  It assumes that standard\nbuilt-in ECMAScript functions and classes have not been altered or replaced.\nYou might want to try it on your own code; it should reduce the minified size.\nSome examples of the optimizations made when this option is enabled:\n\n- `new Array(1, 2, 3)` or `Array(1, 2, 3)` → `[ 1, 2, 3 ]`\n- `new Object()` → `{}`\n- `String(exp)` or `exp.toString()` → `\"\" + exp`\n- `new Object/RegExp/Function/Error/Array (...)` → we discard the `new`\n- `\"foo bar\".substr(4)` → `\"bar\"`\n\n### Conditional compilation\n\nYou can use the `--define` (`-d`) switch in order to declare global\nvariables that Terser will assume to be constants (unless defined in\nscope).  For example if you pass `--define DEBUG=false` then, coupled with\ndead code removal Terser will discard the following from the output:\n```javascript\nif (DEBUG) {\n    console.log(\"debug stuff\");\n}\n```\n\nYou can specify nested constants in the form of `--define env.DEBUG=false`.\n\nTerser will warn about the condition being always false and about dropping\nunreachable code; for now there is no option to turn off only this specific\nwarning, you can pass `warnings=false` to turn off *all* warnings.\n\nAnother way of doing that is to declare your globals as constants in a\nseparate file and include it into the build.  For example you can have a\n`build/defines.js` file with the following:\n```javascript\nvar DEBUG = false;\nvar PRODUCTION = true;\n// etc.\n```\n\nand build your code like this:\n\n    terser build/defines.js js/foo.js js/bar.js... -c\n\nTerser will notice the constants and, since they cannot be altered, it\nwill evaluate references to them to the value itself and drop unreachable\ncode as usual.  The build will contain the `const` declarations if you use\nthem. If you are targeting < ES6 environments which does not support `const`,\nusing `var` with `reduce_vars` (enabled by default) should suffice.\n\n### Conditional compilation API\n\nYou can also use conditional compilation via the programmatic API. With the difference that the\nproperty name is `global_defs` and is a compressor property:\n\n```javascript\nvar result = Terser.minify(fs.readFileSync(\"input.js\", \"utf8\"), {\n    compress: {\n        dead_code: true,\n        global_defs: {\n            DEBUG: false\n        }\n    }\n});\n```\n\nTo replace an identifier with an arbitrary non-constant expression it is\nnecessary to prefix the `global_defs` key with `\"@\"` to instruct Terser\nto parse the value as an expression:\n```javascript\nTerser.minify(\"alert('hello');\", {\n    compress: {\n        global_defs: {\n            \"@alert\": \"console.log\"\n        }\n    }\n}).code;\n// returns: 'console.log(\"hello\");'\n```\n\nOtherwise it would be replaced as string literal:\n```javascript\nTerser.minify(\"alert('hello');\", {\n    compress: {\n        global_defs: {\n            \"alert\": \"console.log\"\n        }\n    }\n}).code;\n// returns: '\"console.log\"(\"hello\");'\n```\n\n### Using native Terser AST with `minify()`\n```javascript\n// example: parse only, produce native Terser AST\n\nvar result = Terser.minify(code, {\n    parse: {},\n    compress: false,\n    mangle: false,\n    output: {\n        ast: true,\n        code: false  // optional - faster if false\n    }\n});\n\n// result.ast contains native Terser AST\n```\n```javascript\n// example: accept native Terser AST input and then compress and mangle\n//          to produce both code and native AST.\n\nvar result = Terser.minify(ast, {\n    compress: {},\n    mangle: {},\n    output: {\n        ast: true,\n        code: true  // optional - faster if false\n    }\n});\n\n// result.ast contains native Terser AST\n// result.code contains the minified code in string form.\n```\n\n\n### Annotations\n\nAnnotations in Terser are a way to tell it to treat a certain function call differently. The following annotations are available:\n\n * `/*@__INLINE__*/` - forces a function to be inlined somewhere.\n * `/*@__NOINLINE__*/` - Makes sure the called function is not inlined into the call site.\n * `/*@__PURE__*/` - Marks a function call as pure. That means, it can safely be dropped.\n\nYou can use either a `@` sign at the start, or a `#`.\n\nHere are some examples on how to use them:\n\n```javascript\n/*@__INLINE__*/\nfunction_always_inlined_here()\n\n/*#__NOINLINE__*/\nfunction_cant_be_inlined_into_here()\n\nconst x = /*#__PURE__*/i_am_dropped_if_x_is_not_used()\n```\n\n\n### Working with Terser AST\n\nTraversal and transformation of the native AST can be performed through\n[`TreeWalker`](https://github.com/fabiosantoscode/terser/blob/master/lib/ast.js) and\n[`TreeTransformer`](https://github.com/fabiosantoscode/terser/blob/master/lib/transform.js)\nrespectively.\n\nLargely compatible native AST examples can be found in the original UglifyJS\ndocumentation. See: [tree walker](http://lisperator.net/uglifyjs/walk) and\n[tree transform](http://lisperator.net/uglifyjs/transform).\n\n### ESTree / SpiderMonkey AST\n\nTerser has its own abstract syntax tree format; for\n[practical reasons](http://lisperator.net/blog/uglifyjs-why-not-switching-to-spidermonkey-ast/)\nwe can't easily change to using the SpiderMonkey AST internally.  However,\nTerser now has a converter which can import a SpiderMonkey AST.\n\nFor example [Acorn][acorn] is a super-fast parser that produces a\nSpiderMonkey AST.  It has a small CLI utility that parses one file and dumps\nthe AST in JSON on the standard output.  To use Terser to mangle and\ncompress that:\n\n    acorn file.js | terser -p spidermonkey -m -c\n\nThe `-p spidermonkey` option tells Terser that all input files are not\nJavaScript, but JS code described in SpiderMonkey AST in JSON.  Therefore we\ndon't use our own parser in this case, but just transform that AST into our\ninternal AST.\n\n### Use Acorn for parsing\n\nMore for fun, I added the `-p acorn` option which will use Acorn to do all\nthe parsing.  If you pass this option, Terser will `require(\"acorn\")`.\n\nAcorn is really fast (e.g. 250ms instead of 380ms on some 650K code), but\nconverting the SpiderMonkey tree that Acorn produces takes another 150ms so\nin total it's a bit more than just using Terser's own parser.\n\n[acorn]: https://github.com/ternjs/acorn\n[sm-spec]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k\n\n### Terser Fast Minify Mode\n\nIt's not well known, but whitespace removal and symbol mangling accounts\nfor 95% of the size reduction in minified code for most JavaScript - not\nelaborate code transforms. One can simply disable `compress` to speed up\nTerser builds by 3 to 4 times.\n\n| d3.js | size | gzip size | time (s) |\n|   --- | ---: |      ---: |     ---: |\n| original                                    | 451,131 | 108,733 |     - |\n| terser@3.7.5 mangle=false, compress=false   | 316,600 |  85,245 |  0.82 |\n| terser@3.7.5 mangle=true, compress=false    | 220,216 |  72,730 |  1.45 |\n| terser@3.7.5 mangle=true, compress=true     | 212,046 |  70,954 |  5.87 |\n| babili@0.1.4                                | 210,713 |  72,140 | 12.64 |\n| babel-minify@0.4.3                          | 210,321 |  72,242 | 48.67 |\n| babel-minify@0.5.0-alpha.01eac1c3           | 210,421 |  72,238 | 14.17 |\n\nTo enable fast minify mode from the CLI use:\n```\nterser file.js -m\n```\nTo enable fast minify mode with the API use:\n```js\nTerser.minify(code, { compress: false, mangle: true });\n```\n\n#### Source maps and debugging\n\nVarious `compress` transforms that simplify, rearrange, inline and remove code\nare known to have an adverse effect on debugging with source maps. This is\nexpected as code is optimized and mappings are often simply not possible as\nsome code no longer exists. For highest fidelity in source map debugging\ndisable the `compress` option and just use `mangle`.\n\n### Compiler assumptions\n\nTo allow for better optimizations, the compiler makes various assumptions:\n\n- `.toString()` and `.valueOf()` don't have side effects, and for built-in\n  objects they have not been overridden.\n- `undefined`, `NaN` and `Infinity` have not been externally redefined.\n- `arguments.callee`, `arguments.caller` and `Function.prototype.caller` are not used.\n- The code doesn't expect the contents of `Function.prototype.toString()` or\n  `Error.prototype.stack` to be anything in particular.\n- Getting and setting properties on a plain object does not cause other side effects\n  (using `.watch()` or `Proxy`).\n- Object properties can be added, removed and modified (not prevented with\n  `Object.defineProperty()`, `Object.defineProperties()`, `Object.freeze()`,\n  `Object.preventExtensions()` or `Object.seal()`).\n- `document.all` is not `== null`\n- Assigning properties to a class doesn't have side effects and does not throw.\n\n### Build Tools and Adaptors using Terser\n\nhttps://www.npmjs.com/browse/depended/terser\n\n### Replacing `uglify-es` with `terser` in a project using `yarn`\n\nA number of JS bundlers and uglify wrappers are still using buggy versions\nof `uglify-es` and have not yet upgraded to `terser`. If you are using `yarn`\nyou can add the following alias to your project's `package.json` file:\n\n```js\n  \"resolutions\": {\n    \"uglify-es\": \"npm:terser\"\n  }\n```\n\nto use `terser` instead of `uglify-es` in all deeply nested dependencies\nwithout changing any code.\n\nNote: for this change to take effect you must run the following commands\nto remove the existing `yarn` lock file and reinstall all packages:\n\n```\n$ rm -rf node_modules yarn.lock\n$ yarn\n```\n\n# Reporting issues\n\nIn the terser CLI we use [source-map-support](https://npmjs.com/source-map-support) to produce good error stacks. In your own app, you're expected to enable source-map-support (read their docs) to have nice stack traces that will make good issues.\n\n# README.md Patrons:\n\n*note*: You can support this project on patreon: <a target=\"_blank\" rel=\"nofollow\" href=\"https://www.patreon.com/fabiosantoscode\"><img src=\"https://c5.patreon.com/external/logo/<EMAIL>\" alt=\"patron\" width=\"100px\" height=\"auto\"></a>. Check out [PATRONS.md](https://github.com/terser/terser/blob/master/PATRONS.md) for our first-tier patrons.\n\nThese are the second-tier patrons. Great thanks for your support!\n\n * CKEditor ![](https://c10.patreonusercontent.com/3/eyJoIjoxMDAsInciOjEwMH0%3D/patreon-media/p/user/15452278/f8548dcf48d740619071e8d614459280/1?token-time=2145916800&token-hash=SIQ54PhIPHv3M7CVz9LxS8_8v4sOw4H304HaXsXj8MM%3D)\n * 38elements ![](https://c10.patreonusercontent.com/3/eyJ3IjoyMDB9/patreon-media/p/user/12501844/88e7fc5dd62d45c6a5626533bbd48cfb/1?token-time=2145916800&token-hash=c3AsQ5T0IQWic0zKxFHu-bGGQJkXQFvafvJ4bPerFR4%3D)\n\n## Contributors\n\n### Code Contributors\n\nThis project exists thanks to all the people who contribute. [[Contribute](CONTRIBUTING.md)].\n<a href=\"https://github.com/terser/terser/graphs/contributors\"><img src=\"https://opencollective.com/terser/contributors.svg?width=890&button=false\" /></a>\n\n### Financial Contributors\n\nBecome a financial contributor and help us sustain our community. [[Contribute](https://opencollective.com/terser/contribute)]\n\n#### Individuals\n\n<a href=\"https://opencollective.com/terser\"><img src=\"https://opencollective.com/terser/individuals.svg?width=890\"></a>\n\n#### Organizations\n\nSupport this project with your organization. Your logo will show up here with a link to your website. [[Contribute](https://opencollective.com/terser/contribute)]\n\n<a href=\"https://opencollective.com/terser/organization/0/website\"><img src=\"https://opencollective.com/terser/organization/0/avatar.svg\"></a>\n<a href=\"https://opencollective.com/terser/organization/1/website\"><img src=\"https://opencollective.com/terser/organization/1/avatar.svg\"></a>\n<a href=\"https://opencollective.com/terser/organization/2/website\"><img src=\"https://opencollective.com/terser/organization/2/avatar.svg\"></a>\n<a href=\"https://opencollective.com/terser/organization/3/website\"><img src=\"https://opencollective.com/terser/organization/3/avatar.svg\"></a>\n<a href=\"https://opencollective.com/terser/organization/4/website\"><img src=\"https://opencollective.com/terser/organization/4/avatar.svg\"></a>\n<a href=\"https://opencollective.com/terser/organization/5/website\"><img src=\"https://opencollective.com/terser/organization/5/avatar.svg\"></a>\n<a href=\"https://opencollective.com/terser/organization/6/website\"><img src=\"https://opencollective.com/terser/organization/6/avatar.svg\"></a>\n<a href=\"https://opencollective.com/terser/organization/7/website\"><img src=\"https://opencollective.com/terser/organization/7/avatar.svg\"></a>\n<a href=\"https://opencollective.com/terser/organization/8/website\"><img src=\"https://opencollective.com/terser/organization/8/avatar.svg\"></a>\n<a href=\"https://opencollective.com/terser/organization/9/website\"><img src=\"https://opencollective.com/terser/organization/9/avatar.svg\"></a>\n", "licenseText": "UglifyJS is released under the BSD license:\n\nCopyright 2012-2018 (c) <PERSON><PERSON> <<EMAIL>>\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions\nare met:\n\n    * Redistributions of source code must retain the above\n      copyright notice, this list of conditions and the following\n      disclaimer.\n\n    * Redistributions in binary form must reproduce the above\n      copyright notice, this list of conditions and the following\n      disclaimer in the documentation and/or other materials\n      provided with the distribution.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDER “AS IS” AND ANY\nEXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\nIMPLIED WARRANTIES OF <PERSON><PERSON><PERSON>NTABILITY AND FITNESS FOR A PARTICULAR\nPURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER BE\nLIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY,\nOR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,\nPROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\nPROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\nTHEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR\nTORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF\nTHE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF\nSUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/terser/-/terser-4.8.0.tgz#63056343d7c70bb29f3af665865a46fe03a0df17", "type": "tarball", "reference": "https://registry.yarnpkg.com/terser/-/terser-4.8.0.tgz", "hash": "63056343d7c70bb29f3af665865a46fe03a0df17", "integrity": "sha512-EAPipTNeWsb/3wLPeup1tVPaXfIaU68xMnVdPafIL1TV05OhASArYyIfFvnvJCNrR2NIOvDVNNTFRa+Re2MWyw==", "registry": "npm", "packageName": "terser", "cacheIntegrity": "sha512-EAPipTNeWsb/3wLPeup1tVPaXfIaU68xMnVdPafIL1TV05OhASArYyIfFvnvJCNrR2NIOvDVNNTFRa+Re2MWyw== sha1-YwVjQ9fHC7KfOvZlhlpG/gOg3xc="}, "registry": "npm", "hash": "63056343d7c70bb29f3af665865a46fe03a0df17"}