{"manifest": {"name": "color-convert", "description": "Plain color conversion functions", "version": "1.9.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Qix-/color-convert.git"}, "scripts": {"pretest": "xo", "test": "node test/basic.js"}, "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "devDependencies": {"chalk": "1.1.1", "xo": "0.11.2"}, "dependencies": {"color-name": "1.1.3"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-color-convert-1.9.3-bb71850690e1f136567de629d2d5471deda4c1e8-integrity\\node_modules\\color-convert\\package.json", "readmeFilename": "README.md", "readme": "# color-convert\n\n[![Build Status](https://travis-ci.org/Qix-/color-convert.svg?branch=master)](https://travis-ci.org/Qix-/color-convert)\n\nColor-convert is a color conversion library for JavaScript and node.\nIt converts all ways between `rgb`, `hsl`, `hsv`, `hwb`, `cmyk`, `ansi`, `ansi16`, `hex` strings, and CSS `keyword`s (will round to closest):\n\n```js\nvar convert = require('color-convert');\n\nconvert.rgb.hsl(140, 200, 100);             // [96, 48, 59]\nconvert.keyword.rgb('blue');                // [0, 0, 255]\n\nvar rgbChannels = convert.rgb.channels;     // 3\nvar cmykChannels = convert.cmyk.channels;   // 4\nvar ansiChannels = convert.ansi16.channels; // 1\n```\n\n# Install\n\n```console\n$ npm install color-convert\n```\n\n# API\n\nSimply get the property of the _from_ and _to_ conversion that you're looking for.\n\nAll functions have a rounded and unrounded variant. By default, return values are rounded. To get the unrounded (raw) results, simply tack on `.raw` to the function.\n\nAll 'from' functions have a hidden property called `.channels` that indicates the number of channels the function expects (not including alpha).\n\n```js\nvar convert = require('color-convert');\n\n// Hex to LAB\nconvert.hex.lab('DEADBF');         // [ 76, 21, -2 ]\nconvert.hex.lab.raw('DEADBF');     // [ 75.56213190997677, 20.653827952644754, -2.290532499330533 ]\n\n// RGB to CMYK\nconvert.rgb.cmyk(167, 255, 4);     // [ 35, 0, 98, 0 ]\nconvert.rgb.cmyk.raw(167, 255, 4); // [ 34.***************, 0, 98.**************, 0 ]\n```\n\n### Arrays\nAll functions that accept multiple arguments also support passing an array.\n\nNote that this does **not** apply to functions that convert from a color that only requires one value (e.g. `keyword`, `ansi256`, `hex`, etc.)\n\n```js\nvar convert = require('color-convert');\n\nconvert.rgb.hex(123, 45, 67);      // '7B2D43'\nconvert.rgb.hex([123, 45, 67]);    // '7B2D43'\n```\n\n## Routing\n\nConversions that don't have an _explicitly_ defined conversion (in [conversions.js](conversions.js)), but can be converted by means of sub-conversions (e.g. XYZ -> **RGB** -> CMYK), are automatically routed together. This allows just about any color model supported by `color-convert` to be converted to any other model, so long as a sub-conversion path exists. This is also true for conversions requiring more than one step in between (e.g. LCH -> **LAB** -> **XYZ** -> **RGB** -> Hex).\n\nKeep in mind that extensive conversions _may_ result in a loss of precision, and exist only to be complete. For a list of \"direct\" (single-step) conversions, see [conversions.js](conversions.js).\n\n# Contribute\n\nIf there is a new model you would like to support, or want to add a direct conversion between two existing models, please send us a pull request.\n\n# License\nCopyright &copy; 2011-2016, Heather Arthur and Josh Junon. Licensed under the [MIT License](LICENSE).\n", "licenseText": "Copyright (c) 2011-2016 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/color-convert/-/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8", "type": "tarball", "reference": "https://registry.yarnpkg.com/color-convert/-/color-convert-1.9.3.tgz", "hash": "bb71850690e1f136567de629d2d5471deda4c1e8", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "registry": "npm", "packageName": "color-convert", "cacheIntegrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg== sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg="}, "registry": "npm", "hash": "bb71850690e1f136567de629d2d5471deda4c1e8"}