{"manifest": {"name": "get-value", "description": "Use property paths (`a.b.c`) to get a nested value from an object.", "version": "2.0.6", "homepage": "https://github.com/jonschlinkert/get-value", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/get-value.git"}, "bugs": {"url": "https://github.com/jonschlinkert/get-value/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"ansi-bold": "^0.1.1", "arr-reduce": "^1.0.1", "benchmarked": "^0.1.4", "dot-prop": "^2.2.0", "getobject": "^0.1.0", "gulp": "^3.9.0", "gulp-eslint": "^1.1.1", "gulp-format-md": "^0.1.5", "gulp-istanbul": "^0.10.2", "gulp-mocha": "^2.1.3", "isobject": "^2.0.0", "matched": "^0.3.2", "minimist": "^1.2.0"}, "keywords": ["get", "key", "nested", "object", "path", "paths", "prop", "properties", "property", "props", "segment", "value", "values"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["has-any", "has-any-deep", "has-value", "set-value", "unset-value"]}, "reflinks": ["verb", "verb-readme-generator"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-get-value-2.0.6-dc15ca1c672387ca76bd37ac0a395ba2042a2c28-integrity\\node_modules\\get-value\\package.json", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2016, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/get-value/-/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28", "type": "tarball", "reference": "https://registry.yarnpkg.com/get-value/-/get-value-2.0.6.tgz", "hash": "dc15ca1c672387ca76bd37ac0a395ba2042a2c28", "integrity": "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=", "registry": "npm", "packageName": "get-value", "cacheIntegrity": "sha512-Ln0UQDlxH1BapMu3GPtf7CuYNwRZf2gwCuPqbyG6pB8WfmFpzqcy4xtAaAMUhnNqjMKTiCPZG2oMT3YSx8U2NA== sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="}, "registry": "npm", "hash": "dc15ca1c672387ca76bd37ac0a395ba2042a2c28"}