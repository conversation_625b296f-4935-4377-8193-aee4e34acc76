{"manifest": {"name": "brorand", "version": "1.1.0", "description": "Random number generator for browsers and node.js", "main": "index.js", "scripts": {"test": "mocha --reporter=spec test/**/*-test.js"}, "repository": {"type": "git", "url": "**************:indutny/brorand"}, "keywords": ["Random", "RNG", "browser", "crypto"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/indutny/brorand/issues"}, "homepage": "https://github.com/indutny/brorand", "devDependencies": {"mocha": "^2.0.1"}, "browser": {"crypto": false}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-brorand-1.1.0-12c25efe40a45e3c323eb8675a0a0ce57b22371f-integrity\\node_modules\\brorand\\package.json", "readmeFilename": "README.md", "readme": "# Brorand\n\n#### LICENSE\n\nThis software is licensed under the MIT License.\n\nCopyright Fedor Indutny, 2014.\n\nPermission is hereby granted, free of charge, to any person obtaining a\ncopy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to permit\npersons to whom the Software is furnished to do so, subject to the\nfollowing conditions:\n\nThe above copyright notice and this permission notice shall be included\nin all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\nOR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN\nNO EVENT SHALL THE AUTHORS OR COPY<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM,\nDAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\nOTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\nUSE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/brorand/-/brorand-1.1.0.tgz#12c25efe40a45e3c323eb8675a0a0ce57b22371f", "type": "tarball", "reference": "https://registry.yarnpkg.com/brorand/-/brorand-1.1.0.tgz", "hash": "12c25efe40a45e3c323eb8675a0a0ce57b22371f", "integrity": "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=", "registry": "npm", "packageName": "brorand", "cacheIntegrity": "sha512-cKV8tMCEpQs4hK/ik71d6LrPOnpkpGBR0wzxqr68g2m/LB2GxVYQroAjMJZRVM1Y4BCjCKc3vAamxSzOY2RP+w== sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8="}, "registry": "npm", "hash": "12c25efe40a45e3c323eb8675a0a0ce57b22371f"}