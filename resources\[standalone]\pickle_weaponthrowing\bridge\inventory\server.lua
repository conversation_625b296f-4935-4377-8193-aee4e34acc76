Inventory = {}

-- Framework
function GetPlayerFromId(source)
    if GetResourceState('es_extended') == 'started' then
        return ESX.GetPlayerFromId(source)
    elseif GetResourceState('qb-core') == 'started' then
        return QBCore.Functions.GetPlayer(source)
    end
end

function GetPlayer(source)
    if GetResourceState('es_extended') == 'started' then
        return ESX.GetPlayerFromId(source)
    elseif GetResourceState('qb-core') == 'started' then
        return QBCore.Functions.GetPlayer(source)
    end
end

function getInventory(player)
    if GetResourceState('es_extended') == 'started' then
        return player.getInventory()
    elseif GetResourceState('qb-core') == 'started' then
        return player.PlayerData.items
    end
end

if GetResourceState('ox_inventory') == 'started' then
    Inventory.GetWeapon = function(source, name)
        local data = exports.ox_inventory:GetCurrentWeapon(source)
        if data then
            return 1, data
        else
            return 0
        end
    end

    Inventory.AddWeapon = function(source, data)
        exports.ox_inventory:AddItem(source, data.weapon, 1, data.metadata, data.slot)
    end

    Inventory.RemoveWeapon = function(source, data)
        exports.ox_inventory:RemoveItem(source, data.weapon, 1, data.metadata, data.slot)
    end

    Inventory.CreateWeaponData = function(source, data, weaponData)
        for k, v in pairs(weaponData) do
            data[k] = v
        end
        return data
    end
elseif GetResourceState('qb-inventory') == 'started' then
    Inventory.PlayerWeapons = {}

    Inventory.GetWeapon = function(source, name)
        local data = Inventory.PlayerWeapons[source]

        local Player = GetPlayer(source)
        
        if Config.OldInventory == "ox" then 
            local items = exports['qb-inventory']:GetPlayerItems(source) 
        else
            local items = Player.PlayerData.items
        end
        
        print("items", json.encode(items))    
        print("DATA " , json.encode(data))

        local myItems = exports['qb-inventory']:GetItemBySlot(source, data.slot)

        if myItems then
            if myItems.name == data.name then
                return 1, myItems
            end
        end
        return 0
    end

    Inventory.AddWeapon = function(source, data)
        local Player = GetPlayer(source)
        -- Player.Functions.AddItem(data.name, 1, data.slot, data.info)
        exports['qb-inventory']:AddItem(source, data.name, 1, data.slot, data.info)
    end

    Inventory.RemoveWeapon = function(source, data)
        local Player = GetPlayer(source)
        -- Player.Functions.RemoveItem(data.name, 1, data.slot)
        exports['qb-inventory']:RemoveItem(source, data.name, 1, data.slot)
    end

    Inventory.CreateWeaponData = function(source, data, weaponData)
        for k, v in pairs(weaponData) do
            data[k] = v
        end
        return data
    end

    RegisterNetEvent('pickle_weaponthrowing:SetCurrentWeapon', function(weaponData)
        local source = source
        Inventory.PlayerWeapons[source] = weaponData
    end)
elseif GetResourceState('qs-inventory') == 'started' then
    Inventory.PlayerWeapons = {}

    Inventory.GetWeapon = function(source, name)
        local data = exports['qs-inventory']:GetCurrentWeapon(source)
        if data then
            return 1, data
        else
            return 0
        end
    end

    Inventory.AddWeapon = function(source, data)
        exports['qs-inventory']:AddItem(source, data.name, 1, data.slot, data.info)
    end

    Inventory.RemoveWeapon = function(source, data)
        exports['qs-inventory']:RemoveItem(source, data.name, 1, data.slot, nil)
    end

    Inventory.CreateWeaponData = function(source, data, weaponData)
        for k, v in pairs(weaponData) do
            data[k] = v
        end
        return data
    end
end
