local QBCore = exports['qb-core']:GetCoreObject()

-- Lokální proměnné pro GPS
local currentWaypoint = nil
local isNavigating = false
local routeBlips = {}
local savedLocations = {}

-- NUI Callbacks pro GPS
RegisterNUICallback('setWaypoint', function(data, cb)
    SetGPSWaypoint(data.x, data.y, data.label)
    cb('ok')
end)

RegisterNUICallback('clearWaypoint', function(data, cb)
    ClearGPSWaypoint()
    cb('ok')
end)

RegisterNUICallback('getCurrentLocation', function(data, cb)
    local location = GetCurrentPlayerLocation()
    cb(location)
end)

RegisterNUICallback('saveLocation', function(data, cb)
    SaveLocation(data.name, data.x, data.y)
    cb('ok')
end)

RegisterNUICallback('getSavedLocations', function(data, cb)
    QBCore.Functions.TriggerCallback('qb-smartphone2:server:getSavedLocations', function(locations)
        cb(locations)
    end)
end)

RegisterNUICallback('deleteLocation', function(data, cb)
    TriggerServerEvent('qb-smartphone2:server:deleteLocation', data.id)
    cb('ok')
end)

RegisterNUICallback('shareLocation', function(data, cb)
    ShareLocation(data.phoneNumber, data.duration)
    cb('ok')
end)

-- Funkce pro nastavení GPS waypoint
function SetGPSWaypoint(x, y, label)
    if currentWaypoint then
        RemoveBlip(currentWaypoint)
    end
    
    currentWaypoint = AddBlipForCoord(x, y, 0.0)
    SetBlipSprite(currentWaypoint, Config.BlipSprite)
    SetBlipColour(currentWaypoint, Config.BlipColor)
    SetBlipRoute(currentWaypoint, true)
    SetBlipRouteColour(currentWaypoint, Config.RouteColor)
    
    if label then
        BeginTextCommandSetBlipName('STRING')
        AddTextComponentSubstringPlayerName(label)
        EndTextCommandSetBlipName(currentWaypoint)
    end
    
    isNavigating = true
    
    QBCore.Functions.Notify('GPS nastaven na: ' .. (label or 'Neznámá lokace'), 'success')
    
    -- Oznámení NUI
    SendNUIMessage({
        action = 'waypointSet',
        data = { x = x, y = y, label = label }
    })
end

-- Funkce pro vymazání GPS waypoint
function ClearGPSWaypoint()
    if currentWaypoint then
        RemoveBlip(currentWaypoint)
        currentWaypoint = nil
    end
    
    isNavigating = false
    
    QBCore.Functions.Notify('GPS vymazán', 'info')
    
    -- Oznámení NUI
    SendNUIMessage({
        action = 'waypointCleared'
    })
end

-- Funkce pro získání současné lokace hráče
function GetCurrentPlayerLocation()
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local heading = GetEntityHeading(ped)
    local streetHash, crossingHash = GetStreetNameAtCoord(coords.x, coords.y, coords.z)
    local streetName = GetStreetNameFromHashKey(streetHash)
    local crossingName = crossingHash ~= 0 and GetStreetNameFromHashKey(crossingHash) or nil
    
    local location = {
        x = math.floor(coords.x * 100) / 100,
        y = math.floor(coords.y * 100) / 100,
        z = math.floor(coords.z * 100) / 100,
        heading = math.floor(heading * 100) / 100,
        street = streetName,
        crossing = crossingName,
        area = GetLabelText(GetNameOfZone(coords.x, coords.y, coords.z))
    }
    
    return location
end

-- Funkce pro uložení lokace
function SaveLocation(name, x, y)
    local coords = x and y and vector3(x, y, 0.0) or GetEntityCoords(PlayerPedId())
    
    TriggerServerEvent('qb-smartphone2:server:saveLocation', {
        name = name,
        x = coords.x,
        y = coords.y,
        z = coords.z
    })
end

-- Funkce pro sdílení lokace
function ShareLocation(phoneNumber, duration)
    local location = GetCurrentPlayerLocation()
    
    TriggerServerEvent('qb-smartphone2:server:shareLocation', {
        phoneNumber = phoneNumber,
        location = location,
        duration = duration or 3600 -- 1 hodina default
    })
end

-- Funkce pro navigaci k uložené lokaci
function NavigateToSavedLocation(locationData)
    SetGPSWaypoint(locationData.x, locationData.y, locationData.name)
end

-- Funkce pro získání vzdálenosti k cíli
function GetDistanceToWaypoint()
    if not currentWaypoint then return nil end
    
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local waypointCoords = GetBlipInfoIdCoord(currentWaypoint)
    
    return #(coords - waypointCoords)
end

-- Funkce pro kontrolu, zda hráč dorazil k cíli
CreateThread(function()
    while true do
        Wait(1000)
        
        if isNavigating and currentWaypoint then
            local distance = GetDistanceToWaypoint()
            
            if distance and distance < 10.0 then
                QBCore.Functions.Notify('Dorazil jsi k cíli!', 'success')
                ClearGPSWaypoint()
            end
        end
    end
end)

-- Event handlery
RegisterNetEvent('qb-smartphone2:client:setWaypoint', function(x, y, label)
    SetGPSWaypoint(x, y, label)
end)

RegisterNetEvent('qb-smartphone2:client:clearWaypoint', function()
    ClearGPSWaypoint()
end)

RegisterNetEvent('qb-smartphone2:client:receiveSharedLocation', function(locationData)
    QBCore.Functions.Notify('Obdržel jsi sdílenou lokaci od ' .. locationData.senderName, 'info')
    
    -- Zobrazení v NUI
    SendNUIMessage({
        action = 'sharedLocationReceived',
        data = locationData
    })
    
    -- Automatické nastavení waypoint po 5 sekundách pokud hráč neklikne
    SetTimeout(5000, function()
        if not isNavigating then
            SetGPSWaypoint(locationData.location.x, locationData.location.y, 'Sdílená lokace')
        end
    end)
end)

RegisterNetEvent('qb-smartphone2:client:locationSaved', function()
    QBCore.Functions.Notify('Lokace byla uložena!', 'success')
    
    -- Aktualizace seznamu v NUI
    SendNUIMessage({
        action = 'refreshSavedLocations'
    })
end)

RegisterNetEvent('qb-smartphone2:client:locationDeleted', function()
    QBCore.Functions.Notify('Lokace byla smazána!', 'success')
    
    -- Aktualizace seznamu v NUI
    SendNUIMessage({
        action = 'refreshSavedLocations'
    })
end)

-- Přednastavené lokace (důležitá místa ve městě)
local PresetLocations = {
    {
        name = 'Nemocnice',
        x = 1839.6,
        y = 3672.93,
        category = 'medical'
    },
    {
        name = 'Policejní stanice',
        x = 428.23,
        y = -981.28,
        category = 'government'
    },
    {
        name = 'Autoservis',
        x = -347.88,
        y = -133.71,
        category = 'service'
    },
    {
        name = 'Banka',
        x = 150.02,
        y = -1040.54,
        category = 'finance'
    },
    {
        name = 'Letište',
        x = -1037.86,
        y = -2737.73,
        category = 'transport'
    },
    {
        name = 'Přístav',
        x = -1686.72,
        y = -1072.25,
        category = 'transport'
    }
}

-- Callback pro získání přednastavených lokací
RegisterNUICallback('getPresetLocations', function(data, cb)
    cb(PresetLocations)
end)

-- Funkce pro navigaci k přednastavené lokaci
RegisterNUICallback('navigateToPreset', function(data, cb)
    local location = nil
    
    for _, preset in ipairs(PresetLocations) do
        if preset.name == data.name then
            location = preset
            break
        end
    end
    
    if location then
        SetGPSWaypoint(location.x, location.y, location.name)
        cb({ success = true })
    else
        cb({ success = false, error = 'Lokace nenalezena' })
    end
end)

-- Export funkce pro jiné scripty
exports('SetWaypoint', SetGPSWaypoint)
exports('ClearWaypoint', ClearGPSWaypoint)
exports('GetCurrentLocation', GetCurrentPlayerLocation)
exports('IsNavigating', function() return isNavigating end)
exports('GetDistanceToWaypoint', GetDistanceToWaypoint)

-- Cleanup při unload
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        ClearGPSWaypoint()
    end
end)
