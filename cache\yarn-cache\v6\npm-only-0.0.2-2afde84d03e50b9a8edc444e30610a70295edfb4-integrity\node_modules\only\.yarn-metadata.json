{"manifest": {"name": "only", "version": "0.0.2", "description": "return whitelisted properties of an object", "keywords": ["utility", "util", "object", "whitelist"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/visionmedia/node-only"}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "main": "index", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-only-0.0.2-2afde84d03e50b9a8edc444e30610a70295edfb4-integrity\\node_modules\\only\\package.json", "readmeFilename": "Readme.md", "readme": "\n# only\n\n  Return whitelisted properties of an object.\n\n## Installation\n\n    $ npm install only\n\n## API\n\n An array or space-delimited string may be given:\n\n```js\nvar obj = {\n  name: 'tobi',\n  last: 'holowaychuk',\n  email: '<EMAIL>',\n  _id: '12345'\n};\n\nvar user = only(obj, 'name last email');\n```\n\nyields:\n\n```js\n{\n  name: 'tobi',\n  last: 'holowaychuk',\n  email: '<EMAIL>'\n}\n```\n\n## License \n\n(The MIT License)\n\nCopyright (c) 2012 TJ Holowaychuk &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.", "license": "MIT"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/only/-/only-0.0.2.tgz#2afde84d03e50b9a8edc444e30610a70295edfb4", "type": "tarball", "reference": "https://registry.yarnpkg.com/only/-/only-0.0.2.tgz", "hash": "2afde84d03e50b9a8edc444e30610a70295edfb4", "integrity": "sha1-Kv3oTQPlC5qO3EROMGEKcCle37Q=", "registry": "npm", "packageName": "only", "cacheIntegrity": "sha512-Fvw+Jemq5fjjyWz6CpKx6w9s7xxqo3+JCyM0WXWeCSOboZ8ABkyvP8ID4CZuChA/wxSx+XSJmdOm8rGVyJ1hdQ== sha1-Kv3oTQPlC5qO3EROMGEKcCle37Q="}, "registry": "npm", "hash": "2afde84d03e50b9a8edc444e30610a70295edfb4"}