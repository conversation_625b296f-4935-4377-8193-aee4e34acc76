{"manifest": {"name": "@citizenfx/http-wrapper", "version": "0.2.2", "description": "A HTTP wrapper for CitizenFX SetHttpHandler.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": {}, "license": "MIT", "dependencies": {}, "repository": {"type": "git", "url": "https://github.com/citizenfx/http-wrapper.git"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@citizenfx-http-wrapper-0.2.2-f3306c6727cba987711fb0643529c473e19a32ce-integrity\\node_modules\\@citizenfx\\http-wrapper\\package.json", "readmeFilename": "README.md", "readme": "# http-wrapper\n\nA quick HTTP wrapper for CitizenFX servers. Can't be used with Express yet as it directly relies on `http` from Node.js, use Koa/similar instead.\n\n## Usage\n\n```js\nconst { setHttpCallback } = require('@citizenfx/http-wrapper');\nconst Koa = require('koa');\n\nconst app = new Koa();\napp.use(async ctx => {\n\tctx.body = 'Hello World!'\n});\n\n// instead of app.listen()\nsetHttpCallback(app.callback());\n```"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@citizenfx/http-wrapper/-/http-wrapper-0.2.2.tgz#f3306c6727cba987711fb0643529c473e19a32ce", "type": "tarball", "reference": "https://registry.yarnpkg.com/@citizenfx/http-wrapper/-/http-wrapper-0.2.2.tgz", "hash": "f3306c6727cba987711fb0643529c473e19a32ce", "integrity": "sha512-hFyrWN2U30KFRgYteyqs7PZ+9aXyiH3tsgOQL/vcOp9dGAZAyRSHDotJYZivTEpjRN4dMbgbZ2h9TgoDFRKi9w==", "registry": "npm", "packageName": "@citizenfx/http-wrapper", "cacheIntegrity": "sha512-hFyrWN2U30KFRgYteyqs7PZ+9aXyiH3tsgOQL/vcOp9dGAZAyRSHDotJYZivTEpjRN4dMbgbZ2h9TgoDFRKi9w== sha1-8zBsZyfLqYdxH7BkNSnEc+GaMs4="}, "registry": "npm", "hash": "f3306c6727cba987711fb0643529c473e19a32ce"}