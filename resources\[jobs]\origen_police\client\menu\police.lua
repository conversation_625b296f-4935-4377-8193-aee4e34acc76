local L0_1, L1_1, L2_1, L3_1, L4_1, L5_1
L0_1 = nil
L1_1 = false
L2_1 = RegisterNUICallback
L3_1 = "LoadPolicePage"
RegisterNUICallback("LoadPolicePage", function(data, cb)
  local response = {}
  local playerData = exports.origen_police:FW_GetPlayerData(true)

  -- Handle home page
  if data.page == "home" then
      response.service = playerData.job.onduty
      
      -- Get police count asynchronously
      local policeCount = nil
      exports.origen_police:FW_TriggerCallback("origen_police:server:GetPoliceCount", function(count)
          policeCount = count
          response.cops = count
      end)
      
      -- Wait for police count
      while policeCount == nil do
          Citizen.Wait(0)
      end
      
      return cb(response)
  end

  -- Handle radio and central pages
  if data.page == "radio" or data.page == "central" then
      if isLoadingHeatAlerts then return end

      -- Check for radio in inventory
      response = false
      for _, item in pairs(playerData.items) do
          if item.name == "radio" then
              local freq = exports.origen_police:GetMultiFrec()
              response = freq or "none"
              exports.origen_police:Toggle(true)
              break
          end
      end

      -- Handle no radio case
      if not response then
          local needRadio = exports.origen_police:GetConfig("NeedRadioForDispatch")
          if not needRadio then
              local freq = exports.origen_police:GetMultiFrec()
              if freq then
                  response = freq
              else
                  response = false
              end
          end
      end

      -- Handle heat map alerts
      if data.page == "central" then
          if Config.HeatMapAlerts then
              if not heatAlerts then
                  isLoadingHeatAlerts = true
                  FW_TriggerCallback("origen_police:server:GetHeatAlerts", function(alerts)
                      heatAlerts = alerts or {}
                  end)
              end
          else
              heatAlerts = {}
          end

          -- Wait for heat alerts to load
          while not heatAlerts and data.page == "central" do
              Citizen.Wait(0)
          end

          response = heatAlerts
          isLoadingHeatAlerts = false
      end

      return cb(response)
  end

  -- Handle penal code page
  if data.page == "penalcode" then
      response.boss = playerData.job.isboss
      return cb(response)
  end

  -- Handle agents page
  if data.page == "agents" then
      if GetResourceState("origen_police") == "started" then
          local minGrade = GetMinimunGrade(playerData.job.name, "AgentManagement")
          response = (minGrade <= playerData.job.grade.level)
      else
          response = true
      end
      return cb(response)
  end

  -- Default response
  cb(response)
end)
L2_1(L3_1, L4_1)
L2_1 = RegisterNUICallback
L3_1 = "GetInventory"
function L4_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L2_2 = exports
  L2_2 = L2_2.origen_police
  L3_2 = L2_2
  L2_2 = L2_2.FW_GetPlayerData
  L4_2 = false
  L2_2 = L2_2(L3_2, L4_2)
  L3_2 = #A0_2
  if 0 == L3_2 then
    L3_2 = A1_2
    L4_2 = L2_2.items
    L3_2(L4_2)
  else
    L3_2 = {}
    L4_2 = pairs
    L5_2 = L2_2.items
    L4_2, L5_2, L6_2, L7_2 = L4_2(L5_2)
    for L8_2, L9_2 in L4_2, L5_2, L6_2, L7_2 do
      L10_2 = InSmallArray
      L11_2 = L9_2.name
      L12_2 = A0_2
      L10_2 = L10_2(L11_2, L12_2)
      if L10_2 then
        L10_2 = table
        L10_2 = L10_2.insert
        L11_2 = L3_2
        L12_2 = L9_2
        L10_2(L11_2, L12_2)
      end
    end
    L4_2 = A1_2
    L5_2 = L3_2
    L4_2(L5_2)
  end
end
L2_1(L3_1, L4_1)
L2_1 = RegisterNetEvent
L3_1 = "origen_police:client:CloseRadioTab"
function L4_1()
  local L0_2, L1_2
  L0_2 = SendNUIMessage
  L1_2 = {}
  L1_2.action = "CloseRadioTab"
  L0_2(L1_2)
end
L2_1(L3_1, L4_1)
L2_1 = RegisterNetEvent
L3_1 = "origen_police:toggleDuty"
function L4_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2
  if A0_2 then
    L1_2 = Wait
    L2_2 = 2000
    L1_2(L2_2)
  end
  L1_2 = TriggerServerEvent
  L2_2 = "QBCore:ToggleDuty"
  L1_2(L2_2)
  L1_2 = TriggerServerEvent
  L2_2 = "origen_police:server_esx:SetDuty"
  L3_2 = exports
  L3_2 = L3_2.origen_police
  L4_2 = L3_2
  L3_2 = L3_2.GetPoliceDuty
  L3_2 = L3_2(L4_2)
  L3_2 = not L3_2
  L1_2(L2_2, L3_2)
end
L2_1(L3_1, L4_1)
function L2_1(A0_2)
  local L1_2, L2_2, L3_2
  L1_2 = FW_TriggerCallback
  L2_2 = "origen_police:server:GetCameraUrl"
  function L3_2(A0_3)
    local L1_3, L2_3
    if A0_3 then
      L1_3 = A0_2
      L2_3 = A0_3
      L1_3(L2_3)
    else
      L1_3 = A0_2
      L2_3 = nil
      L1_3(L2_3)
    end
  end
  L1_2(L2_2, L3_2)
end
GetCameraWebhook = L2_1
L2_1 = exports
L3_1 = "GetWebhook"
L4_1 = GetCameraWebhook
L2_1(L3_1, L4_1)
L2_1 = false
L3_1 = RegisterNUICallback
L4_1 = "TakePicture"
function L5_1(A0_2, A1_2)
  local L2_2, L3_2
  L2_2 = L2_1
  if not L2_2 then
    L2_2 = GetCameraWebhook
    function L3_2(A0_3)
      local L1_3, L2_3
      webhookUrl = A0_3
      L1_3 = webhookUrl
      if not L1_3 then
        L1_3 = A1_2
        L2_3 = false
        L1_3(L2_3)
        L1_3 = false
        L2_1 = L1_3
        L1_3 = TogglePause
        L2_3 = false
        L1_3(L2_3)
        L1_3 = Wait
        L2_3 = 100
        L1_3(L2_3)
        L1_3 = TogglePause
        L2_3 = true
        L1_3(L2_3)
        L1_3 = ShowNotification
        L2_3 = Config
        L2_3 = L2_3.Translations
        L2_3 = L2_3.ErrorOccurred
        return L1_3(L2_3)
      end
      L1_3 = true
      L2_1 = L1_3
      L1_3 = Citizen
      L1_3 = L1_3.CreateThread
      function L2_3()
        local L0_4, L1_4, L2_4, L3_4, L4_4, L5_4, L6_4, L7_4, L8_4, L9_4, L10_4, L11_4, L12_4, L13_4, L14_4
        L0_4 = GetGameplayCamFov
        L0_4 = L0_4()
        L1_4 = PlayerPedId
        L1_4 = L1_4()
        L2_4 = GetEntityCoords
        L3_4 = L1_4
        L2_4 = L2_4(L3_4)
        L3_4 = vector3
        L4_4 = 0
        L5_4 = 0
        L6_4 = 0.6
        L3_4 = L3_4(L4_4, L5_4, L6_4)
        L2_4 = L2_4 + L3_4
        L3_4 = GetEntityForwardVector
        L4_4 = L1_4
        L3_4 = L3_4(L4_4)
        L3_4 = L3_4 * 0.7
        L2_4 = L2_4 + L3_4
        L3_4 = CreateCamWithParams
        L4_4 = "DEFAULT_SCRIPTED_CAMERA"
        L5_4 = L2_4
        L6_4 = GetEntityRotation
        L7_4 = L1_4
        L8_4 = 2
        L6_4 = L6_4(L7_4, L8_4)
        L7_4 = L0_4
        L3_4 = L3_4(L4_4, L5_4, L6_4, L7_4)
        L4_4 = L0_4
        L5_4 = IsMinimapRendering
        L5_4 = L5_4()
        L6_4 = SetCamActive
        L7_4 = L3_4
        L8_4 = true
        L6_4(L7_4, L8_4)
        L6_4 = RenderScriptCams
        L7_4 = true
        L8_4 = true
        L9_4 = 2000
        L10_4 = true
        L11_4 = false
        L6_4(L7_4, L8_4, L9_4, L10_4, L11_4)
        L6_4 = DisplayRadar
        L7_4 = false
        L6_4(L7_4)
        L6_4 = GetGameTimer
        L6_4 = L6_4()
        L7_4 = Citizen
        L7_4 = L7_4.Wait
        L8_4 = 2000
        L7_4(L8_4)
        L7_4 = false
        while true do
          L8_4 = L2_1
          if not L8_4 then
            break
          end
          L8_4 = Citizen
          L8_4 = L8_4.Wait
          L9_4 = 0
          L8_4(L9_4)
          L8_4 = HandleZoom
          L9_4 = L3_4
          L10_4 = L0_4
          L11_4 = L4_4
          L8_4 = L8_4(L9_4, L10_4, L11_4)
          L0_4 = L8_4
          L8_4 = CheckInputRotation
          L9_4 = L3_4
          L10_4 = L0_4
          L8_4(L9_4, L10_4)
          L8_4 = DisableAllControlActions
          L9_4 = 0
          L8_4(L9_4)
          L8_4 = IsDisabledControlJustPressed
          L9_4 = 0
          L10_4 = 176
          L8_4 = L8_4(L9_4, L10_4)
          if L8_4 and not L7_4 then
            L7_4 = true
            L8_4 = exports
            L8_4 = L8_4.origen_police
            L9_4 = L8_4
            L8_4 = L8_4.TakePhoto
            L10_4 = webhookUrl
            L8_4 = L8_4(L9_4, L10_4)
            L9_4 = exports
            L9_4 = L9_4.origen_police
            L10_4 = L9_4
            L9_4 = L9_4.FW_TriggerCallback
            L11_4 = "origen_police:police:UpdateCitizenStatus"
            function L12_4(A0_5)
              local L1_5, L2_5
              L1_5 = A1_2
              if A0_5 then
                L2_5 = L8_4
                if L2_5 then
                  goto lbl_8
                end
              end
              L2_5 = false
              ::lbl_8::
              L1_5(L2_5)
              L1_5 = false
              L2_1 = L1_5
              L1_5 = TogglePause
              L2_5 = true
              L1_5(L2_5)
            end
            L13_4 = {}
            L14_4 = A0_2.citizenid
            L13_4.citizenid = L14_4
            L13_4.column = "image"
            L13_4.value = L8_4
            L9_4(L10_4, L11_4, L12_4, L13_4)
          end
          L8_4 = IsDisabledControlJustPressed
          L9_4 = 0
          L10_4 = 177
          L8_4 = L8_4(L9_4, L10_4)
          if L8_4 and not L7_4 then
            L8_4 = A1_2
            L9_4 = false
            L8_4(L9_4)
            L8_4 = false
            L2_1 = L8_4
            L8_4 = TogglePause
            L9_4 = true
            L8_4(L9_4)
          end
        end
        L8_4 = DisplayRadar
        L9_4 = L5_4
        L8_4(L9_4)
        L8_4 = ClearPedTasks
        L9_4 = L1_4
        L8_4(L9_4)
        L8_4 = SetCamActive
        L9_4 = L3_4
        L10_4 = false
        L8_4(L9_4, L10_4)
        L8_4 = DestroyCam
        L9_4 = L3_4
        L10_4 = true
        L8_4(L9_4, L10_4)
        L8_4 = RenderScriptCams
        L9_4 = false
        L10_4 = true
        L11_4 = 2000
        L12_4 = true
        L13_4 = false
        L8_4(L9_4, L10_4, L11_4, L12_4, L13_4)
      end
      L1_3(L2_3)
    end
    L2_2(L3_2)
  end
end
L3_1(L4_1, L5_1)
function L3_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L2_2 = GetDisabledControlNormal
  L3_2 = 0
  L4_2 = 220
  L2_2 = L2_2(L3_2, L4_2)
  L3_2 = GetDisabledControlNormal
  L4_2 = 0
  L5_2 = 221
  L3_2 = L3_2(L4_2, L5_2)
  L4_2 = GetCamRot
  L5_2 = A0_2
  L6_2 = 2
  L4_2 = L4_2(L5_2, L6_2)
  if 0.0 ~= L2_2 or 0.0 ~= L3_2 then
    L5_2 = L4_2.z
    L6_2 = L2_2 * -1.0
    L6_2 = L6_2 * 0.1
    L7_2 = A1_2 + 0.1
    L6_2 = L6_2 * L7_2
    L5_2 = L5_2 + L6_2
    new_z = L5_2
    L5_2 = math
    L5_2 = L5_2.max
    L6_2 = math
    L6_2 = L6_2.min
    L7_2 = 65.0
    L8_2 = L4_2.x
    L9_2 = L3_2 * -1.0
    L9_2 = L9_2 * 0.1
    L10_2 = A1_2 + 0.1
    L9_2 = L9_2 * L10_2
    L8_2 = L8_2 + L9_2
    L6_2 = L6_2(L7_2, L8_2)
    L7_2 = -65.0
    L5_2 = L5_2(L6_2, L7_2)
    new_x = L5_2
    L5_2 = SetCamRot
    L6_2 = A0_2
    L7_2 = new_x
    L8_2 = 0.0
    L9_2 = new_z
    L10_2 = 2
    L5_2(L6_2, L7_2, L8_2, L9_2, L10_2)
  end
end
CheckInputRotation = L3_1
function L3_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2
  L3_2 = A1_2
  L4_2 = IsDisabledControlJustPressed
  L5_2 = 0
  L6_2 = 241
  L4_2 = L4_2(L5_2, L6_2)
  if L4_2 then
    L4_2 = math
    L4_2 = L4_2.max
    L5_2 = L3_2 - 3.0
    L6_2 = 5.0
    L4_2 = L4_2(L5_2, L6_2)
    L3_2 = L4_2
  end
  L4_2 = IsDisabledControlJustPressed
  L5_2 = 0
  L6_2 = 242
  L4_2 = L4_2(L5_2, L6_2)
  if L4_2 then
    L4_2 = math
    L4_2 = L4_2.min
    L5_2 = L3_2 + 3.0
    L6_2 = A2_2
    L4_2 = L4_2(L5_2, L6_2)
    L3_2 = L4_2
  end
  L4_2 = GetCamFov
  L5_2 = A0_2
  L4_2 = L4_2(L5_2)
  L5_2 = math
  L5_2 = L5_2.abs
  L6_2 = L3_2 - L4_2
  L5_2 = L5_2(L6_2)
  L6_2 = 0.1
  if L5_2 < L6_2 then
    L3_2 = L4_2
  end
  L5_2 = SetCamFov
  L6_2 = A0_2
  L7_2 = L3_2 - L4_2
  L7_2 = L7_2 * 0.05
  L7_2 = L4_2 + L7_2
  L5_2(L6_2, L7_2)
  return L3_2
end
HandleZoom = L3_1
function L3_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2
  L2_2 = 0
  L3_2 = #A1_2
  L4_2 = 1
  for L5_2 = L2_2, L3_2, L4_2 do
    L6_2 = A1_2[L5_2]
    if L6_2 == A0_2 then
      L6_2 = true
      return L6_2
    end
  end
  L2_2 = false
  return L2_2
end
InSmallArray = L3_1
L3_1 = RegisterNUICallback
L4_1 = "ShowBodycam"
function L5_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2
  L2_2 = TriggerServerEvent
  L3_2 = "origen_police:server:ShowBodycam"
  L4_2 = tonumber
  L5_2 = A0_2.id
  L4_2, L5_2 = L4_2(L5_2)
  L2_2(L3_2, L4_2, L5_2)
  L2_2 = A1_2
  L3_2 = true
  L2_2(L3_2)
end
L3_1(L4_1, L5_1)
L3_1 = RegisterNUICallback
L4_1 = "ShowCarcam"
function L5_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2
  L2_2 = TriggerServerEvent
  L3_2 = "origen_police:server:ShowCarcam"
  L4_2 = tonumber
  L5_2 = A0_2.netid
  L4_2, L5_2 = L4_2(L5_2)
  L2_2(L3_2, L4_2, L5_2)
  L2_2 = A1_2
  L3_2 = true
  L2_2(L3_2)
end
L3_1(L4_1, L5_1)
L3_1 = RegisterNUICallback
L4_1 = "GetPoliceGrades"
function L5_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2
  L2_2 = A1_2
  L3_2 = Framework
  L3_2 = L3_2.Shared
  L3_2 = L3_2.Jobs
  L4_2 = A0_2.job
  L3_2 = L3_2[L4_2]
  L3_2 = L3_2.grades
  L2_2(L3_2)
end
L3_1(L4_1, L5_1)
L3_1 = RegisterNetEvent
L4_1 = "origen_police:client:HideCamHud"
function L5_1()
  local L0_2, L1_2
  L0_2 = SendNUIMessage
  L1_2 = {}
  L1_2.action = "HideCamHud"
  L0_2(L1_2)
  L0_2 = TogglePause
  L1_2 = true
  L0_2(L1_2)
end
L3_1(L4_1, L5_1)
L3_1 = RegisterNetEvent
L4_1 = "origen_police:client:rpol"
function L5_1(A0_2)
  local L1_2, L2_2
  L1_2 = SendNUIMessage
  L2_2 = {}
  L2_2.action = "RpolMessage"
  L2_2.message = A0_2
  L1_2(L2_2)
end
L3_1(L4_1, L5_1)
L3_1 = RegisterNUICallback
L4_1 = "SendRpolMessage"
function L5_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2
  L2_2 = UseCommand
  L3_2 = "rpol"
  L4_2 = A0_2.message
  L2_2(L3_2, L4_2)
  L2_2 = A1_2
  L3_2 = true
  L2_2(L3_2)
end
L3_1(L4_1, L5_1)
L3_1 = RegisterNetEvent
L4_1 = "origen_police_menu:client:ReceiveAlert"
function L5_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2
  L1_2 = GetEntityCoords
  L2_2 = PlayerPedId
  L2_2, L3_2, L4_2, L5_2 = L2_2()
  L1_2 = L1_2(L2_2, L3_2, L4_2, L5_2)
  L2_2 = A0_2.coords
  L1_2 = L1_2 - L2_2
  L1_2 = #L1_2
  A0_2.distance = L1_2
  L1_2 = A0_2.distance
  L2_2 = 1000
  if L1_2 < L2_2 then
    L1_2 = FW_Round
    L2_2 = A0_2.distance
    L3_2 = 2
    L1_2 = L1_2(L2_2, L3_2)
    L2_2 = " m"
    L1_2 = L1_2 .. L2_2
    A0_2.distance = L1_2
  else
    L1_2 = FW_Round
    L2_2 = A0_2.distance
    L2_2 = L2_2 / 1000
    L3_2 = 2
    L1_2 = L1_2(L2_2, L3_2)
    L2_2 = " Km"
    L1_2 = L1_2 .. L2_2
    A0_2.distance = L1_2
  end
  L1_2 = GetStreetNameFromHashKey
  L2_2 = GetStreetNameAtCoord
  L3_2 = A0_2.coords
  L3_2 = L3_2.x
  L4_2 = A0_2.coords
  L4_2 = L4_2.y
  L5_2 = A0_2.coords
  L5_2 = L5_2.z
  L2_2, L3_2, L4_2, L5_2 = L2_2(L3_2, L4_2, L5_2)
  L1_2 = L1_2(L2_2, L3_2, L4_2, L5_2)
  A0_2.street = L1_2
  A0_2.time = 0
  L1_2 = PlaySoundFrontend
  L2_2 = -1
  L3_2 = "Event_Message_Purple"
  L4_2 = "GTAO_FM_Events_Soundset"
  L5_2 = false
  L1_2(L2_2, L3_2, L4_2, L5_2)
  L1_2 = SendNUIMessage
  L2_2 = {}
  L2_2.action = "ReceiveAlert"
  L2_2.alert = A0_2
  L1_2(L2_2)
end
L3_1(L4_1, L5_1)
L3_1 = RegisterNetEvent
L4_1 = "origen_police_menu:client:EditAlert"
function L5_1(A0_2)
  local L1_2, L2_2
  L1_2 = SendNUIMessage
  L2_2 = {}
  L2_2.action = "EditAlert"
  L2_2.data = A0_2
  L1_2(L2_2)
end
L3_1(L4_1, L5_1)
L3_1 = RegisterNetEvent
L4_1 = "origen_police:client:AddCentralMark"
function L5_1(A0_2, A1_2)
  local L2_2, L3_2
  L2_2 = SendNUIMessage
  L3_2 = {}
  L3_2.action = "AddCentralMark"
  L3_2.id = A0_2
  L3_2.data = A1_2
  L2_2(L3_2)
end
L3_1(L4_1, L5_1)
L3_1 = RegisterNetEvent
L4_1 = "origen_police:client:UpdateCentralMark"
function L5_1(A0_2, A1_2)
  local L2_2, L3_2
  L2_2 = SendNUIMessage
  L3_2 = {}
  L3_2.action = "UpdateCentralMark"
  L3_2.id = A0_2
  L3_2.data = A1_2
  L2_2(L3_2)
end
L3_1(L4_1, L5_1)
L3_1 = RegisterNetEvent
L4_1 = "origen_police:client:RemoveCentralMark"
function L5_1(A0_2)
  local L1_2, L2_2
  L1_2 = SendNUIMessage
  L2_2 = {}
  L2_2.action = "RemoveCentralMark"
  L2_2.id = A0_2
  L1_2(L2_2)
end
L3_1(L4_1, L5_1)
L3_1 = RegisterNetEvent
L4_1 = "origen_police:client:UpdateCentralPositions"
function L5_1(A0_2, A1_2)
  local L2_2, L3_2
  L2_2 = SendNUIMessage
  L3_2 = {}
  L3_2.action = "UpdateCentralPositions"
  L3_2.VehicleTrackeds = A0_2
  L3_2.Cops = A1_2
  L2_2(L3_2)
end
L3_1(L4_1, L5_1)
