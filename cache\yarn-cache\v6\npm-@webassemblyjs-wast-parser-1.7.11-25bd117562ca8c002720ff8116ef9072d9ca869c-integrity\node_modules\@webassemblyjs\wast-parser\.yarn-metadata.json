{"manifest": {"name": "@webassemblyjs/wast-parser", "version": "1.7.11", "description": "WebAssembly text format parser", "keywords": ["webassembly", "javascript", "ast", "parser", "wat", "wast"], "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "mocha"}, "author": {"name": "<PERSON>"}, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/floating-point-hex-parser": "1.7.11", "@webassemblyjs/helper-api-error": "1.7.11", "@webassemblyjs/helper-code-frame": "1.7.11", "@webassemblyjs/helper-fsm": "1.7.11", "@xtuc/long": "4.2.1"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.11", "mamacro": "^0.0.3"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@webassemblyjs-wast-parser-1.7.11-25bd117562ca8c002720ff8116ef9072d9ca869c-integrity\\node_modules\\@webassemblyjs\\wast-parser\\package.json", "readmeFilename": "README.md", "readme": "# @webassemblyjs/wast-parser\n\n> WebAssembly text format parser\n\n## Installation\n\n```sh\nyarn add @webassemblyjs/wast-parser\n```\n\n## Usage\n\n```js\nimport { parse } from \"@webassemblyjs/wast-parser\";\n\nconst ast = parse(source);\n```\n", "licenseText": "MIT License\n\nCopyright (c) 2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@webassemblyjs/wast-parser/-/wast-parser-1.7.11.tgz#25bd117562ca8c002720ff8116ef9072d9ca869c", "type": "tarball", "reference": "https://registry.yarnpkg.com/@webassemblyjs/wast-parser/-/wast-parser-1.7.11.tgz", "hash": "25bd117562ca8c002720ff8116ef9072d9ca869c", "integrity": "sha512-lEyVCg2np15tS+dm7+JJTNhNWq9yTZvi3qEhAIIOaofcYlUp0UR5/tVqOwa/gXYr3gjwSZqw+/lS9dscyLelbQ==", "registry": "npm", "packageName": "@webassemblyjs/wast-parser", "cacheIntegrity": "sha512-lEyVCg2np15tS+dm7+JJTNhNWq9yTZvi3qEhAIIOaofcYlUp0UR5/tVqOwa/gXYr3gjwSZqw+/lS9dscyLelbQ== sha1-Jb0RdWLKjAAnIP+BFu+QctnKhpw="}, "registry": "npm", "hash": "25bd117562ca8c002720ff8116ef9072d9ca869c"}