{"name": "cookies", "description": "Cookies, optionally signed using Keygrip.", "version": "0.8.0", "author": "<PERSON> <<EMAIL>> (http://jed.is)", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "repository": "pillarjs/cookies", "dependencies": {"depd": "~2.0.0", "keygrip": "~1.1.0"}, "devDependencies": {"eslint": "4.19.1", "express": "4.17.1", "mocha": "6.2.1", "nyc": "14.1.1", "restify": "8.4.0", "supertest": "4.0.2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}}