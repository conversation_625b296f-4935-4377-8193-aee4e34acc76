local L0_1, L1_1, L2_1, L3_1, L4_1, L5_1, L6_1, L7_1, L8_1, L9_1, L10_1, L11_1, L12_1, L13_1, L14_1, L15_1, L16_1
function L0_1()
  local L0_2, L1_2, L2_2, L3_2
  L0_2 = promise
  L0_2 = L0_2.new
  L0_2 = L0_2()
  L1_2 = FW_TriggerCallback
  L2_2 = "origen_police:server:GetPlayerMetadata"
  function L3_2(A0_3)
    local L1_3, L2_3, L3_3
    L1_3 = L0_2
    L2_3 = L1_3
    L1_3 = L1_3.resolve
    L3_3 = A0_3
    L1_3(L2_3, L3_3)
  end
  L1_2(L2_2, L3_2)
  L1_2 = Citizen
  L1_2 = L1_2.Await
  L2_2 = L0_2
  return L1_2(L2_2)
end
FW_GetPlayerMetadata = L0_1
function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L1_2 = tonumber
  L2_2 = A0_2
  L1_2 = L1_2(L2_2)
  A0_2 = L1_2
  L1_2 = pairs
  L2_2 = Config
  L2_2 = L2_2.BossGrade
  L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2)
  for L5_2, L6_2 in L1_2, L2_2, L3_2, L4_2 do
    if L6_2 <= A0_2 then
      L7_2 = true
      return L7_2
    end
  end
  L1_2 = false
  return L1_2
end
IsBoss = L0_1
function L0_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2
  if A1_2 then
    L2_2 = 10
    L2_2 = L2_2 ^ A1_2
    L3_2 = math
    L3_2 = L3_2.floor
    L4_2 = A0_2 * L2_2
    L4_2 = L4_2 + 0.5
    L3_2 = L3_2(L4_2)
    L3_2 = L3_2 / L2_2
    return L3_2
  else
    L2_2 = math
    L2_2 = L2_2.floor
    L3_2 = A0_2 + 0.5
    return L2_2(L3_2)
  end
end
FW_Round = L0_1
L0_1 = Citizen
L0_1 = L0_1.CreateThread
function L1_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  function L0_2(A0_3, A1_3)
    local L2_3, L3_3, L4_3, L5_3, L6_3, L7_3, L8_3, L9_3, L10_3, L11_3
    if not A1_3 then
      A1_3 = ""
    end
    L2_3 = pairs
    L3_3 = A0_3
    L2_3, L3_3, L4_3, L5_3 = L2_3(L3_3)
    for L6_3, L7_3 in L2_3, L3_3, L4_3, L5_3 do
      L8_3 = type
      L9_3 = L7_3
      L8_3 = L8_3(L9_3)
      if "table" == L8_3 then
        L8_3 = L0_2
        L9_3 = L7_3
        L10_3 = A1_3
        L11_3 = "  "
        L10_3 = L10_3 .. L11_3
        L8_3(L9_3, L10_3)
      elseif "__index" == L6_3 then
        L8_3 = type
        L9_3 = L7_3
        L8_3 = L8_3(L9_3)
        if "function" == L8_3 then
          return L7_3
        end
      end
    end
  end
  L1_2 = getmetatable
  L2_2 = exports
  L2_2 = L2_2["pma-voice"]
  L1_2 = L1_2(L2_2)
  L2_2 = type
  L3_2 = L1_2
  L2_2 = L2_2(L3_2)
  if "table" == L2_2 then
    L2_2 = L0_2
    L3_2 = L1_2
    L2_2 = L2_2(L3_2)
    L3_2 = pcall
    function L4_2()
      local L0_3, L1_3, L2_3
      L0_3 = L2_2
      L1_3 = "pma-voice"
      L2_3 = "playerTargets"
      L0_3(L1_3, L2_3)
    end
    L3_2, L4_2 = L3_2(L4_2)
    L5_2 = pcall
    function L6_2()
      local L0_3, L1_3, L2_3
      L0_3 = L2_2
      L1_3 = "pma-voice"
      L2_3 = "toggleVoice"
      L0_3(L1_3, L2_3)
    end
    L5_2, L6_2 = L5_2(L6_2)
    if not L3_2 and not L5_2 then
      L7_2 = print
      L8_2 = "^4origen_police: Can't find pma-voice playerTargets and toggleVoice exports, please make sure to read the installation.txt file"
      L7_2(L8_2)
      return
    end
    if not L3_2 then
      L7_2 = print
      L8_2 = "^4origen_police: Can't find pma-voice playerTargets export, please make sure to have the latest version of pma-voice or ask for support in the discord server"
      L7_2(L8_2)
      return
    end
  end
end
L0_1(L1_1)
L0_1 = "ox_lib"
L1_1 = GetResourceState
L2_1 = L0_1
L1_1 = L1_1(L2_1)
if "started" == L1_1 then
  L1_1 = exports
  L1_1 = L1_1[L0_1]
  L2_1 = L1_1.hasLoaded
  L2_1 = L2_1()
  if true ~= L2_1 then
    L3_1 = Debuger
    L4_1 = "OX LIB LOAD:"
    L5_1 = L2_1
    L6_1 = 2
    L3_1(L4_1, L5_1, L6_1)
  end
  L3_1 = msgpack
  L3_1 = L3_1.setoption
  L4_1 = "ignore_invalid"
  L5_1 = true
  L3_1(L4_1, L5_1)
  L3_1 = LoadResourceFile
  L4_1 = IsDuplicityVersion
  L4_1 = L4_1()
  if L4_1 then
    L4_1 = "server"
    if L4_1 then
      goto lbl_43
    end
  end
  L4_1 = "client"
  ::lbl_43::
  function L5_1()
    local L0_2, L1_2
  end
  noop = L5_1
  function L5_1(A0_2, A1_2)
    local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
    L2_2 = "imports/%s"
    L3_2 = L2_2
    L2_2 = L2_2.format
    L4_2 = A1_2
    L2_2 = L2_2(L3_2, L4_2)
    L3_2 = L3_1
    L4_2 = L0_1
    L5_2 = "%s/%s.lua"
    L6_2 = L5_2
    L5_2 = L5_2.format
    L7_2 = L2_2
    L8_2 = L4_1
    L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2 = L5_2(L6_2, L7_2, L8_2)
    L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
    L4_2 = L3_1
    L5_2 = L0_1
    L6_2 = "%s/shared.lua"
    L7_2 = L6_2
    L6_2 = L6_2.format
    L8_2 = L2_2
    L6_2, L7_2, L8_2, L9_2, L10_2, L11_2 = L6_2(L7_2, L8_2)
    L4_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
    if L4_2 then
      if L3_2 then
        L5_2 = [[
%s
%s]]
        L6_2 = L5_2
        L5_2 = L5_2.format
        L7_2 = L4_2
        L8_2 = L3_2
        L5_2 = L5_2(L6_2, L7_2, L8_2)
        if L5_2 then
          goto lbl_32
          L3_2 = L5_2 or L3_2
        end
      end
      L3_2 = L4_2
    end
    ::lbl_32::
    if L3_2 then
      L5_2 = load
      L6_2 = L3_2
      L7_2 = "@@ox_lib/imports/%s/%s.lua"
      L8_2 = L7_2
      L7_2 = L7_2.format
      L9_2 = A1_2
      L10_2 = L4_1
      L7_2, L8_2, L9_2, L10_2, L11_2 = L7_2(L8_2, L9_2, L10_2)
      L5_2, L6_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
      if not L5_2 or L6_2 then
        L7_2 = Debuger
        L8_2 = [[

^1Error importing module (%s): %s^0]]
        L9_2 = L8_2
        L8_2 = L8_2.format
        L10_2 = L2_2
        L11_2 = L6_2
        L8_2 = L8_2(L9_2, L10_2, L11_2)
        L9_2 = 3
        return L7_2(L8_2, L9_2)
      end
      L7_2 = L5_2
      L7_2 = L7_2()
      L8_2 = L7_2 or L8_2
      if not L7_2 then
        L8_2 = noop
      end
      A0_2[A1_2] = L8_2
      L8_2 = A0_2[A1_2]
      return L8_2
    end
  end
  function L6_1(A0_2, A1_2, ...)
    local L2_2, L3_2, L4_2, L5_2
    L2_2 = rawget
    L3_2 = A0_2
    L4_2 = A1_2
    L2_2 = L2_2(L3_2, L4_2)
    if not L2_2 then
      L3_2 = noop
      A0_2[A1_2] = L3_2
      L3_2 = L5_1
      L4_2 = A0_2
      L5_2 = A1_2
      L3_2 = L3_2(L4_2, L5_2)
      L2_2 = L3_2
      if not L2_2 then
        function L3_2(...)
          local L0_3, L1_3, L2_3
          L1_3 = A1_2
          L0_3 = L1_1
          L0_3 = L0_3[L1_3]
          L1_3 = nil
          L2_3 = ...
          return L0_3(L1_3, L2_3)
        end
        L4_2 = (...)
        if not L4_2 then
          A0_2[A1_2] = L3_2
        end
        return L3_2
      end
    end
    return L2_2
  end
  L7_1 = setmetatable
  L8_1 = {}
  L8_1.name = L0_1
  L8_1.context = L4_1
  function L9_1(A0_2, A1_2)
    local L2_2, L3_2, L4_2, L5_2
    L2_2 = AddEventHandler
    L3_2 = "ox_lib:cache:%s"
    L4_2 = L3_2
    L3_2 = L3_2.format
    L5_2 = A0_2
    L3_2 = L3_2(L4_2, L5_2)
    L4_2 = A1_2
    L2_2(L3_2, L4_2)
  end
  L8_1.onCache = L9_1
  L9_1 = {}
  L9_1.__index = L6_1
  L9_1.__call = L6_1
  L7_1 = L7_1(L8_1, L9_1)
  lib = L7_1
  L7_1 = lib
  L7_1 = L7_1.require
  require = L7_1
  L7_1 = {}
  function L8_1(A0_2, A1_2, ...)
    local L2_2, L3_2, L4_2, L5_2, L6_2
    if not A1_2 then
      A1_2 = 0
    end
    L2_2 = type
    L3_2 = A1_2
    L2_2 = L2_2(L3_2)
    if "number" ~= L2_2 then
      L2_2 = Debuger
      L3_2 = "Interval must be a number. Received %s"
      L4_2 = L3_2
      L3_2 = L3_2.format
      L5_2 = json
      L5_2 = L5_2.encode
      L6_2 = A1_2
      L5_2, L6_2 = L5_2(L6_2)
      L3_2, L4_2, L5_2, L6_2 = L3_2(L4_2, L5_2, L6_2)
      return L2_2(L3_2, L4_2, L5_2, L6_2)
    end
    L2_2 = type
    L3_2 = A0_2
    L2_2 = L2_2(L3_2)
    if "number" == L2_2 then
      L3_2 = L7_1
      L3_2 = L3_2[A0_2]
      if L3_2 then
        L3_2 = L7_1
        L4_2 = A1_2 or L4_2
        if not A1_2 then
          L4_2 = 0
        end
        L3_2[A0_2] = L4_2
        return
      end
    end
    if "function" ~= L2_2 then
      L3_2 = Debuger
      L4_2 = "Callback must be a function. Received %s"
      L5_2 = L4_2
      L4_2 = L4_2.format
      L6_2 = L2_2
      L4_2, L5_2, L6_2 = L4_2(L5_2, L6_2)
      return L3_2(L4_2, L5_2, L6_2)
    end
    L3_2 = {}
    L4_2, L5_2, L6_2 = ...
    L3_2[1] = L4_2
    L3_2[2] = L5_2
    L3_2[3] = L6_2
    L4_2 = nil
    L5_2 = Citizen
    L5_2 = L5_2.CreateThreadNow
    function L6_2(A0_3)
      local L1_3, L2_3, L3_3
      L4_2 = A0_3
      L2_3 = L4_2
      L1_3 = L7_1
      L3_3 = A1_2
      if not L3_3 then
        L3_3 = 0
      end
      L1_3[L2_3] = L3_3
      repeat
        L2_3 = L4_2
        L1_3 = L7_1
        L1_3 = L1_3[L2_3]
        A1_2 = L1_3
        L1_3 = Wait
        L2_3 = A1_2
        L1_3(L2_3)
        L1_3 = A0_2
        L2_3 = table
        L2_3 = L2_3.unpack
        L3_3 = L3_2
        L2_3, L3_3 = L2_3(L3_3)
        L1_3(L2_3, L3_3)
        L1_3 = A1_2
      until L1_3 < 0
      L2_3 = L4_2
      L1_3 = L7_1
      L1_3[L2_3] = nil
    end
    L5_2(L6_2)
    return L4_2
  end
  SetInterval = L8_1
  function L8_1(A0_2)
    local L1_2, L2_2, L3_2, L4_2, L5_2
    L1_2 = type
    L2_2 = A0_2
    L1_2 = L1_2(L2_2)
    if "number" ~= L1_2 then
      L1_2 = Debuger
      L2_2 = "Interval id must be a number. Received %s"
      L3_2 = L2_2
      L2_2 = L2_2.format
      L4_2 = json
      L4_2 = L4_2.encode
      L5_2 = A0_2
      L4_2, L5_2 = L4_2(L5_2)
      L2_2, L3_2, L4_2, L5_2 = L2_2(L3_2, L4_2, L5_2)
      return L1_2(L2_2, L3_2, L4_2, L5_2)
    end
    L1_2 = L7_1
    L1_2 = L1_2[A0_2]
    if not L1_2 then
      L1_2 = Debuger
      L2_2 = "No interval exists with id %s"
      L3_2 = L2_2
      L2_2 = L2_2.format
      L4_2 = A0_2
      L2_2, L3_2, L4_2, L5_2 = L2_2(L3_2, L4_2)
      return L1_2(L2_2, L3_2, L4_2, L5_2)
    end
    L1_2 = L7_1
    L1_2[A0_2] = -1
  end
  ClearInterval = L8_1
  function L8_1(A0_2, A1_2, A2_2)
  end
  cache = L8_1
  L8_1 = setmetatable
  L9_1 = {}
  L10_1 = GetGameName
  L10_1 = L10_1()
  L9_1.game = L10_1
  L10_1 = resourceName
  L9_1.resource = L10_1
  L10_1 = {}
  if "client" == L4_1 then
    function L11_1(A0_2, A1_2)
      local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
      L2_2 = AddEventHandler
      L3_2 = "ox_lib:cache:%s"
      L4_2 = L3_2
      L3_2 = L3_2.format
      L5_2 = A1_2
      L3_2 = L3_2(L4_2, L5_2)
      function L4_2(A0_3)
        local L1_3, L2_3
        L2_3 = A1_2
        L1_3 = A0_2
        L1_3[L2_3] = A0_3
      end
      L2_2(L3_2, L4_2)
      L2_2 = rawset
      L3_2 = A0_2
      L4_2 = A1_2
      L5_2 = L1_1.cache
      L6_2 = nil
      L7_2 = A1_2
      L5_2 = L5_2(L6_2, L7_2)
      if not L5_2 then
        L5_2 = false
      end
      L2_2 = L2_2(L3_2, L4_2, L5_2)
      L2_2 = L2_2[A1_2]
      return L2_2
    end
    if L11_1 then
      goto lbl_87
    end
  end
  L11_1 = nil
  ::lbl_87::
  L10_1.__index = L11_1
  function L11_1(A0_2, A1_2, A2_2, A3_2)
    local L4_2, L5_2, L6_2, L7_2, L8_2
    L4_2 = rawget
    L5_2 = A0_2
    L6_2 = A1_2
    L4_2 = L4_2(L5_2, L6_2)
    if not L4_2 then
      L5_2 = A2_2
      L5_2 = L5_2()
      L4_2 = L5_2
      L5_2 = rawset
      L6_2 = A0_2
      L7_2 = A1_2
      L8_2 = L4_2
      L5_2(L6_2, L7_2, L8_2)
      if A3_2 then
        L5_2 = SetTimeout
        L6_2 = A3_2
        function L7_2()
          local L0_3, L1_3
          L1_3 = A1_2
          L0_3 = A0_2
          L0_3[L1_3] = nil
        end
        L5_2(L6_2, L7_2)
      end
    end
    return L4_2
  end
  L10_1.__call = L11_1
  L8_1 = L8_1(L9_1, L10_1)
  cache = L8_1
  L8_1 = "__ox_notify_%s"
  L9_1 = L8_1
  L8_1 = L8_1.format
  L10_1 = cache
  L10_1 = L10_1.resource
  L8_1 = L8_1(L9_1, L10_1)
  if "client" == L4_1 then
    L9_1 = RegisterNetEvent
    L10_1 = L8_1
    function L11_1(A0_2)
      local L1_2, L2_2, L3_2
      L1_2 = locale
      if L1_2 then
        L1_2 = A0_2.title
        if L1_2 then
          L1_2 = locale
          L2_2 = A0_2.title
          L1_2 = L1_2(L2_2)
          if not L1_2 then
            L1_2 = A0_2.title
          end
          A0_2.title = L1_2
        end
        L1_2 = A0_2.description
        if L1_2 then
          L1_2 = locale
          L2_2 = A0_2.description
          L1_2 = L1_2(L2_2)
          if not L1_2 then
            L1_2 = A0_2.description
          end
          A0_2.description = L1_2
        end
      end
      L1_2 = L1_1
      L2_2 = L1_2
      L1_2 = L1_2.notify
      L3_2 = A0_2
      return L1_2(L2_2, L3_2)
    end
    L9_1(L10_1, L11_1)
    L9_1 = cache
    L10_1 = PlayerId
    L10_1 = L10_1()
    L9_1.playerId = L10_1
    L9_1 = cache
    L10_1 = GetPlayerServerId
    L11_1 = cache
    L11_1 = L11_1.playerId
    L10_1 = L10_1(L11_1)
    L9_1.serverId = L10_1
  else
    L9_1 = lib
    function L10_1(A0_2, A1_2)
      local L2_2, L3_2, L4_2, L5_2
      L2_2 = TriggerClientEvent
      L3_2 = L8_1
      L4_2 = A0_2
      L5_2 = A1_2
      L2_2(L3_2, L4_2, L5_2)
    end
    L9_1.notify = L10_1
  end
  L9_1 = 1
  L10_1 = GetNumResourceMetadata
  L11_1 = cache
  L11_1 = L11_1.resource
  L12_1 = "ox_lib"
  L10_1 = L10_1(L11_1, L12_1)
  L11_1 = 1
  for L12_1 = L9_1, L10_1, L11_1 do
    L13_1 = GetResourceMetadata
    L14_1 = cache
    L14_1 = L14_1.resource
    L15_1 = "ox_lib"
    L16_1 = L12_1 - 1
    L13_1 = L13_1(L14_1, L15_1, L16_1)
    L14_1 = rawget
    L15_1 = lib
    L16_1 = L13_1
    L14_1 = L14_1(L15_1, L16_1)
    if not L14_1 then
      L14_1 = L5_1
      L15_1 = lib
      L16_1 = L13_1
      L14_1 = L14_1(L15_1, L16_1)
      L15_1 = type
      L16_1 = L14_1
      L15_1 = L15_1(L16_1)
      if "function" == L15_1 then
        L15_1 = pcall
        L16_1 = L14_1
        L15_1(L16_1)
      end
    end
  end
end
L1_1 = Config
L1_1 = L1_1.OxLibMenu
if L1_1 then
  L1_1 = lib
  if not L1_1 then
    L1_1 = Debuger
    L2_1 = "You have set the menu to ox_lib but you don't have uncommented the ox_lib line in the fxmanifest of origen_police"
    L1_1(L2_1)
  end
end
function L1_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2
  L3_2 = AddEventHandler
  L4_2 = "__cfx_export_"
  L5_2 = A0_2
  L6_2 = "_"
  L7_2 = A1_2
  L4_2 = L4_2 .. L5_2 .. L6_2 .. L7_2
  function L5_2(A0_3)
    local L1_3, L2_3
    L1_3 = A0_3
    L2_3 = A2_2
    return L1_3(L2_3)
  end
  L3_2(L4_2, L5_2)
end
AddReplaceExport = L1_1
