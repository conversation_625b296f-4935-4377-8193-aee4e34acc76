{"manifest": {"name": "node-libs-browser", "version": "2.2.1", "author": {"name": "<PERSON> @sokra"}, "description": "The node core libs for in browser usage.", "repository": {"type": "git", "url": "git+https://github.com/webpack/node-libs-browser.git"}, "dependencies": {"assert": "^1.1.1", "browserify-zlib": "^0.2.0", "buffer": "^4.3.0", "console-browserify": "^1.1.0", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.11.0", "domain-browser": "^1.1.1", "events": "^3.0.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "0.0.1", "process": "^0.11.10", "punycode": "^1.2.4", "querystring-es3": "^0.2.0", "readable-stream": "^2.3.3", "stream-browserify": "^2.0.1", "stream-http": "^2.7.2", "string_decoder": "^1.0.0", "timers-browserify": "^2.0.4", "tty-browserify": "0.0.0", "url": "^0.11.0", "util": "^0.11.0", "vm-browserify": "^1.0.1"}, "homepage": "http://github.com/webpack/node-libs-browser", "main": "index.js", "files": ["index.js", "mock/"], "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "license": "MIT", "bugs": {"url": "https://github.com/webpack/node-libs-browser/issues"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-node-libs-browser-2.2.1-b64f513d18338625f90346d27b0d235e631f6425-integrity\\node_modules\\node-libs-browser\\package.json", "readmeFilename": "README.md", "readme": "# node-libs-browser\n\nThe node core libs for in-browser usage.\n\nNOTE: This library is deprecated and won't accept Pull Requests that include Breaking Changes or new Features. Only bugfixes are accepted.\n\n[![dependencies status](http://david-dm.org/webpack/node-libs-browser.png)](http://david-dm.org/webpack/node-libs-browser)\n\nExports a hash [object] of absolute paths to each lib, keyed by lib names. Modules without browser replacements are `null`.\n\nSome modules have mocks in the `mock` directory. These are replacements with minimal functionality.\n\n| lib name | browser implementation | mock implementation |\n|:--------:|:----------------------:|:-------------------:|\n| assert | [defunctzombie/commonjs-assert](https://github.com/defunctzombie/commonjs-assert) | --- |\n| buffer | [feross/buffer](https://github.com/feross/buffer) | [buffer.js](https://github.com/webpack/node-libs-browser/blob/master/mock/buffer.js) |\n| child_process | --- | --- |\n| cluster | --- | --- |\n| console | [Raynos/console-browserify](https://github.com/Raynos/console-browserify) | [console.js](https://github.com/webpack/node-libs-browser/blob/master/mock/console.js) |\n| constants | [juliangruber/constants-browserify](https://github.com/juliangruber/constants-browserify) | --- |\n| crypto | [crypto-browserify/crypto-browserify](https://github.com/crypto-browserify/crypto-browserify) | --- |\n| dgram | --- | --- |\n| dns | --- | [dns.js](https://github.com/webpack/node-libs-browser/blob/master/mock/dns.js) |\n| domain | [bevry/domain-browser](https://github.com/bevry/domain-browser) | --- |\n| events | [Gozala/events](https://github.com/Gozala/events) | --- |\n| fs | --- | --- |\n| http | [jhiesey/stream-http](https://github.com/jhiesey/stream-http) | --- |\n| https | [substack/https-browserify](https://github.com/substack/https-browserify) | --- |\n| module | --- | --- |\n| net | --- | [net.js](https://github.com/webpack/node-libs-browser/blob/master/mock/net.js) |\n| os | [CoderPuppy/os-browserify](https://github.com/CoderPuppy/os-browserify) | --- |\n| path | [substack/path-browserify](https://github.com/substack/path-browserify) | --- |\n| process | [shtylman/node-process](https://github.com/shtylman/node-process) | [process.js](https://github.com/webpack/node-libs-browser/blob/master/mock/process.js) |\n| punycode | [bestiejs/punycode.js](https://github.com/bestiejs/punycode.js) | --- |\n| querystring | [mike-spainhower/querystring](https://github.com/mike-spainhower/querystring) | --- |\n| readline | --- | --- |\n| repl | --- | --- |\n| stream | [substack/stream-browserify](https://github.com/substack/stream-browserify) | --- |\n| string_decoder | [rvagg/string_decoder](https://github.com/rvagg/string_decoder) | --- |\n| sys | [defunctzombie/node-util](https://github.com/defunctzombie/node-util) | --- |\n| timers | [jryans/timers-browserify](https://github.com/jryans/timers-browserify) | --- | \n| tls | --- | [tls.js](https://github.com/webpack/node-libs-browser/blob/master/mock/tls.js) |\n| tty | [substack/tty-browserify](https://github.com/substack/tty-browserify) | [tty.js](https://github.com/webpack/node-libs-browser/blob/master/mock/tty.js) |\n| url | [defunctzombie/node-url](https://github.com/defunctzombie/node-url) | --- |\n| util | [defunctzombie/node-util](https://github.com/defunctzombie/node-util) | --- |\n| vm | [substack/vm-browserify](https://github.com/substack/vm-browserify) | --- |\n| zlib | [devongovett/browserify-zlib](https://github.com/devongovett/browserify-zlib) | --- |\n\n## Outdated versions \n\n### `buffer`\n\nThe current `buffer` implementation uses feross/buffer@4.x because feross/buffer@5.x relies on [typed arrays](https://github.com/feross/buffer/commit/5daca86b7cd5d2b8ccb167534d47421029f639e9#commitcomment-19698936).\nThis will be dropped as soon as IE9 is not a typical browser target anymore.\n\n### `punycode`\n\nThe current `punycode` implementation uses bestiejs/punycode.js@1.x because bestiejs/punycode.js@2.x requires modern JS engines that understand `const` and `let`.\nIt will be removed someday since it has already been [deprecated from the node API](https://nodejs.org/api/punycode.html).\n\n## License\n\nMIT\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2012 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/node-libs-browser/-/node-libs-browser-2.2.1.tgz#b64f513d18338625f90346d27b0d235e631f6425", "type": "tarball", "reference": "https://registry.yarnpkg.com/node-libs-browser/-/node-libs-browser-2.2.1.tgz", "hash": "b64f513d18338625f90346d27b0d235e631f6425", "integrity": "sha512-h/zcD8H9kaDZ9ALUWwlBUDo6TKF8a7qBSCSEGfjTVIYeqsioSKaAX+BN7NgiMGp6iSIXZ3PxgCu8KS3b71YK5Q==", "registry": "npm", "packageName": "node-libs-browser", "cacheIntegrity": "sha512-h/zcD8H9kaDZ9ALUWwlBUDo6TKF8a7qBSCSEGfjTVIYeqsioSKaAX+BN7NgiMGp6iSIXZ3PxgCu8KS3b71YK5Q== sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU="}, "registry": "npm", "hash": "b64f513d18338625f90346d27b0d235e631f6425"}