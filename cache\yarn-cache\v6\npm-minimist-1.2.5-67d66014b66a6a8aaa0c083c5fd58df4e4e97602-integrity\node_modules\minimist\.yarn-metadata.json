{"manifest": {"name": "minimist", "version": "1.2.5", "description": "parse argument options", "main": "index.js", "devDependencies": {"covert": "^1.0.0", "tap": "~0.4.0", "tape": "^3.5.0"}, "scripts": {"test": "tap test/*.js", "coverage": "covert test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-minimist-1.2.5-67d66014b66a6a8aaa0c083c5fd58df4e4e97602-integrity\\node_modules\\minimist\\package.json", "readmeFilename": "readme.markdown", "readme": "# minimist\n\nparse argument options\n\nThis module is the guts of optimist's argument parser without all the\nfanciful decoration.\n\n# example\n\n``` js\nvar argv = require('minimist')(process.argv.slice(2));\nconsole.log(argv);\n```\n\n```\n$ node example/parse.js -a beep -b boop\n{ _: [], a: 'beep', b: 'boop' }\n```\n\n```\n$ node example/parse.js -x 3 -y 4 -n5 -abc --beep=boop foo bar baz\n{ _: [ 'foo', 'bar', 'baz' ],\n  x: 3,\n  y: 4,\n  n: 5,\n  a: true,\n  b: true,\n  c: true,\n  beep: 'boop' }\n```\n\n# security\n\nPrevious versions had a prototype pollution bug that could cause privilege\nescalation in some circumstances when handling untrusted user input.\n\nPlease use version 1.2.3 or later: https://snyk.io/vuln/SNYK-JS-MINIMIST-559764\n\n# methods\n\n``` js\nvar parseArgs = require('minimist')\n```\n\n## var argv = parseArgs(args, opts={})\n\nReturn an argument object `argv` populated with the array arguments from `args`.\n\n`argv._` contains all the arguments that didn't have an option associated with\nthem.\n\nNumeric-looking arguments will be returned as numbers unless `opts.string` or\n`opts.boolean` is set for that argument name.\n\nAny arguments after `'--'` will not be parsed and will end up in `argv._`.\n\noptions can be:\n\n* `opts.string` - a string or array of strings argument names to always treat as\nstrings\n* `opts.boolean` - a boolean, string or array of strings to always treat as\nbooleans. if `true` will treat all double hyphenated arguments without equal signs\nas boolean (e.g. affects `--foo`, not `-f` or `--foo=bar`)\n* `opts.alias` - an object mapping string names to strings or arrays of string\nargument names to use as aliases\n* `opts.default` - an object mapping string argument names to default values\n* `opts.stopEarly` - when true, populate `argv._` with everything after the\nfirst non-option\n* `opts['--']` - when true, populate `argv._` with everything before the `--`\nand `argv['--']` with everything after the `--`. Here's an example:\n\n  ```\n  > require('./')('one two three -- four five --six'.split(' '), { '--': true })\n  { _: [ 'one', 'two', 'three' ],\n    '--': [ 'four', 'five', '--six' ] }\n  ```\n\n  Note that with `opts['--']` set, parsing for arguments still stops after the\n  `--`.\n\n* `opts.unknown` - a function which is invoked with a command line parameter not\ndefined in the `opts` configuration object. If the function returns `false`, the\nunknown option is not added to `argv`.\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install minimist\n```\n\n# license\n\nMIT\n", "licenseText": "This software is released under the MIT license:\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\n<PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/minimist/-/minimist-1.2.5.tgz#67d66014b66a6a8aaa0c083c5fd58df4e4e97602", "type": "tarball", "reference": "https://registry.yarnpkg.com/minimist/-/minimist-1.2.5.tgz", "hash": "67d66014b66a6a8aaa0c083c5fd58df4e4e97602", "integrity": "sha512-FM9nNUYrRBAELZQT3xeZQ7fmMOBg6nWNmJKTcgsJeaLstP/UODVpGsr5OhXhhXg6f+qtJ8uiZ+PUxkDWcgIXLw==", "registry": "npm", "packageName": "minimist", "cacheIntegrity": "sha512-FM9nNUYrRBAELZQT3xeZQ7fmMOBg6nWNmJKTcgsJeaLstP/UODVpGsr5OhXhhXg6f+qtJ8uiZ+PUxkDWcgIXLw== sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI="}, "registry": "npm", "hash": "67d66014b66a6a8aaa0c083c5fd58df4e4e97602"}