{"manifest": {"name": "fill-range", "description": "Fill in a range of numbers or letters, optionally passing an increment or `step` to use, or create a regex-compatible range with `options.toRegex`", "version": "7.0.1", "homepage": "https://github.com/jonschlinkert/fill-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "edo.rivai.nl"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "paulmillr.com"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/fill-range.git"}, "bugs": {"url": "https://github.com/jonschlinkert/fill-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=8"}, "scripts": {"test": "mocha"}, "dependencies": {"to-regex-range": "^5.0.1"}, "devDependencies": {"gulp-format-md": "^2.0.0", "mocha": "^6.1.1"}, "keywords": ["alpha", "alphabetical", "array", "bash", "brace", "expand", "expansion", "fill", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sh"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-fill-range-7.0.1-1919a6a7c75fe38b2c7c77e5198535da9acdda40-integrity\\node_modules\\fill-range\\package.json", "readmeFilename": "README.md", "readme": "# fill-range [![Donate](https://img.shields.io/badge/Donate-PayPal-green.svg)](https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=W8YFZ425KND68) [![NPM version](https://img.shields.io/npm/v/fill-range.svg?style=flat)](https://www.npmjs.com/package/fill-range) [![NPM monthly downloads](https://img.shields.io/npm/dm/fill-range.svg?style=flat)](https://npmjs.org/package/fill-range) [![NPM total downloads](https://img.shields.io/npm/dt/fill-range.svg?style=flat)](https://npmjs.org/package/fill-range) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/fill-range.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/fill-range)\n\n> Fill in a range of numbers or letters, optionally passing an increment or `step` to use, or create a regex-compatible range with `options.toRegex`\n\nPlease consider following this project's author, [<PERSON> Schlinkert](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save fill-range\n```\n\n## Usage\n\nExpands numbers and letters, optionally using a `step` as the last argument. _(Numbers may be defined as JavaScript numbers or strings)_.\n\n```js\nconst fill = require('fill-range');\n// fill(from, to[, step, options]);\n\nconsole.log(fill('1', '10')); //=> ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']\nconsole.log(fill('1', '10', { toRegex: true })); //=> [1-9]|10\n```\n\n**Params**\n\n* `from`: **{String|Number}** the number or letter to start with\n* `to`: **{String|Number}** the number or letter to end with\n* `step`: **{String|Number|Object|Function}** Optionally pass a [step](#optionsstep) to use.\n* `options`: **{Object|Function}**: See all available [options](#options)\n\n## Examples\n\nBy default, an array of values is returned.\n\n**Alphabetical ranges**\n\n```js\nconsole.log(fill('a', 'e')); //=> ['a', 'b', 'c', 'd', 'e']\nconsole.log(fill('A', 'E')); //=> [ 'A', 'B', 'C', 'D', 'E' ]\n```\n\n**Numerical ranges**\n\nNumbers can be defined as actual numbers or strings.\n\n```js\nconsole.log(fill(1, 5));     //=> [ 1, 2, 3, 4, 5 ]\nconsole.log(fill('1', '5')); //=> [ 1, 2, 3, 4, 5 ]\n```\n\n**Negative ranges**\n\nNumbers can be defined as actual numbers or strings.\n\n```js\nconsole.log(fill('-5', '-1')); //=> [ '-5', '-4', '-3', '-2', '-1' ]\nconsole.log(fill('-5', '5')); //=> [ '-5', '-4', '-3', '-2', '-1', '0', '1', '2', '3', '4', '5' ]\n```\n\n**Steps (increments)**\n\n```js\n// numerical ranges with increments\nconsole.log(fill('0', '25', 4)); //=> [ '0', '4', '8', '12', '16', '20', '24' ]\nconsole.log(fill('0', '25', 5)); //=> [ '0', '5', '10', '15', '20', '25' ]\nconsole.log(fill('0', '25', 6)); //=> [ '0', '6', '12', '18', '24' ]\n\n// alphabetical ranges with increments\nconsole.log(fill('a', 'z', 4)); //=> [ 'a', 'e', 'i', 'm', 'q', 'u', 'y' ]\nconsole.log(fill('a', 'z', 5)); //=> [ 'a', 'f', 'k', 'p', 'u', 'z' ]\nconsole.log(fill('a', 'z', 6)); //=> [ 'a', 'g', 'm', 's', 'y' ]\n```\n\n## Options\n\n### options.step\n\n**Type**: `number` (formatted as a string or number)\n\n**Default**: `undefined`\n\n**Description**: The increment to use for the range. Can be used with letters or numbers.\n\n**Example(s)**\n\n```js\n// numbers\nconsole.log(fill('1', '10', 2)); //=> [ '1', '3', '5', '7', '9' ]\nconsole.log(fill('1', '10', 3)); //=> [ '1', '4', '7', '10' ]\nconsole.log(fill('1', '10', 4)); //=> [ '1', '5', '9' ]\n\n// letters\nconsole.log(fill('a', 'z', 5)); //=> [ 'a', 'f', 'k', 'p', 'u', 'z' ]\nconsole.log(fill('a', 'z', 7)); //=> [ 'a', 'h', 'o', 'v' ]\nconsole.log(fill('a', 'z', 9)); //=> [ 'a', 'j', 's' ]\n```\n\n### options.strictRanges\n\n**Type**: `boolean`\n\n**Default**: `false`\n\n**Description**: By default, `null` is returned when an invalid range is passed. Enable this option to throw a `RangeError` on invalid ranges.\n\n**Example(s)**\n\nThe following are all invalid:\n\n```js\nfill('1.1', '2');   // decimals not supported in ranges\nfill('a', '2');     // incompatible range values\nfill(1, 10, 'foo'); // invalid \"step\" argument\n```\n\n### options.stringify\n\n**Type**: `boolean`\n\n**Default**: `undefined`\n\n**Description**: Cast all returned values to strings. By default, integers are returned as numbers.\n\n**Example(s)**\n\n```js\nconsole.log(fill(1, 5));                    //=> [ 1, 2, 3, 4, 5 ]\nconsole.log(fill(1, 5, { stringify: true })); //=> [ '1', '2', '3', '4', '5' ]\n```\n\n### options.toRegex\n\n**Type**: `boolean`\n\n**Default**: `undefined`\n\n**Description**: Create a regex-compatible source string, instead of expanding values to an array.\n\n**Example(s)**\n\n```js\n// alphabetical range\nconsole.log(fill('a', 'e', { toRegex: true })); //=> '[a-e]'\n// alphabetical with step\nconsole.log(fill('a', 'z', 3, { toRegex: true })); //=> 'a|d|g|j|m|p|s|v|y'\n// numerical range\nconsole.log(fill('1', '100', { toRegex: true })); //=> '[1-9]|[1-9][0-9]|100'\n// numerical range with zero padding\nconsole.log(fill('000001', '100000', { toRegex: true }));\n//=> '0{5}[1-9]|0{4}[1-9][0-9]|0{3}[1-9][0-9]{2}|0{2}[1-9][0-9]{3}|0[1-9][0-9]{4}|100000'\n```\n\n### options.transform\n\n**Type**: `function`\n\n**Default**: `undefined`\n\n**Description**: Customize each value in the returned array (or [string](#optionstoRegex)). _(you can also pass this function as the last argument to `fill()`)_.\n\n**Example(s)**\n\n```js\n// add zero padding\nconsole.log(fill(1, 5, value => String(value).padStart(4, '0')));\n//=> ['0001', '0002', '0003', '0004', '0005']\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Contributors\n\n| **Commits** | **Contributor** |  \n| --- | --- |  \n| 116 | [jonschlinkert](https://github.com/jonschlinkert) |  \n| 4   | [paulmillr](https://github.com/paulmillr) |  \n| 2   | [realityking](https://github.com/realityking) |  \n| 2   | [bluelovers](https://github.com/bluelovers) |  \n| 1   | [edorivai](https://github.com/edorivai) |  \n| 1   | [wtgtybhertgeghgtwtg](https://github.com/wtgtybhertgeghgtwtg) |  \n\n### Author\n\n**Jon Schlinkert**\n\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n\nPlease consider supporting me on Patreon, or [start your own Patreon page](https://patreon.com/invite/bxpbvm)!\n\n<a href=\"https://www.patreon.com/jonschlinkert\">\n<img src=\"https://c5.patreon.com/external/logo/<EMAIL>\" height=\"50\">\n</a>\n\n### License\n\nCopyright © 2019, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.8.0, on April 08, 2019._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-present, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/fill-range/-/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40", "type": "tarball", "reference": "https://registry.yarnpkg.com/fill-range/-/fill-range-7.0.1.tgz", "hash": "1919a6a7c75fe38b2c7c77e5198535da9acdda40", "integrity": "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==", "registry": "npm", "packageName": "fill-range", "cacheIntegrity": "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ== sha1-GRmmp8df44ssfHflGYU12prN2kA="}, "registry": "npm", "hash": "1919a6a7c75fe38b2c7c77e5198535da9acdda40"}