{"manifest": {"name": "nth-check", "version": "1.0.2", "description": "performant nth-check parser & compiler", "main": "index.js", "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "https://github.com/fb55/nth-check"}, "files": ["compile.js", "index.js", "parse.js"], "keywords": ["nth-child", "nth", "css"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/fb55/nth-check/issues"}, "homepage": "https://github.com/fb55/nth-check", "dependencies": {"boolbase": "~1.0.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-nth-check-1.0.2-b2bd295c37e3dd58a3bf0700376663ba4d9cf05c-integrity\\node_modules\\nth-check\\package.json", "readmeFilename": "README.md", "readme": "# nth-check [![Build Status](https://travis-ci.org/fb55/nth-check.svg)](https://travis-ci.org/fb55/nth-check)\n\nA performant nth-check parser & compiler.\n\n### About\n\nThis module can be used to parse & compile nth-checks, as they are found in CSS 3's `nth-child()` and `nth-last-of-type()`.\n\n`nth-check` focusses on speed, providing optimized functions for different kinds of nth-child formulas, while still following the [spec](http://www.w3.org/TR/css3-selectors/#nth-child-pseudo).\n\n### API\n\n```js\nvar nthCheck = require(\"nth-check\");\n```\n\n##### `nthCheck(formula)`\n\nFirst parses, then compiles the formula.\n\n##### `nthCheck.parse(formula)`\n\nParses the expression, throws a `SyntaxError` if it fails, otherwise returns an array containing two elements.\n\n__Example:__\n\n```js\nnthCheck.parse(\"2n+3\") //[2, 3]\n```\n\n##### `nthCheck.compile([a, b])`\n\nTakes an array with two elements (as returned by `.parse`) and returns a highly optimized function.\n\nIf the formula doesn't match any elements, it returns [`boolbase`](https://github.com/fb55/boolbase)'s `falseFunc`, otherwise, a function accepting an _index_ is returned, which returns whether or not a passed _index_ matches the formula. (Note: The spec starts counting at `1`, the returned function at `0`).\n\n__Example:__\n```js\nvar check = nthCheck.compile([2, 3]);\n\ncheck(0) //false\ncheck(1) //false\ncheck(2) //true\ncheck(3) //false\ncheck(4) //true\ncheck(5) //false\ncheck(6) //true\n```\n\n---\nLicense: BSD\n", "licenseText": "Copyright (c) <PERSON>\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n\nRedistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.\n\nRedistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.\n\nTHIS IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERC<PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS,\nEVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/nth-check/-/nth-check-1.0.2.tgz#b2bd295c37e3dd58a3bf0700376663ba4d9cf05c", "type": "tarball", "reference": "https://registry.yarnpkg.com/nth-check/-/nth-check-1.0.2.tgz", "hash": "b2bd295c37e3dd58a3bf0700376663ba4d9cf05c", "integrity": "sha512-WeBOdju8SnzPN5vTUJYxYUxLeXpCaVP5i5e0LF8fg7WORF2Wd7wFX/pk0tYZk7s8T+J7VLy0Da6J1+wCT0AtHg==", "registry": "npm", "packageName": "nth-check", "cacheIntegrity": "sha512-WeBOdju8SnzPN5vTUJYxYUxLeXpCaVP5i5e0LF8fg7WORF2Wd7wFX/pk0tYZk7s8T+J7VLy0Da6J1+wCT0AtHg== sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw="}, "registry": "npm", "hash": "b2bd295c37e3dd58a3bf0700376663ba4d9cf05c"}