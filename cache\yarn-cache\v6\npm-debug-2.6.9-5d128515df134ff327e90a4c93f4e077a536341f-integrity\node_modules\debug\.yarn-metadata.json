{"manifest": {"name": "debug", "version": "2.6.9", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"ms": "2.0.0"}, "devDependencies": {"browserify": "9.0.3", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-debug-2.6.9-5d128515df134ff327e90a4c93f4e077a536341f-integrity\\node_modules\\debug\\package.json", "readmeFilename": "README.md", "readme": "# debug\n[![Build Status](https://travis-ci.org/visionmedia/debug.svg?branch=master)](https://travis-ci.org/visionmedia/debug)  [![Coverage Status](https://coveralls.io/repos/github/visionmedia/debug/badge.svg?branch=master)](https://coveralls.io/github/visionmedia/debug?branch=master)  [![Slack](https://visionmedia-community-slackin.now.sh/badge.svg)](https://visionmedia-community-slackin.now.sh/) [![OpenCollective](https://opencollective.com/debug/backers/badge.svg)](#backers) \n[![OpenCollective](https://opencollective.com/debug/sponsors/badge.svg)](#sponsors)\n\n\n\nA tiny node.js debugging utility modelled after node core's debugging technique.\n\n**Discussion around the V3 API is under way [here](https://github.com/visionmedia/debug/issues/370)**\n\n## Installation\n\n```bash\n$ npm install debug\n```\n\n## Usage\n\n`debug` exposes a function; simply pass this function the name of your module, and it will return a decorated version of `console.error` for you to pass debug statements to. This will allow you to toggle the debug output for different parts of your module as well as the module as a whole.\n\nExample _app.js_:\n\n```js\nvar debug = require('debug')('http')\n  , http = require('http')\n  , name = 'My App';\n\n// fake app\n\ndebug('booting %s', name);\n\nhttp.createServer(function(req, res){\n  debug(req.method + ' ' + req.url);\n  res.end('hello\\n');\n}).listen(3000, function(){\n  debug('listening');\n});\n\n// fake worker of some kind\n\nrequire('./worker');\n```\n\nExample _worker.js_:\n\n```js\nvar debug = require('debug')('worker');\n\nsetInterval(function(){\n  debug('doing some work');\n}, 1000);\n```\n\n The __DEBUG__ environment variable is then used to enable these based on space or comma-delimited names. Here are some examples:\n\n  ![debug http and worker](http://f.cl.ly/items/18471z1H402O24072r1J/Screenshot.png)\n\n  ![debug worker](http://f.cl.ly/items/1X413v1a3M0d3C2c1E0i/Screenshot.png)\n\n#### Windows note\n\n On Windows the environment variable is set using the `set` command.\n\n ```cmd\n set DEBUG=*,-not_this\n ```\n\n Note that PowerShell uses different syntax to set environment variables.\n\n ```cmd\n $env:DEBUG = \"*,-not_this\"\n  ```\n\nThen, run the program to be debugged as usual.\n\n## Millisecond diff\n\n  When actively developing an application it can be useful to see when the time spent between one `debug()` call and the next. Suppose for example you invoke `debug()` before requesting a resource, and after as well, the \"+NNNms\" will show you how much time was spent between calls.\n\n  ![](http://f.cl.ly/items/2i3h1d3t121M2Z1A3Q0N/Screenshot.png)\n\n  When stdout is not a TTY, `Date#toUTCString()` is used, making it more useful for logging the debug information as shown below:\n\n  ![](http://f.cl.ly/items/112H3i0e0o0P0a2Q2r11/Screenshot.png)\n\n## Conventions\n\n  If you're using this in one or more of your libraries, you _should_ use the name of your library so that developers may toggle debugging as desired without guessing names. If you have more than one debuggers you _should_ prefix them with your library name and use \":\" to separate features. For example \"bodyParser\" from Connect would then be \"connect:bodyParser\".\n\n## Wildcards\n\n  The `*` character may be used as a wildcard. Suppose for example your library has debuggers named \"connect:bodyParser\", \"connect:compress\", \"connect:session\", instead of listing all three with `DEBUG=connect:bodyParser,connect:compress,connect:session`, you may simply do `DEBUG=connect:*`, or to run everything using this module simply use `DEBUG=*`.\n\n  You can also exclude specific debuggers by prefixing them with a \"-\" character.  For example, `DEBUG=*,-connect:*` would include all debuggers except those starting with \"connect:\".\n\n## Environment Variables\n\n  When running through Node.js, you can set a few environment variables that will\n  change the behavior of the debug logging:\n\n| Name      | Purpose                                         |\n|-----------|-------------------------------------------------|\n| `DEBUG`   | Enables/disables specific debugging namespaces. |\n| `DEBUG_COLORS`| Whether or not to use colors in the debug output. |\n| `DEBUG_DEPTH` | Object inspection depth. |\n| `DEBUG_SHOW_HIDDEN` | Shows hidden properties on inspected objects. |\n\n\n  __Note:__ The environment variables beginning with `DEBUG_` end up being\n  converted into an Options object that gets used with `%o`/`%O` formatters.\n  See the Node.js documentation for\n  [`util.inspect()`](https://nodejs.org/api/util.html#util_util_inspect_object_options)\n  for the complete list.\n\n## Formatters\n\n\n  Debug uses [printf-style](https://wikipedia.org/wiki/Printf_format_string) formatting. Below are the officially supported formatters:\n\n| Formatter | Representation |\n|-----------|----------------|\n| `%O`      | Pretty-print an Object on multiple lines. |\n| `%o`      | Pretty-print an Object all on a single line. |\n| `%s`      | String. |\n| `%d`      | Number (both integer and float). |\n| `%j`      | JSON. Replaced with the string '[Circular]' if the argument contains circular references. |\n| `%%`      | Single percent sign ('%'). This does not consume an argument. |\n\n### Custom formatters\n\n  You can add custom formatters by extending the `debug.formatters` object. For example, if you wanted to add support for rendering a Buffer as hex with `%h`, you could do something like:\n\n```js\nconst createDebug = require('debug')\ncreateDebug.formatters.h = (v) => {\n  return v.toString('hex')\n}\n\n// …elsewhere\nconst debug = createDebug('foo')\ndebug('this is hex: %h', new Buffer('hello world'))\n//   foo this is hex: 68656c6c6f20776f726c6421 +0ms\n```\n\n## Browser support\n  You can build a browser-ready script using [browserify](https://github.com/substack/node-browserify),\n  or just use the [browserify-as-a-service](https://wzrd.in/) [build](https://wzrd.in/standalone/debug@latest),\n  if you don't want to build it yourself.\n\n  Debug's enable state is currently persisted by `localStorage`.\n  Consider the situation shown below where you have `worker:a` and `worker:b`,\n  and wish to debug both. You can enable this using `localStorage.debug`:\n\n```js\nlocalStorage.debug = 'worker:*'\n```\n\nAnd then refresh the page.\n\n```js\na = debug('worker:a');\nb = debug('worker:b');\n\nsetInterval(function(){\n  a('doing some work');\n}, 1000);\n\nsetInterval(function(){\n  b('doing some work');\n}, 1200);\n```\n\n#### Web Inspector Colors\n\n  Colors are also enabled on \"Web Inspectors\" that understand the `%c` formatting\n  option. These are WebKit web inspectors, Firefox ([since version\n  31](https://hacks.mozilla.org/2014/05/editable-box-model-multiple-selection-sublime-text-keys-much-more-firefox-developer-tools-episode-31/))\n  and the Firebug plugin for Firefox (any version).\n\n  Colored output looks something like:\n\n  ![](https://cloud.githubusercontent.com/assets/71256/3139768/b98c5fd8-e8ef-11e3-862a-f7253b6f47c6.png)\n\n\n## Output streams\n\n  By default `debug` will log to stderr, however this can be configured per-namespace by overriding the `log` method:\n\nExample _stdout.js_:\n\n```js\nvar debug = require('debug');\nvar error = debug('app:error');\n\n// by default stderr is used\nerror('goes to stderr!');\n\nvar log = debug('app:log');\n// set this namespace to log via console.log\nlog.log = console.log.bind(console); // don't forget to bind to console!\nlog('goes to stdout');\nerror('still goes to stderr!');\n\n// set all output to go via console.info\n// overrides all per-namespace log settings\ndebug.log = console.info.bind(console);\nerror('now goes to stdout via console.info');\nlog('still goes to stdout, but via console.info now');\n```\n\n\n## Authors\n\n - TJ Holowaychuk\n - Nathan Rajlich\n - Andrew Rhyne\n \n## Backers\n\nSupport us with a monthly donation and help us continue our activities. [[Become a backer](https://opencollective.com/debug#backer)]\n\n<a href=\"https://opencollective.com/debug/backer/0/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/0/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/1/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/1/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/2/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/2/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/3/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/3/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/4/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/4/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/5/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/5/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/6/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/6/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/7/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/7/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/8/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/8/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/9/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/9/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/10/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/10/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/11/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/11/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/12/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/12/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/13/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/13/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/14/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/14/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/15/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/15/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/16/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/16/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/17/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/17/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/18/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/18/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/19/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/19/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/20/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/20/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/21/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/21/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/22/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/22/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/23/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/23/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/24/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/24/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/25/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/25/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/26/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/26/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/27/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/27/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/28/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/28/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/29/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/29/avatar.svg\"></a>\n\n\n## Sponsors\n\nBecome a sponsor and get your logo on our README on Github with a link to your site. [[Become a sponsor](https://opencollective.com/debug#sponsor)]\n\n<a href=\"https://opencollective.com/debug/sponsor/0/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/0/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/1/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/1/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/2/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/2/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/3/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/3/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/4/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/4/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/5/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/5/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/6/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/6/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/7/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/7/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/8/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/8/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/9/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/9/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/10/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/10/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/11/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/11/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/12/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/12/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/13/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/13/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/14/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/14/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/15/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/15/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/16/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/16/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/17/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/17/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/18/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/18/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/19/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/19/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/20/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/20/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/21/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/21/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/22/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/22/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/23/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/23/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/24/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/24/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/25/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/25/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/26/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/26/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/27/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/27/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/28/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/28/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/29/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/29/avatar.svg\"></a>\n\n## License\n\n(The MIT License)\n\nCopyright (c) 2014-2016 TJ Holowaychuk &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2014 <PERSON><PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software \nand associated documentation files (the 'Software'), to deal in the Software without restriction, \nincluding without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, \nand/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial \nportions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT \nLIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. \nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, \nWHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE \nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/debug/-/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f", "type": "tarball", "reference": "https://registry.yarnpkg.com/debug/-/debug-2.6.9.tgz", "hash": "5d128515df134ff327e90a4c93f4e077a536341f", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "registry": "npm", "packageName": "debug", "cacheIntegrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA== sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="}, "registry": "npm", "hash": "5d128515df134ff327e90a4c93f4e077a536341f"}