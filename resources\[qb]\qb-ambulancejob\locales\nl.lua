local Translations = {
    error = {
        canceled = 'Geannuleerd',
        bled_out = 'Je hebt het bewustzijn volledig verloren',
        impossible = 'Deze actie is niet mogelijk..',
        no_player = 'Geen burger in de buurt',
        no_firstaid = 'Je hebt een EHBO kit nodig',
        no_bandage = 'Je hebt een verbandje nodig',
        beds_taken = 'Alle bedden zijn volzet..',
        possessions_taken = 'Je bezittingen zijn tijdelijk afgenomen..',
        not_enough_money = 'Je hebt niet genoeg geld bij..',
        cant_help = 'Je kan deze persoon niet helpen...',
        not_ems = 'Je bent geen medisch personeel, of je bent niet indienst',
        not_online = 'Kan burger niet vinden'
    },
    success = {
        revived = 'Je hebt iemand volledig geholpen',
        healthy_player = 'Persoon is gezond',
        helped_player = 'Je hebt de persoon geholpen',
        wounds_healed = 'Je kwaaltjes zijn verzorgd',
        being_helped = 'Je wordt geholpen..'
    },
    info = {
        civ_died = 'Persoon is gestorven',
        civ_down = 'Persoon is gewond/onwel',
        civ_call = 'Noodoproep 112',
        self_death = 'Door zichzelf of een hersenloze',
        wep_unknown = 'Onbekend',
        respawn_txt = 'RESPAWN IN: ~r~%{deathtime}~s~ SECONDEN',
        respawn_revive = 'HOUDT [~r~E~s~] INGEDRUKT VOOR %{holdtime} TE RESPAWNEN VOOR €~r~%{cost}~s~',
        bleed_out = 'JE VERLIEST HET BEWUSTZIJN IN: ~r~%{time}~s~ SECONDEN',
        bleed_out_help = 'JE VERLIEST HET BEWUSTZIJN IN: ~r~%{time}~s~ SECONDEN, MAAR JE KAN NOG GEHOLPEN WORDEN',
        request_help = 'DRUK OP [~r~G~s~] OM HULP TE VRAGEN',
        help_requested = 'De noodcentrale 112 is in kennis',
        amb_plate = 'AMBU',  -- Should only be 4 characters long due to the last 4 being a random 4 digits
        heli_plate = 'HELI', -- Should only be 4 characters long due to the last 4 being a random 4 digits
        status = 'Amnese',
        is_status = 'Is %{status}',
        healthy = 'Je bent weer tip top!',
        safe = 'Ziekenhuiskluis',
        pb_hospital = 'AZ Degrens',
        paleto_hospital = 'AZ Degrens Campus Paleto',
        pain_message = 'Je %{limb} voelt %{severity}',
        many_places = 'Je hebt op verschillende plaatsen pijn..',
        bleed_alert = 'Je bent %{bleedstate}',
        ems_alert = 'Noodcentrale 112 - %{text}',
        mr = 'Mr.',
        mrs = 'Mevr.',
        dr_needed = 'Medisch personeel wordt gevraagd in %{hospital}',
        ems_report = 'Noodoproep',
        message_sent = 'Bericht wordt verzonden',
        check_health = 'Controlleer prsoon',
        heal_player = 'Verzorg persoon',
        revive_player = 'Help een persoon volledig',
        revive_player_a = 'Revive een speler of jezelf (Admin Only)',
        player_id = 'Persoons ID (mag leeg zijn)',
        pain_level = 'Zet een pijn level voor een speler of jezelf (Admin Only)',
        kill = 'Kill een speler of jezelf (Admin Only)',
        heal_player_a = 'Verzorg een speler of jezelf (Admin Only)',
    },
    mail = {
        subject = 'Ziekenhuiskosten',
        message = 'Beste %{gender} %{lastname}, <br /><br />Bedankt voor het bezoek aan de spoedgevallendienst AZ Degrens.<br />Totaal bedrag: <strong>€%{costs}</strong><br /><br />Veel beterschap enzo!'
    },
    states = {
        irritated = 'Geïrriteerd ',
        quite_painful = 'Redelijk pijnpijk',
        painful = 'pijn',
        really_painful = 'veel pijn',
        little_bleed = 'Bloedt een beetje..',
        bleed = 'Redelijke bloeding..',
        lot_bleed = 'Veel bloed..',
        big_bleed = 'heel veel bloed..',
    },
    menu = {
        amb_vehicles = 'Medische voertuigen',
        status = 'gezondheidstatus',
        close = '⬅ Menu sluiten',
    },
    text = {
        pstash_button = '[E] - Persoonlijke opslag',
        pstash = 'Persoonlijke opslag',
        onduty_button = '[E] - Ga indienst',
        offduty_button = '[E] - Ga uitdienst',
        duty = 'in/uit dienst',
        armory_button = '[E] - steekvest',
        armory = 'Steekvest',
        veh_button = '[E] - Pak of parkeer voertuig',
        heli_button = '[E] - Pak of parkeer heli',
        elevator_roof = '[E] - Lift naar het dak',
        elevator_main = '[E] - Lift naar beneden',
        bed_out = '[E] - Om recht te staan..',
        call_doc = '[E] - Roep medisch personeel',
        call = 'Roep',
        check_in = '[E] Inschrijven',
        check = 'Inschrijven',
        lie_bed = '[E] - Om in het bed te gaan liggen'
    },
    body = {
        head = 'Hoofd',
        neck = 'Nek',
        spine = 'Wervelkolom',
        upper_body = 'Bovenlichaam',
        lower_body = 'Onderlichaam',
        left_arm = 'Arm links',
        left_hand = 'Hand links',
        left_fingers = 'Vingers links',
        left_leg = 'Been links',
        left_foot = 'Voet links',
        right_arm = 'Arm rechts',
        right_hand = 'Hand rechts',
        right_fingers = 'Vingers rechts',
        right_leg = 'Been rechts',
        right_foot = 'Voet rechts',
    },
    progress = {
        ifaks = 'Ifaks nemen..',
        bandage = 'Verbrand gebruiken..',
        painkillers = 'Pijnmedicatie nemen..',
        revive = 'Persoon helpen..',
        healing = 'Wonden verzorgen..',
        checking_in = 'Inschrijven..',
    },
    logs = {
        death_log_title = '%{playername} (%{playerid}) is bewusteloos',
        death_log_message = '%{killername} heeft %{playername} vermoord/verwond met **%{weaponlabel}** (%{weaponname})',
    }
}

if GetConvar('qb_locale', 'en') == 'nl' then
    Lang = Locale:new({
        phrases = Translations,
        warnOnMissing = true,
        fallbackLang = Lang,
    })
end
