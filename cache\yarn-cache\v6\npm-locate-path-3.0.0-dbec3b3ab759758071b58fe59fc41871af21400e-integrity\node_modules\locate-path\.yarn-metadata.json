{"manifest": {"name": "locate-path", "version": "3.0.0", "description": "Get the first path that exists on disk of multiple paths", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/locate-path.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["locate", "path", "paths", "file", "files", "exists", "find", "finder", "search", "searcher", "array", "iterable", "iterator"], "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-locate-path-3.0.0-dbec3b3ab759758071b58fe59fc41871af21400e-integrity\\node_modules\\locate-path\\package.json", "readmeFilename": "readme.md", "readme": "# locate-path [![Build Status](https://travis-ci.org/sindresorhus/locate-path.svg?branch=master)](https://travis-ci.org/sindresorhus/locate-path)\n\n> Get the first path that exists on disk of multiple paths\n\n\n## Install\n\n```\n$ npm install locate-path\n```\n\n\n## Usage\n\nHere we find the first file that exists on disk, in array order.\n\n```js\nconst locatePath = require('locate-path');\n\nconst files = [\n\t'unicorn.png',\n\t'rainbow.png', // Only this one actually exists on disk\n\t'pony.png'\n];\n\n(async () => {\n\tconsole(await locatePath(files));\n\t//=> 'rainbow'\n})();\n```\n\n\n## API\n\n### locatePath(input, [options])\n\nReturns a `Promise` for the first path that exists or `undefined` if none exists.\n\n#### input\n\nType: `Iterable<string>`\n\nPaths to check.\n\n#### options\n\nType: `Object`\n\n##### concurrency\n\nType: `number`<br>\nDefault: `Infinity`<br>\nMinimum: `1`\n\nNumber of concurrently pending promises.\n\n##### preserveOrder\n\nType: `boolean`<br>\nDefault: `true`\n\nPreserve `input` order when searching.\n\nDisable this to improve performance if you don't care about the order.\n\n##### cwd\n\nType: `string`<br>\nDefault: `process.cwd()`\n\nCurrent working directory.\n\n### locatePath.sync(input, [options])\n\nReturns the first path that exists or `undefined` if none exists.\n\n#### input\n\nType: `Iterable<string>`\n\nPaths to check.\n\n#### options\n\nType: `Object`\n\n##### cwd\n\nSame as above.\n\n\n## Related\n\n- [path-exists](https://github.com/sindresorhus/path-exists) - Check if a path exists\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com)\n", "licenseText": "MIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/locate-path/-/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e", "type": "tarball", "reference": "https://registry.yarnpkg.com/locate-path/-/locate-path-3.0.0.tgz", "hash": "dbec3b3ab759758071b58fe59fc41871af21400e", "integrity": "sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==", "registry": "npm", "packageName": "locate-path", "cacheIntegrity": "sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A== sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4="}, "registry": "npm", "hash": "dbec3b3ab759758071b58fe59fc41871af21400e"}