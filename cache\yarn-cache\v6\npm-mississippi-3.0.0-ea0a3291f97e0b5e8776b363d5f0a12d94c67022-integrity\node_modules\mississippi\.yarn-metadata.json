{"manifest": {"name": "mississippi", "version": "3.0.0", "description": "a collection of useful streams", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^3.0.0", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}, "devDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/maxogden/mississippi.git"}, "engines": {"node": ">=4.0.0"}, "bugs": {"url": "https://github.com/maxogden/mississippi/issues"}, "homepage": "https://github.com/maxogden/mississippi#readme", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-mississippi-3.0.0-ea0a3291f97e0b5e8776b363d5f0a12d94c67022-integrity\\node_modules\\mississippi\\package.json", "readmeFilename": "readme.md", "readme": "# mississippi\n\na collection of useful stream utility modules. learn how the modules work using this and then pick the ones you want and use them individually\n\nthe goal of the modules included in mississippi is to make working with streams easy without sacrificing speed, error handling or composability.\n\n## usage\n\n```js\nvar miss = require('mississippi')\n```\n\n## methods\n\n- [pipe](#pipe)\n- [each](#each)\n- [pipeline](#pipeline)\n- [duplex](#duplex)\n- [through](#through)\n- [from](#from)\n- [to](#to)\n- [concat](#concat)\n- [finished](#finished)\n- [parallel](#parallel)\n\n### pipe\n\n##### `miss.pipe(stream1, stream2, stream3, ..., cb)`\n\nPipes streams together and destroys all of them if one of them closes. Calls `cb` with `(error)` if there was an error in any of the streams.\n\nWhen using standard `source.pipe(destination)` the source will _not_ be destroyed if the destination emits close or error. You are also not able to provide a callback to tell when the pipe has finished.\n\n`miss.pipe` does these two things for you, ensuring you handle stream errors 100% of the time (unhandled errors are probably the most common bug in most node streams code)\n\n#### original module\n\n`miss.pipe` is provided by [`require('pump')`](https://www.npmjs.com/package/pump)\n\n#### example\n\n```js\n// lets do a simple file copy\nvar fs = require('fs')\n\nvar read = fs.createReadStream('./original.zip')\nvar write = fs.createWriteStream('./copy.zip')\n\n// use miss.pipe instead of read.pipe(write)\nmiss.pipe(read, write, function (err) {\n  if (err) return console.error('Copy error!', err)\n  console.log('Copied successfully')\n})\n```\n\n### each\n\n##### `miss.each(stream, each, [done])`\n\nIterate the data in `stream` one chunk at a time. Your `each` function will be called with `(data, next)` where data is a data chunk and next is a callback. Call `next` when you are ready to consume the next chunk.\n\nOptionally you can call `next` with an error to destroy the stream. You can also pass the optional third argument, `done`, which is a function that will be called with `(err)` when the stream ends. The `err` argument will be populated with an error if the stream emitted an error.\n\n#### original module\n\n`miss.each` is provided by [`require('stream-each')`](https://www.npmjs.com/package/stream-each)\n\n#### example\n\n```js\nvar fs = require('fs')\nvar split = require('split2')\n\nvar newLineSeparatedNumbers = fs.createReadStream('numbers.txt')\n\nvar pipeline = miss.pipeline(newLineSeparatedNumbers, split())\nmiss.each(pipeline, eachLine, done)\nvar sum = 0\n\nfunction eachLine (line, next) {\n  sum += parseInt(line.toString())\n  next()\n}\n\nfunction done (err) {\n  if (err) throw err\n  console.log('sum is', sum)\n}\n```\n\n### pipeline\n\n##### `var pipeline = miss.pipeline(stream1, stream2, stream3, ...)`\n\nBuilds a pipeline from all the transform streams passed in as arguments by piping them together and returning a single stream object that lets you write to the first stream and read from the last stream.\n\nIf you are pumping object streams together use `pipeline = miss.pipeline.obj(s1, s2, ...)`.\n\nIf any of the streams in the pipeline emits an error or gets destroyed, or you destroy the stream it returns, all of the streams will be destroyed and cleaned up for you.\n\n#### original module\n\n`miss.pipeline` is provided by [`require('pumpify')`](https://www.npmjs.com/package/pumpify)\n\n#### example\n\n```js\n// first create some transform streams (note: these two modules are fictional)\nvar imageResize = require('image-resizer-stream')({width: 400})\nvar pngOptimizer = require('png-optimizer-stream')({quality: 60})\n\n// instead of doing a.pipe(b), use pipelin\nvar resizeAndOptimize = miss.pipeline(imageResize, pngOptimizer)\n// `resizeAndOptimize` is a transform stream. when you write to it, it writes\n// to `imageResize`. when you read from it, it reads from `pngOptimizer`.\n// it handles piping all the streams together for you\n\n// use it like any other transform stream\nvar fs = require('fs')\n\nvar read = fs.createReadStream('./image.png')\nvar write = fs.createWriteStream('./resized-and-optimized.png')\n\nmiss.pipe(read, resizeAndOptimize, write, function (err) {\n  if (err) return console.error('Image processing error!', err)\n  console.log('Image processed successfully')\n})\n```\n\n### duplex\n\n##### `var duplex = miss.duplex([writable, readable, opts])`\n\nTake two separate streams, a writable and a readable, and turn them into a single [duplex (readable and writable) stream](https://nodejs.org/api/stream.html#stream_class_stream_duplex).\n\nThe returned stream will emit data from the readable. When you write to it it writes to the writable.\n\nYou can either choose to supply the writable and the readable at the time you create the stream, or you can do it later using the `.setWritable` and `.setReadable` methods and data written to the stream in the meantime will be buffered for you.\n\n#### original module\n\n`miss.duplex` is provided by [`require('duplexify')`](https://www.npmjs.com/package/duplexify)\n\n#### example\n\n```js\n// lets spawn a process and take its stdout and stdin and combine them into 1 stream\nvar child = require('child_process')\n\n// @- tells it to read from stdin, --data-binary sets 'raw' binary mode\nvar curl = child.spawn('curl -X POST --data-binary @- http://foo.com')\n\n// duplexCurl will write to stdin and read from stdout\nvar duplexCurl = miss.duplex(curl.stdin, curl.stdout)\n```\n\n### through\n\n##### `var transformer = miss.through([options, transformFunction, flushFunction])`\n\nMake a custom [transform stream](https://nodejs.org/docs/latest/api/stream.html#stream_class_stream_transform).\n\nThe `options` object is passed to the internal transform stream and can be used to create an `objectMode` stream (or use the shortcut `miss.through.obj([...])`)\n\nThe `transformFunction` is called when data is available for the writable side and has the signature `(chunk, encoding, cb)`. Within the function, add data to the readable side any number of times with `this.push(data)`. Call `cb()` to indicate processing of the `chunk` is complete. Or to easily emit a single error or chunk, call `cb(err, chunk)`\n\nThe `flushFunction`, with signature `(cb)`, is called just before the stream is complete and should be used to wrap up stream processing.\n\n#### original module\n\n`miss.through` is provided by [`require('through2')`](https://www.npmjs.com/package/through2)\n\n#### example\n\n```js\nvar fs = require('fs')\n\nvar read = fs.createReadStream('./boring_lowercase.txt')\nvar write = fs.createWriteStream('./AWESOMECASE.TXT')\n\n// Leaving out the options object\nvar uppercaser = miss.through(\n  function (chunk, enc, cb) {\n    cb(null, chunk.toString().toUpperCase())\n  },\n  function (cb) {\n    cb(null, 'ONE LAST BIT OF UPPERCASE')\n  }\n)\n\nmiss.pipe(read, uppercaser, write, function (err) {\n  if (err) return console.error('Trouble uppercasing!')\n  console.log('Splendid uppercasing!')\n})\n```\n\n### from\n\n##### `miss.from([opts], read)`\n\nMake a custom [readable stream](https://nodejs.org/docs/latest/api/stream.html#stream_class_stream_readable).\n\n`opts` contains the options to pass on to the ReadableStream constructor e.g. for creating a readable object stream (or use the shortcut `miss.from.obj([...])`).\n\nReturns a readable stream that calls `read(size, next)` when data is requested from the stream.\n\n- `size` is the recommended amount of data (in bytes) to retrieve.\n- `next(err, chunk)` should be called when you're ready to emit more data.\n\n#### original module\n\n`miss.from` is provided by [`require('from2')`](https://www.npmjs.com/package/from2)\n\n#### example\n\n```js\n\n\nfunction fromString(string) {\n  return miss.from(function(size, next) {\n    // if there's no more content\n    // left in the string, close the stream.\n    if (string.length <= 0) return next(null, null)\n\n    // Pull in a new chunk of text,\n    // removing it from the string.\n    var chunk = string.slice(0, size)\n    string = string.slice(size)\n\n    // Emit \"chunk\" from the stream.\n    next(null, chunk)\n  })\n}\n\n// pipe \"hello world\" out\n// to stdout.\nfromString('hello world').pipe(process.stdout)\n```\n\n### to\n\n##### `miss.to([options], write, [flush])`\n\nMake a custom [writable stream](https://nodejs.org/docs/latest/api/stream.html#stream_class_stream_writable).\n\n`opts` contains the options to pass on to the WritableStream constructor e.g. for creating a writable object stream (or use the shortcut `miss.to.obj([...])`).\n\nReturns a writable stream that calls `write(data, enc, cb)` when data is written to the stream.\n\n- `data` is the received data to write the destination.\n- `enc` encoding of the piece of data received.\n- `cb(err, data)` should be called when you're ready to write more data, or encountered an error.\n\n`flush(cb)` is called before `finish` is emitted and allows for cleanup steps to occur.\n\n#### original module\n\n`miss.to` is provided by [`require('flush-write-stream')`](https://www.npmjs.com/package/flush-write-stream)\n\n#### example\n\n```js\nvar ws = miss.to(write, flush)\n\nws.on('finish', function () {\n  console.log('finished')\n})\n\nws.write('hello')\nws.write('world')\nws.end()\n\nfunction write (data, enc, cb) {\n  // i am your normal ._write method\n  console.log('writing', data.toString())\n  cb()\n}\n\nfunction flush (cb) {\n  // i am called before finish is emitted\n  setTimeout(cb, 1000) // wait 1 sec\n}\n```\n\nIf you run the above it will produce the following output\n\n```\nwriting hello\nwriting world\n(nothing happens for 1 sec)\nfinished\n```\n\n### concat\n\n##### `var concat = miss.concat(cb)`\n\nReturns a writable stream that concatenates all data written to the stream and calls a callback with the single result.\n\nCalling `miss.concat(cb)` returns a writable stream. `cb` is called when the writable stream is finished, e.g. when all data is done being written to it. `cb` is called with a single argument, `(data)`, which will contain the result of concatenating all the data written to the stream.\n\nNote that `miss.concat` will not handle stream errors for you. To handle errors, use `miss.pipe` or handle the `error` event manually.\n\n#### original module\n\n`miss.concat` is provided by [`require('concat-stream')`](https://www.npmjs.com/package/concat-stream)\n\n#### example\n\n```js\nvar fs = require('fs')\n\nvar readStream = fs.createReadStream('cat.png')\nvar concatStream = miss.concat(gotPicture)\n\nfunction callback (err) {\n  if (err) {\n    console.error(err)\n    process.exit(1)\n  }\n}\n\nmiss.pipe(readStream, concatStream, callback)\n\nfunction gotPicture(imageBuffer) {\n  // imageBuffer is all of `cat.png` as a node.js Buffer\n}\n\nfunction handleError(err) {\n  // handle your error appropriately here, e.g.:\n  console.error(err) // print the error to STDERR\n  process.exit(1) // exit program with non-zero exit code\n}\n```\n\n### finished\n\n##### `miss.finished(stream, cb)`\n\nWaits for `stream` to finish or error and then calls `cb` with `(err)`. `cb` will only be called once. `err` will be null if the stream finished without error, or else it will be populated with the error from the streams `error` event.\n\nThis function is useful for simplifying stream handling code as it lets you handle success or error conditions in a single code path. It's used internally `miss.pipe`.\n\n#### original module\n\n`miss.finished` is provided by [`require('end-of-stream')`](https://www.npmjs.com/package/end-of-stream)\n\n#### example\n\n```js\nvar copySource = fs.createReadStream('./movie.mp4')\nvar copyDest = fs.createWriteStream('./movie-copy.mp4')\n\ncopySource.pipe(copyDest)\n\nmiss.finished(copyDest, function(err) {\n  if (err) return console.log('write failed', err)\n  console.log('write success')\n})\n```\n\n### parallel\n\n##### `miss.parallel(concurrency, each)`\n\nThis works like `through` except you can process items in parallel, while still preserving the original input order.\n\nThis is handy if you wanna take advantage of node's async I/O and process streams of items in batches. With this module you can build your very own streaming parallel job queue.\n\nNote that `miss.parallel` preserves input ordering, if you don't need that then you can use [through2-concurrent](https://github.com/almost/through2-concurrent) instead, which is very similar to this otherwise.\n\n#### original module\n\n`miss.parallel` is provided by [`require('parallel-transform')`](https://npmjs.org/parallel-transform)\n\n#### example\n\nThis example fetches the GET HTTP headers for a stream of input URLs 5 at a time in parallel.\n\n```js\nfunction getResponse (item, cb) {\n  var r = request(item.url)\n  r.on('error', function (err) {\n    cb(err)\n  })\n  r.on('response', function (re) {\n    cb(null, {url: item.url, date: new Date(), status: re.statusCode, headers: re.headers})\n    r.abort()\n  })\n}\n\nmiss.pipe(\n  fs.createReadStream('./urls.txt'), // one url per line\n  split(),\n  miss.parallel(5, getResponse),\n  miss.through(function (row, enc, next) {\n    console.log(JSON.stringify(row))\n    next()\n  })\n)\n```\n\n## see also\n\n- [substack/stream-handbook](https://github.com/substack/stream-handbook)\n- [nodejs.org/api/stream.html](https://nodejs.org/api/stream.html)\n- [awesome-nodejs-streams](https://github.com/thejmazz/awesome-nodejs-streams)\n\n## license\n\nLicensed under the BSD 2-clause license.\n", "licenseText": "Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n\n1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.\n\n2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF ME<PERSON><PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON>U<PERSON><PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DAT<PERSON>, OR PROFITS; OR BUSINESS INTERRUPTION) <PERSON><PERSON><PERSON>VE<PERSON> CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/mississippi/-/mississippi-3.0.0.tgz#ea0a3291f97e0b5e8776b363d5f0a12d94c67022", "type": "tarball", "reference": "https://registry.yarnpkg.com/mississippi/-/mississippi-3.0.0.tgz", "hash": "ea0a3291f97e0b5e8776b363d5f0a12d94c67022", "integrity": "sha512-x471SsVjUtBRtcvd4BzKE9kFC+/2TeWgKCgw0bZcw1b9l2X3QX5vCWgF+KaZaYm87Ss//rHnWryupDrgLvmSkA==", "registry": "npm", "packageName": "mississippi", "cacheIntegrity": "sha512-x471SsVjUtBRtcvd4BzKE9kFC+/2TeWgKCgw0bZcw1b9l2X3QX5vCWgF+KaZaYm87Ss//rHnWryupDrgLvmSkA== sha1-6goykfl+C16HdrNj1fChLZTGcCI="}, "registry": "npm", "hash": "ea0a3291f97e0b5e8776b363d5f0a12d94c67022"}