{"manifest": {"name": "has", "description": "Object.prototype.hasOwnProperty.call shortcut", "version": "1.0.3", "homepage": "https://github.com/tarruda/has", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "repository": {"type": "git", "url": "git://github.com/tarruda/has.git"}, "bugs": {"url": "https://github.com/tarruda/has/issues"}, "license": "MIT", "licenses": [{"type": "MIT", "url": "https://github.com/tarruda/has/blob/master/LICENSE-MIT"}], "main": "./src", "dependencies": {"function-bind": "^1.1.1"}, "devDependencies": {"@ljharb/eslint-config": "^12.2.1", "eslint": "^4.19.1", "tape": "^4.9.0"}, "engines": {"node": ">= 0.4.0"}, "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "tape test"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-has-1.0.3-722d7cbfc1f6aa8241f16dd814e011e1f41e8796-integrity\\node_modules\\has\\package.json", "readmeFilename": "README.md", "readme": "# has\n\n> Object.prototype.hasOwnProperty.call shortcut\n\n## Installation\n\n```sh\nnpm install --save has\n```\n\n## Usage\n\n```js\nvar has = require('has');\n\nhas({}, 'hasOwnProperty'); // false\nhas(Object.prototype, 'hasOwnProperty'); // true\n```\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/has/-/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796", "type": "tarball", "reference": "https://registry.yarnpkg.com/has/-/has-1.0.3.tgz", "hash": "722d7cbfc1f6aa8241f16dd814e011e1f41e8796", "integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "registry": "npm", "packageName": "has", "cacheIntegrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw== sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y="}, "registry": "npm", "hash": "722d7cbfc1f6aa8241f16dd814e011e1f41e8796"}