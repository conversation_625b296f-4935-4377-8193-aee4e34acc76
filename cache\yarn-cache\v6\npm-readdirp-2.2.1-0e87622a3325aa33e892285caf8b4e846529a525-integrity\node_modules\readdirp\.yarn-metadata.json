{"manifest": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "thlorenz.com"}, "name": "readdirp", "description": "Recursive version of fs.readdir with streaming api.", "version": "2.2.1", "homepage": "https://github.com/paulmillr/readdirp", "repository": {"type": "git", "url": "git://github.com/paulmillr/readdirp.git"}, "engines": {"node": ">=0.10"}, "files": ["readdirp.js", "stream-api.js"], "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "main": "readdirp.js", "scripts": {"test-main": "(cd test && set -e; for t in ./*.js; do node $t; done)", "test-0.10": "nave use 0.10 npm run test-main", "test-0.12": "nave use 0.12 npm run test-main", "test-4": "nave use 4.4 npm run test-main", "test-6": "nave use 6.2 npm run test-main", "test-all": "npm run test-main && npm run test-0.10 && npm run test-0.12 && npm run test-4 && npm run test-6", "test": "npm run test-main"}, "dependencies": {"graceful-fs": "^4.1.11", "micromatch": "^3.1.10", "readable-stream": "^2.0.2"}, "devDependencies": {"nave": "^0.5.1", "proxyquire": "^1.7.9", "tap": "1.3.2", "through2": "^2.0.0"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-readdirp-2.2.1-0e87622a3325aa33e892285caf8b4e846529a525-integrity\\node_modules\\readdirp\\package.json", "readmeFilename": "README.md", "readme": "# readdirp [![Build Status](https://secure.travis-ci.org/thlorenz/readdirp.svg)](http://travis-ci.org/thlorenz/readdirp)\n\n[![NPM](https://nodei.co/npm/readdirp.png?downloads=true&stars=true)](https://nodei.co/npm/readdirp/)\n\nRecursive version of [fs.readdir](http://nodejs.org/docs/latest/api/fs.html#fs_fs_readdir_path_callback). Exposes a **stream api**.\n\n```javascript\nvar readdirp = require('readdirp')\n  , path = require('path')\n  , es = require('event-stream');\n\n// print out all JavaScript files along with their size\n\nvar stream = readdirp({ root: path.join(__dirname), fileFilter: '*.js' });\nstream\n  .on('warn', function (err) {\n    console.error('non-fatal error', err);\n    // optionally call stream.destroy() here in order to abort and cause 'close' to be emitted\n  })\n  .on('error', function (err) { console.error('fatal error', err); })\n  .pipe(es.mapSync(function (entry) {\n    return { path: entry.path, size: entry.stat.size };\n  }))\n  .pipe(es.stringify())\n  .pipe(process.stdout);\n```\n\nMeant to be one of the recursive versions of [fs](http://nodejs.org/docs/latest/api/fs.html) functions, e.g., like [mkdirp](https://github.com/substack/node-mkdirp).\n\n**Table of Contents**  *generated with [DocToc](http://doctoc.herokuapp.com/)*\n\n- [Installation](#installation)\n- [API](#api)\n\t- [entry stream](#entry-stream)\n\t- [options](#options)\n\t- [entry info](#entry-info)\n\t- [Filters](#filters)\n\t- [Callback API](#callback-api)\n\t\t- [allProcessed ](#allprocessed)\n\t\t- [fileProcessed](#fileprocessed)\n- [More Examples](#more-examples)\n\t- [stream api](#stream-api)\n\t- [stream api pipe](#stream-api-pipe)\n\t- [grep](#grep)\n\t- [using callback api](#using-callback-api)\n\t- [tests](#tests)\n\n\n# Installation\n\n    npm install readdirp\n\n# API\n\n***var entryStream = readdirp (options)***\n\nReads given root recursively and returns a `stream` of [entry info](#entry-info)s.\n\n## entry stream\n\nBehaves as follows:\n\n- `emit('data')` passes an [entry info](#entry-info) whenever one is found\n- `emit('warn')` passes a non-fatal `Error` that prevents a file/directory from being processed (i.e., if it is\n  inaccessible to the user)\n- `emit('error')` passes a fatal `Error` which also ends the stream (i.e., when illegal options where passed)\n- `emit('end')` called when all entries were found and no more will be emitted (i.e., we are done)\n- `emit('close')` called when the stream is destroyed via `stream.destroy()` (which could be useful if you want to\n  manually abort even on a non fatal error) - at that point the stream is no longer `readable` and no more entries,\n  warning or errors are emitted\n- to learn more about streams, consult the very detailed\n  [nodejs streams documentation](http://nodejs.org/api/stream.html) or the\n  [stream-handbook](https://github.com/substack/stream-handbook)\n\n\n## options\n\n- **root**: path in which to start reading and recursing into subdirectories\n\n- **fileFilter**: filter to include/exclude files found (see [Filters](#filters) for more)\n\n- **directoryFilter**: filter to include/exclude directories found and to recurse into (see [Filters](#filters) for more)\n\n- **depth**: depth at which to stop recursing even if more subdirectories are found\n\n- **entryType**: determines if data events on the stream should be emitted for `'files'`, `'directories'`, `'both'`, or `'all'`. Setting to `'all'` will also include entries for other types of file descriptors like character devices, unix sockets and named pipes. Defaults to `'files'`.\n\n- **lstat**: if `true`, readdirp uses `fs.lstat` instead of `fs.stat` in order to stat files and includes symlink entries in the stream along with files.\n\n## entry info\n\nHas the following properties:\n\n- **parentDir**     :  directory in which entry was found (relative to given root)\n- **fullParentDir** :  full path to parent directory\n- **name**          :  name of the file/directory\n- **path**          :  path to the file/directory (relative to given root)\n- **fullPath**      :  full path to the file/directory found\n- **stat**          :  built in [stat object](http://nodejs.org/docs/v0.4.9/api/fs.html#fs.Stats)\n- **Example**: (assuming root was `/User/dev/readdirp`)\n\n        parentDir     :  'test/bed/root_dir1',\n        fullParentDir :  '/User/dev/readdirp/test/bed/root_dir1',\n        name          :  'root_dir1_subdir1',\n        path          :  'test/bed/root_dir1/root_dir1_subdir1',\n        fullPath      :  '/User/dev/readdirp/test/bed/root_dir1/root_dir1_subdir1',\n        stat          :  [ ... ]\n\n## Filters\n\nThere are three different ways to specify filters for files and directories respectively.\n\n- **function**: a function that takes an entry info as a parameter and returns true to include or false to exclude the entry\n\n- **glob string**: a string (e.g., `*.js`) which is matched using [minimatch](https://github.com/isaacs/minimatch), so go there for more\n    information.\n\n    Globstars (`**`) are not supported since specifying a recursive pattern for an already recursive function doesn't make sense.\n\n    Negated globs (as explained in the minimatch documentation) are allowed, e.g., `!*.txt` matches everything but text files.\n\n- **array of glob strings**: either need to be all inclusive or all exclusive (negated) patterns otherwise an error is thrown.\n\n    `[ '*.json', '*.js' ]` includes all JavaScript and Json files.\n\n\n    `[ '!.git', '!node_modules' ]` includes all directories except the '.git' and 'node_modules'.\n\nDirectories that do not pass a filter will not be recursed into.\n\n## Callback API\n\nAlthough the stream api is recommended, readdirp also exposes a callback based api.\n\n***readdirp (options, callback1 [, callback2])***\n\nIf callback2 is given, callback1 functions as the **fileProcessed** callback, and callback2 as the **allProcessed** callback.\n\nIf only callback1 is given, it functions as the **allProcessed** callback.\n\n### allProcessed\n\n- function with err and res parameters, e.g., `function (err, res) { ... }`\n- **err**: array of errors that occurred during the operation, **res may still be present, even if errors occurred**\n- **res**: collection of file/directory [entry infos](#entry-info)\n\n### fileProcessed\n\n- function with [entry info](#entry-info) parameter e.g., `function (entryInfo) { ... }`\n\n\n# More Examples\n\n`on('error', ..)`, `on('warn', ..)` and `on('end', ..)` handling omitted for brevity\n\n```javascript\nvar readdirp = require('readdirp');\n\n// Glob file filter\nreaddirp({ root: './test/bed', fileFilter: '*.js' })\n  .on('data', function (entry) {\n    // do something with each JavaScript file entry\n  });\n\n// Combined glob file filters\nreaddirp({ root: './test/bed', fileFilter: [ '*.js', '*.json' ] })\n  .on('data', function (entry) {\n    // do something with each JavaScript and Json file entry\n  });\n\n// Combined negated directory filters\nreaddirp({ root: './test/bed', directoryFilter: [ '!.git', '!*modules' ] })\n  .on('data', function (entry) {\n    // do something with each file entry found outside '.git' or any modules directory\n  });\n\n// Function directory filter\nreaddirp({ root: './test/bed', directoryFilter: function (di) { return di.name.length === 9; } })\n  .on('data', function (entry) {\n    // do something with each file entry found inside directories whose name has length 9\n  });\n\n// Limiting depth\nreaddirp({ root: './test/bed', depth: 1 })\n  .on('data', function (entry) {\n    // do something with each file entry found up to 1 subdirectory deep\n  });\n\n// callback api\nreaddirp({ root: '.' }, function(fileInfo) {\n   // do something with file entry here\n  }, function (err, res) {\n    // all done, move on or do final step for all file entries here\n});\n```\n\nTry more examples by following [instructions](https://github.com/paulmillr/readdirp/blob/master/examples/Readme.md)\non how to get going.\n\n## tests\n\nThe [readdirp tests](https://github.com/paulmillr/readdirp/blob/master/test/readdirp.js) also will give you a good idea on\nhow things work.\n\n", "licenseText": "This software is released under the MIT license:\n\nCopyright (c) 2012-2015 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/readdirp/-/readdirp-2.2.1.tgz#0e87622a3325aa33e892285caf8b4e846529a525", "type": "tarball", "reference": "https://registry.yarnpkg.com/readdirp/-/readdirp-2.2.1.tgz", "hash": "0e87622a3325aa33e892285caf8b4e846529a525", "integrity": "sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ==", "registry": "npm", "packageName": "readdirp", "cacheIntegrity": "sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ== sha1-DodiKjMlqjPokihcr4tOhGUppSU="}, "registry": "npm", "hash": "0e87622a3325aa33e892285caf8b4e846529a525"}