{"manifest": {"name": "bluebird", "description": "Full featured Promises/A+ implementation with exceptionally good performance", "version": "3.7.2", "keywords": ["promise", "performance", "promises", "promises-a", "promises-aplus", "async", "await", "deferred", "deferreds", "future", "flow control", "dsl", "fluent interface"], "scripts": {"lint": "node scripts/jshint.js", "test": "node --expose-gc tools/test.js", "istanbul": "istanbul", "prepublish": "npm run generate-browser-core && npm run generate-browser-full", "generate-browser-full": "node tools/build.js --no-clean --no-debug --release --browser --minify", "generate-browser-core": "node tools/build.js --features=core --no-debug --release --zalgo --browser --minify && mv js/browser/bluebird.js js/browser/bluebird.core.js && mv js/browser/bluebird.min.js js/browser/bluebird.core.min.js"}, "homepage": "https://github.com/petkaantonov/bluebird", "repository": {"type": "git", "url": "git://github.com/petkaantonov/bluebird.git"}, "bugs": {"url": "http://github.com/petkaantonov/bluebird/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://github.com/petkaantonov/"}, "devDependencies": {"acorn": "^6.0.2", "acorn-walk": "^6.1.0", "baconjs": "^0.7.43", "bluebird": "^2.9.2", "body-parser": "^1.10.2", "browserify": "^8.1.1", "cli-table": "~0.3.1", "co": "^4.2.0", "cross-spawn": "^0.2.3", "glob": "^4.3.2", "grunt-saucelabs": "~8.4.1", "highland": "^2.3.0", "istanbul": "^0.3.5", "jshint": "^2.6.0", "jshint-stylish": "~0.2.0", "kefir": "^2.4.1", "mkdirp": "~0.5.0", "mocha": "~2.1", "open": "~0.0.5", "optimist": "~0.6.1", "rimraf": "~2.2.6", "rx": "^2.3.25", "serve-static": "^1.7.1", "sinon": "~1.7.3", "uglify-js": "~2.4.16"}, "readmeFilename": "README.md", "main": "./js/release/bluebird.js", "webpack": "./js/release/bluebird.js", "browser": "./js/browser/bluebird.js", "files": ["js/browser", "js/release", "LICENSE"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-bluebird-3.7.2-9f229c15be272454ffa973ace0dbee79a1b0c36f-integrity\\node_modules\\bluebird\\package.json", "readme": "<a href=\"http://promisesaplus.com/\">\n    <img src=\"http://promisesaplus.com/assets/logo-small.png\" alt=\"Promises/A+ logo\"\n         title=\"Promises/A+ 1.1 compliant\" align=\"right\" />\n</a>\n\n\n[![Build Status](https://travis-ci.org/petkaantonov/bluebird.svg?branch=master)](https://travis-ci.org/petkaantonov/bluebird)\n[![coverage-98%](https://img.shields.io/badge/coverage-98%25-brightgreen.svg?style=flat)](http://petkaantonov.github.io/bluebird/coverage/debug/index.html)\n\n**Got a question?** Join us on [stackoverflow](http://stackoverflow.com/questions/tagged/bluebird), the [mailing list](https://groups.google.com/forum/#!forum/bluebird-js) or chat on [IRC](https://webchat.freenode.net/?channels=#promises)\n\n# Introduction\n\nBluebird is a fully featured promise library with focus on innovative features and performance\n\nSee the [**bluebird website**](http://bluebirdjs.com/docs/getting-started.html) for further documentation, references and instructions. See the [**API reference**](http://bluebirdjs.com/docs/api-reference.html) here.\n\nFor bluebird 2.x documentation and files, see the [2.x tree](https://github.com/petkaantonov/bluebird/tree/2.x).\n\n### Note \n\nPromises in Node.js 10 are significantly faster than before. Bluebird still includes a lot of features like cancellation, iteration methods and warnings that native promises don't. If you are using Bluebird for performance rather than for those - please consider giving native promises a shot and running the benchmarks yourself.\n\n# Questions and issues\n\nThe [github issue tracker](https://github.com/petkaantonov/bluebird/issues) is **_only_** for bug reports and feature requests. Anything else, such as questions for help in using the library, should be posted in [StackOverflow](http://stackoverflow.com/questions/tagged/bluebird) under tags `promise` and `bluebird`.\n\n\n\n## Thanks\n\nThanks to BrowserStack for providing us with a free account which lets us support old browsers like IE8. \n\n# License\n\nThe MIT License (MIT)\n\nCopyright (c) 2013-2019 Petka Antonov\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2013-2018 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/bluebird/-/bluebird-3.7.2.tgz#9f229c15be272454ffa973ace0dbee79a1b0c36f", "type": "tarball", "reference": "https://registry.yarnpkg.com/bluebird/-/bluebird-3.7.2.tgz", "hash": "9f229c15be272454ffa973ace0dbee79a1b0c36f", "integrity": "sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==", "registry": "npm", "packageName": "bluebird", "cacheIntegrity": "sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg== sha1-nyKcFb4nJFT/qXOs4NvueaGww28="}, "registry": "npm", "hash": "9f229c15be272454ffa973ace0dbee79a1b0c36f"}