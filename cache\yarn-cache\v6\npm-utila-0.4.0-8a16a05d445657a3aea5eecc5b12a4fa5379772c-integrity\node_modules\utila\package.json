{"name": "utila", "version": "0.4.0", "description": "notareplacementforunderscore", "main": "lib/utila.js", "devDependencies": {"coffee-script": "~1.6.3", "little-popo": "~0.1"}, "scripts": {"prepublish": "coffee --bare --compile --output ./lib ./src"}, "repository": {"type": "git", "url": "https://github.com/AriaMinaei/utila.git"}, "keywords": ["utilities"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/AriaMinaei/utila/issues"}}