{"manifest": {"name": "map-cache", "description": "Basic cache object for storing key-value pairs.", "version": "0.2.2", "homepage": "https://github.com/jonschlinkert/map-cache", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/map-cache.git"}, "bugs": {"url": "https://github.com/jonschlinkert/map-cache/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.9", "should": "^8.3.1"}, "keywords": ["cache", "get", "has", "object", "set", "storage", "store"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["config-cache", "option-cache", "cache-base"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-map-cache-0.2.2-c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf-integrity\\node_modules\\map-cache\\package.json", "readmeFilename": "README.md", "readme": "# map-cache [![NPM version](https://img.shields.io/npm/v/map-cache.svg?style=flat)](https://www.npmjs.com/package/map-cache) [![NPM downloads](https://img.shields.io/npm/dm/map-cache.svg?style=flat)](https://npmjs.org/package/map-cache) [![Build Status](https://img.shields.io/travis/jonschlinkert/map-cache.svg?style=flat)](https://travis-ci.org/jonschlinkert/map-cache)\n\nBasic cache object for storing key-value pairs.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install map-cache --save\n```\n\nBased on MapCache in Lo-dash v3.0. [MIT License](https://github.com/lodash/lodash/blob/master/LICENSE.txt)\n\n## Usage\n\n```js\nvar MapCache = require('map-cache');\nvar mapCache = new MapCache();\n```\n\n## API\n\n### [MapCache](index.js#L28)\n\nCreates a cache object to store key/value pairs.\n\n**Example**\n\n```js\nvar cache = new MapCache();\n```\n\n### [.set](index.js#L45)\n\nAdds `value` to `key` on the cache.\n\n**Params**\n\n* `key` **{String}**: The key of the value to cache.\n* `value` **{any}**: The value to cache.\n* `returns` **{Object}**: Returns the `Cache` object for chaining.\n\n**Example**\n\n```js\ncache.set('foo', 'bar');\n```\n\n### [.get](index.js#L65)\n\nGets the cached value for `key`.\n\n**Params**\n\n* `key` **{String}**: The key of the value to get.\n* `returns` **{any}**: Returns the cached value.\n\n**Example**\n\n```js\ncache.get('foo');\n//=> 'bar'\n```\n\n### [.has](index.js#L82)\n\nChecks if a cached value for `key` exists.\n\n**Params**\n\n* `key` **{String}**: The key of the entry to check.\n* `returns` **{Boolean}**: Returns `true` if an entry for `key` exists, else `false`.\n\n**Example**\n\n```js\ncache.has('foo');\n//=> true\n```\n\n### [.del](index.js#L98)\n\nRemoves `key` and its value from the cache.\n\n**Params**\n\n* `key` **{String}**: The key of the value to remove.\n* `returns` **{Boolean}**: Returns `true` if the entry was removed successfully, else `false`.\n\n**Example**\n\n```js\ncache.del('foo');\n```\n\n## Related projects\n\nYou might also be interested in these projects:\n\n* [cache-base](https://www.npmjs.com/package/cache-base): Basic object cache with `get`, `set`, `del`, and `has` methods for node.js/javascript projects. | [homepage](https://github.com/jonschlinkert/cache-base)\n* [config-cache](https://www.npmjs.com/package/config-cache): General purpose JavaScript object storage methods. | [homepage](https://github.com/jonschlinkert/config-cache)\n* [option-cache](https://www.npmjs.com/package/option-cache): Simple API for managing options in JavaScript applications. | [homepage](https://github.com/jonschlinkert/option-cache)\n\n## Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/map-cache/issues/new).\n\n## Building docs\n\nGenerate readme and API documentation with [verb](https://github.com/verbose/verb):\n\n```sh\n$ npm install verb && npm run docs\n```\n\nOr, if [verb](https://github.com/verbose/verb) is installed globally:\n\n```sh\n$ verb\n```\n\n## Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm install -d && npm test\n```\n\n## Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n## License\n\nCopyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT license](https://github.com/jonschlinkert/map-cache/blob/master/LICENSE).\n\n***\n\n_This file was generated by [verb](https://github.com/verbose/verb), v0.9.0, on May 10, 2016._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015-2016, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/map-cache/-/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf", "type": "tarball", "reference": "https://registry.yarnpkg.com/map-cache/-/map-cache-0.2.2.tgz", "hash": "c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf", "integrity": "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=", "registry": "npm", "packageName": "map-cache", "cacheIntegrity": "sha512-8y/eV9QQZCiyn1SprXSrCmqJN0yNRATe+PO8ztwqrvrbdRLA3eYJF0yaR0YayLWkMbsQSKWS9N2gPcGEc4UsZg== sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8="}, "registry": "npm", "hash": "c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"}