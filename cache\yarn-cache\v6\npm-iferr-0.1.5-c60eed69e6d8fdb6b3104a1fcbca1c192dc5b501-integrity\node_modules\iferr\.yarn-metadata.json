{"manifest": {"name": "iferr", "version": "0.1.5", "description": "Higher-order functions for easier error handling", "main": "index.js", "scripts": {"test": "mocha", "prepublish": "coffee -c index.coffee"}, "repository": {"type": "git", "url": "https://github.com/shesek/iferr"}, "keywords": ["error", "errors"], "author": {"name": "Nadav I<PERSON>gi"}, "license": "MIT", "bugs": {"url": "https://github.com/shesek/iferr/issues"}, "homepage": "https://github.com/shesek/iferr", "devDependencies": {"coffee-script": "^1.7.1", "mocha": "^1.18.2"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-iferr-0.1.5-c60eed69e6d8fdb6b3104a1fcbca1c192dc5b501-integrity\\node_modules\\iferr\\package.json", "readmeFilename": "README.md", "readme": "# iferr\n\nHigher-order functions for easier error handling.\n\n`if (err) return cb(err);` be gone!\n\n## Install\n```bash\nnpm install iferr\n```\n\n## Use\n\n### JavaScript example\n```js\nvar iferr = require('iferr');\n\nfunction get_friends_count(id, cb) {\n  User.load_user(id, iferr(cb, function(user) {\n    user.load_friends(iferr(cb, function(friends) {\n      cb(null, friends.length);\n    }));\n  }));\n}\n```\n\n### CoffeeScript example\n```coffee\niferr = require 'iferr'\n\nget_friends_count = (id, cb) ->\n  User.load_user id, iferr cb, (user) ->\n    user.load_friends iferr cb, (friends) ->\n      cb null, friends.length\n```\n\n(TODO: document tiferr, throwerr and printerr)\n\n## License\nMIT\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 Nadav Ivgi\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/iferr/-/iferr-0.1.5.tgz#c60eed69e6d8fdb6b3104a1fcbca1c192dc5b501", "type": "tarball", "reference": "https://registry.yarnpkg.com/iferr/-/iferr-0.1.5.tgz", "hash": "c60eed69e6d8fdb6b3104a1fcbca1c192dc5b501", "integrity": "sha1-xg7taebY/bazEEofy8ocGS3FtQE=", "registry": "npm", "packageName": "iferr", "cacheIntegrity": "sha512-DUNFN5j7Tln0D+TxzloUjKB+CtVu6myn0JEFak6dG18mNt9YkQ6lzGCdafwofISZ1lLF3xRHJ98VKy9ynkcFaA== sha1-xg7taebY/bazEEofy8ocGS3FtQE="}, "registry": "npm", "hash": "c60eed69e6d8fdb6b3104a1fcbca1c192dc5b501"}