{"manifest": {"name": "is-binary-path", "version": "2.1.0", "description": "Check if a file path is a binary file", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/is-binary-path.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["binary", "extensions", "extension", "file", "path", "check", "detect", "is"], "dependencies": {"binary-extensions": "^2.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-binary-path-2.1.0-ea1f7f3b80f064236e83470f86c09c254fb45b09-integrity\\node_modules\\is-binary-path\\package.json", "readmeFilename": "readme.md", "readme": "# is-binary-path [![Build Status](https://travis-ci.org/sindresorhus/is-binary-path.svg?branch=master)](https://travis-ci.org/sindresorhus/is-binary-path)\n\n> Check if a file path is a binary file\n\n\n## Install\n\n```\n$ npm install is-binary-path\n```\n\n\n## Usage\n\n```js\nconst isBinaryPath = require('is-binary-path');\n\nisBinaryPath('source/unicorn.png');\n//=> true\n\nisBinaryPath('source/unicorn.txt');\n//=> false\n```\n\n\n## Related\n\n- [binary-extensions](https://github.com/sindresorhus/binary-extensions) - List of binary file extensions\n- [is-text-path](https://github.com/sindresorhus/is-text-path) - Check if a filepath is a text file\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com), [<PERSON>](https://paulmillr.com)\n", "licenseText": "MIT License\n\nCopyright (c) 2019 Sindre Sorhus <<EMAIL>> (https://sindresorhus.com), <PERSON> (https://paulmillr.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-binary-path/-/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-binary-path/-/is-binary-path-2.1.0.tgz", "hash": "ea1f7f3b80f064236e83470f86c09c254fb45b09", "integrity": "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==", "registry": "npm", "packageName": "is-binary-path", "cacheIntegrity": "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw== sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk="}, "registry": "npm", "hash": "ea1f7f3b80f064236e83470f86c09c254fb45b09"}