{"manifest": {"name": "object.getownpropertydescriptors", "version": "2.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ES2017 spec-compliant shim for `Object.getOwnPropertyDescriptors` that works in ES5.", "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "posttest": "npx aud --production", "tests-only": "nyc tape 'test/**/*.js'", "lint": "eslint .", "postlint": "es-shim-api --bound"}, "repository": {"type": "git", "url": "git://github.com/es-shims/object.getownpropertydescriptors.git"}, "keywords": ["Object.getOwnPropertyDescriptors", "descriptor", "property descriptor", "ES8", "ES2017", "shim", "polyfill", "getOwnPropertyDescriptor", "es-shim API"], "dependencies": {"call-bind": "^1.0.0", "define-properties": "^1.1.3", "es-abstract": "^1.18.0-next.1"}, "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^17.2.0", "aud": "^1.1.2", "eslint": "^7.8.1", "functions-have-names": "^1.2.1", "has-strict-mode": "^1.0.0", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.0.1"}, "testling": {"files": ["test/index.js", "test/shimmed.js"], "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/5.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/12.0..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.8"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-object-getownpropertydescriptors-2.1.1-0dfda8d108074d9c563e80490c883b6661091544-integrity\\node_modules\\object.getownpropertydescriptors\\package.json", "readmeFilename": "README.md", "readme": "# Object.getOwnPropertyDescriptors <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![Build Status][travis-svg]][travis-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nAn ES2017 spec-compliant shim for `Object.getOwnPropertyDescriptors` that works in ES5.\nInvoke its \"shim\" method to shim `Object.getOwnPropertyDescriptors` if it is unavailable, and if `Object.getOwnPropertyDescriptor` is available.\n\nThis package implements the [es-shim API](https://github.com/es-shims/api) interface. It works in an ES3-supported environment and complies with the [spec](https://github.com/tc39/ecma262/pull/582).\n\n## Example\n\n```js\nvar getDescriptors = require('object.getownpropertydescriptors');\nvar assert = require('assert');\nvar obj = { normal: Infinity };\nvar enumDescriptor = {\n\tenumerable: false,\n\twritable: false,\n\tconfigurable: true,\n\tvalue: true\n};\nvar writableDescriptor = {\n\tenumerable: true,\n\twritable: true,\n\tconfigurable: true,\n\tvalue: 42\n};\nvar symbol = Symbol();\nvar symDescriptor = {\n\tenumerable: true,\n\twritable: true,\n\tconfigurable: false,\n\tvalue: [symbol]\n};\n\nObject.defineProperty(obj, 'enumerable', enumDescriptor);\nObject.defineProperty(obj, 'writable', writableDescriptor);\nObject.defineProperty(obj, 'symbol', symDescriptor);\n\nvar descriptors = getDescriptors(obj);\n\nassert.deepEqual(descriptors, {\n\tnormal: {\n\t\tenumerable: true,\n\t\twritable: true,\n\t\tconfigurable: true,\n\t\tvalue: Infinity\n\t},\n\tenumerable: enumDescriptor,\n\twritable: writableDescriptor,\n\tsymbol: symDescriptor\n});\n```\n\n```js\nvar getDescriptors = require('object.getownpropertydescriptors');\nvar assert = require('assert');\n/* when Object.getOwnPropertyDescriptors is not present */\ndelete Object.getOwnPropertyDescriptors;\nvar shimmedDescriptors = getDescriptors.shim();\nassert.equal(shimmedDescriptors, getDescriptors);\nassert.deepEqual(shimmedDescriptors(obj), getDescriptors(obj));\n```\n\n```js\nvar getDescriptors = require('object.getownpropertydescriptors');\nvar assert = require('assert');\n/* when Object.getOwnPropertyDescriptors is present */\nvar shimmedDescriptors = getDescriptors.shim();\nassert.notEqual(shimmedDescriptors, getDescriptors);\nassert.deepEqual(shimmedDescriptors(obj), getDescriptors(obj));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/object.getownpropertydescriptors\n[npm-version-svg]: http://versionbadg.es/es-shims/object.getownpropertydescriptors.svg\n[travis-svg]: https://travis-ci.org/es-shims/Object.getOwnPropertyDescriptors.svg\n[travis-url]: https://travis-ci.org/es-shims/Object.getOwnPropertyDescriptors\n[deps-svg]: https://david-dm.org/es-shims/object.getownpropertydescriptors.svg\n[deps-url]: https://david-dm.org/es-shims/object.getownpropertydescriptors\n[dev-deps-svg]: https://david-dm.org/es-shims/object.getownpropertydescriptors/dev-status.svg\n[dev-deps-url]: https://david-dm.org/es-shims/object.getownpropertydescriptors#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/object.getownpropertydescriptors.png?downloads=true&stars=true\n[license-image]: http://img.shields.io/npm/l/object.getownpropertydescriptors.svg\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/object.getownpropertydescriptors.svg\n[downloads-url]: http://npm-stat.com/charts.html?package=object.getownpropertydescriptors\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015 Jordan Harband\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.1.tgz#0dfda8d108074d9c563e80490c883b6661091544", "type": "tarball", "reference": "https://registry.yarnpkg.com/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.1.tgz", "hash": "0dfda8d108074d9c563e80490c883b6661091544", "integrity": "sha512-6DtXgZ/lIZ9hqx4GtZETobXLR/ZLaa0aqV0kzbn80Rf8Z2e/XFnhA0I7p07N2wH8bBBltr2xQPi6sbKWAY2Eng==", "registry": "npm", "packageName": "object.getownpropertydescriptors", "cacheIntegrity": "sha512-6DtXgZ/lIZ9hqx4GtZETobXLR/ZLaa0aqV0kzbn80Rf8Z2e/XFnhA0I7p07N2wH8bBBltr2xQPi6sbKWAY2Eng== sha1-Df2o0QgHTZxWPoBJDIg7ZmEJFUQ="}, "registry": "npm", "hash": "0dfda8d108074d9c563e80490c883b6661091544"}