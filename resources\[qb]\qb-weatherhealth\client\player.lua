-- Player Health Management for Weather Health System

local QBCore = exports['qb-core']:GetCoreObject()

-- Player health variables
local playerHealthData = {
    currentDisease = nil,
    diseaseStartTime = 0,
    diseaseSeverity = 0,
    immunityLevel = 50,
    lastTreatment = 0,
    temperatureEffects = {},
    activeSymptoms = {}
}

-- Symptom loop tracking
local symptomLoops = {}
local healthEffectsActive = false

-- Initialize player health system
CreateThread(function()
    while not QBCore do
        Wait(100)
    end
    
    -- Wait for player to be loaded
    while not QBCore.Functions.GetPlayerData() do
        Wait(100)
    end
    
    StartPlayerHealthEffects()
    StartSymptomSystem()
end)

-- Start player health effects monitoring
function StartPlayerHealthEffects()
    CreateThread(function()
        while true do
            if playerHealthData.currentDisease then
                ApplyDiseaseEffects()
                CheckDiseaseProgression()
            end

            -- Check for weather-based effects (shivering, sweating)
            CheckColdWeatherEffects()

            Wait(30000) -- Check every 30 seconds
        end
    end)
end

-- Start symptom animation system
function StartSymptomSystem()
    CreateThread(function()
        while true do
            if playerHealthData.currentDisease then
                ProcessSymptoms()
            end
            
            Wait(60000) -- Check symptoms every minute
        end
    end)
end

-- Apply disease effects to player
function ApplyDiseaseEffects()
    if not playerHealthData.currentDisease then return end
    
    local disease = Config.HealthEffects.diseases[playerHealthData.currentDisease]
    if not disease then return end
    
    local ped = PlayerPedId()
    
    -- Apply movement speed effects
    if disease.effects.walkSpeed then
        SetPedMoveRateOverride(ped, disease.effects.walkSpeed)
    end
    
    if disease.effects.runSpeed then
        -- This affects stamina/running speed
        local stamina = GetPlayerStamina(PlayerId())
        SetPlayerStamina(PlayerId(), stamina * disease.effects.runSpeed)
    end
    
    -- Apply visual effects
    if disease.effects.blurredVision then
        SetTimecycleModifier("drug_flying_base")
        SetTimecycleModifierStrength(0.2 + (playerHealthData.diseaseSeverity * 0.1))
    end
    
    -- Apply thirst increase for heat stroke
    if disease.effects.thirstIncrease then
        local Player = QBCore.Functions.GetPlayerData()
        if Player and Player.metadata then
            local newThirst = math.max(0, Player.metadata.thirst - disease.effects.thirstIncrease)
            TriggerServerEvent('weatherhealth:server:updateThirst', newThirst)
        end
    end
    
    healthEffectsActive = true
end

-- Remove all disease effects
function RemoveAllDiseaseEffects()
    local ped = PlayerPedId()
    
    -- Reset movement speed
    SetPedMoveRateOverride(ped, 1.0)
    
    -- Clear visual effects
    ClearTimecycleModifier()
    
    -- Stop all symptom loops
    for symptom, _ in pairs(symptomLoops) do
        StopSymptomLoop(symptom)
    end
    
    healthEffectsActive = false
end

-- Check disease progression
function CheckDiseaseProgression()
    if not playerHealthData.currentDisease then return end

    local disease = Config.HealthEffects.diseases[playerHealthData.currentDisease]
    if not disease then return end

    local currentTime = GetGameTimer()
    local diseaseTime = currentTime - playerHealthData.diseaseStartTime

    -- Check if disease should be cured naturally
    if diseaseTime > disease.duration then
        CureDisease(playerHealthData.currentDisease)
        return
    end

    -- Increase severity over time
    local severityIncrease = math.floor(diseaseTime / 300000) -- Every 5 minutes
    playerHealthData.diseaseSeverity = math.min(5, severityIncrease)

    -- Apply health loss
    if diseaseTime % 60000 < 1000 then -- Every minute
        local healthLoss = disease.healthLoss + playerHealthData.diseaseSeverity
        TriggerServerEvent('weatherhealth:server:applyHealthLoss', healthLoss)
    end
end

-- Check for cold weather effects and trigger shivering
function CheckColdWeatherEffects()
    local ped = PlayerPedId()
    local currentWeather = exports['qb-weatherhealth']:getCurrentWeather()

    if not currentWeather then return end

    -- Skip if in interior
    if Utils.IsPlayerInInterior() then return end

    local temp = Config.WeatherTemperature[currentWeather] or 20
    local clothing = Utils.GetPlayerClothing(ped)
    local comfort = Utils.CalculateTemperatureComfort(currentWeather, clothing)

    -- Trigger shivering animation for cold weather
    if temp < 10 and (comfort == 'uncomfortable' or comfort == 'dangerous' or comfort == 'critical') then
        if math.random(1, 100) <= 30 then -- 30% chance every check
            exports['qb-weatherhealth']:playSymptomAnimation('shiver')
            Utils.Debug("Player shivering due to cold weather")
        end
    end

    -- Trigger sweating for hot weather
    if temp > 25 and (comfort == 'uncomfortable' or comfort == 'dangerous' or comfort == 'critical') then
        if math.random(1, 100) <= 25 then -- 25% chance every check
            exports['qb-weatherhealth']:playSymptomAnimation('wipe_sweat')
            Utils.Debug("Player sweating due to hot weather")
        end
    end
end

-- Process symptoms
function ProcessSymptoms()
    if not playerHealthData.currentDisease then return end
    
    local disease = Config.HealthEffects.diseases[playerHealthData.currentDisease]
    if not disease or not disease.symptoms then return end
    
    for _, symptom in ipairs(disease.symptoms) do
        local animConfig = Config.Animations.symptoms[symptom]
        if animConfig and Utils.ShouldPlayAnimation(animConfig.chance) then
            PlaySymptomAnimation(symptom)
            ShowSymptomMessage(symptom)
        end
    end
end

-- Play symptom animation
function PlaySymptomAnimation(symptom)
    local animConfig = Config.Animations.symptoms[symptom]
    if not animConfig then return end
    
    local ped = PlayerPedId()
    
    RequestAnimDict(animConfig.dict)
    while not HasAnimDictLoaded(animConfig.dict) do
        Wait(100)
    end
    
    -- Don't interrupt important actions
    if IsPedInAnyVehicle(ped, false) or IsPedRagdoll(ped) or IsPedFalling(ped) then
        return
    end
    
    TaskPlayAnim(ped, animConfig.dict, animConfig.anim, 8.0, -8.0, 
                animConfig.duration or 3000, 0, 0, false, false, false)
    
    RemoveAnimDict(animConfig.dict)
    
    Utils.Debug("Played symptom animation: %s", symptom)
end

-- Show symptom message
function ShowSymptomMessage(symptom)
    local message = Lang:t('symptoms.' .. symptom)
    if message then
        QBCore.Functions.Notify(message, 'error', 3000)
    end
end

-- Start symptom loop
function StartSymptomLoop(symptom)
    if symptomLoops[symptom] then return end
    
    symptomLoops[symptom] = CreateThread(function()
        while symptomLoops[symptom] do
            local animConfig = Config.Animations.symptoms[symptom]
            if animConfig and Utils.ShouldPlayAnimation(animConfig.chance) then
                PlaySymptomAnimation(symptom)
            end
            
            Wait(math.random(30000, 120000)) -- Random interval between 30s-2min
        end
    end)
    
    Utils.Debug("Started symptom loop: %s", symptom)
end

-- Stop symptom loop
function StopSymptomLoop(symptom)
    if symptomLoops[symptom] then
        symptomLoops[symptom] = nil
        Utils.Debug("Stopped symptom loop: %s", symptom)
    end
end

-- Contract disease
function ContractDisease(diseaseType, severity)
    if playerHealthData.currentDisease then
        -- Already sick, potentially make it worse
        if severity > playerHealthData.diseaseSeverity then
            playerHealthData.diseaseSeverity = severity
        end
        return
    end
    
    playerHealthData.currentDisease = diseaseType
    playerHealthData.diseaseStartTime = GetGameTimer()
    playerHealthData.diseaseSeverity = severity or 1
    
    local disease = Config.HealthEffects.diseases[diseaseType]
    if disease then
        -- Start symptom loops
        if disease.symptoms then
            for _, symptom in ipairs(disease.symptoms) do
                StartSymptomLoop(symptom)
            end
        end
        
        -- Apply immediate effects
        ApplyDiseaseEffects()
        
        Utils.Debug("Player contracted disease: %s (severity: %d)", diseaseType, severity)
    end
end

-- Cure disease
function CureDisease(diseaseType)
    if playerHealthData.currentDisease ~= diseaseType then return end
    
    playerHealthData.currentDisease = nil
    playerHealthData.diseaseStartTime = 0
    playerHealthData.diseaseSeverity = 0
    
    -- Remove all effects
    RemoveAllDiseaseEffects()
    
    -- Increase immunity slightly
    playerHealthData.immunityLevel = math.min(100, playerHealthData.immunityLevel + 5)
    
    Utils.Debug("Player cured of disease: %s", diseaseType)
end

-- Use treatment item
function UseTreatmentItem(itemName)
    local currentTime = GetGameTimer()
    
    -- Check cooldown
    if currentTime - playerHealthData.lastTreatment < 300000 then -- 5 minute cooldown
        QBCore.Functions.Notify(Lang:t('treatments.already_treated'), 'error')
        return false
    end
    
    local treatment = Config.TreatmentItems[itemName]
    if not treatment then
        QBCore.Functions.Notify(Lang:t('errors.invalid_item'), 'error')
        return false
    end
    
    -- Check if player has disease that can be treated
    if not playerHealthData.currentDisease then
        QBCore.Functions.Notify(Lang:t('treatments.no_disease'), 'primary')
        return false
    end
    
    -- Check if treatment is effective for current disease
    if treatment.diseases and not table.contains(treatment.diseases, playerHealthData.currentDisease) then
        QBCore.Functions.Notify(Lang:t('treatments.treatment_failed'), 'error')
        return false
    end
    
    -- Apply treatment
    local success = math.random(1, 100) <= (treatment.cureChance or 50)
    
    if success then
        CureDisease(playerHealthData.currentDisease)
        QBCore.Functions.Notify(Lang:t('treatments.treatment_successful'), 'success')
        TriggerServerEvent('weatherhealth:server:recordTreatment', itemName, playerHealthData.currentDisease, true)
    else
        QBCore.Functions.Notify(Lang:t('treatments.treatment_failed'), 'error')
        TriggerServerEvent('weatherhealth:server:recordTreatment', itemName, playerHealthData.currentDisease, false)
    end
    
    playerHealthData.lastTreatment = currentTime
    return success
end

-- Apply temperature bonus (heat/cool packs)
function ApplyTemperatureBonus(bonusType, value, duration)
    playerHealthData.temperatureEffects[bonusType] = {
        value = value,
        endTime = GetGameTimer() + duration
    }
    
    Utils.Debug("Applied temperature bonus: %s (%d for %dms)", bonusType, value, duration)
    
    -- Remove bonus after duration
    SetTimeout(duration, function()
        if playerHealthData.temperatureEffects[bonusType] then
            playerHealthData.temperatureEffects[bonusType] = nil
            Utils.Debug("Temperature bonus expired: %s", bonusType)
        end
    end)
end

-- Get current temperature bonus
function GetTemperatureBonus()
    local bonus = 0
    local currentTime = GetGameTimer()
    
    for bonusType, data in pairs(playerHealthData.temperatureEffects) do
        if currentTime < data.endTime then
            if bonusType == 'warmth' then
                bonus = bonus + data.value
            elseif bonusType == 'cool' then
                bonus = bonus - data.value
            end
        else
            playerHealthData.temperatureEffects[bonusType] = nil
        end
    end
    
    return bonus
end

-- Event handlers
RegisterNetEvent('weatherhealth:client:contractDisease', function(diseaseType, severity)
    ContractDisease(diseaseType, severity)
end)

RegisterNetEvent('weatherhealth:client:cureDisease', function(diseaseType)
    CureDisease(diseaseType)
end)

RegisterNetEvent('weatherhealth:client:useTreatment', function(itemName)
    UseTreatmentItem(itemName)
end)

RegisterNetEvent('weatherhealth:client:applyTemperatureBonus', function(bonusType, value, duration)
    ApplyTemperatureBonus(bonusType, value, duration)
end)

RegisterNetEvent('weatherhealth:client:startSymptomLoop', function(symptom)
    StartSymptomLoop(symptom)
end)

RegisterNetEvent('weatherhealth:client:stopSymptomLoop', function(symptom)
    StopSymptomLoop(symptom)
end)

RegisterNetEvent('weatherhealth:client:temporaryRelief', function(duration)
    -- Temporarily reduce disease effects
    if playerHealthData.currentDisease then
        local originalSeverity = playerHealthData.diseaseSeverity
        playerHealthData.diseaseSeverity = math.max(1, originalSeverity - 2)

        -- Restore after duration
        SetTimeout(duration, function()
            if playerHealthData.currentDisease then
                playerHealthData.diseaseSeverity = originalSeverity
            end
        end)

        Utils.Debug("Temporary relief applied for %d ms", duration)
    end
end)

RegisterNetEvent('weatherhealth:client:reduceSymptom', function(symptom, duration)
    -- Temporarily stop specific symptom
    if symptomLoops[symptom] then
        StopSymptomLoop(symptom)

        -- Restart after duration
        SetTimeout(duration, function()
            if playerHealthData.currentDisease then
                local disease = Config.HealthEffects.diseases[playerHealthData.currentDisease]
                if disease and disease.symptoms and table.contains(disease.symptoms, symptom) then
                    StartSymptomLoop(symptom)
                end
            end
        end)

        Utils.Debug("Symptom %s reduced for %d ms", symptom, duration)
    end
end)

RegisterNetEvent('weatherhealth:client:forceHealthCheck', function()
    CheckColdWeatherEffects()
    Utils.Debug("Forced health check triggered")
end)

-- Commands
RegisterCommand('healthstatus', function()
    local status = "Healthy"
    if playerHealthData.currentDisease then
        status = string.format("Sick: %s (Severity: %d)", 
                             playerHealthData.currentDisease, 
                             playerHealthData.diseaseSeverity)
    end
    
    QBCore.Functions.Notify(string.format("Health Status: %s | Immunity: %d", 
                           status, playerHealthData.immunityLevel), 'primary')
end)

-- Utility function
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

-- Export functions
exports('getPlayerHealthData', function()
    return playerHealthData
end)

exports('isPlayerSick', function()
    return playerHealthData.currentDisease ~= nil
end)

exports('getCurrentDisease', function()
    return playerHealthData.currentDisease
end)

exports('getTemperatureBonus', function()
    return GetTemperatureBonus()
end)
