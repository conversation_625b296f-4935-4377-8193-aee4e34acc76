{"manifest": {"name": "co", "version": "4.6.0", "description": "generator async control flow goodness", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "devDependencies": {"browserify": "^10.0.0", "istanbul-harmony": "0", "mocha": "^2.0.0", "mz": "^1.0.2"}, "scripts": {"test": "mocha --harmony", "test-cov": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --reporter dot", "test-travis": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --reporter dot", "prepublish": "npm run browserify", "browserify": "browserify index.js -o ./co-browser.js -s co"}, "files": ["index.js"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/co.git"}, "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-co-4.6.0-6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184-integrity\\node_modules\\co\\package.json", "readmeFilename": "Readme.md", "readme": "# co\n\n[![Gitter][gitter-image]][gitter-url]\n[![NPM version][npm-image]][npm-url]\n[![Build status][travis-image]][travis-url]\n[![Test coverage][coveralls-image]][coveralls-url]\n[![Downloads][downloads-image]][downloads-url]\n\n  Generator based control flow goodness for nodejs and the browser,\n  using promises, letting you write non-blocking code in a nice-ish way.\n\n## Co v4\n\n  `co@4.0.0` has been released, which now relies on promises.\n  It is a stepping stone towards [ES7 async/await](https://github.com/lukehoban/ecmascript-asyncawait).\n  The primary API change is how `co()` is invoked.\n  Before, `co` returned a \"thunk\", which you then called with a callback and optional arguments.\n  Now, `co()` returns a promise.\n\n```js\nco(function* () {\n  var result = yield Promise.resolve(true);\n  return result;\n}).then(function (value) {\n  console.log(value);\n}, function (err) {\n  console.error(err.stack);\n});\n```\n\n  If you want to convert a `co`-generator-function into a regular function that returns a promise,\n  you now use `co.wrap(fn*)`.\n\n```js\nvar fn = co.wrap(function* (val) {\n  return yield Promise.resolve(val);\n});\n\nfn(true).then(function (val) {\n\n});\n```\n\n## Platform Compatibility\n\n  `co@4+` requires a `Promise` implementation.\n  For versions of node `< 0.11` and for many older browsers,\n  you should/must include your own `Promise` polyfill.\n\n  When using node 0.11.x or greater, you must use the `--harmony-generators`\n  flag or just `--harmony` to get access to generators.\n\n  When using node 0.10.x and lower or browsers without generator support,\n  you must use [gnode](https://github.com/TooTallNate/gnode) and/or [regenerator](http://facebook.github.io/regenerator/).\n\n  io.js is supported out of the box, you can use `co` without flags or polyfills.\n\n## Installation\n\n```\n$ npm install co\n```\n\n## Associated libraries\n\nAny library that returns promises work well with `co`.\n\n- [mz](https://github.com/normalize/mz) - wrap all of node's code libraries as promises.\n\nView the [wiki](https://github.com/visionmedia/co/wiki) for more libraries.\n\n## Examples\n\n```js\nvar co = require('co');\n\nco(function *(){\n  // yield any promise\n  var result = yield Promise.resolve(true);\n}).catch(onerror);\n\nco(function *(){\n  // resolve multiple promises in parallel\n  var a = Promise.resolve(1);\n  var b = Promise.resolve(2);\n  var c = Promise.resolve(3);\n  var res = yield [a, b, c];\n  console.log(res);\n  // => [1, 2, 3]\n}).catch(onerror);\n\n// errors can be try/catched\nco(function *(){\n  try {\n    yield Promise.reject(new Error('boom'));\n  } catch (err) {\n    console.error(err.message); // \"boom\"\n }\n}).catch(onerror);\n\nfunction onerror(err) {\n  // log any uncaught errors\n  // co will not throw any errors you do not handle!!!\n  // HANDLE ALL YOUR ERRORS!!!\n  console.error(err.stack);\n}\n```\n\n## Yieldables\n\n  The `yieldable` objects currently supported are:\n\n  - promises\n  - thunks (functions)\n  - array (parallel execution)\n  - objects (parallel execution)\n  - generators (delegation)\n  - generator functions (delegation)\n\nNested `yieldable` objects are supported, meaning you can nest\npromises within objects within arrays, and so on!\n\n### Promises\n\n[Read more on promises!](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)\n\n### Thunks\n\nThunks are functions that only have a single argument, a callback.\nThunk support only remains for backwards compatibility and may\nbe removed in future versions of `co`.\n\n### Arrays\n\n`yield`ing an array will resolve all the `yieldables` in parallel.\n\n```js\nco(function* () {\n  var res = yield [\n    Promise.resolve(1),\n    Promise.resolve(2),\n    Promise.resolve(3),\n  ];\n  console.log(res); // => [1, 2, 3]\n}).catch(onerror);\n```\n\n### Objects\n\nJust like arrays, objects resolve all `yieldable`s in parallel.\n\n```js\nco(function* () {\n  var res = yield {\n    1: Promise.resolve(1),\n    2: Promise.resolve(2),\n  };\n  console.log(res); // => { 1: 1, 2: 2 }\n}).catch(onerror);\n```\n\n### Generators and Generator Functions\n\nAny generator or generator function you can pass into `co`\ncan be yielded as well. This should generally be avoided\nas we should be moving towards spec-compliant `Promise`s instead.\n\n## API\n\n### co(fn*).then( val => )\n\nReturns a promise that resolves a generator, generator function,\nor any function that returns a generator.\n\n```js\nco(function* () {\n  return yield Promise.resolve(true);\n}).then(function (val) {\n  console.log(val);\n}, function (err) {\n  console.error(err.stack);\n});\n```\n\n### var fn = co.wrap(fn*)\n\nConvert a generator into a regular function that returns a `Promise`.\n\n```js\nvar fn = co.wrap(function* (val) {\n  return yield Promise.resolve(val);\n});\n\nfn(true).then(function (val) {\n\n});\n```\n\n## License\n\n  MIT\n\n[npm-image]: https://img.shields.io/npm/v/co.svg?style=flat-square\n[npm-url]: https://npmjs.org/package/co\n[travis-image]: https://img.shields.io/travis/tj/co.svg?style=flat-square\n[travis-url]: https://travis-ci.org/tj/co\n[coveralls-image]: https://img.shields.io/coveralls/tj/co.svg?style=flat-square\n[coveralls-url]: https://coveralls.io/r/tj/co\n[downloads-image]: http://img.shields.io/npm/dm/co.svg?style=flat-square\n[downloads-url]: https://npmjs.org/package/co\n[gitter-image]: https://badges.gitter.im/Join%20Chat.svg\n[gitter-url]: https://gitter.im/tj/co?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge&utm_content=badge\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2014 TJ <PERSON> &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/co/-/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184", "type": "tarball", "reference": "https://registry.yarnpkg.com/co/-/co-4.6.0.tgz", "hash": "6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=", "registry": "npm", "packageName": "co", "cacheIntegrity": "sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ== sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="}, "registry": "npm", "hash": "6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"}