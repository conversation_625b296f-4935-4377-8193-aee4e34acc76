{"manifest": {"name": "is-negative-zero", "version": "2.0.1", "description": "Is this value negative zero? === will lie to you", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "posttest": "npx aud --production", "lint": "eslint .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-negative-zero.git"}, "bugs": {"url": "https://github.com/inspect-js/is-negative-zero/issues"}, "homepage": "https://github.com/inspect-js/is-negative-zero", "keywords": ["is", "negative", "zero", "negative zero", "number", "positive", "0", "-0"], "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^17.3.0", "aud": "^1.1.3", "auto-changelog": "^2.2.1", "eslint": "^7.14.0", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.0.1"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-negative-zero-2.0.1-3de746c18dda2319241a53675908d8f766f11c24-integrity\\node_modules\\is-negative-zero\\package.json", "readmeFilename": "README.md", "readme": "# is-negative-zero <sup>[![Version Badge][2]][1]</sup>\n\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\nIs this value negative zero? === will lie to you.\n\n## Example\n\n```js\nvar isNegativeZero = require('is-negative-zero');\nvar assert = require('assert');\n\nassert.notOk(isNegativeZero(undefined));\nassert.notOk(isNegativeZero(null));\nassert.notOk(isNegativeZero(false));\nassert.notOk(isNegativeZero(true));\nassert.notOk(isNegativeZero(0));\nassert.notOk(isNegativeZero(42));\nassert.notOk(isNegativeZero(Infinity));\nassert.notOk(isNegativeZero(-Infinity));\nassert.notOk(isNegativeZero(NaN));\nassert.notOk(isNegativeZero('foo'));\nassert.notOk(isNegativeZero(function () {}));\nassert.notOk(isNegativeZero([]));\nassert.notOk(isNegativeZero({}));\n\nassert.ok(isNegativeZero(-0));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[1]: https://npmjs.org/package/is-negative-zero\n[2]: http://versionbadg.es/inspect-js/is-negative-zero.svg\n[3]: https://travis-ci.org/inspect-js/is-negative-zero.svg\n[4]: https://travis-ci.org/inspect-js/is-negative-zero\n[5]: https://david-dm.org/inspect-js/is-negative-zero.svg\n[6]: https://david-dm.org/inspect-js/is-negative-zero\n[7]: https://david-dm.org/inspect-js/is-negative-zero/dev-status.svg\n[8]: https://david-dm.org/inspect-js/is-negative-zero#info=devDependencies\n[11]: https://nodei.co/npm/is-negative-zero.png?downloads=true&stars=true\n[license-image]: http://img.shields.io/npm/l/is-negative-zero.svg\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/is-negative-zero.svg\n[downloads-url]: http://npm-stat.com/charts.html?package=is-negative-zero\n\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 Jordan Harband\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-negative-zero/-/is-negative-zero-2.0.1.tgz#3de746c18dda2319241a53675908d8f766f11c24", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-negative-zero/-/is-negative-zero-2.0.1.tgz", "hash": "3de746c18dda2319241a53675908d8f766f11c24", "integrity": "sha512-2z6JzQvZRa9A2Y7xC6dQQm4FSTSTNWjKIYYTt4246eMTJmIo0Q+ZyOsU66X8lxK1AbB92dFeglPLrhwpeRKO6w==", "registry": "npm", "packageName": "is-negative-zero", "cacheIntegrity": "sha512-2z6JzQvZRa9A2Y7xC6dQQm4FSTSTNWjKIYYTt4246eMTJmIo0Q+ZyOsU66X8lxK1AbB92dFeglPLrhwpeRKO6w== sha1-PedGwY3aIxkkGlNnWQjY92bxHCQ="}, "registry": "npm", "hash": "3de746c18dda2319241a53675908d8f766f11c24"}