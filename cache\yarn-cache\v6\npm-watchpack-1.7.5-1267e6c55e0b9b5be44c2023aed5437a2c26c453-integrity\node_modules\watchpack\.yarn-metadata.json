{"manifest": {"name": "watchpack", "version": "1.7.5", "description": "Wrapper library for directory and file watching.", "main": "./lib/watchpack.js", "files": ["lib/"], "scripts": {"pretest": "npm run lint", "test": "mocha", "travis": "npm run cover -- --report lcovonly", "lint": "eslint lib", "precover": "npm run lint", "cover": "istanbul cover node_modules/mocha/bin/_mocha"}, "repository": {"type": "git", "url": "https://github.com/webpack/watchpack.git"}, "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "bugs": {"url": "https://github.com/webpack/watchpack/issues"}, "homepage": "https://github.com/webpack/watchpack", "devDependencies": {"codecov.io": "^0.1.6", "coveralls": "^3.0.0", "eslint": "^4.18.1", "istanbul": "^0.4.3", "mocha": "^5.0.1", "rimraf": "^2.6.2", "should": "^8.3.1"}, "optionalDependencies": {"chokidar": "^3.4.1", "watchpack-chokidar2": "^2.0.1"}, "dependencies": {"graceful-fs": "^4.1.2", "neo-async": "^2.5.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-watchpack-1.7.5-1267e6c55e0b9b5be44c2023aed5437a2c26c453-integrity\\node_modules\\watchpack\\package.json", "readmeFilename": "README.md", "readme": "# watchpack\n\nWrapper library for directory and file watching.\n\n[![Build Status](https://travis-ci.org/webpack/watchpack.svg?branch=master)](https://travis-ci.org/webpack/watchpack) [![Build status](https://ci.appveyor.com/api/projects/status/e5u2qvmugtv0r647/branch/master?svg=true)](https://ci.appveyor.com/project/sokra/watchpack/branch/master) [![Test coverage][coveralls-image]][coveralls-url]\n\n## Concept\n\nwatchpack high level API doesn't map directly to watchers. Instead a three level architecture ensures that for each directory only a single watcher exists.\n\n* The high level API requests `DirectoryWatchers` from a `WatcherManager`, which ensures that only a single `DirectoryWatcher` per directory is created.\n* A user-faced `Watcher` can be obtained from a `DirectoryWatcher` and provides a filtered view on the `DirectoryWatcher`.\n* Reference-counting is used on the `DirectoryWatcher` and `Watcher` to decide when to close them.\n* The real watchers (currently chokidar) are created by the `DirectoryWatcher`.\n* Files are never watched directly. This should keep the watcher count low.\n* Watching can be started in the past. This way watching can start after file reading.\n* Symlinks are not followed, instead the symlink is watched.\n\n## API\n\n``` javascript\nvar Watchpack = require(\"watchpack\");\n\nvar wp = new Watchpack({\n\t// options:\n\taggregateTimeout: 1000\n\t// fire \"aggregated\" event when after a change for 1000ms no additional change occurred\n\t// aggregated defaults to undefined, which doesn't fire an \"aggregated\" event\n\n\tpoll: true\n\t// poll: true - use polling with the default interval\n\t// poll: 10000 - use polling with an interval of 10s\n\t// poll defaults to undefined, which prefer native watching methods\n\t// Note: enable polling when watching on a network path\n\n\tignored: /node_modules/,\n\t// anymatch-compatible definition of files/paths to be ignored\n\t// see https://github.com/paulmillr/chokidar#path-filtering\n});\n\n// Watchpack.prototype.watch(string[] files, string[] directories, [number startTime])\nwp.watch(listOfFiles, listOfDirectories, Date.now() - 10000);\n// starts watching these files and directories\n// calling this again will override the files and directories\n\nwp.on(\"change\", function(filePath, mtime) {\n\t// filePath: the changed file\n\t// mtime: last modified time for the changed file\n});\n\nwp.on(\"aggregated\", function(changes) {\n\t// changes: an array of all changed files\n});\n\n// Watchpack.prototype.pause()\nwp.pause();\n// stops emitting events, but keeps watchers open\n// next \"watch\" call can reuse the watchers\n\n// Watchpack.prototype.close()\nwp.close();\n// stops emitting events and closes all watchers\n\n// Watchpack.prototype.getTimes()\nvar fileTimes = wp.getTimes();\n// returns an object with all know change times for files\n// this include timestamps from files not directly watched\n// key: absolute path, value: timestamp as number\n```\n\n[coveralls-url]: https://coveralls.io/r/webpack/watchpack/\n[coveralls-image]: https://img.shields.io/coveralls/webpack/watchpack.svg\n", "licenseText": "Copyright JS Foundation and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/watchpack/-/watchpack-1.7.5.tgz#1267e6c55e0b9b5be44c2023aed5437a2c26c453", "type": "tarball", "reference": "https://registry.yarnpkg.com/watchpack/-/watchpack-1.7.5.tgz", "hash": "1267e6c55e0b9b5be44c2023aed5437a2c26c453", "integrity": "sha512-9P3MWk6SrKjHsGkLT2KHXdQ/9SNkyoJbabxnKOoJepsvJjJG8uYTR3yTPxPQvNDI3w4Nz1xnE0TLHK4RIVe/MQ==", "registry": "npm", "packageName": "watchpack", "cacheIntegrity": "sha512-9P3MWk6SrKjHsGkLT2KHXdQ/9SNkyoJbabxnKOoJepsvJjJG8uYTR3yTPxPQvNDI3w4Nz1xnE0TLHK4RIVe/MQ== sha1-EmfmxV4Lm1vkTCAjrtVDeiwmxFM="}, "registry": "npm", "hash": "1267e6c55e0b9b5be44c2023aed5437a2c26c453"}