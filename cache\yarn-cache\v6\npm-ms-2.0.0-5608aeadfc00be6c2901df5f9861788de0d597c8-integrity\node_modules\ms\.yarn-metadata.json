{"manifest": {"name": "ms", "version": "2.0.0", "description": "Tiny milisecond conversion utility", "repository": {"type": "git", "url": "https://github.com/zeit/ms.git"}, "main": "./index", "files": ["index.js"], "scripts": {"precommit": "lint-staged", "lint": "eslint lib/* bin/*", "test": "mocha tests.js"}, "eslintConfig": {"extends": "eslint:recommended", "env": {"node": true, "es6": true}}, "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "license": "MIT", "devDependencies": {"eslint": "3.19.0", "expect.js": "0.3.1", "husky": "0.13.3", "lint-staged": "3.4.1", "mocha": "3.4.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-ms-2.0.0-5608aeadfc00be6c2901df5f9861788de0d597c8-integrity\\node_modules\\ms\\package.json", "readmeFilename": "readme.md", "readme": "# ms\n\n[![Build Status](https://travis-ci.org/zeit/ms.svg?branch=master)](https://travis-ci.org/zeit/ms)\n[![Slack Channel](http://zeit-slackin.now.sh/badge.svg)](https://zeit.chat/)\n\nUse this package to easily convert various time formats to milliseconds.\n\n## Examples\n\n```js\nms('2 days')  // 172800000\nms('1d')      // 86400000\nms('10h')     // 36000000\nms('2.5 hrs') // 9000000\nms('2h')      // 7200000\nms('1m')      // 60000\nms('5s')      // 5000\nms('1y')      // 31557600000\nms('100')     // 100\n```\n\n### Convert from milliseconds\n\n```js\nms(60000)             // \"1m\"\nms(2 * 60000)         // \"2m\"\nms(ms('10 hours'))    // \"10h\"\n```\n\n### Time format written-out\n\n```js\nms(60000, { long: true })             // \"1 minute\"\nms(2 * 60000, { long: true })         // \"2 minutes\"\nms(ms('10 hours'), { long: true })    // \"10 hours\"\n```\n\n## Features\n\n- Works both in [node](https://nodejs.org) and in the browser.\n- If a number is supplied to `ms`, a string with a unit is returned.\n- If a string that contains the number is supplied, it returns it as a number (e.g.: it returns `100` for `'100'`).\n- If you pass a string with a number and a valid unit, the number of equivalent ms is returned.\n\n## Caught a bug?\n\n1. [Fork](https://help.github.com/articles/fork-a-repo/) this repository to your own GitHub account and then [clone](https://help.github.com/articles/cloning-a-repository/) it to your local device\n2. Link the package to the global module directory: `npm link`\n3. Within the module you want to test your local development instance of ms, just link it to the dependencies: `npm link ms`. Instead of the default one from npm, node will now use your clone of ms!\n\nAs always, you can run the tests using: `npm test`\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016 Zeit, Inc.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/ms/-/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8", "type": "tarball", "reference": "https://registry.yarnpkg.com/ms/-/ms-2.0.0.tgz", "hash": "5608aeadfc00be6c2901df5f9861788de0d597c8", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "registry": "npm", "packageName": "ms", "cacheIntegrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A== sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "registry": "npm", "hash": "5608aeadfc00be6c2901df5f9861788de0d597c8"}