{"manifest": {"name": "typescript", "author": {"name": "Microsoft Corp."}, "homepage": "https://www.typescriptlang.org/", "version": "3.2.2", "license": "Apache-2.0", "description": "TypeScript is a language for application scale JavaScript development", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript"], "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "repository": {"type": "git", "url": "https://github.com/Microsoft/TypeScript.git"}, "main": "./lib/typescript.js", "typings": "./lib/typescript.d.ts", "bin": {"tsc": "bin\\tsc", "tsserver": "bin\\tsserver"}, "engines": {"node": ">=4.2.0"}, "devDependencies": {"@octokit/rest": "latest", "@types/browserify": "latest", "@types/chai": "latest", "@types/convert-source-map": "latest", "@types/del": "latest", "@types/glob": "latest", "@types/gulp": "3.X", "@types/gulp-concat": "latest", "@types/gulp-help": "latest", "@types/gulp-if": "0.0.33", "@types/gulp-newer": "latest", "@types/gulp-rename": "0.0.33", "@types/gulp-sourcemaps": "0.0.32", "@types/jake": "latest", "@types/merge2": "latest", "@types/minimatch": "latest", "@types/minimist": "latest", "@types/mkdirp": "latest", "@types/mocha": "latest", "@types/node": "8.5.5", "@types/q": "latest", "@types/run-sequence": "latest", "@types/source-map-support": "latest", "@types/through2": "latest", "@types/travis-fold": "latest", "@types/xml2js": "^0.4.0", "browser-resolve": "^1.11.2", "browserify": "latest", "chai": "latest", "chalk": "latest", "convert-source-map": "latest", "del": "latest", "fancy-log": "latest", "fs-extra": "^6.0.1", "gulp": "3.X", "gulp-clone": "latest", "gulp-concat": "latest", "gulp-help": "latest", "gulp-if": "latest", "gulp-insert": "latest", "gulp-newer": "latest", "gulp-rename": "latest", "gulp-sourcemaps": "latest", "gulp-typescript": "latest", "istanbul": "latest", "jake": "latest", "lodash": "4.17.10", "merge2": "latest", "minimist": "latest", "mkdirp": "latest", "mocha": "latest", "mocha-fivemat-progress-reporter": "latest", "plugin-error": "latest", "prex": "^0.4.3", "q": "latest", "remove-internal": "^2.9.2", "run-sequence": "latest", "source-map-support": "latest", "through2": "latest", "travis-fold": "latest", "tslint": "latest", "typescript": "next", "vinyl": "latest", "vinyl-sourcemaps-apply": "latest", "xml2js": "^0.4.19"}, "scripts": {"pretest": "jake tests", "test": "jake runtests-parallel light=false", "build": "npm run build:compiler && npm run build:tests", "build:compiler": "jake local", "build:tests": "jake tests", "start": "node lib/tsc", "clean": "jake clean", "gulp": "gulp", "jake": "jake", "lint": "jake lint", "setup-hooks": "node scripts/link-hooks.js"}, "browser": {"fs": false, "os": false, "path": false}, "dependencies": {}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-typescript-3.2.2-fe8101c46aa123f8353523ebdcf5730c2ae493e5-integrity\\node_modules\\typescript\\package.json", "readmeFilename": "README.md", "readme": "[![Build Status](https://travis-ci.org/Microsoft/TypeScript.svg?branch=master)](https://travis-ci.org/Microsoft/TypeScript)\n[![VSTS Build Status](https://typescript.visualstudio.com/_apis/public/build/definitions/cf7ac146-d525-443c-b23c-0d58337efebc/4/badge)](https://typescript.visualstudio.com/TypeScript/_build/latest?definitionId=4&view=logs) \n[![npm version](https://badge.fury.io/js/typescript.svg)](https://www.npmjs.com/package/typescript)\n[![Downloads](https://img.shields.io/npm/dm/typescript.svg)](https://www.npmjs.com/package/typescript)\n\n# TypeScript\n\n[![Join the chat at https://gitter.im/Microsoft/TypeScript](https://badges.gitter.im/Join%20Chat.svg)](https://gitter.im/Microsoft/TypeScript?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge&utm_content=badge)\n\n[TypeScript](https://www.typescriptlang.org/) is a language for application-scale JavaScript. TypeScript adds optional types to JavaScript that support tools for large-scale JavaScript applications for any browser, for any host, on any OS. TypeScript compiles to readable, standards-based JavaScript. Try it out at the [playground](https://www.typescriptlang.org/play/), and stay up to date via [our blog](https://blogs.msdn.microsoft.com/typescript) and [Twitter account](https://twitter.com/typescriptlang).\n\n## Installing\n\nFor the latest stable version:\n\n```bash\nnpm install -g typescript\n```\n\nFor our nightly builds:\n\n```bash\nnpm install -g typescript@next\n```\n\n## Contribute\n\nThere are many ways to [contribute](https://github.com/Microsoft/TypeScript/blob/master/CONTRIBUTING.md) to TypeScript.\n* [Submit bugs](https://github.com/Microsoft/TypeScript/issues) and help us verify fixes as they are checked in.\n* Review the [source code changes](https://github.com/Microsoft/TypeScript/pulls).\n* Engage with other TypeScript users and developers on [StackOverflow](https://stackoverflow.com/questions/tagged/typescript). \n* Join the [#typescript](https://twitter.com/search?q=%23TypeScript) discussion on Twitter.\n* [Contribute bug fixes](https://github.com/Microsoft/TypeScript/blob/master/CONTRIBUTING.md).\n* Read the language specification ([docx](https://github.com/Microsoft/TypeScript/blob/master/doc/TypeScript%20Language%20Specification.docx?raw=true),\n [pdf](https://github.com/Microsoft/TypeScript/blob/master/doc/TypeScript%20Language%20Specification.pdf?raw=true), [md](https://github.com/Microsoft/TypeScript/blob/master/doc/spec.md)).\n\nThis project has adopted the [Microsoft Open Source Code of Conduct](https://opensource.microsoft.com/codeofconduct/). For more information see \nthe [Code of Conduct FAQ](https://opensource.microsoft.com/codeofconduct/faq/) or contact [<EMAIL>](mailto:<EMAIL>) \nwith any additional questions or comments.\n\n## Documentation\n\n*  [Quick tutorial](https://www.typescriptlang.org/docs/tutorial.html)\n*  [Programming handbook](https://www.typescriptlang.org/docs/handbook/basic-types.html)\n*  [Language specification](https://github.com/Microsoft/TypeScript/blob/master/doc/spec.md)\n*  [Homepage](https://www.typescriptlang.org/)\n\n## Building\n\nIn order to build the TypeScript compiler, ensure that you have [Git](https://git-scm.com/downloads) and [Node.js](https://nodejs.org/) installed.\n\nClone a copy of the repo:\n\n```bash\ngit clone https://github.com/Microsoft/TypeScript.git\n```\n\nChange to the TypeScript directory:\n\n```bash\ncd TypeScript\n```\n\nInstall [Jake](http://jakejs.com/) tools and dev dependencies:\n\n```bash\nnpm install -g jake\nnpm install\n```\n\nUse one of the following to build and test:\n\n```\njake local            # Build the compiler into built/local \njake clean            # Delete the built compiler \njake LKG              # Replace the last known good with the built one.\n                      # Bootstrapping step to be executed when the built compiler reaches a stable state.\njake tests            # Build the test infrastructure using the built compiler. \njake runtests         # Run tests using the built compiler and test infrastructure. \n                      # You can override the host or specify a test for this command. \n                      # Use host=<hostName> or tests=<testPath>. \njake runtests-browser # Runs the tests using the built run.js file. Syntax is jake runtests. Optional\n                        parameters 'host=', 'tests=[regex], reporter=[list|spec|json|<more>]'.\njake baseline-accept  # This replaces the baseline test results with the results obtained from jake runtests.\njake lint             # Runs tslint on the TypeScript source.\njake help             # List the above commands. \n```\n\n\n## Usage\n\n```bash\nnode built/local/tsc.js hello.ts\n```\n\n\n## Roadmap\n\nFor details on our planned features and future direction please refer to our [roadmap](https://github.com/Microsoft/TypeScript/wiki/Roadmap).\n", "licenseText": "Apache License\n\nVersion 2.0, January 2004\n\nhttp://www.apache.org/licenses/ \n\nTERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n1. Definitions.\n\n\"License\" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.\n\n\"Licensor\" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.\n\n\"Legal Entity\" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, \"control\" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.\n\n\"You\" (or \"Your\") shall mean an individual or Legal Entity exercising permissions granted by this License.\n\n\"Source\" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.\n\n\"Object\" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.\n\n\"Work\" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).\n\n\"Derivative Works\" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.\n\n\"Contribution\" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, \"submitted\" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as \"Not a Contribution.\"\n\n\"Contributor\" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.\n\n2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.\n\n3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.\n\n4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:\n\nYou must give any other recipients of the Work or Derivative Works a copy of this License; and\n\nYou must cause any modified files to carry prominent notices stating that You changed the files; and\n\nYou must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and\n\nIf the Work includes a \"NOTICE\" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License. You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.\n\n5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.\n\n6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.\n\n7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.\n\n8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.\n\n9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability.\n\nEND OF TERMS AND CONDITIONS\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/typescript/-/typescript-3.2.2.tgz#fe8101c46aa123f8353523ebdcf5730c2ae493e5", "type": "tarball", "reference": "https://registry.yarnpkg.com/typescript/-/typescript-3.2.2.tgz", "hash": "fe8101c46aa123f8353523ebdcf5730c2ae493e5", "integrity": "sha512-VCj5UiSyHBjwfYacmDuc/NOk4QQixbE+Wn7MFJuS0nRuPQbof132Pw4u53dm264O8LPc2MVsc7RJNml5szurkg==", "registry": "npm", "packageName": "typescript", "cacheIntegrity": "sha512-VCj5UiSyHBjwfYacmDuc/NOk4QQixbE+Wn7MFJuS0nRuPQbof132Pw4u53dm264O8LPc2MVsc7RJNml5szurkg== sha1-/oEBxGqhI/g1NSPr3PVzDCrkk+U="}, "registry": "npm", "hash": "fe8101c46aa123f8353523ebdcf5730c2ae493e5"}