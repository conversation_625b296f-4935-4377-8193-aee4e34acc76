{"manifest": {"name": "encodeurl", "description": "Encode a URL to a percent-encoded form, excluding already-encoded sequences", "version": "1.0.2", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["encode", "encodeurl", "url"], "repository": {"type": "git", "url": "https://github.com/pillarjs/encodeurl.git"}, "devDependencies": {"eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-node": "5.2.1", "eslint-plugin-promise": "3.6.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "2.5.3"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-encodeurl-1.0.2-ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59-integrity\\node_modules\\encodeurl\\package.json", "readmeFilename": "README.md", "readme": "# encodeurl\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nEncode a URL to a percent-encoded form, excluding already-encoded sequences\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install encodeurl\n```\n\n## API\n\n```js\nvar encodeUrl = require('encodeurl')\n```\n\n### encodeUrl(url)\n\nEncode a URL to a percent-encoded form, excluding already-encoded sequences.\n\nThis function will take an already-encoded URL and encode all the non-URL\ncode points (as UTF-8 byte sequences). This function will not encode the\n\"%\" character unless it is not part of a valid sequence (`%20` will be\nleft as-is, but `%foo` will be encoded as `%25foo`).\n\nThis encode is meant to be \"safe\" and does not throw errors. It will try as\nhard as it can to properly encode the given URL, including replacing any raw,\nunpaired surrogate pairs with the Unicode replacement character prior to\nencoding.\n\nThis function is _similar_ to the intrinsic function `encodeURI`, except it\nwill not encode the `%` character if that is part of a valid sequence, will\nnot encode `[` and `]` (for IPv6 hostnames) and will replace raw, unpaired\nsurrogate pairs with the Unicode replacement character (instead of throwing).\n\n## Examples\n\n### Encode a URL containing user-controled data\n\n```js\nvar encodeUrl = require('encodeurl')\nvar escapeHtml = require('escape-html')\n\nhttp.createServer(function onRequest (req, res) {\n  // get encoded form of inbound url\n  var url = encodeUrl(req.url)\n\n  // create html message\n  var body = '<p>Location ' + escapeHtml(url) + ' not found</p>'\n\n  // send a 404\n  res.statusCode = 404\n  res.setHeader('Content-Type', 'text/html; charset=UTF-8')\n  res.setHeader('Content-Length', String(Buffer.byteLength(body, 'utf-8')))\n  res.end(body, 'utf-8')\n})\n```\n\n### Encode a URL for use in a header field\n\n```js\nvar encodeUrl = require('encodeurl')\nvar escapeHtml = require('escape-html')\nvar url = require('url')\n\nhttp.createServer(function onRequest (req, res) {\n  // parse inbound url\n  var href = url.parse(req)\n\n  // set new host for redirect\n  href.host = 'localhost'\n  href.protocol = 'https:'\n  href.slashes = true\n\n  // create location header\n  var location = encodeUrl(url.format(href))\n\n  // create html message\n  var body = '<p>Redirecting to new site: ' + escapeHtml(location) + '</p>'\n\n  // send a 301\n  res.statusCode = 301\n  res.setHeader('Content-Type', 'text/html; charset=UTF-8')\n  res.setHeader('Content-Length', String(Buffer.byteLength(body, 'utf-8')))\n  res.setHeader('Location', location)\n  res.end(body, 'utf-8')\n})\n```\n\n## Testing\n\n```sh\n$ npm test\n$ npm run lint\n```\n\n## References\n\n- [RFC 3986: Uniform Resource Identifier (URI): Generic Syntax][rfc-3986]\n- [WHATWG URL Living Standard][whatwg-url]\n\n[rfc-3986]: https://tools.ietf.org/html/rfc3986\n[whatwg-url]: https://url.spec.whatwg.org/\n\n## License\n\n[MIT](LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/encodeurl.svg\n[npm-url]: https://npmjs.org/package/encodeurl\n[node-version-image]: https://img.shields.io/node/v/encodeurl.svg\n[node-version-url]: https://nodejs.org/en/download\n[travis-image]: https://img.shields.io/travis/pillarjs/encodeurl.svg\n[travis-url]: https://travis-ci.org/pillarjs/encodeurl\n[coveralls-image]: https://img.shields.io/coveralls/pillarjs/encodeurl.svg\n[coveralls-url]: https://coveralls.io/r/pillarjs/encodeurl?branch=master\n[downloads-image]: https://img.shields.io/npm/dm/encodeurl.svg\n[downloads-url]: https://npmjs.org/package/encodeurl\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2016 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/encodeurl/-/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59", "type": "tarball", "reference": "https://registry.yarnpkg.com/encodeurl/-/encodeurl-1.0.2.tgz", "hash": "ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=", "registry": "npm", "packageName": "encodeurl", "cacheIntegrity": "sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w== sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="}, "registry": "npm", "hash": "ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"}