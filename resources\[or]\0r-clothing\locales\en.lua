local Translations = {
    store = {
        barber = "Barbershop",
        surgeon = "Plastic Surgeon",
        clothing = "Clothing store",
        tattoo = "Tattoo shop",
        outfitchanger = "Outfit Changer"
    },
    interaction = {
        get_stylish = "Get Stylish",
        barber_shop = "Barber Shop",
        tattoo_shop = "Tattoo Shop",
        edit_character = "Edit Character",
        surgeon = "Surgeon"
    },
    notifications = {
        clothes_paid = "Clothes paid.",
        not_enough_money = "You don't have enough money.",
        char_model_not_allowed = "Your character model isn't allowed to open a clothing store."
    },
    menu = {
        are_you_sure = "Are you sure?",
        confirm_character_creation = "This will confirm the character creation of your character.",
        finalize_character = "Finalize Character",
        discard = "Discard",
        cancel = "Cancel",
        character = "Character",
        creator = "Creator",
        character_creator_description = "A menu where you can build yourself easily and quick, enjoy!",
        click = "Click",
        male_peds = "Male Peds",
        female_peds = "Female Peds",
        custom_ped = "Ped Model",
        custom_ped_input = "Custom Ped Model - Press Enter",
        face_one = "Face One",
        skin_one = "Skin One",
        face_two = "Face Two",
        skin_two = "Skin Two",
        face_three = "Face Three",
        skin_three = "Skin Three",
        face_mix = "Face Mix",
        skin_mix = "Skin Mix",
        third_mix = "Third Mix",
        nose = "Nose",
        bone_height = "Bone Height",
        width = "Width",
        peak_length = "Peak Length",
        peak_height = "Peak Height",
        bone_twist = "Bone Twist",
        peak_lowering = "Peak Lowering",
        eyebrows = "Eyebrows",
        eyebrow_colors = "Eyebrow Color",
        eyebrow_depth = "Eyebrow Depth",
        eyebrow_height = "Eyebrow Height",
        cheeks = "Cheeks",
        bone_width = "Bone Width",
        jaw_bone = "Jaw Bone",
        bone_length = "Bone Length",
        chin = "Chin",
        chin_cleft = "Chin Cleft",
        misc_features = "Miscellaneous Features",
        lips_thickness = "Lips Thickness",
        eyes_squint = "Eyes Squint",
        neck_thickness = "Neck Thickness",
        eye_color = "Eye Color",
        blemishes = "Blemishes",
        opacity = "Opacity",
        ageing = "Ageing",
        complexion = "Complexion",
        sun_damage = "Sun Damage & Scars",
        moles_freckles = "Moles & Freckles",
        chest_hair = "Chest Hair",
        body_blemishes = "Body Blemishes",
        add_body_blemishes = "Add Body Blemishes",
        color = "Color",
        highlight_color = "Highlight Color",
        facial_hairs = "Facial Hairs",
        facial_hairs_colors = "Facial Color",
        hairs = "Hair",
        hair_texture = "Hair Texture",
        hair_fade = "Hair Fade",
        hairs_colors = "Hair Color",
        makeup = "Makeup",
        first_color = "First Color",
        makeup_color = "Makeup Color",
        blush = "Blush",
        second_color = "Second Color",
        lipstick = "Lipstick",
        jacket = "Jacket",
        undershirt = "Undershirt",
        arms_gloves = "Arms & Gloves",
        pants = "Pants",
        shoes = "Shoes",
        decals = "Decals",
        masks = "Masks",
        scarfs_necklaces = "Scarfs & Necklaces",
        vest = "Vest",
        bag = "Bag",
        hat = "Hat",
        glasses = "Glasses",
        earrings = "Earrings",
        watches = "Watches",
        bracelets = "Bracelets",
        finish = "Finish",
        back = "Back",
        next = "Next",
        tattoos = "Tattoos",
        payment = "Payment",
        amount = "Amount",
        vat_included = "VAT incl.",
        go_back = "Go Back",
        pay = "Pay",
        pay_via_bank = "Pay via Banking Card",
        pay_via_cash = "Pay via Cash",
        adjust_camera = "Adjust your camera",
        click_to_rotate = "Click + left/right to rotate",
        use_arrow_to_rotate = "Use arrow keys to rotate",
        click_to_adjust_height = "Click + up/down to adjust height",
        scroll_to_adjust_zoom = "Scroll to adjust zoom",
        none = "None",
        clothing = "Clothing",
        barber = "Barber",
        tattoo = "Tattoo",
        shop = "Shop",
        menu = "Menu",
        confirm_payment = "This will confirm the purchase of your clothing",
        cash = "Cash",
        bank = "Bank",
        head = "Head",
        rightleg = "Right Leg",
        leftleg = "Left Leg",
        rightarm = "Right Arm",
        leftarm = "Left Arm",
        torso = "Torso"
    }
}

Lang = Lang or Locale:new({
    phrases = Translations,
    warnOnMissing = true
})