{"manifest": {"name": "assert", "description": "The node.js assert module, re-packaged for web browsers.", "version": "1.5.0", "dependencies": {"object-assign": "^4.1.1", "util": "0.10.3"}, "devDependencies": {"mocha": "~1.21.4", "zuul": "~3.10.0", "zuul-ngrok": "^4.0.0"}, "homepage": "https://github.com/browserify/commonjs-assert", "keywords": ["assert", "browser"], "license": "MIT", "main": "./assert.js", "repository": {"type": "git", "url": "git://github.com/browserify/commonjs-assert.git"}, "scripts": {"browser-local": "zuul --no-coverage --local 8000 -- test.js", "test": "npm run test-node && npm run test-browser", "test-browser": "zuul -- test.js", "test-native": "TEST_NATIVE=true mocha --ui qunit test.js", "test-node": "mocha --ui qunit test.js"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-assert-1.5.0-55c109aaf6e0aefdb3dc4b71240c70bf574b18eb-integrity\\node_modules\\assert\\package.json", "readmeFilename": "README.md", "readme": "# assert\n\n[![Build Status](https://travis-ci.org/browserify/commonjs-assert.svg?branch=master)](https://travis-ci.org/browserify/commonjs-assert)\n\nThis module is used for writing unit tests for your applications, you can access it with `require('assert')`.\n\nIt aims to be fully compatibe with the [node.js assert module](http://nodejs.org/api/assert.html), same API and same behavior, just adding support for web browsers.\nThe API and code may contain traces of the [CommonJS Unit Testing 1.0 spec](http://wiki.commonjs.org/wiki/Unit_Testing/1.0) which they were based on, but both have evolved significantly since then.\n\nA `strict` and a `legacy` mode exist, while it is recommended to only use `strict mode`.\n\n## Strict mode\n\nWhen using the `strict mode`, any `assert` function will use the equality used in the strict function mode. So `assert.deepEqual()` will, for example, work the same as `assert.deepStrictEqual()`.\n\nIt can be accessed using:\n\n```js\nconst assert = require('assert').strict;\n```\n\n## Legacy mode\n\n> Deprecated: Use strict mode instead.\n\nWhen accessing `assert` directly instead of using the `strict` property, the\n[Abstract Equality Comparison](https://tc39.github.io/ecma262/#sec-abstract-equality-comparison) will be used for any function without a\n\"strict\" in its name (e.g. `assert.deepEqual()`).\n\nIt can be accessed using:\n\n```js\nconst assert = require('assert');\n```\n\nIt is recommended to use the `strict mode` instead as the Abstract Equality Comparison can often have surprising results. Especially\nin case of `assert.deepEqual()` as the used comparison rules there are very lax.\n\nE.g.\n\n```js\n// WARNING: This does not throw an AssertionError!\nassert.deepEqual(/a/gi, new Date());\n```\n\n\n## assert.fail(actual, expected, message, operator)\nThrows an exception that displays the values for actual and expected separated by the provided operator.\n\n## assert(value, message), assert.ok(value, [message])\nTests if value is truthy, it is equivalent to assert.equal(true, !!value, message);\n\n## assert.equal(actual, expected, [message])\nTests shallow, coercive equality with the equal comparison operator ( == ).\n\n## assert.notEqual(actual, expected, [message])\nTests shallow, coercive non-equality with the not equal comparison operator ( != ).\n\n## assert.deepEqual(actual, expected, [message])\nTests for deep equality.\n\n## assert.deepStrictEqual(actual, expected, [message])\nTests for deep equality, as determined by the strict equality operator ( === )\n\n## assert.notDeepEqual(actual, expected, [message])\nTests for any deep inequality.\n\n## assert.strictEqual(actual, expected, [message])\nTests strict equality, as determined by the strict equality operator ( === )\n\n## assert.notStrictEqual(actual, expected, [message])\nTests strict non-equality, as determined by the strict not equal operator ( !== )\n\n## assert.throws(block, [error], [message])\nExpects block to throw an error. error can be constructor, regexp or validation function.\n\nValidate instanceof using constructor:\n\n```javascript\nassert.throws(function() { throw new Error(\"Wrong value\"); }, Error);\n```\n\nValidate error message using RegExp:\n\n```javascript\nassert.throws(function() { throw new Error(\"Wrong value\"); }, /value/);\n```\n\nCustom error validation:\n\n```javascript\nassert.throws(function() {\n    throw new Error(\"Wrong value\");\n}, function(err) {\n    if ( (err instanceof Error) && /value/.test(err) ) {\n        return true;\n    }\n}, \"unexpected error\");\n```\n\n## assert.doesNotThrow(block, [message])\nExpects block not to throw an error, see assert.throws for details.\n\n## assert.ifError(value)\nTests if value is not a false value, throws if it is a true value. Useful when testing the first argument, error in callbacks.\n", "licenseText": "Copyright Joyent, Inc. and other Node contributors. All rights reserved.\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/assert/-/assert-1.5.0.tgz#55c109aaf6e0aefdb3dc4b71240c70bf574b18eb", "type": "tarball", "reference": "https://registry.yarnpkg.com/assert/-/assert-1.5.0.tgz", "hash": "55c109aaf6e0aefdb3dc4b71240c70bf574b18eb", "integrity": "sha512-EDsgawzwoun2CZkCgtxJbv392v4nbk9XDD06zI+kQYoBM/3RBWLlEyJARDOmhAAosBjWACEkKL6S+lIZtcAubA==", "registry": "npm", "packageName": "assert", "cacheIntegrity": "sha512-EDsgawzwoun2CZkCgtxJbv392v4nbk9XDD06zI+kQYoBM/3RBWLlEyJARDOmhAAosBjWACEkKL6S+lIZtcAubA== sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs="}, "registry": "npm", "hash": "55c109aaf6e0aefdb3dc4b71240c70bf574b18eb"}