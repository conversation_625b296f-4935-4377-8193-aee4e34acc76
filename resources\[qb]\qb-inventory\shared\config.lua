-- local QBCore = exports['qb-core']:GetCoreObject()


Config = {}

Config.OldInventory = "qb"

Config.ServerCallbacks = {}

Config.Admin  = {
    ["perms"] = {'superadmin', 'admin', 'mod'}
}


Config.UseTarget = true -- Use qb-target interactions (don't change this, go to your server.cfg and add `setr UseTarget true` to use this and just that from true to false or the other way around)

Config.Interaction = { -- realboss dont change
		UseTarget = true, -- realboss dont change
		Target = "qb-target", -- realboss dont change
}-- realboss dont change

Config.HealthOpenKey = 303 -- U key https://docs.fivem.net/docs/game-references/controls/

Config.RenderDistance = 20.0
Config.CatchRadius = 2.5
Config.CommandSpawning = false -- Set this to true if you want to be able to get throwables without using items.

Config.Table = {
    ["es_extended"] = {
        ["player"] = "users",
        ["inventory"] = "inventory",
        ["identifier"] = "identifier",
    },
    ["qb-core"] = {
        ["player"] = "players",
        ["inventory"] = "inventory",
        ["identifier"] = "citizenid",
    }
}

Config.WeaponsAmmo = {
    ["pistol_ammo"] = "weapon_pistol_magazine",
    ["rifle_ammo"] = "weapon_carbinerifle_magazine",
    ["smg_ammo"] = "weapon_smg_magazine",
    ["shotgun_ammo"] = "weapon_pumpshotgun_magazine",
    ["sniper_ammo"] = "weapon_sniperrifle_magazine",
    ["mg_ammo"] = "weapon_combatmg_magazine",
    ["ammo-9"] = "weapon_pistol_magazine",
    ["ammo-12"] = "weapon_pumpshotgun_magazine",
}

Config.WeaponsCount = {
    ["pistol_ammo"] = 12,
    ["rifle_ammo"] = 30,
    ["smg_ammo"] = 30,
    ["shotgun_ammo"] = 8,
    ["sniper_ammo"] = 10,
    ["mg_ammo"] = 50,
    ["ammo-9"] = 12,
    ["ammo-12"] = 8,
}

Config.MagazineMaxAmmo = {
    ["weapon_pistol_magazine"] = 60,
    ["weapon_carbinerifle_magazine"] = 120,
    ["weapon_smg_magazine"] = 120,
    ["weapon_pumpshotgun_magazine"] = 40,
    ["weapon_sniperrifle_magazine"] = 50,
    ["weapon_combatmg_magazine"] = 200,
}


Config.NewInventoryItems = {
    leftItems = { -- The order and item names of the items that will come to the inventory with the 5 slots on the left side
        ["1"] = "bread",
        ["2"] = "zart",
        ["3"] = "lockpick",
        ["4"] = "zort",
        ["5"] = "id-card"
    },
    
    -- rightItems tablosu
    rightItems = { -- The order and item names of the items that will come to the inventory with the 5 slots on the right side
        ["42"] = "ID Card",
        ["43"] = "Phone",
        ["44"] = "Key",
        ["45"] = "wallet",
        ["46"] = "Armor"
    },

    rightItemsLabel = {
        ["ID Card"] = "badge",
        ["Phone"] = "smartphone",
        ["Key"] = "key",
        ["wallet"] = "wallet",
        ["Armor"] = "backpack",
    },

    simCard = "Sim_card", -- The item label of the sim card item
    phoneItem = "phone" -- The item name of the phone item
}    

Config.MaxDistance = 5.0 -- determines how far you can place the item from the player

	
Config.ReviveEvent = 'hospital:client:Revive'


Config.MaxInventoryWeight = 120000                           -- Max weight a player can carry (default 120kg, written in grams)
Config.MaxInventorySlots = 53                              -- Max inventory slots for a player

Config.KeyBinds = {
    Inventory = 'TAB',
    HotBar = 'z'
}

Config.CleanupDropTime = 15 * 60                -- How many seconds it takes for drops to be untouched before being deleted
Config.MaxDropViewDistance = 12.5               -- The distance in GTA Units that a drop can be seen
Config.UseItemDrop = true                      -- This will enable item object to spawn on drops instead of markers
Config.ItemDropObject = `prop_paper_bag_01` -- if Config.UseItemDrop is true, this will be the prop that spawns for the item

Config.VendingObjects = {
    'prop_vend_soda_01',
    'prop_vend_soda_02',
    'prop_vend_water_01'
}

Config.BinObjects = {
    'prop_bin_05a',
}

Config.Filter = {
    ["boxs"] = {
        { name = "Bottle of Water" },
        { name = "box2" },
        { name = "box3" },
        { name = "box4" }
    },
    ["settings"] = {
        { name = "settings" },
        { name = "settings2" },
        { name = "settings3" },
        { name = "settings4" }
    },
    ["foods"] = {
        { name = "Bottle of Water" },
        { name = "bread" },
        { name = "food3" },
        { name = "food4" }
    },
    ["clothes"] = {
        { name = "tshirt" },
        { name = "tshirt2" },
        { name = "tshirt3" },
        { name = "tshirt4" }
    }
}

Config.CraftingObject = `prop_toolchest_05`

Config.VendingItem = {
    {
        name = 'kurkakola',
        price = 4,
        amount = 50,
        info = {},
        type = 'item',
        slot = 1,
    },
    {
        name = 'water_bottle',
        price = 4,
        amount = 50,
        info = {},
        type = 'item',
        slot = 2,
    },
}

-- See the vehicle class here: https://docs.fivem.net/natives/?_0x29439776AAA00A62
-- The template:
-- [vehicleClass] = {slots = [number], maxWeight = [number]}
Config.TrunkSpace = {
    ['default'] = { -- All the vehicle class that not listed here will use this as default
        slots = 35,
        maxWeight = 60000
    },
    [0] = { -- Compacts
        slots = 30,
        maxWeight = 38000
    },
    [1] = { -- Sedans
        slots = 40,
        maxWeight = 50000
    },
    [2] = { -- SUVs
        slots = 50,
        maxWeight = 75000
    },
    [3] = { -- Coupes
        slots = 35,
        maxWeight = 42000
    },
    [4] = { -- Muscle
        slots = 30,
        maxWeight = 38000
    },
    [5] = { -- Sports Classics
        slots = 25,
        maxWeight = 30000
    },
    [6] = { -- Sports
        slots = 25,
        maxWeight = 30000
    },
    [7] = { -- Super
        slots = 25,
        maxWeight = 30000
    },
    [8] = { -- Motorcycles
        slots = 15,
        maxWeight = 15000
    },
    [9] = { -- Off-road
        slots = 35,
        maxWeight = 60000
    },
    [12] = { -- Vans
        slots = 35,
        maxWeight = 120000
    },
    [13] = { -- Cycles
        slots = 0,
        maxWeight = 0
    },
    [14] = { -- Boats
        slots = 50,
        maxWeight = 120000
    },
    [15] = { -- Helicopters
        slots = 50,
        maxWeight = 120000
    },
    [16] = { -- Planes
        slots = 50,
        maxWeight = 120000
    },
}

Config.CraftingItems = {
    {
        name = 'lockpick',
        amount = 50,
        threshold = 0,
        points = 1,
        costs = {
            ['metalscrap'] = 22,
            ['plastic'] = 32,
        },
    },
    {
        name = 'screwdriverset',
        amount = 50,
        threshold = 0,
        points = 2,
        costs = {
            ['metalscrap'] = 30,
            ['plastic'] = 42,
        },
    },
    {
        name = 'electronickit',
        amount = 50,
        threshold = 0,
        points = 3,
        costs = {
            ['metalscrap'] = 30,
            ['plastic'] = 45,
            ['aluminum'] = 28,
        },
    },
    {
        name = 'radioscanner',
        amount = 50,
        threshold = 0,
        points = 4,
        costs = {
            ['electronickit'] = 2,
            ['plastic'] = 52,
            ['steel'] = 40,
        },
    },
    {
        name = 'gatecrack',
        amount = 50,
        threshold = 110,
        points = 5,
        costs = {
            ['metalscrap'] = 10,
            ['plastic'] = 50,
            ['aluminum'] = 30,
            ['iron'] = 17,
            ['electronickit'] = 2,
        },
    },
    {
        name = 'handcuffs',
        amount = 50,
        threshold = 160,
        points = 6,
        costs = {
            ['metalscrap'] = 36,
            ['steel'] = 24,
            ['aluminum'] = 28,
        },
    },
    {
        name = 'repairkit',
        amount = 50,
        threshold = 200,
        points = 7,
        costs = {
            ['metalscrap'] = 32,
            ['steel'] = 43,
            ['plastic'] = 61,
        },
    },
    {
        name = 'pistol_ammo',
        amount = 50,
        threshold = 250,
        points = 8,
        costs = {
            ['metalscrap'] = 50,
            ['steel'] = 37,
            ['copper'] = 26,
        },
    },
    {
        name = 'ironoxide',
        amount = 50,
        threshold = 300,
        points = 9,
        costs = {
            ['iron'] = 60,
            ['glass'] = 30,
        },
    },
    {
        name = 'aluminumoxide',
        amount = 50,
        threshold = 300,
        points = 10,
        costs = {
            ['aluminum'] = 60,
            ['glass'] = 30,
        },
    },
    {
        name = 'armor',
        amount = 50,
        threshold = 350,
        points = 11,
        costs = {
            ['iron'] = 33,
            ['steel'] = 44,
            ['plastic'] = 55,
            ['aluminum'] = 22,
        },
    },
    {
        name = 'drill',
        amount = 50,
        threshold = 1750,
        points = 12,
        costs = {
            ['iron'] = 50,
            ['steel'] = 50,
            ['screwdriverset'] = 3,
            ['advancedlockpick'] = 2,
        },
    },
}

Config.AttachmentCraftingLocation = vector3(88.91, 3743.88, 40.77)

Config.AttachmentCrafting = {
    {
        name = 'pistol_extendedclip',
        amount = 50,
        threshold = 0,
        points = 1,
        costs = {
            ['metalscrap'] = 140,
            ['steel'] = 250,
            ['rubber'] = 60,
        },
    },
    {
        name = 'pistol_suppressor',
        amount = 50,
        threshold = 10,
        points = 2,
        costs = {
            ['metalscrap'] = 165,
            ['steel'] = 285,
            ['rubber'] = 75,
        },
    },
    {
        name = 'smg_extendedclip',
        amount = 50,
        threshold = 25,
        points = 3,
        costs = {
            ['metalscrap'] = 190,
            ['steel'] = 305,
            ['rubber'] = 85,
        },
    },
    {
        name = 'microsmg_extendedclip',
        amount = 50,
        threshold = 50,
        points = 4,
        costs = {
            ['metalscrap'] = 205,
            ['steel'] = 340,
            ['rubber'] = 110,
        },
    },
    {
        name = 'smg_drum',
        amount = 50,
        threshold = 75,
        points = 5,
        costs = {
            ['metalscrap'] = 230,
            ['steel'] = 365,
            ['rubber'] = 130,
        },
    },
    {
        name = 'smg_scope',
        amount = 50,
        threshold = 100,
        points = 6,
        costs = {
            ['metalscrap'] = 255,
            ['steel'] = 390,
            ['rubber'] = 145,
        },
    },
    {
        name = 'assaultrifle_extendedclip',
        amount = 50,
        threshold = 150,
        points = 7,
        costs = {
            ['metalscrap'] = 270,
            ['steel'] = 435,
            ['rubber'] = 155,
            ['smg_extendedclip'] = 1,
        },
    },
    {
        name = 'assaultrifle_drum',
        amount = 50,
        threshold = 200,
        points = 8,
        costs = {
            ['metalscrap'] = 300,
            ['steel'] = 469,
            ['rubber'] = 170,
            ['smg_extendedclip'] = 2,
        },
    },
}

BackEngineVehicles = {
    [`ninef`] = true,
    [`adder`] = true,
    [`vagner`] = true,
    [`t20`] = true,
    [`infernus`] = true,
    [`zentorno`] = true,
    [`reaper`] = true,
    [`comet2`] = true,
    [`comet3`] = true,
    [`jester`] = true,
    [`jester2`] = true,
    [`cheetah`] = true,
    [`cheetah2`] = true,
    [`prototipo`] = true,
    [`turismor`] = true,
    [`pfister811`] = true,
    [`ardent`] = true,
    [`nero`] = true,
    [`nero2`] = true,
    [`tempesta`] = true,
    [`vacca`] = true,
    [`bullet`] = true,
    [`osiris`] = true,
    [`entityxf`] = true,
    [`turismo2`] = true,
    [`fmj`] = true,
    [`re7b`] = true,
    [`tyrus`] = true,
    [`italigtb`] = true,
    [`penetrator`] = true,
    [`monroe`] = true,
    [`ninef2`] = true,
    [`stingergt`] = true,
    [`surfer`] = true,
    [`surfer2`] = true,
    [`gp1`] = true,
    [`autarch`] = true,
    [`tyrant`] = true
}

Config.MaximumAmmoValues = {
    ['pistol'] = 250,
    ['smg'] = 250,
    ['shotgun'] = 200,
    ['rifle'] = 250,
    ['sniper'] = 100,
    ['mg'] = 500,
    ['ammo-9'] = 250,
    ['ammo-12'] = 200,
    
}


Config.WeaponsExtra = {
	-- // WEAPONS
	-- Melee
	[`weapon_unarmed`]               = { name = 'weapon_unarmed', label = 'Fists', weapontype = 'Melee', ammotype = nil, damagereason = 'Melee killed / Whacked / Executed / Beat down / Murdered / Battered' },
	[`weapon_dagger`]                = { name = 'weapon_dagger', label = 'Dagger', weapontype = 'Melee', ammotype = nil, damagereason = 'Knifed / Stabbed / Eviscerated' },
	[`weapon_bat`]                   = { name = 'weapon_bat', label = 'Bat', weapontype = 'Melee', ammotype = nil, damagereason = 'Melee killed / Whacked / Executed / Beat down / Murdered / Battered' },
	[`weapon_bottle`]                = { name = 'weapon_bottle', label = 'Broken Bottle', weapontype = 'Melee', ammotype = nil, damagereason = 'Knifed / Stabbed / Eviscerated' },
	[`weapon_crowbar`]               = { name = 'weapon_crowbar', label = 'Crowbar', weapontype = 'Melee', ammotype = nil, damagereason = 'Melee killed / Whacked / Executed / Beat down / Murdered / Battered' },
	[`weapon_flashlight`]            = { name = 'weapon_flashlight', label = 'Flashlight', weapontype = 'Melee', ammotype = nil, damagereason = 'Melee killed / Whacked / Executed / Beat down / Murdered / Battered' },
	[`weapon_golfclub`]              = { name = 'weapon_golfclub', label = 'Golfclub', weapontype = 'Melee', ammotype = nil, damagereason = 'Melee killed / Whacked / Executed / Beat down / Murdered / Battered' },
	[`weapon_hammer`]                = { name = 'weapon_hammer', label = 'Hammer', weapontype = 'Melee', ammotype = nil, damagereason = 'Melee killed / Whacked / Executed / Beat down / Murdered / Battered' },
	[`weapon_hatchet`]               = { name = 'weapon_hatchet', label = 'Hatchet', weapontype = 'Melee', ammotype = nil, damagereason = 'Knifed / Stabbed / Eviscerated' },
	[`weapon_knuckle`]               = { name = 'weapon_knuckle', label = 'Knuckle', weapontype = 'Melee', ammotype = nil, damagereason = 'Melee killed / Whacked / Executed / Beat down / Murdered / Battered' },
	[`weapon_knife`]                 = { name = 'weapon_knife', label = 'Knife', weapontype = 'Melee', ammotype = nil, damagereason = 'Knifed / Stabbed / Eviscerated' },
	[`weapon_machete`]               = { name = 'weapon_machete', label = 'Machete', weapontype = 'Melee', ammotype = nil, damagereason = 'Knifed / Stabbed / Eviscerated' },
	[`weapon_switchblade`]           = { name = 'weapon_switchblade', label = 'Switchblade', weapontype = 'Melee', ammotype = nil, damagereason = 'Knifed / Stabbed / Eviscerated' },
	[`weapon_nightstick`]            = { name = 'weapon_nightstick', label = 'Nightstick', weapontype = 'Melee', ammotype = nil, damagereason = 'Melee killed / Whacked / Executed / Beat down / Murdered / Battered' },
	[`weapon_wrench`]                = { name = 'weapon_wrench', label = 'Wrench', weapontype = 'Melee', ammotype = nil, damagereason = 'Melee killed / Whacked / Executed / Beat down / Murdered / Battered' },
	[`weapon_battleaxe`]             = { name = 'weapon_battleaxe', label = 'Battle Axe', weapontype = 'Melee', ammotype = nil, damagereason = 'Knifed / Stabbed / Eviscerated' },
	[`weapon_poolcue`]               = { name = 'weapon_poolcue', label = 'Poolcue', weapontype = 'Melee', ammotype = nil, damagereason = 'Melee killed / Whacked / Executed / Beat down / Murdered / Battered' },
	[`weapon_briefcase`]             = { name = 'weapon_briefcase', label = 'Briefcase', weapontype = 'Melee', ammotype = nil, damagereason = 'Melee killed / Whacked / Executed / Beat down / Murdered / Battered' },
	[`weapon_briefcase_02`]          = { name = 'weapon_briefcase_02', label = 'Briefcase', weapontype = 'Melee', ammotype = nil, damagereason = 'Melee killed / Whacked / Executed / Beat down / Murdered / Battered' },
	[`weapon_garbagebag`]            = { name = 'weapon_garbagebag', label = 'Garbage Bag', weapontype = 'Melee', ammotype = nil, damagereason = 'Melee killed / Whacked / Executed / Beat down / Murdered / Battered' },
	[`weapon_handcuffs`]             = { name = 'weapon_handcuffs', label = 'Handcuffs', weapontype = 'Melee', ammotype = nil, damagereason = 'Melee killed / Whacked / Executed / Beat down / Murdered / Battered' },
	[`weapon_bread`]                 = { name = 'weapon_bread', label = 'Baquette', weapontype = 'Melee', ammotype = nil, damagereason = 'Melee killed / Whacked / Executed / Beat down / Murdered / Battered' },
	[`weapon_stone_hatchet`]         = { name = 'weapon_stone_hatchet', label = 'Stone Hatchet', weapontype = 'Melee', ammotype = nil, damagereason = 'Knifed / Stabbed / Eviscerated' },
	[`weapon_candycane`]             = { name = 'weapon_candycane', label = 'Candy Cane', weapontype = 'Melee', ammotype = nil, damagereason = 'Melee Killed / Whacked / Executed / Beat down / Musrdered / Battered / Candy Caned' },

	-- Handguns
	[`weapon_pistol`]                = { name = 'weapon_pistol', label = 'Pistol', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plugged / Bust a cap in' },
	[`weapon_pistol_mk2`]            = { name = 'weapon_pistol_mk2', label = 'Pistol Mk2', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plugged / Bust a cap in' },
	[`weapon_combatpistol`]          = { name = 'weapon_combatpistol', label = 'Combat Pistol', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plugged / Bust a cap in' },
	[`weapon_appistol`]              = { name = 'weapon_appistol', label = 'AP Pistol', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plugged / Bust a cap in' },
	[`weapon_stungun`]               = { name = 'weapon_stungun', label = 'Taser', weapontype = 'Pistol', ammotype = 'AMMO_STUNGUN', damagereason = 'Died' },
	[`weapon_pistol50`]              = { name = 'weapon_pistol50', label = 'Pistol .50 Cal', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plugged / Bust a cap in' },
	[`weapon_snspistol`]             = { name = 'weapon_snspistol', label = 'SNS Pistol', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plugged / Bust a cap in' },
	[`weapon_snspistol_mk2`]         = { name = 'weapon_snspistol_mk2', label = 'SNS Pistol MK2', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plugged / Bust a cap in' },
	[`weapon_heavypistol`]           = { name = 'weapon_heavypistol', label = 'Heavy Pistol', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plugged / Bust a cap in' },
	[`weapon_vintagepistol`]         = { name = 'weapon_vintagepistol', label = 'Vintage Pistol', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plugged / Bust a cap in' },
	[`weapon_flaregun`]              = { name = 'weapon_flaregun', label = 'Flare Gun', weapontype = 'Pistol', ammotype = 'AMMO_FLARE', damagereason = 'Died' },
	[`weapon_marksmanpistol`]        = { name = 'weapon_marksmanpistol', label = 'Marksman Pistol', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plugged / Bust a cap in' },
	[`weapon_revolver`]              = { name = 'weapon_revolver', label = 'Revolver', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plugged / Bust a cap in' },
	[`weapon_revolver_mk2`]          = { name = 'weapon_revolver_mk2', label = 'Revolver MK2', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plugged / Bust a cap in' },
	[`weapon_doubleaction`]          = { name = 'weapon_doubleaction', label = 'Double Action Revolver', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plugged / Bust a cap in' },
	[`weapon_raypistol`]             = { name = 'weapon_raypistol', label = 'Ray Pistol', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plugged / Bust a cap in' },
	[`weapon_ceramicpistol`]         = { name = 'weapon_ceramicpistol', label = 'Ceramic Pistol', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plugged / Bust a cap in' },
	[`weapon_navyrevolver`]          = { name = 'weapon_navyrevolver', label = 'Navy Revolver', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plugged / Bust a cap in' },
	[`weapon_gadgetpistol`]          = { name = 'weapon_gadgetpistol', label = 'Gadget Pistol', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plugged / Bust a cap in' },
	[`weapon_stungun_mp`]            = { name = 'weapon_stungun_mp', label = 'Taser', weapontype = 'Pistol', ammotype = 'AMMO_STUNGUN', damagereason = 'Died' },
	[`weapon_pistolxm3`]             = { name = 'weapon_pistolxm3', label = 'Pistol XM3', weapontype = 'Pistol', ammotype = 'AMMO_PISTOL', damagereason = 'Pistoled / Blasted / Plaugged / Bust a cap in' },

	-- Submachine Guns
	[`weapon_microsmg`]              = { name = 'weapon_microsmg', label = 'Micro SMG', weapontype = 'Submachine Gun', ammotype = 'AMMO_SMG', damagereason = 'Riddled / Drilled / Finished / Submachine Gunned' },
	[`weapon_smg`]                   = { name = 'weapon_smg', label = 'SMG', weapontype = 'Submachine Gun', ammotype = 'AMMO_SMG', damagereason = 'Riddled / Drilled / Finished / Submachine Gunned' },
	[`weapon_smg_mk2`]               = { name = 'weapon_smg_mk2', label = 'SMG MK2', weapontype = 'Submachine Gun', ammotype = 'AMMO_SMG', damagereason = 'Riddled / Drilled / Finished / Submachine Gunned' },
	[`weapon_assaultsmg`]            = { name = 'weapon_assaultsmg', label = 'Assault SMG', weapontype = 'Submachine Gun', ammotype = 'AMMO_SMG', damagereason = 'Riddled / Drilled / Finished / Submachine Gunned' },
	[`weapon_combatpdw`]             = { name = 'weapon_combatpdw', label = 'Combat PDW', weapontype = 'Submachine Gun', ammotype = 'AMMO_SMG', damagereason = 'Riddled / Drilled / Finished / Submachine Gunned' },
	[`weapon_machinepistol`]         = { name = 'weapon_machinepistol', label = 'Tec-9', weapontype = 'Submachine Gun', ammotype = 'AMMO_PISTOL', damagereason = 'Riddled / Drilled / Finished / Submachine Gunned' },
	[`weapon_minismg`]               = { name = 'weapon_minismg', label = 'Mini SMG', weapontype = 'Submachine Gun', ammotype = 'AMMO_SMG', damagereason = 'Riddled / Drilled / Finished / Submachine Gunned' },
	[`weapon_raycarbine`]            = { name = 'weapon_raycarbine', label = 'Raycarbine', weapontype = 'Submachine Gun', ammotype = 'AMMO_SMG', damagereason = 'Riddled / Drilled / Finished / Submachine Gunned' },

	-- Shotguns
	[`weapon_pumpshotgun`]           = { name = 'weapon_pumpshotgun', label = 'Pump Shotgun', weapontype = 'Shotgun', ammotype = 'AMMO_SHOTGUN', damagereason = 'Devastated / Pulverized / Shotgunned' },
	[`weapon_pumpshotgun_mk2`]       = { name = 'weapon_pumpshotgun_mk2', label = 'Pump Shotgun MK2', weapontype = 'Shotgun', ammotype = 'AMMO_SHOTGUN', damagereason = 'Devastated / Pulverized / Shotgunned' },
	[`weapon_sawnoffshotgun`]        = { name = 'weapon_sawnoffshotgun', label = 'Sawn-off Shotgun', weapontype = 'Shotgun', ammotype = 'AMMO_SHOTGUN', damagereason = 'Devastated / Pulverized / Shotgunned' },
	[`weapon_assaultshotgun`]        = { name = 'weapon_assaultshotgun', label = 'Assault Shotgun', weapontype = 'Shotgun', ammotype = 'AMMO_SHOTGUN', damagereason = 'Devastated / Pulverized / Shotgunned' },
	[`weapon_bullpupshotgun`]        = { name = 'weapon_bullpupshotgun', label = 'Bullpup Shotgun', weapontype = 'Shotgun', ammotype = 'AMMO_SHOTGUN', damagereason = 'Devastated / Pulverized / Shotgunned' },
	[`weapon_musket`]                = { name = 'weapon_musket', label = 'Musket', weapontype = 'Shotgun', ammotype = 'AMMO_SHOTGUN', damagereason = 'Devastated / Pulverized / Shotgunned' },
	[`weapon_heavyshotgun`]          = { name = 'weapon_heavyshotgun', label = 'Heavy Shotgun', weapontype = 'Shotgun', ammotype = 'AMMO_SHOTGUN', damagereason = 'Devastated / Pulverized / Shotgunned' },
	[`weapon_dbshotgun`]             = { name = 'weapon_dbshotgun', label = 'Double-barrel Shotgun', weapontype = 'Shotgun', ammotype = 'AMMO_SHOTGUN', damagereason = 'Devastated / Pulverized / Shotgunned' },
	[`weapon_autoshotgun`]           = { name = 'weapon_autoshotgun', label = 'Auto Shotgun', weapontype = 'Shotgun', ammotype = 'AMMO_SHOTGUN', damagereason = 'Devastated / Pulverized / Shotgunned' },
	[`weapon_combatshotgun`]         = { name = 'weapon_combatshotgun', label = 'Combat Shotgun', weapontype = 'Shotgun', ammotype = 'AMMO_SHOTGUN', damagereason = 'Devastated / Pulverized / Shotgunned' },

	-- Assault Rifles
	[`weapon_assaultrifle`]          = { name = 'weapon_assaultrifle', label = 'Assault Rifle', weapontype = 'Assault Rifle', ammotype = 'AMMO_RIFLE', damagereason = 'Ended / Rifled / Shot down / Floored' },
	[`weapon_assaultrifle_mk2`]      = { name = 'weapon_assaultrifle_mk2', label = 'Assault Rifle MK2', weapontype = 'Assault Rifle', ammotype = 'AMMO_RIFLE', damagereason = 'Ended / Rifled / Shot down / Floored' },
	[`weapon_carbinerifle`]          = { name = 'weapon_carbinerifle', label = 'Carbine Rifle', weapontype = 'Assault Rifle', ammotype = 'AMMO_RIFLE', damagereason = 'Ended / Rifled / Shot down / Floored' },
	[`weapon_carbinerifle_mk2`]      = { name = 'weapon_carbinerifle_mk2', label = 'Carbine Rifle MK2', weapontype = 'Assault Rifle', ammotype = 'AMMO_RIFLE', damagereason = 'Ended / Rifled / Shot down / Floored' },
	[`weapon_advancedrifle`]         = { name = 'weapon_advancedrifle', label = 'Advanced Rifle', weapontype = 'Assault Rifle', ammotype = 'AMMO_RIFLE', damagereason = 'Ended / Rifled / Shot down / Floored' },
	[`weapon_specialcarbine`]        = { name = 'weapon_specialcarbine', label = 'Special Carbine', weapontype = 'Assault Rifle', ammotype = 'AMMO_RIFLE', damagereason = 'Ended / Rifled / Shot down / Floored' },
	[`weapon_specialcarbine_mk2`]    = { name = 'weapon_specialcarbine_mk2', label = 'Specialcarbine MK2', weapontype = 'Assault Rifle', ammotype = 'AMMO_RIFLE', damagereason = 'Ended / Rifled / Shot down / Floored' },
	[`weapon_bullpuprifle`]          = { name = 'weapon_bullpuprifle', label = 'Bullpup Rifle', weapontype = 'Assault Rifle', ammotype = 'AMMO_RIFLE', damagereason = 'Ended / Rifled / Shot down / Floored' },
	[`weapon_bullpuprifle_mk2`]      = { name = 'weapon_bullpuprifle_mk2', label = 'Bull Puprifle MK2', weapontype = 'Assault Rifle', ammotype = 'AMMO_RIFLE', damagereason = 'Ended / Rifled / Shot down / Floored' },
	[`weapon_compactrifle`]          = { name = 'weapon_compactrifle', label = 'Compact Rifle', weapontype = 'Assault Rifle', ammotype = 'AMMO_RIFLE', damagereason = 'Ended / Rifled / Shot down / Floored' },
	[`weapon_militaryrifle`]         = { name = 'weapon_militaryrifle', label = 'Military Rifle', weapontype = 'Assault Rifle', ammotype = 'AMMO_RIFLE', damagereason = 'Ended / Rifled / Shot down / Floored' },
	[`weapon_heavyrifle`]            = { name = 'weapon_heavyrifle', label = 'Heavy Rifle', weapontype = 'Assault Rifle', ammotype = 'AMMO_RIFLE', damagereason = 'Ended / Rifled / Shot down / Floored' },

	-- Light Machine Guns
	[`weapon_mg`]                    = { name = 'weapon_mg', label = 'Machinegun', weapontype = 'Light Machine Gun', ammotype = 'AMMO_MG', damagereason = 'Machine gunned / Sprayed / Ruined' },
	[`weapon_combatmg`]              = { name = 'weapon_combatmg', label = 'Combat MG', weapontype = 'Light Machine Gun', ammotype = 'AMMO_MG', damagereason = 'Machine gunned / Sprayed / Ruined' },
	[`weapon_combatmg_mk2`]          = { name = 'weapon_combatmg_mk2', label = 'Combat MG MK2', weapontype = 'Light Machine Gun', ammotype = 'AMMO_MG', damagereason = 'Machine gunned / Sprayed / Ruined' },
	[`weapon_gusenberg`]             = { name = 'weapon_gusenberg', label = 'Thompson SMG', weapontype = 'Light Machine Gun', ammotype = 'AMMO_MG', damagereason = 'Machine gunned / Sprayed / Ruined' },

	-- Sniper Rifles
	[`weapon_sniperrifle`]           = { name = 'weapon_sniperrifle', label = 'Sniper Rifle', weapontype = 'Sniper Rifle', ammotype = 'AMMO_SNIPER', damagereason = 'Sniped / Picked off / Scoped' },
	[`weapon_heavysniper`]           = { name = 'weapon_heavysniper', label = 'Heavy Sniper', weapontype = 'Sniper Rifle', ammotype = 'AMMO_SNIPER', damagereason = 'Sniped / Picked off / Scoped' },
	[`weapon_heavysniper_mk2`]       = { name = 'weapon_heavysniper_mk2', label = 'Heavysniper MK2', weapontype = 'Sniper Rifle', ammotype = 'AMMO_SNIPER', damagereason = 'Sniped / Picked off / Scoped' },
	[`weapon_marksmanrifle`]         = { name = 'weapon_marksmanrifle', label = 'Marksman Rifle', weapontype = 'Sniper Rifle', ammotype = 'AMMO_SNIPER', damagereason = 'Sniped / Picked off / Scoped' },
	[`weapon_marksmanrifle_mk2`]     = { name = 'weapon_marksmanrifle_mk2', label = 'Marksman Rifle MK2', weapontype = 'Sniper Rifle', ammotype = 'AMMO_SNIPER', damagereason = 'Sniped / Picked off / Scoped' },
	[`weapon_remotesniper`]          = { name = 'weapon_remotesniper', label = 'Remote Sniper', weapontype = 'Sniper Rifle', ammotype = 'AMMO_SNIPER_REMOTE', damagereason = 'Sniped / Picked off / Scoped' },

	-- Heavy Weapons
	[`weapon_rpg`]                   = { name = 'weapon_rpg', label = 'RPG', weapontype = 'Heavy Weapons', ammotype = 'AMMO_RPG', damagereason = 'Killed / Exploded / Obliterated / Destroyed / Erased / Annihilated' },
	[`weapon_grenadelauncher`]       = { name = 'weapon_grenadelauncher', label = 'Grenade Launcher', weapontype = 'Heavy Weapons', ammotype = 'AMMO_GRENADELAUNCHER', damagereason = 'Killed / Exploded / Obliterated / Destroyed / Erased / Annihilated' },
	[`weapon_grenadelauncher_smoke`] = { name = 'weapon_grenadelauncher_smoke', label = 'Smoke Grenade Launcher', weapontype = 'Heavy Weapons', ammotype = 'AMMO_GRENADELAUNCHER', damagereason = 'Killed / Exploded / Obliterated / Destroyed / Erased / Annihilated' },
	[`weapon_minigun`]               = { name = 'weapon_minigun', label = 'Minigun', weapontype = 'Heavy Weapons', ammotype = 'AMMO_MINIGUN', damagereason = 'Killed / Exploded / Obliterated / Destroyed / Erased / Annihilated' },
	[`weapon_firework`]              = { name = 'weapon_firework', label = 'Firework Launcher', weapontype = 'Heavy Weapons', ammotype = nil, damagereason = 'Killed / Exploded / Obliterated / Destroyed / Erased / Annihilated' },
	[`weapon_railgun`]               = { name = 'weapon_railgun', label = 'Railgun', weapontype = 'Heavy Weapons', ammotype = nil, damagereason = 'Killed / Exploded / Obliterated / Destroyed / Erased / Annihilated' },
	[`weapon_railgunxm3`]            = { name = 'weapon_railgunxm3', label = 'Railgun XM3', weapontype = 'Heavy Weapons', ammotype = nil, damagereason = 'Killed / Exploded / Obliterated / Destroyed / Erased / Annihilated' },
	[`weapon_hominglauncher`]        = { name = 'weapon_hominglauncher', label = 'Homing Launcher', weapontype = 'Heavy Weapons', ammotype = 'AMMO_STINGER', damagereason = 'Killed / Exploded / Obliterated / Destroyed / Erased / Annihilated' },
	[`weapon_compactlauncher`]       = { name = 'weapon_compactlauncher', label = 'Compact Launcher', weapontype = 'Heavy Weapons', ammotype = nil, damagereason = 'Killed / Exploded / Obliterated / Destroyed / Erased / Annihilated' },
	[`weapon_rayminigun`]            = { name = 'weapon_rayminigun', label = 'Ray Minigun', weapontype = 'Heavy Weapons', ammotype = 'AMMO_MINIGUN', damagereason = 'Killed / Exploded / Obliterated / Destroyed / Erased / Annihilated' },
	[`weapon_emplauncher`]           = { name = 'weapon_emplauncher', label = 'EMP Launcher', weapontype = 'Heavy Weapons', ammotype = 'AMMO_EMPLAUNCHER', damagereason = 'Died' },

	-- Throwables
	[`weapon_grenade`]               = { name = 'weapon_grenade', label = 'Grenade', weapontype = 'Throwable', ammotype = nil, damagereason = 'Bombed / Exploded / Detonated / Blew up' },
	[`weapon_bzgas`]                 = { name = 'weapon_bzgas', label = 'BZ Gas', weapontype = 'Throwable', ammotype = nil, damagereason = 'Died' },
	[`weapon_molotov`]               = { name = 'weapon_molotov', label = 'Molotov', weapontype = 'Throwable', ammotype = nil, damagereason = 'Torched / Flambeed / Barbecued' },
	[`weapon_stickybomb`]            = { name = 'weapon_stickybomb', label = 'C4', weapontype = 'Throwable', ammotype = nil, damagereason = 'Bombed / Exploded / Detonated / Blew up' },
	[`weapon_proxmine`]              = { name = 'weapon_proxmine', label = 'Proxmine Grenade', weapontype = 'Throwable', ammotype = nil, damagereason = 'Bombed / Exploded / Detonated / Blew up' },
	[`weapon_snowball`]              = { name = 'weapon_snowball', label = 'Snowball', weapontype = 'Throwable', ammotype = nil, damagereason = 'Died' },
	[`weapon_pipebomb`]              = { name = 'weapon_pipebomb', label = 'Pipe Bomb', weapontype = 'Throwable', ammotype = nil, damagereason = 'Bombed / Exploded / Detonated / Blew up' },
	[`weapon_ball`]                  = { name = 'weapon_ball', label = 'Ball', weapontype = 'Throwable', ammotype = 'AMMO_BALL', damagereason = 'Died' },
	[`weapon_smokegrenade`]          = { name = 'weapon_smokegrenade', label = 'Smoke Grenade', weapontype = 'Throwable', ammotype = nil, damagereason = 'Died' },
	[`weapon_flare`]                 = { name = 'weapon_flare', label = 'Flare pistol', weapontype = 'Throwable', ammotype = 'AMMO_FLARE', damagereason = 'Died' },

	-- Miscellaneous
	[`weapon_petrolcan`]             = { name = 'weapon_petrolcan', label = 'Petrol Can', weapontype = 'Miscellaneous', ammotype = 'AMMO_PETROLCAN', damagereason = 'Died' },
	[`gadget_parachute`]             = { name = 'gadget_parachute', label = 'Parachute', weapontype = 'Miscellaneous', ammotype = nil, damagereason = 'Died' },
	[`weapon_fireextinguisher`]      = { name = 'weapon_fireextinguisher', label = 'Fire Extinguisher', weapontype = 'Miscellaneous', ammotype = nil, damagereason = 'Died' },
	[`weapon_hazardcan`]             = { name = 'weapon_hazardcan', label = 'Hazardcan', weapontype = 'Miscellaneous', ammotype = 'AMMO_PETROLCAN', damagereason = 'Died' },
	[`weapon_fertilizercan`]         = { name = 'weapon_fertilizercan', label = 'Fertilizer Can', weapontype = 'Miscellaneous', ammotype = 'AMMO_FERTILIZERCAN', damagereason = 'Died' },
	[`weapon_barbed_wire`]           = { name = 'weapon_barbed_wire', label = 'Barbed Wire', weapontype = 'Miscellaneous', ammotype = nil, damagereason = 'Prodded' },
	[`weapon_drowning`]              = { name = 'weapon_drowning', label = 'Drowning', weapontype = 'Miscellaneous', ammotype = nil, damagereason = 'Died' },
	[`weapon_drowning_in_vehicle`]   = { name = 'weapon_drowning_in_vehicle', label = 'Drowning in a Vehicle', weapontype = 'Miscellaneous', ammotype = nil, damagereason = 'Died' },
	[`weapon_bleeding`]              = { name = 'weapon_bleeding', label = 'Bleeding', weapontype = 'Miscellaneous', ammotype = nil, damagereason = 'Bled out' },
	[`weapon_electric_fence`]        = { name = 'weapon_electric_fence', label = 'Electric Fence', weapontype = 'Miscellaneous', ammotype = nil, damagereason = 'Fried' },
	[`weapon_explosion`]             = { name = 'weapon_explosion', label = 'Explosion', weapontype = 'Miscellaneous', ammotype = nil, damagereason = 'Killed / Exploded / Obliterated / Destroyed / Erased / Annihilated' },
	[`weapon_fall`]                  = { name = 'weapon_fall', label = 'Fall', weapontype = 'Miscellaneous', ammotype = nil, damagereason = 'Committed suicide' },
	[`weapon_exhaustion`]            = { name = 'weapon_exhaustion', label = 'Exhaustion', weapontype = 'Miscellaneous', ammotype = nil, damagereason = 'Died' },
	[`weapon_hit_by_water_cannon`]   = { name = 'weapon_hit_by_water_cannon', label = 'Water Cannon', weapontype = 'Miscellaneous', ammotype = nil, damagereason = 'Died' },
	[`weapon_rammed_by_car`]         = { name = 'weapon_rammed_by_car', label = 'Rammed - Vehicle', weapontype = 'Miscellaneous', ammotype = nil, damagereason = 'Flattened / Ran over / Ran down' },
	[`weapon_run_over_by_car`]       = { name = 'weapon_run_over_by_car', label = 'Run Over - Vehicle', weapontype = 'Miscellaneous', ammotype = nil, damagereason = 'Flattened / Ran over / Ran down' },
	[`weapon_heli_crash`]            = { name = 'weapon_heli_crash', label = 'Heli Crash', weapontype = 'Miscellaneous', ammotype = nil, damagereason = 'Helicopter Crash' },
	[`weapon_fire`]                  = { name = 'weapon_fire', label = 'Fire', weapontype = 'Miscellaneous', ammotype = nil, damagereason = 'Torched / Flambeed / Barbecued' },

	-- Animals
	[`weapon_animal`]                = { name = 'weapon_animal', label = 'Animal', weapontype = 'Animals', ammotype = nil, damagereason = 'Mauled' },
	[`weapon_cougar`]                = { name = 'weapon_cougar', label = 'Cougar', weapontype = 'Animals', ammotype = nil, damagereason = 'Mauled' },
}
 


Config.PlayerBones = {
    ['RFoot'] = 52301,
    ['LFoot'] = 14201,
    ['RKnee'] = 36864,
    ['LKnee'] = 63931,
    ['Head'] = 31086,
    ['RArm'] = 28252,
    ['LArm'] = 61163,
    ['Chest'] = 24818,
}

Config.BoneGroups = {
    [52301] = {  -- RFoot
         20781,24806,52301
    },
    [14201] = {  -- LFoot
        2108,14201,35502,57717,65245
    },
    [36864] = {  -- RKnee
        16335,36864,51826
    },
    [63931] = {  -- LKnee
        23639,46078,58271,63931
    },
    [31086] = {  -- Head
        1356, 10706, 11174,12844,17188,17719,19336,20178,20279,20623,21550,25260,27474,29868,31086,35731,
        37193,39317,43536,45750,46240,47419,47495,49979,58331,61839,64729,65068
    },
    [28252] = {  -- RArm
        2992,6286,6442,26610,26611,26612,26613,26614,28252,28422,37119,
        40269,43810,57005, 
        58866,58867,58868,58869,58870,
        64016,64017,64064,64065,64080,64081,64096,64097,64112,64113
    },
    [61163] = {  -- LArm
        4089,4090,4137,4138,4153,4154,4169,4170,4185,4186,
        5232,18905,22711,36029,45509,60309,61007,61163
    },
    [24818] = {  -- Chest
        0, 11816,23553,24816,24817,24818,56604,57597
    }
}

Config.BoneLabelText = {
    [52301] = { name = "RFoot", Label = "Right Foot", unity = 'RFoot' },
    [14201] = { name = "LFoot", Label = "Left Foot", unity = 'LFoot' },
    [36864] = { name = "RKnee", Label = "Right Leg", unity = 'RKnee' },
    [63931] = { name = "LKnee", Label = "Left Leg", unity = 'LKnee' },
    [31086] = { name = "Head", Label = "Head" , unity = 'Head' },
    [28252] = { name = "RArm", Label = "Right Arm", unity = 'RArm' },
    [61163] = { name = "LArm", Label = "Left Arm", unity = 'LArm' },
    [24818] = { name = "Chest", Label = "Body", unity = 'Chest' },
}

Config.FirstData = {
    ["Chest"] = {
        bullets = 0,
        broken = false,
        bleeding = false,
        severity = 0,
        health = 100,
        damage = 5,
    },
    ["Head"] = {
        bullets = 0,
        broken = false,
        bleeding = false,
        severity = 0,
        health = 100,
        damage = 10,
    },
    ["LArm"] = {
        bullets = 0,
        broken = false,
        bleeding = false,
        severity = 0,
        health = 100,
        damage = 5,
    },
    ["RArm"] = {
        bullets = 0,
        broken = false,
        bleeding = false,
        severity = 0,
        health = 100,
        damage = 5,
    },
    ["LKnee"] = {
        bullets = 0,
        broken = false,
        bleeding = false,
        severity = 0,
        health = 100,
        damage = 5,
    },
    ["RKnee"] = {
        bullets = 0,
        broken = false,
        bleeding = false,
        severity = 0,
        health = 100,
        damage = 5,
    },
}
 
Config.GetAlternativeBone = function(bone) 

   for k,v in pairs(Config.PlayerBones) do 
	   if v == bone then 
		   return v
	   end
   end

   for k,v in pairs(Config.BoneGroups) do
		for kk,vv in pairs(v) do
			if vv == bone then
                
				return k
			end
		end
   end

 
   return false
end
	

Config.DropItems = {
    weed_skunk = "prop_stockade_wheel_flat",
    lockpick = "v_res_tre_plant",
    jerry_can = "prop_jerrycan_01a",
    weapon_dagger = "prop_w_me_dagger",
    weapon_bat = "p_cs_bbbat_01",
    weapon_bottle = "prop_w_me_bottle",
    weapon_crowbar = "prop_ing_crowbar",
    weapon_golfclub = "prop_golf_iron_01",
    weapon_hammer = "prop_tool_hammer",
    weapon_hatchet = "prop_w_me_hatchet",
    weapon_knife = "prop_cs_bowie_knife",
    weapon_machete = "prop_ld_w_me_machette",
    weapon_nightstick = "w_me_nightstick",
    weapon_wrench = "prop_tool_wrench",
    weapon_battleaxe = "prop_tool_fireaxe",
    weapon_poolcue = "prop_pool_cue",
    weapon_briefcase = "prop_security_case_01",
    weapon_briefcase_02 = "prop_security_case_01",
    weapon_garbagebag = "prop_rub_binbag_sd_01",
    weapon_handcuffs = "prop_cs_cuffs_01",
    weapon_bread = "v_ret_247_bread1",

    --handguns
    weapon_pistol = "w_pi_combatpistol_luxe",
    weapon_pistol_mk2 = "weapon_pistol_mk2",
    weapon_combatpistol = "w_pi_combatpistol",
    weapon_stungun = "w_pi_stungun",
    weapon_pistol50 = "w_pi_pistol50",
    weapon_snspistol = "w_pi_sns_pistol",
    weapon_heavypistol = "w_pi_heavypistol",
    weapon_vintagepistol = "w_pi_vintage_pistol",
    weapon_revolver = "w_pi_heavypistol",
    
    --submachine guns
    weapon_microsmg = "w_sb_microsmg",
    weapon_smg = "w_sb_smg",
    weapon_smg_mk2 = "w_sb_smg",
    weapon_assaultsmg = "w_sb_smg",
    weapon_combatpdw = "w_sb_smg",
    weapon_machinepistol = "w_pi_appistol",
    weapon_minismg = "w_sb_smg",
   
    --shotguns
    weapon_pumpshotgun = "w_sg_pumpshotgun",
    weapon_sawnoffshotgun = "w_sg_sawnoff",
    weapon_musket = "w_ar_musket",
    weapon_dbshotgun = "w_sg_pumpshotgun",

      -- AR
      weapon_assaultrifle = "w_ar_assaultrifle",
      weapon_assaultrifle_mk2 = "w_ar_assaultrifle",
      weapon_carbinerifle = "w_ar_carbinerifle",
      weapon_carbinerifle_mk2 = "w_ar_carbinerifle",
      weapon_advancedrifle = "w_ar_advancedrifle",
      weapon_specialcarbine = "w_ar_specialcarbine",
      weapon_bullpuprifle = "w_ar_bullpuprifle",


      --random weapon
      weapon_petrolcan = "prop_jerrycan_01a",


      -- Ammo
      pistol_ammo = "w_pi_pistol_mag1",
      rifle_ammo =    "w_ar_assaultrifle_mag1",
      smg_ammo =   "w_sb_assaultsmg_mag2",
      shotgun_ammo = "w_sg_assaultshotgun_mag1",
      mg_ammo =  "prop_box_ammo01a",
      snp_ammo  =  "prop_box_ammo01a",
      
      
      -- Pistol Attachments
      pistol_defaultclip = "w_pi_sns_pistol_mag1",
      pistol_extendedclip = "w_pi_sns_pistol_mag1",
    --   pistol_flashlight = "w_pi_sns_pistol_mag1",
    pistol_suppressor = "w_at_pi_supp",
    pistol_luxuryfinish = "prop_paints_can05",
    combatpistol_defaultclip = "w_pi_sns_pistol_mag1",
    combatpistol_extendedclip = "w_pi_sns_pistol_mag1",
    combatpistol_luxuryfinish = "prop_paints_can05",
    appistol_defaultclip = "w_pi_sns_pistol_mag1",
    appistol_extendedclip = "w_pi_sns_pistol_mag1",
    appistol_luxuryfinish = "prop_paints_can05",
    pistol50_defaultclip = "w_pi_sns_pistol_mag1",
    pistol50_extendedclip = "w_pi_sns_pistol_mag1",
    pistol50_luxuryfinish = "prop_paints_can05",
    revolver_defaultclip = "w_pi_sns_pistol_mag1",
    snspistol_defaultclip = "w_pi_sns_pistol_mag1",
    snspistol_extendedclip = "w_pi_sns_pistol_mag1",
    snspistol_grip = "w_at_ar_afgrip",
    heavypistol_defaultclip = "w_pi_sns_pistol_mag1",
    heavypistol_extendedclip = "w_pi_sns_pistol_mag1",
    heavypistol_grip = "w_at_ar_afgrip",
    vintagepistol_defaultclip = "w_pi_sns_pistol_mag1",
    vintagepistol_extendedclip = "w_pi_sns_pistol_mag1",

    --SMG attachments
    microsmg_defaultclip = "w_pi_sns_pistol_mag1",
    microsmg_extendedclip = "w_pi_sns_pistol_mag1",
    microsmg_scope = "w_at_scope_medium",
    microsmg_luxuryfinish = "prop_paints_can05",
    smg_defaultclip = "w_pi_sns_pistol_mag1",
    smg_extendedclip = "w_pi_sns_pistol_mag1",
    smg_suppressor = "w_pi_sns_pistol_mag1",
    smg_drum = "w_mg_mg_mag1",
    smg_scope = "w_at_scope_medium",
    smg_luxuryfinish = "prop_paints_can05",
    assaultsmg_defaultclip = "w_pi_sns_pistol_mag1",
    assaultsmg_extendedclip = "w_pi_sns_pistol_mag1",
    assaultsmg_luxuryfinish = "prop_paints_can05",
    machinepistol_defaultclip = "w_pi_sns_pistol_mag1",
    machinepistol_extendedclip = "w_pi_sns_pistol_mag1",
    machinepistol_drum = "w_mg_mg_mag1",
    combatpdw_defaultclip = "w_pi_sns_pistol_mag1",
    combatpdw_extendedclip = "w_pi_sns_pistol_mag1",
    combatpdw_drum = "w_mg_mg_mag1",
    combatpdw_grip = "w_at_ar_afgrip",
    combatpdw_scope = "w_at_scope_medium",

    -- RIFLE ATTACHMENTS
    assaultrifle_defaultclip = "w_ar_specialcarbine_mag2",
    assaultrifle_extendedclip = "w_ar_specialcarbine_mag2",
    assaultrifle_drum = "w_mg_mg_mag1",
    rifle_grip = "w_at_ar_afgrip",
    rifle_suppressor = "w_at_ar_supp",
    assaultrifle_luxuryfinish = "prop_paints_can05",
    carbinerifle_defaultclip = "w_ar_specialcarbine_mag2",
    carbinerifle_extendedclip = "w_ar_specialcarbine_mag2",
    carbinerifle_drum = "w_mg_mg_mag1",
    carbinerifle_scope = "w_at_scope_medium",
    carbinerifle_luxuryfinish = "prop_paints_can05",
    combatpdw_scope = "w_at_scope_medium",
    combatpdw_scope = "w_at_scope_medium",

      -- sniper
      weapon_sniperrifle = "w_sr_sniperrifle",
      weapon_heavysniper = "w_sr_heavysniper",
      weapon_marksmanrifle = "w_sr_marksmanrifle",
      weapon_heavysniper_mk2 = "w_sr_heavysniper",
      weapon_marksmanrifle_mk2 = "w_sr_marksmanrifle",
      
      -- Thorwable
      weapon_molotov = "w_ex_molotov",
      weapon_grenade = "w_ex_grenadefrag",
      weapon_smokegrenade = "w_ex_grenadesmoke",
      
      -- mat
      tosti = "prop_sandwich_01",
      twerks_candy = "ng_proc_candy01a",
      sandwich = "prop_sandwich_01",
      
      -- drikke
      water_bottle = "prop_ld_flow_bottle",
      coffee = "p_amb_coffeecup_01",
      kurkakola = "ng_proc_sodacan_01a",
      ecola = "ng_proc_sodacan_01a",
      ecola = "sprunk",
      
      -- alkohol
      beer = "prop_prop_beer_pissh",
      whiskey = "prop_bottle_richard",
      vodka = "prop_vodka_bottle",
      wine = "prop_wine_bot_01",
      grapejuice = "prop_food_bs_juice03",

      -- ID
      id_card = "p_ld_id_card_002",
      driver_license = "prop_cs_swipe_card",
      security_card_01 = "prop_cs_r_business_card",
      security_card_02 = "prop_cs_r_business_card",

      -- drugs
      weed_skunk = "prop_weed_bottle",
      cokebaggy = "prop_meth_bag_01",
      meth = "prop_meth_bag_01",
      joint = "p_amb_joint_01",
      cigar = "prop_sh_cigar_01",
      rolling_paper = "p_cs_papers_01",
      cigarette = "prop_cs_ciggy_01b",

      --materials
      plastic = "prop_cs_cardbox_01",
      metalscrap = "prop_cs_cardbox_01",
      copper = "prop_cs_cardbox_01",
      aluminum = "prop_cs_cardbox_01",
      aluminumoxide = "prop_cs_cardbox_01",
      iron = "prop_cs_cardbox_01",
      ironoxide = "prop_cs_cardbox_01",
      steel = "prop_cs_cardbox_01",
      rubber = "prop_cs_cardbox_01",
      glass = "prop_cs_cardbox_01",
      cloth = "prop_cs_cardbox_01",

      --tools
      lockpick = "prop_tool_screwdvr03",
      advancedlockpick = "prop_tool_screwdvr03",
      electronickit = "hei_prop_hst_usb_drive",
      gatecrack = "hei_prop_hst_usb_drive",
      thermite = "hei_prop_heist_thermite",
      trojan_usb = "hei_prop_hst_usb_drive",
      screwdriverset = "prop_tool_screwdvr03",
      drill = "prop_tool_drill",
      laserdrill = "hei_prop_heist_drill",


       --vehicle tools
       repairkit = "prop_tool_box_06",
       advancedrepairkit = "prop_tool_box_06",
       cleaningkit = "prop_rag_01",
       jerry_can = "w_am_jerrycan",
   
       -- Medication
       firstaid = "prop_cs_pills",
       bandage = "prop_cs_pills",
       ifaks = "prop_cs_pills",
       painkillers = "prop_cs_pills",
       walkstick = "prop_cs_walking_stick",

       -- Communication

       phone = "lb_phone_prop",
       radio = "prop_cs_walkie_talkie",
    
       -- Theft and Jewelry
       rolex = "p_watch_04",
       diamond_ring = "prop_jewel_03b",
       goldchain = "p_jewel_necklace01_s",
       goldbar = "hei_prop_heist_gold_bar",
       radio = "prop_cs_walkie_talkie",
       radio = "prop_cs_walkie_talkie",

       -- Cops Tools
       armor = "prop_bodyarmour_03",
       heavyarmor = "prop_bodyarmour_03",
       empty_evidence_bag = "prop_meth_bag_01",
       filled_evidence_bag = "prop_meth_bag_01",

       -- Other Tools
       stickynote = "p_notepad_01_s",
       parachute = "p_parachute_s",
       binoculars = "prop_binoc_01",
       lighter = "p_cs_lighter_01",
       markertesedler = "prop_anim_cash_pile_01",


       -- Ransutstyr
       juvelen_bakrom = "prop_cs_r_business_card",
       robbery_keycard_01 = "prop_cs_r_business_card",
       robbery_keycard_02 = "prop_cs_r_business_card",
       robbery_keycard_03 = "prop_cs_r_business_card",
       robbery_keycard_04 = "prop_cs_r_business_card",
       robbery_keycard_05 = "prop_cs_r_business_card",
       robbery_keycard_06 = "prop_cs_r_business_card",
       robbery_keycard_07 = "prop_cs_r_business_card",

       -- jaksam
       doors_lockpick = "prop_tool_screwdvr03",
       storbank_kort = "prop_cs_r_business_card",
       hvelv_kort = "prop_cs_r_business_card",
       eksplosiv = "prop_bomb_01",

       -- Random
       cups = "prop_plastic_cup_02",
       contract = "prop_cd_paper_pile3",
       flyid = "prop_cs_swipe_card",
       hacker_phone = "lb_phone_prop",
       dogfood = "prop_paper_bag_01",
       catfood = "prop_paper_bag_01",
       chickenfood = "prop_paper_bag_01",
       rentalpapers = "prop_cd_paper_pile3",
       boombox = "prop_boombox_01",
       security_card_03 = "prop_cs_r_business_card",
       security_card_04 = "prop_cs_r_business_card",
       spray = "prop_spraygun_01",
       spray = "prop_rag_01",

       --kq
       kq_outfitbag = "prop_cs_heist_bag_01",

       towing_rope = "prop_trevor_rope_01",

      -- sykehus
      crutch = "prop_cs_walking_stick",
      wheelchair = "prop_wheelchair_01",

      -- houserobbery
      powder = "prop_meth_bag_01",
      watch = "p_watch_04",
      toothpaste = "prop_toothpaste_01",
      soap = "prop_toilet_soap_01",
      shampoo = "prop_toilet_shamp_01",
      romantic_book = "prop_cs_stock_book",
      pencil = "prop_pencil_01",
      notepad = "p_notepad_01_s",
      necklace = "p_jewel_necklace01_s",
      house_locator = "hei_prop_hst_usb_drive",
      gold_watch = "p_watch_06",
      gold_bracelet = "prop_jewel_03a",
      earings = "p_tmom_earrings_s",
      book = "prop_cs_stock_book",
      bracelet = "prop_jewel_03a",
      sculpture = "prop_toiletfoot_static",
      j_phone = "lb_phone_prop",
      tapeplayer = "prop_boombox_01",
      coffemachine = "prop_coffee_mac_02",
      bong = "prop_bong_01",
      boombox = "prop_boombox_01",
      console = "prop_console_01",
      dj_deck = "prop_dj_deck_02",
      shoebox = "prop_cs_cardbox_01",
      fan = "prop_fan_01",
      radio_alarm = "v_res_fa_radioalrm",
      flat_television = "prop_tv_flat_michael",
      television = "prop_tv_05",
      monitor = "prop_monitor_w_large",
      npc_phone = "lb_phone_prop",
      printer = "prop_printer_02",
      laptop = "prop_laptop_02_closed",
      loot_bag = "prop_cs_heist_bag_01",
      hack_laptop = "prop_laptop_02_closed",
      toiletry = "prop_toilet_brush_01",
      skull = "p_jewel_pickup33_s",

      -- mask
      mask = "prop_mask_test_01",
 
      --falsk legg
      info_usb = "hei_prop_hst_usb_drive",
      clean_card = "p_ld_id_card_002",


        --jaksam robberies creator
        hacking_computer = "prop_laptop_02_closed",
        thermal_charge = "hei_prop_heist_thermite",
        gas_mask = "prop_mask_test_01",
        drill = "prop_tool_drill",
        gold_ingot = "hei_prop_heist_gold_bar",


        -- KamaKaZibO
        tablet = "prop_cs_tablet_02",

        --tequilala
        appledrink = "prop_drink_whisky",
        bananadrink = "prop_drink_whisky",
        orangedrink = "prop_drink_whisky",
        whiterum = "prop_drink_whisky",
        brandy = "prop_drink_whisky",
        wine_white = "prop_drink_whtwine",
        wine_rose = "prop_drink_whtwine",
        wine_red = "prop_drink_redwine",
        tequila = "prop_drink_whtwine",
        whiskey_richards = "prop_drink_whisky",
        whiskey_macbeth = "prop_drink_whisky",
        whiskey_mount = "prop_drink_whisky",
        hamburger = "prop_cs_burger_01",
        cheeseburger = "prop_cs_burger_01",
        steak = "prop_cs_steak",
        kiwi = "prop_bar_fruit",
        sugar = "prop_food_sugarjar",
        watermelon = "prop_bar_fruit",
        banana = "prop_bar_fruit",
        coconut = "prop_bar_fruit",
        strawberry = "prop_bar_fruit",
        apple = "prop_bar_fruit",
        orange = "prop_bar_fruit",
        daquiri = "prop_bar_fruit",
        tracker = "hei_prop_hst_usb_drive",

        -- ps-drugprocessing
        methtray = "prop_chem_vial_02",
        coke = "prop_coke_block_01",
        marijuana = "prop_weed_02",

        -- GGC Custom Weapons -- Melees
        weapon_katana = "w_me_katana",
        weapon_shiv = "w_me_shiv",
        weapon_sledgehammer = "prop_tool_sledgeham",
        weapon_karambit = "w_me_karambit",
        weapon_keyboard = "w_me_keyboard",

        -- GGC Custom Weapons -- Hand Guns
        weapon_glock17 = "w_pi_glock17_luxe",
        weapon_glock18c = "w_pi_glock18c",
        weapon_glock22 = "w_pi_glock22",
        weapon_deagle = "deserteagle",
        weapon_fnx45 = "w_pi_fnx45",
        weapon_m1911 = "w_pi_m1911",
        weapon_glock20 = "glock20",
        weapon_glock19gen4 = "glock19gen4",

          -- GGC Custom Weapons -- SMGs
          weapon_pmxfm = "pmx",
          weapon_mac10 = "w_sb_mac10",

          -- GGC Custom Weapons -- Rifles
          weapon_mk47fm = "MK47FluffysMods",
          weapon_m6ic = "m6ic",
          weapon_scarsc = "scarsc",
          weapon_m4 = "w_ar_m4",
          weapon_ak47 = "w_ar_ak47",
          weapon_ak74 = "w_ar_ak74",
          weapon_aks74 = "w_ar_assaultrifle_aks74",
          weapon_groza = "w_ar_groza",
          weapon_scarh = "w_ar_scarh",

          -- Airsoft
          weapon_airsoftak47 = "airsoftak",
          weapon_airsoftg36c = "airsoftg36",
          weapon_airsoftglock20 = "airsoftglock20",
          weapon_airsoftm4 = "airsoftm4a1",
          weapon_airsoftm249 = "airsoftm249",
          weapon_airsoftmicrouzi = "airsoftmicrouzi",
          weapon_airsoftmp5 = "airsoftmp5",
          weapon_airsoftr700 = "airsoftr700",
          weapon_airsoftr870 = "airsoft870",


          --R14 police
          nikon = "prop_pap_camera_01",
          microfibercloth = "prop_rag_01",

          basketball = "prop_bskball_01",
          basketball_hoop = "stt_prop_hoop_small_01",

          teddy = "v_ilev_mr_rasberryclean",
          bunch_of_flowers = "prop_snow_flower_02",
          rose = "prop_single_rose",

          smalltv = "prop_tv_01",
          mediumtv = "prop_tv_flat_03b",
          bigtv = "prop_tv_flat_michael",
}