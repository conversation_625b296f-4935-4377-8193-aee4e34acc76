{"manifest": {"name": "setprot<PERSON>of", "version": "1.1.1", "description": "A small polyfill for Object.setprototypeof", "main": "index.js", "typings": "index.d.ts", "scripts": {"test": "standard && mocha", "testallversions": "npm run node010 && npm run node4 && npm run node6 && npm run node9 && npm run node11", "testversion": "docker run -it --rm -v $(PWD):/usr/src/app -w /usr/src/app node:${NODE_VER} npm install mocha@${MOCHA_VER:-latest} && npm t", "node010": "NODE_VER=0.10 MOCHA_VER=3 npm run testversion", "node4": "NODE_VER=4 npm run testversion", "node6": "NODE_VER=6 npm run testversion", "node9": "NODE_VER=9 npm run testversion", "node11": "NODE_VER=11 npm run testversion"}, "repository": {"type": "git", "url": "https://github.com/wesleytodd/setprototypeof.git"}, "keywords": ["polyfill", "object", "setprot<PERSON>of"], "author": {"name": "<PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/wesleytodd/setprototypeof/issues"}, "homepage": "https://github.com/wesleytodd/setprototypeof", "devDependencies": {"mocha": "^5.2.0", "standard": "^12.0.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-setprototypeof-1.1.1-7e95acb24aa92f5885e0abef5ba131330d4ae683-integrity\\node_modules\\setprototypeof\\package.json", "readmeFilename": "README.md", "readme": "# Polyfill for `Object.setPrototypeOf`\n\n[![NPM Version](https://img.shields.io/npm/v/setprototypeof.svg)](https://npmjs.org/package/setprototypeof)\n[![NPM Downloads](https://img.shields.io/npm/dm/setprototypeof.svg)](https://npmjs.org/package/setprototypeof)\n[![js-standard-style](https://img.shields.io/badge/code%20style-standard-brightgreen.svg)](https://github.com/standard/standard)\n\nA simple cross platform implementation to set the prototype of an instianted object.  Supports all modern browsers and at least back to IE8.\n\n## Usage:\n\n```\n$ npm install --save setprototypeof\n```\n\n```javascript\nvar setPrototypeOf = require('setprototypeof')\n\nvar obj = {}\nsetPrototypeOf(obj, {\n  foo: function () {\n    return 'bar'\n  }\n})\nobj.foo() // bar\n```\n\nTypeScript is also supported:\n\n```typescript\nimport setPrototypeOf = require('setprototypeof')\n```\n", "licenseText": "Copyright (c) 2015, <PERSON>\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING AL<PERSON> IMPLIED WARRANTIES OF\nMERCHANTABILITY AND F<PERSON>NESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY\nSPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, <PERSON>ATA OR PROFITS, WHETHER IN AN ACTION\nOF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN\nCONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/setprototypeof/-/setprototypeof-1.1.1.tgz#7e95acb24aa92f5885e0abef5ba131330d4ae683", "type": "tarball", "reference": "https://registry.yarnpkg.com/setprototypeof/-/setprototypeof-1.1.1.tgz", "hash": "7e95acb24aa92f5885e0abef5ba131330d4ae683", "integrity": "sha512-JvdAWfbXeIGaZ9cILp38HntZSFSo3mWg6xGcJJsd+d4aRMOqauag1C63dJfDw7OaMYwEbHMOxEZ1lqVRYP2OAw==", "registry": "npm", "packageName": "setprot<PERSON>of", "cacheIntegrity": "sha512-JvdAWfbXeIGaZ9cILp38HntZSFSo3mWg6xGcJJsd+d4aRMOqauag1C63dJfDw7OaMYwEbHMOxEZ1lqVRYP2OAw== sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM="}, "registry": "npm", "hash": "7e95acb24aa92f5885e0abef5ba131330d4ae683"}