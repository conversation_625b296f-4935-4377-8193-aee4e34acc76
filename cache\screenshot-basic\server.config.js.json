[{"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[cfx-default]\\[system]\\[builders]\\webpack\\node_modules\\webpack\\buildin\\module.js", "stats": {"mtime": 1749054663461, "size": 497, "inode": 844424930286628}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@citizenfx\\client\\index.d.ts", "stats": {"mtime": 1749054673865, "size": 3330, "inode": 1125899907005773}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@citizenfx\\client\\natives_universal.d.ts", "stats": {"mtime": 1749054673865, "size": 1576346, "inode": 1125899907005778}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@citizenfx\\http-wrapper\\index.js", "stats": {"mtime": 1749054673860, "size": 6129, "inode": 1125899907005792}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@citizenfx\\server\\index.d.ts", "stats": {"mtime": 1749054673867, "size": 3327, "inode": 1125899907005789}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@citizenfx\\server\\natives_server.d.ts", "stats": {"mtime": 1749054673867, "size": 68153, "inode": 1125899907005794}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\accepts\\index.d.ts", "stats": {"mtime": 1749054673886, "size": 4617, "inode": 1125899907005780}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\body-parser\\index.d.ts", "stats": {"mtime": 1749054674566, "size": 4247, "inode": 1125899907005779}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\connect\\index.d.ts", "stats": {"mtime": 1749054674470, "size": 3433, "inode": 1125899907005800}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\content-disposition\\index.d.ts", "stats": {"mtime": 1749054673888, "size": 2082, "inode": 1125899907005821}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\cookies\\index.d.ts", "stats": {"mtime": 1749054673889, "size": 5721, "inode": 1125899907005808}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\express-serve-static-core\\index.d.ts", "stats": {"mtime": 1749054674577, "size": 37248, "inode": 1125899907005831}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\express\\index.d.ts", "stats": {"mtime": 1749054674472, "size": 4734, "inode": 1125899907005812}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\formidable\\index.d.ts", "stats": {"mtime": 1749054679977, "size": 1362, "inode": 1125899907005793}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\http-assert\\index.d.ts", "stats": {"mtime": 1749054674132, "size": 1885, "inode": 1125899907005820}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\http-errors\\index.d.ts", "stats": {"mtime": 1749054674145, "size": 3011, "inode": 1125899907005840}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\keygrip\\index.d.ts", "stats": {"mtime": 1749054674229, "size": 617, "inode": 1125899907005809}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\koa-compose\\index.d.ts", "stats": {"mtime": 1749054674445, "size": 2608, "inode": 1125899907005851}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\koa-router\\index.d.ts", "stats": {"mtime": 1749054674752, "size": 17132, "inode": 1125899907005833}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\koa\\index.d.ts", "stats": {"mtime": 1749054673868, "size": 20105, "inode": 1125899907005826}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\mime\\index.d.ts", "stats": {"mtime": 1749054674700, "size": 627, "inode": 1125899907005845}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\mv\\index.d.ts", "stats": {"mtime": 1749054674822, "size": 538, "inode": 1125899907005907}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\async_hooks.d.ts", "stats": {"mtime": 1749054674460, "size": 9693, "inode": 1125899907005858}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\buffer.d.ts", "stats": {"mtime": 1749054674460, "size": 708, "inode": 1125899907005856}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\child_process.d.ts", "stats": {"mtime": 1749054674460, "size": 25084, "inode": 1125899907005857}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\cluster.d.ts", "stats": {"mtime": 1749054674460, "size": 16125, "inode": 1125899907005860}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\console.d.ts", "stats": {"mtime": 1749054674460, "size": 5923, "inode": 1125899907005859}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\constants.d.ts", "stats": {"mtime": 1749054674460, "size": 459, "inode": 1125899907005861}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\crypto.d.ts", "stats": {"mtime": 1749054674460, "size": 46334, "inode": 1125899907005863}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\dgram.d.ts", "stats": {"mtime": 1749054674460, "size": 7195, "inode": 1125899907005862}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\dns.d.ts", "stats": {"mtime": 1749054674460, "size": 16315, "inode": 1125899907005864}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\domain.d.ts", "stats": {"mtime": 1749054674460, "size": 715, "inode": 1125899907005865}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\events.d.ts", "stats": {"mtime": 1749054674460, "size": 3691, "inode": 1125899907005866}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\fs.d.ts", "stats": {"mtime": 1749054674460, "size": 117840, "inode": 1125899907005867}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\fs\\promises.d.ts", "stats": {"mtime": 1749054674460, "size": 30101, "inode": 1125899907008832}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\globals.d.ts", "stats": {"mtime": 1749054674460, "size": 23701, "inode": 1125899907005869}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\http.d.ts", "stats": {"mtime": 1749054674460, "size": 18871, "inode": 1125899907005871}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\http2.d.ts", "stats": {"mtime": 1749054674460, "size": 56595, "inode": 1125899907005873}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\https.d.ts", "stats": {"mtime": 1749054674460, "size": 1684, "inode": 1125899907005870}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\inspector.d.ts", "stats": {"mtime": 1749054674460, "size": 121933, "inode": 1125899907005875}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\module.d.ts", "stats": {"mtime": 1749054674460, "size": 1643, "inode": 1125899907005876}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\net.d.ts", "stats": {"mtime": 1749054674460, "size": 12897, "inode": 1125899907005878}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\os.d.ts", "stats": {"mtime": 1749054674460, "size": 8178, "inode": 1125899907005877}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\path.d.ts", "stats": {"mtime": 1749054674460, "size": 6309, "inode": 1125899907005881}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\perf_hooks.d.ts", "stats": {"mtime": 1749054674460, "size": 10616, "inode": 1125899907005880}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\process.d.ts", "stats": {"mtime": 1749054674460, "size": 20332, "inode": 1125899907005884}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\punycode.d.ts", "stats": {"mtime": 1749054674460, "size": 3159, "inode": 1125899907005882}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\querystring.d.ts", "stats": {"mtime": 1749054674460, "size": 1046, "inode": 1125899907005883}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\readline.d.ts", "stats": {"mtime": 1749054674460, "size": 7330, "inode": 1125899907005886}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\repl.d.ts", "stats": {"mtime": 1749054674460, "size": 18118, "inode": 1125899907005887}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\stream.d.ts", "stats": {"mtime": 1749054674460, "size": 19374, "inode": 1125899907005889}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\string_decoder.d.ts", "stats": {"mtime": 1749054674460, "size": 193, "inode": 1125899907005888}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\timers.d.ts", "stats": {"mtime": 1749054674460, "size": 826, "inode": 1125899907005890}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\tls.d.ts", "stats": {"mtime": 1749054674460, "size": 37760, "inode": 1125899907005891}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\trace_events.d.ts", "stats": {"mtime": 1749054674460, "size": 2113, "inode": 1125899907005892}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\ts3.4\\assert.d.ts", "stats": {"mtime": 1749054674460, "size": 4648, "inode": 1125899907008846}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\ts3.4\\base.d.ts", "stats": {"mtime": 1749054674460, "size": 2420, "inode": 1125899907008849}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\ts3.4\\globals.global.d.ts", "stats": {"mtime": 1749054674460, "size": 35, "inode": 1125899907008850}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\ts3.4\\index.d.ts", "stats": {"mtime": 1749054674460, "size": 429, "inode": 1125899907008851}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\tty.d.ts", "stats": {"mtime": 1749054674460, "size": 2442, "inode": 1125899907005893}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\url.d.ts", "stats": {"mtime": 1749054674460, "size": 4209, "inode": 1125899907005894}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\util.d.ts", "stats": {"mtime": 1749054674460, "size": 12753, "inode": 1125899907005898}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\v8.d.ts", "stats": {"mtime": 1749054674460, "size": 6769, "inode": 1125899907005896}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\vm.d.ts", "stats": {"mtime": 1749054674460, "size": 5900, "inode": 1125899907005900}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\worker_threads.d.ts", "stats": {"mtime": 1749054674460, "size": 11446, "inode": 1125899907005904}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\node\\zlib.d.ts", "stats": {"mtime": 1749054674460, "size": 14327, "inode": 1125899907005901}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\qs\\index.d.ts", "stats": {"mtime": 1749054674602, "size": 2671, "inode": 1125899907005895}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\range-parser\\index.d.ts", "stats": {"mtime": 1749054674695, "size": 1224, "inode": 1125899907005899}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\serve-static\\index.d.ts", "stats": {"mtime": 1749054674660, "size": 4606, "inode": 1125899907005905}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\@types\\uuid\\index.d.ts", "stats": {"mtime": 1749054674845, "size": 2867, "inode": 1125899907005917}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\accepts\\index.js", "stats": {"mtime": 1749054678319, "size": 5252, "inode": 1125899907005328}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\any-promise\\index.js", "stats": {"mtime": 1749054679598, "size": 49, "inode": 1125899907005383}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\any-promise\\loader.js", "stats": {"mtime": 1749054679598, "size": 2581, "inode": 1125899907005382}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\any-promise\\register.js", "stats": {"mtime": 1749054679598, "size": 2910, "inode": 1125899907005386}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\balanced-match\\index.js", "stats": {"mtime": 1749054670934, "size": 1174, "inode": 1125899907005432}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\brace-expansion\\index.js", "stats": {"mtime": 1749054670510, "size": 4792, "inode": 1125899907005459}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\bytes\\index.js", "stats": {"mtime": 1749054680422, "size": 3469, "inode": 1125899907005518}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\cache-content-type\\index.js", "stats": {"mtime": 1749054678330, "size": 317, "inode": 1125899907005559}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\co-body\\index.js", "stats": {"mtime": 1749054679990, "size": 163, "inode": 1125899907005591}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\co-body\\lib\\any.js", "stats": {"mtime": 1749054679990, "size": 1204, "inode": 1125899907008765}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\co-body\\lib\\form.js", "stats": {"mtime": 1749054679990, "size": 1365, "inode": 1125899907008766}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\co-body\\lib\\json.js", "stats": {"mtime": 1749054679990, "size": 1621, "inode": 1407374883719423}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\co-body\\lib\\text.js", "stats": {"mtime": 1749054679990, "size": 1054, "inode": 1125899907008773}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\co-body\\lib\\utils.js", "stats": {"mtime": 1749054679990, "size": 189, "inode": 1125899907008771}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\co\\index.js", "stats": {"mtime": 1749054679402, "size": 5036, "inode": 1125899907005585}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\concat-map\\index.js", "stats": {"mtime": 1749054670936, "size": 345, "inode": 1125899907005644}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\content-disposition\\index.js", "stats": {"mtime": 1749054678373, "size": 10594, "inode": 1125899907005756}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\content-disposition\\node_modules\\safe-buffer\\index.js", "stats": {"mtime": 1749054667690, "size": 1529, "inode": 1125899907005765}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\content-type\\index.js", "stats": {"mtime": 1749054678423, "size": 4809, "inode": 1125899907005654}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\cookies\\index.js", "stats": {"mtime": 1749054678435, "size": 6133, "inode": 1125899907005750}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\cookies\\node_modules\\depd\\index.js", "stats": {"mtime": 1749054678518, "size": 10932, "inode": 1125899907005769}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\deep-equal\\index.js", "stats": {"mtime": 1749054679200, "size": 3051, "inode": 1125899907005998}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\deep-equal\\lib\\is_arguments.js", "stats": {"mtime": 1749054679200, "size": 641, "inode": 1125899907008938}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\deep-equal\\lib\\keys.js", "stats": {"mtime": 1749054679200, "size": 202, "inode": 1125899907008943}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\delegates\\index.js", "stats": {"mtime": 1749054678493, "size": 2065, "inode": 1125899907006021}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\depd\\index.js", "stats": {"mtime": 1749054679261, "size": 10669, "inode": 1125899907006024}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\depd\\lib\\compat\\callsite-tostring.js", "stats": {"mtime": 1749054679261, "size": 2229, "inode": 844424930300905}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\depd\\lib\\compat\\event-listener-count.js", "stats": {"mtime": 1749054679261, "size": 338, "inode": 844424930300903}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\depd\\lib\\compat\\index.js", "stats": {"mtime": 1749054679261, "size": 1421, "inode": 844424930300908}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\destroy\\index.js", "stats": {"mtime": 1749054678558, "size": 1043, "inode": 1125899907006037}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\ee-first\\index.js", "stats": {"mtime": 1749054679483, "size": 1684, "inode": 1125899907006081}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\encodeurl\\index.js", "stats": {"mtime": 1749054678572, "size": 1586, "inode": 1407374883716747}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\escape-html\\index.js", "stats": {"mtime": 1749054678644, "size": 1362, "inode": 1407374883716795}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\formidable\\lib\\file.js", "stats": {"mtime": 1749054679999, "size": 1643, "inode": 844424930299256}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\formidable\\lib\\incoming_form.js", "stats": {"mtime": 1749054679999, "size": 13698, "inode": 844424930299260}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\formidable\\lib\\index.js", "stats": {"mtime": 1749054679999, "size": 133, "inode": 844424930299263}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\formidable\\lib\\json_parser.js", "stats": {"mtime": 1749054679999, "size": 650, "inode": 844424930299259}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\formidable\\lib\\multipart_parser.js", "stats": {"mtime": 1749054679999, "size": 8460, "inode": 844424930299262}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\formidable\\lib\\octet_parser.js", "stats": {"mtime": 1749054679999, "size": 456, "inode": 844424930299264}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\formidable\\lib\\querystring_parser.js", "stats": {"mtime": 1749054679999, "size": 740, "inode": 844424930299267}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\fresh\\index.js", "stats": {"mtime": 1749054678661, "size": 2711, "inode": 1407374883716943}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\has-flag\\index.js", "stats": {"mtime": 1749054681282, "size": 320, "inode": 1407374883716965}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\http-assert\\index.js", "stats": {"mtime": 1749054678682, "size": 937, "inode": 1407374883717029}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\http-errors\\index.js", "stats": {"mtime": 1749054679204, "size": 5878, "inode": 1407374883717023}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\dbcs-codec.js", "stats": {"mtime": 1749054680448, "size": 21415, "inode": 844424930299321}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\dbcs-data.js", "stats": {"mtime": 1749054680448, "size": 8291, "inode": 844424930299323}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\index.js", "stats": {"mtime": 1749054680448, "size": 710, "inode": 844424930299325}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\internal.js", "stats": {"mtime": 1749054680448, "size": 6115, "inode": 844424930299324}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\sbcs-codec.js", "stats": {"mtime": 1749054680448, "size": 2191, "inode": 844424930299326}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\sbcs-data-generated.js", "stats": {"mtime": 1749054680448, "size": 32034, "inode": 844424930299330}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\sbcs-data.js", "stats": {"mtime": 1749054680448, "size": 4686, "inode": 844424930299335}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\tables\\big5-added.json", "stats": {"mtime": 1749054680448, "size": 17717, "inode": 844424930300962}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\tables\\cp936.json", "stats": {"mtime": 1749054680448, "size": 47320, "inode": 844424930300964}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\tables\\cp949.json", "stats": {"mtime": 1749054680448, "size": 38122, "inode": 844424930300965}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\tables\\cp950.json", "stats": {"mtime": 1749054680448, "size": 42356, "inode": 844424930300966}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\tables\\eucjp.json", "stats": {"mtime": 1749054680448, "size": 41064, "inode": 844424930300968}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\tables\\gb18030-ranges.json", "stats": {"mtime": 1749054680448, "size": 2216, "inode": 844424930300969}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\tables\\gbk-added.json", "stats": {"mtime": 1749054680448, "size": 1227, "inode": 844424930300975}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\tables\\shiftjis.json", "stats": {"mtime": 1749054680448, "size": 23782, "inode": 844424930300973}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\utf16.js", "stats": {"mtime": 1749054680448, "size": 5011, "inode": 844424930299333}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\utf7.js", "stats": {"mtime": 1749054680448, "size": 9215, "inode": 844424930299338}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\lib\\bom-handling.js", "stats": {"mtime": 1749054680448, "size": 1109, "inode": 844424930299332}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\lib\\extend-node.js", "stats": {"mtime": 1749054680448, "size": 8701, "inode": 844424930299334}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\lib\\index.js", "stats": {"mtime": 1749054680448, "size": 5123, "inode": 844424930299336}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\iconv-lite\\lib\\streams.js", "stats": {"mtime": 1749054680448, "size": 3387, "inode": 844424930299341}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\inflation\\index.js", "stats": {"mtime": 1749054680005, "size": 637, "inode": 1407374883717068}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\inflight\\inflight.js", "stats": {"mtime": 1749054669714, "size": 1365, "inode": 1407374883717088}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\inherits\\inherits.js", "stats": {"mtime": 1749054667587, "size": 250, "inode": 1407374883717085}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\inherits\\inherits_browser.js", "stats": {"mtime": 1749054667587, "size": 753, "inode": 1407374883717086}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\is-generator-function\\index.js", "stats": {"mtime": 1749054678692, "size": 912, "inode": 1407374883717144}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\keygrip\\index.js", "stats": {"mtime": 1749054679194, "size": 1158, "inode": 1407374883717338}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa-body\\index.d.ts", "stats": {"mtime": 1749054679638, "size": 5400, "inode": 1407374883717371}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa-body\\index.js", "stats": {"mtime": 1749054679638, "size": 6211, "inode": 1407374883717370}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa-body\\unparsed.js", "stats": {"mtime": 1749054679638, "size": 275, "inode": 1407374883717377}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa-compose\\index.js", "stats": {"mtime": 1749054679453, "size": 1189, "inode": 1407374883717365}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa-convert\\index.js", "stats": {"mtime": 1749054678787, "size": 1584, "inode": 1407374883717356}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa-router\\lib\\layer.js", "stats": {"mtime": 1749054680532, "size": 5750, "inode": 844424930299396}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa-router\\lib\\router.js", "stats": {"mtime": 1749054680532, "size": 17653, "inode": 844424930299399}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa-router\\node_modules\\debug\\src\\browser.js", "stats": {"mtime": 1749054680579, "size": 6285, "inode": 844424930300009}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa-router\\node_modules\\debug\\src\\common.js", "stats": {"mtime": 1749054680579, "size": 5929, "inode": 844424930300011}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa-router\\node_modules\\debug\\src\\index.js", "stats": {"mtime": 1749054680579, "size": 331, "inode": 844424930300013}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa-router\\node_modules\\debug\\src\\node.js", "stats": {"mtime": 1749054680579, "size": 4415, "inode": 844424930300015}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa-router\\node_modules\\http-errors\\index.js", "stats": {"mtime": 1749054678684, "size": 6521, "inode": 1125899907008002}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa-router\\node_modules\\ms\\index.js", "stats": {"mtime": 1749054680757, "size": 3024, "inode": 1125899907008027}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa-router\\node_modules\\setprototypeof\\index.js", "stats": {"mtime": 1749054679354, "size": 407, "inode": 1125899907008020}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa\\lib\\application.js", "stats": {"mtime": 1749054678255, "size": 7342, "inode": 844424930299393}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa\\lib\\context.js", "stats": {"mtime": 1749054678255, "size": 5582, "inode": 844424930299397}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa\\lib\\request.js", "stats": {"mtime": 1749054678255, "size": 14464, "inode": 844424930299395}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa\\lib\\response.js", "stats": {"mtime": 1749054678255, "size": 12101, "inode": 844424930299401}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa\\node_modules\\debug\\src\\browser.js", "stats": {"mtime": 1749054678447, "size": 5707, "inode": 844424930300014}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa\\node_modules\\debug\\src\\debug.js", "stats": {"mtime": 1749054678447, "size": 4889, "inode": 844424930300022}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa\\node_modules\\debug\\src\\index.js", "stats": {"mtime": 1749054678447, "size": 263, "inode": 844424930300021}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa\\node_modules\\debug\\src\\node.js", "stats": {"mtime": 1749054678447, "size": 4339, "inode": 844424930300019}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa\\node_modules\\depd\\index.js", "stats": {"mtime": 1749054678518, "size": 10932, "inode": 1125899907008007}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa\\node_modules\\http-errors\\index.js", "stats": {"mtime": 1749054678684, "size": 6521, "inode": 1125899907008069}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa\\node_modules\\http-errors\\node_modules\\depd\\index.js", "stats": {"mtime": 1749054679261, "size": 10669, "inode": 1125899907008072}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa\\node_modules\\http-errors\\node_modules\\depd\\lib\\compat\\callsite-tostring.js", "stats": {"mtime": 1749054679261, "size": 2229, "inode": 844424930301039}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa\\node_modules\\http-errors\\node_modules\\depd\\lib\\compat\\event-listener-count.js", "stats": {"mtime": 1749054679261, "size": 338, "inode": 844424930301047}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa\\node_modules\\http-errors\\node_modules\\depd\\lib\\compat\\index.js", "stats": {"mtime": 1749054679261, "size": 1421, "inode": 844424930301045}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa\\node_modules\\koa-compose\\index.js", "stats": {"mtime": 1749054678768, "size": 1115, "inode": 1125899907008001}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\koa\\node_modules\\setprototypeof\\index.js", "stats": {"mtime": 1749054679354, "size": 407, "inode": 1125899907008009}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\media-typer\\index.js", "stats": {"mtime": 1749054679526, "size": 6375, "inode": 1407374883717402}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\methods\\index.js", "stats": {"mtime": 1749054680612, "size": 1040, "inode": 1688849860428709}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\mime-db\\db.json", "stats": {"mtime": 1749054679548, "size": 180015, "inode": 1407374883718079}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\mime-db\\index.js", "stats": {"mtime": 1749054679548, "size": 136, "inode": 1407374883718074}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\mime-types\\index.js", "stats": {"mtime": 1749054679009, "size": 3663, "inode": 1407374883718075}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\minimatch\\minimatch.js", "stats": {"mtime": 1749054669718, "size": 25347, "inode": 1407374883718087}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\mkdirp\\index.js", "stats": {"mtime": 1749054664550, "size": 2644, "inode": 1407374883718107}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\ms\\index.js", "stats": {"mtime": 1749054669319, "size": 2764, "inode": 1407374883718106}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\mv\\index.js", "stats": {"mtime": 1749054680789, "size": 2533, "inode": 1407374883718120}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\mv\\node_modules\\glob\\common.js", "stats": {"mtime": 1749054680937, "size": 5584, "inode": 1125899907008044}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\mv\\node_modules\\glob\\glob.js", "stats": {"mtime": 1749054680937, "size": 18849, "inode": 1125899907008047}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\mv\\node_modules\\glob\\sync.js", "stats": {"mtime": 1749054680937, "size": 11461, "inode": 1125899907008055}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\mv\\node_modules\\rimraf\\rimraf.js", "stats": {"mtime": 1749054680927, "size": 7937, "inode": 1125899907007977}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\ncp\\lib\\ncp.js", "stats": {"mtime": 1749054680911, "size": 6112, "inode": 844424930299868}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\negotiator\\index.js", "stats": {"mtime": 1749054679038, "size": 3344, "inode": 1407374883718129}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\negotiator\\lib\\charset.js", "stats": {"mtime": 1749054679038, "size": 3081, "inode": 844424930299877}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\negotiator\\lib\\encoding.js", "stats": {"mtime": 1749054679038, "size": 3506, "inode": 844424930299875}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\negotiator\\lib\\language.js", "stats": {"mtime": 1749054679038, "size": 3408, "inode": 844424930299884}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\negotiator\\lib\\mediaType.js", "stats": {"mtime": 1749054679038, "size": 5358, "inode": 844424930299883}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\on-finished\\index.js", "stats": {"mtime": 1749054678818, "size": 3686, "inode": 1407374883718346}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\once\\once.js", "stats": {"mtime": 1749054669750, "size": 935, "inode": 1407374883718351}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\only\\index.js", "stats": {"mtime": 1749054678822, "size": 247, "inode": 1407374883718365}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\parseurl\\index.js", "stats": {"mtime": 1749054678869, "size": 2809, "inode": 1407374883718388}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\path-is-absolute\\index.js", "stats": {"mtime": 1749054669776, "size": 611, "inode": 1407374883718416}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\path-to-regexp\\index.js", "stats": {"mtime": 1749054680617, "size": 10854, "inode": 1407374883718426}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\path-to-regexp\\node_modules\\isarray\\index.js", "stats": {"mtime": 1749054680772, "size": 120, "inode": 1125899907007982}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\qs\\lib\\formats.js", "stats": {"mtime": 1749054680122, "size": 521, "inode": 844424930299957}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\qs\\lib\\index.js", "stats": {"mtime": 1749054680122, "size": 211, "inode": 844424930299961}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\qs\\lib\\parse.js", "stats": {"mtime": 1749054680122, "size": 9197, "inode": 844424930299970}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\qs\\lib\\stringify.js", "stats": {"mtime": 1749054680122, "size": 8030, "inode": 844424930299963}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\qs\\lib\\utils.js", "stats": {"mtime": 1749054680122, "size": 6636, "inode": 844424930299965}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\raw-body\\index.js", "stats": {"mtime": 1749054680209, "size": 6059, "inode": 1125899907007888}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\safer-buffer\\safer.js", "stats": {"mtime": 1749054670825, "size": 2110, "inode": 1125899907008112}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\setprototypeof\\index.js", "stats": {"mtime": 1749054679587, "size": 384, "inode": 1125899907008152}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\statuses\\codes.json", "stats": {"mtime": 1749054678948, "size": 1821, "inode": 1125899907008216}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\statuses\\index.js", "stats": {"mtime": 1749054678948, "size": 2088, "inode": 1125899907008222}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\supports-color\\index.js", "stats": {"mtime": 1749054681089, "size": 2771, "inode": 1125899907008283}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\toidentifier\\index.js", "stats": {"mtime": 1749054679371, "size": 490, "inode": 1125899907008324}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\tsscmp\\lib\\index.js", "stats": {"mtime": 1749054679558, "size": 1176, "inode": 844424930300115}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\type-is\\index.js", "stats": {"mtime": 1749054678999, "size": 5562, "inode": 1125899907008374}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.collection.d.ts", "stats": {"mtime": 1749054681540, "size": 2845, "inode": 844424930300143}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.core.d.ts", "stats": {"mtime": 1749054681540, "size": 19608, "inode": 844424930300142}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.d.ts", "stats": {"mtime": 1749054681540, "size": 1250, "inode": 844424930300145}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.generator.d.ts", "stats": {"mtime": 1749054681540, "size": 2129, "inode": 844424930300144}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.iterable.d.ts", "stats": {"mtime": 1749054681540, "size": 14536, "inode": 844424930300147}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.promise.d.ts", "stats": {"mtime": 1749054681540, "size": 10652, "inode": 844424930300146}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.proxy.d.ts", "stats": {"mtime": 1749054681540, "size": 1961, "inode": 844424930300148}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.reflect.d.ts", "stats": {"mtime": 1749054681540, "size": 1913, "inode": 844424930300149}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.symbol.d.ts", "stats": {"mtime": 1749054681540, "size": 1657, "inode": 844424930300151}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.symbol.wellknown.d.ts", "stats": {"mtime": 1749054681540, "size": 10257, "inode": 844424930300150}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2016.array.include.d.ts", "stats": {"mtime": 1749054681540, "size": 4870, "inode": 844424930300153}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2016.d.ts", "stats": {"mtime": 1749054681540, "size": 937, "inode": 844424930300152}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.d.ts", "stats": {"mtime": 1749054681540, "size": 1092, "inode": 844424930300157}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.intl.d.ts", "stats": {"mtime": 1749054681540, "size": 1253, "inode": 844424930300158}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.object.d.ts", "stats": {"mtime": 1749054681540, "size": 2461, "inode": 844424930300156}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.sharedmemory.d.ts", "stats": {"mtime": 1749054681540, "size": 6148, "inode": 844424930300160}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.string.d.ts", "stats": {"mtime": 1749054681540, "size": 2387, "inode": 844424930300161}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.typedarrays.d.ts", "stats": {"mtime": 1749054681540, "size": 1434, "inode": 844424930300159}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2018.d.ts", "stats": {"mtime": 1749054681540, "size": 1006, "inode": 844424930300162}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2018.intl.d.ts", "stats": {"mtime": 1749054681540, "size": 1821, "inode": 844424930300165}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2018.promise.d.ts", "stats": {"mtime": 1749054681540, "size": 1361, "inode": 844424930300164}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2018.regexp.d.ts", "stats": {"mtime": 1749054681540, "size": 1236, "inode": 844424930300166}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es5.d.ts", "stats": {"mtime": 1749054681540, "size": 200360, "inode": 844424930300167}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.esnext.asynciterable.d.ts", "stats": {"mtime": 1749054681540, "size": 1544, "inode": 844424930300170}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.esnext.bigint.d.ts", "stats": {"mtime": 1749054681540, "size": 30733, "inode": 844424930300171}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.esnext.intl.d.ts", "stats": {"mtime": 1749054681540, "size": 1255, "inode": 844424930300174}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\unpipe\\index.js", "stats": {"mtime": 1749054680481, "size": 1118, "inode": 1125899907008391}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\urijs\\src\\IPv6.js", "stats": {"mtime": 1749054680700, "size": 4422, "inode": 844424930300219}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\urijs\\src\\SecondLevelDomains.js", "stats": {"mtime": 1749054680700, "size": 12088, "inode": 844424930300226}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\urijs\\src\\URI.js", "stats": {"mtime": 1749054680700, "size": 66382, "inode": 844424930300231}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\urijs\\src\\punycode.js", "stats": {"mtime": 1749054680700, "size": 14670, "inode": 844424930300221}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\uuid\\index.js", "stats": {"mtime": 1749054681543, "size": 120, "inode": 1125899907008466}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\uuid\\lib\\bytesToUuid.js", "stats": {"mtime": 1749054681543, "size": 775, "inode": 844424930300246}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\uuid\\lib\\rng.js", "stats": {"mtime": 1749054681543, "size": 246, "inode": 844424930300252}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\uuid\\v1.js", "stats": {"mtime": 1749054681543, "size": 3331, "inode": 1125899907008471}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\uuid\\v4.js", "stats": {"mtime": 1749054681543, "size": 680, "inode": 1125899907008474}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\vary\\index.js", "stats": {"mtime": 1749054679002, "size": 2930, "inode": 1125899907008477}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\wrappy\\wrappy.js", "stats": {"mtime": 1749054670499, "size": 905, "inode": 1125899907008522}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\node_modules\\ylru\\index.js", "stats": {"mtime": 1749054679164, "size": 2337, "inode": 1125899907008520}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\src\\server\\server.ts", "stats": {"mtime": 1749053766312.6462, "size": 2523, "inode": 1970324837117915}}, {"name": "D:\\txData\\QBCore_4070F0.base\\resources\\[standalone]\\screenshot-basic\\src\\server\\types\\@citizenfx\\http-wrapper.d.ts", "stats": {"mtime": 1749053766314.6472, "size": 60, "inode": 1970324837117917}}]