{"manifest": {"name": "mime-db", "description": "Media Type Database", "version": "1.45.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.7.2", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "4.12.0", "eslint": "7.9.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.22.0", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "gnode": "0.1.2", "mocha": "8.1.3", "nyc": "15.1.0", "raw-body": "2.4.1", "stream-to-array": "2.3.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-mime-db-1.45.0-cceeda21ccd7c3a745eba2decd55d4b73e7879ea-integrity\\node_modules\\mime-db\\package.json", "readmeFilename": "README.md", "readme": "# mime-db\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-image]][node-url]\n[![Build Status][travis-image]][travis-url]\n[![Coverage Status][coveralls-image]][coveralls-url]\n\nThis is a database of all mime types.\nIt consists of a single, public JSON file and does not include any logic,\nallowing it to remain as un-opinionated as possible with an API.\nIt aggregates data from the following sources:\n\n- http://www.iana.org/assignments/media-types/media-types.xhtml\n- http://svn.apache.org/repos/asf/httpd/httpd/trunk/docs/conf/mime.types\n- http://hg.nginx.org/nginx/raw-file/default/conf/mime.types\n\n## Installation\n\n```bash\nnpm install mime-db\n```\n\n### Database Download\n\nIf you're crazy enough to use this in the browser, you can just grab the\nJSON file using [jsDelivr](https://www.jsdelivr.com/). It is recommended to\nreplace `master` with [a release tag](https://github.com/jshttp/mime-db/tags)\nas the JSON format may change in the future.\n\n```\nhttps://cdn.jsdelivr.net/gh/jshttp/mime-db@master/db.json\n```\n\n## Usage\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar db = require('mime-db')\n\n// grab data on .js files\nvar data = db['application/javascript']\n```\n\n## Data Structure\n\nThe JSON file is a map lookup for lowercased mime types.\nEach mime type has the following properties:\n\n- `.source` - where the mime type is defined.\n    If not set, it's probably a custom media type.\n    - `apache` - [Apache common media types](http://svn.apache.org/repos/asf/httpd/httpd/trunk/docs/conf/mime.types)\n    - `iana` - [IANA-defined media types](http://www.iana.org/assignments/media-types/media-types.xhtml)\n    - `nginx` - [nginx media types](http://hg.nginx.org/nginx/raw-file/default/conf/mime.types)\n- `.extensions[]` - known extensions associated with this mime type.\n- `.compressible` - whether a file of this type can be gzipped.\n- `.charset` - the default charset associated with this type, if any.\n\nIf unknown, every property could be `undefined`.\n\n## Contributing\n\nTo edit the database, only make PRs against `src/custom-types.json` or\n`src/custom-suffix.json`.\n\nThe `src/custom-types.json` file is a JSON object with the MIME type as the\nkeys and the values being an object with the following keys:\n\n- `compressible` - leave out if you don't know, otherwise `true`/`false` to\n  indicate whether the data represented by the type is typically compressible.\n- `extensions` - include an array of file extensions that are associated with\n  the type.\n- `notes` - human-readable notes about the type, typically what the type is.\n- `sources` - include an array of URLs of where the MIME type and the associated\n  extensions are sourced from. This needs to be a [primary source](https://en.wikipedia.org/wiki/Primary_source);\n  links to type aggregating sites and Wikipedia are _not acceptable_.\n\nTo update the build, run `npm run build`.\n\n### Adding Custom Media Types\n\nThe best way to get new media types included in this library is to register\nthem with the IANA. The community registration procedure is outlined in\n[RFC 6838 section 5](http://tools.ietf.org/html/rfc6838#section-5). Types\nregistered with the IANA are automatically pulled into this library.\n\nIf that is not possible / feasible, they can be added directly here as a\n\"custom\" type. To do this, it is required to have a primary source that\ndefinitively lists the media type. If an extension is going to be listed as\nassociateed with this media type, the source must definitively link the\nmedia type and extension as well.\n\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/mime-db/master\n[coveralls-url]: https://coveralls.io/r/jshttp/mime-db?branch=master\n[node-image]: https://badgen.net/npm/node/mime-db\n[node-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/mime-db\n[npm-url]: https://npmjs.org/package/mime-db\n[npm-version-image]: https://badgen.net/npm/v/mime-db\n[travis-image]: https://badgen.net/travis/jshttp/mime-db/master\n[travis-url]: https://travis-ci.org/jshttp/mime-db\n", "licenseText": "\nThe MIT License (MIT)\n\nCopyright (c) 2014 <NAME_EMAIL>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/mime-db/-/mime-db-1.45.0.tgz#cceeda21ccd7c3a745eba2decd55d4b73e7879ea", "type": "tarball", "reference": "https://registry.yarnpkg.com/mime-db/-/mime-db-1.45.0.tgz", "hash": "cceeda21ccd7c3a745eba2decd55d4b73e7879ea", "integrity": "sha512-CkqLUxUk15hofLoLyljJSrukZi8mAtgd+yE5uO4tqRZsdsAJKv0O+rFMhVDRJgozy+yG6md5KwuXhD4ocIoP+w==", "registry": "npm", "packageName": "mime-db", "cacheIntegrity": "sha512-CkqLUxUk15hofLoLyljJSrukZi8mAtgd+yE5uO4tqRZsdsAJKv0O+rFMhVDRJgozy+yG6md5KwuXhD4ocIoP+w== sha1-zO7aIczXw6dF66LezVXUtz54eeo="}, "registry": "npm", "hash": "cceeda21ccd7c3a745eba2decd55d4b73e7879ea"}