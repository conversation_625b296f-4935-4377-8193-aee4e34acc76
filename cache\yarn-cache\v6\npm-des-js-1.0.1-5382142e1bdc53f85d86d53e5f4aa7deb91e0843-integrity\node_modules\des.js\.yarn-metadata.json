{"manifest": {"name": "des.js", "version": "1.0.1", "description": "DES implementation", "main": "lib/des.js", "scripts": {"test": "mocha --reporter=spec test/*-test.js && jscs lib/*.js lib/**/*.js test/*.js && jshint lib/*.js lib/**/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/indutny/des.js.git"}, "keywords": ["DES", "3DES", "EDE", "CBC"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/indutny/des.js/issues"}, "homepage": "https://github.com/indutny/des.js#readme", "devDependencies": {"jscs": "^3.0.7", "jshint": "^2.8.0", "mocha": "^6.2.2"}, "dependencies": {"inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-des-js-1.0.1-5382142e1bdc53f85d86d53e5f4aa7deb91e0843-integrity\\node_modules\\des.js\\package.json", "readmeFilename": "README.md", "readme": "# DES.js\n\n## LICENSE\n\nThis software is licensed under the MIT License.\n\nCopyright Fedor Indutny, 2015.\n\nPermission is hereby granted, free of charge, to any person obtaining a\ncopy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to permit\npersons to whom the Software is furnished to do so, subject to the\nfollowing conditions:\n\nThe above copyright notice and this permission notice shall be included\nin all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\nOR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN\nNO EVENT SHALL THE AUTHORS OR COPY<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM,\nDAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\nOTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\nUSE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/des.js/-/des.js-1.0.1.tgz#5382142e1bdc53f85d86d53e5f4aa7deb91e0843", "type": "tarball", "reference": "https://registry.yarnpkg.com/des.js/-/des.js-1.0.1.tgz", "hash": "5382142e1bdc53f85d86d53e5f4aa7deb91e0843", "integrity": "sha512-Q0I4pfFrv2VPd34/vfLrFOoRmlYj3OV50i7fskps1jZWK1kApMWWT9G6RRUeYedLcBDIhnSDaUvJMb3AhUlaEA==", "registry": "npm", "packageName": "des.js", "cacheIntegrity": "sha512-Q0I4pfFrv2VPd34/vfLrFOoRmlYj3OV50i7fskps1jZWK1kApMWWT9G6RRUeYedLcBDIhnSDaUvJMb3AhUlaEA== sha1-U4IULhvcU/hdhtU+X0qn3rkeCEM="}, "registry": "npm", "hash": "5382142e1bdc53f85d86d53e5f4aa7deb91e0843"}