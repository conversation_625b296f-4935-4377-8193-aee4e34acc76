const { createApp, reactive } = Vue;

const app = createApp({
    data() {
        return {
            pages: {
                multicharacter: false,
                spawnSelector: false,
            },
            activeCharacter: {},
            activeIndex: 0,
            activePlayButton: false,
            bgSound: null,
            characterSlot: 0,
            currency: false,
            location: null,
            locales: {},
            slotKey: null,
            settings: {
                color: {},
                filter: {},
                particle: {},
                animations: {},
                streamer: false,
                mutedMusic: false,
                focus: false,
                clear: false,
                music: {},
                coords: {},
                time: {},
                weather: {},
            },
            datas: {
                characters: [],
                colors: [],
                filters: [],
                effects: [],
                animations: [],
                scenarios: [],
                coords: [],
                weather: [
                    'CLEAR',
                    'EXTRASUNNY',
                    'CLOUDS',
                    'OVERCAST',
                    'RAIN',
                    'CLEARING',
                    'THUNDER',
                    'SMOG',
                    'FOGGY',
                    'XMAS',
                    'SNOW',
                    'SNOWLIGHT',
                    'BLIZZARD',
                    'HALLOWEEN',
                    'NEUTRAL'
                ]
            },
            isLoading: true,
            isNewCharacter: false,
            isDeletable: false,
            isNewSlotKey: false,
            isPhotoMode: false,
            nationalities: [],
            selectNationality: false,
            registerContext: {
                cid: null,
                firstname: null,
                lastname: null,
                heigth: null,
                birthdate: null,
                gender: null,
                nationality: null,
            },
            searchedNationalities: null,
            notifications: [],
            dob : {},
            use0ResmonSpawn: false,
            spawnLocations: [],
            selectedLocation: 'last-location',
            isPressedPlay: false,
            popups: {
                delete: false,
                key: false,
            },
        };
    },
    methods: {
        messageHandler(event) {
            switch (event.data.action) {
                case "setCharacters":
                    this.characters = event.data.payload;

                    if (!this.characters || this.characters.length === 0) {
                        this.isNewCharacter = true;
                        postNUI("newCharacterCam", true);
                    } else {
                        this.characters = this.characters.sort((a, b) => a.charinfo.cid - b.charinfo.cid);
                        this.activeCharacter = this.characters[0];
                        this.activeIndex = 0;
                        postNUI("selectCharacter", this.activeCharacter);
                    }
                
                    setTimeout(() => {
                        const serverLogo = document.getElementById("server-logo");
                        const topBg = document.getElementById("top-bg");
                        const bottomBg = document.getElementById("bottom-bg");

                        serverLogo.classList.add('animation-fade-out');

                        if (this.settings.animations.entrance === "top-and-bottom") {
                            topBg.classList.add('animation-top-out');
                            bottomBg.classList.add('animation-bottom-out');
                        } else if (this.settings.animations.entrance === "left-and-right") {
                            topBg.classList.add('animation-left-out');
                            bottomBg.classList.add('animation-right-out');
                        }

                        if (this.settings.music.Status) {
                            this.bgSound = new Audio(`assets/music/${this.settings.music.Name}`);
                            this.bgSound.volume = 0.1;
                            this.bgSound.loop = true;
                            this.bgSound.play();
    
                            if (this.settings.mutedMusic) {
                                this.bgSound.muted = true;
                            }
                        }

                        setTimeout(() => {    
                            serverLogo.style.display = "none";
                            this.isLoading = false;
                            this.pages.multicharacter = true;
    
                            setTimeout(() => {
                                const leftSide = document.getElementById("left-side");
                                const rightSide = document.getElementById("right-side");
    
                                document.getElementById("left-side").style.display = "flex";
                                document.getElementById("right-side").style.display = "flex";
                        
                                leftSide.classList.add('animation-left-in');
                                rightSide.classList.add('animation-right-in');
                            }, 1);
                        }, 1200);
                    }, 1000);
                    break;
                case "setCountries":
                    this.nationalities = event.data.payload;
                    this.nationalities = this.nationalities.sort();
                    break;
                case "setConfig":
                    this.isDeletable = event.data.payload.DeleteButton;
                    this.currency = event.data.payload.Currency;
                    this.location = event.data.payload.Location;
                    this.cameraFilters = event.data.payload.FilterMode;
                    this.datas.effects = event.data.payload.Effects;
                    this.datas.filters = event.data.payload.Filters;
                    this.datas.animations = event.data.payload.Animations;
                    this.datas.scenarios = event.data.payload.Scenarios;
                    this.datas.colors = event.data.payload.Colors;
                    this.settings.music = event.data.payload.BackgroundMusic;
                    this.datas.coords = event.data.payload.Coords;
                    this.dob = event.data.payload.DOBLimit;
                    this.use0ResmonSpawn = event.data.payload.Use0Resmon;
                    if (event.data.payload.Use0Resmon) {
                        this.spawnLocations = event.data.payload.SpawnLocations;
                    }
                    break;
                case "setAvailableSlot":
                    this.characterSlot = event.data.payload;
                    break;
                case "setLocales":
                    this.locales = event.data.payload;
                    break;
                case "setMugshot":
                    this.activeCharacterMugshot = event.data.payload;
                    break;
                case "showSpawn":
                    this.pages = {
                        multicharacter: false,
                        spawnSelector: true,
                    }
                    break;
                case "isLoading":
                    this.isLoading = event.data.payload;
                    this.activePlayButton = false;
                    setTimeout(() => {
                        const serverLogo = document.getElementById("server-logo");

                    
                        serverLogo.style.display = "flex";
                        document.getElementById('app').style.display = "flex";
                    }, 1);
                    break;
                case "setSettings":
                    const sqlData = event.data.payload;

                    this.settings = {
                        color: sqlData.color,
                        filter: sqlData.filter,
                        particle: sqlData.particle,
                        streamer: sqlData.streamerMode,
                        mutedMusic: sqlData.mutedMusic,
                        focus: sqlData.focusPlayer,
                        clear: sqlData.clearMode,
                        animations: sqlData.animations,
                        coords: sqlData.coords,
                        time: sqlData.time,
                        weather: sqlData.weather,
                    }
                    break;
                case "checkNui":
                    postNUI("checkNui", JSON.stringify({}));
                    break;
                default:
                    break;
            };
        },
        keyHandler(e) {
            if (this.isPhotoMode && e.which == 27) {
                this.isPhotoMode = false;

                const leftSide = document.getElementById("left-side");
                const rightSide = document.getElementById("right-side");

                leftSide.classList.remove('animation-left-out');
                rightSide.classList.remove('animation-right-out');

                leftSide.classList.add('animation-left-in');
                rightSide.classList.add('animation-right-in');
                return;
            }
        },
        selectCharacter(index) {
            if (this.activeIndex === index) return;
            
            this.activeCharacter = this.characters[index];
            this.activeIndex = index;
            postNUI("selectCharacter", this.activeCharacter);
        },
        playCharacter() {
            if (this.activeCharacter.cid === null) return;

            this.isPressedPlay = true;

            if (this.use0ResmonSpawn) {
                let leftSide = document.getElementById("left-side");
                let rightSide = document.getElementById("right-side");

                // Remove the animation classes
                leftSide.classList.remove('animation-left-in');
                rightSide.classList.remove('animation-right-in');

                // Add the animation classes
                leftSide.classList.add('animation-left-out');
                rightSide.classList.add('animation-right-out');

                // Hide the left and right side
                setTimeout(() => {
                    document.getElementById("left-side").style.display = "none";
                    document.getElementById("right-side").style.display = "none";

                    // Stop the background music
                    if (this.settings.music.Status) {
                        this.bgSound.pause();
                        this.bgSound.currentTime = 0;
                        this.bgSound = null;
                    }

                    // Set the spawn location
                    postNUI("playCharacter", { position: this.activeCharacter.position });

                    this.pages.multicharacter = false;
                    this.registerContext = {
                        cid: null,
                        firstname: null,
                        lastname: null,
                        heigth: null,
                        birthdate: null,
                    };

                    this.isNewCharacter = false;
                }, 1500);
            } else {
                let leftSide = document.getElementById("left-side");
                let rightSide = document.getElementById("right-side");

                // Remove the animation classes
                leftSide.classList.remove('animation-left-in');
                rightSide.classList.remove('animation-right-in');

                // Add the animation classes
                leftSide.classList.add('animation-left-out');
                rightSide.classList.add('animation-right-out');

                // Hide the left and right side
                setTimeout(() => {
                    document.getElementById("left-side").style.display = "none";
                    document.getElementById("right-side").style.display = "none";

                    // Stop the background music
                    if (this.settings.music.Status) {
                        this.bgSound.pause();
                        this.bgSound.currentTime = 0;
                        this.bgSound = null;
                    }

                    // Set the spawn location
                    this.isLoading = true;

                    setTimeout(() => {
                        const topBg = document.getElementById("top-bg");
                        const bottomBg = document.getElementById("bottom-bg");

                        const serverLogo = document.getElementById("server-logo");

                        serverLogo.style.display = "none";

                        if (this.settings.animations.entrance === "top-and-bottom") {
                            topBg.classList.remove('animation-top-out');
                            bottomBg.classList.remove('animation-bottom-out');
                            topBg.classList.add('animation-top-in');
                            bottomBg.classList.add('animation-bottom-in');
                        } else if (this.settings.animations.entrance === "left-and-right") {
                            topBg.classList.remove('animation-left-out');
                            bottomBg.classList.remove('animation-right-out');
                            topBg.classList.add('animation-left-in');
                            bottomBg.classList.add('animation-right-in');
                        };

                        setTimeout(() => {
                            postNUI("playCharacter", this.activeCharacter);

                            this.pages = {
                                multicharacter: false,
                                spawnSelector: false,
                            }

                            this.registerContext = {
                                cid: null,
                                firstname: null,
                                lastname: null,
                                heigth: null,
                                birthdate: null,
                            };

                            this.isNewCharacter = false;

                            setTimeout(() => {
                                document.getElementById('app').style.display = "none";
                            }, 500);
                        }, 1000);
                    }, 1);
                }, 1500);
            }
        },
        async deleteCharacter(value) {
            if (!this.isDeletable) return;

            if (value) {
                const response = await postNUI("deleteCharacter", this.activeCharacter);

                if (response) {
                    this.characters = this.characters.filter((char) => char.charinfo.cid !== this.activeCharacter.charinfo.cid);
                    if (this.characters.length === 0) {
                        this.isNewCharacter = true;
                        this.activeCharacter = {};
                        this.activeIndex = 0;
                        postNUI("newCharacter");
                        postNUI("newCharacterCam", true);

                        this.popups.delete = false;
                    } else {
                        this.activeCharacter = this.characters[0];
                        this.activeIndex = 0;
                        postNUI("selectCharacter", this.activeCharacter);
                    }
                }   
            } else {
                if (this.popups.delete) {
                    this.popups.delete = false;
                } else {
                    this.popups.delete = true;
                }
            }
          
        },
        formatCash(value) {
            return new Intl.NumberFormat(this.location, {
                style: "currency",
                currency: this.currency,
                maximumSignificantDigits: Math.trunc(Math.abs(value)).toFixed().length,
            }).format(value);
        },
        toggleKeyInput(value) {
            this.popups.key = value ?? !this.popups.key;

            if (!this.popups.key) {
                this.slotKey = null;
                document.getElementById("slot-key").value = "";
            }
        },
        toggleSettings() {
            const settingsPage = document.getElementById("settings");

            if (settingsPage.style.display === "none") {
                settingsPage.style.display = "flex";
                settingsPage.classList.add('animation-zoom-in');

                setTimeout(() => {
                    settingsPage.classList.remove('animation-zoom-in');
                }, 500);
            } else {
                settingsPage.classList.add('animation-zoom-out');

                setTimeout(() => {
                    settingsPage.classList.remove('animation-zoom-out');
                    settingsPage.style.display = "none";
                }, 500);
            }
        },
        changeSetting(head, subhead, body) {
            if (subhead) {
                this.settings[head][subhead][body] = !this.settings[head][subhead][body];
                postNUI("changeSetting", { head: head, subhead: subhead, body: body, value: this.settings[head][subhead][body] });
            } else if (body) {
                this.settings[head][body] = !this.settings[head][body];
                if (head === 'filter' && !this.settings[head][body]) {
                    this.settings.filter.type = this.datas.filters[0];
                };
                postNUI("changeSetting", { head: head, subhead: subhead, body: body, value: this.settings[head][body] });
            } else {
                this.settings[head] = !this.settings[head];
                postNUI("changeSetting", { head: head, subhead: subhead, body: body, value: this.settings[head] });
            };
        },
        changeFilter(process) {
            if (process === 'prev') {
                const index = this.datas.filters.indexOf(this.settings.filter.type);

                if (index === 0) {
                    this.settings.filter.type = this.datas.filters[this.datas.filters.length - 1];
                } else {
                    this.settings.filter.type = this.datas.filters[index - 1];
                }
            } else if (process === 'next') {
                const index = this.datas.filters.indexOf(this.settings.filter.type);

                if (index === this.datas.filters.length - 1) {
                    this.settings.filter.type = this.datas.filters[0];
                } else {
                    this.settings.filter.type = this.datas.filters[index + 1];
                }
            }

            postNUI("changeFilter", this.settings.filter.type);
        },
        changeParticle(process) {
            if (process === 'prev') {
                const index = this.datas.effects.indexOf(this.settings.particle.type);

                if (index === 0) {
                    this.settings.particle.type = this.datas.effects[this.datas.effects.length - 1];
                } else {
                    this.settings.particle.type = this.datas.effects[index - 1];
                }
            } else if (process === 'next') {
                const index = this.datas.effects.indexOf(this.settings.particle.type);

                if (index === this.datas.effects.length - 1) {
                    this.settings.particle.type = this.datas.effects[0];
                } else {
                    this.settings.particle.type = this.datas.effects[index + 1];
                }
            }

            postNUI("changeParticle", this.settings.particle.type);
        },
        changeAnimation(process) {
            if (process === 'prev') {
                const index = this.datas.animations.indexOf(this.settings.animations.single);

                if (index === 0) {
                    this.settings.animations.single = this.datas.animations[this.datas.animations.length - 1];
                } else {
                    this.settings.animations.single = this.datas.animations[index - 1];
                }
            } else if (process === 'next') {
                const index = this.datas.animations.indexOf(this.settings.animations.single);

                if (index === this.datas.animations.length - 1) {
                    this.settings.animations.single = this.datas.animations[0];
                } else {
                    this.settings.animations.single = this.datas.animations[index + 1];
                }
            }

            postNUI("changeAnimation", this.settings.animations.single);
        },
        changeScenario(process) {
            if (process === 'prev') {
                const index = this.datas.scenarios.indexOf(this.settings.animations.scenario.single);

                if (index === 0) {
                    this.settings.animations.scenario.single = this.datas.scenarios[this.datas.scenarios.length - 1];
                } else {
                    this.settings.animations.scenario.single = this.datas.scenarios[index - 1];
                }
            } else if (process === 'next') {
                const index = this.datas.scenarios.indexOf(this.settings.animations.scenario.single);

                if (index === this.datas.scenarios.length - 1) {
                    this.settings.animations.scenario.single = this.datas.scenarios[0];
                } else {
                    this.settings.animations.scenario.single = this.datas.scenarios[index + 1];
                }
            }

            postNUI("changeScenario", this.settings.animations.scenario.single);
        },
        changeStreamerMode() {
            this.settings.streamer = !this.settings.streamer;
            postNUI("changeStreamerMode", this.settings.streamer);
        },
        changeMuteMusic() {
            this.settings.mutedMusic = !this.settings.mutedMusic;
            if (this.bgSound) {
                this.bgSound.muted = this.settings.mutedMusic;
            }
            postNUI("changeMuteMusic", this.settings.mutedMusic);
        },
        changeFocusPlayer() {
            this.settings.focus = !this.settings.focus;
            postNUI("changeFocusPlayer", this.settings.focus);
        },
        changeClearMode() {
            this.settings.clear = !this.settings.clear;
            postNUI("changeClearMode", this.settings.clear);
        },
        changeTheme() {
            const index = this.datas.colors.findIndex((a) => a.name === this.settings.color.name);

            if (index === -1 || index === this.datas.colors.length - 1) {
                this.settings.color = this.datas.colors[0];
            } else {
                this.settings.color = this.datas.colors[index + 1];
            }
            
            postNUI("changeTheme", this.settings.color);
        },
        changeBackground(process) {
            if (!this.datas.coords.length) return; // Koordinatlar boşsa işlemi durdur

            const index = this.datas.coords.findIndex((a) => a.id === this.settings.coords.id);
            
            if (index === -1) return; // Geçerli bir koordinat bulunamazsa işlemi durdur
            
            if (process === 'prev') {
                this.settings.coords = index === 0 
                    ? this.datas.coords[this.datas.coords.length - 1] 
                    : this.datas.coords[index - 1];
            } else if (process === 'next') {
                this.settings.coords = index === this.datas.coords.length - 1 
                    ? this.datas.coords[0] 
                    : this.datas.coords[index + 1];
            }

            const leftSide = document.getElementById("left-side");
            const rightSide = document.getElementById("right-side");

            leftSide.classList.remove('animation-left-in');
            rightSide.classList.remove('animation-right-in');
                        
            leftSide.classList.add('animation-left-out');
            rightSide.classList.add('animation-right-out');

            setTimeout(() => {
                document.getElementById("left-side").style.display = "none";
                document.getElementById("right-side").style.display = "none";

                this.isLoading = true;

                setTimeout(() => {
                    const topBg = document.getElementById("top-bg");
                    const bottomBg = document.getElementById("bottom-bg");

                    const serverLogo = document.getElementById("server-logo");

                    serverLogo.style.display = "none";

                    if (this.settings.animations.entrance === "top-and-bottom") {
                        topBg.classList.remove('animation-top-out');
                        bottomBg.classList.remove('animation-bottom-out');
                        topBg.classList.add('animation-top-in');
                        bottomBg.classList.add('animation-bottom-in');
                    } else if (this.settings.animations.entrance === "left-and-right") {
                        topBg.classList.remove('animation-left-out');
                        bottomBg.classList.remove('animation-right-out');
                        topBg.classList.add('animation-left-in');
                        bottomBg.classList.add('animation-right-in');
                    };

                    setTimeout(() => {
                        postNUI("changeBackground", this.settings.coords);

                       setTimeout(() => {
                            const serverLogo = document.getElementById("server-logo");
                            const topBg = document.getElementById("top-bg");
                            const bottomBg = document.getElementById("bottom-bg");
                        
                            serverLogo.classList.add('animation-fade-out');

                            if (this.settings.animations.entrance === "top-and-bottom") {
                                topBg.classList.remove('animation-top-in');
                                bottomBg.classList.remove('animation-bottom-in');
                                topBg.classList.add('animation-top-out');
                                bottomBg.classList.add('animation-bottom-out');
                            } else if (this.settings.animations.entrance === "left-and-right") {
                                topBg.classList.remove('animation-left-in');
                                bottomBg.classList.remove('animation-right-in');
                                topBg.classList.add('animation-left-out');
                                bottomBg.classList.add('animation-right-out');
                            };

                            setTimeout(() => {    
                                this.isLoading = false;
        
                                setTimeout(() => {
                                    const leftSide = document.getElementById("left-side");
                                    const rightSide = document.getElementById("right-side");
        
                                    document.getElementById("left-side").style.display = "flex";
                                    document.getElementById("right-side").style.display = "flex";
                            
                                    leftSide.classList.add('animation-left-in');
                                    rightSide.classList.add('animation-right-in');
                                }, 1);
                            }, 1200);
                       }, 3000)
                    }, 1000);
                }, 1);
            }, 1500);
        },
        togglePhotoMode() {
            this.isPhotoMode = !this.isPhotoMode;

            if (this.isPhotoMode) {
                const leftSide = document.getElementById("left-side");
                const rightSide = document.getElementById("right-side");

                leftSide.classList.remove('animation-left-in');
                rightSide.classList.remove('animation-right-in');

                leftSide.classList.add('animation-left-out');
                rightSide.classList.add('animation-right-out');
            }
        },
        changePage(page) {
            if (page == 'new-character' && this.isNewCharacter) return;
            if (page !== 'new-character' && !this.isNewCharacter) return;

            const leftSide = document.getElementById("left-side");

            leftSide.classList.remove('animation-left-in');
            leftSide.classList.add('animation-left-out');

            setTimeout(() => {

                if (page === 'new-character') {
                    this.isNewCharacter = true
                } else {
                    this.isNewCharacter = false
                }

                console.log(this.isNewCharacter)
                postNUI("newCharacterCam", this.isNewCharacter);

                leftSide.classList.remove('animation-left-out');
                leftSide.classList.add('animation-left-in');
            }, 1500);
        },
        changeWeather(type) {
            if (!this.settings.weather.status) return;

            if (type == 'prev') {
                const index = this.datas.weather.findIndex((a) => a === this.settings.weather.type);

                if (index === 0) {
                    this.settings.weather.type = this.datas.weather[this.datas.weather.length - 1];
                } else {
                    this.settings.weather.type = this.datas.weather[index - 1];
                }

                postNUI("changeWeather", this.settings.weather.type);
            } else if (type == 'next') {
                const index = this.datas.weather.findIndex((a) => a === this.settings.weather.type);

                if (index === this.datas.weather.length - 1) {
                    this.settings.weather.type = this.datas.weather[0];
                } else {
                    this.settings.weather.type = this.datas.weather[index + 1];
                }

                postNUI("changeWeather", this.settings.weather.type);
            }

            const notifyMessage = this.locales.weatherChanged.replace('{weather}', this.settings.weather.type);

            this.addNotification(notifyMessage, 'success');
        },
        changeTime(type) {
            if (!this.settings.time.status) return;

            if (type === 'prev') {
                if (this.settings.time.hour > 0) {
                    this.settings.time.hour -= 1;
                } else {
                    this.settings.time.hour = 23;
                }
            } else if (type === 'next') {
                if (this.settings.time.hour < 23) {
                    this.settings.time.hour += 1;
                } else {
                    this.settings.time.hour = 0;
                }
            }
            
            const notifyMessage = this.locales.timeChanged.replace('{hour}', this.settings.time.hour < 10 ? `0${this.settings.time.hour}` : this.settings.time.hour);

            this.addNotification(notifyMessage, 'success');
            postNUI("changeTime", this.settings.time.hour);
        },
        setGender(gender) {
            if (this.registerContext.gender === gender) return;

            this.registerContext.gender = gender;
        },
        changeNationality(nationalitie) {
            if (this.registerContext.nationality === nationalitie) return;

            this.registerContext.nationality = nationalitie;
            this.selectNationality = false;
        },
        createCharacter() {
            if (!this.registerContext.firstname || !this.registerContext.lastname || !this.registerContext.heigth || !this.registerContext.birthdate || !this.registerContext.gender || !this.registerContext.nationality) {
                this.addNotification(this.locales.emptyFields, 'error');
                return;
            };

            this.registerContext.firstname = this.registerContext.firstname.charAt(0).toUpperCase() + this.registerContext.firstname.slice(1).toLowerCase();
            this.registerContext.lastname = this.registerContext.lastname.charAt(0).toUpperCase() + this.registerContext.lastname.slice(1).toLowerCase();

            var hasProfanity = profList.some(word => this.registerContext.firstname.toLowerCase().includes(word)) || 
                                profList.some(word => this.registerContext.lastname.toLowerCase().includes(word));

            if (hasProfanity) {
                this.addNotification(this.locales.invalidName, 'error');
                return;
            }

            if (Number(this.registerContext.heigth) < 150 || Number(this.registerContext.heigth) > 210) {
                this.addNotification(this.locales.invalidHeight, 'error');
                return;
            }

            const birthdate = new Date(this.registerContext.birthdate);

            if (birthdate.getFullYear() < this.dob.Highest || birthdate.getFullYear() > this.dob.Lowest) {
                this.addNotification(this.dob.Message, 'error');
                return;
            }

            const maxCid = this.characters.length > 0 ? Math.max(...this.characters.map(char => char.cid))+1 : 1;

            this.registerContext.cid = maxCid
            this.registerContext.gender = this.registerContext.gender == 'm' ? 0 : 1;

            document.getElementById('app').style.display = "none";

            this.pages = {
                multicharacter: false,
                spawnSelector: false,
            };

            if (this.settings.music.Status) {
                this.bgSound.pause();
                this.bgSound.currentTime = 0;
                this.bgSound = null;
            }

            postNUI("createCharacter", this.registerContext);

            this.isNewCharacter = false;

            this.registerContext = {
                cid: null,
                firstname: null,
                lastname: null,
                heigth: null,
                birthdate: null,
            };
        },
        formatDate(timestamp) {
            const date = new Date(timestamp);
            const day = date.getDate().toString().padStart(2, "0");
            const month = (date.getMonth() + 1).toString().padStart(2, "0");
            const year = date.getFullYear();
            
            return `${month}-${day}-${year}`;
        },
        async createSlotKey() {
            if (this.slotKey == null || this.slotKey == '') return;

            const response = await postNUI("createSlotKey", this.slotKey);

            if (response) {
                this.characterSlot = response;
                this.isNewSlotKey = false;
                this.slotKey = null;
                document.getElementById("slot-key").value = "";
                this.popups.key = false;
            };
        },
        addNotification(message, type) {
            const id = Date.now();
            this.notifications.push({
                id: id,
                message: message,
                type: type,
            });

            setTimeout(() => {
                this.removeNotification(id);
            }, 5000);
        },
        removeNotification(id) {
            const notificationDiv = document.getElementById(`notification-${id}`);

            if (notificationDiv) {
                notificationDiv.classList.remove('notify-in');
                notificationDiv.classList.add('notify-out');

                setTimeout(() => {
                    this.notifications = (this.notifications || {}).length
                        ? this.notifications.filter((notification) => notification.id !== id)
                        : {};
                }, 400);
            } else {
                console.log('Not found div!')
            }
        },
        selectLocation(location) {
            if (location == 'last-location') {
                this.selectedLocation = 'last-location';
            } else {
                this.selectedLocation = location;
            }

            postNUI("selectLocation", {
                type: location == 'last-location' ? 'last-location' : 'custom',
                location: location.coords,
                playerLastLocation: this.activeCharacter.position
            });
        },
        spawnLocation() {
            postNUI("spawnLocation", {
                type: this.selectedLocation == 'last-location' ? 'last-location' : 'custom',
                character: this.activeCharacter,
                location: this.selectedLocation.coords,
                playerLastLocation: this.activeCharacter.position
            });

            this.pages = {
                multicharacter: false,
                spawnSelector: false,
            };

            document.getElementById('app').style.display = "none";
        }
    },
    computed: {
        filteredNationalities() {
            if (!this.searchedNationalities) return this.nationalities;
    
            return this.nationalities.filter((nationality) => {
                return nationality.toLowerCase().includes(this.searchedNationalities.toLowerCase());
            });
        }
    },
    mounted() {
        window.addEventListener("message", this.messageHandler);
        window.addEventListener("keyup", this.keyHandler);
    },
    beforeDestroy() {
        window.removeEventListener("message", this.messageHandler);
        window.removeEventListener("keyup", this.keyHandler);
    },
});


app.mount("#app");

var resourceName = window.GetParentResourceName ?  window.GetParentResourceName() : "0r-multicharacterv2";

window.postNUI = async (name, data) => {
    try {
        const response = await fetch(`https://${resourceName}/${name}`, {
            method: "POST",
            mode: "cors",
            cache: "no-cache",
            credentials: "same-origin",
            headers: {
                "Content-Type": "application/json"
            },
            redirect: "follow",
            referrerPolicy: "no-referrer",
            body: JSON.stringify(data)
        });

        return await response.json()
    } catch (error) {
        return null;
    }
};