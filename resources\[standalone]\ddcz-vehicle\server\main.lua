-- ================================================
-- DDCZ-VEHICLE Server Script
-- ================================================

local QBCore = exports['qb-core']:GetCoreObject()

-- ================================================
-- Vehicle Synchronization Events
-- ================================================

RegisterNetEvent('ddcz-vehicle:server:syncDoor', function(vehicleNetId, doorId, open)
    local src = source
    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    
    if not DoesEntityExist(vehicle) then return end
    
    if Config.Debug then
        print('[DDCZ-VEHICLE] Syncing door ' .. doorId .. ' for vehicle ' .. vehicleNetId)
    end
    
    -- Sync to all other players
    TriggerClientEvent('ddcz-vehicle:client:syncDoor', -1, vehicleNetId, doorId, open, src)
end)

RegisterNetEvent('ddcz-vehicle:server:syncEngine', function(vehicleNetId, engineOn)
    local src = source
    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    
    if not DoesEntityExist(vehicle) then return end
    
    if Config.Debug then
        print('[DDCZ-VEHICLE] Syncing engine state for vehicle ' .. vehicleNetId)
    end
    
    -- Sync to all other players
    TriggerClientEvent('ddcz-vehicle:client:syncEngine', -1, vehicleNetId, engineOn, src)
end)

RegisterNetEvent('ddcz-vehicle:server:syncLocks', function(vehicleNetId, locked)
    local src = source
    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    
    if not DoesEntityExist(vehicle) then return end
    
    if Config.Debug then
        print('[DDCZ-VEHICLE] Syncing locks for vehicle ' .. vehicleNetId)
    end
    
    -- Sync to all other players
    TriggerClientEvent('ddcz-vehicle:client:syncLocks', -1, vehicleNetId, locked, src)
end)

RegisterNetEvent('ddcz-vehicle:server:syncExtra', function(vehicleNetId, extraId, enabled)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    
    if not DoesEntityExist(vehicle) or not Player then return end
    
    -- Check if player has permission for extras
    local hasPermission = false
    for _, job in pairs(Config.RestrictedJobs) do
        if Player.PlayerData.job.name == job then
            hasPermission = true
            break
        end
    end
    
    if not hasPermission then
        if Config.Debug then
            print('[DDCZ-VEHICLE] Player ' .. src .. ' tried to use extras without permission')
        end
        return
    end
    
    if Config.Debug then
        print('[DDCZ-VEHICLE] Syncing extra ' .. extraId .. ' for vehicle ' .. vehicleNetId)
    end
    
    -- Sync to all other players
    TriggerClientEvent('ddcz-vehicle:client:syncExtra', -1, vehicleNetId, extraId, enabled, src)
end)

RegisterNetEvent('ddcz-vehicle:server:syncLivery', function(vehicleNetId, livery)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    
    if not DoesEntityExist(vehicle) or not Player then return end
    
    -- Check if player has permission for livery
    local hasPermission = false
    for _, job in pairs(Config.RestrictedJobs) do
        if Player.PlayerData.job.name == job then
            hasPermission = true
            break
        end
    end
    
    if not hasPermission then
        if Config.Debug then
            print('[DDCZ-VEHICLE] Player ' .. src .. ' tried to change livery without permission')
        end
        return
    end
    
    if Config.Debug then
        print('[DDCZ-VEHICLE] Syncing livery ' .. livery .. ' for vehicle ' .. vehicleNetId)
    end
    
    -- Sync to all other players
    TriggerClientEvent('ddcz-vehicle:client:syncLivery', -1, vehicleNetId, livery, src)
end)

-- ================================================
-- Admin Commands
-- ================================================

RegisterCommand('vehicleadmin', function(source, args, rawCommand)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    
    if not Player then return end
    
    -- Check if player is admin
    if Player.PlayerData.job.name ~= 'admin' and not QBCore.Functions.HasPermission(src, 'admin') then
        TriggerClientEvent('QBCore:Notify', src, 'Nemáte oprávnění!', 'error')
        return
    end
    
    if args[1] == 'reload' then
        -- Reload config for all players
        TriggerClientEvent('ddcz-vehicle:client:reloadConfig', -1)
        TriggerClientEvent('QBCore:Notify', src, 'Vehicle config reloadován!', 'success')
        
    elseif args[1] == 'reset' and args[2] then
        local targetId = tonumber(args[2])
        if targetId then
            -- Force close vehicle menu for target player
            TriggerClientEvent('ddcz-vehicle:client:forceClose', targetId)
            TriggerClientEvent('QBCore:Notify', src, 'Vehicle menu resetováno pro hráče ID: ' .. targetId, 'success')
        else
            TriggerClientEvent('QBCore:Notify', src, 'Neplatné ID hráče!', 'error')
        end
        
    else
        TriggerClientEvent('QBCore:Notify', src, 'Použití: /vehicleadmin [reload/reset] [playerid]', 'primary')
    end
end, true)

-- ================================================
-- Utility Functions
-- ================================================

-- Check if player has restricted job access
function HasRestrictedAccess(source)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return false end
    
    for _, job in pairs(Config.RestrictedJobs) do
        if Player.PlayerData.job.name == job then
            return true
        end
    end
    
    return false
end

-- Export for other resources
exports('HasRestrictedAccess', HasRestrictedAccess)

-- ================================================
-- Resource Events
-- ================================================

AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        print('[DDCZ-VEHICLE] Server started successfully!')
        
        -- Send config to all players
        CreateThread(function()
            Wait(2000) -- Wait for players to load
            TriggerClientEvent('ddcz-vehicle:client:receiveConfig', -1, Config)
        end)
    end
end)

AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        print('[DDCZ-VEHICLE] Server stopped!')
        
        -- Close all open menus
        TriggerClientEvent('ddcz-vehicle:client:forceClose', -1)
    end
end)

-- ================================================
-- Player Events
-- ================================================

RegisterNetEvent('QBCore:Server:OnPlayerLoaded', function()
    local src = source
    
    -- Send config to newly loaded player
    TriggerClientEvent('ddcz-vehicle:client:receiveConfig', src, Config)
end)

-- ================================================
-- Vehicle Persistence (Optional)
-- ================================================

-- You can add database integration here to save vehicle states
-- This would require creating a database table and implementing
-- save/load functions for vehicle modifications

--[[
Example database structure:

CREATE TABLE IF NOT EXISTS `vehicle_modifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `plate` varchar(8) NOT NULL,
  `modifications` longtext DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `plate` (`plate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

RegisterNetEvent('ddcz-vehicle:server:saveVehicleMods', function(plate, modifications)
    local src = source
    
    MySQL.Async.execute('INSERT INTO vehicle_modifications (plate, modifications) VALUES (@plate, @mods) ON DUPLICATE KEY UPDATE modifications = @mods', {
        ['@plate'] = plate,
        ['@mods'] = json.encode(modifications)
    }, function(affectedRows)
        if affectedRows > 0 then
            TriggerClientEvent('QBCore:Notify', src, 'Úpravy vozidla uloženy!', 'success')
        end
    end)
end)

RegisterNetEvent('ddcz-vehicle:server:loadVehicleMods', function(plate)
    local src = source
    
    MySQL.Async.fetchScalar('SELECT modifications FROM vehicle_modifications WHERE plate = @plate', {
        ['@plate'] = plate
    }, function(result)
        if result then
            local modifications = json.decode(result)
            TriggerClientEvent('ddcz-vehicle:client:applyMods', src, modifications)
        end
    end)
end)
--]]

-- ================================================
-- Debug Commands
-- ================================================

if Config.Debug then
    RegisterCommand('vehicledebug', function(source, args, rawCommand)
        local src = source
        
        if args[1] == 'info' then
            local Player = QBCore.Functions.GetPlayer(src)
            if Player then
                print('[DDCZ-VEHICLE DEBUG] Player: ' .. src .. ', Job: ' .. Player.PlayerData.job.name)
                print('[DDCZ-VEHICLE DEBUG] Has Restricted Access: ' .. tostring(HasRestrictedAccess(src)))
            end
            
        elseif args[1] == 'config' then
            print('[DDCZ-VEHICLE DEBUG] Config:')
            print(json.encode(Config, {indent = true}))
        end
    end, true)
end
