{"name": "webpack", "version": "4.28.4", "author": "<PERSON> @sokra", "description": "Packs CommonJs/AMD modules for the browser. Allows to split your codebase into multiple bundles, which can be loaded on demand. Support loaders to preprocess files, i.e. json, jsx, es7, css, less, ... and your custom stuff.", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/helper-module-context": "1.7.11", "@webassemblyjs/wasm-edit": "1.7.11", "@webassemblyjs/wasm-parser": "1.7.11", "acorn": "^5.6.2", "acorn-dynamic-import": "^3.0.0", "ajv": "^6.1.0", "ajv-keywords": "^3.1.0", "chrome-trace-event": "^1.0.0", "enhanced-resolve": "^4.1.0", "eslint-scope": "^4.0.0", "json-parse-better-errors": "^1.0.2", "loader-runner": "^2.3.0", "loader-utils": "^1.1.0", "memory-fs": "~0.4.1", "micromatch": "^3.1.8", "mkdirp": "~0.5.0", "neo-async": "^2.5.0", "node-libs-browser": "^2.0.0", "schema-utils": "^0.4.4", "tapable": "^1.1.0", "terser-webpack-plugin": "^1.1.0", "watchpack": "^1.5.0", "webpack-sources": "^1.3.0"}, "devDependencies": {"@types/node": "^9.6.4", "@types/tapable": "^1.0.1", "@types/webpack-sources": "^0.1.4", "benchmark": "^2.1.1", "bundle-loader": "~0.5.0", "codacy-coverage": "^2.0.1", "coffee-loader": "^0.9.0", "coffeescript": "^1.10.0", "coveralls": "^3.0.2", "css-loader": "^0.28.3", "es6-promise-polyfill": "^1.1.1", "eslint": "^5.8.0", "eslint-config-prettier": "^3.1.0", "eslint-plugin-jest": "^21.26.2", "eslint-plugin-node": "^8.0.0", "eslint-plugin-prettier": "^3.0.0", "express": "~4.13.1", "file-loader": "^1.1.6", "glob": "^7.1.3", "husky": "^1.1.3", "i18n-webpack-plugin": "^1.0.0", "istanbul": "^0.4.5", "jade": "^1.11.0", "jade-loader": "~0.8.0", "jest": "24.0.0-alpha.1", "jest-junit": "^5.2.0", "json-loader": "^0.5.7", "json-schema-to-typescript": "^6.0.1", "less": "^2.5.1", "less-loader": "^4.0.3", "lint-staged": "^8.0.4", "lodash": "^4.17.4", "prettier": "^1.14.3", "pug": "^2.0.3", "pug-loader": "^2.4.0", "raw-loader": "~0.5.0", "react": "^15.2.1", "react-dom": "^15.2.1", "rimraf": "^2.6.2", "script-loader": "~0.7.0", "simple-git": "^1.65.0", "style-loader": "^0.19.1", "typescript": "^3.0.0-rc", "url-loader": "^0.6.2", "val-loader": "^1.0.2", "vm-browserify": "~0.0.0", "wast-loader": "^1.5.5", "webpack-dev-middleware": "^1.9.0", "worker-loader": "^1.1.1", "xxhashjs": "^0.2.1"}, "engines": {"node": ">=6.11.5"}, "repository": {"type": "git", "url": "https://github.com/webpack/webpack.git"}, "homepage": "https://github.com/webpack/webpack", "main": "lib/webpack.js", "web": "lib/webpack.web.js", "bin": "./bin/webpack.js", "files": ["lib/", "bin/", "buildin/", "declarations/", "hot/", "web_modules/", "schemas/", "SECURITY.md"], "scripts": {"setup": "node ./setup/setup.js", "test": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest", "test:update-snapshots": "yarn jest -u", "test:integration": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.test.js\"", "test:basic": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/{TestCasesNormal,StatsTestCases,ConfigTestCases}.test.js\"", "test:unit": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.unittest.js\"", "travis:integration": "yarn cover:init && yarn cover:integration --ci $JEST", "travis:basic": "yarn test:basic --ci $JEST", "travis:lint-unit": "yarn lint && yarn cover:init && yarn cover:unit --ci $JEST", "travis:benchmark": "yarn benchmark --ci", "appveyor:integration": "yarn cover:init && yarn cover:integration --ci %JEST%", "appveyor:unit": "yarn cover:init && yarn cover:unit --ci %JEST%", "appveyor:benchmark": "yarn benchmark --ci", "build:examples": "cd examples && node buildAll.js", "pretest": "yarn lint", "prelint": "yarn setup", "lint": "yarn code-lint && yarn schema-lint && yarn type-lint && yarn special-lint", "code-lint": "eslint --cache \"{setup,lib,bin,hot,buildin,benchmark,tooling,schemas}/**/*.js\" \"test/*.js\" \"test/{configCases,watchCases,statsCases,hotCases}/**/webpack.config.js\" \"examples/**/webpack.config.js\"", "type-lint": "tsc --pretty", "special-lint": "node tooling/inherit-types && node tooling/format-schemas && node tooling/compile-to-definitions", "special-lint-fix": "node tooling/inherit-types --write --override && node tooling/format-schemas --write && node tooling/compile-to-definitions --write", "fix": "yarn code-lint --fix && yarn special-lint-fix", "pretty": "prettier --loglevel warn --write \"*.{ts,js,json,yml,yaml}\" \"{setup,lib,bin,hot,buildin,benchmark,tooling,schemas}/**/*.{js,json}\" \"test/*.js\" \"test/{configCases,watchCases,statsCases,hotCases}/**/webpack.config.js\" \"examples/**/webpack.config.js\"", "schema-lint": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.lint.js\" --no-verbose", "benchmark": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.benchmark.js\" --runInBand", "cover": "yarn cover:init && yarn cover:all && yarn cover:report", "cover:init": "rimraf coverage", "cover:all": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --coverage", "cover:integration": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.test.js\" --coverage", "cover:unit": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.unittest.js\" --coverage", "cover:report": "istanbul report"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js|{lib,setup,bin,hot,buildin,tooling,schemas}/**/*.js|test/*.js|{test,examples}/**/webpack.config.js}": ["eslint --cache"]}, "jest": {"forceExit": true, "setupFilesAfterEnv": ["<rootDir>/test/setupTestFramework.js"], "testMatch": ["<rootDir>/test/*.test.js", "<rootDir>/test/*.unittest.js"], "watchPathIgnorePatterns": ["<rootDir>/.git", "<rootDir>/node_modules", "<rootDir>/test/js", "<rootDir>/test/browsertest/js", "<rootDir>/test/fixtures/temp-cache-fixture", "<rootDir>/test/fixtures/temp-", "<rootDir>/benchmark", "<rootDir>/examples/*/dist", "<rootDir>/coverage", "<rootDir>/.eslintcache"], "modulePathIgnorePatterns": ["<rootDir>/.git", "<rootDir>/node_modules/webpack/node_modules", "<rootDir>/test/js", "<rootDir>/test/browsertest/js", "<rootDir>/test/fixtures/temp-cache-fixture", "<rootDir>/test/fixtures/temp-", "<rootDir>/benchmark", "<rootDir>/examples/*/dist", "<rootDir>/coverage", "<rootDir>/.eslintcache"], "transformIgnorePatterns": ["<rootDir>"], "coverageDirectory": "<rootDir>/coverage", "coveragePathIgnorePatterns": ["\\.runtime\\.js$", "<rootDir>/test", "<rootDir>/schemas", "<rootDir>/node_modules"], "testEnvironment": "node", "coverageReporters": ["json"]}}