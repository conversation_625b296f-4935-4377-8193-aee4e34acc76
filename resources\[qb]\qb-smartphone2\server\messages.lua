local QBCore = exports['qb-core']:GetCoreObject()

-- Callback pro získání konverzací
QBCore.Functions.CreateCallback('qb-smartphone2:server:getConversations', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb({}) return end
    
    local phoneNumber = Player.PlayerData.charinfo.phone
    
    MySQL.Async.fetchAll([[
        SELECT 
            CASE 
                WHEN sender = ? THEN receiver 
                ELSE sender 
            END as contact_number,
            MAX(created_at) as last_message_time,
            COUNT(CASE WHEN receiver = ? AND read_status = 0 THEN 1 END) as unread_count
        FROM phone_messages 
        WHERE sender = ? OR receiver = ? 
        GROUP BY contact_number 
        ORDER BY last_message_time DESC
    ]], {phoneNumber, phoneNumber, phoneNumber, phoneNumber}, function(result)
        cb(result or {})
    end)
end)

-- Callback pro získání zpráv v konverzaci
QBCore.Functions.CreateCallback('qb-smartphone2:server:getMessages', function(source, cb, contactNumber)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb({}) return end
    
    local phoneNumber = Player.PlayerData.charinfo.phone
    
    MySQL.Async.fetchAll([[
        SELECT * FROM phone_messages 
        WHERE (sender = ? AND receiver = ?) OR (sender = ? AND receiver = ?) 
        ORDER BY created_at ASC 
        LIMIT ?
    ]], {phoneNumber, contactNumber, contactNumber, phoneNumber, Config.MaxMessages}, function(result)
        cb(result or {})
    end)
end)

-- Event pro odeslání zprávy
RegisterNetEvent('qb-smartphone2:server:sendMessage', function(messageData)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local senderNumber = Player.PlayerData.charinfo.phone
    local receiverNumber = messageData.receiver
    local message = messageData.message
    local attachments = messageData.attachments or nil
    
    -- Kontrola, zda příjemce existuje
    local ReceiverPlayer = QBCore.Functions.GetPlayerByPhone(receiverNumber)
    if not ReceiverPlayer then
        TriggerClientEvent('QBCore:Notify', src, 'Číslo neexistuje!', 'error')
        return
    end
    
    -- Kontrola, zda není odesílatel blokovaný
    QBCore.Functions.TriggerCallback('qb-smartphone2:server:isNumberBlocked', function(isBlocked)
        if isBlocked then
            TriggerClientEvent('QBCore:Notify', src, 'Zpráva nebyla doručena', 'error')
            return
        end
        
        -- Uložení zprávy do databáze
        MySQL.Async.execute('INSERT INTO phone_messages (sender, receiver, message, attachments) VALUES (?, ?, ?, ?)', {
            senderNumber,
            receiverNumber,
            message,
            json.encode(attachments)
        }, function(insertId)
            if insertId then
                -- Odeslání zprávy odesílateli
                TriggerClientEvent('qb-smartphone2:client:receiveMessage', src, {
                    id = insertId,
                    sender = senderNumber,
                    receiver = receiverNumber,
                    message = message,
                    attachments = attachments,
                    read_status = 1,
                    created_at = os.date('%Y-%m-%d %H:%M:%S')
                })
                
                -- Odeslání zprávy příjemci
                local receiverSrc = ReceiverPlayer.PlayerData.source
                TriggerClientEvent('qb-smartphone2:client:receiveMessage', receiverSrc, {
                    id = insertId,
                    sender = senderNumber,
                    receiver = receiverNumber,
                    message = message,
                    attachments = attachments,
                    read_status = 0,
                    created_at = os.date('%Y-%m-%d %H:%M:%S')
                })
                
                -- Odeslání notifikace příjemci
                local senderName = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname
                
                -- Zkus najít kontakt pro zobrazení jména
                QBCore.Functions.TriggerCallback('qb-smartphone2:server:getContactByNumber', function(contact)
                    local displayName = contact and contact.name or senderName
                    
                    exports['qb-smartphone2']:SendNotification(receiverNumber, 'Nová zpráva', message, 'messages', {
                        sender = senderNumber,
                        senderName = displayName
                    })
                end, receiverSrc, senderNumber)
                
                TriggerClientEvent('QBCore:Notify', src, 'Zpráva odeslána!', 'success')
            else
                TriggerClientEvent('QBCore:Notify', src, 'Chyba při odesílání zprávy!', 'error')
            end
        end)
    end, ReceiverPlayer.PlayerData.source, senderNumber)
end)

-- Event pro označení zpráv jako přečtených
RegisterNetEvent('qb-smartphone2:server:markMessagesRead', function(contactNumber)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local phoneNumber = Player.PlayerData.charinfo.phone
    
    MySQL.Async.execute('UPDATE phone_messages SET read_status = 1 WHERE sender = ? AND receiver = ?', {
        contactNumber, phoneNumber
    }, function(affectedRows)
        if affectedRows > 0 then
            TriggerClientEvent('qb-smartphone2:client:messagesMarkedRead', src, contactNumber)
        end
    end)
end)

-- Event pro smazání zprávy
RegisterNetEvent('qb-smartphone2:server:deleteMessage', function(messageId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local phoneNumber = Player.PlayerData.charinfo.phone
    
    -- Kontrola vlastnictví zprávy
    MySQL.Async.fetchAll('SELECT * FROM phone_messages WHERE id = ? AND (sender = ? OR receiver = ?)', {
        messageId, phoneNumber, phoneNumber
    }, function(result)
        if not result[1] then
            TriggerClientEvent('QBCore:Notify', src, 'Zpráva nenalezena!', 'error')
            return
        end
        
        -- Smazání zprávy
        MySQL.Async.execute('DELETE FROM phone_messages WHERE id = ?', {messageId}, function(affectedRows)
            if affectedRows > 0 then
                TriggerClientEvent('QBCore:Notify', src, 'Zpráva byla smazána!', 'success')
                TriggerClientEvent('qb-smartphone2:client:messageDeleted', src, messageId)
            else
                TriggerClientEvent('QBCore:Notify', src, 'Chyba při mazání zprávy!', 'error')
            end
        end)
    end)
end)

-- Event pro smazání celé konverzace
RegisterNetEvent('qb-smartphone2:server:deleteConversation', function(contactNumber)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local phoneNumber = Player.PlayerData.charinfo.phone
    
    MySQL.Async.execute('DELETE FROM phone_messages WHERE (sender = ? AND receiver = ?) OR (sender = ? AND receiver = ?)', {
        phoneNumber, contactNumber, contactNumber, phoneNumber
    }, function(affectedRows)
        if affectedRows > 0 then
            TriggerClientEvent('QBCore:Notify', src, 'Konverzace byla smazána!', 'success')
            TriggerClientEvent('qb-smartphone2:client:conversationDeleted', src, contactNumber)
        else
            TriggerClientEvent('QBCore:Notify', src, 'Chyba při mazání konverzace!', 'error')
        end
    end)
end)

-- Callback pro vyhledání zpráv
QBCore.Functions.CreateCallback('qb-smartphone2:server:searchMessages', function(source, cb, searchTerm)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb({}) return end
    
    local phoneNumber = Player.PlayerData.charinfo.phone
    
    MySQL.Async.fetchAll([[
        SELECT * FROM phone_messages 
        WHERE (sender = ? OR receiver = ?) AND message LIKE ? 
        ORDER BY created_at DESC 
        LIMIT 50
    ]], {phoneNumber, phoneNumber, '%' .. searchTerm .. '%'}, function(result)
        cb(result or {})
    end)
end)

-- Callback pro získání statistik zpráv
QBCore.Functions.CreateCallback('qb-smartphone2:server:getMessageStats', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb({}) return end
    
    local phoneNumber = Player.PlayerData.charinfo.phone
    
    MySQL.Async.fetchAll([[
        SELECT 
            COUNT(*) as total_messages,
            COUNT(CASE WHEN sender = ? THEN 1 END) as sent_messages,
            COUNT(CASE WHEN receiver = ? THEN 1 END) as received_messages,
            COUNT(CASE WHEN receiver = ? AND read_status = 0 THEN 1 END) as unread_messages
        FROM phone_messages 
        WHERE sender = ? OR receiver = ?
    ]], {phoneNumber, phoneNumber, phoneNumber, phoneNumber, phoneNumber}, function(result)
        cb(result[1] or {})
    end)
end)

-- Event pro hromadné smazání zpráv
RegisterNetEvent('qb-smartphone2:server:bulkDeleteMessages', function(messageIds)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local phoneNumber = Player.PlayerData.charinfo.phone
    
    if #messageIds == 0 then
        TriggerClientEvent('QBCore:Notify', src, 'Žádné zprávy k smazání!', 'error')
        return
    end
    
    local placeholders = string.rep('?,', #messageIds):sub(1, -2)
    local query = string.format('DELETE FROM phone_messages WHERE id IN (%s) AND (sender = ? OR receiver = ?)', placeholders)
    
    local params = {}
    for _, id in ipairs(messageIds) do
        table.insert(params, id)
    end
    table.insert(params, phoneNumber)
    table.insert(params, phoneNumber)
    
    MySQL.Async.execute(query, params, function(affectedRows)
        if affectedRows > 0 then
            TriggerClientEvent('QBCore:Notify', src, 'Smazáno ' .. affectedRows .. ' zpráv!', 'success')
            TriggerClientEvent('qb-smartphone2:client:messagesDeleted', src, messageIds)
        else
            TriggerClientEvent('QBCore:Notify', src, 'Chyba při mazání zpráv!', 'error')
        end
    end)
end)

-- Export funkce pro jiné scripty
exports('SendMessage', function(senderNumber, receiverNumber, message, attachments)
    local SenderPlayer = QBCore.Functions.GetPlayerByPhone(senderNumber)
    local ReceiverPlayer = QBCore.Functions.GetPlayerByPhone(receiverNumber)
    
    if not ReceiverPlayer then return false end
    
    MySQL.Async.execute('INSERT INTO phone_messages (sender, receiver, message, attachments) VALUES (?, ?, ?, ?)', {
        senderNumber,
        receiverNumber,
        message,
        json.encode(attachments or {})
    }, function(insertId)
        if insertId then
            local messageData = {
                id = insertId,
                sender = senderNumber,
                receiver = receiverNumber,
                message = message,
                attachments = attachments,
                read_status = 0,
                created_at = os.date('%Y-%m-%d %H:%M:%S')
            }
            
            -- Odeslání zprávy příjemci
            local receiverSrc = ReceiverPlayer.PlayerData.source
            TriggerClientEvent('qb-smartphone2:client:receiveMessage', receiverSrc, messageData)
            
            -- Odeslání notifikace
            exports['qb-smartphone2']:SendNotification(receiverNumber, 'Nová zpráva', message, 'messages', {
                sender = senderNumber
            })
            
            -- Pokud je odesílatel online, pošli mu také zprávu
            if SenderPlayer then
                messageData.read_status = 1
                TriggerClientEvent('qb-smartphone2:client:receiveMessage', SenderPlayer.PlayerData.source, messageData)
            end
        end
    end)
    
    return true
end)

exports('GetPlayerMessages', function(source, contactNumber)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return {} end
    
    local phoneNumber = Player.PlayerData.charinfo.phone
    local p = promise.new()
    
    MySQL.Async.fetchAll([[
        SELECT * FROM phone_messages 
        WHERE (sender = ? AND receiver = ?) OR (sender = ? AND receiver = ?) 
        ORDER BY created_at ASC
    ]], {phoneNumber, contactNumber, contactNumber, phoneNumber}, function(result)
        p:resolve(result or {})
    end)
    
    return Citizen.Await(p)
end)
