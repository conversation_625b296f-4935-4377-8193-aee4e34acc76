{"manifest": {"name": "cacache", "publishConfig": {"tag": "legacy"}, "version": "12.0.4", "cache-version": {"content": "2", "index": "5"}, "description": "Fast, fault-tolerant, cross-platform, disk-based, data-agnostic, content-addressable cache.", "main": "index.js", "files": ["*.js", "lib", "locales"], "scripts": {"benchmarks": "node test/benchmarks", "prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "pretest": "standard", "release": "standard-version -s", "test": "cross-env CACACHE_UPDATE_LOCALE_FILES=true tap --coverage --nyc-arg=--all -J test/*.js", "test-docker": "docker run -it --rm --name pacotest -v \"$PWD\":/tmp -w /tmp node:latest npm test"}, "repository": {"type": "git", "url": "https://github.com/npm/cacache"}, "keywords": ["cache", "caching", "content-addressable", "sri", "sri hash", "subresource integrity", "cache", "storage", "store", "file store", "filesystem", "disk cache", "disk storage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "ISC", "dependencies": {"bluebird": "^3.5.5", "chownr": "^1.1.1", "figgy-pudding": "^3.5.1", "glob": "^7.1.4", "graceful-fs": "^4.1.15", "infer-owner": "^1.0.3", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.3", "ssri": "^6.0.1", "unique-filename": "^1.1.1", "y18n": "^4.0.0"}, "devDependencies": {"benchmark": "^2.1.4", "chalk": "^2.4.2", "cross-env": "^5.1.4", "require-inject": "^1.4.4", "standard": "^12.0.1", "standard-version": "^6.0.1", "tacks": "^1.3.0", "tap": "^12.7.0"}, "config": {"nyc": {"exclude": ["node_modules/**", "test/**"]}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-cacache-12.0.4-668bcbd105aeb5f1d92fe25570ec9525c8faa40c-integrity\\node_modules\\cacache\\package.json", "readmeFilename": "README.es.md", "readme": "# cacache [![npm version](https://img.shields.io/npm/v/cacache.svg)](https://npm.im/cacache) [![license](https://img.shields.io/npm/l/cacache.svg)](https://npm.im/cacache) [![Travis](https://img.shields.io/travis/zkat/cacache.svg)](https://travis-ci.org/zkat/cacache) [![AppVeyor](https://ci.appveyor.com/api/projects/status/github/zkat/cacache?svg=true)](https://ci.appveyor.com/project/zkat/cacache) [![Coverage Status](https://coveralls.io/repos/github/zkat/cacache/badge.svg?branch=latest)](https://coveralls.io/github/zkat/cacache?branch=latest)\n\n[`cacache`](https://github.com/zkat/cacache) es una librería de Node.js para\nmanejar caches locales en disco, con acceso tanto con claves únicas como\ndirecciones de contenido (hashes/hacheos). Es súper rápida, excelente con el\nacceso concurrente, y jamás te dará datos incorrectos, aún si se corrompen o\nmanipulan directamente los ficheros del cache.\n\nEl propósito original era reemplazar el caché local de\n[npm](https://npm.im/npm), pero se puede usar por su propia cuenta.\n\n_Traducciones: [English](README.md)_\n\n## Instalación\n\n`$ npm install --save cacache`\n\n## Índice\n\n* [Ejemplo](#ejemplo)\n* [Características](#características)\n* [Cómo Contribuir](#cómo-contribuir)\n* [API](#api)\n  * [Usando el API en español](#localized-api)\n  * Leer\n    * [`ls`](#ls)\n    * [`ls.flujo`](#ls-stream)\n    * [`saca`](#get-data)\n    * [`saca.flujo`](#get-stream)\n    * [`saca.info`](#get-info)\n    * [`saca.tieneDatos`](#get-hasContent)\n  * Escribir\n    * [`mete`](#put-data)\n    * [`mete.flujo`](#put-stream)\n    * [opciones para `mete*`](#put-options)\n    * [`rm.todo`](#rm-all)\n    * [`rm.entrada`](#rm-entry)\n    * [`rm.datos`](#rm-content)\n  * Utilidades\n    * [`ponLenguaje`](#set-locale)\n    * [`limpiaMemoizado`](#clear-memoized)\n    * [`tmp.hazdir`](#tmp-mkdir)\n    * [`tmp.conTmp`](#with-tmp)\n  * Integridad\n    * [Subresource Integrity](#integrity)\n    * [`verifica`](#verify)\n    * [`verifica.ultimaVez`](#verify-last-run)\n\n### Ejemplo\n\n```javascript\nconst cacache = require('cacache/es')\nconst fs = require('fs')\n\nconst tarbol = '/ruta/a/mi-tar.tgz'\nconst rutaCache = '/tmp/my-toy-cache'\nconst clave = 'mi-clave-única-1234'\n\n// ¡Añádelo al caché! Usa `rutaCache` como raíz del caché.\ncacache.mete(rutaCache, clave, '10293801983029384').then(integrity => {\n  console.log(`Saved content to ${rutaCache}.`)\n})\n\nconst destino = '/tmp/mytar.tgz'\n\n// Copia el contenido del caché a otro fichero, pero esta vez con flujos.\ncacache.saca.flujo(\n  rutaCache, clave\n).pipe(\n  fs.createWriteStream(destino)\n).on('finish', () => {\n  console.log('extracción completada')\n})\n\n// La misma cosa, pero accesando el contenido directamente, sin tocar el índice.\ncacache.saca.porHacheo(rutaCache, integridad).then(datos => {\n  fs.writeFile(destino, datos, err => {\n    console.log('datos del tarbol sacados basado en su sha512, y escrito a otro fichero')\n  })\n})\n```\n\n### Características\n\n* Extracción por clave o por dirección de contenido (shasum, etc)\n* Usa el estándard de web, [Subresource Integrity](#integrity)\n* Compatible con multiples algoritmos - usa sha1, sha512, etc, en el mismo caché sin problema\n* Entradas con contenido idéntico comparten ficheros\n* Tolerancia de fallas (inmune a corrupción, ficheros parciales, carreras de proceso, etc)\n* Verificación completa de datos cuando (escribiendo y leyendo)\n* Concurrencia rápida, segura y \"lockless\"\n* Compatible con `stream`s (flujos)\n* Compatible con `Promise`s (promesas)\n* Bastante rápida -- acceso, incluyendo verificación, en microsegundos\n* Almacenaje de metadatos arbitrarios\n* Colección de basura y verificación adicional fuera de banda\n* Cobertura rigurosa de pruebas\n* Probablente hay un \"Bloom filter\" por ahí en algún lado. Eso le mola a la gente, ¿Verdad? 🤔\n\n### Cómo Contribuir\n\nEl equipo de cacache felizmente acepta contribuciones de código y otras maneras de participación. ¡Hay muchas formas diferentes de contribuir! La [Guía de Colaboradores](CONTRIBUTING.md) (en inglés) tiene toda la información que necesitas para cualquier tipo de contribución: todo desde cómo reportar errores hasta cómo someter parches con nuevas características. Con todo y eso, no se preocupe por si lo que haces está exáctamente correcto: no hay ningún problema en hacer preguntas si algo no está claro, o no lo encuentras.\n\nEl equipo de cacache tiene miembros hispanohablantes: es completamente aceptable crear `issues` y `pull requests` en español/castellano.\n\nTodos los participantes en este proyecto deben obedecer el [Código de Conducta](CODE_OF_CONDUCT.md) (en inglés), y en general actuar de forma amable y respetuosa mientras participan en esta comunidad.\n\nPor favor refiérase al [Historial de Cambios](CHANGELOG.md) (en inglés) para detalles sobre cambios importantes incluídos en cada versión.\n\nFinalmente, cacache tiene un sistema de localización de lenguaje. Si te interesa añadir lenguajes o mejorar los que existen, mira en el directorio `./locales` para comenzar.\n\nHappy hacking!\n\n### API\n\n#### <a name=\"localized-api\"></a> Usando el API en español\n\ncacache incluye una traducción completa de su API al castellano, con las mismas\ncaracterísticas. Para usar el API como está documentado en este documento, usa\n`require('cacache/es')`\n\ncacache también tiene otros lenguajes: encuéntralos bajo `./locales`, y podrás\nusar el API en ese lenguaje con `require('cacache/<lenguaje>')`\n\n#### <a name=\"ls\"></a> `> cacache.ls(cache) -> Promise<Object>`\n\nEnumera todas las entradas en el caché, dentro de un solo objeto. Cada entrada\nen el objeto tendrá como clave la clave única usada para el índice, el valor\nsiendo un objeto de [`saca.info`](#get-info).\n\n##### Ejemplo\n\n```javascript\ncacache.ls(rutaCache).then(console.log)\n// Salida\n{\n  'my-thing': {\n    key: 'my-thing',\n    integrity: 'sha512-BaSe64/EnCoDED+HAsh=='\n    path: '.testcache/content/deadbeef', // unido con `rutaCache`\n    time: 12345698490,\n    size: 4023948,\n    metadata: {\n      name: 'blah',\n      version: '1.2.3',\n      description: 'this was once a package but now it is my-thing'\n    }\n  },\n  'other-thing': {\n    key: 'other-thing',\n    integrity: 'sha1-ANothER+hasH=',\n    path: '.testcache/content/bada55',\n    time: 11992309289,\n    size: 111112\n  }\n}\n```\n\n#### <a name=\"ls-stream\"></a> `> cacache.ls.flujo(cache) -> Readable`\n\nEnumera todas las entradas en el caché, emitiendo un objeto de\n[`saca.info`](#get-info) por cada evento de `data` en el flujo.\n\n##### Ejemplo\n\n```javascript\ncacache.ls.flujo(rutaCache).on('data', console.log)\n// Salida\n{\n  key: 'my-thing',\n  integrity: 'sha512-BaSe64HaSh',\n  path: '.testcache/content/deadbeef', // unido con `rutaCache`\n  time: 12345698490,\n  size: 13423,\n  metadata: {\n    name: 'blah',\n    version: '1.2.3',\n    description: 'this was once a package but now it is my-thing'\n  }\n}\n\n{\n  key: 'other-thing',\n  integrity: 'whirlpool-WoWSoMuchSupport',\n  path: '.testcache/content/bada55',\n  time: 11992309289,\n  size: 498023984029\n}\n\n{\n  ...\n}\n```\n\n#### <a name=\"get-data\"></a> `> cacache.saca(cache, clave, [ops]) -> Promise({data, metadata, integrity})`\n\nDevuelve un objeto con los datos, hacheo de integridad y metadatos identificados\npor la `clave`. La propiedad `data` de este objeto será una instancia de\n`Buffer` con los datos almacenados en el caché. to do with it! cacache just\nwon't care.\n\n`integrity` es un `string` de [Subresource Integrity](#integrity). Dígase, un\n`string` que puede ser usado para verificar a la `data`, que tiene como formato\n`<algoritmo>-<hacheo-integridad-base64>`.\n\nSo no existe ninguna entrada identificada por `clave`, o se los datos\nalmacenados localmente fallan verificación, el `Promise` fallará.\n\nUna sub-función, `saca.porHacheo`, tiene casi el mismo comportamiento, excepto\nque busca entradas usando el hacheo de integridad, sin tocar el índice general.\nEsta versión *sólo* devuelve `data`, sin ningún objeto conteniéndola.\n\n##### Nota\n\nEsta función lee la entrada completa a la memoria antes de devolverla. Si estás\nalmacenando datos Muy Grandes, es posible que [`saca.flujo`](#get-stream) sea\nuna mejor solución.\n\n##### Ejemplo\n\n```javascript\n// Busca por clave\ncache.saca(rutaCache, 'my-thing').then(console.log)\n// Salida:\n{\n  metadata: {\n    thingName: 'my'\n  },\n  integrity: 'sha512-BaSe64HaSh',\n  data: Buffer#<deadbeef>,\n  size: 9320\n}\n\n// Busca por hacheo\ncache.saca.porHacheo(rutaCache, 'sha512-BaSe64HaSh').then(console.log)\n// Salida:\nBuffer#<deadbeef>\n```\n\n#### <a name=\"get-stream\"></a> `> cacache.saca.flujo(cache, clave, [ops]) -> Readable`\n\nDevuelve un [Readable\nStream](https://nodejs.org/api/stream.html#stream_readable_streams) de los datos\nalmacenados bajo `clave`.\n\nSo no existe ninguna entrada identificada por `clave`, o se los datos\nalmacenados localmente fallan verificación, el `Promise` fallará.\n\n`metadata` y `integrity` serán emitidos como eventos antes de que el flujo\ncierre.\n\nUna sub-función, `saca.flujo.porHacheo`, tiene casi el mismo comportamiento,\nexcepto que busca entradas usando el hacheo de integridad, sin tocar el índice\ngeneral. Esta versión no emite eventos de `metadata` o `integrity`.\n\n##### Ejemplo\n\n```javascript\n// Busca por clave\ncache.saca.flujo(\n  rutaCache, 'my-thing'\n).on('metadata', metadata => {\n  console.log('metadata:', metadata)\n}).on('integrity', integrity => {\n  console.log('integrity:', integrity)\n}).pipe(\n  fs.createWriteStream('./x.tgz')\n)\n// Salidas:\nmetadata: { ... }\nintegrity: 'sha512-SoMeDIGest+64=='\n\n// Busca por hacheo\ncache.saca.flujo.porHacheo(\n  rutaCache, 'sha512-SoMeDIGest+64=='\n).pipe(\n  fs.createWriteStream('./x.tgz')\n)\n```\n\n#### <a name=\"get-info\"></a> `> cacache.saca.info(cache, clave) -> Promise`\n\nBusca la `clave` en el índice del caché, devolviendo información sobre la\nentrada si existe.\n\n##### Campos\n\n* `key` - Clave de la entrada. Igual al argumento `clave`.\n* `integrity` - [hacheo de Subresource Integrity](#integrity) del contenido al que se refiere esta entrada.\n* `path` - Dirección del fichero de datos almacenados, unida al argumento `cache`.\n* `time` - Hora de creación de la entrada\n* `metadata` - Metadatos asignados a esta entrada por el usuario\n\n##### Ejemplo\n\n```javascript\ncacache.saca.info(rutaCache, 'my-thing').then(console.log)\n\n// Salida\n{\n  key: 'my-thing',\n  integrity: 'sha256-MUSTVERIFY+ALL/THINGS=='\n  path: '.testcache/content/deadbeef',\n  time: 12345698490,\n  size: 849234,\n  metadata: {\n    name: 'blah',\n    version: '1.2.3',\n    description: 'this was once a package but now it is my-thing'\n  }\n}\n```\n\n#### <a name=\"get-hasContent\"></a> `> cacache.saca.tieneDatos(cache, integrity) -> Promise`\n\nBusca un [hacheo Subresource Integrity](#integrity) en el caché. Si existe el\ncontenido asociado con `integrity`, devuelve un objeto con dos campos: el hacheo\n_específico_ que se usó para la búsqueda, `sri`, y el tamaño total del\ncontenido, `size`. Si no existe ningún contenido asociado con `integrity`,\ndevuelve `false`.\n\n##### Ejemplo\n\n```javascript\ncacache.saca.tieneDatos(rutaCache, 'sha256-MUSTVERIFY+ALL/THINGS==').then(console.log)\n\n// Salida\n{\n  sri: {\n    source: 'sha256-MUSTVERIFY+ALL/THINGS==',\n    algorithm: 'sha256',\n    digest: 'MUSTVERIFY+ALL/THINGS==',\n    options: []\n  },\n  size: 9001\n}\n\ncacache.saca.tieneDatos(rutaCache, 'sha521-NOT+IN/CACHE==').then(console.log)\n\n// Salida\nfalse\n```\n\n#### <a name=\"put-data\"></a> `> cacache.mete(cache, clave, datos, [ops]) -> Promise`\n\nInserta `datos` en el caché. El `Promise` devuelto se resuelve con un hacheo\n(generado conforme a [`ops.algorithms`](#optsalgorithms)) después que la entrada\nhaya sido escrita en completo.\n\n##### Ejemplo\n\n```javascript\nfetch(\n  'https://registry.npmjs.org/cacache/-/cacache-1.0.0.tgz'\n).then(datos => {\n  return cacache.mete(rutaCache, 'registry.npmjs.org|cacache@1.0.0', datos)\n}).then(integridad => {\n  console.log('el hacheo de integridad es', integridad)\n})\n```\n\n#### <a name=\"put-stream\"></a> `> cacache.mete.flujo(cache, clave, [ops]) -> Writable`\n\nDevuelve un [Writable\nStream](https://nodejs.org/api/stream.html#stream_writable_streams) que inserta\nal caché los datos escritos a él. Emite un evento `integrity` con el hacheo del\ncontenido escrito, cuando completa.\n\n##### Ejemplo\n\n```javascript\nrequest.get(\n  'https://registry.npmjs.org/cacache/-/cacache-1.0.0.tgz'\n).pipe(\n  cacache.mete.flujo(\n    rutaCache, 'registry.npmjs.org|cacache@1.0.0'\n  ).on('integrity', d => console.log(`integrity digest is ${d}`))\n)\n```\n\n#### <a name=\"put-options\"></a> `> opciones para cacache.mete`\n\nLa funciones `cacache.mete` tienen un número de opciones en común.\n\n##### `ops.metadata`\n\nMetadatos del usuario que se almacenarán con la entrada.\n\n##### `ops.size`\n\nEl tamaño declarado de los datos que se van a insertar. Si es proveído, cacache\nverificará que los datos escritos sean de ese tamaño, o si no, fallará con un\nerror con código `EBADSIZE`.\n\n##### `ops.integrity`\n\nEl hacheo de integridad de los datos siendo escritos.\n\nSi es proveído, y los datos escritos no le corresponden, la operación fallará\ncon un error con código `EINTEGRITY`.\n\n`ops.algorithms` no tiene ningún efecto si esta opción está presente.\n\n##### `ops.algorithms`\n\nPor Defecto: `['sha512']`\n\nAlgoritmos que se deben usar cuando se calcule el hacheo de [subresource\nintegrity](#integrity) para los datos insertados. Puede usar cualquier algoritmo\nenumerado en `crypto.getHashes()`.\n\nPor el momento, sólo se acepta un algoritmo (dígase, un array con exáctamente un\nvalor). No tiene ningún efecto si `ops.integrity` también ha sido proveido.\n\n##### `ops.uid`/`ops.gid`\n\nSi están presentes, cacache hará todo lo posible para asegurarse que todos los\nficheros creados en el proceso de sus operaciones en el caché usen esta\ncombinación en particular.\n\n##### `ops.memoize`\n\nPor Defecto: `null`\n\nSi es verdad, cacache tratará de memoizar los datos de la entrada en memoria. La\npróxima vez que el proceso corriente trate de accesar los datos o entrada,\ncacache buscará en memoria antes de buscar en disco.\n\nSi `ops.memoize` es un objeto regular o un objeto como `Map` (es decir, un\nobjeto con métodos `get()` y `set()`), este objeto en sí sera usado en vez del\ncaché de memoria global. Esto permite tener lógica específica a tu aplicación\nencuanto al almacenaje en memoria de tus datos.\n\nSi quieres asegurarte que los datos se lean del disco en vez de memoria, usa\n`memoize: false` cuando uses funciones de `cacache.saca`.\n\n#### <a name=\"rm-all\"></a> `> cacache.rm.todo(cache) -> Promise`\n\nBorra el caché completo, incluyendo ficheros temporeros, ficheros de datos, y el\níndice del caché.\n\n##### Ejemplo\n\n```javascript\ncacache.rm.todo(rutaCache).then(() => {\n  console.log('THE APOCALYPSE IS UPON US 😱')\n})\n```\n\n#### <a name=\"rm-entry\"></a> `> cacache.rm.entrada(cache, clave) -> Promise`\n\nAlias: `cacache.rm`\n\nBorra la entrada `clave` del índuce. El contenido asociado con esta entrada\nseguirá siendo accesible por hacheo usando\n[`saca.flujo.porHacheo`](#get-stream).\n\nPara borrar el contenido en sí, usa [`rm.datos`](#rm-content). Si quieres hacer\nesto de manera más segura (pues ficheros de contenido pueden ser usados por\nmultiples entradas), usa [`verifica`](#verify) para borrar huérfanos.\n\n##### Ejemplo\n\n```javascript\ncacache.rm.entrada(rutaCache, 'my-thing').then(() => {\n  console.log('I did not like it anyway')\n})\n```\n\n#### <a name=\"rm-content\"></a> `> cacache.rm.datos(cache, integrity) -> Promise`\n\nBorra el contenido identificado por `integrity`. Cualquier entrada que se\nrefiera a este contenido quedarán huérfanas y se invalidarán si se tratan de\naccesar, al menos que contenido idéntico sea añadido bajo `integrity`.\n\n##### Ejemplo\n\n```javascript\ncacache.rm.datos(rutaCache, 'sha512-SoMeDIGest/IN+BaSE64==').then(() => {\n  console.log('los datos para `mi-cosa` se borraron')\n})\n```\n\n#### <a name=\"set-locale\"></a> `> cacache.ponLenguaje(locale)`\n\nConfigura el lenguaje usado para mensajes y errores de cacache. La lista de\nlenguajes disponibles está en el directorio `./locales` del proyecto.\n\n_Te interesa añadir más lenguajes? [Somete un PR](CONTRIBUTING.md)!_\n\n#### <a name=\"clear-memoized\"></a> `> cacache.limpiaMemoizado()`\n\nCompletamente reinicializa el caché de memoria interno. Si estás usando tu\npropio objecto con `ops.memoize`, debes hacer esto de manera específica a él.\n\n#### <a name=\"tmp-mkdir\"></a> `> tmp.hazdir(cache, ops) -> Promise<Path>`\n\nAlias: `tmp.mkdir`\n\nDevuelve un directorio único dentro del directorio `tmp` del caché.\n\nUna vez tengas el directorio, es responsabilidad tuya asegurarte que todos los\nficheros escrito a él sean creados usando los permisos y `uid`/`gid` concordante\ncon el caché. Si no, puedes pedirle a cacache que lo haga llamando a\n[`cacache.tmp.fix()`](#tmp-fix). Esta función arreglará todos los permisos en el\ndirectorio tmp.\n\nSi quieres que cacache limpie el directorio automáticamente cuando termines, usa\n[`cacache.tmp.conTmp()`](#with-tpm).\n\n##### Ejemplo\n\n```javascript\ncacache.tmp.mkdir(cache).then(dir => {\n  fs.writeFile(path.join(dir, 'blablabla'), Buffer#<1234>, ...)\n})\n```\n\n#### <a name=\"with-tmp\"></a> `> tmp.conTmp(cache, ops, cb) -> Promise`\n\nCrea un directorio temporero con [`tmp.mkdir()`](#tmp-mkdir) y ejecuta `cb` con\nél como primer argumento. El directorio creado será removido automáticamente\ncuando el valor devolvido por `cb()` se resuelva.\n\nLas mismas advertencias aplican en cuanto a manejando permisos para los ficheros\ndentro del directorio.\n\n##### Ejemplo\n\n```javascript\ncacache.tmp.conTmp(cache, dir => {\n  return fs.writeFileAsync(path.join(dir, 'blablabla'), Buffer#<1234>, ...)\n}).then(() => {\n  // `dir` no longer exists\n})\n```\n\n#### <a name=\"integrity\"></a> Hacheos de Subresource Integrity\n\ncacache usa strings que siguen la especificación de [Subresource Integrity\nspec](https://developer.mozilla.org/en-US/docs/Web/Security/Subresource_Integrity).\n\nEs decir, donde quiera cacache espera un argumento o opción `integrity`, ese\nstring debería usar el formato `<algoritmo>-<hacheo-base64>`.\n\nUna variación importante sobre los hacheos que cacache acepta es que acepta el\nnombre de cualquier algoritmo aceptado por el proceso de Node.js donde se usa.\nPuedes usar `crypto.getHashes()` para ver cuales están disponibles.\n\n##### Generando tus propios hacheos\n\nSi tienes un `shasum`, en general va a estar en formato de string hexadecimal\n(es decir, un `sha1` se vería como algo así:\n`5f5513f8822fdbe5145af33b64d8d970dcf95c6e`).\n\nPara ser compatible con cacache, necesitas convertir esto a su equivalente en\nsubresource integrity. Por ejemplo, el hacheo correspondiente al ejemplo\nanterior sería: `sha1-X1UT+IIv2+UUWvM7ZNjZcNz5XG4=`.\n\nPuedes usar código así para generarlo por tu cuenta:\n\n```javascript\nconst crypto = require('crypto')\nconst algoritmo = 'sha512'\nconst datos = 'foobarbaz'\n\nconst integrity = (\n  algorithm +\n  '-' +\n  crypto.createHash(algoritmo).update(datos).digest('base64')\n)\n```\n\nTambién puedes usar [`ssri`](https://npm.im/ssri) para deferir el trabajo a otra\nlibrería que garantiza que todo esté correcto, pues maneja probablemente todas\nlas operaciones que tendrías que hacer con SRIs, incluyendo convirtiendo entre\nhexadecimal y el formato SRI.\n\n#### <a name=\"verify\"></a> `> cacache.verifica(cache, ops) -> Promise`\n\nExamina y arregla tu caché:\n\n* Limpia entradas inválidas, huérfanas y corrompidas\n* Te deja filtrar cuales entradas retener, con tu propio filtro\n* Reclama cualquier ficheros de contenido sin referencias en el índice\n* Verifica integridad de todos los ficheros de contenido y remueve los malos\n* Arregla permisos del caché\n* Remieve el directorio `tmp` en el caché, y todo su contenido.\n\nCuando termine, devuelve un objeto con varias estadísticas sobre el proceso de\nverificación, por ejemplo la cantidad de espacio de disco reclamado, el número\nde entradas válidas, número de entradas removidas, etc.\n\n##### Opciones\n\n* `ops.uid` - uid para asignarle al caché y su contenido\n* `ops.gid` - gid para asignarle al caché y su contenido\n* `ops.filter` - recibe una entrada como argumento. Devuelve falso para removerla. Nota: es posible que esta función sea invocada con la misma entrada más de una vez.\n\n##### Example\n\n```sh\necho somegarbage >> $RUTACACHE/content/deadbeef\n```\n\n```javascript\ncacache.verifica(rutaCache).then(stats => {\n  // deadbeef collected, because of invalid checksum.\n  console.log('cache is much nicer now! stats:', stats)\n})\n```\n\n#### <a name=\"verify-last-run\"></a> `> cacache.verifica.ultimaVez(cache) -> Promise`\n\nAlias: `últimaVez`\n\nDevuelve un `Date` que representa la última vez que `cacache.verifica` fue\nejecutada en `cache`.\n\n##### Example\n\n```javascript\ncacache.verifica(rutaCache).then(() => {\n  cacache.verifica.ultimaVez(rutaCache).then(última => {\n    console.log('La última vez que se usó cacache.verifica() fue ' + última)\n  })\n})\n```\n", "licenseText": "ISC License\n\nCopyright (c) npm, Inc.\n\nPermission to use, copy, modify, and/or distribute this software for\nany purpose with or without fee is hereby granted, provided that the\nabove copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE COPYRIGHT HOLDER DISCLAIMS\nALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED\nWARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE\nCOPYRIGHT HOLDER BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR\nCONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS\nOF USE, <PERSON><PERSON><PERSON> OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE\nOR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE\nUSE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/cacache/-/cacache-12.0.4.tgz#668bcbd105aeb5f1d92fe25570ec9525c8faa40c", "type": "tarball", "reference": "https://registry.yarnpkg.com/cacache/-/cacache-12.0.4.tgz", "hash": "668bcbd105aeb5f1d92fe25570ec9525c8faa40c", "integrity": "sha512-a0tMB40oefvuInr4Cwb3GerbL9xTj1D5yg0T5xrjGCGyfvbxseIXX7BAO/u/hIXdafzOI5JC3wDwHyf24buOAQ==", "registry": "npm", "packageName": "cacache", "cacheIntegrity": "sha512-a0tMB40oefvuInr4Cwb3GerbL9xTj1D5yg0T5xrjGCGyfvbxseIXX7BAO/u/hIXdafzOI5JC3wDwHyf24buOAQ== sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw="}, "registry": "npm", "hash": "668bcbd105aeb5f1d92fe25570ec9525c8faa40c"}