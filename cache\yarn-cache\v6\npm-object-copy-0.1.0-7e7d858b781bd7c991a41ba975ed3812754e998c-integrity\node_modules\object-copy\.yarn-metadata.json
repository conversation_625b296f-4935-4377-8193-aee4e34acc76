{"manifest": {"name": "object-copy", "description": "Copy static properties, prototype properties, and descriptors from one object to another.", "version": "0.1.0", "homepage": "https://github.com/jonschlinkert/object-copy", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/object-copy.git"}, "bugs": {"url": "https://github.com/jonschlinkert/object-copy/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "devDependencies": {"gulp-format-md": "*", "mocha": "*"}, "keywords": ["copy", "object"], "verb": {"layout": "default", "plugins": ["gulp-format-md"], "related": {"list": []}, "reflinks": ["verb"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-object-copy-0.1.0-7e7d858b781bd7c991a41ba975ed3812754e998c-integrity\\node_modules\\object-copy\\package.json", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/object-copy/-/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c", "type": "tarball", "reference": "https://registry.yarnpkg.com/object-copy/-/object-copy-0.1.0.tgz", "hash": "7e7d858b781bd7c991a41ba975ed3812754e998c", "integrity": "sha1-fn2Fi3gb18mRpBupde04EnVOmYw=", "registry": "npm", "packageName": "object-copy", "cacheIntegrity": "sha512-79LYn6VAb63zgtmAteVOWo9Vdj71ZVBy3Pbse+VqxDpEP83XuujMrGqHIwAXJ5I/aM0zU7dIyIAhifVTPrNItQ== sha1-fn2Fi3gb18mRpBupde04EnVOmYw="}, "registry": "npm", "hash": "7e7d858b781bd7c991a41ba975ed3812754e998c"}