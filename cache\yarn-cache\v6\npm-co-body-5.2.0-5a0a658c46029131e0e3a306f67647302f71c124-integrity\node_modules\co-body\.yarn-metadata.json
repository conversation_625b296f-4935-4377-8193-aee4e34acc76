{"manifest": {"name": "co-body", "version": "5.2.0", "repository": {"type": "git", "url": "https://github.com/cojs/co-body.git"}, "description": "request body parsing for co", "keywords": ["request", "parse", "parser", "json", "co", "generators", "u<PERSON><PERSON><PERSON>"], "dependencies": {"inflation": "^2.0.0", "qs": "^6.4.0", "raw-body": "^2.2.0", "type-is": "^1.6.14"}, "devDependencies": {"istanbul": "^0.4.5", "koa": "^1.2.5", "mocha": "^3.2.0", "safe-qs": "^6.0.1", "should": "^11.2.0", "supertest": "^1.0.1"}, "license": "MIT", "scripts": {"test": "make test", "test-cov": "make test-cov"}, "files": ["index.js", "lib/"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-co-body-5.2.0-5a0a658c46029131e0e3a306f67647302f71c124-integrity\\node_modules\\co-body\\package.json", "readmeFilename": "Readme.md", "readme": "\n# co-body\n\n[![NPM version][npm-image]][npm-url]\n[![build status][travis-image]][travis-url]\n[![Test coverage][coveralls-image]][coveralls-url]\n[![<PERSON> de<PERSON>][david-image]][david-url]\n[![npm download][download-image]][download-url]\n\n[npm-image]: https://img.shields.io/npm/v/co-body.svg?style=flat-square\n[npm-url]: https://npmjs.org/package/co-body\n[travis-image]: https://img.shields.io/travis/cojs/co-body.svg?style=flat-square\n[travis-url]: https://travis-ci.org/cojs/co-body\n[coveralls-image]: https://img.shields.io/coveralls/cojs/co-body.svg?style=flat-square\n[coveralls-url]: https://coveralls.io/r/cojs/co-body?branch=master\n[david-image]: https://img.shields.io/david/cojs/co-body.svg?style=flat-square\n[david-url]: https://david-dm.org/cojs/co-body\n[download-image]: https://img.shields.io/npm/dm/co-body.svg?style=flat-square\n[download-url]: https://npmjs.org/package/co-body\n\n  Parse request bodies with generators inspired by [Raynos/body](https://github.com/Raynos/body).\n\n## Installation\n\n```bash\n$ npm install co-body\n```\n\n## Options\n\n  - `limit` number or string representing the request size limit (1mb for json and 56kb for form-urlencoded)\n  - `strict` when set to `true`, JSON parser will only accept arrays and objects; when `false` will accept anything `JSON.parse` accepts. Defaults to `true`. (also `strict` mode will always return object).\n  - `queryString` an object of options when parsing query strings and form data. See [qs](https://github.com/hapijs/qs) for more information.\n  - `returnRawBody` when set to `true`, the return value of `co-body` will be an object with two properties: `{ parsed: /* parsed value */, raw: /* raw body */}`.\n  - `jsonTypes` is used to determine what media type **co-body** will parse as **json**, this option is passed directly to the [type-is](https://github.com/jshttp/type-is) library.\n  - `formTypes` is used to determine what media type **co-body** will parse as **form**, this option is passed directly to the [type-is](https://github.com/jshttp/type-is) library.\n  - `textTypes` is used to determine what media type **co-body** will parse as **text**, this option is passed directly to the [type-is](https://github.com/jshttp/type-is) library.\n\nmore options available via [raw-body](https://github.com/stream-utils/raw-body#getrawbodystream-options-callback):\n\n## Example\n\n```js\n// application/json\nvar body = yield parse.json(req);\n\n// explicit limit\nvar body = yield parse.json(req, { limit: '10kb' });\n\n// application/x-www-form-urlencoded\nvar body = yield parse.form(req);\n\n// text/plain\nvar body = yield parse.text(req);\n\n// either\nvar body = yield parse(req);\n\n// custom type\nvar body = yield parse(req, { textTypes: ['text', 'html'] });\n```\n\n## Koa\n\n  This lib also supports `ctx.req` in Koa (or other libraries),\n  so that you may simply use `this` instead of `this.req`.\n\n```js\n// application/json\nvar body = yield parse.json(this);\n\n// application/x-www-form-urlencoded\nvar body = yield parse.form(this);\n\n// text/plain\nvar body = yield parse.text(this);\n\n// either\nvar body = yield parse(this);\n```\n\n# License\n\n  MIT\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/co-body/-/co-body-5.2.0.tgz#5a0a658c46029131e0e3a306f67647302f71c124", "type": "tarball", "reference": "https://registry.yarnpkg.com/co-body/-/co-body-5.2.0.tgz", "hash": "5a0a658c46029131e0e3a306f67647302f71c124", "integrity": "sha512-sX/LQ7LqUhgyaxzbe7IqwPeTr2yfpfUIQ/dgpKo6ZI4y4lpQA0YxAomWIY+7I7rHWcG02PG+OuPREzMW/5tszQ==", "registry": "npm", "packageName": "co-body", "cacheIntegrity": "sha512-sX/LQ7LqUhgyaxzbe7IqwPeTr2yfpfUIQ/dgpKo6ZI4y4lpQA0YxAomWIY+7I7rHWcG02PG+OuPREzMW/5tszQ== sha1-WgpljEYCkTHg46MG9nZHMC9xwSQ="}, "registry": "npm", "hash": "5a0a658c46029131e0e3a306f67647302f71c124"}