<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Modern smartphone interface for QBCore"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css">
    
    <title>QB Smartphone 2</title>
    
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        margin: 0;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background: transparent;
        overflow: hidden;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
      }
      
      code {
        font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
          monospace;
      }
      
      #root {
        width: 100vw;
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        background: transparent;
      }
      
      /* Scrollbar styling */
      ::-webkit-scrollbar {
        width: 6px;
      }
      
      ::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
      }
      
      ::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 3px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
      }
      
      /* Disable text selection */
      .no-select {
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }
      
      /* Phone animations */
      .phone-enter {
        opacity: 0;
        transform: scale(0.8) translateY(50px);
      }
      
      .phone-enter-active {
        opacity: 1;
        transform: scale(1) translateY(0);
        transition: all 0.3s ease-out;
      }
      
      .phone-exit {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
      
      .phone-exit-active {
        opacity: 0;
        transform: scale(0.8) translateY(50px);
        transition: all 0.3s ease-in;
      }
      
      /* App animations */
      .app-enter {
        opacity: 0;
        transform: translateX(100%);
      }
      
      .app-enter-active {
        opacity: 1;
        transform: translateX(0);
        transition: all 0.3s ease-out;
      }
      
      .app-exit {
        opacity: 1;
        transform: translateX(0);
      }
      
      .app-exit-active {
        opacity: 0;
        transform: translateX(-100%);
        transition: all 0.3s ease-in;
      }
      
      /* Notification animations */
      .notification-enter {
        opacity: 0;
        transform: translateY(-100%);
      }
      
      .notification-enter-active {
        opacity: 1;
        transform: translateY(0);
        transition: all 0.3s ease-out;
      }
      
      .notification-exit {
        opacity: 1;
        transform: translateY(0);
      }
      
      .notification-exit-active {
        opacity: 0;
        transform: translateY(-100%);
        transition: all 0.3s ease-in;
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
