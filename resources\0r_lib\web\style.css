body {
  overflow: hidden;
  margin: 0;
}

/* UI Text */

.textuiContainer {
  width: fit-content;
  height: 3.4rem;
  padding-left: 1vh;
  padding-right: 1vh;
  flex-shrink: 0;
  position: absolute;
  margin: auto;
  top: 0;
  bottom: 0;
  right: 2rem;
  border-radius: .2rem;
  background: #111112;
}

.textuiContainer h1 {
  color: #C9C9C9;
  font-family: Roboto;
  font-size: 17px;
  padding: 15px;
  font-style: normal;
  margin: auto;
  text-align: center;
  font-weight: 400;
  line-height: normal;
}

.leftBar {
  width: fit-content;
  height: 100%;
  display: flex;
  align-items: center;
  font-size: 1.4rem;
  justify-content: center;
  color: #d3d3d3;
  padding-left: .4rem;
  float: left;
  background-color: transparent;
}

.leftright {
  width: fit-content;
  height: 100%;
  background-color: transparent;
  float: left; 
  align-items: center;
  justify-content: center;
  display: flex;
}

/* Yeni Notify */

.NotifyPart {
    background: transparent;
    width: fit-content;
    padding-right: 1rem;
    position: absolute;
    right: 0;
    height: 95vh;
    float: right;
}

.NotifyRow {
  display: none;
}

.NotifyContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
    max-width: 100rem;
    margin-top: 1rem;
    height: fit-content;
    padding-right: 3.5rem;
    position: relative;
    padding-bottom: 0.1rem;
    background: #131415;
    z-index: 8000;
    border-radius: .5rem;
    top: 1.5rem;
}

.NotifyContent {
  flex: 1;
}

.NotifyContainer h3 {
  margin: auto;
  font-size: 4.2vh;
  font-family: 'Inter';
  margin-left: 1.8rem;
  margin-top: .5rem;
  font-size: 3.7rem;
  padding-top: 6%;
  float: left;
  color: #0dd571;
}


.NotifyContainer h4 {
  color: #C6C6C6;
  font-family: 'Inter';
  font-size: 1.3rem;
  font-style: normal;
  margin: auto;
  font-weight: 600;
  text-transform: uppercase;
  line-height: normal;
  position: relative;
  left: 1.2rem;
  top: 1.5rem;
}

.NotifyContainer p {
  color: #949494;
  font-family: 'Inter';
  font-size: .95rem;
  font-style: normal;
  padding-left: 5.4rem;
  top: .8rem;
  padding-right: 3rem;
  position: relative;
  font-weight: 500;
  left: 1.3em;
  line-height: normal;
}

@keyframes slide-in {
  0% {
      transform: translateX(100%);
  }
  100% {
      transform: translateX(0);
  }
}

.NewContainer {
  width: fit-content;
  height: 4.9375rem;
  border-radius: 0.5625rem;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.20) 0%, rgba(0, 0, 0, 0.20) 100%), #191919;
  position: absolute;
  right: 1rem;
  margin-top: 1rem;
  display: flex; /* Yeni eklenen kısım */
}

.ContainerNot {
    display: flex;
    justify-content: center;
    align-items: center;
    float: left;
    width: 7rem;
    height: 100%;
    border-radius: 0.5625rem;
    background-color: transparent;
}

.ContainerNot span {
  font-size: 3rem;
}

.ContainerNext {
  flex: 1; /* Yeni eklenen kısım */
  padding-right: 3rem;
  background-color: transparent;
  width: fit-content;
  max-width: 25rem;
}

.ContainerNext h3 {
  color: #DCDCDC;
  font-family: Poppins;
  font-size: 1.25rem;
  font-style: normal;
  margin: auto;
  position: relative;
  left: 0rem;
  top: 1.2rem;
  font-weight: 600;
  line-height: normal;
  
}

.NewSuccess {
    color: #26CB90;
}

.ContainerNext p {
  color: #26CB90;
  font-family: Poppins;
  font-size: 0.9375rem;
  font-style: normal;
  width: 17rem;
  font-weight: 500;
  position: relative;
  left: .6rem;
  top: .6rem;
  margin: auto;
  line-height: normal;
}





.Bunlar {
  background-color: #202020;
  width: 23rem;
  position: absolute;
  height: fit-content;
  padding-bottom: .32rem;
  border-radius: .8rem;
  right: 1rem;
  margin-top: 1rem;
  display: flex;
}

.LeftBoar {
  background-color: transparent;
  flex: 1; /* LeftBoar'ın esnekliğini ayarla, böylece RightBoar ile aynı hizada olur */
  display: flex;
  justify-content: center;
  align-items: center;
}

.RightBoar {
  background-color: transparent;
  width: 17rem;
  padding-right: 1rem;
}  

.LeftBoar span {
  color: #0fdd8b;
  font-size: 2.2rem;
}


.RightBoar h1 {
  color: #01fa97;
  font-size: 1.2rem;
  font-weight: 600;
  font-family: Poppins;
  margin: auto;
  position: relative;
  top: 1.1rem;
}

.RightBoar p {
  font-family: Poppins;
  font-size: .94rem;
  font-weight: 500;
  color: #aeaeae;
}