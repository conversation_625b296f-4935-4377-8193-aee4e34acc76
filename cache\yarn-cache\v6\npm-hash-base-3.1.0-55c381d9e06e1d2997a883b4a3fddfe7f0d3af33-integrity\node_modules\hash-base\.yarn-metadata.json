{"manifest": {"name": "hash-base", "version": "3.1.0", "description": "abstract base class for hash-streams", "keywords": ["hash", "stream"], "homepage": "https://github.com/crypto-browserify/hash-base", "bugs": {"url": "https://github.com/crypto-browserify/hash-base/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fanatid"}, "files": ["index.js"], "main": "index.js", "repository": {"type": "git", "url": "https://github.com/crypto-browserify/hash-base.git"}, "scripts": {"coverage": "nyc node test/*.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "node test/*.js"}, "dependencies": {"inherits": "^2.0.4", "readable-stream": "^3.6.0", "safe-buffer": "^5.2.0"}, "devDependencies": {"nyc": "^15.0.1", "standard": "^14.3.3", "tape": "^5.0.0"}, "engines": {"node": ">=4"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-hash-base-3.1.0-55c381d9e06e1d2997a883b4a3fddfe7f0d3af33-integrity\\node_modules\\hash-base\\package.json", "readmeFilename": "README.md", "readme": "# hash-base\n\n[![NPM Package](https://img.shields.io/npm/v/hash-base.svg?style=flat-square)](https://www.npmjs.org/package/hash-base)\n[![Build Status](https://img.shields.io/travis/crypto-browserify/hash-base.svg?branch=master&style=flat-square)](https://travis-ci.org/crypto-browserify/hash-base)\n[![Dependency status](https://img.shields.io/david/crypto-browserify/hash-base.svg?style=flat-square)](https://david-dm.org/crypto-browserify/hash-base#info=dependencies)\n\n[![js-standard-style](https://cdn.rawgit.com/feross/standard/master/badge.svg)](https://github.com/feross/standard)\n\nAbstract base class to inherit from if you want to create streams implementing the same API as node crypto [Hash][1] (for [Cipher][2] / [Decipher][3] check [crypto-browserify/cipher-base][4]).\n\n## Example\n\n```js\nconst HashBase = require('hash-base')\nconst inherits = require('inherits')\n\n// our hash function is XOR sum of all bytes\nfunction MyHash () {\n  HashBase.call(this, 1) // in bytes\n\n  this._sum = 0x00\n}\n\ninherits(MyHash, HashBase)\n\nMyHash.prototype._update = function () {\n  for (let i = 0; i < this._block.length; ++i) this._sum ^= this._block[i]\n}\n\nMyHash.prototype._digest = function () {\n  return this._sum\n}\n\nconst data = Buffer.from([ 0x00, 0x42, 0x01 ])\nconst hash = new MyHash().update(data).digest()\nconsole.log(hash) // => 67\n```\nYou also can check [source code](index.js) or [crypto-browserify/md5.js][5]\n\n## LICENSE\n\nMIT\n\n[1]: https://nodejs.org/api/crypto.html#crypto_class_hash\n[2]: https://nodejs.org/api/crypto.html#crypto_class_cipher\n[3]: https://nodejs.org/api/crypto.html#crypto_class_decipher\n[4]: https://github.com/crypto-browserify/cipher-base\n[5]: https://github.com/crypto-browserify/md5.js\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016 Kirill Fomichev\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/hash-base/-/hash-base-3.1.0.tgz#55c381d9e06e1d2997a883b4a3fddfe7f0d3af33", "type": "tarball", "reference": "https://registry.yarnpkg.com/hash-base/-/hash-base-3.1.0.tgz", "hash": "55c381d9e06e1d2997a883b4a3fddfe7f0d3af33", "integrity": "sha512-1nmYp/rhMDiE7AYkDw+lLwlAzz0AntGIe51F3RfFfEqyQ3feY2eI/NcwC6umIQVOASPMsWJLJScWKSSvzL9IVA==", "registry": "npm", "packageName": "hash-base", "cacheIntegrity": "sha512-1nmYp/rhMDiE7AYkDw+lLwlAzz0AntGIe51F3RfFfEqyQ3feY2eI/NcwC6umIQVOASPMsWJLJScWKSSvzL9IVA== sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM="}, "registry": "npm", "hash": "55c381d9e06e1d2997a883b4a3fddfe7f0d3af33"}