{"manifest": {"name": "is-number", "description": "Returns true if the value is a number. comprehensive tests.", "version": "3.0.0", "homepage": "https://github.com/jonschlinkert/is-number", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON><PERSON><PERSON> Reagent", "url": "http://www.tunnckocore.tk"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-number.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"kind-of": "^3.0.2"}, "devDependencies": {"benchmarked": "^0.2.5", "chalk": "^1.1.3", "gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "keywords": ["check", "coerce", "coercion", "integer", "is", "is-nan", "is-num", "is-number", "istype", "kind", "math", "nan", "num", "number", "numeric", "test", "type", "typeof", "value"], "verb": {"related": {"list": ["even", "is-even", "is-odd", "is-primitive", "kind-of", "odd"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb", "verb-generate-readme"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-number-3.0.0-24fd6201a4782cf50561c810276afc7d12d71195-integrity\\node_modules\\is-number\\package.json", "readmeFilename": "README.md", "readme": "# is-number [![NPM version](https://img.shields.io/npm/v/is-number.svg?style=flat)](https://www.npmjs.com/package/is-number) [![NPM downloads](https://img.shields.io/npm/dm/is-number.svg?style=flat)](https://npmjs.org/package/is-number) [![Build Status](https://img.shields.io/travis/jonschlinkert/is-number.svg?style=flat)](https://travis-ci.org/jonschlinkert/is-number)\n\n> Returns true if the value is a number. comprehensive tests.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save is-number\n```\n\n## Usage\n\nTo understand some of the rationale behind the decisions made in this library (and to learn about some oddities of number evaluation in JavaScript), [see this gist](https://gist.github.com/jonschlinkert/e30c70c713da325d0e81).\n\n```js\nvar isNumber = require('is-number');\n```\n\n### true\n\nSee the [tests](./test.js) for more examples.\n\n```js\nisNumber(5e3)      //=> 'true'\nisNumber(0xff)     //=> 'true'\nisNumber(-1.1)     //=> 'true'\nisNumber(0)        //=> 'true'\nisNumber(1)        //=> 'true'\nisNumber(1.1)      //=> 'true'\nisNumber(10)       //=> 'true'\nisNumber(10.10)    //=> 'true'\nisNumber(100)      //=> 'true'\nisNumber('-1.1')   //=> 'true'\nisNumber('0')      //=> 'true'\nisNumber('012')    //=> 'true'\nisNumber('0xff')   //=> 'true'\nisNumber('1')      //=> 'true'\nisNumber('1.1')    //=> 'true'\nisNumber('10')     //=> 'true'\nisNumber('10.10')  //=> 'true'\nisNumber('100')    //=> 'true'\nisNumber('5e3')    //=> 'true'\nisNumber(parseInt('012'))   //=> 'true'\nisNumber(parseFloat('012')) //=> 'true'\n```\n\n### False\n\nSee the [tests](./test.js) for more examples.\n\n```js\nisNumber('foo')             //=> 'false'\nisNumber([1])               //=> 'false'\nisNumber([])                //=> 'false'\nisNumber(function () {})    //=> 'false'\nisNumber(Infinity)          //=> 'false'\nisNumber(NaN)               //=> 'false'\nisNumber(new Array('abc'))  //=> 'false'\nisNumber(new Array(2))      //=> 'false'\nisNumber(new Buffer('abc')) //=> 'false'\nisNumber(null)              //=> 'false'\nisNumber(undefined)         //=> 'false'\nisNumber({abc: 'abc'})      //=> 'false'\n```\n\n## About\n\n### Related projects\n\n* [even](https://www.npmjs.com/package/even): Get the even numbered items from an array. | [homepage](https://github.com/jonschlinkert/even \"Get the even numbered items from an array.\")\n* [is-even](https://www.npmjs.com/package/is-even): Return true if the given number is even. | [homepage](https://github.com/jonschlinkert/is-even \"Return true if the given number is even.\")\n* [is-odd](https://www.npmjs.com/package/is-odd): Returns true if the given number is odd. | [homepage](https://github.com/jonschlinkert/is-odd \"Returns true if the given number is odd.\")\n* [is-primitive](https://www.npmjs.com/package/is-primitive): Returns `true` if the value is a primitive.  | [homepage](https://github.com/jonschlinkert/is-primitive \"Returns `true` if the value is a primitive. \")\n* [kind-of](https://www.npmjs.com/package/kind-of): Get the native type of a value. | [homepage](https://github.com/jonschlinkert/kind-of \"Get the native type of a value.\")\n* [odd](https://www.npmjs.com/package/odd): Get the odd numbered items from an array. | [homepage](https://github.com/jonschlinkert/odd \"Get the odd numbered items from an array.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Building docs\n\n_(This document was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme) (a [verb](https://github.com/verbose/verb) generator), please don't edit the readme directly. Any changes to the readme must be made in [.verb.md](.verb.md).)_\n\nTo generate the readme and API documentation with [verb](https://github.com/verbose/verb):\n\n```sh\n$ npm install -g verb verb-generate-readme && verb\n```\n\n### Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm install -d && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT license](https://github.com/jonschlinkert/is-number/blob/master/LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.1.30, on September 10, 2016._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2016, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-number/-/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-number/-/is-number-3.0.0.tgz", "hash": "24fd6201a4782cf50561c810276afc7d12d71195", "integrity": "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=", "registry": "npm", "packageName": "is-number", "cacheIntegrity": "sha512-4cboCqIpliH+mAvFNegjZQ4kgKc3ZUhQVr3HvWbSh5q3WH2v82ct+T2Y1hdU5Gdtorx/cLifQjqCbL7bpznLTg== sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU="}, "registry": "npm", "hash": "24fd6201a4782cf50561c810276afc7d12d71195"}