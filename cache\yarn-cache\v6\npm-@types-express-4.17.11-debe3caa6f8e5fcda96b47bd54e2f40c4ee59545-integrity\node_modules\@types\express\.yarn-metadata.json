{"manifest": {"name": "@types/express", "version": "4.17.11", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "51b7ca65fbad2f43fbcff8d74c47db7b8f36b31f71458c1fb328511d0075ac5a", "typeScriptVersion": "3.4", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-express-4.17.11-debe3caa6f8e5fcda96b47bd54e2f40c4ee59545-integrity\\node_modules\\@types\\express\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/express`\n\n# Summary\nThis package contains type definitions for Express (http://expressjs.com).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express.\n\n### Additional Details\n * Last updated: <PERSON><PERSON>, 12 Jan 2021 21:42:39 GMT\n * Dependencies: [@types/body-parser](https://npmjs.com/package/@types/body-parser), [@types/serve-static](https://npmjs.com/package/@types/serve-static), [@types/express-serve-static-core](https://npmjs.com/package/@types/express-serve-static-core), [@types/qs](https://npmjs.com/package/@types/qs)\n * Global values: none\n\n# Credits\nThese definitions were written by [<PERSON>](https://github.com/b<PERSON><PERSON><PERSON>), [China Medical University Hospital](https://github.com/CMUH), [Puneet Arora](https://github.com/puneetar), and [<PERSON>](https://github.com/dfrankland).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/express/-/express-4.17.11.tgz#debe3caa6f8e5fcda96b47bd54e2f40c4ee59545", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/express/-/express-4.17.11.tgz", "hash": "debe3caa6f8e5fcda96b47bd54e2f40c4ee59545", "integrity": "sha512-no+R6rW60JEc59977wIxreQVsIEOAYwgCqldrA/vkpCnbD7MqTefO97lmoBe4WE0F156bC4uLSP1XHDOySnChg==", "registry": "npm", "packageName": "@types/express", "cacheIntegrity": "sha512-no+R6rW60JEc59977wIxreQVsIEOAYwgCqldrA/vkpCnbD7MqTefO97lmoBe4WE0F156bC4uLSP1XHDOySnChg== sha1-3r48qm+OX82pa0e9VOL0DE7llUU="}, "registry": "npm", "hash": "debe3caa6f8e5fcda96b47bd54e2f40c4ee59545"}