{"manifest": {"name": "toidentifier", "description": "Convert a string of words to a JavaScript identifier", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://niftylettuce.com/"}], "repository": {"type": "git", "url": "https://github.com/component/toidentifier.git"}, "devDependencies": {"eslint": "4.19.1", "eslint-config-standard": "11.0.0", "eslint-plugin-import": "2.11.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "6.0.1", "eslint-plugin-promise": "3.7.0", "eslint-plugin-standard": "3.1.0", "mocha": "1.21.5", "nyc": "11.8.0"}, "engines": {"node": ">=0.6"}, "license": "MIT", "files": ["index.js"], "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-toidentifier-1.0.0-7e1be3470f1e77948bc43d94a3c8f4d7752ba553-integrity\\node_modules\\toidentifier\\package.json", "readmeFilename": "README.md", "readme": "# toidentifier\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][codecov-image]][codecov-url]\n\n> Convert a string of words to a JavaScript identifier\n\n## Install\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```bash\n$ npm install toidentifier\n```\n\n## Example\n\n```js\nvar toIdentifier = require('toidentifier')\n\nconsole.log(toIdentifier('Bad Request'))\n// => \"BadRequest\"\n```\n\n## API\n\nThis CommonJS module exports a single default function: `toIdentifier`.\n\n### toIdentifier(string)\n\nGiven a string as the argument, it will be transformed according to\nthe following rules and the new string will be returned:\n\n1. Split into words separated by space characters (`0x20`).\n2. Upper case the first character of each word.\n3. Join the words together with no separator.\n4. Remove all non-word (`[0-9a-z_]`) characters.\n\n## License\n\n[MIT](LICENSE)\n\n[codecov-image]: https://img.shields.io/codecov/c/github/component/toidentifier.svg\n[codecov-url]: https://codecov.io/gh/component/toidentifier\n[downloads-image]: https://img.shields.io/npm/dm/toidentifier.svg\n[downloads-url]: https://npmjs.org/package/toidentifier\n[npm-image]: https://img.shields.io/npm/v/toidentifier.svg\n[npm-url]: https://npmjs.org/package/toidentifier\n[travis-image]: https://img.shields.io/travis/component/toidentifier/master.svg\n[travis-url]: https://travis-ci.org/component/toidentifier\n\n\n##\n\n[npm]: https://www.npmjs.com/\n\n[yarn]: https://yarnpkg.com/\n", "licenseText": "MIT License\n\nCopyright (c) 2016 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/toidentifier/-/toidentifier-1.0.0.tgz#7e1be3470f1e77948bc43d94a3c8f4d7752ba553", "type": "tarball", "reference": "https://registry.yarnpkg.com/toidentifier/-/toidentifier-1.0.0.tgz", "hash": "7e1be3470f1e77948bc43d94a3c8f4d7752ba553", "integrity": "sha512-yaOH/Pk/VEhBWWTlhI+qXxDFXlejDGcQipMlyxda9nthulaxLZUNcUqFxokp0vcYnvteJln5FNQDRrxj3YcbVw==", "registry": "npm", "packageName": "toidentifier", "cacheIntegrity": "sha512-yaOH/Pk/VEhBWWTlhI+qXxDFXlejDGcQipMlyxda9nthulaxLZUNcUqFxokp0vcYnvteJln5FNQDRrxj3YcbVw== sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM="}, "registry": "npm", "hash": "7e1be3470f1e77948bc43d94a3c8f4d7752ba553"}