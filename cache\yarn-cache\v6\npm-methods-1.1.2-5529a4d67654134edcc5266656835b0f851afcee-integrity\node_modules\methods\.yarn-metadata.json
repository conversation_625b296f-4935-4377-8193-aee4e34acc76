{"manifest": {"name": "methods", "description": "HTTP methods that node supports", "version": "1.1.2", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/methods.git"}, "devDependencies": {"istanbul": "0.4.1", "mocha": "1.21.5"}, "files": ["index.js", "HISTORY.md", "LICENSE"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "browser": {"http": false}, "keywords": ["http", "methods"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-methods-1.1.2-5529a4d67654134edcc5266656835b0f851afcee-integrity\\node_modules\\methods\\package.json", "readmeFilename": "README.md", "readme": "# Methods\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nHTTP verbs that Node.js core's HTTP parser supports.\n\nThis module provides an export that is just like `http.METHODS` from Node.js core,\nwith the following differences:\n\n  * All method names are lower-cased.\n  * Contains a fallback list of methods for Node.js versions that do not have a\n    `http.METHODS` export (0.10 and lower).\n  * Provides the fallback list when using tools like `browserify` without pulling\n    in the `http` shim module.\n\n## Install\n\n```bash\n$ npm install methods\n```\n\n## API\n\n```js\nvar methods = require('methods')\n```\n\n### methods\n\nThis is an array of lower-cased method names that Node.js supports. If Node.js\nprovides the `http.METHODS` export, then this is the same array lower-cased,\notherwise it is a snapshot of the verbs from Node.js 0.10.\n\n## License\n\n[MIT](LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/methods.svg?style=flat\n[npm-url]: https://npmjs.org/package/methods\n[node-version-image]: https://img.shields.io/node/v/methods.svg?style=flat\n[node-version-url]: https://nodejs.org/en/download/\n[travis-image]: https://img.shields.io/travis/jshttp/methods.svg?style=flat\n[travis-url]: https://travis-ci.org/jshttp/methods\n[coveralls-image]: https://img.shields.io/coveralls/jshttp/methods.svg?style=flat\n[coveralls-url]: https://coveralls.io/r/jshttp/methods?branch=master\n[downloads-image]: https://img.shields.io/npm/dm/methods.svg?style=flat\n[downloads-url]: https://npmjs.org/package/methods\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2013-2014 <PERSON><PERSON> <<EMAIL>>\nCopyright (c) 2015-2016 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/methods/-/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee", "type": "tarball", "reference": "https://registry.yarnpkg.com/methods/-/methods-1.1.2.tgz", "hash": "5529a4d67654134edcc5266656835b0f851afcee", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "registry": "npm", "packageName": "methods", "cacheIntegrity": "sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w== sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="}, "registry": "npm", "hash": "5529a4d67654134edcc5266656835b0f851afcee"}