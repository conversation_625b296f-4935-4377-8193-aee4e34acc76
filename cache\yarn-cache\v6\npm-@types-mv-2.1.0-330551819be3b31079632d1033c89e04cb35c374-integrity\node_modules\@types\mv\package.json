{"name": "@types/mv", "version": "2.1.0", "description": "TypeScript definitions for mv", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/nenadalm", "githubUsername": "ne<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "c8b5cd5c60404bcc346fa5a9fa1728c9575a9fed35f3af18ffba476dfa770cc4", "typeScriptVersion": "2.0"}