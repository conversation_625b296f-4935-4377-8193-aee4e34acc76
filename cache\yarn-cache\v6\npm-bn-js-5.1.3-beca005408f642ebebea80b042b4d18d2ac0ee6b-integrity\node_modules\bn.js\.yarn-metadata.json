{"manifest": {"name": "bn.js", "version": "5.1.3", "description": "Big number implementation in pure javascript", "keywords": ["BN", "Big number", "BigNum", "<PERSON><PERSON><PERSON>", "<PERSON>"], "homepage": "https://github.com/indutny/bn.js", "bugs": {"url": "https://github.com/indutny/bn.js/issues"}, "repository": {"type": "git", "url": "**************:indutny/bn.js"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "files": ["lib/bn.js"], "main": "lib/bn.js", "browser": {"buffer": false}, "scripts": {"lint": "standardx", "test": "npm run lint && npm run unit", "unit": "mocha --reporter=spec test/*-test.js"}, "devDependencies": {"babel-eslint": "^10.0.3", "mocha": "^7.0.1", "standardx": "^5.0.0"}, "standardx": {"parser": "babel-es<PERSON>"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-bn-js-5.1.3-beca005408f642ebebea80b042b4d18d2ac0ee6b-integrity\\node_modules\\bn.js\\package.json", "readmeFilename": "README.md", "readme": "# <img src=\"./logo.png\" alt=\"bn.js\" width=\"160\" height=\"160\" />\n\n> BigNum in pure javascript\n\n[![Build Status](https://secure.travis-ci.org/indutny/bn.js.png)](http://travis-ci.org/indutny/bn.js)\n\n## Install\n`npm install --save bn.js`\n\n## Usage\n\n```js\nconst BN = require('bn.js');\n\nvar a = new BN('dead', 16);\nvar b = new BN('101010', 2);\n\nvar res = a.add(b);\nconsole.log(res.toString(10));  // 57047\n```\n\n**Note**: decimals are not supported in this library.\n\n## Notation\n\n### Prefixes\n\nThere are several prefixes to instructions that affect the way the work. Here\nis the list of them in the order of appearance in the function name:\n\n* `i` - perform operation in-place, storing the result in the host object (on\n  which the method was invoked). Might be used to avoid number allocation costs\n* `u` - unsigned, ignore the sign of operands when performing operation, or\n  always return positive value. Second case applies to reduction operations\n  like `mod()`. In such cases if the result will be negative - modulo will be\n  added to the result to make it positive\n\n### Postfixes\n\n* `n` - the argument of the function must be a plain JavaScript\n  Number. Decimals are not supported.\n* `rn` - both argument and return value of the function are plain JavaScript\n  Numbers. Decimals are not supported.\n\n### Examples\n\n* `a.iadd(b)` - perform addition on `a` and `b`, storing the result in `a`\n* `a.umod(b)` - reduce `a` modulo `b`, returning positive value\n* `a.iushln(13)` - shift bits of `a` left by 13\n\n## Instructions\n\nPrefixes/postfixes are put in parens at the of the line. `endian` - could be\neither `le` (little-endian) or `be` (big-endian).\n\n### Utilities\n\n* `a.clone()` - clone number\n* `a.toString(base, length)` - convert to base-string and pad with zeroes\n* `a.toNumber()` - convert to Javascript Number (limited to 53 bits)\n* `a.toJSON()` - convert to JSON compatible hex string (alias of `toString(16)`)\n* `a.toArray(endian, length)` - convert to byte `Array`, and optionally zero\n  pad to length, throwing if already exceeding\n* `a.toArrayLike(type, endian, length)` - convert to an instance of `type`,\n  which must behave like an `Array`\n* `a.toBuffer(endian, length)` - convert to Node.js Buffer (if available). For\n  compatibility with browserify and similar tools, use this instead:\n  `a.toArrayLike(Buffer, endian, length)`\n* `a.bitLength()` - get number of bits occupied\n* `a.zeroBits()` - return number of less-significant consequent zero bits\n  (example: `1010000` has 4 zero bits)\n* `a.byteLength()` - return number of bytes occupied\n* `a.isNeg()` - true if the number is negative\n* `a.isEven()` - no comments\n* `a.isOdd()` - no comments\n* `a.isZero()` - no comments\n* `a.cmp(b)` - compare numbers and return `-1` (a `<` b), `0` (a `==` b), or `1` (a `>` b)\n  depending on the comparison result (`ucmp`, `cmpn`)\n* `a.lt(b)` - `a` less than `b` (`n`)\n* `a.lte(b)` - `a` less than or equals `b` (`n`)\n* `a.gt(b)` - `a` greater than `b` (`n`)\n* `a.gte(b)` - `a` greater than or equals `b` (`n`)\n* `a.eq(b)` - `a` equals `b` (`n`)\n* `a.toTwos(width)` - convert to two's complement representation, where `width` is bit width\n* `a.fromTwos(width)` - convert from two's complement representation, where `width` is the bit width\n* `BN.isBN(object)` - returns true if the supplied `object` is a BN.js instance\n* `BN.max(a, b)` - return `a` if `a` bigger than `b`\n* `BN.min(a, b)` - return `a` if `a` less than `b`\n\n### Arithmetics\n\n* `a.neg()` - negate sign (`i`)\n* `a.abs()` - absolute value (`i`)\n* `a.add(b)` - addition (`i`, `n`, `in`)\n* `a.sub(b)` - subtraction (`i`, `n`, `in`)\n* `a.mul(b)` - multiply (`i`, `n`, `in`)\n* `a.sqr()` - square (`i`)\n* `a.pow(b)` - raise `a` to the power of `b`\n* `a.div(b)` - divide (`divn`, `idivn`)\n* `a.mod(b)` - reduct (`u`, `n`) (but no `umodn`)\n* `a.divRound(b)` - rounded division\n\n### Bit operations\n\n* `a.or(b)` - or (`i`, `u`, `iu`)\n* `a.and(b)` - and (`i`, `u`, `iu`, `andln`) (NOTE: `andln` is going to be replaced\n  with `andn` in future)\n* `a.xor(b)` - xor (`i`, `u`, `iu`)\n* `a.setn(b)` - set specified bit to `1`\n* `a.shln(b)` - shift left (`i`, `u`, `iu`)\n* `a.shrn(b)` - shift right (`i`, `u`, `iu`)\n* `a.testn(b)` - test if specified bit is set\n* `a.maskn(b)` - clear bits with indexes higher or equal to `b` (`i`)\n* `a.bincn(b)` - add `1 << b` to the number\n* `a.notn(w)` - not (for the width specified by `w`) (`i`)\n\n### Reduction\n\n* `a.gcd(b)` - GCD\n* `a.egcd(b)` - Extended GCD results (`{ a: ..., b: ..., gcd: ... }`)\n* `a.invm(b)` - inverse `a` modulo `b`\n\n## Fast reduction\n\nWhen doing lots of reductions using the same modulo, it might be beneficial to\nuse some tricks: like [Montgomery multiplication][0], or using special algorithm\nfor [Mersenne Prime][1].\n\n### Reduction context\n\nTo enable this tricks one should create a reduction context:\n\n```js\nvar red = BN.red(num);\n```\nwhere `num` is just a BN instance.\n\nOr:\n\n```js\nvar red = BN.red(primeName);\n```\n\nWhere `primeName` is either of these [Mersenne Primes][1]:\n\n* `'k256'`\n* `'p224'`\n* `'p192'`\n* `'p25519'`\n\nOr:\n\n```js\nvar red = BN.mont(num);\n```\n\nTo reduce numbers with [Montgomery trick][0]. `.mont()` is generally faster than\n`.red(num)`, but slower than `BN.red(primeName)`.\n\n### Converting numbers\n\nBefore performing anything in reduction context - numbers should be converted\nto it. Usually, this means that one should:\n\n* Convert inputs to reducted ones\n* Operate on them in reduction context\n* Convert outputs back from the reduction context\n\nHere is how one may convert numbers to `red`:\n\n```js\nvar redA = a.toRed(red);\n```\nWhere `red` is a reduction context created using instructions above\n\nHere is how to convert them back:\n\n```js\nvar a = redA.fromRed();\n```\n\n### Red instructions\n\nMost of the instructions from the very start of this readme have their\ncounterparts in red context:\n\n* `a.redAdd(b)`, `a.redIAdd(b)`\n* `a.redSub(b)`, `a.redISub(b)`\n* `a.redShl(num)`\n* `a.redMul(b)`, `a.redIMul(b)`\n* `a.redSqr()`, `a.redISqr()`\n* `a.redSqrt()` - square root modulo reduction context's prime\n* `a.redInvm()` - modular inverse of the number\n* `a.redNeg()`\n* `a.redPow(b)` - modular exponentiation\n\n### Number Size\n\nOptimized for elliptic curves that work with 256-bit numbers.\nThere is no limitation on the size of the numbers.\n\n## LICENSE\n\nThis software is licensed under the MIT License.\n\nCopyright Fedor Indutny, 2015.\n\nPermission is hereby granted, free of charge, to any person obtaining a\ncopy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to permit\npersons to whom the Software is furnished to do so, subject to the\nfollowing conditions:\n\nThe above copyright notice and this permission notice shall be included\nin all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\nOR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\nNO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\nDAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\nOTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\nUSE OR OTHER DEALINGS IN THE SOFTWARE.\n\n[0]: https://en.wikipedia.org/wiki/Montgomery_modular_multiplication\n[1]: https://en.wikipedia.org/wiki/Mersenne_prime\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/bn.js/-/bn.js-5.1.3.tgz#beca005408f642ebebea80b042b4d18d2ac0ee6b", "type": "tarball", "reference": "https://registry.yarnpkg.com/bn.js/-/bn.js-5.1.3.tgz", "hash": "beca005408f642ebebea80b042b4d18d2ac0ee6b", "integrity": "sha512-GkTiFpjFtUzU9CbMeJ5iazkCzGL3jrhzerzZIuqLABjbwRaFt33I9tUdSNryIptM+RxDet6OKm2WnLXzW51KsQ==", "registry": "npm", "packageName": "bn.js", "cacheIntegrity": "sha512-GkTiFpjFtUzU9CbMeJ5iazkCzGL3jrhzerzZIuqLABjbwRaFt33I9tUdSNryIptM+RxDet6OKm2WnLXzW51KsQ== sha1-vsoAVAj2Quvr6oCwQrTRjSrA7ms="}, "registry": "npm", "hash": "beca005408f642ebebea80b042b4d18d2ac0ee6b"}