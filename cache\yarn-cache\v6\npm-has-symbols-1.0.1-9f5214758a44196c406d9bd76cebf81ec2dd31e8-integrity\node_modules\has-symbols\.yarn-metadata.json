{"manifest": {"name": "has-symbols", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "description": "Determine if the JS environment has Symbol support. Supports spec, or shams.", "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "posttest": "npx aud", "tests-only": "npm run --silent test:stock && npm run --silent test:staging && npm run --silent test:shams", "test:stock": "node test", "test:staging": "node --harmony --es-staging test", "test:shams": "npm run --silent test:shams:getownpropertysymbols && npm run --silent test:shams:corejs", "test:shams:corejs": "node test/shams/core-js.js", "test:shams:getownpropertysymbols": "node test/shams/get-own-property-symbols.js", "lint": "eslint *.js", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/ljharb/has-symbols.git"}, "keywords": ["Symbol", "symbols", "typeof", "sham", "polyfill", "native", "core-js", "ES6"], "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^15.0.1", "auto-changelog": "^1.16.2", "core-js": "^2.6.10", "eslint": "^6.6.0", "get-own-property-symbols": "^0.9.4", "safe-publish-latest": "^1.1.4", "tape": "^4.11.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-has-symbols-1.0.1-9f5214758a44196c406d9bd76cebf81ec2dd31e8-integrity\\node_modules\\has-symbols\\package.json", "readmeFilename": "README.md", "readme": "# has-symbols <sup>[![Version Badge][2]][1]</sup>\n\n[![Build Status][3]][4]\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\nDetermine if the JS environment has Symbol support. Supports spec, or shams.\n\n## Example\n\n```js\nvar hasSymbols = require('has-symbols');\n\nhasSymbols() === true; // if the environment has native Symbol support. Not polyfillable, not forgeable.\n\nvar hasSymbolsKinda = require('has-symbols/shams');\nhasSymbolsKinda() === true; // if the environment has a Symbol sham that mostly follows the spec.\n```\n\n## Supported Symbol shams\n - get-own-property-symbols [npm](https://www.npmjs.com/package/get-own-property-symbols) | [github](https://github.com/WebReflection/get-own-property-symbols)\n - core-js [npm](https://www.npmjs.com/package/core-js) | [github](https://github.com/zloirock/core-js)\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[1]: https://npmjs.org/package/has-symbols\n[2]: http://versionbadg.es/ljharb/has-symbols.svg\n[3]: https://travis-ci.org/ljharb/has-symbols.svg\n[4]: https://travis-ci.org/ljharb/has-symbols\n[5]: https://david-dm.org/ljharb/has-symbols.svg\n[6]: https://david-dm.org/ljharb/has-symbols\n[7]: https://david-dm.org/ljharb/has-symbols/dev-status.svg\n[8]: https://david-dm.org/ljharb/has-symbols#info=devDependencies\n[9]: https://ci.testling.com/ljharb/has-symbols.png\n[10]: https://ci.testling.com/ljharb/has-symbols\n[11]: https://nodei.co/npm/has-symbols.png?downloads=true&stars=true\n[license-image]: http://img.shields.io/npm/l/has-symbols.svg\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/has-symbols.svg\n[downloads-url]: http://npm-stat.com/charts.html?package=has-symbols\n", "licenseText": "MIT License\n\nCopyright (c) 2016 Jordan Harband\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/has-symbols/-/has-symbols-1.0.1.tgz#9f5214758a44196c406d9bd76cebf81ec2dd31e8", "type": "tarball", "reference": "https://registry.yarnpkg.com/has-symbols/-/has-symbols-1.0.1.tgz", "hash": "9f5214758a44196c406d9bd76cebf81ec2dd31e8", "integrity": "sha512-PLcsoqu++dmEIZB+6totNFKq/7Do+Z0u4oT0zKOJNl3lYK6vGwwu2hjHs+68OEZbTjiUE9bgOABXbP/GvrS0Kg==", "registry": "npm", "packageName": "has-symbols", "cacheIntegrity": "sha512-PLcsoqu++dmEIZB+6totNFKq/7Do+Z0u4oT0zKOJNl3lYK6vGwwu2hjHs+68OEZbTjiUE9bgOABXbP/GvrS0Kg== sha1-n1IUdYpEGWxAbZvXbOv4HsLdMeg="}, "registry": "npm", "hash": "9f5214758a44196c406d9bd76cebf81ec2dd31e8"}