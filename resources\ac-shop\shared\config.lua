Config = {}
Config.UseTextUI = false --only qb target false
Config.InventoryFolder = "nui://qb-inventory/html/images/"
Config.Text = "textui1" --textui2
--ox nui://ox_inventory/web/images/
--qs nui://qs-inventory/html/images/

Config.PaInv = true --make it true if you are using pa inventory
Config.OldInv = "" --if you are using pa inventory and if you are using ox before pa write ox here if not you don't need to write anything

Config.ServerCallbacks = {}

Config.Notify = {
    ["success"] = "The purchase was successful enjoy",
    ["error"] = "You don't have enough balance"
}

Config.Shops = {
    {
        name = "Market",
        label = "7/24 Market",
        type = "normal",
        blip = true,
        blipSprite = 59,
        blipColor = 2,
        blipScale = 0.5,
        categories = {
            [1] = {
                name = "General",
                description = "Needs",
                items = {
                    {name = "markedbills", label = "Water", perPrice = 150, description = "Drink", metadata = {worth = 100}, image = "water.png"},
                    {name = "blueprint", label = "Blueprint", perPrice = 150, description = "Drink", metadata = {type = 'phone'}, image = "blueprint.png"},
                    {name = "sandwich", label = "Sandwich", perPrice = 150, description = "Food", metadata = {}, image = "sandwich.png"},
                    {name = "twerks_candy", label = "Twerks", perPrice = 150, description = "Food", metadata = {}, image = "twerks_candy.png"},
                    {name = "snikkel_candy", label = "Snikkel", perPrice = 150, description = "Food", metadata = {}, image = "snikkel_candy.png"},
                    {name = "tosti", label = "Grilled Cheese", perPrice = 150, description = "Food", metadata = {}, image = "tosti.png"},
                    {name = "weapon_pistol", label = "Pistol", perPrice = 450, description = "Weapon", metadata = {}, image = "weapon_pistol.png"}
                }
            },
        },
        pedHash = 'mp_m_shopkeep_01',
        scenario = 'WORLD_HUMAN_STAND_MOBILE',
        coords = {
            {ped = nil, coords = vector4(24.47, -1346.62, 29.5, 271.66)},
            {ped = nil, coords = vector4(-3039.54, 584.38, 7.91, 17.27)},
            {ped = nil, coords = vector4(-3242.97, 1000.01, 12.83, 357.57)},
            {ped = nil, coords = vector4(1728.07, 6415.63, 35.04, 242.95)},
            {ped = nil, coords = vector4(1959.82, 3740.48, 32.34, 301.57)},
            {ped = nil, coords = vector4(549.13, 2670.85, 42.16, 99.39)},
            {ped = nil, coords = vector4(2677.47, 3279.76, 55.24, 335.08)},
            {ped = nil, coords = vector4(2556.66, 380.84, 108.62, 356.67)},
            {ped = nil, coords = vector4(372.66, 326.98, 103.57, 253.73)}
        }
    },
    {
        name = "Market",
        label = "LTD Gasoline",
        type = "normal",
        blip = true,
        blipSprite = 59,
        blipColor = 2,
        blipScale = 0.5,
        categories = {
            [1] = {
                name = "Genel",
                description = "malys",
                items = {
                    {name = "markedbills", label = "Su", perPrice = 150, description = "İçecek", metadata = {worth = 100}, image = "markedbills.png"},
                }
            },
        },
        pedHash = 'mp_m_shopkeep_01',
        scenario = 'WORLD_HUMAN_STAND_MOBILE',
        coords = {
            {ped = nil, coords = vector4(-47.02, -1758.23, 29.42, 45.05)},
            {ped = nil, coords = vector4(-706.06, -913.97, 19.22, 88.04)},
            {ped = nil, coords = vector4(-1820.02, 794.03, 138.09, 135.45)},
            {ped = nil, coords = vector4(1164.71, -322.94, 69.21, 101.72)},
            {ped = nil, coords = vector4(1697.87, 4922.96, 42.06, 324.71)}
        }
    },
    {
        name = "Market",
        label = "Rob's Liqour",
        type = "normal",
        blip = true,
        blipSprite = 59,
        blipColor = 2,
        blipScale = 0.5,
        categories = {
            [1] = {
                name = "Furnisher",
                description = "malys",
                items = {
                    {name = "markedbills", label = "Markedbills", perPrice = 150, description = "İçecek", metadata = {worth = 100}, image = "markedbills.png"},
                    {name = "bench", label = "Bench", perPrice = 150, description = "Material", metadata = {}, image = "bench.png"},
                    {name = "advancedbench", label = "Advanced Bench", perPrice = 150, description = "Material", metadata = {}, image = "advancedbench.png"},
                }
            },
        },
        pedHash = 'mp_m_shopkeep_01',
        scenario = 'WORLD_HUMAN_STAND_MOBILE',
        coords = {
            {ped = nil, coords = vector4(-1221.58, -908.15, 12.33, 35.49)},
            {ped = nil, coords = vector4(-1486.59, -377.68, 40.16, 139.51)},
            {ped = nil, coords = vector4(-2966.39, 391.42, 15.04, 87.48)},
            {ped = nil, coords = vector4(1165.17, 2710.88, 38.16, 179.43)},
            {ped = nil, coords = vector4(1134.2, -982.91, 46.42, 277.24)},
            {ped = nil, coords = vector4(45.68, -1749.04, 29.61, 53.13)},
            {ped = nil, coords = vector4(2747.71, 3472.85, 55.67, 255.08)},
        }
    },
    {
        name = "Job Market",
        label = "LSPD Ammunation",
        type = "job",
        jobName = "police",
        blip = false,
        categories = {
            [1] = {
                name = "General",
                description = "malys",
                items = {
                    {name = "radio2", label = "Telsiz", perPrice = 150, description = "Genel", metadata = {}, image = "radio2.png"},
                    {name = "gps", label = "GPS", perPrice = 150, description = "Ekipman", metadata = {}, image = "gps.png"},
                    {name = "armor", label = "Zırh", perPrice = 150, description = "Ekipman", metadata = {}, image = "armor.png"},
                    {name = "ifaks", label = "İfak S", perPrice = 150, description = "Ekipman", metadata = {}, image = "ifaks.png"},
                    {name = "bandage", label = "Bandaj", perPrice = 150, description = "Ekipman", metadata = {}, image = "bandage.png"},
                    {name = "weapon_pistol", label = "Pistol", perPrice = 450, description = "Weapon", metadata = {}, image = "weapon_pistol.png"}
                }
            },
            [2] = {
                name = "Weapon",
                description = "malys",
                items = {
                    {name = "water_bottle", label = "Su", perPrice = 150, description = "İçecek", metadata = {}, image = "water_bottle.png"}
                }
            },
        },
        pedHash = 'ig_andreas',
        scenario = 'WORLD_HUMAN_STAND_MOBILE',
        coords = {
            {ped = nil, coords = vector4(454.11, -980.12, 30.69, 86.65)}
        }
    },

    {
        name = "Job Market",
        label = "Lester",
        type = "job",
        jobName = "",
        blip = false,
        categories = {
            [1] = {
                name = "General",
                description = "malys",
                items = {
                    {name = "ac_vpn", label = "Vpn", perPrice = 150, description = "Genel", metadata = {}, image = "ac_vpn.png"},
                    {name = "ac_hdd", label = "Hdd", perPrice = 150, description = "Ekipman", metadata = {}, image = "ac_hdd.png"},
                    {name = "ac_gpu", label = "Gpu", perPrice = 150, description = "Ekipman", metadata = {}, image = "ac_gpu.png"},
                    {name = "ac_cpu", label = "Gpu", perPrice = 150, description = "Ekipman", metadata = {}, image = "ac_cpu.png"},
                    {name = "weapon_pistol", label = "Pistol", perPrice = 450, description = "Weapon", metadata = {}, image = "weapon_pistol.png"}
                }
            },
        },
        pedHash = 'ig_lestercrest_2',
        scenario = 'WORLD_HUMAN_STAND_MOBILE',
        coords = {
            {ped = nil, coords = vector4(1275.6, -1714.85, 54.77, 18.08)},
        }
    },
    {
        name = "Job Market",
        label = "Race",
        type = "job",
        jobName = "",
        blip = false,
        categories = {
            [1] = {
                name = "General",
                description = "malys",
                items = {
                    {name = "racechip", image="underground_chip.png", label = "Vpn", perPrice = 150, description = "Race Chip", metadata = {}},
                    {name = "trackchip", image ="track_chip.png", label = "Hdd", perPrice = 150, description = "Track Chip", metadata = {}},
                    {name = "racetablet", image ="np_tablet.png", label = "Gpu", perPrice = 150, description = "Race Tablet", metadata = {}},
                }
            },
        },
        pedHash = 'ig_lestercrest_2',
        scenario = 'WORLD_HUMAN_STAND_MOBILE',
        coords = {
            {ped = nil, coords = vector4(1723.0033, 3303.3972, 41.2235, 284.8250)},
        }
    },
}
