{"manifest": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.24", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.87.0", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-iconv-lite-0.4.24-2022b4b25fbddc21d2f524974a474aafe733908b-integrity\\node_modules\\iconv-lite\\package.json", "readmeFilename": "README.md", "readme": "## Pure JS character encoding conversion [![Build Status](https://travis-ci.org/ashtuchkin/iconv-lite.svg?branch=master)](https://travis-ci.org/ashtuchkin/iconv-lite)\n\n * Doesn't need native code compilation. Works on Windows and in sandboxed environments like [Cloud9](http://c9.io).\n * Used in popular projects like [Express.js (body_parser)](https://github.com/expressjs/body-parser), \n   [<PERSON>run<PERSON>](http://gruntjs.com/), [Nodemailer](http://www.nodemailer.com/), [Yeoman](http://yeoman.io/) and others.\n * Faster than [node-iconv](https://github.com/bnoordhuis/node-iconv) (see below for performance comparison).\n * Intuitive encode/decode API\n * Streaming support for Node v0.10+\n * [Deprecated] Can extend Node.js primitives (buffers, streams) to support all iconv-lite encodings.\n * In-browser usage via [Browserify](https://github.com/substack/node-browserify) (~180k gzip compressed with <PERSON><PERSON><PERSON> shim included).\n * Typescript [type definition file](https://github.com/ashtuchkin/iconv-lite/blob/master/lib/index.d.ts) included.\n * React Native is supported (need to explicitly `npm install` two more modules: `buffer` and `stream`).\n * License: MIT.\n\n[![NPM Stats](https://nodei.co/npm/iconv-lite.png?downloads=true&downloadRank=true)](https://npmjs.org/packages/iconv-lite/)\n\n## Usage\n### Basic API\n```javascript\nvar iconv = require('iconv-lite');\n\n// Convert from an encoded buffer to js string.\nstr = iconv.decode(Buffer.from([0x68, 0x65, 0x6c, 0x6c, 0x6f]), 'win1251');\n\n// Convert from js string to an encoded buffer.\nbuf = iconv.encode(\"Sample input string\", 'win1251');\n\n// Check if encoding is supported\niconv.encodingExists(\"us-ascii\")\n```\n\n### Streaming API (Node v0.10+)\n```javascript\n\n// Decode stream (from binary stream to js strings)\nhttp.createServer(function(req, res) {\n    var converterStream = iconv.decodeStream('win1251');\n    req.pipe(converterStream);\n\n    converterStream.on('data', function(str) {\n        console.log(str); // Do something with decoded strings, chunk-by-chunk.\n    });\n});\n\n// Convert encoding streaming example\nfs.createReadStream('file-in-win1251.txt')\n    .pipe(iconv.decodeStream('win1251'))\n    .pipe(iconv.encodeStream('ucs2'))\n    .pipe(fs.createWriteStream('file-in-ucs2.txt'));\n\n// Sugar: all encode/decode streams have .collect(cb) method to accumulate data.\nhttp.createServer(function(req, res) {\n    req.pipe(iconv.decodeStream('win1251')).collect(function(err, body) {\n        assert(typeof body == 'string');\n        console.log(body); // full request body string\n    });\n});\n```\n\n### [Deprecated] Extend Node.js own encodings\n> NOTE: This doesn't work on latest Node versions. See [details](https://github.com/ashtuchkin/iconv-lite/wiki/Node-v4-compatibility).\n\n```javascript\n// After this call all Node basic primitives will understand iconv-lite encodings.\niconv.extendNodeEncodings();\n\n// Examples:\nbuf = new Buffer(str, 'win1251');\nbuf.write(str, 'gbk');\nstr = buf.toString('latin1');\nassert(Buffer.isEncoding('iso-8859-15'));\nBuffer.byteLength(str, 'us-ascii');\n\nhttp.createServer(function(req, res) {\n    req.setEncoding('big5');\n    req.collect(function(err, body) {\n        console.log(body);\n    });\n});\n\nfs.createReadStream(\"file.txt\", \"shift_jis\");\n\n// External modules are also supported (if they use Node primitives, which they probably do).\nrequest = require('request');\nrequest({\n    url: \"http://github.com/\", \n    encoding: \"cp932\"\n});\n\n// To remove extensions\niconv.undoExtendNodeEncodings();\n```\n\n## Supported encodings\n\n *  All node.js native encodings: utf8, ucs2 / utf16-le, ascii, binary, base64, hex.\n *  Additional unicode encodings: utf16, utf16-be, utf-7, utf-7-imap.\n *  All widespread singlebyte encodings: Windows 125x family, ISO-8859 family, \n    IBM/DOS codepages, Macintosh family, KOI8 family, all others supported by iconv library. \n    Aliases like 'latin1', 'us-ascii' also supported.\n *  All widespread multibyte encodings: CP932, CP936, CP949, CP950, GB2312, GBK, GB18030, Big5, Shift_JIS, EUC-JP.\n\nSee [all supported encodings on wiki](https://github.com/ashtuchkin/iconv-lite/wiki/Supported-Encodings).\n\nMost singlebyte encodings are generated automatically from [node-iconv](https://github.com/bnoordhuis/node-iconv). Thank you Ben Noordhuis and libiconv authors!\n\nMultibyte encodings are generated from [Unicode.org mappings](http://www.unicode.org/Public/MAPPINGS/) and [WHATWG Encoding Standard mappings](http://encoding.spec.whatwg.org/). Thank you, respective authors!\n\n\n## Encoding/decoding speed\n\nComparison with node-iconv module (1000x256kb, on MacBook Pro, Core i5/2.6 GHz, Node v0.12.0). \nNote: your results may vary, so please always check on your hardware.\n\n    operation             iconv@2.1.4   iconv-lite@0.4.7\n    ----------------------------------------------------------\n    encode('win1251')     ~96 Mb/s      ~320 Mb/s\n    decode('win1251')     ~95 Mb/s      ~246 Mb/s\n\n## BOM handling\n\n * Decoding: BOM is stripped by default, unless overridden by passing `stripBOM: false` in options\n   (f.ex. `iconv.decode(buf, enc, {stripBOM: false})`).\n   A callback might also be given as a `stripBOM` parameter - it'll be called if BOM character was actually found.\n * If you want to detect UTF-8 BOM when decoding other encodings, use [node-autodetect-decoder-stream](https://github.com/danielgindi/node-autodetect-decoder-stream) module.\n * Encoding: No BOM added, unless overridden by `addBOM: true` option.\n\n## UTF-16 Encodings\n\nThis library supports UTF-16LE, UTF-16BE and UTF-16 encodings. First two are straightforward, but UTF-16 is trying to be\nsmart about endianness in the following ways:\n * Decoding: uses BOM and 'spaces heuristic' to determine input endianness. Default is UTF-16LE, but can be \n   overridden with `defaultEncoding: 'utf-16be'` option. Strips BOM unless `stripBOM: false`.\n * Encoding: uses UTF-16LE and writes BOM by default. Use `addBOM: false` to override.\n\n## Other notes\n\nWhen decoding, be sure to supply a Buffer to decode() method, otherwise [bad things usually happen](https://github.com/ashtuchkin/iconv-lite/wiki/Use-Buffers-when-decoding).  \nUntranslatable characters are set to � or ?. No transliteration is currently supported.  \nNode versions 0.10.31 and 0.11.13 are buggy, don't use them (see #65, #77).  \n\n## Testing\n\n```bash\n$ <NAME_EMAIL>:ashtuchkin/iconv-lite.git\n$ cd iconv-lite\n$ npm install\n$ npm test\n    \n$ # To view performance:\n$ node test/performance.js\n\n$ # To view test coverage:\n$ npm run coverage\n$ open coverage/lcov-report/index.html\n```\n", "licenseText": "Copyright (c) 2011 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/iconv-lite/-/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b", "type": "tarball", "reference": "https://registry.yarnpkg.com/iconv-lite/-/iconv-lite-0.4.24.tgz", "hash": "2022b4b25fbddc21d2f524974a474aafe733908b", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "registry": "npm", "packageName": "iconv-lite", "cacheIntegrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA== sha1-ICK0sl+93CHS9SSXSkdKr+czkIs="}, "registry": "npm", "hash": "2022b4b25fbddc21d2f524974a474aafe733908b"}