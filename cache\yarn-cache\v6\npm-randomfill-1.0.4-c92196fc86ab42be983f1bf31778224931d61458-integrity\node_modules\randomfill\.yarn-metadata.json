{"manifest": {"name": "randomfill", "version": "1.0.4", "description": "random fill from browserify stand alone", "main": "index.js", "scripts": {"test": "standard && node test.js | tspec", "phantom": "zuul --phantom -- test.js", "local": "zuul --local --no-coverage -- test.js"}, "repository": {"type": "git", "url": "https://github.com/crypto-browserify/randomfill.git"}, "keywords": ["crypto", "random"], "author": {}, "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/randomfill/issues"}, "homepage": "https://github.com/crypto-browserify/randomfill", "browser": "browser.js", "devDependencies": {"phantomjs": "^1.9.9", "standard": "^10.0.2", "tap-spec": "^2.1.2", "tape": "^4.6.3", "zuul": "^3.7.2"}, "dependencies": {"randombytes": "^2.0.5", "safe-buffer": "^5.1.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-randomfill-1.0.4-c92196fc86ab42be983f1bf31778224931d61458-integrity\\node_modules\\randomfill\\package.json", "readmeFilename": "README.md", "readme": "randomfill\n===\n\n[![Version](http://img.shields.io/npm/v/randomfill.svg)](https://www.npmjs.org/package/randomfill)\n\nrandomfill from node that works in the browser.  In node you just get crypto.randomBytes, but in the browser it uses .crypto/msCrypto.getRandomValues\n\n```js\nvar randomFill = require('randomfill');\nvar buf\nrandomFill.randomFillSync(16);//get 16 random bytes\nrandomFill.randomFill(16, function (err, resp) {\n  // resp is 16 random bytes\n});\n```\n", "licenseText": "MIT License\n\nCopyright (c) 2017 crypto-browserify\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/randomfill/-/randomfill-1.0.4.tgz#c92196fc86ab42be983f1bf31778224931d61458", "type": "tarball", "reference": "https://registry.yarnpkg.com/randomfill/-/randomfill-1.0.4.tgz", "hash": "c92196fc86ab42be983f1bf31778224931d61458", "integrity": "sha512-87lcbR8+MhcWcUiQ+9e+Rwx8MyR2P7qnt15ynUlbm3TU/fjbgz4GsvfSUDTemtCCtVCqb4ZcEFlyPNTh9bBTLw==", "registry": "npm", "packageName": "randomfill", "cacheIntegrity": "sha512-87lcbR8+MhcWcUiQ+9e+Rwx8MyR2P7qnt15ynUlbm3TU/fjbgz4GsvfSUDTemtCCtVCqb4ZcEFlyPNTh9bBTLw== sha1-ySGW/IarQr6YPxvzF3giSTHWFFg="}, "registry": "npm", "hash": "c92196fc86ab42be983f1bf31778224931d61458"}