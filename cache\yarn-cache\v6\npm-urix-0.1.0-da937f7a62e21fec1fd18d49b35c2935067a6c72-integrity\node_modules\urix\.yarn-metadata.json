{"manifest": {"name": "u<PERSON>", "version": "0.1.0", "author": {"name": "<PERSON>"}, "license": "MIT", "description": "Makes Windows-style paths more unix and URI friendly.", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/lydell/urix.git"}, "keywords": ["path", "url", "uri", "unix", "windows", "backslash", "slash"], "scripts": {"test": "jshint index.js test/ && mocha"}, "devDependencies": {"mocha": "^1.17.1", "jshint": "^2.4.4"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-urix-0.1.0-da937f7a62e21fec1fd18d49b35c2935067a6c72-integrity\\node_modules\\urix\\package.json", "readmeFilename": "readme.md", "readme": "[![Build Status](https://travis-ci.org/lydell/urix.png?branch=master)](https://travis-ci.org/lydell/urix)\n\nOverview\n========\n\nMakes Windows-style paths more unix and URI friendly. Useful if you work with\npaths that eventually will be used in URLs.\n\n```js\nvar urix = require(\"urix\")\n\n// On Windows:\nurix(\"c:\\\\users\\\\<USER>\\\\foo\")\n// /users/you/foo\n\n// On unix-like systems:\nurix(\"c:\\\\users\\\\<USER>\\\\foo\")\n// c:\\users\\<USER>\\foo\n```\n\n\nInstallation\n============\n\n`npm install urix`\n\n```js\nvar urix = require(\"urix\")\n```\n\n\nUsage\n=====\n\n### `urix(path)` ###\n\nOn Windows, replaces all backslashes with slashes and uses a slash instead of a\ndrive letter and a colon for absolute paths.\n\nOn unix-like systems it is a no-op.\n\n\nLicense\n=======\n\n[The X11 (“MIT”) License](LICENSE).\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2013 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/urix/-/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72", "type": "tarball", "reference": "https://registry.yarnpkg.com/urix/-/urix-0.1.0.tgz", "hash": "da937f7a62e21fec1fd18d49b35c2935067a6c72", "integrity": "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=", "registry": "npm", "packageName": "u<PERSON>", "cacheIntegrity": "sha512-Am1ousAhSLBeB9cG/7k7r2R0zj50uDRlZHPGbazid5s9rlF1F/QKYObEKSIunSjIOkJZqwRRLpvewjEkM7pSqg== sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI="}, "registry": "npm", "hash": "da937f7a62e21fec1fd18d49b35c2935067a6c72"}