{"manifest": {"name": "cyclist", "version": "1.0.1", "repository": {"type": "git", "url": "git://github.com/mafintosh/cyclist"}, "description": "Cyclist is an efficient cyclic list implemention.", "dependencies": {}, "keywords": ["circular", "buffer", "ring", "cyclic", "data"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "devDependencies": {"standard": "^3.8.0", "tape": "^4.0.0"}, "bugs": {"url": "https://github.com/mafintosh/cyclist/issues"}, "homepage": "https://github.com/mafintosh/cyclist", "main": "index.js", "scripts": {"test": "standard && tape test.js"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-cyclist-1.0.1-596e9698fd0c80e12038c2b82d6eb1b35b6224d9-integrity\\node_modules\\cyclist\\package.json", "readmeFilename": "README.md", "readme": "# Cyclist\n\nCyclist is an efficient [cyclic list](http://en.wikipedia.org/wiki/Circular_buffer) implemention for Javascript.\nIt is available through npm\n\n```\nnpm install cyclist\n```\n\n[![build status](http://img.shields.io/travis/mafintosh/cyclist.svg?style=flat)](http://travis-ci.org/mafintosh/cyclist)\n\n## What?\n\nCyclist allows you to create a list of fixed size that is cyclic.\nIn a cyclist list the element following the last one is the first one.\nThis property can be really useful when for example trying to order data\npackets that can arrive out of order over a network stream.\n\n## Usage\n\n``` js\nvar cyclist = require('cyclist')\nvar list = cyclist(4)\n\nlist.put(42, 'hello 42') // store something and index 42\nlist.put(43, 'hello 43') // store something and index 43\n\nconsole.log(list.get(42)) // prints hello 42\nconsole.log(list.get(46)) // prints hello 42 again since 46 - 42 == list.size\n```\n\n## API\n\n* `cyclist(size)` creates a new buffer\n* `cyclist#get(index)` get an object stored in the buffer\n* `cyclist#put(index,value)` insert an object into the buffer\n* `cyclist#del(index)` delete an object from an index\n* `cyclist#size` property containing current size of buffer\n\n## License\n\nMIT\n\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015 Mathias Buus\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/cyclist/-/cyclist-1.0.1.tgz#596e9698fd0c80e12038c2b82d6eb1b35b6224d9", "type": "tarball", "reference": "https://registry.yarnpkg.com/cyclist/-/cyclist-1.0.1.tgz", "hash": "596e9698fd0c80e12038c2b82d6eb1b35b6224d9", "integrity": "sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk=", "registry": "npm", "packageName": "cyclist", "cacheIntegrity": "sha512-NJGVKPS81XejHcLhaLJS7plab0fK3slPh11mESeeDq2W4ZI5kUKK/LRRdVDvjJseojbPB7ZwjnyOybg3Igea/A== sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk="}, "registry": "npm", "hash": "596e9698fd0c80e12038c2b82d6eb1b35b6224d9"}