{"manifest": {"name": "pify", "version": "4.0.1", "description": "Promisify a callback-style function", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/pify.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava", "optimization-test": "node --allow-natives-syntax optimization-test.js"}, "files": ["index.js"], "keywords": ["promise", "promises", "promisify", "all", "denodify", "denodeify", "callback", "cb", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "to", "async", "await", "es2015", "bluebird"], "devDependencies": {"ava": "^0.25.0", "pinkie-promise": "^2.0.0", "v8-natives": "^1.1.0", "xo": "^0.23.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-pify-4.0.1-4b2cd25c50d598735c50292224fd8c6df41e3231-integrity\\node_modules\\pify\\package.json", "readmeFilename": "readme.md", "readme": "# pify [![Build Status](https://travis-ci.org/sindresorhus/pify.svg?branch=master)](https://travis-ci.org/sindresorhus/pify)\n\n> Promisify a callback-style function\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-pify?utm_source=npm-pify&utm_medium=referral&utm_campaign=readme\">Get professional support for 'pify' with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n\n---\n\n\n## Install\n\n```\n$ npm install pify\n```\n\n\n## Usage\n\n```js\nconst fs = require('fs');\nconst pify = require('pify');\n\n// Promisify a single function\npify(fs.readFile)('package.json', 'utf8').then(data => {\n\tconsole.log(JSON.parse(data).name);\n\t//=> 'pify'\n});\n\n// Promisify all methods in a module\npify(fs).readFile('package.json', 'utf8').then(data => {\n\tconsole.log(JSON.parse(data).name);\n\t//=> 'pify'\n});\n```\n\n\n## API\n\n### pify(input, [options])\n\nReturns a `Promise` wrapped version of the supplied function or module.\n\n#### input\n\nType: `Function` `Object`\n\nCallback-style function or module whose methods you want to promisify.\n\n#### options\n\n##### multiArgs\n\nType: `boolean`<br>\nDefault: `false`\n\nBy default, the promisified function will only return the second argument from the callback, which works fine for most APIs. This option can be useful for modules like `request` that return multiple arguments. Turning this on will make it return an array of all arguments from the callback, excluding the error argument, instead of just the second argument. This also applies to rejections, where it returns an array of all the callback arguments, including the error.\n\n```js\nconst request = require('request');\nconst pify = require('pify');\n\npify(request, {multiArgs: true})('https://sindresorhus.com').then(result => {\n\tconst [httpResponse, body] = result;\n});\n```\n\n##### include\n\nType: `string[]` `RegExp[]`\n\nMethods in a module to promisify. Remaining methods will be left untouched.\n\n##### exclude\n\nType: `string[]` `RegExp[]`<br>\nDefault: `[/.+(Sync|Stream)$/]`\n\nMethods in a module **not** to promisify. Methods with names ending with `'Sync'` are excluded by default.\n\n##### excludeMain\n\nType: `boolean`<br>\nDefault: `false`\n\nIf given module is a function itself, it will be promisified. Turn this option on if you want to promisify only methods of the module.\n\n```js\nconst pify = require('pify');\n\nfunction fn() {\n\treturn true;\n}\n\nfn.method = (data, callback) => {\n\tsetImmediate(() => {\n\t\tcallback(null, data);\n\t});\n};\n\n// Promisify methods but not `fn()`\nconst promiseFn = pify(fn, {excludeMain: true});\n\nif (promiseFn()) {\n\tpromiseFn.method('hi').then(data => {\n\t\tconsole.log(data);\n\t});\n}\n```\n\n##### errorFirst\n\nType: `boolean`<br>\nDefault: `true`\n\nWhether the callback has an error as the first argument. You'll want to set this to `false` if you're dealing with an API that doesn't have an error as the first argument, like `fs.exists()`, some browser APIs, Chrome Extension APIs, etc.\n\n##### promiseModule\n\nType: `Function`\n\nCustom promise module to use instead of the native one.\n\nCheck out [`pinkie-promise`](https://github.com/floatdrop/pinkie-promise) if you need a tiny promise polyfill.\n\n\n## Related\n\n- [p-event](https://github.com/sindresorhus/p-event) - Promisify an event by waiting for it to be emitted\n- [p-map](https://github.com/sindresorhus/p-map) - Map over promises concurrently\n- [More…](https://github.com/sindresorhus/promise-fun)\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com)\n", "licenseText": "MIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/pify/-/pify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231", "type": "tarball", "reference": "https://registry.yarnpkg.com/pify/-/pify-4.0.1.tgz", "hash": "4b2cd25c50d598735c50292224fd8c6df41e3231", "integrity": "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==", "registry": "npm", "packageName": "pify", "cacheIntegrity": "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g== sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE="}, "registry": "npm", "hash": "4b2cd25c50d598735c50292224fd8c6df41e3231"}