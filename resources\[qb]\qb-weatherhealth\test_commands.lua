-- Test commands for Weather Health System
-- Add these to server console or as admin commands for testing

local QBCore = exports['qb-core']:GetCoreObject()

-- Test command to give weather health items
QBCore.Commands.Add('giveweatheritems', 'Give weather health items for testing (Admin Only)', {{name = 'id', help = 'Player ID (optional)'}}, false, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Check admin permission
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local targetId = args[1] and tonumber(args[1]) or source
    local TargetPlayer = QBCore.Functions.GetPlayer(targetId)
    
    if not TargetPlayer then
        TriggerClientEvent('QBCore:Notify', source, 'Player not found', 'error')
        return
    end
    
    -- Give basic weather health items
    local items = {
        -- Basic medical items
        {item = 'medicine', amount = 5},
        {item = 'heatpack', amount = 3},
        {item = 'coolpack', amount = 3},
        {item = 'thermometer', amount = 1},
        {item = 'vitamin_c', amount = 10},
        {item = 'flu_vaccine', amount = 1},
        {item = 'hand_warmer', amount = 5},
        {item = 'cooling_towel', amount = 2},
        {item = 'energy_drink', amount = 3},
        {item = 'hot_tea', amount = 3},
        {item = 'ice_pack', amount = 2},
        {item = 'blanket', amount = 1},
        {item = 'first_aid_kit', amount = 1},

        -- Advanced medical items
        {item = 'antibiotics', amount = 3},
        {item = 'pain_killers', amount = 5},
        {item = 'cough_syrup', amount = 2},
        {item = 'nasal_spray', amount = 2},
        {item = 'throat_lozenges', amount = 5},
        {item = 'electrolyte_drink', amount = 3},

        -- Winter clothing
        {item = 'winter_coat', amount = 1},
        {item = 'winter_boots', amount = 1},
        {item = 'winter_hat', amount = 1},
        {item = 'winter_gloves', amount = 1},
        {item = 'scarf', amount = 1},

        -- Summer clothing
        {item = 'summer_shirt', amount = 1},
        {item = 'shorts', amount = 1},
        {item = 'sandals', amount = 1},
        {item = 'sun_hat', amount = 1},
        {item = 'sunglasses', amount = 1},

        -- Rain gear
        {item = 'raincoat', amount = 1},
        {item = 'rain_boots', amount = 1},
        {item = 'umbrella', amount = 1},

        -- Special items
        {item = 'weather_radio', amount = 1},
        {item = 'emergency_flare', amount = 2},
        {item = 'survival_kit', amount = 1},
        {item = 'portable_heater', amount = 1},
        {item = 'cooling_vest', amount = 1}
    }
    
    for _, itemData in ipairs(items) do
        TargetPlayer.Functions.AddItem(itemData.item, itemData.amount)
        TriggerClientEvent('inventory:client:ItemBox', TargetPlayer.PlayerData.source, QBCore.Shared.Items[itemData.item], 'add')
    end
    
    TriggerClientEvent('QBCore:Notify', source, string.format('Gave weather health items to %s', TargetPlayer.PlayerData.name), 'success')
    TriggerClientEvent('QBCore:Notify', TargetPlayer.PlayerData.source, 'You received weather health testing items', 'success')
end)

-- Test command to simulate weather scenarios
QBCore.Commands.Add('testweatherscenario', 'Test weather scenario (Admin Only)', {{name = 'scenario', help = 'cold/hot/rain/snow'}}, true, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Check admin permission
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local scenario = args[1]:lower()
    local weatherType = 'CLEAR'
    local message = ''
    
    if scenario == 'cold' then
        weatherType = 'SNOW'
        message = 'Cold weather scenario activated (Snow). Dress warmly!'
    elseif scenario == 'hot' then
        weatherType = 'EXTRASUNNY'
        message = 'Hot weather scenario activated (Extra Sunny). Dress lightly!'
    elseif scenario == 'rain' then
        weatherType = 'RAIN'
        message = 'Rainy weather scenario activated. Seek shelter!'
    elseif scenario == 'snow' then
        weatherType = 'BLIZZARD'
        message = 'Blizzard scenario activated. Find warmth immediately!'
    else
        TriggerClientEvent('QBCore:Notify', source, 'Invalid scenario. Use: cold, hot, rain, snow', 'error')
        return
    end
    
    -- Change weather
    TriggerEvent('qb-weathersync:server:setWeather', weatherType)
    
    -- Notify all players
    TriggerClientEvent('QBCore:Notify', -1, message, 'primary')
    TriggerClientEvent('QBCore:Notify', source, string.format('Weather changed to %s for testing', weatherType), 'success')
end)

-- Test command to check system status
QBCore.Commands.Add('weathersystemstatus', 'Check weather health system status (Admin Only)', {}, false, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Check admin permission
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'You do not have permission to use this command', 'error')
        return
    end
    
    -- Check if system is running
    local isActive = exports['qb-weatherhealth']:isSystemActive and exports['qb-weatherhealth']:isSystemActive() or false
    local currentWeather = exports['qb-weatherhealth']:getCurrentWeather and exports['qb-weatherhealth']:getCurrentWeather() or 'Unknown'
    local stats = exports['qb-weatherhealth']:getWeatherStats and exports['qb-weatherhealth']:getWeatherStats() or {}
    
    local status = string.format(
        "Weather Health System Status:\n" ..
        "Active: %s\n" ..
        "Current Weather: %s\n" ..
        "Players Tracked: %d\n" ..
        "NPCs Tracked: %d\n" ..
        "System Version: 1.0.0",
        isActive and "✅ YES" or "❌ NO",
        currentWeather,
        stats.totalPlayers or 0,
        stats.totalNPCs or 0
    )
    
    TriggerClientEvent('QBCore:Notify', source, status, 'primary', 10000)
end)

-- Test command to force health check
QBCore.Commands.Add('forcehealthcheck', 'Force health check for player (Admin Only)', {{name = 'id', help = 'Player ID (optional)'}}, false, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Check admin permission
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local targetId = args[1] and tonumber(args[1]) or source
    local TargetPlayer = QBCore.Functions.GetPlayer(targetId)
    
    if not TargetPlayer then
        TriggerClientEvent('QBCore:Notify', source, 'Player not found', 'error')
        return
    end
    
    -- Trigger health check on client
    TriggerClientEvent('weatherhealth:client:forceHealthCheck', TargetPlayer.PlayerData.source)
    
    TriggerClientEvent('QBCore:Notify', source, string.format('Forced health check for %s', TargetPlayer.PlayerData.name), 'success')
    TriggerClientEvent('QBCore:Notify', TargetPlayer.PlayerData.source, 'Health check forced by administrator', 'primary')
end)

-- Test command to simulate disease progression
QBCore.Commands.Add('simulatedisease', 'Simulate disease progression (Admin Only)', {{name = 'id', help = 'Player ID'}, {name = 'disease', help = 'Disease type'}, {name = 'severity', help = 'Severity (1-5)'}}, true, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Check admin permission
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'You do not have permission to use this command', 'error')
        return
    end
    
    local targetId = tonumber(args[1])
    local diseaseType = args[2]
    local severity = tonumber(args[3]) or 1
    local TargetPlayer = QBCore.Functions.GetPlayer(targetId)
    
    if not TargetPlayer then
        TriggerClientEvent('QBCore:Notify', source, 'Player not found', 'error')
        return
    end
    
    if not Config.HealthEffects.diseases[diseaseType] then
        TriggerClientEvent('QBCore:Notify', source, 'Invalid disease type', 'error')
        return
    end
    
    if severity < 1 or severity > 5 then
        TriggerClientEvent('QBCore:Notify', source, 'Severity must be between 1-5', 'error')
        return
    end
    
    -- Give disease with specific severity
    TriggerClientEvent('weatherhealth:client:contractDisease', TargetPlayer.PlayerData.source, diseaseType, severity)
    
    TriggerClientEvent('QBCore:Notify', source, string.format('Gave %s %s (severity %d)', TargetPlayer.PlayerData.name, diseaseType, severity), 'success')
    TriggerClientEvent('QBCore:Notify', TargetPlayer.PlayerData.source, string.format('You contracted %s (severity %d) for testing', diseaseType, severity), 'error')
end)

-- Test command to trigger animations
QBCore.Commands.Add('testanimation', 'Test weather animations (Admin Only)', {{name = 'animation', help = 'shiver/cough/sneeze/wipe_sweat'}}, true, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end

    -- Check admin permission
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'You do not have permission to use this command', 'error')
        return
    end

    local animationType = args[1]:lower()
    local validAnimations = {'shiver', 'cough', 'sneeze', 'wipe_sweat'}

    if not table.contains(validAnimations, animationType) then
        TriggerClientEvent('QBCore:Notify', source, 'Invalid animation. Use: shiver, cough, sneeze, wipe_sweat', 'error')
        return
    end

    TriggerClientEvent('weatherhealth:client:playAnimation', source, 'symptom', {symptom = animationType})
    TriggerClientEvent('QBCore:Notify', source, string.format('Playing %s animation', animationType), 'success')
end)

-- Test command to force NPC outfit update
QBCore.Commands.Add('updatenpcs', 'Force update all NPC outfits (Admin Only)', {}, false, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end

    -- Check admin permission
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'You do not have permission to use this command', 'error')
        return
    end

    -- Trigger weather change event to force NPC updates
    local currentWeather = exports['qb-weatherhealth']:getCurrentWeather()
    TriggerClientEvent('weatherhealth:client:weatherChanged', source, currentWeather, 'CLEAR')

    TriggerClientEvent('QBCore:Notify', source, 'Forced NPC outfit update for ' .. currentWeather, 'success')
end)

-- Test command to check nearby NPC clothing
QBCore.Commands.Add('checknpcclothing', 'Check nearby NPC clothing (Admin Only)', {}, false, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end

    -- Check admin permission
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'You do not have permission to use this command', 'error')
        return
    end

    TriggerClientEvent('weatherhealth:client:checkNearbyNPCClothing', source)
end)

-- Utility function for test commands
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

print("^2[WeatherHealth]^7 Test commands loaded")
