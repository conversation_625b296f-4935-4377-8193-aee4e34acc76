{"manifest": {"name": "@types/http-assert", "version": "1.5.1", "description": "TypeScript definitions for http-assert", "license": "MIT", "contributors": [{"name": "j<PERSON><PERSON>", "url": "https://github.com/jkeylu"}, {"name": "<PERSON>", "url": "https://github.com/stripedpajamas"}, {"name": "<PERSON>", "url": "https://github.com/sapfear"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/http-assert"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "25e5ec6bb6a8c0e3ba83fc4a67c444744defd0d4d2707b09649fc09dbb271083", "typeScriptVersion": "2.3", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-http-assert-1.5.1-d775e93630c2469c2f980fc27e3143240335db3b-integrity\\node_modules\\@types\\http-assert\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/http-assert`\n\n# Summary\nThis package contains type definitions for http-assert (https://github.com/jshttp/http-assert).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-assert\n\nAdditional Details\n * Last updated: Mon, 19 Aug 2019 00:51:16 GMT\n * Dependencies: none\n * Global values: none\n\n# Credits\nThese definitions were written by <PERSON><PERSON><PERSON> <https://github.com/jkeylu>, <PERSON> <https://github.com/stripedpajamas>, and <PERSON> <https://github.com/sapfear>.\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation. All rights reserved.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/http-assert/-/http-assert-1.5.1.tgz#d775e93630c2469c2f980fc27e3143240335db3b", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/http-assert/-/http-assert-1.5.1.tgz", "hash": "d775e93630c2469c2f980fc27e3143240335db3b", "integrity": "sha512-PGAK759pxyfXE78NbKxyfRcWYA/KwW17X290cNev/qAsn9eQIxkH4shoNBafH37wewhDG/0p1cHPbK6+SzZjWQ==", "registry": "npm", "packageName": "@types/http-assert", "cacheIntegrity": "sha512-PGAK759pxyfXE78NbKxyfRcWYA/KwW17X290cNev/qAsn9eQIxkH4shoNBafH37wewhDG/0p1cHPbK6+SzZjWQ== sha1-13XpNjDCRpwvmA/CfjFDJAM12zs="}, "registry": "npm", "hash": "d775e93630c2469c2f980fc27e3143240335db3b"}