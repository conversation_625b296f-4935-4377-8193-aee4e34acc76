{"manifest": {"name": "upath", "description": "A proxy to `path`, replacing `\\` with `/` for all results & new methods to normalize & join keeping leading `./` and add, change, default, trim file extensions.", "version": "1.2.0", "homepage": "http://github.com/anodynos/upath/", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["path", "unix", "windows", "extension", "file extension", "replace extension", "change extension", "trim extension", "add extension", "default extension"], "repository": {"type": "git", "url": "git://github.com/anodynos/upath"}, "bugs": {"url": "http://github.com/anodynos/upath/issues", "email": "<EMAIL>"}, "main": "./build/code/upath.js", "types": "./upath.d.ts", "preferGlobal": false, "scripts": {"test": "grunt", "build": "grunt lib"}, "engines": {"node": ">=4", "yarn": "*"}, "devDependencies": {"chai": "~4.0.2", "coffee-script": "1.12.6", "grunt": "0.4.5", "grunt-contrib-watch": "^1.1.0", "grunt-urequire": "0.7.x", "lodash": "^4.17.15", "mocha": "~3.4.2", "uberscore": "0.0.19", "underscore.string": "^3.3.5", "urequire": "0.7.0-beta.33", "urequire-ab-specrunner": "^0.2.5", "urequire-rc-inject-version": "^0.1.6"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-upath-1.2.0-8f66dbcd55a883acdae4408af8b035a5044c1894-integrity\\node_modules\\upath\\package.json", "readmeFilename": "readme.md", "readme": "# upath v1.2.0\n\n[![Build Status](https://travis-ci.org/anodynos/upath.svg?branch=master)](https://travis-ci.org/anodynos/upath)\n[![Up to date Status](https://david-dm.org/anodynos/upath.png)](https://david-dm.org/anodynos/upath)\n\nA drop-in replacement / proxy to node<PERSON><PERSON>'s `path` that:\n\n  * Replaces the windows `\\` with the unix `/` in all string params & results. This has significant positives - see below.\n\n  * Adds **filename extensions** functions `addExt`, `trimExt`, `removeExt`, `changeExt`, and `defaultExt`.\n\n  * Add a `normalizeSafe` function to preserve any meaningful leading `./` & a `normalizeTrim` which additionally trims any useless ending `/`.\n\n  * Plus a helper `toUnix` that simply converts `\\` to `/` and consolidates duplicates.\n\n**Useful note: these docs are actually auto generated from [specs](https://github.com/anodynos/upath/blob/master/source/spec/upath-spec.coffee), running on Linux.**\n\nNotes:\n\n * `upath.sep` is set to `'/'` for seamless replacement (as of 1.0.3).\n\n * upath has no runtime dependencies, except built-in `path` (as of 1.0.4)\n\n * travis-ci tested in node versions 4 to 12\n      \n\n## Why ?\n\nNormal `path` doesn't convert paths to a unified format (ie `/`) before calculating paths (`normalize`, `join`), which can lead to numerous problems.\nAlso path joining, normalization etc on the two formats is not consistent, depending on where it runs. Running `path` on Windows yields different results than when it runs on Linux / Mac.\n\nIn general, if you code your paths logic while developing on Unix/Mac and it runs on Windows, you may run into problems when using `path`.\n\nNote that using **Unix `/` on Windows** works perfectly inside nodejs (and other languages), so there's no reason to stick to the Windows legacy at all.\n\n##### Examples / specs\n        \n\nCheck out the different (improved) behavior to vanilla `path`:\n\n    `upath.normalize(path)`        --returns-->\n\n          ✓ `'c:/windows/nodejs/path'`           --->      `'c:/windows/nodejs/path'`  // equal to `path.normalize()`\n          ✓ `'c:/windows/../nodejs/path'`        --->              `'c:/nodejs/path'`  // equal to `path.normalize()`\n          ✓ `'c:\\\\windows\\\\nodejs\\\\path'`        --->      `'c:/windows/nodejs/path'`  // `path.normalize()` gives `'c:\\windows\\nodejs\\path'`\n          ✓ `'c:\\\\windows\\\\..\\\\nodejs\\\\path'`    --->              `'c:/nodejs/path'`  // `path.normalize()` gives `'c:\\windows\\..\\nodejs\\path'`\n          ✓ `'//windows\\\\unix/mixed'`            --->         `'/windows/unix/mixed'`  // `path.normalize()` gives `'/windows\\unix/mixed'`\n          ✓ `'\\\\windows//unix/mixed'`            --->         `'/windows/unix/mixed'`  // `path.normalize()` gives `'\\windows/unix/mixed'`\n          ✓ `'////\\\\windows\\\\..\\\\unix/mixed/'`   --->                `'/unix/mixed/'`  // `path.normalize()` gives `'/\\windows\\..\\unix/mixed/'`\n        \n\nJoining paths can also be a problem:\n\n    `upath.join(paths...)`        --returns-->\n\n          ✓ `'some/nodejs/deep', '../path'`       --->       `'some/nodejs/path'`  // equal to `path.join()`\n          ✓ `'some/nodejs\\\\windows', '../path'`   --->       `'some/nodejs/path'`  // `path.join()` gives `'some/path'`\n          ✓ `'some\\\\windows\\\\only', '..\\\\path'`   --->      `'some/windows/path'`  // `path.join()` gives `'some\\windows\\only/..\\path'`\n        \n\nParsing with `path.parse()` should also be consistent across OSes:\n\n  `upath.parse(path)`        --returns-->\n\n          ✓ `'c:\\Windows\\Directory\\somefile.ext'`      ---> `{ root: '', dir: 'c:/Windows/Directory', base: 'somefile.ext', ext: '.ext', name: 'somefile' }`\n                                    // `path.parse()` gives `'{ root: '', dir: '', base: 'c:\\\\Windows\\\\Directory\\\\somefile.ext', ext: '.ext', name: 'c:\\\\Windows\\\\Directory\\\\somefile' }'`\n          ✓ `'/root/of/unix/somefile.ext'`             ---> `{ root: '/', dir: '/root/of/unix', base: 'somefile.ext', ext: '.ext', name: 'somefile' }`  // equal to `path.parse()`\n    \n\n## Added functions\n      \n\n#### `upath.toUnix(path)`\n\nJust converts all `` to `/` and consolidates duplicates, without performing any normalization.\n\n##### Examples / specs\n\n    `upath.toUnix(path)`        --returns-->\n\n        ✓ `'.//windows\\//unix//mixed////'`      --->         `'./windows/unix/mixed/'`\n        ✓ `'..///windows\\..\\\\unix/mixed'`       --->      `'../windows/../unix/mixed'`\n      \n\n#### `upath.normalizeSafe(path)`\n\nExactly like `path.normalize(path)`, but it keeps the first meaningful `./`.\n\nNote that the unix `/` is returned everywhere, so windows `\\` is always converted to unix `/`.\n\n##### Examples / specs & how it differs from vanilla `path`\n\n    `upath.normalizeSafe(path)`        --returns-->\n\n        ✓ `''`                               --->                          `'.'`  // equal to `path.normalize()`\n        ✓ `'.'`                              --->                          `'.'`  // equal to `path.normalize()`\n        ✓ `'./'`                             --->                         `'./'`  // equal to `path.normalize()`\n        ✓ `'.//'`                            --->                         `'./'`  // equal to `path.normalize()`\n        ✓ `'.\\\\'`                            --->                         `'./'`  // `path.normalize()` gives `'.\\'`\n        ✓ `'.\\\\//'`                          --->                         `'./'`  // `path.normalize()` gives `'.\\/'`\n        ✓ `'./..'`                           --->                         `'..'`  // equal to `path.normalize()`\n        ✓ `'.//..'`                          --->                         `'..'`  // equal to `path.normalize()`\n        ✓ `'./../'`                          --->                        `'../'`  // equal to `path.normalize()`\n        ✓ `'.\\\\..\\\\'`                        --->                        `'../'`  // `path.normalize()` gives `'.\\..\\'`\n        ✓ `'./../dep'`                       --->                     `'../dep'`  // equal to `path.normalize()`\n        ✓ `'../dep'`                         --->                     `'../dep'`  // equal to `path.normalize()`\n        ✓ `'../path/dep'`                    --->                `'../path/dep'`  // equal to `path.normalize()`\n        ✓ `'../path/../dep'`                 --->                     `'../dep'`  // equal to `path.normalize()`\n        ✓ `'dep'`                            --->                        `'dep'`  // equal to `path.normalize()`\n        ✓ `'path//dep'`                      --->                   `'path/dep'`  // equal to `path.normalize()`\n        ✓ `'./dep'`                          --->                      `'./dep'`  // `path.normalize()` gives `'dep'`\n        ✓ `'./path/dep'`                     --->                 `'./path/dep'`  // `path.normalize()` gives `'path/dep'`\n        ✓ `'./path/../dep'`                  --->                      `'./dep'`  // `path.normalize()` gives `'dep'`\n        ✓ `'.//windows\\\\unix/mixed/'`        --->      `'./windows/unix/mixed/'`  // `path.normalize()` gives `'windows\\unix/mixed/'`\n        ✓ `'..//windows\\\\unix/mixed'`        --->      `'../windows/unix/mixed'`  // `path.normalize()` gives `'../windows\\unix/mixed'`\n        ✓ `'windows\\\\unix/mixed/'`           --->        `'windows/unix/mixed/'`  // `path.normalize()` gives `'windows\\unix/mixed/'`\n        ✓ `'..//windows\\\\..\\\\unix/mixed'`    --->              `'../unix/mixed'`  // `path.normalize()` gives `'../windows\\..\\unix/mixed'`\n      \n\n#### `upath.normalizeTrim(path)`\n\nExactly like `path.normalizeSafe(path)`, but it trims any useless ending `/`.\n\n##### Examples / specs\n\n    `upath.normalizeTrim(path)`        --returns-->\n\n        ✓ `'./'`                          --->                         `'.'`  // `upath.normalizeSafe()` gives `'./'`\n        ✓ `'./../'`                       --->                        `'..'`  // `upath.normalizeSafe()` gives `'../'`\n        ✓ `'./../dep/'`                   --->                    `'../dep'`  // `upath.normalizeSafe()` gives `'../dep/'`\n        ✓ `'path//dep\\\\'`                 --->                  `'path/dep'`  // `upath.normalizeSafe()` gives `'path/dep/'`\n        ✓ `'.//windows\\\\unix/mixed/'`     --->      `'./windows/unix/mixed'`  // `upath.normalizeSafe()` gives `'./windows/unix/mixed/'`\n      \n\n#### `upath.joinSafe([path1][, path2][, ...])`\n\nExactly like `path.join()`, but it keeps the first meaningful `./`.\n\nNote that the unix `/` is returned everywhere, so windows `\\` is always converted to unix `/`.\n\n##### Examples / specs & how it differs from vanilla `path`\n\n    `upath.joinSafe(path)`        --returns-->\n\n        ✓ `'some/nodejs/deep', '../path'`                --->           `'some/nodejs/path'`  // equal to `path.join()`\n        ✓ `'./some/local/unix/', '../path'`              --->          `'./some/local/path'`  // `path.join()` gives `'some/local/path'`\n        ✓ `'./some\\\\current\\\\mixed', '..\\\\path'`         --->        `'./some/current/path'`  // `path.join()` gives `'some\\current\\mixed/..\\path'`\n        ✓ `'../some/relative/destination', '..\\\\path'`   --->      `'../some/relative/path'`  // `path.join()` gives `'../some/relative/destination/..\\path'`\n    \n\n## Added functions for *filename extension* manipulation.\n\n**Happy notes:**\n\n  In all functions you can:\n\n  * use both `.ext` & `ext` - the dot `.` on the extension is always adjusted correctly.\n\n  * omit the `ext` param (pass null/undefined/empty string) and the common sense thing will happen.\n\n  * ignore specific extensions from being considered as valid ones (eg `.min`, `.dev` `.aLongExtIsNotAnExt` etc), hence no trimming or replacement takes place on them.\n\n       \n\n#### `upath.addExt(filename, [ext])`\n\nAdds `.ext` to `filename`, but only if it doesn't already have the exact extension.\n\n##### Examples / specs\n\n    `upath.addExt(filename, 'js')`     --returns-->\n\n        ✓ `'myfile/addExt'`           --->           `'myfile/addExt.js'`\n        ✓ `'myfile/addExt.txt'`       --->       `'myfile/addExt.txt.js'`\n        ✓ `'myfile/addExt.js'`        --->           `'myfile/addExt.js'`\n        ✓ `'myfile/addExt.min.'`      --->      `'myfile/addExt.min..js'`\n        \n\nIt adds nothing if no `ext` param is passed.\n\n    `upath.addExt(filename)`           --returns-->\n\n          ✓ `'myfile/addExt'`           --->              `'myfile/addExt'`\n          ✓ `'myfile/addExt.txt'`       --->          `'myfile/addExt.txt'`\n          ✓ `'myfile/addExt.js'`        --->           `'myfile/addExt.js'`\n          ✓ `'myfile/addExt.min.'`      --->         `'myfile/addExt.min.'`\n      \n\n#### `upath.trimExt(filename, [ignoreExts], [maxSize=7])`\n\nTrims a filename's extension.\n\n  * Extensions are considered to be up to `maxSize` chars long, counting the dot (defaults to 7).\n\n  * An `Array` of `ignoreExts` (eg `['.min']`) prevents these from being considered as extension, thus are not trimmed.\n\n##### Examples / specs\n\n    `upath.trimExt(filename)`          --returns-->\n\n        ✓ `'my/trimedExt.txt'`             --->                 `'my/trimedExt'`\n        ✓ `'my/trimedExt'`                 --->                 `'my/trimedExt'`\n        ✓ `'my/trimedExt.min'`             --->                 `'my/trimedExt'`\n        ✓ `'my/trimedExt.min.js'`          --->             `'my/trimedExt.min'`\n        ✓ `'../my/trimedExt.longExt'`      --->      `'../my/trimedExt.longExt'`\n        \n\nIt is ignoring `.min` & `.dev` as extensions, and considers exts with up to 8 chars.\n\n    `upath.trimExt(filename, ['min', '.dev'], 8)`          --returns-->\n\n          ✓ `'my/trimedExt.txt'`              --->                  `'my/trimedExt'`\n          ✓ `'my/trimedExt.min'`              --->              `'my/trimedExt.min'`\n          ✓ `'my/trimedExt.dev'`              --->              `'my/trimedExt.dev'`\n          ✓ `'../my/trimedExt.longExt'`       --->               `'../my/trimedExt'`\n          ✓ `'../my/trimedExt.longRExt'`      --->      `'../my/trimedExt.longRExt'`\n      \n\n#### `upath.removeExt(filename, ext)`\n\nRemoves the specific `ext` extension from filename, if it has it. Otherwise it leaves it as is.\nAs in all upath functions, it be `.ext` or `ext`.\n\n##### Examples / specs\n\n    `upath.removeExt(filename, '.js')`          --returns-->\n\n        ✓ `'removedExt.js'`          --->          `'removedExt'`\n        ✓ `'removedExt.txt.js'`      --->      `'removedExt.txt'`\n        ✓ `'notRemoved.txt'`         --->      `'notRemoved.txt'`\n\nIt does not care about the length of exts.\n\n    `upath.removeExt(filename, '.longExt')`          --returns-->\n\n        ✓ `'removedExt.longExt'`          --->          `'removedExt'`\n        ✓ `'removedExt.txt.longExt'`      --->      `'removedExt.txt'`\n        ✓ `'notRemoved.txt'`              --->      `'notRemoved.txt'`\n\n\n#### `upath.changeExt(filename, [ext], [ignoreExts], [maxSize=7])`\n\nChanges a filename's extension to `ext`. If it has no (valid) extension, it adds it.\n\n  * Valid extensions are considered to be up to `maxSize` chars long, counting the dot (defaults to 7).\n\n  * An `Array` of `ignoreExts` (eg `['.min']`) prevents these from being considered as extension, thus are not changed - the new extension is added instead.\n\n##### Examples / specs\n\n    `upath.changeExt(filename, '.js')`  --returns-->\n\n        ✓ `'my/module.min'`            --->                `'my/module.js'`\n        ✓ `'my/module.coffee'`         --->                `'my/module.js'`\n        ✓ `'my/module'`                --->                `'my/module.js'`\n        ✓ `'file/withDot.'`            --->             `'file/withDot.js'`\n        ✓ `'file/change.longExt'`      --->      `'file/change.longExt.js'`\n        \n\nIf no `ext` param is given, it trims the current extension (if any).\n\n    `upath.changeExt(filename)`        --returns-->\n\n          ✓ `'my/module.min'`            --->                   `'my/module'`\n          ✓ `'my/module.coffee'`         --->                   `'my/module'`\n          ✓ `'my/module'`                --->                   `'my/module'`\n          ✓ `'file/withDot.'`            --->                `'file/withDot'`\n          ✓ `'file/change.longExt'`      --->         `'file/change.longExt'`\n        \n\nIt is ignoring `.min` & `.dev` as extensions, and considers exts with up to 8 chars.\n\n    `upath.changeExt(filename, 'js', ['min', '.dev'], 8)`        --returns-->\n\n          ✓ `'my/module.coffee'`          --->                 `'my/module.js'`\n          ✓ `'file/notValidExt.min'`      --->      `'file/notValidExt.min.js'`\n          ✓ `'file/notValidExt.dev'`      --->      `'file/notValidExt.dev.js'`\n          ✓ `'file/change.longExt'`       --->               `'file/change.js'`\n          ✓ `'file/change.longRExt'`      --->      `'file/change.longRExt.js'`\n      \n\n#### `upath.defaultExt(filename, [ext], [ignoreExts], [maxSize=7])`\n\nAdds `.ext` to `filename`, only if it doesn't already have _any_ *old* extension.\n\n  * (Old) extensions are considered to be up to `maxSize` chars long, counting the dot (defaults to 7).\n\n  * An `Array` of `ignoreExts` (eg `['.min']`) will force adding default `.ext` even if one of these is present.\n\n##### Examples / specs\n\n    `upath.defaultExt(filename, 'js')`   --returns-->\n\n        ✓ `'fileWith/defaultExt'`              --->              `'fileWith/defaultExt.js'`\n        ✓ `'fileWith/defaultExt.js'`           --->              `'fileWith/defaultExt.js'`\n        ✓ `'fileWith/defaultExt.min'`          --->             `'fileWith/defaultExt.min'`\n        ✓ `'fileWith/defaultExt.longExt'`      --->      `'fileWith/defaultExt.longExt.js'`\n        \n\nIf no `ext` param is passed, it leaves filename intact.\n\n    `upath.defaultExt(filename)`       --returns-->\n\n          ✓ `'fileWith/defaultExt'`              --->                 `'fileWith/defaultExt'`\n          ✓ `'fileWith/defaultExt.js'`           --->              `'fileWith/defaultExt.js'`\n          ✓ `'fileWith/defaultExt.min'`          --->             `'fileWith/defaultExt.min'`\n          ✓ `'fileWith/defaultExt.longExt'`      --->         `'fileWith/defaultExt.longExt'`\n        \n\nIt is ignoring `.min` & `.dev` as extensions, and considers exts with up to 8 chars.\n\n    `upath.defaultExt(filename, 'js', ['min', '.dev'], 8)` --returns-->\n\n          ✓ `'fileWith/defaultExt'`               --->               `'fileWith/defaultExt.js'`\n          ✓ `'fileWith/defaultExt.min'`           --->           `'fileWith/defaultExt.min.js'`\n          ✓ `'fileWith/defaultExt.dev'`           --->           `'fileWith/defaultExt.dev.js'`\n          ✓ `'fileWith/defaultExt.longExt'`       --->          `'fileWith/defaultExt.longExt'`\n          ✓ `'fileWith/defaultExt.longRext'`      --->      `'fileWith/defaultExt.longRext.js'`\n\n\nCopyright(c) 2014-2019 Angelos Pikoulas (<EMAIL>)\n\nPermission is hereby granted, free of charge, to any person\nobtaining a copy of this software and associated documentation\nfiles (the \"Software\"), to deal in the Software without\nrestriction, including without limitation the rights to use,\ncopy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the\nSoftware is furnished to do so, subject to the following\nconditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\nHOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\nWHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\nOTHER DEALINGS IN THE SOFTWARE.\n", "licenseText": "Copyright(c) 2014-2019 <PERSON><PERSON> (<EMAIL>)\n\nPermission is hereby granted, free of charge, to any person\nobtaining a copy of this software and associated documentation\nfiles (the \"Software\"), to deal in the Software without\nrestriction, including without limitation the rights to use,\ncopy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the\nSoftware is furnished to do so, subject to the following\nconditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\nHOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\nWHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\nOTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/upath/-/upath-1.2.0.tgz#8f66dbcd55a883acdae4408af8b035a5044c1894", "type": "tarball", "reference": "https://registry.yarnpkg.com/upath/-/upath-1.2.0.tgz", "hash": "8f66dbcd55a883acdae4408af8b035a5044c1894", "integrity": "sha512-aZwGpamFO61g3OlfT7OQCHqhGnW43ieH9WZeP7QxN/G/jS4jfqUkZxoryvJgVPEcrl5NL/ggHsSmLMHuH64Lhg==", "registry": "npm", "packageName": "upath", "cacheIntegrity": "sha512-aZwGpamFO61g3OlfT7OQCHqhGnW43ieH9WZeP7QxN/G/jS4jfqUkZxoryvJgVPEcrl5NL/ggHsSmLMHuH64Lhg== sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ="}, "registry": "npm", "hash": "8f66dbcd55a883acdae4408af8b035a5044c1894"}