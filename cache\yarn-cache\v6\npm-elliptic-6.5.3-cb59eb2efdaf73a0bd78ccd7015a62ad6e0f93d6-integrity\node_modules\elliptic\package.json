{"name": "elliptic", "version": "6.5.3", "description": "EC cryptography", "main": "lib/elliptic.js", "files": ["lib"], "scripts": {"jscs": "jscs benchmarks/*.js lib/*.js lib/**/*.js lib/**/**/*.js test/index.js", "jshint": "jscs benchmarks/*.js lib/*.js lib/**/*.js lib/**/**/*.js test/index.js", "lint": "npm run jscs && npm run jshint", "unit": "istanbul test _mocha --reporter=spec test/index.js", "test": "npm run lint && npm run unit", "version": "grunt dist && git add dist/"}, "repository": {"type": "git", "url": "**************:indutny/elliptic"}, "keywords": ["EC", "Elliptic", "curve", "Cryptography"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/indutny/elliptic/issues"}, "homepage": "https://github.com/indutny/elliptic", "devDependencies": {"brfs": "^1.4.3", "coveralls": "^3.0.8", "grunt": "^1.0.4", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-connect": "^1.0.0", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-uglify": "^1.0.1", "grunt-mocha-istanbul": "^3.0.1", "grunt-saucelabs": "^9.0.1", "istanbul": "^0.4.2", "jscs": "^3.0.7", "jshint": "^2.10.3", "mocha": "^6.2.2"}, "dependencies": {"bn.js": "^4.4.0", "brorand": "^1.0.1", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.0"}}