{"manifest": {"name": "ajv-keywords", "version": "3.5.2", "description": "Custom JSON-Schema keywords for Ajv validator", "main": "index.js", "typings": "ajv-keywords.d.ts", "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib keywords", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-cov", "eslint": "eslint index.js keywords/*.js spec", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "istanbul cover -x 'spec/**' node_modules/mocha/bin/_mocha -- spec/*.spec.js -R spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["index.js", "ajv-keywords.d.ts", "keywords"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "peerDependencies": {"ajv": "^6.9.1"}, "devDependencies": {"ajv": "^6.9.1", "ajv-pack": "^0.3.0", "chai": "^4.2.0", "coveralls": "^3.0.2", "dot": "^1.1.1", "eslint": "^7.2.0", "glob": "^7.1.3", "istanbul": "^0.4.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "mocha": "^8.0.1", "pre-commit": "^1.1.3", "uuid": "^8.1.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-ajv-keywords-3.5.2-31f29da5ab6e00d1c2d329acf7b5929614d5014d-integrity\\node_modules\\ajv-keywords\\package.json", "readmeFilename": "README.md", "readme": "# ajv-keywords\n\nCustom JSON-Schema keywords for [Ajv](https://github.com/epoberezkin/ajv) validator\n\n[![Build Status](https://travis-ci.org/ajv-validator/ajv-keywords.svg?branch=master)](https://travis-ci.org/ajv-validator/ajv-keywords)\n[![npm](https://img.shields.io/npm/v/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![npm downloads](https://img.shields.io/npm/dm/ajv-keywords.svg)](https://www.npmjs.com/package/ajv-keywords)\n[![Coverage Status](https://coveralls.io/repos/github/ajv-validator/ajv-keywords/badge.svg?branch=master)](https://coveralls.io/github/ajv-validator/ajv-keywords?branch=master)\n[![Dependabot](https://api.dependabot.com/badges/status?host=github&repo=ajv-validator/ajv-keywords)](https://app.dependabot.com/accounts/ajv-validator/repos/********)\n[![Gitter](https://img.shields.io/gitter/room/ajv-validator/ajv.svg)](https://gitter.im/ajv-validator/ajv)\n\n\n## Contents\n\n- [Install](#install)\n- [Usage](#usage)\n- [Keywords](#keywords)\n  - [Types](#types)\n    - [typeof](#typeof)\n    - [instanceof](#instanceof)\n  - [Keywords for numbers](#keywords-for-numbers)\n    - [range and exclusiveRange](#range-and-exclusiverange)\n  - [Keywords for strings](#keywords-for-strings)\n    - [regexp](#regexp)\n    - [formatMaximum / formatMinimum and formatExclusiveMaximum / formatExclusiveMinimum](#formatmaximum--formatminimum-and-formatexclusivemaximum--formatexclusiveminimum)\n    - [transform](#transform)<sup>\\*</sup>\n  - [Keywords for arrays](#keywords-for-arrays)\n    - [uniqueItemProperties](#uniqueitemproperties)\n  - [Keywords for objects](#keywords-for-objects)\n    - [allRequired](#allrequired)\n    - [anyRequired](#anyrequired)\n    - [oneRequired](#onerequired)\n    - [patternRequired](#patternrequired)\n    - [prohibited](#prohibited)\n    - [deepProperties](#deepproperties)\n    - [deepRequired](#deeprequired)\n  - [Compound keywords](#compound-keywords)\n    - [switch](#switch) (deprecated)\n    - [select/selectCases/selectDefault](#selectselectcasesselectdefault) (BETA)\n  - [Keywords for all types](#keywords-for-all-types)\n    - [dynamicDefaults](#dynamicdefaults)<sup>\\*</sup>\n- [Security contact](#security-contact)\n- [Open-source software support](#open-source-software-support)\n- [License](#license)\n\n<sup>\\*</sup> - keywords that modify data\n\n\n## Install\n\n```\nnpm install ajv-keywords\n```\n\n\n## Usage\n\nTo add all available keywords:\n\n```javascript\nvar Ajv = require('ajv');\nvar ajv = new Ajv;\nrequire('ajv-keywords')(ajv);\n\najv.validate({ instanceof: 'RegExp' }, /.*/); // true\najv.validate({ instanceof: 'RegExp' }, '.*'); // false\n```\n\nTo add a single keyword:\n\n```javascript\nrequire('ajv-keywords')(ajv, 'instanceof');\n```\n\nTo add multiple keywords:\n\n```javascript\nrequire('ajv-keywords')(ajv, ['typeof', 'instanceof']);\n```\n\nTo add a single keyword in browser (to avoid adding unused code):\n\n```javascript\nrequire('ajv-keywords/keywords/instanceof')(ajv);\n```\n\n\n## Keywords\n\n### Types\n\n#### `typeof`\n\nBased on JavaScript `typeof` operation.\n\nThe value of the keyword should be a string (`\"undefined\"`, `\"string\"`, `\"number\"`, `\"object\"`, `\"function\"`, `\"boolean\"` or `\"symbol\"`) or array of strings.\n\nTo pass validation the result of `typeof` operation on the value should be equal to the string (or one of the strings in the array).\n\n```\najv.validate({ typeof: 'undefined' }, undefined); // true\najv.validate({ typeof: 'undefined' }, null); // false\najv.validate({ typeof: ['undefined', 'object'] }, null); // true\n```\n\n\n#### `instanceof`\n\nBased on JavaScript `instanceof` operation.\n\nThe value of the keyword should be a string (`\"Object\"`, `\"Array\"`, `\"Function\"`, `\"Number\"`, `\"String\"`, `\"Date\"`, `\"RegExp\"`, `\"Promise\"` or `\"Buffer\"`) or array of strings.\n\nTo pass validation the result of `data instanceof ...` operation on the value should be true:\n\n```\najv.validate({ instanceof: 'Array' }, []); // true\najv.validate({ instanceof: 'Array' }, {}); // false\najv.validate({ instanceof: ['Array', 'Function'] }, function(){}); // true\n```\n\nYou can add your own constructor function to be recognised by this keyword:\n\n```javascript\nfunction MyClass() {}\nvar instanceofDefinition = require('ajv-keywords').get('instanceof').definition;\n// or require('ajv-keywords/keywords/instanceof').definition;\ninstanceofDefinition.CONSTRUCTORS.MyClass = MyClass;\n\najv.validate({ instanceof: 'MyClass' }, new MyClass); // true\n```\n\n\n### Keywords for numbers\n\n#### `range` and `exclusiveRange`\n\nSyntax sugar for the combination of minimum and maximum keywords, also fails schema compilation if there are no numbers in the range.\n\nThe value of this keyword must be the array consisting of two numbers, the second must be greater or equal than the first one.\n\nIf the validated value is not a number the validation passes, otherwise to pass validation the value should be greater (or equal) than the first number and smaller (or equal) than the second number in the array. If `exclusiveRange` keyword is present in the same schema and its value is true, the validated value must not be equal to the range boundaries.\n\n```javascript\nvar schema = { range: [1, 3] };\najv.validate(schema, 1); // true\najv.validate(schema, 2); // true\najv.validate(schema, 3); // true\najv.validate(schema, 0.99); // false\najv.validate(schema, 3.01); // false\n\nvar schema = { range: [1, 3], exclusiveRange: true };\najv.validate(schema, 1.01); // true\najv.validate(schema, 2); // true\najv.validate(schema, 2.99); // true\najv.validate(schema, 1); // false\najv.validate(schema, 3); // false\n```\n\n\n### Keywords for strings\n\n#### `regexp`\n\nThis keyword allows to use regular expressions with flags in schemas (the standard `pattern` keyword does not support flags).\n\nThis keyword applies only to strings. If the data is not a string, the validation succeeds.\n\nThe value of this keyword can be either a string (the result of `regexp.toString()`) or an object with the properties `pattern` and `flags` (the same strings that should be passed to RegExp constructor).\n\n```javascript\nvar schema = {\n  type: 'object',\n  properties: {\n    foo: { regexp: '/foo/i' },\n    bar: { regexp: { pattern: 'bar', flags: 'i' } }\n  }\n};\n\nvar validData = {\n  foo: 'Food',\n  bar: 'Barmen'\n};\n\nvar invalidData = {\n  foo: 'fog',\n  bar: 'bad'\n};\n```\n\n\n#### `formatMaximum` / `formatMinimum` and `formatExclusiveMaximum` / `formatExclusiveMinimum`\n\nThese keywords allow to define minimum/maximum constraints when the format keyword defines ordering.\n\nThese keywords apply only to strings. If the data is not a string, the validation succeeds.\n\nThe value of keyword `formatMaximum` (`formatMinimum`) should be a string. This value is the maximum (minimum) allowed value for the data to be valid as determined by `format` keyword. If `format` is not present schema compilation will throw exception.\n\nWhen this keyword is added, it defines comparison rules for formats `\"date\"`, `\"time\"` and `\"date-time\"`. Custom formats also can have comparison rules. See [addFormat](https://github.com/epoberezkin/ajv#api-addformat) method.\n\nThe value of keyword `formatExclusiveMaximum` (`formatExclusiveMinimum`) should be a boolean value. These keyword cannot be used without `formatMaximum` (`formatMinimum`). If this keyword value is equal to `true`, the data to be valid should not be equal to the value in `formatMaximum` (`formatMinimum`) keyword.\n\n```javascript\nrequire('ajv-keywords')(ajv, ['formatMinimum', 'formatMaximum']);\n\nvar schema = {\n  format: 'date',\n  formatMinimum: '2016-02-06',\n  formatMaximum: '2016-12-27',\n  formatExclusiveMaximum: true\n}\n\nvar validDataList = ['2016-02-06', '2016-12-26', 1];\n\nvar invalidDataList = ['2016-02-05', '2016-12-27', 'abc'];\n```\n\n\n#### `transform`\n\nThis keyword allows a string to be modified before validation. \n\nThese keywords apply only to strings. If the data is not a string, the transform is skipped.\n\nThere are limitation due to how ajv is written:\n- a stand alone string cannot be transformed. ie `data = 'a'; ajv.validate(schema, data);`\n- currently cannot work with `ajv-pack`\n\n**Supported options:**\n- `trim`: remove whitespace from start and end\n- `trimLeft`: remove whitespace from start\n- `trimRight`: remove whitespace from end\n- `toLowerCase`: case string to all lower case\n- `toUpperCase`: case string to all upper case\n- `toEnumCase`: case string to match case in schema\n\nOptions are applied in the order they are listed.\n\nNote: `toEnumCase` requires that all allowed values are unique when case insensitive.\n\n**Example: multiple options**\n```javascript\nrequire('ajv-keywords')(ajv, ['transform']);\n\nvar schema = {\n  type: 'array',\n  items: {\n    type:'string',\n    transform:['trim','toLowerCase']\n  }\n};\n\nvar data = ['  MixCase  '];\najv.validate(schema, data);\nconsole.log(data); // ['mixcase']\n\n```\n\n**Example: `enumcase`**\n```javascript\nrequire('ajv-keywords')(ajv, ['transform']);\n\nvar schema = {\n  type: 'array',\n  items: {\n    type:'string',\n    transform:['trim','toEnumCase'],\n    enum:['pH']\n  }\n};\n\nvar data = ['ph',' Ph','PH','pH '];\najv.validate(schema, data);\nconsole.log(data); // ['pH','pH','pH','pH']\n```\n\n\n### Keywords for arrays\n\n#### `uniqueItemProperties`\n\nThe keyword allows to check that some properties in array items are unique.\n\nThis keyword applies only to arrays. If the data is not an array, the validation succeeds.\n\nThe value of this keyword must be an array of strings - property names that should have unique values across all items.\n\n```javascript\nvar schema = { uniqueItemProperties: [ \"id\", \"name\" ] };\n\nvar validData = [\n  { id: 1 },\n  { id: 2 },\n  { id: 3 }\n];\n\nvar invalidData1 = [\n  { id: 1 },\n  { id: 1 }, // duplicate \"id\"\n  { id: 3 }\n];\n\nvar invalidData2 = [\n  { id: 1, name: \"taco\" },\n  { id: 2, name: \"taco\" }, // duplicate \"name\"\n  { id: 3, name: \"salsa\" }\n];\n```\n\nThis keyword is contributed by [@blainesch](https://github.com/blainesch).\n\n\n### Keywords for objects\n\n#### `allRequired`\n\nThis keyword allows to require the presence of all properties used in `properties` keyword in the same schema object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be boolean.\n\nIf the value of the keyword is `false`, the validation succeeds.\n\nIf the value of the keyword is `true`, the validation succeeds if the data contains all properties defined in `properties` keyword (in the same schema object).\n\nIf the `properties` keyword is not present in the same schema object, schema compilation will throw exception.\n\n```javascript\nvar schema = {\n  properties: {\n    foo: {type: 'number'},\n    bar: {type: 'number'}\n  }\n  allRequired: true\n};\n\nvar validData = { foo: 1, bar: 2 };\nvar alsoValidData = { foo: 1, bar: 2, baz: 3 };\n\nvar invalidDataList = [ {}, { foo: 1 }, { bar: 2 } ];\n```\n\n\n#### `anyRequired`\n\nThis keyword allows to require the presence of any (at least one) property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid at least one of the properties in this array should be present in the object.\n\n```javascript\nvar schema = {\n  anyRequired: ['foo', 'bar']\n};\n\nvar validData = { foo: 1 };\nvar alsoValidData = { foo: 1, bar: 2 };\n\nvar invalidDataList = [ {}, { baz: 3 } ];\n```\n\n\n#### `oneRequired`\n\nThis keyword allows to require the presence of only one property from the list.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword must be an array of strings, each string being a property name. For data object to be valid exactly one of the properties in this array should be present in the object.\n\n```javascript\nvar schema = {\n  oneRequired: ['foo', 'bar']\n};\n\nvar validData = { foo: 1 };\nvar alsoValidData = { bar: 2, baz: 3 };\n\nvar invalidDataList = [ {}, { baz: 3 }, { foo: 1, bar: 2 } ];\n```\n\n\n#### `patternRequired`\n\nThis keyword allows to require the presence of properties that match some pattern(s).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a regular expression. For data object to be valid each regular expression in this array should match at least one property name in the data object.\n\nIf the array contains multiple regular expressions, more than one expression can match the same property name.\n\n```javascript\nvar schema = { patternRequired: [ 'f.*o', 'b.*r' ] };\n\nvar validData = { foo: 1, bar: 2 };\nvar alsoValidData = { foobar: 3 };\n\nvar invalidDataList = [ {}, { foo: 1 }, { bar: 2 } ];\n```\n\n\n#### `prohibited`\n\nThis keyword allows to prohibit that any of the properties in the list is present in the object.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value of this keyword should be an array of strings, each string being a property name. For data object to be valid none of the properties in this array should be present in the object.\n\n```\nvar schema = { prohibited: ['foo', 'bar']};\n\nvar validData = { baz: 1 };\nvar alsoValidData = {};\n\nvar invalidDataList = [\n  { foo: 1 },\n  { bar: 2 },\n  { foo: 1, bar: 2}\n];\n```\n\n__Please note__: `{prohibited: ['foo', 'bar']}` is equivalent to `{not: {anyRequired: ['foo', 'bar']}}` (i.e. it has the same validation result for any data).\n\n\n#### `deepProperties`\n\nThis keyword allows to validate deep properties (identified by JSON pointers).\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an object, where keys are JSON pointers to the data, starting from the current position in data, and the values are JSON schemas. For data object to be valid the value of each JSON pointer should be valid according to the corresponding schema.\n\n```javascript\nvar schema = {\n  type: 'object',\n  deepProperties: {\n    \"/users/1/role\": { \"enum\": [\"admin\"] }\n  }\n};\n\nvar validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: 'admin'\n    }\n  ]\n};\n\nvar alsoValidData = {\n  users: {\n    \"1\": {\n      id: 123,\n      role: 'admin'\n    }\n  }\n};\n\nvar invalidData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: 'user'\n    }\n  ]\n};\n\nvar alsoInvalidData = {\n  users: {\n    \"1\": {\n      id: 123,\n      role: 'user'\n    }\n  }\n};\n```\n\n\n#### `deepRequired`\n\nThis keyword allows to check that some deep properties (identified by JSON pointers) are available.\n\nThis keyword applies only to objects. If the data is not an object, the validation succeeds.\n\nThe value should be an array of JSON pointers to the data, starting from the current position in data. For data object to be valid each JSON pointer should be some existing part of the data.\n\n```javascript\nvar schema = {\n  type: 'object',\n  deepRequired: [\"/users/1/role\"]\n};\n\nvar validData = {\n  users: [\n    {},\n    {\n      id: 123,\n      role: 'admin'\n    }\n  ]\n};\n\nvar invalidData = {\n  users: [\n    {},\n    {\n      id: 123\n    }\n  ]\n};\n```\n\nSee [json-schema-org/json-schema-spec#203](https://github.com/json-schema-org/json-schema-spec/issues/203#issue-197211916) for an example of the equivalent schema without `deepRequired` keyword.\n\n\n### Compound keywords\n\n#### `switch` (deprecated)\n\n__Please note__: this keyword is provided to preserve backward compatibility with previous versions of Ajv. It is strongly recommended to use `if`/`then`/`else` keywords instead, as they have been added to the draft-07 of JSON Schema specification.\n\nThis keyword allows to perform advanced conditional validation.\n\nThe value of the keyword is the array of if/then clauses. Each clause is the object with the following properties:\n\n- `if` (optional) - the value is JSON-schema\n- `then` (required) - the value is JSON-schema or boolean\n- `continue` (optional) - the value is boolean\n\nThe validation process is dynamic; all clauses are executed sequentially in the following way:\n\n1. `if`:\n    1.  `if` property is JSON-schema according to which the data is:\n        1.  valid => go to step 2.\n        2.  invalid => go to the NEXT clause, if this was the last clause the validation of `switch` SUCCEEDS.\n    2.  `if` property is absent => go to step 2.\n2. `then`:\n    1.  `then` property is `true` or it is JSON-schema according to which the data is valid => go to step 3.\n    2.  `then` property is `false` or it is JSON-schema according to which the data is invalid => the validation of `switch` FAILS.\n3. `continue`:\n    1.  `continue` property is `true` => go to the NEXT clause, if this was the last clause the validation of `switch` SUCCEEDS.\n    2.  `continue` property is `false` or absent => validation of `switch` SUCCEEDS.\n\n```javascript\nrequire('ajv-keywords')(ajv, 'switch');\n\nvar schema = {\n  type: 'array',\n  items: {\n    type: 'integer',\n    'switch': [\n      { if: { not: { minimum: 1 } }, then: false },\n      { if: { maximum: 10 }, then: true },\n      { if: { maximum: 100 }, then: { multipleOf: 10 } },\n      { if: { maximum: 1000 }, then: { multipleOf: 100 } },\n      { then: false }\n    ]\n  }\n};\n\nvar validItems = [1, 5, 10, 20, 50, 100, 200, 500, 1000];\n\nvar invalidItems = [1, 0, 2000, 11, 57, 123, 'foo'];\n```\n\nThe above schema is equivalent to (for example):\n\n```javascript\n{\n  type: 'array',\n  items: {\n    type: 'integer',\n    if: { minimum: 1, maximum: 10 },\n    then: true,\n    else: {\n      if: { maximum: 100 },\n      then: { multipleOf: 10 },\n      else: {\n        if: { maximum: 1000 },\n        then: { multipleOf: 100 },\n        else: false\n      }\n    }\n  }\n}\n```\n\n\n#### `select`/`selectCases`/`selectDefault`\n\nThese keywords allow to choose the schema to validate the data based on the value of some property in the validated data.\n\nThese keywords must be present in the same schema object (`selectDefault` is optional).\n\nThe value of `select` keyword should be a [$data reference](https://github.com/epoberezkin/ajv/tree/5.0.2-beta.0#data-reference) that points to any primitive JSON type (string, number, boolean or null) in the data that is validated. You can also use a constant of primitive type as the value of this keyword (e.g., for debugging purposes).\n\nThe value of `selectCases` keyword must be an object where each property name is a possible string representation of the value of `select` keyword and each property value is a corresponding schema (from draft-06 it can be boolean) that must be used to validate the data.\n\nThe value of `selectDefault` keyword is a schema (from draft-06 it can be boolean) that must be used to validate the data in case `selectCases` has no key equal to the stringified value of `select` keyword.\n\nThe validation succeeds in one of the following cases:\n- the validation of data using selected schema succeeds,\n- none of the schemas is selected for validation,\n- the value of select is undefined (no property in the data that the data reference points to).\n\nIf `select` value (in data) is not a primitive type the validation fails.\n\n__Please note__: these keywords require Ajv `$data` option to support [$data reference](https://github.com/epoberezkin/ajv/tree/5.0.2-beta.0#data-reference).\n\n\n```javascript\nrequire('ajv-keywords')(ajv, 'select');\n\nvar schema = {\n  type: object,\n  required: ['kind'],\n  properties: {\n    kind: { type: 'string' }\n  },\n  select: { $data: '0/kind' },\n  selectCases: {\n    foo: {\n      required: ['foo'],\n      properties: {\n        kind: {},\n        foo: { type: 'string' }\n      },\n      additionalProperties: false\n    },\n    bar: {\n      required: ['bar'],\n      properties: {\n        kind: {},\n        bar: { type: 'number' }\n      },\n      additionalProperties: false\n    }\n  },\n  selectDefault: {\n    propertyNames: {\n      not: { enum: ['foo', 'bar'] }\n    }\n  }\n};\n\nvar validDataList = [\n  { kind: 'foo', foo: 'any' },\n  { kind: 'bar', bar: 1 },\n  { kind: 'anything_else', not_bar_or_foo: 'any value' }\n];\n\nvar invalidDataList = [\n  { kind: 'foo' }, // no propery foo\n  { kind: 'bar' }, // no propery bar\n  { kind: 'foo', foo: 'any', another: 'any value' }, // additional property\n  { kind: 'bar', bar: 1, another: 'any value' }, // additional property\n  { kind: 'anything_else', foo: 'any' } // property foo not allowed\n  { kind: 'anything_else', bar: 1 } // property bar not allowed\n];\n```\n\n__Please note__: the current implementation is BETA. It does not allow using relative URIs in $ref keywords in schemas in `selectCases` and `selectDefault` that point outside of these schemas. The workaround is to use absolute URIs (that can point to any (sub-)schema added to Ajv, including those inside the current root schema where `select` is used). See [tests](https://github.com/epoberezkin/ajv-keywords/blob/v2.0.0/spec/tests/select.json#L314).\n\n\n### Keywords for all types\n\n#### `dynamicDefaults`\n\nThis keyword allows to assign dynamic defaults to properties, such as timestamps, unique IDs etc.\n\nThis keyword only works if `useDefaults` options is used and not inside `anyOf` keywords etc., in the same way as [default keyword treated by Ajv](https://github.com/epoberezkin/ajv#assigning-defaults).\n\nThe keyword should be added on the object level. Its value should be an object with each property corresponding to a property name, in the same way as in standard `properties` keyword. The value of each property can be:\n\n- an identifier of default function (a string)\n- an object with properties `func` (an identifier) and `args` (an object with parameters that will be passed to this function during schema compilation - see examples).\n\nThe properties used in `dynamicDefaults` should not be added to `required` keyword (or validation will fail), because unlike `default` this keyword is processed after validation.\n\nThere are several predefined dynamic default functions:\n\n- `\"timestamp\"` - current timestamp in milliseconds\n- `\"datetime\"` - current date and time as string (ISO, valid according to `date-time` format)\n- `\"date\"` - current date as string (ISO, valid according to `date` format)\n- `\"time\"` - current time as string (ISO, valid according to `time` format)\n- `\"random\"` - pseudo-random number in [0, 1) interval\n- `\"randomint\"` - pseudo-random integer number. If string is used as a property value, the function will randomly return 0 or 1. If object `{ func: 'randomint', args: { max: N } }` is used then the default will be an integer number in [0, N) interval.\n- `\"seq\"` - sequential integer number starting from 0. If string is used as a property value, the default sequence will be used. If object `{ func: 'seq', args: { name: 'foo'} }` is used then the sequence with name `\"foo\"` will be used. Sequences are global, even if different ajv instances are used.\n\n```javascript\nvar schema = {\n  type: 'object',\n  dynamicDefaults: {\n    ts: 'datetime',\n    r: { func: 'randomint', args: { max: 100 } },\n    id: { func: 'seq', args: { name: 'id' } }\n  },\n  properties: {\n    ts: {\n      type: 'string',\n      format: 'date-time'\n    },\n    r: {\n      type: 'integer',\n      minimum: 0,\n      exclusiveMaximum: 100\n    },\n    id: {\n      type: 'integer',\n      minimum: 0\n    }\n  }\n};\n\nvar data = {};\najv.validate(data); // true\ndata; // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n\nvar data1 = {};\najv.validate(data1); // true\ndata1; // { ts: '2016-12-01T22:07:29.832Z', r: 68, id: 1 }\n\najv.validate(data1); // true\ndata1; // didn't change, as all properties were defined\n```\n\nWhen using the `useDefaults` option value `\"empty\"`, properties and items equal to `null` or `\"\"` (empty string) will be considered missing and assigned defaults.  Use the `allOf` [compound keyword](https://github.com/epoberezkin/ajv/blob/master/KEYWORDS.md#compound-keywords) to execute `dynamicDefaults` before validation.\n\n```javascript\nvar schema = {\n  allOf: [\n    {\n      dynamicDefaults: {\n        ts: 'datetime',\n        r: { func: 'randomint', args: { min: 5, max: 100 } },\n        id: { func: 'seq', args: { name: 'id' } }\n      }\n    },\n    {\n      type: 'object',\n      properties: {\n        ts: {\n          type: 'string'\n        },\n        r: {\n          type: 'number',\n          minimum: 5,\n          exclusiveMaximum: 100\n        },\n        id: {\n          type: 'integer',\n          minimum: 0\n        }\n      }\n    }\n  ]\n};\n\nvar data = { ts: '', r: null };\najv.validate(data); // true\ndata; // { ts: '2016-12-01T22:07:28.829Z', r: 25, id: 0 }\n```\n\nYou can add your own dynamic default function to be recognised by this keyword:\n\n```javascript\nvar uuid = require('uuid');\n\nfunction uuidV4() { return uuid.v4(); }\n\nvar definition = require('ajv-keywords').get('dynamicDefaults').definition;\n// or require('ajv-keywords/keywords/dynamicDefaults').definition;\ndefinition.DEFAULTS.uuid = uuidV4;\n\nvar schema = {\n  dynamicDefaults: { id: 'uuid' },\n  properties: { id: { type: 'string', format: 'uuid' } }\n};\n\nvar data = {};\najv.validate(schema, data); // true\ndata; // { id: 'a1183fbe-697b-4030-9bcc-cfeb282a9150' };\n\nvar data1 = {};\najv.validate(schema, data1); // true\ndata1; // { id: '5b008de7-1669-467a-a5c6-70fa244d7209' }\n```\n\nYou also can define dynamic default that accepts parameters, e.g. version of uuid:\n\n```javascript\nvar uuid = require('uuid');\n\nfunction getUuid(args) {\n  var version = 'v' + (arvs && args.v || 4);\n  return function() {\n    return uuid[version]();\n  };\n}\n\nvar definition = require('ajv-keywords').get('dynamicDefaults').definition;\ndefinition.DEFAULTS.uuid = getUuid;\n\nvar schema = {\n  dynamicDefaults: {\n    id1: 'uuid', // v4\n    id2: { func: 'uuid', v: 4 }, // v4\n    id3: { func: 'uuid', v: 1 } // v1\n  }\n};\n```\n\n\n## Security contact\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\nPlease do NOT report security vulnerabilities via GitHub issues.\n\n\n## Open-source software support\n\nAjv-keywords is a part of [Tidelift subscription](https://tidelift.com/subscription/pkg/npm-ajv-keywords?utm_source=npm-ajv-keywords&utm_medium=referral&utm_campaign=readme) - it provides a centralised support to open-source software users, in addition to the support provided by software maintainers.\n\n\n## License\n\n[MIT](https://github.com/epoberezkin/ajv-keywords/blob/master/LICENSE)\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016 <PERSON><PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz#31f29da5ab6e00d1c2d329acf7b5929614d5014d", "type": "tarball", "reference": "https://registry.yarnpkg.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "hash": "31f29da5ab6e00d1c2d329acf7b5929614d5014d", "integrity": "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==", "registry": "npm", "packageName": "ajv-keywords", "cacheIntegrity": "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ== sha1-MfKdpatuANHC0yms97WSlhTVAU0="}, "registry": "npm", "hash": "31f29da5ab6e00d1c2d329acf7b5929614d5014d"}