{"manifest": {"name": "media-typer", "description": "Simple RFC 6838 media type parser and formatter", "version": "0.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/media-typer.git"}, "devDependencies": {"istanbul": "0.3.2", "mocha": "~1.21.4", "should": "~4.0.4"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-media-typer-0.3.0-8710d7af0aa626f8fffa1ce00168545263255748-integrity\\node_modules\\media-typer\\package.json", "readmeFilename": "README.md", "readme": "# media-typer\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nSimple RFC 6838 media type parser\n\n## Installation\n\n```sh\n$ npm install media-typer\n```\n\n## API\n\n```js\nvar typer = require('media-typer')\n```\n\n### typer.parse(string)\n\n```js\nvar obj = typer.parse('image/svg+xml; charset=utf-8')\n```\n\nParse a media type string. This will return an object with the following\nproperties (examples are shown for the string `'image/svg+xml; charset=utf-8'`):\n\n - `type`: The type of the media type (always lower case). Example: `'image'`\n\n - `subtype`: The subtype of the media type (always lower case). Example: `'svg'`\n\n - `suffix`: The suffix of the media type (always lower case). Example: `'xml'`\n\n - `parameters`: An object of the parameters in the media type (name of parameter always lower case). Example: `{charset: 'utf-8'}`\n\n### typer.parse(req)\n\n```js\nvar obj = typer.parse(req)\n```\n\nParse the `content-type` header from the given `req`. Short-cut for\n`typer.parse(req.headers['content-type'])`.\n\n### typer.parse(res)\n\n```js\nvar obj = typer.parse(res)\n```\n\nParse the `content-type` header set on the given `res`. Short-cut for\n`typer.parse(res.getHeader('content-type'))`.\n\n### typer.format(obj)\n\n```js\nvar obj = typer.format({type: 'image', subtype: 'svg', suffix: 'xml'})\n```\n\nFormat an object into a media type string. This will return a string of the\nmime type for the given object. For the properties of the object, see the\ndocumentation for `typer.parse(string)`.\n\n## License\n\n[MIT](LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/media-typer.svg?style=flat\n[npm-url]: https://npmjs.org/package/media-typer\n[node-version-image]: https://img.shields.io/badge/node.js-%3E%3D_0.6-brightgreen.svg?style=flat\n[node-version-url]: http://nodejs.org/download/\n[travis-image]: https://img.shields.io/travis/jshttp/media-typer.svg?style=flat\n[travis-url]: https://travis-ci.org/jshttp/media-typer\n[coveralls-image]: https://img.shields.io/coveralls/jshttp/media-typer.svg?style=flat\n[coveralls-url]: https://coveralls.io/r/jshttp/media-typer\n[downloads-image]: https://img.shields.io/npm/dm/media-typer.svg?style=flat\n[downloads-url]: https://npmjs.org/package/media-typer\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2014 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/media-typer/-/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748", "type": "tarball", "reference": "https://registry.yarnpkg.com/media-typer/-/media-typer-0.3.0.tgz", "hash": "8710d7af0aa626f8fffa1ce00168545263255748", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "registry": "npm", "packageName": "media-typer", "cacheIntegrity": "sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ== sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="}, "registry": "npm", "hash": "8710d7af0aa626f8fffa1ce00168545263255748"}