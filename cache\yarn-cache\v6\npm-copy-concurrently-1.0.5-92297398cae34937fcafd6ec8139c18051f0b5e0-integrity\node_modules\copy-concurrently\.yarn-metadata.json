{"manifest": {"name": "copy-concurrently", "version": "1.0.5", "description": "Promises of copies of files, directories and symlinks, with concurrency controls and win32 junction fallback.", "main": "copy.js", "scripts": {"test": "standard && tap --coverage test"}, "keywords": ["copy", "cpr"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "dependencies": {"aproba": "^1.1.1", "fs-write-stream-atomic": "^1.0.8", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.0"}, "devDependencies": {"standard": "^8.6.0", "tacks": "^1.2.6", "tap": "^10.1.1"}, "files": ["copy.js", "is-windows.js"], "repository": {"type": "git", "url": "git+https://github.com/npm/copy-concurrently.git"}, "bugs": {"url": "https://github.com/npm/copy-concurrently/issues"}, "homepage": "https://www.npmjs.com/package/copy-concurrently", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-copy-concurrently-1.0.5-92297398cae34937fcafd6ec8139c18051f0b5e0-integrity\\node_modules\\copy-concurrently\\package.json", "readmeFilename": "README.md", "readme": "# copy-concurrently\n\nCopy files, directories and symlinks\n\n```\nconst copy = require('copy-concurrently')\ncopy('/path/to/thing', '/new/path/thing').then(() => {\n  // this is now copied\n}).catch(err => {\n  // oh noooo\n})\n```\n\nCopies files, directories and symlinks.  Ownership is maintained when\nrunning as root, permissions are always maintained.  On Windows, if symlinks\nare unavailable then junctions will be used.\n\n## PUBLIC INTERFACE\n\n### copy(from, to, [options]) → Promise\n\nRecursively copies `from` to `to` and resolves its promise when finished. \nIf `to` already exists then the promise will be rejected with an `EEXIST`\nerror.\n\nOptions are:\n\n* maxConcurrency – (Default: `1`) The maximum number of concurrent copies to do at once.\n* recurseWith - (Default: `copy.item`) The function to call on each file after recursing into a directory.\n* isWindows - (Default: `process.platform === 'win32'`) If true enables Windows symlink semantics. This requires\n  an extra `stat` to determine if the destination of a symlink is a file or directory. If symlinking a directory\n  fails then we'll try making a junction instead.\n\nOptions can also include dependency injection:\n\n* Promise - (Default: `global.Promise`) The promise implementation to use, defaults to Node's.\n* fs - (Default: `require('fs')`) The filesystem module to use.  Can be used\n  to use `graceful-fs` or to inject a mock.\n* writeStreamAtomic - (Default: `require('fs-write-stream-atomic')`) The\n  implementation of `writeStreamAtomic` to use.  Used to inject a mock.\n* getuid - (Default: `process.getuid`) A function that returns the current UID. Used to inject a mock.\n\n## EXTENSION INTERFACE\n\nOrdinarily you'd only call `copy` above.  But it's possible to use it's\ncomponent functions directly.  This is useful if, say, you're writing\n[move-concurently](https://npmjs.com/package/move-concurrently).\n\n### copy.file(from, to, options) → Promise\n\nCopies an ordinary file `from` to destination `to`.  Uses\n`fs-write-stream-atomic` to ensure that the file is either entirely copied\nor not at all.\n\nOptions are:\n\n* uid, gid - (Optional) If `getuid()` is `0` then this and gid will be used to\n  set the user and group of `to`.  If uid is present then gid must be too.\n* mode - (Optional) If set then `to` will have its perms set to `mode`.\n* fs - (Default: `require('fs')`) The filesystem module to use.  Can be used\n  to use `graceful-fs` or to inject a mock.\n* Promise - (Default: `global.Promise`) The promise implementation to use, defaults to Node's.\n* writeStreamAtomic - (Default `require('fs-write-stream-atomic')`) The\n  implementation of `writeStreamAtomic` to use.  Used to inject a mock.\n\n### copy.symlink(from, to, options) → Promise\n\nCopies a symlink `from` to destination `to`.  If you're using Windows and\nsymlinking fails and what you're linking is a directory then junctions will\nbe tried instead.\n\nOptions are:\n\n* top - The top level the copy is being run from.  This is used to determine\n  if the symlink destination is within the set of files we're copying or\n  outside it.\n* fs - (Default: `require('fs')`) The filesystem module to use.  Can be used\n  to use `graceful-fs` or to inject a mock.\n* Promise - (Default: `global.Promise`) The promise implementation to use, defaults to Node's.\n* isWindows - (Default: `process.platform === 'win32'`) If true enables Windows symlink semantics. This requires\n  an extra `stat` to determine if the destination of a symlink is a file or directory. If symlinking a directory\n  fails then we'll try making a junction instead.\n\n### copy.recurse(from, to, options) → Promise\n\nReads all of the files in directory `from` and adds them to the `queue`\nusing `recurseWith` (by default `copy.item`).\n\nOptions are:\n\n* queue - A [`run-queue`](https://npmjs.com/package/run-queue) object to add files found inside `from` to.\n* recurseWith - (Default: `copy.item`) The function to call on each file after recursing into a directory.\n* uid, gid - (Optional) If `getuid()` is `0` then this and gid will be used to\n  set the user and group of `to`.  If uid is present then gid must be too.\n* mode - (Optional) If set then `to` will have its perms set to `mode`.\n* fs - (Default: `require('fs')`) The filesystem module to use.  Can be used\n  to use `graceful-fs` or to inject a mock.\n* getuid - (Default: `process.getuid`) A function that returns the current UID. Used to inject a mock.\n\n### copy.item(from, to, options) → Promise\n\nCopies some kind of `from` to destination `to`.  This looks at the filetype\nand calls `copy.file`, `copy.symlink` or `copy.recurse` as appropriate.\n\nSymlink copies are queued with a priority such that they happen after all\nfile and directory copies as you can't create a junction on windows to a\nfile that doesn't exist yet.\n\nOptions are:\n\n* top - The top level the copy is being run from.  This is used to determine\n  if the symlink destination is within the set of files we're copying or\n  outside it.\n* queue - The [`run-queue`](https://npmjs.com/package/run-queue) object to\n  pass to `copy.recurse` if `from` is a directory.\n* recurseWith - (Default: `copy.item`) The function to call on each file after recursing into a directory.\n* uid, gid - (Optional) If `getuid()` is `0` then this and gid will be used to\n  set the user and group of `to`.  If uid is present then gid must be too.\n* mode - (Optional) If set then `to` will have its perms set to `mode`.\n* fs - (Default: `require('fs')`) The filesystem module to use.  Can be used\n  to use `graceful-fs` or to inject a mock.\n* getuid - (Default: `process.getuid`) A function that returns the current UID. Used to inject a mock.\n* isWindows - (Default: `process.platform === 'win32'`) If true enables Windows symlink semantics. This requires\n  an extra `stat` to determine if the destination of a symlink is a file or directory. If symlinking a directory\n  fails then we'll try making a junction instead.\n* Promise - (Default: `global.Promise`) The promise implementation to use, defaults to Node's.\n* writeStreamAtomic - (Default `require('fs-write-stream-atomic')`) The\n  implementation of `writeStreamAtomic` to use.  Used to inject a mock.\n", "licenseText": "Copyright (c) 2017, <PERSON> <<EMAIL>>\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\nOR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/copy-concurrently/-/copy-concurrently-1.0.5.tgz#92297398cae34937fcafd6ec8139c18051f0b5e0", "type": "tarball", "reference": "https://registry.yarnpkg.com/copy-concurrently/-/copy-concurrently-1.0.5.tgz", "hash": "92297398cae34937fcafd6ec8139c18051f0b5e0", "integrity": "sha512-f2domd9fsVDFtaFcbaRZuYXwtdmnzqbADSwhSWYxYB/Q8zsdUUFMXVRwXGDMWmbEzAn1kdRrtI1T/KTFOL4X2A==", "registry": "npm", "packageName": "copy-concurrently", "cacheIntegrity": "sha512-f2domd9fsVDFtaFcbaRZuYXwtdmnzqbADSwhSWYxYB/Q8zsdUUFMXVRwXGDMWmbEzAn1kdRrtI1T/KTFOL4X2A== sha1-kilzmMrjSTf8r9bsgTnBgFHwteA="}, "registry": "npm", "hash": "92297398cae34937fcafd6ec8139c18051f0b5e0"}