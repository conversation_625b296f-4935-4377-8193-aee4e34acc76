Core = nil
CoreName = nil
CoreReady = false
Citizen.CreateThread(function()
    for k, v in pairs(Cores) do
        if GetResourceState(v.ResourceName) == "starting" or GetResourceState(v.ResourceName) == "started" then
            CoreName = v.ConstantName
            Core = v.GetFramework()
            CoreReady = true
            PlayerData = GetPlayerData()
        end
    end
end)

Config.ServerCallbacks = {}
function TriggerCallback(name, cb, ...)
    Config.ServerCallbacks[name] = cb
    TriggerServerEvent('0r-clothing:server:triggerCallback', name, ...)
end

RegisterNetEvent('0r-clothing:client:triggerCallback', function(name, ...)
    if Config.ServerCallbacks[name] then
        Config.ServerCallbacks[name](...)
        Config.ServerCallbacks[name] = nil
    end
end)

function Notify(text, type, length)
    length = length or 5000
    if GetResourceState("origen_notify") == "started" then
        return exports["origen_notify"]:ShowNotification(text) 
    end
    if CoreName == "qb" then
        Core.Functions.Notify(text, type, length)
    elseif CoreName == "esx" then
        Core.ShowNotification(text)
    end
end

function GetPlayerData()
    if CoreName == "qb" then
        local player = Core.Functions.GetPlayerData()
        return player
    elseif CoreName == "esx" then
        local player = Core.GetPlayerData()
        return player
    end
end

Citizen.CreateThread(function()
    for k, _ in pairs (Config.Stores) do
        if Config.Stores[k].shopType == "clothing" then
            if Config.Stores[k].hideBlip then return end
            local clothingShop = AddBlipForCoord(Config.Stores[k].coords)
            SetBlipSprite(clothingShop, 366)
            SetBlipColour(clothingShop, 47)
            SetBlipScale (clothingShop, 0.7)
            SetBlipAsShortRange(clothingShop, true)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(Lang:t("store.clothing"))
            EndTextCommandSetBlipName(clothingShop)
        end
        if Config.Stores[k].shopType == "barber" then
            if Config.Stores[k].hideBlip then return end
            local barberShop = AddBlipForCoord(Config.Stores[k].coords)
            SetBlipSprite(barberShop, 71)
            SetBlipColour(barberShop, 0)
            SetBlipScale (barberShop, 0.7)
            SetBlipAsShortRange(barberShop, true)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(Lang:t("store.barber"))
            EndTextCommandSetBlipName(barberShop)
        end
        if Config.Stores[k].shopType == "tattoo" then
            if Config.Stores[k].hideBlip then return end
            local tatooShop = AddBlipForCoord(Config.Stores[k].coords)
            SetBlipSprite(tatooShop, 75)
            SetBlipColour(tatooShop, 4)
            SetBlipScale (tatooShop, 0.7)
            SetBlipAsShortRange(tatooShop, true)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(Lang:t("store.tattoo"))
            EndTextCommandSetBlipName(tatooShop)
        end
        if Config.Stores[k].shopType == "surgeon" then
            if Config.Stores[k].hideBlip then return end
            local surgeonShop = AddBlipForCoord(Config.Stores[k].coords)
            SetBlipSprite(surgeonShop, 71)
            SetBlipColour(surgeonShop, 0)
            SetBlipScale  (surgeonShop, 0.7)
            SetBlipAsShortRange(surgeonShop, true)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(Lang:t("store.surgeon"))
            EndTextCommandSetBlipName(surgeonShop)
        end
    end
end)

function canOpenStore(jtype, name)
    if not name then return true end
    if name == "all" then return true end
    local myJob = nil
    if CoreName == "qb" then
        myJob = Core.Functions.GetPlayerData().job.name
        local myGang = Core.Functions.GetPlayerData().gang.name
        if jtype == "gang" then
            if myGang == name then
                return true
            end
        end
        if jtype == "job" then
            if myJob == name then
                return true
            end
        end
        return false
    elseif CoreName == "esx" then
        local player = Core.GetPlayerData()
        myJob =  player.job.name
        if jtype == "job" then
            if myJob == name then
                return true
            end
        end
        return false
    end
end

Citizen.CreateThread(function()
    if Config.Interaction.TextUI.Enable then
        closestClothingArea = {}
        local showTextUI = false
        Citizen.CreateThread(function()
            while true do
                local sleep = 100
                if not menuActive then
                    playerPed = PlayerPedId()
                    playerCoords = GetEntityCoords(playerPed)
                    if not closestClothingArea.id then
                        for k, v in pairs(Config.Stores) do
                            local dist = #(playerCoords - vector3(v.coords.x, v.coords.y, v.coords.z))
                            if dist <= 5.0 then
                                if canOpenStore(v.groupType, v.group) then
                                    function currentShow()
                                        local label = Lang:t("interaction.get_stylish")
                                        if v.shopType == "barber" then
                                            label = Lang:t("interaction.barber_shop")
                                        elseif v.shopType == "tattoo" then
                                            label = Lang:t("interaction.tattoo_shop")
                                        elseif v.shopType == "surgeon" then
                                            label = Lang:t("interaction.surgeon")
                                        end
                                        Config.Interaction.TextUI.Show(label)
                                        showTextUI = true
                                    end
                                    function currentHide()
                                        local label = Lang:t("interaction.get_stylish")
                                        if v.shopType == "barber" then
                                            label = Lang:t("interaction.barber_shop")
                                        elseif v.shopType == "tattoo" then
                                            label = Lang:t("interaction.tattoo_shop")
                                        elseif v.shopType == "surgeon" then
                                            label = Lang:t("interaction.surgeon")
                                        end
                                        Config.Interaction.TextUI.Hide(label)
                                    end
                                    closestClothingArea = {id = k, distance = dist, maxDist = 5.0, type = v.shopType, data = {coords = vector3(v.coords.x, v.coords.y, v.coords.z)}}
                                end
                            end
                        end
                    end
                    if closestClothingArea.id then
                        while true do
                            playerPed = PlayerPedId()
                            playerCoords = GetEntityCoords(playerPed)
                            closestClothingArea.distance = #(vector3(closestClothingArea.data.coords.x, closestClothingArea.data.coords.y, closestClothingArea.data.coords.z) - playerCoords)
                            if closestClothingArea.distance < closestClothingArea.maxDist then
                                if IsControlJustReleased(0, 38) then
                                    openClothStore(closestClothingArea.type)
                                end
                                if not showTextUI then
                                    currentShow()
                                end
                            else
                                currentHide()
                                break
                            end
                            Citizen.Wait(0)
                        end
                        showTextUI = false
                        closestClothingArea = {}
                        sleep = 0
                    end
                end
                Citizen.Wait(sleep)
            end
        end)
    else
        for k, v in pairs(Config.Stores) do
            local label = Lang:t("interaction.get_stylish")
            if v.shopType == "barber" then
                label = Lang:t("interaction.barber_shop")
            elseif v.shopType == "tattoo" then
                label = Lang:t("interaction.tattoo_shop")
            elseif v.shopType == "surgeon" then
                label = Lang:t("interaction.surgeon")
            end
            if GetResourceState('qb-target') == 'started' or GetResourceState('pa-target') == 'started' then
                exports['qb-target']:AddBoxZone(k .. "_clothing_boxzone", vector3(v.coords.x, v.coords.y, v.coords.z), Config.Interaction.Target.Zone, Config.Interaction.Target.Zone, {
                    name = k .. "_clothing_boxzone",
                    heading = 180.0,
                    debugPoly = false,
                    minZ = v.coords.z - 1,
                    maxZ = v.coords.z + 1,
                }, {
                    options = {
                        {
                            num = 1,
                            icon = Config.Interaction.Target.Icon,
                            label = label,
                            action = function()
                                openClothStore(v.shopType)
                            end,
                            canInteract = function()
                                return canOpenStore(v.groupType, v.group)
                            end
                        }
                    },
                    distance = Config.Interaction.Target.Distance
                })
            elseif GetResourceState('ox_target') == 'started' then
                exports.ox_target:addBoxZone({
                    coords = vector3(v.coords.x, v.coords.y, v.coords.z),
                    size = vec3(Config.Interaction.Target.Zone, Config.Interaction.Target.Zone, Config.Interaction.Target.Zone),
                    rotation = 180.0,
                    options = {
                        {
                            num = 1,
                            icon = Config.Interaction.Target.Icon,
                            label = label,
                            distance = Config.Interaction.Target.Distance,
                            onSelect = function()
                                openClothStore(v.shopType)
                            end,
                            canInteract = function()
                                return canOpenStore(v.groupType, v.group)
                            end
                        }
                    },
                })
            end
        end
    end
    if Config.EditCharacter.Enable then
        for k, v in pairs(Config.EditCharacter.Areas) do
            if v.UsePed then
                local pedHash2 = type(v.PedModel) == "number" and v.PedModel or joaat(v.PedModel)
                RequestModel(pedHash2)
                while not HasModelLoaded(pedHash2) do
                    Citizen.Wait(0)
                end
                v.Ped = CreatePed(0, pedHash2, v.Coords.x, v.Coords.y, v.Coords.z - 1, v.Coords.w, false, true)
                FreezeEntityPosition(v.Ped, true)
                SetEntityInvincible(v.Ped, true)
                SetBlockingOfNonTemporaryEvents(v.Ped, true)
                PlaceObjectOnGroundProperly(v.Ped)
                SetEntityAsMissionEntity(v.Ped, false, false)
                SetPedCanPlayAmbientAnims(v.Ped, false) 
                SetModelAsNoLongerNeeded(pedHash2)
                if Config.EditCharacter.Interaction.Target.Enable then
                    if GetResourceState('ox_target') == 'started' then
                        exports['ox_target']:addLocalEntity(v.Ped, {
                            [1] = {
                                label = Lang:t("interaction.edit_character"),
                                icon = Config.EditCharacter.Interaction.Target.Icon,
                                distance = Config.EditCharacter.Interaction.Target.Distance,
                                onSelect = function()
                                    if CoreName == "qb" then
                                        Core.Functions.GetPlayerData(function(pData)
                                            local gender = "male"
                                            if pData.charinfo.gender == 1 then
                                                gender = "female"
                                            end
                                            createFirstCharacterWithoutReset(gender, Config.CharacterCreationMenuCategories.Normal, true, true)
                                        end)
                                    else
                                        local pData = GetPlayerData()
                                        if pData.sex == 0 or tonumber(pData.sex) == 0 or pData.sex == "m" then
                                            createFirstCharacterWithoutReset("male", Config.CharacterCreationMenuCategories.Normal, true, true)
                                        else
                                            createFirstCharacterWithoutReset("female", Config.CharacterCreationMenuCategories.Normal, true, true)
                                        end
                                    end
                                end
                            },
                        })
                    elseif GetResourceState('qb-target') == 'started' or GetResourceState('pa-target') == 'started' then
                        exports['qb-target']:AddTargetEntity(v.Ped, {
                            options = {
                                {
                                    label = Lang:t("interaction.edit_character"),
                                    icon = Config.EditCharacter.Interaction.Target.Icon,
                                    action = function()
                                        if CoreName == "qb" then
                                            Core.Functions.GetPlayerData(function(pData)
                                                local gender = "male"
                                                if pData.charinfo.gender == 1 then
                                                    gender = "female"
                                                end
                                                createFirstCharacterWithoutReset(gender, Config.CharacterCreationMenuCategories.Normal, true, true)
                                            end)
                                        else
                                            local pData = GetPlayerData()
                                            if pData.sex == 0 or tonumber(pData.sex) == 0 or pData.sex == "m" then
                                                createFirstCharacterWithoutReset("male", Config.CharacterCreationMenuCategories.Normal, true, true)
                                            else
                                                createFirstCharacterWithoutReset("female", Config.CharacterCreationMenuCategories.Normal, true, true)
                                            end
                                        end
                                    end
                                }
                            },
                            distance = Config.EditCharacter.Interaction.Target.Distance
                        })
                    end
                else
                    closestClothingArea2 = {}
                    local showTextUI2 = false
                    Citizen.CreateThread(function()
                        while true do
                            local sleep = 100
                            if not menuActive then
                                playerPed = PlayerPedId()
                                playerCoords = GetEntityCoords(playerPed)
                                if not closestClothingArea2.id then
                                    for k, v in pairs(Config.EditCharacter.Areas) do
                                        --if v.Interaction.DrawText.Enable then
                                            local dist = #(playerCoords - vector3(v.Coords.x, v.Coords.y, v.Coords.z))
                                            if dist <= 5.0 then
                                                function currentShow()
                                                    Config.Interaction.TextUI.Show(Lang:t("interaction.edit_character"))
                                                    showTextUI2 = true
                                                end
                                                function currentHide()
                                                    Config.Interaction.TextUI.Hide(Lang:t("interaction.edit_character"))
                                                end
                                                closestClothingArea2 = {id = k, distance = dist, maxDist = Config.EditCharacter.Interaction.TextUI.Distance, data = {coords = vector3(v.Coords.x, v.Coords.y, v.Coords.z)}}
                                            end
                                        --end
                                    end
                                end
                                if closestClothingArea2.id then
                                    while true do
                                        playerPed = PlayerPedId()
                                        playerCoords = GetEntityCoords(playerPed)
                                        closestClothingArea2.distance = #(vector3(closestClothingArea2.data.coords.x, closestClothingArea2.data.coords.y, closestClothingArea2.data.coords.z) - playerCoords)
                                        if closestClothingArea2.distance < closestClothingArea2.maxDist then
                                            if IsControlJustReleased(0, 38) then
                                                if CoreName == "qb" then
                                                    Core.Functions.GetPlayerData(function(pData)
                                                        local gender = "male"
                                                        if pData.charinfo.gender == 1 then
                                                            gender = "female"
                                                        end
                                                        createFirstCharacterWithoutReset(gender, Config.CharacterCreationMenuCategories.Normal, true, true)
                                                    end)
                                                else
                                                    local pData = GetPlayerData()
                                                    if pData.sex == 0 or tonumber(pData.sex) == 0 or pData.sex == "m" then
                                                        createFirstCharacterWithoutReset("male", Config.CharacterCreationMenuCategories.Normal, true, true)
                                                    else
                                                        createFirstCharacterWithoutReset("female", Config.CharacterCreationMenuCategories.Normal, true, true)
                                                    end
                                                end
                                            end
                                            if not showTextUI2 then
                                                currentShow()
                                            end
                                        else
                                            currentHide()
                                            break
                                        end
                                        Citizen.Wait(0)
                                    end
                                    showTextUI2 = false
                                    closestClothingArea2 = {}
                                    sleep = 0
                                end
                            end
                            Citizen.Wait(sleep)
                        end
                    end)
                end
            end
        end
    end
end)