{"manifest": {"name": "define-property", "description": "Define a non-enumerable property on an object. Uses Reflect.defineProperty when available, otherwise Object.defineProperty.", "version": "2.0.2", "homepage": "https://github.com/jonschlinkert/define-property", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/define-property.git"}, "bugs": {"url": "https://github.com/jonschlinkert/define-property/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["define", "define-property", "enumerable", "key", "non", "non-enumerable", "object", "prop", "property", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["assign-deep", "extend-shallow", "merge-deep", "mixin-deep"]}, "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-define-property-2.0.2-d459689e8d654ba77e02a817f8710d702cb16e9d-integrity\\node_modules\\define-property\\package.json", "readmeFilename": "README.md", "readme": "# define-property [![NPM version](https://img.shields.io/npm/v/define-property.svg?style=flat)](https://www.npmjs.com/package/define-property) [![NPM monthly downloads](https://img.shields.io/npm/dm/define-property.svg?style=flat)](https://npmjs.org/package/define-property) [![NPM total downloads](https://img.shields.io/npm/dt/define-property.svg?style=flat)](https://npmjs.org/package/define-property) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/define-property.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/define-property)\n\n> Define a non-enumerable property on an object. Uses Reflect.defineProperty when available, otherwise Object.defineProperty.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save define-property\n```\n\n## Release history\n\nSee [the CHANGELOG](changelog.md) for updates.\n\n## Usage\n\n**Params**\n\n* `object`: The object on which to define the property.\n* `key`: The name of the property to be defined or modified.\n* `value`: The value or descriptor of the property being defined or modified.\n\n```js\nvar define = require('define-property');\nvar obj = {};\ndefine(obj, 'foo', function(val) {\n  return val.toUpperCase();\n});\n\n// by default, defined properties are non-enumberable\nconsole.log(obj);\n//=> {}\n\nconsole.log(obj.foo('bar'));\n//=> 'BAR'\n```\n\n**defining setters/getters**\n\nPass the same properties you would if using [Object.defineProperty](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/defineProperty) or [Reflect.defineProperty](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Reflect/defineProperty).\n\n```js\ndefine(obj, 'foo', {\n  set: function() {},\n  get: function() {}\n});\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [assign-deep](https://www.npmjs.com/package/assign-deep): Deeply assign the enumerable properties and/or es6 Symbol properies of source objects to the target… [more](https://github.com/jonschlinkert/assign-deep) | [homepage](https://github.com/jonschlinkert/assign-deep \"Deeply assign the enumerable properties and/or es6 Symbol properies of source objects to the target (first) object.\")\n* [extend-shallow](https://www.npmjs.com/package/extend-shallow): Extend an object with the properties of additional objects. node.js/javascript util. | [homepage](https://github.com/jonschlinkert/extend-shallow \"Extend an object with the properties of additional objects. node.js/javascript util.\")\n* [merge-deep](https://www.npmjs.com/package/merge-deep): Recursively merge values in a javascript object. | [homepage](https://github.com/jonschlinkert/merge-deep \"Recursively merge values in a javascript object.\")\n* [mixin-deep](https://www.npmjs.com/package/mixin-deep): Deeply mix the properties of objects into the first object. Like merge-deep, but doesn't clone. | [homepage](https://github.com/jonschlinkert/mixin-deep \"Deeply mix the properties of objects into the first object. Like merge-deep, but doesn't clone.\")\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 28 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 1 | [doowb](https://github.com/doowb) |\n\n### Author\n\n**Jon Schlinkert**\n\n* Connect with me on [linkedin/in/jonschlinkert](https://linkedin.com/in/jonschlinkert)\n* Follow me on [github/jonschlinkert](https://github.com/jonschlinkert)\n* Follow me on [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on January 25, 2018._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015-2018, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/define-property/-/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d", "type": "tarball", "reference": "https://registry.yarnpkg.com/define-property/-/define-property-2.0.2.tgz", "hash": "d459689e8d654ba77e02a817f8710d702cb16e9d", "integrity": "sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ==", "registry": "npm", "packageName": "define-property", "cacheIntegrity": "sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ== sha1-1Flono1lS6d+AqgX+HENcCyxbp0="}, "registry": "npm", "hash": "d459689e8d654ba77e02a817f8710d702cb16e9d"}