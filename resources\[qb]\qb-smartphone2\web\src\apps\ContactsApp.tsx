import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { usePhone } from '../contexts/PhoneContext';
import { useTheme } from '../contexts/ThemeContext';
import { fetchNui } from '../utils/fetchNui';
import { formatPhoneNumber } from '../utils/misc';
import './ContactsApp.css';

interface Contact {
  id: number;
  name: string;
  number: string;
  avatar?: string;
  favorite: boolean;
  blocked: boolean;
}

const ContactsApp: React.FC = () => {
  const { closeApp } = usePhone();
  const { colors } = useTheme();
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [newContact, setNewContact] = useState({ name: '', number: '' });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadContacts();
  }, []);

  const loadContacts = async () => {
    try {
      setLoading(true);
      const result = await fetchNui('getContacts', {}, [
        { id: 1, name: 'Jan Novák', number: '555-0123', favorite: true, blocked: false },
        { id: 2, name: 'Marie Svobodová', number: '555-0456', favorite: false, blocked: false },
        { id: 3, name: 'Petr Dvořák', number: '555-0789', favorite: false, blocked: false },
      ]);
      setContacts(result);
    } catch (error) {
      console.error('Error loading contacts:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredContacts = contacts.filter(contact =>
    contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.number.includes(searchTerm)
  );

  const favoriteContacts = filteredContacts.filter(contact => contact.favorite);
  const regularContacts = filteredContacts.filter(contact => !contact.favorite);

  const handleAddContact = async () => {
    if (!newContact.name.trim() || !newContact.number.trim()) return;

    try {
      await fetchNui('addContact', newContact);
      setNewContact({ name: '', number: '' });
      setShowAddForm(false);
      loadContacts();
    } catch (error) {
      console.error('Error adding contact:', error);
    }
  };

  const handleToggleFavorite = async (contactId: number) => {
    try {
      await fetchNui('toggleFavoriteContact', { contactId });
      loadContacts();
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  const handleCall = (contact: Contact) => {
    // TODO: Implement call functionality
    console.log('Calling:', contact.name);
  };

  const handleMessage = (contact: Contact) => {
    // TODO: Open messages app with this contact
    console.log('Messaging:', contact.name);
  };

  const ContactItem: React.FC<{ contact: Contact }> = ({ contact }) => (
    <motion.div
      className="contact-item"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      style={{ backgroundColor: colors.surface, borderColor: colors.border }}
    >
      <div className="contact-avatar">
        {contact.avatar ? (
          <img src={contact.avatar} alt={contact.name} />
        ) : (
          <div 
            className="contact-avatar-placeholder"
            style={{ backgroundColor: colors.primary, color: 'white' }}
          >
            {contact.name.charAt(0).toUpperCase()}
          </div>
        )}
        {contact.favorite && (
          <div className="favorite-badge">
            <i className="fas fa-star" style={{ color: '#FFD700' }} />
          </div>
        )}
      </div>
      
      <div className="contact-info">
        <div className="contact-name" style={{ color: colors.text }}>
          {contact.name}
        </div>
        <div className="contact-number" style={{ color: colors.textSecondary }}>
          {formatPhoneNumber(contact.number)}
        </div>
      </div>
      
      <div className="contact-actions">
        <button
          className="contact-action-btn"
          onClick={() => handleCall(contact)}
          style={{ color: colors.success }}
        >
          <i className="fas fa-phone" />
        </button>
        <button
          className="contact-action-btn"
          onClick={() => handleMessage(contact)}
          style={{ color: colors.primary }}
        >
          <i className="fas fa-sms" />
        </button>
        <button
          className="contact-action-btn"
          onClick={() => handleToggleFavorite(contact.id)}
          style={{ color: contact.favorite ? '#FFD700' : colors.textSecondary }}
        >
          <i className={contact.favorite ? 'fas fa-star' : 'far fa-star'} />
        </button>
      </div>
    </motion.div>
  );

  return (
    <div className="contacts-app">
      {/* Header */}
      <div className="app-header" style={{ backgroundColor: colors.surface, borderColor: colors.border }}>
        <button className="app-back-btn" onClick={closeApp} style={{ color: colors.primary }}>
          <i className="fas fa-arrow-left" />
          Zpět
        </button>
        <div className="app-title" style={{ color: colors.text }}>Kontakty</div>
        <button 
          className="app-action-btn"
          onClick={() => setShowAddForm(true)}
          style={{ color: colors.primary }}
        >
          <i className="fas fa-plus" />
        </button>
      </div>

      {/* Search */}
      <div className="search-container" style={{ backgroundColor: colors.background }}>
        <div className="search-input-container" style={{ backgroundColor: colors.surface }}>
          <i className="fas fa-search" style={{ color: colors.textSecondary }} />
          <input
            type="text"
            placeholder="Hledat kontakty..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{ color: colors.text }}
          />
        </div>
      </div>

      {/* Content */}
      <div className="app-content" style={{ backgroundColor: colors.background }}>
        {loading ? (
          <div className="loading-container">
            <div className="loading-spinner" style={{ borderColor: colors.primary }}></div>
            <div style={{ color: colors.textSecondary }}>Načítání kontaktů...</div>
          </div>
        ) : (
          <>
            {/* Favorites */}
            {favoriteContacts.length > 0 && (
              <div className="contacts-section">
                <div className="section-title" style={{ color: colors.text }}>
                  <i className="fas fa-star" style={{ color: '#FFD700' }} />
                  Oblíbené
                </div>
                {favoriteContacts.map(contact => (
                  <ContactItem key={contact.id} contact={contact} />
                ))}
              </div>
            )}

            {/* Regular Contacts */}
            {regularContacts.length > 0 && (
              <div className="contacts-section">
                <div className="section-title" style={{ color: colors.text }}>
                  <i className="fas fa-users" />
                  Všechny kontakty
                </div>
                {regularContacts.map(contact => (
                  <ContactItem key={contact.id} contact={contact} />
                ))}
              </div>
            )}

            {filteredContacts.length === 0 && !loading && (
              <div className="empty-state">
                <i className="fas fa-address-book" style={{ color: colors.textSecondary }} />
                <div style={{ color: colors.textSecondary }}>
                  {searchTerm ? 'Žádné kontakty nenalezeny' : 'Žádné kontakty'}
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Add Contact Modal */}
      {showAddForm && (
        <div className="modal-overlay" onClick={() => setShowAddForm(false)}>
          <motion.div
            className="add-contact-modal"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            onClick={(e) => e.stopPropagation()}
            style={{ backgroundColor: colors.surface }}
          >
            <div className="modal-header">
              <h3 style={{ color: colors.text }}>Nový kontakt</h3>
              <button 
                className="modal-close-btn"
                onClick={() => setShowAddForm(false)}
                style={{ color: colors.textSecondary }}
              >
                <i className="fas fa-times" />
              </button>
            </div>
            
            <div className="modal-content">
              <div className="form-group">
                <label style={{ color: colors.text }}>Jméno</label>
                <input
                  type="text"
                  value={newContact.name}
                  onChange={(e) => setNewContact({ ...newContact, name: e.target.value })}
                  placeholder="Zadej jméno..."
                  style={{ 
                    backgroundColor: colors.background, 
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
              </div>
              
              <div className="form-group">
                <label style={{ color: colors.text }}>Telefonní číslo</label>
                <input
                  type="tel"
                  value={newContact.number}
                  onChange={(e) => setNewContact({ ...newContact, number: e.target.value })}
                  placeholder="Zadej číslo..."
                  style={{ 
                    backgroundColor: colors.background, 
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
              </div>
            </div>
            
            <div className="modal-actions">
              <button 
                className="modal-btn secondary"
                onClick={() => setShowAddForm(false)}
                style={{ color: colors.textSecondary }}
              >
                Zrušit
              </button>
              <button 
                className="modal-btn primary"
                onClick={handleAddContact}
                style={{ backgroundColor: colors.primary, color: 'white' }}
              >
                Přidat
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default ContactsApp;
