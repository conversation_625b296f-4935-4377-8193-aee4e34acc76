{"manifest": {"name": "koa-compose", "description": "compose Koa middleware", "repository": {"type": "git", "url": "https://github.com/koajs/compose.git"}, "version": "4.1.0", "keywords": ["koa", "middleware", "compose"], "files": ["index.js"], "dependencies": {}, "devDependencies": {"codecov": "^3.0.0", "jest": "^21.0.0", "matcha": "^0.7.0", "standard": "^10.0.3"}, "scripts": {"bench": "matcha bench/bench.js", "lint": "standard --fix .", "test": "jest --forceExit --coverage"}, "jest": {"testEnvironment": "node"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-koa-compose-4.1.0-507306b9371901db41121c812e923d0d67d3e877-integrity\\node_modules\\koa-compose\\package.json", "readmeFilename": "Readme.md", "readme": "\n# koa-compose\n\n[![NPM version][npm-image]][npm-url]\n[![Build status][travis-image]][travis-url]\n[![Test coverage][codecov-image]][codecov-url]\n[![Dependency Status][david-image]][david-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n Compose middleware.\n\n## Installation\n\n```js\n$ npm install koa-compose\n```\n\n## API\n\n### compose([a, b, c, ...])\n\n  Compose the given middleware and return middleware.\n\n## License\n\n  MIT\n\n[npm-image]: https://img.shields.io/npm/v/koa-compose.svg?style=flat-square\n[npm-url]: https://npmjs.org/package/koa-compose\n[travis-image]: https://img.shields.io/travis/koajs/compose/next.svg?style=flat-square\n[travis-url]: https://travis-ci.org/koajs/compose\n[codecov-image]: https://img.shields.io/codecov/c/github/koajs/compose/next.svg?style=flat-square\n[codecov-url]: https://codecov.io/github/koajs/compose\n[david-image]: http://img.shields.io/david/koajs/compose.svg?style=flat-square\n[david-url]: https://david-dm.org/koajs/compose\n[license-image]: http://img.shields.io/npm/l/koa-compose.svg?style=flat-square\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/koa-compose.svg?style=flat-square\n[downloads-url]: https://npmjs.org/package/koa-compose\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/koa-compose/-/koa-compose-4.1.0.tgz#507306b9371901db41121c812e923d0d67d3e877", "type": "tarball", "reference": "https://registry.yarnpkg.com/koa-compose/-/koa-compose-4.1.0.tgz", "hash": "507306b9371901db41121c812e923d0d67d3e877", "integrity": "sha512-8ODW8TrDuMYvXRwra/Kh7/rJo9BtOfPc6qO8eAfC80CnCvSjSl0bkRM24X6/XBBEyj0v1nRUQ1LyOy3dbqOWXw==", "registry": "npm", "packageName": "koa-compose", "cacheIntegrity": "sha512-8ODW8TrDuMYvXRwra/Kh7/rJo9BtOfPc6qO8eAfC80CnCvSjSl0bkRM24X6/XBBEyj0v1nRUQ1LyOy3dbqOWXw== sha1-UHMGuTcZAdtBEhyBLpI9DWfT6Hc="}, "registry": "npm", "hash": "507306b9371901db41121c812e923d0d67d3e877"}