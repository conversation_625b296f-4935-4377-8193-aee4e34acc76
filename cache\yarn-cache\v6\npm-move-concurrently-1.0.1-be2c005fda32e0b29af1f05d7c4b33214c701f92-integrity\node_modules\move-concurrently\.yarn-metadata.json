{"manifest": {"name": "move-concurrently", "version": "1.0.1", "description": "Promises of moves of files or directories with rename, falling back to recursive rename/copy on EXDEV errors, with configurable concurrency and win32 junction support.", "main": "move.js", "scripts": {"test": "standard && tap --coverage test"}, "keywords": ["move"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "dependencies": {"copy-concurrently": "^1.0.0", "aproba": "^1.1.1", "fs-write-stream-atomic": "^1.0.8", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.3"}, "devDependencies": {"standard": "^8.6.0", "tacks": "^1.2.6", "tap": "^10.1.1"}, "files": ["move.js", "is-windows.js"], "repository": {"type": "git", "url": "git+https://github.com/npm/move-concurrently.git"}, "bugs": {"url": "https://github.com/npm/move-concurrently/issues"}, "homepage": "https://www.npmjs.com/package/move-concurrently", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-move-concurrently-1.0.1-be2c005fda32e0b29af1f05d7c4b33214c701f92-integrity\\node_modules\\move-concurrently\\package.json", "readmeFilename": "README.md", "readme": "# move-concurrently\n\nMove files and directories.\n\n```\nconst move = require('move-concurrently')\nmove('/path/to/thing', '/new/path/thing').then(() => {\n  // thing is now moved!\n}).catch(err => {\n  // oh no!\n})\n```\n\nUses `rename` to move things as fast as possible.\n\nIf you `move` across devices or on filesystems that don't support renaming\nlarge directories.  That is, situations that result in `rename` returning\nthe `EXDEV` error, then `move` will fallback to copy + delete.\n\nWhen recursively copying directories it will first try to rename the\ncontents before falling back to copying.  While this will be slightly slower\nin true cross-device scenarios, it is MUCH faster in cases where the\nfilesystem can't handle directory renames.\n\nWhen copying ownership is maintained when running as root.  Permissions are\nalways maintained.  On Windows, if symlinks are unavailable then junctions\nwill be used.\n\n## INTERFACE\n\n### move(from, to, options) → Promise\n\nRecursively moves `from` to `to` and resolves its promise when finished.\nIf `to` already exists then the promise will be rejected with an `EEXIST`\nerror.\n\nStarts by trying to rename `from` to `to`.\n\nOptions are:\n\n* maxConcurrency – (Default: `1`) The maximum number of concurrent copies to do at once.\n* isWindows - (Default: `process.platform === 'win32'`) If true enables Windows symlink semantics. This requires\n  an extra `stat` to determine if the destination of a symlink is a file or directory. If symlinking a directory\n  fails then we'll try making a junction instead.\n\nOptions can also include dependency injection:\n\n* Promise - (Default: `global.Promise`) The promise implementation to use, defaults to Node's.\n* fs - (Default: `require('fs')`) The filesystem module to use.  Can be used\n  to use `graceful-fs` or to inject a mock.\n* writeStreamAtomic - (Default: `require('fs-write-stream-atomic')`) The\n  implementation of `writeStreamAtomic` to use.  Used to inject a mock.\n* getuid - (Default: `process.getuid`) A function that returns the current UID. Used to inject a mock.\n", "licenseText": "Copyright (c) 2017, <PERSON> <<EMAIL>>\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\nOR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/move-concurrently/-/move-concurrently-1.0.1.tgz#be2c005fda32e0b29af1f05d7c4b33214c701f92", "type": "tarball", "reference": "https://registry.yarnpkg.com/move-concurrently/-/move-concurrently-1.0.1.tgz", "hash": "be2c005fda32e0b29af1f05d7c4b33214c701f92", "integrity": "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=", "registry": "npm", "packageName": "move-concurrently", "cacheIntegrity": "sha512-hdrFxZOycD/g6A6SoI2bB5NA/5NEqD0569+S47WZhPvm46sD50ZHdYaFmnua5lndde9rCHGjmfK7Z8BuCt/PcQ== sha1-viwAX9oy4LKa8fBdfEszIUxwH5I="}, "registry": "npm", "hash": "be2c005fda32e0b29af1f05d7c4b33214c701f92"}