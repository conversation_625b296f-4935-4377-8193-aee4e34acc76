{"name": "collection-visit", "description": "Visit a method over the items in an object, or map visit over the objects in an array.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/collection-visit", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> <<EMAIL>> (https://twitter.com/doowb)", "<PERSON> <<EMAIL>> (http://twitter.com/jonschlinkert)"], "repository": "jonschlinkert/collection-visit", "bugs": {"url": "https://github.com/jonschlinkert/collection-visit/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}, "devDependencies": {"clone-deep": "^0.2.4", "gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.12", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.0", "mocha": "^3.2.0"}, "keywords": ["array", "arrays", "collection", "context", "function", "helper", "invoke", "key", "map", "method", "object", "objects", "value", "visit", "visitor"], "verb": {"related": {"list": ["base-methods", "map-visit", "object-visit"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}}