{"name": "inflation", "description": "Easily unzip an HTTP stream", "version": "2.0.0", "author": "<PERSON> <<EMAIL>> (http://jongleberry.com)", "license": "MIT", "repository": "stream-utils/inflation", "keywords": ["decompress", "unzip", "inflate", "zlib", "gunzip"], "devDependencies": {"istanbul": "0.2.10", "mocha": "~1.20.1", "readable-stream": "~1.0.27", "should": "4.0.4"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "files": ["index.js"]}