Config.cameraProps = { -- ADD YOUR OWN CAMERAS
    'prop_cctv_cam_06a'
}

Config.Props = {
    ["Conos"] = {
        Text = 'el cono',
        Animations = {
            dict = nil,
            anim = nil,
            dictponer = "anim@heists@narcotics@trash",
            animponer = "pickup"
        },
        Props = {
            {
                model = "prop_mp_cone_02",
                bone = 71,
                x = 0.70,
                y = -0.25,
                z = 0.00,
                xr = 25.0,
                yr = 270.0,
                zr = 180.0,
                physics = false,
            },
            {
                model = "prop_roadcone01c",
                bone = 71,
                x = 0.90,
                y = -0.35,
                z = 0.00,
                xr = 25.0,
                yr = 270.0,
                zr = 180.0,
                physics = false,
            },
            {
                model = "prop_air_conelight",
                bone = 71,
                x = 0.90,
                y = -0.35,
                z = 0.00,
                xr = 25.0,
                yr = 270.0,
                zr = 180.0,
                physics = false,
            },
            {
                model = "prop_roadcone02c",
                bone = 71,
                x = 0.70,
                y = -0.25,
                z = 0.00,
                xr = 25.0,
                yr = 270.0,
                zr = 180.0,
                physics = false,
            },
            {
                model = "prop_roadcone02a",
                bone = 71,
                x = 0.70,
                y = -0.25,
                z = 0.00,
                xr = 25.0,
                yr = 270.0,
                zr = 180.0,
                physics = false,
            },
            {
                model = "prop_mp_cone_04",
                bone = 71,
                x = 0.90,
                y = -0.35,
                z = 0.00,
                xr = 25.0,
                yr = 270.0,
                zr = 180.0,
                physics = false,
            },
            {
                model = "prop_roadcone02a",
                bone = 71,
                x = 0.70,
                y = -0.25,
                z = 0.00,
                xr = 25.0,
                yr = 270.0,
                zr = 180.0,
                physics = false,
            },
            {
                model = "prop_roadcone02b",
                bone = 71,
                x = 0.70,
                y = -0.25,
                z = 0.00,
                xr = 25.0,
                yr = 270.0,
                zr = 180.0,
                physics = false,
            },
        }
    },
    ["Barreras"] = {
        Text = 'la barrera',
        Animations = {
            dict = "anim@amb@nightclub@lazlow@ig1_vip@",
            anim = "clubvip_base_laz",
            dictponer = nil,
            animponer = nil
        },
        Props = {
            {
                model = "prop_barrier_work01a",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_barrier_work05",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_barrier_work06a",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_barrier_wat_03b",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_barrier_work01a",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "ba_prop_battle_barrier_02a",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_mp_arrow_barrier_01",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_mp_barrier_01",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_mp_barrier_01b",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_mp_barrier_02",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_mp_barrier_02b",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_barrier_wat_03a",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_barrier_wat_04a",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_barrier_wat_04b",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 180.0,
                physics = false,
            },
            {
                model = "prop_barrier_work01b",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_barrier_work01d",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_barrier_work02a",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_mp_barrier_02",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_mp_barrier_02",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_mc_conc_barrier_01",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_mp_conc_barrier_01",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
        }
    },
    ["Señales de tráfico"] = {
        Text = 'la señal',
        Animations = {
            dict = "anim@amb@nightclub@lazlow@ig1_vip@",
            anim = "clubvip_base_laz",
            dictponer = nil,
            animponer = nil
        },
        Props = {
            {
                model = "prop_consign_02a",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_consign_01a",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_consign_01b",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
            {
                model = "prop_consign_02a",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
        }
    },
    ["Pinchos"] = {
        Text = 'los pinchos',
        Animations = {
            dict = nil,
            anim = nil,
            dictponer = "pickup_object",
            animponer = "pickup_low"
        },
        Props = {
            {
                model = "p_ld_stinger_s",
                bone = 4103,
                x = 0.0,
                y = 0.5,
                z = -1.0,
                xr = 0.0,
                yr = 0.0,
                zr = 0.0,
                physics = false,
            },
        }
    },
    ["Radar"] = {
        Text = 'el radar',
        Animations = {
            dict = "anim@amb@nightclub@lazlow@ig1_vip@",
            anim = "clubvip_base_laz",
            dictponer = nil,
            animponer = nil
        },
        Props = {
            {
                type = "speed", -- speed, plate
                model = "p_tv_cam_02_s",
                bone = 4103,
                x = 0.0,
                y = 0.85,
                z = 0.0,
                xr = 0.0,
                yr = 0.0,
                zr = 180.0,
                physics = false,
            },
            {
                type = "plate", -- speed, plate
                model = "prop_cctv_pole_01a",
                bone = 4103,
                x = 0.0,
                y = 0.85,
                z = -7.0,
                xr = 0.0,
                yr = 0.0,
                zr = 240.0,
                physics = false,
            },
        }
    }
}