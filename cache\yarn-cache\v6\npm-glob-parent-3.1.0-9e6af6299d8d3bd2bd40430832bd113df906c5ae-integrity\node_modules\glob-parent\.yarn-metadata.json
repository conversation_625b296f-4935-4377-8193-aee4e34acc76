{"manifest": {"name": "glob-parent", "version": "3.1.0", "description": "Strips glob magic from a string to provide the parent directory path", "main": "index.js", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/es128/glob-parent"}, "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"], "files": ["index.js"], "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, "license": "ISC", "bugs": {"url": "https://github.com/es128/glob-parent/issues"}, "homepage": "https://github.com/es128/glob-parent", "dependencies": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}, "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-glob-parent-3.1.0-9e6af6299d8d3bd2bd40430832bd113df906c5ae-integrity\\node_modules\\glob-parent\\package.json", "readmeFilename": "README.md", "readme": "glob-parent [![Build Status](https://travis-ci.org/es128/glob-parent.svg)](https://travis-ci.org/es128/glob-parent) [![Coverage Status](https://img.shields.io/coveralls/es128/glob-parent.svg)](https://coveralls.io/r/es128/glob-parent?branch=master)\n======\nJavascript module to extract the non-magic parent path from a glob string.\n\n[![NPM](https://nodei.co/npm/glob-parent.png?downloads=true&downloadRank=true&stars=true)](https://nodei.co/npm/glob-parent/)\n[![NPM](https://nodei.co/npm-dl/glob-parent.png?height=3&months=9)](https://nodei.co/npm-dl/glob-parent/)\n\nUsage\n-----\n```sh\nnpm install glob-parent --save\n```\n\n**Examples**\n\n```js\nvar globParent = require('glob-parent');\n\nglobParent('path/to/*.js'); // 'path/to'\nglobParent('/root/path/to/*.js'); // '/root/path/to'\nglobParent('/*.js'); // '/'\nglobParent('*.js'); // '.'\nglobParent('**/*.js'); // '.'\nglobParent('path/{to,from}'); // 'path'\nglobParent('path/!(to|from)'); // 'path'\nglobParent('path/?(to|from)'); // 'path'\nglobParent('path/+(to|from)'); // 'path'\nglobParent('path/*(to|from)'); // 'path'\nglobParent('path/@(to|from)'); // 'path'\nglobParent('path/**/*'); // 'path'\n\n// if provided a non-glob path, returns the nearest dir\nglobParent('path/foo/bar.js'); // 'path/foo'\nglobParent('path/foo/'); // 'path/foo'\nglobParent('path/foo'); // 'path' (see issue #3 for details)\n```\n\n## Escaping\n\nThe following characters have special significance in glob patterns and must be escaped if you want them to be treated as regular path characters:\n\n- `?` (question mark)\n- `*` (star)\n- `|` (pipe)\n- `(` (opening parenthesis)\n- `)` (closing parenthesis)\n- `{` (opening curly brace)\n- `}` (closing curly brace)\n- `[` (opening bracket)\n- `]` (closing bracket)\n\n**Example**\n\n```js\nglobParent('foo/[bar]/') // 'foo'\nglobParent('foo/\\\\[bar]/') // 'foo/[bar]'\n```\n\n## Limitations\n\n#### Braces & Brackets\nThis library attempts a quick and imperfect method of determining which path\nparts have glob magic without fully parsing/lexing the pattern. There are some\nadvanced use cases that can trip it up, such as nested braces where the outer\npair is escaped and the inner one contains a path separator. If you find\nyourself in the unlikely circumstance of being affected by this or need to\nensure higher-fidelity glob handling in your library, it is recommended that you\npre-process your input with [expand-braces] and/or [expand-brackets].\n\n#### Windows\nBackslashes are not valid path separators for globs. If a path with backslashes\nis provided anyway, for simple cases, glob-parent will replace the path\nseparator for you and return the non-glob parent path (now with\nforward-slashes, which are still valid as Windows path separators).\n\nThis cannot be used in conjunction with escape characters.\n\n```js\n// BAD\nglobParent('C:\\\\Program Files \\\\(x86\\\\)\\\\*.ext') // 'C:/Program Files /(x86/)'\n\n// GOOD\nglobParent('C:/Program Files\\\\(x86\\\\)/*.ext') // 'C:/Program Files (x86)'\n```\n\nIf you are using escape characters for a pattern without path parts (i.e.\nrelative to `cwd`), prefix with `./` to avoid confusing glob-parent.\n\n```js\n// BAD\nglobParent('foo \\\\[bar]') // 'foo '\nglobParent('foo \\\\[bar]*') // 'foo '\n\n// GOOD\nglobParent('./foo \\\\[bar]') // 'foo [bar]'\nglobParent('./foo \\\\[bar]*') // '.'\n```\n\n\nChange Log\n----------\n[See release notes page on GitHub](https://github.com/es128/glob-parent/releases)\n\nLicense\n-------\n[ISC](https://raw.github.com/es128/glob-parent/master/LICENSE)\n\n[expand-braces]: https://github.com/jonschlinkert/expand-braces\n[expand-brackets]: https://github.com/jonschlinkert/expand-brackets\n", "licenseText": "The ISC License\n\nCopyright (c) 2015 <PERSON><PERSON>\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SO<PERSON>WARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON><PERSON><PERSON> IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/glob-parent/-/glob-parent-3.1.0.tgz#9e6af6299d8d3bd2bd40430832bd113df906c5ae", "type": "tarball", "reference": "https://registry.yarnpkg.com/glob-parent/-/glob-parent-3.1.0.tgz", "hash": "9e6af6299d8d3bd2bd40430832bd113df906c5ae", "integrity": "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=", "registry": "npm", "packageName": "glob-parent", "cacheIntegrity": "sha512-E8Ak/2+dZY6fnzlR7+ueWvhsH1SjHr4jjss4YS/h4py44jY9MhK/VFdaZJAWDz6BbL21KeteKxFSFpq8OS5gVA== sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4="}, "registry": "npm", "hash": "9e6af6299d8d3bd2bd40430832bd113df906c5ae"}