{"manifest": {"name": "enhanced-resolve", "version": "4.5.0", "author": {"name": "<PERSON> @sokra"}, "description": "Offers a async require.resolve function. It's highly configurable.", "files": ["lib", "LICENSE"], "dependencies": {"graceful-fs": "^4.1.2", "memory-fs": "^0.5.0", "tapable": "^1.0.0"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "devDependencies": {"codecov.io": "^0.1.6", "coveralls": "^2.11.6", "eslint": "^5.9.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-prettier": "^3.0.0", "husky": "^1.2.0", "istanbul": "^0.4.1", "lint-staged": "^8.1.0", "mocha": "^2.3.4", "prettier": "^1.15.2", "should": "^8.0.2"}, "engines": {"node": ">=6.9.0"}, "main": "lib/node.js", "homepage": "http://github.com/webpack/enhanced-resolve", "scripts": {"lint": "eslint lib test", "pretty": "prettier --loglevel warn --write \"{lib,test}/**/*.{js,json}\"", "pretest": "yarn lint", "test": "mocha --full-trace --check-leaks", "precover": "yarn lint", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "yarn cover --report lcovonly"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["eslint --cache"]}, "repository": {"type": "git", "url": "git://github.com/webpack/enhanced-resolve.git"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-enhanced-resolve-4.5.0-2f3cfd84dbe3b487f18f2db2ef1e064a571ca5ec-integrity\\node_modules\\enhanced-resolve\\package.json", "readmeFilename": "README.md", "readme": "# enhanced-resolve\n\nOffers an async require.resolve function. It's highly configurable.\n\n## Features\n\n* plugin system\n* provide a custom filesystem\n* sync and async node.js filesystems included\n\n\n## Getting Started\n### Install\n```sh\n# npm\nnpm install enhanced-resolve\n# or Yarn\nyarn add enhanced-resolve\n```\n\n### Creating a Resolver\nThe easiest way to create a resolver is to use the `createResolver` function on `ResolveFactory`, along with one of the supplied File System implementations.\n```js\nconst {\n  NodeJsInputFileSystem,\n  CachedInputFileSystem,\n  ResolverFactory\n} = require('enhanced-resolve');\n\n// create a resolver\nconst myResolver = ResolverFactory.createResolver({\n  // Typical usage will consume the `NodeJsInputFileSystem` + `CachedInputFileSystem`, which wraps the Node.js `fs` wrapper to add resilience + caching.\n  fileSystem: new CachedInputFileSystem(new NodeJsInputFileSystem(), 4000),\n  extensions: ['.js', '.json']\n  /* any other resolver options here. Options/defaults can be seen below */\n});\n\n// resolve a file with the new resolver\nconst context = {};\nconst resolveContext = {};\nconst lookupStartPath = '/Users/<USER>/some/root/dir';\nconst request = './path/to-look-up.js';\nmyResolver.resolve({}, lookupStartPath, request, resolveContext, (err/*Error*/, filepath/*string*/) => {\n  // Do something with the path\n});\n```\n\nFor more examples creating different types resolvers (sync/async, context, etc) see `lib/node.js`.\n#### Resolver Options\n| Field                    | Default                     | Description                                                                        |\n| ------------------------ | --------------------------- | ---------------------------------------------------------------------------------- |\n| alias                    | []                          | A list of module alias configurations or an object which maps key to value |\n| aliasFields              | []                          | A list of alias fields in description files |\n| cacheWithContext         | true                        | If unsafe cache is enabled, includes `request.context` in the cache key  |\n| descriptionFiles         | [\"package.json\"]            | A list of description files to read from |\n| enforceExtension         | false                       | Enforce that a extension from extensions must be used |\n| enforceModuleExtension   | false                       | Enforce that a extension from moduleExtensions must be used |\n| extensions               | [\".js\", \".json\", \".node\"]   | A list of extensions which should be tried for files |\n| mainFields               | [\"main\"]                    | A list of main fields in description files |\n| mainFiles                | [\"index\"]                   | A list of main files in directories |\n| modules                  | [\"node_modules\"]            | A list of directories to resolve modules from, can be absolute path or folder name |\n| roots                    | []                          | A list of directories to resolve request starting with `/` from |\n| ignoreRootsErrors        | false                       | Ignore fatal errors happening during handling of `roots` (allows to add `roots` without a breaking change) |\n| preferAbsolute           | false                       | Prefer to resolve server-relative urls as absolute paths before falling back to resolve in roots |\n| unsafeCache              | false                       | Use this cache object to unsafely cache the successful requests |\n| plugins                  | []                          | A list of additional resolve plugins which should be applied |\n| symlinks                 | true                        | Whether to resolve symlinks to their symlinked location |\n| cachePredicate           | function() { return true }; | A function which decides whether a request should be cached or not. An object is passed to the function with `path` and `request` properties. |\n| moduleExtensions         | []                          | A list of module extensions which should be tried for modules |\n| resolveToContext         | false                       | Resolve to a context instead of a file |\n| restrictions             | []                          | A list of resolve restrictions |\n| fileSystem               |                             | The file system which should be used |\n| resolver                 | undefined                   | A prepared Resolver to which the plugins are attached |\n\n## Plugins\nSimilar to `webpack`, the core of `enhanced-resolve` functionality is implemented as individual plugins that are executed using [`Tapable`](https://github.com/webpack/tapable). These plugins can extend the functionality of the library, adding other ways for files/contexts to be resolved.\n\nA plugin should be a `class` (or its ES5 equivalent) with an `apply` method. The `apply` method will receive a `resolver` instance, that can be used to hook in to the event system.\n\n### Plugin Boilerplate\n```js\nclass MyResolverPlugin {\n  constructor(source, target) {\n    this.source = source;\n    this.target = target;\n  }\n\n  apply(resolver) {\n    const target = resolver.ensureHook(this.target);\n    resolver.getHook(this.source).tapAsync(\"MyResolverPlugin\", (request, resolveContext, callback) => {\n      // Any logic you need to create a new `request` can go here\n      resolver.doResolve(target, request, null, resolveContext, callback);\n    });\n  }\n}\n```\n\nPlugins are executed in a pipeline, and register which event they should be executed before/after. In the example above, `source` is the name of the event that starts the pipeline, and `target` is what event this plugin should fire, which is what continues the execution of the pipeline. For an example of how these different plugin events create a chain, see `lib/ResolverFactory.js`, in the `//// pipeline ////` section.\n\n## Tests\n\n``` javascript\nnpm test\n```\n\n[![Build Status](https://secure.travis-ci.org/webpack/enhanced-resolve.png?branch=master)](http://travis-ci.org/webpack/enhanced-resolve)\n\n\n## Passing options from webpack\nIf you are using `webpack`, and you want to pass custom options to `enhanced-resolve`, the options are passed from the `resolve` key of your webpack configuration e.g.:\n\n```\nresolve: {\n  extensions: ['', '.js', '.jsx'],\n  modules: ['src', 'node_modules'],\n  plugins: [new DirectoryNamedWebpackPlugin()]\n  ...\n},\n```\n\n## License\n\nCopyright (c) 2012-2016 Tobias Koppers\n\nMIT (http://www.opensource.org/licenses/mit-license.php)\n", "license": "MIT", "licenseText": "Copyright JS Foundation and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/enhanced-resolve/-/enhanced-resolve-4.5.0.tgz#2f3cfd84dbe3b487f18f2db2ef1e064a571ca5ec", "type": "tarball", "reference": "https://registry.yarnpkg.com/enhanced-resolve/-/enhanced-resolve-4.5.0.tgz", "hash": "2f3cfd84dbe3b487f18f2db2ef1e064a571ca5ec", "integrity": "sha512-Nv9m36S/vxpsI+Hc4/ZGRs0n9mXqSWGGq49zxb/cJfPAQMbUtttJAlNPS4AQzaBdw/pKskw5bMbekT/Y7W/Wlg==", "registry": "npm", "packageName": "enhanced-resolve", "cacheIntegrity": "sha512-Nv9m36S/vxpsI+Hc4/ZGRs0n9mXqSWGGq49zxb/cJfPAQMbUtttJAlNPS4AQzaBdw/pKskw5bMbekT/Y7W/Wlg== sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew="}, "registry": "npm", "hash": "2f3cfd84dbe3b487f18f2db2ef1e064a571ca5ec"}