{"manifest": {"name": "arr-union", "description": "Combines a list of arrays, returning a single array with unique values, using strict equality for comparisons.", "version": "3.1.0", "homepage": "https://github.com/jonschlinkert/arr-union", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/arr-union.git"}, "bugs": {"url": "https://github.com/jonschlinkert/arr-union/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"ansi-bold": "^0.1.1", "array-union": "^1.0.1", "array-unique": "^0.2.1", "benchmarked": "^0.1.4", "gulp-format-md": "^0.1.7", "minimist": "^1.1.1", "mocha": "*", "should": "*"}, "keywords": ["add", "append", "array", "arrays", "combine", "concat", "extend", "union", "uniq", "unique", "util", "utility", "utils"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["arr-diff", "arr-flatten", "arr-filter", "arr-map", "arr-pluck", "arr-reduce", "array-unique"]}, "reflinks": ["verb", "array-union"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-arr-union-3.1.0-e39b09aea9def866a8f206e288af63919bae39c4-integrity\\node_modules\\arr-union\\package.json", "readmeFilename": "README.md", "readme": "# arr-union [![NPM version](https://img.shields.io/npm/v/arr-union.svg)](https://www.npmjs.com/package/arr-union) [![Build Status](https://img.shields.io/travis/jonschlinkert/arr-union.svg)](https://travis-ci.org/jonschlinkert/arr-union)\n\n> Combines a list of arrays, returning a single array with unique values, using strict equality for comparisons.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm i arr-union --save\n```\n\n## Benchmarks\n\nThis library is **10-20 times faster** and more performant than [array-union](https://github.com/sindresorhus/array-union).\n\nSee the [benchmarks](./benchmark).\n\n```sh\n#1: five-arrays\n  array-union x 511,121 ops/sec ±0.80% (96 runs sampled)\n  arr-union x 5,716,039 ops/sec ±0.86% (93 runs sampled)\n\n#2: ten-arrays\n  array-union x 245,196 ops/sec ±0.69% (94 runs sampled)\n  arr-union x 1,850,786 ops/sec ±0.84% (97 runs sampled)\n\n#3: two-arrays\n  array-union x 563,869 ops/sec ±0.97% (94 runs sampled)\n  arr-union x 9,602,852 ops/sec ±0.87% (92 runs sampled)\n```\n\n## Usage\n\n```js\nvar union = require('arr-union');\n\nunion(['a'], ['b', 'c'], ['d', 'e', 'f']);\n//=> ['a', 'b', 'c', 'd', 'e', 'f']\n```\n\nReturns only unique elements:\n\n```js\nunion(['a', 'a'], ['b', 'c']);\n//=> ['a', 'b', 'c']\n```\n\n## Related projects\n\n* [arr-diff](https://www.npmjs.com/package/arr-diff): Returns an array with only the unique values from the first array, by excluding all… [more](https://www.npmjs.com/package/arr-diff) | [homepage](https://github.com/jonschlinkert/arr-diff)\n* [arr-filter](https://www.npmjs.com/package/arr-filter): Faster alternative to javascript's native filter method. | [homepage](https://github.com/jonschlinkert/arr-filter)\n* [arr-flatten](https://www.npmjs.com/package/arr-flatten): Recursively flatten an array or arrays. This is the fastest implementation of array flatten. | [homepage](https://github.com/jonschlinkert/arr-flatten)\n* [arr-map](https://www.npmjs.com/package/arr-map): Faster, node.js focused alternative to JavaScript's native array map. | [homepage](https://github.com/jonschlinkert/arr-map)\n* [arr-pluck](https://www.npmjs.com/package/arr-pluck): Retrieves the value of a specified property from all elements in the collection. | [homepage](https://github.com/jonschlinkert/arr-pluck)\n* [arr-reduce](https://www.npmjs.com/package/arr-reduce): Fast array reduce that also loops over sparse elements. | [homepage](https://github.com/jonschlinkert/arr-reduce)\n* [array-unique](https://www.npmjs.com/package/array-unique): Return an array free of duplicate values. Fastest ES5 implementation. | [homepage](https://github.com/jonschlinkert/array-unique)\n\n## Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/arr-union/issues/new).\n\n## Building docs\n\nGenerate readme and API documentation with [verb](https://github.com/verbose/verb):\n\n```sh\n$ npm i verb && npm run docs\n```\n\nOr, if [verb](https://github.com/verbose/verb) is installed globally:\n\n```sh\n$ verb\n```\n\n## Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm i -d && npm test\n```\n\n## Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n## License\n\nCopyright © 2016 [Jon Schlinkert](https://github.com/jonschlinkert)\nReleased under the [MIT license](https://github.com/jonschlinkert/arr-union/blob/master/LICENSE).\n\n***\n\n_This file was generated by [verb](https://github.com/verbose/verb), v0.9.0, on February 23, 2016._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2016, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/arr-union/-/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4", "type": "tarball", "reference": "https://registry.yarnpkg.com/arr-union/-/arr-union-3.1.0.tgz", "hash": "e39b09aea9def866a8f206e288af63919bae39c4", "integrity": "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=", "registry": "npm", "packageName": "arr-union", "cacheIntegrity": "sha512-sKpyeERZ02v1FeCZT8lrfJq5u6goHCtpTAzPwJYe7c8SPFOboNjNg1vz2L4VTn9T4PQxEx13TbXLmYUcS6Ug7Q== sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="}, "registry": "npm", "hash": "e39b09aea9def866a8f206e288af63919bae39c4"}