{"manifest": {"name": "@types/body-parser", "version": "1.19.0", "description": "TypeScript definitions for body-parser", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/santialbo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/vilic"}, {"name": "<PERSON>", "url": "https://github.com/dreampulse"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/blendsdk"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tlaziuk"}, {"name": "<PERSON>", "url": "https://github.com/jwalton"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/body-parser"}, "scripts": {}, "dependencies": {"@types/connect": "*", "@types/node": "*"}, "typesPublisherContentHash": "4257cff3580f6064eb283c690c28aa3a5347cd3cae2a2e208b8f23c61705724a", "typeScriptVersion": "2.8", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-body-parser-1.19.0-0685b3c47eb3006ffed117cdd55164b61f80538f-integrity\\node_modules\\@types\\body-parser\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/body-parser`\n\n# Summary\nThis package contains type definitions for body-parser (https://github.com/expressjs/body-parser).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/body-parser.\n\n### Additional Details\n * Last updated: Mon, 10 Feb 2020 21:19:04 GMT\n * Dependencies: [@types/connect](https://npmjs.com/package/@types/connect), [@types/node](https://npmjs.com/package/@types/node)\n * Global values: none\n\n# Credits\nThese definitions were written by <PERSON><PERSON> (https://github.com/santialbo), <PERSON><PERSON><PERSON> (https://github.com/vilic), <PERSON> (https://github.com/dreampulse), <PERSON><PERSON><PERSON> (https://github.com/blendsdk), <PERSON><PERSON> (https://github.com/t<PERSON><PERSON><PERSON>), <PERSON> (https://github.com/j<PERSON><PERSON>), and <PERSON><PERSON><PERSON> (https://github.com/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation. All rights reserved.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/body-parser/-/body-parser-1.19.0.tgz#0685b3c47eb3006ffed117cdd55164b61f80538f", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/body-parser/-/body-parser-1.19.0.tgz", "hash": "0685b3c47eb3006ffed117cdd55164b61f80538f", "integrity": "sha512-W98JrE0j2K78swW4ukqMleo8R7h/pFETjM2DQ90MF6XK2i4LO4W3gQ71Lt4w3bfm2EvVSyWHplECvB5sK22yFQ==", "registry": "npm", "packageName": "@types/body-parser", "cacheIntegrity": "sha512-W98JrE0j2K78swW4ukqMleo8R7h/pFETjM2DQ90MF6XK2i4LO4W3gQ71Lt4w3bfm2EvVSyWHplECvB5sK22yFQ== sha1-BoWzxH6zAG/+0RfN1VFkth+AU48="}, "registry": "npm", "hash": "0685b3c47eb3006ffed117cdd55164b61f80538f"}