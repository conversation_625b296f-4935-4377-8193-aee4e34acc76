{"manifest": {"name": "tapable", "version": "1.1.3", "author": {"name": "<PERSON> @sokra"}, "description": "Just a little module for plugins.", "license": "MIT", "repository": {"type": "git", "url": "http://github.com/webpack/tapable.git"}, "devDependencies": {"babel-core": "^6.26.0", "babel-jest": "^21.0.2", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0", "codecov": "^2.3.0", "jest": "^21.0.4", "prettier": "^1.13.2"}, "engines": {"node": ">=6"}, "files": ["lib", "!lib/__tests__"], "homepage": "https://github.com/webpack/tapable", "main": "lib/index.js", "scripts": {"test": "jest", "travis": "jest --coverage && codecov", "pretty": "prettier --write lib/*.js lib/__tests__/*.js"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-tapable-1.1.3-a1fccc06b58db61fd7a45da2da44f5f3a3e67ba2-integrity\\node_modules\\tapable\\package.json", "readmeFilename": "README.md", "readme": "# Tapable\n\nThe tapable package expose many Hook classes, which can be used to create hooks for plugins.\n\n``` javascript\nconst {\n\tSyncHook,\n\tSyncBailHook,\n\tSyncWaterfallHook,\n\tSyncLoopHook,\n\tAsyncParallelHook,\n\tAsyncParallelBailHook,\n\tAsyncS<PERSON>Hook,\n\tAsync<PERSON><PERSON>B<PERSON>Hook,\n\tAsyncSeriesWaterfallHook\n } = require(\"tapable\");\n```\n\n## Installation\n\n``` shell\nnpm install --save tapable\n```\n\n## Usage\n\nAll Hook constructors take one optional argument, which is a list of argument names as strings.\n\n``` js\nconst hook = new SyncHook([\"arg1\", \"arg2\", \"arg3\"]);\n```\n\nThe best practice is to expose all hooks of a class in a `hooks` property:\n\n``` js\nclass Car {\n\tconstructor() {\n\t\tthis.hooks = {\n\t\t\taccelerate: new SyncHook([\"newSpeed\"]),\n\t\t\tbrake: new SyncHook(),\n\t\t\tcalculateRoutes: new AsyncParallelHook([\"source\", \"target\", \"routesList\"])\n\t\t};\n\t}\n\n\t/* ... */\n}\n```\n\nOther people can now use these hooks:\n\n``` js\nconst myCar = new Car();\n\n// Use the tap method to add a consument\nmyCar.hooks.brake.tap(\"WarningLampPlugin\", () => warningLamp.on());\n```\n\nIt's required to pass a name to identify the plugin/reason.\n\nYou may receive arguments:\n\n``` js\nmyCar.hooks.accelerate.tap(\"LoggerPlugin\", newSpeed => console.log(`Accelerating to ${newSpeed}`));\n```\n\nFor sync hooks, `tap` is the only valid method to add a plugin. Async hooks also support async plugins:\n\n``` js\nmyCar.hooks.calculateRoutes.tapPromise(\"GoogleMapsPlugin\", (source, target, routesList) => {\n\t// return a promise\n\treturn google.maps.findRoute(source, target).then(route => {\n\t\troutesList.add(route);\n\t});\n});\nmyCar.hooks.calculateRoutes.tapAsync(\"BingMapsPlugin\", (source, target, routesList, callback) => {\n\tbing.findRoute(source, target, (err, route) => {\n\t\tif(err) return callback(err);\n\t\troutesList.add(route);\n\t\t// call the callback\n\t\tcallback();\n\t});\n});\n\n// You can still use sync plugins\nmyCar.hooks.calculateRoutes.tap(\"CachedRoutesPlugin\", (source, target, routesList) => {\n\tconst cachedRoute = cache.get(source, target);\n\tif(cachedRoute)\n\t\troutesList.add(cachedRoute);\n})\n```\n\nThe class declaring these hooks need to call them:\n\n``` js\nclass Car {\n\t/* ... */\n\n\tsetSpeed(newSpeed) {\n\t\tthis.hooks.accelerate.call(newSpeed);\n\t}\n\n\tuseNavigationSystemPromise(source, target) {\n\t\tconst routesList = new List();\n\t\treturn this.hooks.calculateRoutes.promise(source, target, routesList).then(() => {\n\t\t\treturn routesList.getRoutes();\n\t\t});\n\t}\n\n\tuseNavigationSystemAsync(source, target, callback) {\n\t\tconst routesList = new List();\n\t\tthis.hooks.calculateRoutes.callAsync(source, target, routesList, err => {\n\t\t\tif(err) return callback(err);\n\t\t\tcallback(null, routesList.getRoutes());\n\t\t});\n\t}\n}\n```\n\nThe Hook will compile a method with the most efficient way of running your plugins. It generates code depending on:\n* The number of registered plugins (none, one, many)\n* The kind of registered plugins (sync, async, promise)\n* The used call method (sync, async, promise)\n* The number of arguments\n* Whether interception is used\n\nThis ensures fastest possible execution.\n\n## Hook types\n\nEach hook can be tapped with one or several functions. How they are executed depends on the hook type:\n\n* Basic hook (without “Waterfall”, “Bail” or “Loop” in its name). This hook simply calls every function it tapped in a row.\n\n* __Waterfall__. A waterfall hook also calls each tapped function in a row. Unlike the basic hook, it passes a return value from each function to the next function.\n\n* __Bail__. A bail hook allows exiting early. When any of the tapped function returns anything, the bail hook will stop executing the remaining ones.\n\n* __Loop__. TODO\n\nAdditionally, hooks can be synchronous or asynchronous. To reflect this, there’re “Sync”, “AsyncSeries”, and “AsyncParallel” hook classes:\n\n* __Sync__. A sync hook can only be tapped with synchronous functions (using `myHook.tap()`).\n\n* __AsyncSeries__. An async-series hook can be tapped with synchronous, callback-based and promise-based functions (using `myHook.tap()`, `myHook.tapAsync()` and `myHook.tapPromise()`). They call each async method in a row.\n\n* __AsyncParallel__. An async-parallel hook can also be tapped with synchronous, callback-based and promise-based functions (using `myHook.tap()`, `myHook.tapAsync()` and `myHook.tapPromise()`). However, they run each async method in parallel.\n\nThe hook type is reflected in its class name. E.g., `AsyncSeriesWaterfallHook` allows asynchronous functions and runs them in series, passing each function’s return value into the next function.\n\n\n## Interception\n\nAll Hooks offer an additional interception API:\n\n``` js\nmyCar.hooks.calculateRoutes.intercept({\n\tcall: (source, target, routesList) => {\n\t\tconsole.log(\"Starting to calculate routes\");\n\t},\n\tregister: (tapInfo) => {\n\t\t// tapInfo = { type: \"promise\", name: \"GoogleMapsPlugin\", fn: ... }\n\t\tconsole.log(`${tapInfo.name} is doing its job`);\n\t\treturn tapInfo; // may return a new tapInfo object\n\t}\n})\n```\n\n**call**: `(...args) => void` Adding `call` to your interceptor will trigger when hooks are triggered. You have access to the hooks arguments.\n\n**tap**: `(tap: Tap) => void` Adding `tap` to your interceptor will trigger when a plugin taps into a hook. Provided is the `Tap` object. `Tap` object can't be changed.\n\n**loop**: `(...args) => void` Adding `loop` to your interceptor will trigger for each loop of a looping hook.\n\n**register**: `(tap: Tap) => Tap | undefined` Adding `register` to your interceptor will trigger for each added `Tap` and allows to modify it.\n\n## Context\n\nPlugins and interceptors can opt-in to access an optional `context` object, which can be used to pass arbitrary values to subsequent plugins and interceptors.\n\n``` js\nmyCar.hooks.accelerate.intercept({\n\tcontext: true,\n\ttap: (context, tapInfo) => {\n\t\t// tapInfo = { type: \"sync\", name: \"NoisePlugin\", fn: ... }\n\t\tconsole.log(`${tapInfo.name} is doing it's job`);\n\n\t\t// `context` starts as an empty object if at least one plugin uses `context: true`.\n\t\t// If no plugins use `context: true`, then `context` is undefined.\n\t\tif (context) {\n\t\t\t// Arbitrary properties can be added to `context`, which plugins can then access.\n\t\t\tcontext.hasMuffler = true;\n\t\t}\n\t}\n});\n\nmyCar.hooks.accelerate.tap({\n\tname: \"NoisePlugin\",\n\tcontext: true\n}, (context, newSpeed) => {\n\tif (context && context.hasMuffler) {\n\t\tconsole.log(\"Silence...\");\n\t} else {\n\t\tconsole.log(\"Vroom!\");\n\t}\n});\n```\n\n## HookMap\n\nA HookMap is a helper class for a Map with Hooks\n\n``` js\nconst keyedHook = new HookMap(key => new SyncHook([\"arg\"]))\n```\n\n``` js\nkeyedHook.tap(\"some-key\", \"MyPlugin\", (arg) => { /* ... */ });\nkeyedHook.tapAsync(\"some-key\", \"MyPlugin\", (arg, callback) => { /* ... */ });\nkeyedHook.tapPromise(\"some-key\", \"MyPlugin\", (arg) => { /* ... */ });\n```\n\n``` js\nconst hook = keyedHook.get(\"some-key\");\nif(hook !== undefined) {\n\thook.callAsync(\"arg\", err => { /* ... */ });\n}\n```\n\n## Hook/HookMap interface\n\nPublic:\n\n``` ts\ninterface Hook {\n\ttap: (name: string | Tap, fn: (context?, ...args) => Result) => void,\n\ttapAsync: (name: string | Tap, fn: (context?, ...args, callback: (err, result: Result) => void) => void) => void,\n\ttapPromise: (name: string | Tap, fn: (context?, ...args) => Promise<Result>) => void,\n\tintercept: (interceptor: HookInterceptor) => void\n}\n\ninterface HookInterceptor {\n\tcall: (context?, ...args) => void,\n\tloop: (context?, ...args) => void,\n\ttap: (context?, tap: Tap) => void,\n\tregister: (tap: Tap) => Tap,\n\tcontext: boolean\n}\n\ninterface HookMap {\n\tfor: (key: any) => Hook,\n\ttap: (key: any, name: string | Tap, fn: (context?, ...args) => Result) => void,\n\ttapAsync: (key: any, name: string | Tap, fn: (context?, ...args, callback: (err, result: Result) => void) => void) => void,\n\ttapPromise: (key: any, name: string | Tap, fn: (context?, ...args) => Promise<Result>) => void,\n\tintercept: (interceptor: HookMapInterceptor) => void\n}\n\ninterface HookMapInterceptor {\n\tfactory: (key: any, hook: Hook) => Hook\n}\n\ninterface Tap {\n\tname: string,\n\ttype: string\n\tfn: Function,\n\tstage: number,\n\tcontext: boolean\n}\n```\n\nProtected (only for the class containing the hook):\n\n``` ts\ninterface Hook {\n\tisUsed: () => boolean,\n\tcall: (...args) => Result,\n\tpromise: (...args) => Promise<Result>,\n\tcallAsync: (...args, callback: (err, result: Result) => void) => void,\n}\n\ninterface HookMap {\n\tget: (key: any) => Hook | undefined,\n\tfor: (key: any) => Hook\n}\n```\n\n## MultiHook\n\nA helper Hook-like class to redirect taps to multiple other hooks:\n\n``` js\nconst { MultiHook } = require(\"tapable\");\n\nthis.hooks.allHooks = new MultiHook([this.hooks.hookA, this.hooks.hookB]);\n```\n", "licenseText": "The MIT License\n\nCopyright (c) <PERSON> @sokra\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/tapable/-/tapable-1.1.3.tgz#a1fccc06b58db61fd7a45da2da44f5f3a3e67ba2", "type": "tarball", "reference": "https://registry.yarnpkg.com/tapable/-/tapable-1.1.3.tgz", "hash": "a1fccc06b58db61fd7a45da2da44f5f3a3e67ba2", "integrity": "sha512-4WK/bYZmj8xLr+HUCODHGF1ZFzsYffasLUgEiMBY4fgtltdO6B4WJtlSbPaDTLpYTcGVwM2qLnFTICEcNxs3kA==", "registry": "npm", "packageName": "tapable", "cacheIntegrity": "sha512-4WK/bYZmj8xLr+HUCODHGF1ZFzsYffasLUgEiMBY4fgtltdO6B4WJtlSbPaDTLpYTcGVwM2qLnFTICEcNxs3kA== sha1-ofzMBrWNth/XpF2i2kT186Pme6I="}, "registry": "npm", "hash": "a1fccc06b58db61fd7a45da2da44f5f3a3e67ba2"}