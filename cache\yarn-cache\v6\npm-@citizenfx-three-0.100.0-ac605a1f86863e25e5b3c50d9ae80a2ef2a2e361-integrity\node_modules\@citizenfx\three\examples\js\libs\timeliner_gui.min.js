(function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require=="function"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error("Cannot find module '"+o+"'");throw f.code="MODULE_NOT_FOUND",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require=="function"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(require,module,exports){
function Do(o){var n=[];this["do"]=function(o){n.push(o)},this.undo=function(o){n.splice(n.indexOf(o),1)},this.fire=function(){for(var t=0;t<n.length;t++)n[t].apply(o,arguments)}}"object"==typeof module&&(module.exports=Do);
},{}],2:[function(require,module,exports){
module.exports={
  "name": "timeliner_gui",
  "version": "0.0.2",
  "description": "Timeliner GUI",
  "main": "timeliner.js",
  "scripts": {
    "build": "browserify src/*.js --full-path=false -o timeliner_gui.js",
    "mini": "browserify src/*.js -g uglifyify --full-path=false -o timeliner_gui.min.js",
    "watch": "watchify src/*.js -o timeliner_gui.js -v",
    "start": "npm run watch",
    "test": "echo \"Error: no tests :(\" && exit 1"
  },
  "repository": {
    "type": "git",
    "url": "https://github.com/tschw/timeliner_gui.git"
  },
  "keywords": [
    "timeline",
    "animation",
    "keyframe",
    "controls",
    "gui"
  ],
  "author": "tschw (the fork)",
  "contributors": [
  	"Joshua 'zz85' Koo (original author)"
  ],
  "license": "MIT",
  "bugs": {
    "url": "https://github.com/tschw/timeliner_gui/issues"
  },
  "homepage": "https://github.com/tschw/timeliner_gui",
  "devDependencies": {
    "do.js": "^1.0.0",
    "uglifyify": "^2.6.0"
  }
}

},{}],3:[function(require,module,exports){
function Dispatcher(){function r(r,t){r in a||(a[r]=[]);var i=a[r];i.push(t)}function t(r){var t=Array.prototype.slice.call(arguments);t.shift();var i=a[r];if(i)for(var n=0;n<i.length;n++){var e=i[n];e.apply(e,t)}}var a={};this.on=r,this.fire=t}module.exports=Dispatcher;
},{}],4:[function(require,module,exports){
function LayerCabinet(e){function t(){d.fire("update.scale",6*Math.pow(100,m.value))}function n(){var t=e.controller.getChannelNames(),n=e.currentTime;f.setValue(n),y.setValue(e.totalTime),f.paint(),y.paint();for(var o=x.length;o-->0;)o>=t.length?(x[o].dom.style.display="none",T.push(x.pop())):(x[o].setState(t[o]),x[o].repaint(n))}var o=document.createElement("div"),a=document.createElement("div");a.style.cssText="margin: 0px; top: 0; left: 0; height: "+LayoutConstants.MARKER_TRACK_HEIGHT+"px";var i=document.createElement("div");style(i,{position:"absolute",top:LayoutConstants.MARKER_TRACK_HEIGHT+"px",left:0,right:0,bottom:0,overflow:"hidden"}),o.appendChild(i);var p=!1,l={width:"22px",height:"22px",padding:"2px"},r={width:"32px",padding:"3px 4px 3px 4px"},d=e.dispatcher,s=(e.controller,new IconButton(16,"play","Play",d));style(s.dom,l,{marginTop:"2px"}),s.onClick(function(e){e.preventDefault(),d.fire("controls.toggle_play")});var u=new IconButton(16,"stop","Stop",d);style(u.dom,l,{marginTop:"2px"}),u.onClick(function(e){d.fire("controls.stop")});var m=document.createElement("input");m.type="range",m.value=0,m.min=-1,m.max=1,m.step=.125,style(m,{width:"80px",margin:"0px",marginLeft:"2px",marginRight:"2px"});var c=0;m.addEventListener("mousedown",function(){c=1}),m.addEventListener("mouseup",function(){c=0,t()}),m.addEventListener("mousemove",function(){c&&t()}),o.appendChild(a);var h={min:0,step:.125},f=new NumberUI(h),y=new NumberUI(h);f.onChange["do"](function(e,t){d.fire("time.update",e),f.paint()}),y.onChange["do"](function(e,t){d.fire("totalTime.update",e),y.paint()}),a.appendChild(f.dom),a.appendChild(document.createTextNode("/")),a.appendChild(y.dom),a.appendChild(s.dom),a.appendChild(u.dom),a.appendChild(m);var v=document.createElement("div");style(v,{marginTop:"4px"}),a.appendChild(v);var g=new IconButton(16,"download_alt","Download animation",d);style(g.dom,r),v.appendChild(g.dom),g.onClick(function(){d.fire("export")});var C=new IconButton(16,"upload_alt","Upload animation",d);style(C.dom,r),v.appendChild(C.dom),C.onClick(function(){d.fire("openfile")});var x=[],T=[];this.layers=x,this.setControlStatus=function(e){p=e,p?(s.setIcon("pause"),s.setTip("Pause")):(s.setIcon("play"),s.setTip("Play"))},this.updateState=function(){var t,n,o=e.controller.getChannelNames();for(t=0;t<o.length;t++){if(n=o[t],!x[t]){var a;T.length?(a=T.pop(),a.dom.style.display="block"):(a=new LayerUI(e,n),i.appendChild(a.dom)),x.push(a)}x[t].setState(n)}},this.repaint=n,this.updateState(),this.scrollTo=function(e){i.scrollTop=e*(i.scrollHeight-i.clientHeight)},this.dom=o,n()}var LayoutConstants=require("./layout_constants"),LayerUI=require("./layer_view"),IconButton=require("./widget/icon_button"),style=require("./utils").style,Theme=require("./theme"),STORAGE_PREFIX=require("./utils").STORAGE_PREFIX,NumberUI=require("./widget/number");module.exports=LayerCabinet;
},{"./layer_view":5,"./layout_constants":6,"./theme":7,"./utils":11,"./widget/icon_button":13,"./widget/number":14}],5:[function(require,module,exports){
function LayerView(e,t){var n=document.createElement("div"),o=document.createElement("span");o.style.cssText="font-size: 12px; padding: 4px;";var i=LayoutConstants.LINE_HEIGHT-1,r=document.createElement("button");r.innerHTML="&#9672;",r.style.cssText="background: none; font-size: 12px; padding: 0px; font-family: monospace; float: right; width: 20px; height: "+i+"px; border-style:none; outline: none;",r.addEventListener("click",function(n){e.dispatcher.fire("keyframe",t)}),n.appendChild(o),n.appendChild(r),n.style.cssText="margin: 0px; border-bottom:1px solid "+Theme.b+"; top: 0; left: 0; height: "+i+"px; line-height: "+i+"px; color: "+Theme.c,this.dom=n;var a=function(n){if(r.style.color=Theme.b,null!=n&&!e.draggingKeyframe&&null!=t){var o=e.controller.getChannelKeyTimes(t);utils.binarySearch(o,n)>=0&&(r.style.color=Theme.c)}};this.repaint=a,this.setState=function(e){t=e,o.textContent=e,a()}}var Theme=require("./theme"),LayoutConstants=require("./layout_constants"),utils=require("./utils");module.exports=LayerView;
},{"./layout_constants":6,"./theme":7,"./utils":11}],6:[function(require,module,exports){
module.exports={LINE_HEIGHT:26,DIAMOND_SIZE:10,MARKER_TRACK_HEIGHT:60,WIDTH:600,HEIGHT:200,LEFT_PANE_WIDTH:250,TIME_SCALE:60};
},{}],7:[function(require,module,exports){
module.exports={a:"#343434",b:"#535353",c:"#b8b8b8",d:"#d6d6d6"};
},{}],8:[function(require,module,exports){
function time_scaled(){var e=60;tickMark1=time_scale/e,tickMark2=2*tickMark1,tickMark3=8*tickMark1}function TimelinePanel(e){function t(t,n,l){var a=this,r=!1;this.time=t,this.path=function(e){e.beginPath().moveTo(n,l).lineTo(n+DIAMOND_SIZE/2,l+DIAMOND_SIZE/2).lineTo(n,l+DIAMOND_SIZE).lineTo(n-DIAMOND_SIZE/2,l+DIAMOND_SIZE/2).closePath()},this.paint=function(e){a.path(e),r?e.fillStyle("yellow"):e.fillStyle(Theme.c),e.fill().stroke()},this.mouseover=function(){r=!0,v.style.cursor="move",a.paint(x)},this.mouseout=function(){r=!1,v.style.cursor="default",a.paint(x)},this.mousedrag=function(t,n){if(void 0!==M){var l=f(t.offsetx),a=Math.max(l-C,-C),r=n.shiftKey;a&&(e.draggingKeyframe=!0,e.controller.moveKeyframe(M,C,a,r),C+=a,i())}}}function i(){K=!0}function n(){for(S.length=0,I=0,R=_.length;R>=I;I++)A.strokeStyle=Theme.b,A.beginPath(),H=I*LINE_HEIGHT,H=~~H-.5,x.moveTo(0,H).lineTo(width,H).stroke();for(I=0;R>I;I++){var i=_[I],n=e.controller.getChannelKeyTimes(i);H=I*LINE_HEIGHT;for(var l=0;l<n.length;l++){var a=n[l];S.push(new t(a,h(a),H+.5*LINE_HEIGHT-DIAMOND_SIZE/2))}}for(I=0,R=S.length;R>I;I++){var r=S[I];r.paint(x)}}function l(){var t=width,i=e.totalTime,n=t/time_scale,l=t/i;P.k=l,P.grip_length=n*l;var a=w;P.left=e.scrollTime*l,P.left=Math.min(Math.max(0,P.left),t-P.grip_length),A.beginPath(),A.fillStyle=Theme.b,A.rect(0,5,t,a),A.fill(),A.fillStyle=Theme.c,A.beginPath(),A.rect(P.left,5,P.grip_length,a),A.fill();var r=k*l;A.fillStyle="red",A.fillRect(0,5,r,2)}function a(e){time_scale!==e&&(time_scale=e,time_scaled())}function r(){var e,t=D;for(D=null,I=S.length;I-->0;)if(e=S[I],e.path(x),A.isPointInPath(b.x*E,b.y*E)){D=e;break}t&&t!=D&&(e=t,e.mouseout&&e.mouseout()),D&&(e=D,e.mouseover&&e.mouseover(),O&&(G=e))}function o(){b&&x.save().scale(E,E).translate(0,MARKER_TRACK_HEIGHT).beginPath().rect(0,0,e.width,e.scrollHeight).translate(-g,-d).clip().run(r).restore()}function s(){if(!K)return void o();a(e.timeScale),k=e.currentTime,frame_start=e.scrollTime,A.fillStyle=Theme.a,A.clearRect(0,0,v.width,v.height),A.save(),A.scale(E,E),A.lineWidth=1,width=e.width,height=e.height;var t=time_scale/tickMark1,i=frame_start*time_scale%t,r=(width-p+i)/t;for(I=0;r>I;I++){y=I*t+p-i,A.strokeStyle=Theme.b,A.beginPath(),A.moveTo(y,0),A.lineTo(y,height),A.stroke(),A.fillStyle=Theme.d,A.textAlign="center";var s=(I*t-i)/time_scale+frame_start;s=utils.format_friendly_seconds(s),A.fillText(s,y,38)}for(t=time_scale/tickMark2,r=(width-p+i)/t,I=0;r>I;I++)A.strokeStyle=Theme.c,A.beginPath(),y=I*t+p-i,A.moveTo(y,MARKER_TRACK_HEIGHT-0),A.lineTo(y,MARKER_TRACK_HEIGHT-16),A.stroke();var c=tickMark3/tickMark2;for(t=time_scale/tickMark3,r=(width-p+i)/t,I=0;r>I;I++)I%c!==0&&(A.strokeStyle=Theme.c,A.beginPath(),y=I*t+p-i,A.moveTo(y,MARKER_TRACK_HEIGHT-0),A.lineTo(y,MARKER_TRACK_HEIGHT-10),A.stroke());x.save().translate(0,MARKER_TRACK_HEIGHT).beginPath().rect(0,0,e.width,e.scrollHeight).translate(-g,-d).clip().run(n).restore(),l(),A.strokeStyle="red",y=(k-frame_start)*time_scale+p;var f=utils.format_friendly_seconds(k),h=A.measureText(f).width,u=MARKER_TRACK_HEIGHT-5,m=h/2+4;A.beginPath(),A.moveTo(y,u),A.lineTo(y,height),A.stroke(),A.fillStyle="red",A.textAlign="center",A.beginPath(),A.moveTo(y,u+5),A.lineTo(y+5,u),A.lineTo(y+m,u),A.lineTo(y+m,u-14),A.lineTo(y-m,u-14),A.lineTo(y-m,u),A.lineTo(y-5,u),A.closePath(),A.fill(),A.fillStyle="white",A.fillText(f,y,u-4),A.restore(),K=!1}function c(e){return 0>e-MARKER_TRACK_HEIGHT?-1:(e-MARKER_TRACK_HEIGHT+d)/LINE_HEIGHT|0}function f(e){var t=time_scale/tickMark3;return frame_start+((e-p)/t|0)/tickMark3}function h(e){var t=e-frame_start;return t*=time_scale,t+=p}function u(e){L=v.getBoundingClientRect();var t=e.clientX-L.left,i=e.clientY-L.top;m(t,i)}function m(e,t){G||(N=!0,b={x:e,y:t})}var _,T=e.dispatcher,d=0,g=0,E=window.devicePixelRatio,v=document.createElement("canvas");this.updateState=function(){_=e.controller.getChannelNames(),i()},this.updateState(),this.scrollTo=function(t){d=t*Math.max(_.length*LINE_HEIGHT-e.scrollHeight,0),i()},this.resize=function(){E=window.devicePixelRatio,v.width=e.width*E,v.height=e.height*E,v.style.width=e.width+"px",v.style.height=e.height+"px",e.scrollHeight=e.height-MARKER_TRACK_HEIGHT},this.dom=v,this.resize();var k,I,y,H,R,M,A=v.getContext("2d"),x=proxy_ctx(A),p=20,K=!1,S=[],C=0,w=20,P={left:0,grip_length:0,k:1};this.setTimeScale=a;var D=null,G=null;this.repaint=i,this._paint=s,i();var L;document.addEventListener("mousemove",u),v.addEventListener("dblclick",function(e){L=v.getBoundingClientRect();var t=e.clientX-L.left,i=e.clientY-L.top,n=c(i);f(t);T.fire("keyframe",_[n],k)});var N=!1,b=null;v.addEventListener("mouseout",function(){b=null});var O=!1,Z=!1;utils.handleDrag(v,function(e){O=!0,b={x:e.offsetx,y:e.offsety},o(),G instanceof t&&(C=G.time,M=_[c(e.offsety)],M||(G=null)),T.fire("time.update",f(e.offsetx))},function(e,t){O=!1,G?(Z=!0,G.mousedrag&&G.mousedrag(e,t)):T.fire("time.update",f(e.offsetx))},function(){Z&&T.fire("keyframe.move"),O=!1,G=null,Z=!1,e.draggingKeyframe=!1,i()});var q;utils.handleDrag(v,function(e){q=P.left},function(t){e.scrollTime=Math.max(0,(q+t.dx)/P.k),i()},function(){},function(e){var t=e.offsetx>=P.left&&e.offsetx<=P.left+P.grip_length;return e.offsety<=w&&t})}var LayoutConstants=require("./layout_constants"),Theme=require("./theme"),utils=require("./utils"),proxy_ctx=utils.proxy_ctx,LINE_HEIGHT=LayoutConstants.LINE_HEIGHT,DIAMOND_SIZE=LayoutConstants.DIAMOND_SIZE,MARKER_TRACK_HEIGHT=LayoutConstants.MARKER_TRACK_HEIGHT,LEFT_PANE_WIDTH=LayoutConstants.LEFT_PANE_WIDTH,time_scale=LayoutConstants.TIME_SCALE,frame_start=0,tickMark1,tickMark2,tickMark3;time_scaled(),module.exports=TimelinePanel;
},{"./layout_constants":6,"./theme":7,"./utils":11}],9:[function(require,module,exports){
function LayerProp(e){this.name=e,this.values=[],this._color="#"+(16777215*Math.random()|0).toString(16)}function Timeliner(e){function t(){E=performance.now()-1e3*w.currentTime,v.setControlStatus(!0)}function n(){E=null,v.setControlStatus(!1)}function i(){if(requestAnimationFrame(i),E){var e=(performance.now()-E)/1e3;b(e),e>w.totalTime&&(E=performance.now())}H&&(T.style.width=w.width+"px",T.style.height=w.height+"px",g(v.dom,y.dom),y.resize(),h(),H=!1,f.fire("resize")),y._paint()}function o(e){}function a(e){e||(e=w.name),e=prompt("Pick a name to save to (localStorage)",e),e&&(w.name=e,o(e))}function r(){var e=w.name;e?o(e):a(e)}function s(){var t=e.serialize(),n="animation.json";saveToFile(JSON.stringify(t,null,"	"),n)}function l(t){e.deserialize(t),c()}function d(e){var t=JSON.parse(e);l(t)}function c(){v.updateState(),y.updateState(),h()}function h(){var e=w.controller.getChannelNames(),t=e.length*LayoutConstants.LINE_HEIGHT;x.setLength(w.scrollHeight/t),v.repaint(),y.repaint()}function u(){var e=prompt("Paste JSON in here to Load");e&&d(e)}function p(e){e&&d(localStorage[STORAGE_PREFIX+e])}function m(e,t){w.width=e-LayoutConstants.LEFT_PANE_WIDTH-4,w.height=t-44,w.scrollHeight=w.height-LayoutConstants.MARKER_TRACK_HEIGHT,x.setHeight(w.scrollHeight-2),style(x.dom,{top:LayoutConstants.MARKER_TRACK_HEIGHT+"px",left:e-16-4+"px"}),H=!0}function g(e,t){e.style.cssText="position: absolute; left: 0px; top: 0px; height: "+w.height+"px;",style(e,{overflow:"hidden"}),e.style.width=LayoutConstants.LEFT_PANE_WIDTH+"px",t.style.position="absolute",t.style.top="0px",t.style.left=LayoutConstants.LEFT_PANE_WIDTH+"px"}var f=new Dispatcher;e.timeliner=this,e.init(this);var w={width:LayoutConstants.WIDTH,height:LayoutConstants.HEIGHT,scrollHeight:0,totalTime:20,timeScale:6,currentTime:0,scrollTime:0,dispatcher:f,controller:e},y=new TimelinePanel(w),v=new LayerCabinet(w),x=(new UndoManager(f),new ScrollBar(0,10)),T=document.createElement("div");e.setDuration(w.totalTime),f.on("keyframe",function(t){var n=w.currentTime;if(null!=n&&null!=t){var i=e.getChannelKeyTimes(t,n);utils.binarySearch(i,n)<0?e.setKeyframe(t,n):e.delKeyframe(t,n),h()}}),f.on("keyframe.move",function(e,t){});var E=null,C=0,b=function(t){var n=Math.min(Math.max(t,0),w.totalTime);w.currentTime=n,e.setDisplayTime(n),E&&(E=performance.now()-1e3*t),h()};f.on("controls.toggle_play",function(){E?n():t()}),f.on("controls.restart_play",function(){E||t(),b(C)}),f.on("controls.play",t),f.on("controls.pause",n),f.on("controls.stop",function(){null!==E&&n(),b(0)}),f.on("time.update",b),f.on("totalTime.update",function(t){w.totalTime=t,e.setDuration(t),y.repaint()}),f.on("update.scale",function(e){w.timeScale=e,y.setTimeScale(e),y.repaint()}),f.on("controls.undo",function(){}),f.on("controls.redo",function(){});var H=!0;i(),this.openLocalSave=p,f.on("import",function(){u()}.bind(this)),f.on("new",function(){data.blank(),c()}),f.on("openfile",function(){openAs(function(e){d(e)},T)}),f.on("open",p),f.on("export",s),f.on("save",r),f.on("save_as",a),this.save=o,this.load=l,style(T,{textAlign:"left",lineHeight:"1em",position:"absolute",top:"22px"});var L=document.createElement("div");style(L,{position:"fixed",top:"20px",left:"20px",margin:0,border:"1px solid "+Theme.a,padding:0,overflow:"hidden",backgroundColor:Theme.a,color:Theme.d,zIndex:Z_INDEX,fontFamily:"monospace",fontSize:"12px"});var _={position:"absolute",top:"0px",width:"100%",height:"22px",lineHeight:"22px",overflow:"hidden"},S={width:"20px",height:"20px",padding:"2px",marginRight:"2px"},k=document.createElement("div");style(k,_,{borderBottom:"1px solid "+Theme.b,textAlign:"center"});var I=document.createElement("span");k.appendChild(I),I.innerHTML=package_json.description+" "+package_json.version,k.appendChild(I);var R=document.createElement("div");style(R,_,{textAlign:"right"}),k.appendChild(R);var A=new IconButton(10,"resize_full","Maximize",f);style(A.dom,S,{marginRight:"2px"}),R.appendChild(A.dom);var M=document.createElement("div"),W={position:"absolute",width:"100%",height:"22px",lineHeight:"22px",bottom:"0",fontSize:"11px"};style(M,W,{borderTop:"1px solid "+Theme.b,background:Theme.a}),L.appendChild(T),L.appendChild(M),L.appendChild(k);var z=document.createElement("span");z.textContent="Hello!",z.style.marginLeft="10px",f.on("status",function(e){z.textContent=e}),f.on("state:save",function(e){f.fire("status",e),o("autosave")});var D=document.createElement("div");style(D,W,{textAlign:"right"}),M.appendChild(z),M.appendChild(D);var P=document.createElement("div");style(P,{background:"#999",opacity:.2,position:"fixed",margin:0,padding:0,zIndex:Z_INDEX-1,transitionProperty:"top, left, width, height, opacity",transitionDuration:"0.25s",transitionTimingFunction:"ease-in-out"}),document.body.appendChild(L),document.body.appendChild(P),T.appendChild(v.dom),T.appendChild(y.dom),T.appendChild(x.dom),x.onScroll["do"](function(e,t){switch(e){case"scrollto":v.scrollTo(t),y.scrollTo(t)}}),document.addEventListener("keydown",function(e){var t=32==e.keyCode,n=13==e.keyCode,i=(e.metaKey&&91==e.keyCode&&!e.shiftKey,document.activeElement);i.nodeName.match(/(INPUT|BUTTON|SELECT)/)&&i.blur(),t?f.fire("controls.toggle_play"):n?f.fire("controls.restart_play"):27==e.keyCode&&f.fire("controls.pause")}),this.dispose=function(){var e=L.parentElement;e.removeChild(L),e.removeChild(P)},function(){"use strict";function e(e,t,n,i,o){e.style.left=t+"px",e.style.top=n+"px",e.style.width=i+"px",e.style.height=o+"px",e===L&&m(i,o)}function t(){e(P,N.left,N.top,N.width,N.height),P.style.opacity=0}function n(e){B=!0}function i(e){B=!1}function o(e){l(e.touches[0]),e.preventDefault()}function a(e){h(e.touches[0])}function r(e){0==e.touches.length&&f(e.changedTouches[0])}function s(e){l(e)}function l(e){c(e);var t=y||v||T||x,n=!t&&d();W={x:E,y:C,cx:e.clientX,cy:e.clientY,w:N.width,h:N.height,isResizing:t,isMoving:n,onTopEdge:T,onLeftEdge:x,onRightEdge:y,onBottomEdge:v},(t||n)&&e.preventDefault(),e.stopPropagation()}function d(){return F}function c(e){N=L.getBoundingClientRect(),E=e.clientX-N.left,C=e.clientY-N.top,T=R>C,x=R>E,y=E>=N.width-R,v=C>=N.height-R}function h(e){q=e,c(q),X=!0,B&&q.stopPropagation()}function u(){if(requestAnimationFrame(u),X){if(X=!1,W&&W.isResizing){if(W.onRightEdge&&(L.style.width=Math.max(E,b)+"px"),W.onBottomEdge&&(L.style.height=Math.max(C,_)+"px"),W.onLeftEdge){var n=Math.max(W.cx-q.clientX+W.w,b);n>b&&(L.style.width=n+"px",L.style.left=q.clientX+"px")}if(W.onTopEdge){var i=Math.max(W.cy-q.clientY+W.h,_);i>_&&(L.style.height=i+"px",L.style.top=q.clientY+"px")}return t(),void m(N.width,N.height)}if(W&&W.isMoving){switch(p()){case"full-screen":e(P,0,0,window.innerWidth,window.innerHeight),P.style.opacity=.2;break;case"snap-top-edge":e(P,0,0,window.innerWidth,.25*window.innerHeight),P.style.opacity=.2;break;case"snap-left-edge":e(P,0,0,.35*window.innerWidth,window.innerHeight),P.style.opacity=.2;break;case"snap-right-edge":e(P,.65*window.innerWidth,0,.35*window.innerWidth,window.innerHeight),P.style.opacity=.2;break;case"snap-bottom-edge":e(P,0,.75*window.innerHeight,window.innerWidth,.25*window.innerHeight),P.style.opacity=.2;break;default:t()}return z?void e(L,q.clientX-z.width/2,q.clientY-Math.min(W.y,z.height),z.width,z.height):(L.style.top=q.clientY-W.y+"px",void(L.style.left=q.clientX-W.x+"px"))}y&&v||x&&T?L.style.cursor="nwse-resize":y&&T||v&&x?L.style.cursor="nesw-resize":y||x?L.style.cursor="ew-resize":v||T?L.style.cursor="ns-resize":d()?L.style.cursor="move":L.style.cursor="default"}}function p(){return q.clientY<S?"full-screen":q.clientY<I?"snap-top-edge":q.clientX<I?"snap-left-edge":window.innerWidth-q.clientX<I?"snap-right-edge":window.innerHeight-q.clientY<I?"snap-bottom-edge":void 0}function g(){var t,n,i,o;switch(D){case"full-screen":t=0,n=0,i=window.innerWidth,o=window.innerHeight;break;case"snap-top-edge":t=0,n=0,i=window.innerWidth,o=.25*window.innerHeight;break;case"snap-left-edge":t=0,n=0,i=.35*window.innerWidth,o=window.innerHeight;break;case"snap-right-edge":t=.65*window.innerWidth,n=0,i=.35*window.innerWidth,o=window.innerHeight;break;case"snap-bottom-edge":t=0,n=.75*window.innerHeight,i=window.innerWidth,o=.25*window.innerHeight;break;default:return}e(L,t,n,i,o),e(P,t,n,i,o)}function f(e){c(e),W&&W.isMoving&&(D=p(),D?(z={width:N.width,height:N.height},g()):z=null,t()),W=null,B&&e.stopPropagation()}var y,v,x,T,E,C,b=120,_=100,S=2,I=12,R=2,M="snap-bottom-edge",W=null,z={width:LayoutConstants.WIDTH,height:LayoutConstants.HEIGHT},D=M,N=L.getBoundingClientRect(),X=!1,F=!1;k.addEventListener("mouseover",function(){F=!0}),k.addEventListener("mouseout",function(){F=!1}),A.onClick(function(){z||(z={width:N.width,height:N.height}),D="full-screen",g()}),window.addEventListener("resize",function(){D?g():H=!0}),e(L,0,0,w.width,w.height),e(P,0,0,w.width,w.height),L.addEventListener("mousedown",s),L.addEventListener("mouseover",n),L.addEventListener("mouseout",i),document.addEventListener("mousemove",h),document.addEventListener("mouseup",f),L.addEventListener("touchstart",o),document.addEventListener("touchmove",a),document.addEventListener("touchend",r);var q,B=!1;u(),g()}()}var undo=require("./undo"),Dispatcher=require("./dispatcher"),Theme=require("./theme"),UndoManager=undo.UndoManager,UndoState=undo.UndoState,LayoutConstants=require("./layout_constants"),utils=require("./utils"),LayerCabinet=require("./layer_cabinet"),TimelinePanel=require("./timeline_panel"),package_json=require("../package.json"),IconButton=require("./widget/icon_button"),style=utils.style,saveToFile=utils.saveToFile,openAs=utils.openAs,STORAGE_PREFIX=utils.STORAGE_PREFIX,ScrollBar=require("./widget/scrollbar"),Z_INDEX=999;Timeliner.binarySearch=utils.binarySearch,window.Timeliner=Timeliner;
},{"../package.json":2,"./dispatcher":3,"./layer_cabinet":4,"./layout_constants":6,"./theme":7,"./timeline_panel":8,"./undo":10,"./utils":11,"./widget/icon_button":13,"./widget/scrollbar":15}],10:[function(require,module,exports){
function UndoState(t,e){this.state=t.getJSONString(),this.description=e}function UndoManager(t,e){this.dispatcher=t,this.MAX_ITEMS=e||100,this.clear()}UndoManager.prototype.save=function(t,e){var n=this.states,i=this.index+1,s=n.length-i;n.splice(i,s,t),n.length>this.MAX_ITEMS&&n.shift(),this.index=n.length-1,e||this.dispatcher.fire("state:save",t.description)},UndoManager.prototype.clear=function(){this.states=[],this.index=-1},UndoManager.prototype.canUndo=function(){return this.index>0},UndoManager.prototype.canRedo=function(){return this.index<this.states.length-1},UndoManager.prototype.undo=function(){return this.canUndo()?(this.dispatcher.fire("status","Undo: "+this.get().description),this.index--):this.dispatcher.fire("status","Nothing to undo"),this.get()},UndoManager.prototype.redo=function(){return this.canRedo()?(this.index++,this.dispatcher.fire("status","Redo: "+this.get().description)):this.dispatcher.fire("status","Nothing to redo"),this.get()},UndoManager.prototype.get=function(){return this.states[this.index]},module.exports={UndoState:UndoState,UndoManager:UndoManager};
},{}],11:[function(require,module,exports){
function binarySearch(e,t){for(var n=0,o=e.length,r=!1;o>n;){var i=n+o>>1;e[i]<t?n=i+1:(o=i,r=e[i]===t)}return r?n:~n}function handleDrag(e,t,n,o,r){function i(e){return u(e),r&&!r(v,e)?void(v=null):(document.addEventListener("mousemove",a),document.addEventListener("mouseup",l),t(v,e),void e.preventDefault())}function a(e){c(e),v.moved=!0,n(v,e)}function u(t){m=e.getBoundingClientRect();var n=t.clientX,o=t.clientY;v={startx:n,starty:o,x:n,y:o,dx:0,dy:0,offsetx:n-m.left,offsety:o-m.top,moved:!1}}function c(t){m=e.getBoundingClientRect();var n=t.clientX,o=t.clientY,r=n-m.left,i=o-m.top;v.x=n,v.y=o,v.dx=t.clientX-v.startx,v.dy=t.clientY-v.starty,v.offsetx=r,v.offsety=i}function l(e){c(e),o(v,e),v=null,document.removeEventListener("mousemove",a),document.removeEventListener("mouseup",l)}function s(n){if(1==n.touches.length){var o=n.touches[0];if(r&&!r(o))return;n.preventDefault(),u(o),t(v,o)}e.addEventListener("touchmove",d),e.addEventListener("touchend",f)}function d(e){var t=e.touches[0];a(t)}function f(t){l(t),e.removeEventListener("touchmove",d),e.removeEventListener("touchend",f)}var v=null,m=e.getBoundingClientRect();e.addEventListener("mousedown",i),e.addEventListener("touchstart",s),this.release=function(){e.removeEventListener("mousedown",i),e.removeEventListener("touchstart",s)}}function style(e,t){for(var n=1;n<arguments.length;++n){var o=arguments[n];for(var r in o)e.style[r]=o[r]}}function saveToFile(e,t){var n=document.createElement("a");document.body.appendChild(n),n.style="display: none";var o=new Blob([e],{type:"octet/stream"}),r=window.URL.createObjectURL(o);n.href=r,n.download=t,fakeClick(n),setTimeout(function(){window.URL.revokeObjectURL(r),document.body.removeChild(n)},500)}function handleFileSelect(e){var t=e.target.files,n=t[0];if(n){var o=new FileReader;o.onload=function(e){var t=e.target.result;openCallback(t)},o.readAsText(n),input.value=""}}function openAs(e,t){openCallback=e,input||(input=document.createElement("input"),input.style.display="none",input.type="file",input.addEventListener("change",handleFileSelect),t=t||document.body,t.appendChild(input)),fakeClick(input)}function fakeClick(e){var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!1,window,0,0,0,0,0,!1,!1,!1,!1,0,null),e.dispatchEvent(t)}function format_friendly_seconds(e,t){var n=0|e,o=n%60,r=n/60|0,i=r%60,a=(o/100).toFixed(2).substring(2),u=i+":"+a;if(e%1>0){var c=e%1*60;"frames"===t?u=o+"+"+c.toFixed(0)+"f":u+=(e%1).toFixed(2).substring(1)}return u}function proxy_ctx(e){function t(t){return function(){return e[t].apply(e,arguments),o}}function n(t){return function(n){return e[t]=n,o}}var o={};o.run=function(e){return e(o),o};for(var r in e){var i=typeof e[r];switch(i){case"object":break;case"function":o[r]=t(r);break;default:o[r]=n(r)}}return o}module.exports={STORAGE_PREFIX:"timeliner-",Z_INDEX:999,style:style,saveToFile:saveToFile,openAs:openAs,format_friendly_seconds:format_friendly_seconds,proxy_ctx:proxy_ctx,handleDrag:handleDrag,binarySearch:binarySearch};var input,openCallback;
},{}],12:[function(require,module,exports){
module.exports={
	"unitsPerEm": 1792,
	"ascender": 1536,
	"descender": -256,
	"fonts": {
		"plus": {
			"advanceWidth": 1408,
			"commands": "M,1408,800 C,1408,853,1365,896,1312,896 L,896,896 L,896,1312 C,896,1365,853,1408,800,1408 L,608,1408 C,555,1408,512,1365,512,1312 L,512,896 L,96,896 C,43,896,0,853,0,800 L,0,608 C,0,555,43,512,96,512 L,512,512 L,512,96 C,512,43,555,0,608,0 L,800,0 C,853,0,896,43,896,96 L,896,512 L,1312,512 C,1365,512,1408,555,1408,608 Z"
		},
		"minus": {
			"advanceWidth": 1408,
			"commands": "M,1408,800 C,1408,853,1365,896,1312,896 L,96,896 C,43,896,0,853,0,800 L,0,608 C,0,555,43,512,96,512 L,1312,512 C,1365,512,1408,555,1408,608 Z"
		},
		"ok": {
			"advanceWidth": 1792,
			"commands": "M,1671,970 C,1671,995,1661,1020,1643,1038 L,1507,1174 C,1489,1192,1464,1202,1439,1202 C,1414,1202,1389,1192,1371,1174 L,715,517 L,421,812 C,403,830,378,840,353,840 C,328,840,303,830,285,812 L,149,676 C,131,658,121,633,121,608 C,121,583,131,558,149,540 L,511,178 L,647,42 C,665,24,690,14,715,14 C,740,14,765,24,783,42 L,919,178 L,1643,902 C,1661,920,1671,945,1671,970 Z"
		},
		"remove": {
			"advanceWidth": 1408,
			"commands": "M,1298,214 C,1298,239,1288,264,1270,282 L,976,576 L,1270,870 C,1288,888,1298,913,1298,938 C,1298,963,1288,988,1270,1006 L,1134,1142 C,1116,1160,1091,1170,1066,1170 C,1041,1170,1016,1160,998,1142 L,704,848 L,410,1142 C,392,1160,367,1170,342,1170 C,317,1170,292,1160,274,1142 L,138,1006 C,120,988,110,963,110,938 C,110,913,120,888,138,870 L,432,576 L,138,282 C,120,264,110,239,110,214 C,110,189,120,164,138,146 L,274,10 C,292,-8,317,-18,342,-18 C,367,-18,392,-8,410,10 L,704,304 L,998,10 C,1016,-8,1041,-18,1066,-18 C,1091,-18,1116,-8,1134,10 L,1270,146 C,1288,164,1298,189,1298,214 Z"
		},
		"zoom_in": {
			"advanceWidth": 1664,
			"commands": "M,1024,736 C,1024,753,1009,768,992,768 L,768,768 L,768,992 C,768,1009,753,1024,736,1024 L,672,1024 C,655,1024,640,1009,640,992 L,640,768 L,416,768 C,399,768,384,753,384,736 L,384,672 C,384,655,399,640,416,640 L,640,640 L,640,416 C,640,399,655,384,672,384 L,736,384 C,753,384,768,399,768,416 L,768,640 L,992,640 C,1009,640,1024,655,1024,672 M,1152,704 C,1152,457,951,256,704,256 C,457,256,256,457,256,704 C,256,951,457,1152,704,1152 C,951,1152,1152,951,1152,704 M,1664,-128 C,1664,-94,1650,-61,1627,-38 L,1284,305 C,1365,422,1408,562,1408,704 C,1408,1093,1093,1408,704,1408 C,315,1408,0,1093,0,704 C,0,315,315,0,704,0 C,846,0,986,43,1103,124 L,1446,-218 C,1469,-242,1502,-256,1536,-256 C,1607,-256,1664,-199,1664,-128 Z"
		},
		"zoom_out": {
			"advanceWidth": 1664,
			"commands": "M,1024,736 C,1024,753,1009,768,992,768 L,416,768 C,399,768,384,753,384,736 L,384,672 C,384,655,399,640,416,640 L,992,640 C,1009,640,1024,655,1024,672 M,1152,704 C,1152,457,951,256,704,256 C,457,256,256,457,256,704 C,256,951,457,1152,704,1152 C,951,1152,1152,951,1152,704 M,1664,-128 C,1664,-94,1650,-61,1627,-38 L,1284,305 C,1365,422,1408,562,1408,704 C,1408,1093,1093,1408,704,1408 C,315,1408,0,1093,0,704 C,0,315,315,0,704,0 C,846,0,986,43,1103,124 L,1446,-218 C,1469,-242,1502,-256,1536,-256 C,1607,-256,1664,-199,1664,-128 Z"
		},
		"cog": {
			"advanceWidth": 1536,
			"commands": "M,1024,640 C,1024,499,909,384,768,384 C,627,384,512,499,512,640 C,512,781,627,896,768,896 C,909,896,1024,781,1024,640 M,1536,749 C,1536,766,1524,782,1507,785 L,1324,813 C,1314,846,1300,879,1283,911 C,1317,958,1354,1002,1388,1048 C,1393,1055,1396,1062,1396,1071 C,1396,1079,1394,1087,1389,1093 C,1347,1152,1277,1214,1224,1263 C,1217,1269,1208,1273,1199,1273 C,1190,1273,1181,1270,1175,1264 L,1033,1157 C,1004,1172,974,1184,943,1194 L,915,1378 C,913,1395,897,1408,879,1408 L,657,1408 C,639,1408,625,1396,621,1380 C,605,1320,599,1255,592,1194 C,561,1184,530,1171,501,1156 L,363,1263 C,355,1269,346,1273,337,1273 C,303,1273,168,1127,144,1094 C,139,1087,135,1080,135,1071 C,135,1062,139,1054,145,1047 C,182,1002,218,957,252,909 C,236,879,223,849,213,817 L,27,789 C,12,786,0,768,0,753 L,0,531 C,0,514,12,498,29,495 L,212,468 C,222,434,236,401,253,369 C,219,322,182,278,148,232 C,143,225,140,218,140,209 C,140,201,142,193,147,186 C,189,128,259,66,312,18 C,319,11,328,7,337,7 C,346,7,355,10,362,16 L,503,123 C,532,108,562,96,593,86 L,621,-98 C,623,-115,639,-128,657,-128 L,879,-128 C,897,-128,911,-116,915,-100 C,931,-40,937,25,944,86 C,975,96,1006,109,1035,124 L,1173,16 C,1181,11,1190,7,1199,7 C,1233,7,1368,154,1392,186 C,1398,193,1401,200,1401,209 C,1401,218,1397,227,1391,234 C,1354,279,1318,323,1284,372 C,1300,401,1312,431,1323,463 L,1508,491 C,1524,494,1536,512,1536,527 Z"
		},
		"trash": {
			"advanceWidth": 1408,
			"commands": "M,512,800 C,512,818,498,832,480,832 L,416,832 C,398,832,384,818,384,800 L,384,224 C,384,206,398,192,416,192 L,480,192 C,498,192,512,206,512,224 M,768,800 C,768,818,754,832,736,832 L,672,832 C,654,832,640,818,640,800 L,640,224 C,640,206,654,192,672,192 L,736,192 C,754,192,768,206,768,224 M,1024,800 C,1024,818,1010,832,992,832 L,928,832 C,910,832,896,818,896,800 L,896,224 C,896,206,910,192,928,192 L,992,192 C,1010,192,1024,206,1024,224 M,1152,76 C,1152,28,1125,0,1120,0 L,288,0 C,283,0,256,28,256,76 L,256,1024 L,1152,1024 L,1152,76 M,480,1152 L,529,1269 C,532,1273,540,1279,546,1280 L,863,1280 C,868,1279,877,1273,880,1269 L,928,1152 M,1408,1120 C,1408,1138,1394,1152,1376,1152 L,1067,1152 L,997,1319 C,977,1368,917,1408,864,1408 L,544,1408 C,491,1408,431,1368,411,1319 L,341,1152 L,32,1152 C,14,1152,0,1138,0,1120 L,0,1056 C,0,1038,14,1024,32,1024 L,128,1024 L,128,72 C,128,-38,200,-128,288,-128 L,1120,-128 C,1208,-128,1280,-34,1280,76 L,1280,1024 L,1376,1024 C,1394,1024,1408,1038,1408,1056 Z"
		},
		"file_alt": {
			"advanceWidth": 1536,
			"commands": "M,1468,1156 L,1156,1468 C,1119,1505,1045,1536,992,1536 L,96,1536 C,43,1536,0,1493,0,1440 L,0,-160 C,0,-213,43,-256,96,-256 L,1440,-256 C,1493,-256,1536,-213,1536,-160 L,1536,992 C,1536,1045,1505,1119,1468,1156 M,1024,1400 C,1041,1394,1058,1385,1065,1378 L,1378,1065 C,1385,1058,1394,1041,1400,1024 L,1024,1024 M,1408,-128 L,128,-128 L,128,1408 L,896,1408 L,896,992 C,896,939,939,896,992,896 L,1408,896 Z"
		},
		"download_alt": {
			"advanceWidth": 1664,
			"commands": "M,1280,192 C,1280,157,1251,128,1216,128 C,1181,128,1152,157,1152,192 C,1152,227,1181,256,1216,256 C,1251,256,1280,227,1280,192 M,1536,192 C,1536,157,1507,128,1472,128 C,1437,128,1408,157,1408,192 C,1408,227,1437,256,1472,256 C,1507,256,1536,227,1536,192 M,1664,416 C,1664,469,1621,512,1568,512 L,1104,512 L,968,376 C,931,340,883,320,832,320 C,781,320,733,340,696,376 L,561,512 L,96,512 C,43,512,0,469,0,416 L,0,96 C,0,43,43,0,96,0 L,1568,0 C,1621,0,1664,43,1664,96 M,1339,985 C,1329,1008,1306,1024,1280,1024 L,1024,1024 L,1024,1472 C,1024,1507,995,1536,960,1536 L,704,1536 C,669,1536,640,1507,640,1472 L,640,1024 L,384,1024 C,358,1024,335,1008,325,985 C,315,961,320,933,339,915 L,787,467 C,799,454,816,448,832,448 C,848,448,865,454,877,467 L,1325,915 C,1344,933,1349,961,1339,985 Z"
		},
		"repeat": {
			"advanceWidth": 1536,
			"commands": "M,1536,1280 C,1536,1306,1520,1329,1497,1339 C,1473,1349,1445,1344,1427,1325 L,1297,1196 C,1156,1329,965,1408,768,1408 C,345,1408,0,1063,0,640 C,0,217,345,-128,768,-128 C,997,-128,1213,-27,1359,149 C,1369,162,1369,181,1357,192 L,1220,330 C,1213,336,1204,339,1195,339 C,1186,338,1177,334,1172,327 C,1074,200,927,128,768,128 C,486,128,256,358,256,640 C,256,922,486,1152,768,1152 C,899,1152,1023,1102,1117,1015 L,979,877 C,960,859,955,831,965,808 C,975,784,998,768,1024,768 L,1472,768 C,1507,768,1536,797,1536,832 Z"
		},
		"pencil": {
			"advanceWidth": 1536,
			"commands": "M,363,0 L,256,0 L,256,128 L,128,128 L,128,235 L,219,326 L,454,91 M,886,928 C,886,922,884,916,879,911 L,337,369 C,332,364,326,362,320,362 C,307,362,298,371,298,384 C,298,390,300,396,305,401 L,847,943 C,852,948,858,950,864,950 C,877,950,886,941,886,928 M,832,1120 L,0,288 L,0,-128 L,416,-128 L,1248,704 M,1515,1024 C,1515,1058,1501,1091,1478,1115 L,1243,1349 C,1219,1373,1186,1387,1152,1387 C,1118,1387,1085,1373,1062,1349 L,896,1184 L,1312,768 L,1478,934 C,1501,957,1515,990,1515,1024 Z"
		},
		"edit": {
			"advanceWidth": 1792,
			"commands": "M,888,352 L,832,352 L,832,448 L,736,448 L,736,504 L,852,620 L,1004,468 M,1328,1072 C,1337,1063,1336,1048,1327,1039 L,977,689 C,968,680,953,679,944,688 C,935,697,936,712,945,721 L,1295,1071 C,1304,1080,1319,1081,1328,1072 M,1408,478 C,1408,491,1400,502,1388,507 C,1376,512,1363,510,1353,500 L,1289,436 C,1283,430,1280,422,1280,414 L,1280,288 C,1280,200,1208,128,1120,128 L,288,128 C,200,128,128,200,128,288 L,128,1120 C,128,1208,200,1280,288,1280 L,1120,1280 C,1135,1280,1150,1278,1165,1274 C,1176,1270,1188,1273,1197,1282 L,1246,1331 C,1254,1339,1257,1349,1255,1360 C,1253,1370,1246,1379,1237,1383 C,1200,1400,1160,1408,1120,1408 L,288,1408 C,129,1408,0,1279,0,1120 L,0,288 C,0,129,129,0,288,0 L,1120,0 C,1279,0,1408,129,1408,288 M,1312,1216 L,640,544 L,640,256 L,928,256 L,1600,928 M,1756,1084 C,1793,1121,1793,1183,1756,1220 L,1604,1372 C,1567,1409,1505,1409,1468,1372 L,1376,1280 L,1664,992 L,1756,1084 Z"
		},
		"play": {
			"advanceWidth": 1408,
			"commands": "M,1384,609 C,1415,626,1415,654,1384,671 L,56,1409 C,25,1426,0,1411,0,1376 L,0,-96 C,0,-131,25,-146,56,-129 Z"
		},
		"pause": {
			"advanceWidth": 1536,
			"commands": "M,1536,1344 C,1536,1379,1507,1408,1472,1408 L,960,1408 C,925,1408,896,1379,896,1344 L,896,-64 C,896,-99,925,-128,960,-128 L,1472,-128 C,1507,-128,1536,-99,1536,-64 M,640,1344 C,640,1379,611,1408,576,1408 L,64,1408 C,29,1408,0,1379,0,1344 L,0,-64 C,0,-99,29,-128,64,-128 L,576,-128 C,611,-128,640,-99,640,-64 Z"
		},
		"stop": {
			"advanceWidth": 1536,
			"commands": "M,1536,1344 C,1536,1379,1507,1408,1472,1408 L,64,1408 C,29,1408,0,1379,0,1344 L,0,-64 C,0,-99,29,-128,64,-128 L,1472,-128 C,1507,-128,1536,-99,1536,-64 Z"
		},
		"resize_full": {
			"advanceWidth": 1536,
			"commands": "M,755,480 C,755,488,751,497,745,503 L,631,617 C,625,623,616,627,608,627 C,600,627,591,623,585,617 L,253,285 L,109,429 C,97,441,81,448,64,448 C,29,448,0,419,0,384 L,0,-64 C,0,-99,29,-128,64,-128 L,512,-128 C,547,-128,576,-99,576,-64 C,576,-47,569,-31,557,-19 L,413,125 L,745,457 C,751,463,755,472,755,480 M,1536,1344 C,1536,1379,1507,1408,1472,1408 L,1024,1408 C,989,1408,960,1379,960,1344 C,960,1327,967,1311,979,1299 L,1123,1155 L,791,823 C,785,817,781,808,781,800 C,781,792,785,783,791,777 L,905,663 C,911,657,920,653,928,653 C,936,653,945,657,951,663 L,1283,995 L,1427,851 C,1439,839,1455,832,1472,832 C,1507,832,1536,861,1536,896 Z"
		},
		"resize_small": {
			"advanceWidth": 1536,
			"commands": "M,768,576 C,768,611,739,640,704,640 L,256,640 C,221,640,192,611,192,576 C,192,559,199,543,211,531 L,355,387 L,23,55 C,17,49,13,40,13,32 C,13,24,17,15,23,9 L,137,-105 C,143,-111,152,-115,160,-115 C,168,-115,177,-111,183,-105 L,515,227 L,659,83 C,671,71,687,64,704,64 C,739,64,768,93,768,128 M,1523,1248 C,1523,1256,1519,1265,1513,1271 L,1399,1385 C,1393,1391,1384,1395,1376,1395 C,1368,1395,1359,1391,1353,1385 L,1021,1053 L,877,1197 C,865,1209,849,1216,832,1216 C,797,1216,768,1187,768,1152 L,768,704 C,768,669,797,640,832,640 L,1280,640 C,1315,640,1344,669,1344,704 C,1344,721,1337,737,1325,749 L,1181,893 L,1513,1225 C,1519,1231,1523,1240,1523,1248 Z"
		},
		"eye_open": {
			"advanceWidth": 1792,
			"commands": "M,1664,576 C,1493,312,1217,128,896,128 C,575,128,299,312,128,576 C,223,723,353,849,509,929 C,469,861,448,783,448,704 C,448,457,649,256,896,256 C,1143,256,1344,457,1344,704 C,1344,783,1323,861,1283,929 C,1439,849,1569,723,1664,576 M,944,960 C,944,934,922,912,896,912 C,782,912,688,818,688,704 C,688,678,666,656,640,656 C,614,656,592,678,592,704 C,592,871,729,1008,896,1008 C,922,1008,944,986,944,960 M,1792,576 C,1792,601,1784,624,1772,645 C,1588,947,1251,1152,896,1152 C,541,1152,204,947,20,645 C,8,624,0,601,0,576 C,0,551,8,528,20,507 C,204,205,541,0,896,0 C,1251,0,1588,204,1772,507 C,1784,528,1792,551,1792,576 Z"
		},
		"eye_close": {
			"advanceWidth": 1792,
			"commands": "M,555,201 C,379,280,232,415,128,576 C,223,723,353,849,509,929 C,469,861,448,783,448,704 C,448,561,517,426,633,342 M,944,960 C,944,934,922,912,896,912 C,782,912,688,819,688,704 C,688,678,666,656,640,656 C,614,656,592,678,592,704 C,592,871,729,1008,896,1008 C,922,1008,944,986,944,960 M,1307,1151 C,1307,1162,1301,1172,1291,1178 C,1270,1190,1176,1248,1158,1248 C,1146,1248,1136,1242,1130,1232 L,1076,1135 C,1017,1146,956,1152,896,1152 C,527,1152,218,949,20,645 C,7,625,0,600,0,576 C,0,551,7,527,20,507 C,135,327,298,177,492,89 C,482,72,448,18,448,2 C,448,-10,454,-20,464,-26 C,485,-38,580,-96,598,-96 C,609,-96,620,-90,626,-80 L,675,9 C,886,386,1095,765,1306,1142 C,1307,1144,1307,1149,1307,1151 M,1344,704 C,1344,732,1341,760,1336,788 L,1056,286 C,1229,352,1344,518,1344,704 M,1792,576 C,1792,602,1785,623,1772,645 C,1694,774,1569,899,1445,982 L,1382,870 C,1495,792,1590,691,1664,576 C,1508,334,1261,157,970,132 L,896,0 C,1197,0,1467,137,1663,362 C,1702,407,1741,456,1772,507 C,1785,529,1792,550,1792,576 Z"
		},
		"folder_open": {
			"advanceWidth": 1920,
			"commands": "M,1879,584 C,1879,629,1828,640,1792,640 L,704,640 C,616,640,498,586,440,518 L,104,122 C,88,104,73,80,73,56 C,73,11,124,0,160,0 L,1248,0 C,1336,0,1454,54,1512,122 L,1848,518 C,1864,536,1879,560,1879,584 M,1536,928 C,1536,1051,1435,1152,1312,1152 L,768,1152 L,768,1184 C,768,1307,667,1408,544,1408 L,224,1408 C,101,1408,0,1307,0,1184 L,0,224 C,0,216,1,207,1,199 L,6,205 L,343,601 C,424,697,579,768,704,768 L,1536,768 Z"
		},
		"signin": {
			"advanceWidth": 1536,
			"commands": "M,1184,640 C,1184,657,1177,673,1165,685 L,621,1229 C,609,1241,593,1248,576,1248 C,541,1248,512,1219,512,1184 L,512,896 L,64,896 C,29,896,0,867,0,832 L,0,448 C,0,413,29,384,64,384 L,512,384 L,512,96 C,512,61,541,32,576,32 C,593,32,609,39,621,51 L,1165,595 C,1177,607,1184,623,1184,640 M,1536,992 C,1536,1151,1407,1280,1248,1280 L,928,1280 C,883,1280,896,1212,896,1184 C,896,1147,935,1152,960,1152 L,1248,1152 C,1336,1152,1408,1080,1408,992 L,1408,288 C,1408,200,1336,128,1248,128 L,928,128 C,883,128,896,60,896,32 C,896,15,911,0,928,0 L,1248,0 C,1407,0,1536,129,1536,288 Z"
		},
		"upload_alt": {
			"advanceWidth": 1664,
			"commands": "M,1280,64 C,1280,29,1251,0,1216,0 C,1181,0,1152,29,1152,64 C,1152,99,1181,128,1216,128 C,1251,128,1280,99,1280,64 M,1536,64 C,1536,29,1507,0,1472,0 C,1437,0,1408,29,1408,64 C,1408,99,1437,128,1472,128 C,1507,128,1536,99,1536,64 M,1664,288 C,1664,341,1621,384,1568,384 L,1141,384 C,1114,310,1043,256,960,256 L,704,256 C,621,256,550,310,523,384 L,96,384 C,43,384,0,341,0,288 L,0,-32 C,0,-85,43,-128,96,-128 L,1568,-128 C,1621,-128,1664,-85,1664,-32 M,1339,936 C,1349,959,1344,987,1325,1005 L,877,1453 C,865,1466,848,1472,832,1472 C,816,1472,799,1466,787,1453 L,339,1005 C,320,987,315,959,325,936 C,335,912,358,896,384,896 L,640,896 L,640,448 C,640,413,669,384,704,384 L,960,384 C,995,384,1024,413,1024,448 L,1024,896 L,1280,896 C,1306,896,1329,912,1339,936 Z"
		},
		"save": {
			"advanceWidth": 1536,
			"commands": "M,384,0 L,384,384 L,1152,384 L,1152,0 M,1280,0 L,1280,416 C,1280,469,1237,512,1184,512 L,352,512 C,299,512,256,469,256,416 L,256,0 L,128,0 L,128,1280 L,256,1280 L,256,864 C,256,811,299,768,352,768 L,928,768 C,981,768,1024,811,1024,864 L,1024,1280 C,1044,1280,1083,1264,1097,1250 L,1378,969 C,1391,956,1408,915,1408,896 L,1408,0 M,896,928 C,896,911,881,896,864,896 L,672,896 C,655,896,640,911,640,928 L,640,1248 C,640,1265,655,1280,672,1280 L,864,1280 C,881,1280,896,1265,896,1248 L,896,928 M,1536,896 C,1536,949,1506,1022,1468,1060 L,1188,1340 C,1150,1378,1077,1408,1024,1408 L,96,1408 C,43,1408,0,1365,0,1312 L,0,-32 C,0,-85,43,-128,96,-128 L,1440,-128 C,1493,-128,1536,-85,1536,-32 Z"
		},
		"undo": {
			"advanceWidth": 1536,
			"commands": "M,1536,640 C,1536,1063,1191,1408,768,1408 C,571,1408,380,1329,239,1196 L,109,1325 C,91,1344,63,1349,40,1339 C,16,1329,0,1306,0,1280 L,0,832 C,0,797,29,768,64,768 L,512,768 C,538,768,561,784,571,808 C,581,831,576,859,557,877 L,420,1015 C,513,1102,637,1152,768,1152 C,1050,1152,1280,922,1280,640 C,1280,358,1050,128,768,128 C,609,128,462,200,364,327 C,359,334,350,338,341,339 C,332,339,323,336,316,330 L,179,192 C,168,181,167,162,177,149 C,323,-27,539,-128,768,-128 C,1191,-128,1536,217,1536,640 Z"
		},
		"paste": {
			"advanceWidth": 1792,
			"commands": "M,768,-128 L,768,1024 L,1152,1024 L,1152,608 C,1152,555,1195,512,1248,512 L,1664,512 L,1664,-128 M,1024,1312 C,1024,1295,1009,1280,992,1280 L,288,1280 C,271,1280,256,1295,256,1312 L,256,1376 C,256,1393,271,1408,288,1408 L,992,1408 C,1009,1408,1024,1393,1024,1376 L,1024,1312 M,1280,640 L,1280,939 L,1579,640 M,1792,512 C,1792,565,1762,638,1724,676 L,1316,1084 C,1305,1095,1293,1104,1280,1112 L,1280,1440 C,1280,1493,1237,1536,1184,1536 L,96,1536 C,43,1536,0,1493,0,1440 L,0,96 C,0,43,43,0,96,0 L,640,0 L,640,-160 C,640,-213,683,-256,736,-256 L,1696,-256 C,1749,-256,1792,-213,1792,-160 Z"
		},
		"folder_open_alt": {
			"advanceWidth": 1920,
			"commands": "M,1781,605 C,1781,590,1772,577,1763,566 L,1469,203 C,1435,161,1365,128,1312,128 L,224,128 C,202,128,171,135,171,163 C,171,178,180,191,189,203 L,483,566 C,517,607,587,640,640,640 L,1728,640 C,1750,640,1781,633,1781,605 M,640,768 C,549,768,442,717,384,646 L,128,331 L,128,1184 C,128,1237,171,1280,224,1280 L,544,1280 C,597,1280,640,1237,640,1184 L,640,1120 C,640,1067,683,1024,736,1024 L,1312,1024 C,1365,1024,1408,981,1408,928 L,1408,768 M,1909,605 C,1909,629,1904,652,1894,673 C,1864,737,1796,768,1728,768 L,1536,768 L,1536,928 C,1536,1051,1435,1152,1312,1152 L,768,1152 L,768,1184 C,768,1307,667,1408,544,1408 L,224,1408 C,101,1408,0,1307,0,1184 L,0,224 C,0,101,101,0,224,0 L,1312,0 C,1402,0,1511,52,1568,122 L,1863,485 C,1890,519,1909,561,1909,605 Z"
		}
	}
}
},{}],13:[function(require,module,exports){
function IconButton(e,t,n,o){var s={padding:"0.2em 0.4em",margin:"0em",background:"none",outline:"none",fontSize:"16px",border:"none",borderRadius:"0.2em"},i=document.createElement("button");style(i,s);var r=document.createElement("canvas"),a=r.getContext("2d");i.appendChild(r),this.ctx=a,this.dom=i,this.canvas=r;var d=this;this.size=e;var l=1;this.resize=function(){l=window.devicePixelRatio;var n=e,o=font.fonts[t];r.height=n*l,r.style.height=n+"px";var s=n/font.unitsPerEm,i=o.advanceWidth*s+.5|0;i+=2,n+=2,r.width=i*l,r.style.width=i+"px",a.fillStyle=Theme.c,d.draw()},o&&o.on("resize",this.resize),this.setSize=function(t){e=t,this.resize()},this.setIcon=function(e){d.icon=e,font.fonts[e]||console.error("Font icon not found!"),this.resize()},this.onClick=function(e){i.addEventListener("click",e)};var c,h=500;this.onLongHold=function(e){function t(t){t.preventDefault(),t.stopPropagation(),c=setTimeout(function(){c&&e()},h)}function n(){clearTimeout(c)}i.addEventListener("mousedown",t),i.addEventListener("touchstart",t),i.addEventListener("mouseup",n),i.addEventListener("mouseout",n),i.addEventListener("touchend",n)},this.setTip=function(e){n=e};var u={border:"1px solid "+Theme.b},f={border:"1px solid transparent"},v="none",m=(Theme.c,Theme.b);i.style.background=v,style(i,f),i.addEventListener("mouseover",function(){style(i,u),a.fillStyle=Theme.d,a.shadowColor=Theme.b,a.shadowBlur=.5*l,a.shadowOffsetX=1*l,a.shadowOffsetY=1*l,d.draw(),n&&o&&o.fire("status",n)}),i.addEventListener("mousedown",function(){i.style.background=m}),i.addEventListener("mouseup",function(){i.style.background=v,style(i,u)}),i.addEventListener("mouseout",function(){i.style.background=v,style(i,f),d.dropshadow=!1,a.fillStyle=Theme.c,a.shadowColor=null,a.shadowBlur=0,a.shadowOffsetX=0,a.shadowOffsetY=0,d.draw()}),t&&this.setIcon(t)}var font=require("./font.json"),Theme=require("../theme"),style=require("../utils").style,dp;IconButton.prototype.CMD_MAP={M:"moveTo",L:"lineTo",Q:"quadraticCurveTo",C:"bezierCurveTo",Z:"closePath"},IconButton.prototype.draw=function(){if(this.icon){var e=this.ctx,t=font.fonts[this.icon],n=this.size,o=window.devicePixelRatio,s=n/font.unitsPerEm*o,i=t.commands.split(" ");if(e.save(),e.clearRect(0,0,this.canvas.width*o,this.canvas.height*o),this.dropshadow){e.save(),e.fillStyle=Theme.b,e.translate(1.5*o,1.5*o),e.scale(s,-s),e.translate(0,-font.ascender),e.beginPath();for(var r=0,a=i.length;a>r;r++){var d=i[r].split(","),l=d.slice(1);e[this.CMD_MAP[d[0]]].apply(e,l)}e.fill(),e.restore()}e.scale(s,-s),e.translate(0,-font.ascender),e.beginPath();for(var r=0,a=i.length;a>r;r++){var d=i[r].split(","),l=d.slice(1);e[this.CMD_MAP[d[0]]].apply(e,l)}e.fill(),e.restore()}},module.exports=IconButton;
},{"../theme":7,"../utils":11,"./font.json":12}],14:[function(require,module,exports){
function NumberUI(e){function n(e){e.moved?o():d.focus()}function t(e){var n=e.dx,t=e.dy,i=1*a;h=s+n*i+t*-i,h=Math.max(r,h),c.onChange.fire(h,!0)}function i(e){s=h}function o(){c.onChange.fire(h)}e=e||{};var r=void 0===e.min?-(1/0):e.min,a=e.step||.1,u=e.precision||3,d=document.createElement("input");style(d,{textAlign:"center",fontSize:"10px",padding:"1px",cursor:"ns-resize",width:"40px",margin:0,marginRight:"10px",appearance:"none",outline:"none",border:0,background:"none",borderBottom:"1px dotted "+Theme.c,color:Theme.c});var s,c=this,h=0;this.onChange=new Do,d.addEventListener("change",function(e){h=parseFloat(d.value,10),o()}),handleDrag(d,i,t,n),this.dom=d,this.setValue=function(e){h=e},this.paint=function(){null!=h&&(d.value=h.toFixed(u))}}var Theme=require("../theme"),Do=require("do.js"),style=require("../utils").style,handleDrag=require("../utils").handleDrag;module.exports=NumberUI;
},{"../theme":7,"../utils":11,"do.js":1}],15:[function(require,module,exports){
function ScrollBar(t,e,o){function r(t){t.preventDefault(),t.target==d?(g=t.clientY,document.addEventListener("mousemove",i,!1),document.addEventListener("mouseup",l,!1)):t.clientY<m?v.onScroll.fire("pageup"):t.clientY>m+h&&v.onScroll.fire("pagedown")}function i(t){t.preventDefault();var e=p-h,o=(t.clientY-g)/e;o>1&&(o=1),0>o&&(o=0),v.setPosition(o),v.onScroll.fire("scrollto",o)}function l(t){i(t),document.removeEventListener("mousemove",i,!1),document.removeEventListener("mouseup",l,!1)}var n=e?e:12,s=3,a=n+2*s,c=25,u=document.createElement("div");utils.style(u,scrolltrack_style);var p=t-2;u.style.height=p+"px",u.style.width=a+"px";var d=document.createElement("div");utils.style(d,scrollbar_style),d.style.width=n+"px",d.style.height=t/2,d.style.top=0,d.style.left=s+"px",u.appendChild(d);var h,m,v=this;this.setLength=function(t){t=Math.max(Math.min(1,t),0),t*=p,h=Math.max(t,c),d.style.height=h+"px"},this.setHeight=function(e){t=e,p=t-2,u.style.height=p+"px"},this.setPosition=function(t){t=Math.max(Math.min(1,t),0);var e=p-h;m=t*e,d.style.top=m},this.setLength(1),this.setPosition(0),this.onScroll=new SimpleEvent;var g;u.addEventListener("mousedown",r,!1),this.dom=u}var SimpleEvent=require("do.js"),utils=require("../utils"),scrolltrack_style={position:"absolute",background:"-webkit-gradient(linear, left top, right top, color-stop(0, rgb(29,29,29)), color-stop(0.6, rgb(50,50,50)) )",border:"1px solid rgb(29, 29, 29)",textAlign:"center",cursor:"pointer"},scrollbar_style={background:"-webkit-gradient(linear, left top, right top, color-stop(0.2, rgb(88,88,88)), color-stop(0.6, rgb(64,64,64)) )",border:"1px solid rgb(25,25,25)",position:"relative",borderRadius:"6px"};module.exports=ScrollBar;
},{"../utils":11,"do.js":1}]},{},[3,4,5,6,7,8,9,10,11]);
