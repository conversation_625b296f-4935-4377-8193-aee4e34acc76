{"manifest": {"name": "console-browserify", "version": "1.2.0", "description": "Emulate console for all the browsers", "keywords": [], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/browserify/console-browserify.git"}, "main": "index", "homepage": "https://github.com/browserify/console-browserify", "contributors": [{"name": "<PERSON><PERSON>"}], "bugs": {"url": "https://github.com/browserify/console-browserify/issues", "email": "<EMAIL>"}, "devDependencies": {"tape": "^2.12.3", "jsonify": "0.0.0", "tap-spec": "^0.1.8", "run-browser": "^1.3.0", "tap-dot": "^0.2.1"}, "licenses": [{"type": "MIT", "url": "http://github.com/browserify/console-browserify/raw/master/LICENSE"}], "scripts": {"test": "node ./test/index.js | tap-spec", "dot": "node ./test/index.js | tap-dot", "start": "node ./index.js", "cover": "istanbul cover --report none --print detail ./test/index.js", "view-cover": "istanbul report html && google-chrome ./coverage/index.html", "browser": "run-browser test/index.js", "phantom": "run-browser test/index.js -b | tap-spec", "build": "browserify test/index.js -o test/static/bundle.js", "testem": "testem"}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-console-browserify-1.2.0-67063cef57ceb6cf4993a2ab3a55840ae8c49336-integrity\\node_modules\\console-browserify\\package.json", "readmeFilename": "README.md", "readme": "# console-browserify [![Build Status](https://travis-ci.org/browserify/console-browserify.png?branch=master)](https://travis-ci.org/browserify/console-browserify)\n\nEmulate console for all the browsers\n\n## Install\n\nYou usually do not have to install `console-browserify` yourself! If your code runs in Node.js, `console` is built in. If your code runs in the browser, bundlers like [browserify](https://github.com/browserify/browserify) or [webpack](https://github.com/webpack/webpack) also include the `console-browserify` module when you do `require('console')`.\n\nBut if none of those apply, with npm do:\n\n```\nnpm install console-browserify\n```\n\n## Usage\n\n```js\nvar console = require(\"console\")\n// Or when manually using console-browserify directly:\n// var console = require(\"console-browserify\")\n\nconsole.log(\"hello world!\")\n```\n\n## API\n\nSee the [Node.js Console docs](https://nodejs.org/api/console.html). `console-browserify` does not support creating new `Console` instances and does not support the Inspector-only methods.\n\n## Contributing\n\nPRs are very welcome! The main way to contribute to `console-browserify` is by porting features, bugfixes and tests from Node.js. Ideally, code contributions to this module are copy-pasted from Node.js and transpiled to ES5, rather than reimplemented from scratch. Matching the Node.js code as closely as possible makes maintenance simpler when new changes land in Node.js.\nThis module intends to provide exactly the same API as Node.js, so features that are not available in the core `console` module will not be accepted. Feature requests should instead be directed at [nodejs/node](https://github.com/nodejs/node) and will be added to this module once they are implemented in Node.js.\n\nIf there is a difference in behaviour between Node.js's `console` module and this module, please open an issue!\n\n## Contributors\n\n - Raynos\n\n## License\n\n[MIT](./LICENSE)\n", "license": "MIT"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/console-browserify/-/console-browserify-1.2.0.tgz#67063cef57ceb6cf4993a2ab3a55840ae8c49336", "type": "tarball", "reference": "https://registry.yarnpkg.com/console-browserify/-/console-browserify-1.2.0.tgz", "hash": "67063cef57ceb6cf4993a2ab3a55840ae8c49336", "integrity": "sha512-ZMkYO/LkF17QvCPqM0gxw8yUzigAOZOSWSHg91FH6orS7vcEj5dVZTidN2fQ14yBSdg97RqhSNwLUXInd52OTA==", "registry": "npm", "packageName": "console-browserify", "cacheIntegrity": "sha512-ZMkYO/LkF17QvCPqM0gxw8yUzigAOZOSWSHg91FH6orS7vcEj5dVZTidN2fQ14yBSdg97RqhSNwLUXInd52OTA== sha1-ZwY871fOts9Jk6KrOlWECujEkzY="}, "registry": "npm", "hash": "67063cef57ceb6cf4993a2ab3a55840ae8c49336"}