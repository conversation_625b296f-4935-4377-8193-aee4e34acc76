-- Shared utility functions for Weather Health System

Utils = {}

-- Získání teplotní kategorie podle počasí
function Utils.GetTemperatureCategory(weather)
    local temp = Config.WeatherTemperature[weather] or 20

    -- Speciální handling pro sněhové počasí
    if weather == 'SNOW' or weather == 'BLIZZARD' or weather == 'SNOWLIGHT' or weather == 'XMAS' then
        return 'freezing'
    end

    if temp >= 25 then
        return 'hot'
    elseif temp >= 15 then
        return 'warm'
    elseif temp >= 5 then
        return 'cold'
    else
        return 'freezing'
    end
end

-- Výpočet tepelné hodnoty oblečení
function Utils.CalculateClothingWarmth(clothing)
    local warmth = 0
    
    -- Torso (horn<PERSON> č<PERSON> těla)
    if clothing.torso then
        warmth = warmth + (Config.ClothingWarmth.torso[clothing.torso] or 0)
    end
    
    -- Legs (nohy)
    if clothing.legs then
        warmth = warmth + (Config.ClothingWarmth.legs[clothing.legs] or 0)
    end
    
    -- Shoes (boty)
    if clothing.shoes then
        warmth = warmth + (Config.ClothingWarmth.shoes[clothing.shoes] or 0)
    end
    
    -- Hat (čepice)
    if clothing.hat then
        warmth = warmth + (Config.ClothingWarmth.hat[clothing.hat] or 0)
    end
    
    return warmth
end

-- Získání aktuálního oblečení hráče
function Utils.GetPlayerClothing(ped)
    if not ped then ped = PlayerPedId() end
    
    return {
        torso = GetPedDrawableVariation(ped, 11),
        legs = GetPedDrawableVariation(ped, 4),
        shoes = GetPedDrawableVariation(ped, 6),
        hat = GetPedPropIndex(ped, 0)
    }
end

-- Nastavení oblečení NPC
function Utils.SetNPCClothing(ped, outfit)
    if not DoesEntityExist(ped) then return false end
    
    -- Nastavení komponentů oblečení
    if outfit.torso then
        SetPedComponentVariation(ped, 11, outfit.torso, 0, 0)
    end
    
    if outfit.legs then
        SetPedComponentVariation(ped, 4, outfit.legs, 0, 0)
    end
    
    if outfit.shoes then
        SetPedComponentVariation(ped, 6, outfit.shoes, 0, 0)
    end
    
    if outfit.hat and outfit.hat > 0 then
        SetPedPropIndex(ped, 0, outfit.hat, 0, true)
    else
        ClearPedProp(ped, 0)
    end
    
    return true
end

-- Výpočet teplotního komfortu
function Utils.CalculateTemperatureComfort(weather, clothing)
    local weatherTemp = Config.WeatherTemperature[weather] or 20
    local clothingWarmth = Utils.CalculateClothingWarmth(clothing)
    
    -- Ideální teplota s oblečením
    local idealTemp = 20 + clothingWarmth
    local tempDifference = math.abs(weatherTemp - idealTemp)
    
    -- Určení kategorie komfortu
    local tolerance = Config.HealthEffects.temperatureTolerance
    
    if tempDifference <= tolerance.perfect.max then
        return 'perfect'
    elseif tempDifference <= tolerance.comfortable.max then
        return 'comfortable'
    elseif tempDifference <= tolerance.uncomfortable.max then
        return 'uncomfortable'
    elseif tempDifference <= tolerance.dangerous.max then
        return 'dangerous'
    else
        return 'critical'
    end
end

-- Získání náhodného outfitu pro NPC podle počasí a pohlaví
function Utils.GetRandomNPCOutfit(weather, gender)
    local tempCategory = Utils.GetTemperatureCategory(weather)
    local outfits = Config.NPCOutfits[tempCategory]
    
    if not outfits or not outfits[gender] then
        return nil
    end
    
    local randomIndex = math.random(1, #outfits[gender])
    return outfits[gender][randomIndex]
end

-- Kontrola, zda je čas denní nebo noční
function Utils.GetTimeCategory()
    local hour = GetClockHours()
    
    local timeConfig = Config.NPCBehavior.timeBasedBehavior
    
    if hour >= timeConfig.day.start and hour < timeConfig.evening.start then
        return 'day'
    elseif hour >= timeConfig.evening.start and hour < timeConfig.night.start then
        return 'evening'
    else
        return 'night'
    end
end

-- Získání aktivity faktoru podle času
function Utils.GetActivityFactor()
    local timeCategory = Utils.GetTimeCategory()
    local timeConfig = Config.NPCBehavior.timeBasedBehavior
    
    return timeConfig[timeCategory].activity or 1.0
end

-- Kontrola, zda NPC potřebuje úkryt
function Utils.NPCNeedsShelter(weather)
    local weatherBehavior = Config.NPCBehavior.weatherBehavior[weather]
    return weatherBehavior and weatherBehavior.seekShelter or false
end

-- Kontrola, zda NPC potřebuje teplo
function Utils.NPCNeedsWarmth(weather)
    local weatherBehavior = Config.NPCBehavior.weatherBehavior[weather]
    return weatherBehavior and weatherBehavior.seekWarmth or false
end

-- Získání faktoru aktivity podle počasí
function Utils.GetWeatherActivityFactor(weather)
    local weatherBehavior = Config.NPCBehavior.weatherBehavior[weather]
    if weatherBehavior then
        return weatherBehavior.activityReduction or 1.0
    end
    return 1.0
end

-- Formátování zpráv s parametry
function Utils.FormatMessage(message, ...)
    return string.format(message, ...)
end

-- Získání náhodné animace pro symptom
function Utils.GetSymptomAnimation(symptom)
    local animations = Config.Animations.symptoms
    return animations[symptom] or nil
end

-- Kontrola šance na spuštění animace
function Utils.ShouldPlayAnimation(chance)
    return math.random(1, 100) <= chance
end

-- Debug funkce
function Utils.Debug(message, ...)
    if Config.Debug then
        print(string.format("[WeatherHealth] " .. message, ...))
    end
end

-- Získání vzdálenosti mezi dvěma pozicemi
function Utils.GetDistance(pos1, pos2)
    return #(vector3(pos1.x, pos1.y, pos1.z) - vector3(pos2.x, pos2.y, pos2.z))
end

-- Kontrola, zda je hráč v interiéru
function Utils.IsPlayerInInterior()
    local ped = PlayerPedId()
    return GetInteriorFromEntity(ped) ~= 0
end

-- Získání náhodného čísla s normálním rozložením
function Utils.GetRandomNormal(mean, stddev)
    local u1 = math.random()
    local u2 = math.random()
    local z0 = math.sqrt(-2 * math.log(u1)) * math.cos(2 * math.pi * u2)
    return z0 * stddev + mean
end

-- Interpolace mezi dvěma hodnotami
function Utils.Lerp(a, b, t)
    return a + (b - a) * t
end

-- Clamp hodnoty mezi min a max
function Utils.Clamp(value, min, max)
    return math.max(min, math.min(max, value))
end
