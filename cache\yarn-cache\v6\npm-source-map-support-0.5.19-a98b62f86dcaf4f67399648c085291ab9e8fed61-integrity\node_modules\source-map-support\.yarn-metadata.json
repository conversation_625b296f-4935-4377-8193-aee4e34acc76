{"manifest": {"name": "source-map-support", "description": "Fixes stack traces for files with source maps", "version": "0.5.19", "main": "./source-map-support.js", "scripts": {"build": "node build.js", "serve-tests": "http-server -p 1336", "prepublish": "npm run build", "test": "mocha"}, "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}, "devDependencies": {"browserify": "^4.2.3", "coffeescript": "^1.12.7", "http-server": "^0.11.1", "mocha": "^3.5.3", "webpack": "^1.15.0"}, "repository": {"type": "git", "url": "https://github.com/evanw/node-source-map-support"}, "bugs": {"url": "https://github.com/evanw/node-source-map-support/issues"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-source-map-support-0.5.19-a98b62f86dcaf4f67399648c085291ab9e8fed61-integrity\\node_modules\\source-map-support\\package.json", "readmeFilename": "README.md", "readme": "# Source Map Support\n[![Build Status](https://travis-ci.org/evanw/node-source-map-support.svg?branch=master)](https://travis-ci.org/evanw/node-source-map-support)\n\nThis module provides source map support for stack traces in node via the [V8 stack trace API](https://github.com/v8/v8/wiki/Stack-Trace-API). It uses the [source-map](https://github.com/mozilla/source-map) module to replace the paths and line numbers of source-mapped files with their original paths and line numbers. The output mimics node's stack trace format with the goal of making every compile-to-JS language more of a first-class citizen. Source maps are completely general (not specific to any one language) so you can use source maps with multiple compile-to-JS languages in the same node process.\n\n## Installation and Usage\n\n#### Node support\n\n```\n$ npm install source-map-support\n```\n\nSource maps can be generated using libraries such as [source-map-index-generator](https://github.com/twolfson/source-map-index-generator). Once you have a valid source map, place a source mapping comment somewhere in the file (usually done automatically or with an option by your transpiler):\n\n```\n//# sourceMappingURL=path/to/source.map\n```\n\nIf multiple sourceMappingURL comments exist in one file, the last sourceMappingURL comment will be\nrespected (e.g. if a file mentions the comment in code, or went through multiple transpilers).\nThe path should either be absolute or relative to the compiled file.\n\nFrom here you have two options.\n\n##### CLI Usage\n\n```bash\nnode -r source-map-support/register compiled.js\n```\n\n##### Programmatic Usage\n\nPut the following line at the top of the compiled file.\n\n```js\nrequire('source-map-support').install();\n```\n\nIt is also possible to install the source map support directly by\nrequiring the `register` module which can be handy with ES6:\n\n```js\nimport 'source-map-support/register'\n\n// Instead of:\nimport sourceMapSupport from 'source-map-support'\nsourceMapSupport.install()\n```\nNote: if you're using babel-register, it includes source-map-support already.\n\nIt is also very useful with Mocha:\n\n```\n$ mocha --require source-map-support/register tests/\n```\n\n#### Browser support\n\nThis library also works in Chrome. While the DevTools console already supports source maps, the V8 engine doesn't and `Error.prototype.stack` will be incorrect without this library. Everything will just work if you deploy your source files using [browserify](http://browserify.org/). Just make sure to pass the `--debug` flag to the browserify command so your source maps are included in the bundled code.\n\nThis library also works if you use another build process or just include the source files directly. In this case, include the file `browser-source-map-support.js` in your page and call `sourceMapSupport.install()`. It contains the whole library already bundled for the browser using browserify.\n\n```html\n<script src=\"browser-source-map-support.js\"></script>\n<script>sourceMapSupport.install();</script>\n```\n\nThis library also works if you use AMD (Asynchronous Module Definition), which is used in tools like [RequireJS](http://requirejs.org/). Just list `browser-source-map-support` as a dependency:\n\n```html\n<script>\n  define(['browser-source-map-support'], function(sourceMapSupport) {\n    sourceMapSupport.install();\n  });\n</script>\n```\n\n## Options\n\nThis module installs two things: a change to the `stack` property on `Error` objects and a handler for uncaught exceptions that mimics node's default exception handler (the handler can be seen in the demos below). You may want to disable the handler if you have your own uncaught exception handler. This can be done by passing an argument to the installer:\n\n```js\nrequire('source-map-support').install({\n  handleUncaughtExceptions: false\n});\n```\n\nThis module loads source maps from the filesystem by default. You can provide alternate loading behavior through a callback as shown below. For example, [Meteor](https://github.com/meteor) keeps all source maps cached in memory to avoid disk access.\n\n```js\nrequire('source-map-support').install({\n  retrieveSourceMap: function(source) {\n    if (source === 'compiled.js') {\n      return {\n        url: 'original.js',\n        map: fs.readFileSync('compiled.js.map', 'utf8')\n      };\n    }\n    return null;\n  }\n});\n```\n\nThe module will by default assume a browser environment if XMLHttpRequest and window are defined. If either of these do not exist it will instead assume a node environment.\nIn some rare cases, e.g. when running a browser emulation and where both variables are also set, you can explictly specify the environment to be either 'browser' or 'node'.\n\n```js\nrequire('source-map-support').install({\n  environment: 'node'\n});\n```\n\nTo support files with inline source maps, the `hookRequire` options can be specified, which will monitor all source files for inline source maps.\n\n\n```js\nrequire('source-map-support').install({\n  hookRequire: true\n});\n```\n\nThis monkey patches the `require` module loading chain, so is not enabled by default and is not recommended for any sort of production usage.\n\n## Demos\n\n#### Basic Demo\n\noriginal.js:\n\n```js\nthrow new Error('test'); // This is the original code\n```\n\ncompiled.js:\n\n```js\nrequire('source-map-support').install();\n\nthrow new Error('test'); // This is the compiled code\n// The next line defines the sourceMapping.\n//# sourceMappingURL=compiled.js.map\n```\n\ncompiled.js.map:\n\n```json\n{\n  \"version\": 3,\n  \"file\": \"compiled.js\",\n  \"sources\": [\"original.js\"],\n  \"names\": [],\n  \"mappings\": \";;AAAA,MAAM,IAAI\"\n}\n```\n\nRun compiled.js using node (notice how the stack trace uses original.js instead of compiled.js):\n\n```\n$ node compiled.js\n\noriginal.js:1\nthrow new Error('test'); // This is the original code\n      ^\nError: test\n    at Object.<anonymous> (original.js:1:7)\n    at Module._compile (module.js:456:26)\n    at Object.Module._extensions..js (module.js:474:10)\n    at Module.load (module.js:356:32)\n    at Function.Module._load (module.js:312:12)\n    at Function.Module.runMain (module.js:497:10)\n    at startup (node.js:119:16)\n    at node.js:901:3\n```\n\n#### TypeScript Demo\n\ndemo.ts:\n\n```typescript\ndeclare function require(name: string);\nrequire('source-map-support').install();\nclass Foo {\n  constructor() { this.bar(); }\n  bar() { throw new Error('this is a demo'); }\n}\nnew Foo();\n```\n\nCompile and run the file using the TypeScript compiler from the terminal:\n\n```\n$ npm install source-map-support typescript\n$ node_modules/typescript/bin/tsc -sourcemap demo.ts\n$ node demo.js\n\ndemo.ts:5\n  bar() { throw new Error('this is a demo'); }\n                ^\nError: this is a demo\n    at Foo.bar (demo.ts:5:17)\n    at new Foo (demo.ts:4:24)\n    at Object.<anonymous> (demo.ts:7:1)\n    at Module._compile (module.js:456:26)\n    at Object.Module._extensions..js (module.js:474:10)\n    at Module.load (module.js:356:32)\n    at Function.Module._load (module.js:312:12)\n    at Function.Module.runMain (module.js:497:10)\n    at startup (node.js:119:16)\n    at node.js:901:3\n```\n\nThere is also the option to use `-r source-map-support/register` with typescript, without the need add the `require('source-map-support').install()` in the code base:\n\n```\n$ npm install source-map-support typescript\n$ node_modules/typescript/bin/tsc  -sourcemap demo.ts\n$ node -r source-map-support/register demo.js\n\ndemo.ts:5\n  bar() { throw new Error('this is a demo'); }\n                ^\nError: this is a demo\n    at Foo.bar (demo.ts:5:17)\n    at new Foo (demo.ts:4:24)\n    at Object.<anonymous> (demo.ts:7:1)\n    at Module._compile (module.js:456:26)\n    at Object.Module._extensions..js (module.js:474:10)\n    at Module.load (module.js:356:32)\n    at Function.Module._load (module.js:312:12)\n    at Function.Module.runMain (module.js:497:10)\n    at startup (node.js:119:16)\n    at node.js:901:3\n```\n\n#### CoffeeScript Demo\n\ndemo.coffee:\n\n```coffee\nrequire('source-map-support').install()\nfoo = ->\n  bar = -> throw new Error 'this is a demo'\n  bar()\nfoo()\n```\n\nCompile and run the file using the CoffeeScript compiler from the terminal:\n\n```sh\n$ npm install source-map-support coffeescript\n$ node_modules/.bin/coffee --map --compile demo.coffee\n$ node demo.js\n\ndemo.coffee:3\n  bar = -> throw new Error 'this is a demo'\n                     ^\nError: this is a demo\n    at bar (demo.coffee:3:22)\n    at foo (demo.coffee:4:3)\n    at Object.<anonymous> (demo.coffee:5:1)\n    at Object.<anonymous> (demo.coffee:1:1)\n    at Module._compile (module.js:456:26)\n    at Object.Module._extensions..js (module.js:474:10)\n    at Module.load (module.js:356:32)\n    at Function.Module._load (module.js:312:12)\n    at Function.Module.runMain (module.js:497:10)\n    at startup (node.js:119:16)\n```\n\n## Tests\n\nThis repo contains both automated tests for node and manual tests for the browser. The automated tests can be run using mocha (type `mocha` in the root directory). To run the manual tests:\n\n* Build the tests using `build.js`\n* Launch the HTTP server (`npm run serve-tests`) and visit\n  * http://127.0.0.1:1336/amd-test\n  * http://127.0.0.1:1336/browser-test\n  * http://127.0.0.1:1336/browserify-test - **Currently not working** due to a bug with browserify (see [pull request #66](https://github.com/evanw/node-source-map-support/pull/66) for details).\n* For `header-test`, run `server.js` inside that directory and visit http://127.0.0.1:1337/\n\n## License\n\nThis code is available under the [MIT license](http://opensource.org/licenses/MIT).\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 Evan <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/source-map-support/-/source-map-support-0.5.19.tgz#a98b62f86dcaf4f67399648c085291ab9e8fed61", "type": "tarball", "reference": "https://registry.yarnpkg.com/source-map-support/-/source-map-support-0.5.19.tgz", "hash": "a98b62f86dcaf4f67399648c085291ab9e8fed61", "integrity": "sha512-Wonm7zOCIJzBGQdB+thsPar0kYuCIzYvxZwlBa87yi/Mdjv7Tip2cyVbLj5o0cFPN4EVkuTwb3GDDyUx2DGnGw==", "registry": "npm", "packageName": "source-map-support", "cacheIntegrity": "sha512-Wonm7zOCIJzBGQdB+thsPar0kYuCIzYvxZwlBa87yi/Mdjv7Tip2cyVbLj5o0cFPN4EVkuTwb3GDDyUx2DGnGw== sha1-qYti+G3K9PZzmWSMCFKRq56P7WE="}, "registry": "npm", "hash": "a98b62f86dcaf4f67399648c085291ab9e8fed61"}