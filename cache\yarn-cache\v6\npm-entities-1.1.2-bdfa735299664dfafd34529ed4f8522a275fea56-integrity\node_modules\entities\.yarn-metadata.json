{"manifest": {"name": "entities", "version": "1.1.2", "description": "Encode & decode XML/HTML entities with ease", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["html", "xml", "entity", "decoding", "encoding"], "main": "./index.js", "devDependencies": {"mocha": "^5.0.1", "mocha-lcov-reporter": "*", "coveralls": "*", "istanbul": "*", "jshint": "2"}, "scripts": {"test": "mocha && npm run lint", "lint": "jshint index.js lib/*.js test/*.js", "lcov": "istanbul cover _mocha --report lcovonly -- -R spec", "coveralls": "npm run lint && npm run lcov && (cat coverage/lcov.info | coveralls || exit 0)"}, "repository": {"type": "git", "url": "git://github.com/fb55/entities.git"}, "license": "BSD-2-<PERSON><PERSON>", "jshintConfig": {"eqeqeq": true, "freeze": true, "latedef": "nofunc", "noarg": true, "nonbsp": true, "quotmark": "double", "undef": true, "unused": true, "trailing": true, "eqnull": true, "proto": true, "smarttabs": true, "node": true, "globals": {"describe": true, "it": true}}, "prettier": {"tabWidth": 4}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-entities-1.1.2-bdfa735299664dfafd34529ed4f8522a275fea56-integrity\\node_modules\\entities\\package.json", "readmeFilename": "readme.md", "readme": "# entities [![NPM version](http://img.shields.io/npm/v/entities.svg)](https://npmjs.org/package/entities)  [![Downloads](https://img.shields.io/npm/dm/entities.svg)](https://npmjs.org/package/entities) [![Build Status](http://img.shields.io/travis/fb55/entities.svg)](http://travis-ci.org/fb55/entities) [![Coverage](http://img.shields.io/coveralls/fb55/entities.svg)](https://coveralls.io/r/fb55/entities)\n\nEn- & decoder for XML/HTML entities.\n\n## How to…\n\n### …install `entities`\n\n    npm i entities\n\n### …use `entities`\n\n```javascript\nvar entities = require(\"entities\");\n//encoding\nentities.encodeXML(\"&#38;\");  // \"&amp;#38;\"\nentities.encodeHTML(\"&#38;\"); // \"&amp;&num;38&semi;\"\n//decoding\nentities.decodeXML(\"asdf &amp; &#xFF; &#xFC; &apos;\");  // \"asdf & ÿ ü '\"\nentities.decodeHTML(\"asdf &amp; &yuml; &uuml; &apos;\"); // \"asdf & ÿ ü '\"\n```\n\n<!-- TODO extend API -->\n\n---\n\nLicense: BSD-2-Clause\n", "licenseText": "Copyright (c) <PERSON>\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n\nRedistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.\n\nRedistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.\n\nTHIS IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERC<PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS,\nEVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/entities/-/entities-1.1.2.tgz#bdfa735299664dfafd34529ed4f8522a275fea56", "type": "tarball", "reference": "https://registry.yarnpkg.com/entities/-/entities-1.1.2.tgz", "hash": "bdfa735299664dfafd34529ed4f8522a275fea56", "integrity": "sha512-f2LZMYl1Fzu7YSBKg+RoROelpOaNrcGmE9AZubeDfrCEia483oW4MI4VyFd5VNHIgQ/7qm1I0wUHK1eJnn2y2w==", "registry": "npm", "packageName": "entities", "cacheIntegrity": "sha512-f2LZMYl1Fzu7YSBKg+RoROelpOaNrcGmE9AZubeDfrCEia483oW4MI4VyFd5VNHIgQ/7qm1I0wUHK1eJnn2y2w== sha1-vfpzUplmTfr9NFKe1PhSKidf6lY="}, "registry": "npm", "hash": "bdfa735299664dfafd34529ed4f8522a275fea56"}