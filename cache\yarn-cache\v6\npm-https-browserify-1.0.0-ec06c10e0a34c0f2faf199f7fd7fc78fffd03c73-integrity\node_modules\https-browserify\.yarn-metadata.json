{"manifest": {"name": "https-browserify", "description": "https module compatability for browserify", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "devDependencies": {"standard": "^9.0.2"}, "homepage": "https://github.com/substack/https-browserify", "keywords": ["browser", "browserify", "https"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/substack/https-browserify.git"}, "scripts": {"test": "standard"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-https-browserify-1.0.0-ec06c10e0a34c0f2faf199f7fd7fc78fffd03c73-integrity\\node_modules\\https-browserify\\package.json", "readmeFilename": "readme.markdown", "readme": "# https-browserify\n\nhttps module compatability for browserify\n\n# example\n\n``` js\nvar https = require('https-browserify')\nvar r = https.request('https://github.com')\nr.on('request', function (res) {\n  console.log(res)\n})\n```\n\n# methods\n\nThe API is the same as the client portion of the\n[node core https module](http://nodejs.org/docs/latest/api/https.html).\n\n# license\n\nMIT\n", "licenseText": "This software is released under the MIT license:\n\nCopyright (c) James <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/https-browserify/-/https-browserify-1.0.0.tgz#ec06c10e0a34c0f2faf199f7fd7fc78fffd03c73", "type": "tarball", "reference": "https://registry.yarnpkg.com/https-browserify/-/https-browserify-1.0.0.tgz", "hash": "ec06c10e0a34c0f2faf199f7fd7fc78fffd03c73", "integrity": "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=", "registry": "npm", "packageName": "https-browserify", "cacheIntegrity": "sha512-J+FkSdyD+0mA0N+81tMotaRMfSL9SGi+xpD3T6YApKsc3bGSXJlfXri3VyFOeYkfLRQisDk1W+jIFFKBeUBbBg== sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM="}, "registry": "npm", "hash": "ec06c10e0a34c0f2faf199f7fd7fc78fffd03c73"}