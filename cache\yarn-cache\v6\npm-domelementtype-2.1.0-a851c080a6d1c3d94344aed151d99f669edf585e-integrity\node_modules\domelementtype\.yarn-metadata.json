{"manifest": {"name": "domelementtype", "version": "2.1.0", "description": "all the types of nodes in htmlparser2's dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "sideEffects": false, "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib/**/*"], "repository": {"type": "git", "url": "git://github.com/fb55/domelementtype.git"}, "keywords": ["dom", "htmlparser2"], "scripts": {"test": "npm run lint && prettier --check **/*.{ts,json,md}", "lint": "eslint src", "format": "prettier --write **/*.{ts,json,md}", "build": "tsc", "prepare": "npm run build"}, "prettier": {"tabWidth": 4}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "eslint": "^7.9.0", "eslint-config-prettier": "^6.0.0", "prettier": "^2.1.1", "typescript": "^4.0.2"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-domelementtype-2.1.0-a851c080a6d1c3d94344aed151d99f669edf585e-integrity\\node_modules\\domelementtype\\package.json", "readmeFilename": "readme.md", "readme": "All the types of nodes in htmlparser2's DOM.\n", "licenseText": "Copyright (c) <PERSON>\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n\nRedistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.\n\nRedistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.\n\nTHIS IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERC<PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS,\nEVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/domelementtype/-/domelementtype-2.1.0.tgz#a851c080a6d1c3d94344aed151d99f669edf585e", "type": "tarball", "reference": "https://registry.yarnpkg.com/domelementtype/-/domelementtype-2.1.0.tgz", "hash": "a851c080a6d1c3d94344aed151d99f669edf585e", "integrity": "sha512-LsTgx/L5VpD+Q8lmsXSHW2WpA+eBlZ9HPf3erD1IoPF00/3JKHZ3BknUVA2QGDNu69ZNmyFmCWBSO45XjYKC5w==", "registry": "npm", "packageName": "domelementtype", "cacheIntegrity": "sha512-LsTgx/L5VpD+Q8lmsXSHW2WpA+eBlZ9HPf3erD1IoPF00/3JKHZ3BknUVA2QGDNu69ZNmyFmCWBSO45XjYKC5w== sha1-qFHAgKbRw9lDRK7RUdmfZp7fWF4="}, "registry": "npm", "hash": "a851c080a6d1c3d94344aed151d99f669edf585e"}