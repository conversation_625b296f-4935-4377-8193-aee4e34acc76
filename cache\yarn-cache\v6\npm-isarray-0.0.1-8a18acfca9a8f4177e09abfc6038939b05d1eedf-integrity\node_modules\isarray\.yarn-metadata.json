{"manifest": {"name": "isarray", "description": "Array#isArray for older browsers", "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/juliangruber/isarray.git"}, "homepage": "https://github.com/juliangruber/isarray", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {}, "devDependencies": {"tap": "*"}, "keywords": ["browser", "isarray", "array"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-isarray-0.0.1-8a18acfca9a8f4177e09abfc6038939b05d1eedf-integrity\\node_modules\\isarray\\package.json", "readmeFilename": "README.md", "readme": "\n# isarray\n\n`Array#isArray` for older browsers.\n\n## Usage\n\n```js\nvar isArray = require('isarray');\n\nconsole.log(isArray([])); // => true\nconsole.log(isArray({})); // => false\n```\n\n## Installation\n\nWith [npm](http://npmjs.org) do\n\n```bash\n$ npm install isarray\n```\n\nThen bundle for the browser with\n[browserify](https://github.com/substack/browserify).\n\nWith [component](http://component.io) do\n\n```bash\n$ component install juliangruber/isarray\n```\n\n## License\n\n(MIT)\n\nCopyright (c) 2013 Julian Gruber &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\nof the Software, and to permit persons to whom the Software is furnished to do\nso, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/isarray/-/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf", "type": "tarball", "reference": "https://registry.yarnpkg.com/isarray/-/isarray-0.0.1.tgz", "hash": "8a18acfca9a8f4177e09abfc6038939b05d1eedf", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "registry": "npm", "packageName": "isarray", "cacheIntegrity": "sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ== sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "registry": "npm", "hash": "8a18acfca9a8f4177e09abfc6038939b05d1eedf"}