{"manifest": {"name": "json5", "version": "1.0.1", "description": "JSON for humans.", "main": "lib/index.js", "bin": {"json5": "lib\\cli.js"}, "browser": "dist/index.js", "files": ["lib/", "dist/"], "scripts": {"build": "babel-node build/build.js && babel src -d lib && rollup -c", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint": "eslint --fix build src", "prepublishOnly": "npm run lint && npm test && npm run production", "pretest": "cross-env NODE_ENV=test npm run build", "preversion": "npm run lint && npm test && npm run production", "production": "cross-env NODE_ENV=production npm run build", "test": "nyc --reporter=html --reporter=text mocha"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.1", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "coveralls": "^3.0.0", "cross-env": "^5.1.4", "del": "^3.0.0", "eslint": "^4.18.2", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-promise": "^3.7.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^5.0.4", "nyc": "^11.4.1", "regenerate": "^1.3.3", "rollup": "^0.56.5", "rollup-plugin-babel": "^3.0.3", "rollup-plugin-commonjs": "^9.0.0", "rollup-plugin-node-resolve": "^3.2.0", "rollup-plugin-uglify": "^3.0.0", "sinon": "^4.4.2", "unicode-9.0.0": "^0.7.5"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-json5-1.0.1-779fb0018604fa854eacbf6252180d83543e3dbe-integrity\\node_modules\\json5\\package.json", "readmeFilename": "README.md", "readme": "# JSON5 – JSON for Humans\n\n[![Build Status](https://travis-ci.org/json5/json5.svg)][Build Status]\n[![Coverage\nStatus](https://coveralls.io/repos/github/json5/json5/badge.svg)][Coverage\nStatus]\n\nThe JSON5 Data Interchange Format (JSON5) is a superset of [JSON] that aims to\nalleviate some of the limitations of JSON by expanding its syntax to include\nsome productions from [ECMAScript 5.1].\n\nThis JavaScript library is the official reference implementation for JSON5\nparsing and serialization libraries.\n\n[Build Status]: https://travis-ci.org/json5/json5\n\n[Coverage Status]: https://coveralls.io/github/json5/json5\n\n[JSON]: https://tools.ietf.org/html/rfc7159\n\n[ECMAScript 5.1]: https://www.ecma-international.org/ecma-262/5.1/\n\n## Summary of Features\nThe following ECMAScript 5.1 features, which are not supported in JSON, have\nbeen extended to JSON5.\n\n### Objects\n- Object keys may be an ECMAScript 5.1 _[IdentifierName]_.\n- Objects may have a single trailing comma.\n\n### Arrays\n- Arrays may have a single trailing comma.\n\n### Strings\n- Strings may be single quoted.\n- Strings may span multiple lines by escaping new line characters.\n- Strings may include character escapes.\n\n### Numbers\n- Numbers may be hexadecimal.\n- Numbers may have a leading or trailing decimal point.\n- Numbers may be [IEEE 754] positive infinity, negative infinity, and NaN.\n- Numbers may begin with an explicit plus sign.\n\n### Comments\n- Single and multi-line comments are allowed.\n\n### White Space\n- Additional white space characters are allowed.\n\n[IdentifierName]: https://www.ecma-international.org/ecma-262/5.1/#sec-7.6\n\n[IEEE 754]: http://ieeexplore.ieee.org/servlet/opac?punumber=4610933\n\n## Short Example\n```js\n{\n  // comments\n  unquoted: 'and you can quote me on that',\n  singleQuotes: 'I can use \"double quotes\" here',\n  lineBreaks: \"Look, Mom! \\\nNo \\\\n's!\",\n  hexadecimal: 0xdecaf,\n  leadingDecimalPoint: .8675309, andTrailing: 8675309.,\n  positiveSign: +1,\n  trailingComma: 'in objects', andIn: ['arrays',],\n  \"backwardsCompatible\": \"with JSON\",\n}\n```\n\n## Specification\nFor a detailed explanation of the JSON5 format, please read the [official\nspecification](https://json5.github.io/json5-spec/).\n\n## Installation\n### Node.js\n```sh\nnpm install json5\n```\n\n```js\nconst JSON5 = require('json5')\n```\n\n### Browsers\n```html\n<script src=\"https://unpkg.com/json5@^1.0.0\"></script>\n```\n\nThis will create a global `JSON5` variable.\n\n## API\nThe JSON5 API is compatible with the [JSON API].\n\n[JSON API]:\nhttps://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON\n\n### JSON5.parse()\nParses a JSON5 string, constructing the JavaScript value or object described by\nthe string. An optional reviver function can be provided to perform a\ntransformation on the resulting object before it is returned.\n\n#### Syntax\n    JSON5.parse(text[, reviver])\n\n#### Parameters\n- `text`: The string to parse as JSON5.\n- `reviver`: If a function, this prescribes how the value originally produced by\n  parsing is transformed, before being returned.\n\n#### Return value\nThe object corresponding to the given JSON5 text.\n\n### JSON5.stringify()\nConverts a JavaScript value to a JSON5 string, optionally replacing values if a\nreplacer function is specified, or optionally including only the specified\nproperties if a replacer array is specified.\n\n#### Syntax\n    JSON5.stringify(value[, replacer[, space]])\n    JSON5.stringify(value[, options])\n\n#### Parameters\n- `value`: The value to convert to a JSON5 string.\n- `replacer`: A function that alters the behavior of the stringification\n  process, or an array of String and Number objects that serve as a whitelist\n  for selecting/filtering the properties of the value object to be included in\n  the JSON5 string. If this value is null or not provided, all properties of the\n  object are included in the resulting JSON5 string.\n- `space`: A String or Number object that's used to insert white space into the\n  output JSON5 string for readability purposes. If this is a Number, it\n  indicates the number of space characters to use as white space; this number is\n  capped at 10 (if it is greater, the value is just 10). Values less than 1\n  indicate that no space should be used. If this is a String, the string (or the\n  first 10 characters of the string, if it's longer than that) is used as white\n  space. If this parameter is not provided (or is null), no white space is used.\n  If white space is used, trailing commas will be used in objects and arrays.\n- `options`: An object with the following properties:\n  - `replacer`: Same as the `replacer` parameter.\n  - `space`: Same as the `space` parameter.\n  - `quote`: A String representing the quote character to use when serializing\n    strings.\n\n#### Return value\nA JSON5 string representing the value.\n\n### Node.js `require()` JSON5 files\nWhen using Node.js, you can `require()` JSON5 files by adding the following\nstatement.\n\n```js\nrequire('json5/lib/register')\n```\n\nThen you can load a JSON5 file with a Node.js `require()` statement. For\nexample:\n\n```js\nconst config = require('./config.json5')\n```\n\n## CLI\nSince JSON is more widely used than JSON5, this package includes a CLI for\nconverting JSON5 to JSON and for validating the syntax of JSON5 documents.\n\n### Installation\n```sh\nnpm install --global json5\n```\n\n### Usage\n```sh\njson5 [options] <file>\n```\n\nIf `<file>` is not provided, then STDIN is used.\n\n#### Options:\n- `-s`, `--space`: The number of spaces to indent or `t` for tabs\n- `-o`, `--out-file [file]`: Output to the specified file, otherwise STDOUT\n- `-v`, `--validate`: Validate JSON5 but do not output JSON\n- `-V`, `--version`: Output the version number\n- `-h`, `--help`: Output usage information\n\n## Contibuting\n### Development\n```sh\ngit clone https://github.com/json5/json5\ncd json5\nnpm install\n```\n\nWhen contributing code, please write relevant tests and run `npm test` and `npm\nrun lint` before submitting pull requests. Please use an editor that supports\n[EditorConfig](http://editorconfig.org/).\n\n### Issues\nTo report bugs or request features regarding the JSON5 data format, please\nsubmit an issue to the [official specification\nrepository](https://github.com/json5/json5-spec).\n\nTo report bugs or request features regarding the JavaScript implentation of\nJSON5, please submit an issue to this repository.\n\n## License\nMIT. See [LICENSE.md](./LICENSE.md) for details.\n\n## Credits\n[Assem Kishore](https://github.com/aseemk) founded this project.\n\n[Michael Bolin](http://bolinfest.com/) independently arrived at and published\nsome of these same ideas with awesome explanations and detail. Recommended\nreading: [Suggested Improvements to JSON](http://bolinfest.com/essays/json.html)\n\n[Douglas Crockford](http://www.crockford.com/) of course designed and built\nJSON, but his state machine diagrams on the [JSON website](http://json.org/), as\ncheesy as it may sound, gave us motivation and confidence that building a new\nparser to implement these ideas was within reach! The original\nimplementation of JSON5 was also modeled directly off of Doug’s open-source\n[json_parse.js] parser. We’re grateful for that clean and well-documented\ncode.\n\n[json_parse.js]:\nhttps://github.com/douglascrockford/JSON-js/blob/master/json_parse.js\n\n[Max Nanasy](https://github.com/MaxNanasy) has been an early and prolific\nsupporter, contributing multiple patches and ideas.\n\n[Andrew Eisenberg](https://github.com/aeisenberg) contributed the original\n`stringify` method.\n\n[Jordan Tucker](https://github.com/jordanbtucker) has aligned JSON5 more closely\nwith ES5, wrote the official JSON5 specification, completely rewrote the\ncodebase from the ground up, and is actively maintaining this project.\n", "licenseText": "MIT License\n\nCopyright (c) 2012-2018 <PERSON><PERSON><PERSON>, and [others].\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n[others]: https://github.com/json5/json5/contributors\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/json5/-/json5-1.0.1.tgz#779fb0018604fa854eacbf6252180d83543e3dbe", "type": "tarball", "reference": "https://registry.yarnpkg.com/json5/-/json5-1.0.1.tgz", "hash": "779fb0018604fa854eacbf6252180d83543e3dbe", "integrity": "sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow==", "registry": "npm", "packageName": "json5", "cacheIntegrity": "sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow== sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4="}, "registry": "npm", "hash": "779fb0018604fa854eacbf6252180d83543e3dbe"}