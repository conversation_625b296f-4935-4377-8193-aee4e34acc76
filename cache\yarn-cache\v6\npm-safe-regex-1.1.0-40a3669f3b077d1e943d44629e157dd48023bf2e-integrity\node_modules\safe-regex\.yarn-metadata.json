{"manifest": {"name": "safe-regex", "version": "1.1.0", "description": "detect possibly catastrophic, exponential-time regular expressions", "main": "index.js", "dependencies": {"ret": "~0.1.10"}, "devDependencies": {"tape": "^3.5.0"}, "scripts": {"test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/8", "ie/9", "ie/10", "firefox/latest", "chrome/latest", "opera/latest", "safari/latest"]}, "repository": {"type": "git", "url": "git://github.com/substack/safe-regex.git"}, "homepage": "https://github.com/substack/safe-regex", "keywords": ["catastrophic", "exponential", "regex", "safe", "sandbox"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-safe-regex-1.1.0-40a3669f3b077d1e943d44629e157dd48023bf2e-integrity\\node_modules\\safe-regex\\package.json", "readmeFilename": "readme.markdown", "readme": "# safe-regex\n\ndetect potentially\n[catastrophic](http://regular-expressions.mobi/catastrophic.html)\n[exponential-time](http://perlgeek.de/blog-en/perl-tips/in-search-of-an-exponetial-regexp.html)\nregular expressions by limiting the\n[star height](https://en.wikipedia.org/wiki/Star_height) to 1\n\nWARNING: This module merely *seems* to work given all the catastrophic regular\nexpressions I could find scouring the internet, but I don't have enough of a\nbackground in automata to be absolutely sure that this module will catch all\nexponential-time cases.\n\n[![browser support](https://ci.testling.com/substack/safe-regex.png)](https://ci.testling.com/substack/safe-regex)\n\n[![build status](https://secure.travis-ci.org/substack/safe-regex.png)](http://travis-ci.org/substack/safe-regex)\n\n# example\n\n``` js\nvar safe = require('safe-regex');\nvar regex = process.argv.slice(2).join(' ');\nconsole.log(safe(regex));\n```\n\n```\n$ node safe.js '(x+x+)+y'\nfalse\n$ node safe.js '(beep|boop)*'\ntrue\n$ node safe.js '(a+){10}'\nfalse\n$ node safe.js '\\blocation\\s*:[^:\\n]+\\b(Oakland|San Francisco)\\b'\ntrue\n```\n\n# methods\n\n``` js\nvar safe = require('safe-regex')\n```\n\n## var ok = safe(re, opts={})\n\nReturn a boolean `ok` whether or not the regex `re` is safe and not possibly\ncatastrophic.\n\n`re` can be a `RegExp` object or just a string.\n\nIf the `re` is a string and is an invalid regex, returns `false`.\n\n* `opts.limit` - maximum number of allowed repetitions in the entire regex.\nDefault: `25`.\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install safe-regex\n```\n\n# license\n\nMIT\n", "licenseText": "This software is released under the MIT license:\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\n<PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/safe-regex/-/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e", "type": "tarball", "reference": "https://registry.yarnpkg.com/safe-regex/-/safe-regex-1.1.0.tgz", "hash": "40a3669f3b077d1e943d44629e157dd48023bf2e", "integrity": "sha1-QKNmnzsHfR6UPURinhV91IAjvy4=", "registry": "npm", "packageName": "safe-regex", "cacheIntegrity": "sha512-aJXcif4xnaNUzvUuC5gcb46oTS7zvg4jpMTnuqtrEPlR3vFr4pxtdTwaF1Qs3Enjn9HK+ZlwQui+a7z0SywIzg== sha1-QKNmnzsHfR6UPURinhV91IAjvy4="}, "registry": "npm", "hash": "40a3669f3b077d1e943d44629e157dd48023bf2e"}