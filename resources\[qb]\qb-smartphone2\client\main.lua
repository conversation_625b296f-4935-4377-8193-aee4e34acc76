local QBCore = exports['qb-core']:GetCoreObject()

-- Lokální proměnné
local isPhoneOpen = false
local phoneData = {}
local batteryLevel = 100
local isCharging = false
local phoneNumber = nil
local phoneSettings = {}
local notifications = {}
local isLocked = true
local loginAttempts = 0

-- NUI Callbacks
RegisterNUICallback('closePhone', function(data, cb)
    print('[QB-Smartphone2] NUI callback: closePhone')
    ClosePhone()
    cb('ok')
end)

RegisterNUICallback('openApp', function(data, cb)
    print('[QB-Smartphone2] NUI callback: openApp -', data.app)
    QBCore.Functions.Notify('Otevírám aplikaci: ' .. data.app, 'success')
    cb('ok')
end)

RegisterNUICallback('makeCall', function(data, cb)
    print('[QB-Smartphone2] Volání na číslo:', data.number)
    QBCore.Functions.Notify('Volám na číslo: ' .. data.number, 'info')
    -- <PERSON><PERSON> přid<PERSON> logiku pro volání
    cb('ok')
end)

RegisterNUICallback('takePhoto', function(data, cb)
    print('[QB-Smartphone2] Pořizování fotky')
    QBCore.Functions.Notify('Fotka pořízena!', 'success')
    -- Zde přidáš logiku pro fotky
    cb('ok')
end)

RegisterNUICallback('recordVideo', function(data, cb)
    print('[QB-Smartphone2] Nahrávání videa')
    QBCore.Functions.Notify('Video se nahrává...', 'info')
    -- Zde přidáš logiku pro video
    cb('ok')
end)

-- Přidej timeout pro automatické zavření
CreateThread(function()
    while true do
        Wait(100)

        if isPhoneOpen then
            -- Zkontroluj, jestli hráč stále existuje
            if not DoesEntityExist(PlayerPedId()) then
                ClosePhone()
            end

            -- Zkontroluj, jestli hráč není v menu
            if IsPauseMenuActive() then
                ClosePhone()
            end
        end
    end
end)

RegisterNUICallback('getPhoneData', function(data, cb)
    QBCore.Functions.TriggerCallback('qb-smartphone2:server:getPhoneData', function(result)
        if result then
            phoneSettings = result
            cb({
                success = true,
                data = {
                    phoneNumber = phoneNumber,
                    settings = phoneSettings,
                    batteryLevel = batteryLevel,
                    isCharging = isCharging,
                    notifications = notifications
                }
            })
        else
            cb({ success = false })
        end
    end)
end)

RegisterNUICallback('saveSettings', function(data, cb)
    phoneSettings = data.settings
    TriggerServerEvent('qb-smartphone2:server:saveSettings', phoneSettings)
    cb('ok')
end)

RegisterNUICallback('unlockPhone', function(data, cb)
    local success = false
    
    if data.method == 'pin' then
        if data.pin == phoneSettings.pin_code then
            success = true
            isLocked = false
            loginAttempts = 0
        else
            loginAttempts = loginAttempts + 1
            if loginAttempts >= Config.MaxLoginAttempts then
                -- Zamkni telefon na určitou dobu
                SetTimeout(Config.LockoutTime * 1000, function()
                    loginAttempts = 0
                end)
            end
        end
    elseif data.method == 'fingerprint' then
        if phoneSettings.fingerprint_enabled == 1 then
            success = true
            isLocked = false
            loginAttempts = 0
        end
    end
    
    cb({ success = success, attempts = loginAttempts })
end)

RegisterNUICallback('lockPhone', function(data, cb)
    isLocked = true
    cb('ok')
end)

-- Funkce pro otevření telefonu
function OpenPhone()
    print('[QB-Smartphone2] Pokus o otevření telefonu')
    print('[QB-Smartphone2] isPhoneOpen:', isPhoneOpen)
    print('[QB-Smartphone2] batteryLevel:', batteryLevel)

    -- Dočasně vypnuto pro debug
    -- if not HasPhone() then
    --     print('[QB-Smartphone2] Hráč nemá telefon')
    --     QBCore.Functions.Notify('Nemáš telefon!', 'error')
    --     return
    -- end

    if batteryLevel <= 0 then
        print('[QB-Smartphone2] Vybitá baterie')
        QBCore.Functions.Notify('Telefon má vybitou baterii!', 'error')
        return
    end

    if isPhoneOpen then
        print('[QB-Smartphone2] Telefon už je otevřený')
        return
    end

    print('[QB-Smartphone2] Otevírám telefon...')
    isPhoneOpen = true
    SetNuiFocus(true, true)

    -- Malý delay pro jistotu, že NUI je připravené
    CreateThread(function()
        Wait(100)
        SendNUIMessage({
            action = 'openPhone',
            data = {
                phoneNumber = phoneNumber,
                settings = phoneSettings,
                batteryLevel = batteryLevel,
                isCharging = isCharging,
                isLocked = isLocked,
                notifications = notifications
            }
        })
    end)

    -- Spusť animaci
    PlayPhoneAnimation('open')

    -- Spusť baterii drain
    if Config.BatteryEnabled then
        StartBatteryDrain()
    end

    TriggerServerEvent('qb-smartphone2:server:openPhone')
    print('[QB-Smartphone2] Telefon otevřen')
end

-- Funkce pro zavření telefonu
function ClosePhone()
    print('[QB-Smartphone2] Pokus o zavření telefonu')
    print('[QB-Smartphone2] isPhoneOpen před zavřením:', isPhoneOpen)

    if not isPhoneOpen then
        print('[QB-Smartphone2] Telefon už je zavřený')
        return
    end

    print('[QB-Smartphone2] Zavírám telefon...')

    -- OKAMŽITĚ změň stav
    isPhoneOpen = false

    -- Vypni NUI focus
    SetNuiFocus(false, false)

    -- Pošli zprávu o zavření
    SendNUIMessage({
        action = 'closePhone'
    })

    -- Zastav animaci
    StopPhoneAnimation()

    -- Vyčisti cursor
    SetCursorLocation(0.5, 0.5)

    print('[QB-Smartphone2] Telefon zavřen, isPhoneOpen:', isPhoneOpen)

    TriggerServerEvent('qb-smartphone2:server:closePhone')

    print('[QB-Smartphone2] Telefon zavřen, isPhoneOpen:', isPhoneOpen)
end

-- Funkce pro kontrolu vlastnictví telefonu
function HasPhone()
    return QBCore.Functions.HasItem(Config.PhoneItem)
end

-- Animace telefonu
function PlayPhoneAnimation(type)
    local ped = PlayerPedId()
    local animDict = Config.Animations[type].dict
    local animName = Config.Animations[type].anim
    local flag = Config.Animations[type].flag
    
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(0)
    end
    
    TaskPlayAnim(ped, animDict, animName, 3.0, -1, -1, flag, 0, false, false, false)
end

function StopPhoneAnimation()
    local ped = PlayerPedId()
    ClearPedTasks(ped)
end

-- Baterie systém
function StartBatteryDrain()
    CreateThread(function()
        while isPhoneOpen and batteryLevel > 0 do
            Wait(60000) -- Každou minutu
            
            if not isCharging then
                batteryLevel = batteryLevel - Config.BatteryDrainRate
                if batteryLevel < 0 then batteryLevel = 0 end
                
                -- Aktualizuj UI
                SendNUIMessage({
                    action = 'updateBattery',
                    batteryLevel = batteryLevel
                })
                
                -- Uložit do databáze
                TriggerServerEvent('qb-smartphone2:server:updateBattery', batteryLevel)
                
                -- Pokud je baterie vybitá, zavři telefon
                if batteryLevel <= 0 then
                    QBCore.Functions.Notify('Telefon se vypnul - vybitá baterie!', 'error')
                    ClosePhone()
                    break
                end
            end
        end
    end)
end

-- Idle baterie drain
CreateThread(function()
    while true do
        Wait(60000) -- Každou minutu
        
        if not isPhoneOpen and not isCharging and batteryLevel > 0 then
            batteryLevel = batteryLevel - Config.BatteryDrainIdle
            if batteryLevel < 0 then batteryLevel = 0 end
            
            TriggerServerEvent('qb-smartphone2:server:updateBattery', batteryLevel)
        end
    end
end)

-- Nabíjení telefonu
RegisterNetEvent('qb-smartphone2:client:chargePhone', function()
    if isCharging then
        QBCore.Functions.Notify('Telefon se už nabíjí!', 'error')
        return
    end
    
    QBCore.Functions.Notify('Nabíjení telefonu...', 'success')
    isCharging = true
    
    CreateThread(function()
        while isCharging and batteryLevel < 100 do
            Wait(30000) -- Každých 30 sekund
            
            batteryLevel = batteryLevel + 5
            if batteryLevel > 100 then batteryLevel = 100 end
            
            -- Aktualizuj UI pokud je telefon otevřený
            if isPhoneOpen then
                SendNUIMessage({
                    action = 'updateBattery',
                    batteryLevel = batteryLevel
                })
            end
            
            TriggerServerEvent('qb-smartphone2:server:updateBattery', batteryLevel)
            
            if batteryLevel >= 100 then
                QBCore.Functions.Notify('Telefon je plně nabitý!', 'success')
                isCharging = false
                break
            end
        end
    end)
    
    -- Automaticky zastav nabíjení po 10 minutách
    SetTimeout(600000, function()
        if isCharging then
            isCharging = false
            QBCore.Functions.Notify('Nabíjení dokončeno', 'info')
        end
    end)
end)

-- Klávesové zkratky
CreateThread(function()
    while true do
        Wait(0)

        -- Klávesa pro otevření/zavření telefonu
        if IsControlJustPressed(0, Config.OpenKey) then
            print('[QB-Smartphone2] F1 stisknuto, isPhoneOpen:', isPhoneOpen)
            if isPhoneOpen then
                print('[QB-Smartphone2] Zavírám telefon přes F1')
                ClosePhone()
            else
                print('[QB-Smartphone2] Otevírám telefon přes F1')
                OpenPhone()
            end
            Wait(300) -- Prevence spamu
        end

        -- ESC pro zavření telefonu (pouze pokud je telefon otevřený)
        if isPhoneOpen and IsControlJustPressed(0, 322) then -- ESC
            print('[QB-Smartphone2] ESC stisknuto')
            ClosePhone()
            Wait(300) -- Prevence spamu
        end
    end
end)

-- Event handlery
RegisterNetEvent('qb-smartphone2:client:openPhone', function()
    OpenPhone()
end)

RegisterNetEvent('qb-smartphone2:client:closePhone', function()
    ClosePhone()
end)

RegisterNetEvent('qb-smartphone2:client:setPhoneNumber', function(number)
    phoneNumber = number
end)

RegisterNetEvent('qb-smartphone2:client:receivePhoneData', function(data)
    phoneData = data
    phoneNumber = data.phoneNumber
    batteryLevel = data.settings.battery_level or 100
    phoneSettings = data.settings
end)

RegisterNetEvent('qb-smartphone2:client:receiveNotification', function(notification)
    table.insert(notifications, notification)
    
    -- Zobraz notifikaci pokud je telefon zavřený
    if not isPhoneOpen then
        QBCore.Functions.Notify(notification.title .. ': ' .. notification.message, 'primary', 5000)
        
        -- Přehrát zvuk notifikace
        if Config.NotificationSound and phoneSettings.notification_sound == 1 then
            PlaySound(-1, "Menu_Accept", "Phone_SoundSet_Default", 0, 0, 1)
        end
        
        -- Vibrace
        if Config.VibrationEnabled and phoneSettings.vibration == 1 then
            SetPadShake(0, 100, 200)
        end
    else
        -- Aktualizuj UI s novou notifikací
        SendNUIMessage({
            action = 'newNotification',
            notification = notification
        })
    end
end)

RegisterNetEvent('qb-smartphone2:client:phoneShutdown', function()
    if isPhoneOpen then
        ClosePhone()
    end
    QBCore.Functions.Notify('Telefon se vypnul - vybitá baterie!', 'error')
end)

-- Bluetooth události
RegisterNetEvent('qb-smartphone2:client:bluetoothConnected', function(targetSource)
    QBCore.Functions.Notify('Bluetooth připojen', 'success')
    
    if isPhoneOpen then
        SendNUIMessage({
            action = 'bluetoothConnected',
            targetSource = targetSource
        })
    end
end)

RegisterNetEvent('qb-smartphone2:client:bluetoothDisconnected', function(targetSource)
    QBCore.Functions.Notify('Bluetooth odpojen', 'info')
    
    if isPhoneOpen then
        SendNUIMessage({
            action = 'bluetoothDisconnected',
            targetSource = targetSource
        })
    end
end)

-- Cleanup při unload
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        if isPhoneOpen then
            ClosePhone()
        end
    end
end)

-- Inicializace při načtení hráče
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    Wait(2000) -- Počkej na načtení ostatních dat
    
    local PlayerData = QBCore.Functions.GetPlayerData()
    phoneNumber = PlayerData.charinfo.phone
    
    -- Načti data telefonu ze serveru
    QBCore.Functions.TriggerCallback('qb-smartphone2:server:getPhoneData', function(result)
        if result then
            phoneSettings = result
            batteryLevel = result.battery_level or 100
        end
    end)
end)
