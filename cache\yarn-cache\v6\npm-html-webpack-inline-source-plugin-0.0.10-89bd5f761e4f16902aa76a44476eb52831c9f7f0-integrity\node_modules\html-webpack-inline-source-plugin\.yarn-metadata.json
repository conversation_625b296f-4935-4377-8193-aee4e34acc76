{"manifest": {"name": "html-webpack-inline-source-plugin", "version": "0.0.10", "description": "Embed javascript and css source inline when using the webpack dev server or middleware", "main": "index.js", "files": ["index.js"], "scripts": {"prepublish": "npm run test", "pretest": "semistandard", "test": "jasmine", "debug": "node-debug jasmine"}, "repository": {"type": "git", "url": "https://github.com/dustinjackson/html-webpack-inline-source-plugin.git"}, "keywords": ["webpack", "plugin", "html-webpack-plugin", "inline", "source"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/dustinjackson/html-webpack-inline-source-plugin/issues"}, "homepage": "https://github.com/dustinjackson/html-webpack-inline-source-plugin", "devDependencies": {"cheerio": "^0.22.0", "css-loader": "^0.25.0", "extract-text-webpack-plugin": "^1.0.1", "html-webpack-plugin": "^2.16.0", "jasmine": "^2.4.1", "rimraf": "^2.5.2", "semistandard": "^7.0.5", "style-loader": "^0.13.1", "webpack": "^1.13.0"}, "dependencies": {"escape-string-regexp": "^1.0.5", "slash": "^1.0.0", "source-map-url": "^0.4.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-html-webpack-inline-source-plugin-0.0.10-89bd5f761e4f16902aa76a44476eb52831c9f7f0-integrity\\node_modules\\html-webpack-inline-source-plugin\\package.json", "readmeFilename": "README.md", "readme": "Inline Source extension for the HTML Webpack Plugin\n========================================\n[![npm version](https://badge.fury.io/js/html-webpack-inline-source-plugin.svg)](https://badge.fury.io/js/html-webpack-inline-source-plugin) [![Build status](https://travis-ci.org/DustinJackson/html-webpack-inline-source-plugin.svg?branch=master)](https://travis-ci.org/DustinJackson/html-webpack-inline-source-plugin) [![js-semistandard-style](https://img.shields.io/badge/code%20style-semistandard-brightgreen.svg?style=flat-square)](https://github.com/Flet/semistandard)\n\nEnhances [html-webpack-plugin](https://github.com/ampedandwired/html-webpack-plugin)\nfunctionality by adding the `{inlineSource: 'regex string'}` option.\n\nThis is an extension plugin for the [webpack](http://webpack.github.io) plugin [html-webpack-plugin](https://github.com/ampedandwired/html-webpack-plugin).  It allows you to embed javascript and css source inline.\n\nInstallation\n------------\nYou must be running webpack on node 4 or higher\n\nInstall the plugin with npm:\n```shell\n$ npm install --save-dev html-webpack-inline-source-plugin\n```\n\nBasic Usage\n-----------\nRequire the plugin in your webpack config:\n\n```javascript\nvar HtmlWebpackInlineSourcePlugin = require('html-webpack-inline-source-plugin');\n```\n\nAdd the plugin to your webpack config as follows:\n\n```javascript\nplugins: [\n  new HtmlWebpackPlugin(),\n  new HtmlWebpackInlineSourcePlugin()\n]  \n```\nThe above configuration will actually do nothing due to the configuration defaults.\n\nWhen you set `inlineSource` to a regular expression the source code for any javascript or css file names that match will be embedded inline in the resulting html document.\n```javascript\nplugins: [\n  new HtmlWebpackPlugin({\n\t\tinlineSource: '.(js|css)$' // embed all javascript and css inline\n\t}),\n  new HtmlWebpackInlineSourcePlugin()\n]  \n```\n\nSourcemaps\n----------\nIf any source files contain a sourceMappingURL directive that isn't a data URI, then the sourcemap URL is corrected to be relative to the domain root (unless it already is) instead of to the original source file.\n\nAll sourcemap comment styles are supported:\n\n* `//# ...`\n* `//@ ...`\n* `/*# ...*/`\n* `/*@ ...*/`\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016 Jan Nicklas\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/html-webpack-inline-source-plugin/-/html-webpack-inline-source-plugin-0.0.10.tgz#89bd5f761e4f16902aa76a44476eb52831c9f7f0", "type": "tarball", "reference": "https://registry.yarnpkg.com/html-webpack-inline-source-plugin/-/html-webpack-inline-source-plugin-0.0.10.tgz", "hash": "89bd5f761e4f16902aa76a44476eb52831c9f7f0", "integrity": "sha512-0ZNU57u7283vrXSF5a4VDnVOMWiSwypKIp1z/XfXWoVHLA1r3Xmyxx5+Lz+mnthz/UvxL1OAf41w5UIF68Jngw==", "registry": "npm", "packageName": "html-webpack-inline-source-plugin", "cacheIntegrity": "sha512-0ZNU57u7283vrXSF5a4VDnVOMWiSwypKIp1z/XfXWoVHLA1r3Xmyxx5+Lz+mnthz/UvxL1OAf41w5UIF68Jngw== sha1-ib1fdh5PFpAqp2pER261KDHJ9/A="}, "registry": "npm", "hash": "89bd5f761e4f16902aa76a44476eb52831c9f7f0"}