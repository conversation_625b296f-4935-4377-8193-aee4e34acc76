{"manifest": {"name": "braces", "description": "Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.", "version": "3.0.2", "homepage": "https://github.com/micromatch/braces", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "url": "https://github.com/eush77"}, {"name": "hemanth.hm", "url": "http://h3manth.com"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "https://github.com/micromatch/braces.git"}, "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "license": "MIT", "files": ["index.js", "lib"], "main": "index.js", "engines": {"node": ">=8"}, "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "dependencies": {"fill-range": "^7.0.1"}, "devDependencies": {"ansi-colors": "^3.2.4", "bash-path": "^2.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.1.1"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "lint": {"reflinks": true}, "plugins": ["gulp-format-md"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-braces-3.0.2-3454e1a462ee8d599e236df336cd9ea4f8afe107-integrity\\node_modules\\braces\\package.json", "readmeFilename": "README.md", "readme": "# braces [![Donate](https://img.shields.io/badge/Donate-PayPal-green.svg)](https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=W8YFZ425KND68) [![NPM version](https://img.shields.io/npm/v/braces.svg?style=flat)](https://www.npmjs.com/package/braces) [![NPM monthly downloads](https://img.shields.io/npm/dm/braces.svg?style=flat)](https://npmjs.org/package/braces) [![NPM total downloads](https://img.shields.io/npm/dt/braces.svg?style=flat)](https://npmjs.org/package/braces) [![Linux Build Status](https://img.shields.io/travis/micromatch/braces.svg?style=flat&label=Travis)](https://travis-ci.org/micromatch/braces)\n\n> Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save braces\n```\n\n## v3.0.0 Released!!\n\nSee the [changelog](CHANGELOG.md) for details.\n\n## Why use braces?\n\nBrace patterns make globs more powerful by adding the ability to match specific ranges and sequences of characters.\n\n* **Accurate** - complete support for the [Bash 4.3 Brace Expansion](www.gnu.org/software/bash/) specification (passes all of the Bash braces tests)\n* **[fast and performant](#benchmarks)** - Starts fast, runs fast and [scales well](#performance) as patterns increase in complexity.\n* **Organized code base** - The parser and compiler are easy to maintain and update when edge cases crop up.\n* **Well-tested** - Thousands of test assertions, and passes all of the Bash, minimatch, and [brace-expansion](https://github.com/juliangruber/brace-expansion) unit tests (as of the date this was written).\n* **Safer** - You shouldn't have to worry about users defining aggressive or malicious brace patterns that can break your application. Braces takes measures to prevent malicious regex that can be used for DDoS attacks (see [catastrophic backtracking](https://www.regular-expressions.info/catastrophic.html)).\n* [Supports lists](#lists) - (aka \"sets\") `a/{b,c}/d` => `['a/b/d', 'a/c/d']`\n* [Supports sequences](#sequences) - (aka \"ranges\") `{01..03}` => `['01', '02', '03']`\n* [Supports steps](#steps) - (aka \"increments\") `{2..10..2}` => `['2', '4', '6', '8', '10']`\n* [Supports escaping](#escaping) - To prevent evaluation of special characters.\n\n## Usage\n\nThe main export is a function that takes one or more brace `patterns` and `options`.\n\n```js\nconst braces = require('braces');\n// braces(patterns[, options]);\n\nconsole.log(braces(['{01..05}', '{a..e}']));\n//=> ['(0[1-5])', '([a-e])']\n\nconsole.log(braces(['{01..05}', '{a..e}'], { expand: true }));\n//=> ['01', '02', '03', '04', '05', 'a', 'b', 'c', 'd', 'e']\n```\n\n### Brace Expansion vs. Compilation\n\nBy default, brace patterns are compiled into strings that are optimized for creating regular expressions and matching.\n\n**Compiled**\n\n```js\nconsole.log(braces('a/{x,y,z}/b')); \n//=> ['a/(x|y|z)/b']\nconsole.log(braces(['a/{01..20}/b', 'a/{1..5}/b'])); \n//=> [ 'a/(0[1-9]|1[0-9]|20)/b', 'a/([1-5])/b' ]\n```\n\n**Expanded**\n\nEnable brace expansion by setting the `expand` option to true, or by using [braces.expand()](#expand) (returns an array similar to what you'd expect from Bash, or `echo {1..5}`, or [minimatch](https://github.com/isaacs/minimatch)):\n\n```js\nconsole.log(braces('a/{x,y,z}/b', { expand: true }));\n//=> ['a/x/b', 'a/y/b', 'a/z/b']\n\nconsole.log(braces.expand('{01..10}'));\n//=> ['01','02','03','04','05','06','07','08','09','10']\n```\n\n### Lists\n\nExpand lists (like Bash \"sets\"):\n\n```js\nconsole.log(braces('a/{foo,bar,baz}/*.js'));\n//=> ['a/(foo|bar|baz)/*.js']\n\nconsole.log(braces.expand('a/{foo,bar,baz}/*.js'));\n//=> ['a/foo/*.js', 'a/bar/*.js', 'a/baz/*.js']\n```\n\n### Sequences\n\nExpand ranges of characters (like Bash \"sequences\"):\n\n```js\nconsole.log(braces.expand('{1..3}'));                // ['1', '2', '3']\nconsole.log(braces.expand('a/{1..3}/b'));            // ['a/1/b', 'a/2/b', 'a/3/b']\nconsole.log(braces('{a..c}', { expand: true }));     // ['a', 'b', 'c']\nconsole.log(braces('foo/{a..c}', { expand: true })); // ['foo/a', 'foo/b', 'foo/c']\n\n// supports zero-padded ranges\nconsole.log(braces('a/{01..03}/b'));   //=> ['a/(0[1-3])/b']\nconsole.log(braces('a/{001..300}/b')); //=> ['a/(0{2}[1-9]|0[1-9][0-9]|[12][0-9]{2}|300)/b']\n```\n\nSee [fill-range](https://github.com/jonschlinkert/fill-range) for all available range-expansion options.\n\n### Steppped ranges\n\nSteps, or increments, may be used with ranges:\n\n```js\nconsole.log(braces.expand('{2..10..2}'));\n//=> ['2', '4', '6', '8', '10']\n\nconsole.log(braces('{2..10..2}'));\n//=> ['(2|4|6|8|10)']\n```\n\nWhen the [.optimize](#optimize) method is used, or [options.optimize](#optionsoptimize) is set to true, sequences are passed to [to-regex-range](https://github.com/jonschlinkert/to-regex-range) for expansion.\n\n### Nesting\n\nBrace patterns may be nested. The results of each expanded string are not sorted, and left to right order is preserved.\n\n**\"Expanded\" braces**\n\n```js\nconsole.log(braces.expand('a{b,c,/{x,y}}/e'));\n//=> ['ab/e', 'ac/e', 'a/x/e', 'a/y/e']\n\nconsole.log(braces.expand('a/{x,{1..5},y}/c'));\n//=> ['a/x/c', 'a/1/c', 'a/2/c', 'a/3/c', 'a/4/c', 'a/5/c', 'a/y/c']\n```\n\n**\"Optimized\" braces**\n\n```js\nconsole.log(braces('a{b,c,/{x,y}}/e'));\n//=> ['a(b|c|/(x|y))/e']\n\nconsole.log(braces('a/{x,{1..5},y}/c'));\n//=> ['a/(x|([1-5])|y)/c']\n```\n\n### Escaping\n\n**Escaping braces**\n\nA brace pattern will not be expanded or evaluted if _either the opening or closing brace is escaped_:\n\n```js\nconsole.log(braces.expand('a\\\\{d,c,b}e'));\n//=> ['a{d,c,b}e']\n\nconsole.log(braces.expand('a{d,c,b\\\\}e'));\n//=> ['a{d,c,b}e']\n```\n\n**Escaping commas**\n\nCommas inside braces may also be escaped:\n\n```js\nconsole.log(braces.expand('a{b\\\\,c}d'));\n//=> ['a{b,c}d']\n\nconsole.log(braces.expand('a{d\\\\,c,b}e'));\n//=> ['ad,ce', 'abe']\n```\n\n**Single items**\n\nFollowing bash conventions, a brace pattern is also not expanded when it contains a single character:\n\n```js\nconsole.log(braces.expand('a{b}c'));\n//=> ['a{b}c']\n```\n\n## Options\n\n### options.maxLength\n\n**Type**: `Number`\n\n**Default**: `65,536`\n\n**Description**: Limit the length of the input string. Useful when the input string is generated or your application allows users to pass a string, et cetera.\n\n```js\nconsole.log(braces('a/{b,c}/d', { maxLength: 3 }));  //=> throws an error\n```\n\n### options.expand\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n**Description**: Generate an \"expanded\" brace pattern (alternatively you can use the `braces.expand()` method, which does the same thing).\n\n```js\nconsole.log(braces('a/{b,c}/d', { expand: true }));\n//=> [ 'a/b/d', 'a/c/d' ]\n```\n\n### options.nodupes\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n**Description**: Remove duplicates from the returned array.\n\n### options.rangeLimit\n\n**Type**: `Number`\n\n**Default**: `1000`\n\n**Description**: To prevent malicious patterns from being passed by users, an error is thrown when `braces.expand()` is used or `options.expand` is true and the generated range will exceed the `rangeLimit`.\n\nYou can customize `options.rangeLimit` or set it to `Inifinity` to disable this altogether.\n\n**Examples**\n\n```js\n// pattern exceeds the \"rangeLimit\", so it's optimized automatically\nconsole.log(braces.expand('{1..1000}'));\n//=> ['([1-9]|[1-9][0-9]{1,2}|1000)']\n\n// pattern does not exceed \"rangeLimit\", so it's NOT optimized\nconsole.log(braces.expand('{1..100}'));\n//=> ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '80', '81', '82', '83', '84', '85', '86', '87', '88', '89', '90', '91', '92', '93', '94', '95', '96', '97', '98', '99', '100']\n```\n\n### options.transform\n\n**Type**: `Function`\n\n**Default**: `undefined`\n\n**Description**: Customize range expansion.\n\n**Example: Transforming non-numeric values**\n\n```js\nconst alpha = braces.expand('x/{a..e}/y', {\n  transform(value, index) {\n    // When non-numeric values are passed, \"value\" is a character code.\n    return 'foo/' + String.fromCharCode(value) + '-' + index;\n  }\n});\nconsole.log(alpha);\n//=> [ 'x/foo/a-0/y', 'x/foo/b-1/y', 'x/foo/c-2/y', 'x/foo/d-3/y', 'x/foo/e-4/y' ]\n```\n\n**Example: Transforming numeric values**\n\n```js\nconst numeric = braces.expand('{1..5}', {\n  transform(value) {\n    // when numeric values are passed, \"value\" is a number\n    return 'foo/' + value * 2;\n  }\n});\nconsole.log(numeric); \n//=> [ 'foo/2', 'foo/4', 'foo/6', 'foo/8', 'foo/10' ]\n```\n\n### options.quantifiers\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n**Description**: In regular expressions, quanitifiers can be used to specify how many times a token can be repeated. For example, `a{1,3}` will match the letter `a` one to three times.\n\nUnfortunately, regex quantifiers happen to share the same syntax as [Bash lists](#lists)\n\nThe `quantifiers` option tells braces to detect when [regex quantifiers](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp#quantifiers) are defined in the given pattern, and not to try to expand them as lists.\n\n**Examples**\n\n```js\nconst braces = require('braces');\nconsole.log(braces('a/b{1,3}/{x,y,z}'));\n//=> [ 'a/b(1|3)/(x|y|z)' ]\nconsole.log(braces('a/b{1,3}/{x,y,z}', {quantifiers: true}));\n//=> [ 'a/b{1,3}/(x|y|z)' ]\nconsole.log(braces('a/b{1,3}/{x,y,z}', {quantifiers: true, expand: true}));\n//=> [ 'a/b{1,3}/x', 'a/b{1,3}/y', 'a/b{1,3}/z' ]\n```\n\n### options.unescape\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n**Description**: Strip backslashes that were used for escaping from the result.\n\n## What is \"brace expansion\"?\n\nBrace expansion is a type of parameter expansion that was made popular by unix shells for generating lists of strings, as well as regex-like matching when used alongside wildcards (globs).\n\nIn addition to \"expansion\", braces are also used for matching. In other words:\n\n* [brace expansion](#brace-expansion) is for generating new lists\n* [brace matching](#brace-matching) is for filtering existing lists\n\n<details>\n<summary><strong>More about brace expansion</strong> (click to expand)</summary>\n\nThere are two main types of brace expansion:\n\n1. **lists**: which are defined using comma-separated values inside curly braces: `{a,b,c}`\n2. **sequences**: which are defined using a starting value and an ending value, separated by two dots: `a{1..3}b`. Optionally, a third argument may be passed to define a \"step\" or increment to use: `a{1..100..10}b`. These are also sometimes referred to as \"ranges\".\n\nHere are some example brace patterns to illustrate how they work:\n\n**Sets**\n\n```\n{a,b,c}       => a b c\n{a,b,c}{1,2}  => a1 a2 b1 b2 c1 c2\n```\n\n**Sequences**\n\n```\n{1..9}        => 1 2 3 4 5 6 7 8 9\n{4..-4}       => 4 3 2 1 0 -1 -2 -3 -4\n{1..20..3}    => 1 4 7 10 13 16 19\n{a..j}        => a b c d e f g h i j\n{j..a}        => j i h g f e d c b a\n{a..z..3}     => a d g j m p s v y\n```\n\n**Combination**\n\nSets and sequences can be mixed together or used along with any other strings.\n\n```\n{a,b,c}{1..3}   => a1 a2 a3 b1 b2 b3 c1 c2 c3\nfoo/{a,b,c}/bar => foo/a/bar foo/b/bar foo/c/bar\n```\n\nThe fact that braces can be \"expanded\" from relatively simple patterns makes them ideal for quickly generating test fixtures, file paths, and similar use cases.\n\n## Brace matching\n\nIn addition to _expansion_, brace patterns are also useful for performing regular-expression-like matching.\n\nFor example, the pattern `foo/{1..3}/bar` would match any of following strings:\n\n```\nfoo/1/bar\nfoo/2/bar\nfoo/3/bar\n```\n\nBut not:\n\n```\nbaz/1/qux\nbaz/2/qux\nbaz/3/qux\n```\n\nBraces can also be combined with [glob patterns](https://github.com/jonschlinkert/micromatch) to perform more advanced wildcard matching. For example, the pattern `*/{1..3}/*` would match any of following strings:\n\n```\nfoo/1/bar\nfoo/2/bar\nfoo/3/bar\nbaz/1/qux\nbaz/2/qux\nbaz/3/qux\n```\n\n## Brace matching pitfalls\n\nAlthough brace patterns offer a user-friendly way of matching ranges or sets of strings, there are also some major disadvantages and potential risks you should be aware of.\n\n### tldr\n\n**\"brace bombs\"**\n\n* brace expansion can eat up a huge amount of processing resources\n* as brace patterns increase _linearly in size_, the system resources required to expand the pattern increase exponentially\n* users can accidentally (or intentially) exhaust your system's resources resulting in the equivalent of a DoS attack (bonus: no programming knowledge is required!)\n\nFor a more detailed explanation with examples, see the [geometric complexity](#geometric-complexity) section.\n\n### The solution\n\nJump to the [performance section](#performance) to see how Braces solves this problem in comparison to other libraries.\n\n### Geometric complexity\n\nAt minimum, brace patterns with sets limited to two elements have quadradic or `O(n^2)` complexity. But the complexity of the algorithm increases exponentially as the number of sets, _and elements per set_, increases, which is `O(n^c)`.\n\nFor example, the following sets demonstrate quadratic (`O(n^2)`) complexity:\n\n```\n{1,2}{3,4}      => (2X2)    => 13 14 23 24\n{1,2}{3,4}{5,6} => (2X2X2)  => 135 136 145 146 235 236 245 246\n```\n\nBut add an element to a set, and we get a n-fold Cartesian product with `O(n^c)` complexity:\n\n```\n{1,2,3}{4,5,6}{7,8,9} => (3X3X3) => 147 148 149 157 158 159 167 168 169 247 248 \n                                    249 257 258 259 267 268 269 347 348 349 357 \n                                    358 359 367 368 369\n```\n\nNow, imagine how this complexity grows given that each element is a n-tuple:\n\n```\n{1..100}{1..100}         => (100X100)     => 10,000 elements (38.4 kB)\n{1..100}{1..100}{1..100} => (100X100X100) => 1,000,000 elements (5.76 MB)\n```\n\nAlthough these examples are clearly contrived, they demonstrate how brace patterns can quickly grow out of control.\n\n**More information**\n\nInterested in learning more about brace expansion?\n\n* [linuxjournal/bash-brace-expansion](http://www.linuxjournal.com/content/bash-brace-expansion)\n* [rosettacode/Brace_expansion](https://rosettacode.org/wiki/Brace_expansion)\n* [cartesian product](https://en.wikipedia.org/wiki/Cartesian_product)\n\n</details>\n\n## Performance\n\nBraces is not only screaming fast, it's also more accurate the other brace expansion libraries.\n\n### Better algorithms\n\nFortunately there is a solution to the [\"brace bomb\" problem](#brace-matching-pitfalls): _don't expand brace patterns into an array when they're used for matching_.\n\nInstead, convert the pattern into an optimized regular expression. This is easier said than done, and braces is the only library that does this currently.\n\n**The proof is in the numbers**\n\nMinimatch gets exponentially slower as patterns increase in complexity, braces does not. The following results were generated using `braces()` and `minimatch.braceExpand()`, respectively.\n\n| **Pattern**                 | **braces**         | **[minimatch][]**            |\n| ---                         | ---                | ---                          |\n| `{1..9007199254740991}`[^1] | `298 B` (5ms 459μs)|  N/A (freezes)               |\n| `{1..1000000000000000}`     | `41 B` (1ms 15μs)  |  N/A (freezes)               |\n| `{1..100000000000000}`      | `40 B` (890μs)     |  N/A (freezes)               |\n| `{1..10000000000000}`       | `39 B` (2ms 49μs)  |  N/A (freezes)               |\n| `{1..1000000000000}`        | `38 B` (608μs)     |  N/A (freezes)               |\n| `{1..100000000000}`         | `37 B` (397μs)     |  N/A (freezes)               |\n| `{1..10000000000}`          | `35 B` (983μs)     |  N/A (freezes)               |\n| `{1..1000000000}`           | `34 B` (798μs)     |  N/A (freezes)               |\n| `{1..100000000}`            | `33 B` (733μs)     |  N/A (freezes)               |\n| `{1..10000000}`             | `32 B` (5ms 632μs) | `78.89 MB` (16s 388ms 569μs) |\n| `{1..1000000}`              | `31 B` (1ms 381μs) | `6.89 MB` (1s 496ms 887μs)   |\n| `{1..100000}`               | `30 B` (950μs)     | `588.89 kB` (146ms 921μs)    |\n| `{1..10000}`                | `29 B` (1ms 114μs) | `48.89 kB` (14ms 187μs)      |\n| `{1..1000}`                 | `28 B` (760μs)     | `3.89 kB` (1ms 453μs)        |\n| `{1..100}`                  | `22 B` (345μs)     | `291 B` (196μs)              |\n| `{1..10}`                   | `10 B` (533μs)     | `20 B` (37μs)                |\n| `{1..3}`                    | `7 B` (190μs)      | `5 B` (27μs)                 |\n\n### Faster algorithms\n\nWhen you need expansion, braces is still much faster.\n\n_(the following results were generated using `braces.expand()` and `minimatch.braceExpand()`, respectively)_\n\n| **Pattern**     | **braces**                  | **[minimatch][]**            |\n| ---             | ---                         | ---                          |\n| `{1..10000000}` | `78.89 MB` (2s 698ms 642μs) | `78.89 MB` (18s 601ms 974μs) |\n| `{1..1000000}`  | `6.89 MB` (458ms 576μs)     | `6.89 MB` (1s 491ms 621μs)   |\n| `{1..100000}`   | `588.89 kB` (20ms 728μs)    | `588.89 kB` (156ms 919μs)    |\n| `{1..10000}`    | `48.89 kB` (2ms 202μs)      | `48.89 kB` (13ms 641μs)      |\n| `{1..1000}`     | `3.89 kB` (1ms 796μs)       | `3.89 kB` (1ms 958μs)        |\n| `{1..100}`      | `291 B` (424μs)             | `291 B` (211μs)              |\n| `{1..10}`       | `20 B` (487μs)              | `20 B` (72μs)                |\n| `{1..3}`        | `5 B` (166μs)               | `5 B` (27μs)                 |\n\nIf you'd like to run these comparisons yourself, see [test/support/generate.js](test/support/generate.js).\n\n## Benchmarks\n\n### Running benchmarks\n\nInstall dev dependencies:\n\n```bash\nnpm i -d && npm benchmark\n```\n\n### Latest results\n\nBraces is more accurate, without sacrificing performance.\n\n```bash\n# range (expanded)\n  braces x 29,040 ops/sec ±3.69% (91 runs sampled))\n  minimatch x 4,735 ops/sec ±1.28% (90 runs sampled)\n\n# range (optimized for regex)\n  braces x 382,878 ops/sec ±0.56% (94 runs sampled)\n  minimatch x 1,040 ops/sec ±0.44% (93 runs sampled)\n\n# nested ranges (expanded)\n  braces x 19,744 ops/sec ±2.27% (92 runs sampled))\n  minimatch x 4,579 ops/sec ±0.50% (93 runs sampled)\n\n# nested ranges (optimized for regex)\n  braces x 246,019 ops/sec ±2.02% (93 runs sampled)\n  minimatch x 1,028 ops/sec ±0.39% (94 runs sampled)\n\n# set (expanded) \n  braces x 138,641 ops/sec ±0.53% (95 runs sampled)\n  minimatch x 219,582 ops/sec ±0.98% (94 runs sampled)\n\n# set (optimized for regex)\n  braces x 388,408 ops/sec ±0.41% (95 runs sampled)\n  minimatch x 44,724 ops/sec ±0.91% (89 runs sampled)\n\n# nested sets (expanded)\n  braces x 84,966 ops/sec ±0.48% (94 runs sampled)\n  minimatch x 140,720 ops/sec ±0.37% (95 runs sampled)\n\n# nested sets (optimized for regex)\n  braces x 263,340 ops/sec ±2.06% (92 runs sampled)\n  minimatch x 28,714 ops/sec ±0.40% (90 runs sampled)\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Contributors\n\n| **Commits** | **Contributor** |  \n| --- | --- |  \n| 197 | [jonschlinkert](https://github.com/jonschlinkert) |  \n| 4   | [doowb](https://github.com/doowb) |  \n| 1   | [es128](https://github.com/es128) |  \n| 1   | [eush77](https://github.com/eush77) |  \n| 1   | [hemanth](https://github.com/hemanth) |  \n| 1   | [wtgtybhertgeghgtwtg](https://github.com/wtgtybhertgeghgtwtg) |  \n\n### Author\n\n**Jon Schlinkert**\n\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n\n### License\n\nCopyright © 2019, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.8.0, on April 08, 2019._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2018, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/braces/-/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107", "type": "tarball", "reference": "https://registry.yarnpkg.com/braces/-/braces-3.0.2.tgz", "hash": "3454e1a462ee8d599e236df336cd9ea4f8afe107", "integrity": "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==", "registry": "npm", "packageName": "braces", "cacheIntegrity": "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A== sha1-NFThpGLujVmeI23zNs2epPiv4Qc="}, "registry": "npm", "hash": "3454e1a462ee8d599e236df336cd9ea4f8afe107"}