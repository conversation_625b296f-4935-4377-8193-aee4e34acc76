# has-value [![NPM version](https://img.shields.io/npm/v/has-value.svg?style=flat)](https://www.npmjs.com/package/has-value) [![NPM monthly downloads](https://img.shields.io/npm/dm/has-value.svg?style=flat)](https://npmjs.org/package/has-value) [![NPM total downloads](https://img.shields.io/npm/dt/has-value.svg?style=flat)](https://npmjs.org/package/has-value) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/has-value.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/has-value)

> Returns true if a value exists, false if empty. Works with deeply nested values using object paths.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save has-value
```

**Works for:**

* booleans
* functions
* numbers
* strings
* nulls
* object
* arrays

## Usage

Works with property values (supports object-path notation, like `foo.bar`) or a single value:

```js
var hasValue = require('has-value');

hasValue('foo');
hasValue({foo: 'bar'}, 'foo');
hasValue({a: {b: {c: 'foo'}}}, 'a.b.c');
//=> true

hasValue('');
hasValue({foo: ''}, 'foo');
//=> false

hasValue(0);
hasValue(1);
hasValue({foo: 0}, 'foo');
hasValue({foo: 1}, 'foo');
hasValue({foo: null}, 'foo');
hasValue({foo: {bar: 'a'}}}, 'foo');
hasValue({foo: {bar: 'a'}}}, 'foo.bar');
//=> true

hasValue({foo: {}}}, 'foo');
hasValue({foo: {bar: {}}}}, 'foo.bar');
hasValue({foo: undefined}, 'foo');
//=> false

hasValue([]);
hasValue([[]]);
hasValue([[], []]);
hasValue([undefined]);
hasValue({foo: []}, 'foo');
//=> false

hasValue([0]);
hasValue([null]);
hasValue(['foo']);
hasValue({foo: ['a']}, 'foo');
//=> true

hasValue(function() {})
hasValue(function(foo) {})
hasValue({foo: function(foo) {}}, 'foo'); 
hasValue({foo: function() {}}, 'foo');
//=> true

hasValue(true);
hasValue(false);
hasValue({foo: true}, 'foo');
hasValue({foo: false}, 'foo');
//=> true
```

## isEmpty

To do the opposite and test for empty values, do:

```js
function isEmpty(o) {
  return !hasValue.apply(hasValue, arguments);
}
```

## Release history

### v1.0.0

* `zero` always returns true
* `array` now recurses, so that an array of empty arrays will return `false`
* `null` now returns true

## About

### Related projects

* [define-property](https://www.npmjs.com/package/define-property): Define a non-enumerable property on an object. | [homepage](https://github.com/jonschlinkert/define-property "Define a non-enumerable property on an object.")
* [get-value](https://www.npmjs.com/package/get-value): Use property paths (`a.b.c`) to get a nested value from an object. | [homepage](https://github.com/jonschlinkert/get-value "Use property paths (`a.b.c`) to get a nested value from an object.")
* [set-value](https://www.npmjs.com/package/set-value): Create nested values and any intermediaries using dot notation (`'a.b.c'`) paths. | [homepage](https://github.com/jonschlinkert/set-value "Create nested values and any intermediaries using dot notation (`'a.b.c'`) paths.")
* [unset-value](https://www.npmjs.com/package/unset-value): Delete nested properties from an object using dot notation. | [homepage](https://github.com/jonschlinkert/unset-value "Delete nested properties from an object using dot notation.")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Contributors

| **Commits** | **Contributor** | 
| --- | --- |
| 17 | [jonschlinkert](https://github.com/jonschlinkert) |
| 2 | [rmharrison](https://github.com/rmharrison) |

### Building docs

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

### Running tests

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on May 19, 2017._