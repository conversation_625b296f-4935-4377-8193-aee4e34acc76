local QBCore = exports['qb-core']:GetCoreObject()

-- Globální proměnné pro hovory
local ActiveCalls = {}
local CallQueue = {}

-- Callback pro získání historie hovorů
QBCore.Functions.CreateCallback('qb-smartphone2:server:getCallHistory', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb({}) return end
    
    local phoneNumber = Player.PlayerData.charinfo.phone
    
    MySQL.Async.fetchAll([[
        SELECT * FROM phone_calls 
        WHERE caller = ? OR receiver = ? 
        ORDER BY created_at DESC 
        LIMIT ?
    ]], {phoneNumber, phoneNumber, Config.MaxCallHistory}, function(result)
        cb(result or {})
    end)
end)

-- Event pro zahájení hovoru
RegisterNetEvent('qb-smartphone2:server:startCall', function(targetNumber)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local callerNumber = Player.PlayerData.charinfo.phone
    
    -- Kontrola, zda volající už není v hovoru
    if ActiveCalls[src] then
        TriggerClientEvent('QBCore:Notify', src, 'Už jsi v hovoru!', 'error')
        return
    end
    
    -- Kontrola, zda cílové číslo existuje
    local TargetPlayer = QBCore.Functions.GetPlayerByPhone(targetNumber)
    if not TargetPlayer then
        TriggerClientEvent('QBCore:Notify', src, 'Číslo neexistuje!', 'error')
        return
    end
    
    local targetSrc = TargetPlayer.PlayerData.source
    
    -- Kontrola, zda cíl není v hovoru
    if ActiveCalls[targetSrc] then
        TriggerClientEvent('QBCore:Notify', src, 'Číslo je obsazené!', 'error')
        -- Zaznamenej zmeškaný hovor
        RecordCall(callerNumber, targetNumber, 0, 'missed')
        return
    end
    
    -- Kontrola, zda není volající blokovaný
    QBCore.Functions.TriggerCallback('qb-smartphone2:server:isNumberBlocked', function(isBlocked)
        if isBlocked then
            TriggerClientEvent('QBCore:Notify', src, 'Hovor nebyl spojen', 'error')
            return
        end
        
        -- Vytvoření hovoru
        local callId = GenerateCallId()
        ActiveCalls[src] = {
            id = callId,
            caller = src,
            receiver = targetSrc,
            callerNumber = callerNumber,
            receiverNumber = targetNumber,
            startTime = os.time(),
            status = 'ringing'
        }
        
        ActiveCalls[targetSrc] = ActiveCalls[src]
        
        -- Odeslání příchozího hovoru cíli
        local callerName = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname
        
        -- Zkus najít kontakt pro zobrazení jména
        QBCore.Functions.TriggerCallback('qb-smartphone2:server:getContactByNumber', function(contact)
            local displayName = contact and contact.name or callerName
            
            TriggerClientEvent('qb-smartphone2:client:incomingCall', targetSrc, {
                callId = callId,
                callerNumber = callerNumber,
                callerName = displayName
            })
            
            -- Odeslání stavu volání volajícímu
            TriggerClientEvent('qb-smartphone2:client:callStatus', src, {
                callId = callId,
                status = 'ringing',
                targetNumber = targetNumber,
                targetName = TargetPlayer.PlayerData.charinfo.firstname .. ' ' .. TargetPlayer.PlayerData.charinfo.lastname
            })
            
            -- Timeout pro hovor (30 sekund)
            SetTimeout(30000, function()
                if ActiveCalls[src] and ActiveCalls[src].status == 'ringing' then
                    EndCall(callId, 'timeout')
                end
            end)
            
        end, targetSrc, callerNumber)
        
    end, targetSrc, callerNumber)
end)

-- Event pro přijetí hovoru
RegisterNetEvent('qb-smartphone2:server:acceptCall', function(callId)
    local src = source
    
    if not ActiveCalls[src] or ActiveCalls[src].id ~= callId then
        return
    end
    
    local call = ActiveCalls[src]
    call.status = 'active'
    call.acceptTime = os.time()
    
    -- Oznámení oběma stranám
    TriggerClientEvent('qb-smartphone2:client:callAccepted', call.caller, callId)
    TriggerClientEvent('qb-smartphone2:client:callAccepted', call.receiver, callId)
    
    -- Spuštění voice chatu (pokud je dostupný)
    if GetResourceState('pma-voice') == 'started' then
        exports['pma-voice']:addPlayerToCall(call.caller, callId)
        exports['pma-voice']:addPlayerToCall(call.receiver, callId)
    elseif GetResourceState('tokovoip_script') == 'started' then
        exports.tokovoip_script:addPlayerToRadio(call.caller, callId)
        exports.tokovoip_script:addPlayerToRadio(call.receiver, callId)
    end
end)

-- Event pro odmítnutí hovoru
RegisterNetEvent('qb-smartphone2:server:declineCall', function(callId)
    local src = source
    
    if not ActiveCalls[src] or ActiveCalls[src].id ~= callId then
        return
    end
    
    EndCall(callId, 'declined')
end)

-- Event pro ukončení hovoru
RegisterNetEvent('qb-smartphone2:server:endCall', function(callId)
    local src = source
    
    if not ActiveCalls[src] or ActiveCalls[src].id ~= callId then
        return
    end
    
    EndCall(callId, 'ended')
end)

-- Funkce pro ukončení hovoru
function EndCall(callId, reason)
    local call = nil
    
    -- Najdi hovor podle ID
    for src, activeCall in pairs(ActiveCalls) do
        if activeCall.id == callId then
            call = activeCall
            break
        end
    end
    
    if not call then return end
    
    local duration = 0
    if call.acceptTime then
        duration = os.time() - call.acceptTime
    end
    
    -- Zaznamenej hovor do databáze
    local callType = 'missed'
    if reason == 'ended' and duration > 0 then
        callType = 'outgoing'
    elseif reason == 'declined' then
        callType = 'missed'
    end
    
    RecordCall(call.callerNumber, call.receiverNumber, duration, callType)
    
    -- Oznámení oběma stranám
    TriggerClientEvent('qb-smartphone2:client:callEnded', call.caller, {
        callId = callId,
        reason = reason,
        duration = duration
    })
    
    TriggerClientEvent('qb-smartphone2:client:callEnded', call.receiver, {
        callId = callId,
        reason = reason,
        duration = duration
    })
    
    -- Ukončení voice chatu
    if GetResourceState('pma-voice') == 'started' then
        exports['pma-voice']:removePlayerFromCall(call.caller)
        exports['pma-voice']:removePlayerFromCall(call.receiver)
    elseif GetResourceState('tokovoip_script') == 'started' then
        exports.tokovoip_script:removePlayerFromRadio(call.caller, callId)
        exports.tokovoip_script:removePlayerFromRadio(call.receiver, callId)
    end
    
    -- Vyčištění aktivních hovorů
    ActiveCalls[call.caller] = nil
    ActiveCalls[call.receiver] = nil
end

-- Funkce pro zaznamenání hovoru
function RecordCall(callerNumber, receiverNumber, duration, callType)
    MySQL.Async.execute('INSERT INTO phone_calls (caller, receiver, duration, call_type) VALUES (?, ?, ?, ?)', {
        callerNumber, receiverNumber, duration, callType
    })
    
    -- Zaznamenej také pro příjemce
    local receiverCallType = 'incoming'
    if callType == 'missed' then
        receiverCallType = 'missed'
    end
    
    MySQL.Async.execute('INSERT INTO phone_calls (caller, receiver, duration, call_type) VALUES (?, ?, ?, ?)', {
        receiverNumber, callerNumber, duration, receiverCallType
    })
end

-- Funkce pro generování ID hovoru
function GenerateCallId()
    return 'call_' .. math.random(100000, 999999) .. '_' .. os.time()
end

-- Event pro smazání historie hovorů
RegisterNetEvent('qb-smartphone2:server:clearCallHistory', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local phoneNumber = Player.PlayerData.charinfo.phone
    
    MySQL.Async.execute('DELETE FROM phone_calls WHERE caller = ? OR receiver = ?', {
        phoneNumber, phoneNumber
    }, function(affectedRows)
        if affectedRows > 0 then
            TriggerClientEvent('QBCore:Notify', src, 'Historie hovorů byla vymazána!', 'success')
            TriggerClientEvent('qb-smartphone2:client:callHistoryCleared', src)
        end
    end)
end)

-- Event pro smazání konkrétního hovoru z historie
RegisterNetEvent('qb-smartphone2:server:deleteCallRecord', function(callId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local phoneNumber = Player.PlayerData.charinfo.phone
    
    MySQL.Async.execute('DELETE FROM phone_calls WHERE id = ? AND (caller = ? OR receiver = ?)', {
        callId, phoneNumber, phoneNumber
    }, function(affectedRows)
        if affectedRows > 0 then
            TriggerClientEvent('QBCore:Notify', src, 'Záznam hovoru byl smazán!', 'success')
            TriggerClientEvent('qb-smartphone2:client:callRecordDeleted', src, callId)
        end
    end)
end)

-- Callback pro kontrolu, zda je hráč v hovoru
QBCore.Functions.CreateCallback('qb-smartphone2:server:isInCall', function(source, cb)
    cb(ActiveCalls[source] ~= nil)
end)

-- Cleanup při odpojení hráče
AddEventHandler('playerDropped', function()
    local src = source
    
    if ActiveCalls[src] then
        EndCall(ActiveCalls[src].id, 'disconnected')
    end
end)

-- Export funkce pro jiné scripty
exports('StartCall', function(callerNumber, receiverNumber)
    local CallerPlayer = QBCore.Functions.GetPlayerByPhone(callerNumber)
    local ReceiverPlayer = QBCore.Functions.GetPlayerByPhone(receiverNumber)
    
    if not CallerPlayer or not ReceiverPlayer then return false end
    
    local callerSrc = CallerPlayer.PlayerData.source
    local receiverSrc = ReceiverPlayer.PlayerData.source
    
    if ActiveCalls[callerSrc] or ActiveCalls[receiverSrc] then return false end
    
    TriggerEvent('qb-smartphone2:server:startCall', receiverNumber)
    return true
end)

exports('EndCall', function(playerSource)
    if ActiveCalls[playerSource] then
        EndCall(ActiveCalls[playerSource].id, 'external')
        return true
    end
    return false
end)

exports('IsPlayerInCall', function(playerSource)
    return ActiveCalls[playerSource] ~= nil
end)
