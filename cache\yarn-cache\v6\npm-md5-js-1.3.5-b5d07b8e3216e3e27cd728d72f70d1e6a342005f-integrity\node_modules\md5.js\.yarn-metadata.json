{"manifest": {"name": "md5.js", "version": "1.3.5", "description": "node style md5 on pure JavaScript", "keywords": ["crypto", "md5"], "homepage": "https://github.com/crypto-browserify/md5.js", "bugs": {"url": "https://github.com/crypto-browserify/md5.js/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fanatid"}, "files": ["index.js"], "main": "index.js", "repository": {"type": "git", "url": "https://github.com/crypto-browserify/md5.js.git"}, "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "node test/*.js"}, "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}, "devDependencies": {"hash-test-vectors": "^1.3.2", "standard": "^7.0.0", "tape": "^4.2.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-md5-js-1.3.5-b5d07b8e3216e3e27cd728d72f70d1e6a342005f-integrity\\node_modules\\md5.js\\package.json", "readmeFilename": "README.md", "readme": "# md5.js\n\n[![NPM Package](https://img.shields.io/npm/v/md5.js.svg?style=flat-square)](https://www.npmjs.org/package/md5.js)\n[![Build Status](https://img.shields.io/travis/crypto-browserify/md5.js.svg?branch=master&style=flat-square)](https://travis-ci.org/crypto-browserify/md5.js)\n[![Dependency status](https://img.shields.io/david/crypto-browserify/md5.js.svg?style=flat-square)](https://david-dm.org/crypto-browserify/md5.js#info=dependencies)\n\n[![js-standard-style](https://cdn.rawgit.com/feross/standard/master/badge.svg)](https://github.com/feross/standard)\n\nNode style `md5` on pure JavaScript.\n\nFrom [NIST SP 800-131A][1]: *md5 is no longer acceptable where collision resistance is required such as digital signatures.*\n\n## Example\n\n```js\nvar MD5 = require('md5.js')\n\nconsole.log(new MD5().update('42').digest('hex'))\n// => a1d0c6e83f027327d8461063f4ac58a6\n\nvar md5stream = new MD5()\nmd5stream.end('42')\nconsole.log(md5stream.read().toString('hex'))\n// => a1d0c6e83f027327d8461063f4ac58a6\n```\n\n## LICENSE [MIT](LICENSE)\n\n[1]: http://nvlpubs.nist.gov/nistpubs/SpecialPublications/NIST.SP.800-131Ar1.pdf\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016 Kirill Fomichev\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/md5.js/-/md5.js-1.3.5.tgz#b5d07b8e3216e3e27cd728d72f70d1e6a342005f", "type": "tarball", "reference": "https://registry.yarnpkg.com/md5.js/-/md5.js-1.3.5.tgz", "hash": "b5d07b8e3216e3e27cd728d72f70d1e6a342005f", "integrity": "sha512-xitP+WxNPcTTOgnTJcrhM0xvdPepipPSf3I8EIpGKeFLjt3PlJLIDG3u8EX53ZIubkb+5U2+3rELYpEhHhzdkg==", "registry": "npm", "packageName": "md5.js", "cacheIntegrity": "sha512-xitP+WxNPcTTOgnTJcrhM0xvdPepipPSf3I8EIpGKeFLjt3PlJLIDG3u8EX53ZIubkb+5U2+3rELYpEhHhzdkg== sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8="}, "registry": "npm", "hash": "b5d07b8e3216e3e27cd728d72f70d1e6a342005f"}