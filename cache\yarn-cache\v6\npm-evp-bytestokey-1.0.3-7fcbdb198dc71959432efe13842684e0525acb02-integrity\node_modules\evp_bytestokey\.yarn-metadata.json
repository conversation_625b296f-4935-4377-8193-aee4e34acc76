{"manifest": {"name": "evp_bytestokey", "version": "1.0.3", "description": "The insecure key derivation algorithm from OpenSSL", "keywords": ["crypto", "openssl"], "homepage": "https://github.com/crypto-browserify/EVP_BytesToKey", "bugs": {"url": "https://github.com/crypto-browserify/EVP_BytesToKey/issues"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "files": ["index.js"], "main": "index.js", "repository": {"type": "git", "url": "https://github.com/crypto-browserify/EVP_BytesToKey.git"}, "scripts": {"coverage": "nyc tape test/*.js", "lint": "standard", "test": "npm run lint && npm run unit", "test:prepare": "node-gyp rebuild", "unit": "tape test/*.js"}, "devDependencies": {"bindings": "^1.2.1", "nan": "^2.4.0", "nyc": "^8.1.0", "standard": "^8.0.0", "tape": "^4.6.0"}, "gypfile": false, "dependencies": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-evp-bytestokey-1.0.3-7fcbdb198dc71959432efe13842684e0525acb02-integrity\\node_modules\\evp_bytestokey\\package.json", "readmeFilename": "README.md", "readme": "# EVP\\_BytesToKey\n[![NPM Package](https://img.shields.io/npm/v/evp_bytestokey.svg?style=flat-square)](https://www.npmjs.org/package/evp_bytestokey)\n[![Build Status](https://img.shields.io/travis/crypto-browserify/EVP_BytesToKey.svg?branch=master&style=flat-square)](https://travis-ci.org/crypto-browserify/EVP_BytesToKey)\n[![Dependency status](https://img.shields.io/david/crypto-browserify/EVP_BytesToKey.svg?style=flat-square)](https://david-dm.org/crypto-browserify/EVP_BytesToKey#info=dependencies)\n\n[![js-standard-style](https://cdn.rawgit.com/feross/standard/master/badge.svg)](https://github.com/feross/standard)\n\nThe insecure [key derivation algorithm from OpenSSL.][1]\n\n**WARNING: DO NOT USE, except for compatibility reasons.**\n\nMD5 is insecure.\n\nUse at least `scrypt` or `pbkdf2-hmac-sha256` instead.\n\n\n## API\n`EVP_BytesToKey(password, salt, keyLen, ivLen)`\n\n* `password` - `Buffer`, password used to derive the key data.\n* `salt` - 8 byte `Buffer` or `null`, salt is used as a salt in the derivation.\n* `keyBits` - `number`, key length in **bits**.\n* `ivLen` - `number`, iv length in bytes.\n\n*Returns*: `{ key: Buffer, iv: Buffer }`\n\n\n## Examples\nMD5 with `aes-256-cbc`:\n\n```js\nconst crypto = require('crypto')\nconst EVP_BytesToKey = require('evp_bytestokey')\n\nconst result = EVP_BytesToKey(\n  'my-secret-password',\n  null,\n  32,\n  16\n)\n// =>\n// { key: <Buffer e3 4f 96 f3 86 24 82 7c c2 5d ff 23 18 6f 77 72 54 45 7f 49 d4 be 4b dd 4f 6e 1b cc 92 a4 27 33>,\n//   iv: <Buffer 85 71 9a bf ae f4 1e 74 dd 46 b6 13 79 56 f5 5b> }\n\nconst cipher = crypto.createCipheriv('aes-256-cbc', result.key, result.iv)\n```\n\n## LICENSE [MIT](LICENSE)\n\n[1]: https://wiki.openssl.org/index.php/Manual:EVP_BytesToKey(3)\n[2]: https://nodejs.org/api/crypto.html#crypto_class_hash\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2017 crypto-browserify contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz#7fcbdb198dc71959432efe13842684e0525acb02", "type": "tarball", "reference": "https://registry.yarnpkg.com/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz", "hash": "7fcbdb198dc71959432efe13842684e0525acb02", "integrity": "sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA==", "registry": "npm", "packageName": "evp_bytestokey", "cacheIntegrity": "sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA== sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI="}, "registry": "npm", "hash": "7fcbdb198dc71959432efe13842684e0525acb02"}