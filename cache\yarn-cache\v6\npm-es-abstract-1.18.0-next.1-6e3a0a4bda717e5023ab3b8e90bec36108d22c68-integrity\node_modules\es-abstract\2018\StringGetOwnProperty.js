'use strict';

var GetIntrinsic = require('../GetIntrinsic');

var $TypeError = GetIntrinsic('%TypeError%');

var callBound = require('../helpers/callBound');
var $charAt = callBound('String.prototype.charAt');
var $stringToString = callBound('String.prototype.toString');

var CanonicalNumericIndexString = require('./CanonicalNumericIndexString');
var IsInteger = require('./IsInteger');
var IsPropertyKey = require('./IsPropertyKey');
var Type = require('./Type');

var isNegativeZero = require('is-negative-zero');

// https://ecma-international.org/ecma-262/8.0/#sec-stringgetownproperty

module.exports = function StringGetOwnProperty(S, P) {
	var str;
	if (Type(S) === 'Object') {
		try {
			str = $stringToString(S);
		} catch (e) { /**/ }
	}
	if (Type(str) !== 'String') {
		throw new $TypeError('Assertion failed: `S` must be a boxed string object');
	}
	if (!IsPropertyKey(P)) {
		throw new $TypeError('Assertion failed: IsPropertyKey(P) is not true');
	}
	if (Type(P) !== 'String') {
		return void undefined;
	}
	var index = CanonicalNumericIndexString(P);
	var len = str.length;
	if (typeof index === 'undefined' || !IsInteger(index) || isNegativeZero(index) || index < 0 || len <= index) {
		return void undefined;
	}
	var resultStr = $charAt(S, index);
	return {
		'[[Configurable]]': false,
		'[[Enumerable]]': true,
		'[[Value]]': resultStr,
		'[[Writable]]': false
	};
};
