{"manifest": {"name": "readdirp", "description": "Recursive version of fs.readdir with streaming API.", "version": "3.5.0", "homepage": "https://github.com/paulmillr/readdirp", "repository": {"type": "git", "url": "git://github.com/paulmillr/readdirp.git"}, "license": "MIT", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "thlorenz.com"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "thlorenz.com"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "main": "index.js", "engines": {"node": ">=8.10.0"}, "files": ["index.js", "index.d.ts"], "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "scripts": {"dtslint": "dtslint", "nyc": "nyc", "mocha": "mocha --exit", "lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && nyc npm run mocha"}, "dependencies": {"picomatch": "^2.2.1"}, "devDependencies": {"@types/node": "^14", "chai": "^4.2", "chai-subset": "^1.6", "dtslint": "^3.3.0", "eslint": "^7.0.0", "mocha": "^7.1.1", "nyc": "^15.0.0", "rimraf": "^3.0.0", "typescript": "^4.0.3"}, "nyc": {"reporter": ["html", "text"]}, "eslintConfig": {"root": true, "extends": "eslint:recommended", "parserOptions": {"ecmaVersion": 9, "sourceType": "script"}, "env": {"node": true, "es6": true}, "rules": {"array-callback-return": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-else-return": ["error", {"allowElseIf": false}], "no-lonely-if": "error", "no-var": "error", "object-shorthand": "error", "prefer-arrow-callback": ["error", {"allowNamedFunctions": true}], "prefer-const": ["error", {"ignoreReadBeforeAssign": true}], "prefer-destructuring": ["error", {"object": true, "array": false}], "prefer-spread": "error", "prefer-template": "error", "radix": "error", "semi": "error", "strict": "error", "quotes": ["error", "single"]}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-readdirp-3.5.0-9ba74c019b15d365278d2e91bb8c48d7b4d42c9e-integrity\\node_modules\\readdirp\\package.json", "readmeFilename": "README.md", "readme": "# readdirp [![Weekly downloads](https://img.shields.io/npm/dw/readdirp.svg)](https://github.com/paulmillr/readdirp)\n\nRecursive version of [fs.readdir](https://nodejs.org/api/fs.html#fs_fs_readdir_path_options_callback). Exposes a **stream API** and a **promise API**.\n\n\n```sh\nnpm install readdirp\n```\n\n```javascript\nconst readdirp = require('readdirp');\n\n// Use streams to achieve small RAM & CPU footprint.\n// 1) Streams example with for-await.\nfor await (const entry of readdirp('.')) {\n  const {path} = entry;\n  console.log(`${JSON.stringify({path})}`);\n}\n\n// 2) Streams example, non for-await.\n// Print out all JS files along with their size within the current folder & subfolders.\nreaddirp('.', {fileFilter: '*.js', alwaysStat: true})\n  .on('data', (entry) => {\n    const {path, stats: {size}} = entry;\n    console.log(`${JSON.stringify({path, size})}`);\n  })\n  // Optionally call stream.destroy() in `warn()` in order to abort and cause 'close' to be emitted\n  .on('warn', error => console.error('non-fatal error', error))\n  .on('error', error => console.error('fatal error', error))\n  .on('end', () => console.log('done'));\n\n// 3) Promise example. More RAM and CPU than streams / for-await.\nconst files = await readdirp.promise('.');\nconsole.log(files.map(file => file.path));\n\n// Other options.\nreaddirp('test', {\n  fileFilter: '*.js',\n  directoryFilter: ['!.git', '!*modules']\n  // directoryFilter: (di) => di.basename.length === 9\n  type: 'files_directories',\n  depth: 1\n});\n```\n\nFor more examples, check out `examples` directory.\n\n## API\n\n`const stream = readdirp(root[, options])` — **Stream API**\n\n- Reads given root recursively and returns a `stream` of [entry infos](#entryinfo)\n- Optionally can be used like `for await (const entry of stream)` with node.js 10+ (`asyncIterator`).\n- `on('data', (entry) => {})` [entry info](#entryinfo) for every file / dir.\n- `on('warn', (error) => {})` non-fatal `Error` that prevents a file / dir from being processed. Example: inaccessible to the user.\n- `on('error', (error) => {})` fatal `Error` which also ends the stream. Example: illegal options where passed.\n- `on('end')` — we are done. Called when all entries were found and no more will be emitted.\n- `on('close')` — stream is destroyed via `stream.destroy()`.\n  Could be useful if you want to manually abort even on a non fatal error.\n  At that point the stream is no longer `readable` and no more entries, warning or errors are emitted\n- To learn more about streams, consult the very detailed [nodejs streams documentation](https://nodejs.org/api/stream.html)\n  or the [stream-handbook](https://github.com/substack/stream-handbook)\n\n`const entries = await readdirp.promise(root[, options])` — **Promise API**. Returns a list of [entry infos](#entryinfo).\n\nFirst argument is awalys `root`, path in which to start reading and recursing into subdirectories.\n\n### options\n\n- `fileFilter: [\"*.js\"]`: filter to include or exclude files. A `Function`, Glob string or Array of glob strings.\n    - **Function**: a function that takes an entry info as a parameter and returns true to include or false to exclude the entry\n    - **Glob string**: a string (e.g., `*.js`) which is matched using [picomatch](https://github.com/micromatch/picomatch), so go there for more\n        information. Globstars (`**`) are not supported since specifying a recursive pattern for an already recursive function doesn't make sense. Negated globs (as explained in the minimatch documentation) are allowed, e.g., `!*.txt` matches everything but text files.\n    - **Array of glob strings**: either need to be all inclusive or all exclusive (negated) patterns otherwise an error is thrown.\n        `['*.json', '*.js']` includes all JavaScript and Json files.\n        `['!.git', '!node_modules']` includes all directories except the '.git' and 'node_modules'.\n    - Directories that do not pass a filter will not be recursed into.\n- `directoryFilter: ['!.git']`: filter to include/exclude directories found and to recurse into. Directories that do not pass a filter will not be recursed into.\n- `depth: 5`: depth at which to stop recursing even if more subdirectories are found\n- `type: 'files'`: determines if data events on the stream should be emitted for `'files'` (default), `'directories'`, `'files_directories'`, or `'all'`. Setting to `'all'` will also include entries for other types of file descriptors like character devices, unix sockets and named pipes.\n- `alwaysStat: false`: always return `stats` property for every file. Default is `false`, readdirp will return `Dirent` entries. Setting it to `true` can double readdir execution time - use it only when you need file `size`, `mtime` etc. Cannot be enabled on node <10.10.0.\n- `lstat: false`: include symlink entries in the stream along with files. When `true`, `fs.lstat` would be used instead of `fs.stat`\n\n### `EntryInfo`\n\nHas the following properties:\n\n- `path: 'assets/javascripts/react.js'`: path to the file/directory (relative to given root)\n- `fullPath: '/Users/<USER>/projects/app/assets/javascripts/react.js'`: full path to the file/directory found\n- `basename: 'react.js'`: name of the file/directory\n- `dirent: fs.Dirent`: built-in [dir entry object](https://nodejs.org/api/fs.html#fs_class_fs_dirent) - only with `alwaysStat: false`\n- `stats: fs.Stats`: built in [stat object](https://nodejs.org/api/fs.html#fs_class_fs_stats) - only with `alwaysStat: true`\n\n## Changelog\n\n- 3.5 (Oct 13, 2020) disallows recursive directory-based symlinks.\n  Before, it could have entered infinite loop.\n- 3.4 (Mar 19, 2020) adds support for directory-based symlinks.\n- 3.3 (Dec 6, 2019) stabilizes RAM consumption and enables perf management with `highWaterMark` option. Fixes race conditions related to `for-await` looping.\n- 3.2 (Oct 14, 2019) improves performance by 250% and makes streams implementation more idiomatic.\n- 3.1 (Jul 7, 2019) brings `bigint` support to `stat` output on Windows. This is backwards-incompatible for some cases. Be careful. It you use it incorrectly, you'll see \"TypeError: Cannot mix BigInt and other types, use explicit conversions\".\n- 3.0 brings huge performance improvements and stream backpressure support.\n- Upgrading 2.x to 3.x:\n    - Signature changed from `readdirp(options)` to `readdirp(root, options)`\n    - Replaced callback API with promise API.\n    - Renamed `entryType` option to `type`\n    - Renamed `entryType: 'both'` to `'files_directories'`\n    - `EntryInfo`\n        - Renamed `stat` to `stats`\n            - Emitted only when `alwaysStat: true`\n            - `dirent` is emitted instead of `stats` by default with `alwaysStat: false`\n        - Renamed `name` to `basename`\n        - Removed `parentDir` and `fullParentDir` properties\n- Supported node.js versions:\n    - 3.x: node 8+\n    - 2.x: node 0.6+\n\n## License\n\nCopyright (c) 2012-2019 Thorsten Lorenz, Paul Miller (<https://paulmillr.com>)\n\nMIT License, see [LICENSE](LICENSE) file.\n", "licenseText": "MIT License\n\nCopyright (c) 2012-2019 <PERSON><PERSON>, <PERSON> (https://paulmillr.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND <PERSON><PERSON><PERSON>RINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/readdirp/-/readdirp-3.5.0.tgz#9ba74c019b15d365278d2e91bb8c48d7b4d42c9e", "type": "tarball", "reference": "https://registry.yarnpkg.com/readdirp/-/readdirp-3.5.0.tgz", "hash": "9ba74c019b15d365278d2e91bb8c48d7b4d42c9e", "integrity": "sha512-cMhu7c/8rdhkHXWsY+osBhfSy0JikwpHK/5+imo+LpeasTF8ouErHrlYkwT0++njiyuDvc7OFY5T3ukvZ8qmFQ==", "registry": "npm", "packageName": "readdirp", "cacheIntegrity": "sha512-cMhu7c/8rdhkHXWsY+osBhfSy0JikwpHK/5+imo+LpeasTF8ouErHrlYkwT0++njiyuDvc7OFY5T3ukvZ8qmFQ== sha1-m6dMAZsV02UnjS6Ru4xI17TULJ4="}, "registry": "npm", "hash": "9ba74c019b15d365278d2e91bb8c48d7b4d42c9e"}