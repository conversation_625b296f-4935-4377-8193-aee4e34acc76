function SetWeatherSync(bool)
    if bool then
        Wait(150)
        if GetResourceState('cd_easytime') == 'started' then
            TriggerEvent('cd_easytime:PauseSync', true)
        elseif GetResourceState('qb-weathersync') == 'started' then
            TriggerEvent('qb-weathersync:client:DisableSync')
        elseif GetResourceState('vSyncc') == 'started' then
            TriggerEvent('vSync:toggle', false)
            Wait(100)
            TriggerEvent('vSync:updateWeather', isSettings.timeWeather.weather, false)
        end
        Wait(50)
        ClearOverrideWeather()
        ClearWeatherTypePersist()

        if weatherThreadRunning then return end

        if isSettings.time.status and isSettings.weather.status then
            weatherThreadRunning = true
        end

        Citizen.CreateThread(function()
            while DoesCamExist(camera) do
                if isSettings.time.status and isSettings.weather.status then
                    NetworkOverrideClockTime(isSettings.time.hour, 0, 0)
                    SetOverrideWeather(isSettings.weather.type)
                    SetWeatherTypePersist(isSettings.weather.type)
                    SetWeatherTypeNow(isSettings.weather.type)
                    SetWeatherTypeNowPersist(isSettings.weather.type)
                elseif not isSettings.time.status or not isSettings.weather.status then
                    weatherThreadRunning = false
                    break
                end

                Wait(1000 * 60 * 5)
            end
        end)
    else
        Wait(150)
        if GetResourceState('cd_easytime') == 'started' then
            TriggerEvent('cd_easytime:PauseSync', false)
        elseif GetResourceState('qb-weathersync') == 'started' then
            TriggerEvent('qb-weathersync:client:EnableSync')
        elseif GetResourceState('vSyncc') == 'started' then
            TriggerEvent('vSync:toggle', true)
            Wait(100)
            TriggerServerEvent('vSync:requestSync')
        end

        weatherThreadRunning = false
    end
end
