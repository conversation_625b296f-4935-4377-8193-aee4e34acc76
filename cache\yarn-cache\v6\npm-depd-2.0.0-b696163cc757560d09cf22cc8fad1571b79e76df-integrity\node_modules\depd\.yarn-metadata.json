{"manifest": {"name": "depd", "description": "Deprecate all the things", "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": {"type": "git", "url": "https://github.com/dougwilson/nodejs-depd.git"}, "browser": "lib/browser/index.js", "devDependencies": {"benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint": "5.7.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-markdown": "1.0.0-beta.7", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "istanbul": "0.4.5", "mocha": "5.2.0", "safe-buffer": "5.1.2", "uid-safe": "2.1.5"}, "files": ["lib/", "History.md", "LICENSE", "index.js", "Readme.md"], "engines": {"node": ">= 0.8"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail test/", "test-ci": "istanbul cover --print=none node_modules/mocha/bin/_mocha -- --reporter spec test/ && istanbul report lcovonly text-summary", "test-cov": "istanbul cover --print=none node_modules/mocha/bin/_mocha -- --reporter dot test/ && istanbul report lcov text-summary"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-depd-2.0.0-b696163cc757560d09cf22cc8fad1571b79e76df-integrity\\node_modules\\depd\\package.json", "readmeFilename": "Readme.md", "readme": "# depd\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-image]][node-url]\n[![Linux Build][travis-image]][travis-url]\n[![Windows Build][appveyor-image]][appveyor-url]\n[![Coverage Status][coveralls-image]][coveralls-url]\n\nDeprecate all the things\n\n> With great modules comes great responsibility; mark things deprecated!\n\n## Install\n\nThis module is installed directly using `npm`:\n\n```sh\n$ npm install depd\n```\n\nThis module can also be bundled with systems like\n[Browserify](http://browserify.org/) or [webpack](https://webpack.github.io/),\nthough by default this module will alter it's API to no longer display or\ntrack deprecations.\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar deprecate = require('depd')('my-module')\n```\n\nThis library allows you to display deprecation messages to your users.\nThis library goes above and beyond with deprecation warnings by\nintrospection of the call stack (but only the bits that it is interested\nin).\n\nInstead of just warning on the first invocation of a deprecated\nfunction and never again, this module will warn on the first invocation\nof a deprecated function per unique call site, making it ideal to alert\nusers of all deprecated uses across the code base, rather than just\nwhatever happens to execute first.\n\nThe deprecation warnings from this module also include the file and line\ninformation for the call into the module that the deprecated function was\nin.\n\n**NOTE** this library has a similar interface to the `debug` module, and\nthis module uses the calling file to get the boundary for the call stacks,\nso you should always create a new `deprecate` object in each file and not\nwithin some central file.\n\n### depd(namespace)\n\nCreate a new deprecate function that uses the given namespace name in the\nmessages and will display the call site prior to the stack entering the\nfile this function was called from. It is highly suggested you use the\nname of your module as the namespace.\n\n### deprecate(message)\n\nCall this function from deprecated code to display a deprecation message.\nThis message will appear once per unique caller site. Caller site is the\nfirst call site in the stack in a different file from the caller of this\nfunction.\n\nIf the message is omitted, a message is generated for you based on the site\nof the `deprecate()` call and will display the name of the function called,\nsimilar to the name displayed in a stack trace.\n\n### deprecate.function(fn, message)\n\nCall this function to wrap a given function in a deprecation message on any\ncall to the function. An optional message can be supplied to provide a custom\nmessage.\n\n### deprecate.property(obj, prop, message)\n\nCall this function to wrap a given property on object in a deprecation message\non any accessing or setting of the property. An optional message can be supplied\nto provide a custom message.\n\nThe method must be called on the object where the property belongs (not\ninherited from the prototype).\n\nIf the property is a data descriptor, it will be converted to an accessor\ndescriptor in order to display the deprecation message.\n\n### process.on('deprecation', fn)\n\nThis module will allow easy capturing of deprecation errors by emitting the\nerrors as the type \"deprecation\" on the global `process`. If there are no\nlisteners for this type, the errors are written to STDERR as normal, but if\nthere are any listeners, nothing will be written to STDERR and instead only\nemitted. From there, you can write the errors in a different format or to a\nlogging source.\n\nThe error represents the deprecation and is emitted only once with the same\nrules as writing to STDERR. The error has the following properties:\n\n  - `message` - This is the message given by the library\n  - `name` - This is always `'DeprecationError'`\n  - `namespace` - This is the namespace the deprecation came from\n  - `stack` - This is the stack of the call to the deprecated thing\n\nExample `error.stack` output:\n\n```\nDeprecationError: my-cool-module deprecated oldfunction\n    at Object.<anonymous> ([eval]-wrapper:6:22)\n    at Module._compile (module.js:456:26)\n    at evalScript (node.js:532:25)\n    at startup (node.js:80:7)\n    at node.js:902:3\n```\n\n### process.env.NO_DEPRECATION\n\nAs a user of modules that are deprecated, the environment variable `NO_DEPRECATION`\nis provided as a quick solution to silencing deprecation warnings from being\noutput. The format of this is similar to that of `DEBUG`:\n\n```sh\n$ NO_DEPRECATION=my-module,othermod node app.js\n```\n\nThis will suppress deprecations from being output for \"my-module\" and \"othermod\".\nThe value is a list of comma-separated namespaces. To suppress every warning\nacross all namespaces, use the value `*` for a namespace.\n\nProviding the argument `--no-deprecation` to the `node` executable will suppress\nall deprecations (only available in Node.js 0.8 or higher).\n\n**NOTE** This will not suppress the deperecations given to any \"deprecation\"\nevent listeners, just the output to STDERR.\n\n### process.env.TRACE_DEPRECATION\n\nAs a user of modules that are deprecated, the environment variable `TRACE_DEPRECATION`\nis provided as a solution to getting more detailed location information in deprecation\nwarnings by including the entire stack trace. The format of this is the same as\n`NO_DEPRECATION`:\n\n```sh\n$ TRACE_DEPRECATION=my-module,othermod node app.js\n```\n\nThis will include stack traces for deprecations being output for \"my-module\" and\n\"othermod\". The value is a list of comma-separated namespaces. To trace every\nwarning across all namespaces, use the value `*` for a namespace.\n\nProviding the argument `--trace-deprecation` to the `node` executable will trace\nall deprecations (only available in Node.js 0.8 or higher).\n\n**NOTE** This will not trace the deperecations silenced by `NO_DEPRECATION`.\n\n## Display\n\n![message](files/message.png)\n\nWhen a user calls a function in your library that you mark deprecated, they\nwill see the following written to STDERR (in the given colors, similar colors\nand layout to the `debug` module):\n\n```\nbright cyan    bright yellow\n|              |          reset       cyan\n|              |          |           |\n▼              ▼          ▼           ▼\nmy-cool-module deprecated oldfunction [eval]-wrapper:6:22\n▲              ▲          ▲           ▲\n|              |          |           |\nnamespace      |          |           location of mycoolmod.oldfunction() call\n               |          deprecation message\n               the word \"deprecated\"\n```\n\nIf the user redirects their STDERR to a file or somewhere that does not support\ncolors, they see (similar layout to the `debug` module):\n\n```\nSun, 15 Jun 2014 05:21:37 GMT my-cool-module deprecated oldfunction at [eval]-wrapper:6:22\n▲                             ▲              ▲          ▲              ▲\n|                             |              |          |              |\ntimestamp of message          namespace      |          |             location of mycoolmod.oldfunction() call\n                                             |          deprecation message\n                                             the word \"deprecated\"\n```\n\n## Examples\n\n### Deprecating all calls to a function\n\nThis will display a deprecated message about \"oldfunction\" being deprecated\nfrom \"my-module\" on STDERR.\n\n```js\nvar deprecate = require('depd')('my-cool-module')\n\n// message automatically derived from function name\n// Object.oldfunction\nexports.oldfunction = deprecate.function(function oldfunction () {\n  // all calls to function are deprecated\n})\n\n// specific message\nexports.oldfunction = deprecate.function(function () {\n  // all calls to function are deprecated\n}, 'oldfunction')\n```\n\n### Conditionally deprecating a function call\n\nThis will display a deprecated message about \"weirdfunction\" being deprecated\nfrom \"my-module\" on STDERR when called with less than 2 arguments.\n\n```js\nvar deprecate = require('depd')('my-cool-module')\n\nexports.weirdfunction = function () {\n  if (arguments.length < 2) {\n    // calls with 0 or 1 args are deprecated\n    deprecate('weirdfunction args < 2')\n  }\n}\n```\n\nWhen calling `deprecate` as a function, the warning is counted per call site\nwithin your own module, so you can display different deprecations depending\non different situations and the users will still get all the warnings:\n\n```js\nvar deprecate = require('depd')('my-cool-module')\n\nexports.weirdfunction = function () {\n  if (arguments.length < 2) {\n    // calls with 0 or 1 args are deprecated\n    deprecate('weirdfunction args < 2')\n  } else if (typeof arguments[0] !== 'string') {\n    // calls with non-string first argument are deprecated\n    deprecate('weirdfunction non-string first arg')\n  }\n}\n```\n\n### Deprecating property access\n\nThis will display a deprecated message about \"oldprop\" being deprecated\nfrom \"my-module\" on STDERR when accessed. A deprecation will be displayed\nwhen setting the value and when getting the value.\n\n```js\nvar deprecate = require('depd')('my-cool-module')\n\nexports.oldprop = 'something'\n\n// message automatically derives from property name\ndeprecate.property(exports, 'oldprop')\n\n// explicit message\ndeprecate.property(exports, 'oldprop', 'oldprop >= 0.10')\n```\n\n## License\n\n[MIT](LICENSE)\n\n[appveyor-image]: https://badgen.net/appveyor/ci/dougwilson/nodejs-depd/master?label=windows\n[appveyor-url]: https://ci.appveyor.com/project/dougwilson/nodejs-depd\n[coveralls-image]: https://badgen.net/coveralls/c/github/dougwilson/nodejs-depd/master\n[coveralls-url]: https://coveralls.io/r/dougwilson/nodejs-depd?branch=master\n[node-image]: https://badgen.net/npm/node/depd\n[node-url]: https://nodejs.org/en/download/\n[npm-downloads-image]: https://badgen.net/npm/dm/depd\n[npm-url]: https://npmjs.org/package/depd\n[npm-version-image]: https://badgen.net/npm/v/depd\n[travis-image]: https://badgen.net/travis/dougwilson/nodejs-depd/master?label=linux\n[travis-url]: https://travis-ci.org/dougwilson/nodejs-depd\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2014-2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/depd/-/depd-2.0.0.tgz#b696163cc757560d09cf22cc8fad1571b79e76df", "type": "tarball", "reference": "https://registry.yarnpkg.com/depd/-/depd-2.0.0.tgz", "hash": "b696163cc757560d09cf22cc8fad1571b79e76df", "integrity": "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==", "registry": "npm", "packageName": "depd", "cacheIntegrity": "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw== sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8="}, "registry": "npm", "hash": "b696163cc757560d09cf22cc8fad1571b79e76df"}