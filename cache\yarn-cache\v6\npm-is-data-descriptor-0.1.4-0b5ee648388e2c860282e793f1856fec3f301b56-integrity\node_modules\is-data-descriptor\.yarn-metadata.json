{"manifest": {"name": "is-data-descriptor", "description": "Returns true if a value has the characteristics of a valid JavaScript data descriptor.", "version": "0.1.4", "homepage": "https://github.com/jonschlinkert/is-data-descriptor", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-data-descriptor.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-data-descriptor/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"kind-of": "^3.0.2"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["accessor", "check", "data", "descriptor", "get", "getter", "is", "keys", "object", "properties", "property", "set", "setter", "type", "valid", "value"], "verb": {"related": {"list": ["is-accessor-descriptor", "is-data-descriptor", "is-descriptor", "isobject"]}, "plugins": ["gulp-format-md"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-data-descriptor-0.1.4-0b5ee648388e2c860282e793f1856fec3f301b56-integrity\\node_modules\\is-data-descriptor\\package.json", "readmeFilename": "README.md", "readme": "# is-data-descriptor [![NPM version](https://img.shields.io/npm/v/is-data-descriptor.svg)](https://www.npmjs.com/package/is-data-descriptor) [![Build Status](https://img.shields.io/travis/jonschlinkert/is-data-descriptor.svg)](https://travis-ci.org/jonschlinkert/is-data-descriptor)\n\n> Returns true if a value has the characteristics of a valid JavaScript data descriptor.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm i is-data-descriptor --save\n```\n\n## Usage\n\n```js\nvar isDataDesc = require('is-data-descriptor');\n```\n\n## Examples\n\n`true` when the descriptor has valid properties with valid values.\n\n```js\n// `value` can be anything\nisDataDesc({value: 'foo'})\nisDataDesc({value: function() {}})\nisDataDesc({value: true})\n//=> true\n```\n\n`false` when not an object\n\n```js\nisDataDesc('a')\n//=> false\nisDataDesc(null)\n//=> false\nisDataDesc([])\n//=> false\n```\n\n`false` when the object has invalid properties\n\n```js\nisDataDesc({value: 'foo', bar: 'baz'})\n//=> false\nisDataDesc({value: 'foo', bar: 'baz'})\n//=> false\nisDataDesc({value: 'foo', get: function(){}})\n//=> false\nisDataDesc({get: function(){}, value: 'foo'})\n//=> false\n```\n\n`false` when a value is not the correct type\n\n```js\nisDataDesc({value: 'foo', enumerable: 'foo'})\n//=> false\nisDataDesc({value: 'foo', configurable: 'foo'})\n//=> false\nisDataDesc({value: 'foo', writable: 'foo'})\n//=> false\n```\n\n## Valid properties\n\nThe only valid data descriptor properties are the following:\n\n* `configurable` (required)\n* `enumerable` (required)\n* `value` (optional)\n* `writable` (optional)\n\nTo be a valid data descriptor, either `value` or `writable` must be defined.\n\n**Invalid properties**\n\nA descriptor may have additional _invalid_ properties (an error will **not** be thrown).\n\n```js\nvar foo = {};\n\nObject.defineProperty(foo, 'bar', {\n  enumerable: true,\n  whatever: 'blah', // invalid, but doesn't cause an error\n  get: function() {\n    return 'baz';\n  }\n});\n\nconsole.log(foo.bar);\n//=> 'baz'\n```\n\n## Related projects\n\n* [is-accessor-descriptor](https://www.npmjs.com/package/is-accessor-descriptor): Returns true if a value has the characteristics of a valid JavaScript accessor descriptor. | [homepage](https://github.com/jonschlinkert/is-accessor-descriptor)\n* [is-descriptor](https://www.npmjs.com/package/is-descriptor): Returns true if a value has the characteristics of a valid JavaScript descriptor. Works for… [more](https://www.npmjs.com/package/is-descriptor) | [homepage](https://github.com/jonschlinkert/is-descriptor)\n* [isobject](https://www.npmjs.com/package/isobject): Returns true if the value is an object and not an array or null. | [homepage](https://github.com/jonschlinkert/isobject)\n\n## Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm i -d && npm test\n```\n\n## Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/is-data-descriptor/issues/new).\n\n## Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n## License\n\nCopyright © 2015 [Jon Schlinkert](https://github.com/jonschlinkert)\nReleased under the MIT license.\n\n***\n\n_This file was generated by [verb](https://github.com/verbose/verb) on December 28, 2015._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz#0b5ee648388e2c860282e793f1856fec3f301b56", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz", "hash": "0b5ee648388e2c860282e793f1856fec3f301b56", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "registry": "npm", "packageName": "is-data-descriptor", "cacheIntegrity": "sha512-+w9D5ulSoBNlmw9OHn3U2v51SyoCd0he+bB3xMl62oijhrspxowjU+AIcDY0N3iEJbUEkB15IlMASQsxYigvXg== sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y="}, "registry": "npm", "hash": "0b5ee648388e2c860282e793f1856fec3f301b56"}