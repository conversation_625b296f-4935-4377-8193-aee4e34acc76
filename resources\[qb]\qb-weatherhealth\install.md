# QBCore Weather Health System - Instalační návod

## 🚀 Rychlá instalace

### 1. Databáze
Spusťte SQL soubor v vaší databázi:
```sql
-- Spusťte obsah souboru sql/install.sql
```

### 2. Items (✅ HOTOVO!)
Items jsou již <PERSON>ky přidány do `qb-core/shared/items.lua`.

**<PERSON><PERSON> není potřeba dělat!** Items jsou připraveny k použití.

**Pokud máte silně upravený items.lua**, zkontrolujte, zda obsahuje tyto items:

```lua
-- Weather Health System Items (přidejte před uzavírací závorku })
medicine                     = { name = 'medicine', label = 'Medicine', weight = 100, type = 'item', image = 'medicine.png', unique = false, useable = true, shouldClose = true, description = 'Medicine to treat common illnesses like cold and flu' },
heatpack                     = { name = 'heatpack', label = 'Heat Pack', weight = 50, type = 'item', image = 'heatpack.png', unique = false, useable = true, shouldClose = true, description = 'Disposable heat pack to warm up in cold weather' },
coolpack                     = { name = 'coolpack', label = 'Cool Pack', weight = 50, type = 'item', image = 'coolpack.png', unique = false, useable = true, shouldClose = true, description = 'Cooling pack to cool down in hot weather' },
thermometer                  = { name = 'thermometer', label = 'Thermometer', weight = 25, type = 'item', image = 'thermometer.png', unique = false, useable = true, shouldClose = true, description = 'Digital thermometer to check body temperature' },
vitamin_c                    = { name = 'vitamin_c', label = 'Vitamin C', weight = 10, type = 'item', image = 'vitamin_c.png', unique = false, useable = true, shouldClose = true, description = 'Vitamin C tablets to boost immunity' },
flu_vaccine                  = { name = 'flu_vaccine', label = 'Flu Vaccine', weight = 75, type = 'item', image = 'flu_vaccine.png', unique = false, useable = true, shouldClose = true, description = 'Flu vaccine to prevent influenza' },
hand_warmer                  = { name = 'hand_warmer', label = 'Hand Warmer', weight = 25, type = 'item', image = 'hand_warmer.png', unique = false, useable = true, shouldClose = true, description = 'Small hand warmer for cold weather' },
cooling_towel                = { name = 'cooling_towel', label = 'Cooling Towel', weight = 100, type = 'item', image = 'cooling_towel.png', unique = false, useable = true, shouldClose = true, description = 'Special towel that stays cool when wet' },
energy_drink                 = { name = 'energy_drink', label = 'Energy Drink', weight = 150, type = 'item', image = 'energy_drink.png', unique = false, useable = true, shouldClose = true, description = 'Energy drink to restore stamina when sick' },
hot_tea                      = { name = 'hot_tea', label = 'Hot Tea', weight = 200, type = 'item', image = 'hot_tea.png', unique = false, useable = true, shouldClose = true, description = 'Hot tea to warm up and feel better' },
ice_pack                     = { name = 'ice_pack', label = 'Ice Pack', weight = 300, type = 'item', image = 'ice_pack.png', unique = false, useable = true, shouldClose = true, description = 'Medical ice pack for treating heat exhaustion' },
blanket                      = { name = 'blanket', label = 'Emergency Blanket', weight = 500, type = 'item', image = 'blanket.png', unique = false, useable = true, shouldClose = true, description = 'Emergency blanket for warmth and comfort' },
first_aid_kit                = { name = 'first_aid_kit', label = 'First Aid Kit', weight = 1000, type = 'item', image = 'first_aid_kit.png', unique = false, useable = true, shouldClose = true, description = 'Complete first aid kit for medical emergencies' },
```

### 3. Server.cfg
Resource je již přidán do server.cfg:
```
ensure qb-weatherhealth
```

### 4. Restart serveru
```
restart qb-core
ensure qb-weatherhealth
```

## 🎮 Testování

### Základní testy:
1. **Příkazy**:
   - `/weatherhealth status` - zobrazí aktuální stav
   - `/healthstatus` - zobrazí zdravotní stav

2. **Items**:
   - Dejte si `medicine` a použijte ho
   - Dejte si `heatpack` a použijte ho
   - Dejte si `thermometer` a zkontrolujte teplotu

3. **Admin příkazy**:
   - `/checkhealthstatus [id]` - zkontroluje zdraví hráče
   - `/givedisease [id] cold` - dá hráči nachlazení
   - `/curedisease [id]` - vyléčí hráče
   - `/weatherstats` - zobrazí statistiky systému

### Testovací scénář:
1. Změňte počasí na sníh: `/weather snow`
2. Oblečte se lehce (tričko, šortky)
3. Počkejte 5-10 minut
4. Měli byste dostat varování o chladu
5. Použijte `heatpack` nebo se oblečte teple

## 🔧 Konfigurace

### Hlavní nastavení v `config.lua`:

```lua
-- Interval kontroly zdraví
Config.PlayerHealthCheckInterval = 60000 -- 1 minuta

-- Teplotní indexy počasí
Config.WeatherTemperature = {
    ['SNOW'] = -5,      -- Sníh
    ['RAIN'] = 8,       -- Déšť
    ['EXTRASUNNY'] = 30 -- Velmi horko
}

-- Rychlost změny zdraví
Config.HealthEffects.healthChangeRate = {
    perfect = 0,        -- Žádná změna
    uncomfortable = -1, -- -1 zdraví za minutu
    dangerous = -3,     -- -3 zdraví za minutu
}
```

## 🐛 Řešení problémů

### Systém nefunguje:
1. Zkontrolujte console na chyby
2. Ověřte, že `qb-weathersync` běží
3. Zkontrolujte databázové připojení

### Items nefungují:
1. Ověřte, že jsou přidány do `items.lua`
2. Restartujte `qb-core`
3. Zkontrolujte, že máte items v inventáři

### NPC se neoblékají:
1. Zkontrolujte `Config.MaxNPCDistance`
2. Ověřte, že jsou NPC v dosahu
3. Zkontrolujte console na chyby

## 📊 Monitoring

### Logy systému:
- Všechny akce jsou logovány do databáze
- Použijte `/detailedweatherstats` pro podrobné statistiky
- Zkontrolujte tabulku `weather_logs` pro historii

### Výkon:
- Systém je optimalizován pro minimální dopad na výkon
- Kontroly probíhají v intervalech
- NPC jsou spravováni pouze v dosahu hráčů

## ✅ Hotovo!

Váš Weather Health System je nyní plně funkční! 

Pro další podporu nebo úpravy se podívejte do `README.md` nebo kontaktujte vývojáře.
