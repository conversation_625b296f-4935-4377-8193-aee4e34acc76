{"manifest": {"name": "@types/keygrip", "version": "1.0.2", "description": "TypeScript definitions for keygrip", "license": "MIT", "contributors": [{"name": "j<PERSON><PERSON>", "url": "https://github.com/jkeylu"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/keygrip"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "85791e9272f5401dc3416aead8d95149b11fbcc20d9d5b22ef8f75aef9021382", "typeScriptVersion": "2.8", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-keygrip-1.0.2-513abfd256d7ad0bf1ee1873606317b33b1b2a72-integrity\\node_modules\\@types\\keygrip\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/keygrip`\n\n# Summary\nThis package contains type definitions for keygrip (https://github.com/crypto-utils/keygrip).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/keygrip.\n\n### Additional Details\n * Last updated: Thu, 26 Dec 2019 18:59:55 GMT\n * Dependencies: none\n * Global values: none\n\n# Credits\nThese definitions were written by <PERSON><PERSON><PERSON> (https://github.com/jkeylu).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation. All rights reserved.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/keygrip/-/keygrip-1.0.2.tgz#513abfd256d7ad0bf1ee1873606317b33b1b2a72", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/keygrip/-/keygrip-1.0.2.tgz", "hash": "513abfd256d7ad0bf1ee1873606317b33b1b2a72", "integrity": "sha512-GJhpTepz2udxGexqos8wgaBx4I/zWIDPh/KOGEwAqtuGDkOUJu5eFvwmdBX4AmB8Odsr+9pHCQqiAqDL/yKMKw==", "registry": "npm", "packageName": "@types/keygrip", "cacheIntegrity": "sha512-GJhpTepz2udxGexqos8wgaBx4I/zWIDPh/KOGEwAqtuGDkOUJu5eFvwmdBX4AmB8Odsr+9pHCQqiAqDL/yKMKw== sha1-UTq/0lbXrQvx7hhzYGMXszsbKnI="}, "registry": "npm", "hash": "513abfd256d7ad0bf1ee1873606317b33b1b2a72"}