{"manifest": {"name": "anymatch", "version": "3.1.1", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "files": ["index.js", "index.d.ts"], "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, "license": "ISC", "homepage": "https://github.com/micromatch/anymatch", "repository": {"type": "git", "url": "https://github.com/micromatch/anymatch"}, "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "scripts": {"test": "nyc mocha", "mocha": "mocha"}, "devDependencies": {"mocha": "^6.1.3", "nyc": "^14.0.0"}, "engines": {"node": ">= 8"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-anymatch-3.1.1-c55ecf02185e2469259399310c173ce31233b142-integrity\\node_modules\\anymatch\\package.json", "readmeFilename": "README.md", "readme": "anymatch [![Build Status](https://travis-ci.org/micromatch/anymatch.svg?branch=master)](https://travis-ci.org/micromatch/anymatch) [![Coverage Status](https://img.shields.io/coveralls/micromatch/anymatch.svg?branch=master)](https://coveralls.io/r/micromatch/anymatch?branch=master)\n======\nJavascript module to match a string against a regular expression, glob, string,\nor function that takes the string as an argument and returns a truthy or falsy\nvalue. The matcher can also be an array of any or all of these. Useful for\nallowing a very flexible user-defined config to define things like file paths.\n\n__Note: This module has Bash-parity, please be aware that Windows-style backslashes are not supported as separators. See https://github.com/micromatch/micromatch#backslashes for more information.__\n\n\nUsage\n-----\n```sh\nnpm install anymatch\n```\n\n#### anymatch(matchers, testString, [returnIndex], [options])\n* __matchers__: (_Array|String|RegExp|Function_)\nString to be directly matched, string with glob patterns, regular expression\ntest, function that takes the testString as an argument and returns a truthy\nvalue if it should be matched, or an array of any number and mix of these types.\n* __testString__: (_String|Array_) The string to test against the matchers. If\npassed as an array, the first element of the array will be used as the\n`testString` for non-function matchers, while the entire array will be applied\nas the arguments for function matchers.\n* __options__: (_Object_ [optional]_) Any of the [picomatch](https://github.com/micromatch/picomatch#options) options.\n    * __returnIndex__: (_Boolean [optional]_) If true, return the array index of\nthe first matcher that that testString matched, or -1 if no match, instead of a\nboolean result.\n\n```js\nconst anymatch = require('anymatch');\n\nconst matchers = [ 'path/to/file.js', 'path/anyjs/**/*.js', /foo.js$/, string => string.includes('bar') && string.length > 10 ] ;\n\nanymatch(matchers, 'path/to/file.js'); // true\nanymatch(matchers, 'path/anyjs/baz.js'); // true\nanymatch(matchers, 'path/to/foo.js'); // true\nanymatch(matchers, 'path/to/bar.js'); // true\nanymatch(matchers, 'bar.js'); // false\n\n// returnIndex = true\nanymatch(matchers, 'foo.js', {returnIndex: true}); // 2\nanymatch(matchers, 'path/anyjs/foo.js', {returnIndex: true}); // 1\n\n// any picomatc\n\n// using globs to match directories and their children\nanymatch('node_modules', 'node_modules'); // true\nanymatch('node_modules', 'node_modules/somelib/index.js'); // false\nanymatch('node_modules/**', 'node_modules/somelib/index.js'); // true\nanymatch('node_modules/**', '/absolute/path/to/node_modules/somelib/index.js'); // false\nanymatch('**/node_modules/**', '/absolute/path/to/node_modules/somelib/index.js'); // true\n\nconst matcher = anymatch(matchers);\n['foo.js', 'bar.js'].filter(matcher);  // [ 'foo.js' ]\nanymatch master* ❯\n\n```\n\n#### anymatch(matchers)\nYou can also pass in only your matcher(s) to get a curried function that has\nalready been bound to the provided matching criteria. This can be used as an\n`Array#filter` callback.\n\n```js\nvar matcher = anymatch(matchers);\n\nmatcher('path/to/file.js'); // true\nmatcher('path/anyjs/baz.js', true); // 1\n\n['foo.js', 'bar.js'].filter(matcher); // ['foo.js']\n```\n\nChangelog\n----------\n[See release notes page on GitHub](https://github.com/micromatch/anymatch/releases)\n\n- **v3.0:** Removed `startIndex` and `endIndex` arguments. Node 8.x-only.\n- **v2.0:** [micromatch](https://github.com/jonschlinkert/micromatch) moves away from minimatch-parity and inline with Bash. This includes handling backslashes differently (see https://github.com/micromatch/micromatch#backslashes for more information).\n- **v1.2:** anymatch uses [micromatch](https://github.com/jonschlinkert/micromatch)\nfor glob pattern matching. Issues with glob pattern matching should be\nreported directly to the [micromatch issue tracker](https://github.com/jonschlinkert/micromatch/issues).\n\nLicense\n-------\n[ISC](https://raw.github.com/micromatch/anymatch/master/LICENSE)\n", "licenseText": "The ISC License\n\nCopyright (c) 2019 <PERSON><PERSON>, <PERSON> (https://paulmillr.com)\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/anymatch/-/anymatch-3.1.1.tgz#c55ecf02185e2469259399310c173ce31233b142", "type": "tarball", "reference": "https://registry.yarnpkg.com/anymatch/-/anymatch-3.1.1.tgz", "hash": "c55ecf02185e2469259399310c173ce31233b142", "integrity": "sha512-mM8522psRCqzV+6LhomX5wgp25YVibjh8Wj23I5RPkPppSVSjyKD2A2mBJmWGa+KN7f2D6LNh9jkBCeyLktzjg==", "registry": "npm", "packageName": "anymatch", "cacheIntegrity": "sha512-mM8522psRCqzV+6LhomX5wgp25YVibjh8Wj23I5RPkPppSVSjyKD2A2mBJmWGa+KN7f2D6LNh9jkBCeyLktzjg== sha1-xV7PAhheJGklk5kxDBc84xIzsUI="}, "registry": "npm", "hash": "c55ecf02185e2469259399310c173ce31233b142"}