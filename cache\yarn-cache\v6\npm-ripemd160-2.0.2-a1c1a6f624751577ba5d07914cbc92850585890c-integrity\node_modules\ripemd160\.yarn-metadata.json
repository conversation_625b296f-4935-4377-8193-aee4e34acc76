{"manifest": {"name": "ripemd160", "version": "2.0.2", "description": "Compute ripemd160 of bytes or strings.", "keywords": ["string", "strings", "ripemd160", "ripe160", "bitcoin", "bytes", "cryptography"], "license": "MIT", "files": ["index.js"], "main": "./index", "repository": {"url": "https://github.com/crypto-browserify/ripemd160", "type": "git"}, "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "node test/*.js"}, "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}, "devDependencies": {"hash-test-vectors": "^1.3.2", "standard": "^6.0.7", "tape": "^4.5.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-ripemd160-2.0.2-a1c1a6f624751577ba5d07914cbc92850585890c-integrity\\node_modules\\ripemd160\\package.json", "readmeFilename": "README.md", "readme": "# ripemd160\n\n[![NPM Package](https://img.shields.io/npm/v/ripemd160.svg?style=flat-square)](https://www.npmjs.org/package/ripemd160)\n[![Build Status](https://img.shields.io/travis/crypto-browserify/ripemd160.svg?branch=master&style=flat-square)](https://travis-ci.org/crypto-browserify/ripemd160)\n[![Dependency status](https://img.shields.io/david/crypto-browserify/ripemd160.svg?style=flat-square)](https://david-dm.org/crypto-browserify/ripemd160#info=dependencies)\n\n[![js-standard-style](https://cdn.rawgit.com/feross/standard/master/badge.svg)](https://github.com/feross/standard)\n\nNode style `ripemd160` on pure JavaScript.\n\n## Example\n\n```js\nvar RIPEMD160 = require('ripemd160')\n\nconsole.log(new RIPEMD160().update('42').digest('hex'))\n// => 0df020ba32aa9b8b904471ff582ce6b579bf8bc8\n\nvar ripemd160stream = new RIPEMD160()\nripemd160stream.end('42')\nconsole.log(ripemd160stream.read().toString('hex'))\n// => 0df020ba32aa9b8b904471ff582ce6b579bf8bc8\n```\n\n## LICENSE\n\nMIT\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016 crypto-browserify\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/ripemd160/-/ripemd160-2.0.2.tgz#a1c1a6f624751577ba5d07914cbc92850585890c", "type": "tarball", "reference": "https://registry.yarnpkg.com/ripemd160/-/ripemd160-2.0.2.tgz", "hash": "a1c1a6f624751577ba5d07914cbc92850585890c", "integrity": "sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA==", "registry": "npm", "packageName": "ripemd160", "cacheIntegrity": "sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA== sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw="}, "registry": "npm", "hash": "a1c1a6f624751577ba5d07914cbc92850585890c"}