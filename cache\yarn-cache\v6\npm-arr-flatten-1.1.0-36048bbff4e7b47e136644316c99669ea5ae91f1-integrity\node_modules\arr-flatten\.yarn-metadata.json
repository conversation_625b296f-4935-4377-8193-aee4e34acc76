{"manifest": {"name": "arr-flatten", "description": "Recursively flatten an array or arrays.", "version": "1.1.0", "homepage": "https://github.com/jonschlinkert/arr-flatten", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "https://lukeed.com"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/arr-flatten.git"}, "bugs": {"url": "https://github.com/jonschlinkert/arr-flatten/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"ansi-bold": "^0.1.1", "array-flatten": "^2.1.1", "array-slice": "^1.0.0", "benchmarked": "^1.0.0", "compute-flatten": "^1.0.0", "flatit": "^1.1.1", "flatten": "^1.0.2", "flatten-array": "^1.0.0", "glob": "^7.1.1", "gulp-format-md": "^0.1.12", "just-flatten-it": "^1.1.23", "lodash.flattendeep": "^4.4.0", "m_flattened": "^1.0.1", "mocha": "^3.2.0", "utils-flatten": "^1.0.0", "write": "^0.3.3"}, "keywords": ["arr", "array", "elements", "flat", "flatten", "nested", "recurse", "recursive", "recursively"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["arr-filter", "arr-union", "array-each", "array-unique"]}, "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-arr-flatten-1.1.0-36048bbff4e7b47e136644316c99669ea5ae91f1-integrity\\node_modules\\arr-flatten\\package.json", "readmeFilename": "README.md", "readme": "# arr-flatten [![NPM version](https://img.shields.io/npm/v/arr-flatten.svg?style=flat)](https://www.npmjs.com/package/arr-flatten) [![NPM monthly downloads](https://img.shields.io/npm/dm/arr-flatten.svg?style=flat)](https://npmjs.org/package/arr-flatten) [![NPM total downloads](https://img.shields.io/npm/dt/arr-flatten.svg?style=flat)](https://npmjs.org/package/arr-flatten) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/arr-flatten.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/arr-flatten) [![Windows Build Status](https://img.shields.io/appveyor/ci/jonschlinkert/arr-flatten.svg?style=flat&label=AppVeyor)](https://ci.appveyor.com/project/jonschlinkert/arr-flatten)\n\n> Recursively flatten an array or arrays.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save arr-flatten\n```\n\n## Install\n\nInstall with [bower](https://bower.io/)\n\n```sh\n$ bower install arr-flatten --save\n```\n\n## Usage\n\n```js\nvar flatten = require('arr-flatten');\n\nflatten(['a', ['b', ['c']], 'd', ['e']]);\n//=> ['a', 'b', 'c', 'd', 'e']\n```\n\n## Why another flatten utility?\n\nI wanted the fastest implementation I could find, with implementation choices that should work for 95% of use cases, but no cruft to cover the other 5%.\n\n## About\n\n### Related projects\n\n* [arr-filter](https://www.npmjs.com/package/arr-filter): Faster alternative to javascript's native filter method. | [homepage](https://github.com/jonschlinkert/arr-filter \"Faster alternative to javascript's native filter method.\")\n* [arr-union](https://www.npmjs.com/package/arr-union): Combines a list of arrays, returning a single array with unique values, using strict equality… [more](https://github.com/jonschlinkert/arr-union) | [homepage](https://github.com/jonschlinkert/arr-union \"Combines a list of arrays, returning a single array with unique values, using strict equality for comparisons.\")\n* [array-each](https://www.npmjs.com/package/array-each): Loop over each item in an array and call the given function on every element. | [homepage](https://github.com/jonschlinkert/array-each \"Loop over each item in an array and call the given function on every element.\")\n* [array-unique](https://www.npmjs.com/package/array-unique): Remove duplicate values from an array. Fastest ES5 implementation. | [homepage](https://github.com/jonschlinkert/array-unique \"Remove duplicate values from an array. Fastest ES5 implementation.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 20 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 1 | [lukeed](https://github.com/lukeed) |\n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on July 05, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/arr-flatten/-/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1", "type": "tarball", "reference": "https://registry.yarnpkg.com/arr-flatten/-/arr-flatten-1.1.0.tgz", "hash": "36048bbff4e7b47e136644316c99669ea5ae91f1", "integrity": "sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg==", "registry": "npm", "packageName": "arr-flatten", "cacheIntegrity": "sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg== sha1-NgSLv/TntH4TZkQxbJlmnqWukfE="}, "registry": "npm", "hash": "36048bbff4e7b47e136644316c99669ea5ae91f1"}