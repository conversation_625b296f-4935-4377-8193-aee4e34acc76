# 🧪 DDCZ-VEHICLE Testing Guide

## ✅ Pre-Installation Checklist

- [ ] QBCore Framework nainstalován
- [ ] Server má restart oprávnění
- [ ] Testovací vozidla k dispozici

## 🔧 Installation Testing

### 1. Resource Loading
```cfg
# Přidat do server.cfg
ensure ddcz-vehicle

# Zkontrolovat server konzoli:
[DDCZ-VEHICLE] Server started successfully!
```

### 2. Config Validation
```lua
# Zkontrolovat config.lua:
Config.DefaultKeybind = 'U'
Config.RestrictedJobs = {'police', 'ambulance', 'fire'}
```

## 🎮 Functionality Testing

### Basic Menu Operations
1. **Otevření menu**: `/vehicle` nebo `U`
   - ✅ Menu se otevře s moderním designem
   - ✅ Zobrazí se informace o vozidle
   - ✅ Všechna tlačítka jsou viditelná

2. **Zavření menu**: `ESC` nebo `X` tlačítko
   - ✅ Menu se zavře s animací
   - ✅ NUI focus se vypne
   - ✅ Žádné chyby v konzoli

### Vehicle Controls Testing
1. **Motor ovládání**: Tlačítko "Motor"
   - ✅ Motor se zapne/vypne
   - ✅ Status se aktualizuje v UI
   - ✅ Notifikace se zobrazí

2. **Zámky**: Tlačítko "Zámky"
   - ✅ Vozidlo se zamkne/odemkne
   - ✅ Správná notifikace
   - ✅ Vizuální feedback

3. **Okna**: Tlačítko "Okna"
   - ✅ Všechna okna se otevřou/zavřou
   - ✅ Animace funguje správně

### Door Controls Testing
1. **Jednotlivé dveře**: Klik na door tlačítka
   - ✅ Přední levé/pravé dveře
   - ✅ Zadní levé/pravé dveře
   - ✅ Kapota se otevře/zavře
   - ✅ Kufr se otevře/zavře
   - ✅ Vizuální stav se aktualizuje

### Light Controls Testing
1. **Světlomety**: Tlačítko světlometů
   - ✅ Světla se zapnou/vypnou
   - ✅ Vizuální indikátor aktivní

2. **Blikače**: Levý/pravý blikač
   - ✅ Blikače fungují samostatně
   - ✅ Výstražná světla fungují

3. **Speciální světla**: Interiér, taxi
   - ✅ Interiérové světlo funguje
   - ✅ Taxi světlo (pokud podporováno)

### Restricted Features Testing (Police/EMS)

#### Setup Test User
```lua
# Nastavit testovacího hráče jako policii:
/setjob [playerid] police 4
```

#### Extras Testing
1. **Přístup k extras**: Restricted section viditelná
   - ✅ Extras tlačítka se zobrazí
   - ✅ Pouze pro povolané profese
   - ✅ Nepovolené profese nemají přístup

2. **Extra functionality**: Klik na extra tlačítka
   - ✅ Extras se zapnou/vypnou
   - ✅ Vizuální stav se aktualizuje
   - ✅ Synchronizace mezi hráči

#### Livery Testing
1. **Livery selector**: Šipky pro změnu
   - ✅ Livery se mění správně
   - ✅ Counter se aktualizuje
   - ✅ Tlačítka se deaktivují na krajích

### Synchronization Testing
1. **Multi-player sync**: Dva hráči u stejného vozidla
   - ✅ Změny dveří se synchronizují
   - ✅ Světla se synchronizují
   - ✅ Extras se synchronizují (police/EMS)
   - ✅ Livery se synchronizuje (police/EMS)

### Permission Testing
1. **Job restrictions**: Testovat různé profese
   - ✅ Police má přístup k extras/livery
   - ✅ Ambulance má přístup k extras/livery
   - ✅ Civilian nemá přístup k extras/livery
   - ✅ Správné error zprávy

### Distance Testing
1. **Menu auto-close**: Vzdálenost od vozidla
   - ✅ Menu se zavře při opuštění vozidla
   - ✅ Respektuje Config.MaxDistance
   - ✅ Config.AutoCloseOnExit funguje

## 🐛 Error Testing

### Invalid Inputs
- [ ] Otevření menu bez vozidla → Error message
- [ ] Příliš daleko od vozidla → Error message
- [ ] Nepovolená profese pro extras → Permission denied

### Edge Cases
- [ ] Vozidlo se zničí během otevřeného menu
- [ ] Hráč se odpojí během používání menu
- [ ] Resource restart během otevřeného menu
- [ ] Rychlé opakované klikání tlačítek

## 📊 Performance Testing

### Client Performance
- [ ] Žádné FPS drops při otevření menu
- [ ] Smooth animace
- [ ] Žádné memory leaky po dlouhém používání

### Server Performance
- [ ] Žádný server lag při synchronizaci
- [ ] Efektivní network eventy
- [ ] Proper error handling

### NUI Performance
- [ ] Rychlé načítání menu
- [ ] Responzivní tlačítka
- [ ] Žádné JavaScript chyby

## 🔍 Debug Mode Testing

Povolit debug v config.lua:
```lua
Config.Debug = true
```

Zkontrolovat konzoli pro:
- [ ] Vehicle data loading messages
- [ ] Synchronization confirmations
- [ ] Permission check results
- [ ] Error details

## ✅ Final Validation

### Core Functionality
- [ ] Menu se otevírá a zavírá správně
- [ ] Všechny vehicle controls fungují
- [ ] Synchronizace mezi hráči funguje
- [ ] Permission systém funguje správně

### User Experience
- [ ] Intuitivní interface
- [ ] Smooth animace
- [ ] Jasné notifikace
- [ ] Responzivní design

### Admin Features
- [ ] Admin příkazy fungují
- [ ] Config reload funguje
- [ ] Force close funguje

## 🚨 Common Issues & Solutions

### Menu se neotevírá
- **Check**: Hráč v/blízko vozidla
- **Solution**: Přiblížit se k vozidlu

### Extras nefungují
- **Check**: Správná profese hráče
- **Solution**: Nastavit správný job

### Synchronizace nefunguje
- **Check**: Config.SyncVehicleChanges = true
- **Solution**: Zkontrolovat server eventy

### Performance problémy
- **Check**: Příliš mnoho otevřených menu
- **Solution**: Implementovat rate limiting

---

**✅ Testing Complete**: Všechny funkce fungují podle očekávání!
