{"manifest": {"name": "is-wsl", "version": "1.1.0", "description": "Check if the process is running inside Windows Subsystem for Linux (Bash on Windows)", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/is-wsl.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["check", "wsl", "windows", "subsystem", "linux", "detect", "bash", "process", "console", "terminal", "is"], "devDependencies": {"ava": "*", "clear-require": "^2.0.0", "proxyquire": "^1.7.11", "xo": "*"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-wsl-1.1.0-1f16e4aa22b04d1336b66188a66af3c600c3a66d-integrity\\node_modules\\is-wsl\\package.json", "readmeFilename": "readme.md", "readme": "# is-wsl [![Build Status](https://travis-ci.org/sindresorhus/is-wsl.svg?branch=master)](https://travis-ci.org/sindresorhus/is-wsl)\n\n> Check if the process is running inside [Windows Subsystem for Linux](https://msdn.microsoft.com/commandline/wsl/about) (Bash on Windows)\n\nCan be useful if you need to work around unimplemented or buggy features in WSL.\n\n\n## Install\n\n```\n$ npm install --save is-wsl\n```\n\n\n## Usage\n\n```js\nconst isWsl = require('is-wsl');\n\n// When running inside Windows Subsystem for Linux\nconsole.log(isWsl);\n//=> true\n```\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com)\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND <PERSON><PERSON>NFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-wsl/-/is-wsl-1.1.0.tgz#1f16e4aa22b04d1336b66188a66af3c600c3a66d", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-wsl/-/is-wsl-1.1.0.tgz", "hash": "1f16e4aa22b04d1336b66188a66af3c600c3a66d", "integrity": "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=", "registry": "npm", "packageName": "is-wsl", "cacheIntegrity": "sha512-gfygJYZ2gLTDlmbWMI0CE2MwnFzSN/2SZfkMlItC4K/JBlsWVDB0bO6XhqcY13YXE7iMcAJnzTCJjPiTeJJ0Mw== sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0="}, "registry": "npm", "hash": "1f16e4aa22b04d1336b66188a66af3c600c3a66d"}