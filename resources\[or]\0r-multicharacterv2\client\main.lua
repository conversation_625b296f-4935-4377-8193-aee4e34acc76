local CoreName = nil
local nuiLoaded = false
camera = nil
local firstSpawn = true
local spawned = false
local singleCoords = nil
isSettings = {}
local cameraAnimationActive = false

local function CheckNui()
    while not nuiLoaded do
        Citizen.Wait(100)
    end
end

local function NuiMessage(action, payload)
    CheckNui()
    SendNUIMessage({
        action = action,
        payload = payload
    })
end

local function DeleteCamera()
    RenderScriptCams(false, true, 2000, true, true)
    DestroyCam(camera, false)
end

local function PlayAnimations(job)
    if not isSettings.animations.status or isSettings.animations.scenario.status then return end

    local anim = isSettings.animations.single

    if not anim then return end

    Config.Animation.Export(anim)
end

local function PlayScnerio()
    if not isSettings.animations.scenario.status and not isSettings['coords'].scenario then return end

    if isSettings['coords'].scenario then
        TaskStartScenarioInPlace(PlayerPedId(), isSettings['coords'].scenario, 0, true)
    else
        TaskStartScenarioInPlace(PlayerPedId(), isSettings.animations.scenario.single, 0, true)
    end
end

local function PlayEffect()
    if not isSettings.particle.status then return end
    
    local pedCoords = GetEntityCoords(PlayerPedId())

    RequestNamedPtfxAsset(isSettings.particle.type.pName)
    while not HasNamedPtfxAssetLoaded(isSettings.particle.type.pName) do
        Citizen.Wait(0)
    end

    UseParticleFxAssetNextCall(isSettings.particle.type.pName)

    local effectHandle = StartParticleFxNonLoopedAtCoord(isSettings.particle.type.pNameChild, pedCoords.x, pedCoords.y, pedCoords.z, 0.0, 0.0, 0.0, 1.0, false, false, false)
end

local function isMapLoadedAtCoords(coords)
    RequestCollisionAtCoord(coords.x, coords.y, coords.z)
    Citizen.Wait(500)

    if HasCollisionLoadedAroundEntity(PlayerPedId()) then
        return true
    else
        return false
    end
end

Citizen.CreateThread(function()
    while not nuiLoaded do
        if NetworkIsSessionStarted() then
            SendNUIMessage({
                action = "checkNui",
            })
        end
        Citizen.Wait(2000)
    end
end)

Citizen.CreateThread(function()
    if GetResourceState('qbx_core') == 'started' then
        QBX = exports['qbx_core']
        CoreName = 'qbx_core'
    elseif GetResourceState('qb-core') == 'started' then
        QBCore = exports['qb-core']:GetCoreObject()
        CoreName = 'qb-core'
    end
end)

Citizen.CreateThread(function()
	while true do
		Wait(0)
		if NetworkIsSessionStarted() then
            while not nuiLoaded do
                Citizen.Wait(100)
            end
			TriggerEvent('0r-multicharacterv2:client:playerLoaded')
			return
		end
	end
end)

AddEventHandler('onResourceStop', function(resource)
    if resource == GetCurrentResourceName() then
        SetNuiFocus(false, false)
        SetEntityVisible(PlayerPedId(), true)
        FreezeEntityPosition(PlayerPedId(), false)
        SetWeatherSync(false)
        ClearPedTasks(PlayerPedId())
        SetTimecycleModifier('default')
        SetTimecycleModifierStrength(0.0)
        DestroyCam(camera, false)
        RenderScriptCams(false, false, 1, true, true)
    end
end)

RegisterNetEvent('0r-multicharacterv2:client:playerLoaded')
AddEventHandler('0r-multicharacterv2:client:playerLoaded', function()
	ShutdownLoadingScreen()
	ShutdownLoadingScreenNui()

    DoScreenFadeOut(300)

    while not IsScreenFadedOut() do
        Citizen.Wait(10)
    end

    NuiMessage('isLoading', true)

    local isLoading = true
    local isAvailableSlot = 0

    lib.callback('0r-multicharacterv2:server:getSettings', false, function(data)
        isSettings = data
        isLoading = false
    end)

    lib.callback('0r-multicharacterv2:server:getAvailableSlot', false, function(data)
        isAvailableSlot = data
    end)

    while isLoading do
        Wait(0)
    end

    if isSettings?.coords then
        singleCoords = isSettings['coords']
    else
        singleCoords = Config.Default.Settings.coords
    end

    SetEntityCoords(PlayerPedId(), isSettings.coords.pedCoords.x, isSettings.coords.pedCoords.y, isSettings.coords.pedCoords.z-0.98)
    SetEntityHeading(PlayerPedId(), singleCoords.pedCoords.w)

    Wait(3000)

    while not isMapLoadedAtCoords(isSettings.coords.pedCoords) do
        Wait(100)

        SetEntityCoords(PlayerPedId(), isSettings.coords.pedCoords.x, isSettings.coords.pedCoords.y, isSettings.coords.pedCoords.z-0.98)
        SetEntityHeading(PlayerPedId(), singleCoords.pedCoords.w)
    end

    local retval = nil

    if isSettings.coords.cameraCoords then
        retval = GetOffsetFromEntityInWorldCoords(PlayerPedId(), isSettings.coords.cameraCoords.x, isSettings.coords.cameraCoords.y, isSettings.coords.cameraCoords.z)
    else
        retval = GetOffsetFromEntityInWorldCoords(PlayerPedId(), 0.0, 1.0, .5)
    end
    
    camera = CreateCamWithParams('DEFAULT_SCRIPTED_CAMERA', retval.x, retval.y, retval.z, 0.0, 0.0, GetEntityHeading(PlayerPedId())-180.0, 70.00, false, 0)
    SetCamActive(camera, true)
    SetCamUseShallowDofMode(camera, true)
    SetCamFov(camera, 70.0)
    SetCamNearDof(camera, 0.3)
    SetCamFarDof(camera, 1.3)
    SetCamDofStrength(camera, 1.0)
    RenderScriptCams(true, false, 1, false, true)

    CreateThread(function()
        while isSettings['focusPlayer'] do
            SetUseHiDof()
            Wait(0)
        end
    end)

    Citizen.CreateThread(function()
        while DoesCamExist(camera) and Config.HideRadar do
            Wait(1)
            HideHudAndRadarThisFrame()
            DisplayRadar(false)
        end
    end)

    TriggerServerEvent('0r-multicharacterv2:server:changePlayerBucket', true)

    if isSettings['filter']['status'] then
        SetTimecycleModifier(isSettings['filter']['type'])
        SetTimecycleModifierStrength(1.0)
    else
        SetTimecycleModifier('default')
        SetTimecycleModifierStrength(0.0)
    end

    NuiMessage('setSettings', isSettings)
    NuiMessage('setConfig', {
        DOBLimit = Config.Dob,
        BackgroundMusic = Config.BackgroundMusic,
        DeleteButton = Config.DeleteButton,
        Location = Config.Location,
        Currency = Config.Currency,
        Filters = Config.Camera.Filters,
        Effects = Config.Effects.List,
        Animations = Config.Animation.List,
        Scenarios = Config.Animation.Scenario.List,
        Colors = Config.Colors,
        Coords = Config.Coords.List,
        Use0Resmon = Config.SpawnSelector.Use0Resmon,
        SpawnLocations = Config.SpawnSelector.Locations
    })
    NuiMessage('setLocales', Locales[Config.Locale].WEB)
    NuiMessage('setAvailableSlot', isAvailableSlot)

    SetWeatherSync(true)

    lib.callback('0r-multicharacterv2:server:checkCharacters', false, function(data)
        if data.chars and #data.chars > 0  then
            SetEntityVisible(PlayerPedId(), false)
            FreezeEntityPosition(PlayerPedId(), true)
        else
            SetEntityVisible(PlayerPedId(), false)
            FreezeEntityPosition(PlayerPedId(), true)
        end

        NuiMessage('setCharacters', data.chars)
        NuiMessage('setCountries', data.countries)
        SetNuiFocus(true, true)
        DoScreenFadeIn(300)
    end)
end)

RegisterNetEvent('0r-multicharacterv2:client:spawnLastLocation')
AddEventHandler('0r-multicharacterv2:client:spawnLastLocation', function(coords, data)
    if CoreName == 'qb-core' then
        if GetResourceState('qb-apartments') == 'started' and not Config.SpawnSelector.Skip then
            QBCore.Functions.TriggerCallback('apartments:GetOwnedApartment', function(result)
                if result then
                    TriggerEvent("apartments:client:SetHomeBlip", result.type)
                    local ped = PlayerPedId()
                    SetEntityCoords(ped, coords.x, coords.y, coords.z)
                    SetEntityHeading(ped, coords.w)
                    FreezeEntityPosition(ped, false)
                    SetEntityVisible(ped, true)
                    local PlayerData = QBCore.Functions.GetPlayerData()
                    local insideMeta = PlayerData.metadata["inside"]
        
                    if insideMeta.house then
                        TriggerEvent('qb-houses:client:LastLocationHouse', insideMeta.house)
                    elseif insideMeta.apartment.apartmentType and insideMeta.apartment.apartmentId then
                        TriggerEvent('qb-apartments:client:LastLocationHouse', insideMeta.apartment.apartmentType, insideMeta.apartment.apartmentId)
                    else
                        SetEntityCoords(ped, coords.x, coords.y, coords.z)
                        SetEntityHeading(ped, coords.w)
                        FreezeEntityPosition(ped, false)
                        SetEntityVisible(ped, true)
                    end
        
                    TriggerServerEvent('QBCore:Server:OnPlayerLoaded')
                    TriggerEvent('QBCore:Client:OnPlayerLoaded')
                    Wait(2000)
                    DoScreenFadeIn(250)
                end
            end, data.citizenid)
        else
            local ped = PlayerPedId()
            SetEntityCoords(ped, coords.x, coords.y, coords.z)
            SetEntityHeading(ped, coords.w)
            FreezeEntityPosition(ped, false)
            SetEntityVisible(ped, true)
            TriggerServerEvent('QBCore:Server:OnPlayerLoaded')
            TriggerEvent('QBCore:Client:OnPlayerLoaded')
            Wait(2000)
            DoScreenFadeIn(250)
        end
    elseif CoreName == 'qbx_core' then
        local ped = PlayerPedId()
        SetEntityCoords(ped, coords.x, coords.y, coords.z)
        SetEntityHeading(ped, coords.w)
        FreezeEntityPosition(ped, false)
        SetEntityVisible(ped, true)
        TriggerServerEvent('QBCore:Server:OnPlayerLoaded')
        TriggerEvent('QBCore:Client:OnPlayerLoaded')
        Wait(2000)
        DoScreenFadeIn(250)
    end
end)

RegisterNetEvent('0r-multicharacterv2:client:closeNUIdefault', function(isNew)
    TriggerServerEvent('QBCore:Server:OnPlayerLoaded')
    TriggerEvent('QBCore:Client:OnPlayerLoaded')
    TriggerServerEvent('qb-houses:server:SetInsideMeta', 0, false)
    TriggerServerEvent('qb-apartments:server:SetInsideMeta', 0, 0, false)
    if isNew then
        SetEntityCoords(PlayerPedId(), Config.Default.Spawn.Single.x, Config.Default.Spawn.Single.y, Config.Default.Spawn.Single.z)
        Config.CreateCharacterExports()
    end
    Wait(500)
    SetEntityVisible(PlayerPedId(), true)
    Wait(500)
    if IsScreenFadedOut() then
        DoScreenFadeIn(250)
    end
    SetWeatherSync(false)
end)

RegisterNetEvent('0r-multicharacterv2:client:createApartment', function()
    TriggerServerEvent('apartments:server:CreateApartment')
end)

RegisterNetEvent('0r-multicharacterv2:client:closeNUI', function()
    SetNuiFocus(false, false)
    SetEntityVisible(PlayerPedId(), true)
end)

RegisterNUICallback('checkNui', function(data, cb)
    nuiLoaded = true
    cb('ok')
end)

RegisterNUICallback('changeSetting', function(data, cb)
    if data.subhead then
        isSettings[data.head][data.subhead][data.body] = data.value
    elseif data.body then
        isSettings[data.head][data.body] = data.value
    else
        isSettings[data.head] = data.value
    end

    lib.callback('0r-multicharacterv2:server:changeSetting', false, function()
        cb('ok')
    end, data)

    if not data.value then
        SetTimecycleModifier('default')
        SetTimecycleModifierStrength(0.0)
        isSettings['filter']['type'] = 'default'
        Wait(1000)
        lib.callback('0r-multicharacterv2:server:changeType', false, function()
            cb('ok')
        end, 'filter', isSettings['filter']['type'])
    end

    if data.head == 'animations' and not data.subhead then
        if data.value then
            Config.Animation.Export(isSettings['animations']['single'])
        else
            Config.Animation.Stop()
        end
    end

    if data.subhead == 'scenario' then
        if data.value then
            if not isSettings['coords'].scenario then
                ClearPedTasks(PlayerPedId())
                Wait(50)
                TaskStartScenarioInPlace(PlayerPedId(), isSettings['animations']['scenario']['single'], 0, true)
            end
        else
            if not isSettings['coords'].scenario then
                ClearPedTasks(PlayerPedId())
            end
        end
    end

    if data.head == 'weather' then
        if isSettings['weather']['status'] and isSettings['time']['status'] then
            SetWeatherSync(true)
        else 
            SetWeatherSync(false)
        end
    end
end)

RegisterNUICallback('changeFilter', function(data, cb)
    if isSettings['filter']['status'] then
        SetTimecycleModifier(data)
        SetTimecycleModifierStrength(1.0)
    else
        SetTimecycleModifier('default')
        SetTimecycleModifierStrength(0.0)
    end

    isSettings['filter']['type'] = data

    lib.callback('0r-multicharacterv2:server:changeType', false, function()
        cb('ok')
    end, 'filter', data)
end)

RegisterNUICallback('changeParticle', function(data, cb)
    local pedCoords = GetEntityCoords(PlayerPedId())
    local forwardVector = GetEntityForwardVector(PlayerPedId())

    local effectX = pedCoords.x + (forwardVector.x * 0.5)
    local effectY = pedCoords.y + (forwardVector.y * 0.5)
    local effectZ = pedCoords.z + 0.0

    RequestNamedPtfxAsset(data.pName)
    while not HasNamedPtfxAssetLoaded(data.pName) do
        Citizen.Wait(0)
    end

    UseParticleFxAssetNextCall(data.pName)

    local effectHandle = StartParticleFxNonLoopedAtCoord(data.pNameChild, effectX, effectY, effectZ, 0.0, 0.0, 0.0, 1.0, false, false, false)

    lib.callback('0r-multicharacterv2:server:changeType', false, function()
        cb('ok')
    end, 'particle', data)
end)

RegisterNUICallback('changeAnimation', function(data, cb)
    if not isSettings.animations.status then return end

    ClearPedTasks(PlayerPedId())
    Config.Animation.Stop()
    Config.Animation.Export(data)

    lib.callback('0r-multicharacterv2:server:changeType', false, function()
        cb('ok')
    end, 'animation', data)
end)

RegisterNUICallback('changeScenario', function(data, cb)
    if not isSettings.animations.scenario.status then return end
    
    if not isSettings['coords'].scenario then
        ClearPedTasks(PlayerPedId())
        Wait(50)
        TaskStartScenarioInPlace(PlayerPedId(), data, 0, true)
    end

    lib.callback('0r-multicharacterv2:server:changeType', false, function()
        cb('ok')
    end, 'scenario', data)
end)

RegisterNUICallback('changeStreamerMode', function(data, cb)
    isSettings['streamerMode'] = data

    lib.callback('0r-multicharacterv2:server:changeSetting', false, function()
        cb('ok')
    end, { head = 'streamerMode', subhead = false, body = false, value = data })
end)

RegisterNUICallback('changeMuteMusic', function(data, cb)
    isSettings['mutedMusic'] = data

    lib.callback('0r-multicharacterv2:server:changeSetting', false, function()
        cb('ok')
    end, { head = 'mutedMusic', subhead = false, body = false, value = data })
end)

RegisterNUICallback('changeFocusPlayer', function(data, cb)
    isSettings['focusPlayer'] = data

    while isSettings['focusPlayer'] do
        SetUseHiDof()
        Wait(0)
    end

    lib.callback('0r-multicharacterv2:server:changeSetting', false, function()
        cb('ok')
    end, { head = 'focusPlayer', subhead = false, body = false, value = data })
end)

RegisterNUICallback('changeClearMode', function(data, cb)
    isSettings['clearMode'] = data

    lib.callback('0r-multicharacterv2:server:changeSetting', false, function()
        cb('ok')
    end, { head = 'clearMode', subhead = false, body = false, value = data })
end)

RegisterNUICallback('changeTheme', function(data, cb)
    isSettings['color'] = data

    lib.callback('0r-multicharacterv2:server:changeType', false, function()
        cb('ok')
    end, 'theme', data)
end)

RegisterNUICallback('changeWeather', function(data,cb)
    isSettings['weather']['type'] = data

    SetOverrideWeather(isSettings.weather.type)
    SetWeatherTypePersist(isSettings.weather.type)
    SetWeatherTypeNow(isSettings.weather.type)
    SetWeatherTypeNowPersist(isSettings.weather.type)

    lib.callback('0r-multicharacterv2:server:changeType', false, function()
        cb('ok')
    end, 'weather', data)
end)

RegisterNUICallback('changeTime', function(data, cb)
    isSettings['time']['hour'] = data

    NetworkOverrideClockTime(isSettings.time.hour, 0, 0)

    lib.callback('0r-multicharacterv2:server:changeType', false, function()
        cb('ok')
    end, 'time', data)
end)

RegisterNUICallback('changeBackground', function(data, cb)
    isSettings.coords = data

    SetEntityCoords(PlayerPedId(), isSettings.coords.pedCoords.x, isSettings.coords.pedCoords.y, isSettings.coords.pedCoords.z-0.98)
    SetEntityHeading(PlayerPedId(), data.pedCoords.w)

    while not isMapLoadedAtCoords(data.pedCoords) do
        Wait(100)

        SetEntityCoords(PlayerPedId(), isSettings.coords.pedCoords.x, isSettings.coords.pedCoords.y, isSettings.coords.pedCoords.z-0.98)
        SetEntityHeading(PlayerPedId(), data.pedCoords.w)
    end

    FreezeEntityPosition(PlayerPedId(), true)

    local retval = GetOffsetFromEntityInWorldCoords(PlayerPedId(), 0.0, 1.0, .5)

    SetCamCoord(camera, retval.x, retval.y, retval.z)
    SetCamRot(camera, 0.0, 0.0, GetEntityHeading(PlayerPedId())-180.0, 2)

    ClearPedTasks()

    if isSettings['coords'].scenario then
        TaskStartScenarioInPlace(PlayerPedId(), isSettings['coords'].scenario, 0, true)
    else
        TaskStartScenarioInPlace(PlayerPedId(), isSettings.animations.scenario.single, 0, true)
    end

    lib.callback('0r-multicharacterv2:server:changeType', false, function()
        cb('ok')
    end, 'background', data)
end)

RegisterNUICallback('createSlotKey', function(data, cb)
    lib.callback('0r-multicharacterv2:server:openNewSlot', false, function(slot)
        cb(slot)
    end, data)
end)

local rotateingCam = false

RegisterNUICallback('newCharacterCam', function(data)
    local heading = GetCamRot(camera).z

    if data then
        SetEntityVisible(PlayerPedId(), false)
        SetEntityInvincible(PlayerPedId(), true)
        rotateingCam = true

        Citizen.CreateThread(function()
            while rotateingCam do
                Citizen.Wait(25)
    
                heading = heading + 0.1
                if heading >= 360.0 then
                    heading = 0.0
                end
    
                SetCamRot(camera, 0.0, 0.0, heading, 2)
            end
        end)
    else
        rotateingCam = false
        Citizen.Wait(50)

        SetEntityVisible(PlayerPedId(), true)
        local retval = GetOffsetFromEntityInWorldCoords(PlayerPedId(), 0.0, 1.0, .5)

        SetCamParams(camera, retval.x, retval.y, retval.z, 0.0, 0.0, GetEntityHeading(PlayerPedId())-180.0, 70.00, 1000, 0, 0, 2)
    end
end)

RegisterNUICallback('selectCharacter', function(data, cb)
    local isLoading = true
    local isModel = false
    local isSkin = false

    Config.CustomHud(true)

    SetEntityCoords(PlayerPedId(), isSettings.coords.pedCoords.x, isSettings.coords.pedCoords.y, isSettings.coords.pedCoords.z-0.98)
    SetEntityHeading(PlayerPedId(), isSettings.coords.pedCoords.w)
    FreezeEntityPosition(PlayerPedId(), true)

    local retval = GetOffsetFromEntityInWorldCoords(PlayerPedId(), 0.0, 1.0, .5)

    SetCamParams(camera, retval.x, retval.y, retval.z, 0.0, 0.0, GetEntityHeading(PlayerPedId())-180.0, 70.00, 1000, 0, 0, 2)

    lib.callback('0r-multicharacterv2:server:getSkin', false, function(model, skin)
        isLoading = false
        isModel = model
        isSkin = skin
    end, data.citizenid)

    while isLoading do
        Wait(0)
    end

    if not isModel then
        return
    end

    if type(tonumber(isModel)) == 'number' then
        if data.charinfo.gender == 0 then
            isModel = 'mp_m_freemode_01'
        else
            isModel = 'mp_f_freemode_01'
        end
    end

    RequestModel(isModel)
    while not HasModelLoaded(isModel) do
        Wait(0)
    end

    SetPlayerModel(PlayerId(), GetHashKey(isModel))
    SetModelAsNoLongerNeeded(GetHashKey(isModel))
    SetEntityVisible(PlayerPedId(), true)
    SetPedComponentVariation(PlayerPedId(), 0, 0, 0, 2)
    FreezeEntityPosition(PlayerPedId(), true)
    SetEntityInvincible(PlayerPedId(), false)
    PlaceObjectOnGroundProperly(PlayerPedId())
    SetBlockingOfNonTemporaryEvents(PlayerPedId(), true)

    if Config.Clothing == 'rcore_clothing' then
        isSkin = exports["rcore_clothing"]:getSkinByIdentifier(data.citizenid)
    end

    Config.ClothingExports(PlayerPedId(), isSkin)

    if firstSpawn then
        Wait(1000)
    end

    mugshot = nil

    PlayAnimations(data.job.name)
    PlayScnerio()
    PlayEffect()

    firstSpawn = false
end)

RegisterNUICallback('deleteCharacter', function(data, cb)
    lib.callback('0r-multicharacterv2:server:deleteCharacter', false, function()
        cb('ok')
    end, data.citizenid)
end)

RegisterNUICallback('playCharacter', function(data, cb)
    if Config.SpawnSelector.Use0Resmon and not Config.SpawnSelector.Skip then
        isSettings['focusPlayer'] = false
        local retval = GetOffsetFromEntityInWorldCoords(PlayerPedId(), 0.0, 0.0, 3.0)
        SetCamParams(camera, retval.x, retval.y, retval.z, -90.0, 0.0, GetEntityHeading(PlayerPedId())-180.0, 70.00, 1000, 0, 0, 2)
        ShakeCam(camera, "HAND_SHAKE", 0.3)

        cameraAnimationActive = true

        SetTimeout(2000, function()
            SetCamParams(camera, retval.x, retval.y, retval.z + 1500, -90.0, 0.0, GetEntityHeading(PlayerPedId())-180.0, 70.00, 3000, 0, 0, 2)

            NuiMessage('showSpawn', true)

            SetTimeout(3500, function()
                local lastPosition = json.decode(data.position)

                SetCamParams(camera, lastPosition.x, lastPosition.y, lastPosition.z + 1500, -90.0, 0.0, GetEntityHeading(PlayerPedId())-180.0, 70.00, 3000, 0, 0, 2)

                SetTimeout(3000, function()
                    cameraAnimationActive = false
                end)
            end)
        end)
    else
        DoScreenFadeOut(500)
        while not IsScreenFadedOut() do
            Citizen.Wait(10)
        end
        DeleteCamera()

        ClearPedTasks(PlayerPedId())
        SetNuiFocus(false, false)
        SetEntityVisible(PlayerPedId(), false)
        FreezeEntityPosition(PlayerPedId(), false)
        Config.CustomHud(false)
        rotateingCam = false
        lib.callback('0r-multicharacterv2:server:playCharacter', false, function()
            cb('ok')
        end, data)
    end

    SetTimecycleModifier('default')
    SetTimecycleModifierStrength(0.0)
end)

RegisterNUICallback('createCharacter', function(data, cb)
    DoScreenFadeOut(500)
    ClearPedTasks(PlayerPedId())
    SetNuiFocus(false, false)
    DeleteCamera()
    SetEntityVisible(PlayerPedId(), false)
    FreezeEntityPosition(PlayerPedId(), false)
    SetWeatherSync(false)
    Config.CustomHud(false)

    rotateingCam = false

    lib.callback('0r-multicharacterv2:server:createCharacter', false, function()
        cb('ok')
    end, data)

    SetTimecycleModifier('default')
    SetTimecycleModifierStrength(0.0)
end)

RegisterNUICallback('selectLocation', function(data, cb)
    while cameraAnimationActive do
        Citizen.Wait(0)
    end
    
    if data.type == 'last-location' then
        local lastPosition = json.decode(data.playerLastLocation)
        SetCamParams(camera, lastPosition.x, lastPosition.y, lastPosition.z + 1500, -90.0, 0.0, GetEntityHeading(PlayerPedId())-180.0, 70.00, 1000, 0, 0, 2)
        SetEntityCoords(PlayerPedId(), lastPosition.x, lastPosition.y, lastPosition.z)
        SetEntityHeading(PlayerPedId(), GetEntityHeading(PlayerPedId()))

        cameraAnimationActive = true

        SetTimeout(1000, function()
            cameraAnimationActive = false
        end)
    elseif data.type == 'custom' then
        SetCamParams(camera, data.location.x, data.location.y, data.location.z + 1500, -90.0, 0.0, GetEntityHeading(PlayerPedId())-180.0, 70.00, 1000, 0, 0, 2)
        SetEntityCoords(PlayerPedId(), data.location.x, data.location.y, data.location.z)
        SetEntityHeading(PlayerPedId(), GetEntityHeading(PlayerPedId()))

        cameraAnimationActive = true

        SetTimeout(1000, function()
            cameraAnimationActive = false
        end)
    end
end)

RegisterNUICallback('spawnLocation', function(data, cb)
    while cameraAnimationActive do
        Citizen.Wait(0)
    end

    if data.type == 'last-location' then
        local lastPosition = json.decode(data.playerLastLocation)
        SetEntityCoords(PlayerPedId(), lastPosition.x, lastPosition.y, lastPosition.z)
        SetEntityHeading(PlayerPedId(), GetEntityHeading(PlayerPedId()))
        local retval = GetOffsetFromEntityInWorldCoords(PlayerPedId(), 0.0, 0.0, 3.0)
        SetCamParams(camera, retval.x, retval.y, retval.z, -90.0, 0.0, GetEntityHeading(PlayerPedId())-180.0, 70.00, 1000, 0, 0, 2)
        SetTimeout(1000, function()
            local retval = GetOffsetFromEntityInWorldCoords(PlayerPedId(), 0.0, 1.0, .5)
            SetCamParams(camera,  retval.x, retval.y, retval.z, 0.0, 0.0, GetEntityHeading(PlayerPedId())-180.0, 70.00, 2000, 0, 0, 2)
            SetTimeout(2500, function()
                DeleteCamera()
            end)
        end)
    elseif data.type == 'custom' then
        SetEntityCoords(PlayerPedId(), data.location.x, data.location.y, data.location.z)
        SetEntityHeading(PlayerPedId(), GetEntityHeading(PlayerPedId()))
        local retval = GetOffsetFromEntityInWorldCoords(PlayerPedId(), 0.0, 0.0, 3.0)
        SetCamParams(camera, retval.x, retval.y, retval.z, -90.0, 0.0, GetEntityHeading(PlayerPedId())-180.0, 70.00, 1000, 0, 0, 2)
        SetTimeout(1000, function()
            local retval = GetOffsetFromEntityInWorldCoords(PlayerPedId(), 0.0, 1.0, .5)
            SetCamParams(camera, retval.x, retval.y, retval.z, 0.0, 0.0, GetEntityHeading(PlayerPedId())-180.0, 70.00, 2000, 0, 0, 2)
            SetTimeout(2500, function()
                DeleteCamera()
            end)
        end)
    end

    ClearPedTasks(PlayerPedId())
    SetNuiFocus(false, false)
    SetEntityVisible(PlayerPedId(), true)
    FreezeEntityPosition(PlayerPedId(), false)
    rotateingCam = false

    lib.callback('0r-multicharacterv2:server:playCharacter', false, function()
        cb('ok')
    end, data.character)
end)
