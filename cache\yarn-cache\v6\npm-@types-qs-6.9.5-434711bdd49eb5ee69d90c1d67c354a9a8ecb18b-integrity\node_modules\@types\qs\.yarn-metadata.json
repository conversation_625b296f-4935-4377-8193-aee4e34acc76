{"manifest": {"name": "@types/qs", "version": "6.9.5", "description": "TypeScript definitions for qs", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/RWander"}, {"name": "<PERSON>", "url": "https://github.com/leonyu"}, {"name": "<PERSON>", "url": "https://github.com/tehbelinda"}, {"name": "<PERSON>", "url": "https://github.com/zyml"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/artursvonda"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dpsmith3"}, {"name": "<PERSON>", "url": "https://github.com/hperrin"}, {"name": "<PERSON>", "url": "https://github.com/ljharb"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/qs"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "1a8820a6aece2344fa333148c105b71a132db5e68f839c47934a78889cd44574", "typeScriptVersion": "3.2", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-qs-6.9.5-434711bdd49eb5ee69d90c1d67c354a9a8ecb18b-integrity\\node_modules\\@types\\qs\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/qs`\n\n# Summary\nThis package contains type definitions for qs (https://github.com/ljharb/qs).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qs.\n\n### Additional Details\n * Last updated: Wed, 16 Sep 2020 23:05:37 GMT\n * Dependencies: none\n * Global values: `qs`\n\n# Credits\nThese definitions were written by [<PERSON>](https://github.com/RWander), [<PERSON>](https://github.com/leonyu), [<PERSON>](https://github.com/tehbelinda), [<PERSON>](https://github.com/zyml), [<PERSON><PERSON>](https://github.com/arturs<PERSON>da), [<PERSON>](https://github.com/<PERSON>), [<PERSON>](https://github.com/dpsmith3), [<PERSON>](https://github.com/hperrin), and [<PERSON>](https://github.com/ljharb).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/qs/-/qs-6.9.5.tgz#434711bdd49eb5ee69d90c1d67c354a9a8ecb18b", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/qs/-/qs-6.9.5.tgz", "hash": "434711bdd49eb5ee69d90c1d67c354a9a8ecb18b", "integrity": "sha512-/JHkVHtx/REVG0VVToGRGH2+23hsYLHdyG+GrvoUGlGAd0ErauXDyvHtRI/7H7mzLm+tBCKA7pfcpkQ1lf58iQ==", "registry": "npm", "packageName": "@types/qs", "cacheIntegrity": "sha512-/JHkVHtx/REVG0VVToGRGH2+23hsYLHdyG+GrvoUGlGAd0ErauXDyvHtRI/7H7mzLm+tBCKA7pfcpkQ1lf58iQ== sha1-Q0cRvdSete5p2QwdZ8NUqajssYs="}, "registry": "npm", "hash": "434711bdd49eb5ee69d90c1d67c354a9a8ecb18b"}