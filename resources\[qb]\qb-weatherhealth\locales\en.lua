local Translations = {
    info = {
        system_started = 'Weather Health System started',
        system_stopped = 'Weather Health System stopped',
        npc_spawned = 'Weather-aware NPC spawned',
        npc_updated = 'NPC clothing updated for weather',
        player_health_check = 'Player health checked',
        weather_changed = 'Weather changed to %s',
    },
    
    warnings = {
        getting_cold = 'You are getting cold. Consider wearing warmer clothes.',
        getting_hot = 'You are getting hot. Consider wearing lighter clothes.',
        feeling_sick = 'You are not feeling well. You should see a doctor.',
        severe_weather = 'Severe weather conditions! Seek shelter immediately.',
        hypothermia_risk = 'Risk of hypothermia! Find warmth immediately.',
        heatstroke_risk = 'Risk of heatstroke! Find shade and cool down.',
    },
    
    diseases = {
        contracted = 'You have contracted: %s',
        cured = 'You feel better. %s has been cured.',
        cold = 'Common Cold',
        flu = 'Flu',
        heatstroke = 'Heat Stroke',
        hypothermia = 'Hypothermia',
    },
    
    symptoms = {
        cough = 'You are coughing',
        sneeze = 'You sneezed',
        shiver = 'You are shivering from cold',
        sweat = 'You are sweating from heat',
        weakness = 'You feel weak',
        dizziness = 'You feel dizzy',
        fever = 'You have a fever',
    },
    
    treatments = {
        medicine_used = 'You used medicine',
        heatpack_used = 'You used a heat pack',
        coolpack_used = 'You used a cool pack',
        treatment_successful = 'Treatment was successful',
        treatment_failed = 'Treatment failed',
        no_disease = 'You are not sick',
        already_treated = 'You have already been treated recently',
    },
    
    npc = {
        sick_npc = 'This NPC looks sick',
        dead_npc = 'This NPC has died from illness',
        seeking_shelter = 'NPC is seeking shelter from the weather',
        seeking_warmth = 'NPC is seeking warmth',
        changed_clothes = 'NPC changed clothes for the weather',
    },
    
    weather = {
        extrasunny = 'Extra Sunny',
        clear = 'Clear',
        neutral = 'Neutral',
        smog = 'Smoggy',
        foggy = 'Foggy',
        overcast = 'Overcast',
        clouds = 'Cloudy',
        clearing = 'Clearing',
        rain = 'Rainy',
        thunder = 'Thunderstorm',
        snow = 'Snowy',
        blizzard = 'Blizzard',
        snowlight = 'Light Snow',
        xmas = 'Christmas',
        halloween = 'Halloween',
    },
    
    commands = {
        checkhealth = 'Check your current health status',
        treatdisease = 'Treat your current disease',
        weatherinfo = 'Get current weather information',
        npcinfo = 'Get information about nearby NPCs',
    },
    
    errors = {
        no_permission = 'You do not have permission to use this command',
        invalid_item = 'Invalid treatment item',
        not_enough_items = 'You do not have enough items',
        system_error = 'System error occurred',
        database_error = 'Database error occurred',
        npc_not_found = 'NPC not found',
        player_not_found = 'Player not found',
    },
    
    success = {
        health_restored = 'Your health has been restored',
        disease_prevented = 'Disease has been prevented',
        npc_healed = 'NPC has been healed',
        system_reset = 'System has been reset',
        data_saved = 'Data has been saved',
    }
}

Lang = Locale:new({
    phrases = Translations,
    warnOnMissing = true
})
