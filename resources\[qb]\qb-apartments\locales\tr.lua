local Translations = {
    error = {
        to_far_from_door = 'Kapı zilinden çok uzaktasın',
        nobody_home = 'Evde kimse yok..',
    },
    success = {
        receive_apart = 'Bir daire aldın',
        changed_apart = '<PERSON>reni taşıdın',
    },
    info = {
        at_the_door = 'Kap<PERSON>da birisi var!',
    },
    text = {
        enter = 'Daireye Girin',
        ring_doorbell = '<PERSON><PERSON> Çal',
        logout = 'Oturumu Kapat',
        change_outfit = 'Kıyafet Değiştir',
        open_stash = '<PERSON>ulay<PERSON> Aç',
        move_here = 'Buraya Taşın',
        open_door = 'Ka<PERSON><PERSON><PERSON><PERSON> Aç',
        leave = 'Apartmandan Ayrıl',
        close_menu = '⬅ Menüyü Kapat',
        tennants = 'Kiracılar',
    },
}

if GetConvar('qb_locale', 'en') == 'tr' then
    Lang = Locale:new({
        phrases = Translations,
        warnOnMissing = true,
        fallbackLang = Lang,
    })
end
