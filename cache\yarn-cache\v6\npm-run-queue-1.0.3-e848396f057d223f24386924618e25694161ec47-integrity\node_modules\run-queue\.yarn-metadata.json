{"manifest": {"name": "run-queue", "version": "1.0.3", "description": "A promise based, dynamic priority queue runner, with concurrency limiting.", "main": "queue.js", "scripts": {"test": "standard && tap -J test"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "devDependencies": {"standard": "^8.6.0", "tap": "^10.2.0"}, "files": ["queue.js"], "dependencies": {"aproba": "^1.1.1"}, "repository": {"type": "git", "url": "git+https://github.com/iarna/run-queue.git"}, "bugs": {"url": "https://github.com/iarna/run-queue/issues"}, "homepage": "https://npmjs.com/package/run-queue", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-run-queue-1.0.3-e848396f057d223f24386924618e25694161ec47-integrity\\node_modules\\run-queue\\package.json", "readmeFilename": "README.md", "readme": "# run-queue\n\nA promise based, dynamic priority queue runner, with concurrency limiting.\n\n```js\nconst RunQueue = require('run-queue')\n\nconst queue = new RunQueue({\n  maxConcurrency: 1\n})\n\nqueue.add(1, example, [-1])\nfor (let ii = 0; ii < 5; ++ii) {\n  queue.add(0, example, [ii])\n}\nconst finished = []\nqueue.run().then(\n  console.log(finished)\n})\n\nfunction example (num, next) {\n  setTimeout(() => {\n    finished.push(num)\n    next()\n  }, 5 - Math.abs(num))\n}\n```\n\nwould output\n\n```\n[ 0, 1, 2, 3, 4, -1 ]\n```\n\nIf you bump concurrency to `2`, then you get:\n\n```\n[ 1, 0, 3, 2, 4, -1 ]\n```\n\nThe concurrency means that they don't finish in order, because some take\nlonger than others.  Each priority level must finish entirely before the\nnext priority level is run.  See\n[PRIORITIES](https://github.com/iarna/run-queue#priorities) below.  This is\neven true if concurrency is set high enough that all of the regular queue\ncan execute at once, for instance, with `maxConcurrency: 10`:\n\n```\n[ 4, 3, 2, 1, 0, -1 ]\n```\n\n## API\n\n### const queue = new RunQueue(options)\n\nCreate a new queue. Options may contain:\n\n* maxConcurrency - (Default: `1`) The maximum number of jobs to execute at once.\n* Promise - (Default: global.Promise) The promise implementation to use.\n\n### queue.add (prio, fn, args)\n\nAdd a new job to the end of the queue at priority `prio` that will run `fn`\nwith `args`. If `fn` is async then it should return a Promise.\n\n### queue.run ()\n\nStart running the job queue.  Returns a Promise that resolves when either\nall the jobs are complete or a job ends in error (throws or returns a\nrejected promise). If a job ended in error then this Promise will be rejected\nwith that error and no further queue running will be done.\n\n## PRIORITIES\n\nPriorities are any integer value >= 0.\n\nLowest is executed first.\n\nPriorities essentially represent distinct job queues.  All jobs in a queue\nmust complete before the next highest priority job queue is executed.\n\nThis means that if you have two queues, `0` and `1` then ALL jobs in `0`\nmust complete before ANY execute in `1`.  If you add new `0` level jobs\nwhile `1` level jobs are running then it will switch back processing the `0`\nqueue and won't execute any more `1` jobs till all of the new `0` jobs\ncomplete.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/run-queue/-/run-queue-1.0.3.tgz#e848396f057d223f24386924618e25694161ec47", "type": "tarball", "reference": "https://registry.yarnpkg.com/run-queue/-/run-queue-1.0.3.tgz", "hash": "e848396f057d223f24386924618e25694161ec47", "integrity": "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=", "registry": "npm", "packageName": "run-queue", "cacheIntegrity": "sha512-ntymy489o0/QQplUDnpYAYUsO50K9SBrIVaKCWDOJzYJts0f9WH9RFJkyagebkw5+y1oi00R7ynNW/d12GBumg== sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec="}, "registry": "npm", "hash": "e848396f057d223f24386924618e25694161ec47"}