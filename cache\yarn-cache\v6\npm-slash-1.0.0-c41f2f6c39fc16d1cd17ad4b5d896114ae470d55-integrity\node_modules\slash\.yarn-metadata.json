{"manifest": {"name": "slash", "version": "1.0.0", "description": "Convert Windows backslash paths to slash paths", "keywords": ["path", "seperator", "sep", "slash", "backslash", "windows", "win"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "repository": {"type": "git", "url": "https://github.com/sindresorhus/slash.git"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "license": "MIT", "files": ["index.js"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-slash-1.0.0-c41f2f6c39fc16d1cd17ad4b5d896114ae470d55-integrity\\node_modules\\slash\\package.json", "readmeFilename": "readme.md", "readme": "# slash [![Build Status](https://travis-ci.org/sindresorhus/slash.svg?branch=master)](https://travis-ci.org/sindresorhus/slash)\n\n> Convert Windows backslash paths to slash paths: `foo\\\\bar` ➔ `foo/bar`\n\n[Forward-slash paths can be used in Windows](http://superuser.com/a/176395/6877) as long as they're not extended-length paths and don't contain any non-ascii characters.\n\nThis was created since the `path` methods in Node outputs `\\\\` paths on Windows.\n\n\n## Install\n\n```sh\n$ npm install --save slash\n```\n\n\n## Usage\n\n```js\nvar path = require('path');\nvar slash = require('slash');\n\nvar str = path.join('foo', 'bar');\n// Unix    => foo/bar\n// Windows => foo\\\\bar\n\nslash(str);\n// Unix    => foo/bar\n// Windows => foo/bar\n```\n\n\n## API\n\n### slash(path)\n\nType: `string`\n\nAccepts a Windows backslash path and returns a slash path.\n\n\n## License\n\nMIT © [Sindre Sorhus](http://sindresorhus.com)\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/slash/-/slash-1.0.0.tgz#c41f2f6c39fc16d1cd17ad4b5d896114ae470d55", "type": "tarball", "reference": "https://registry.yarnpkg.com/slash/-/slash-1.0.0.tgz", "hash": "c41f2f6c39fc16d1cd17ad4b5d896114ae470d55", "integrity": "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=", "registry": "npm", "packageName": "slash", "cacheIntegrity": "sha512-3TYDR7xWt4dIqV2JauJr+EJeW356RXijHeUlO+8djJ+uBXPn8/2dpzBc8yQhh583sVvc9CvFAeQVgijsH+PNNg== sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU="}, "registry": "npm", "hash": "c41f2f6c39fc16d1cd17ad4b5d896114ae470d55"}