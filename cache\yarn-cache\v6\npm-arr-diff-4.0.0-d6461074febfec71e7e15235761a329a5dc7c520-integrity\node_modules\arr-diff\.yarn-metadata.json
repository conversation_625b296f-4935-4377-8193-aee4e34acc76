{"manifest": {"name": "arr-diff", "description": "Returns an array with only the unique values from the first array, by excluding all values from additional arrays using strict equality for comparisons.", "version": "4.0.0", "homepage": "https://github.com/jonschlinkert/arr-diff", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "paulmillr.com"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/arr-diff.git"}, "bugs": {"url": "https://github.com/jonschlinkert/arr-diff/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {}, "devDependencies": {"ansi-bold": "^0.1.1", "arr-flatten": "^1.0.1", "array-differ": "^1.0.0", "benchmarked": "^0.2.4", "gulp-format-md": "^0.1.9", "minimist": "^1.2.0", "mocha": "^2.4.5"}, "keywords": ["arr", "array", "array differ", "array-differ", "diff", "differ", "difference"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["arr-flatten", "array-filter", "array-intersection"]}, "reflinks": ["array-differ", "verb"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-arr-diff-4.0.0-d6461074febfec71e7e15235761a329a5dc7c520-integrity\\node_modules\\arr-diff\\package.json", "readmeFilename": "README.md", "readme": "# arr-diff [![NPM version](https://img.shields.io/npm/v/arr-diff.svg?style=flat)](https://www.npmjs.com/package/arr-diff) [![NPM monthly downloads](https://img.shields.io/npm/dm/arr-diff.svg?style=flat)](https://npmjs.org/package/arr-diff) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/arr-diff.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/arr-diff)\n\n> Returns an array with only the unique values from the first array, by excluding all values from additional arrays using strict equality for comparisons.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save arr-diff\n```\n\nInstall with [yarn](https://yarnpkg.com):\n\n```sh\n$ yarn add arr-diff\n```\n\nInstall with [bower](https://bower.io/)\n\n```sh\n$ bower install arr-diff --save\n```\n\n## Usage\n\nReturns the difference between the first array and additional arrays.\n\n```js\nvar diff = require('arr-diff');\n\nvar a = ['a', 'b', 'c', 'd'];\nvar b = ['b', 'c'];\n\nconsole.log(diff(a, b))\n//=> ['a', 'd']\n```\n\n## Benchmarks\n\nThis library versus [array-differ](https://github.com/sindresorhus/array-differ), on April 14, 2017:\n\n```\nBenchmarking: (4 of 4)\n · long-dupes\n · long\n · med\n · short\n\n# benchmark/fixtures/long-dupes.js (100804 bytes)\n  arr-diff-3.0.0 x 822 ops/sec ±0.67% (86 runs sampled)\n  arr-diff-4.0.0 x 2,141 ops/sec ±0.42% (89 runs sampled)\n  array-differ x 708 ops/sec ±0.70% (89 runs sampled)\n\n  fastest is arr-diff-4.0.0\n\n# benchmark/fixtures/long.js (94529 bytes)\n  arr-diff-3.0.0 x 882 ops/sec ±0.60% (87 runs sampled)\n  arr-diff-4.0.0 x 2,329 ops/sec ±0.97% (83 runs sampled)\n  array-differ x 769 ops/sec ±0.61% (90 runs sampled)\n\n  fastest is arr-diff-4.0.0\n\n# benchmark/fixtures/med.js (708 bytes)\n  arr-diff-3.0.0 x 856,150 ops/sec ±0.42% (89 runs sampled)\n  arr-diff-4.0.0 x 4,665,249 ops/sec ±1.06% (89 runs sampled)\n  array-differ x 653,888 ops/sec ±1.02% (86 runs sampled)\n\n  fastest is arr-diff-4.0.0\n\n# benchmark/fixtures/short.js (60 bytes)\n  arr-diff-3.0.0 x 3,078,467 ops/sec ±0.77% (93 runs sampled)\n  arr-diff-4.0.0 x 9,213,296 ops/sec ±0.65% (89 runs sampled)\n  array-differ x 1,337,051 ops/sec ±0.91% (92 runs sampled)\n\n  fastest is arr-diff-4.0.0\n```\n\n## About\n\n### Related projects\n\n* [arr-flatten](https://www.npmjs.com/package/arr-flatten): Recursively flatten an array or arrays. This is the fastest implementation of array flatten. | [homepage](https://github.com/jonschlinkert/arr-flatten \"Recursively flatten an array or arrays. This is the fastest implementation of array flatten.\")\n* [array-filter](https://www.npmjs.com/package/array-filter): Array#filter for older browsers. | [homepage](https://github.com/juliangruber/array-filter \"Array#filter for older browsers.\")\n* [array-intersection](https://www.npmjs.com/package/array-intersection): Return an array with the unique values present in _all_ given arrays using strict equality… [more](https://github.com/jonschlinkert/array-intersection) | [homepage](https://github.com/jonschlinkert/array-intersection \"Return an array with the unique values present in _all_ given arrays using strict equality for comparisons.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 33 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 2 | [paulmillr](https://github.com/paulmillr) |\n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.5.0, on April 14, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/arr-diff/-/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520", "type": "tarball", "reference": "https://registry.yarnpkg.com/arr-diff/-/arr-diff-4.0.0.tgz", "hash": "d6461074febfec71e7e15235761a329a5dc7c520", "integrity": "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=", "registry": "npm", "packageName": "arr-diff", "cacheIntegrity": "sha512-YVIQ82gZPGBebQV/a8dar4AitzCQs0jjXwMPZllpXMaGjXPYVUawSxQrRsjhjupyVxEvbHgUmIhKVlND+j02kA== sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA="}, "registry": "npm", "hash": "d6461074febfec71e7e15235761a329a5dc7c520"}