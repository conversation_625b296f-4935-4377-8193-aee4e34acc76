{"manifest": {"name": "cookies", "description": "Cookies, optionally signed using Keygrip.", "version": "0.8.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jed.is"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pillarjs/cookies.git"}, "dependencies": {"depd": "~2.0.0", "keygrip": "~1.1.0"}, "devDependencies": {"eslint": "4.19.1", "express": "4.17.1", "mocha": "6.2.1", "nyc": "14.1.1", "restify": "8.4.0", "supertest": "4.0.2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-cookies-0.8.0-1293ce4b391740a8406e3c9870e828c4b54f3f90-integrity\\node_modules\\cookies\\package.json", "readmeFilename": "README.md", "readme": "Cookies\n=======\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nCookies is a [node.js](http://nodejs.org/) module for getting and setting HTTP(S) cookies. Cookies can be signed to prevent tampering, using [Keygrip](https://www.npmjs.com/package/keygrip). It can be used with the built-in node.js HTTP library, or as Connect/Express middleware.\n\n## Install\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```\n$ npm install cookies\n```\n\n## Features\n\n* **Lazy**: Since cookie verification against multiple keys could be expensive, cookies are only verified lazily when accessed, not eagerly on each request.\n\n* **Secure**: All cookies are `httponly` by default, and cookies sent over SSL are `secure` by default. An error will be thrown if you try to send secure cookies over an insecure socket.\n\n* **Unobtrusive**: Signed cookies are stored the same way as unsigned cookies, instead of in an obfuscated signing format. An additional signature cookie is stored for each signed cookie, using a standard naming convention (_cookie-name_`.sig`). This allows other libraries to access the original cookies without having to know the signing mechanism.\n\n* **Agnostic**: This library is optimized for use with [Keygrip](https://www.npmjs.com/package/keygrip), but does not require it; you can implement your own signing scheme instead if you like and use this library only to read/write cookies. Factoring the signing into a separate library encourages code reuse and allows you to use the same signing library for other areas where signing is needed, such as in URLs.\n\n## API\n\n### cookies = new Cookies( request, response, [ options ] )\n\nThis creates a cookie jar corresponding to the current _request_ and _response_, additionally passing an object _options_.\n\nA [Keygrip](https://www.npmjs.com/package/keygrip) object or an array of keys can optionally be passed as _options.keys_ to enable cryptographic signing based on SHA1 HMAC, using rotated credentials.\n\nA Boolean can optionally be passed as _options.secure_ to explicitally specify if the connection is secure, rather than this module examining _request_.\n\nNote that since this only saves parameters without any other processing, it is very lightweight. Cookies are only parsed on demand when they are accessed.\n\n### express.createServer( Cookies.express( keys ) )\n\nThis adds cookie support as a Connect middleware layer for use in Express apps, allowing inbound cookies to be read using `req.cookies.get` and outbound cookies to be set using `res.cookies.set`.\n\n### cookies.get( name, [ options ] )\n\nThis extracts the cookie with the given name from the `Cookie` header in the request. If such a cookie exists, its value is returned. Otherwise, nothing is returned.\n\n`{ signed: true }` can optionally be passed as the second parameter _options_. In this case, a signature cookie (a cookie of same name ending with the `.sig` suffix appended) is fetched. If no such cookie exists, nothing is returned.\n\nIf the signature cookie _does_ exist, the provided [Keygrip](https://www.npmjs.com/package/keygrip) object is used to check whether the hash of _cookie-name_=_cookie-value_ matches that of any registered key:\n\n* If the signature cookie hash matches the first key, the original cookie value is returned.\n* If the signature cookie hash matches any other key, the original cookie value is returned AND an outbound header is set to update the signature cookie's value to the hash of the first key. This enables automatic freshening of signature cookies that have become stale due to key rotation.\n* If the signature cookie hash does not match any key, nothing is returned, and an outbound header with an expired date is used to delete the cookie.\n\n### cookies.set( name, [ value ], [ options ] )\n\nThis sets the given cookie in the response and returns the current context to allow chaining.\n\nIf the _value_ is omitted, an outbound header with an expired date is used to delete the cookie.\n\nIf the _options_ object is provided, it will be used to generate the outbound cookie header as follows:\n\n* `maxAge`: a number representing the milliseconds from `Date.now()` for expiry\n* `expires`: a `Date` object indicating the cookie's expiration date (expires at the end of session by default).\n* `path`: a string indicating the path of the cookie (`/` by default).\n* `domain`: a string indicating the domain of the cookie (no default).\n* `secure`: a boolean indicating whether the cookie is only to be sent over HTTPS (`false` by default for HTTP, `true` by default for HTTPS). [Read more about this option below](#secure-cookies).\n* `httpOnly`: a boolean indicating whether the cookie is only to be sent over HTTP(S), and not made available to client JavaScript (`true` by default).\n* `sameSite`: a boolean or string indicating whether the cookie is a \"same site\" cookie (`false` by default). This can be set to `'strict'`, `'lax'`, or `true` (which maps to `'strict'`).\n* `signed`: a boolean indicating whether the cookie is to be signed (`false` by default). If this is true, another cookie of the same name with the `.sig` suffix appended will also be sent, with a 27-byte url-safe base64 SHA1 value representing the hash of _cookie-name_=_cookie-value_ against the first [Keygrip](https://www.npmjs.com/package/keygrip) key. This signature key is used to detect tampering the next time a cookie is received.\n* `overwrite`: a boolean indicating whether to overwrite previously set cookies of the same name (`false` by default). If this is true, all cookies set during the same request with the same name (regardless of path or domain) are filtered out of the Set-Cookie header when setting this cookie.\n\n### Secure cookies\n\nTo send a secure cookie, you set a cookie with the `secure: true` option.\n\nHTTPS is necessary for secure cookies. When `cookies.set` is called with `secure: true` and a secure connection is not detected, the cookie will not be set and an error will be thrown.\n\nThis module will test each request to see if it's secure by checking:\n\n* if the `protocol` property of the request is set to `https`, or\n* if the `connection.encrypted` property of the request is set to `true`.\n\nIf your server is running behind a proxy and you are using `secure: true`, you need to configure your server to read the request headers added by your proxy to determine whether the request is using a secure connection.\n\nFor more information about working behind proxies, consult the framework you are using:\n\n* For Koa - [`app.proxy = true`](http://koajs.com/#settings)\n* For Express - [trust proxy setting](http://expressjs.com/en/4x/api.html#trust.proxy.options.table)\n\nIf your Koa or Express server is properly configured, the `protocol` property of the request will be set to match the protocol reported by the proxy in the `X-Forwarded-Proto` header.\n\n## Example\n\n```js\nvar http = require('http')\nvar Cookies = require('cookies')\n\n// Optionally define keys to sign cookie values\n// to prevent client tampering\nvar keys = ['keyboard cat']\n\nvar server = http.createServer(function (req, res) {\n  // Create a cookies object\n  var cookies = new Cookies(req, res, { keys: keys })\n\n  // Get a cookie\n  var lastVisit = cookies.get('LastVisit', { signed: true })\n\n  // Set the cookie to a value\n  cookies.set('LastVisit', new Date().toISOString(), { signed: true })\n\n  if (!lastVisit) {\n    res.setHeader('Content-Type', 'text/plain')\n    res.end('Welcome, first time visitor!')\n  } else {\n    res.setHeader('Content-Type', 'text/plain')\n    res.end('Welcome back! Nothing much changed since your last visit at ' + lastVisit + '.')\n  }\n})\n\nserver.listen(3000, function () {\n  console.log('Visit us at http://127.0.0.1:3000/ !')\n})\n```\n\n## License\n\n[MIT](LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/cookies.svg\n[npm-url]: https://npmjs.org/package/cookies\n[coveralls-image]: https://img.shields.io/coveralls/pillarjs/cookies/master.svg\n[coveralls-url]: https://coveralls.io/r/pillarjs/cookies?branch=master\n[downloads-image]: https://img.shields.io/npm/dm/cookies.svg\n[downloads-url]: https://npmjs.org/package/cookies\n[node-version-image]: https://img.shields.io/node/v/cookies.svg\n[node-version-url]: https://nodejs.org/en/download/\n[travis-image]: https://img.shields.io/travis/pillarjs/cookies/master.svg\n[travis-url]: https://travis-ci.org/pillarjs/cookies\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2014 <PERSON>, http://jed.is/\nCopyright (c) 2015-2016 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/cookies/-/cookies-0.8.0.tgz#1293ce4b391740a8406e3c9870e828c4b54f3f90", "type": "tarball", "reference": "https://registry.yarnpkg.com/cookies/-/cookies-0.8.0.tgz", "hash": "1293ce4b391740a8406e3c9870e828c4b54f3f90", "integrity": "sha512-8aPsApQfebXnuI+537McwYsDtjVxGm8gTIzQI3FDW6t5t/DAhERxtnbEPN/8RX+uZthoz4eCOgloXaE5cYyNow==", "registry": "npm", "packageName": "cookies", "cacheIntegrity": "sha512-8aPsApQfebXnuI+537McwYsDtjVxGm8gTIzQI3FDW6t5t/DAhERxtnbEPN/8RX+uZthoz4eCOgloXaE5cYyNow== sha1-EpPOSzkXQKhAbjyYcOgoxLVPP5A="}, "registry": "npm", "hash": "1293ce4b391740a8406e3c9870e828c4b54f3f90"}