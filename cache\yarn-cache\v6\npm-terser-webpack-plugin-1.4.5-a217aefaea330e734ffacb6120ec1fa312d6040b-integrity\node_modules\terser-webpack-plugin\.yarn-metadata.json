{"manifest": {"name": "terser-webpack-plugin", "version": "1.4.5", "description": "Terser plugin for webpack", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/webpack-contrib/terser-webpack-plugin.git"}, "author": {"name": "webpack Contrib Team"}, "homepage": "https://github.com/webpack-contrib/terser-webpack-plugin", "bugs": {"url": "https://github.com/webpack-contrib/terser-webpack-plugin/issues"}, "main": "dist/cjs.js", "engines": {"node": ">= 6.9.0"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:js": "eslint --cache src test", "lint": "npm-run-all -l -p \"lint:**\"", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test npm run test:coverage", "defaults": "webpack-defaults"}, "files": ["dist"], "peerDependencies": {"webpack": "^4.0.0"}, "dependencies": {"cacache": "^12.0.2", "find-cache-dir": "^2.1.0", "is-wsl": "^1.1.0", "schema-utils": "^1.0.0", "serialize-javascript": "^4.0.0", "source-map": "^0.6.1", "terser": "^4.1.2", "webpack-sources": "^1.4.0", "worker-farm": "^1.7.0"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/preset-env": "^7.5.5", "@commitlint/cli": "^8.1.0", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/defaults": "^5.0.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.8.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^5.2.0", "del": "^4.1.1", "del-cli": "^1.1.0", "eslint": "^6.1.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-import": "^2.18.2", "husky": "^3.0.2", "jest": "^24.8.0", "jest-junit": "^7.0.0", "lint-staged": "^9.2.1", "memory-fs": "^0.4.1", "npm-run-all": "^4.1.5", "prettier": "^1.18.2", "standard-version": "^7.0.0", "uglify-js": "^3.6.0", "webpack": "^4.38.0"}, "keywords": ["uglify", "uglify-js", "uglify-es", "terser", "webpack", "webpack-plugin", "minification", "compress", "compressor", "min", "minification", "minifier", "minify", "optimize", "optimizer"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-terser-webpack-plugin-1.4.5-a217aefaea330e734ffacb6120ec1fa312d6040b-integrity\\node_modules\\terser-webpack-plugin\\package.json", "readmeFilename": "README.md", "readme": "<div align=\"center\">\n  <a href=\"https://github.com/webpack/webpack\">\n    <img width=\"200\" height=\"200\" src=\"https://webpack.js.org/assets/icon-square-big.svg\">\n  </a>\n</div>\n\n[![npm][npm]][npm-url]\n[![node][node]][node-url]\n[![deps][deps]][deps-url]\n[![tests][tests]][tests-url]\n[![cover][cover]][cover-url]\n[![chat][chat]][chat-url]\n[![size][size]][size-url]\n\n# terser-webpack-plugin\n\nThis plugin uses [terser](https://github.com/terser-js/terser) to minify your JavaScript.\n\n> ℹ️ For `webpack@3` use [terser-webpack-plugin-legacy](https://www.npmjs.com/package/terser-webpack-plugin-legacy) package\n\n## Getting Started\n\nTo begin, you'll need to install `terser-webpack-plugin`:\n\n```console\n$ npm install terser-webpack-plugin --save-dev\n```\n\nThen add the plugin to your `webpack` config. For example:\n\n**webpack.config.js**\n\n```js\nconst TerserPlugin = require('terser-webpack-plugin');\n\nmodule.exports = {\n  optimization: {\n    minimizer: [new TerserPlugin()],\n  },\n};\n```\n\nAnd run `webpack` via your preferred method.\n\n## Options\n\n### `test`\n\nType: `String|RegExp|Array<String|RegExp>`\nDefault: `/\\.m?js(\\?.*)?$/i`\n\nTest to match files against.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        test: /\\.js(\\?.*)?$/i,\n      }),\n    ],\n  },\n};\n```\n\n### `include`\n\nType: `String|RegExp|Array<String|RegExp>`\nDefault: `undefined`\n\nFiles to include.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        include: /\\/includes/,\n      }),\n    ],\n  },\n};\n```\n\n### `exclude`\n\nType: `String|RegExp|Array<String|RegExp>`\nDefault: `undefined`\n\nFiles to exclude.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        exclude: /\\/excludes/,\n      }),\n    ],\n  },\n};\n```\n\n### `chunkFilter`\n\nType: `Function<(chunk) -> boolean>`\nDefault: `() => true`\n\nAllowing to filter which chunks should be uglified (by default all chunks are uglified).\nReturn `true` to uglify the chunk, `false` otherwise.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        chunkFilter: (chunk) => {\n          // Exclude uglification for the `vendor` chunk\n          if (chunk.name === 'vendor') {\n            return false;\n          }\n\n          return true;\n        },\n      }),\n    ],\n  },\n};\n```\n\n### `cache`\n\nType: `Boolean|String`\nDefault: `false`\n\nEnable file caching.\nDefault path to cache directory: `node_modules/.cache/terser-webpack-plugin`.\n\n> ℹ️ If you use your own `minify` function please read the `minify` section for cache invalidation correctly.\n\n#### `Boolean`\n\nEnable/disable file caching.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        cache: true,\n      }),\n    ],\n  },\n};\n```\n\n#### `String`\n\nEnable file caching and set path to cache directory.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        cache: 'path/to/cache',\n      }),\n    ],\n  },\n};\n```\n\n### `cacheKeys`\n\nType: `Function<(defaultCacheKeys, file) -> Object>`\nDefault: `defaultCacheKeys => defaultCacheKeys`\n\nAllows you to override default cache keys.\n\nDefault cache keys:\n\n```js\n({\n  terser: require('terser/package.json').version, // terser version\n  'terser-webpack-plugin': require('../package.json').version, // plugin version\n  'terser-webpack-plugin-options': this.options, // plugin options\n  path: compiler.outputPath ? `${compiler.outputPath}/${file}` : file, // asset path\n  hash: crypto\n    .createHash('md4')\n    .update(input)\n    .digest('hex'), // source file hash\n});\n```\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        cache: true,\n        cacheKeys: (defaultCacheKeys, file) => {\n          defaultCacheKeys.myCacheKey = 'myCacheKeyValue';\n\n          return defaultCacheKeys;\n        },\n      }),\n    ],\n  },\n};\n```\n\n### `parallel`\n\nType: `Boolean|Number`\nDefault: `false`\n\nUse multi-process parallel running to improve the build speed.\nDefault number of concurrent runs: `os.cpus().length - 1`.\n\n> ℹ️ Parallelization can speedup your build significantly and is therefore **highly recommended**.\n\n#### `Boolean`\n\nEnable/disable multi-process parallel running.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        parallel: true,\n      }),\n    ],\n  },\n};\n```\n\n#### `Number`\n\nEnable multi-process parallel running and set number of concurrent runs.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        parallel: 4,\n      }),\n    ],\n  },\n};\n```\n\n### `sourceMap`\n\nType: `Boolean`\nDefault: `false`\n\nUse source maps to map error message locations to modules (this slows down the compilation).\nIf you use your own `minify` function please read the `minify` section for handling source maps correctly.\n\n> ⚠️ **`cheap-source-map` options don't work with this plugin**.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        sourceMap: true,\n      }),\n    ],\n  },\n};\n```\n\n### `minify`\n\nType: `Function`\nDefault: `undefined`\n\nAllows you to override default minify function.\nBy default plugin uses [terser](https://github.com/terser-js/terser) package.\nUseful for using and testing unpublished versions or forks.\n\n> ⚠️ **Always use `require` inside `minify` function when `parallel` option enabled**.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        minify: (file, sourceMap) => {\n          const extractedComments = [];\n\n          // Custom logic for extract comments\n\n          const { error, map, code, warnings } = require('uglify-module') // Or require('./path/to/uglify-module')\n            .minify(file, {\n              /* Your options for minification */\n            });\n\n          return { error, map, code, warnings, extractedComments };\n        },\n      }),\n    ],\n  },\n};\n```\n\n### `terserOptions`\n\nType: `Object`\nDefault: [default](https://github.com/terser-js/terser#minify-options)\n\nTerser minify [options](https://github.com/terser-js/terser#minify-options).\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        terserOptions: {\n          ecma: undefined,\n          warnings: false,\n          parse: {},\n          compress: {},\n          mangle: true, // Note `mangle.properties` is `false` by default.\n          module: false,\n          output: null,\n          toplevel: false,\n          nameCache: null,\n          ie8: false,\n          keep_classnames: undefined,\n          keep_fnames: false,\n          safari10: false,\n        },\n      }),\n    ],\n  },\n};\n```\n\n### `extractComments`\n\nType: `Boolean|String|RegExp|Function<(node, comment) -> Boolean|Object>|Object`\nDefault: `false`\n\nWhether comments shall be extracted to a separate file, (see [details](https://github.com/webpack/webpack/commit/71933e979e51c533b432658d5e37917f9e71595a)).\nBy default extract only comments using `/^\\**!|@preserve|@license|@cc_on/i` regexp condition and remove remaining comments.\nIf the original file is named `foo.js`, then the comments will be stored to `foo.js.LICENSE`.\nThe `terserOptions.output.comments` option specifies whether the comment will be preserved, i.e. it is possible to preserve some comments (e.g. annotations) while extracting others or even preserving comments that have been extracted.\n\n#### `Boolean`\n\nEnable/disable extracting comments.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        extractComments: true,\n      }),\n    ],\n  },\n};\n```\n\n#### `String`\n\nExtract `all` or `some` (use `/^\\**!|@preserve|@license|@cc_on/i` RegExp) comments.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        extractComments: 'all',\n      }),\n    ],\n  },\n};\n```\n\n#### `RegExp`\n\nAll comments that match the given expression will be extracted to the separate file.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        extractComments: /@extract/i,\n      }),\n    ],\n  },\n};\n```\n\n#### `Function<(node, comment) -> Boolean>`\n\nAll comments that match the given expression will be extracted to the separate file.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        extractComments: (astNode, comment) => {\n          if (/@extract/i.test(comment.value)) {\n            return true;\n          }\n\n          return false;\n        },\n      }),\n    ],\n  },\n};\n```\n\n#### `Object`\n\nAllow to customize condition for extract comments, specify extracted file name and banner.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        extractComments: {\n          condition: /^\\**!|@preserve|@license|@cc_on/i,\n          filename: (file) => {\n            return `${file}.LICENSE`;\n          },\n          banner: (licenseFile) => {\n            return `License information can be found in ${licenseFile}`;\n          },\n        },\n      }),\n    ],\n  },\n};\n```\n\n##### `condition`\n\nType: `Boolean|String|RegExp|Function<(node, comment) -> Boolean|Object>`\n\nCondition what comments you need extract.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        extractComments: {\n          condition: 'some',\n          filename: (file) => {\n            return `${file}.LICENSE`;\n          },\n          banner: (licenseFile) => {\n            return `License information can be found in ${licenseFile}`;\n          },\n        },\n      }),\n    ],\n  },\n};\n```\n\n##### `filename`\n\nType: `String|Function<(string) -> String>`\nDefault: `${file}.LICENSE`\n\nThe file where the extracted comments will be stored.\nDefault is to append the suffix `.LICENSE` to the original filename.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        extractComments: {\n          condition: /^\\**!|@preserve|@license|@cc_on/i,\n          filename: 'extracted-comments.js',\n          banner: (licenseFile) => {\n            return `License information can be found in ${licenseFile}`;\n          },\n        },\n      }),\n    ],\n  },\n};\n```\n\n##### `banner`\n\nType: `Boolean|String|Function<(string) -> String>`\nDefault: `/*! For license information please see ${commentsFile} */`\n\nThe banner text that points to the extracted file and will be added on top of the original file.\nCan be `false` (no banner), a `String`, or a `Function<(string) -> String>` that will be called with the filename where extracted comments have been stored.\nWill be wrapped into comment.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        extractComments: {\n          condition: true,\n          filename: (file) => {\n            return `${file}.LICENSE`;\n          },\n          banner: (commentsFile) => {\n            return `My custom banner about license information ${commentsFile}`;\n          },\n        },\n      }),\n    ],\n  },\n};\n```\n\n### `warningsFilter`\n\nType: `Function<(warning, source) -> Boolean>`\nDefault: `() => true`\n\nAllow to filter [terser](https://github.com/terser-js/terser) warnings.\nReturn `true` to keep the warning, `false` otherwise.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        warningsFilter: (warning, source) => {\n          if (/Dropping unreachable code/i.test(warning)) {\n            return true;\n          }\n\n          if (/filename\\.js/i.test(source)) {\n            return true;\n          }\n\n          return false;\n        },\n      }),\n    ],\n  },\n};\n```\n\n## Examples\n\n### Cache And Parallel\n\nEnable cache and multi-process parallel running.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        cache: true,\n        parallel: true,\n      }),\n    ],\n  },\n};\n```\n\n### Preserve Comments\n\nExtract all legal comments (i.e. `/^\\**!|@preserve|@license|@cc_on/i`) and preserve `/@license/i` comments.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        terserOptions: {\n          output: {\n            comments: /@license/i,\n          },\n        },\n        extractComments: true,\n      }),\n    ],\n  },\n};\n```\n\n### Remove Comments\n\nIf you avoid building with comments, set **terserOptions.output.comments** to **false** as in this config:\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        terserOptions: {\n          output: {\n            comments: false,\n          },\n        },\n      }),\n    ],\n  },\n};\n```\n\n### Custom Minify Function\n\nOverride default minify function - use `uglify-js` for minification.\n\n**webpack.config.js**\n\n```js\nmodule.exports = {\n  optimization: {\n    minimizer: [\n      new TerserPlugin({\n        // Uncomment lines below for cache invalidation correctly\n        // cache: true,\n        // cacheKeys: (defaultCacheKeys) => {\n        //   delete defaultCacheKeys.terser;\n        //\n        //   return Object.assign(\n        //     {},\n        //     defaultCacheKeys,\n        //     { 'uglify-js': require('uglify-js/package.json').version },\n        //   );\n        // },\n        minify: (file, sourceMap) => {\n          // https://github.com/mishoo/UglifyJS2#minify-options\n          const uglifyJsOptions = {\n            /* your `uglify-js` package options */\n          };\n\n          if (sourceMap) {\n            uglifyJsOptions.sourceMap = {\n              content: sourceMap,\n            };\n          }\n\n          return require('uglify-js').minify(file, uglifyJsOptions);\n        },\n      }),\n    ],\n  },\n};\n```\n\n## Contributing\n\nPlease take a moment to read our contributing guidelines if you haven't yet done so.\n\n[CONTRIBUTING](./.github/CONTRIBUTING.md)\n\n## License\n\n[MIT](./LICENSE)\n\n[npm]: https://img.shields.io/npm/v/terser-webpack-plugin.svg\n[npm-url]: https://npmjs.com/package/terser-webpack-plugin\n[node]: https://img.shields.io/node/v/terser-webpack-plugin.svg\n[node-url]: https://nodejs.org\n[deps]: https://david-dm.org/webpack-contrib/terser-webpack-plugin.svg\n[deps-url]: https://david-dm.org/webpack-contrib/terser-webpack-plugin\n[tests]: https://dev.azure.com/webpack-contrib/terser-webpack-plugin/_apis/build/status/webpack-contrib.terser-webpack-plugin?branchName=master\n[tests-url]: https://dev.azure.com/webpack-contrib/terser-webpack-plugin/_build/latest?definitionId=7&branchName=master\n[cover]: https://codecov.io/gh/webpack-contrib/terser-webpack-plugin/branch/master/graph/badge.svg\n[cover-url]: https://codecov.io/gh/webpack-contrib/terser-webpack-plugin\n[chat]: https://img.shields.io/badge/gitter-webpack%2Fwebpack-brightgreen.svg\n[chat-url]: https://gitter.im/webpack/webpack\n[size]: https://packagephobia.now.sh/badge?p=terser-webpack-plugin\n[size-url]: https://packagephobia.now.sh/result?p=terser-webpack-plugin\n", "licenseText": "Copyright JS Foundation and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/terser-webpack-plugin/-/terser-webpack-plugin-1.4.5.tgz#a217aefaea330e734ffacb6120ec1fa312d6040b", "type": "tarball", "reference": "https://registry.yarnpkg.com/terser-webpack-plugin/-/terser-webpack-plugin-1.4.5.tgz", "hash": "a217aefaea330e734ffacb6120ec1fa312d6040b", "integrity": "sha512-04Rfe496lN8EYruwi6oPQkG0vo8C+HT49X687FZnpPF0qMAIHONI6HEXYPKDOE8e5HjXTyKfqRd/agHtH0kOtw==", "registry": "npm", "packageName": "terser-webpack-plugin", "cacheIntegrity": "sha512-04Rfe496lN8EYruwi6oPQkG0vo8C+HT49X687FZnpPF0qMAIHONI6HEXYPKDOE8e5HjXTyKfqRd/agHtH0kOtw== sha1-oheu+uozDnNP+sthIOwfoxLWBAs="}, "registry": "npm", "hash": "a217aefaea330e734ffacb6120ec1fa312d6040b"}