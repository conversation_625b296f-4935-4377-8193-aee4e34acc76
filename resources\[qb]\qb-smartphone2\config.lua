Config = {}

-- Základní nastavení
Config.OpenKey = 288 -- Klávesa pro otevření telefonu (F1 = 288)
Config.PhoneItem = 'phone' -- Název itemu v inventáři
Config.MaxContacts = 50 -- Maximální počet kontaktů
Config.MaxMessages = 100 -- Maximální počet zpráv na konverzaci
Config.MaxCallHistory = 50 -- Maximální počet záznamů v historii hovorů

-- Baterie systém
Config.BatteryEnabled = true
Config.BatteryDrainRate = 1 -- Procenta za minutu při používání
Config.BatteryDrainIdle = 0.1 -- Procenta za minutu při nečinnosti
Config.ChargerItem = 'phone_charger' -- Item pro nabíjení

-- Zvuky a notifikace
Config.NotificationSound = true
Config.VibrationEnabled = true
Config.RingtoneVolume = 0.5

-- Fotoaparát
Config.CameraEnabled = true
Config.MaxPhotos = 50
Config.PhotoQuality = 85 -- JP<PERSON> kvalita (1-100)
Config.PhotoFormat = 'jpg'

-- GPS a navigace
Config.GPSEnabled = true
Config.BlipSprite = 1
Config.BlipColor = 3
Config.RouteColor = 3

-- DarkWeb
Config.DarkWebEnabled = true
Config.DarkWebItem = 'crypto_usb' -- Item potřebný pro přístup
Config.DarkWebCurrency = 'crypto' -- Měna pro nákupy

-- Sociální síť
Config.SocialEnabled = true
Config.MaxPostLength = 280
Config.MaxCommentLength = 140
Config.PostCooldown = 30 -- Sekundy mezi posty

-- Marketplace
Config.MarketplaceEnabled = true
Config.MaxAds = 10 -- Maximální počet inzerátů na hráče
Config.AdDuration = 7 -- Dny, po které inzerát vydrží
Config.AdFee = 100 -- Poplatek za inzerát

-- Banking integrace
Config.BankingEnabled = true
Config.QRPaymentEnabled = true
Config.MaxTransferAmount = 50000

-- Job integrace
Config.JobIntegration = {
    police = {
        canReadMessages = true,
        canTrackLocation = true,
        canAccessCallHistory = true
    },
    ambulance = {
        canTrackLocation = true
    }
}

-- Bluetooth chat
Config.BluetoothEnabled = true
Config.BluetoothRange = 10.0 -- Metry

-- Zabezpečení
Config.SecurityEnabled = true
Config.PinLength = 4
Config.FingerprintEnabled = true
Config.MaxLoginAttempts = 3
Config.LockoutTime = 300 -- Sekundy

-- Widgety
Config.WidgetsEnabled = true
Config.WeatherWidget = true
Config.ClockWidget = true
Config.BatteryWidget = true

-- Aplikace
Config.Apps = {
    contacts = { enabled = true, icon = 'fas fa-address-book', name = 'Kontakty' },
    messages = { enabled = true, icon = 'fas fa-sms', name = 'Zprávy' },
    calls = { enabled = true, icon = 'fas fa-phone', name = 'Telefon' },
    camera = { enabled = true, icon = 'fas fa-camera', name = 'Fotoaparát' },
    gallery = { enabled = true, icon = 'fas fa-images', name = 'Galerie' },
    gps = { enabled = true, icon = 'fas fa-map-marked-alt', name = 'GPS' },
    settings = { enabled = true, icon = 'fas fa-cog', name = 'Nastavení' },
    darkweb = { enabled = true, icon = 'fas fa-user-secret', name = 'DarkWeb', hidden = true },
    social = { enabled = true, icon = 'fas fa-share-alt', name = 'Sociální síť' },
    marketplace = { enabled = true, icon = 'fas fa-store', name = 'Marketplace' },
    banking = { enabled = true, icon = 'fas fa-university', name = 'Banka' },
    email = { enabled = true, icon = 'fas fa-envelope', name = 'Email' },
    calendar = { enabled = true, icon = 'fas fa-calendar', name = 'Kalendář' },
    games = { enabled = true, icon = 'fas fa-gamepad', name = 'Hry' }
}

-- Wallpapery
Config.Wallpapers = {
    'default.jpg',
    'nature1.jpg',
    'nature2.jpg',
    'city1.jpg',
    'city2.jpg',
    'abstract1.jpg',
    'abstract2.jpg',
    'space1.jpg',
    'space2.jpg',
    'custom.jpg'
}

-- Témata
Config.Themes = {
    light = {
        name = 'Světlý',
        primary = '#007AFF',
        background = '#FFFFFF',
        surface = '#F2F2F7',
        text = '#000000'
    },
    dark = {
        name = 'Tmavý',
        primary = '#0A84FF',
        background = '#000000',
        surface = '#1C1C1E',
        text = '#FFFFFF'
    }
}

-- Minihry
Config.Games = {
    snake = { enabled = true, highscoreReward = 100 },
    taptap = { enabled = true, highscoreReward = 50 },
    memory = { enabled = true, highscoreReward = 75 }
}

-- Animace
Config.Animations = {
    open = { dict = 'cellphone@', anim = 'cellphone_text_read_base', flag = 49 },
    close = { dict = 'cellphone@', anim = 'cellphone_text_read_base_exit', flag = 49 },
    call = { dict = 'cellphone@', anim = 'cellphone_call_listen_base', flag = 49 }
}

-- Debug
Config.Debug = false
