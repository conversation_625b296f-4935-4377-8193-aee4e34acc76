{"manifest": {"name": "big.js", "description": "A small, fast, easy-to-use library for arbitrary-precision decimal arithmetic", "version": "5.2.2", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "repository": {"type": "git", "url": "https://github.com/MikeMcl/big.js.git"}, "main": "big", "browser": "big.js", "module": "big.mjs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/MikeMcl/big.js/issues"}, "engines": {"node": "*"}, "license": "MIT", "scripts": {"test": "node ./test/every-test.js", "build": "uglifyjs big.js --source-map -c -m -o big.min.js"}, "files": ["big.js", "big.mjs", "big.min.js"], "collective": {"type": "opencollective", "url": "https://opencollective.com/bigjs"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-big-js-5.2.2-65f0af382f578bcdc742bd9c281e9cb2d7768328-integrity\\node_modules\\big.js\\package.json", "readmeFilename": "README.md", "readme": "# big.js\n\n**A small, fast JavaScript library for arbitrary-precision decimal arithmetic.**\n\nThe little sister to [bignumber.js](https://github.com/MikeMcl/bignumber.js/) and [decimal.js](https://github.com/MikeMcl/decimal.js/). See [here](https://github.com/MikeMcl/big.js/wiki) for some notes on the difference between them.\n\n## Features\n\n  - Faster, smaller and easier-to-use than JavaScript versions of Java's BigDecimal\n  - Only 5.9 KB minified and 2.7 KB gzipped\n  - Simple API\n  - Replicates the `toExponential`, `toFixed` and `toPrecision` methods of JavaScript's Number type\n  - Includes a `sqrt` method\n  - Stores values in an accessible decimal floating point format\n  - No dependencies\n  - Comprehensive [documentation](http://mikemcl.github.io/big.js/) and test set\n\n## Set up\n\nThe library is the single JavaScript file *big.js* (or *big.min.js*, which is *big.js* minified).\n\nBrowser:\n\n```html\n<script src='path/to/big.js'></script>\n```\n\n[Node.js](http://nodejs.org):\n\n```bash\n$ npm install big.js\n```\n\n```javascript\nconst Big = require('big.js');\n```\n\nES6 module:\n\n```javascript\nimport Big from 'big.mjs';\n```\n## Use\n\n*In all examples below, `var`, semicolons and `toString` calls are not shown. If a commented-out value is in quotes it means `toString` has been called on the preceding expression.*\n\nThe library exports a single function, `Big`, the constructor of Big number instances.\nIt accepts a value of type number, string or Big number object.\n\n    x = new Big(123.4567)\n    y = Big('123456.7e-3')             // 'new' is optional\n    z = new Big(x)\n    x.eq(y) && x.eq(z) && y.eq(z)      // true\n\nA Big number is immutable in the sense that it is not changed by its methods.\n\n    0.3 - 0.1                          // 0.19999999999999998\n    x = new Big(0.3)\n    x.minus(0.1)                       // \"0.2\"\n    x                                  // \"0.3\"\n\nThe methods that return a Big number can be chained.\n\n    x.div(y).plus(z).times(9).minus('1.234567801234567e+8').plus(976.54321).div('2598.11772')\n    x.sqrt().div(y).pow(3).gt(y.mod(z))    // true\n\nLike JavaScript's Number type, there are `toExponential`, `toFixed` and `toPrecision` methods.\n\n    x = new Big(255.5)\n    x.toExponential(5)                 // \"2.55500e+2\"\n    x.toFixed(5)                       // \"255.50000\"\n    x.toPrecision(5)                   // \"255.50\"\n\nThe arithmetic methods always return the exact result except `div`, `sqrt` and `pow`\n(with negative exponent), as these methods involve division.\n\nThe maximum number of decimal places and the rounding mode used to round the results of these methods is determined by the value of the `DP` and `RM` properties of the `Big` number constructor.\n\n    Big.DP = 10\n    Big.RM = 1\n\n    x = new Big(2);\n    y = new Big(3);\n    z = x.div(y)                       // \"0.6666666667\"\n    z.sqrt()                           // \"0.8164965809\"\n    z.pow(-3)                          // \"3.3749999995\"\n    z.times(z)                         // \"0.44444444448888888889\"\n    z.times(z).round(10)               // \"0.4444444445\"\n\nMultiple Big number constructors can be created, each with an independent configuration.\n\nThe value of a Big number is stored in a decimal floating point format in terms of a coefficient, exponent and sign.\n\n    x = new Big(-123.456);\n    x.c                                // [1,2,3,4,5,6]    coefficient (i.e. significand)\n    x.e                                // 2                exponent\n    x.s                                // -1               sign\n\nFor further information see the [API](http://mikemcl.github.io/big.js/) reference from the *doc* folder.\n\n## Test\n\nThe *test* directory contains the test scripts for each Big number method.\n\nThe tests can be run with Node.js or a browser.\n\nTo run all the tests\n\n    $ npm test\n\nTo test a single method\n\n    $ node test/toFixed\n\nFor the browser, see *single-test.html* and *every-test.html* in the *test/browser* directory.\n\n*big-vs-number.html* is a simple application that enables some of the methods of big.js to be compared with those of JavaScript's Number type.\n\n## Performance\n\nThe *perf* directory contains two legacy applications and a *lib* directory containing the BigDecimal libraries used by both.\n\n*big-vs-bigdecimal.html* tests the performance of big.js against the JavaScript translations of two versions of BigDecimal, its use should be more or less self-explanatory.\n\n* [GWT: java.math.BigDecimal](https://github.com/iriscouch/bigdecimal.js)\n* [ICU4J: com.ibm.icu.math.BigDecimal](https://github.com/dtrebbien/BigDecimal.js)\n\nThe BigDecimal in the npm registry is the GWT version. It has some bugs, see the Node.js script *perf/lib/bigdecimal_GWT/bugs.js* for examples of flaws in its *remainder*, *divide* and *compareTo* methods.\n\n*bigtime.js* is a Node.js command-line application which tests the performance of big.js against the GWT version of\nBigDecimal from the npm registry.\n\nFor example, to compare the time taken by the big.js `plus` method and the BigDecimal `add` method\n\n    $ node bigtime plus 10000 40\n\nThis will time 10000 calls to each, using operands of up to 40 random digits and will check that the results match.\n\nFor help\n\n    $ node bigtime -h\n\n## Build\n\nIf [uglify-js](https://github.com/mishoo/UglifyJS2) is installed globally\n\n    $ npm install uglify-js -g\n\nthen\n\n    $ npm run build\n\nwill create *big.min.js*.\n\n## TypeScript\n\nThe [DefinitelyTyped](https://github.com/borisyankov/DefinitelyTyped) project has a Typescript type definitions file for big.js.\n\n    $ npm install @types/big.js\n\nAny questions about the TypeScript type definitions file should be addressed to the DefinitelyTyped project.\n\n## Feedback\n\nBugs/comments/questions?\n\nOpen an issue, or email <a href=\"mailto:<EMAIL>\">Michael</a>\n\n## Licence\n\n[MIT](LICENCE)\n\n## Contributors\n\nThis project exists thanks to all the people who contribute. [[Contribute](CONTRIBUTING.md)].\n<a href=\"graphs/contributors\"><img src=\"https://opencollective.com/bigjs/contributors.svg?width=890&button=false\" /></a>\n\n\n## Backers\n\nThank you to all our backers! 🙏 [[Become a backer](https://opencollective.com/bigjs#backer)]\n\n<a href=\"https://opencollective.com/bigjs#backers\" target=\"_blank\"><img src=\"https://opencollective.com/bigjs/backers.svg?width=890\"></a>\n\n\n## Sponsors\n\nSupport this project by becoming a sponsor. Your logo will show up here with a link to your website. [[Become a sponsor](https://opencollective.com/bigjs#sponsor)]\n\n<a href=\"https://opencollective.com/bigjs/sponsor/0/website\" target=\"_blank\"><img src=\"https://opencollective.com/bigjs/sponsor/0/avatar.svg\"></a>\n<a href=\"https://opencollective.com/bigjs/sponsor/1/website\" target=\"_blank\"><img src=\"https://opencollective.com/bigjs/sponsor/1/avatar.svg\"></a>\n<a href=\"https://opencollective.com/bigjs/sponsor/2/website\" target=\"_blank\"><img src=\"https://opencollective.com/bigjs/sponsor/2/avatar.svg\"></a>\n<a href=\"https://opencollective.com/bigjs/sponsor/3/website\" target=\"_blank\"><img src=\"https://opencollective.com/bigjs/sponsor/3/avatar.svg\"></a>\n<a href=\"https://opencollective.com/bigjs/sponsor/4/website\" target=\"_blank\"><img src=\"https://opencollective.com/bigjs/sponsor/4/avatar.svg\"></a>\n<a href=\"https://opencollective.com/bigjs/sponsor/5/website\" target=\"_blank\"><img src=\"https://opencollective.com/bigjs/sponsor/5/avatar.svg\"></a>\n<a href=\"https://opencollective.com/bigjs/sponsor/6/website\" target=\"_blank\"><img src=\"https://opencollective.com/bigjs/sponsor/6/avatar.svg\"></a>\n<a href=\"https://opencollective.com/bigjs/sponsor/7/website\" target=\"_blank\"><img src=\"https://opencollective.com/bigjs/sponsor/7/avatar.svg\"></a>\n<a href=\"https://opencollective.com/bigjs/sponsor/8/website\" target=\"_blank\"><img src=\"https://opencollective.com/bigjs/sponsor/8/avatar.svg\"></a>\n<a href=\"https://opencollective.com/bigjs/sponsor/9/website\" target=\"_blank\"><img src=\"https://opencollective.com/bigjs/sponsor/9/avatar.svg\"></a>\n\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/big.js/-/big.js-5.2.2.tgz#65f0af382f578bcdc742bd9c281e9cb2d7768328", "type": "tarball", "reference": "https://registry.yarnpkg.com/big.js/-/big.js-5.2.2.tgz", "hash": "65f0af382f578bcdc742bd9c281e9cb2d7768328", "integrity": "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==", "registry": "npm", "packageName": "big.js", "cacheIntegrity": "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ== sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg="}, "registry": "npm", "hash": "65f0af382f578bcdc742bd9c281e9cb2d7768328"}