{"manifest": {"name": "split-string", "description": "Split a string on a character except when the character is escaped.", "version": "3.1.0", "homepage": "https://github.com/jonschlinkert/split-string", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/split-string.git"}, "bugs": {"url": "https://github.com/jonschlinkert/split-string/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"extend-shallow": "^3.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["character", "escape", "split", "string"], "verb": {"toc": false, "layout": "default", "titles": [".", "install", "Why use this?"], "related": {"list": ["deromanize", "randomatic", "repeat-string", "romanize"]}, "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-split-string-3.1.0-7cb09dda3a86585705c64b39a6466038682e8fe2-integrity\\node_modules\\split-string\\package.json", "readmeFilename": "README.md", "readme": "# split-string [![NPM version](https://img.shields.io/npm/v/split-string.svg?style=flat)](https://www.npmjs.com/package/split-string) [![NPM monthly downloads](https://img.shields.io/npm/dm/split-string.svg?style=flat)](https://npmjs.org/package/split-string) [![NPM total downloads](https://img.shields.io/npm/dt/split-string.svg?style=flat)](https://npmjs.org/package/split-string) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/split-string.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/split-string)\n\n> Split a string on a character except when the character is escaped.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save split-string\n```\n\n<!-- section: Why use this? -->\n\n<details>\n<summary><strong>Why use this?</strong></summary>\n\n<br>\n\nAlthough it's easy to split on a string:\n\n```js\nconsole.log('a.b.c'.split('.'));\n//=> ['a', 'b', 'c']\n```\n\nIt's more challenging to split a string whilst respecting escaped or quoted characters.\n\n**Bad**\n\n```js\nconsole.log('a\\\\.b.c'.split('.'));\n//=> ['a\\\\', 'b', 'c']\n\nconsole.log('\"a.b.c\".d'.split('.'));\n//=> ['\"a', 'b', 'c\"', 'd']\n```\n\n**Good**\n\n```js\nvar split = require('split-string');\nconsole.log(split('a\\\\.b.c'));\n//=> ['a.b', 'c']\n\nconsole.log(split('\"a.b.c\".d'));\n//=> ['a.b.c', 'd']\n```\n\nSee the [options](#options) to learn how to choose the separator or retain quotes or escaping.\n\n<br>\n\n</details>\n\n## Usage\n\n```js\nvar split = require('split-string');\n\nsplit('a.b.c');\n//=> ['a', 'b', 'c']\n\n// respects escaped characters\nsplit('a.b.c\\\\.d');\n//=> ['a', 'b', 'c.d']\n\n// respects double-quoted strings\nsplit('a.\"b.c.d\".e');\n//=> ['a', 'b.c.d', 'e']\n```\n\n**Brackets**\n\nAlso respects brackets [unless disabled](#optionsbrackets):\n\n```js\nsplit('a (b c d) e', ' ');\n//=> ['a', '(b c d)', 'e']\n```\n\n## Options\n\n### options.brackets\n\n**Type**: `object|boolean`\n\n**Default**: `undefined`\n\n**Description**\n\nIf enabled, split-string will not split inside brackets. The following brackets types are supported when `options.brackets` is `true`,\n\n```js\n{\n  '<': '>',\n  '(': ')',\n  '[': ']',\n  '{': '}'\n}\n```\n\nOr, if object of brackets must be passed, each property on the object must be a bracket type, where the property key is the opening delimiter and property value is the closing delimiter.\n\n**Examples**\n\n```js\n// no bracket support by default\nsplit('a.{b.c}');\n//=> [ 'a', '{b', 'c}' ]\n\n// support all basic bracket types: \"<>{}[]()\"\nsplit('a.{b.c}', {brackets: true});\n//=> [ 'a', '{b.c}' ]\n\n// also supports nested brackets \nsplit('a.{b.{c.d}.e}.f', {brackets: true});\n//=> [ 'a', '{b.{c.d}.e}', 'f' ]\n\n// support only the specified brackets\nsplit('[a.b].(c.d)', {brackets: {'[': ']'}});\n//=> [ '[a.b]', '(c', 'd)' ]\n```\n\n### options.sep\n\n**Type**: `string`\n\n**Default**: `.`\n\nThe separator/character to split on.\n\n**Example**\n\n```js\nsplit('a.b,c', {sep: ','});\n//=> ['a.b', 'c']\n\n// you can also pass the separator as string as the last argument\nsplit('a.b,c', ',');\n//=> ['a.b', 'c']\n```\n\n### options.keepEscaping\n\n**Type**: `boolean`\n\n**Default**: `undefined`\n\nKeep backslashes in the result.\n\n**Example**\n\n```js\nsplit('a.b\\\\.c');\n//=> ['a', 'b.c']\n\nsplit('a.b.\\\\c', {keepEscaping: true});\n//=> ['a', 'b\\.c']\n```\n\n### options.keepQuotes\n\n**Type**: `boolean`\n\n**Default**: `undefined`\n\nKeep single- or double-quotes in the result.\n\n**Example**\n\n```js\nsplit('a.\"b.c.d\".e');\n//=> ['a', 'b.c.d', 'e']\n\nsplit('a.\"b.c.d\".e', {keepQuotes: true});\n//=> ['a', '\"b.c.d\"', 'e']\n\nsplit('a.\\'b.c.d\\'.e', {keepQuotes: true});\n//=> ['a', '\\'b.c.d\\'', 'e']\n```\n\n### options.keepDoubleQuotes\n\n**Type**: `boolean`\n\n**Default**: `undefined`\n\nKeep double-quotes in the result.\n\n**Example**\n\n```js\nsplit('a.\"b.c.d\".e');\n//=> ['a', 'b.c.d', 'e']\n\nsplit('a.\"b.c.d\".e', {keepDoubleQuotes: true});\n//=> ['a', '\"b.c.d\"', 'e']\n```\n\n### options.keepSingleQuotes\n\n**Type**: `boolean`\n\n**Default**: `undefined`\n\nKeep single-quotes in the result.\n\n**Example**\n\n```js\nsplit('a.\\'b.c.d\\'.e');\n//=> ['a', 'b.c.d', 'e']\n\nsplit('a.\\'b.c.d\\'.e', {keepSingleQuotes: true});\n//=> ['a', '\\'b.c.d\\'', 'e']\n```\n\n## Customizer\n\n**Type**: `function`\n\n**Default**: `undefined`\n\nPass a function as the last argument to customize how tokens are added to the array.\n\n**Example**\n\n```js\nvar arr = split('a.b', function(tok) {\n  if (tok.arr[tok.arr.length - 1] === 'a') {\n    tok.split = false;\n  }\n});\nconsole.log(arr);\n//=> ['a.b']\n```\n\n**Properties**\n\nThe `tok` object has the following properties:\n\n* `tok.val` (string) The current value about to be pushed onto the result array\n* `tok.idx` (number) the current index in the string\n* `tok.str` (string) the entire string\n* `tok.arr` (array) the result array\n\n## Release history\n\n### v3.0.0 - 2017-06-17\n\n**Added**\n\n* adds support for brackets\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [deromanize](https://www.npmjs.com/package/deromanize): Convert roman numerals to arabic numbers (useful for books, outlines, documentation, slide decks, etc) | [homepage](https://github.com/jonschlinkert/deromanize \"Convert roman numerals to arabic numbers (useful for books, outlines, documentation, slide decks, etc)\")\n* [randomatic](https://www.npmjs.com/package/randomatic): Generate randomized strings of a specified length using simple character sequences. The original generate-password. | [homepage](https://github.com/jonschlinkert/randomatic \"Generate randomized strings of a specified length using simple character sequences. The original generate-password.\")\n* [repeat-string](https://www.npmjs.com/package/repeat-string): Repeat the given string n times. Fastest implementation for repeating a string. | [homepage](https://github.com/jonschlinkert/repeat-string \"Repeat the given string n times. Fastest implementation for repeating a string.\")\n* [romanize](https://www.npmjs.com/package/romanize): Convert numbers to roman numerals (useful for books, outlines, documentation, slide decks, etc) | [homepage](https://github.com/jonschlinkert/romanize \"Convert numbers to roman numerals (useful for books, outlines, documentation, slide decks, etc)\")\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 28 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 9 | [doowb](https://github.com/doowb) |\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on November 19, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015-2017, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/split-string/-/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2", "type": "tarball", "reference": "https://registry.yarnpkg.com/split-string/-/split-string-3.1.0.tgz", "hash": "7cb09dda3a86585705c64b39a6466038682e8fe2", "integrity": "sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw==", "registry": "npm", "packageName": "split-string", "cacheIntegrity": "sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw== sha1-fLCd2jqGWFcFxks5pkZgOGguj+I="}, "registry": "npm", "hash": "7cb09dda3a86585705c64b39a6466038682e8fe2"}