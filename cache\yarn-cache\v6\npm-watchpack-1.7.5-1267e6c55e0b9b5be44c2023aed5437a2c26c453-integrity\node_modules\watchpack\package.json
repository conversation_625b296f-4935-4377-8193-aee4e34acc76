{"name": "watchpack", "version": "1.7.5", "description": "", "main": "./lib/watchpack.js", "directories": {"test": "test"}, "files": ["lib/"], "scripts": {"pretest": "npm run lint", "test": "mocha", "travis": "npm run cover -- --report lcovonly", "lint": "eslint lib", "precover": "npm run lint", "cover": "istanbul cover node_modules/mocha/bin/_mocha"}, "repository": {"type": "git", "url": "https://github.com/webpack/watchpack.git"}, "author": "<PERSON> @sokra", "license": "MIT", "bugs": {"url": "https://github.com/webpack/watchpack/issues"}, "homepage": "https://github.com/webpack/watchpack", "devDependencies": {"codecov.io": "^0.1.6", "coveralls": "^3.0.0", "eslint": "^4.18.1", "istanbul": "^0.4.3", "mocha": "^5.0.1", "rimraf": "^2.6.2", "should": "^8.3.1"}, "optionalDependencies": {"chokidar": "^3.4.1", "watchpack-chokidar2": "^2.0.1"}, "dependencies": {"graceful-fs": "^4.1.2", "neo-async": "^2.5.0", "chokidar": "^3.4.1", "watchpack-chokidar2": "^2.0.1"}}