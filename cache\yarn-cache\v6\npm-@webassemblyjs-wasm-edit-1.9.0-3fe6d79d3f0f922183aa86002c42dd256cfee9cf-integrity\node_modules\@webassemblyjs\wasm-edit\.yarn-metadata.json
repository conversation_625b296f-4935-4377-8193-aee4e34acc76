{"manifest": {"name": "@webassemblyjs/wasm-edit", "version": "1.9.0", "description": "> Rewrite a WASM binary", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "author": {"name": "<PERSON>"}, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/helper-wasm-section": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0", "@webassemblyjs/wasm-opt": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0", "@webassemblyjs/wast-printer": "1.9.0"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.9.0"}, "gitHead": "0440b420888c1f7701eb9762ec657775506b87d8", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@webassemblyjs-wasm-edit-1.9.0-3fe6d79d3f0f922183aa86002c42dd256cfee9cf-integrity\\node_modules\\@webassemblyjs\\wasm-edit\\package.json", "readmeFilename": "README.md", "readme": "# @webassemblyjs/wasm-edit\n\n> Rewrite a WASM binary\n\nReplace in-place an AST node in the binary.\n\n## Installation\n\n```sh\nyarn add @webassemblyjs/wasm-edit\n```\n\n## Usage\n\nUpdate:\n\n```js\nimport { edit } from \"@webassemblyjs/wasm-edit\";\n\nconst binary = [/*...*/];\n\nconst visitors = {\n  ModuleImport({ node }) {\n    node.module = \"foo\";\n    node.name = \"bar\";\n  }\n};\n\nconst newBinary = edit(binary, visitors);\n```\n\nReplace:\n\n```js\nimport { edit } from \"@webassemblyjs/wasm-edit\";\n\nconst binary = [/*...*/];\n\nconst visitors = {\n  Instr(path) {\n    const newNode = t.callInstruction(t.indexLiteral(0));\n    path.replaceWith(newNode);\n  }\n};\n\nconst newBinary = edit(binary, visitors);\n```\n\nRemove:\n\n```js\nimport { edit } from \"@webassemblyjs/wasm-edit\";\n\nconst binary = [/*...*/];\n\nconst visitors = {\n  ModuleExport({ node }) {\n    path.remove()\n  }\n};\n\nconst newBinary = edit(binary, visitors);\n```\n\nInsert:\n\n```js\nimport { add } from \"@webassemblyjs/wasm-edit\";\n\nconst binary = [/*...*/];\n\nconst newBinary = add(actualBinary, [\n  t.moduleImport(\"env\", \"mem\", t.memory(t.limit(1)))\n]);\n```\n\n## Providing the AST\n\nProviding an AST allows you to handle the decoding yourself, here is the API:\n\n```js\naddWithAST(Program, ArrayBuffer, Array<Node>): ArrayBuffer;\neditWithAST(Program, ArrayBuffer, visitors): ArrayBuffer;\n```\n\nNote that the AST will be updated in-place.\n", "licenseText": "MIT License\n\nCopyright (c) 2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@webassemblyjs/wasm-edit/-/wasm-edit-1.9.0.tgz#3fe6d79d3f0f922183aa86002c42dd256cfee9cf", "type": "tarball", "reference": "https://registry.yarnpkg.com/@webassemblyjs/wasm-edit/-/wasm-edit-1.9.0.tgz", "hash": "3fe6d79d3f0f922183aa86002c42dd256cfee9cf", "integrity": "sha512-FgHzBm80uwz5M8WKnMTn6j/sVbqilPdQXTWraSjBwFXSYGirpkSWE2R9Qvz9tNiTKQvoKILpCuTjBKzOIm0nxw==", "registry": "npm", "packageName": "@webassemblyjs/wasm-edit", "cacheIntegrity": "sha512-FgHzBm80uwz5M8WKnMTn6j/sVbqilPdQXTWraSjBwFXSYGirpkSWE2R9Qvz9tNiTKQvoKILpCuTjBKzOIm0nxw== sha1-P+bXnT8PkiGDqoYALELdJWz+6c8="}, "registry": "npm", "hash": "3fe6d79d3f0f922183aa86002c42dd256cfee9cf"}