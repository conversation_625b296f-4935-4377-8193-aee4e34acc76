{"manifest": {"name": "es-abstract", "version": "1.18.0-next.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "description": "ECMAScript spec abstract operations.", "license": "MIT", "main": "index.js", "scripts": {"prespackle": "git ls-files | xargs git check-attr spackled | grep -v 'unspecified$' | cut -d: -f1 | xargs rm || true", "spackle": "node operations/spackle 1", "postspackle": "git ls-files | xargs git check-attr spackled | grep -v 'unspecified$' | cut -d: -f1 | xargs git add", "prepublish": "safe-publish-latest && (not-in-publish || npm run spackle)", "pretest": "npm run lint", "test": "npm run tests-only", "posttest": "aud --production", "tests-only": "node test", "coverage": "nyc npm run tests-only >/dev/null", "postcoverage": "nyc report", "lint": "eslint .", "eccheck": "eclint check *.js **/*.js > /dev/null"}, "repository": {"type": "git", "url": "git://github.com/ljharb/es-abstract.git"}, "keywords": ["ECMAScript", "ES", "abstract", "operation", "abstract operation", "JavaScript", "ES5", "ES6", "ES7"], "dependencies": {"es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1", "is-callable": "^1.2.2", "is-negative-zero": "^2.0.0", "is-regex": "^1.1.1", "object-inspect": "^1.8.0", "object-keys": "^1.1.1", "object.assign": "^4.1.1", "string.prototype.trimend": "^1.0.1", "string.prototype.trimstart": "^1.0.1"}, "devDependencies": {"@ljharb/eslint-config": "^17.2.0", "array.prototype.indexof": "^1.0.0", "aud": "^1.1.2", "cheerio": "^1.0.0-rc.3", "diff": "^4.0.2", "eclint": "^2.8.1", "eslint": "^7.10.0", "foreach": "^2.0.5", "functions-have-names": "^1.2.1", "has-bigints": "^1.0.0", "has-strict-mode": "^1.0.0", "in-publish": "^2.0.1", "make-arrow-function": "^1.2.0", "make-async-function": "^1.0.0", "make-async-generator-function": "^1.0.0", "make-generator-function": "^2.0.0", "nyc": "^10.3.2", "object-is": "^1.1.2", "object.fromentries": "^2.0.2", "safe-publish-latest": "^1.1.4", "ses": "^0.10.4", "tape": "^5.0.1"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "greenkeeper": {"//": "nyc is ignored because it requires node 4+, and we support older than that", "ignore": ["nyc"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-es-abstract-1.18.0-next.1-6e3a0a4bda717e5023ab3b8e90bec36108d22c68-integrity\\node_modules\\es-abstract\\package.json", "readmeFilename": "README.md", "readme": "# es-abstract <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![Build Status][travis-svg]][travis-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\n[![browser support][testling-svg]][testling-url]\n\nECMAScript spec abstract operations.\nWhen different versions of the spec conflict, the default export will be the latest version of the abstract operation.\nAll abstract operations will also be available under an `es5`/`es2015`/`es2016`/`es2017`/`es2018`/`es2019` entry point, and exported property, if you require a specific version.\n\n## Example\n\n```js\nvar ES = require('es-abstract');\nvar assert = require('assert');\n\nassert(ES.isCallable(function () {}));\nassert(!ES.isCallable(/a/g));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n## Security\n\nPlease email [@ljharb](https://github.com/ljharb) or see https://tidelift.com/security if you have a potential security vulnerability to report.\n\n[package-url]: https://npmjs.org/package/es-abstract\n[npm-version-svg]: http://versionbadg.es/ljharb/es-abstract.svg\n[travis-svg]: https://travis-ci.org/ljharb/es-abstract.svg\n[travis-url]: https://travis-ci.org/ljharb/es-abstract\n[deps-svg]: https://david-dm.org/ljharb/es-abstract.svg\n[deps-url]: https://david-dm.org/ljharb/es-abstract\n[dev-deps-svg]: https://david-dm.org/ljharb/es-abstract/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/es-abstract#info=devDependencies\n[testling-svg]: https://ci.testling.com/ljharb/es-abstract.png\n[testling-url]: https://ci.testling.com/ljharb/es-abstract\n[npm-badge-png]: https://nodei.co/npm/es-abstract.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/es-abstract.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/es-abstract.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=es-abstract\n", "licenseText": "The MIT License (MIT)\n\nCopyright (C) 2015 Jordan Harband\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/es-abstract/-/es-abstract-1.18.0-next.1.tgz#6e3a0a4bda717e5023ab3b8e90bec36108d22c68", "type": "tarball", "reference": "https://registry.yarnpkg.com/es-abstract/-/es-abstract-1.18.0-next.1.tgz", "hash": "6e3a0a4bda717e5023ab3b8e90bec36108d22c68", "integrity": "sha512-I4UGspA0wpZXWENrdA0uHbnhte683t3qT/1VFH9aX2dA5PPSf6QW5HHXf5HImaqPmjXaVeVk4RGWnaylmV7uAA==", "registry": "npm", "packageName": "es-abstract", "cacheIntegrity": "sha512-I4UGspA0wpZXWENrdA0uHbnhte683t3qT/1VFH9aX2dA5PPSf6QW5HHXf5HImaqPmjXaVeVk4RGWnaylmV7uAA== sha1-bjoKS9pxflAjqzuOkL7DYQjSLGg="}, "registry": "npm", "hash": "6e3a0a4bda717e5023ab3b8e90bec36108d22c68"}