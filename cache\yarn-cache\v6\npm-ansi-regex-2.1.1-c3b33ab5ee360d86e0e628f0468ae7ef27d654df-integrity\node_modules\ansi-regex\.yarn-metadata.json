{"manifest": {"name": "ansi-regex", "version": "2.1.1", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/chalk/ansi-regex.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "jbnicolai.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/qix-"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava --verbose", "view-supported": "node fixtures/view-codes.js"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"ava": "0.17.0", "xo": "0.16.0"}, "xo": {"rules": {"guard-for-in": 0, "no-loop-func": 0}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-ansi-regex-2.1.1-c3b33ab5ee360d86e0e628f0468ae7ef27d654df-integrity\\node_modules\\ansi-regex\\package.json", "readmeFilename": "readme.md", "readme": "# ansi-regex [![Build Status](https://travis-ci.org/chalk/ansi-regex.svg?branch=master)](https://travis-ci.org/chalk/ansi-regex)\n\n> Regular expression for matching [ANSI escape codes](http://en.wikipedia.org/wiki/ANSI_escape_code)\n\n\n## Install\n\n```\n$ npm install --save ansi-regex\n```\n\n\n## Usage\n\n```js\nconst ansiRegex = require('ansi-regex');\n\nansiRegex().test('\\u001b[4mcake\\u001b[0m');\n//=> true\n\nansiRegex().test('cake');\n//=> false\n\n'\\u001b[4mcake\\u001b[0m'.match(ansiRegex());\n//=> ['\\u001b[4m', '\\u001b[0m']\n```\n\n## FAQ\n\n### Why do you test for codes not in the ECMA 48 standard?\n\nSome of the codes we run as a test are codes that we acquired finding various lists of non-standard or manufacturer specific codes. If I recall correctly, we test for both standard and non-standard codes, as most of them follow the same or similar format and can be safely matched in strings without the risk of removing actual string content. There are a few non-standard control codes that do not follow the traditional format (i.e. they end in numbers) thus forcing us to exclude them from the test because we cannot reliably match them.\n\nOn the historical side, those ECMA standards were established in the early 90's whereas the VT100, for example, was designed in the mid/late 70's. At that point in time, control codes were still pretty ungoverned and engineers used them for a multitude of things, namely to activate hardware ports that may have been proprietary. Somewhere else you see a similar 'anarchy' of codes is in the x86 architecture for processors; there are a ton of \"interrupts\" that can mean different things on certain brands of processors, most of which have been phased out.\n\n\n## License\n\nMIT © [Sindre Sorhus](http://sindresorhus.com)\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND <PERSON><PERSON>NFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df", "type": "tarball", "reference": "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-2.1.1.tgz", "hash": "c3b33ab5ee360d86e0e628f0468ae7ef27d654df", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "registry": "npm", "packageName": "ansi-regex", "cacheIntegrity": "sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA== sha1-w7M6te42DYbg5ijwRorn7yfWVN8="}, "registry": "npm", "hash": "c3b33ab5ee360d86e0e628f0468ae7ef27d654df"}