{"manifest": {"name": "cipher-base", "version": "1.0.4", "description": "abstract base class for crypto-streams", "main": "index.js", "scripts": {"test": "node test.js | tspec"}, "repository": {"type": "git", "url": "git+https://github.com/crypto-browserify/cipher-base.git"}, "keywords": ["cipher", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/cipher-base/issues"}, "homepage": "https://github.com/crypto-browserify/cipher-base#readme", "dependencies": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}, "devDependencies": {"standard": "^10.0.2", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-cipher-base-1.0.4-8760e4ecc272f4c363532f926d874aae2c1397de-integrity\\node_modules\\cipher-base\\package.json", "readmeFilename": "README.md", "readme": "cipher-base\n===\n\n[![Build Status](https://travis-ci.org/crypto-browserify/cipher-base.svg)](https://travis-ci.org/crypto-browserify/cipher-base)\n\nAbstract base class to inherit from if you want to create streams implementing\nthe same api as node crypto streams.\n\nRequires you to implement 2 methods `_final` and `_update`. `_update` takes a\nbuffer and should return a buffer, `_final` takes no arguments and should return\na buffer.\n\n\nThe constructor takes one argument and that is a string which if present switches\nit into hash mode, i.e. the object you get from crypto.createHash or\ncrypto.createSign, this switches the name of the final method to be the string\nyou passed instead of `final` and returns `this` from update.\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2017 crypto-browserify contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/cipher-base/-/cipher-base-1.0.4.tgz#8760e4ecc272f4c363532f926d874aae2c1397de", "type": "tarball", "reference": "https://registry.yarnpkg.com/cipher-base/-/cipher-base-1.0.4.tgz", "hash": "8760e4ecc272f4c363532f926d874aae2c1397de", "integrity": "sha512-Kkht5ye6ZGmwv40uUDZztayT2ThLQGfnj/T71N/XzeZeo3nf8foyW7zGTsPYkEya3m5f3cAypH+qe7YOrM1U2Q==", "registry": "npm", "packageName": "cipher-base", "cacheIntegrity": "sha512-Kkht5ye6ZGmwv40uUDZztayT2ThLQGfnj/T71N/XzeZeo3nf8foyW7zGTsPYkEya3m5f3cAypH+qe7YOrM1U2Q== sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94="}, "registry": "npm", "hash": "8760e4ecc272f4c363532f926d874aae2c1397de"}