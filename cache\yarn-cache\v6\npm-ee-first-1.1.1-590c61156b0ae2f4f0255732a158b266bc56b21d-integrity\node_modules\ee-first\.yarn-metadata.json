{"manifest": {"name": "ee-first", "description": "return the first event in a set of ee/event pairs", "version": "1.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jonathanong/ee-first.git"}, "devDependencies": {"istanbul": "0.3.9", "mocha": "2.2.5"}, "files": ["index.js", "LICENSE"], "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-ee-first-1.1.1-590c61156b0ae2f4f0255732a158b266bc56b21d-integrity\\node_modules\\ee-first\\package.json", "readmeFilename": "README.md", "readme": "# EE First\n\n[![NPM version][npm-image]][npm-url]\n[![Build status][travis-image]][travis-url]\n[![Test coverage][coveralls-image]][coveralls-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n[![Gittip][gittip-image]][gittip-url]\n\nGet the first event in a set of event emitters and event pairs,\nthen clean up after itself.\n\n## Install\n\n```sh\n$ npm install ee-first\n```\n\n## API\n\n```js\nvar first = require('ee-first')\n```\n\n### first(arr, listener)\n\nInvoke `listener` on the first event from the list specified in `arr`. `arr` is\nan array of arrays, with each array in the format `[ee, ...event]`. `listener`\nwill be called only once, the first time any of the given events are emitted. If\n`error` is one of the listened events, then if that fires first, the `listener`\nwill be given the `err` argument.\n\nThe `listener` is invoked as `listener(err, ee, event, args)`, where `err` is the\nfirst argument emitted from an `error` event, if applicable; `ee` is the event\nemitter that fired; `event` is the string event name that fired; and `args` is an\narray of the arguments that were emitted on the event.\n\n```js\nvar ee1 = new EventEmitter()\nvar ee2 = new EventEmitter()\n\nfirst([\n  [ee1, 'close', 'end', 'error'],\n  [ee2, 'error']\n], function (err, ee, event, args) {\n  // listener invoked\n})\n```\n\n#### .cancel()\n\nThe group of listeners can be cancelled before being invoked and have all the event\nlisteners removed from the underlying event emitters.\n\n```js\nvar thunk = first([\n  [ee1, 'close', 'end', 'error'],\n  [ee2, 'error']\n], function (err, ee, event, args) {\n  // listener invoked\n})\n\n// cancel and clean up\nthunk.cancel()\n```\n\n[npm-image]: https://img.shields.io/npm/v/ee-first.svg?style=flat-square\n[npm-url]: https://npmjs.org/package/ee-first\n[github-tag]: http://img.shields.io/github/tag/jonathanong/ee-first.svg?style=flat-square\n[github-url]: https://github.com/jonathanong/ee-first/tags\n[travis-image]: https://img.shields.io/travis/jonathanong/ee-first.svg?style=flat-square\n[travis-url]: https://travis-ci.org/jonathanong/ee-first\n[coveralls-image]: https://img.shields.io/coveralls/jonathanong/ee-first.svg?style=flat-square\n[coveralls-url]: https://coveralls.io/r/jonathanong/ee-first?branch=master\n[license-image]: http://img.shields.io/npm/l/ee-first.svg?style=flat-square\n[license-url]: LICENSE.md\n[downloads-image]: http://img.shields.io/npm/dm/ee-first.svg?style=flat-square\n[downloads-url]: https://npmjs.org/package/ee-first\n[gittip-image]: https://img.shields.io/gittip/jonathanong.svg?style=flat-square\n[gittip-url]: https://www.gittip.com/jonathanong/\n", "licenseText": "\nThe MIT License (MIT)\n\nCopyright (c) 2014 <NAME_EMAIL>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/ee-first/-/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d", "type": "tarball", "reference": "https://registry.yarnpkg.com/ee-first/-/ee-first-1.1.1.tgz", "hash": "590c61156b0ae2f4f0255732a158b266bc56b21d", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "registry": "npm", "packageName": "ee-first", "cacheIntegrity": "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow== sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="}, "registry": "npm", "hash": "590c61156b0ae2f4f0255732a158b266bc56b21d"}