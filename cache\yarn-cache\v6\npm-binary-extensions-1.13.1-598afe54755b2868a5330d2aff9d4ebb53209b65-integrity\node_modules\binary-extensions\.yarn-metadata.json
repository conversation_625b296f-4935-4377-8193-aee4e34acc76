{"manifest": {"name": "binary-extensions", "version": "1.13.1", "description": "List of binary file extensions", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/binary-extensions.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "ava"}, "main": "binary-extensions.json", "files": ["binary-extensions.json"], "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "devDependencies": {"ava": "0.16.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-binary-extensions-1.13.1-598afe54755b2868a5330d2aff9d4ebb53209b65-integrity\\node_modules\\binary-extensions\\package.json", "readmeFilename": "readme.md", "readme": "# binary-extensions [![Build Status](https://travis-ci.org/sindresorhus/binary-extensions.svg?branch=master)](https://travis-ci.org/sindresorhus/binary-extensions)\n\n> List of binary file extensions\n\nThe list is just a [JSON file](binary-extensions.json) and can be used anywhere.\n\n\n## Install\n\n```\n$ npm install binary-extensions\n```\n\n\n## Usage\n\n```js\nconst binaryExtensions = require('binary-extensions');\n\nconsole.log(binaryExtensions);\n//=> ['3ds', '3g2', …]\n```\n\n\n## Related\n\n- [is-binary-path](https://github.com/sindresorhus/is-binary-path) - Check if a filepath is a binary file\n- [text-extensions](https://github.com/sindresorhus/text-extensions) - List of text file extensions\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com), <PERSON> (https://paulmillr.com)\n", "licenseText": "MIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/binary-extensions/-/binary-extensions-1.13.1.tgz#598afe54755b2868a5330d2aff9d4ebb53209b65", "type": "tarball", "reference": "https://registry.yarnpkg.com/binary-extensions/-/binary-extensions-1.13.1.tgz", "hash": "598afe54755b2868a5330d2aff9d4ebb53209b65", "integrity": "sha512-Un7MIEDdUC5gNpcGDV97op1Ywk748MpHcFTHoYs6qnj1Z3j7I53VG3nwZhKzoBZmbdRNnb6WRdFlwl7tSDuZGw==", "registry": "npm", "packageName": "binary-extensions", "cacheIntegrity": "sha512-Un7MIEDdUC5gNpcGDV97op1Ywk748MpHcFTHoYs6qnj1Z3j7I53VG3nwZhKzoBZmbdRNnb6WRdFlwl7tSDuZGw== sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U="}, "registry": "npm", "hash": "598afe54755b2868a5330d2aff9d4ebb53209b65"}