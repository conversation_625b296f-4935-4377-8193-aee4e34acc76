{"manifest": {"name": "errno", "authors": ["<PERSON> Vagg @rvagg <<EMAIL>> (https://github.com/rvagg)"], "description": "libuv errno details exposed", "keywords": ["errors", "errno", "libuv"], "version": "0.1.8", "main": "errno.js", "dependencies": {"prr": "~1.0.1"}, "bin": {"errno": "cli.js"}, "devDependencies": {"error-stack-parser": "^2.0.1", "inherits": "^2.0.3", "tape": "~4.8.0"}, "repository": {"type": "git", "url": "https://github.com/rvagg/node-errno.git"}, "license": "MIT", "scripts": {"test": "node --use_strict test.js"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-errno-0.1.8-8bb3e9c7d463be4976ff888f76b4809ebc2e811f-integrity\\node_modules\\errno\\package.json", "readmeFilename": "README.md", "readme": "# node-errno\n\n> Better [libuv](https://github.com/libuv/libuv)/[Node.js](https://nodejs.org)/[io.js](https://iojs.org) error handling & reporting. Available in npm as *errno*.\n\n[![npm](https://img.shields.io/npm/v/errno.svg)](https://www.npmjs.com/package/errno)\n[![Build Status](https://secure.travis-ci.org/rvagg/node-errno.png)](http://travis-ci.org/rvagg/node-errno)\n[![npm](https://img.shields.io/npm/dm/errno.svg)](https://www.npmjs.com/package/errno)\n\n* [errno exposed](#errnoexposed)\n* [Custom errors](#customerrors)\n\n<a name=\"errnoexposed\"></a>\n## errno exposed\n\nEver find yourself needing more details about Node.js errors? Me too, so *node-errno* contains the errno mappings direct from libuv so you can use them in your code.\n\n**By errno:**\n\n```js\nrequire('errno').errno[3]\n// → {\n//     \"errno\": 3,\n//     \"code\": \"EACCES\",\n//     \"description\": \"permission denied\"\n//   }\n```\n\n**By code:**\n\n```js\nrequire('errno').code.ENOTEMPTY\n// → {\n//     \"errno\": 53,\n//     \"code\": \"ENOTEMPTY\",\n//     \"description\": \"directory not empty\"\n//   }\n```\n\n**Make your errors more descriptive:**\n\n```js\nvar errno = require('errno')\n\nfunction errmsg(err) {\n  var str = 'Error: '\n  // if it's a libuv error then get the description from errno\n  if (errno.errno[err.errno])\n    str += errno.errno[err.errno].description\n  else\n    str += err.message\n\n  // if it's a `fs` error then it'll have a 'path' property\n  if (err.path)\n    str += ' [' + err.path + ']'\n\n  return str\n}\n\nvar fs = require('fs')\n\nfs.readFile('thisisnotarealfile.txt', function (err, data) {\n  if (err)\n    console.log(errmsg(err))\n})\n```\n\n**Use as a command line tool:**\n\n```\n~ $ errno 53\n{\n  \"errno\": 53,\n  \"code\": \"ENOTEMPTY\",\n  \"description\": \"directory not empty\"\n}\n~ $ errno EROFS\n{\n  \"errno\": 56,\n  \"code\": \"EROFS\",\n  \"description\": \"read-only file system\"\n}\n~ $ errno foo\nNo such errno/code: \"foo\"\n```\n\nSupply no arguments for the full list. Error codes are processed case-insensitive.\n\nYou will need to install with `npm install errno -g` if you want the `errno` command to be available without supplying a full path to the node_modules installation.\n\n<a name=\"customerrors\"></a>\n## Custom errors\n\nUse `errno.custom.createError()` to create custom `Error` objects to throw around in your Node.js library. Create error hierarchies so `instanceof` becomes a useful tool in tracking errors. Call-stack is correctly captured at the time you create an instance of the error object, plus a `cause` property will make available the original error object if you pass one in to the constructor.\n\n```js\nvar create = require('errno').custom.createError\nvar MyError = create('MyError') // inherits from Error\nvar SpecificError = create('SpecificError', MyError) // inherits from MyError\nvar OtherError = create('OtherError', MyError)\n\n// use them!\nif (condition) throw new SpecificError('Eeek! Something bad happened')\n\nif (err) return callback(new OtherError(err))\n```\n\nAlso available is a `errno.custom.FilesystemError` with in-built access to errno properties:\n\n```js\nfs.readFile('foo', function (err, data) {\n  if (err) return callback(new errno.custom.FilesystemError(err))\n  // do something else\n})\n```\n\nThe resulting error object passed through the callback will have the following properties: `code`, `errno`, `path` and `message` will contain a descriptive human-readable message.\n\n## Contributors\n\n* [bahamas10](https://github.com/bahamas10) (Dave Eddy) - Added CLI\n* [ralphtheninja](https://github.com/ralphtheninja) (Lars-Magnus Skog)\n\n## Copyright & Licence\n\n*Copyright (c) 2012-2015 [Rod Vagg](https://github.com/rvagg) ([@rvagg](https://twitter.com/rvagg))*\n\nMade available under the MIT licence:\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is furnished\nto do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/errno/-/errno-0.1.8.tgz#8bb3e9c7d463be4976ff888f76b4809ebc2e811f", "type": "tarball", "reference": "https://registry.yarnpkg.com/errno/-/errno-0.1.8.tgz", "hash": "8bb3e9c7d463be4976ff888f76b4809ebc2e811f", "integrity": "sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==", "registry": "npm", "packageName": "errno", "cacheIntegrity": "sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A== sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8="}, "registry": "npm", "hash": "8bb3e9c7d463be4976ff888f76b4809ebc2e811f"}