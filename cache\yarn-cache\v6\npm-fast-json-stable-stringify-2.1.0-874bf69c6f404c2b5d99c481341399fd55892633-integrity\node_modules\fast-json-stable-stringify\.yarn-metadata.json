{"manifest": {"name": "fast-json-stable-stringify", "version": "2.1.0", "description": "deterministic `JSON.stringify()` - a faster version of substack's json-stable-strigify without jsonify", "main": "index.js", "types": "index.d.ts", "dependencies": {}, "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.0.0", "eslint": "^6.7.0", "fast-stable-stringify": "latest", "faster-stable-stringify": "latest", "json-stable-stringify": "latest", "nyc": "^14.1.0", "pre-commit": "^1.2.2", "tape": "^4.11.0"}, "scripts": {"eslint": "eslint index.js test", "test-spec": "tape test/*.js", "test": "npm run eslint && nyc npm run test-spec"}, "repository": {"type": "git", "url": "git://github.com/epoberezkin/fast-json-stable-stringify.git"}, "homepage": "https://github.com/epoberezkin/fast-json-stable-stringify", "keywords": ["json", "stringify", "deterministic", "hash", "stable"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "nyc": {"exclude": ["test", "node_modules"], "reporter": ["lcov", "text-summary"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-fast-json-stable-stringify-2.1.0-874bf69c6f404c2b5d99c481341399fd55892633-integrity\\node_modules\\fast-json-stable-stringify\\package.json", "readmeFilename": "README.md", "readme": "# fast-json-stable-stringify\n\nDeterministic `JSON.stringify()` - a faster version of [@substack](https://github.com/substack)'s json-stable-strigify without [jsonify](https://github.com/substack/jsonify).\n\nYou can also pass in a custom comparison function.\n\n[![Build Status](https://travis-ci.org/epoberezkin/fast-json-stable-stringify.svg?branch=master)](https://travis-ci.org/epoberezkin/fast-json-stable-stringify)\n[![Coverage Status](https://coveralls.io/repos/github/epoberezkin/fast-json-stable-stringify/badge.svg?branch=master)](https://coveralls.io/github/epoberezkin/fast-json-stable-stringify?branch=master)\n\n# example\n\n``` js\nvar stringify = require('fast-json-stable-stringify');\nvar obj = { c: 8, b: [{z:6,y:5,x:4},7], a: 3 };\nconsole.log(stringify(obj));\n```\n\noutput:\n\n```\n{\"a\":3,\"b\":[{\"x\":4,\"y\":5,\"z\":6},7],\"c\":8}\n```\n\n\n# methods\n\n``` js\nvar stringify = require('fast-json-stable-stringify')\n```\n\n## var str = stringify(obj, opts)\n\nReturn a deterministic stringified string `str` from the object `obj`.\n\n\n## options\n\n### cmp\n\nIf `opts` is given, you can supply an `opts.cmp` to have a custom comparison\nfunction for object keys. Your function `opts.cmp` is called with these\nparameters:\n\n``` js\nopts.cmp({ key: akey, value: avalue }, { key: bkey, value: bvalue })\n```\n\nFor example, to sort on the object key names in reverse order you could write:\n\n``` js\nvar stringify = require('fast-json-stable-stringify');\n\nvar obj = { c: 8, b: [{z:6,y:5,x:4},7], a: 3 };\nvar s = stringify(obj, function (a, b) {\n    return a.key < b.key ? 1 : -1;\n});\nconsole.log(s);\n```\n\nwhich results in the output string:\n\n```\n{\"c\":8,\"b\":[{\"z\":6,\"y\":5,\"x\":4},7],\"a\":3}\n```\n\nOr if you wanted to sort on the object values in reverse order, you could write:\n\n```\nvar stringify = require('fast-json-stable-stringify');\n\nvar obj = { d: 6, c: 5, b: [{z:3,y:2,x:1},9], a: 10 };\nvar s = stringify(obj, function (a, b) {\n    return a.value < b.value ? 1 : -1;\n});\nconsole.log(s);\n```\n\nwhich outputs:\n\n```\n{\"d\":6,\"c\":5,\"b\":[{\"z\":3,\"y\":2,\"x\":1},9],\"a\":10}\n```\n\n### cycles\n\nPass `true` in `opts.cycles` to stringify circular property as `__cycle__` - the result will not be a valid JSON string in this case.\n\nTypeError will be thrown in case of circular object without this option.\n\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install fast-json-stable-stringify\n```\n\n\n# benchmark\n\nTo run benchmark (requires Node.js 6+):\n```\nnode benchmark\n```\n\nResults:\n```\nfast-json-stable-stringify x 17,189 ops/sec ±1.43% (83 runs sampled)\njson-stable-stringify x 13,634 ops/sec ±1.39% (85 runs sampled)\nfast-stable-stringify x 20,212 ops/sec ±1.20% (84 runs sampled)\nfaster-stable-stringify x 15,549 ops/sec ±1.12% (84 runs sampled)\nThe fastest is fast-stable-stringify\n```\n\n\n## Enterprise support\n\nfast-json-stable-stringify package is a part of [Tidelift enterprise subscription](https://tidelift.com/subscription/pkg/npm-fast-json-stable-stringify?utm_source=npm-fast-json-stable-stringify&utm_medium=referral&utm_campaign=enterprise&utm_term=repo) - it provides a centralised commercial support to open-source software users, in addition to the support provided by software maintainers.\n\n\n## Security contact\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure. Please do NOT report security vulnerability via GitHub issues.\n\n\n# license\n\n[MIT](https://github.com/epoberezkin/fast-json-stable-stringify/blob/master/LICENSE)\n", "licenseText": "This software is released under the MIT license:\n\nCopyright (c) 2017 <PERSON><PERSON><PERSON>\nCopyright (c) 2013 James <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633", "type": "tarball", "reference": "https://registry.yarnpkg.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "hash": "874bf69c6f404c2b5d99c481341399fd55892633", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==", "registry": "npm", "packageName": "fast-json-stable-stringify", "cacheIntegrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw== sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM="}, "registry": "npm", "hash": "874bf69c6f404c2b5d99c481341399fd55892633"}