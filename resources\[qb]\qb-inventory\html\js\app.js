var totalWeight = 0;
var totalWeightOther = 0;
var playerMaxWeight = 0;
var otherMaxWeight = 0;
var otherLabel = "";
var ClickedItemData = {};
var weaponsName = [];
var boxName = [];
var settingsName = [];
var foodsName = [];
var clothesName = [];
var SelectedAttachment = null;
var AttachmentScreenActive = false;
var ControlPressed = false;
var disableRightMouse = false;
var selectedItem = null;
var IsDragging = false;
var healthData = null;
var currentItemImg = null;
var currentItemLabel = null;
var currentItemAmount = null;
var currentItemData = null;
var currentItem = null;
var rightItems = [];
var leftItems = [];
var weaponsAmmoList = [];
var allWeapons = [];
var magazineList = [];
var simCard = "";
var phoneItem = "";
var dropType = null;

$(document).on("keydown", function () {
    if (event.repeat) {
        return;
    }
    switch (event.keyCode) {
        case 27: // ESC
        $('.health-system ').css('left', '-50%');
        $('.health-system').css('display', 'none');
            Inventory.Close();
            break;
        case 9: // TAB
            Inventory.Close();
            break;
        case 17: // TAB
            ControlPressed = true;
            break;
    }
});

$(document).on("dblclick", ".item-slot", function (e) {
    var ItemData = $(this).data("item");
    var ItemInventory = $(this).parent().attr("data-inventory");
    if (ItemData) {
        Inventory.Close();
        console.log("zort2");
        Inventory.UseItem(ItemData);
        $.post(
            "https://qb-inventory/UseItem",
            JSON.stringify({
                inventory: ItemInventory,
                item: ItemData,
            })
        );
    }
});

$(document).on("keyup", function () {
    switch (event.keyCode) {
        case 17: // TAB
            ControlPressed = false;
            break;
    }
});

mouseX = 0;
mouseY = 0;
$(document).on("mouseenter", ".item-slot", function (e) {
    e.preventDefault();
    if ($(this).data("item") != null) {
        currentItemLabel = $(this).find('.item-slot-label p').text();
        slot = Number($(this).data("slot"))

        amount = $(this).data("item").amount
        if (amount<=1) {
            $('#item-give').css('display', 'none');
        }else{
            $('#item-give').css('display', 'block');
        }


        if (rightLabel.hasOwnProperty(currentItemLabel)){
            $('.item-equip').css('display', 'flex');
            if (slot==42 || slot==43 || slot==44 || slot == 45 || slot == 46 || slot==47 || slot == 48 || slot == 49 || slot == 50) {
                $('.item-equip p').html('Unequip item');
            }else{
                $('.item-equip p').html('Equip item');
            }
        }else{
            $('.item-equip').css('display', 'none');
        }

        $('.item-info-description').fadeIn(100);
        
        mouseX = e.pageX;
        mouseY = e.pageY;
    
        $(".ply-iteminfo-container").css({
            'top': mouseY+50 + 'px',
            'left': mouseX+80 + 'px',
        });
        // $('#item-give').css('display', 'block');
        $('#item-split').css('display', 'none');
        $('.inv-options-list').css('display', 'none');
        $('#item-give , #item-use , .item-equip  , .giveitem').fadeIn(100);

        $(".ply-iteminfo-container").fadeIn(150);
        FormatItemInfo($(this).data("item"));
    } else {
        $('.inv-options').fadeOut(100);
        $(".ply-iteminfo-container").fadeOut(100);
    }
});

function GetFirstFreeSlot($toInv, $fromSlot) {
    var retval = null;
    $.each($toInv.find(".item-slot"), function (i, slot) {
        if ($(slot).data("item") === undefined) {
            if (retval === null) {
                retval = i + 1;
            }
        }
    });
    return retval;
}

function CanQuickMove() {
    var otherinventory = otherLabel.toLowerCase();
    var retval = true;

    if (otherinventory.split("-")[0] == "player") {
        retval = false;
    }

    return retval;
}

$(document).on("mousedown", ".item-slot", function (event) {
    dropType = "down"
    switch (event.which) {
        case 3:
            fromSlot = $(this).attr("data-slot");
            fromInventory = $(this).parent();

            if ($(fromInventory).attr("data-inventory") == "bossNew") {
              
            }

            if ($(fromInventory).attr("data-inventory") == "player" || $(fromInventory).attr("data-inventory") == "bossNew") {
                toInventory = $(".other-inventory");
            } else {
                toInventory = $(".player-inventory");
            }
            toSlot = GetFirstFreeSlot(toInventory, $(this));
            if ($(this).data("item") === undefined) {
                return;
            }
            toAmount = $(this).data("item").amount;
            if (toAmount > 1) {
                toAmount = 1;
            }
            if (CanQuickMove()) {
                if (toSlot === null) {
                    console.log("INVENTORY ERROR 1");
                    InventoryError(fromInventory, fromSlot);
                    return;
                }
                if (fromSlot == toSlot && fromInventory == toInventory) {
                    return;
                }
                if (toAmount >= 0) {
                    if (updateweights(fromSlot, toSlot, fromInventory, toInventory, toAmount)) {
                        console.log("swap 1");
                        swap(fromSlot, toSlot, fromInventory, toInventory, toAmount,dropType);
                    }
                }
            } else {
                console.log("INVENTORY ERROR 2");
                InventoryError(fromInventory, fromSlot);
            }
            break;
    }

});

$(document).on("click", "#item-give", function (e) {
    $('#item-split').fadeIn(100);
    $('#item-give , #item-use , .item-equip  , .giveitem').fadeOut(100);
})

$(document).on("click", ".giveitem", function (e) {
        fromData = currentItemData;
        fromInventory = "player";
        splitAmount = fromData.amount;
        amount = $("#item-amount").val();
        if (amount == 0) {
            amount = fromData.amount;
        }
        $.post(
            "https://qb-inventory/GiveItem",
            JSON.stringify({
                inventory: fromInventory,
                item: fromData,
                amount: parseInt(amount),
            })
        );
})


close = false;
$(document).on("click", ".left-background", function (e) {
    if (close) {
        $(this).children('span').css('transform', 'rotate(180deg)')
        $(this).children('span').css('color', 'white')
        $('.alt-stash-inventory').fadeOut(100)
    }else{
        $(this).children('span').css('transform', 'rotate(270deg)')
        $(this).children('span').css('color', '#0ceebf')
        $('.alt-stash-inventory').fadeIn(100)
    }
    close = !close;

})


$(document).on("click", ".item-equip", function (e) {

    $('.ply-iteminfo-container').fadeOut(100);
    $(".inv-options").fadeOut(100);

    pTag = $(this).find('p').text();

    console.log(currentItemAmount);


    $(currentItem).data('equip', false);
    $(currentItem).find('.item-slot-label p').text('');
    $(currentItem).find('.item-slot-amount p').text('');
    $(currentItem).find('.item-slot-img img').remove();
    $(currentItem).data('item', null);
    $(currentItem).data('slot', null);

    if (pTag == 'Equip item') {
        var phoneIndex = Object.keys(rightItems).find(key => rightItems[key] === currentItemLabel);
        $('.player-inventory').find(`[data-slot="${phoneIndex}"]`).data('equip', true);
        $('.player-inventory').find(`[data-slot="${phoneIndex}"]`).data('item', currentItemData);
        $('.player-inventory').find(`[data-slot="${phoneIndex}"]`).find('.item-slot-label p').text(currentItemLabel);


        $(`.item-slot[data-slot="${phoneIndex}"] .item-slot-label p`).text(currentItemLabel);
        // $('.player-inventory').find(`[data-slot="${phoneIndex}"] .item-slot-amount p`).text(currentItemAmount);
        $('.player-inventory').find(`[data-slot="${phoneIndex}"]`).find('p').text(currentItemAmount);
        var newImg = $('<img>');
        $('.player-inventory').find(`[data-slot="${phoneIndex}"]`).find('.item-slot-img').append(newImg);
        newImg.attr('src', currentItemImg);
        $('.player-inventory').find(`[data-slot="${phoneIndex}"]`).addClass('item-drag ui-draggable ui-draggable-handle ');
        $('.player-inventory').find(`[data-slot="${phoneIndex}"]`).data('slot', phoneIndex);
        $('.player-inventory').find(`[data-slot="${phoneIndex}"]`).find('span').css('display', 'none');

        $.post(
            "https://qb-inventory/SetInventoryData",
            JSON.stringify({
                fromInventory: "player",
                toInventory: "player",
                fromSlot: currentItemData.slot,
                toSlot: $(this).data("slot"),
                fromAmount:  currentItemData.amount,
                dropType: dropType
            })
        );
        return false; 
    }else{  
            // item-slot class foreach
            var bossPlayerInventory = $(".player-inventory > .item-slot");
        
            bossPlayerInventory.each(function() {
                var label = $(this).find(".item-slot-label").text().toLowerCase();
                
                if (label.trim().length <= 1) {
                    $(this).find(".item-slot-label").text();
                    $(this).find(".item-slot-amount p").text(currentItemAmount);
                    var newImg = $('<img>');
                    $(this).find('.item-slot-img').append(newImg);
                    newImg.attr('src', currentItemImg);
                    $(this).data('item', currentItemData);
                    $(this).data('equip', true);
                    $(this).data('slot', $(this).data("slot"));
                    $(this).addClass('item-drag ui-draggable ui-draggable-handle ');       
                    $.post(
                        "https://qb-inventory/SetInventoryData",
                        JSON.stringify({
                            fromInventory: "player",
                            toInventory: "player",
                            fromSlot: currentItemData.slot,
                            toSlot: $(this).data("slot"),
                            fromAmount:  currentItemData.amount,
                            dropType: dropType
                        })
                    );
                    return false; 
                }
            });
    }
})

$(document).on("click", ".item-simslot", function (e) {

    if (slot=="weapon") {

        // post
        $.post(
            "https://qb-inventory/getWeapons",
            JSON.stringify({
                inventory: "player",
            }),
            function (data) {
                if (data) {
                    $('.header-bg , .alt-stash-inventory').css('display', 'flex');
                    $('.player-inventory .item-slot[data-slot="52"]').css('display','none')
                    $('.player-inventory .item-slot[data-slot="53"]').css('display','block')
                    $('.header-bg').css('background', 'radial-gradient(83% 83% at 50% 50%,rgba(180,180,180,.15),rgba(120,120,120,.05)')
                    $('.header-bg .left-background').css('display', 'flex')
                    $('.kg-text').css('color', 'rgb(209 213 219 / 1)')
                    $('.kg-text').html('0.0kg / 100.0kg')
                    $('.header-icon .material-symbols-outlined').html('sim_card_download')
                }else{
                    $('.header-bg , .alt-stash-inventory').css('display', 'none');
                    $('.player-inventory .item-slot[data-slot="52"]').css('display','none')
                    $('.player-inventory .item-slot[data-slot="53"]').css('display','none')
                    $('.header-bg , .alt-stash-inventory').css('display', 'flex');
                    $('.header-bg').css('background', 'transparent')
                    $('.header-bg .left-background').css('display', 'none')
                    $('.kg-text').html('Weapon not equipped')
                    $('.kg-text').css('color', 'red')
                    $('.header-icon .material-symbols-outlined').html('exclamation')
                }
            }
        );
    }else{
        // $('.header-text').text('Mobile Phone');
        $('.player-inventory .item-slot[data-slot="53"]').css('display','none')
        $('.player-inventory .item-slot[data-slot="52"]').css('display','block')
    }

})


currentItemData = null;
$(document).on("click", ".item-slot", function (e) {

    currentItemImg = $(this).find('.item-slot-img img').attr('src');
    currentItemLabel = $(this).find('.item-slot-label p').text();
    currentItemAmount = $(this).find('.item-slot-amount p').text();
    currentItemData = $(this).data('item');
    currentItem = $(this);

    e.preventDefault();

    var ItemData = $(this).data("item");
    currentItemData = ItemData;
    $(".combine-option-container").hide();
    if (ItemData !== null && ItemData !== undefined) {
        if (ItemData.name !== undefined) {
            found = false;
            $.each(allWeapons, function (i,v) { 
                if (v.name == ItemData.name) {
                    $('.item-simslot').css('display', 'flex');
                    $('.item-simslot').find('p').text("Open Weapon Slot");
                    $('.header-text').text(v.label);
                    slot = "weapon"
                    $('.player-inventory .item-slot[data-slot="52"]').css('display','none')
                    found = true;
                }else{
                    if (!found) {
                        if (ItemData.name==phoneItem) {
                            slot = "phone"
                            $('.player-inventory .item-slot[data-slot="53"]').css('display','none')
                            $('.item-simslot').css('display', 'flex');
                            $('.header-text').text('Mobile Phone');
                            $('.item-simslot').find('p').text("Open Sim Slot");
                        }else{
                            $('.item-simslot').css('display', 'none');
                        }
                    }
                }
            });



            $('.item-info-description').fadeOut(100);
            $(".inv-options").fadeIn(100);

            $(".inv-options").css({
                'top': mouseY+74 + 'px',
                'left': mouseX+80 + 'px',
                'display': 'block'
            });
            $('.inv-options-list').css('display', 'block');
            
            $('#item-split').css('display', 'none');
            $('#item-give , #item-use , .item-equip  , .giveitem').fadeIn(100);

            if ($(this).data("item").amount<=1) {
                $('#item-give').css('display', 'none');
            }else{
                $('#item-give').css('display', 'block');
            }
    

            if (ItemData.name.split("_")[0] == "weapon") {
                if (!$("#weapon-attachments").length) {
                    // $(".inv-options-list").append('<div class="inv-option-item" id="weapon-attachments"><p>ATTACHMENTS</p></div>');
                    $("#weapon-attachments").hide().fadeIn(250);
                    ClickedItemData = ItemData;
                } else if (ClickedItemData == ItemData) {
                    $("#weapon-attachments").fadeOut(250, function () {
                        $("#weapon-attachments").remove();
                    });
                    ClickedItemData = {};
                } else {
                    ClickedItemData = ItemData;
                }
            } else {
                ClickedItemData = {};
                if ($("#weapon-attachments").length) {
                    $("#weapon-attachments").fadeOut(250, function () {
                        $("#weapon-attachments").remove();
                    });
                }
            }
        } else {
            ClickedItemData = {};
            if ($("#weapon-attachments").length) {
                $("#weapon-attachments").fadeOut(250, function () {
                    $("#weapon-attachments").remove();
                });
            }
        }
    } else {
        ClickedItemData = {};
        if ($("#weapon-attachments").length) {
            $("#weapon-attachments").fadeOut(250, function () {
                $("#weapon-attachments").remove();
            });
        }
    }
});

$(document).on("click", "#inv-close", function (e) {
    e.preventDefault();
    Inventory.Close();
});

$(document).on("click", ".weapon-attachments-back", function (e) {
    e.preventDefault();
    $("#qbcore-inventory").css({ display: "block" });
    $("#qbcore-inventory").animate({ left: 0 + "vw" }, 200);
    $(".weapon-attachments-container").animate({ left: -100 + "vw" }, 200, function () {
        $(".weapon-attachments-container").css({ display: "none" });
    });
    AttachmentScreenActive = false;
});

function FormatAttachmentInfo(data) {
    $.post(
        "https://qb-inventory/GetWeaponData",
        JSON.stringify({
            weapon: data.name,
            ItemData: ClickedItemData,
        }),
        function (data) {
            var AmmoLabel = "9mm";
            var Durability = 100;
            if (data.WeaponData.ammotype == "AMMO_RIFLE") {
                AmmoLabel = "7.62";
            } else if (data.WeaponData.ammotype == "AMMO_SHOTGUN") {
                AmmoLabel = "12 Gauge";
            }
            if (ClickedItemData.info.quality !== undefined) {
                Durability = ClickedItemData.info.quality;
            }

            $(".weapon-attachments-container-title").html(data.WeaponData.label + " | " + AmmoLabel);
            $(".weapon-attachments-container-description").html(data.WeaponData.description);
            $(".weapon-attachments-container-details").html('<span style="font-weight: bold; letter-spacing: .1vh;">Serial Number</span><br> ' + ClickedItemData.info.serie + '<br><br><span style="font-weight: bold; letter-spacing: .1vh;">Durability - ' + Durability.toFixed() + '% </span> <div class="weapon-attachments-container-detail-durability"><div class="weapon-attachments-container-detail-durability-total"></div></div>');
            $(".weapon-attachments-container-detail-durability-total").css({
                width: Durability + "%",
            });
            $(".weapon-attachments-container-image").attr("src", "./attachment_images/" + data.WeaponData.name + ".png");
            $(".weapon-attachments").html("");

            if (data.AttachmentData !== null && data.AttachmentData !== undefined) {
                if (data.AttachmentData.length > 0) {
                    $(".weapon-attachments-title").html('<span style="font-weight: bold; letter-spacing: .1vh;">Attachments</span>');
                    $.each(data.AttachmentData, function (i, attachment) {
                        var WeaponType = data.WeaponData.ammotype.split("_")[1].toLowerCase();
                        $(".weapon-attachments").append('<div class="item-slot weapon-attachment" id="weapon-attachment-' + i + '"> <div class="item-slot-label"><p>' + attachment.label + '</p></div> <div class="item-slot-img"><img src="./images/' + attachment.attachment + '.png"></div> </div>');
                        attachment.id = i;
                        $("#weapon-attachment-" + i).data("AttachmentData", attachment);
                    });
                } else {
                    $(".weapon-attachments-title").html('<span style="font-weight: bold; letter-spacing: .1vh;">This gun doesn\'t contain attachments</span>');
                }
            } else {
                $(".weapon-attachments-title").html('<span style="font-weight: bold; letter-spacing: .1vh;">This gun doesn\'t contain attachments</span>');
            }

            handleAttachmentDrag();
        }
    );
}

var AttachmentDraggingData = {};

function handleAttachmentDrag() {
    $(".weapon-attachment").draggable({
        helper: "clone",
        appendTo: "body",
        scroll: true,
        revertDuration: 0,
        revert: "invalid",
        cursorAt: { top: Math.floor($(".item-slot").outerHeight() / 2), left: Math.floor($(".item-slot").outerWidth() / 2) },
        start: function (event, ui) {
            var ItemData = $(this).data("AttachmentData");
            $(this).addClass("weapon-dragging-class");
            $(ui.helper).css({
                width: $(this).width(),
                height: $(this).height(),
            });
            AttachmentDraggingData = ItemData;
        },
        stop: function () {
            $(this).removeClass("weapon-dragging-class");
        },
    });
    $(".weapon-attachments-remove").droppable({
        accept: ".weapon-attachment",
        hoverClass: "weapon-attachments-remove-hover",
        drop: function (event, ui) {
            $.post(
                "https://qb-inventory/RemoveAttachment",
                JSON.stringify({
                    AttachmentData: AttachmentDraggingData,
                    WeaponData: ClickedItemData,
                }),
                function (data) {
                    if (data.Attachments !== null && data.Attachments !== undefined) {
                        if (data.Attachments.length > 0) {
                            $("#weapon-attachment-" + AttachmentDraggingData.id).fadeOut(150, function () {
                                $("#weapon-attachment-" + AttachmentDraggingData.id).remove();
                                AttachmentDraggingData = null;
                            });
                        } else {
                            $("#weapon-attachment-" + AttachmentDraggingData.id).fadeOut(150, function () {
                                $("#weapon-attachment-" + AttachmentDraggingData.id).remove();
                                AttachmentDraggingData = null;
                                $(".weapon-attachments").html("");
                            });
                            $(".weapon-attachments-title").html('<span style="font-weight: bold; letter-spacing: .1vh;">This gun doesn\'t contain attachments</span>');
                        }
                    } else {
                        $("#weapon-attachment-" + AttachmentDraggingData.id).fadeOut(150, function () {
                            $("#weapon-attachment-" + AttachmentDraggingData.id).remove();
                            AttachmentDraggingData = null;
                            $(".weapon-attachments").html("");
                        });
                        $(".weapon-attachments-title").html('<span style="font-weight: bold; letter-spacing: .1vh;">This gun doesn\'t contain attachments</span>');
                    }
                }
            );
        },
    });
}

$(document).on("click", "#weapon-attachments", function (e) {
    e.preventDefault();
    if (!Inventory.IsWeaponBlocked(ClickedItemData.name)) {
        $(".weapon-attachments-container").css({ display: "block" });
        $("#qbcore-inventory").animate(
            {
                left: 100 + "vw",
            },
            200,
            function () {
                $("#qbcore-inventory").css({ display: "none" });
            }
        );
        $(".weapon-attachments-container").animate(
            {
                left: 0 + "vw",
            },
            200
        );
        AttachmentScreenActive = true;
        FormatAttachmentInfo(ClickedItemData);
    } else {
        $.post(
            "https://qb-inventory/Notify",
            JSON.stringify({
                message: "Attachments are unavailable for this gun.",
                type: "error",
            })
        );
    }
});

function getGender(info) {
    return info.gender === 1 ? "Woman" : "Man";
}

function setItemInfo(title, description,weight,durability) {
    $(".item-info-title").html(`<p>${title}</p>`);
    $(".item-info-description").html(`<div class="desc">${description}</div>`);
    $('.weight-text').html(`${weight/1000}kg`);
    if (durability == undefined) {
        $('.durability-text').html(`100`);
    }else{
        $('.durability-text').html(`${durability.toFixed()}%`);
    }
}

function generateDescription(itemData) {
    if (itemData.type === "weapon") {
        let ammo = itemData.info.ammo ?? 0;
        return `<p><strong>Serial Number: </strong><span>${itemData.info.serie}</span></p>
                    <p><strong>Ammunition: </strong><span>${ammo}</span></p>
                    <p>${itemData.description}</p>`;
    }
    switch (itemData.name) {
        case "id_card":
            return `<p><strong>CSN: </strong><span>${itemData.info.citizenid}</span></p>
              <p><strong>First Name: </strong><span>${itemData.info.firstname}</span></p>
              <p><strong>Last Name: </strong><span>${itemData.info.lastname}</span></p>
              <p><strong>Birth Date: </strong><span>${itemData.info.birthdate}</span></p>
              <p><strong>Gender: </strong><span>${getGender(itemData.info)}</span></p>
              <p><strong>Nationality: </strong><span>${itemData.info.nationality}</span></p>`;
        case "driver_license":
            return `<p><strong>First Name: </strong><span>${itemData.info.firstname}</span></p>
            <p><strong>Last Name: </strong><span>${itemData.info.lastname}</span></p>
            <p><strong>Birth Date: </strong><span>${itemData.info.birthdate}</span>
            </p><p><strong>Licenses: </strong><span>${itemData.info.type}</span></p>`;
        case "weaponlicense":
            return `<p><strong>First Name: </strong><span>${itemData.info.firstname}</span></p>`;
        case "lawyerpass":
            return `<p><strong>Pass-ID: </strong><span>${itemData.info.id}</span></p>
            <p><strong>First Name: </strong><span>${itemData.info.firstname}</span></p>
            <p><strong>Last Name: </strong><span>${itemData.info.lastname}</span></p>
            <p><strong>CSN: </strong><span>${itemData.info.citizenid}</span></p>`;
        case "harness":
            return `<p>${itemData.info.uses} uses left</p>`;
        case "filled_evidence_bag":
            if (itemData.info.type == "casing") {
                return `<p><strong>Evidence material: </strong><span>${itemData.info.label}</span></p>
                <p><strong>Type number: </strong><span>${itemData.info.ammotype}</span></p>
                <p><strong>Caliber: </strong><span>${itemData.info.ammolabel}</span></p>
                <p><strong>Serial Number: </strong><span>${itemData.info.serie}</span></p>
                <p><strong>Crime scene: </strong><span>${itemData.info.street}</span></p><br /><p>${itemData.description}</p>`;
            } else if (itemData.info.type == "blood") {
                return `<p><strong>Evidence material: </strong><span>${itemData.info.label}</span></p>
                <p><strong>Blood type: </strong><span>${itemData.info.bloodtype}</span></p>
                <p><strong>DNA Code: </strong><span>${itemData.info.dnalabel}</span></p>
                <p><strong>Crime scene: </strong><span>${itemData.info.street}</span></p><br /><p>${itemData.description}</p>`;
            } else if (itemData.info.type == "fingerprint") {
                return `<p><strong>Evidence material: </strong><span>${itemData.info.label}</span></p>
                <p><strong>Fingerprint: </strong><span>${itemData.info.fingerprint}</span></p>
                <p><strong>Crime Scene: </strong><span>${itemData.info.street}</span></p><br /><p>${itemData.description}</p>`;
            } else if (itemData.info.type == "dna") {
                return `<p><strong>Evidence material: </strong><span>${itemData.info.label}</span></p>
                <p><strong>DNA Code: </strong><span>${itemData.info.dnalabel}</span></p><br /><p>${itemData.description}</p>`;
            }
        case "stickynote":
            return `<p>${itemData.info.label}</p>`;
        case "moneybag":
            return `<p><strong>Amount of cash: </strong><span>$${itemData.info.cash}</span></p>`;
        case "markedbills":
            return `<p><strong>Worth: </strong><span>$${itemData.info.worth}</span></p>`;
        case "visa":
            return `<p><strong>Card Holder: </strong><span>${itemData.info.name}</span></p>`;
        case "mastercard":
            return `<p><strong>Card Holder: </strong><span>${itemData.info.name}</span></p>`;
        case "labkey":
            return `<p>Lab: ${itemData.info.lab}</p>`;
        default:
            let itemDescr = itemData.description;
	    if (itemData.info.costs != undefined && itemData.info.costs != null) itemDescr += `<p><strong>ITEMS NEEDED:</strong> <span>${itemData.info.costs}</span></p>`;
            return itemDescr;
    }
}

function FormatItemInfo(itemData) {
    if (itemData && itemData.info !== "") {
        const description = generateDescription(itemData);
        quality = itemData.info.quality;
        setItemInfo(itemData.label, description,itemData.weight,quality);
    } else {
        setItemInfo(itemData.label, itemData.description ,itemData.weight , quality|| "");
    }
}

$(document).on("wheel", function (e) {
    if (IsDragging) {
        var delta = e.originalEvent.deltaY;
        var $playerInventory = $(".player-inventory");
        var $otherInventory = $(".other-inventory");

        var playerInventoryOffset = $playerInventory.offset();
        var otherInventoryOffset = $otherInventory.offset();
        var mouseX = e.originalEvent.clientX;
        var mouseY = e.originalEvent.clientY;

        if (mouseX > playerInventoryOffset.left && mouseX < playerInventoryOffset.left + $playerInventory.width() && mouseY > playerInventoryOffset.top && mouseY < playerInventoryOffset.top + $playerInventory.height()) {
            $playerInventory.scrollTop($playerInventory.scrollTop() + delta);
        } else if (mouseX > otherInventoryOffset.left && mouseX < otherInventoryOffset.left + $otherInventory.width() && mouseY > otherInventoryOffset.top && mouseY < otherInventoryOffset.top + $otherInventory.height()) {
            $otherInventory.scrollTop($otherInventory.scrollTop() + delta);
        }

        if ((mouseX > playerInventoryOffset.left && mouseX < playerInventoryOffset.left + $playerInventory.width() && mouseY > playerInventoryOffset.top && mouseY < playerInventoryOffset.top + $playerInventory.height()) || (mouseX > otherInventoryOffset.left && mouseX < otherInventoryOffset.left + $otherInventory.width() && mouseY > otherInventoryOffset.top && mouseY < otherInventoryOffset.top + $otherInventory.height())) {
            e.preventDefault();
        }
    }
});

dragAmount = 0;
function handleDragDrop() {
    $(".item-drag").draggable({
        helper: "clone",
        appendTo: "body",
        scroll: false,
        revertDuration: 0,
        revert: "invalid",
        cancel: ".item-nodrag",
        cursorAt: { top: Math.floor($(".item-slot").outerHeight() / 2), left: Math.floor($(".item-slot").outerWidth() / 2) },
        start: function (event, ui) {
            IsDragging = true;
            $(this).find("img").css("filter", "brightness(50%)");
            $(ui.helper).css({
                width: $(this).width(),
                height: $(this).height(),
            });
            var itemData = $(this).data("item");
            dragAmount = $("#item-amount").val();
            if (dragAmount == 0) {
                if (itemData.price != null) {
                    $(this).find(".item-slot-amount p").html("0 (0.0)");
                    $(".ui-draggable-dragging")
                        .find(".item-slot-amount p")
                        .html("(" + itemData.amount + ") $" + itemData.price);
                    $(".ui-draggable-dragging").find(".item-slot-key").remove();
                    if ($(this).parent().attr("data-inventory") == "hotbar") {
                    }
                } else {
                    $(this).find(".item-slot-amount p").html("0 (0.0)");
                    $(".ui-draggable-dragging")
                        .find(".item-slot-amount p")
                        .html(itemData.amount + " (" + ((itemData.weight * itemData.amount) / 1000).toFixed(1) + ")");
                    $(".ui-draggable-dragging").find(".item-slot-key").remove();
                    if ($(this).parent().attr("data-inventory") == "hotbar") {
                    }
                }
            } else if (dragAmount > itemData.amount) {
                if (itemData.price != null) {
                    $(this)
                        .find(".item-slot-amount p")
                        .html("(" + itemData.amount + ") $" + itemData.price);
                    if ($(this).parent().attr("data-inventory") == "hotbar") {
                    }
                } else {
                    $(this)
                        .find(".item-slot-amount p")
                        .html(itemData.amount + " (" + ((itemData.weight * itemData.amount) / 1000).toFixed(1) + ")");
                    if ($(this).parent().attr("data-inventory") == "hotbar") {
                    }
                }
                console.log("INVENTORY ERROR 3");
                InventoryError($(this).parent(), $(this).attr("data-slot"));
            } else if (dragAmount > 0) {
                if (itemData.price != null) {
                    $(this)
                        .find(".item-slot-amount p")
                        .html("(" + itemData.amount + ") $" + itemData.price);
                    $(".ui-draggable-dragging")
                        .find(".item-slot-amount p")
                        .html("(" + itemData.amount + ") $" + itemData.price);
                    $(".ui-draggable-dragging").find(".item-slot-key").remove();
                    if ($(this).parent().attr("data-inventory") == "hotbar") {
                    }
                } else {
                    $(this)
                        .find(".item-slot-amount p")
                        .html(itemData.amount - dragAmount + " (" + ((itemData.weight * (itemData.amount - dragAmount)) / 1000).toFixed(1) + ")");
                    $(".ui-draggable-dragging")
                        .find(".item-slot-amount p")
                        .html(dragAmount + " (" + ((itemData.weight * dragAmount) / 1000).toFixed(1) + ")");
                    $(".ui-draggable-dragging").find(".item-slot-key").remove();
                    if ($(this).parent().attr("data-inventory") == "hotbar") {
                    }
                }
            } else {
                if ($(this).parent().attr("data-inventory") == "hotbar") {
                }
                $(".ui-draggable-dragging").find(".item-slot-key").remove();
                $(this)
                    .find(".item-slot-amount p")
                    .html(itemData.amount + " (" + ((itemData.weight * itemData.amount) / 1000).toFixed(1) + ")");
                    console.log("INVENTORY ERROR 4");
                InventoryError($(this).parent(), $(this).attr("data-slot"));
            }
        },
        stop: function () {
            setTimeout(function () {
                IsDragging = false;
            }, 300);
            // $(this).css("background", "rgba(0, 0, 0, 0.3)");
            $(this).find("img").css("filter", "brightness(100%)");
        },
    });

    $(".item-slot").droppable({
        hoverClass: "item-slot-hoverClass",
        drop: function (event, ui) {
            setTimeout(function () {
                IsDragging = false;
            }, 300);
            fromSlot = ui.draggable.attr("data-slot");
            fromInventory = ui.draggable.parent();
            toSlot = $(this).attr("data-slot");
            toInventory = $(this).parent();
            toInventoryDataName = toInventory.data("inventory");
            toAmount = $("#item-amount").val();
            
            if (toSlot==52 && toSlot==53) {
                console.log("Custom Slot");
                toInventory = $(".player-inventory");
            }

            if (fromSlot == toSlot && fromInventory == toInventory) {
                return;
            }
            if (toAmount >= 0) {
                if (updateweights(fromSlot, toSlot, fromInventory, toInventory, toAmount)) {
                    console.log("swap 2");
                    swap(fromSlot, toSlot, fromInventory, toInventory, toAmount);
                }
            }
        },
    });

    $("#item-use").droppable({
        hoverClass: "button-hover",
        drop: function (event, ui) {
            setTimeout(function () {
                IsDragging = false;
            }, 300);
            fromData = ui.draggable.data("item");
            fromInventory = ui.draggable.parent().attr("data-inventory");
            if (fromData.useable) {
                if (fromData.shouldClose) {
                    console.log("zort1");
                    Inventory.Close();
                }
                $.post(
                    "https://qb-inventory/UseItem",
                    JSON.stringify({
                        inventory: fromInventory,
                        item: fromData,
                    })
                );
            }
        },
    });

    $("#item-drop").droppable({
        hoverClass: "item-slot-hoverClass",
        drop: function (event, ui) {
            setTimeout(function () {
                IsDragging = false;
            }, 300);
            fromData = ui.draggable.data("item");
            fromInventory = ui.draggable.parent().attr("data-inventory");
            amount = $("#item-amount").val();
            if (amount == 0) {
                amount = fromData.amount;
            }
            $(this).css("background", "rgba(35,35,35, 0.7");
            $.post(
                "https://qb-inventory/DropItem",
                JSON.stringify({
                    inventory: fromInventory,
                    item: fromData,
                    amount: parseInt(amount),
                })
            );
        },
    });

    $("#item-amount").val(0);
}

function updateProgressBar(totalWeight, playerMaxWeight) {
    var percentage = (totalWeight / playerMaxWeight) * 100;
    var progressBarClass;

    if (percentage < 50) {
        progressBarClass = "";
    } else if (percentage >= 50 && percentage < 75) {
        progressBarClass = "ui-progressbar-medium";
    } else {
        progressBarClass = "ui-progressbar-high";
    }

    $("#player-inv-progressbar")
        .progressbar({ value: parseInt(totalWeight), max: playerMaxWeight })
        .removeClass("ui-progressbar-medium ui-progressbar-high")
        .addClass(progressBarClass);

    $("#player-inv-weight-value").text(totalWeight / 1000 + "/" + playerMaxWeight / 1000);
}

function updateOtherProgressBar(totalWeightOther, otherMaxWeight) {
    var percentage = (totalWeightOther / otherMaxWeight) * 100;
    var progressBarClass;

    if (percentage < 50) {
        progressBarClass = "";
    } else if (percentage >= 50 && percentage < 75) {
        progressBarClass = "ui-progressbar-medium";
    } else {
        progressBarClass = "ui-progressbar-high";
    }

    $("#other-inv-progressbar")
        .progressbar({ value: parseInt(totalWeightOther), max: otherMaxWeight })
        .removeClass("ui-progressbar-medium ui-progressbar-high")
        .addClass(progressBarClass);

    $("#other-inv-weight-value").text(totalWeightOther / 1000 + "/" + otherMaxWeight / 1000);
}

function updateweights($fromSlot, $toSlot, $fromInv, $toInv, $toAmount) {
    var otherinventory = otherLabel.toLowerCase();
    if (otherinventory.split("-")[0] == "dropped") {
        toData = $toInv.find("[data-slot=" + $toSlot + "]").data("item");
        if (toData !== null && toData !== undefined) {
            console.log("INVENTORY ERROR 5");
            InventoryError($fromInv, $fromSlot);
            return false;
        }
    }

    if (($fromInv.attr("data-inventory") == "hotbar" && $toInv.attr("data-inventory") == "player") || ($fromInv.attr("data-inventory") == "player" && $toInv.attr("data-inventory") == "hotbar") || ($fromInv.attr("data-inventory") == "player" && $toInv.attr("data-inventory") == "player") || ($fromInv.attr("data-inventory") == "hotbar" && $toInv.attr("data-inventory") == "hotbar")) {
        return true;
    }

    if (($fromInv.attr("data-inventory").split("-")[0] == "itemshop" && $toInv.attr("data-inventory").split("-")[0] == "itemshop") || ($fromInv.attr("data-inventory") == "crafting" && $toInv.attr("data-inventory") == "crafting") || ($fromInv.attr("data-inventory") == "attachment_crafting" && $toInv.attr("data-inventory") == "attachment_crafting")) {
        // itemData = $fromInv.find("[data-slot=" + $fromSlot + "]").data("item");
        if ($fromInv.attr("data-inventory").split("-")[0] == "itemshop") {
            $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-img"><img src="images/' + itemData.image + '" alt="' + itemData.name + '" /></div><div class="item-slot-amount"><p>(' + itemData.amount + ") $" + itemData.price + '</p></div><div class="item-slot-label"><p>' + itemData.label + "</p></div>");
        } else {
            $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-img"><img src="images/' + itemData.image + '" alt="' + itemData.name + '" /></div><div class="item-slot-amount"><p>' + itemData.amount + " (" + ((itemData.weight * itemData.amount) / 1000).toFixed(1) + ')</p></div><div class="item-slot-label"><p>' + itemData.label + "</p></div>");
        }

        console.log("INVENTORY ERROR 6")
        InventoryError($fromInv, $fromSlot);
        return false;
    }

    if ($toAmount == 0 && ($fromInv.attr("data-inventory").split("-")[0] == "itemshop" || $fromInv.attr("data-inventory") == "crafting"  || $fromInv.attr("data-inventory") == "attachment_crafting")) {
        itemData = $fromInv.find("[data-slot=" + $fromSlot + "]").data("item");
        if ($fromInv.attr("data-inventory").split("-")[0] == "itemshop") {
            $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-img"><img src="images/' + itemData.image + '" alt="' + itemData.name + '" /></div><div class="item-slot-amount"><p>(' + itemData.amount + ") $" + itemData.price + '</p></div><div class="item-slot-label"><p>' + itemData.label + "</p></div>");
        } else {
            $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-img"><img src="images/' + itemData.image + '" alt="' + itemData.name + '" /></div><div class="item-slot-amount"><p>' + itemData.amount + " (" + ((itemData.weight * itemData.amount) / 1000).toFixed(1) + ')</p></div><div class="item-slot-label"><p>' + itemData.label + "</p></div>");
        }
        console.log("INVENTORY ERROR 7")
        InventoryError($fromInv, $fromSlot);
        return false;
    }

    // split error controller

    

    if ($toInv.attr("data-inventory").split("-")[0] == "itemshop" || $toInv.attr("data-inventory") == "crafting" || $toInv.attr("data-inventory") == "attachment_crafting") {
        itemData = $toInv.find("[data-slot=" + $toSlot + "]").data("item");
        if ($toInv.attr("data-inventory").split("-")[0] == "itemshop") {
            $toInv.find("[data-slot=" + $toSlot + "]").html('<div class="item-slot-img"><img src="images/' + itemData.image + '" alt="' + itemData.name + '" /></div><div class="item-slot-amount"><p>(' + itemData.amount + ") $" + itemData.price + '</p></div><div class="item-slot-label"><p>' + itemData.label + "</p></div>");
        } else {
            $toInv.find("[data-slot=" + $toSlot + "]").html('<div class="item-slot-img"><img src="images/' + itemData.image + '" alt="' + itemData.name + '" /></div><div class="item-slot-amount"><p>' + itemData.amount + " (" + ((itemData.weight * itemData.amount) / 1000).toFixed(1) + ')</p></div><div class="item-slot-label"><p>' + itemData.label + "</p></div>");
        }

        console.log("INVENTORY ERROR 8")
        InventoryError($fromInv, $fromSlot);
        return false;
    }

    if ($fromInv.attr("data-inventory") != $toInv.attr("data-inventory")) {
        fromData = $fromInv.find("[data-slot=" + $fromSlot + "]").data("item");
        toData = $toInv.find("[data-slot=" + $toSlot + "]").data("item");
        if ($toAmount == 0) {
            $toAmount = fromData.amount;
        }
        if (toData == null || fromData.name == toData.name) {
            if ($fromInv.attr("data-inventory") == "player" || $fromInv.attr("data-inventory") == "hotbar") {
                totalWeight = totalWeight - fromData.weight * $toAmount;
                totalWeightOther = totalWeightOther + fromData.weight * $toAmount;
            } else {
                totalWeight = totalWeight + fromData.weight * $toAmount;
                totalWeightOther = totalWeightOther - fromData.weight * $toAmount;
            }
        } else {
            if ($fromInv.attr("data-inventory") == "player" || $fromInv.attr("data-inventory") == "hotbar") {
                totalWeight = totalWeight - fromData.weight * $toAmount;
                totalWeight = totalWeight + toData.weight * toData.amount;

                totalWeightOther = totalWeightOther + fromData.weight * $toAmount;
                totalWeightOther = totalWeightOther - toData.weight * toData.amount;
            } else {
                totalWeight = totalWeight + fromData.weight * $toAmount;
                totalWeight = totalWeight - toData.weight * toData.amount;

                totalWeightOther = totalWeightOther - fromData.weight * $toAmount;
                totalWeightOther = totalWeightOther + toData.weight * toData.amount;
            }
        }
    }

    if (totalWeight > playerMaxWeight || (totalWeightOther > otherMaxWeight && $fromInv.attr("data-inventory").split("-")[0] != "itemshop" && $fromInv.attr("data-inventory") != "crafting" && $fromInv.attr("data-inventory") != "attachment_crafting")) {
        console.log("INVENTORY ERROR 9")
        InventoryError($fromInv, $fromSlot);
        return false;
    }
    updateProgressBar(parseInt(totalWeight), playerMaxWeight);
    if ($fromInv.attr("data-inventory").split("-")[0] != "itemshop" && $toInv.attr("data-inventory").split("-")[0] != "itemshop" && $fromInv.attr("data-inventory") != "crafting" && $toInv.attr("data-inventory") != "attachment_crafting") {
        $("#other-inv-label").html(otherLabel);
        updateOtherProgressBar(parseInt(totalWeightOther), otherMaxWeight);
    }
    return true;
}

var combineslotData = null;

$(document).on("click", ".CombineItem", function (e) {
    e.preventDefault();
    if (combineslotData.toData.combinable.anim != null) {
        $.post(
            "https://qb-inventory/combineWithAnim",
            JSON.stringify({
                combineData: combineslotData.toData.combinable,
                usedItem: combineslotData.toData.name,
                requiredItem: combineslotData.fromData.name,
            })
        );
    } else {
        $.post(
            "https://qb-inventory/combineItem",
            JSON.stringify({
                reward: combineslotData.toData.combinable.reward,
                toItem: combineslotData.toData.name,
                fromItem: combineslotData.fromData.name,
            })
        );
    }
    Inventory.Close();
});

$(document).on("click", ".SwitchItem", function (e) {
    e.preventDefault();
    $(".combine-option-container").hide();

    optionSwitch(combineslotData.fromSlot, combineslotData.toSlot, combineslotData.fromInv, combineslotData.toInv, combineslotData.toAmount, combineslotData.toData, combineslotData.fromData);
});

function optionSwitch($fromSlot, $toSlot, $fromInv, $toInv, $toAmount, toData, fromData) {
    fromData.slot = parseInt($toSlot);

    $toInv.find("[data-slot=" + $toSlot + "]").data("item", fromData);

    $toInv.find("[data-slot=" + $toSlot + "]").addClass("item-drag");
    $toInv.find("[data-slot=" + $toSlot + "]").removeClass("item-nodrag");

    if ($toSlot < 6) {
        $toInv.find("[data-slot=" + $toSlot + "]").html('<div class="item-slot-key"><p>' + $toSlot + '</p></div><div class="item-slot-img"><img src="images/' + fromData.image + '" alt="' + fromData.name + '" /></div><div class="item-slot-amount"><p>' + fromData.amount + " (" + ((fromData.weight * fromData.amount) / 1000).toFixed(1) + ')</p></div><div class="item-slot-label"><p>' + fromData.label + "</p></div>");
    } else {
        $toInv.find("[data-slot=" + $toSlot + "]").html('<div class="item-slot-img"><img src="images/' + fromData.image + '" alt="' + fromData.name + '" /></div><div class="item-slot-amount"><p>' + fromData.amount + " (" + ((fromData.weight * fromData.amount) / 1000).toFixed(1) + ')</p></div><div class="item-slot-label"><p>' + fromData.label + "</p></div>");
    }

    toData.slot = parseInt($fromSlot);

    $fromInv.find("[data-slot=" + $fromSlot + "]").addClass("item-drag");
    $fromInv.find("[data-slot=" + $fromSlot + "]").removeClass("item-nodrag");

    $fromInv.find("[data-slot=" + $fromSlot + "]").data("item", toData);

    if ($fromSlot < 6) {
        $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-key"><p>' + $fromSlot + '</p></div><div class="item-slot-img"><img src="images/' + toData.image + '" alt="' + toData.name + '" /></div><div class="item-slot-amount"><p>' + toData.amount + " (" + ((toData.weight * toData.amount) / 1000).toFixed(1) + ')</p></div><div class="item-slot-label"><p>' + toData.label + "</p></div>");
    } else {
        $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-img"><img src="images/' + toData.image + '" alt="' + toData.name + '" /></div><div class="item-slot-amount"><p>' + toData.amount + " (" + ((toData.weight * toData.amount) / 1000).toFixed(1) + ')</p></div><div class="item-slot-label"><p>' + toData.label + "</p></div>");
    }

    console.log("ZORTLADIM KNAK 1 ");
    $.post(
        "https://qb-inventory/SetInventoryData",
        JSON.stringify({
            fromInventory: $fromInv.attr("data-inventory"),
            toInventory: $toInv.attr("data-inventory"),
            fromSlot: $fromSlot,
            toSlot: $toSlot,
            fromAmount: $toAmount,
            toAmount: toData.amount,
            dropType: dropType
        })
    );
}

function swap($fromSlot, $toSlot, $fromInv, $toInv, $toAmount,drType) {
    console.log("SWAP CALISTI");
    if (drType != undefined) {
        dropType = drType;
    } else {
        dropType = null;
    }
    
    fromData = $fromInv.find("[data-slot=" + $fromSlot + "]").data("item");
    toData = $toInv.find("[data-slot=" + $toSlot + "]").data("item");
    var otherinventory = otherLabel.toLowerCase();


    if (otherinventory.split("-")[0] == "dropped") {
        if (toData !== null && toData !== undefined) {
            console.log("INVENTORY ERROR 10");
            InventoryError($fromInv, $fromSlot);
            return;
        }
    }

    if (fromData !== undefined && fromData.amount >= $toAmount) {
        if (fromData.unique && $toAmount > 1) {
            console.log("INVENTORY ERROR 11");
            InventoryError($fromInv, $fromSlot);
            return;
        }

        if (($fromInv.attr("data-inventory") == "player" || $fromInv.attr("data-inventory") == "hotbar") && $toInv.attr("data-inventory").split("-")[0] == "itemshop" && $toInv.attr("data-inventory") == "crafting") {
            console.log("INVENTORY ERROR 12");
            InventoryError($fromInv, $fromSlot);
            return;
        }


        if ($toAmount == 0 && $fromInv.attr("data-inventory").split("-")[0] == "itemshop" && $fromInv.attr("data-inventory") == "crafting") {
            console.log("INVENTORY ERROR 13");
            InventoryError($fromInv, $fromSlot);
            return;
        } else if ($toAmount == 0) {
            $toAmount = fromData.amount;
        }
        if ((toData != undefined || toData != null) && toData.name == fromData.name && !fromData.unique) {
            var newData = [];
            newData.name = toData.name;
            newData.label = toData.label;
            newData.amount = parseInt($toAmount) + parseInt(toData.amount);
            newData.type = toData.type;
            newData.description = toData.description;
            newData.image = toData.image;
            newData.weight = toData.weight;
            newData.info = toData.info;
            newData.useable = toData.useable;
            newData.unique = toData.unique;
            newData.slot = parseInt($toSlot);

            if (fromData.amount == $toAmount) {
                $toInv.find("[data-slot=" + $toSlot + "]").data("item", newData);

                $toInv.find("[data-slot=" + $toSlot + "]").addClass("item-drag");
                $toInv.find("[data-slot=" + $toSlot + "]").removeClass("item-nodrag");

                var ItemLabel = '<div class="item-slot-label"><p>' + newData.label + "</p></div>";
                if (newData.name.split("_")[0] == "weapon") {
                    if (!Inventory.IsWeaponBlocked(newData.name)) {
                        ItemLabel = `<div class="item-slot-quality"><div class="item-slot-quality-bar"><p>100</p></div></div><div class="item-slot-label"><p>' ${newData.label} "</p></div>`;
                    }
                }

                if ($toSlot < 6 && $toInv.attr("data-inventory") == "player") {
                    $toInv.find("[data-slot=" + $toSlot + "]").html('<div class="item-slot-key"><p>' + $toSlot + '</p></div><div class="item-slot-img"><img src="images/' + newData.image + '" alt="' + newData.name + '" /></div><div class="item-slot-amount"><p>' + newData.amount + " (" + ((newData.weight * newData.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                } else if ($toSlot == 41 && $toInv.attr("data-inventory") == "player") {
                    $toInv.find("[data-slot=" + $toSlot + "]").html('<div class="item-slot-key"><p>6</p></div><div class="item-slot-img"><img src="images/' + newData.image + '" alt="' + newData.name + '" /></div><div class="item-slot-amount"><p>' + newData.amount + " (" + ((newData.weight * newData.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                } else {
                    console.log("tiss 1");
                    $toInv.find("[data-slot=" + $toSlot + "]").html('<div class="item-slot-img"><img src="images/' + newData.image + '" alt="' + newData.name + '" /></div><div class="item-slot-amount"><p>' + newData.amount + " (" + ((newData.weight * newData.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                }

                if (newData.name.split("_")[0] == "weapon") {
                    if (!Inventory.IsWeaponBlocked(newData.name)) {
                        if (newData.info.quality == undefined) {
                            newData.info.quality = 100.0;
                        }
                        var QualityColor = "rgb(0 248 185)";
                        if (newData.info.quality < 25) {
                            QualityColor = "rgb(192, 57, 43)";
                        } else if (newData.info.quality > 25 && newData.info.quality < 50) {
                            QualityColor = "rgb(230, 126, 34)";
                        } else if (newData.info.quality >= 50) {
                            QualityColor = "rgb(0 248 185)";
                        }
                        if (newData.info.quality !== undefined) {
                            qualityLabel = newData.info.quality.toFixed();
                        } else {
                            qualityLabel = newData.info.quality;
                        }
                        if (newData.info.quality == 0) {
                            qualityLabel = "BROKEN";
                        }
                        $toInv
                            .find("[data-slot=" + $toSlot + "]")
                            .find(".item-slot-quality-bar")
                            .css({
                                width: qualityLabel + "%",
                                "background-color": QualityColor,
                            })
                            .find("p")
                            .html(qualityLabel);
                    }
                }

                $fromInv.find("[data-slot=" + $fromSlot + "]").removeClass("item-drag");
                $fromInv.find("[data-slot=" + $fromSlot + "]").addClass("item-nodrag");

                $fromInv.find("[data-slot=" + $fromSlot + "]").removeData("item");
                $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-img"></div><div class="item-slot-label"><p>&nbsp;</p></div>');
            } else if (fromData.amount > $toAmount) {
                var newDataFrom = [];
                newDataFrom.name = fromData.name;
                newDataFrom.label = fromData.label;
                newDataFrom.amount = parseInt(fromData.amount - $toAmount);
                newDataFrom.type = fromData.type;
                newDataFrom.description = fromData.description;
                newDataFrom.image = fromData.image;
                newDataFrom.weight = fromData.weight;
                newDataFrom.price = fromData.price;
                newDataFrom.info = fromData.info;
                newDataFrom.useable = fromData.useable;
                newDataFrom.unique = fromData.unique;
                newDataFrom.slot = parseInt($fromSlot);

                $toInv.find("[data-slot=" + $toSlot + "]").data("item", newData);

                $toInv.find("[data-slot=" + $toSlot + "]").addClass("item-drag");
                $toInv.find("[data-slot=" + $toSlot + "]").removeClass("item-nodrag");

                var ItemLabel = '<div class="item-slot-label"><p>' + newData.label + "</p></div>";
                if (newData.name.split("_")[0] == "weapon") {
                    if (!Inventory.IsWeaponBlocked(newData.name)) {
                        ItemLabel = '<div class="item-slot-quality"><div class="item-slot-quality-bar"><p>100</p></div></div><div class="item-slot-label"><p>' + newData.label + "</p></div>";
                    }
                }

                if ($toSlot < 6 && $toInv.attr("data-inventory") == "player") {
                    $toInv.find("[data-slot=" + $toSlot + "]").html('<div class="item-slot-key"><p>' + $toSlot + '</p></div><div class="item-slot-img"><img src="images/' + newData.image + '" alt="' + newData.name + '" /></div><div class="item-slot-amount"><p>' + newData.amount + " (" + ((newData.weight * newData.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                } else if ($toSlot == 41 && $toInv.attr("data-inventory") == "player") {
                    $toInv.find("[data-slot=" + $toSlot + "]").html('<div class="item-slot-key"><p>6</p></div><div class="item-slot-img"><img src="images/' + newData.image + '" alt="' + newData.name + '" /></div><div class="item-slot-amount"><p>' + newData.amount + " (" + ((newData.weight * newData.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                } else {
                    console.log("tiss 2");
                    $toInv.find("[data-slot=" + $toSlot + "]").html('<div class="item-slot-img"><img src="images/' + newData.image + '" alt="' + newData.name + '" /></div><div class="item-slot-amount"><p>' + newData.amount + " (" + ((newData.weight * newData.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                }

                if (newData.name.split("_")[0] == "weapon") {
                    if (!Inventory.IsWeaponBlocked(newData.name)) {
                        if (newData.info.quality == undefined) {
                            newData.info.quality = 100.0;
                        }
                        var QualityColor = "rgb(0 248 185)";
                        if (newData.info.quality < 25) {
                            QualityColor = "rgb(192, 57, 43)";
                        } else if (newData.info.quality > 25 && newData.info.quality < 50) {
                            QualityColor = "rgb(230, 126, 34)";
                        } else if (newData.info.quality >= 50) {
                            QualityColor = "rgb(0 248 185)";
                        }
                        if (newData.info.quality !== undefined) {
                            qualityLabel = newData.info.quality.toFixed();
                        } else {
                            qualityLabel = newData.info.quality;
                        }
                        if (newData.info.quality == 0) {
                            qualityLabel = "BROKEN";
                        }
                        $toInv
                            .find("[data-slot=" + $toSlot + "]")
                            .find(".item-slot-quality-bar")
                            .css({
                                width: qualityLabel + "%",
                                "background-color": QualityColor,
                            })
                            .find("p")
                            .html(qualityLabel);
                    }
                }

                $fromInv.find("[data-slot=" + $fromSlot + "]").data("item", newDataFrom);

                $fromInv.find("[data-slot=" + $fromSlot + "]").addClass("item-drag");
                $fromInv.find("[data-slot=" + $fromSlot + "]").removeClass("item-nodrag");

                if ($fromInv.attr("data-inventory").split("-")[0] == "itemshop") {
                    $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-img"><img src="images/' + newDataFrom.image + '" alt="' + newDataFrom.name + '" /></div><div class="item-slot-amount"><p>(' + newDataFrom.amount + ") $" + newDataFrom.price + '</p></div><div class="item-slot-label"><p>' + newDataFrom.label + "</p></div>");
                } else {
                    var ItemLabel = '<div class="item-slot-label"><p>' + newDataFrom.label + "</p></div>";
                    if (newDataFrom.name.split("_")[0] == "weapon") {
                        if (!Inventory.IsWeaponBlocked(newDataFrom.name)) {
                            ItemLabel = '<div class="item-slot-quality"><div class="item-slot-quality-bar"><p>100</p></div></div><div class="item-slot-label"><p>' + newDataFrom.label + "</p></div>";
                        }
                    }

                    if ($fromSlot < 6 && $fromInv.attr("data-inventory") == "player") {
                        $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-key"><p>' + $fromSlot + '</p></div><div class="item-slot-img"><img src="images/' + newDataFrom.image + '" alt="' + newDataFrom.name + '" /></div><div class="item-slot-amount"><p>' + newDataFrom.amount + " (" + ((newDataFrom.weight * newDataFrom.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                    } else if ($fromSlot == 41 && $fromInv.attr("data-inventory") == "player") {
                        $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-key"><p>6</p></div><div class="item-slot-img"><img src="images/' + newDataFrom.image + '" alt="' + newDataFrom.name + '" /></div><div class="item-slot-amount"><p>' + newDataFrom.amount + " (" + ((newDataFrom.weight * newDataFrom.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                    } else {
                        console.log("tiss 3");
                        $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-img"><img src="images/' + newDataFrom.image + '" alt="' + newDataFrom.name + '" /></div><div class="item-slot-amount"><p>' + newDataFrom.amount + " (" + ((newDataFrom.weight * newDataFrom.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                    }

                    if (newDataFrom.name.split("_")[0] == "weapon") {
                        if (!Inventory.IsWeaponBlocked(newDataFrom.name)) {
                            if (newDataFrom.info.quality == undefined) {
                                newDataFrom.info.quality = 100.0;
                            }
                            var QualityColor = "rgb(0 248 185)";
                            if (newDataFrom.info.quality < 25) {
                                QualityColor = "rgb(192, 57, 43)";
                            } else if (newDataFrom.info.quality > 25 && newDataFrom.info.quality < 50) {
                                QualityColor = "rgb(230, 126, 34)";
                            } else if (newDataFrom.info.quality >= 50) {
                                QualityColor = "rgb(0 248 185)";
                            }
                            if (newDataFrom.info.quality !== undefined) {
                                qualityLabel = newDataFrom.info.quality.toFixed();
                            } else {
                                qualityLabel = newDataFrom.info.quality;
                            }
                            if (newDataFrom.info.quality == 0) {
                                qualityLabel = "BROKEN";
                            }
                            $fromInv
                                .find("[data-slot=" + $fromSlot + "]")
                                .find(".item-slot-quality-bar")
                                .css({
                                    width: qualityLabel + "%",
                                    "background-color": QualityColor,
                                })
                                .find("p")
                                .html(qualityLabel);
                        }
                    }
                }
            }
            console.log("ZORTLADIM KNAK 2 ");
            $.post("https://qb-inventory/PlayDropSound", JSON.stringify({}));
            $.post(
                "https://qb-inventory/SetInventoryData",
                JSON.stringify({
                    fromInventory: $fromInv.attr("data-inventory"),
                    toInventory: $toInv.attr("data-inventory"),
                    fromSlot: $fromSlot,
                    toSlot: $toSlot,
                    fromAmount: $toAmount,
                    dropType: dropType
                })
            );
        } else {
            if (fromData.amount == $toAmount) {
                if (toData && toData.unique) {
                    var found = false; 
                    $.each(weaponsAmmoList, function (i, v) { 
                        if (toData.name == v) {
                            $.post("https://qb-inventory/PlayDropSound", JSON.stringify({}));
                            $.post(
                                "https://qb-inventory/SetInventoryData",
                                JSON.stringify({
                                    fromInventory: $fromInv.attr("data-inventory"),
                                    toInventory: $toInv.attr("data-inventory"),
                                    fromSlot: $fromSlot,
                                    toSlot: $toSlot,
                                    fromAmount: $toAmount,
                                    dropType: dropType
                                })
                            );
                            found = true; 
                            return false; 
                        }
                    });
                
                    if (!found) {
                        InventoryError($fromInv, $fromSlot);
                    }
                    return;
                }
                if (toData != undefined && toData.combinable != null && isItemAllowed(fromData.name, toData.combinable.accept)) {
                    $.post("https://qb-inventory/getCombineItem", JSON.stringify({ item: toData.combinable.reward }), function (item) {
                        $(".combine-option-text").html("<p>If you combine these items you get: <b>" + item.label + "</b></p>");
                    });
                    $(".combine-option-container").css("display", "flex");
                    $(".combine-option-container").fadeIn(100);
                    combineslotData = [];
                    combineslotData.fromData = fromData;
                    combineslotData.toData = toData;
                    combineslotData.fromSlot = $fromSlot;
                    combineslotData.toSlot = $toSlot;
                    combineslotData.fromInv = $fromInv;
                    combineslotData.toInv = $toInv;
                    combineslotData.toAmount = $toAmount;
                    return;
                }

                fromData.slot = parseInt($toSlot);

                $toInv.find("[data-slot=" + $toSlot + "]").data("item", fromData);

                $toInv.find("[data-slot=" + $toSlot + "]").addClass("item-drag");
                $toInv.find("[data-slot=" + $toSlot + "]").removeClass("item-nodrag");

                var ItemLabel = '<div class="item-slot-label"><p>' + fromData.label + "</p></div>";
                if (fromData.name.split("_")[0] == "weapon") {
                    if (!Inventory.IsWeaponBlocked(fromData.name)) {
                        ItemLabel = '<div class="item-slot-quality"><div class="item-slot-quality-bar"><p>100</p></div></div><div class="item-slot-label"><p>' + fromData.label + "</p></div>";
                    }
                }

                if ($toSlot < 6 && $toInv.attr("data-inventory") == "player") {
                    $toInv.find("[data-slot=" + $toSlot + "]").html('<div class="item-slot-key"><p>' + $toSlot + '</p></div><div class="item-slot-img"><img src="images/' + fromData.image + '" alt="' + fromData.name + '" /></div><div class="item-slot-amount"><p>' + fromData.amount + " (" + ((fromData.weight * fromData.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                } else if ($toSlot == 41 && $toInv.attr("data-inventory") == "player") {
                    $toInv.find("[data-slot=" + $toSlot + "]").html('<div class="item-slot-key"><p>6</p></div><div class="item-slot-img"><img src="images/' + fromData.image + '" alt="' + fromData.name + '" /></div><div class="item-slot-amount"><p>' + fromData.amount + " (" + ((fromData.weight * fromData.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                } else {
    
                    console.log("tiss 4" , currentItemLabel);
                    console.log("TO SLOT " ,toSlot);
                    if($toSlot==52 && currentItemLabel!=simCard){
                        console.log("kanka senin eşit gelmen çok saçma");
                        InventoryError($fromInv, $fromSlot);
                        return
                    }

                    if ($toSlot==53){
                        if (magazineList.hasOwnProperty(fromData.name)) {
                            $.post(
                                "https://qb-inventory/weaponreload",
                                JSON.stringify({
                                    fromInventory: $fromInv.attr("data-inventory"),
                                    toInventory: $toInv.attr("data-inventory"),
                                    fromSlot: $fromSlot,
                                    toSlot: $toSlot,
                                    fromAmount: $toAmount,
                                    fromData: $($fromInv.find("[data-slot=" + $fromSlot + "]")).data("item"),
                                    dropType: dropType
                                })
                            );
                            console.log("kanka senin eşit gelmen çok saçma");
                            // return false;
                        } else {
                            console.log("Bu özellik magazineList nesnesinde bulunmuyor." , fromData.name);
                        }
                    }else if($fromSlot==53){
                        console.log("SARJORU ÇIKARTTIK");
                        $.post(
                            "https://qb-inventory/removeMagazine",
                            JSON.stringify({
                                fromInventory: $fromInv.attr("data-inventory"),
                                toInventory: $toInv.attr("data-inventory"),
                                fromSlot: $fromSlot,
                                toSlot: $toSlot,
                                fromAmount: $toAmount,
                                fromData: $($fromInv.find("[data-slot=" + $fromSlot + "]")).data("item"),
                                dropType: dropType
                            })
                        )
                    }
                
                    if (!rightLabel.hasOwnProperty(currentItemLabel) && rightLabel.hasOwnProperty(rightItems[$toSlot]) ) {
                        console.log("PATLADIN KNKKK");
                            InventoryError($fromInv, $fromSlot);
                            return
                    }

                    if (rightLabel.hasOwnProperty(currentItemLabel) &&  rightItems[$toSlot] != undefined && rightItems[$toSlot] != currentItemLabel) {
                        console.log("kanka senin eşit gelmen çok saçma");
                            console.log("right item from slot " , rightItems[$fromSlot]);
                            console.log("current item label " , currentItemLabel);    
                        InventoryError($fromInv, $fromSlot);
                            return
                    }

                    $toInv.find("[data-slot=" + $toSlot + "]").html('<div class="item-slot-img"><img src="images/' + fromData.image + '" alt="' + fromData.name + '" /></div><div class="item-slot-amount"><p>' + fromData.amount + " (" + ((fromData.weight * fromData.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                }

                if (fromData.name.split("_")[0] == "weapon") {
                    if (!Inventory.IsWeaponBlocked(fromData.name)) {
                        if (fromData.info.quality == undefined) {
                            fromData.info.quality = 100.0;
                        }
                        var QualityColor = "rgb(0 248 185)";
                        if (fromData.info.quality < 25) {
                            QualityColor = "rgb(192, 57, 43)";
                        } else if (fromData.info.quality > 25 && fromData.info.quality < 50) {
                            QualityColor = "rgb(230, 126, 34)";
                        } else if (fromData.info.quality >= 50) {
                            QualityColor = "rgb(0 248 185)";
                        }
                        if (fromData.info.quality !== undefined) {
                            qualityLabel = fromData.info.quality.toFixed();
                        } else {
                            qualityLabel = fromData.info.quality;
                        }
                        if (fromData.info.quality == 0) {
                            qualityLabel = "BROKEN";
                        }
                        $toInv
                            .find("[data-slot=" + $toSlot + "]")
                            .find(".item-slot-quality-bar")
                            .css({
                                width: qualityLabel + "%",
                                "background-color": QualityColor,
                            })
                            .find("p")
                            .html(qualityLabel);
                    }
                }

                if (toData != undefined) {
                    toData.slot = parseInt($fromSlot);

                    $fromInv.find("[data-slot=" + $fromSlot + "]").addClass("item-drag");
                    $fromInv.find("[data-slot=" + $fromSlot + "]").removeClass("item-nodrag");

                    $fromInv.find("[data-slot=" + $fromSlot + "]").data("item", toData);

                    var ItemLabel = '<div class="item-slot-label"><p>' + toData.label + "</p></div>";
                    if (toData.name.split("_")[0] == "weapon") {
                        if (!Inventory.IsWeaponBlocked(toData.name)) {
                            ItemLabel = '<div class="item-slot-quality"><div class="item-slot-quality-bar"><p>100</p></div></div><div class="item-slot-label"><p>' + toData.label + "</p></div>";
                        }
                    }

                    if ($fromSlot < 6 && $fromInv.attr("data-inventory") == "player") {
                        $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-key"><p>' + $fromSlot + '</p></div><div class="item-slot-img"><img src="images/' + toData.image + '" alt="' + toData.name + '" /></div><div class="item-slot-amount"><p>' + toData.amount + " (" + ((toData.weight * toData.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                    } else if ($fromSlot == 41 && $fromInv.attr("data-inventory") == "player") {
                        $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-key"><p>6</p></div><div class="item-slot-img"><img src="images/' + toData.image + '" alt="' + toData.name + '" /></div><div class="item-slot-amount"><p>' + toData.amount + " (" + ((toData.weight * toData.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                    } else {
                        console.log("tiss 5");
                        $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-img"><img src="images/' + toData.image + '" alt="' + toData.name + '" /></div><div class="item-slot-amount"><p>' + toData.amount + " (" + ((toData.weight * toData.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                    }

                    if (toData.name.split("_")[0] == "weapon") {
                        if (!Inventory.IsWeaponBlocked(toData.name)) {
                            if (toData.info.quality == undefined) {
                                toData.info.quality = 100.0;
                            }
                            var QualityColor = "rgb(0 248 185)";
                            if (toData.info.quality < 25) {
                                QualityColor = "rgb(192, 57, 43)";
                            } else if (toData.info.quality > 25 && toData.info.quality < 50) {
                                QualityColor = "rgb(230, 126, 34)";
                            } else if (toData.info.quality >= 50) {
                                QualityColor = "rgb(0 248 185)";
                            }
                            if (toData.info.quality !== undefined) {
                                qualityLabel = toData.info.quality.toFixed();
                            } else {
                                qualityLabel = toData.info.quality;
                            }
                            if (toData.info.quality == 0) {
                                qualityLabel = "BROKEN";
                            }
                            $fromInv
                                .find("[data-slot=" + $fromSlot + "]")
                                .find(".item-slot-quality-bar")
                                .css({
                                    width: qualityLabel + "%",
                                    "background-color": QualityColor,
                                })
                                .find("p")
                                .html(qualityLabel);
                        }
                    }

                    console.log("ZORTLADIM KNAK 3 ");
                    $.post(
                        "https://qb-inventory/SetInventoryData",
                        JSON.stringify({
                            fromInventory: $fromInv.attr("data-inventory"),
                            toInventory: $toInv.attr("data-inventory"),
                            fromSlot: $fromSlot,
                            toSlot: $toSlot,
                            fromAmount: $toAmount,
                            toAmount: toData.amount,
                            dropType: dropType
                        })
                    );
                } else {
     
                    $fromInv.find("[data-slot=" + $fromSlot + "]").removeClass("item-drag");
                    $fromInv.find("[data-slot=" + $fromSlot + "]").addClass("item-nodrag");

                    $fromInv.find("[data-slot=" + $fromSlot + "]").removeData("item");

                    if ($fromSlot < 6 && $fromInv.attr("data-inventory") == "player") {
                        $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-key"><p>' + $fromSlot + '</p></div><div class="item-slot-img"></div><div class="item-slot-label"><p>&nbsp;</p></div>');
                    } else if ($fromSlot == 41 && $fromInv.attr("data-inventory") == "player") {
                        $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-key"><p>6</p></div><div class="item-slot-img"></div><div class="item-slot-label"><p>&nbsp;</p></div>');
                    } else {   
                            $fromInv.find("[data-slot=" + $fromSlot + "]").html(`
                            <div class="item-slot-img"></div><div class="item-slot-label"><p>&nbsp;</p></div>
                            `);
                    }

                    console.log("from inventory " , $fromInv.attr("data-inventory"));
                    console.log("to inventory " , $toInv.attr("data-inventory"));
                    $.post(
                        "https://qb-inventory/SetInventoryData",
                        JSON.stringify({
                            fromInventory: $fromInv.attr("data-inventory"),
                            toInventory: $toInv.attr("data-inventory"),
                            fromSlot: $fromSlot,
                            toSlot: $toSlot,
                            fromAmount: $toAmount,
                            dropType : dropType
                        })
                    );
                }
                $.post("https://qb-inventory/PlayDropSound", JSON.stringify({}));
            } else if (fromData.amount > $toAmount && (toData == undefined || toData == null)) {
                var newDataTo = [];
                newDataTo.name = fromData.name;
                newDataTo.label = fromData.label;
                newDataTo.amount = parseInt($toAmount);
                newDataTo.type = fromData.type;
                newDataTo.description = fromData.description;
                newDataTo.image = fromData.image;
                newDataTo.weight = fromData.weight;
                newDataTo.info = fromData.info;
                newDataTo.useable = fromData.useable;
                newDataTo.unique = fromData.unique;
                newDataTo.slot = parseInt($toSlot);

                $toInv.find("[data-slot=" + $toSlot + "]").data("item", newDataTo);

                $toInv.find("[data-slot=" + $toSlot + "]").addClass("item-drag");
                $toInv.find("[data-slot=" + $toSlot + "]").removeClass("item-nodrag");

                var ItemLabel = '<div class="item-slot-label"><p>' + newDataTo.label + "</p></div>";
                if (newDataTo.name.split("_")[0] == "weapon") {
                    if (!Inventory.IsWeaponBlocked(newDataTo.name)) {
                        ItemLabel = '<div class="item-slot-quality"><div class="item-slot-quality-bar"><p>100</p></div></div><div class="item-slot-label"><p>' + newDataTo.label + "</p></div>";
                    }
                }

                if ($toSlot < 6 && $toInv.attr("data-inventory") == "player") {
                    $toInv.find("[data-slot=" + $toSlot + "]").html('<div class="item-slot-key"><p>' + $toSlot + '</p></div><div class="item-slot-img"><img src="images/' + newDataTo.image + '" alt="' + newDataTo.name + '" /></div><div class="item-slot-amount"><p>' + newDataTo.amount + " (" + ((newDataTo.weight * newDataTo.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                } else if ($toSlot == 41 && $toInv.attr("data-inventory") == "player") {
                    $toInv.find("[data-slot=" + $toSlot + "]").html('<div class="item-slot-key"><p>6</p></div><div class="item-slot-img"><img src="images/' + newDataTo.image + '" alt="' + newDataTo.name + '" /></div><div class="item-slot-amount"><p>' + newDataTo.amount + " (" + ((newDataTo.weight * newDataTo.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                } else {
                    console.log("tiss 7");
                    $toInv.find("[data-slot=" + $toSlot + "]").html('<div class="item-slot-img"><img src="images/' + newDataTo.image + '" alt="' + newDataTo.name + '" /></div><div class="item-slot-amount"><p>' + newDataTo.amount + " (" + ((newDataTo.weight * newDataTo.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                }

                if (newDataTo.name.split("_")[0] == "weapon") {
                    if (!Inventory.IsWeaponBlocked(newDataTo.name)) {
                        if (newDataTo.info.quality == undefined) {
                            newDataTo.info.quality = 100.0;
                        }
                        var QualityColor = "rgb(0 248 185)";
                        if (newDataTo.info.quality < 25) {
                            QualityColor = "rgb(192, 57, 43)";
                        } else if (newDataTo.info.quality > 25 && newDataTo.info.quality < 50) {
                            QualityColor = "rgb(230, 126, 34)";
                        } else if (newDataTo.info.quality >= 50) {
                            QualityColor = "rgb(0 248 185)";
                        }
                        if (newDataTo.info.quality !== undefined) {
                            qualityLabel = newDataTo.info.quality.toFixed();
                        } else {
                            qualityLabel = newDataTo.info.quality;
                        }
                        if (newDataTo.info.quality == 0) {
                            qualityLabel = "BROKEN";
                        }
                        $toInv
                            .find("[data-slot=" + $toSlot + "]")
                            .find(".item-slot-quality-bar")
                            .css({
                                width: qualityLabel + "%",
                                "background-color": QualityColor,
                            })
                            .find("p")
                            .html(qualityLabel);
                    }
                }

                var newDataFrom = [];
                newDataFrom.name = fromData.name;
                newDataFrom.label = fromData.label;
                newDataFrom.amount = parseInt(fromData.amount - $toAmount);
                newDataFrom.type = fromData.type;
                newDataFrom.description = fromData.description;
                newDataFrom.image = fromData.image;
                newDataFrom.weight = fromData.weight;
                newDataFrom.price = fromData.price;
                newDataFrom.info = fromData.info;
                newDataFrom.useable = fromData.useable;
                newDataFrom.unique = fromData.unique;
                newDataFrom.slot = parseInt($fromSlot);

                $fromInv.find("[data-slot=" + $fromSlot + "]").data("item", newDataFrom);

                $fromInv.find("[data-slot=" + $fromSlot + "]").addClass("item-drag");
                $fromInv.find("[data-slot=" + $fromSlot + "]").removeClass("item-nodrag");

                if ($fromInv.attr("data-inventory").split("-")[0] == "itemshop") {
                    $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-img"><img src="images/' + newDataFrom.image + '" alt="' + newDataFrom.name + '" /></div><div class="item-slot-amount"><p>(' + newDataFrom.amount + ") $" + newDataFrom.price + '</p></div><div class="item-slot-label"><p>' + newDataFrom.label + "</p></div>");
                } else {
                    var ItemLabel = '<div class="item-slot-label"><p>' + newDataFrom.label + "</p></div>";
                    if (newDataFrom.name.split("_")[0] == "weapon") {
                        if (!Inventory.IsWeaponBlocked(newDataFrom.name)) {
                            ItemLabel = '<div class="item-slot-quality"><div class="item-slot-quality-bar"><p>100</p></div></div><div class="item-slot-label"><p>' + newDataFrom.label + "</p></div>";
                        }
                    }

                    if ($fromSlot < 6 && $fromInv.attr("data-inventory") == "player") {
                        $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-key"><p>' + $fromSlot + '</p></div><div class="item-slot-img"><img src="images/' + newDataFrom.image + '" alt="' + newDataFrom.name + '" /></div><div class="item-slot-amount"><p>' + newDataFrom.amount + " (" + ((newDataFrom.weight * newDataFrom.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                    } else if ($fromSlot == 41 && $fromInv.attr("data-inventory") == "player") {
                        $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-key"><p>6</p></div><div class="item-slot-img"><img src="images/' + newDataFrom.image + '" alt="' + newDataFrom.name + '" /></div><div class="item-slot-amount"><p>' + newDataFrom.amount + " (" + ((newDataFrom.weight * newDataFrom.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                    } else {
                        console.log("tiss 8");
                        $fromInv.find("[data-slot=" + $fromSlot + "]").html('<div class="item-slot-img"><img src="images/' + newDataFrom.image + '" alt="' + newDataFrom.name + '" /></div><div class="item-slot-amount"><p>' + newDataFrom.amount + " (" + ((newDataFrom.weight * newDataFrom.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                    }

                    if (newDataFrom.name.split("_")[0] == "weapon") {
                        if (!Inventory.IsWeaponBlocked(newDataFrom.name)) {
                            if (newDataFrom.info.quality == undefined) {
                                newDataFrom.info.quality = 100.0;
                            }
                            var QualityColor = "rgb(0 248 185)";
                            if (newDataFrom.info.quality < 25) {
                                QualityColor = "rgb(192, 57, 43)";
                            } else if (newDataFrom.info.quality > 25 && newDataFrom.info.quality < 50) {
                                QualityColor = "rgb(230, 126, 34)";
                            } else if (newDataFrom.info.quality >= 50) {
                                QualityColor = "rgb(0 248 185)";
                            }
                            if (newDataFrom.info.quality !== undefined) {
                                qualityLabel = newDataFrom.info.quality.toFixed();
                            } else {
                                qualityLabel = newDataFrom.info.quality;
                            }
                            if (newDataFrom.info.quality == 0) {
                                qualityLabel = "BROKEN";
                            }
                            $fromInv
                                .find("[data-slot=" + $fromSlot + "]")
                                .find(".item-slot-quality-bar")
                                .css({
                                    width: qualityLabel + "%",
                                    "background-color": QualityColor,
                                })
                                .find("p")
                                .html(qualityLabel);
                        }
                    }
                }
                console.log("ZORTLADIM KNAK 5 ");
                $.post("https://qb-inventory/PlayDropSound", JSON.stringify({}));
                $.post(
                    "https://qb-inventory/SetInventoryData",
                    JSON.stringify({
                        fromInventory: $fromInv.attr("data-inventory"),
                        toInventory: $toInv.attr("data-inventory"),
                        fromSlot: $fromSlot,
                        toSlot: $toSlot,
                        fromAmount: $toAmount,
                        dropType: dropType
                    })
                );
            } else {
                console.log("INVENTORY ERROR 15");
                InventoryError($fromInv, $fromSlot);
            }
        }
    } else {
    }
    handleDragDrop();
}

function isItemAllowed(item, allowedItems) {
    var retval = false;
    $.each(allowedItems, function (index, i) {
        if (i == item) {
            retval = true;
        }
    });
    return retval;
}

function InventoryError($elinv, $elslot) {
    $elinv
        .find("[data-slot=" + $elslot + "]")
        .css("background", "rgba(156, 20, 20, 0.5)")
        .css("transition", "background 500ms");
    setTimeout(function () {
        $elinv.find("[data-slot=" + $elslot + "]").css("background", "radial-gradient(83% 83% at 50% 50%,rgba(180,180,180,.15),rgba(120,120,120,.05))");
    }, 500);
    $.post("https://qb-inventory/PlayDropFail", JSON.stringify({}));
}

var requiredItemOpen = false;

(() => {
    Inventory = {};

    Inventory.slots = 40;

    Inventory.dropslots = 30;
    Inventory.droplabel = "Drop";
    Inventory.dropmaxweight = 100000;

    Inventory.Error = function () {
        $.post("https://qb-inventory/PlayDropFail", JSON.stringify({}));
    };

    Inventory.IsWeaponBlocked = function (WeaponName) {
        var DurabilityBlockedWeapons = ["weapon_unarmed"];

        var retval = false;
        $.each(DurabilityBlockedWeapons, function (i, name) {
            if (name == WeaponName) {
                retval = true;
            }
        });
        return retval;
    };

    Inventory.QualityCheck = function (item, IsHotbar, IsOtherInventory) {
        if (!Inventory.IsWeaponBlocked(item.name)) {
            if (item.name.split("_")[0] == "weapon") {
                if (item.info.quality == undefined) {
                    item.info.quality = 100;
                }
                var QualityColor = "rgb(0 248 185)";
                if (item.info.quality < 25) {
                    QualityColor = "rgb(192, 57, 43)";
                } else if (item.info.quality > 25 && item.info.quality < 50) {
                    QualityColor = "rgb(230, 126, 34)";
                } else if (item.info.quality >= 50) {
                    QualityColor = "rgb(0 248 185)";
                }
                if (item.info.quality !== undefined) {
                    qualityLabel = item.info.quality.toFixed();
                } else {
                    qualityLabel = item.info.quality;
                }
                if (item.info.quality == 0) {
                    qualityLabel = "BROKEN";
                    if (!IsOtherInventory) {
                        if (!IsHotbar) {
                            $(".player-inventory")
                                .find("[data-slot=" + item.slot + "]")
                                .find(".item-slot-quality-bar")
                                .css({
                                    width: "100%",
                                    "background-color": QualityColor,
                                })
                                .find("p")
                                .html(qualityLabel);
                        } else {
                            $(".z-hotbar-inventory")
                                .find("[data-zhotbarslot=" + item.slot + "]")
                                .find(".item-slot-quality-bar")
                                .css({
                                    width: "100%",
                                    "background-color": QualityColor,
                                })
                                .find("p")
                                .html(qualityLabel);
                        }
                    } else {
                        $(".other-inventory")
                            .find("[data-slot=" + item.slot + "]")
                            .find(".item-slot-quality-bar")
                            .css({
                                width: "100%",
                                "background-color": QualityColor,
                            })
                            .find("p")
                            .html(qualityLabel);
                    }
                } else {
                    if (!IsOtherInventory) {
                        if (!IsHotbar) {
                            $(".player-inventory")
                                .find("[data-slot=" + item.slot + "]")
                                .find(".item-slot-quality-bar")
                                .css({
                                    width: qualityLabel + "%",
                                    "background-color": QualityColor,
                                })
                                .find("p")
                                .html(qualityLabel);
                        } else {
                            $(".z-hotbar-inventory")
                                .find("[data-zhotbarslot=" + item.slot + "]")
                                .find(".item-slot-quality-bar")
                                .css({
                                    width: qualityLabel + "%",
                                    "background-color": QualityColor,
                                })
                                .find("p")
                                .html(qualityLabel);
                        }
                    } else {
                        $(".other-inventory")
                            .find("[data-slot=" + item.slot + "]")
                            .find(".item-slot-quality-bar")
                            .css({
                                width: qualityLabel + "%",
                                "background-color": QualityColor,
                            })
                            .find("p")
                            .html(qualityLabel);
                    }
                }
            }
        }
    };

    Inventory.Open = function (data) {
        console.log("MY ITEMS " , JSON.stringify(data.inventory));
        weaponsAmmoList = data.weaponsAmmoList;
        magazineList = data.magazineList;
        totalWeight = 0;
        totalWeightOther = 0;
        $('.health-system ').css('left', '-50%');
        $('.health-system').css('display', 'none');

        $('.player-inventory .item-slot[data-slot="52"]').css('display','none')
        $('.player-inventory .item-slot[data-slot="53"]').css('display','none')

        $('.player-inventory .item-slot[data-slot="52"]').css('display','none')
        $('.player-inventory .item-slot[data-slot="53"]').css('display','none')
        $('.header-bg , .alt-stash-inventory').css('display', 'none');


        $('.place-box').css('display', 'none');

        $(".player-inventory").find(".item-slot").remove();
        $(".ply-hotbar-inventory").find(".item-slot").remove();

        if (requiredItemOpen) {
            $(".requiredItem-container").hide();
            requiredItemOpen = false;
        }

        rightItems = data.realboss["rightItems"]
        leftItems = data.realboss["leftItems"]

        simCard = data.realboss["simCard"]
        phoneItem = data.realboss["phoneItem"]
        allWeapons = data.weapons


        fiveData = {
            "1": "headphones",
            "2": "domino_mask",
            "3": "eyeglasses",
            "4": "security",
            "5": "styler",
        }

        rightData = {
            "43" : "badge",
            "44" : "smartphone",
            "45" : "key",
            "46" : "wallet",
            "47" : "backpack",
        }

        $("#qbcore-inventory , body").fadeIn(300);
        if (data.other != null && data.other != "") {
            $(".other-inventory").attr("data-inventory", data.other.name);
        } else {
            $(".other-inventory").attr("data-inventory", 0);
        }
        for (i = 1; i < 6; i++) {
            $(".player-inventory").append('<div class="item-slot" data-slot="' + i + '"><div class="item-slot-key"><p>' + i + '</p></div><div class="item-slot-img"></div><div class="item-slot-label"><p>&nbsp;</p></div></div>');
        }
        for (i = 6; i < 53 + 1; i++) {
            if (i == 41) {
                $(".player-inventory").append('<div data-equip="true" class="item-slot" data-slot="' + i + '"><div class="item-slot-key"><p>6</p></div><div class="item-slot-img"></div><div class="item-slot-label"><p>&nbsp;</p></div></div>');
                $('.player-inventory .item-slot[data-slot="' + 41 + '"]').css('display','none')
            } else if(i<16){
                $(".player-inventory").append('<div class="item-slot" data-equip="true" data-slot="' + i + '"><div class="item-slot-img"></div><div class="item-slot-label"><p>&nbsp;</p></div></div>');
            }else if(i>=16 && i<40){
                $(".player-inventory .alt-inventory").append('<div class="item-slot" data-equip="true" data-slot="' + i + '"><div class="item-slot-img"></div><div class="item-slot-label"><p>&nbsp;</p></div></div>');
            }else if(i>41 && i<47) {
                $('.player-inventory .alt-alt-inventory').append(`
                <div class="item-slot" data-equip="true"  data-slot="${i}"> 
                <span style="color:#f3f4f61a" class="material-symbols-outlined emptyicon">${rightData[i + 1]}</span>
                 <div class="item-slot-img"></div>
                 <div class="item-slot-label"><p>&nbsp;</p></div>
                </div>`);
            }else if(i>=47 && i<=51){
                $('.player-inventory .alt-left-inventory').append(`
                <div class="item-slot" data-equip="true"  data-slot="${i}">
                <span style="color:#f3f4f61a" class="material-symbols-outlined emptyicon">${fiveData[i - 46]}</span>
                    <div class="item-slot-img"></div>
                    <div class="item-slot-label"><p>&nbsp;</p></div>
                </div>`);
            }else if(i>=52 && i<=53){
                $('.player-inventory .alt-stash-inventory').append(`
                <div class="item-slot" data-equip="true"  data-slot="${i}">
                    <div class="item-slot-img"></div>
                    <div class="item-slot-label"><p>&nbsp;</p></div>
                </div>`);
            }
        }


        if (data.other != null && data.other != "") {
            for (i = 1; i < data.other.slots + 1; i++) {
                $(".other-inventory").append('<div class="item-slot" data-slot="' + i + '"><div class="item-slot-img"></div><div class="item-slot-label"><p>&nbsp;</p></div></div>');
            }
        } else {
            for (i = 1; i < Inventory.dropslots + 1; i++) {
                $(".other-inventory").append('<div class="item-slot" data-slot="' + i + '"><div class="item-slot-img"></div><div class="item-slot-label"><p>&nbsp;</p></div></div>');
            }
        }

        if (data.inventory !== null) {
            $.each(data.inventory, function (i, item) {
                if (item != null) {
                    totalWeight += item.weight * item.amount;
                    var ItemLabel = '<div class="item-slot-label"><p>' + item.label + "</p></div>";
                    console.log("ITEM NAME " , JSON.stringify(item));
                    if (item.name.split("_")[0] == "weapon") {
                        if (!Inventory.IsWeaponBlocked(item.name)) {
                            ItemLabel = '<div class="item-slot-quality"><div class="item-slot-quality-bar"><p>100</p></div></div><div class="item-slot-label"><p>' + item.label + "</p></div>";
                        }
                    }
                    if (item.slot < 6) {
                        $(".player-inventory")
                            .find("[data-slot=" + item.slot + "]")
                            .addClass("item-drag");
                        $(".player-inventory")
                            .find("[data-slot=" + item.slot + "]")
                            .html('<div class="item-slot-key"><p>' + item.slot + '</p></div><div class="item-slot-img"><img src="images/' + item.image + '" alt="' + item.name + '" /></div><div class="item-slot-amount"><p>' + item.amount + " (" + ((item.weight * item.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                        $(".player-inventory")
                            .find("[data-slot=" + item.slot + "]")
                            .data("item", item);
                    } else if (item.slot == 41) {
                        $(".player-inventory")
                            .find("[data-slot=" + item.slot + "]")
                            .addClass("item-drag");
                        $(".player-inventory")
                            .find("[data-slot=" + item.slot + "]")
                            .html('<div class="item-slot-key"><p>6</p></div><div class="item-slot-img"><img src="images/' + item.image + '" alt="' + item.name + '" /></div><div class="item-slot-amount"><p>' + item.amount + " (" + ((item.weight * item.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                        $(".player-inventory")
                            .find("[data-slot=" + item.slot + "]")
                            .data("item", item);
                    } else {
                        $(".player-inventory").find("[data-slot=" + item.slot + "]").addClass("item-drag");
                        $(".player-inventory").find("[data-slot=" + item.slot + "]").html('<div class="item-slot-img"><img src="images/' + item.image + '" alt="' + item.name + '" /></div><div class="item-slot-amount"><p>' + item.amount + " (" + ((item.weight * item.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                        $(".player-inventory").find("[data-slot=" + item.slot + "]").data("item", item);
                        $(".player-inventory").find("[data-slot=" + item.slot + "]").data("equip", true);
                    }
                    Inventory.QualityCheck(item, false, false);
                }
            });
        }

        if (data.other != null && data.other != "" && data.other.inventory != null) {
            $.each(data.other.inventory, function (i, item) {
                if (item != null) {
                    totalWeightOther += item.weight * item.amount;
                    var ItemLabel = '<div class="item-slot-label"><p>' + item.label + "</p></div>";
                    if (item.name.split("_")[0] == "weapon") {
                        if (!Inventory.IsWeaponBlocked(item.name)) {
                            ItemLabel = '<div class="item-slot-quality"><div class="item-slot-quality-bar"><p>100</p></div></div><div class="item-slot-label"><p>' + item.label + "</p></div>";
                        }
                    }
                    $(".other-inventory")
                        .find("[data-slot=" + item.slot + "]")
                        .addClass("item-drag");
                    if (item.price != null) {
                        $(".other-inventory")
                            .find("[data-slot=" + item.slot + "]")
                            .html('<div class="item-slot-img"><img src="images/' + item.image + '" alt="' + item.name + '" /></div><div class="item-slot-amount"><p>(' + item.amount + ") $" + item.price + "</p></div>" + ItemLabel);
                    } else {
                        $(".other-inventory")
                            .find("[data-slot=" + item.slot + "]")
                            .html('<div class="item-slot-img"><img src="images/' + item.image + '" alt="' + item.name + '" /></div><div class="item-slot-amount"><p>' + item.amount + " (" + ((item.weight * item.amount) / 1000).toFixed(1) + ")</p></div>" + ItemLabel);
                    }
                    $(".other-inventory")
                        .find("[data-slot=" + item.slot + "]")
                        .data("item", item);
                    Inventory.QualityCheck(item, false, true);
                }
            });
        }
        updateProgressBar(totalWeight, data.maxweight);
        playerMaxWeight = data.maxweight;
        if (data.other != null) {
            var name = data.other.name.toString();
            if (name != null && (name.split("-")[0] == "itemshop" || name == "crafting")) {
                $("#other-inv-label").html(data.other.label);
            } else {
                $("#other-inv-label").html(data.other.label);
                updateOtherProgressBar(totalWeightOther, data.other.maxweight);
            }
            otherMaxWeight = data.other.maxweight;
            otherLabel = data.other.label;
        } else {
            $("#other-inv-label").html(Inventory.droplabel);
            $("#other-inv-weight").progressbar({ value: totalWeightOther, max: Inventory.dropmaxweight });
            otherMaxWeight = Inventory.dropmaxweight;
            otherLabel = Inventory.droplabel;
        }

        $.each(data.maxammo, function (index, ammotype) {
            $("#" + index + "_ammo")
                .find(".ammo-box-amount")
                .css({ height: "0%" });
        });

        if (data.Ammo !== null) {
            $.each(data.Ammo, function (i, amount) {
                var Handler = i.split("_");
                var Type = Handler[1].toLowerCase();
                if (amount > data.maxammo[Type]) {
                    amount = data.maxammo[Type];
                }
                var Percentage = (amount / data.maxammo[Type]) * 100;

                $("#" + Type + "_ammo")
                    .find(".ammo-box-amount")
                    .css({ height: Percentage + "%" });
                $("#" + Type + "_ammo")
                    .find("span")
                    .html(amount + "x");
            });
        }

        handleDragDrop();

        weaponsName = data["weapons"]
        settingsName = data.filter["settings"]
        foodsName = data.filter["foods"]
        clothesName = data.filter["clothes"]
        boxName = data.filter["boxs"]


        $('.char-info').css('display', 'none');
        
        healthData = data.firstData;

        var woundColors = {
            Head: "rgba(255, 103, 103, {opacity})",
            Chest: "rgba(255, 103, 103, {opacity})",
            RArm: "rgba(255, 103, 103, {opacity})",
            LArm: "rgba(255, 103, 103, {opacity})",
            RKnee: "rgba(255, 103, 103, {opacity})",
            LKnee: "rgba(255, 103, 103, {opacity})"
        };

        Object.keys(woundColors).forEach(function(area) {
           var opacity = 100 - healthData[area].health;
           var color = woundColors[area].replace("{opacity}", opacity/100);
           $('.'+area).css("fill", color);
           $('.bar-'+area).css("width", healthData[area].health + "%" );
            $('.health-'+area).html(healthData[area].health + "%");
        })



        fiveData = {
            "1": "headphones",
            "2": "domino_mask",
            "3": "eyeglasses",
            "4": "security",
            "5": "styler",
        }

        rightData = {
            "1" : "badge",
            "2" : "smartphone",
            "3" : "key",
            "4" : "wallet",
            "5" : "backpack",
        }

        // rightLabel = {
        //     "Phone" : "smartphone",
        //     "badge" : "badge",
        //     "key" : "key",
        //     "wallet" : "wallet",
        //     "backpack" : "backpack",
        // }

        rightLabel = data.realboss["rightItemsLabel"]
        rightItems = data.realboss["rightItems"]
        leftItems = data.realboss["leftItems"]


        
        var bossPlayerInventory = $(".player-inventory .item-slot");

        bossPlayerInventory.each(function() {
            var label = $(this).find(".item-slot-label").text().toLowerCase();
            
        
            if (Object.values(leftItems).includes(label)) {

                $('.five-inventory .item-slot[data-slot="' + label + '"] .item-slot-label p').text(label);
                
                // $('.five-inventory .item-slot[data-slot="' + label + '"] .emptyicon').css('display', 'none');

                $('.five-inventory .item-slot[data-slot="' + label + '"] .item-slot-img').html($(this).find(".item-slot-img").html());

                $('.five-inventory .item-slot[data-slot="' + label + '"] .item-slot-amount').html($(this).find(".item-slot-amount").html());

                $('.five-inventory .item-slot[data-slot="' + label + '"] .item-slot-label').html($(this).find(".item-slot-label").html());

                data = $(this).data('item');

                $('.five-inventory .item-slot[data-slot="' + label + '"]').data('item', data);

                $(this).removeClass('item-drag ui-draggable ui-draggable-handle ui-droppable');
                $(this).find(".item-slot-img").html('');
                $(this).find(".item-slot-amount").html('');
                $(this).find(".item-slot-label").html('<p>&nbsp;</p>');
                $(this).addClass('item-slot ui-droppable item-nodrag');
                $(this).data('item', null);
            }
        
            if (Object.values(rightItems).includes(label)) {

                $('.alt-alt-inventory .item-slot[data-zort="' + label + '"] .item-slot-label p').text(label);

                $('.alt-alt-inventory .item-slot[data-zort="' + label + '"] .emptyicon').css('display', 'none');

                $('.alt-alt-inventory .item-slot[data-zort="' + label + '"] .item-slot-img').html($(this).find(".item-slot-img").html());
                
                $('.alt-alt-inventory .item-slot[data-zort="' + label + '"] .item-slot-amount').html($(this).find(".item-slot-amount").html());

                $('.alt-alt-inventory .item-slot[data-zort="' + label + '"] .item-slot-label').html($(this).find(".item-slot-label").html());

                data = $(this).data('item');

                // get item first slot
                

                $('.alt-alt-inventory .item-slot[data-zort="' + label + '"]').data('item', data);

                $(this).removeClass('item-drag ui-draggable ui-draggable-handle ui-droppable');
                $(this).find(".item-slot-img").html('');
                $(this).find(".item-slot-amount").html('');
                $(this).find(".item-slot-label").html('<p>&nbsp;</p>');
                $(this).addClass('item-slot ui-droppable item-nodrag');
                $(this).data('item', null);

            }
        
        });


};

    Inventory.Close = function () {
        $(".item-slot").css("border", "1px solid rgba(255, 255, 255, 0.1)");
        $(".ply-hotbar-inventory").css("display", "block");
        $(".ply-iteminfo-container").css("display", "none");
        $("#qbcore-inventory , body").fadeOut(300);
        $(".combine-option-container").hide();
        $("#other-inv-progressbar").progressbar({ value: 0 });
        $("#other-inv-weight-value").html("");
        $(".item-slot").remove();
        if ($("#rob-money").length) {
            $("#rob-money").remove();
        }
        $.post("https://qb-inventory/CloseInventory", JSON.stringify({}));

        if (AttachmentScreenActive) {
            $(".weapon-attachments-container").css({ display: "none" });
            AttachmentScreenActive = false;
        }

        if (ClickedItemData !== null) {
            $("#weapon-attachments").fadeOut(250, function () {
                $("#weapon-attachments").remove();
                ClickedItemData = {};
            });
        }
    };

    Inventory.Update = function (data) {
        totalWeight = 0;
        totalWeightOther = 0;
        $(".player-inventory").find(".item-slot").remove();
        $(".ply-hotbar-inventory").find(".item-slot").remove();
        if (data.error) {
            Inventory.Error();
        }
        for (i = 1; i < data.slots + 1; i++) {
            if (i == 41) {
                $(".player-inventory").append('<div class="item-slot" data-slot="' + i + '"><div class="item-slot-key"><p>6</p></div><div class="item-slot-img"></div><div class="item-slot-label"><p>&nbsp;</p></div></div>');
            } else {
                $(".player-inventory").append('<div class="item-slot" data-slot="' + i + '"><div class="item-slot-img"></div><div class="item-slot-label"><p>&nbsp;</p></div></div>');
            }
        }

        $.each(data.inventory, function (i, item) {
            if (item != null) {
                totalWeight += item.weight * item.amount;
                if (item.slot < 6) {
                    $(".player-inventory")
                        .find("[data-slot=" + item.slot + "]")
                        .addClass("item-drag");
                    $(".player-inventory")
                        .find("[data-slot=" + item.slot + "]")
                        .html('<div class="item-slot-key"><p>' + item.slot + '</p></div><div class="item-slot-img"><img src="images/' + item.image + '" alt="' + item.name + '" /></div><div class="item-slot-amount"><p>' + item.amount + " (" + ((item.weight * item.amount) / 1000).toFixed(1) + ')</p></div><div class="item-slot-label"><p>' + item.label + "</p></div>");
                    $(".player-inventory")
                        .find("[data-slot=" + item.slot + "]")
                        .data("item", item);
                } else if (item.slot == 41) {
                    $(".player-inventory")
                        .find("[data-slot=" + item.slot + "]")
                        .addClass("item-drag");
                    $(".player-inventory")
                        .find("[data-slot=" + item.slot + "]")
                        .html('<div class="item-slot-key"><p>6</p></div><div class="item-slot-img"><img src="images/' + item.image + '" alt="' + item.name + '" /></div><div class="item-slot-amount"><p>' + item.amount + " (" + ((item.weight * item.amount) / 1000).toFixed(1) + ')</p></div><div class="item-slot-label"><p>' + item.label + "</p></div>");
                    $(".player-inventory")
                        .find("[data-slot=" + item.slot + "]")
                        .data("item", item);
                } else {
                    $(".player-inventory")
                        .find("[data-slot=" + item.slot + "]")
                        .addClass("item-drag");
                    $(".player-inventory")
                        .find("[data-slot=" + item.slot + "]")
                        .html('<div class="item-slot-img"><img src="images/' + item.image + '" alt="' + item.name + '" /></div><div class="item-slot-amount"><p>' + item.amount + " (" + ((item.weight * item.amount) / 1000).toFixed(1) + ')</p></div><div class="item-slot-label"><p>' + item.label + "</p></div>");
                    $(".player-inventory")
                        .find("[data-slot=" + item.slot + "]")
                        .data("item", item);
                }
            }
        });
        updateProgressBar(totalWeight, data.maxweight);
        handleDragDrop();
    };

    Inventory.ToggleHotbar = function (data) {
        $('body').css('background-color','transparent')
        if (data.open) {
            $(".z-hotbar-inventory").html("");
            for (i = 1; i < 6; i++) {
                console.log(i);
                var elem = `
                <div style="height:20vh;background: linear-gradient(to bottom, rgba(0, 0, 255, 0), rgba(38, 43, 58, 0.5)"  class="item-slot" data-zhotbarslot="${i}">
                <svg style="position:absolute;" width="70" height="100" viewBox="0 0 88 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M45.75 1.58771L85.0513 24.2783C86.1342 24.9035 86.8013 26.059 86.8013 27.3094V72.6906C86.8013 73.941 86.1342 75.0965 85.0513 75.7217L45.75 98.4123C44.6671 99.0375 43.3329 99.0375 42.25 98.4123L2.94873 75.7217C1.86583 75.0965 1.19873 73.941 1.19873 72.6906V27.3094C1.19873 26.059 1.86583 24.9035 2.94873 24.2783L42.25 1.58771C43.3329 0.962499 44.6671 0.962499 45.75 1.58771Z"
                    fill="url(#paint0_radial_4467_154)" fill-opacity="0.25"
                    stroke="url(#paint1_radial_4467_154)" />
                <defs>
                    <radialGradient id="paint0_radial_4467_154" cx="0" cy="0" r="1"
                        gradientUnits="userSpaceOnUse"
                        gradientTransform="translate(44 50) rotate(48.6215) scale(62.1925)">
                        <stop stop-color="#00F8B9" />
                        <stop offset="1" stop-color="#00664C" />
                    </radialGradient>
                    <radialGradient id="paint1_radial_4467_154" cx="0" cy="0" r="1"
                        gradientUnits="userSpaceOnUse"
                        gradientTransform="translate(44 50) rotate(73.393) scale(74.3514)">
                        <stop stop-color="#00F8B9" />
                        <stop offset="1" stop-color="#00F8B9" stop-opacity="0.39" />
                    </radialGradient>
                </defs>
            </svg>
            
                 <div style="top:11%;left: 13%;position: absolute;" class="item-slot-img"></div>
                 <div class="zort-slot-label"><p>&nbsp;</p></div>
                 </div>`;
                $(".z-hotbar-inventory").append(elem);
            }
            // var elem = `
            // <div style="height:20vh;background: linear-gradient(to bottom, rgba(0, 0, 255, 0), rgba(38, 43, 58, 0.5)" class="item-slot" data-zhotbarslot="41"> 
            //             <svg style="position:absolute;" width="70" height="100" viewBox="0 0 88 100" fill="none" xmlns="http://www.w3.org/2000/svg">
            //     <path
            //         d="M45.75 1.58771L85.0513 24.2783C86.1342 24.9035 86.8013 26.059 86.8013 27.3094V72.6906C86.8013 73.941 86.1342 75.0965 85.0513 75.7217L45.75 98.4123C44.6671 99.0375 43.3329 99.0375 42.25 98.4123L2.94873 75.7217C1.86583 75.0965 1.19873 73.941 1.19873 72.6906V27.3094C1.19873 26.059 1.86583 24.9035 2.94873 24.2783L42.25 1.58771C43.3329 0.962499 44.6671 0.962499 45.75 1.58771Z"
            //         fill="url(#paint0_radial_4467_154)" fill-opacity="0.25"
            //         stroke="url(#paint1_radial_4467_154)" />
            //     <defs>
            //         <radialGradient id="paint0_radial_4467_154" cx="0" cy="0" r="1"
            //             gradientUnits="userSpaceOnUse"
            //             gradientTransform="translate(44 50) rotate(48.6215) scale(62.1925)">
            //             <stop stop-color="#00F8B9" />
            //             <stop offset="1" stop-color="#00664C" />
            //         </radialGradient>
            //         <radialGradient id="paint1_radial_4467_154" cx="0" cy="0" r="1"
            //             gradientUnits="userSpaceOnUse"
            //             gradientTransform="translate(44 50) rotate(73.393) scale(74.3514)">
            //             <stop stop-color="#00F8B9" />
            //             <stop offset="1" stop-color="#00F8B9" stop-opacity="0.39" />
            //         </radialGradient>
            //     </defs>
            // </svg>
            // <div style="top:11%;left: 13%;position: absolute;" class="item-slot-img"></div>
            // <div class="zort-slot-label"><p>&nbsp;</p></div>
            // </div>`;

            $(".z-hotbar-inventory").append(elem);
            $.each(data.items, function (i, item) {
                if (item != null) {
                    var ItemLabel = '<div style="top:75%" class="zort-slot-label"><p>' + item.label + "</p></div>";
                    if (item.name.split("_")[0] == "weapon") {
                        if (!Inventory.IsWeaponBlocked(item.name)) {
                            ItemLabel = '<div class="item-slot-quality"><div class="item-slot-quality-bar"><p>100</p></div></div><div class="item-slot-label"><p>' + item.name + "</p></div>";
                        }
                    }
                    if (item.slot == 41) {
                        $(".z-hotbar-inventory")
                            .find("[data-zhotbarslot=" + item.slot + "]")
                            .html('<div class="item-slot-img"><img src="images/' + item.image + '" alt="' + item.name + '" /></div>' + ItemLabel);
                        } else {
                        $(".z-hotbar-inventory")
                            .find("[data-zhotbarslot=" + item.slot + "]")
                            .html('<svg style="position:absolute;" width="70" height="100" viewBox="0 0 88 100" fill="none" xmlns="http://www.w3.org/2000/svg">   <path       d="M45.75 1.58771L85.0513 24.2783C86.1342 24.9035 86.8013 26.059 86.8013 27.3094V72.6906C86.8013 73.941 86.1342 75.0965 85.0513 75.7217L45.75 98.4123C44.6671 99.0375 43.3329 99.0375 42.25 98.4123L2.94873 75.7217C1.86583 75.0965 1.19873 73.941 1.19873 72.6906V27.3094C1.19873 26.059 1.86583 24.9035 2.94873 24.2783L42.25 1.58771C43.3329 0.962499 44.6671 0.962499 45.75 1.58771Z"       fill="url(#paint0_radial_4467_154)" fill-opacity="0.25"       stroke="url(#paint1_radial_4467_154)" />   <defs>       <radialGradient id="paint0_radial_4467_154" cx="0" cy="0" r="1"           gradientUnits="userSpaceOnUse"           gradientTransform="translate(44 50) rotate(48.6215) scale(62.1925)">           <stop stop-color="#00F8B9" />           <stop offset="1" stop-color="#00664C" />       </radialGradient>       <radialGradient id="paint1_radial_4467_154" cx="0" cy="0" r="1"           gradientUnits="userSpaceOnUse"           gradientTransform="translate(44 50) rotate(73.393) scale(74.3514)">           <stop stop-color="#00F8B9" />           <stop offset="1" stop-color="#00F8B9" stop-opacity="0.39" />       </radialGradient>   </defs>/svg>   <div class="item-slot-img"><img src="images/' + item.image + '" alt="' + item.name + '" /></div><div class="zort-slot-label"><p>' + item.label + "</p></div>" + ItemLabel);
                        }
                    Inventory.QualityCheck(item, true, false);
                }
            });
            $('.z-hotbar-inventory').css('display','flex')
            $("body").fadeIn(150);
        } else {
            $(".z-hotbar-inventory , body").fadeOut(150, function () {
                $(".z-hotbar-inventory").html("");
            });
        }

    };

    Inventory.UseItem = function (data) {
        console.log(data.label);
        // setTimeout(() => {
        //  $('.notify , body').css('display', 'block');
        //  $(".notify").animate({
        //     left: "0%"
        // }, 1000);

        //  $('.notify .item-name').html(data.label);
        //  $('.notify .item-count').html(data.amount);
        //  $('.notify .item-img img').attr('src', 'images/' + data.image);
        
        // }, 500);
        // setTimeout(function () {
        //     $(".notify").animate({
        //         left: "-20%"
        //     }, 1000);
        // }, 3000);
        // $('.notify , body').css('display', 'none');

    };

    var itemBoxtimer = null;
    var requiredTimeout = null;

    Inventory.itemBox = function (data) {
        if (itemBoxtimer !== null) {
            clearTimeout(itemBoxtimer);
        }
        
        console.log(data.type);
        var type = "Used";
        if (data.type == "add") {
            type = "Added";
            $('.notify .item-count').css('left', '58.5%')
        } else if (data.type == "remove") {
            type = "Removed";
            $('.notify .item-count').css('left', '63%')
        }else if (data.type == "use") {
            type = "Used";
            $('.notify .item-count').css('left', '55.5%')
        }

        setTimeout(() => {
            $('.notify , body').css('display', 'block');
            $(".notify").animate({left: "0%"}, 1000);
            $('.notify .item-name').html(data.item.label);
            $('.notify .item-count').html(data.item.amount);
            $('.notify .item-img img').attr('src', 'images/' + data.item.image);
            $('.notify .item-type').html(type)            
            }, 500);

           setTimeout(function () {
               $(".notify").animate({left: "-20%"}, 1000);
           }, 3000);
           $('.notify , body').css('display', 'none');
    };

    Inventory.RequiredItem = function (data) {
        if (requiredTimeout !== null) {
            clearTimeout(requiredTimeout);
        }
        if (data.toggle) {
            if (!requiredItemOpen) {
                $(".requiredItem-container").html("");
                $.each(data.items, function (index, item) {
                    var element = `<div class="item-slot requiredItem-box"><div class="item-slot-amount"><p>Required</p></div><div class="item-slot-img"><img src="images/${item.image}" alt="${item.name}" /></div><div class="item-slot-label"><p>${item.label}</p></div></div>`;
                    $(".requiredItem-container").hide();
                    $(".requiredItem-container").append(element);
                    $(".requiredItem-container").fadeIn(100);
                });
                requiredItemOpen = true;
            }
        } else {
            $(".requiredItem-container").fadeOut(100);
            requiredTimeout = setTimeout(function () {
                $(".requiredItem-container").html("");
                requiredItemOpen = false;
            }, 100);
        }
    };

    window.onload = function (e) {
        window.addEventListener("message", function (event) {
            dropType = null;
            $('body').css('background-color','transparent');
            $('body').css('background','transparent');
            // $("#qbcore-inventory , body").css("display", "none");

            switch (event.data.action) {
                case "open":
                    Inventory.Open(event.data);
                    break;
                case "close":
                    Inventory.Close();
                    break;
                case "update":
                    Inventory.Update(event.data);
                    break;
                case "itemBox":
                    Inventory.itemBox(event.data);
                    break;
                case "requiredItem":
                    Inventory.RequiredItem(event.data);
                    break;
                case "toggleHotbar":
                    Inventory.ToggleHotbar(event.data);
                    break;
                case "openplace":
                    setTimeout(() => {
                        $("body , .place-box").show();
                    }, 1000);
                    $('body').css('background-color','transparent');
                    $('body').css('background','transparent');
                    break;
                    case "closeplace":
                        setTimeout(() => {
                            $("body , .place-box").fadeOut(500);
                        }, 1000);
                        $('body').css('background-color','transparent');
                        $('body').css('background','transparent');
                    break;
                case "health":
                    $(".health-system , body").css("display", "block");
                    $('body').css('background-color','transparent')
                    $(".health-system").animate({
                        left: "0%"
                    }, 1000);
                    $('.two-circle .text').html(event.data.playerDamage['label']);
                    $('.two-circle .number').html(event.data.playerDamage['count']);
                break;

                case "RobMoney":
                    // $(".inv-options-list").append('<div class="inv-option-item" id="rob-money"><p>TAKE MONEY</p></div>');
                    $("#rob-money").data("TargetId", event.data.TargetId);
                    break;
            }
        });
    };
})();


$(".invsearch-input").on("input", function () {
    var val = $(this).val().toLowerCase();

    $(".player-inventory .item-slot").each(function () {
        var html = $(this).find(".item-slot-label").html().toLowerCase();

        if (html.indexOf("&nbsp;") === -1) {
            if (html.indexOf(val) === -1) {
                $(this).css('opacity', '0.3');
            } else {
                $(this).css('opacity', '1');
            }
        }
    });
});


var filter = false
$(document).on("click", ".item-box-list svg", function (e) {
    e.preventDefault();
    var item = $(this).data("type");
    var controller = false

    if (filter) {
        $(".player-inventory .item-slot").css('opacity', '1');
        filter = false;
        return;
    }

    var itemList;
    switch (item) {
        case "weaponsName":
            itemList = weaponsName;
            controller = true
            break;
        case "boxName":
            itemList = boxName;
            break;
        case "clothesName":
            itemList = clothesName;
            break;
        case "foodsName":
            itemList = foodsName;
            break;
        case "settingsName":
            itemList = settingsName;
            break;

        default:
            itemList = [];
            break;
    }
    $(".player-inventory .item-slot").each(function () {
        var html = $(this).find(".item-slot-label p").html().toLowerCase();
        var itemFound = false;

        if (controller) {
            if (html.indexOf("&nbsp;") === -1) {
                itemList.forEach(function(itemText) {
                    if (html.indexOf(itemText.toLowerCase()) !== -1) {
                        itemFound = true;
                    }
                });
            }
        }

        if (html.indexOf("&nbsp;") === -1 && !controller) {
            itemList.forEach(function(itemText) {
                if (html.indexOf(itemText.name.toLowerCase()) !== -1) {
                    itemFound = true;
                }
            });
        }

        if (!itemFound) {
            $(this).css('opacity', '0.3');
        } else {
            $(this).css('opacity', '1');
        }
    });

    filter = !filter;
});


$(document).on("click", ".settings-button", function (e) {
    if ($(this).data("type") == "settings") {
        $('.help-box').css('display', 'block');       
    }
})

$(document).on("click", ".exit-box", function (e) {
    Inventory.Close();
})


$(document).on("click", ".helpclose", function (e) {
    $('.help-box').css('display', 'none');       
})

$(document).on("click", "#rob-money", function (e) {
    e.preventDefault();
    var TargetId = $(this).data("TargetId");
    $.post(
        "https://qb-inventory/RobMoney",
        JSON.stringify({
            TargetId: TargetId,
        })
    );
    $("#rob-money").remove();
});

const rangeInputs = document.querySelectorAll('.firstRange')
function handleInputFirst(e) {
  let target = e.target
  if (e.target.type !== 'range') {
    target = document.getElementById('range')
  }
  const min = target.min
  const max = target.max
  const val = target.value
  const realbossval = parseFloat(target.value)
  target.style.backgroundSize = (val - min) * 100 / (max - min) + '% 100%'
  priceUpdate(realbossval)
}

function priceUpdate(count) {
    $('.amoun-value-input').val(count)
}

rangeInputs.forEach(input => {
  input.addEventListener('input', handleInputFirst)
})

$(document).ready(function() {
    $('.wound').hover(function() {
        var hover = $(this).data("type");
        var data = healthData[hover];

        data.bleeding = data.bleeding ? "Yes" : "No";
        data.severity = ["Low", "Medium", "High", "Critical"][data.severity];
        data.broken = data.broken ? "Yes" : "No";

        $('.bullets').html(data.health);
        $('.broken').html(data.broken);
        $('.severity').html(data.severity);
        $('.bleeding').html(data.bleeding);

        var woundPos = $(this).offset();
        var charInfo = $('.char-info');
        
        var leftPos = woundPos.left + $(this).width() + 10;
        var topPos = woundPos.top - (charInfo.height() / 2) + ($(this).height() / 2);
        
        charInfo.css({
            'display': 'block',
            'left': leftPos + 'px',
            'top': topPos + 200 + 'px'
        });
    }, function() {
        $('.char-info').css('display', 'none');
    });
});



splitAmount = 0;
$("#item-give").droppable({
    hoverClass: "button-hover",
    drop: function (event, ui) {
        setTimeout(function () {
            IsDragging = false;
        }, 300);
        fromData = ui.draggable.data("item");
        fromInventory = ui.draggable.parent().attr("data-inventory");
        splitAmount = fromData.amount;
        // amount = $("#item-amount").val();
        if (amount == 0) {
            amount = fromData.amount;
        }
        $.post(
            "https://qb-inventory/GiveItem",
            JSON.stringify({
                inventory: fromInventory,
                item: fromData,
                amount: parseInt(amount),
            })
        );
    },
});

$(document).on("click", ".split-button", function (e) {
    e.preventDefault();
    var fromData = currentItemData;
    var fromInventory = "player";
    var splitAmount = $(".amoun-value-input").val();
    if (splitAmount == 0) {
        splitAmount = fromData.amount;
    }
    $.post(
        "https://qb-inventory/SplitItem",
        JSON.stringify({
            inventory: fromInventory,
            item: fromData,
            amount: parseInt(amount),
        })
    );
})
