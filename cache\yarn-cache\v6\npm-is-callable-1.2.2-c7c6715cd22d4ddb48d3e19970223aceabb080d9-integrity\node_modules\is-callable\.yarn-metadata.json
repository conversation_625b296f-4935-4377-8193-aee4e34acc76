{"manifest": {"name": "is-callable", "version": "1.2.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "description": "Is this JS value callable? Works with Functions and GeneratorFunctions, despite ES6 @@toStringTag.", "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "posttest": "npx aud --production", "tests-only": "npm run --silent test:stock && npm run --silent test:staging", "test:stock": "node test", "test:staging": "node --es-staging test", "coverage": "npm run --silent istanbul", "covert": "covert test", "covert:quiet": "covert test --quiet", "istanbul": "npm run --silent istanbul:clean && npm run --silent istanbul:std && npm run --silent istanbul:harmony && npm run --silent istanbul:merge && istanbul check", "istanbul:clean": "rimraf coverage coverage-std coverage-harmony", "istanbul:merge": "istanbul-merge --out coverage/coverage.raw.json coverage-harmony/coverage.raw.json coverage-std/coverage.raw.json && istanbul report html", "istanbul:harmony": "node --harmony ./node_modules/istanbul/lib/cli.js cover test --dir coverage-harmony", "istanbul:std": "istanbul cover test --report html --dir coverage-std", "prelint": "eclint check *", "lint": "eslint ."}, "repository": {"type": "git", "url": "git://github.com/ljharb/is-callable.git"}, "keywords": ["Function", "function", "callable", "generator", "generator function", "arrow", "arrow function", "ES6", "toStringTag", "@@toStringTag"], "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^17.2.0", "aud": "^1.1.2", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^7.9.0", "foreach": "^2.0.5", "istanbul": "1.1.0-alpha.1", "istanbul-merge": "^1.1.1", "make-arrow-function": "^1.2.0", "make-async-function": "^1.0.0", "make-generator-function": "^2.0.0", "rimraf": "^2.7.1", "safe-publish-latest": "^1.1.4", "tape": "^5.0.1"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "greenkeeper": {"ignore": ["<PERSON><PERSON><PERSON>"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-callable-1.2.2-c7c6715cd22d4ddb48d3e19970223aceabb080d9-integrity\\node_modules\\is-callable\\package.json", "readmeFilename": "README.md", "readme": "# is-callable <sup>[![Version Badge][2]][1]</sup>\n\n[![Build Status][3]][4]\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\n[![browser support][9]][10]\n\nIs this JS value callable? Works with Functions and GeneratorFunctions, despite ES6 @@toStringTag.\n\n## Example\n\n```js\nvar isCallable = require('is-callable');\nvar assert = require('assert');\n\nassert.notOk(isCallable(undefined));\nassert.notOk(isCallable(null));\nassert.notOk(isCallable(false));\nassert.notOk(isCallable(true));\nassert.notOk(isCallable([]));\nassert.notOk(isCallable({}));\nassert.notOk(isCallable(/a/g));\nassert.notOk(isCallable(new RegExp('a', 'g')));\nassert.notOk(isCallable(new Date()));\nassert.notOk(isCallable(42));\nassert.notOk(isCallable(NaN));\nassert.notOk(isCallable(Infinity));\nassert.notOk(isCallable(new Number(42)));\nassert.notOk(isCallable('foo'));\nassert.notOk(isCallable(Object('foo')));\n\nassert.ok(isCallable(function () {}));\nassert.ok(isCallable(function* () {}));\nassert.ok(isCallable(x => x * x));\n```\n\n## Install\n\nInstall with\n\n```\nnpm install is-callable\n```\n\n## Tests\n\nSimply clone the repo, `npm install`, and run `npm test`\n\n[1]: https://npmjs.org/package/is-callable\n[2]: http://versionbadg.es/ljharb/is-callable.svg\n[3]: https://travis-ci.org/ljharb/is-callable.svg\n[4]: https://travis-ci.org/ljharb/is-callable\n[5]: https://david-dm.org/ljharb/is-callable.svg\n[6]: https://david-dm.org/ljharb/is-callable\n[7]: https://david-dm.org/ljharb/is-callable/dev-status.svg\n[8]: https://david-dm.org/ljharb/is-callable#info=devDependencies\n[9]: https://ci.testling.com/ljharb/is-callable.png\n[10]: https://ci.testling.com/ljharb/is-callable\n[11]: https://nodei.co/npm/is-callable.png?downloads=true&stars=true\n[license-image]: http://img.shields.io/npm/l/is-callable.svg\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/is-callable.svg\n[downloads-url]: http://npm-stat.com/charts.html?package=is-callable\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015 Jordan Harband\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-callable/-/is-callable-1.2.2.tgz#c7c6715cd22d4ddb48d3e19970223aceabb080d9", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-callable/-/is-callable-1.2.2.tgz", "hash": "c7c6715cd22d4ddb48d3e19970223aceabb080d9", "integrity": "sha512-dnMqspv5nU3LoewK2N/y7KLtxtakvTuaCsU9FU50/QDmdbHNy/4/JuRtMHqRU22o3q+W89YQndQEeCVwK+3qrA==", "registry": "npm", "packageName": "is-callable", "cacheIntegrity": "sha512-dnMqspv5nU3LoewK2N/y7KLtxtakvTuaCsU9FU50/QDmdbHNy/4/JuRtMHqRU22o3q+W89YQndQEeCVwK+3qrA== sha1-x8ZxXNItTdtI0+GZcCI6zquwgNk="}, "registry": "npm", "hash": "c7c6715cd22d4ddb48d3e19970223aceabb080d9"}