local SecretKey = 'YOUR_TEBEX_SECRET_KEY'

local url = 'https://plugin.tebex.io/payments/%s'

local function HttpRequestAwait(url, method, data, headers, options)
    local p = promise.new()
    PerformHttpRequest(url, function(...)
        p:resolve({ ... })
    end, method, data, headers, options)

    Citizen.Await(p)
    return table.unpack(p.value)
end

function ReqTebex(id, transaction_id)
    local status, data = HttpRequestAwait((url):format(transaction_id), 'GET', '', {
        ['Content-Type'] = 'application/json',
        ['X-Tebex-Secret'] = SecretKey
    })

    if status ~= 200 then
        return false
    end

    if data then
        local responseData = json.decode(data)

        if responseData.status ~= 'Complete' then
            return false
        end

        local result = ExecuteSql('SELECT * FROM `0r_multicharacterv2-transaction` WHERE transactionId = ?', { transaction_id })

        if result[1] ~= nil then
            return false
        end

        local packages = responseData.packages

        for i = 1, #packages do
            for k, v in pairs(Config.Slots.Package) do
                if v.id == packages[i].id then
                    ExecuteSql('INSERT INTO `0r_multicharacterv2-transaction` (transactionId) VALUES (?)', { transaction_id })
            
                    return v.totalSlot
                end
            end
        end
    end
end
