{"manifest": {"name": "@webassemblyjs/wasm-parser", "version": "1.9.0", "keywords": ["webassembly", "javascript", "ast", "parser", "wasm"], "description": "WebAssembly binary format parser", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "mocha"}, "author": {"name": "<PERSON>"}, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-api-error": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/ieee754": "1.9.0", "@webassemblyjs/leb128": "1.9.0", "@webassemblyjs/utf8": "1.9.0"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "devDependencies": {"@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/helper-test-framework": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0", "@webassemblyjs/wast-parser": "1.9.0", "mamacro": "^0.0.7", "wabt": "1.0.12"}, "gitHead": "0440b420888c1f7701eb9762ec657775506b87d8", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@webassemblyjs-wasm-parser-1.9.0-9d48e44826df4a6598294aa6c87469d642fff65e-integrity\\node_modules\\@webassemblyjs\\wasm-parser\\package.json", "readmeFilename": "README.md", "readme": "# @webassemblyjs/wasm-parser\n\n> WebAssembly binary format parser\n\n## Installation\n\n```sh\nyarn add @webassemblyjs/wasm-parser\n```\n\n## Usage\n\n```js\nimport { decode } from \"@webassemblyjs/wasm-parser\";\n\nconst decoderOpts = {};\n\nconst ast = decode(binary, decoderOpts);\n```\n\n### Decoder options\n\n- `dump`: print dump information while decoding (default `false`)\n- `ignoreCodeSection`: ignore the code section (default `false`)\n- `ignoreDataSection`: ignore the data section (default `false`)\n\n", "licenseText": "MIT License\n\nCopyright (c) 2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@webassemblyjs/wasm-parser/-/wasm-parser-1.9.0.tgz#9d48e44826df4a6598294aa6c87469d642fff65e", "type": "tarball", "reference": "https://registry.yarnpkg.com/@webassemblyjs/wasm-parser/-/wasm-parser-1.9.0.tgz", "hash": "9d48e44826df4a6598294aa6c87469d642fff65e", "integrity": "sha512-9+wkMowR2AmdSWQzsPEjFU7njh8HTO5MqO8vjwEHuM+AMHioNqSBONRdr0NQQ3dVQrzp0s8lTcYqzUdb7YgELA==", "registry": "npm", "packageName": "@webassemblyjs/wasm-parser", "cacheIntegrity": "sha512-9+wkMowR2AmdSWQzsPEjFU7njh8HTO5MqO8vjwEHuM+AMHioNqSBONRdr0NQQ3dVQrzp0s8lTcYqzUdb7YgELA== sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4="}, "registry": "npm", "hash": "9d48e44826df4a6598294aa6c87469d642fff65e"}