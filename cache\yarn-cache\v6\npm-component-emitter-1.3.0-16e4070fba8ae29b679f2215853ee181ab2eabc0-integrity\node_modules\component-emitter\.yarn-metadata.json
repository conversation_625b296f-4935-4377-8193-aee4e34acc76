{"manifest": {"name": "component-emitter", "description": "Event emitter", "version": "1.3.0", "license": "MIT", "devDependencies": {"mocha": "*", "should": "*"}, "component": {"scripts": {"emitter/index.js": "index.js"}}, "main": "index.js", "repository": {"type": "git", "url": "https://github.com/component/emitter.git"}, "scripts": {"test": "make test"}, "files": ["index.js", "LICENSE"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-component-emitter-1.3.0-16e4070fba8ae29b679f2215853ee181ab2eabc0-integrity\\node_modules\\component-emitter\\package.json", "readmeFilename": "Readme.md", "readme": "# Emitter [![Build Status](https://travis-ci.org/component/emitter.png)](https://travis-ci.org/component/emitter)\n\n  Event emitter component.\n\n## Installation\n\n```\n$ component install component/emitter\n```\n\n## API\n\n### Emitter(obj)\n\n  The `Emitter` may also be used as a mixin. For example\n  a \"plain\" object may become an emitter, or you may\n  extend an existing prototype.\n\n  As an `Emitter` instance:\n\n```js\nvar Emitter = require('emitter');\nvar emitter = new Emitter;\nemitter.emit('something');\n```\n\n  As a mixin:\n\n```js\nvar Emitter = require('emitter');\nvar user = { name: 'tobi' };\nEmitter(user);\n\nuser.emit('im a user');\n```\n\n  As a prototype mixin:\n\n```js\nvar Emitter = require('emitter');\nEmitter(User.prototype);\n```\n\n### Emitter#on(event, fn)\n\n  Register an `event` handler `fn`.\n\n### Emitter#once(event, fn)\n\n  Register a single-shot `event` handler `fn`,\n  removed immediately after it is invoked the\n  first time.\n\n### Emitter#off(event, fn)\n\n  * Pass `event` and `fn` to remove a listener.\n  * Pass `event` to remove all listeners on that event.\n  * Pass nothing to remove all listeners on all events.\n\n### Emitter#emit(event, ...)\n\n  Emit an `event` with variable option args.\n\n### Emitter#listeners(event)\n\n  Return an array of callbacks, or an empty array.\n\n### Emitter#hasListeners(event)\n\n  Check if this emitter has `event` handlers.\n\n## License\n\nMIT\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2014 Component contributors <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person\nobtaining a copy of this software and associated documentation\nfiles (the \"Software\"), to deal in the Software without\nrestriction, including without limitation the rights to use,\ncopy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the\nSoftware is furnished to do so, subject to the following\nconditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\nHOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\nWHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\nOTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/component-emitter/-/component-emitter-1.3.0.tgz#16e4070fba8ae29b679f2215853ee181ab2eabc0", "type": "tarball", "reference": "https://registry.yarnpkg.com/component-emitter/-/component-emitter-1.3.0.tgz", "hash": "16e4070fba8ae29b679f2215853ee181ab2eabc0", "integrity": "sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg==", "registry": "npm", "packageName": "component-emitter", "cacheIntegrity": "sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg== sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A="}, "registry": "npm", "hash": "16e4070fba8ae29b679f2215853ee181ab2eabc0"}