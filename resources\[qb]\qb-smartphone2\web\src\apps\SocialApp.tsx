import React from 'react';
import { usePhone } from '../contexts/PhoneContext';
import { useTheme } from '../contexts/ThemeContext';

const SocialApp: React.FC = () => {
  const { closeApp } = usePhone();
  const { colors } = useTheme();

  return (
    <div className="social-app">
      <div className="app-header" style={{ backgroundColor: colors.surface, borderColor: colors.border }}>
        <button className="app-back-btn" onClick={closeApp} style={{ color: colors.primary }}>
          <i className="fas fa-arrow-left" />
          Zpět
        </button>
        <div className="app-title" style={{ color: colors.text }}>Sociální síť</div>
      </div>
      
      <div className="app-content" style={{ backgroundColor: colors.background }}>
        <div style={{ padding: '40px 20px', textAlign: 'center', color: colors.textSecondary }}>
          <i className="fas fa-share-alt" style={{ fontSize: '48px', marginBottom: '16px' }} />
          <p>Sociální síť aplikace bude implementována</p>
        </div>
      </div>
    </div>
  );
};

export default SocialApp;
