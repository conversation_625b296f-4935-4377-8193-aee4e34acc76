{"manifest": {"name": "commondir", "version": "1.0.1", "description": "compute the closest common parent for file paths", "main": "index.js", "dependencies": {}, "devDependencies": {"tape": "^3.5.0"}, "scripts": {"test": "tape test/*.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-commondir.git"}, "keywords": ["common", "path", "directory", "file", "parent", "root"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "engine": {"node": ">=0.4"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-commondir-1.0.1-ddd800da0c66127393cca5950ea968a3aaf1253b-integrity\\node_modules\\commondir\\package.json", "readmeFilename": "readme.markdown", "readme": "# commondir\n\ncompute the closest common parent directory among an array of directories\n\n# example\n\n``` js\nvar commondir = require('commondir');\nvar dir = commondir(process.argv.slice(2))\nconsole.log(dir);\n```\n\noutput:\n\n```\n$ node dir.js /x/y/z /x/y /x/y/w/q\n/x/y\n$ node ../baz ../../foo/quux ./bizzy\n/foo\n```\n\n# methods\n\n``` js\nvar commondir = require('commondir');\n```\n\n## commondir(absolutePaths)\n\nCompute the closest common parent directory for an array `absolutePaths`.\n\n## commondir(basedir, relativePaths)\n\nCompute the closest common parent directory for an array `relativePaths` which\nwill be resolved for each `dir` in `relativePaths` according to:\n`path.resolve(basedir, dir)`.\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install commondir\n```\n\n# license\n\nMIT\n", "licenseText": "The MIT License\n\nCopyright (c) 2013 <PERSON> (<EMAIL>)\n\nPermission is hereby granted, free of charge, \nto any person obtaining a copy of this software and \nassociated documentation files (the \"Software\"), to \ndeal in the Software without restriction, including \nwithout limitation the rights to use, copy, modify, \nmerge, publish, distribute, sublicense, and/or sell \ncopies of the Software, and to permit persons to whom \nthe Software is furnished to do so, \nsubject to the following conditions:\n\nThe above copyright notice and this permission notice \nshall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, \nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES \nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. \nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR \nANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, \nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE \nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/commondir/-/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b", "type": "tarball", "reference": "https://registry.yarnpkg.com/commondir/-/commondir-1.0.1.tgz", "hash": "ddd800da0c66127393cca5950ea968a3aaf1253b", "integrity": "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=", "registry": "npm", "packageName": "commondir", "cacheIntegrity": "sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg== sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs="}, "registry": "npm", "hash": "ddd800da0c66127393cca5950ea968a3aaf1253b"}