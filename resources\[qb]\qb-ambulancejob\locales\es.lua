local Translations = {
    error = {
        canceled = 'Cancelado',
        bled_out = 'Te desangraste...',
        impossible = 'Acción imposible...',
        no_player = 'No hay ningún jugador cerca',
        no_firstaid = 'Necesitas un kit de primeros auxilios',
        no_bandage = 'Necesitas una benda',
        beds_taken = 'Las camas están ocupadas...',
        possessions_taken = 'Todas tus posesiones han sido confiscadas...',
        not_enough_money = 'No tienes suficiente dinero...',
        cant_help = 'No puedes ayudar a esta persona...',
        not_ems = 'No eres EMS',
        not_online = 'El jugador no está en línea'
    },
    success = {
        revived = 'Persona reanimada',
        healthy_player = 'El paciente ya está saludable',
        helped_player = 'Has ayudado a la persona',
        wounds_healed = '¡Tus heridas han sido curadas!',
        being_helped = 'Estás siendo tratado...'
    },
    info = {
        civ_died = 'Civil muerto',
        civ_down = 'Civil caído',
        civ_call = 'Llamada de civil',
        self_death = 'Suicidio o un NPC',
        wep_unknown = 'Desconocido',
        respawn_txt = 'REAPARECERAS EN %{deathtime} SEGUNDOS',
        respawn_revive = 'MANTÉN [E] DURANTE %{holdtime} SEGUNDOS PARA SER REVIVIDO POR $%{cost}',
        bleed_out = 'TE DESANGRARAS EN %{time} SEGUNDOS',
        bleed_out_help = 'TE DESANGRARAS EN %{time} SEGUNDOS, PUEDES SER AYUDADO',
        request_help = 'PULSA [G] PARA PEDIR AYUDA',
        help_requested = 'EMS EN CAMINO',
        amb_plate = 'LSMD', -- Should only be 4 characters long due to the last 4 being a random 4 digits
        heli_plate = 'LSMD',  -- Should only be 4 characters long due to the last 4 being a random 4 digits
        status = 'Revisión de estado',
        is_status = 'es %{status}',
        healthy = '¡Ya estás completamente saludable de nuevo!',
        safe = 'Caja fuerte de hopital',
        pb_hospital = 'Hospital Pillbox',
        pain_message = 'Sientes %{severity} en %{limb}',
        many_places = 'Te duele todo...',
        bleed_alert = 'Estás %{bleedstate}',
        ems_alert = 'Alerta EMS - %{text}',
        mr = 'Dr.',
        mrs = 'Dra.',
        dr_needed = 'Se necesita un doctor en el hospital',
        ems_report = 'Reporte EMS',
        message_sent = 'Mensaje enviado',
        check_health = 'Revisar salud de jugador',
        heal_player = 'Curar jugador',
        revive_player = 'Reanimar jugador',
        revive_player_a = 'Reanimar jugador (Admin)',
        player_id = 'ID de jugador (puede quedar vacio)',
        pain_level = 'Establecer nivel de dolor (Admin)',
        kill = 'Matar un jugador o a ti mismo (Admin)',
        heal_player_a = 'Curar un jugador o a ti mismo (Admin)',
    },
    mail = {
        sender = 'Hospital Pillbox',
        subject = 'Costos de hospital',
        message = 'Querido %{gender} %{lastname}, <br /><br />Le adjuntamos la factura con los costos de su última estancia en el hospital.<br />El costo total es de: <strong>$%{costs}</strong><br /><br />¡Le deseamos una pronta recuperación!'
    },
    states = {
        irritated = 'molestias',
        quite_painful = 'mucho dolor',
        painful = 'dolor',
        really_painful = 'dolor insoportable',
        little_bleed = 'sangrando un poco...',
        bleed = 'sangrando...',
        lot_bleed = 'sangrando mucho...',
        big_bleed = 'desangrándote gravemente...',
    },
    menu = {
        amb_vehicles = 'Vehículos EMS',
        status = 'Estado de salud',
        close = '⬅ Cerrar menu',
    },
    text = {
        pstash_button = '[E] - Stash personal',
        pstash = 'Stash personal',
        onduty_button = '[E] - Entrar en servicio',
        offduty_button = '[E] - Salir de servicio',
        duty = 'En/fuera de servicio',
        armory_button = '[E] - Armería',
        armory = 'Armería',
        veh_button = '[E] - Sacar / guardar vehículo',
        heli_button = '[E] - Sacar / guardar helicoptero',
        elevator_roof = '[E] - Tomar el elevador al último piso',
        elevator_main = '[E] - Tomar el elevador hacía abajo',
        bed_out = '[E] - Para salir de la cama..',
        call_doc = '[E] - Llamar doctor',
        call = 'Llamar',
        check_in = '[E] Hacer check-in',
        check = 'Check-in',
        lie_bed = '[E] - Para acostarse en la cama'
    },
    body = {
        head = 'cabeza',
        neck = 'cuello',
        spine = 'columna',
        upper_body = 'parte superior',
        lower_body = 'parte inferior',
        left_arm = 'brazo izquierdo',
        left_hand = 'mano izquierda',
        left_fingers = 'dedos izquierdos',
        left_leg = 'pierna izquierda',
        left_foot = 'pie izquierdo',
        right_arm = 'brazo derecho',
        right_hand = 'mano derecha',
        right_fingers = 'dedos derechos',
        right_leg = 'pierna derecha',
        right_foot = 'pie derecho',
    },
    progress = {
        ifaks = 'Tomando ifaks...',
        bandage = 'Usando vendas...',
        painkillers = 'Tomando pastillas para el dolor...',
        revive = 'Reanimando persona..',
        healing = 'Curando heridas...',
        checking_in = 'Realizando revisión...',
    },
    logs = {
        death_log_title = "%{playername} (%{playerid}) está muerto",
        death_log_message = "%{killername} ha matado %{playername} con **%{weaponlabel}** (%{weaponname})",
    }
}

if GetConvar('qb_locale', 'en') == 'es' then
    Lang = Locale:new({
        phrases = Translations,
        warnOnMissing = true,
        fallbackLang = Lang,
    })
end
