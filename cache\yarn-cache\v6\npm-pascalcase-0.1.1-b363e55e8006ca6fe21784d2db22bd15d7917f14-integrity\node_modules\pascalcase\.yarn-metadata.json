{"manifest": {"name": "pascalcase", "description": "Convert a string to pascal-case.", "version": "0.1.1", "homepage": "https://github.com/jonschlinkert/pascalcase", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/pascalcase.git"}, "bugs": {"url": "https://github.com/jonschlinkert/pascalcase/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["camelcase", "case", "casing", "pascal", "pascal-case", "pascalcase", "string"], "verb": {"related": {"list": ["pad-left", "pad-right", "word-wrap", "repeat-string", "justified"]}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-pascalcase-0.1.1-b363e55e8006ca6fe21784d2db22bd15d7917f14-integrity\\node_modules\\pascalcase\\package.json", "readmeFilename": "README.md", "readme": "# pascalcase [![NPM version](https://badge.fury.io/js/pascalcase.svg)](http://badge.fury.io/js/pascalcase)\n\n> Convert a string to pascal-case.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/)\n\n```sh\n$ npm i pascalcase --save\n```\n\n## Usage\n\n```js\nvar pascalcase = require('pascalcase');\n\npascalcase('a');\n//=> 'A'\n\npascalcase('foo bar baz');\n//=> 'FooBarBaz'\n\npascalcase('foo_bar-baz');\n//=> 'FooBarBaz'\n\npascalcase('foo.bar.baz');\n//=> 'FooBarBaz'\n\npascalcase('foo/bar/baz');\n//=> 'FooBarBaz'\n\npascalcase('foo[bar)baz');\n//=> 'FooBarBaz'\n\npascalcase('#foo+bar*baz');\n//=> 'FooBarBaz'\n\npascalcase('$foo~bar`baz');\n//=> 'FooBarBaz'\n\npascalcase('_foo_bar-baz-');\n//=> 'FooBarBaz'\n```\n\n## Related projects\n\n* [justified](https://github.com/jonschlinkert/justified): Wrap words to a specified length and justified the text.\n* [pad-left](https://github.com/jonschlinkert/pad-left): Left pad a string with zeros or a specified string. Fastest implementation.\n* [pad-right](https://github.com/jonschlinkert/pad-right): Right pad a string with zeros or a specified string. Fastest implementation.\n* [repeat-string](https://github.com/jonschlinkert/repeat-string): Repeat the given string n times. Fastest implementation for repeating a string.\n* [word-wrap](https://github.com/jonschlinkert/word-wrap): Wrap words to a specified length.\n\n## Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm i -d && npm test\n```\n\n## Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/pascalcase/issues/new)\n\n## Author\n\n**Jon Schlinkert**\n\n+ [github/jonschlinkert](https://github.com/jonschlinkert)\n+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n## License\n\nCopyright © 2015 Jon Schlinkert\nReleased under the MIT license.\n\n***\n\n_This file was generated by [verb-cli](https://github.com/assemble/verb-cli) on August 19, 2015._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/pascalcase/-/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14", "type": "tarball", "reference": "https://registry.yarnpkg.com/pascalcase/-/pascalcase-0.1.1.tgz", "hash": "b363e55e8006ca6fe21784d2db22bd15d7917f14", "integrity": "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=", "registry": "npm", "packageName": "pascalcase", "cacheIntegrity": "sha512-XHXfu/yOQRy9vYOtUDVMN60OEJjW013GoObG1o+xwQTpB9eYJX/BjXMsdW13ZDPruFhYYn0AG22w0xgQMwl3Nw== sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="}, "registry": "npm", "hash": "b363e55e8006ca6fe21784d2db22bd15d7917f14"}