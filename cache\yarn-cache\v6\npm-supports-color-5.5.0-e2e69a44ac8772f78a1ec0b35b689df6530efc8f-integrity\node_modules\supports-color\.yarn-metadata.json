{"manifest": {"name": "supports-color", "version": "5.5.0", "description": "Detect whether a terminal supports color", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/chalk/supports-color.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "browser.js"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "dependencies": {"has-flag": "^3.0.0"}, "devDependencies": {"ava": "^0.25.0", "import-fresh": "^2.0.0", "xo": "^0.20.0"}, "browser": "browser.js", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-supports-color-5.5.0-e2e69a44ac8772f78a1ec0b35b689df6530efc8f-integrity\\node_modules\\supports-color\\package.json", "readmeFilename": "readme.md", "readme": "# supports-color [![Build Status](https://travis-ci.org/chalk/supports-color.svg?branch=master)](https://travis-ci.org/chalk/supports-color)\n\n> Detect whether a terminal supports color\n\n\n## Install\n\n```\n$ npm install supports-color\n```\n\n\n## Usage\n\n```js\nconst supportsColor = require('supports-color');\n\nif (supportsColor.stdout) {\n\tconsole.log('Terminal stdout supports color');\n}\n\nif (supportsColor.stdout.has256) {\n\tconsole.log('Terminal stdout supports 256 colors');\n}\n\nif (supportsColor.stderr.has16m) {\n\tconsole.log('Terminal stderr supports 16 million colors (truecolor)');\n}\n```\n\n\n## API\n\nReturns an `Object` with a `stdout` and `stderr` property for testing either streams. Each property is an `Object`, or `false` if color is not supported.\n\nThe `stdout`/`stderr` objects specifies a level of support for color through a `.level` property and a corresponding flag:\n\n- `.level = 1` and `.hasBasic = true`: Basic color support (16 colors)\n- `.level = 2` and `.has256 = true`: 256 color support\n- `.level = 3` and `.has16m = true`: Truecolor support (16 million colors)\n\n\n## Info\n\nIt obeys the `--color` and `--no-color` CLI flags.\n\nCan be overridden by the user with the flags `--color` and `--no-color`. For situations where using `--color` is not possible, add the environment variable `FORCE_COLOR=1` to forcefully enable color or `FORCE_COLOR=0` to forcefully disable. The use of `FORCE_COLOR` overrides all other color support checks.\n\nExplicit 256/Truecolor mode can be enabled using the `--color=256` and `--color=16m` flags, respectively.\n\n\n## Related\n\n- [supports-color-cli](https://github.com/chalk/supports-color-cli) - CLI for this module\n- [chalk](https://github.com/chalk/chalk) - Terminal string styling done right\n\n\n## Maintainers\n\n- [Sindre Sorhus](https://github.com/sindresorhus)\n- [Josh Junon](https://github.com/qix-)\n\n\n## License\n\nMIT\n", "licenseText": "MIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/supports-color/-/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f", "type": "tarball", "reference": "https://registry.yarnpkg.com/supports-color/-/supports-color-5.5.0.tgz", "hash": "e2e69a44ac8772f78a1ec0b35b689df6530efc8f", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "registry": "npm", "packageName": "supports-color", "cacheIntegrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow== sha1-4uaaRKyHcveKHsCzW2id9lMO/I8="}, "registry": "npm", "hash": "e2e69a44ac8772f78a1ec0b35b689df6530efc8f"}