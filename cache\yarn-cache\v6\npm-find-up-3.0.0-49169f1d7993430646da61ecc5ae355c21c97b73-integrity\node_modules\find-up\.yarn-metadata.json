{"manifest": {"name": "find-up", "version": "3.0.0", "description": "Find a file or directory by walking up parent directories", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/find-up.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "dependencies": {"locate-path": "^3.0.0"}, "devDependencies": {"ava": "*", "tempy": "^0.2.1", "xo": "*"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-find-up-3.0.0-49169f1d7993430646da61ecc5ae355c21c97b73-integrity\\node_modules\\find-up\\package.json", "readmeFilename": "readme.md", "readme": "# find-up [![Build Status: Linux and macOS](https://travis-ci.org/sindresorhus/find-up.svg?branch=master)](https://travis-ci.org/sindresorhus/find-up) [![Build Status: Windows](https://ci.appveyor.com/api/projects/status/l0cyjmvh5lq72vq2/branch/master?svg=true)](https://ci.appveyor.com/project/sindresorhus/find-up/branch/master)\n\n> Find a file or directory by walking up parent directories\n\n\n## Install\n\n```\n$ npm install find-up\n```\n\n\n## Usage\n\n```\n/\n└── Users\n\t\t└── sindresorhus\n\t\t\t\t├── unicorn.png\n\t\t\t\t└── foo\n\t\t\t\t\t\t└── bar\n\t\t\t\t\t\t\t\t├── baz\n\t\t\t\t\t\t\t\t└── example.js\n```\n\n`example.js`\n\n```js\nconst findUp = require('find-up');\n\n(async () => {\n\tconsole.log(await findUp('unicorn.png'));\n\t//=> '/Users/<USER>/unicorn.png'\n\n\tconsole.log(await findUp(['rainbow.png', 'unicorn.png']));\n\t//=> '/Users/<USER>/unicorn.png'\n})();\n```\n\n\n## API\n\n### findUp(filename, [options])\n\nReturns a `Promise` for either the filepath or `null` if it couldn't be found.\n\n### findUp([filenameA, filenameB], [options])\n\nReturns a `Promise` for either the first filepath found (by respecting the order) or `null` if none could be found.\n\n### findUp.sync(filename, [options])\n\nReturns a filepath or `null`.\n\n### findUp.sync([filenameA, filenameB], [options])\n\nReturns the first filepath found (by respecting the order) or `null`.\n\n#### filename\n\nType: `string`\n\nFilename of the file to find.\n\n#### options\n\nType: `Object`\n\n##### cwd\n\nType: `string`<br>\nDefault: `process.cwd()`\n\nDirectory to start from.\n\n\n## Related\n\n- [find-up-cli](https://github.com/sindresorhus/find-up-cli) - CLI for this module\n- [pkg-up](https://github.com/sindresorhus/pkg-up) - Find the closest package.json file\n- [pkg-dir](https://github.com/sindresorhus/pkg-dir) - Find the root directory of an npm package\n- [resolve-from](https://github.com/sindresorhus/resolve-from) - Resolve the path of a module like `require.resolve()` but from a given path\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com)\n", "licenseText": "MIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/find-up/-/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73", "type": "tarball", "reference": "https://registry.yarnpkg.com/find-up/-/find-up-3.0.0.tgz", "hash": "49169f1d7993430646da61ecc5ae355c21c97b73", "integrity": "sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==", "registry": "npm", "packageName": "find-up", "cacheIntegrity": "sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg== sha1-SRafHXmTQwZG2mHsxa41XCHJe3M="}, "registry": "npm", "hash": "49169f1d7993430646da61ecc5ae355c21c97b73"}