{"name": "@types/uuid", "version": "8.3.0", "description": "TypeScript definitions for uuid", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/iamolivinius", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/felipeochoa", "githubUsername": "f<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cjbarth", "githubUsername": "cj<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LinusU", "githubUsername": "LinusU"}, {"name": "<PERSON>", "url": "https://github.com/ctavan", "githubUsername": "ctavan"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/uuid"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "ad6e3cc91784463861502ee3a21d504b2b1708a3cbda624b0d0b662a24946484", "typeScriptVersion": "3.0"}