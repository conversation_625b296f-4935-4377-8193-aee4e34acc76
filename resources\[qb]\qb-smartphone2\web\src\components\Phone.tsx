import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../contexts/ThemeContext';
import { usePhone } from '../contexts/PhoneContext';
import './Phone.css';

interface PhoneProps {
  children: ReactNode;
  onClose: () => void;
}

const Phone: React.FC<PhoneProps> = ({ children, onClose }) => {
  const { theme } = useTheme();
  const { phoneData } = usePhone();

  const phoneVariants = {
    hidden: {
      opacity: 0,
      scale: 0.8,
      y: 50,
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
      },
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      y: 50,
      transition: {
        duration: 0.2,
      },
    },
  };

  return (
    <motion.div
      className="phone-wrapper"
      variants={phoneVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      onClick={(e) => e.stopPropagation()}
    >
      <div className={`phone-container ${theme}`}>
        {/* Phone Frame */}
        <div className="phone-frame">
          {/* Notch */}
          <div className="phone-notch">
            <div className="notch-speaker"></div>
            <div className="notch-camera"></div>
          </div>
          
          {/* Screen */}
          <div className="phone-screen">
            {children}
          </div>
          
          {/* Home Indicator */}
          <div className="home-indicator"></div>
        </div>
        
        {/* Power Button */}
        <div className="power-button" onClick={onClose}></div>
        
        {/* Volume Buttons */}
        <div className="volume-buttons">
          <div className="volume-up"></div>
          <div className="volume-down"></div>
        </div>
      </div>
    </motion.div>
  );
};

export default Phone;
