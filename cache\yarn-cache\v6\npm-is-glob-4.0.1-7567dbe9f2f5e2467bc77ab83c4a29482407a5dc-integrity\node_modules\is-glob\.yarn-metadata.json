{"manifest": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a better user experience.", "version": "4.0.1", "homepage": "https://github.com/micromatch/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "https://tuvistavie.com"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "https://github.com/micromatch/is-glob.git"}, "bugs": {"url": "https://github.com/micromatch/is-glob/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-extglob": "^2.1.1"}, "devDependencies": {"gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "is", "match", "matches", "pattern", "regex", "regular", "string", "test"], "verb": {"layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["assemble", "base", "update", "verb"]}, "reflinks": ["assemble", "bach", "base", "composer", "gulp", "has-glob", "is-valid-glob", "micromatch", "npm", "scaffold", "verb", "vinyl"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-glob-4.0.1-7567dbe9f2f5e2467bc77ab83c4a29482407a5dc-integrity\\node_modules\\is-glob\\package.json", "readmeFilename": "README.md", "readme": "# is-glob [![NPM version](https://img.shields.io/npm/v/is-glob.svg?style=flat)](https://www.npmjs.com/package/is-glob) [![NPM monthly downloads](https://img.shields.io/npm/dm/is-glob.svg?style=flat)](https://npmjs.org/package/is-glob) [![NPM total downloads](https://img.shields.io/npm/dt/is-glob.svg?style=flat)](https://npmjs.org/package/is-glob) [![Linux Build Status](https://img.shields.io/travis/micromatch/is-glob.svg?style=flat&label=Travis)](https://travis-ci.org/micromatch/is-glob) [![Windows Build Status](https://img.shields.io/appveyor/ci/micromatch/is-glob.svg?style=flat&label=AppVeyor)](https://ci.appveyor.com/project/micromatch/is-glob)\n\n> Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a better user experience.\n\nPlease consider following this project's author, [Jon Schlinkert](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save is-glob\n```\n\nYou might also be interested in [is-valid-glob](https://github.com/jonschlinkert/is-valid-glob) and [has-glob](https://github.com/jonschlinkert/has-glob).\n\n## Usage\n\n```js\nvar isGlob = require('is-glob');\n```\n\n### Default behavior\n\n**True**\n\nPatterns that have glob characters or regex patterns will return `true`:\n\n```js\nisGlob('!foo.js');\nisGlob('*.js');\nisGlob('**/abc.js');\nisGlob('abc/*.js');\nisGlob('abc/(aaa|bbb).js');\nisGlob('abc/[a-z].js');\nisGlob('abc/{a,b}.js');\n//=> true\n```\n\nExtglobs\n\n```js\nisGlob('abc/@(a).js');\nisGlob('abc/!(a).js');\nisGlob('abc/+(a).js');\nisGlob('abc/*(a).js');\nisGlob('abc/?(a).js');\n//=> true\n```\n\n**False**\n\nEscaped globs or extglobs return `false`:\n\n```js\nisGlob('abc/\\\\@(a).js');\nisGlob('abc/\\\\!(a).js');\nisGlob('abc/\\\\+(a).js');\nisGlob('abc/\\\\*(a).js');\nisGlob('abc/\\\\?(a).js');\nisGlob('\\\\!foo.js');\nisGlob('\\\\*.js');\nisGlob('\\\\*\\\\*/abc.js');\nisGlob('abc/\\\\*.js');\nisGlob('abc/\\\\(aaa|bbb).js');\nisGlob('abc/\\\\[a-z].js');\nisGlob('abc/\\\\{a,b}.js');\n//=> false\n```\n\nPatterns that do not have glob patterns return `false`:\n\n```js\nisGlob('abc.js');\nisGlob('abc/def/ghi.js');\nisGlob('foo.js');\nisGlob('abc/@.js');\nisGlob('abc/+.js');\nisGlob('abc/?.js');\nisGlob();\nisGlob(null);\n//=> false\n```\n\nArrays are also `false` (If you want to check if an array has a glob pattern, use [has-glob](https://github.com/jonschlinkert/has-glob)):\n\n```js\nisGlob(['**/*.js']);\nisGlob(['foo.js']);\n//=> false\n```\n\n### Option strict\n\nWhen `options.strict === false` the behavior is less strict in determining if a pattern is a glob. Meaning that\nsome patterns that would return `false` may return `true`. This is done so that matching libraries like [micromatch](https://github.com/micromatch/micromatch) have a chance at determining if the pattern is a glob or not.\n\n**True**\n\nPatterns that have glob characters or regex patterns will return `true`:\n\n```js\nisGlob('!foo.js', {strict: false});\nisGlob('*.js', {strict: false});\nisGlob('**/abc.js', {strict: false});\nisGlob('abc/*.js', {strict: false});\nisGlob('abc/(aaa|bbb).js', {strict: false});\nisGlob('abc/[a-z].js', {strict: false});\nisGlob('abc/{a,b}.js', {strict: false});\n//=> true\n```\n\nExtglobs\n\n```js\nisGlob('abc/@(a).js', {strict: false});\nisGlob('abc/!(a).js', {strict: false});\nisGlob('abc/+(a).js', {strict: false});\nisGlob('abc/*(a).js', {strict: false});\nisGlob('abc/?(a).js', {strict: false});\n//=> true\n```\n\n**False**\n\nEscaped globs or extglobs return `false`:\n\n```js\nisGlob('\\\\!foo.js', {strict: false});\nisGlob('\\\\*.js', {strict: false});\nisGlob('\\\\*\\\\*/abc.js', {strict: false});\nisGlob('abc/\\\\*.js', {strict: false});\nisGlob('abc/\\\\(aaa|bbb).js', {strict: false});\nisGlob('abc/\\\\[a-z].js', {strict: false});\nisGlob('abc/\\\\{a,b}.js', {strict: false});\n//=> false\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [assemble](https://www.npmjs.com/package/assemble): Get the rocks out of your socks! Assemble makes you fast at creating web projects… [more](https://github.com/assemble/assemble) | [homepage](https://github.com/assemble/assemble \"Get the rocks out of your socks! Assemble makes you fast at creating web projects. Assemble is used by thousands of projects for rapid prototyping, creating themes, scaffolds, boilerplates, e-books, UI components, API documentation, blogs, building websit\")\n* [base](https://www.npmjs.com/package/base): Framework for rapidly creating high quality, server-side node.js applications, using plugins like building blocks | [homepage](https://github.com/node-base/base \"Framework for rapidly creating high quality, server-side node.js applications, using plugins like building blocks\")\n* [update](https://www.npmjs.com/package/update): Be scalable! Update is a new, open source developer framework and CLI for automating updates… [more](https://github.com/update/update) | [homepage](https://github.com/update/update \"Be scalable! Update is a new, open source developer framework and CLI for automating updates of any kind in code projects.\")\n* [verb](https://www.npmjs.com/package/verb): Documentation generator for GitHub projects. Verb is extremely powerful, easy to use, and is used… [more](https://github.com/verbose/verb) | [homepage](https://github.com/verbose/verb \"Documentation generator for GitHub projects. Verb is extremely powerful, easy to use, and is used on hundreds of projects of all sizes to generate everything from API docs to readmes.\")\n\n### Contributors\n\n| **Commits** | **Contributor** |  \n| --- | --- |  \n| 47 | [jonschlinkert](https://github.com/jonschlinkert) |  \n| 5  | [doowb](https://github.com/doowb) |  \n| 1  | [phated](https://github.com/phated) |  \n| 1  | [danhper](https://github.com/danhper) |  \n| 1  | [paulmillr](https://github.com/paulmillr) |  \n\n### Author\n\n**Jon Schlinkert**\n\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n\n### License\n\nCopyright © 2019, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.8.0, on March 27, 2019._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-glob/-/is-glob-4.0.1.tgz#7567dbe9f2f5e2467bc77ab83c4a29482407a5dc", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-glob/-/is-glob-4.0.1.tgz", "hash": "7567dbe9f2f5e2467bc77ab83c4a29482407a5dc", "integrity": "sha512-5G0tKtBTFImOqDnLB2hG6Bp2qcKEFduo4tZu9MT/H6NQv/ghhy30o55ufafxJ/LdH79LLs2Kfrn85TLKyA7BUg==", "registry": "npm", "packageName": "is-glob", "cacheIntegrity": "sha512-5G0tKtBTFImOqDnLB2hG6Bp2qcKEFduo4tZu9MT/H6NQv/ghhy30o55ufafxJ/LdH79LLs2Kfrn85TLKyA7BUg== sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw="}, "registry": "npm", "hash": "7567dbe9f2f5e2467bc77ab83c4a29482407a5dc"}