// Generated by CoffeeScript 1.9.3
var MixedDeclarationSet,
  slice = [].slice;

module.exports = MixedDeclarationSet = (function() {
  var self;

  self = MixedDeclarationSet;

  MixedDeclarationSet.mix = function() {
    var i, len, mixed, ruleSets, rules;
    ruleSets = 1 <= arguments.length ? slice.call(arguments, 0) : [];
    mixed = new self;
    for (i = 0, len = ruleSets.length; i < len; i++) {
      rules = ruleSets[i];
      mixed.mixWithList(rules);
    }
    return mixed;
  };

  function MixedDeclarationSet() {
    this._declarations = {};
  }

  MixedDeclarationSet.prototype.mixWithList = function(rules) {
    var i, len, rule;
    rules.sort(function(a, b) {
      return a.selector.priority > b.selector.priority;
    });
    for (i = 0, len = rules.length; i < len; i++) {
      rule = rules[i];
      this._mixWithRule(rule);
    }
    return this;
  };

  MixedDeclarationSet.prototype._mixWithRule = function(rule) {
    var dec, prop, ref;
    ref = rule.styles._declarations;
    for (prop in ref) {
      dec = ref[prop];
      this._mixWithDeclaration(dec);
    }
  };

  MixedDeclarationSet.prototype._mixWithDeclaration = function(dec) {
    var cur;
    cur = this._declarations[dec.prop];
    if ((cur != null) && cur.important && !dec.important) {
      return;
    }
    this._declarations[dec.prop] = dec;
  };

  MixedDeclarationSet.prototype.get = function(prop) {
    if (prop == null) {
      return this._declarations;
    }
    if (this._declarations[prop] == null) {
      return null;
    }
    return this._declarations[prop].val;
  };

  MixedDeclarationSet.prototype.toObject = function() {
    var dec, obj, prop, ref;
    obj = {};
    ref = this._declarations;
    for (prop in ref) {
      dec = ref[prop];
      obj[prop] = dec.val;
    }
    return obj;
  };

  return MixedDeclarationSet;

})();
