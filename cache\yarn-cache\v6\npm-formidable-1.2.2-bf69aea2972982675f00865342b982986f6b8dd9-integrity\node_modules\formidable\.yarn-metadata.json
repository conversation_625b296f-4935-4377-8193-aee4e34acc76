{"manifest": {"name": "formidable", "description": "A node.js module for parsing form data, especially file uploads.", "homepage": "https://github.com/node-formidable/formidable", "funding": "https://ko-fi.com/tunnckoCore/commissions", "repository": {"type": "git", "url": "https://github.com/node-formidable/formidable.git"}, "license": "MIT", "version": "1.2.2", "devDependencies": {"gently": "^0.8.0", "findit": "^0.1.2", "hashish": "^0.0.4", "urun": "^0.0.6", "utest": "^0.0.8", "request": "^2.11.4"}, "files": ["lib", "benchmark-2020-01-29_xeon-x3440.png"], "main": "./lib/index.js", "scripts": {"test": "node test/run.js", "clean": "rm test/tmp/*"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-formidable-1.2.2-bf69aea2972982675f00865342b982986f6b8dd9-integrity\\node_modules\\formidable\\package.json", "readmeFilename": "README.md", "readme": "<p align=\"center\">\n  <img alt=\"npm formidable package logo\" src=\"https://raw.githubusercontent.com/node-formidable/formidable/master/logo.png\" />\n</p>\n\n# formidable [![npm version][npmv-img]][npmv-url] [![MIT license][license-img]][license-url] [![Libera Manifesto][libera-manifesto-img]][libera-manifesto-url] [![Twitter][twitter-img]][twitter-url]\n\n> A Node.js module for parsing form data, especially file uploads.\n\n### Important Notes\n\n- This README is for the upcoming (end of February) v2 release!\n- Every version prior and including `v1.2.2` is deprecated, please upgrade!\n- Install with `formidable@canary` until v2 land officially in `latest`\n- see more about the changes in the [CHANGELOG.md](https://github.com/node-formidable/formidable/blob/master/CHANGELOG.md)\n\n[![Code style][codestyle-img]][codestyle-url]\n[![codecoverage][codecov-img]][codecov-url]\n[![linux build status][linux-build-img]][build-url]\n[![windows build status][windows-build-img]][build-url]\n[![macos build status][macos-build-img]][build-url]\n\nIf you have any _how-to_ kind of questions, please read the [Contributing\nGuide][contributing-url] and [Code of Conduct][code_of_conduct-url]\ndocuments.<br /> For bugs reports and feature requests, [please create an\nissue][open-issue-url] or ping [@tunnckoCore](https://twitter.com/tunnckoCore)\nat Twitter.\n\n[![Conventional Commits][ccommits-img]][ccommits-url]\n[![Minimum Required Nodejs][nodejs-img]][npmv-url]\n[![Tidelift Subcsription][tidelift-img]][tidelift-url]\n[![Buy me a Kofi][kofi-img]][kofi-url]\n[![Renovate App Status][renovateapp-img]][renovateapp-url]\n[![Make A Pull Request][prs-welcome-img]][prs-welcome-url]\n\nThis project is [semantically versioned](https://semver.org) and available as\npart of the [Tidelift Subscription][tidelift-url] for professional grade\nassurances, enhanced support and security.\n[Learn more.](https://tidelift.com/subscription/pkg/npm-formidable?utm_source=npm-formidable&utm_medium=referral&utm_campaign=enterprise)\n\n_The maintainers of `formidable` and thousands of other packages are working\nwith Tidelift to deliver commercial support and maintenance for the Open Source\ndependencies you use to build your applications. Save time, reduce risk, and\nimprove code health, while paying the maintainers of the exact dependencies you\nuse._\n\n[![][npm-weekly-img]][npmv-url] [![][npm-monthly-img]][npmv-url]\n[![][npm-yearly-img]][npmv-url] [![][npm-alltime-img]][npmv-url]\n\n## Status: Maintained [![npm version][npmv-canary-img]][npmv-url]\n\nThis module was initially developed by\n[**@felixge**](https://github.com/felixge) for\n[Transloadit](http://transloadit.com/), a service focused on uploading and\nencoding images and videos. It has been battle-tested against hundreds of GBs of\nfile uploads from a large variety of clients and is considered production-ready\nand is used in production for years.\n\nCurrently, we are few maintainers trying to deal with it. :) More contributors\nare always welcome! :heart: Jump on\n[issue #412](https://github.com/felixge/node-formidable/issues/412) which is\nclosed, but if you are interested we can discuss it and add you after strict rules, like\nenabling Two-Factor Auth in your npm and GitHub accounts.\n\n_**Note:** The github `master` branch is a \"canary\" branch - try it with `npm i formidable@canary`.\nDo not expect (for now) things from it to be inside the`latest` \"dist-tag\" in the\nNpm. The`formidable@latest`is the`v1.2.1` version and probably it will be the\nlast`v1` release!_\n\n_**Note: v2 is coming soon!**_\n\n## Highlights\n\n- [Fast (~900-2500 mb/sec)](#benchmarks) & streaming multipart parser\n- Automatically writing file uploads to disk (soon optionally)\n- [Plugins API](#useplugin-plugin) - allowing custom parsers and plugins\n- Low memory footprint\n- Graceful error handling\n- Very high test coverage\n\n## Install\n\nThis project requires `Node.js >= 10.13`. Install it using\n[yarn](https://yarnpkg.com) or [npm](https://npmjs.com).<br /> _We highly\nrecommend to use Yarn when you think to contribute to this project._\n\n```sh\nnpm install formidable\n# or the canary version\nnpm install formidable@canary\n```\n\nor with Yarn v1/v2\n\n```sh\nyarn add formidable\n# or the canary version\nyarn add formidable@canary\n```\n\nThis is a low-level package, and if you're using a high-level framework it _may_\nalready be included. Check the examples below and the `examples/` folder.\n\n## Examples\n\nFor more examples look at the `examples/` directory.\n\n### with Node.js http module\n\nParse an incoming file upload, with the\n[Node.js's built-in `http` module](https://nodejs.org/api/http.html).\n\n```js\nconst http = require('http');\nconst formidable = require('formidable');\n\nconst server = http.createServer((req, res) => {\n  if (req.url === '/api/upload' && req.method.toLowerCase() === 'post') {\n    // parse a file upload\n    const form = formidable({ multiples: true });\n\n    form.parse(req, (err, fields, files) => {\n      res.writeHead(200, { 'content-type': 'application/json' });\n      res.end(JSON.stringify({ fields, files }, null, 2));\n    });\n\n    return;\n  }\n\n  // show a file upload form\n  res.writeHead(200, { 'content-type': 'text/html' });\n  res.end(`\n    <h2>With Node.js <code>\"http\"</code> module</h2>\n    <form action=\"/api/upload\" enctype=\"multipart/form-data\" method=\"post\">\n      <div>Text field title: <input type=\"text\" name=\"title\" /></div>\n      <div>File: <input type=\"file\" name=\"multipleFiles\" multiple=\"multiple\" /></div>\n      <input type=\"submit\" value=\"Upload\" />\n    </form>\n  `);\n});\n\nserver.listen(8080, () => {\n  console.log('Server listening on http://localhost:8080/ ...');\n});\n```\n\n### with Express.js\n\nThere are multiple variants to do this, but Formidable just need Node.js Request\nstream, so something like the following example should work just fine, without\nany third-party [Express.js](https://ghub.now.sh/express) middleware.\n\nOr try the\n[examples/with-express.js](https://github.com/node-formidable/node-formidable/blob/master/examples/with-express.js)\n\n```js\nconst express = require('express');\nconst formidable = require('formidable');\n\nconst app = express();\n\napp.get('/', (req, res) => {\n  res.send(`\n    <h2>With <code>\"express\"</code> npm package</h2>\n    <form action=\"/api/upload\" enctype=\"multipart/form-data\" method=\"post\">\n      <div>Text field title: <input type=\"text\" name=\"title\" /></div>\n      <div>File: <input type=\"file\" name=\"someExpressFiles\" multiple=\"multiple\" /></div>\n      <input type=\"submit\" value=\"Upload\" />\n    </form>\n  `);\n});\n\napp.post('/api/upload', (req, res, next) => {\n  const form = formidable({ multiples: true });\n\n  form.parse(req, (err, fields, files) => {\n    if (err) {\n      next(err);\n      return;\n    }\n    res.json({ fields, files });\n  });\n});\n\napp.listen(3000, () => {\n  console.log('Server listening on http://localhost:3000 ...');\n});\n```\n\n### with Koa and Formidable\n\nOf course, with [Koa v1, v2 or future v3](https://ghub.now.sh/koa) the things\nare very similar. You can use `formidable` manually as shown below or through\nthe [koa-better-body](https://ghub.now.sh/koa-better-body) package which is\nusing `formidable` under the hood and support more features and different\nrequest bodies, check its documentation for more info.\n\n_Note: this example is assuming Koa v2. Be aware that you should pass `ctx.req`\nwhich is Node.js's Request, and **NOT** the `ctx.request` which is Koa's Request\nobject - there is a difference._\n\n```js\nconst Koa = require('koa');\nconst formidable = require('formidable');\n\nconst app = new Koa();\n\napp.on('error', (err) => {\n  console.error('server error', err);\n});\n\napp.use(async (ctx, next) => {\n  if (ctx.url === '/api/upload' && ctx.method.toLowerCase() === 'post') {\n    const form = formidable({ multiples: true });\n\n    // not very elegant, but that's for now if you don't want touse `koa-better-body`\n    // or other middlewares.\n    await new Promise((resolve, reject) => {\n      form.parse(ctx.req, (err, fields, files) => {\n        if (err) {\n          reject(err);\n          return;\n        }\n\n        ctx.set('Content-Type', 'application/json');\n        ctx.status = 200;\n        ctx.state = { fields, files };\n        ctx.body = JSON.stringify(ctx.state, null, 2);\n        resolve();\n      });\n    });\n    await next();\n    return;\n  }\n\n  // show a file upload form\n  ctx.set('Content-Type', 'text/html');\n  ctx.status = 200;\n  ctx.body = `\n    <h2>With <code>\"koa\"</code> npm package</h2>\n    <form action=\"/api/upload\" enctype=\"multipart/form-data\" method=\"post\">\n    <div>Text field title: <input type=\"text\" name=\"title\" /></div>\n    <div>File: <input type=\"file\" name=\"koaFiles\" multiple=\"multiple\" /></div>\n    <input type=\"submit\" value=\"Upload\" />\n    </form>\n  `;\n});\n\napp.use((ctx) => {\n  console.log('The next middleware is called');\n  console.log('Results:', ctx.state);\n});\n\napp.listen(3000, () => {\n  console.log('Server listening on http://localhost:3000 ...');\n});\n```\n\n## Benchmarks (for v2)\n\nThe benchmark is quite old, from the old codebase. But maybe quite true though.\nPreviously the numbers was around ~500 mb/sec. Currently with moving to the new\nNode.js Streams API it's faster. You can clearly see the differences between the\nNode versions.\n\n_Note: a lot better benchmarking could and should be done in future._\n\nBenchmarked on 8GB RAM, Xeon X3440 (2.53 GHz, 4 cores, 8 threads)\n\n```\n~/github/node-formidable master\n❯ nve --parallel 8 10 12 13 node benchmark/bench-multipart-parser.js\n\n ⬢  Node 8\n\n1261.08 mb/sec\n\n ⬢  Node 10\n\n1113.04 mb/sec\n\n ⬢  Node 12\n\n2107.00 mb/sec\n\n ⬢  Node 13\n\n2566.42 mb/sec\n```\n\n![benchmark January 29th, 2020](./benchmark-2020-01-29_xeon-x3440.png)\n\n## API\n\n### Formidable / IncomingForm\n\nAll shown are equivalent.\n\n_Please pass [`options`](#options) to the function/constructor, not by assigning\nthem to the instance `form`_\n\n```js\nconst formidable = require('formidable');\nconst form = formidable(options);\n\n// or\nconst { formidable } = require('formidable');\nconst form = formidable(options);\n\n// or\nconst { IncomingForm } = require('formidable');\nconst form = new IncomingForm(options);\n\n// or\nconst { Formidable } = require('formidable');\nconst form = new Formidable(options);\n```\n\n### Options\n\nSee it's defaults in [src/Formidable.js](./src/Formidable.js#L14-L22) (the\n`DEFAULT_OPTIONS` constant).\n\n- `options.encoding` **{string}** - default `'utf-8'`; sets encoding for\n  incoming form fields,\n- `options.uploadDir` **{string}** - default `os.tmpdir()`; the directory for\n  placing file uploads in. You can move them later by using `fs.rename()`\n- `options.keepExtensions` **{boolean}** - default `false`; to include the\n  extensions of the original files or not\n- `options.maxFileSize` **{number}** - default `200 * 1024 * 1024` (200mb);\n  limit the size of uploaded file.\n- `options.maxFields` **{number}** - default `1000`; limit the number of fields\n  that the Querystring parser will decode, set 0 for unlimited\n- `options.maxFieldsSize` **{number}** - default `20 * 1024 * 1024` (20mb);\n  limit the amount of memory all fields together (except files) can allocate in\n  bytes.\n- `options.hash` **{boolean}** - default `false`; include checksums calculated\n  for incoming files, set this to some hash algorithm, see\n  [crypto.createHash](https://nodejs.org/api/crypto.html#crypto_crypto_createhash_algorithm_options)\n  for available algorithms\n- `options.multiples` **{boolean}** - default `false`; when you call the\n  `.parse` method, the `files` argument (of the callback) will contain arrays of\n  files for inputs which submit multiple files using the HTML5 `multiple`\n  attribute. Also, the `fields` argument will contain arrays of values for\n  fields that have names ending with '[]'.\n\n_**Note:** If this size of combined fields, or size of some file is exceeded, an\n`'error'` event is fired._\n\n```js\n// The amount of bytes received for this form so far.\nform.bytesReceived;\n```\n\n```js\n// The expected number of bytes in this form.\nform.bytesExpected;\n```\n\n### .parse(request, callback)\n\nParses an incoming Node.js `request` containing form data. If `callback` is\nprovided, all fields and files are collected and passed to the callback.\n\n```js\nconst formidable = require('formidable');\n\nconst form = formidable({ multiples: true, uploadDir: __dirname });\n\nform.parse(req, (err, fields, files) => {\n  console.log('fields:', fields);\n  console.log('files:', files);\n});\n```\n\nYou may overwrite this method if you are interested in directly accessing the\nmultipart stream. Doing so will disable any `'field'` / `'file'` events\nprocessing which would occur otherwise, making you fully responsible for\nhandling the processing.\n\nIn the example below, we listen on couple of events and direct them to the\n`data` listener, so you can do whatever you choose there, based on whether its\nbefore the file been emitted, the header value, the header name, on field, on\nfile and etc.\n\nOr the other way could be to just override the `form.onPart` as it's shown a bit\nlater.\n\n```js\nform.once('error', console.error);\n\nform.on('fileBegin', (filename, file) => {\n  form.emit('data', { name: 'fileBegin', filename, value: file });\n});\n\nform.on('file', (filename, file) => {\n  form.emit('data', { name: 'file', key: filename, value: file });\n});\n\nform.on('field', (fieldName, fieldValue) => {\n  form.emit('data', { name: 'field', key: fieldName, value: fieldValue });\n});\n\nform.once('end', () => {\n  console.log('Done!');\n});\n\n// If you want to customize whatever you want...\nform.on('data', ({ name, key, value, buffer, start, end, ...more }) => {\n  if (name === 'partBegin') {\n  }\n  if (name === 'partData') {\n  }\n  if (name === 'headerField') {\n  }\n  if (name === 'headerValue') {\n  }\n  if (name === 'headerEnd') {\n  }\n  if (name === 'headersEnd') {\n  }\n  if (name === 'field') {\n    console.log('field name:', key);\n    console.log('field value:', value);\n  }\n  if (name === 'file') {\n    console.log('file:', key, value);\n  }\n  if (name === 'fileBegin') {\n    console.log('fileBegin:', key, value);\n  }\n});\n```\n\n### .use(plugin: Plugin)\n\nA method that allows you to extend the Formidable library. By default we include\n4 plugins, which esentially are adapters to plug the different built-in parsers.\n\n**The plugins added by this method are always enabled.**\n\n_See [src/plugins/](./src/plugins/) for more detailed look on default plugins._\n\nThe `plugin` param has such signature:\n\n```typescript\nfunction(formidable: Formidable, options: Options): void;\n```\n\nThe architecture is simple. The `plugin` is a function that is passed with the\nFormidable instance (the `form` across the README examples) and the options.\n\n**Note:** the plugin function's `this` context is also the same instance.\n\n```js\nconst formidable = require('formidable');\n\nconst form = formidable({ keepExtensions: true });\n\nform.use((self, options) => {\n  // self === this === form\n  console.log('woohoo, custom plugin');\n  // do your stuff; check `src/plugins` for inspiration\n});\n\nform.parse(req, (error, fields, files) => {\n  console.log('done!');\n});\n```\n\n**Important to note**, is that inside plugin `this.options`, `self.options` and\n`options` MAY or MAY NOT be the same. General best practice is to always use the\n`this`, so you can later test your plugin independently and more easily.\n\nIf you want to disable some parsing capabilities of Formidable, you can disable\nthe plugin which corresponds to the parser. For example, if you want to disable\nmultipart parsing (so the [src/parsers/Multipart.js](./src/parsers/Multipart.js)\nwhich is used in [src/plugins/multipart.js](./src/plugins/multipart.js)), then\nyou can remove it from the `options.enabledPlugins`, like so\n\n```js\nconst { Formidable } = require('formidable');\n\nconst form = new Formidable({\n  hash: 'sha1',\n  enabledPlugins: ['octetstream', 'querystring', 'json'],\n});\n```\n\n**Be aware** that the order _MAY_ be important too. The names corresponds 1:1 to\nfiles in [src/plugins/](./src/plugins) folder.\n\nPull requests for new built-in plugins MAY be accepted - for example, more\nadvanced querystring parser. Add your plugin as a new file in `src/plugins/`\nfolder (lowercased) and follow how the other plugins are made.\n\n### form.onPart\n\nIf you want to use Formidable to only handle certain parts for you, you can do\nsomething similar. Or see\n[#387](https://github.com/node-formidable/node-formidable/issues/387) for\ninspiration, you can for example validate the mime-type.\n\n```js\nconst form = formidable();\n\nform.onPart = (part) => {\n  part.on('data', (buffer) {\n    // do whatever you want here\n  });\n};\n```\n\nFor example, force Formidable to be used only on non-file \"parts\" (i.e., html\nfields)\n\n```js\nconst form = formidable();\n\nform.onPart = function(part) {\n  // let formidable handle only non-file parts\n  if (part.filename === '' || !part.mime) {\n    // used internally, please do not override!\n    form.handlePart(part);\n  }\n};\n```\n\n### File\n\n```ts\nexport interface File {\n  // The size of the uploaded file in bytes.\n  // If the file is still being uploaded (see `'fileBegin'` event),\n  // this property says how many bytes of the file have been written to disk yet.\n  file.size: number;\n\n  // The path this file is being written to. You can modify this in the `'fileBegin'` event in\n  // case you are unhappy with the way formidable generates a temporary path for your files.\n  file.path: string;\n\n  // The name this file had according to the uploading client.\n  file.name: string | null;\n\n  // The mime type of this file, according to the uploading client.\n  file.type: string | null;\n\n  // A Date object (or `null`) containing the time this file was last written to.\n  // Mostly here for compatibility with the [W3C File API Draft](http://dev.w3.org/2006/webapi/FileAPI/).\n  file.lastModifiedDate: Date | null;\n\n  // If `options.hash` calculation was set, you can read the hex digest out of this var.\n  file.hash: string | 'sha1' | 'md5' | 'sha256' | null;\n}\n```\n\n#### file.toJSON()\n\nThis method returns a JSON-representation of the file, allowing you to\n`JSON.stringify()` the file which is useful for logging and responding to\nrequests.\n\n### Events\n\n#### `'progress'`\n\nEmitted after each incoming chunk of data that has been parsed. Can be used to\nroll your own progress bar.\n\n```js\nform.on('progress', (bytesReceived, bytesExpected) => {});\n```\n\n#### `'field'`\n\nEmitted whenever a field / value pair has been received.\n\n```js\nform.on('field', (name, value) => {});\n```\n\n#### `'fileBegin'`\n\nEmitted whenever a new file is detected in the upload stream. Use this event if\nyou want to stream the file to somewhere else while buffering the upload on the\nfile system.\n\n```js\nform.on('fileBegin', (name, file) => {});\n```\n\n#### `'file'`\n\nEmitted whenever a field / file pair has been received. `file` is an instance of\n`File`.\n\n```js\nform.on('file', (name, file) => {});\n```\n\n#### `'error'`\n\nEmitted when there is an error processing the incoming form. A request that\nexperiences an error is automatically paused, you will have to manually call\n`request.resume()` if you want the request to continue firing `'data'` events.\n\n```js\nform.on('error', (err) => {});\n```\n\n#### `'aborted'`\n\nEmitted when the request was aborted by the user. Right now this can be due to a\n'timeout' or 'close' event on the socket. After this event is emitted, an\n`error` event will follow. In the future there will be a separate 'timeout'\nevent (needs a change in the node core).\n\n```js\nform.on('aborted', () => {});\n```\n\n#### `'end'`\n\nEmitted when the entire request has been received, and all contained files have\nfinished flushing to disk. This is a great place for you to send your response.\n\n```js\nform.on('end', () => {});\n```\n\n## Ports & Credits\n\n- [multipart-parser](http://github.com/FooBarWidget/multipart-parser): a C++\n  parser based on formidable\n- [Ryan Dahl](http://twitter.com/ryah) for his work on\n  [http-parser](http://github.com/ry/http-parser) which heavily inspired the\n  initial `multipart_parser.js`.\n\n## Contributing\n\nIf the documentation is unclear or has a typo, please click on the page's `Edit`\nbutton (pencil icon) and suggest a correction. If you would like to help us fix\na bug or add a new feature, please check our\n[Contributing Guide](./CONTRIBUTING.md). Pull requests are welcome!\n\nThanks goes to these wonderful people\n([emoji key](https://allcontributors.org/docs/en/emoji-key)):\n\n<!-- ALL-CONTRIBUTORS-LIST:START -->\n<!-- prettier-ignore-start -->\n<!-- markdownlint-disable -->\n<table>\n  <tr>\n    <td align=\"center\"><a href=\"https://twitter.com/felixge\"><img src=\"https://avatars3.githubusercontent.com/u/15000?s=460&v=4\" width=\"100px;\" alt=\"\"/><br /><sub><b>Felix Geisendörfer</b></sub></a><br /><a href=\"https://github.com/node-formidable/node-formidable/commits?author=felixge\" title=\"Code\">💻</a> <a href=\"#design-felixge\" title=\"Design\">🎨</a> <a href=\"#ideas-felixge\" title=\"Ideas, Planning, & Feedback\">🤔</a> <a href=\"https://github.com/node-formidable/node-formidable/commits?author=felixge\" title=\"Documentation\">📖</a></td>\n    <td align=\"center\"><a href=\"https://tunnckoCore.com\"><img src=\"https://avatars3.githubusercontent.com/u/5038030?v=4\" width=\"100px;\" alt=\"\"/><br /><sub><b>Charlike Mike Reagent</b></sub></a><br /><a href=\"https://github.com/node-formidable/node-formidable/issues?q=author%3AtunnckoCore\" title=\"Bug reports\">🐛</a> <a href=\"#infra-tunnckoCore\" title=\"Infrastructure (Hosting, Build-Tools, etc)\">🚇</a> <a href=\"#design-tunnckoCore\" title=\"Design\">🎨</a> <a href=\"https://github.com/node-formidable/node-formidable/commits?author=tunnckoCore\" title=\"Code\">💻</a> <a href=\"https://github.com/node-formidable/node-formidable/commits?author=tunnckoCore\" title=\"Documentation\">📖</a> <a href=\"#example-tunnckoCore\" title=\"Examples\">💡</a> <a href=\"#ideas-tunnckoCore\" title=\"Ideas, Planning, & Feedback\">🤔</a> <a href=\"#maintenance-tunnckoCore\" title=\"Maintenance\">🚧</a> <a href=\"https://github.com/node-formidable/node-formidable/commits?author=tunnckoCore\" title=\"Tests\">⚠️</a></td>\n    <td align=\"center\"><a href=\"https://github.com/kedarv\"><img src=\"https://avatars1.githubusercontent.com/u/1365665?v=4\" width=\"100px;\" alt=\"\"/><br /><sub><b>Kedar</b></sub></a><br /><a href=\"https://github.com/node-formidable/node-formidable/commits?author=kedarv\" title=\"Code\">💻</a> <a href=\"https://github.com/node-formidable/node-formidable/commits?author=kedarv\" title=\"Tests\">⚠️</a> <a href=\"#question-kedarv\" title=\"Answering Questions\">💬</a> <a href=\"https://github.com/node-formidable/node-formidable/issues?q=author%3Akedarv\" title=\"Bug reports\">🐛</a></td>\n    <td align=\"center\"><a href=\"https://github.com/GrosSacASac\"><img src=\"https://avatars0.githubusercontent.com/u/5721194?v=4\" width=\"100px;\" alt=\"\"/><br /><sub><b>Walle Cyril</b></sub></a><br /><a href=\"#question-GrosSacASac\" title=\"Answering Questions\">💬</a> <a href=\"https://github.com/node-formidable/node-formidable/issues?q=author%3AGrosSacASac\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/node-formidable/node-formidable/commits?author=GrosSacASac\" title=\"Code\">💻</a> <a href=\"#financial-GrosSacASac\" title=\"Financial\">💵</a> <a href=\"#ideas-GrosSacASac\" title=\"Ideas, Planning, & Feedback\">🤔</a> <a href=\"#maintenance-GrosSacASac\" title=\"Maintenance\">🚧</a></td>\n    <td align=\"center\"><a href=\"https://github.com/xarguments\"><img src=\"https://avatars2.githubusercontent.com/u/40522463?v=4\" width=\"100px;\" alt=\"\"/><br /><sub><b>Xargs</b></sub></a><br /><a href=\"#question-xarguments\" title=\"Answering Questions\">💬</a> <a href=\"https://github.com/node-formidable/node-formidable/issues?q=author%3Axarguments\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/node-formidable/node-formidable/commits?author=xarguments\" title=\"Code\">💻</a> <a href=\"#maintenance-xarguments\" title=\"Maintenance\">🚧</a></td>\n    <td align=\"center\"><a href=\"https://github.com/Amit-A\"><img src=\"https://avatars1.githubusercontent.com/u/7987238?v=4\" width=\"100px;\" alt=\"\"/><br /><sub><b>Amit-A</b></sub></a><br /><a href=\"#question-Amit-A\" title=\"Answering Questions\">💬</a> <a href=\"https://github.com/node-formidable/node-formidable/issues?q=author%3AAmit-A\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/node-formidable/node-formidable/commits?author=Amit-A\" title=\"Code\">💻</a></td>\n  </tr>\n  <tr>\n    <td align=\"center\"><a href=\"https://charmander.me/\"><img src=\"https://avatars1.githubusercontent.com/u/1889843?v=4\" width=\"100px;\" alt=\"\"/><br /><sub><b>Charmander</b></sub></a><br /><a href=\"#question-charmander\" title=\"Answering Questions\">💬</a> <a href=\"https://github.com/node-formidable/node-formidable/issues?q=author%3Acharmander\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/node-formidable/node-formidable/commits?author=charmander\" title=\"Code\">💻</a> <a href=\"#ideas-charmander\" title=\"Ideas, Planning, & Feedback\">🤔</a> <a href=\"#maintenance-charmander\" title=\"Maintenance\">🚧</a></td>\n    <td align=\"center\"><a href=\"https://twitter.com/dylan_piercey\"><img src=\"https://avatars2.githubusercontent.com/u/4985201?v=4\" width=\"100px;\" alt=\"\"/><br /><sub><b>Dylan Piercey</b></sub></a><br /><a href=\"#ideas-DylanPiercey\" title=\"Ideas, Planning, & Feedback\">🤔</a></td>\n    <td align=\"center\"><a href=\"http://ochrona.jawne.info.pl\"><img src=\"https://avatars1.githubusercontent.com/u/3618479?v=4\" width=\"100px;\" alt=\"\"/><br /><sub><b>Adam Dobrawy</b></sub></a><br /><a href=\"https://github.com/node-formidable/node-formidable/issues?q=author%3Aad-m\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/node-formidable/node-formidable/commits?author=ad-m\" title=\"Documentation\">📖</a></td>\n    <td align=\"center\"><a href=\"https://github.com/amitrohatgi\"><img src=\"https://avatars3.githubusercontent.com/u/12177021?v=4\" width=\"100px;\" alt=\"\"/><br /><sub><b>amitrohatgi</b></sub></a><br /><a href=\"#ideas-amitrohatgi\" title=\"Ideas, Planning, & Feedback\">🤔</a></td>\n    <td align=\"center\"><a href=\"https://github.com/fengxinming\"><img src=\"https://avatars2.githubusercontent.com/u/6262382?v=4\" width=\"100px;\" alt=\"\"/><br /><sub><b>Jesse Feng</b></sub></a><br /><a href=\"https://github.com/node-formidable/node-formidable/issues?q=author%3Afengxinming\" title=\"Bug reports\">🐛</a></td>\n    <td align=\"center\"><a href=\"https://qtmsheep.com\"><img src=\"https://avatars1.githubusercontent.com/u/7271496?v=4\" width=\"100px;\" alt=\"\"/><br /><sub><b>Nathanael Demacon</b></sub></a><br /><a href=\"#question-quantumsheep\" title=\"Answering Questions\">💬</a> <a href=\"https://github.com/node-formidable/node-formidable/commits?author=quantumsheep\" title=\"Code\">💻</a> <a href=\"https://github.com/node-formidable/node-formidable/pulls?q=is%3Apr+reviewed-by%3Aquantumsheep\" title=\"Reviewed Pull Requests\">👀</a></td>\n  </tr>\n  <tr>\n    <td align=\"center\"><a href=\"https://github.com/MunMunMiao\"><img src=\"https://avatars1.githubusercontent.com/u/18216142?v=4\" width=\"100px;\" alt=\"\"/><br /><sub><b>MunMunMiao</b></sub></a><br /><a href=\"https://github.com/node-formidable/node-formidable/issues?q=author%3AMunMunMiao\" title=\"Bug reports\">🐛</a></td>\n    <td align=\"center\"><a href=\"https://github.com/gabipetrovay\"><img src=\"https://avatars0.githubusercontent.com/u/1170398?v=4\" width=\"100px;\" alt=\"\"/><br /><sub><b>Gabriel Petrovay</b></sub></a><br /><a href=\"https://github.com/node-formidable/node-formidable/issues?q=author%3Agabipetrovay\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/node-formidable/node-formidable/commits?author=gabipetrovay\" title=\"Code\">💻</a></td>\n    <td align=\"center\"><a href=\"https://github.com/Elzair\"><img src=\"https://avatars0.githubusercontent.com/u/2352818?v=4\" width=\"100px;\" alt=\"\"/><br /><sub><b>Philip Woods</b></sub></a><br /><a href=\"https://github.com/node-formidable/node-formidable/commits?author=Elzair\" title=\"Code\">💻</a> <a href=\"#ideas-Elzair\" title=\"Ideas, Planning, & Feedback\">🤔</a></td>\n    <td align=\"center\"><a href=\"https://github.com/dmolim\"><img src=\"https://avatars2.githubusercontent.com/u/7090374?v=4\" width=\"100px;\" alt=\"\"/><br /><sub><b>Dmitry Ivonin</b></sub></a><br /><a href=\"https://github.com/node-formidable/node-formidable/commits?author=dmolim\" title=\"Documentation\">📖</a></td>\n    <td align=\"center\"><a href=\"https://audiobox.fm\"><img src=\"https://avatars1.githubusercontent.com/u/12844?v=4\" width=\"100px;\" alt=\"\"/><br /><sub><b>Claudio Poli</b></sub></a><br /><a href=\"https://github.com/node-formidable/node-formidable/commits?author=masterkain\" title=\"Code\">💻</a></td>\n  </tr>\n</table>\n\n<!-- markdownlint-enable -->\n<!-- prettier-ignore-end -->\n\n<!-- ALL-CONTRIBUTORS-LIST:END -->\n\n## License\n\nFormidable is licensed under the [MIT License][license-url].\n\n<!-- badges -->\n<!-- prettier-ignore-start -->\n\n[codestyle-url]: https://github.com/airbnb/javascript\n[codestyle-img]: https://badgen.net/badge/code%20style/airbnb%20%2B%20prettier/ff5a5f?icon=airbnb&cache=300\n[codecov-url]: https://codecov.io/gh/node-formidable/node-formidable\n[codecov-img]: https://badgen.net/codecov/c/github/node-formidable/node-formidable/master?icon=codecov\n[npmv-canary-img]: https://badgen.net/npm/v/formidable/canary?icon=npm\n[npmv-dev-img]: https://badgen.net/npm/v/formidable/dev?icon=npm\n[npmv-img]: https://badgen.net/npm/v/formidable?icon=npm\n[npmv-url]: https://npmjs.com/package/formidable\n[license-img]: https://badgen.net/npm/license/formidable\n[license-url]: https://github.com/node-formidable/node-formidable/blob/master/LICENSE\n[chat-img]: https://badgen.net/badge/chat/on%20gitter/46BC99?icon=gitter\n[chat-url]: https://gitter.im/node-formidable/Lobby\n[libera-manifesto-url]: https://liberamanifesto.com\n[libera-manifesto-img]: https://badgen.net/badge/libera/manifesto/grey\n[renovateapp-url]: https://renovatebot.com\n[renovateapp-img]: https://badgen.net/badge/renovate/enabled/green?cache=300\n[prs-welcome-img]: https://badgen.net/badge/PRs/welcome/green?cache=300\n[prs-welcome-url]: http://makeapullrequest.com\n[twitter-url]: https://twitter.com/tunnckoCore\n[twitter-img]: https://badgen.net/twitter/follow/tunnckoCore?icon=twitter&color=1da1f2&cache=300\n\n[npm-weekly-img]: https://badgen.net/npm/dw/formidable?icon=npm&cache=300\n[npm-monthly-img]: https://badgen.net/npm/dm/formidable?icon=npm&cache=300\n[npm-yearly-img]: https://badgen.net/npm/dy/formidable?icon=npm&cache=300\n[npm-alltime-img]: https://badgen.net/npm/dt/formidable?icon=npm&cache=300&label=total%20downloads\n\n[nodejs-img]: https://badgen.net/badge/node/>=%2010.13/green?cache=300\n\n[ccommits-url]: https://conventionalcommits.org/\n[ccommits-img]: https://badgen.net/badge/conventional%20commits/v1.0.0/green?cache=300\n\n[contributing-url]: https://github.com/node-formidable/node-formidable/blob/master/CONTRIBUTING.md\n[code_of_conduct-url]: https://github.com/node-formidable/node-formidable/blob/master/CODE_OF_CONDUCT.md\n\n[open-issue-url]: https://github.com/node-formidable/node-formidable/issues/new\n\n[tidelift-url]: https://tidelift.com/subscription/pkg/npm-formidable?utm_source=npm-formidable&utm_medium=referral&utm_campaign=enterprise\n[tidelift-img]: https://badgen.net/badge/tidelift/subscription/4B5168?labelColor=F6914D\n\n[kofi-url]: https://ko-fi.com/tunnckoCore/commissions\n[kofi-img]: https://badgen.net/badge/ko-fi/support/29abe0c2?cache=300&icon=https://rawcdn.githack.com/tunnckoCore/badgen-icons/f8264c6414e0bec449dd86f2241d50a9b89a1203/icons/kofi.svg\n\n[linux-build-img]: https://badgen-net.charlike.now.sh/github/checks/node-formidable/node-formidable?label=linux%20build&icon=github\n[macos-build-img]: https://badgen-net.charlike.now.sh/github/checks/node-formidable/node-formidable?label=macos%20build&icon=github\n[windows-build-img]: https://badgen-net.charlike.now.sh/github/checks/node-formidable/node-formidable?label=windows%20build&icon=github\n[build-url]: https://github.com/node-formidable/node-formidable/actions?query=workflow%3Anodejs\n<!-- prettier-ignore-end -->\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2011-present <PERSON>, and contributors.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/formidable/-/formidable-1.2.2.tgz#bf69aea2972982675f00865342b982986f6b8dd9", "type": "tarball", "reference": "https://registry.yarnpkg.com/formidable/-/formidable-1.2.2.tgz", "hash": "bf69aea2972982675f00865342b982986f6b8dd9", "integrity": "sha512-V8gLm+41I/8kguQ4/o1D3RIHRmhYFG4pnNyonvua+40rqcEmT4+V71yaZ3B457xbbgCsCfjSPi65u/W6vK1U5Q==", "registry": "npm", "packageName": "formidable", "cacheIntegrity": "sha512-V8gLm+41I/8kguQ4/o1D3RIHRmhYFG4pnNyonvua+40rqcEmT4+V71yaZ3B457xbbgCsCfjSPi65u/W6vK1U5Q== sha1-v2muopcpgmdfAIZTQrmCmG9rjdk="}, "registry": "npm", "hash": "bf69aea2972982675f00865342b982986f6b8dd9"}