{"manifest": {"name": "browserify-des", "version": "1.0.2", "description": "", "main": "index.js", "scripts": {"test": "standard && node test.js | tspec"}, "repository": {"type": "git", "url": "git+https://github.com/crypto-browserify/browserify-des.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/browserify-des/issues"}, "homepage": "https://github.com/crypto-browserify/browserify-des#readme", "dependencies": {"cipher-base": "^1.0.1", "des.js": "^1.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}, "devDependencies": {"standard": "^5.3.1", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-browserify-des-1.0.2-3af4f1f59839403572f1c66204375f7a7f703e9c-integrity\\node_modules\\browserify-des\\package.json", "readmeFilename": "readme.md", "readme": "browserify-des\n===\n\n[![Build Status](https://travis-ci.org/crypto-browserify/browserify-des.svg)](https://travis-ci.org/crypto-browserify/browserify-des)\n\nDES for browserify\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017 <PERSON>, <PERSON><PERSON> & contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/browserify-des/-/browserify-des-1.0.2.tgz#3af4f1f59839403572f1c66204375f7a7f703e9c", "type": "tarball", "reference": "https://registry.yarnpkg.com/browserify-des/-/browserify-des-1.0.2.tgz", "hash": "3af4f1f59839403572f1c66204375f7a7f703e9c", "integrity": "sha512-BioO1xf3hFwz4kc6iBhI3ieDFompMhrMlnDFC4/0/vd5MokpuAc3R+LYbwTA9A5Yc9pq9UYPqffKpW2ObuwX5A==", "registry": "npm", "packageName": "browserify-des", "cacheIntegrity": "sha512-BioO1xf3hFwz4kc6iBhI3ieDFompMhrMlnDFC4/0/vd5MokpuAc3R+LYbwTA9A5Yc9pq9UYPqffKpW2ObuwX5A== sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw="}, "registry": "npm", "hash": "3af4f1f59839403572f1c66204375f7a7f703e9c"}