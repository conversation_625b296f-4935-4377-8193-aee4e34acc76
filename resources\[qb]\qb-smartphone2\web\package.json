{"name": "qb-smartphone2-ui", "version": "2.0.0", "description": "Modern smartphone UI for QBCore", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.11.47", "@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "typescript": "^4.7.4", "web-vitals": "^2.1.4", "@emotion/react": "^11.10.0", "@emotion/styled": "^11.10.0", "@mui/material": "^5.9.3", "@mui/icons-material": "^5.8.4", "@mui/lab": "^5.0.0-alpha.91", "framer-motion": "^6.5.1", "date-fns": "^2.29.1", "react-spring": "^9.5.2", "react-use-gesture": "^9.1.3", "react-qr-code": "^2.0.7", "qrcode-reader": "^1.0.4", "react-camera-pro": "^1.4.0", "react-swipeable": "^7.0.0"}, "scripts": {"start": "react-scripts start", "start:game": "BROWSER=none react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^6.4.5"}, "homepage": "."}