<div class="app police">
	<img src="./img/lspdlogo.webp" class="logo" />
	<div class="tabs-bar">
		<div class="tabs-list">
			<div class="tab active" data-tab="1">
				<div class="tab-name" translate="Home">Home</div>
				<div class="tab-close"><i class="fas fa-times"></i></div>
			</div>
		</div>
		<div class="tab add">+</div>
	</div>
	<div class="tab-content-menu"></div>
	<div class="app-container police-home">
		<div class="scale-in">
			<div class="d-flex align-items-center justify-content-between">
				<h1 class="display-5 app-title me-2" style="width: 34vh" translate="PublicServices">
					Public Services
				</h1>
				<div class="polices-on-service">
					<img src="./img/polices-icon.png" />
					<div class="number-polices"></div>
				</div>
				<div class="text-uppercase service-tag text-end" style="width: 34vh" translate="OutDuty">
					OUT OF DUTY
				</div>
			</div>

			<div class="row mt-2">
				<div class="col-12 ps-3">
					<div class="right-buttons d-flex">
						<div class="secondary-box btn-police-central">
							<i class="lni lni-graph"></i>
							<div translate="Dispatch">DISPATCH</div>
						</div>
						<div class="secondary-box btn-police-citizen">
							<i class="fas fa-fingerprint"></i>
							<div translate="CitizenSearch" translate="CitizenSearch">CITIZEN SEARCH</div>
						</div>
						<div class="secondary-box btn-police-reports">
							<i class="lni lni-empty-file"></i>
							<div translate="Reports">REPORTS</div>
						</div>
						<div class="secondary-box btn-vehicles">
							<i class="lni lni-car-alt"></i>
							<div translate="Vehicles">VEHICLES</div>
						</div>
						<div class="secondary-box btn-codigopenal">
							<i class="fas fa-gavel"></i>
							<div translate="CriminalCode">CRIMINAL CODE</div>
						</div>
						<div class="secondary-box btn-byc">
							<img src="./img/icons/eRhjwVQ.png" />
							<div translate="SearchCapture">Search and Capture</div>
						</div>
						<div class="secondary-box btn-deudores">
							<img src="./img/icons/DT2idKV.png" />
							<div translate="Debtors">Debtors</div>
						</div>
						<div class="secondary-box btn-federal">
							<i class="fa-solid fa-handcuffs"></i>
							<div translate="FederalManagement">FEDERAL MANAGEMENT</div>
						</div>
						<div class="secondary-box btn-agentes">
							<img src="./img/icons/RAry0HS.png" />
							<div translate="AgentManagement">Agent Management</div>
						</div>
						<div class="secondary-box btn-camaras">
							<img src="./img/icons/8MVXb4V.png" />
							<div translate="SecurityCameras">Security Cameras</div>
						</div>
					</div>
					<div class="row p-0 pt-5">
						<div class="col-3"></div>
						<div class="col-6">
							<div class="row">
								<div class="col-6">
									<div class="radio-button d-flex align-items-center">
										<img
											src="img/knLlLkD.png"
											class="bg-radio-button" disableHueRotate="true" />
										<div class="bg-radio-gradient"></div>
										<div class="title">
											<img src="img/webp/walkie2.webp/" /> <div  translate="Radio">Radio</div>
										</div>
										<i class="fa-solid fa-walkie-talkie"></i>
									</div>
								</div>
								<div class="col-6">
									<div class="duty-button d-flex align-items-center">
										<img
											src="img/4nqiLBB.png"
											class="bg-radio-button" disableHueRotate="true" />
										<div class="bg-radio-gradient"></div>
										<div class="title">
											<img src="img/webp/duty.webp/" /> <div  translate="Duty">Duty</div>
										</div>
										<i class="fa-solid fa-walkie-talkie"></i>
									</div>
									<!-- <div class="btn btn-secondary w-100 duty-button">
										ENTER SERVICE
									</div> -->
								</div>
								<div class="col-12">
									<div class="other-place">
										<h3 class="bankgothic" translate="WelcomeTitle">
											Welcome to the police internal network!
										</h3>
										<p translate="WelcomeDesc">
											Welcome to the official application for police
											and sheriff. This application has been
											designed to help improve efficiency and
											communication in the daily work of police and
											sheriff officers.
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!-- <div class="other-place">
						<h5>Don't know what to put here</h5>
						<p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Deleniti mollitia, quae unde harum ea dignissimos ducimus vero quos optio, incidunt voluptatem rerum dolorum reprehenderit laborum, consequuntur ab libero? Dolore, velit.</p>
					</div> -->
					<div class="other-buttons"></div>
				</div>
			</div>
			<div class="botton-bar w-100 d-flex justify-content-start align-items-center">
				<div class="times-button btn-sound btn-action" style="display: flex;align-items: center;gap: .25vw;">
					<i class="fa-solid fa-business-time"></i>  <div translate="TimeControl">Time control</div>
				</div>
				<div class="operations-button btn-sound btn-action" style="display: flex;align-items: center;gap: .25vw; margin-left: 1vh;">
					<i class="fa-solid fa-draw-polygon"></i>  <div translate="Operations">Operations</div>
				</div>
				<div class="guide-button btn-sound btn-action" style="display: none;align-items: center;gap: .25vw; margin-left: auto;">
					<i class="fa-solid fa-book"></i>  <div translate="Guide">Guide of Use</div>
				</div>
				<div class="settings-button btn-sound btn-action" style="display: none;align-items: center;gap: .25vw; margin-left: 1vh;">
					<i class="fa-solid fa-gear"></i>  <div translate="Settings">Settings</div>
				</div>
			</div>
		</div>
	</div>

	<div class="app-container police-citizen">
		<!-- <div class="back-section text-white d-flex align-items-center"><i class="fas fa-chevron-left"></i> Volver</div> -->
		<div class="scale-in">
			<h1 class="display-5 app-title" translate="CitizenSearch">Citizen Search</h1>
			<div class="row mt-4">
				<div class="col-3">
					<div class="bg-box h-max">
						<div class="title-2 m-titles" translate="CitizenList">Citizen List</div>
						<div class="row m-titles search-box">
							<div class="col-10 p-0">
								<input
									type="text"
									placeholder="{{SearchCitizen}}"
									translate="placeholder"
									class="search-input w-100 input-search-citizen" />
							</div>
							<div class="col-2 p-0">
								<div class="btn btn-search btn-search-citizen w-100">
									<i class="fas fa-search"></i>
								</div>
							</div>
						</div>
						<div class="search-list citizen-list">
							<div class="citizen-item m-titles text-muted">
								<div class="citizen-name" translate="PerformSearch">
									Perform a search to display results
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-9">
					<div class="bg-box h-max">
						<div class="m-titles">
							<div class="title-2" translate="CitizenProfile">Citizen Profile</div>
							<div class="citizen-scroll citizen-ficha">
								<div
									class="d-flex w-100 align-items-center flex-column"
									style="height: 73vh">
									<h1 translate="SelectACitizen">Select a citizen to load their information</h1>
									<img src="./img/webp/finger.webp" />
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="app-container police-reports">
		<!-- <div class="back-section text-white d-flex align-items-center"><i class="fas fa-chevron-left"></i> Volver</div> -->
		<div class="scale-in informes">
			<h1
				class="display-5 app-title d-flex align-items-center justify-content-between">
				<div translate="Reports">Reports</div>
				<button class="btn-action add-informe" style="display: flex;align-items: center;gap: .25vw;">
					<i class="fas fa-plus"></i> <span translate="NewReport">New Report</span>
				</button>
			</h1>
			<div class="row mt-4">
				<div class="col-3">
					<div class="bg-box h-max">
						<div class="title-2 m-titles" translate="ReportList">Reports List</div>
						<div class="row m-titles search-box">
							<div class="col-10 p-0">
								<input
									type="text"
									translate="placeholder"
									placeholder="{{SearchReport}}"
									class="search-input w-100 input-search-report" />
							</div>
							<div class="col-2 p-0">
								<div class="btn btn-search btn-search-report w-100">
									<i class="fas fa-search"></i>
								</div>
							</div>
						</div>
						<div class="row m-titles">
							<div class="col-12 p-0">
								<select class="input select-tags-filter w-100">
									<option value="0" translate="AllTags">All tags</option>
								</select>
							</div>
						</div>
						<div class="search-list report-list"></div>
					</div>
				</div>
				<div class="col-9">
					<div class="bg-box h-max">
						<div class="m-titles">
							<div class="title-2" translate="Report">Report</div>
							<div class="citizen-scroll informe-report">
								<div
									class="d-flex w-100 align-items-center flex-column"
									style="height: 73vh">
									<h1 translate="SelectReport">Select a report to load its information</h1>
									<img src="./img/webp/document.webp" />
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="app-container police-settings">
		<!-- <div class="back-section text-white d-flex align-items-center"><i class="fas fa-chevron-left"></i> Volver</div> -->
		<div class="scale-in settings">
			<h1
				class="display-5 app-title d-flex align-items-center justify-content-between" style="border-radius: 10px 10px 0px 0px;">
				<div translate="Settings">Settings</div>
			</h1>
			<div class="settings-options">
				<div tab="station" class="setting-option active col-12" style="border-radius: 0px 0px 10px 10px;">
					<div class="d-flex align-items-center">
						<img src="./img/setting-icon.png" class="button-image p-3">
						<div class="button-title">POLICE STATIONS</div>
					</div>
				</div>
				<!-- <div tab="permissions" class="setting-option col-4" style="border-radius: 0px 0px 0px 0px;">
					<div class="d-flex align-items-center">
						<img src="./img/setting-icon.png" class="button-image p-3">
						<div class="button-title">AGENTS PERMISSIONS</div>
					</div>
				</div>
				<div tab="alerts" class="setting-option col-4" style="border-radius: 0px 0px 10px 0px;">
					<div class="d-flex align-items-center">
						<img src="./img/setting-icon.png" class="button-image p-3">
						<div class="button-title">ALERTS ZONES</div>
					</div>
				</div> -->
			</div>

			<div class="setting-container" tab="station">
				<div class="row mt-4">
					<div class="col-3" style="padding-left: 0;">
						<div class="setting-tab">
							<div class="mb-0 m-4 title-2 m-titles">STATIONS LIST</div>
							<div class="search-list station-list p-4">
								
								
							</div>
							<!-- <div class="add-button">
								<i class="fas fa-plus"></i>
							</div> -->
						</div>
					</div>
					<div class="col-9" style="padding-right: 0;">
						<div class="setting-tab" style="padding: 2vh;">
							<div class="m-titles">
								<div class="title-2">STATION DATA</div>
								<div class="ficha-station">
									<div id="station-data-placeholder" class="d-flex w-100 align-items-center flex-column" style="height: 73vh">
										<h1>SELECT A STATION TO LOAD INFORMATION</h1>
										<img src="./img/webp/finger.webp">
									</div>
									<div id="station-data" style="display: none;" class="row m-titles">
										<div class="col-6 p-0">
											<div class="info-box m-1 h-100">
												<div class="info-box-title">
													Name
												</div>
												<div class="info-box-value" id="station-name">
													LOADING...
												</div>
											</div>
										</div>
										<div class="col-6 p-0">
											<div class="info-box m-1 h-100">
												<div class="info-box-title">
													Label
												</div>
												<div class="info-box-value id-informe" id="station-label">
													LOADING...
												</div>
											</div>
										</div>
										<div class="col-3 p-0 mt-2">
											<div class="info-box m-1 h-100">
												<div class="info-box-title">
													Last Modified User
												</div>
												<div class="info-box-value id-informe" id="station-user">
													LOADING...
												</div>
											</div>
										</div>
										<div class="col-3 p-0 mt-2">
											<div class="info-box m-1 h-100">
												<div class="info-box-title">
													Last Modified Date
												</div>
												<div class="info-box-value id-informe" id="station-date">
													LOADING...
												</div>
											</div>
										</div>
										<div class="col-6 p-0 mt-2">
											<div class="info-box m-1 h-100">
												<div class="info-box-title">
													IS ACTIVE
												</div>
												<div class="info-box-value">
													<div class="btn-group mt-2 w-100" role="group" aria-label="Basic radio toggle button group">
														<input type="radio" class="btn-check si" id="station-active" autocomplete="off">
														<label class="btn btn-outline-primary" id="station-active-l">YES</label>

														<input type="radio" class="btn-check" id="station-unactive" autocomplete="off" checked="">
														<label class="btn btn-outline-primary no" id="station-unactive-l">NO</label>
													</div>
												</div>
											</div>
										</div>
										<div class="col-4 p-0 mt-2">
											<div class="info-box m-1 h-100">
												<div class="info-box-title" style="display: flex;position: relative;">
													Markers List
													<div class="new-button marker-list-info" style="position: absolute;right: 0;">
														<i class="fa-solid fa-circle-info"></i>
													</div>
												</div>
												<div class="citizen-info-container-mini mt-2" style="height: 35vh;">
													<ul class="list-group marker-list">

														
														
													</ul>
												</div>
											</div>
										</div>
										<div class="col-4 p-0 mt-2">
											<div class="info-box m-1 h-100">
												<div class="info-box-title">
													Current Markers
												</div>
												<div class="citizen-info-container-mini mt-2" style="height: 35vh;">
													<ul class="list-group current-marker-list">
	
														<li class="list-group-item list-group-item-action">
															<div class="d-flex justify-content-between align-items-center">
																<div class="d-flex align-items-center marker-option">
																	
																	<h5>Request Vehicle</h5>
																	
																	<div style="display: flex;right: 0;position: absolute;gap: 2vh;">
																		<div class="marker-btn" style="position: relative;">
																			<i class="fa-solid fa-street-view"></i>
																		</div>
																		<div class="marker-btn" style="position: relative;">
																			<i class="fa-solid fa-location-dot"></i>
																		</div>
																		<div class="marker-btn" style="position: relative;">
																			<i class="fa-solid fa-trash"></i>
																		</div>
																	</div>
																</div>
															</div>
														</li>
														
													</ul>
												</div>
											</div>
										</div>
										<div class="col-4 p-0 mt-2">
											<div class="info-box m-1 h-100">
												<div class="info-box-title" style="display: flex;position: relative;">
													Allowed Jobs
													<div class="new-button" onclick="settingsFunctions.addJobCatModal(this)" style="position: absolute;right: 0;">
														<i class="fas fa-plus"></i> ADD JOB CATEGORY
													</div>
												</div>
												<div class="citizen-info-container-mini mt-2" style="height: 35vh;">
													<ul class="list-group job-list">
                                   
														<li class="list-group-item list-group-item-action">
															<h5>Police Category</h5>
															<ul>
																<li><p>police</p></li>
																<li><p>sheriff</p></li>
															</ul>
															<div class="delete-button">
																<i class="fa-solid fa-trash"></i>
															</div>
														</li>
														
													</ul>
												</div>
											</div>
										</div>
										<div class="col-12 text-center">
											<button class="btn btn-danger mt-3 mb-2" onclick="settingsFunctions.deleteStation()">
												<i class="lni lni-trash-can"></i>
												DELETE STATION
											</button>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="setting-container" tab="permissions" style="display: none;">
				<div class="row mt-4">
					<div class="col-3" style="padding-left: 0;">
						<div class="setting-tab">
							<div class="mb-0 m-4 title-2 m-titles">PERMISSIONS</div>
							<div class="search-list station-list p-4">
								
								
							</div>
						</div>
					</div>
					<div class="col-9" style="padding-right: 0;">
						<div class="setting-tab"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="app-container police-vehicles">
		<!-- <div class="back-section text-white d-flex align-items-center"><i class="fas fa-chevron-left"></i> Volver</div> -->
		<div class="scale-in">
			<h1
				class="display-5 app-title d-flex align-items-center justify-content-between" translate="Vehicles">
				Vehicles
			</h1>
			<div class="row mt-4">
				<div class="col-3">
					<div class="bg-box h-max">
						<div class="title-2 m-titles" translate="VehicleList">Vehicle list</div>
						<div class="row m-titles search-box">
							<div class="col-10 p-0">
								<input
									type="text"
									translate="placeholder"
									placeholder="{{TypeLicense}}"
									class="search-input w-100 input-search-vehicles" />
							</div>
							<div class="col-2 p-0">
								<div class="btn btn-search btn-search-vehicle w-100">
									<i class="fas fa-search"></i>
								</div>
							</div>
						</div>
						<div class="search-list vehicle-list">
							<div class="citizen-item m-titles text-muted">
								<div class="citizen-name" translate="PerformSearchVehicle">
									Perform a search to display results
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-9">
					<div class="bg-box h-max">
						<div class="m-titles">
							<div class="title-2" translate="VehicleData">Vehicle data</div>
							<div
								class="citizen-scroll ficha-vehiculo"
								id="vehicle-4234"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="app-container police-codigopenal">
		<!-- <div class="back-section text-white d-flex align-items-center"><i class="fas fa-chevron-left"></i> Volver</div> -->
		<div class="scale-in app-codigo-penal">
			<h1
				class="display-5 app-title d-flex align-items-center justify-content-between">
				<div translate="CriminalCode">Criminal Code</div>
				<div class="d-flex justify-content-end">
					<button class="btn-action add-capitulo me-2 d-none" style="display: flex;align-items: center;gap: .25vw;">
						<i class="fas fa-plus"></i> <span translate="NewChapter">New Chapter</span>
					</button>
					<button class="btn-action add-articulo d-none" style="display: flex;align-items: center;gap: .25vw;">
						<i class="fas fa-plus"></i> <span translate="NewArticle">New article</span>
					</button>
				</div>
			</h1>
			<div class="row mt-4">
				<div class="col-12">
					<div class="bg-box">
						<input
							type="text"
							class="input w-100 search-cp"
							translate="placeholder"
							placeholder="{{SearchCriminalCode}}" />
						<div class="scroll-codigopenal">
							<table border="0" class="tabla-codigo-penal info-box gtable">
								<thead>
									<tr>
										<th class="h-articulo" translate="Article">Article</th>
										<th class="h-descripcion" translate="Description">Description</th>
										<th class="h-importe text-center" translate="Amount">Amount</th>
										<th class="h-pena text-center" translate="Sentence">Sentence</th>
										<th class="h-accion text-center" translate="Action">Action</th>
									</tr>
								</thead>
								<tbody></tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="app-container police-radio">
		<!-- <div class="back-section text-white d-flex align-items-center"><i class="fas fa-chevron-left"></i> Volver</div> -->
		<div class="scale-in radio">
			<h1
				class="display-5 app-title d-flex align-items-center justify-content-between">
				<div translate="InternalRadio">Internal Radio</div>
				<div class="text-center zona-conectar">
					<p translate="ConnectedTo">CONNECTED TO</p>
					<div class="badge connected">
						<div class="frecuencia-actual">ADAM-10</div>
						<div class="desconectar" translate="Disconnect">Disconnect</div>
					</div>
				</div>
				<div class="text-end">
					<button class="btn-action btn-teclas" style="display: flex;align-items: center;gap: .25vw;float: right;position: relative;right: .5vw;">
						<i class="fas fa-plus"></i> <span translate="ShortCuts">SHORTCUTS</span>
					</button>
				</div>
			</h1>
			<div class="row mt-4" id="radioContainerFreqs">
				
			</div>
		</div>
	</div>
	<div class="app-container police-central">
		<!-- <div class="back-section text-white d-flex align-items-center"><i class="fas fa-chevron-left"></i> Volver</div> -->
		<div class="scale-in central">
			<div class="centra-container">
				<div class="row">
					<div class="col-4">
						<div class="info-box" style="height: 35vh">
							<h4 translate="AgentsOnDuty">Agents on Duty</h4>
							<div class="table-agentes-container">
								<table class="ctable agentes-servicio">
									<thead>
										<tr>
											<th style="width: 10%" translate="DeptAbrev">Dept.</th>
											<th style="width: 25%" translate="Rank">Rank</th>
											<th style="width: 35%" translate="Agent">Agent</th>
											<th style="width: 20%" translate="Status">Status</th>
											<th style="width: 5%" translate="LocAbrev">Loc.</th>
										</tr>
									</thead>
									<tbody>
										
									</tbody>
								</table>
							</div>
						</div>

						<div class="info-box mt-2">
							<div
								class="d-flex justify-content-between align-items-center">
								<h4 translate="Radio">RADIO</h4>
								<div class="actions-title-buttons d-flex">
									<div
										action="SAFD"
										class="broadcast d-flex align-items-center me-2">
										<i class="fa-solid fa-walkie-talkie me-1"></i>
										<div translate="BroadcastSAFD">BROADCAST SAFD</div>
									</div>
									<div
										action="SAPD"
										class="broadcast d-flex align-items-center">
										<i class="fa-solid fa-walkie-talkie me-1"></i>
										<div translate="BroadcastSapd">BROADCAST SAPD</div>
									</div>
								</div>
							</div>
							<div class="central-freq"></div>
						</div>
					</div>
					<div class="col-4">
						<div class="central-acceso-rapido">
							<div
								class="central-access-button action-button w-100"
								action="wanted">
								<img src="img/icons/XwS0i1Y.png" />
								<div translate="CitizenSearch" translate="CitizenSearch">CITIZEN SEARCH</div>
							</div>
							<div
								class="central-access-button action-button w-100 me-2 ms-2"
								action="informes">
								<img src="img/icons/3C0bwtB.png" />
								<div class="w-100" translate="Reports">REPORTS</div>
							</div>
							<div
								class="central-access-button action-button w-100"
								action="cameras">
								<img src="img/icons/2YUwATY.png" />
								<div translate="SecurityCameras">SECURITY CAMERAS</div>
							</div>
						</div>
						<div class="zona-mapa" disablehuerotate>
							<div id="mapCentral" class="map"></div>
							<div class="info-mapa">
								<div class="close-button">
									<i class="fa-solid fa-xmark"></i>
								</div>
								<div class="d-flex">
									<img
										src="img/webp/alert.webp"
										style="
											width: 9vh;
											transform: translate(0vh, -1.1vh);
										" />
									<div class="info-data">
										<div class="info-title"></div>
										<!-- <div class="info-message"></div> -->
										<div class="info-metadata">
											<div class="id-label">
												<i class="lni lni-list text-warning"></i>
												<span class="label">#125</span>
											</div>
											<div class="location-label">
												<i
													class="lni lni-map-marker text-warning"></i>
												<span class="location"
													>Spanish Avenue</span
												>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="info-box" style="height: 25.6vh; margin-top: 1vh">
							<h4 translate="InternalRadio">Internal Radio</h4>
							<div class="chat-messages"></div>
							<div class="chat-input">
								<input
									type="text"
									placeholder="{{TypeMessage}}"
									class="form-control input-rpol" translate="placeholder"/>
							</div>
						</div>
					</div>
					<div class="col-4">
						<div class="info-box">
							<div
								class="d-flex emergencias justify-content-between align-items-center">
								<h4 translate="Emergencies">EMERGENCIES</h4>
								<div class="actions-title">
									<div class="delete-alert d-flex align-items-center">
										<i class="lni lni-trash-can me-1"></i>
										<div translate="DeleteAlert">Delete Alert</div>
									</div>
								</div>
							</div>
							<div class="alerts-container">
								<div class="row">
									<div class="col-2">
										<div translate="Notice">NOTICE</div>
										<div class="box-info text-center id-alert">-</div>
									</div>
									<div class="col-10">
										<div translate="Title">TITLE</div>
										<div class="box-info title-alert">-</div>
									</div>
									<div class="col-6">
										<div translate="Location">LOCATION</div>
										<div class="box-info text-center street-alert">
											-
										</div>
									</div>
									<div class="col-6">
										<div translate="Time">TIME</div>
										<div class="box-info text-center time-alert">
											-
										</div>
									</div>
									<div class="col-12">
										<div translate="DetailedDesc">DETAILED DESCRIPTION</div>
										<div class="box-info">
											<div
												class="box-scroll-emergencias message-alert">
												-
											</div>
										</div>
									</div>
									<div class="col-12">
										<div translate="Notes">NOTES</div>
										<div class="box-info">
											<textarea
												disabled
												class="box-scroll-emergencias input-note-alert"
												translate="placeholder"
												placeholder="{{AddNoteToEmergency}}"></textarea>
										</div>
										<button
											class="btn-action-mini w-100 btn-save-note-alert" translate="SaveNote">
											Save Note
										</button>
									</div>
								</div>
								<div class="selector-container">
									<div class="selector-title" translate="SendToUnit">SEND TO UNIT</div>
									<div class="unidad-selector">
										<div translate="AvailableUnits">Available Units</div>
										<div class="unidades-dispo"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="info-box" style="height: 33.7vh; margin-top: 1vh">
							<h4 translate="LastAlerts">Last Alerts</h4>
							<div class="table-alerts-container">
								<table class="ctable tabla-dispatch">
									<thead>
										<tr>
											<th style="width: 5%" translate="RefAbrev">REF.</th>
											<th style="width: 15%" translate="Emergency">EMERGENCY</th>
											<th style="width: 30%" translate="Location">Location</th>
											<th style="width: 15%" translate="Ago">AGO</th>
											<th style="width: 10%" translate="Units">UNITS</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td
												colspan="5"
												class="text-muted text-center no-alerts" translate="NoRecived">
												No alerts received.
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="app-container police-manage">
		<!-- <div class="back-section text-white d-flex align-items-center"><i class="fas fa-chevron-left"></i> Volver</div> -->
		<div class="scale-in agentes">
			<h1
				class="display-5 app-title d-flex align-items-center justify-content-between">
				<div translate="PoliceManagement">Police management</div>
				<div class="d-flex gap-2 w-auto">
					<button class="btn-action generar-placa" style="display: flex;align-items: center;gap: .25vw;">
						<i class="fa-solid fa-id-badge"></i> <span translate="GenerateBadge">Generate Badge</span>
					</button>
					<button class="btn-action add-agente" style="display: flex;align-items: center;gap: .25vw;">
						<i class="fas fa-plus"></i> <span translate="AddPolice">Add Police</span>
					</button>
				</div>
			</h1>
			<div class="row mt-4">
				<div class="col-3">
					<div class="bg-box h-max">
						<div class="search-list agent-list"></div>
					</div>
				</div>
				<div class="col-9">
					<div class="bg-box h-max">
						<div class="m-titles">
							<div class="title-2" translate="PoliceFile">Police File</div>
							<div class="citizen-scroll agent-ficha">
								<div
									class="d-flex w-100 align-items-center flex-column"
									style="height: 73vh">
									<h1 translate="SelectAnAgent">Select an agent to upload their information</h1>
									<img src="./img/webp/finger.webp" />
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="app-container police-cameras">
		<!-- <div class="back-section text-white d-flex align-items-center"><i class="fas fa-chevron-left"></i> Volver</div> -->
		<div class="scale-in cameras">
			<h1
				class="display-5 app-title d-flex align-items-center justify-content-between">
				<div translate="SecurityCameras">Security Cameras</div>
				<button class="btn-action refresh-cameras" style="display: flex;align-items: center;gap: .25vw;">
					<i class="fas fa-sync-alt"></i> <span translate="Refresh">Refresh</span>
				</button>
			</h1>
			<div class="row mt-4">
				<div class="col-4">
					<div class="bg-box h-max">
						<div class="title-2 m-titles" translate="BusinessCameras">Business Cameras</div>
						<div class="camera-list business"></div>
					</div>
				</div>
				<div class="col-4">
					<div class="bg-box h-max">
						<div class="title-2 m-titles" translate="VehicleCameras">Vehicle Cameras</div>
						<div class="camera-list vehicles">
							<div class="camera">
								<img src="img/icons/46IfeYQ.png" />
								<div class="camera-info">
									<div class="camera-title">Badulake</div>
									<div class="camera-owner">Business</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-4">
					<div class="bg-box h-max">
						<div class="title-2 m-titles" translate="BodyCam">Bodycam Cameras</div>
						<div class="camera-list bodycam"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="app-container police-operations">
		<div class="scale-in operations">
			<h1 class="display-5 app-title" translate="Operations">Operations</h1>
			<div class="row mt-4">
				<div class="col-9">
					<div class="zona-mapa h-max">
						<div id="mapShapes" disablehuerotate class="s-map"></div>
					</div>
				</div>
				<div class="col-3">
					<div class="bg-box h-max">
						<div class="title-2 m-titles" translate="ShapesCreated">Shapes created</div>
						<div class="shape-list mt-3">
							<div class="operation-item m-titles text-muted text-center mb-3">
								<div class="no-operations" translate="NoShapes">
									No shapes created
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="multas-container">
		<div class="multas-bg">
			<div class="multas-box scale-in" data-name="" data-id="">
				<div class="close-button">
					<i class="fas fa-times"></i>
				</div>
				<h1 class="display-5 app-title" translate="AddFine">Add a fine</h1>
				<div class="row">
					<div class="col-8">
						<h4 class="title-1-menu mt-3" translate="CriminalCode">Criminal Code</h4>
						<input
							type="text"
							class="input w-100 search-cp-multas"
							translate="placeholder"
							placeholder="{{SearchCriminalCode}}" />
						<div class="container-cp-multas">
							<table border="0" class="tabla-codigo-penal-multas gtable">
								<thead>
									<tr>
										<th class="h-articulo" translate="Article">Article</th>
										<th class="h-descripcion" translate="Description">Description</th>
										<th class="h-importe text-center" translate="Amount">Amount</th>
										<th class="h-pena text-center" translate="Sentence">Sentence</th>
										<th class="h-accion text-center" translate="Action">Action</th>
									</tr>
								</thead>
								<tbody></tbody>
							</table>
						</div>
						<h4 class="title-1-menu mt-3" translate="CustomFine">Custom fine</h4>
						<div class="custom-multa d-flex">
							<div class="concepto">
								<h5 class="title-2" translate="Concept">Concept</h5>
								<input
									type="text"
									class="input w-100 input-concepto"
									maxlength="150"
									translate="placeholder"
									placeholder="{{EnterConcept}}" />
							</div>
							<div class="importe">
								<h5 class="title-2" translate="Amount">Amount</h5>
								<input
									type="number"
									min="0"
									class="input w-100 input-importe"
									translate="placeholder"
									placeholder="{{EnterAmount}}" />
							</div>
							<div class="pena">
								<h5 class="title-2" translate="Sentence">Sentence</h5>
								<input
									type="number"
									class="input w-100 input-meses"
									translate="placeholder"
									placeholder="{{EnterSentence}}" />
							</div>
							<div class="agregar d-flex">
								<button class="btn btn-search btn-add-custom-art" translate="Add">
									Agregar
								</button>
							</div>
						</div>
					</div>
					<div class="col-4">
						<h4 class="title-1-menu mt-3" translate="More">More</h4>
						<div class="bg-box zona-poner-multas">
							<h5 translate="FineConcepts">Fine concepts</h5>
							<ul class="list-group lista-articulos-multa"></ul>
						</div>
						<h4 class="title-1-menu mt-3" translate="ProcessFine">Process fine</h4>
						<div class="finalizar-multa d-flex">
							<div class="pena-total">
								<div class="info-box-title" translate="TotalSentence">Total sentence</div>
								<h4 class="total-meses" translate="Default0Months">0 months</h4>
							</div>
							<div class="importe-total">
								<div class="info-box-title" translate="TotalAmount">Total Amount</div>
								<h4 class="total-importe text-danger fw-bold">0$</h4>
							</div>
							<button class="btn btn-secondary w-100 btn-save-multa" translate="ConfirmFine">
								Confirm the fine
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="personas-container">
		<div class="multas-bg">
			<div class="personas-box scale-in">
				<div class="close-button">
					<i class="fas fa-times"></i>
				</div>
				<h1 class="display-5 app-title" translate="AddFine">Select a citizen</h1>
				<div class="row m-titles search-box">
					<div class="col-10 p-0">
						<input
							type="text"
							translate="placeholder"
							placeholder="{{FindACitizen}}"
							class="search-input w-100 input-search-citizen-selector" />
					</div>
					<div class="col-2 p-0">
						<div class="btn btn-search btn-search-citizen-selector w-100">
							<i class="fas fa-search"></i>
						</div>
					</div>
				</div>
				<div class="citizen-box-list">
					<div class="row m-titles">
						<div class="col-12 text-muted">
							<h4 class="citizen-name" translate="AddFine">
								Introduces a name to the search engine to show the results
							</h4>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="vehiculos-container">
		<div class="multas-bg">
			<div class="personas-box scale-in">
				<div class="close-button">
					<i class="fas fa-times"></i>
				</div>
				<h1 class="display-5 app-title" translate="SelectVehicle">Select a vehicle</h1>
				<div class="row m-titles search-box">
					<div class="col-10 p-0">
						<input
							type="text"
							translate="placeholder"
							placeholder="{{FindVehicles}}"
							class="search-input w-100 input-search-vehicle-selector" />
					</div>
					<div class="col-2 p-0">
						<div class="btn btn-search btn-search-vehicle-selector w-100">
							<i class="fas fa-search"></i>
						</div>
					</div>
				</div>
				<div class="vehicles-box-list">
					<div class="row m-titles">
						<div class="col-12 text-muted">
							<h4 class="citizen-name" translate="EnterNameEngine">
								Introduces a name to the search engine to show the results
							</h4>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="evidencias-container">
		<div class="multas-bg">
			<div class="personas-box scale-in">
				<div class="close-button">
					<i class="fas fa-times"></i>
				</div>
				<h1 class="display-5 app-title" translate="SelectEvidence">Select evidence</h1>

				<div class="evidencias-box-list">
					<div class="row m-titles">
						<div class="col-12 text-muted">
							<h4 class="citizen-name" translate="NoEvidenceInv">
								There is no evidence in your inventory
							</h4>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="informe-view w-100 h-100">
		<div class="d-flex align-items-center justify-content-center scale-in h-100">
			<img src="img/gDMmmRQ.png" />
		</div>
	</div>
	<div class="duty-alert w-100 h-100">
		<div class="d-flex align-items-center justify-content-center w-100 h-100">
			<div class="animate__animated animate__shakeX" translate="NotInDuty"></div>
		</div>
	</div>
</div>
