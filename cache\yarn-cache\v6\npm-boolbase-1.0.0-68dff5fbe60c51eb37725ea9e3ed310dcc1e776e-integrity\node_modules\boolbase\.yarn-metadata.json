{"manifest": {"name": "boolbase", "version": "1.0.0", "description": "two functions: One that returns true, one that returns false", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/fb55/boolbase"}, "keywords": ["boolean", "function"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/fb55/boolbase/issues"}, "homepage": "https://github.com/fb55/boolbase", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-boolbase-1.0.0-68dff5fbe60c51eb37725ea9e3ed310dcc1e776e-integrity\\node_modules\\boolbase\\package.json", "readmeFilename": "README.md", "readme": "#boolbase\nThis very simple module provides two basic functions, one that always returns true (`trueFunc`) and one that always returns false (`falseFunc`).\n\n###WTF?\n\nBy having only a single instance of these functions around, it's possible to do some nice optimizations. Eg. [`CSSselect`](https://github.com/fb55/CSSselect) uses these functions to determine whether a selector won't match any elements. If that's the case, the DOM doesn't even have to be touched.\n\n###And why is this a separate module?\n\nI'm trying to modularize `CSSselect` and most modules depend on these functions. IMHO, having a separate module is the easiest solution to this problem."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/boolbase/-/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e", "type": "tarball", "reference": "https://registry.yarnpkg.com/boolbase/-/boolbase-1.0.0.tgz", "hash": "68dff5fbe60c51eb37725ea9e3ed310dcc1e776e", "integrity": "sha1-aN/1++YMUes3cl6p4+0xDcwed24=", "registry": "npm", "packageName": "boolbase", "cacheIntegrity": "sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww== sha1-aN/1++YMUes3cl6p4+0xDcwed24="}, "registry": "npm", "hash": "68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"}