{"manifest": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "name": "crypto-browserify", "description": "implementation of crypto for the browser", "version": "3.12.0", "homepage": "https://github.com/crypto-browserify/crypto-browserify", "repository": {"type": "git", "url": "git://github.com/crypto-browserify/crypto-browserify.git"}, "scripts": {"standard": "standard", "test": "npm run standard && npm run unit", "unit": "node test/", "browser": "zuul --browser-version $BROWSER_VERSION --browser-name $BROWSER_NAME -- test/index.js"}, "engines": {"node": "*"}, "dependencies": {"browserify-cipher": "^1.0.0", "browserify-sign": "^4.0.0", "create-ecdh": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.0", "diffie-hellman": "^5.0.0", "inherits": "^2.0.1", "pbkdf2": "^3.0.3", "public-encrypt": "^4.0.0", "randombytes": "^2.0.0", "randomfill": "^1.0.3"}, "devDependencies": {"hash-test-vectors": "~1.3.2", "pseudorandombytes": "^2.0.0", "safe-buffer": "^5.1.1", "standard": "^5.0.2", "tape": "~2.3.2", "zuul": "^3.6.0"}, "optionalDependencies": {}, "browser": {"crypto": false}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-crypto-browserify-3.12.0-396cf9f3137f03e4b8e532c58f698254e00f80ec-integrity\\node_modules\\crypto-browserify\\package.json", "readmeFilename": "README.md", "readme": "# crypto-browserify\n\nA port of node's `crypto` module to the browser.\n\n[![Build Status](https://travis-ci.org/crypto-browserify/crypto-browserify.svg?branch=master)](https://travis-ci.org/crypto-browserify/crypto-browserify)\n[![js-standard-style](https://cdn.rawgit.com/feross/standard/master/badge.svg)](https://github.com/feross/standard)\n[![Sauce Test Status](https://saucelabs.com/browser-matrix/crypto-browserify.svg)](https://saucelabs.com/u/crypto-browserify)\n\nThe goal of this module is to reimplement node's crypto module,\nin pure javascript so that it can run in the browser.\n\nHere is the subset that is currently implemented:\n\n* createHash (sha1, sha224, sha256, sha384, sha512, md5, rmd160)\n* createHmac (sha1, sha224, sha256, sha384, sha512, md5, rmd160)\n* pbkdf2\n* pbkdf2Sync\n* randomBytes\n* pseudoRandomBytes\n* createCipher (aes)\n* createDecipher (aes)\n* createDiffieHellman\n* createSign (rsa, ecdsa)\n* createVerify (rsa, ecdsa)\n* createECDH (secp256k1)\n* publicEncrypt/privateDecrypt (rsa)\n* privateEncrypt/publicDecrypt (rsa)\n\n## todo\n\nthese features from node's `crypto` are still unimplemented.\n\n* createCredentials\n\n## contributions\n\nIf you are interested in writing a feature, please implement as a new module,\nwhich will be incorporated into crypto-browserify as a dependency.\n\nAll deps must be compatible with node's crypto\n(generate example inputs and outputs with node,\nand save base64 strings inside JSON, so that tests can run in the browser.\nsee [sha.js](https://github.com/dominictarr/sha.js)\n\nCrypto is _extra serious_ so please do not hesitate to review the code,\nand post comments if you do.\n\n## License\n\nMIT\n", "licenseText": "The MIT License\n\nCopyright (c) 2013 Dominic <PERSON>\n\nPermission is hereby granted, free of charge, \nto any person obtaining a copy of this software and \nassociated documentation files (the \"Software\"), to \ndeal in the Software without restriction, including \nwithout limitation the rights to use, copy, modify, \nmerge, publish, distribute, sublicense, and/or sell \ncopies of the Software, and to permit persons to whom \nthe Software is furnished to do so, \nsubject to the following conditions:\n\nThe above copyright notice and this permission notice \nshall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, \nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES \nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. \nIN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR \nANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, \nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE \nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/crypto-browserify/-/crypto-browserify-3.12.0.tgz#396cf9f3137f03e4b8e532c58f698254e00f80ec", "type": "tarball", "reference": "https://registry.yarnpkg.com/crypto-browserify/-/crypto-browserify-3.12.0.tgz", "hash": "396cf9f3137f03e4b8e532c58f698254e00f80ec", "integrity": "sha512-fz4spIh+znjO2VjL+IdhEpRJ3YN6sMzITSBijk6FK2UvTqruSQW+/cCZTSNsMiZNvUeq0CqurF+dAbyiGOY6Wg==", "registry": "npm", "packageName": "crypto-browserify", "cacheIntegrity": "sha512-fz4spIh+znjO2VjL+IdhEpRJ3YN6sMzITSBijk6FK2UvTqruSQW+/cCZTSNsMiZNvUeq0CqurF+dAbyiGOY6Wg== sha1-OWz58xN/A+S45TLFj2mCVOAPgOw="}, "registry": "npm", "hash": "396cf9f3137f03e4b8e532c58f698254e00f80ec"}