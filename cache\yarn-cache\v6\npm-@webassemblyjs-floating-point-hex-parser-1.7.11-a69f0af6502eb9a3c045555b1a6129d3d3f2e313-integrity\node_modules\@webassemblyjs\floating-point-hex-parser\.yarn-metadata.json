{"manifest": {"name": "@webassemblyjs/floating-point-hex-parser", "scripts": {"build-fuzzer": "[ -f ./test/fuzzing/parse.out ] || gcc ./test/fuzzing/parse.c -o ./test/fuzzing/parse.out -lm -Wall"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "version": "1.7.11", "description": "A function to parse floating point hexadecimal strings as defined by the WebAssembly specification", "main": "lib/index.js", "module": "esm/index.js", "keywords": ["webassembly", "floating-point"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@webassemblyjs-floating-point-hex-parser-1.7.11-a69f0af6502eb9a3c045555b1a6129d3d3f2e313-integrity\\node_modules\\@webassemblyjs\\floating-point-hex-parser\\package.json", "readmeFilename": "README.md", "readme": "# Parser function for floating point hexadecimals\n\n[![license](https://img.shields.io/github/license/maurobringolf/@webassemblyjs/floating-point-hex-parser.svg)]()\n[![GitHub last commit](https://img.shields.io/github/last-commit/maurobringolf/@webassemblyjs/floating-point-hex-parser.svg)]()\n[![npm](https://img.shields.io/npm/v/@webassemblyjs/floating-point-hex-parser.svg)]()\n\n> A JavaScript function to parse floating point hexadecimals as defined by the [WebAssembly specification](https://webassembly.github.io/spec/core/text/values.html#text-hexfloat).\n\n## Usage\n\n```javascript\nimport parseHexFloat from '@webassemblyjs/floating-point-hex-parser'\n\nparseHexFloat('0x1p-1')               // 0.5\nparseHexFloat('0x1.921fb54442d18p+2') // 6.283185307179586\n```\n\n## Tests\n\nThis module is tested in two ways. The first one is through a small set of test cases that can be found in [test/regular.test.js](https://github.com/maurobringolf/@webassemblyjs/floating-point-hex-parser/blob/master/test/regular.test.js). The second one is non-deterministic (sometimes called *fuzzing*):\n\n1. Generate a random IEEE754 double precision value `x`.\n1. Compute its representation `y` in floating point hexadecimal format using the C standard library function `printf` since C supports this format.\n1. Give both values to JS testcase and see if `parseHexFloat(y) === x`.\n\nBy default one `npm test` run tests 100 random samples. If you want to do more, you can set the environment variable `FUZZ_AMOUNT` to whatever number of runs you'd like. Because it uses one child process for each sample, it is really slow though. For more details about the randomized tests see [the source](https://github.com/maurobringolf/@webassemblyjs/floating-point-hex-parser/tree/master/test/fuzzing).\n\n## Links\n\n* [maurobringolf.ch/2017/12/hexadecimal-floating-point-notation/](https://maurobringolf.ch/2017/12/hexadecimal-floating-point-notation/)\n\n* [github.com/xtuc/js-webassembly-interpreter/issues/32](https://github.com/xtuc/js-webassembly-interpreter/issues/32)\n\n* [github.com/WebAssembly/design/issues/292](https://github.com/WebAssembly/design/issues/292)\n", "licenseText": "MIT License\n\nCopyright (c) 2017 Mauro <PERSON>olf\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.7.11.tgz#a69f0af6502eb9a3c045555b1a6129d3d3f2e313", "type": "tarball", "reference": "https://registry.yarnpkg.com/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.7.11.tgz", "hash": "a69f0af6502eb9a3c045555b1a6129d3d3f2e313", "integrity": "sha512-zY8dSNyYcgzNRNT666/zOoAyImshm3ycKdoLsyDw/Bwo6+/uktb7p4xyApuef1dwEBo/U/SYQzbGBvV+nru2Xg==", "registry": "npm", "packageName": "@webassemblyjs/floating-point-hex-parser", "cacheIntegrity": "sha512-zY8dSNyYcgzNRNT666/zOoAyImshm3ycKdoLsyDw/Bwo6+/uktb7p4xyApuef1dwEBo/U/SYQzbGBvV+nru2Xg== sha1-pp8K9lAuuaPARVVbGmEp09Py4xM="}, "registry": "npm", "hash": "a69f0af6502eb9a3c045555b1a6129d3d3f2e313"}