{"manifest": {"name": "setimmediate", "description": "A shim for the setImmediate efficient script yielding API", "version": "1.0.5", "author": {"name": "YuzuJS"}, "contributors": [{"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me"}, {"name": "Donavon West", "email": "<EMAIL>", "url": "http://donavon.com"}, {"name": "Yaffle"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/YuzuJS/setImmediate.git"}, "main": "setImmediate.js", "files": ["setImmediate.js"], "scripts": {"lint": "jshint setImmediate.js", "test": "mocha test/tests.js", "test-browser": "opener http://localhost:9008/__zuul && zuul test/tests.js --ui mocha-bdd --local 9008", "test-browser-only": "opener http://localhost:9007/test/browserOnly/index.html && http-server . -p 9007"}, "devDependencies": {"jshint": "^2.5.0", "mocha": "~1.18.2", "http-server": "~0.6.1", "opener": "^1.3", "zuul": "^1.6.4"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-setimmediate-1.0.5-290cbb232e306942d7d7ea9b83732ab7856f8285-integrity\\node_modules\\setimmediate\\package.json", "licenseText": "Copyright (c) 2012 Barnesandnoble.com, llc, Donavon West, and Domenic Denicola\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/setimmediate/-/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285", "type": "tarball", "reference": "https://registry.yarnpkg.com/setimmediate/-/setimmediate-1.0.5.tgz", "hash": "290cbb232e306942d7d7ea9b83732ab7856f8285", "integrity": "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=", "registry": "npm", "packageName": "setimmediate", "cacheIntegrity": "sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA== sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU="}, "registry": "npm", "hash": "290cbb232e306942d7d7ea9b83732ab7856f8285"}