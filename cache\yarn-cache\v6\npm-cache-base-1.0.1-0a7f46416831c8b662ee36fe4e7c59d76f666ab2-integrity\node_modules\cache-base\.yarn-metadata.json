{"manifest": {"name": "cache-base", "description": "Basic object cache with `get`, `set`, `del`, and `has` methods for node.js/javascript projects.", "version": "1.0.1", "homepage": "https://github.com/jonschlinkert/cache-base", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/cache-base.git"}, "bugs": {"url": "https://github.com/jonschlinkert/cache-base/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.4.2"}, "keywords": ["base", "cache", "config", "data", "get", "has", "hash", "hasown", "object", "set", "store"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"highligh": "base", "list": ["base-methods", "get-value", "has-value", "option-cache", "set-value", "unset-value"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-cache-base-1.0.1-0a7f46416831c8b662ee36fe4e7c59d76f666ab2-integrity\\node_modules\\cache-base\\package.json", "readmeFilename": "README.md", "readme": "# cache-base [![NPM version](https://img.shields.io/npm/v/cache-base.svg?style=flat)](https://www.npmjs.com/package/cache-base) [![NPM monthly downloads](https://img.shields.io/npm/dm/cache-base.svg?style=flat)](https://npmjs.org/package/cache-base) [![NPM total downloads](https://img.shields.io/npm/dt/cache-base.svg?style=flat)](https://npmjs.org/package/cache-base) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/cache-base.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/cache-base)\n\n> Basic object cache with `get`, `set`, `del`, and `has` methods for node.js/javascript projects.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save cache-base\n```\n\n## Usage\n\n```js\nvar Cache = require('cache-base');\n\n// instantiate\nvar app = new Cache();\n\n// set values\napp.set('a', 'b');\napp.set('c.d', 'e');\n\n// get values\napp.get('a');\n//=> 'b'\napp.get('c');\n//=> {d: 'e'}\n\nconsole.log(app.cache);\n//=> {a: 'b'}\n```\n\n**Inherit**\n\n```js\nvar util = require('util');\nvar Cache = require('cache-base');\n\nfunction MyApp() {\n  Cache.call(this);\n}\nutil.inherits(MyApp, Cache);\n\nvar app = new MyApp();\napp.set('a', 'b');\napp.get('a');\n//=> 'b'\n```\n\n**Namespace**\n\nDefine a custom property for storing values.\n\n```js\nvar Cache = require('cache-base').namespace('data');\nvar app = new Cache();\napp.set('a', 'b');\nconsole.log(app.data);\n//=> {a: 'b'}\n```\n\n## API\n\n### [namespace](index.js#L29)\n\nCreate a `Cache` constructor that when instantiated will store values on the given `prop`.\n\n**Params**\n\n* `prop` **{String}**: The property name to use for storing values.\n* `returns` **{Function}**: Returns a custom `Cache` constructor\n\n**Example**\n\n```js\nvar Cache = require('cache-base').namespace('data');\nvar cache = new Cache();\n\ncache.set('foo', 'bar');\n//=> {data: {foo: 'bar'}}\n```\n\n### [Cache](index.js#L43)\n\nCreate a new `Cache`. Internally the `Cache` constructor is created using the `namespace` function, with `cache` defined as the storage object.\n\n**Params**\n\n* `cache` **{Object}**: Optionally pass an object to initialize with.\n\n**Example**\n\n```js\nvar app = new Cache();\n```\n\n### [.set](index.js#L84)\n\nAssign `value` to `key`. Also emits `set` with the key and value.\n\n**Params**\n\n* `key` **{String}**\n* `value` **{any}**\n* `returns` **{Object}**: Returns the instance for chaining.\n\n**Events**\n\n* `emits`: `set` with `key` and `value` as arguments.\n\n**Example**\n\n```js\napp.on('set', function(key, val) {\n  // do something when `set` is emitted\n});\n\napp.set(key, value);\n\n// also takes an object or array\napp.set({name: 'Halle'});\napp.set([{foo: 'bar'}, {baz: 'quux'}]);\nconsole.log(app);\n//=> {name: 'Halle', foo: 'bar', baz: 'quux'}\n```\n\n### [.union](index.js#L114)\n\nUnion `array` to `key`. Also emits `set` with the key and value.\n\n**Params**\n\n* `key` **{String}**\n* `value` **{any}**\n* `returns` **{Object}**: Returns the instance for chaining.\n\n**Example**\n\n```js\napp.union('a.b', ['foo']);\napp.union('a.b', ['bar']);\nconsole.log(app.get('a'));\n//=> {b: ['foo', 'bar']}\n```\n\n### [.get](index.js#L144)\n\nReturn the value of `key`. Dot notation may be used to get [nested property values](https://github.com/jonschlinkert/get-value).\n\n**Params**\n\n* `key` **{String}**: The name of the property to get. Dot-notation may be used.\n* `returns` **{any}**: Returns the value of `key`\n\n**Events**\n\n* `emits`: `get` with `key` and `value` as arguments.\n\n**Example**\n\n```js\napp.set('a.b.c', 'd');\napp.get('a.b');\n//=> {c: 'd'}\n\napp.get(['a', 'b']);\n//=> {c: 'd'}\n```\n\n### [.has](index.js#L171)\n\nReturn true if app has a stored value for `key`, false only if value is `undefined`.\n\n**Params**\n\n* `key` **{String}**\n* `returns` **{Boolean}**\n\n**Events**\n\n* `emits`: `has` with `key` and true or false as arguments.\n\n**Example**\n\n```js\napp.set('foo', 'bar');\napp.has('foo');\n//=> true\n```\n\n### [.del](index.js#L199)\n\nDelete one or more properties from the instance.\n\n**Params**\n\n* `key` **{String|Array}**: Property name or array of property names.\n* `returns` **{Object}**: Returns the instance for chaining.\n\n**Events**\n\n* `emits`: `del` with the `key` as the only argument.\n\n**Example**\n\n```js\napp.del(); // delete all\n// or\napp.del('foo');\n// or\napp.del(['foo', 'bar']);\n```\n\n### [.clear](index.js#L218)\n\nReset the entire cache to an empty object.\n\n**Example**\n\n```js\napp.clear();\n```\n\n### [.visit](index.js#L235)\n\nVisit `method` over the properties in the given object, or map\nvisit over the object-elements in an array.\n\n**Params**\n\n* `method` **{String}**: The name of the `base` method to call.\n* `val` **{Object|Array}**: The object or array to iterate over.\n* `returns` **{Object}**: Returns the instance for chaining.\n\n## About\n\n### Related projects\n\n* [base-methods](https://www.npmjs.com/package/base-methods): base-methods is the foundation for creating modular, unit testable and highly pluggable node.js applications, starting… [more](https://github.com/jonschlinkert/base-methods) | [homepage](https://github.com/jonschlinkert/base-methods \"base-methods is the foundation for creating modular, unit testable and highly pluggable node.js applications, starting with a handful of common methods, like `set`, `get`, `del` and `use`.\")\n* [get-value](https://www.npmjs.com/package/get-value): Use property paths (`a.b.c`) to get a nested value from an object. | [homepage](https://github.com/jonschlinkert/get-value \"Use property paths (`a.b.c`) to get a nested value from an object.\")\n* [has-value](https://www.npmjs.com/package/has-value): Returns true if a value exists, false if empty. Works with deeply nested values using… [more](https://github.com/jonschlinkert/has-value) | [homepage](https://github.com/jonschlinkert/has-value \"Returns true if a value exists, false if empty. Works with deeply nested values using object paths.\")\n* [option-cache](https://www.npmjs.com/package/option-cache): Simple API for managing options in JavaScript applications. | [homepage](https://github.com/jonschlinkert/option-cache \"Simple API for managing options in JavaScript applications.\")\n* [set-value](https://www.npmjs.com/package/set-value): Create nested values and any intermediaries using dot notation (`'a.b.c'`) paths. | [homepage](https://github.com/jonschlinkert/set-value \"Create nested values and any intermediaries using dot notation (`'a.b.c'`) paths.\")\n* [unset-value](https://www.npmjs.com/package/unset-value): Delete nested properties from an object using dot notation. | [homepage](https://github.com/jonschlinkert/unset-value \"Delete nested properties from an object using dot notation.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 54 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 2 | [wtgtybhertgeghgtwtg](https://github.com/wtgtybhertgeghgtwtg) |\n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on July 22, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/cache-base/-/cache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2", "type": "tarball", "reference": "https://registry.yarnpkg.com/cache-base/-/cache-base-1.0.1.tgz", "hash": "0a7f46416831c8b662ee36fe4e7c59d76f666ab2", "integrity": "sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ==", "registry": "npm", "packageName": "cache-base", "cacheIntegrity": "sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ== sha1-Cn9GQWgxyLZi7jb+TnxZ129marI="}, "registry": "npm", "hash": "0a7f46416831c8b662ee36fe4e7c59d76f666ab2"}