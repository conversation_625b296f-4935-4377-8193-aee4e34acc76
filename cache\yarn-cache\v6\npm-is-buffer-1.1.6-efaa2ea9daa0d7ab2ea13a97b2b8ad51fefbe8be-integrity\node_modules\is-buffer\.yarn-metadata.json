{"manifest": {"name": "is-buffer", "description": "Determine if an object is a Buffer", "version": "1.1.6", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "bugs": {"url": "https://github.com/feross/is-buffer/issues"}, "dependencies": {}, "devDependencies": {"standard": "*", "tape": "^4.0.0", "zuul": "^3.0.0"}, "keywords": ["buffer", "buffers", "type", "core buffer", "browser buffer", "browserify", "typed array", "uint32array", "int16array", "int32array", "float32array", "float64array", "browser", "arraybuffer", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/is-buffer.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js", "test-node": "tape test/*.js"}, "testling": {"files": "test/*.js"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-buffer-1.1.6-efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be-integrity\\node_modules\\is-buffer\\package.json", "readmeFilename": "README.md", "readme": "# is-buffer [![travis][travis-image]][travis-url] [![npm][npm-image]][npm-url] [![downloads][downloads-image]][downloads-url] [![javascript style guide][standard-image]][standard-url]\n\n[travis-image]: https://img.shields.io/travis/feross/is-buffer/master.svg\n[travis-url]: https://travis-ci.org/feross/is-buffer\n[npm-image]: https://img.shields.io/npm/v/is-buffer.svg\n[npm-url]: https://npmjs.org/package/is-buffer\n[downloads-image]: https://img.shields.io/npm/dm/is-buffer.svg\n[downloads-url]: https://npmjs.org/package/is-buffer\n[standard-image]: https://img.shields.io/badge/code_style-standard-brightgreen.svg\n[standard-url]: https://standardjs.com\n\n#### Determine if an object is a [`Buffer`](http://nodejs.org/api/buffer.html) (including the [browserify Buffer](https://github.com/feross/buffer))\n\n[![saucelabs][saucelabs-image]][saucelabs-url]\n\n[saucelabs-image]: https://saucelabs.com/browser-matrix/is-buffer.svg\n[saucelabs-url]: https://saucelabs.com/u/is-buffer\n\n## Why not use `Buffer.isBuffer`?\n\nThis module lets you check if an object is a `Buffer` without using `Buffer.isBuffer` (which includes the whole [buffer](https://github.com/feross/buffer) module in [browserify](http://browserify.org/)).\n\nIt's future-proof and works in node too!\n\n## install\n\n```bash\nnpm install is-buffer\n```\n\n## usage\n\n```js\nvar isBuffer = require('is-buffer')\n\nisBuffer(new Buffer(4)) // true\n\nisBuffer(undefined) // false\nisBuffer(null) // false\nisBuffer('') // false\nisBuffer(true) // false\nisBuffer(false) // false\nisBuffer(0) // false\nisBuffer(1) // false\nisBuffer(1.0) // false\nisBuffer('string') // false\nisBuffer({}) // false\nisBuffer(function foo () {}) // false\n```\n\n## license\n\nMIT. Copyright (C) [Feross Aboukhadijeh](http://feross.org).\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-buffer/-/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-buffer/-/is-buffer-1.1.6.tgz", "hash": "efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==", "registry": "npm", "packageName": "is-buffer", "cacheIntegrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w== sha1-76ouqdqg16suoTqXsritUf776L4="}, "registry": "npm", "hash": "efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"}