-- ================================================
-- DDCZ-VEHICLE Configuration
-- ================================================

Config = {}

-- ================================================
-- General Settings
-- ================================================

-- Default keybind for opening vehicle menu
Config.DefaultKeybind = 'U'

-- Maximum distance from vehicle to open menu
Config.MaxDistance = 5.0

-- Enable debug mode
Config.Debug = false

-- ================================================
-- Vehicle Control Settings
-- ================================================

-- Vehicle doors (0-5: front left, front right, back left, back right, hood, trunk)
Config.VehicleDoors = {
    {id = 0, name = 'Přední levé', icon = '🚪'},
    {id = 1, name = 'Přední pravé', icon = '🚪'},
    {id = 2, name = 'Zadní levé', icon = '🚪'},
    {id = 3, name = 'Zadní pravé', icon = '🚪'},
    {id = 4, name = 'Kapota', icon = '🔧'},
    {id = 5, name = 'Kufr', icon = '📦'}
}

-- Vehicle lights
Config.VehicleLights = {
    {id = 0, name = 'Všechna světla', icon = '💡'},
    {id = 1, name = 'Levý blikač', icon = '⬅️'},
    {id = 2, name = 'Pravý blikač', icon = '➡️'},
    {id = 3, name = 'Výstražná světla', icon = '⚠️'}
}

-- ================================================
-- Job Restrictions
-- ================================================

-- Jobs that can access extras and livery
Config.RestrictedJobs = {
    'police',
    'ambulance',
    'fire'
}

-- Check if player has restricted job access
Config.HasRestrictedAccess = function(source)
    local QBCore = exports['qb-core']:GetCoreObject()
    local Player = QBCore.Functions.GetPlayer(source)
    
    if not Player then return false end
    
    for _, job in pairs(Config.RestrictedJobs) do
        if Player.PlayerData.job.name == job then
            return true
        end
    end
    
    return false
end

-- ================================================
-- Vehicle Extras Settings
-- ================================================

-- Maximum number of extras to check
Config.MaxExtras = 12

-- Extras that are commonly used
Config.CommonExtras = {
    {id = 1, name = 'Extra 1', description = 'Světelná rampa'},
    {id = 2, name = 'Extra 2', description = 'Přední nárazník'},
    {id = 3, name = 'Extra 3', description = 'Zadní nárazník'},
    {id = 4, name = 'Extra 4', description = 'Boční světla'},
    {id = 5, name = 'Extra 5', description = 'Anténa'},
    {id = 6, name = 'Extra 6', description = 'Speciální výbava'}
}

-- ================================================
-- Livery Settings
-- ================================================

-- Maximum number of liveries to check
Config.MaxLiveries = 20

-- ================================================
-- Animation Settings
-- ================================================

-- NUI animation duration (in milliseconds)
Config.AnimationDuration = 300

-- Menu fade animation
Config.MenuFadeTime = 250

-- ================================================
-- Notification Settings
-- ================================================

Config.Messages = {
    no_vehicle = 'Nejste v žádném vozidle!',
    too_far = 'Jste příliš daleko od vozidla!',
    not_driver = 'Nejste řidič tohoto vozidla!',
    door_opened = 'Dveře otevřeny',
    door_closed = 'Dveře zavřeny',
    lights_on = 'Světla zapnuta',
    lights_off = 'Světla vypnuta',
    engine_on = 'Motor zapnut',
    engine_off = 'Motor vypnut',
    no_permission = 'Nemáte oprávnění k této funkci!',
    extra_enabled = 'Extra %s zapnuto',
    extra_disabled = 'Extra %s vypnuto',
    livery_changed = 'Livery změněno na %s'
}

-- ================================================
-- UI Settings
-- ================================================

-- Menu position
Config.MenuPosition = {
    x = 'center', -- 'left', 'center', 'right'
    y = 'center'  -- 'top', 'center', 'bottom'
}

-- Color scheme
Config.Colors = {
    primary = '#3498db',
    secondary = '#2c3e50',
    success = '#27ae60',
    warning = '#f39c12',
    danger = '#e74c3c',
    info = '#17a2b8'
}

-- ================================================
-- Advanced Settings
-- ================================================

-- Enable vehicle synchronization between players
Config.SyncVehicleChanges = true

-- Automatically close menu when player exits vehicle
Config.AutoCloseOnExit = true

-- Show vehicle info in menu
Config.ShowVehicleInfo = true

-- Enable sound effects
Config.EnableSounds = true

-- ================================================
-- Blacklisted Vehicles
-- ================================================

-- Vehicles that cannot be controlled (by model hash)
Config.BlacklistedVehicles = {
    -- Add vehicle hashes here if needed
    -- GetHashKey('vehiclename')
}
