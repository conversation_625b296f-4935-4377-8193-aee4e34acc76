# QB-Banking

## Features
- Handles all player interaction with bank/job/gang/shared accounts
- ATM and bank card integration
- Shared accounts between players
- Auto creation of job/gang accounts on bank first open
- Boss-only access to job/gang accounts

Documentation: https://docs.qbcore.org/qbcore-documentation/qbcore-resources/qb-banking

# License

    QBCore Framework
    Copyright (C) 2021 Joshua <PERSON>ger

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>
