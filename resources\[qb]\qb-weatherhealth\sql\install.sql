-- Weather Health System Database Tables

-- Tabulka pro NPC data
CREATE TABLE IF NOT EXISTS `weather_npc_data` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `npc_id` varchar(50) NOT NULL,
    `model` varchar(50) NOT NULL,
    `position_x` float NOT NULL,
    `position_y` float NOT NULL,
    `position_z` float NOT NULL,
    `heading` float NOT NULL,
    `health_status` int(11) DEFAULT 100,
    `disease_type` varchar(50) DEFAULT NULL,
    `disease_start_time` timestamp NULL DEFAULT NULL,
    `current_outfit` text DEFAULT NULL,
    `last_weather_check` timestamp DEFAULT CURRENT_TIMESTAMP,
    `temperature_tolerance` int(11) DEFAULT 0,
    `is_dead` tinyint(1) DEFAULT 0,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `npc_id` (`npc_id`),
    KEY `health_status` (`health_status`),
    KEY `is_dead` (`is_dead`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabulka pro zdravotní data hráčů
CREATE TABLE IF NOT EXISTS `weather_player_health` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `citizenid` varchar(50) NOT NULL,
    `health_status` int(11) DEFAULT 100,
    `disease_type` varchar(50) DEFAULT NULL,
    `disease_start_time` timestamp NULL DEFAULT NULL,
    `disease_severity` int(11) DEFAULT 0,
    `last_weather_check` timestamp DEFAULT CURRENT_TIMESTAMP,
    `temperature_comfort` varchar(20) DEFAULT 'comfortable',
    `clothing_warmth` int(11) DEFAULT 0,
    `immunity_level` int(11) DEFAULT 50,
    `treatment_history` text DEFAULT NULL,
    `last_treatment_time` timestamp NULL DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `citizenid` (`citizenid`),
    KEY `health_status` (`health_status`),
    KEY `disease_type` (`disease_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabulka pro statistiky oblečení
CREATE TABLE IF NOT EXISTS `weather_clothing_stats` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `citizenid` varchar(50) NOT NULL,
    `clothing_component` varchar(20) NOT NULL,
    `component_id` int(11) NOT NULL,
    `warmth_value` int(11) NOT NULL,
    `usage_count` int(11) DEFAULT 1,
    `effectiveness_rating` float DEFAULT 1.0,
    `last_used` timestamp DEFAULT CURRENT_TIMESTAMP,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `citizenid` (`citizenid`),
    KEY `clothing_component` (`clothing_component`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabulka pro weather logy
CREATE TABLE IF NOT EXISTS `weather_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `weather_type` varchar(20) NOT NULL,
    `temperature` int(11) NOT NULL,
    `time_hour` int(11) NOT NULL,
    `affected_players` int(11) DEFAULT 0,
    `affected_npcs` int(11) DEFAULT 0,
    `health_incidents` int(11) DEFAULT 0,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `weather_type` (`weather_type`),
    KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabulka pro treatment history
CREATE TABLE IF NOT EXISTS `weather_treatments` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `citizenid` varchar(50) NOT NULL,
    `treatment_type` varchar(50) NOT NULL,
    `item_used` varchar(50) DEFAULT NULL,
    `disease_treated` varchar(50) DEFAULT NULL,
    `success_rate` float DEFAULT 0.0,
    `treatment_result` varchar(20) NOT NULL,
    `health_before` int(11) NOT NULL,
    `health_after` int(11) NOT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `citizenid` (`citizenid`),
    KEY `treatment_type` (`treatment_type`),
    KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabulka pro NPC behavior patterns
CREATE TABLE IF NOT EXISTS `weather_npc_behavior` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `npc_id` varchar(50) NOT NULL,
    `weather_type` varchar(20) NOT NULL,
    `behavior_type` varchar(30) NOT NULL,
    `activity_level` float DEFAULT 1.0,
    `shelter_seeking` tinyint(1) DEFAULT 0,
    `warmth_seeking` tinyint(1) DEFAULT 0,
    `last_behavior_change` timestamp DEFAULT CURRENT_TIMESTAMP,
    `behavior_duration` int(11) DEFAULT 0,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `npc_id` (`npc_id`),
    KEY `weather_type` (`weather_type`),
    KEY `behavior_type` (`behavior_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default data
INSERT IGNORE INTO `weather_clothing_stats` (`citizenid`, `clothing_component`, `component_id`, `warmth_value`) VALUES
('default', 'torso', 0, 0),
('default', 'torso', 1, 2),
('default', 'torso', 2, 4),
('default', 'torso', 3, 6),
('default', 'torso', 4, 8),
('default', 'torso', 5, 10),
('default', 'torso', 6, 12),
('default', 'torso', 7, 15),
('default', 'legs', 0, 0),
('default', 'legs', 1, 1),
('default', 'legs', 2, 3),
('default', 'legs', 3, 5),
('default', 'legs', 4, 7),
('default', 'shoes', 0, 0),
('default', 'shoes', 1, 1),
('default', 'shoes', 2, 2),
('default', 'shoes', 3, 4),
('default', 'shoes', 4, 6),
('default', 'hat', 0, 0),
('default', 'hat', 1, 2),
('default', 'hat', 2, 4),
('default', 'hat', 3, 6);
