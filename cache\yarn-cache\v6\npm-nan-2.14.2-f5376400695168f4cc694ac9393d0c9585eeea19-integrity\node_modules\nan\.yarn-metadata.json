{"manifest": {"name": "nan", "version": "2.14.2", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 14 compatibility", "main": "include_dirs.js", "repository": {"type": "git", "url": "git://github.com/nodejs/nan.git"}, "scripts": {"test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test", "docs": "doc/.build.sh"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/rvagg"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kkoopa/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/trevnorris"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/TooTallNate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/brett19"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/agnat"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mkrufky"}], "devDependencies": {"bindings": "~1.2.1", "commander": "^2.8.1", "glob": "^5.0.14", "request": "=2.81.0", "node-gyp": "~3.6.2", "readable-stream": "^2.1.4", "tap": "~0.7.1", "xtend": "~4.0.0"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-nan-2.14.2-f5376400695168f4cc694ac9393d0c9585eeea19-integrity\\node_modules\\nan\\package.json", "readmeFilename": "README.md", "readme": "Native Abstractions for Node.js\n===============================\n\n**A header file filled with macro and utility goodness for making add-on development for Node.js easier across versions 0.8, 0.10, 0.12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13 and 14.**\n\n***Current version: 2.14.2***\n\n*(See [CHANGELOG.md](https://github.com/nodejs/nan/blob/master/CHANGELOG.md) for complete ChangeLog)*\n\n[![NPM](https://nodei.co/npm/nan.png?downloads=true&downloadRank=true)](https://nodei.co/npm/nan/) [![NPM](https://nodei.co/npm-dl/nan.png?months=6&height=3)](https://nodei.co/npm/nan/)\n\n[![Build Status](https://api.travis-ci.org/nodejs/nan.svg?branch=master)](https://travis-ci.org/nodejs/nan)\n[![Build status](https://ci.appveyor.com/api/projects/status/kh73pbm9dsju7fgh)](https://ci.appveyor.com/project/RodVagg/nan)\n\nThanks to the crazy changes in V8 (and some in Node core), keeping native addons compiling happily across versions, particularly 0.10 to 0.12 to 4.0, is a minor nightmare. The goal of this project is to store all logic necessary to develop native Node.js addons without having to inspect `NODE_MODULE_VERSION` and get yourself into a macro-tangle.\n\nThis project also contains some helper utilities that make addon development a bit more pleasant.\n\n * **[News & Updates](#news)**\n * **[Usage](#usage)**\n * **[Example](#example)**\n * **[API](#api)**\n * **[Tests](#tests)**\n * **[Known issues](#issues)**\n * **[Governance & Contributing](#governance)**\n\n<a name=\"news\"></a>\n\n## News & Updates\n\n<a name=\"usage\"></a>\n\n## Usage\n\nSimply add **NAN** as a dependency in the *package.json* of your Node addon:\n\n``` bash\n$ npm install --save nan\n```\n\nPull in the path to **NAN** in your *binding.gyp* so that you can use `#include <nan.h>` in your *.cpp* files:\n\n``` python\n\"include_dirs\" : [\n    \"<!(node -e \\\"require('nan')\\\")\"\n]\n```\n\nThis works like a `-I<path-to-NAN>` when compiling your addon.\n\n<a name=\"example\"></a>\n\n## Example\n\nJust getting started with Nan? Take a look at the **[Node Add-on Examples](https://github.com/nodejs/node-addon-examples)**.\n\nRefer to a [quick-start **Nan** Boilerplate](https://github.com/fcanas/node-native-boilerplate) for a ready-to-go project that utilizes basic Nan functionality.\n\nFor a simpler example, see the **[async pi estimation example](https://github.com/nodejs/nan/tree/master/examples/async_pi_estimate)** in the examples directory for full code and an explanation of what this Monte Carlo Pi estimation example does. Below are just some parts of the full example that illustrate the use of **NAN**.\n\nYet another example is **[nan-example-eol](https://github.com/CodeCharmLtd/nan-example-eol)**. It shows newline detection implemented as a native addon.\n\nAlso take a look at our comprehensive **[C++ test suite](https://github.com/nodejs/nan/tree/master/test/cpp)** which has a plethora of code snippets for your pasting pleasure.\n\n<a name=\"api\"></a>\n\n## API\n\nAdditional to the NAN documentation below, please consult:\n\n* [The V8 Getting Started * Guide](https://v8.dev/docs/embed)\n* [V8 API Documentation](https://v8docs.nodesource.com/)\n* [Node Add-on Documentation](https://nodejs.org/api/addons.html)\n\n<!-- START API -->\n\n### JavaScript-accessible methods\n\nA _template_ is a blueprint for JavaScript functions and objects in a context. You can use a template to wrap C++ functions and data structures within JavaScript objects so that they can be manipulated from JavaScript. See the V8 Embedders Guide section on [Templates](https://github.com/v8/v8/wiki/Embedder%27s-Guide#templates) for further information.\n\nIn order to expose functionality to JavaScript via a template, you must provide it to V8 in a form that it understands. Across the versions of V8 supported by NAN, JavaScript-accessible method signatures vary widely, NAN fully abstracts method declaration and provides you with an interface that is similar to the most recent V8 API but is backward-compatible with older versions that still use the now-deceased `v8::Argument` type.\n\n* **Method argument types**\n - <a href=\"doc/methods.md#api_nan_function_callback_info\"><b><code>Nan::FunctionCallbackInfo</code></b></a>\n - <a href=\"doc/methods.md#api_nan_property_callback_info\"><b><code>Nan::PropertyCallbackInfo</code></b></a>\n - <a href=\"doc/methods.md#api_nan_return_value\"><b><code>Nan::ReturnValue</code></b></a>\n* **Method declarations**\n - <a href=\"doc/methods.md#api_nan_method\"><b>Method declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_getter\"><b>Getter declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_setter\"><b>Setter declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_property_getter\"><b>Property getter declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_property_setter\"><b>Property setter declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_property_enumerator\"><b>Property enumerator declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_property_deleter\"><b>Property deleter declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_property_query\"><b>Property query declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_index_getter\"><b>Index getter declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_index_setter\"><b>Index setter declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_index_enumerator\"><b>Index enumerator declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_index_deleter\"><b>Index deleter declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_index_query\"><b>Index query declaration</b></a>\n* Method and template helpers\n - <a href=\"doc/methods.md#api_nan_set_method\"><b><code>Nan::SetMethod()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_prototype_method\"><b><code>Nan::SetPrototypeMethod()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_accessor\"><b><code>Nan::SetAccessor()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_named_property_handler\"><b><code>Nan::SetNamedPropertyHandler()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_indexed_property_handler\"><b><code>Nan::SetIndexedPropertyHandler()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_template\"><b><code>Nan::SetTemplate()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_prototype_template\"><b><code>Nan::SetPrototypeTemplate()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_instance_template\"><b><code>Nan::SetInstanceTemplate()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_call_handler\"><b><code>Nan::SetCallHandler()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_call_as_function_handler\"><b><code>Nan::SetCallAsFunctionHandler()</code></b></a>\n\n### Scopes\n\nA _local handle_ is a pointer to an object. All V8 objects are accessed using handles, they are necessary because of the way the V8 garbage collector works.\n\nA handle scope can be thought of as a container for any number of handles. When you've finished with your handles, instead of deleting each one individually you can simply delete their scope.\n\nThe creation of `HandleScope` objects is different across the supported versions of V8. Therefore, NAN provides its own implementations that can be used safely across these.\n\n - <a href=\"doc/scopes.md#api_nan_handle_scope\"><b><code>Nan::HandleScope</code></b></a>\n - <a href=\"doc/scopes.md#api_nan_escapable_handle_scope\"><b><code>Nan::EscapableHandleScope</code></b></a>\n\nAlso see the V8 Embedders Guide section on [Handles and Garbage Collection](https://github.com/v8/v8/wiki/Embedder%27s%20Guide#handles-and-garbage-collection).\n\n### Persistent references\n\nAn object reference that is independent of any `HandleScope` is a _persistent_ reference. Where a `Local` handle only lives as long as the `HandleScope` in which it was allocated, a `Persistent` handle remains valid until it is explicitly disposed.\n\nDue to the evolution of the V8 API, it is necessary for NAN to provide a wrapper implementation of the `Persistent` classes to supply compatibility across the V8 versions supported.\n\n - <a href=\"doc/persistent.md#api_nan_persistent_base\"><b><code>Nan::PersistentBase & v8::PersistentBase</code></b></a>\n - <a href=\"doc/persistent.md#api_nan_non_copyable_persistent_traits\"><b><code>Nan::NonCopyablePersistentTraits & v8::NonCopyablePersistentTraits</code></b></a>\n - <a href=\"doc/persistent.md#api_nan_copyable_persistent_traits\"><b><code>Nan::CopyablePersistentTraits & v8::CopyablePersistentTraits</code></b></a>\n - <a href=\"doc/persistent.md#api_nan_persistent\"><b><code>Nan::Persistent</code></b></a>\n - <a href=\"doc/persistent.md#api_nan_global\"><b><code>Nan::Global</code></b></a>\n - <a href=\"doc/persistent.md#api_nan_weak_callback_info\"><b><code>Nan::WeakCallbackInfo</code></b></a>\n - <a href=\"doc/persistent.md#api_nan_weak_callback_type\"><b><code>Nan::WeakCallbackType</code></b></a>\n\nAlso see the V8 Embedders Guide section on [Handles and Garbage Collection](https://developers.google.com/v8/embed#handles).\n\n### New\n\nNAN provides a `Nan::New()` helper for the creation of new JavaScript objects in a way that's compatible across the supported versions of V8.\n\n - <a href=\"doc/new.md#api_nan_new\"><b><code>Nan::New()</code></b></a>\n - <a href=\"doc/new.md#api_nan_undefined\"><b><code>Nan::Undefined()</code></b></a>\n - <a href=\"doc/new.md#api_nan_null\"><b><code>Nan::Null()</code></b></a>\n - <a href=\"doc/new.md#api_nan_true\"><b><code>Nan::True()</code></b></a>\n - <a href=\"doc/new.md#api_nan_false\"><b><code>Nan::False()</code></b></a>\n - <a href=\"doc/new.md#api_nan_empty_string\"><b><code>Nan::EmptyString()</code></b></a>\n\n\n### Converters\n\nNAN contains functions that convert `v8::Value`s to other `v8::Value` types and native types. Since type conversion is not guaranteed to succeed, they return `Nan::Maybe` types. These converters can be used in place of `value->ToX()` and `value->XValue()` (where `X` is one of the types, e.g. `Boolean`) in a way that provides a consistent interface across V8 versions. Newer versions of V8 use the new `v8::Maybe` and `v8::MaybeLocal` types for these conversions, older versions don't have this functionality so it is provided by NAN.\n\n - <a href=\"doc/converters.md#api_nan_to\"><b><code>Nan::To()</code></b></a>\n\n### Maybe Types\n\nThe `Nan::MaybeLocal` and `Nan::Maybe` types are monads that encapsulate `v8::Local` handles that _may be empty_.\n\n* **Maybe Types**\n  - <a href=\"doc/maybe_types.md#api_nan_maybe_local\"><b><code>Nan::MaybeLocal</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_maybe\"><b><code>Nan::Maybe</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_nothing\"><b><code>Nan::Nothing</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_just\"><b><code>Nan::Just</code></b></a>\n* **Maybe Helpers**\n  - <a href=\"doc/maybe_types.md#api_nan_call\"><b><code>Nan::Call()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_to_detail_string\"><b><code>Nan::ToDetailString()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_to_array_index\"><b><code>Nan::ToArrayIndex()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_equals\"><b><code>Nan::Equals()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_new_instance\"><b><code>Nan::NewInstance()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_function\"><b><code>Nan::GetFunction()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_set\"><b><code>Nan::Set()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_define_own_property\"><b><code>Nan::DefineOwnProperty()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_force_set\"><del><b><code>Nan::ForceSet()</code></b></del></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get\"><b><code>Nan::Get()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_property_attribute\"><b><code>Nan::GetPropertyAttributes()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_has\"><b><code>Nan::Has()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_delete\"><b><code>Nan::Delete()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_property_names\"><b><code>Nan::GetPropertyNames()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_own_property_names\"><b><code>Nan::GetOwnPropertyNames()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_set_prototype\"><b><code>Nan::SetPrototype()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_object_proto_to_string\"><b><code>Nan::ObjectProtoToString()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_has_own_property\"><b><code>Nan::HasOwnProperty()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_has_real_named_property\"><b><code>Nan::HasRealNamedProperty()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_has_real_indexed_property\"><b><code>Nan::HasRealIndexedProperty()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_has_real_named_callback_property\"><b><code>Nan::HasRealNamedCallbackProperty()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_real_named_property_in_prototype_chain\"><b><code>Nan::GetRealNamedPropertyInPrototypeChain()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_real_named_property\"><b><code>Nan::GetRealNamedProperty()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_call_as_function\"><b><code>Nan::CallAsFunction()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_call_as_constructor\"><b><code>Nan::CallAsConstructor()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_source_line\"><b><code>Nan::GetSourceLine()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_line_number\"><b><code>Nan::GetLineNumber()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_start_column\"><b><code>Nan::GetStartColumn()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_end_column\"><b><code>Nan::GetEndColumn()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_clone_element_at\"><b><code>Nan::CloneElementAt()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_has_private\"><b><code>Nan::HasPrivate()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_private\"><b><code>Nan::GetPrivate()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_set_private\"><b><code>Nan::SetPrivate()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_delete_private\"><b><code>Nan::DeletePrivate()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_make_maybe\"><b><code>Nan::MakeMaybe()</code></b></a>\n\n### Script\n\nNAN provides a `v8::Script` helpers as the API has changed over the supported versions of V8.\n\n - <a href=\"doc/script.md#api_nan_compile_script\"><b><code>Nan::CompileScript()</code></b></a>\n - <a href=\"doc/script.md#api_nan_run_script\"><b><code>Nan::RunScript()</code></b></a>\n\n\n### JSON\n\nThe _JSON_ object provides the C++ versions of the methods offered by the `JSON` object in javascript. V8 exposes these methods via the `v8::JSON` object.\n\n - <a href=\"doc/json.md#api_nan_json_parse\"><b><code>Nan::JSON.Parse</code></b></a>\n - <a href=\"doc/json.md#api_nan_json_stringify\"><b><code>Nan::JSON.Stringify</code></b></a>\n\nRefer to the V8 JSON object in the [V8 documentation](https://v8docs.nodesource.com/node-8.16/da/d6f/classv8_1_1_j_s_o_n.html) for more information about these methods and their arguments.\n\n### Errors\n\nNAN includes helpers for creating, throwing and catching Errors as much of this functionality varies across the supported versions of V8 and must be abstracted.\n\nNote that an Error object is simply a specialized form of `v8::Value`.\n\nAlso consult the V8 Embedders Guide section on [Exceptions](https://developers.google.com/v8/embed#exceptions) for more information.\n\n - <a href=\"doc/errors.md#api_nan_error\"><b><code>Nan::Error()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_range_error\"><b><code>Nan::RangeError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_reference_error\"><b><code>Nan::ReferenceError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_syntax_error\"><b><code>Nan::SyntaxError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_type_error\"><b><code>Nan::TypeError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_throw_error\"><b><code>Nan::ThrowError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_throw_range_error\"><b><code>Nan::ThrowRangeError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_throw_reference_error\"><b><code>Nan::ThrowReferenceError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_throw_syntax_error\"><b><code>Nan::ThrowSyntaxError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_throw_type_error\"><b><code>Nan::ThrowTypeError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_fatal_exception\"><b><code>Nan::FatalException()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_errno_exception\"><b><code>Nan::ErrnoException()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_try_catch\"><b><code>Nan::TryCatch</code></b></a>\n\n\n### Buffers\n\nNAN's `node::Buffer` helpers exist as the API has changed across supported Node versions. Use these methods to ensure compatibility.\n\n - <a href=\"doc/buffers.md#api_nan_new_buffer\"><b><code>Nan::NewBuffer()</code></b></a>\n - <a href=\"doc/buffers.md#api_nan_copy_buffer\"><b><code>Nan::CopyBuffer()</code></b></a>\n - <a href=\"doc/buffers.md#api_nan_free_callback\"><b><code>Nan::FreeCallback()</code></b></a>\n\n### Nan::Callback\n\n`Nan::Callback` makes it easier to use `v8::Function` handles as callbacks. A class that wraps a `v8::Function` handle, protecting it from garbage collection and making it particularly useful for storage and use across asynchronous execution.\n\n - <a href=\"doc/callback.md#api_nan_callback\"><b><code>Nan::Callback</code></b></a>\n\n### Asynchronous work helpers\n\n`Nan::AsyncWorker`, `Nan::AsyncProgressWorker` and `Nan::AsyncProgressQueueWorker` are helper classes that make working with asynchronous code easier.\n\n - <a href=\"doc/asyncworker.md#api_nan_async_worker\"><b><code>Nan::AsyncWorker</code></b></a>\n - <a href=\"doc/asyncworker.md#api_nan_async_progress_worker\"><b><code>Nan::AsyncProgressWorkerBase &amp; Nan::AsyncProgressWorker</code></b></a>\n - <a href=\"doc/asyncworker.md#api_nan_async_progress_queue_worker\"><b><code>Nan::AsyncProgressQueueWorker</code></b></a>\n - <a href=\"doc/asyncworker.md#api_nan_async_queue_worker\"><b><code>Nan::AsyncQueueWorker</code></b></a>\n\n### Strings & Bytes\n\nMiscellaneous string & byte encoding and decoding functionality provided for compatibility across supported versions of V8 and Node. Implemented by NAN to ensure that all encoding types are supported, even for older versions of Node where they are missing.\n\n - <a href=\"doc/string_bytes.md#api_nan_encoding\"><b><code>Nan::Encoding</code></b></a>\n - <a href=\"doc/string_bytes.md#api_nan_encode\"><b><code>Nan::Encode()</code></b></a>\n - <a href=\"doc/string_bytes.md#api_nan_decode_bytes\"><b><code>Nan::DecodeBytes()</code></b></a>\n - <a href=\"doc/string_bytes.md#api_nan_decode_write\"><b><code>Nan::DecodeWrite()</code></b></a>\n\n\n### Object Wrappers\n\nThe `ObjectWrap` class can be used to make wrapped C++ objects and a factory of wrapped objects.\n\n - <a href=\"doc/object_wrappers.md#api_nan_object_wrap\"><b><code>Nan::ObjectWrap</code></b></a>\n\n\n### V8 internals\n\nThe hooks to access V8 internals—including GC and statistics—are different across the supported versions of V8, therefore NAN provides its own hooks that call the appropriate V8 methods.\n\n - <a href=\"doc/v8_internals.md#api_nan_gc_callback\"><b><code>NAN_GC_CALLBACK()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_add_gc_epilogue_callback\"><b><code>Nan::AddGCEpilogueCallback()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_remove_gc_epilogue_callback\"><b><code>Nan::RemoveGCEpilogueCallback()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_add_gc_prologue_callback\"><b><code>Nan::AddGCPrologueCallback()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_remove_gc_prologue_callback\"><b><code>Nan::RemoveGCPrologueCallback()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_get_heap_statistics\"><b><code>Nan::GetHeapStatistics()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_set_counter_function\"><b><code>Nan::SetCounterFunction()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_set_create_histogram_function\"><b><code>Nan::SetCreateHistogramFunction()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_set_add_histogram_sample_function\"><b><code>Nan::SetAddHistogramSampleFunction()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_idle_notification\"><b><code>Nan::IdleNotification()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_low_memory_notification\"><b><code>Nan::LowMemoryNotification()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_context_disposed_notification\"><b><code>Nan::ContextDisposedNotification()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_get_internal_field_pointer\"><b><code>Nan::GetInternalFieldPointer()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_set_internal_field_pointer\"><b><code>Nan::SetInternalFieldPointer()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_adjust_external_memory\"><b><code>Nan::AdjustExternalMemory()</code></b></a>\n\n\n### Miscellaneous V8 Helpers\n\n - <a href=\"doc/v8_misc.md#api_nan_utf8_string\"><b><code>Nan::Utf8String</code></b></a>\n - <a href=\"doc/v8_misc.md#api_nan_get_current_context\"><b><code>Nan::GetCurrentContext()</code></b></a>\n - <a href=\"doc/v8_misc.md#api_nan_set_isolate_data\"><b><code>Nan::SetIsolateData()</code></b></a>\n - <a href=\"doc/v8_misc.md#api_nan_get_isolate_data\"><b><code>Nan::GetIsolateData()</code></b></a>\n - <a href=\"doc/v8_misc.md#api_nan_typedarray_contents\"><b><code>Nan::TypedArrayContents</code></b></a>\n\n\n### Miscellaneous Node Helpers\n\n - <a href=\"doc/node_misc.md#api_nan_asyncresource\"><b><code>Nan::AsyncResource</code></b></a>\n - <a href=\"doc/node_misc.md#api_nan_make_callback\"><b><code>Nan::MakeCallback()</code></b></a>\n - <a href=\"doc/node_misc.md#api_nan_module_init\"><b><code>NAN_MODULE_INIT()</code></b></a>\n - <a href=\"doc/node_misc.md#api_nan_export\"><b><code>Nan::Export()</code></b></a>\n\n<!-- END API -->\n\n\n<a name=\"tests\"></a>\n\n### Tests\n\nTo run the NAN tests do:\n\n``` sh\nnpm install\nnpm run-script rebuild-tests\nnpm test\n```\n\nOr just:\n\n``` sh\nnpm install\nmake test\n```\n\n<a name=\"issues\"></a>\n\n## Known issues\n\n### Compiling against Node.js 0.12 on OSX\n\nWith new enough compilers available on OSX, the versions of V8 headers corresponding to Node.js 0.12\ndo not compile anymore. The error looks something like:\n\n```\n❯   CXX(target) Release/obj.target/accessors/cpp/accessors.o\nIn file included from ../cpp/accessors.cpp:9:\nIn file included from ../../nan.h:51:\nIn file included from /Users/<USER>/.node-gyp/0.12.18/include/node/node.h:61:\n/Users/<USER>/.node-gyp/0.12.18/include/node/v8.h:5800:54: error: 'CreateHandle' is a protected member of 'v8::HandleScope'\n  return Handle<T>(reinterpret_cast<T*>(HandleScope::CreateHandle(\n                                        ~~~~~~~~~~~~~^~~~~~~~~~~~\n```\n\nThis can be worked around by patching your local versions of v8.h corresponding to Node 0.12 to make\n`v8::Handle` a friend of `v8::HandleScope`. Since neither Node.js not V8 support this release line anymore\nthis patch cannot be released by either project in an official release.\n\nFor this reason, we do not test against Node.js 0.12 on OSX in this project's CI. If you need to support\nthat configuration, you will need to either get an older compiler, or apply a source patch to the version\nof V8 headers as a workaround.\n\n<a name=\"governance\"></a>\n\n## Governance & Contributing\n\nNAN is governed by the [Node.js Addon API Working Group](https://github.com/nodejs/CTC/blob/master/WORKING_GROUPS.md#addon-api)\n\n### Addon API Working Group (WG)\n\nThe NAN project is jointly governed by a Working Group which is responsible for high-level guidance of the project.\n\nMembers of the WG are also known as Collaborators, there is no distinction between the two, unlike other Node.js projects.\n\nThe WG has final authority over this project including:\n\n* Technical direction\n* Project governance and process (including this policy)\n* Contribution policy\n* GitHub repository hosting\n* Maintaining the list of additional Collaborators\n\nFor the current list of WG members, see the project [README.md](./README.md#collaborators).\n\nIndividuals making significant and valuable contributions are made members of the WG and given commit-access to the project. These individuals are identified by the WG and their addition to the WG is discussed via GitHub and requires unanimous consensus amongst those WG members participating in the discussion with a quorum of 50% of WG members required for acceptance of the vote.\n\n_Note:_ If you make a significant contribution and are not considered for commit-access log an issue or contact a WG member directly.\n\nFor the current list of WG members / Collaborators, see the project [README.md](./README.md#collaborators).\n\n### Consensus Seeking Process\n\nThe WG follows a [Consensus Seeking](https://en.wikipedia.org/wiki/Consensus-seeking_decision-making) decision making model.\n\nModifications of the contents of the NAN repository are made on a collaborative basis. Anybody with a GitHub account may propose a modification via pull request and it will be considered by the WG. All pull requests must be reviewed and accepted by a WG member with sufficient expertise who is able to take full responsibility for the change. In the case of pull requests proposed by an existing WG member, an additional WG member is required for sign-off. Consensus should be sought if additional WG members participate and there is disagreement around a particular modification.\n\nIf a change proposal cannot reach a consensus, a WG member can call for a vote amongst the members of the WG. Simple majority wins.\n\n<a id=\"developers-certificate-of-origin\"></a>\n\n## Developer's Certificate of Origin 1.1\n\nBy making a contribution to this project, I certify that:\n\n* (a) The contribution was created in whole or in part by me and I\n  have the right to submit it under the open source license\n  indicated in the file; or\n\n* (b) The contribution is based upon previous work that, to the best\n  of my knowledge, is covered under an appropriate open source\n  license and I have the right under that license to submit that\n  work with modifications, whether created in whole or in part\n  by me, under the same open source license (unless I am\n  permitted to submit under a different license), as indicated\n  in the file; or\n\n* (c) The contribution was provided directly to me by some other\n  person who certified (a), (b) or (c) and I have not modified\n  it.\n\n* (d) I understand and agree that this project and the contribution\n  are public and that a record of the contribution (including all\n  personal information I submit with it, including my sign-off) is\n  maintained indefinitely and may be redistributed consistent with\n  this project or the open source license(s) involved.\n\n<a name=\"collaborators\"></a>\n\n### WG Members / Collaborators\n\n<table><tbody>\n<tr><th align=\"left\">Rod Vagg</th><td><a href=\"https://github.com/rvagg\">GitHub/rvagg</a></td><td><a href=\"http://twitter.com/rvagg\">Twitter/@rvagg</a></td></tr>\n<tr><th align=\"left\">Benjamin Byholm</th><td><a href=\"https://github.com/kkoopa/\">GitHub/kkoopa</a></td><td>-</td></tr>\n<tr><th align=\"left\">Trevor Norris</th><td><a href=\"https://github.com/trevnorris\">GitHub/trevnorris</a></td><td><a href=\"http://twitter.com/trevnorris\">Twitter/@trevnorris</a></td></tr>\n<tr><th align=\"left\">Nathan Rajlich</th><td><a href=\"https://github.com/TooTallNate\">GitHub/TooTallNate</a></td><td><a href=\"http://twitter.com/TooTallNate\">Twitter/@TooTallNate</a></td></tr>\n<tr><th align=\"left\">Brett Lawson</th><td><a href=\"https://github.com/brett19\">GitHub/brett19</a></td><td><a href=\"http://twitter.com/brett19x\">Twitter/@brett19x</a></td></tr>\n<tr><th align=\"left\">Ben Noordhuis</th><td><a href=\"https://github.com/bnoordhuis\">GitHub/bnoordhuis</a></td><td><a href=\"http://twitter.com/bnoordhuis\">Twitter/@bnoordhuis</a></td></tr>\n<tr><th align=\"left\">David Siegel</th><td><a href=\"https://github.com/agnat\">GitHub/agnat</a></td><td><a href=\"http://twitter.com/agnat\">Twitter/@agnat</a></td></tr>\n<tr><th align=\"left\">Michael Ira Krufky</th><td><a href=\"https://github.com/mkrufky\">GitHub/mkrufky</a></td><td><a href=\"http://twitter.com/mkrufky\">Twitter/@mkrufky</a></td></tr>\n</tbody></table>\n\n## Licence &amp; copyright\n\nCopyright (c) 2018 NAN WG Members / Collaborators (listed above).\n\nNative Abstractions for Node.js is licensed under an MIT license. All rights not explicitly granted in the MIT license are reserved. See the included LICENSE file for more details.\n", "licenseText": "The MIT License (MIT)\n=====================\n\nCopyright (c) 2018 NAN contributors\n-----------------------------------\n\n*NAN contributors listed at <https://github.com/nodejs/nan#contributors>*\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/nan/-/nan-2.14.2.tgz#f5376400695168f4cc694ac9393d0c9585eeea19", "type": "tarball", "reference": "https://registry.yarnpkg.com/nan/-/nan-2.14.2.tgz", "hash": "f5376400695168f4cc694ac9393d0c9585eeea19", "integrity": "sha512-M2ufzIiINKCuDfBSAUr1vWQ+vuVcA9kqx8JJUsbQi6yf1uGRyb7HfpdfUr5qLXf3B/t8dPvcjhKMmlfnP47EzQ==", "registry": "npm", "packageName": "nan", "cacheIntegrity": "sha512-M2ufzIiINKCuDfBSAUr1vWQ+vuVcA9kqx8JJUsbQi6yf1uGRyb7HfpdfUr5qLXf3B/t8dPvcjhKMmlfnP47EzQ== sha1-9TdkAGlRaPTMaUrJOT0MlYXu6hk="}, "registry": "npm", "hash": "f5376400695168f4cc694ac9393d0c9585eeea19"}