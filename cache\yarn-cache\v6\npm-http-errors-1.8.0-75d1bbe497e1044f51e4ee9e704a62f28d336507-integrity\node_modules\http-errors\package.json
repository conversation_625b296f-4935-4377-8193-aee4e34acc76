{"name": "http-errors", "description": "Create HTTP error objects", "version": "1.8.0", "author": "<PERSON> <<EMAIL>> (http://jongleberry.com)", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "MIT", "repository": "jshttp/http-errors", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "devDependencies": {"eslint": "6.8.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.22.0", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "mocha": "8.0.1", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md . && node ./scripts/lint-readme-list.js", "test": "mocha --reporter spec --bail", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "keywords": ["http", "error"], "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"]}