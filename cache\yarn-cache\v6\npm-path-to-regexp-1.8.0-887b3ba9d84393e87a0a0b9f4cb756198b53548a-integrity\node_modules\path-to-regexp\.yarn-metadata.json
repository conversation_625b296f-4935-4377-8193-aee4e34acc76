{"manifest": {"name": "path-to-regexp", "description": "Express style path to RegExp utility", "version": "1.8.0", "main": "index.js", "typings": "index.d.ts", "files": ["index.js", "index.d.ts", "LICENSE"], "scripts": {"lint": "standard", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "prepublish": "typings install", "test": "npm run lint && npm run test-cov"}, "keywords": ["express", "regexp", "route", "routing"], "component": {"scripts": {"path-to-regexp": "index.js"}}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pillarjs/path-to-regexp.git"}, "devDependencies": {"chai": "^2.3.0", "istanbul": "~0.3.0", "mocha": "~2.2.4", "standard": "~3.7.3", "ts-node": "^0.5.5", "typescript": "^1.8.7", "typings": "^1.0.4"}, "dependencies": {"isarray": "0.0.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-path-to-regexp-1.8.0-887b3ba9d84393e87a0a0b9f4cb756198b53548a-integrity\\node_modules\\path-to-regexp\\package.json", "readmeFilename": "Readme.md", "readme": "# Path-to-RegExp\n\n> Turn an Express-style path string such as `/user/:name` into a regular expression.\n\n[![NPM version][npm-image]][npm-url]\n[![Build status][travis-image]][travis-url]\n[![Test coverage][coveralls-image]][coveralls-url]\n[![Dependency Status][david-image]][david-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n## Installation\n\n```\nnpm install path-to-regexp --save\n```\n\n## Usage\n\n```javascript\nvar pathToRegexp = require('path-to-regexp')\n\n// pathToRegexp(path, keys, options)\n// pathToRegexp.parse(path)\n// pathToRegexp.compile(path)\n```\n\n- **path** An Express-style string, an array of strings, or a regular expression.\n- **keys** An array to be populated with the keys found in the path.\n- **options**\n  - **sensitive** When `true` the route will be case sensitive. (default: `false`)\n  - **strict** When `false` the trailing slash is optional. (default: `false`)\n  - **end** When `false` the path will match at the beginning. (default: `true`)\n  - **delimiter** Set the default delimiter for repeat parameters. (default: `'/'`)\n\n```javascript\nvar keys = []\nvar re = pathToRegexp('/foo/:bar', keys)\n// re = /^\\/foo\\/([^\\/]+?)\\/?$/i\n// keys = [{ name: 'bar', prefix: '/', delimiter: '/', optional: false, repeat: false, pattern: '[^\\\\/]+?' }]\n```\n\n**Please note:** The `RegExp` returned by `path-to-regexp` is intended for use with pathnames or hostnames. It can not handle the query strings or fragments of a URL.\n\n### Parameters\n\nThe path string can be used to define parameters and populate the keys.\n\n#### Named Parameters\n\nNamed parameters are defined by prefixing a colon to the parameter name (`:foo`). By default, the parameter will match until the following path segment.\n\n```js\nvar re = pathToRegexp('/:foo/:bar', keys)\n// keys = [{ name: 'foo', prefix: '/', ... }, { name: 'bar', prefix: '/', ... }]\n\nre.exec('/test/route')\n//=> ['/test/route', 'test', 'route']\n```\n\n**Please note:** Named parameters must be made up of \"word characters\" (`[A-Za-z0-9_]`).\n\n```js\nvar re = pathToRegexp('/(apple-)?icon-:res(\\\\d+).png', keys)\n// keys = [{ name: 0, prefix: '/', ... }, { name: 'res', prefix: '', ... }]\n\nre.exec('/icon-76.png')\n//=> ['/icon-76.png', undefined, '76']\n```\n\n#### Modified Parameters\n\n##### Optional\n\nParameters can be suffixed with a question mark (`?`) to make the parameter optional. This will also make the prefix optional.\n\n```js\nvar re = pathToRegexp('/:foo/:bar?', keys)\n// keys = [{ name: 'foo', ... }, { name: 'bar', delimiter: '/', optional: true, repeat: false }]\n\nre.exec('/test')\n//=> ['/test', 'test', undefined]\n\nre.exec('/test/route')\n//=> ['/test', 'test', 'route']\n```\n\n##### Zero or more\n\nParameters can be suffixed with an asterisk (`*`) to denote a zero or more parameter matches. The prefix is taken into account for each match.\n\n```js\nvar re = pathToRegexp('/:foo*', keys)\n// keys = [{ name: 'foo', delimiter: '/', optional: true, repeat: true }]\n\nre.exec('/')\n//=> ['/', undefined]\n\nre.exec('/bar/baz')\n//=> ['/bar/baz', 'bar/baz']\n```\n\n##### One or more\n\nParameters can be suffixed with a plus sign (`+`) to denote a one or more parameter matches. The prefix is taken into account for each match.\n\n```js\nvar re = pathToRegexp('/:foo+', keys)\n// keys = [{ name: 'foo', delimiter: '/', optional: false, repeat: true }]\n\nre.exec('/')\n//=> null\n\nre.exec('/bar/baz')\n//=> ['/bar/baz', 'bar/baz']\n```\n\n#### Custom Match Parameters\n\nAll parameters can be provided a custom regexp, which overrides the default (`[^\\/]+`).\n\n```js\nvar re = pathToRegexp('/:foo(\\\\d+)', keys)\n// keys = [{ name: 'foo', ... }]\n\nre.exec('/123')\n//=> ['/123', '123']\n\nre.exec('/abc')\n//=> null\n```\n\n**Please note:** Backslashes need to be escaped with another backslash in strings.\n\n#### Unnamed Parameters\n\nIt is possible to write an unnamed parameter that only consists of a matching group. It works the same as a named parameter, except it will be numerically indexed.\n\n```js\nvar re = pathToRegexp('/:foo/(.*)', keys)\n// keys = [{ name: 'foo', ... }, { name: 0, ... }]\n\nre.exec('/test/route')\n//=> ['/test/route', 'test', 'route']\n```\n\n#### Asterisk\n\nAn asterisk can be used for matching everything. It is equivalent to an unnamed matching group of `(.*)`.\n\n```js\nvar re = pathToRegexp('/foo/*', keys)\n// keys = [{ name: '0', ... }]\n\nre.exec('/foo/bar/baz')\n//=> ['/foo/bar/baz', 'bar/baz']\n```\n\n### Parse\n\nThe parse function is exposed via `pathToRegexp.parse`. This will return an array of strings and keys.\n\n```js\nvar tokens = pathToRegexp.parse('/route/:foo/(.*)')\n\nconsole.log(tokens[0])\n//=> \"/route\"\n\nconsole.log(tokens[1])\n//=> { name: 'foo', prefix: '/', delimiter: '/', optional: false, repeat: false, pattern: '[^\\\\/]+?' }\n\nconsole.log(tokens[2])\n//=> { name: 0, prefix: '/', delimiter: '/', optional: false, repeat: false, pattern: '.*' }\n```\n\n**Note:** This method only works with Express-style strings.\n\n### Compile (\"Reverse\" Path-To-RegExp)\n\nPath-To-RegExp exposes a compile function for transforming an Express-style path into a valid path.\n\n```js\nvar toPath = pathToRegexp.compile('/user/:id')\n\ntoPath({ id: 123 }) //=> \"/user/123\"\ntoPath({ id: 'café' }) //=> \"/user/caf%C3%A9\"\ntoPath({ id: '/' }) //=> \"/user/%2F\"\n\ntoPath({ id: ':' }) //=> \"/user/%3A\"\ntoPath({ id: ':' }, { pretty: true }) //=> \"/user/:\"\n\nvar toPathRepeated = pathToRegexp.compile('/:segment+')\n\ntoPathRepeated({ segment: 'foo' }) //=> \"/foo\"\ntoPathRepeated({ segment: ['a', 'b', 'c'] }) //=> \"/a/b/c\"\n\nvar toPathRegexp = pathToRegexp.compile('/user/:id(\\\\d+)')\n\ntoPathRegexp({ id: 123 }) //=> \"/user/123\"\ntoPathRegexp({ id: '123' }) //=> \"/user/123\"\ntoPathRegexp({ id: 'abc' }) //=> Throws `TypeError`.\n```\n\n**Note:** The generated function will throw on invalid input. It will do all necessary checks to ensure the generated path is valid. This method only works with strings.\n\n### Working with Tokens\n\nPath-To-RegExp exposes the two functions used internally that accept an array of tokens.\n\n* `pathToRegexp.tokensToRegExp(tokens, options)` Transform an array of tokens into a matching regular expression.\n* `pathToRegexp.tokensToFunction(tokens)` Transform an array of tokens into a path generator function.\n\n#### Token Information\n\n* `name` The name of the token (`string` for named or `number` for index)\n* `prefix` The prefix character for the segment (`/` or `.`)\n* `delimiter` The delimiter for the segment (same as prefix or `/`)\n* `optional` Indicates the token is optional (`boolean`)\n* `repeat` Indicates the token is repeated (`boolean`)\n* `partial` Indicates this token is a partial path segment (`boolean`)\n* `pattern` The RegExp used to match this token (`string`)\n* `asterisk` Indicates the token is an `*` match (`boolean`)\n\n## Compatibility with Express <= 4.x\n\nPath-To-RegExp breaks compatibility with Express <= `4.x`:\n\n* No longer a direct conversion to a RegExp with sugar on top - it's a path matcher with named and unnamed matching groups\n  * It's unlikely you previously abused this feature, it's rare and you could always use a RegExp instead\n* All matching RegExp special characters can be used in a matching group. E.g. `/:user(.*)`\n  * Other RegExp features are not support - no nested matching groups, non-capturing groups or look aheads\n* Parameters have suffixes that augment meaning - `*`, `+` and `?`. E.g. `/:user*`\n\n## TypeScript\n\nIncludes a [`.d.ts`](index.d.ts) file for TypeScript users.\n\n## Live Demo\n\nYou can see a live demo of this library in use at [express-route-tester](http://forbeslindesay.github.com/express-route-tester/).\n\n## License\n\nMIT\n\n[npm-image]: https://img.shields.io/npm/v/path-to-regexp.svg?style=flat\n[npm-url]: https://npmjs.org/package/path-to-regexp\n[travis-image]: https://img.shields.io/travis/pillarjs/path-to-regexp.svg?style=flat\n[travis-url]: https://travis-ci.org/pillarjs/path-to-regexp\n[coveralls-image]: https://img.shields.io/coveralls/pillarjs/path-to-regexp.svg?style=flat\n[coveralls-url]: https://coveralls.io/r/pillarjs/path-to-regexp?branch=master\n[david-image]: http://img.shields.io/david/pillarjs/path-to-regexp.svg?style=flat\n[david-url]: https://david-dm.org/pillarjs/path-to-regexp\n[license-image]: http://img.shields.io/npm/l/path-to-regexp.svg?style=flat\n[license-url]: LICENSE.md\n[downloads-image]: http://img.shields.io/npm/dm/path-to-regexp.svg?style=flat\n[downloads-url]: https://npmjs.org/package/path-to-regexp\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 <PERSON> (<EMAIL>)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/path-to-regexp/-/path-to-regexp-1.8.0.tgz#887b3ba9d84393e87a0a0b9f4cb756198b53548a", "type": "tarball", "reference": "https://registry.yarnpkg.com/path-to-regexp/-/path-to-regexp-1.8.0.tgz", "hash": "887b3ba9d84393e87a0a0b9f4cb756198b53548a", "integrity": "sha512-n43JRhlUKUAlibEJhPeir1ncUID16QnEjNpwzNdO3Lm4ywrBpBZ5oLD0I6br9evr1Y9JTqwRtAh7JLoOzAQdVA==", "registry": "npm", "packageName": "path-to-regexp", "cacheIntegrity": "sha512-n43JRhlUKUAlibEJhPeir1ncUID16QnEjNpwzNdO3Lm4ywrBpBZ5oLD0I6br9evr1Y9JTqwRtAh7JLoOzAQdVA== sha1-iHs7qdhDk+h6CgufTLdWGYtTVIo="}, "registry": "npm", "hash": "887b3ba9d84393e87a0a0b9f4cb756198b53548a"}