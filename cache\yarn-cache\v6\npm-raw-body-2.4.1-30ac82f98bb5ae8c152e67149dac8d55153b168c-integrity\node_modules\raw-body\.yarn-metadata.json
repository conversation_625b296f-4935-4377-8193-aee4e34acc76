{"manifest": {"name": "raw-body", "description": "Get and validate the raw body of a readable stream.", "version": "2.4.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/stream-utils/raw-body.git"}, "dependencies": {"bytes": "3.1.0", "http-errors": "1.7.3", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "devDependencies": {"bluebird": "3.5.5", "eslint": "6.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.18.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-node": "9.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.0", "istanbul": "0.4.5", "mocha": "6.1.4", "readable-stream": "2.3.6", "safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.8"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.d.ts", "index.js"], "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --trace-deprecation --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --trace-deprecation --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --trace-deprecation --reporter spec --check-leaks test/"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-raw-body-2.4.1-30ac82f98bb5ae8c152e67149dac8d55153b168c-integrity\\node_modules\\raw-body\\package.json", "readmeFilename": "README.md", "readme": "# raw-body\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build status][travis-image]][travis-url]\n[![Test coverage][coveralls-image]][coveralls-url]\n\nGets the entire buffer of a stream either as a `Buffer` or a string.\nValidates the stream's length against an expected length and maximum limit.\nIdeal for parsing request bodies.\n\n## Install\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install raw-body\n```\n\n### TypeScript\n\nThis module includes a [TypeScript](https://www.typescriptlang.org/)\ndeclaration file to enable auto complete in compatible editors and type\ninformation for TypeScript projects. This module depends on the Node.js\ntypes, so install `@types/node`:\n\n```sh\n$ npm install @types/node\n```\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar getRawBody = require('raw-body')\n```\n\n### getRawBody(stream, [options], [callback])\n\n**Returns a promise if no callback specified and global `Promise` exists.**\n\nOptions:\n\n- `length` - The length of the stream.\n  If the contents of the stream do not add up to this length,\n  an `400` error code is returned.\n- `limit` - The byte limit of the body.\n  This is the number of bytes or any string format supported by\n  [bytes](https://www.npmjs.com/package/bytes),\n  for example `1000`, `'500kb'` or `'3mb'`.\n  If the body ends up being larger than this limit,\n  a `413` error code is returned.\n- `encoding` - The encoding to use to decode the body into a string.\n  By default, a `Buffer` instance will be returned when no encoding is specified.\n  Most likely, you want `utf-8`, so setting `encoding` to `true` will decode as `utf-8`.\n  You can use any type of encoding supported by [iconv-lite](https://www.npmjs.org/package/iconv-lite#readme).\n\nYou can also pass a string in place of options to just specify the encoding.\n\nIf an error occurs, the stream will be paused, everything unpiped,\nand you are responsible for correctly disposing the stream.\nFor HTTP requests, no handling is required if you send a response.\nFor streams that use file descriptors, you should `stream.destroy()` or `stream.close()` to prevent leaks.\n\n## Errors\n\nThis module creates errors depending on the error condition during reading.\nThe error may be an error from the underlying Node.js implementation, but is\notherwise an error created by this module, which has the following attributes:\n\n  * `limit` - the limit in bytes\n  * `length` and `expected` - the expected length of the stream\n  * `received` - the received bytes\n  * `encoding` - the invalid encoding\n  * `status` and `statusCode` - the corresponding status code for the error\n  * `type` - the error type\n\n### Types\n\nThe errors from this module have a `type` property which allows for the progamatic\ndetermination of the type of error returned.\n\n#### encoding.unsupported\n\nThis error will occur when the `encoding` option is specified, but the value does\nnot map to an encoding supported by the [iconv-lite](https://www.npmjs.org/package/iconv-lite#readme)\nmodule.\n\n#### entity.too.large\n\nThis error will occur when the `limit` option is specified, but the stream has\nan entity that is larger.\n\n#### request.aborted\n\nThis error will occur when the request stream is aborted by the client before\nreading the body has finished.\n\n#### request.size.invalid\n\nThis error will occur when the `length` option is specified, but the stream has\nemitted more bytes.\n\n#### stream.encoding.set\n\nThis error will occur when the given stream has an encoding set on it, making it\na decoded stream. The stream should not have an encoding set and is expected to\nemit `Buffer` objects.\n\n## Examples\n\n### Simple Express example\n\n```js\nvar contentType = require('content-type')\nvar express = require('express')\nvar getRawBody = require('raw-body')\n\nvar app = express()\n\napp.use(function (req, res, next) {\n  getRawBody(req, {\n    length: req.headers['content-length'],\n    limit: '1mb',\n    encoding: contentType.parse(req).parameters.charset\n  }, function (err, string) {\n    if (err) return next(err)\n    req.text = string\n    next()\n  })\n})\n\n// now access req.text\n```\n\n### Simple Koa example\n\n```js\nvar contentType = require('content-type')\nvar getRawBody = require('raw-body')\nvar koa = require('koa')\n\nvar app = koa()\n\napp.use(function * (next) {\n  this.text = yield getRawBody(this.req, {\n    length: this.req.headers['content-length'],\n    limit: '1mb',\n    encoding: contentType.parse(this.req).parameters.charset\n  })\n  yield next\n})\n\n// now access this.text\n```\n\n### Using as a promise\n\nTo use this library as a promise, simply omit the `callback` and a promise is\nreturned, provided that a global `Promise` is defined.\n\n```js\nvar getRawBody = require('raw-body')\nvar http = require('http')\n\nvar server = http.createServer(function (req, res) {\n  getRawBody(req)\n    .then(function (buf) {\n      res.statusCode = 200\n      res.end(buf.length + ' bytes submitted')\n    })\n    .catch(function (err) {\n      res.statusCode = 500\n      res.end(err.message)\n    })\n})\n\nserver.listen(3000)\n```\n\n### Using with TypeScript\n\n```ts\nimport * as getRawBody from 'raw-body';\nimport * as http from 'http';\n\nconst server = http.createServer((req, res) => {\n  getRawBody(req)\n  .then((buf) => {\n    res.statusCode = 200;\n    res.end(buf.length + ' bytes submitted');\n  })\n  .catch((err) => {\n    res.statusCode = err.statusCode;\n    res.end(err.message);\n  });\n});\n\nserver.listen(3000);\n```\n\n## License\n\n[MIT](LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/raw-body.svg\n[npm-url]: https://npmjs.org/package/raw-body\n[node-version-image]: https://img.shields.io/node/v/raw-body.svg\n[node-version-url]: https://nodejs.org/en/download/\n[travis-image]: https://img.shields.io/travis/stream-utils/raw-body/master.svg\n[travis-url]: https://travis-ci.org/stream-utils/raw-body\n[coveralls-image]: https://img.shields.io/coveralls/stream-utils/raw-body/master.svg\n[coveralls-url]: https://coveralls.io/r/stream-utils/raw-body?branch=master\n[downloads-image]: https://img.shields.io/npm/dm/raw-body.svg\n[downloads-url]: https://npmjs.org/package/raw-body\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2013-2014 <PERSON> <<EMAIL>>\nCopyright (c) 2014-2015 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/raw-body/-/raw-body-2.4.1.tgz#30ac82f98bb5ae8c152e67149dac8d55153b168c", "type": "tarball", "reference": "https://registry.yarnpkg.com/raw-body/-/raw-body-2.4.1.tgz", "hash": "30ac82f98bb5ae8c152e67149dac8d55153b168c", "integrity": "sha512-9WmIKF6mkvA0SLmA2Knm9+qj89e+j1zqgyn8aXGd7+nAduPoqgI9lO57SAZNn/Byzo5P7JhXTyg9PzaJbH73bA==", "registry": "npm", "packageName": "raw-body", "cacheIntegrity": "sha512-9WmIKF6mkvA0SLmA2Knm9+qj89e+j1zqgyn8aXGd7+nAduPoqgI9lO57SAZNn/Byzo5P7JhXTyg9PzaJbH73bA== sha1-MKyC+Yu1rowVLmcUnayNVRU7Fow="}, "registry": "npm", "hash": "30ac82f98bb5ae8c152e67149dac8d55153b168c"}