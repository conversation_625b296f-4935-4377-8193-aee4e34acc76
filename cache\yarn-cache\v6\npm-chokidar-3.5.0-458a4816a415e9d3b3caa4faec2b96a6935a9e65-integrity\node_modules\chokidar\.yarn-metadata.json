{"manifest": {"name": "chokidar", "description": "Minimal and efficient cross-platform file watching library", "version": "3.5.0", "homepage": "https://github.com/paulmillr/chokidar", "author": {"name": "<PERSON>", "url": "https://paulmillr.com"}, "contributors": [{"name": "<PERSON>", "url": "https://paulmillr.com"}, {"name": "<PERSON><PERSON>"}], "engines": {"node": ">= 8.10.0"}, "main": "index.js", "dependencies": {"anymatch": "~3.1.1", "braces": "~3.0.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.5.0"}, "optionalDependencies": {"fsevents": "~2.3.1"}, "devDependencies": {"@types/node": "^14", "chai": "^4.2", "dtslint": "^3.3.0", "eslint": "^7.0.0", "mocha": "^7.0.0", "nyc": "^15.0.0", "rimraf": "^3.0.0", "sinon": "^9.0.1", "sinon-chai": "^3.3.0", "upath": "^1.2.0"}, "files": ["index.js", "lib/*.js", "types/index.d.ts"], "repository": {"type": "git", "url": "git+https://github.com/paulmillr/chokidar.git"}, "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "license": "MIT", "scripts": {"dtslint": "dtslint types", "lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "mocha": "mocha --exit --timeout 60000", "test": "npm run lint && npm run mocha"}, "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "types": "./types/index.d.ts", "nyc": {"include": ["index.js", "lib/*.js"], "reporter": ["html", "text"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-chokidar-3.5.0-458a4816a415e9d3b3caa4faec2b96a6935a9e65-integrity\\node_modules\\chokidar\\package.json", "readmeFilename": "README.md", "readme": "# Chokidar [![Weekly downloads](https://img.shields.io/npm/dw/chokidar.svg)](https://github.com/paulmillr/chokidar) [![Yearly downloads](https://img.shields.io/npm/dy/chokidar.svg)](https://github.com/paulmillr/chokidar)\n\n> Minimal and efficient cross-platform file watching library\n\n[![NPM](https://nodei.co/npm/chokidar.png)](https://www.npmjs.com/package/chokidar)\n\n## Why?\n\nNode.js `fs.watch`:\n\n* Doesn't report filenames on MacOS.\n* Doesn't report events at all when using editors like Sublime on MacOS.\n* Often reports events twice.\n* Emits most changes as `rename`.\n* Does not provide an easy way to recursively watch file trees.\n\nNode.js `fs.watchFile`:\n\n* Almost as bad at event handling.\n* Also does not provide any recursive watching.\n* Results in high CPU utilization.\n\nChokidar resolves these problems.\n\nInitially made for **[Brunch](https://brunch.io/)** (an ultra-swift web app build tool), it is now used in\n[Microsoft's Visual Studio Code](https://github.com/microsoft/vscode),\n[gulp](https://github.com/gulpjs/gulp/),\n[karma](https://karma-runner.github.io/),\n[PM2](https://github.com/Unitech/PM2),\n[browserify](http://browserify.org/),\n[webpack](https://webpack.github.io/),\n[BrowserSync](https://www.browsersync.io/),\nand [many others](https://www.npmjs.com/browse/depended/chokidar).\nIt has proven itself in production environments.\n\nVersion 3 is out! Check out our blog post about it: [Chokidar 3: How to save 32TB of traffic every week](https://paulmillr.com/posts/chokidar-3-save-32tb-of-traffic/)\n\n## How?\n\nChokidar does still rely on the Node.js core `fs` module, but when using\n`fs.watch` and `fs.watchFile` for watching, it normalizes the events it\nreceives, often checking for truth by getting file stats and/or dir contents.\n\nOn MacOS, chokidar by default uses a native extension exposing the Darwin\n`FSEvents` API. This provides very efficient recursive watching compared with\nimplementations like `kqueue` available on most \\*nix platforms. Chokidar still\ndoes have to do some work to normalize the events received that way as well.\n\nOn other platforms, the `fs.watch`-based implementation is the default, which\navoids polling and keeps CPU usage down. Be advised that chokidar will initiate\nwatchers recursively for everything within scope of the paths that have been\nspecified, so be judicious about not wasting system resources by watching much\nmore than needed.\n\n## Getting started\n\nInstall with npm:\n\n```sh\nnpm install chokidar\n```\n\nThen `require` and use it in your code:\n\n```javascript\nconst chokidar = require('chokidar');\n\n// One-liner for current directory\nchokidar.watch('.').on('all', (event, path) => {\n  console.log(event, path);\n});\n```\n\n## API\n\n```javascript\n// Example of a more typical implementation structure\n\n// Initialize watcher.\nconst watcher = chokidar.watch('file, dir, glob, or array', {\n  ignored: /(^|[\\/\\\\])\\../, // ignore dotfiles\n  persistent: true\n});\n\n// Something to use when events are received.\nconst log = console.log.bind(console);\n// Add event listeners.\nwatcher\n  .on('add', path => log(`File ${path} has been added`))\n  .on('change', path => log(`File ${path} has been changed`))\n  .on('unlink', path => log(`File ${path} has been removed`));\n\n// More possible events.\nwatcher\n  .on('addDir', path => log(`Directory ${path} has been added`))\n  .on('unlinkDir', path => log(`Directory ${path} has been removed`))\n  .on('error', error => log(`Watcher error: ${error}`))\n  .on('ready', () => log('Initial scan complete. Ready for changes'))\n  .on('raw', (event, path, details) => { // internal\n    log('Raw event info:', event, path, details);\n  });\n\n// 'add', 'addDir' and 'change' events also receive stat() results as second\n// argument when available: https://nodejs.org/api/fs.html#fs_class_fs_stats\nwatcher.on('change', (path, stats) => {\n  if (stats) console.log(`File ${path} changed size to ${stats.size}`);\n});\n\n// Watch new files.\nwatcher.add('new-file');\nwatcher.add(['new-file-2', 'new-file-3', '**/other-file*']);\n\n// Get list of actual paths being watched on the filesystem\nvar watchedPaths = watcher.getWatched();\n\n// Un-watch some files.\nawait watcher.unwatch('new-file*');\n\n// Stop watching.\n// The method is async!\nwatcher.close().then(() => console.log('closed'));\n\n// Full list of options. See below for descriptions.\n// Do not use this example!\nchokidar.watch('file', {\n  persistent: true,\n\n  ignored: '*.txt',\n  ignoreInitial: false,\n  followSymlinks: true,\n  cwd: '.',\n  disableGlobbing: false,\n\n  usePolling: false,\n  interval: 100,\n  binaryInterval: 300,\n  alwaysStat: false,\n  depth: 99,\n  awaitWriteFinish: {\n    stabilityThreshold: 2000,\n    pollInterval: 100\n  },\n\n  ignorePermissionErrors: false,\n  atomic: true // or a custom 'atomicity delay', in milliseconds (default 100)\n});\n\n```\n\n`chokidar.watch(paths, [options])`\n\n* `paths` (string or array of strings). Paths to files, dirs to be watched\nrecursively, or glob patterns.\n    - Note: globs must not contain windows separators (`\\`),\n    because that's how they work by the standard —\n    you'll need to replace them with forward slashes (`/`).\n    - Note 2: for additional glob documentation, check out low-level\n    library: [picomatch](https://github.com/micromatch/picomatch).\n* `options` (object) Options object as defined below:\n\n#### Persistence\n\n* `persistent` (default: `true`). Indicates whether the process\nshould continue to run as long as files are being watched. If set to\n`false` when using `fsevents` to watch, no more events will be emitted\nafter `ready`, even if the process continues to run.\n\n#### Path filtering\n\n* `ignored` ([anymatch](https://github.com/es128/anymatch)-compatible definition)\nDefines files/paths to be ignored. The whole relative or absolute path is\ntested, not just filename. If a function with two arguments is provided, it\ngets called twice per path - once with a single argument (the path), second\ntime with two arguments (the path and the\n[`fs.Stats`](https://nodejs.org/api/fs.html#fs_class_fs_stats)\nobject of that path).\n* `ignoreInitial` (default: `false`). If set to `false` then `add`/`addDir` events are also emitted for matching paths while\ninstantiating the watching as chokidar discovers these file paths (before the `ready` event).\n* `followSymlinks` (default: `true`). When `false`, only the\nsymlinks themselves will be watched for changes instead of following\nthe link references and bubbling events through the link's path.\n* `cwd` (no default). The base directory from which watch `paths` are to be\nderived. Paths emitted with events will be relative to this.\n* `disableGlobbing` (default: `false`). If set to `true` then the strings passed to `.watch()` and `.add()` are treated as\nliteral path names, even if they look like globs.\n\n#### Performance\n\n* `usePolling` (default: `false`).\nWhether to use fs.watchFile (backed by polling), or fs.watch. If polling\nleads to high CPU utilization, consider setting this to `false`. It is\ntypically necessary to **set this to `true` to successfully watch files over\na network**, and it may be necessary to successfully watch files in other\nnon-standard situations. Setting to `true` explicitly on MacOS overrides the\n`useFsEvents` default. You may also set the CHOKIDAR_USEPOLLING env variable\nto true (1) or false (0) in order to override this option.\n* _Polling-specific settings_ (effective when `usePolling: true`)\n  * `interval` (default: `100`). Interval of file system polling, in milliseconds. You may also\n    set the CHOKIDAR_INTERVAL env variable to override this option.\n  * `binaryInterval` (default: `300`). Interval of file system\n  polling for binary files.\n  ([see list of binary extensions](https://github.com/sindresorhus/binary-extensions/blob/master/binary-extensions.json))\n* `useFsEvents` (default: `true` on MacOS). Whether to use the\n`fsevents` watching interface if available. When set to `true` explicitly\nand `fsevents` is available this supercedes the `usePolling` setting. When\nset to `false` on MacOS, `usePolling: true` becomes the default.\n* `alwaysStat` (default: `false`). If relying upon the\n[`fs.Stats`](https://nodejs.org/api/fs.html#fs_class_fs_stats)\nobject that may get passed with `add`, `addDir`, and `change` events, set\nthis to `true` to ensure it is provided even in cases where it wasn't\nalready available from the underlying watch events.\n* `depth` (default: `undefined`). If set, limits how many levels of\nsubdirectories will be traversed.\n* `awaitWriteFinish` (default: `false`).\nBy default, the `add` event will fire when a file first appears on disk, before\nthe entire file has been written. Furthermore, in some cases some `change`\nevents will be emitted while the file is being written. In some cases,\nespecially when watching for large files there will be a need to wait for the\nwrite operation to finish before responding to a file creation or modification.\nSetting `awaitWriteFinish` to `true` (or a truthy value) will poll file size,\nholding its `add` and `change` events until the size does not change for a\nconfigurable amount of time. The appropriate duration setting is heavily\ndependent on the OS and hardware. For accurate detection this parameter should\nbe relatively high, making file watching much less responsive.\nUse with caution.\n  * *`options.awaitWriteFinish` can be set to an object in order to adjust\n  timing params:*\n  * `awaitWriteFinish.stabilityThreshold` (default: 2000). Amount of time in\n  milliseconds for a file size to remain constant before emitting its event.\n  * `awaitWriteFinish.pollInterval` (default: 100). File size polling interval, in milliseconds.\n\n#### Errors\n\n* `ignorePermissionErrors` (default: `false`). Indicates whether to watch files\nthat don't have read permissions if possible. If watching fails due to `EPERM`\nor `EACCES` with this set to `true`, the errors will be suppressed silently.\n* `atomic` (default: `true` if `useFsEvents` and `usePolling` are `false`).\nAutomatically filters out artifacts that occur when using editors that use\n\"atomic writes\" instead of writing directly to the source file. If a file is\nre-added within 100 ms of being deleted, Chokidar emits a `change` event\nrather than `unlink` then `add`. If the default of 100 ms does not work well\nfor you, you can override it by setting `atomic` to a custom value, in\nmilliseconds.\n\n### Methods & Events\n\n`chokidar.watch()` produces an instance of `FSWatcher`. Methods of `FSWatcher`:\n\n* `.add(path / paths)`: Add files, directories, or glob patterns for tracking.\nTakes an array of strings or just one string.\n* `.on(event, callback)`: Listen for an FS event.\nAvailable events: `add`, `addDir`, `change`, `unlink`, `unlinkDir`, `ready`,\n`raw`, `error`.\nAdditionally `all` is available which gets emitted with the underlying event\nname and path for every event other than `ready`, `raw`, and `error`.  `raw` is internal, use it carefully.\n* `.unwatch(path / paths)`: Stop watching files, directories, or glob patterns.\nTakes an array of strings or just one string. Use with `await` to ensure bugs don't happen.\n* `.close()`: **async** Removes all listeners from watched files. Asynchronous, returns Promise.\n* `.getWatched()`: Returns an object representing all the paths on the file\nsystem being watched by this `FSWatcher` instance. The object's keys are all the\ndirectories (using absolute paths unless the `cwd` option was used), and the\nvalues are arrays of the names of the items contained in each directory.\n\n## CLI\n\nIf you need a CLI interface for your file watching, check out\n[chokidar-cli](https://github.com/kimmobrunfeldt/chokidar-cli), allowing you to\nexecute a command on each change, or get a stdio stream of change events.\n\n## Install Troubleshooting\n\n* `npm WARN optional dep failed, continuing fsevents@n.n.n`\n  * This message is normal part of how `npm` handles optional dependencies and is\n    not indicative of a problem. Even if accompanied by other related error messages,\n    Chokidar should function properly.\n\n* `TypeError: fsevents is not a constructor`\n  * Update chokidar by doing `rm -rf node_modules package-lock.json yarn.lock && npm install`, or update your dependency that uses chokidar.\n\n* Chokidar is producing `ENOSP` error on Linux, like this:\n  * `bash: cannot set terminal process group (-1): Inappropriate ioctl for device bash: no job control in this shell`\n  `Error: watch /home/<USER>\n  * This means Chokidar ran out of file handles and you'll need to increase their count by executing the following command in Terminal:\n  `echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf && sudo sysctl -p`\n\n## Changelog\n\nFor more detailed changelog, see [`full_changelog.md`](.github/full_changelog.md).\n- **v3.5 (Jan 6, 2021):** Support for ARM Macs with Apple Silicon. Fixes for deleted symlinks.\n- **v3.4 (Apr 26, 2020):** Support for directory-based symlinks. Fixes for macos file replacement.\n- **v3.3 (Nov 2, 2019):** `FSWatcher#close()` method became async. That fixes IO race conditions related to close method.\n- **v3.2 (Oct 1, 2019):** Improve Linux RAM usage by 50%. Race condition fixes. Windows glob fixes. Improve stability by using tight range of dependency versions.\n- **v3.1 (Sep 16, 2019):** dotfiles are no longer filtered out by default. Use `ignored` option if needed. Improve initial Linux scan time by 50%.\n- **v3 (Apr 30, 2019):** massive CPU & RAM consumption improvements; reduces deps / package size by a factor of 17x and bumps Node.js requirement to v8.16 and higher.\n- **v2 (Dec 29, 2017):** Globs are now posix-style-only; without windows support. Tons of bugfixes.\n- **v1 (Apr 7, 2015):** Glob support, symlink support, tons of bugfixes. Node 0.8+ is supported\n- **v0.1 (Apr 20, 2012):** Initial release, extracted from [Brunch](https://github.com/brunch/brunch/blob/9847a065aea300da99bd0753f90354cde9de1261/src/helpers.coffee#L66)\n\n## Also\n\nWhy was chokidar named this way? What's the meaning behind it?\n\n>Chowkidar is a transliteration of a Hindi word meaning 'watchman, gatekeeper', चौकीदार. This ultimately comes from Sanskrit _ चतुष्क_ (crossway, quadrangle, consisting-of-four).\n\n## License\n\nMIT (c) Paul Miller (<https://paulmillr.com>), see [LICENSE](LICENSE) file.\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2012-2019 <PERSON> (https://paulmillr.com), <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the “Software”), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/chokidar/-/chokidar-3.5.0.tgz#458a4816a415e9d3b3caa4faec2b96a6935a9e65", "type": "tarball", "reference": "https://registry.yarnpkg.com/chokidar/-/chokidar-3.5.0.tgz", "hash": "458a4816a415e9d3b3caa4faec2b96a6935a9e65", "integrity": "sha512-JgQM9JS92ZbFR4P90EvmzNpSGhpPBGBSj10PILeDyYFwp4h2/D9OM03wsJ4zW1fEp4ka2DGrnUeD7FuvQ2aZ2Q==", "registry": "npm", "packageName": "chokidar", "cacheIntegrity": "sha512-JgQM9JS92ZbFR4P90EvmzNpSGhpPBGBSj10PILeDyYFwp4h2/D9OM03wsJ4zW1fEp4ka2DGrnUeD7FuvQ2aZ2Q== sha1-RYpIFqQV6dOzyqT67CuWppNanmU="}, "registry": "npm", "hash": "458a4816a415e9d3b3caa4faec2b96a6935a9e65"}