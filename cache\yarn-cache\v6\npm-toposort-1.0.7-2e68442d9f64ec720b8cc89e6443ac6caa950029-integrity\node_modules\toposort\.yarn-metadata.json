{"manifest": {"name": "toposort", "version": "1.0.7", "description": "Topological sort of directed ascyclic graphs (like dependecy lists)", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/marcelklehr/toposort.git"}, "devDependencies": {"vows": "0.7.x"}, "keywords": ["topological", "sort", "sorting", "graphs", "graph", "dependency", "list", "dependencies", "acyclic"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-toposort-1.0.7-2e68442d9f64ec720b8cc89e6443ac6caa950029-integrity\\node_modules\\toposort\\package.json", "readmeFilename": "README.md", "readme": "# Toposort\n\nSort directed acyclic graphs\n\n[![Build Status](https://travis-ci.org/marcelklehr/toposort.png)](https://travis-ci.org/marcelklehr/toposort)\n\n## Installation\n\n`npm install toposort` or `component install marcelklehr/toposort`  \n\nthen in your code:\n\n```js\ntoposort = require('toposort')\n```\n\n## Usage\nWe want to sort the following graph.\n\n![graph](https://cdn.rawgit.com/marcelklehr/toposort/8b14e9fd/graph.svg)\n\n```js\n// First, we define our edges.\nvar graph = [\n  ['put on your shoes', 'tie your shoes']\n, ['put on your shirt', 'put on your jacket']\n, ['put on your shorts', 'put on your jacket']\n, ['put on your shorts', 'put on your shoes']\n]\n\n\n// Now, sort the vertices topologically, to reveal a legal execution order.\ntoposort(graph)\n// [ 'put on your shirt'\n// , 'put on your shorts'\n// , 'put on your jacket'\n// , 'put on your shoes'\n// , 'tie your shoes' ]\n```\n\n(Note that there is no defined order for graph parts that are not connected\n -- you could also put on your jacket after having tied your shoes...)\n\n### Sorting dependencies\nIt is usually more convenient to specify *dependencies* instead of \"sequences\".\n```js\n// This time, edges represent dependencies.\nvar graph = [\n  ['tie your shoes', 'put on your shoes']\n, ['put on your jacket', 'put on your shirt']\n, ['put on your shoes', 'put on your shorts']\n, ['put on your jacket', 'put on your shorts']\n]\n\ntoposort(graph) \n// [ 'tie your shoes'\n// , 'put on your shoes'\n// , 'put on your jacket'\n// , 'put on your shirt'\n// , 'put on your shorts' ]\n\n// Now, reversing the list will reveal a legal execution order.\ntoposort(graph).reverse() \n// [ 'put on your shorts'\n// , 'put on your shirt'\n// , 'put on your jacket'\n// , 'put on your shoes'\n// , 'tie your shoes' ]\n```\n\n## API\n\n### toposort(edges)\n\n+ edges {Array} An array of directed edges describing a graph. An edge looks like this: `[node1, node2]` (vertices needn't be strings but can be of any type).\n\nReturns: {Array} a list of vertices, sorted from \"start\" to \"end\"\n\nThrows an error if there are any cycles in the graph.\n\n### toposort.array(nodes, edges)\n\n+ nodes {Array} An array of nodes\n+ edges {Array} An array of directed edges. You don't need to mention all `nodes` here.\n\nThis is a convenience method that allows you to define nodes that may or may not be connected to any other nodes. The ordering of unconnected nodes is not defined.\n\nReturns: {Array} a list of vertices, sorted from \"start\" to \"end\"\n\nThrows an error if there are any cycles in the graph.\n\n## Tests\n\nRun the tests with `node test.js`.\n\n## Legal\n\nMIT License\n", "licenseText": "\nToposort - Topological sorting for node.js\nCopyright (c) 2012 by <PERSON> <<EMAIL>>\nMIT LICENSE\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/toposort/-/toposort-1.0.7.tgz#2e68442d9f64ec720b8cc89e6443ac6caa950029", "type": "tarball", "reference": "https://registry.yarnpkg.com/toposort/-/toposort-1.0.7.tgz", "hash": "2e68442d9f64ec720b8cc89e6443ac6caa950029", "integrity": "sha1-LmhELZ9k7HILjMieZEOsbKqVACk=", "registry": "npm", "packageName": "toposort", "cacheIntegrity": "sha512-FclLrw8b9bMWf4QlCJuHBEVhSRsqDj6u3nIjAzPeJvgl//1hBlffdlk0MALceL14+koWEdU4ofRAXofbODxQzg== sha1-LmhELZ9k7HILjMieZEOsbKqVACk="}, "registry": "npm", "hash": "2e68442d9f64ec720b8cc89e6443ac6caa950029"}