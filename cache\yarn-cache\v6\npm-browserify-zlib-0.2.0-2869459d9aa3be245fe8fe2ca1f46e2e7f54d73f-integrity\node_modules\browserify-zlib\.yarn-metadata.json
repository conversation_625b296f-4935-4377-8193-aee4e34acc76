{"manifest": {"name": "browserify-zlib", "version": "0.2.0", "description": "Full zlib module for the browser", "keywords": ["zlib", "browserify"], "main": "lib/index.js", "dependencies": {"pako": "~1.0.5"}, "devDependencies": {"assert": "^1.4.1", "babel-cli": "^6.24.1", "babel-plugin-transform-es2015-arrow-functions": "^6.22.0", "babel-plugin-transform-es2015-block-scoping": "^6.24.1", "babel-plugin-transform-es2015-template-literals": "^6.22.0", "babelify": "^7.3.0", "brfs": "^1.4.3", "browserify": "^14.4.0", "exec-glob": "^1.2.2", "glob": "^7.1.2", "karma": "^1.7.0", "karma-chrome-launcher": "^2.1.1", "karma-firefox-launcher": "^1.0.1", "karma-mocha": "^1.3.0", "karma-mocha-own-reporter": "^1.1.2", "karma-phantomjs-launcher": "^1.0.4", "mocha": "^3.4.2", "phantomjs-prebuilt": "^2.1.14", "standard": "^10.0.2", "watchify": "^3.9.0"}, "scripts": {"build": "babel src --out-dir lib", "lint": "standard \"*.js\" \"!(node_modules|lib)/!(*test-zlib*|index).js\"", "pretest": "npm run build", "test": "npm run test:node && npm run test:browser", "test:node": "node node_modules/exec-glob node \"test/test-*\"", "pretest:browser": "node test/build", "test:browser": "karma start --single-run=true karma.conf.js"}, "babel": {"plugins": ["transform-es2015-arrow-functions", "transform-es2015-block-scoping", "transform-es2015-template-literals"]}, "author": {"name": "Devon Govett", "email": "<EMAIL>"}, "homepage": "https://github.com/devongovett/browserify-zlib", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/devongovett/browserify-zlib.git"}, "bugs": {"url": "https://github.com/devongovett/browserify-zlib/issues"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-browserify-zlib-0.2.0-2869459d9aa3be245fe8fe2ca1f46e2e7f54d73f-integrity\\node_modules\\browserify-zlib\\package.json", "readmeFilename": "README.md", "readme": "# browserify-zlib\n\n[![<PERSON>](https://travis-ci.org/devongovett/browserify-zlib.svg?branch=master)](https://travis-ci.org/devongovett/browserify-zlib)\n[![Dependency Status](https://david-dm.org/devongovett/browserify-zlib.svg?style=flat-square)](https://david-dm.org/devongovett/browserify-zlib) [![js-standard-style](https://img.shields.io/badge/code%20style-standard-brightgreen.svg?style=flat-square)](https://github.com/feross/standard)\n\n## Description\n\nEmulates Node's [zlib](https://nodejs.org/api/zlib.html) module for the browser. Can be used as a drop in replacement with [Browserify](http://browserify.org) and [webpack](http://webpack.github.io/).\n\nThe heavy lifting is done using [pako](https://github.com/nodeca/pako). The code in this module is modeled closely after the code in the source of Node core to get as much compatability as possible.\n\n## API\n\nhttps://nodejs.org/api/zlib.html\n\n## Not implemented\n\nThe following options/methods are not supported because pako does not support them yet.\n\n* The `params` method\n\n## License\n\nMIT\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2015 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\nThis project contains parts of Node.js.\nNode.js is licensed for use as follows:\n\n\"\"\"\nCopyright Node.js contributors. All rights reserved.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n\"\"\"\n\nThis license applies to parts of Node.js originating from the\nhttps://github.com/joyent/node repository:\n\n\"\"\"\nCopyright Joyent, Inc. and other Node contributors. All rights reserved.\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n\"\"\"\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/browserify-zlib/-/browserify-zlib-0.2.0.tgz#2869459d9aa3be245fe8fe2ca1f46e2e7f54d73f", "type": "tarball", "reference": "https://registry.yarnpkg.com/browserify-zlib/-/browserify-zlib-0.2.0.tgz", "hash": "2869459d9aa3be245fe8fe2ca1f46e2e7f54d73f", "integrity": "sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA==", "registry": "npm", "packageName": "browserify-zlib", "cacheIntegrity": "sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA== sha1-KGlFnZqjviRf6P4sofRuLn9U1z8="}, "registry": "npm", "hash": "2869459d9aa3be245fe8fe2ca1f46e2e7f54d73f"}