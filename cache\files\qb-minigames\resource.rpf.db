[{"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/client/hacking.lua", "mt": 1749054006, "s": 867, "i": "PTgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/client/keyminigame.lua", "mt": 1749054006, "s": 748, "i": "PjgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/client/lockpick.lua", "mt": 1749054006, "s": 684, "i": "PzgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/client/pinpad.lua", "mt": 1749054006, "s": 747, "i": "QDgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/client/quiz.lua", "mt": 1749054006, "s": 890, "i": "QTgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/client/skillbar.lua", "mt": 1749054006, "s": 539, "i": "QjgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/client/wordguess.lua", "mt": 1749054006, "s": 1048, "i": "QzgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/client/wordscramble.lua", "mt": 1749054006, "s": 1353, "i": "RDgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/fxmanifest.lua", "mt": 1749054006, "s": 344, "i": "RTgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/css/hacking.css", "mt": 1749054006, "s": 1255, "i": "RjgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/css/keyminigame.css", "mt": 1749054006, "s": 665, "i": "RzgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/css/lockpick.css", "mt": 1749054006, "s": 1559, "i": "SDgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/css/pinpad.css", "mt": 1749054006, "s": 1206, "i": "STgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/css/quiz.css", "mt": 1749054006, "s": 7281, "i": "SjgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/css/skillbar.css", "mt": 1749054006, "s": 204, "i": "SzgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/css/tictactoe.css", "mt": 1749054006, "s": 0, "i": "TDgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/css/wordguess.css", "mt": 1749054006, "s": 1492, "i": "TTgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/css/wordscramble.css", "mt": 1749054006, "s": 1760, "i": "TjgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/images/phone.png", "mt": 1749054006, "s": 8796, "i": "TzgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/index.html", "mt": 1749054006, "s": 7833, "i": "UDgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/js/hacking.js", "mt": 1749054006, "s": 10218, "i": "UTgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/js/keyminigame.js", "mt": 1749054006, "s": 4415, "i": "UjgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/js/lockpick.js", "mt": 1749054006, "s": 7084, "i": "UzgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/js/pinpad.js", "mt": 1749054006, "s": 2677, "i": "VDgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/js/quiz.js", "mt": 1749054006, "s": 7365, "i": "VTgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/js/skillbar.js", "mt": 1749054006, "s": 5858, "i": "VjgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/js/wordguess.js", "mt": 1749054006, "s": 3379, "i": "VzgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/js/wordscramble.js", "mt": 1749054006, "s": 3074, "i": "WDgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/sounds/beep.ogg", "mt": 1749054006, "s": 6744, "i": "WTgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/sounds/correct.ogg", "mt": 1749054006, "s": 10447, "i": "WjgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/sounds/fail.ogg", "mt": 1749054006, "s": 21845, "i": "WzgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/sounds/finish.ogg", "mt": 1749054006, "s": 13809, "i": "XDgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/sounds/start.ogg", "mt": 1749054006, "s": 28770, "i": "XTgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-minigames/html/sounds/wrong.ogg", "mt": 1749054006, "s": 7018, "i": "XjgCAAAABwAAAAAAAAAAAA=="}]