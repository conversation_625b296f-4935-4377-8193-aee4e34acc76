{"manifest": {"name": "no-case", "version": "2.3.2", "description": "Remove case from a string", "main": "no-case.js", "typings": "no-case.d.ts", "files": ["no-case.js", "no-case.d.ts", "vendor", "LICENSE"], "scripts": {"lint": "standard", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail", "test": "npm run lint && npm run test-cov", "build": "node build.js"}, "standard": {"ignore": ["coverage/**"]}, "repository": {"type": "git", "url": "git://github.com/blakeembrey/no-case.git"}, "keywords": ["no", "case", "space", "lower", "trim"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/blakeembrey/no-case/issues"}, "homepage": "https://github.com/blakeembrey/no-case", "devDependencies": {"chai": "^4.0.2", "istanbul": "^0.4.3", "jsesc": "^2.2.0", "mocha": "^3.0.0", "standard": "^10.0.2", "xregexp": "^3.1.1"}, "dependencies": {"lower-case": "^1.1.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-no-case-2.3.2-60b813396be39b3f1288a4c1ed5d1e7d28b464ac-integrity\\node_modules\\no-case\\package.json", "readmeFilename": "README.md", "readme": "# No Case\n\n[![NPM version][npm-image]][npm-url]\n[![NPM downloads][downloads-image]][downloads-url]\n[![Build status][travis-image]][travis-url]\n[![Test coverage][coveralls-image]][coveralls-url]\n[![Greenkeeper badge](https://badges.greenkeeper.io/blakeembrey/no-case.svg)](https://greenkeeper.io/)\n\nTransform a string to lower space cased. Optional locale and replacement character supported.\n\nSupports Unicode (non-ASCII characters) and non-string entities, such as objects with a `toString` property, numbers and booleans. Empty values (`null` and `undefined`) will result in an empty string.\n\n## Installation\n\n```\nnpm install no-case --save\n```\n\n## Usage\n\n```javascript\nvar noCase = require('no-case')\n\nnoCase(null)              //=> \"\"\nnoCase('string')          //=> \"string\"\nnoCase('dot.case')        //=> \"dot case\"\nnoCase('camelCase')       //=> \"camel case\"\nnoCase('<PERSON>') //=> \"beyoncé knowles\"\n\nnoCase('A STRING', 'tr') //=> \"a strıng\"\n\nnoCase('HELLO WORLD!', null, '_') //=> \"hello_world\"\n```\n\n## Typings\n\nIncludes a [TypeScript definition](no-case.d.ts).\n\n## License\n\nMIT\n\n[npm-image]: https://img.shields.io/npm/v/no-case.svg?style=flat\n[npm-url]: https://npmjs.org/package/no-case\n[downloads-image]: https://img.shields.io/npm/dm/no-case.svg?style=flat\n[downloads-url]: https://npmjs.org/package/no-case\n[travis-image]: https://img.shields.io/travis/blakeembrey/no-case.svg?style=flat\n[travis-url]: https://travis-ci.org/blakeembrey/no-case\n[coveralls-image]: https://img.shields.io/coveralls/blakeembrey/no-case.svg?style=flat\n[coveralls-url]: https://coveralls.io/r/blakeembrey/no-case?branch=master\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 <PERSON> (<EMAIL>)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/no-case/-/no-case-2.3.2.tgz#60b813396be39b3f1288a4c1ed5d1e7d28b464ac", "type": "tarball", "reference": "https://registry.yarnpkg.com/no-case/-/no-case-2.3.2.tgz", "hash": "60b813396be39b3f1288a4c1ed5d1e7d28b464ac", "integrity": "sha512-rmTZ9kz+f3rCvK2TD1Ue/oZlns7OGoIWP4fc3llxxRXlOkHKoWPPWJOfFYpITabSow43QJbRIoHQXtt10VldyQ==", "registry": "npm", "packageName": "no-case", "cacheIntegrity": "sha512-rmTZ9kz+f3rCvK2TD1Ue/oZlns7OGoIWP4fc3llxxRXlOkHKoWPPWJOfFYpITabSow43QJbRIoHQXtt10VldyQ== sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw="}, "registry": "npm", "hash": "60b813396be39b3f1288a4c1ed5d1e7d28b464ac"}