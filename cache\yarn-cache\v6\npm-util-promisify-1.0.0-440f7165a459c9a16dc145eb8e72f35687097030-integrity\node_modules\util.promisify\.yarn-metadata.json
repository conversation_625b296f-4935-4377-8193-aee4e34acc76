{"manifest": {"name": "util.promisify", "version": "1.0.0", "description": "Polyfill/shim for util.promisify in node versions < v8", "main": "index.js", "dependencies": {"define-properties": "^1.1.2", "object.getownpropertydescriptors": "^2.0.3"}, "devDependencies": {"@es-shims/api": "^1.2.0", "@ljharb/eslint-config": "^11.0.0", "eslint": "^3.19.0", "safe-publish-latest": "^1.1.1"}, "scripts": {"prepublish": "safe-publish-latest", "lint": "eslint .", "pretest": "npm run lint", "tests-only": "es-shim-api --bound", "test": "npm run tests-only"}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/util.promisify.git"}, "keywords": ["promisify", "promise", "util", "polyfill", "shim", "util.promisify"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/util.promisify/issues"}, "homepage": "https://github.com/ljharb/util.promisify#readme", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-util-promisify-1.0.0-440f7165a459c9a16dc145eb8e72f35687097030-integrity\\node_modules\\util.promisify\\package.json", "readmeFilename": "README.md", "readme": "# util.promisify\nPolyfill for util.promisify in node versions &lt; v8\n\nnode v8.0.0 added support for a built-in `util.promisify`: https://github.com/nodejs/node/pull/12442/\n\nThis package provides the built-in `util.promisify` in node v8.0.0 and later, and a replacement in other environments.\n\nNote: this package requires a native ES5 environment, and for `Promise` to be globally available. It will throw upon requiring it if these are not present.\n", "licenseText": "MIT License\n\nCopyright (c) 2017 Jordan Harband\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/util.promisify/-/util.promisify-1.0.0.tgz#440f7165a459c9a16dc145eb8e72f35687097030", "type": "tarball", "reference": "https://registry.yarnpkg.com/util.promisify/-/util.promisify-1.0.0.tgz", "hash": "440f7165a459c9a16dc145eb8e72f35687097030", "integrity": "sha512-i+6qA2MPhvoKLuxnJNpXAGhg7HphQOSUq2LKMZD0m15EiskXUkMvKdF4Uui0WYeCUGea+o2cw/ZuwehtfsrNkA==", "registry": "npm", "packageName": "util.promisify", "cacheIntegrity": "sha512-i+6qA2MPhvoKLuxnJNpXAGhg7HphQOSUq2LKMZD0m15EiskXUkMvKdF4Uui0WYeCUGea+o2cw/ZuwehtfsrNkA== sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA="}, "registry": "npm", "hash": "440f7165a459c9a16dc145eb8e72f35687097030"}