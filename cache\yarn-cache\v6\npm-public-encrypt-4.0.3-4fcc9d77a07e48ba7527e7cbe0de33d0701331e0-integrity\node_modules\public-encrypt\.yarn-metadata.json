{"manifest": {"name": "public-encrypt", "version": "4.0.3", "description": "browserify version of publicEncrypt & privateDecrypt", "main": "index.js", "browser": "browser.js", "scripts": {"test": "node test/index.js | tspec", "lint": "standard"}, "repository": {"type": "git", "url": "https://github.com/crypto-browserify/publicEncrypt.git"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/publicEncrypt/issues"}, "homepage": "https://github.com/crypto-browserify/publicEncrypt", "dependencies": {"bn.js": "^4.1.0", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "parse-asn1": "^5.0.0", "randombytes": "^2.0.1", "safe-buffer": "^5.1.2"}, "devDependencies": {"standard": "^12.0.0", "tap-spec": "^2.1.2", "tape": "^3.0.3"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-public-encrypt-4.0.3-4fcc9d77a07e48ba7527e7cbe0de33d0701331e0-integrity\\node_modules\\public-encrypt\\package.json", "readmeFilename": "readme.md", "readme": "publicEncrypt\n===\n\n[![Build Status](https://travis-ci.org/crypto-browserify/publicEncrypt.svg)](https://travis-ci.org/crypto-browserify/publicEncrypt)\n\npublicEncrypt/privateDecrypt for browserify\n\n[Blog post about the moving parts that have gone into this.](http://calvinmetcalf.com/post/109301244759/porting-nodejs-crypto-to-the-browser-part-3)\n", "licenseText": "Copyright (c) 2017 Calvin <PERSON>f\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/public-encrypt/-/public-encrypt-4.0.3.tgz#4fcc9d77a07e48ba7527e7cbe0de33d0701331e0", "type": "tarball", "reference": "https://registry.yarnpkg.com/public-encrypt/-/public-encrypt-4.0.3.tgz", "hash": "4fcc9d77a07e48ba7527e7cbe0de33d0701331e0", "integrity": "sha512-zVpa8oKZSz5bTMTFClc1fQOnyyEzpl5ozpi1B5YcvBrdohMjH2rfsBtyXcuNuwjsDIXmBYlF2N5FlJYhR29t8Q==", "registry": "npm", "packageName": "public-encrypt", "cacheIntegrity": "sha512-zVpa8oKZSz5bTMTFClc1fQOnyyEzpl5ozpi1B5YcvBrdohMjH2rfsBtyXcuNuwjsDIXmBYlF2N5FlJYhR29t8Q== sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA="}, "registry": "npm", "hash": "4fcc9d77a07e48ba7527e7cbe0de33d0701331e0"}