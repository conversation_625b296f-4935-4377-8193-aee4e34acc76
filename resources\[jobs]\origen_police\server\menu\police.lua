local L0_1, L1_1, L2_1
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:SearchCitizen"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2
  L3_2 = {}
  L4_2 = Config
  L4_2 = L4_2.Framework
  if "qbcore" == L4_2 then
    L4_2 = ""
    L5_2 = A2_2.job
    if L5_2 then
      L5_2 = CanOpenTablet
      L6_2 = FW_GetPlayer
      L7_2 = A0_2
      L6_2 = L6_2(L7_2)
      L6_2 = L6_2.PlayerData
      L6_2 = L6_2.job
      L6_2 = L6_2.name
      L5_2 = L5_2(L6_2)
      L5_2 = L5_2[2]
      L6_2 = pairs
      L7_2 = Config
      L7_2 = L7_2.JobCategory
      L7_2 = L7_2[L5_2]
      L6_2, L7_2, L8_2, L9_2 = L6_2(L7_2)
      for L10_2, L11_2 in L6_2, L7_2, L8_2, L9_2 do
        L12_2 = L4_2
        if "" ~= L4_2 then
          L13_2 = "OR"
          if L13_2 then
            goto lbl_33
          end
        end
        L13_2 = ""
        ::lbl_33::
        L14_2 = " job LIKE '%\"name\":\""
        L15_2 = L11_2.name
        L16_2 = "\"%' "
        L12_2 = L12_2 .. L13_2 .. L14_2 .. L15_2 .. L16_2
        L4_2 = L12_2
      end
      L6_2 = L4_2
      L7_2 = "AND ("
      L8_2 = L6_2
      L9_2 = ")"
      L7_2 = L7_2 .. L8_2 .. L9_2
      L4_2 = L7_2
    end
    L5_2 = SearchCitizen
    L6_2 = A2_2.text
    L7_2 = L4_2
    L5_2 = L5_2(L6_2, L7_2)
    L6_2 = 1
    L7_2 = #L5_2
    L8_2 = 1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = json
      L10_2 = L10_2.decode
      L11_2 = L5_2[L9_2]
      L11_2 = L11_2.charinfo
      L10_2 = L10_2(L11_2)
      L11_2 = GetPhoneFromIdentifier
      L12_2 = L5_2[L9_2]
      L12_2 = L12_2.citizenid
      L11_2 = L11_2(L12_2)
      L12_2 = table
      L12_2 = L12_2.insert
      L13_2 = L3_2
      L14_2 = {}
      L15_2 = L5_2[L9_2]
      L15_2 = L15_2.citizenid
      L14_2.citizenid = L15_2
      L14_2.phone = L11_2
      L15_2 = L10_2.firstname
      L14_2.firstname = L15_2
      L15_2 = L10_2.lastname
      L14_2.lastname = L15_2
      L15_2 = L5_2[L9_2]
      L15_2 = L15_2.image
      L14_2.image = L15_2
      L12_2(L13_2, L14_2)
    end
  else
    L4_2 = Config
    L4_2 = L4_2.Framework
    if "esx" == L4_2 then
      L4_2 = ""
      L5_2 = A2_2.job
      if L5_2 then
        L5_2 = CanOpenTablet
        L6_2 = FW_GetPlayer
        L7_2 = A0_2
        L6_2 = L6_2(L7_2)
        L6_2 = L6_2.PlayerData
        L6_2 = L6_2.job
        L6_2 = L6_2.name
        L5_2 = L5_2(L6_2)
        L5_2 = L5_2[2]
        L6_2 = pairs
        L7_2 = Config
        L7_2 = L7_2.JobCategory
        L7_2 = L7_2[L5_2]
        L6_2, L7_2, L8_2, L9_2 = L6_2(L7_2)
        for L10_2, L11_2 in L6_2, L7_2, L8_2, L9_2 do
          L12_2 = L4_2
          if "" ~= L4_2 then
            L13_2 = "OR"
            if L13_2 then
              goto lbl_113
            end
          end
          L13_2 = ""
          ::lbl_113::
          L14_2 = " job LIKE '%\"name\":\""
          L15_2 = L11_2.name
          L16_2 = "\"%' "
          L12_2 = L12_2 .. L13_2 .. L14_2 .. L15_2 .. L16_2
          L4_2 = L12_2
        end
        L6_2 = L4_2
        L7_2 = "OR ("
        L8_2 = L6_2
        L9_2 = ")"
        L7_2 = L7_2 .. L8_2 .. L9_2
        L4_2 = L7_2
      end
      L5_2 = SearchCitizen
      L6_2 = A2_2.text
      L7_2 = L4_2
      L5_2 = L5_2(L6_2, L7_2)
      L6_2 = 1
      L7_2 = #L5_2
      L8_2 = 1
      for L9_2 = L6_2, L7_2, L8_2 do
        L10_2 = GetPhoneFromIdentifier
        L11_2 = L5_2[L9_2]
        L11_2 = L11_2.identifier
        L10_2 = L10_2(L11_2)
        L11_2 = table
        L11_2 = L11_2.insert
        L12_2 = L3_2
        L13_2 = {}
        L14_2 = L5_2[L9_2]
        L14_2 = L14_2.identifier
        L13_2.citizenid = L14_2
        L13_2.phone = L10_2
        L14_2 = L5_2[L9_2]
        L14_2 = L14_2.firstname
        L13_2.firstname = L14_2
        L14_2 = L5_2[L9_2]
        L14_2 = L14_2.lastname
        L13_2.lastname = L14_2
        L14_2 = L5_2[L9_2]
        L14_2 = L14_2.image
        L13_2.image = L14_2
        L11_2(L12_2, L13_2)
      end
    end
  end
  L4_2 = table
  L4_2 = L4_2.sort
  L5_2 = L3_2
  function L6_2(A0_3, A1_3)
    local L2_3, L3_3, L4_3
    L2_3 = A0_3.firstname
    L3_3 = A0_3.lastname
    L2_3 = L2_3 .. L3_3
    L3_3 = A1_3.firstname
    L4_3 = A1_3.lastname
    L3_3 = L3_3 .. L4_3
    L2_3 = L2_3 < L3_3
    return L2_3
  end
  L4_2(L5_2, L6_2)
  L4_2 = A1_2
  L5_2 = L3_2
  L4_2(L5_2)
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:GetCitizen"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  L3_2 = false
  L4_2 = CanOpenTablet
  L5_2 = A0_2
  L4_2 = L4_2(L5_2)
  L4_2 = L4_2[2]
  L5_2 = Config
  L5_2 = L5_2.Framework
  if "qbcore" == L5_2 then
    L5_2 = GetCitizen
    L6_2 = A2_2.citizenid
    L5_2 = L5_2(L6_2)
    L6_2 = L5_2[1]
    if L6_2 then
      L6_2 = json
      L6_2 = L6_2.decode
      L7_2 = L5_2[1]
      L7_2 = L7_2.charinfo
      L6_2 = L6_2(L7_2)
      L7_2 = json
      L7_2 = L7_2.decode
      L8_2 = L5_2[1]
      L8_2 = L8_2.job
      L7_2 = L7_2(L8_2)
      L8_2 = GetPhoneFromIdentifier
      L9_2 = L5_2[1]
      L9_2 = L9_2.citizenid
      L8_2 = L8_2(L9_2)
      L9_2 = {}
      L10_2 = MySQL
      L10_2 = L10_2.awaitQuery
      L11_2 = "SELECT * FROM origen_police_notes WHERE citizenid = ? ORDER BY id DESC"
      L12_2 = {}
      L13_2 = A2_2.citizenid
      L12_2[1] = L13_2
      L10_2 = L10_2(L11_2, L12_2)
      L9_2.notes = L10_2
      L10_2 = GetBillsFromCitizenID
      L11_2 = A2_2.citizenid
      L12_2 = L4_2
      L10_2 = L10_2(L11_2, L12_2)
      L9_2.bills = L10_2
      L10_2 = json
      L10_2 = L10_2.encode
      L11_2 = GetLicensesByIdentifier
      L12_2 = A2_2.citizenid
      L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L11_2(L12_2)
      L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
      L9_2.licenses = L10_2
      L10_2 = GetCitizenProperties
      L11_2 = L5_2[1]
      L11_2 = L11_2.citizenid
      L10_2 = L10_2(L11_2)
      L9_2.properties = L10_2
      L10_2 = GetWeapons
      L11_2 = A2_2.citizenid
      L12_2 = json
      L12_2 = L12_2.decode
      L13_2 = L5_2[1]
      L13_2 = L13_2.inventory
      L12_2, L13_2, L14_2, L15_2, L16_2, L17_2 = L12_2(L13_2)
      L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
      L9_2.weapons = L10_2
      L10_2 = MySQL
      L10_2 = L10_2.awaitQuery
      L11_2 = "SELECT id, title FROM origen_police_reports WHERE implicated LIKE @citizenid OR victims LIKE @citizenid ORDER BY id DESC"
      L12_2 = {}
      L13_2 = "%"
      L14_2 = A2_2.citizenid
      L15_2 = "%"
      L13_2 = L13_2 .. L14_2 .. L15_2
      L12_2["@citizenid"] = L13_2
      L10_2 = L10_2(L11_2, L12_2)
      L9_2.reports = L10_2
      L10_2 = GetVehiclesByIdentifier
      L11_2 = A2_2.citizenid
      L10_2 = L10_2(L11_2)
      L9_2.vehicles = L10_2
      L10_2 = L5_2[1]
      L10_2 = L10_2.citizenid
      L9_2.citizenid = L10_2
      L10_2 = L5_2[1]
      L10_2 = L10_2.image
      L9_2.image = L10_2
      L10_2 = L6_2.firstname
      L9_2.firstname = L10_2
      L10_2 = L6_2.lastname
      L9_2.lastname = L10_2
      L9_2.phone = L8_2
      L10_2 = GetPlayerBankNumber
      L11_2 = A2_2.citizenid
      L12_2 = L6_2.account
      L10_2 = L10_2(L11_2, L12_2)
      L9_2.iban = L10_2
      L10_2 = L5_2[1]
      L10_2 = L10_2.dangerous
      L9_2.dangerous = L10_2
      L10_2 = L5_2[1]
      L10_2 = L10_2.wanted
      L9_2.wanted = L10_2
      L10_2 = L6_2.birthdate
      L9_2.birthdate = L10_2
      L10_2 = L6_2.nationality
      L9_2.nationality = L10_2
      L10_2 = GetGender
      L11_2 = L6_2.gender
      L10_2 = L10_2(L11_2)
      L9_2.gender = L10_2
      L10_2 = L7_2.label
      L9_2.job = L10_2
      L10_2 = L7_2.grade
      L10_2 = L10_2.name
      L9_2.grade = L10_2
      L10_2 = GetTrackerFromCitizenid
      L11_2 = L5_2[1]
      L11_2 = L11_2.citizenid
      L10_2 = L10_2(L11_2)
      L9_2.ankle = L10_2
      L3_2 = L9_2
    end
  else
    L5_2 = Config
    L5_2 = L5_2.Framework
    if "esx" == L5_2 then
      L5_2 = GetCitizen
      L6_2 = A2_2.citizenid
      L5_2 = L5_2(L6_2)
      L6_2 = L5_2[1]
      if L6_2 then
        L6_2 = GetPhoneFromIdentifier
        L7_2 = L5_2[1]
        L7_2 = L7_2.identifier
        L6_2 = L6_2(L7_2)
        L7_2 = GetVehiclesByIdentifier
        L8_2 = A2_2.citizenid
        L7_2 = L7_2(L8_2)
        L8_2 = GetCitizenProperties
        L9_2 = L5_2[1]
        L9_2 = L9_2.identifier
        L8_2 = L8_2(L9_2)
        L9_2 = 1
        L10_2 = #L7_2
        L11_2 = 1
        for L12_2 = L9_2, L10_2, L11_2 do
          L13_2 = L7_2[L12_2]
          L14_2 = json
          L14_2 = L14_2.decode
          L15_2 = L7_2[L12_2]
          L15_2 = L15_2.vehicle
          L14_2 = L14_2(L15_2)
          L13_2.vehicle = L14_2
          L13_2 = L7_2[L12_2]
          L14_2 = L7_2[L12_2]
          L14_2 = L14_2.vehicle
          L14_2 = L14_2.model
          L13_2.hash = L14_2
          L13_2 = L7_2[L12_2]
          L13_2.garage = "Garage"
        end
        L9_2 = Framework
        L9_2 = L9_2.Shared
        L9_2 = L9_2.Jobs
        L10_2 = L5_2[1]
        L10_2 = L10_2.job
        L9_2 = L9_2[L10_2]
        L10_2 = "XXXXXXXXX"
        if L9_2 then
          L11_2 = L9_2.grades
          L12_2 = tostring
          L13_2 = L5_2[1]
          L13_2 = L13_2.job_grade
          L12_2 = L12_2(L13_2)
          L11_2 = L11_2[L12_2]
          if L11_2 then
            L11_2 = L9_2.grades
            L12_2 = tostring
            L13_2 = L5_2[1]
            L13_2 = L13_2.job_grade
            L12_2 = L12_2(L13_2)
            L11_2 = L11_2[L12_2]
            L10_2 = L11_2.label
          end
          L9_2 = L9_2.label
        else
          L9_2 = "XXXXXXXXX"
        end
        L11_2 = {}
        L12_2 = MySQL
        L12_2 = L12_2.awaitQuery
        L13_2 = "SELECT * FROM origen_police_notes WHERE citizenid = ? ORDER BY id DESC"
        L14_2 = {}
        L15_2 = A2_2.citizenid
        L14_2[1] = L15_2
        L12_2 = L12_2(L13_2, L14_2)
        L11_2.notes = L12_2
        L12_2 = GetBillsFromCitizenID
        L13_2 = A2_2.citizenid
        L14_2 = L4_2
        L12_2 = L12_2(L13_2, L14_2)
        L11_2.bills = L12_2
        L12_2 = json
        L12_2 = L12_2.encode
        L13_2 = GetLicensesByIdentifier
        L14_2 = A2_2.citizenid
        L13_2, L14_2, L15_2, L16_2, L17_2 = L13_2(L14_2)
        L12_2 = L12_2(L13_2, L14_2, L15_2, L16_2, L17_2)
        L11_2.licenses = L12_2
        L11_2.properties = L8_2
        L12_2 = MySQL
        L12_2 = L12_2.awaitQuery
        L13_2 = "SELECT id, title FROM origen_police_reports WHERE implicated LIKE @citizenid OR victims LIKE @citizenid ORDER BY id DESC"
        L14_2 = {}
        L15_2 = "%"
        L16_2 = A2_2.citizenid
        L17_2 = "%"
        L15_2 = L15_2 .. L16_2 .. L17_2
        L14_2["@citizenid"] = L15_2
        L12_2 = L12_2(L13_2, L14_2)
        L11_2.reports = L12_2
        L12_2 = L7_2 or L12_2
        if not L7_2 then
          L12_2 = {}
        end
        L11_2.vehicles = L12_2
        L12_2 = L5_2[1]
        L12_2 = L12_2.identifier
        L11_2.citizenid = L12_2
        L12_2 = GetWeapons
        L13_2 = A2_2.citizenid
        L14_2 = json
        L14_2 = L14_2.decode
        L15_2 = L5_2[1]
        L15_2 = L15_2.inventory
        L14_2, L15_2, L16_2, L17_2 = L14_2(L15_2)
        L12_2 = L12_2(L13_2, L14_2, L15_2, L16_2, L17_2)
        L11_2.weapons = L12_2
        L12_2 = L5_2[1]
        L12_2 = L12_2.image
        L11_2.image = L12_2
        L12_2 = L5_2[1]
        L12_2 = L12_2.firstname
        L11_2.firstname = L12_2
        L12_2 = L5_2[1]
        L12_2 = L12_2.lastname
        L11_2.lastname = L12_2
        L11_2.phone = L6_2
        L12_2 = GetPlayerBankNumber
        L13_2 = L5_2[1]
        L13_2 = L13_2.identifier
        L12_2 = L12_2(L13_2)
        L11_2.iban = L12_2
        L12_2 = L5_2[1]
        L12_2 = L12_2.dangerous
        L11_2.dangerous = L12_2
        L12_2 = L5_2[1]
        L12_2 = L12_2.wanted
        L11_2.wanted = L12_2
        L12_2 = L5_2[1]
        L12_2 = L12_2.dateofbirth
        L11_2.birthdate = L12_2
        L12_2 = GetPlayerNationality
        L13_2 = L5_2[1]
        L13_2 = L13_2.identifier
        L12_2 = L12_2(L13_2)
        L11_2.nationality = L12_2
        L12_2 = GetGender
        L13_2 = L5_2[1]
        L13_2 = L13_2.sex
        L12_2 = L12_2(L13_2)
        L11_2.gender = L12_2
        L11_2.job = L9_2
        L11_2.grade = L10_2
        L12_2 = GetTrackerFromCitizenid
        L13_2 = L5_2[1]
        L13_2 = L13_2.identifier
        L12_2 = L12_2(L13_2)
        L11_2.ankle = L12_2
        L3_2 = L11_2
      end
    end
  end
  L5_2 = A1_2
  L6_2 = L3_2
  L5_2(L6_2)
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:SearchVehicle"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  L3_2 = {}
  L4_2 = Config
  L4_2 = L4_2.Framework
  if "qbcore" == L4_2 then
    L4_2 = SearchDataFromVehicle
    L5_2 = A2_2.plate
    L4_2 = L4_2(L5_2)
    L5_2 = 1
    L6_2 = #L4_2
    L7_2 = 1
    for L8_2 = L5_2, L6_2, L7_2 do
      L9_2 = json
      L9_2 = L9_2.decode
      L10_2 = L4_2[L8_2]
      L10_2 = L10_2.charinfo
      L9_2 = L9_2(L10_2)
      if L9_2 then
        L10_2 = L9_2.firstname
        L11_2 = " "
        L12_2 = L9_2.lastname
        L13_2 = " ("
        L14_2 = L4_2[L8_2]
        L14_2 = L14_2.citizenid
        L15_2 = ")"
        L10_2 = L10_2 .. L11_2 .. L12_2 .. L13_2 .. L14_2 .. L15_2
        if L10_2 then
          goto lbl_32
        end
      end
      L10_2 = false
      ::lbl_32::
      if not L10_2 then
        L10_2 = "Unknown"
      end
      L11_2 = table
      L11_2 = L11_2.insert
      L12_2 = L3_2
      L13_2 = {}
      L14_2 = L4_2[L8_2]
      L14_2 = L14_2.hash
      L13_2.hash = L14_2
      L14_2 = L4_2[L8_2]
      L14_2 = L14_2.plate
      L13_2.plate = L14_2
      L13_2.owner = L10_2
      L14_2 = L4_2[L8_2]
      L14_2 = L14_2.garage
      if L14_2 then
        L14_2 = L4_2[L8_2]
        L14_2 = L14_2.garage
        if L14_2 then
          goto lbl_56
        end
      end
      L14_2 = "Outside"
      ::lbl_56::
      L13_2.garage = L14_2
      L11_2(L12_2, L13_2)
    end
  else
    L4_2 = Config
    L4_2 = L4_2.Framework
    if "esx" == L4_2 then
      L4_2 = SearchDataFromVehicle
      L5_2 = A2_2.plate
      L4_2 = L4_2(L5_2)
      L5_2 = 1
      L6_2 = #L4_2
      L7_2 = 1
      for L8_2 = L5_2, L6_2, L7_2 do
        L9_2 = L4_2[L8_2]
        L9_2 = L9_2.firstname
        if L9_2 then
          L9_2 = L4_2[L8_2]
          L9_2 = L9_2.firstname
          L10_2 = " "
          L11_2 = L4_2[L8_2]
          L11_2 = L11_2.lastname
          L12_2 = " ("
          L13_2 = L4_2[L8_2]
          L13_2 = L13_2.owner
          L14_2 = ")"
          L9_2 = L9_2 .. L10_2 .. L11_2 .. L12_2 .. L13_2 .. L14_2
          if L9_2 then
            goto lbl_88
          end
        end
        L9_2 = false
        ::lbl_88::
        if not L9_2 then
          L9_2 = "Unknown"
        end
        L10_2 = table
        L10_2 = L10_2.insert
        L11_2 = L3_2
        L12_2 = {}
        L13_2 = json
        L13_2 = L13_2.decode
        L14_2 = L4_2[L8_2]
        L14_2 = L14_2.vehicle
        L13_2 = L13_2(L14_2)
        L13_2 = L13_2.model
        L12_2.hash = L13_2
        L13_2 = L4_2[L8_2]
        L13_2 = L13_2.plate
        L12_2.plate = L13_2
        L12_2.owner = L9_2
        L12_2.garage = "Garage"
        L10_2(L11_2, L12_2)
      end
    end
  end
  L4_2 = A1_2
  L5_2 = L3_2
  L4_2(L5_2)
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:NewPoliceNote"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L3_2 = FW_GetPlayer
  L4_2 = A0_2
  L3_2 = L3_2(L4_2)
  L4_2 = L3_2.PlayerData
  L4_2 = L4_2.charinfo
  L4_2 = L4_2.firstname
  L5_2 = " "
  L6_2 = L3_2.PlayerData
  L6_2 = L6_2.charinfo
  L6_2 = L6_2.lastname
  L7_2 = " ("
  L8_2 = L3_2.PlayerData
  L8_2 = L8_2.metadata
  L8_2 = L8_2.police_badge
  if not L8_2 then
    L8_2 = "0000"
  end
  L9_2 = ")"
  L4_2 = L4_2 .. L5_2 .. L6_2 .. L7_2 .. L8_2 .. L9_2
  L5_2 = MySQL
  L5_2 = L5_2.insert
  L6_2 = "INSERT INTO origen_police_notes (citizenid, title, description, author) VALUES (?, ?, ?, ?)"
  L7_2 = {}
  L8_2 = A2_2.citizenid
  L9_2 = A2_2.police
  if L9_2 then
    L9_2 = "-police"
    if L9_2 then
      goto lbl_33
    end
  end
  L9_2 = ""
  ::lbl_33::
  L8_2 = L8_2 .. L9_2
  L9_2 = A2_2.noteTitle
  L10_2 = A2_2.noteText
  L11_2 = L4_2
  L7_2[1] = L8_2
  L7_2[2] = L9_2
  L7_2[3] = L10_2
  L7_2[4] = L11_2
  function L8_2(A0_3)
    local L1_3, L2_3, L3_3, L4_3, L5_3, L6_3, L7_3, L8_3
    if A0_3 then
      L1_3 = A1_2
      L2_3 = {}
      L2_3.id = A0_3
      L3_3 = L4_2
      L2_3.author = L3_3
      L3_3 = os
      L3_3 = L3_3.time
      L3_3 = L3_3()
      L2_3.date = L3_3
      L1_3(L2_3)
      L1_3 = CreateLog
      L2_3 = {}
      L2_3.type = "Notes"
      L3_3 = {}
      L4_3 = Config
      L4_3 = L4_3.LogsTranslations
      L4_3 = L4_3.CreateNote
      L4_3 = L4_3.title
      L3_3.title = L4_3
      L4_3 = Config
      L4_3 = L4_3.LogsTranslations
      L4_3 = L4_3.CreateNote
      L4_3 = L4_3.message
      L5_3 = L4_3
      L4_3 = L4_3.format
      L6_3 = A2_2.noteTitle
      L7_3 = A2_2.noteText
      L8_3 = L4_2
      L4_3 = L4_3(L5_3, L6_3, L7_3, L8_3)
      L3_3.description = L4_3
      L3_3.color = 1791423
      L2_3.embed = L3_3
      L3_3 = A0_2
      L2_3.source = L3_3
      L1_3(L2_3)
    else
      L1_3 = TriggerClientEvent
      L2_3 = "origen_police:ShowNotification"
      L3_3 = A0_2
      L4_3 = Config
      L4_3 = L4_3.Translations
      L4_3 = L4_3.ProblemCreateNote
      L1_3(L2_3, L3_3, L4_3)
      L1_3 = A1_2
      L2_3 = false
      L1_3(L2_3)
    end
  end
  L5_2(L6_2, L7_2, L8_2)
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:UpdatePoliceNote"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L3_2 = false
  L4_2 = A2_2.type
  if "pin" == L4_2 then
    L4_2 = MySQL
    L4_2 = L4_2.awaitUpdate
    L5_2 = "UPDATE origen_police_notes SET fixed = 1 WHERE id = ?"
    L6_2 = {}
    L7_2 = A2_2.noteid
    L6_2[1] = L7_2
    L4_2 = L4_2(L5_2, L6_2)
    L3_2 = L4_2 > 0
  else
    L4_2 = A2_2.type
    if "unpin" == L4_2 then
      L4_2 = MySQL
      L4_2 = L4_2.awaitUpdate
      L5_2 = "UPDATE origen_police_notes SET fixed = 0 WHERE id = ?"
      L6_2 = {}
      L7_2 = A2_2.noteid
      L6_2[1] = L7_2
      L4_2 = L4_2(L5_2, L6_2)
      L3_2 = L4_2 > 0
    else
      L4_2 = A2_2.type
      if "delete" == L4_2 then
        L4_2 = MySQL
        L4_2 = L4_2.awaitQuery
        L5_2 = "DELETE FROM origen_police_notes WHERE id = ?"
        L6_2 = {}
        L7_2 = A2_2.noteid
        L6_2[1] = L7_2
        L4_2 = L4_2(L5_2, L6_2)
        L4_2 = L4_2.affectedRows
        L3_2 = L4_2 > 0
        L4_2 = CreateLog
        L5_2 = {}
        L5_2.type = "Notes"
        L6_2 = {}
        L7_2 = Config
        L7_2 = L7_2.LogsTranslations
        L7_2 = L7_2.DeleteNote
        L7_2 = L7_2.title
        L6_2.title = L7_2
        L7_2 = Config
        L7_2 = L7_2.LogsTranslations
        L7_2 = L7_2.DeleteNote
        L7_2 = L7_2.message
        L8_2 = L7_2
        L7_2 = L7_2.format
        L9_2 = A2_2.noteid
        L7_2 = L7_2(L8_2, L9_2)
        L6_2.description = L7_2
        L6_2.color = 1791423
        L5_2.embed = L6_2
        L5_2.source = A0_2
        L4_2(L5_2)
      end
    end
  end
  L4_2 = A1_2
  L5_2 = L3_2
  L4_2(L5_2)
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:UpdateCitizenStatus"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L3_2 = Config
  L3_2 = L3_2.Framework
  if "qbcore" == L3_2 then
    L3_2 = A1_2
    L4_2 = MySQL
    L4_2 = L4_2.awaitUpdate
    L5_2 = "UPDATE players SET "
    L6_2 = A2_2.column
    L7_2 = " = ? WHERE citizenid = ?"
    L5_2 = L5_2 .. L6_2 .. L7_2
    L6_2 = {}
    L7_2 = A2_2.value
    L8_2 = A2_2.citizenid
    L6_2[1] = L7_2
    L6_2[2] = L8_2
    L4_2 = L4_2(L5_2, L6_2)
    L4_2 = L4_2 > 0
    L3_2(L4_2)
  else
    L3_2 = Config
    L3_2 = L3_2.Framework
    if "esx" == L3_2 then
      L3_2 = A1_2
      L4_2 = MySQL
      L4_2 = L4_2.awaitUpdate
      L5_2 = "UPDATE users SET "
      L6_2 = A2_2.column
      L7_2 = " = ? WHERE identifier = ?"
      L5_2 = L5_2 .. L6_2 .. L7_2
      L6_2 = {}
      L7_2 = A2_2.value
      L8_2 = A2_2.citizenid
      L6_2[1] = L7_2
      L6_2[2] = L8_2
      L4_2 = L4_2(L5_2, L6_2)
      L4_2 = L4_2 > 0
      L3_2(L4_2)
    end
  end
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:UpdateCitizenImage"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L3_2 = Config
  L3_2 = L3_2.Framework
  if "qbcore" == L3_2 then
    L3_2 = A1_2
    L4_2 = MySQL
    L4_2 = L4_2.awaitUpdate
    L5_2 = "UPDATE players SET image = ? WHERE citizenid = ?"
    L6_2 = {}
    L7_2 = A2_2.value
    L8_2 = A2_2.citizenid
    L6_2[1] = L7_2
    L6_2[2] = L8_2
    L4_2 = L4_2(L5_2, L6_2)
    L4_2 = L4_2 > 0
    L3_2(L4_2)
  else
    L3_2 = Config
    L3_2 = L3_2.Framework
    if "esx" == L3_2 then
      L3_2 = A1_2
      L4_2 = MySQL
      L4_2 = L4_2.awaitUpdate
      L5_2 = "UPDATE users SET image = ? WHERE identifier = ?"
      L6_2 = {}
      L7_2 = A2_2.value
      L8_2 = A2_2.citizenid
      L6_2[1] = L7_2
      L6_2[2] = L8_2
      L4_2 = L4_2(L5_2, L6_2)
      L4_2 = L4_2 > 0
      L3_2(L4_2)
    end
  end
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:NewReport"
function L2_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L2_2 = FW_GetPlayer
  L3_2 = A0_2
  L2_2 = L2_2(L3_2)
  L3_2 = os
  L3_2 = L3_2.time
  L3_2 = L3_2()
  L3_2 = L3_2 * 1000
  L4_2 = L2_2.PlayerData
  L4_2 = L4_2.charinfo
  L4_2 = L4_2.firstname
  L5_2 = " "
  L6_2 = L2_2.PlayerData
  L6_2 = L6_2.charinfo
  L6_2 = L6_2.lastname
  L7_2 = " ("
  L8_2 = L2_2.PlayerData
  L8_2 = L8_2.metadata
  L8_2 = L8_2.police_badge
  if not L8_2 then
    L8_2 = "0000"
  end
  L9_2 = ")"
  L4_2 = L4_2 .. L5_2 .. L6_2 .. L7_2 .. L8_2 .. L9_2
  L5_2 = exports
  L5_2 = L5_2.origen_police
  L6_2 = L5_2
  L5_2 = L5_2.CanOpenTablet
  L7_2 = L2_2.PlayerData
  L7_2 = L7_2.job
  L7_2 = L7_2.name
  L5_2 = L5_2(L6_2, L7_2)
  L5_2 = L5_2[2]
  L6_2 = MySQL
  L6_2 = L6_2.insert
  L7_2 = "INSERT INTO origen_police_reports (title, author, job) VALUES (?, ?, ?)"
  L8_2 = {}
  L9_2 = exports
  L9_2 = L9_2.origen_police
  L10_2 = L9_2
  L9_2 = L9_2.GetConfig
  L11_2 = "Translations"
  L9_2 = L9_2(L10_2, L11_2)
  L9_2 = L9_2.IntroduceName
  L10_2 = L4_2
  L11_2 = L5_2
  L8_2[1] = L9_2
  L8_2[2] = L10_2
  L8_2[3] = L11_2
  function L9_2(A0_3)
    local L1_3, L2_3, L3_3, L4_3
    if A0_3 then
      L1_3 = A1_2
      L2_3 = {}
      L2_3.id = A0_3
      L3_3 = L3_2
      L2_3.date = L3_3
      L3_3 = L4_2
      L2_3.author = L3_3
      L1_3(L2_3)
    else
      L1_3 = TriggerClientEvent
      L2_3 = "origen_police:ShowNotification"
      L3_3 = A0_2
      L4_3 = Config
      L4_3 = L4_3.Translations
      L4_3 = L4_3.ProblemCreateReport
      L1_3(L2_3, L3_3, L4_3)
      L1_3 = A1_2
      L2_3 = false
      L1_3(L2_3)
    end
  end
  L6_2(L7_2, L8_2, L9_2)
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:Get100Reports"
function L2_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L2_2 = exports
  L2_2 = L2_2.origen_police
  L3_2 = L2_2
  L2_2 = L2_2.CanOpenTablet
  L4_2 = FW_GetPlayer
  L5_2 = A0_2
  L4_2 = L4_2(L5_2)
  L4_2 = L4_2.PlayerData
  L4_2 = L4_2.job
  L4_2 = L4_2.name
  L2_2 = L2_2(L3_2, L4_2)
  L2_2 = L2_2[2]
  L3_2 = A1_2
  L4_2 = MySQL
  L4_2 = L4_2.awaitQuery
  L5_2 = "SELECT id, title, author, date, tags FROM origen_police_reports WHERE job = ? ORDER BY id DESC LIMIT 100"
  L6_2 = {}
  L7_2 = L2_2
  L6_2[1] = L7_2
  L4_2, L5_2, L6_2, L7_2 = L4_2(L5_2, L6_2)
  L3_2(L4_2, L5_2, L6_2, L7_2)
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:SearchReport"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L3_2 = {}
  L4_2 = exports
  L4_2 = L4_2.origen_police
  L5_2 = L4_2
  L4_2 = L4_2.CanOpenTablet
  L6_2 = FW_GetPlayer
  L7_2 = A0_2
  L6_2 = L6_2(L7_2)
  L6_2 = L6_2.PlayerData
  L6_2 = L6_2.job
  L6_2 = L6_2.name
  L4_2 = L4_2(L5_2, L6_2)
  L4_2 = L4_2[2]
  L5_2 = A2_2.tags
  if L5_2 then
    L5_2 = A2_2.text
    if "" ~= L5_2 then
      L5_2 = MySQL
      L5_2 = L5_2.awaitQuery
      L6_2 = "SELECT id, title, author, date, tags FROM origen_police_reports WHERE (id LIKE @text OR title LIKE @text OR author LIKE @text) AND job = @job AND tags LIKE @tags ORDER BY id DESC LIMIT 100"
      L7_2 = {}
      L8_2 = "%"
      L9_2 = A2_2.text
      L10_2 = "%"
      L8_2 = L8_2 .. L9_2 .. L10_2
      L7_2["@text"] = L8_2
      L8_2 = "%"
      L9_2 = A2_2.tags
      L10_2 = "%"
      L8_2 = L8_2 .. L9_2 .. L10_2
      L7_2["@tags"] = L8_2
      L7_2["@job"] = L4_2
      L5_2 = L5_2(L6_2, L7_2)
      L3_2 = L5_2
    else
      L5_2 = MySQL
      L5_2 = L5_2.awaitQuery
      L6_2 = "SELECT id, title, author, date, tags FROM origen_police_reports WHERE job = @job AND tags LIKE @tags ORDER BY id DESC LIMIT 100"
      L7_2 = {}
      L7_2["@job"] = L4_2
      L8_2 = "%"
      L9_2 = A2_2.tags
      L10_2 = "%"
      L8_2 = L8_2 .. L9_2 .. L10_2
      L7_2["@tags"] = L8_2
      L5_2 = L5_2(L6_2, L7_2)
      L3_2 = L5_2
    end
  else
    L5_2 = MySQL
    L5_2 = L5_2.awaitQuery
    L6_2 = "SELECT id, title, author, date, tags FROM origen_police_reports WHERE job = @job AND (id LIKE @text OR title LIKE @text OR author LIKE @text) ORDER BY id DESC LIMIT 100"
    L7_2 = {}
    L7_2["@job"] = L4_2
    L8_2 = "%"
    L9_2 = A2_2.text
    L10_2 = "%"
    L8_2 = L8_2 .. L9_2 .. L10_2
    L7_2["@text"] = L8_2
    L5_2 = L5_2(L6_2, L7_2)
    L3_2 = L5_2
  end
  L5_2 = A1_2
  L6_2 = L3_2
  L5_2(L6_2)
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:GetReport"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2
  L3_2 = MySQL
  L3_2 = L3_2.awaitQuery
  L4_2 = "SELECT * FROM origen_police_reports WHERE id = ?"
  L5_2 = {}
  L6_2 = A2_2.reportid
  L5_2[1] = L6_2
  L3_2 = L3_2(L4_2, L5_2)
  if L3_2 then
    L4_2 = L3_2[1]
    if L4_2 then
      L4_2 = {}
      L5_2 = GetBillsFromReporter
      L6_2 = A2_2.reportid
      L5_2 = L5_2(L6_2)
      L6_2 = pairs
      L7_2 = L5_2
      L6_2, L7_2, L8_2, L9_2 = L6_2(L7_2)
      for L10_2, L11_2 in L6_2, L7_2, L8_2, L9_2 do
        L12_2 = L11_2.citizenid
        L13_2 = {}
        L14_2 = L11_2.concepts
        L13_2.bills = L14_2
        L14_2 = L11_2.price
        L13_2.price = L14_2
        L14_2 = L11_2.months
        L13_2.months = L14_2
        L4_2[L12_2] = L13_2
      end
      L6_2 = L3_2[1]
      L7_2 = json
      L7_2 = L7_2.decode
      L8_2 = L3_2[1]
      L8_2 = L8_2.implicated
      L7_2 = L7_2(L8_2)
      L6_2.implicated = L7_2
      L6_2 = 1
      L7_2 = L3_2[1]
      L7_2 = L7_2.implicated
      L7_2 = #L7_2
      L8_2 = 1
      for L9_2 = L6_2, L7_2, L8_2 do
        L10_2 = L3_2[1]
        L10_2 = L10_2.implicated
        L10_2 = L10_2[L9_2]
        L11_2 = L3_2[1]
        L11_2 = L11_2.implicated
        L11_2 = L11_2[L9_2]
        L11_2 = L11_2.citizenid
        L11_2 = L4_2[L11_2]
        if not L11_2 then
          L11_2 = {}
        end
        L10_2.bills = L11_2
      end
      L6_2 = L3_2[1]
      L7_2 = json
      L7_2 = L7_2.encode
      L8_2 = L3_2[1]
      L8_2 = L8_2.implicated
      L7_2 = L7_2(L8_2)
      L6_2.implicated = L7_2
      L6_2 = A1_2
      L7_2 = L3_2[1]
      L6_2(L7_2)
  end
  else
    L4_2 = A1_2
    L5_2 = false
    L4_2(L5_2)
  end
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:UpdateReport"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2
  L3_2 = A0_2
  L4_2 = A2_2.key
  if "implicated" == L4_2 then
    L4_2 = true
    L5_2 = 1
    L6_2 = A2_2.value
    L6_2 = #L6_2
    L7_2 = 1
    for L8_2 = L5_2, L6_2, L7_2 do
      L9_2 = A2_2.value
      L9_2 = L9_2[L8_2]
      L9_2 = L9_2.bills
      if L9_2 then
        L9_2 = A2_2.value
        L9_2 = L9_2[L8_2]
        L9_2 = L9_2.billid
        if L9_2 then
          L9_2 = A2_2.value
          L9_2 = L9_2[L8_2]
          L9_2 = L9_2.billid
          if "none" ~= L9_2 then
            goto lbl_62
          end
        end
        L9_2 = FW_GetPlayer
        L10_2 = L3_2
        L9_2 = L9_2(L10_2)
        L10_2 = L9_2.PlayerData
        L10_2 = L10_2.charinfo
        L10_2 = L10_2.firstname
        L11_2 = " "
        L12_2 = L9_2.PlayerData
        L12_2 = L12_2.charinfo
        L12_2 = L12_2.lastname
        L13_2 = " ("
        L14_2 = L9_2.PlayerData
        L14_2 = L14_2.metadata
        L14_2 = L14_2.police_badge
        if not L14_2 then
          L14_2 = "0000"
        end
        L15_2 = ")"
        L10_2 = L10_2 .. L11_2 .. L12_2 .. L13_2 .. L14_2 .. L15_2
        L11_2 = CanOpenTablet
        L12_2 = L9_2.PlayerData
        L12_2 = L12_2.job
        L12_2 = L12_2.name
        L11_2 = L11_2(L12_2)
        L11_2 = L11_2[2]
        L12_2 = NewBillReport
        L13_2 = L8_2
        L14_2 = A2_2
        L15_2 = L10_2
        L16_2 = L11_2
        L12_2 = L12_2(L13_2, L14_2, L15_2, L16_2)
        L4_2 = L12_2
        L12_2 = A2_2.value
        L12_2 = L12_2[L8_2]
        L12_2.billid = L4_2
      ::lbl_62::
      else
        L9_2 = A2_2.value
        L9_2 = L9_2[L8_2]
        L9_2 = L9_2.bills
        if L9_2 then
          L9_2 = A2_2.value
          L9_2 = L9_2[L8_2]
          L9_2 = L9_2.billid
          if L9_2 then
            L9_2 = A2_2.value
            L9_2 = L9_2[L8_2]
            L9_2 = L9_2.billid
            if "none" ~= L9_2 then
              L9_2 = UpdateBillReport
              L10_2 = L8_2
              L11_2 = A2_2
              L9_2 = L9_2(L10_2, L11_2)
              L4_2 = L9_2
            end
          end
        end
      end
      L9_2 = A2_2.value
      L9_2 = L9_2[L8_2]
      L9_2.bills = nil
      L9_2 = A2_2.value
      L9_2 = L9_2[L8_2]
      L9_2.price = nil
      L9_2 = A2_2.value
      L9_2 = L9_2[L8_2]
      L9_2.months = nil
    end
    L5_2 = A2_2.deletedbill
    if L5_2 then
      L5_2 = DeleteBill
      L6_2 = A0_2
      L7_2 = {}
      L8_2 = A2_2.deletedbill
      L7_2.billid = L8_2
      L5_2(L6_2, L7_2)
    end
    L5_2 = MySQL
    L5_2 = L5_2.update
    L6_2 = "UPDATE origen_police_reports SET implicated = ? WHERE id = ?"
    L7_2 = {}
    L8_2 = json
    L8_2 = L8_2.encode
    L9_2 = A2_2.value
    L8_2 = L8_2(L9_2)
    L9_2 = A2_2.reportid
    L7_2[1] = L8_2
    L7_2[2] = L9_2
    function L8_2(A0_3)
      local L1_3, L2_3
      L1_3 = A1_2
      if A0_3 > 0 then
        L2_3 = L4_2
        if L2_3 then
          goto lbl_8
        end
      end
      L2_3 = false
      ::lbl_8::
      L1_3(L2_3)
    end
    L5_2(L6_2, L7_2, L8_2)
  else
    L4_2 = MySQL
    L4_2 = L4_2.update
    L5_2 = "UPDATE origen_police_reports SET `"
    L6_2 = A2_2.key
    L7_2 = "` = ? WHERE id = ?"
    L5_2 = L5_2 .. L6_2 .. L7_2
    L6_2 = {}
    L7_2 = A2_2.value
    L8_2 = A2_2.reportid
    L6_2[1] = L7_2
    L6_2[2] = L8_2
    function L7_2(A0_3)
      local L1_3, L2_3
      L1_3 = A1_2
      L2_3 = A0_3 > 0
      L1_3(L2_3)
    end
    L4_2(L5_2, L6_2, L7_2)
  end
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:DeleteReport"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2
  L3_2 = A1_2
  L4_2 = MySQL
  L4_2 = L4_2.awaitQuery
  L5_2 = "DELETE FROM origen_police_reports WHERE id = ?"
  L6_2 = {}
  L7_2 = A2_2.reportid
  L6_2[1] = L7_2
  L4_2 = L4_2(L5_2, L6_2)
  L4_2 = L4_2.affectedRows
  L4_2 = L4_2 > 0
  L3_2(L4_2)
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:SendBill"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2
  L3_2 = A1_2
  L4_2 = sendBill
  L5_2 = A0_2
  L6_2 = A2_2
  L4_2, L5_2, L6_2 = L4_2(L5_2, L6_2)
  L3_2(L4_2, L5_2, L6_2)
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:DeleteBill"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2
  L3_2 = A1_2
  L4_2 = DeleteBill
  L5_2 = A0_2
  L6_2 = A2_2
  L4_2, L5_2, L6_2 = L4_2(L5_2, L6_2)
  L3_2(L4_2, L5_2, L6_2)
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:SetPoliceBadge"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2
  L3_2 = FW_GetPlayerFromCitizenid
  L4_2 = A2_2.citizenid
  L3_2 = L3_2(L4_2)
  L4_2 = string
  L4_2 = L4_2.len
  L5_2 = A2_2.badge
  L4_2 = L4_2(L5_2)
  L5_2 = exports
  L5_2 = L5_2.origen_police
  L6_2 = L5_2
  L5_2 = L5_2.GetConfig
  L7_2 = "PoliceBadgeLength"
  L5_2 = L5_2(L6_2, L7_2)
  if L4_2 > L5_2 then
    L4_2 = A2_2.badge
    L5_2 = L4_2
    L4_2 = L4_2.sub
    L6_2 = 2
    L7_2 = -1
    L4_2 = L4_2(L5_2, L6_2, L7_2)
    A2_2.badge = L4_2
  else
    L4_2 = string
    L4_2 = L4_2.len
    L5_2 = A2_2.badge
    L4_2 = L4_2(L5_2)
    L5_2 = exports
    L5_2 = L5_2.origen_police
    L6_2 = L5_2
    L5_2 = L5_2.GetConfig
    L7_2 = "PoliceBadgeLength"
    L5_2 = L5_2(L6_2, L7_2)
    if L4_2 < L5_2 then
      L4_2 = "0"
      L5_2 = A2_2.badge
      L4_2 = L4_2 .. L5_2
      A2_2.badge = L4_2
    end
  end
  L4_2 = L3_2.Functions
  L4_2 = L4_2.SetMetaData
  L5_2 = "police_badge"
  L6_2 = A2_2.badge
  L4_2(L5_2, L6_2)
  L4_2 = CreateLog
  L5_2 = {}
  L5_2.type = "Management"
  L6_2 = {}
  L7_2 = Config
  L7_2 = L7_2.LogsTranslations
  L7_2 = L7_2.SetBadge
  L7_2 = L7_2.title
  L6_2.title = L7_2
  L7_2 = Config
  L7_2 = L7_2.LogsTranslations
  L7_2 = L7_2.SetBadge
  L7_2 = L7_2.message
  L8_2 = L7_2
  L7_2 = L7_2.format
  L9_2 = L3_2.PlayerData
  L9_2 = L9_2.charinfo
  L9_2 = L9_2.firstname
  L10_2 = " "
  L11_2 = L3_2.PlayerData
  L11_2 = L11_2.charinfo
  L11_2 = L11_2.lastname
  L12_2 = " ("
  L13_2 = A2_2.badge
  if not L13_2 then
    L13_2 = "0000"
  end
  L14_2 = ")"
  L9_2 = L9_2 .. L10_2 .. L11_2 .. L12_2 .. L13_2 .. L14_2
  L10_2 = A2_2.badge
  L7_2 = L7_2(L8_2, L9_2, L10_2)
  L6_2.description = L7_2
  L6_2.color = 1791423
  L5_2.embed = L6_2
  L5_2.source = A0_2
  L4_2(L5_2)
  L4_2 = A1_2
  L5_2 = true
  L4_2(L5_2)
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:GetPoliceList"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2
  L3_2 = {}
  L4_2 = {}
  L5_2 = {}
  L6_2 = FW_GetPlayer
  L7_2 = A0_2
  L6_2 = L6_2(L7_2)
  L6_2 = L6_2.PlayerData
  L6_2 = L6_2.job
  L6_2 = L6_2.name
  L7_2 = exports
  L7_2 = L7_2.origen_police
  L8_2 = L7_2
  L7_2 = L7_2.CanOpenTablet
  L9_2 = L6_2
  L7_2 = L7_2(L8_2, L9_2)
  L7_2 = L7_2[2]
  L8_2 = exports
  L8_2 = L8_2.origen_police
  L9_2 = L8_2
  L8_2 = L8_2.GetConfig
  L10_2 = "JobCategory"
  L8_2 = L8_2(L9_2, L10_2)
  L8_2 = L8_2[L7_2]
  L9_2 = Config
  L9_2 = L9_2.Framework
  if "qbcore" == L9_2 then
    L9_2 = pairs
    L10_2 = L8_2
    L9_2, L10_2, L11_2, L12_2 = L9_2(L10_2)
    for L13_2, L14_2 in L9_2, L10_2, L11_2, L12_2 do
      L15_2 = Framework
      L15_2 = L15_2.Functions
      L15_2 = L15_2.GetPlayersOnDuty
      L16_2 = L14_2.name
      L15_2, L16_2 = L15_2(L16_2)
      L17_2 = pairs
      L18_2 = L15_2
      L17_2, L18_2, L19_2, L20_2 = L17_2(L18_2)
      for L21_2, L22_2 in L17_2, L18_2, L19_2, L20_2 do
        L23_2 = table
        L23_2 = L23_2.insert
        L24_2 = L5_2
        L25_2 = L22_2
        L23_2(L24_2, L25_2)
      end
    end
  else
    L9_2 = Config
    L9_2 = L9_2.Framework
    if "esx" == L9_2 then
      if nil == L8_2 then
        L9_2 = A1_2
        L10_2 = {}
        L9_2(L10_2)
        L9_2 = print
        L10_2 = "CANT FIND JOBS IN CONFIG WITH CATEGORY: "
        L11_2 = L7_2
        L9_2(L10_2, L11_2)
      end
      L9_2 = GetResourceState
      L10_2 = "origen_police"
      L9_2 = L9_2(L10_2)
      if "started" == L9_2 then
        L9_2 = exports
        L9_2 = L9_2.origen_police
        L10_2 = L9_2
        L9_2 = L9_2.GetPlayersInDuty
        L11_2 = L7_2
        L9_2 = L9_2(L10_2, L11_2)
        L5_2 = L9_2 or L5_2
      end
      if not L9_2 then
        L9_2 = {}
        L5_2 = L9_2
      end
    else
      L9_2 = A1_2
      L10_2 = {}
      L9_2(L10_2)
      L9_2 = print
      L10_2 = "CANT FIND FRAMEWORK IN CONFIG"
      L9_2(L10_2)
    end
  end
  L9_2 = 1
  L10_2 = #L5_2
  L11_2 = 1
  for L12_2 = L9_2, L10_2, L11_2 do
    L13_2 = FW_GetPlayer
    L14_2 = L5_2[L12_2]
    L13_2 = L13_2(L14_2)
    if L13_2 then
      L14_2 = L13_2.PlayerData
      if L14_2 then
        L14_2 = MySQL
        L14_2 = L14_2.awaitQuery
        L15_2 = "SELECT image FROM "
        L16_2 = Config
        L16_2 = L16_2.Framework
        if "qbcore" == L16_2 then
          L16_2 = "players"
          if L16_2 then
            goto lbl_114
          end
        end
        L16_2 = "users"
        ::lbl_114::
        L17_2 = " WHERE "
        L18_2 = Config
        L18_2 = L18_2.Framework
        if "qbcore" == L18_2 then
          L18_2 = "citizenid"
          if L18_2 then
            goto lbl_123
          end
        end
        L18_2 = "identifier"
        ::lbl_123::
        L19_2 = " = ?"
        L15_2 = L15_2 .. L16_2 .. L17_2 .. L18_2 .. L19_2
        L16_2 = {}
        L17_2 = L13_2.PlayerData
        L17_2 = L17_2.citizenid
        L16_2[1] = L17_2
        L14_2 = L14_2(L15_2, L16_2)
        L15_2 = L14_2[1]
        if L15_2 then
          L15_2 = L14_2[1]
          L14_2 = L15_2.image
        else
          L14_2 = nil
        end
        L15_2 = ""
        L16_2 = pairs
        L17_2 = L8_2
        L16_2, L17_2, L18_2, L19_2 = L16_2(L17_2)
        for L20_2, L21_2 in L16_2, L17_2, L18_2, L19_2 do
          L22_2 = L21_2.name
          L23_2 = L13_2.PlayerData
          L23_2 = L23_2.job
          L23_2 = L23_2.name
          if L22_2 == L23_2 then
            L15_2 = L21_2.titleListLabel
          end
        end
        L16_2 = table
        L16_2 = L16_2.insert
        L17_2 = L3_2
        L18_2 = {}
        L19_2 = L13_2.PlayerData
        L19_2 = L19_2.citizenid
        L18_2.citizenid = L19_2
        L19_2 = L13_2.PlayerData
        L19_2 = L19_2.charinfo
        L19_2 = L19_2.firstname
        L18_2.firstname = L19_2
        L19_2 = L13_2.PlayerData
        L19_2 = L19_2.charinfo
        L19_2 = L19_2.lastname
        L18_2.lastname = L19_2
        L18_2.image = L14_2
        L19_2 = L13_2.PlayerData
        L19_2 = L19_2.metadata
        if nil == L19_2 then
          L19_2 = {}
          if L19_2 then
            goto lbl_184
          end
        end
        L19_2 = L13_2.PlayerData
        L19_2 = L19_2.metadata
        L19_2 = L19_2.police_badge
        if not L19_2 then
          L19_2 = "0000"
        end
        ::lbl_184::
        L18_2.badge = L19_2
        L19_2 = L13_2.PlayerData
        L19_2 = L19_2.job
        L19_2 = L19_2.grade
        L19_2 = L19_2.name
        L18_2.grade = L19_2
        L19_2 = L13_2.PlayerData
        L19_2 = L19_2.job
        L19_2 = L19_2.name
        L18_2.job = L19_2
        L19_2 = L15_2 or L19_2
        if not L15_2 then
          L19_2 = "x List"
        end
        L18_2.titleLabel = L19_2
        L16_2(L17_2, L18_2)
        L16_2 = L13_2.PlayerData
        L16_2 = L16_2.citizenid
        L4_2[L16_2] = true
      end
    end
  end
  L9_2 = Config
  L9_2 = L9_2.Framework
  if "qbcore" == L9_2 then
    L9_2 = exports
    L9_2 = L9_2.origen_police
    L10_2 = L9_2
    L9_2 = L9_2.GetConfig
    L11_2 = "JobCategory"
    L9_2 = L9_2(L10_2, L11_2)
    L9_2 = L9_2[L7_2]
    L10_2 = ""
    L11_2 = pairs
    L12_2 = L9_2
    L11_2, L12_2, L13_2, L14_2 = L11_2(L12_2)
    for L15_2, L16_2 in L11_2, L12_2, L13_2, L14_2 do
      L17_2 = L10_2
      L18_2 = "job LIKE '%"
      L19_2 = L16_2.name
      L20_2 = "%' OR "
      L17_2 = L17_2 .. L18_2 .. L19_2 .. L20_2
      L10_2 = L17_2
    end
    L12_2 = L10_2
    L11_2 = L10_2.sub
    L13_2 = 1
    L14_2 = -5
    L11_2 = L11_2(L12_2, L13_2, L14_2)
    L10_2 = L11_2
    L11_2 = GetPoliceList
    L12_2 = L10_2
    L11_2 = L11_2(L12_2)
    L12_2 = 1
    L13_2 = #L11_2
    L14_2 = 1
    for L15_2 = L12_2, L13_2, L14_2 do
      L16_2 = L11_2[L15_2]
      L16_2 = L16_2.citizenid
      L16_2 = L4_2[L16_2]
      if not L16_2 then
        L16_2 = FW_GetPlayerFromCitizenid
        L17_2 = L11_2[L15_2]
        L17_2 = L17_2.citizenid
        L16_2 = L16_2(L17_2)
        if L16_2 then
          L17_2 = L16_2.PlayerData
          if L17_2 then
            L17_2 = json
            L17_2 = L17_2.decode
            L18_2 = L11_2[L15_2]
            L18_2 = L18_2.charinfo
            L17_2 = L17_2(L18_2)
            if L16_2 then
              L18_2 = L16_2.PlayerData
              L18_2 = L18_2.metadata
              if L18_2 then
                goto lbl_269
              end
            end
            L18_2 = json
            L18_2 = L18_2.decode
            L19_2 = L11_2[L15_2]
            L19_2 = L19_2.metadata
            L18_2 = L18_2(L19_2)
            ::lbl_269::
            if L16_2 then
              L19_2 = L16_2.PlayerData
              L19_2 = L19_2.job
              if L19_2 then
                goto lbl_280
              end
            end
            L19_2 = json
            L19_2 = L19_2.decode
            L20_2 = L11_2[L15_2]
            L20_2 = L20_2.job
            L19_2 = L19_2(L20_2)
            ::lbl_280::
            L20_2 = nil
            L21_2 = pairs
            L22_2 = L9_2
            L21_2, L22_2, L23_2, L24_2 = L21_2(L22_2)
            for L25_2, L26_2 in L21_2, L22_2, L23_2, L24_2 do
              L27_2 = L26_2.name
              L28_2 = L19_2.name
              if L27_2 == L28_2 then
                L20_2 = L26_2.titleListLabel
              end
            end
            if nil ~= L20_2 then
              L21_2 = table
              L21_2 = L21_2.insert
              L22_2 = L3_2
              L23_2 = {}
              L24_2 = L11_2[L15_2]
              L24_2 = L24_2.citizenid
              L23_2.citizenid = L24_2
              L24_2 = L17_2.firstname
              L23_2.firstname = L24_2
              L24_2 = L17_2.lastname
              L23_2.lastname = L24_2
              L24_2 = L11_2[L15_2]
              L24_2 = L24_2.image
              L23_2.image = L24_2
              L24_2 = L18_2.police_badge
              if not L24_2 then
                L24_2 = "0000"
              end
              L23_2.badge = L24_2
              L24_2 = L19_2.grade
              L24_2 = L24_2.name
              L23_2.grade = L24_2
              L24_2 = L19_2.name
              L23_2.job = L24_2
              L23_2.titleLabel = L20_2
              L21_2(L22_2, L23_2)
            end
        end
        else
          L17_2 = Debuger
          L18_2 = "Can't find PlayerData for player with citizenid: "
          L19_2 = L11_2[L15_2]
          L19_2 = L19_2.citizenid
          L17_2(L18_2, L19_2)
        end
      end
    end
  else
    L9_2 = Config
    L9_2 = L9_2.Framework
    if "esx" == L9_2 then
      L9_2 = exports
      L9_2 = L9_2.origen_police
      L10_2 = L9_2
      L9_2 = L9_2.GetConfig
      L11_2 = "JobCategory"
      L9_2 = L9_2(L10_2, L11_2)
      L9_2 = L9_2[L7_2]
      L10_2 = ""
      L11_2 = pairs
      L12_2 = L9_2
      L11_2, L12_2, L13_2, L14_2 = L11_2(L12_2)
      for L15_2, L16_2 in L11_2, L12_2, L13_2, L14_2 do
        L17_2 = L10_2
        L18_2 = "job LIKE '%"
        L19_2 = L16_2.name
        L20_2 = "%' OR "
        L17_2 = L17_2 .. L18_2 .. L19_2 .. L20_2
        L10_2 = L17_2
      end
      L12_2 = L10_2
      L11_2 = L10_2.sub
      L13_2 = 1
      L14_2 = -5
      L11_2 = L11_2(L12_2, L13_2, L14_2)
      L10_2 = L11_2
      L11_2 = GetPoliceList
      L12_2 = L10_2
      L11_2 = L11_2(L12_2)
      L12_2 = 1
      L13_2 = #L11_2
      L14_2 = 1
      for L15_2 = L12_2, L13_2, L14_2 do
        L16_2 = L11_2[L15_2]
        L16_2 = L16_2.identifier
        L16_2 = L4_2[L16_2]
        if not L16_2 then
          L16_2 = FW_GetPlayerFromCitizenid
          L17_2 = L11_2[L15_2]
          L17_2 = L17_2.identifier
          L16_2 = L16_2(L17_2)
          L17_2 = L16_2.Functions
          L17_2 = L17_2.GetMetaData
          L17_2 = L17_2()
          L18_2 = "XXXXXXXXX"
          L19_2 = Framework
          L19_2 = L19_2.Shared
          L19_2 = L19_2.Jobs
          if L16_2 then
            L20_2 = L16_2.PlayerData
            L20_2 = L20_2.job
            L20_2 = L20_2.name
            if L20_2 then
              goto lbl_391
            end
          end
          L20_2 = L11_2[L15_2]
          L20_2 = L20_2.job
          ::lbl_391::
          L19_2 = L19_2[L20_2]
          L20_2 = L16_2.PlayerData
          L20_2 = L20_2.job
          L20_2 = L20_2.grade
          L20_2 = L20_2.label
          if not L20_2 then
            L20_2 = L11_2[L15_2]
            L20_2 = L20_2.job_grade
          end
          if L19_2 then
            L19_2 = L19_2.name
          end
          L21_2 = nil
          L22_2 = pairs
          L23_2 = L9_2
          L22_2, L23_2, L24_2, L25_2 = L22_2(L23_2)
          for L26_2, L27_2 in L22_2, L23_2, L24_2, L25_2 do
            L28_2 = L27_2.name
            if L28_2 == L19_2 then
              L21_2 = L27_2.titleListLabel
            end
          end
          if nil ~= L21_2 then
            L22_2 = table
            L22_2 = L22_2.insert
            L23_2 = L3_2
            L24_2 = {}
            L25_2 = L11_2[L15_2]
            L25_2 = L25_2.identifier
            L24_2.citizenid = L25_2
            L25_2 = L11_2[L15_2]
            L25_2 = L25_2.firstname
            L24_2.firstname = L25_2
            L25_2 = L11_2[L15_2]
            L25_2 = L25_2.lastname
            L24_2.lastname = L25_2
            L25_2 = L11_2[L15_2]
            L25_2 = L25_2.image
            L24_2.image = L25_2
            L25_2 = L17_2.police_badge
            if not L25_2 then
              L25_2 = "0000"
            end
            L24_2.badge = L25_2
            L24_2.grade = L20_2
            L24_2.job = L19_2
            L24_2.titleLabel = L21_2
            L22_2(L23_2, L24_2)
          end
        end
      end
    end
  end
  L9_2 = table
  L9_2 = L9_2.sort
  L10_2 = L3_2
  function L11_2(A0_3, A1_3)
    local L2_3, L3_3, L4_3
    L2_3 = A0_3.firstname
    L3_3 = A0_3.lastname
    L2_3 = L2_3 .. L3_3
    L3_3 = A1_3.firstname
    L4_3 = A1_3.lastname
    L3_3 = L3_3 .. L4_3
    L2_3 = L2_3 < L3_3
    return L2_3
  end
  L9_2(L10_2, L11_2)
  L9_2 = A1_2
  L10_2 = L3_2
  L9_2(L10_2)
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:GetPolice"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  L3_2 = FW_GetPlayerFromCitizenid
  L4_2 = A2_2.citizenid
  L3_2 = L3_2(L4_2)
  L4_2 = false
  if L3_2 then
    L5_2 = L3_2.PlayerData
    L5_2 = L5_2.source
    if nil ~= L5_2 then
      L5_2 = MySQL
      L5_2 = L5_2.awaitQuery
      L6_2 = "SELECT image FROM "
      L7_2 = Config
      L7_2 = L7_2.Framework
      if "qbcore" == L7_2 then
        L7_2 = "players"
        if L7_2 then
          goto lbl_22
        end
      end
      L7_2 = "users"
      ::lbl_22::
      L8_2 = " WHERE "
      L9_2 = Config
      L9_2 = L9_2.Framework
      if "qbcore" == L9_2 then
        L9_2 = "citizenid"
        if L9_2 then
          goto lbl_31
        end
      end
      L9_2 = "identifier"
      ::lbl_31::
      L10_2 = " = ?"
      L6_2 = L6_2 .. L7_2 .. L8_2 .. L9_2 .. L10_2
      L7_2 = {}
      L8_2 = L3_2.PlayerData
      L8_2 = L8_2.citizenid
      L7_2[1] = L8_2
      L5_2 = L5_2(L6_2, L7_2)
      L6_2 = L5_2[1]
      if L6_2 then
        L6_2 = L5_2[1]
        L5_2 = L6_2.image
      else
        L5_2 = nil
      end
      L6_2 = "lspd"
      L7_2 = Framework
      L7_2 = L7_2.Shared
      L7_2 = L7_2.Jobs
      L8_2 = L3_2.PlayerData
      L8_2 = L8_2.job
      L8_2 = L8_2.name
      L7_2 = L7_2[L8_2]
      L8_2 = L3_2.PlayerData
      L8_2 = L8_2.job
      L8_2 = L8_2.grade
      L8_2 = L8_2.name
      L9_2 = Config
      L9_2 = L9_2.Framework
      if "esx" == L9_2 and L7_2 then
        L9_2 = L7_2.grades
        L10_2 = tostring
        L11_2 = L3_2.PlayerData
        L11_2 = L11_2.job
        L11_2 = L11_2.grade
        L11_2 = L11_2.level
        L10_2 = L10_2(L11_2)
        L9_2 = L9_2[L10_2]
        if L9_2 then
          L9_2 = L7_2.grades
          L10_2 = tostring
          L11_2 = L3_2.PlayerData
          L11_2 = L11_2.job
          L11_2 = L11_2.grade
          L11_2 = L11_2.level
          L10_2 = L10_2(L11_2)
          L9_2 = L9_2[L10_2]
          L8_2 = L9_2.label
        end
      end
      if L7_2 then
        L9_2 = L7_2.grades
        L10_2 = tostring
        L11_2 = L3_2.PlayerData
        L11_2 = L11_2.job
        L11_2 = L11_2.grade
        L11_2 = L11_2.level
        L10_2 = L10_2(L11_2)
        L9_2 = L9_2[L10_2]
        if L9_2 then
          L9_2 = L7_2.grades
          L10_2 = tostring
          L11_2 = L3_2.PlayerData
          L11_2 = L11_2.job
          L11_2 = L11_2.grade
          L11_2 = L11_2.level
          L10_2 = L10_2(L11_2)
          L9_2 = L9_2[L10_2]
          L9_2 = L9_2.type
          if L9_2 then
            goto lbl_107
            L6_2 = L9_2 or L6_2
          end
        end
      end
      L6_2 = "unk"
      ::lbl_107::
      L9_2 = GetPhoneFromIdentifier
      L10_2 = L3_2.PlayerData
      L10_2 = L10_2.citizenid
      L9_2 = L9_2(L10_2)
      L10_2 = {}
      L11_2 = L3_2.PlayerData
      L11_2 = L11_2.citizenid
      L10_2.citizenid = L11_2
      L10_2.image = L5_2
      L11_2 = L3_2.PlayerData
      L11_2 = L11_2.charinfo
      L11_2 = L11_2.firstname
      L10_2.firstname = L11_2
      L11_2 = L3_2.PlayerData
      L11_2 = L11_2.charinfo
      L11_2 = L11_2.lastname
      L10_2.lastname = L11_2
      L10_2.phone = L9_2
      L11_2 = GetPlayerBankNumber
      L12_2 = L3_2.PlayerData
      L12_2 = L12_2.citizenid
      L13_2 = L3_2.PlayerData
      L13_2 = L13_2.charinfo
      L13_2 = L13_2.account
      L11_2 = L11_2(L12_2, L13_2)
      L10_2.iban = L11_2
      L11_2 = L3_2.PlayerData
      L11_2 = L11_2.charinfo
      L11_2 = L11_2.birthdate
      L10_2.birthdate = L11_2
      L11_2 = Config
      L11_2 = L11_2.Framework
      if "qbcore" == L11_2 then
        L11_2 = L3_2.PlayerData
        L11_2 = L11_2.charinfo
        L11_2 = L11_2.nationality
        if L11_2 then
          goto lbl_151
        end
      end
      L11_2 = GetPlayerNationality
      L12_2 = L3_2.PlayerData
      L12_2 = L12_2.citizenid
      L11_2 = L11_2(L12_2)
      ::lbl_151::
      L10_2.nationality = L11_2
      L11_2 = L3_2.PlayerData
      L11_2 = L11_2.metadata
      L11_2 = L11_2.police_badge
      if not L11_2 then
        L11_2 = "0000"
      end
      L10_2.badge = L11_2
      L11_2 = GetGender
      L12_2 = L3_2.PlayerData
      L12_2 = L12_2.charinfo
      L12_2 = L12_2.gender
      L11_2 = L11_2(L12_2)
      L10_2.gender = L11_2
      L11_2 = L3_2.PlayerData
      L11_2 = L11_2.metadata
      L11_2 = L11_2.condecorates
      if not L11_2 then
        L11_2 = {}
      end
      L10_2.condecorates = L11_2
      L11_2 = L3_2.PlayerData
      L11_2 = L11_2.metadata
      L11_2 = L11_2.divisions
      if not L11_2 then
        L11_2 = {}
      end
      L10_2.divisions = L11_2
      L10_2.department = L6_2
      L10_2.grade = L8_2
      L4_2 = L10_2
  end
  else
    L5_2 = Config
    L5_2 = L5_2.Framework
    if "qbcore" == L5_2 then
      L5_2 = GetPolice
      L6_2 = A2_2.citizenid
      L5_2 = L5_2(L6_2)
      L6_2 = L5_2[1]
      if not L6_2 then
        goto lbl_424
      end
      L6_2 = json
      L6_2 = L6_2.decode
      L7_2 = L5_2[1]
      L7_2 = L7_2.charinfo
      L6_2 = L6_2(L7_2)
      L7_2 = json
      L7_2 = L7_2.decode
      L8_2 = L5_2[1]
      L8_2 = L8_2.job
      L7_2 = L7_2(L8_2)
      L8_2 = Config
      L8_2 = L8_2.fixQS
      if L8_2 then
        L8_2 = FW_GetMetadata
        L9_2 = L5_2[1]
        L9_2 = L9_2.citizenid
        L8_2 = L8_2(L9_2)
        if L8_2 then
          goto lbl_220
        end
      end
      L8_2 = json
      L8_2 = L8_2.decode
      L9_2 = L5_2[1]
      L9_2 = L9_2.metadata
      L8_2 = L8_2(L9_2)
      ::lbl_220::
      L9_2 = GetPhoneFromIdentifier
      L10_2 = L5_2[1]
      L10_2 = L10_2.citizenid
      L9_2 = L9_2(L10_2)
      L10_2 = {}
      L11_2 = L5_2[1]
      L11_2 = L11_2.citizenid
      L10_2.citizenid = L11_2
      L11_2 = L5_2[1]
      L11_2 = L11_2.image
      L10_2.image = L11_2
      L11_2 = L6_2.firstname
      L10_2.firstname = L11_2
      L11_2 = L6_2.lastname
      L10_2.lastname = L11_2
      L10_2.phone = L9_2
      L11_2 = GetPlayerBankNumber
      L12_2 = L5_2[1]
      L12_2 = L12_2.citizenid
      L13_2 = L6_2.account
      L11_2 = L11_2(L12_2, L13_2)
      L10_2.iban = L11_2
      L11_2 = L6_2.birthdate
      L10_2.birthdate = L11_2
      L11_2 = L6_2.nationality
      L10_2.nationality = L11_2
      L11_2 = L8_2.police_badge
      if not L11_2 then
        L11_2 = "0000"
      end
      L10_2.badge = L11_2
      L11_2 = GetGender
      L12_2 = L6_2.gender
      L11_2 = L11_2(L12_2)
      L10_2.gender = L11_2
      L11_2 = L8_2.condecorates
      if not L11_2 then
        L11_2 = {}
      end
      L10_2.condecorates = L11_2
      L11_2 = L8_2.divisions
      if not L11_2 then
        L11_2 = {}
      end
      L10_2.divisions = L11_2
      L11_2 = Framework
      L11_2 = L11_2.Shared
      L11_2 = L11_2.Jobs
      L12_2 = L7_2.name
      L11_2 = L11_2[L12_2]
      if L11_2 then
        L11_2 = Framework
        L11_2 = L11_2.Shared
        L11_2 = L11_2.Jobs
        L12_2 = L7_2.name
        L11_2 = L11_2[L12_2]
        L11_2 = L11_2.grades
        L12_2 = tostring
        L13_2 = L7_2.grade
        L13_2 = L13_2.level
        L12_2 = L12_2(L13_2)
        L11_2 = L11_2[L12_2]
        if L11_2 then
          L11_2 = Framework
          L11_2 = L11_2.Shared
          L11_2 = L11_2.Jobs
          L12_2 = L7_2.name
          L11_2 = L11_2[L12_2]
          L11_2 = L11_2.grades
          L12_2 = tostring
          L13_2 = L7_2.grade
          L13_2 = L13_2.level
          L12_2 = L12_2(L13_2)
          L11_2 = L11_2[L12_2]
          L11_2 = L11_2.type
          if L11_2 then
            goto lbl_303
          end
        end
      end
      L11_2 = "lspd"
      ::lbl_303::
      L10_2.department = L11_2
      L11_2 = L7_2.grade
      L11_2 = L11_2.name
      L10_2.grade = L11_2
      L4_2 = L10_2
    else
      L5_2 = Config
      L5_2 = L5_2.Framework
      if "esx" == L5_2 then
        L5_2 = GetPolice
        L6_2 = A2_2.citizenid
        L5_2 = L5_2(L6_2)
        L6_2 = L5_2[1]
        if L6_2 then
          L6_2 = GetPhoneFromIdentifier
          L7_2 = L5_2[1]
          L7_2 = L7_2.identifier
          L6_2 = L6_2(L7_2)
          L7_2 = Framework
          L7_2 = L7_2.Shared
          L7_2 = L7_2.Jobs
          L8_2 = L3_2.PlayerData
          L8_2 = L8_2.job
          L8_2 = L8_2.name
          L7_2 = L7_2[L8_2]
          L8_2 = L3_2.Functions
          L8_2 = L8_2.GetMetaData
          L8_2 = L8_2()
          L9_2 = "XXXXXXXXX"
          if L7_2 then
            L10_2 = L7_2.grades
            L11_2 = tostring
            L12_2 = L5_2[1]
            L12_2 = L12_2.job_grade
            L11_2 = L11_2(L12_2)
            L10_2 = L10_2[L11_2]
            if L10_2 then
              L10_2 = L7_2.grades
              L11_2 = tostring
              L12_2 = L5_2[1]
              L12_2 = L12_2.job_grade
              L11_2 = L11_2(L12_2)
              L10_2 = L10_2[L11_2]
              L9_2 = L10_2.label
            end
          end
          L10_2 = {}
          L11_2 = L5_2[1]
          L11_2 = L11_2.identifier
          L10_2.citizenid = L11_2
          L11_2 = L5_2[1]
          L11_2 = L11_2.image
          L10_2.image = L11_2
          L11_2 = L5_2[1]
          L11_2 = L11_2.firstname
          L10_2.firstname = L11_2
          L11_2 = L5_2[1]
          L11_2 = L11_2.lastname
          L10_2.lastname = L11_2
          L10_2.phone = L6_2
          L11_2 = GetPlayerBankNumber
          L12_2 = L5_2[1]
          L12_2 = L12_2.identifier
          L11_2 = L11_2(L12_2)
          L10_2.iban = L11_2
          L11_2 = L5_2[1]
          L11_2 = L11_2.dateofbirth
          L10_2.birthdate = L11_2
          L11_2 = GetPlayerNationality
          L12_2 = L5_2[1]
          L12_2 = L12_2.identifier
          L11_2 = L11_2(L12_2)
          L10_2.nationality = L11_2
          L11_2 = L8_2.police_badge
          if not L11_2 then
            L11_2 = "0000"
          end
          L10_2.badge = L11_2
          L11_2 = GetGender
          L12_2 = L5_2[1]
          L12_2 = L12_2.sex
          L11_2 = L11_2(L12_2)
          L10_2.gender = L11_2
          L11_2 = L8_2.condecorates
          if not L11_2 then
            L11_2 = {}
          end
          L10_2.condecorates = L11_2
          L11_2 = L8_2.divisions
          if not L11_2 then
            L11_2 = {}
          end
          L10_2.divisions = L11_2
          if L7_2 then
            L11_2 = L7_2.grades
            L12_2 = tostring
            L13_2 = L5_2[1]
            L13_2 = L13_2.job_grade
            L12_2 = L12_2(L13_2)
            L11_2 = L11_2[L12_2]
            if L11_2 then
              L11_2 = L7_2.grades
              L12_2 = tostring
              L13_2 = L5_2[1]
              L13_2 = L13_2.job_grade
              L12_2 = L12_2(L13_2)
              L11_2 = L11_2[L12_2]
              L11_2 = L11_2.type
              if L11_2 then
                goto lbl_421
              end
            end
          end
          L11_2 = "lspd"
          ::lbl_421::
          L10_2.department = L11_2
          L10_2.grade = L9_2
          L4_2 = L10_2
        end
      end
    end
  end
  ::lbl_424::
  if L4_2 then
    L5_2 = MySQL
    L5_2 = L5_2.awaitQuery
    L6_2 = "SELECT * FROM origen_police_notes WHERE citizenid = ? ORDER BY id DESC"
    L7_2 = {}
    L8_2 = A2_2.citizenid
    L9_2 = "-police"
    L8_2 = L8_2 .. L9_2
    L7_2[1] = L8_2
    L5_2 = L5_2(L6_2, L7_2)
    L4_2.notes = L5_2
    L5_2 = MySQL
    L5_2 = L5_2.awaitQuery
    L6_2 = "SELECT id, title FROM origen_police_reports WHERE cops LIKE @citizenid OR author LIKE @name ORDER BY id DESC"
    L7_2 = {}
    L8_2 = "%"
    L9_2 = A2_2.citizenid
    L10_2 = "%"
    L8_2 = L8_2 .. L9_2 .. L10_2
    L7_2["@citizenid"] = L8_2
    L8_2 = "%"
    L9_2 = L4_2.firstname
    L10_2 = " "
    L11_2 = L4_2.lastname
    L12_2 = "%"
    L8_2 = L8_2 .. L9_2 .. L10_2 .. L11_2 .. L12_2
    L7_2["@name"] = L8_2
    L5_2 = L5_2(L6_2, L7_2)
    L4_2.reports = L5_2
  end
  L5_2 = A1_2
  L6_2 = L4_2
  L5_2(L6_2)
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:UpdatePoliceMetaData"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L3_2 = FW_GetPlayerFromCitizenid
  L4_2 = A2_2.citizenid
  L3_2 = L3_2(L4_2)
  if L3_2 then
    L4_2 = L3_2.PlayerData
    if L4_2 then
      L4_2 = L3_2.PlayerData
      L4_2 = L4_2.source
      if nil ~= L4_2 then
        L4_2 = L3_2.PlayerData
        L4_2 = L4_2.metadata
        L5_2 = A2_2.type
        L4_2 = L4_2[L5_2]
        if not L4_2 then
          L4_2 = L3_2.PlayerData
          L4_2 = L4_2.metadata
          L5_2 = A2_2.type
          L6_2 = {}
          L4_2[L5_2] = L6_2
        end
        L4_2 = L3_2.PlayerData
        L4_2 = L4_2.metadata
        L5_2 = A2_2.type
        L4_2 = L4_2[L5_2]
        L5_2 = A2_2.id
        L6_2 = A2_2.value
        L4_2[L5_2] = L6_2
        L4_2 = L3_2.Functions
        L4_2 = L4_2.SetMetaData
        L5_2 = A2_2.type
        L6_2 = L3_2.PlayerData
        L6_2 = L6_2.metadata
        L7_2 = A2_2.type
        L6_2 = L6_2[L7_2]
        L4_2(L5_2, L6_2)
        L4_2 = A1_2
        L5_2 = true
        L4_2(L5_2)
    end
  end
  else
    L4_2 = Config
    L4_2 = L4_2.fixQS
    if L4_2 then
      L4_2 = L3_2.PlayerData
      L4_2 = L4_2.metadata
      L5_2 = A2_2.type
      L4_2 = L4_2[L5_2]
      if not L4_2 then
        L4_2 = L3_2.PlayerData
        L4_2 = L4_2.metadata
        L5_2 = A2_2.type
        L6_2 = {}
        L4_2[L5_2] = L6_2
      end
      L4_2 = L3_2.PlayerData
      L4_2 = L4_2.metadata
      L5_2 = A2_2.type
      L4_2 = L4_2[L5_2]
      L5_2 = A2_2.id
      L6_2 = A2_2.value
      L4_2[L5_2] = L6_2
      L4_2 = L3_2.Functions
      L4_2 = L4_2.SetMetaData
      L5_2 = A2_2.type
      L6_2 = L3_2.PlayerData
      L6_2 = L6_2.metadata
      L7_2 = A2_2.type
      L6_2 = L6_2[L7_2]
      L4_2(L5_2, L6_2)
      L4_2 = A1_2
      L5_2 = true
      return L4_2(L5_2)
    end
    L4_2 = MySQL
    L4_2 = L4_2.awaitQuery
    L5_2 = "SELECT metadata FROM "
    L6_2 = Config
    L6_2 = L6_2.Framework
    if "qbcore" == L6_2 then
      L6_2 = "players"
      if L6_2 then
        goto lbl_90
      end
    end
    L6_2 = "users"
    ::lbl_90::
    L7_2 = " WHERE "
    L8_2 = Config
    L8_2 = L8_2.Framework
    if "qbcore" == L8_2 then
      L8_2 = "citizenid"
      if L8_2 then
        goto lbl_99
      end
    end
    L8_2 = "identifier"
    ::lbl_99::
    L9_2 = " = ?"
    L5_2 = L5_2 .. L6_2 .. L7_2 .. L8_2 .. L9_2
    L6_2 = {}
    L7_2 = A2_2.citizenid
    L6_2[1] = L7_2
    L4_2 = L4_2(L5_2, L6_2)
    L5_2 = L4_2[1]
    if L5_2 then
      L5_2 = L4_2[1]
      L6_2 = json
      L6_2 = L6_2.decode
      L7_2 = L4_2[1]
      L7_2 = L7_2.metadata
      L6_2 = L6_2(L7_2)
      L5_2.metadata = L6_2
      L5_2 = L4_2[1]
      L5_2 = L5_2.metadata
      L6_2 = A2_2.type
      L5_2 = L5_2[L6_2]
      if not L5_2 then
        L5_2 = L4_2[1]
        L5_2 = L5_2.metadata
        L6_2 = A2_2.type
        L7_2 = {}
        L5_2[L6_2] = L7_2
      end
      L5_2 = L4_2[1]
      L5_2 = L5_2.metadata
      L6_2 = A2_2.type
      L5_2 = L5_2[L6_2]
      L6_2 = A2_2.id
      L7_2 = A2_2.value
      L5_2[L6_2] = L7_2
      L5_2 = A1_2
      L6_2 = MySQL
      L6_2 = L6_2.awaitUpdate
      L7_2 = "UPDATE "
      L8_2 = Config
      L8_2 = L8_2.Framework
      if "qbcore" == L8_2 then
        L8_2 = "players"
        if L8_2 then
          goto lbl_147
        end
      end
      L8_2 = "users"
      ::lbl_147::
      L9_2 = " SET metadata = ? WHERE "
      L10_2 = Config
      L10_2 = L10_2.Framework
      if "qbcore" == L10_2 then
        L10_2 = "citizenid"
        if L10_2 then
          goto lbl_156
        end
      end
      L10_2 = "identifier"
      ::lbl_156::
      L11_2 = " = ?"
      L7_2 = L7_2 .. L8_2 .. L9_2 .. L10_2 .. L11_2
      L8_2 = {}
      L9_2 = json
      L9_2 = L9_2.encode
      L10_2 = L4_2[1]
      L10_2 = L10_2.metadata
      L9_2 = L9_2(L10_2)
      L10_2 = A2_2.citizenid
      L8_2[1] = L9_2
      L8_2[2] = L10_2
      L6_2 = L6_2(L7_2, L8_2)
      L6_2 = L6_2 > 0
      L5_2(L6_2)
    end
  end
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:GetBusqueda"
function L2_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  L2_2 = Config
  L2_2 = L2_2.Framework
  if "qbcore" == L2_2 then
    L2_2 = GetWanted
    L2_2, L3_2 = L2_2()
    L4_2 = 1
    L5_2 = #L2_2
    L6_2 = 1
    for L7_2 = L4_2, L5_2, L6_2 do
      L8_2 = L2_2[L7_2]
      L9_2 = json
      L9_2 = L9_2.decode
      L10_2 = L2_2[L7_2]
      L10_2 = L10_2.charinfo
      L9_2 = L9_2(L10_2)
      L8_2.charinfo = L9_2
      L8_2 = table
      L8_2 = L8_2.insert
      L9_2 = L3_2
      L10_2 = {}
      L11_2 = L2_2[L7_2]
      L11_2 = L11_2.citizenid
      L10_2.citizenid = L11_2
      L11_2 = L2_2[L7_2]
      L11_2 = L11_2.charinfo
      L11_2 = L11_2.firstname
      L12_2 = " "
      L13_2 = L2_2[L7_2]
      L13_2 = L13_2.charinfo
      L13_2 = L13_2.lastname
      L11_2 = L11_2 .. L12_2 .. L13_2
      L10_2.name = L11_2
      L11_2 = L2_2[L7_2]
      L11_2 = L11_2.image
      L10_2.image = L11_2
      L8_2(L9_2, L10_2)
    end
    L4_2 = A1_2
    L5_2 = L3_2
    L4_2(L5_2)
  else
    L2_2 = Config
    L2_2 = L2_2.Framework
    if "esx" == L2_2 then
      L2_2 = GetWanted
      L2_2, L3_2 = L2_2()
      L4_2 = 1
      L5_2 = #L2_2
      L6_2 = 1
      for L7_2 = L4_2, L5_2, L6_2 do
        L8_2 = table
        L8_2 = L8_2.insert
        L9_2 = L3_2
        L10_2 = {}
        L11_2 = L2_2[L7_2]
        L11_2 = L11_2.identifier
        L10_2.citizenid = L11_2
        L11_2 = L2_2[L7_2]
        L11_2 = L11_2.firstname
        L12_2 = " "
        L13_2 = L2_2[L7_2]
        L13_2 = L13_2.lastname
        L11_2 = L11_2 .. L12_2 .. L13_2
        L10_2.name = L11_2
        L11_2 = L2_2[L7_2]
        L11_2 = L11_2.image
        L10_2.image = L11_2
        L8_2(L9_2, L10_2)
      end
      L4_2 = A1_2
      L5_2 = L3_2
      L4_2(L5_2)
    end
  end
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:GetMorosos"
function L2_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2
  L2_2 = A0_2
  L3_2 = FW_GetPlayer
  L4_2 = L2_2
  L3_2 = L3_2(L4_2)
  L4_2 = CanOpenTablet
  L5_2 = L3_2.PlayerData
  L5_2 = L5_2.job
  L5_2 = L5_2.name
  L4_2 = L4_2(L5_2)
  L4_2 = L4_2[2]
  L5_2 = Config
  L5_2 = L5_2.Framework
  if "qbcore" == L5_2 then
    L5_2 = GetDebors
    L6_2 = L4_2
    L5_2 = L5_2(L6_2)
    L6_2 = {}
    L7_2 = 1
    L8_2 = #L5_2
    L9_2 = 1
    for L10_2 = L7_2, L8_2, L9_2 do
      L11_2 = L5_2[L10_2]
      L12_2 = json
      L12_2 = L12_2.decode
      L13_2 = L5_2[L10_2]
      L13_2 = L13_2.charinfo
      L12_2 = L12_2(L13_2)
      L11_2.charinfo = L12_2
      L11_2 = table
      L11_2 = L11_2.insert
      L12_2 = L6_2
      L13_2 = {}
      L14_2 = L5_2[L10_2]
      L14_2 = L14_2.citizenid
      L13_2.citizenid = L14_2
      L14_2 = L5_2[L10_2]
      L14_2 = L14_2.totalprice
      L13_2.price = L14_2
      L14_2 = L5_2[L10_2]
      L14_2 = L14_2.charinfo
      L14_2 = L14_2.firstname
      L15_2 = " "
      L16_2 = L5_2[L10_2]
      L16_2 = L16_2.charinfo
      L16_2 = L16_2.lastname
      L14_2 = L14_2 .. L15_2 .. L16_2
      L13_2.name = L14_2
      L14_2 = L5_2[L10_2]
      L14_2 = L14_2.image
      L13_2.image = L14_2
      L11_2(L12_2, L13_2)
    end
    L7_2 = table
    L7_2 = L7_2.sort
    L8_2 = L6_2
    function L9_2(A0_3, A1_3)
      local L2_3, L3_3
      L2_3 = A0_3.price
      L3_3 = A1_3.price
      L2_3 = L2_3 > L3_3
      return L2_3
    end
    L7_2(L8_2, L9_2)
    L7_2 = A1_2
    L8_2 = L6_2
    L7_2(L8_2)
  else
    L5_2 = Config
    L5_2 = L5_2.Framework
    if "esx" == L5_2 then
      L5_2 = GetDebors
      L6_2 = L4_2
      L5_2 = L5_2(L6_2)
      L6_2 = {}
      L7_2 = 1
      L8_2 = #L5_2
      L9_2 = 1
      for L10_2 = L7_2, L8_2, L9_2 do
        L11_2 = table
        L11_2 = L11_2.insert
        L12_2 = L6_2
        L13_2 = {}
        L14_2 = L5_2[L10_2]
        L14_2 = L14_2.citizenid
        L13_2.citizenid = L14_2
        L14_2 = L5_2[L10_2]
        L14_2 = L14_2.totalprice
        L13_2.price = L14_2
        L14_2 = L5_2[L10_2]
        L14_2 = L14_2.firstname
        L15_2 = " "
        L16_2 = L5_2[L10_2]
        L16_2 = L16_2.lastname
        L14_2 = L14_2 .. L15_2 .. L16_2
        L13_2.name = L14_2
        L14_2 = L5_2[L10_2]
        L14_2 = L14_2.image
        L13_2.image = L14_2
        L11_2(L12_2, L13_2)
      end
      L7_2 = table
      L7_2 = L7_2.sort
      L8_2 = L6_2
      function L9_2(A0_3, A1_3)
        local L2_3, L3_3
        L2_3 = A0_3.price
        L3_3 = A1_3.price
        L2_3 = L2_3 > L3_3
        return L2_3
      end
      L7_2(L8_2, L9_2)
      L7_2 = A1_2
      L8_2 = L6_2
      L7_2(L8_2)
    end
  end
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:GetVehicle"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L3_2 = false
  L4_2 = Config
  L4_2 = L4_2.Framework
  if "qbcore" == L4_2 then
    L4_2 = GetDataFromVehicle
    L5_2 = A2_2.plate
    L4_2 = L4_2(L5_2)
    L5_2 = L4_2[1]
    if not L5_2 then
      goto lbl_123
    end
    L5_2 = json
    L5_2 = L5_2.decode
    L6_2 = L4_2[1]
    L6_2 = L6_2.charinfo
    L5_2 = L5_2(L6_2)
    if L5_2 then
      L6_2 = L5_2.firstname
      L7_2 = " "
      L8_2 = L5_2.lastname
      L9_2 = " ("
      L10_2 = L4_2[1]
      L10_2 = L10_2.citizenid
      L11_2 = ")"
      L6_2 = L6_2 .. L7_2 .. L8_2 .. L9_2 .. L10_2 .. L11_2
      if L6_2 then
        goto lbl_30
      end
    end
    L6_2 = false
    ::lbl_30::
    if not L6_2 then
      L6_2 = "Desconocido"
    end
    L7_2 = {}
    L8_2 = L4_2[1]
    L8_2 = L8_2.hash
    L7_2.hash = L8_2
    L8_2 = L4_2[1]
    L8_2 = L8_2.plate
    L7_2.plate = L8_2
    L7_2.owner = L6_2
    L8_2 = L4_2[1]
    L8_2 = L8_2.garage
    if L8_2 then
      L8_2 = L4_2[1]
      L8_2 = L8_2.garage
      if L8_2 then
        goto lbl_51
      end
    end
    L8_2 = "Outside"
    ::lbl_51::
    L7_2.garage = L8_2
    L8_2 = L4_2[1]
    L8_2 = L8_2.wanted
    L7_2.wanted = L8_2
    L8_2 = L4_2[1]
    L8_2 = L8_2.description
    L7_2.description = L8_2
    L3_2 = L7_2
  else
    L4_2 = Config
    L4_2 = L4_2.Framework
    if "esx" == L4_2 then
      L4_2 = {}
      L5_2 = {}
      L5_2.firstname = "John"
      L5_2.lastname = "Doe"
      L5_2.owner = "John Doe (1234567890)"
      L5_2.vehicle = "{model = \"adder\"}"
      L5_2.plate = "123456"
      L5_2.wanted = 0
      L5_2.description = "This is a description"
      L4_2[1] = L5_2
      L5_2 = GetDataFromVehicle
      L6_2 = A2_2.plate
      L5_2 = L5_2(L6_2)
      L6_2 = L5_2[1]
      if L6_2 then
        L6_2 = L5_2[1]
        L6_2 = L6_2.firstname
        if L6_2 then
          L6_2 = L5_2[1]
          L6_2 = L6_2.firstname
          L7_2 = " "
          L8_2 = L5_2[1]
          L8_2 = L8_2.lastname
          L9_2 = " ("
          L10_2 = L5_2[1]
          L10_2 = L10_2.owner
          L11_2 = ")"
          L6_2 = L6_2 .. L7_2 .. L8_2 .. L9_2 .. L10_2 .. L11_2
          if L6_2 then
            goto lbl_99
          end
        end
        L6_2 = false
        ::lbl_99::
        if not L6_2 then
          L6_2 = "Desconocido"
        end
        L7_2 = {}
        L8_2 = json
        L8_2 = L8_2.decode
        L9_2 = L5_2[1]
        L9_2 = L9_2.vehicle
        L8_2 = L8_2(L9_2)
        L8_2 = L8_2.model
        L7_2.hash = L8_2
        L8_2 = L5_2[1]
        L8_2 = L8_2.plate
        L7_2.plate = L8_2
        L7_2.owner = L6_2
        L7_2.garage = "Garage"
        L8_2 = L5_2[1]
        L8_2 = L8_2.wanted
        L7_2.wanted = L8_2
        L8_2 = L5_2[1]
        L8_2 = L8_2.description
        L7_2.description = L8_2
        L3_2 = L7_2
      end
    end
  end
  ::lbl_123::
  L4_2 = A1_2
  L5_2 = L3_2
  L4_2(L5_2)
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:police:UpdateVehicle"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2
  L3_2 = A1_2
  L4_2 = UpdateVehicleData
  L5_2 = A2_2.key
  L6_2 = A2_2.value
  L7_2 = A2_2.plate
  L4_2, L5_2, L6_2, L7_2 = L4_2(L5_2, L6_2, L7_2)
  L3_2(L4_2, L5_2, L6_2, L7_2)
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:server:GetPoliceCount"
function L2_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2
  L2_2 = A0_2
  L3_2 = Config
  L3_2 = L3_2.Framework
  if "qbcore" == L3_2 then
    L3_2 = CanOpenTablet
    L4_2 = FW_GetPlayer
    L5_2 = L2_2
    L4_2 = L4_2(L5_2)
    L4_2 = L4_2.PlayerData
    L4_2 = L4_2.job
    L4_2 = L4_2.name
    L3_2 = L3_2(L4_2)
    L3_2 = L3_2[2]
    L4_2 = {}
    L5_2 = pairs
    L6_2 = Config
    L6_2 = L6_2.JobCategory
    L6_2 = L6_2[L3_2]
    L5_2, L6_2, L7_2, L8_2 = L5_2(L6_2)
    for L9_2, L10_2 in L5_2, L6_2, L7_2, L8_2 do
      L11_2 = Framework
      L11_2 = L11_2.Functions
      L11_2 = L11_2.GetPlayersOnDuty
      L12_2 = L10_2.name
      L11_2, L12_2 = L11_2(L12_2)
      L13_2 = 1
      L14_2 = #L11_2
      L15_2 = 1
      for L16_2 = L13_2, L14_2, L15_2 do
        L17_2 = table
        L17_2 = L17_2.insert
        L18_2 = L4_2
        L19_2 = L11_2[L16_2]
        L17_2(L18_2, L19_2)
      end
    end
    L5_2 = A1_2
    L6_2 = #L4_2
    L5_2(L6_2)
  else
    L3_2 = Config
    L3_2 = L3_2.Framework
    if "esx" == L3_2 then
      L3_2 = CanOpenTablet
      L4_2 = FW_GetPlayer
      L5_2 = L2_2
      L4_2 = L4_2(L5_2)
      L4_2 = L4_2.PlayerData
      L4_2 = L4_2.job
      L4_2 = L4_2.name
      L3_2 = L3_2(L4_2)
      L3_2 = L3_2[2]
      L4_2 = GetPlayersInDuty
      L5_2 = L3_2
      L4_2 = L4_2(L5_2)
      L4_2 = #L4_2
      if not L4_2 then
        L4_2 = 0
      end
      L5_2 = A1_2
      L6_2 = L4_2
      L5_2(L6_2)
    end
  end
end
L0_1(L1_1, L2_1)
L0_1 = FW_CreateCallback
L1_1 = "origen_police:server:SetPoliceJob"
function L2_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  L3_2 = FW_GetPlayer
  L4_2 = A0_2
  L3_2 = L3_2(L4_2)
  L4_2 = L3_2.PlayerData
  L4_2 = L4_2.job
  L4_2 = L4_2.name
  L5_2 = exports
  L5_2 = L5_2.origen_police
  L6_2 = L5_2
  L5_2 = L5_2.CanOpenTablet
  L7_2 = L4_2
  L5_2 = L5_2(L6_2, L7_2)
  L5_2 = L5_2[2]
  if not L5_2 then
    L6_2 = print
    L7_2 = "A player tried to hire someone without a job category with citidenid: "
    L8_2 = L3_2.PlayerData
    L8_2 = L8_2.citizenid
    L6_2(L7_2, L8_2)
    L6_2 = A1_2
    L7_2 = false
    L6_2(L7_2)
    return
  end
  L6_2 = FW_GetPlayerFromCitizenid
  L7_2 = A2_2.citizenid
  L6_2 = L6_2(L7_2)
  L7_2 = tonumber
  L8_2 = L6_2.PlayerData
  L8_2 = L8_2.job
  L8_2 = L8_2.grade
  L8_2 = L8_2.level
  L7_2 = L7_2(L8_2)
  L8_2 = tonumber
  L9_2 = L3_2.PlayerData
  L9_2 = L9_2.job
  L9_2 = L9_2.grade
  L9_2 = L9_2.level
  L8_2 = L8_2(L9_2)
  if L7_2 >= L8_2 then
    L7_2 = A1_2
    L8_2 = "You can't change the grade of someone with the same or higher grade than you."
    L7_2(L8_2)
    return
  end
  L7_2 = A2_2.grade
  if not L7_2 then
    L7_2 = 0
    if L7_2 then
      goto lbl_54
    end
  end
  L7_2 = tonumber
  L8_2 = A2_2.grade
  L7_2 = L7_2(L8_2)
  ::lbl_54::
  A2_2.grade = L7_2
  L7_2 = A2_2.job
  if not L7_2 then
    A2_2.job = "unemployed"
    A2_2.grade = 0
  end
  if L6_2 then
    L7_2 = string
    L7_2 = L7_2.lower
    L8_2 = A2_2.job
    L7_2 = L7_2(L8_2)
    A2_2.job = L7_2
    L7_2 = A2_2.job
    if "unemployed" == L7_2 then
      L7_2 = Config
      L7_2 = L7_2.Framework
      if "esx" == L7_2 then
        L7_2 = L6_2.PlayerData
        if nil ~= L7_2 then
          L7_2 = L6_2.PlayerData
          L7_2 = L7_2.source
          if nil ~= L7_2 then
            L7_2 = DropDutyPlayer
            L8_2 = L6_2.PlayerData
            L8_2 = L8_2.source
            L7_2(L8_2)
          end
        end
      end
      L7_2 = L6_2.Functions
      L7_2 = L7_2.SetMetaData
      L8_2 = "police_badge"
      L9_2 = "0000"
      L7_2(L8_2, L9_2)
      L7_2 = L6_2.Functions
      L7_2 = L7_2.SetMetaData
      L8_2 = "divisions"
      L9_2 = {}
      L7_2(L8_2, L9_2)
      L7_2 = L6_2.Functions
      L7_2 = L7_2.SetMetaData
      L8_2 = "condecorates"
      L9_2 = {}
      L7_2(L8_2, L9_2)
      L7_2 = CreateLog
      L8_2 = {}
      L8_2.type = "Management"
      L9_2 = {}
      L10_2 = Config
      L10_2 = L10_2.LogsTranslations
      L10_2 = L10_2.FirePolice
      L10_2 = L10_2.title
      L9_2.title = L10_2
      L10_2 = Config
      L10_2 = L10_2.LogsTranslations
      L10_2 = L10_2.FirePolice
      L10_2 = L10_2.message
      L11_2 = L10_2
      L10_2 = L10_2.format
      L12_2 = L6_2.PlayerData
      L12_2 = L12_2.charinfo
      L12_2 = L12_2.firstname
      L13_2 = " "
      L14_2 = L6_2.PlayerData
      L14_2 = L14_2.charinfo
      L14_2 = L14_2.lastname
      L12_2 = L12_2 .. L13_2 .. L14_2
      L10_2 = L10_2(L11_2, L12_2)
      L9_2.description = L10_2
      L9_2.color = 1791423
      L8_2.embed = L9_2
      L8_2.source = A0_2
      L7_2(L8_2)
    end
    L7_2 = L6_2.PlayerData
    if L7_2 then
      L7_2 = L6_2.PlayerData
      L7_2 = L7_2.source
      if L7_2 then
        L7_2 = L6_2.Functions
        L7_2 = L7_2.SetJob
        L8_2 = A2_2.job
        L9_2 = A2_2.grade
        L7_2(L8_2, L9_2)
    end
    else
      L7_2 = {}
      L8_2 = A2_2.job
      L7_2.name = L8_2
      L8_2 = Framework
      L8_2 = L8_2.Shared
      L8_2 = L8_2.Jobs
      L9_2 = A2_2.job
      L8_2 = L8_2[L9_2]
      L8_2 = L8_2.label
      L7_2.label = L8_2
      L8_2 = Framework
      L8_2 = L8_2.Shared
      L8_2 = L8_2.Jobs
      L9_2 = A2_2.job
      L8_2 = L8_2[L9_2]
      L8_2 = L8_2.defaultDuty
      L7_2.onduty = L8_2
      L8_2 = Framework
      L8_2 = L8_2.Shared
      L8_2 = L8_2.Jobs
      L9_2 = A2_2.job
      L8_2 = L8_2[L9_2]
      L8_2 = L8_2.grades
      L9_2 = tostring
      L10_2 = A2_2.grade
      L9_2 = L9_2(L10_2)
      if not L9_2 then
        L9_2 = "0"
      end
      L8_2 = L8_2[L9_2]
      if L8_2 then
        L8_2 = Framework
        L8_2 = L8_2.Shared
        L8_2 = L8_2.Jobs
        L9_2 = A2_2.job
        L8_2 = L8_2[L9_2]
        L8_2 = L8_2.grades
        L9_2 = tostring
        L10_2 = A2_2.grade
        L9_2 = L9_2(L10_2)
        if not L9_2 then
          L9_2 = "0"
        end
        L8_2 = L8_2[L9_2]
        L9_2 = {}
        L7_2.grade = L9_2
        L9_2 = L7_2.grade
        L10_2 = L8_2.name
        L9_2.name = L10_2
        L9_2 = L7_2.grade
        L10_2 = tostring
        L11_2 = A2_2.grade
        L10_2 = L10_2(L11_2)
        if not L10_2 then
          L10_2 = "0"
        end
        L9_2.level = L10_2
        L9_2 = L8_2.payment
        if nil ~= L9_2 then
          L9_2 = L8_2.payment
          if L9_2 then
            goto lbl_212
          end
        end
        L9_2 = 30
        ::lbl_212::
        L7_2.payment = L9_2
        L9_2 = L8_2.isboss
        if nil ~= L9_2 then
          L9_2 = L8_2.isboss
          if L9_2 then
            goto lbl_220
          end
        end
        L9_2 = false
        ::lbl_220::
        L7_2.isboss = L9_2
      else
        L8_2 = {}
        L7_2.grade = L8_2
        L8_2 = L7_2.grade
        L8_2.name = "No Grades"
        L8_2 = L7_2.grade
        L8_2.level = 0
        L7_2.payment = 30
        L7_2.isboss = false
      end
      L8_2 = MySQL
      L8_2 = L8_2.awaitQuery
      L9_2 = "SELECT "
      L10_2 = Config
      L10_2 = L10_2.Framework
      if "qbcore" == L10_2 then
        L10_2 = "charinfo"
        if L10_2 then
          goto lbl_242
        end
      end
      L10_2 = "firstname, lastname"
      ::lbl_242::
      L11_2 = ", image FROM "
      L12_2 = Config
      L12_2 = L12_2.Framework
      if "qbcore" == L12_2 then
        L12_2 = "players"
        if L12_2 then
          goto lbl_251
        end
      end
      L12_2 = "users"
      ::lbl_251::
      L13_2 = " WHERE "
      L14_2 = Config
      L14_2 = L14_2.Framework
      if "qbcore" == L14_2 then
        L14_2 = "citizenid"
        if L14_2 then
          goto lbl_260
        end
      end
      L14_2 = "identifier"
      ::lbl_260::
      L15_2 = " = ?"
      L9_2 = L9_2 .. L10_2 .. L11_2 .. L12_2 .. L13_2 .. L14_2 .. L15_2
      L10_2 = {}
      L11_2 = A2_2.citizenid
      L10_2[1] = L11_2
      L8_2 = L8_2(L9_2, L10_2)
      L9_2 = Config
      L9_2 = L9_2.Framework
      if "qbcore" == L9_2 then
        L9_2 = MySQL
        L9_2 = L9_2.awaitUpdate
        L10_2 = "UPDATE players SET job = ? WHERE citizenid = ?"
        L11_2 = {}
        L12_2 = json
        L12_2 = L12_2.encode
        L13_2 = L7_2
        L12_2 = L12_2(L13_2)
        L13_2 = A2_2.citizenid
        L11_2[1] = L12_2
        L11_2[2] = L13_2
        L9_2(L10_2, L11_2)
        L9_2 = L8_2[1]
        L10_2 = json
        L10_2 = L10_2.decode
        L11_2 = L8_2[1]
        L11_2 = L11_2.charinfo
        L10_2 = L10_2(L11_2)
        L9_2.charinfo = L10_2
        L9_2 = A1_2
        L10_2 = {}
        L11_2 = L8_2[1]
        L11_2 = L11_2.charinfo
        L11_2 = L11_2.firstname
        L10_2.firstname = L11_2
        L11_2 = L8_2[1]
        L11_2 = L11_2.charinfo
        L11_2 = L11_2.lastname
        L10_2.lastname = L11_2
        L11_2 = A2_2.citizenid
        L10_2.citizenid = L11_2
        L11_2 = L8_2[1]
        L11_2 = L11_2.image
        L10_2.image = L11_2
        L11_2 = L7_2.grade
        L11_2 = L11_2.name
        L10_2.grade = L11_2
        L9_2(L10_2)
      else
        L9_2 = MySQL
        L9_2 = L9_2.awaitUpdate
        L10_2 = "UPDATE users SET job = ?, job_grade = ? WHERE identifier = ?"
        L11_2 = {}
        L12_2 = L7_2.name
        L13_2 = tonumber
        L14_2 = L7_2.grade
        L14_2 = L14_2.level
        L13_2 = L13_2(L14_2)
        L14_2 = A2_2.citizenid
        L11_2[1] = L12_2
        L11_2[2] = L13_2
        L11_2[3] = L14_2
        L9_2(L10_2, L11_2)
        L9_2 = A1_2
        L10_2 = {}
        L11_2 = L8_2[1]
        L11_2 = L11_2.firstname
        L10_2.firstname = L11_2
        L11_2 = L8_2[1]
        L11_2 = L11_2.lastname
        L10_2.lastname = L11_2
        L11_2 = A2_2.citizenid
        L10_2.citizenid = L11_2
        L11_2 = L8_2[1]
        L11_2 = L11_2.image
        L10_2.image = L11_2
        L11_2 = L7_2.grade
        L11_2 = L11_2.name
        L10_2.grade = L11_2
        L9_2(L10_2)
        goto lbl_530
        L7_2 = MySQL
        L7_2 = L7_2.awaitQuery
        L8_2 = "SELECT image FROM "
        L9_2 = Config
        L9_2 = L9_2.Framework
        if "qbcore" == L9_2 then
          L9_2 = "players"
          if L9_2 then
            goto lbl_354
          end
        end
        L9_2 = "users"
        ::lbl_354::
        L10_2 = " WHERE "
        L11_2 = Config
        L11_2 = L11_2.Framework
        if "qbcore" == L11_2 then
          L11_2 = "citizenid"
          if L11_2 then
            goto lbl_363
          end
        end
        L11_2 = "identifier"
        ::lbl_363::
        L12_2 = " = ?"
        L8_2 = L8_2 .. L9_2 .. L10_2 .. L11_2 .. L12_2
        L9_2 = {}
        L10_2 = L6_2.PlayerData
        L10_2 = L10_2.citizenid
        L9_2[1] = L10_2
        L7_2 = L7_2(L8_2, L9_2)
        L8_2 = L7_2[1]
        if L8_2 then
          L8_2 = L7_2[1]
          L7_2 = L8_2.image
        else
          L7_2 = nil
        end
        L8_2 = A2_2.job
        if "unemployed" ~= L8_2 then
          L8_2 = L6_2.PlayerData
          if nil ~= L8_2 then
            L8_2 = L6_2.PlayerData
            L8_2 = L8_2.source
            if nil ~= L8_2 then
              L8_2 = TriggerClientEvent
              L9_2 = "origen_police:toggleDuty"
              L10_2 = tonumber
              L11_2 = L6_2.PlayerData
              L11_2 = L11_2.source
              L10_2 = L10_2(L11_2)
              L11_2 = true
              L8_2(L9_2, L10_2, L11_2)
            end
          end
        end
        L8_2 = CreateLog
        L9_2 = {}
        L9_2.type = "Management"
        L10_2 = {}
        L11_2 = Config
        L11_2 = L11_2.LogsTranslations
        L11_2 = L11_2.HirePolice
        L11_2 = L11_2.title
        L10_2.title = L11_2
        L11_2 = Config
        L11_2 = L11_2.LogsTranslations
        L11_2 = L11_2.HirePolice
        L11_2 = L11_2.message
        L12_2 = L11_2
        L11_2 = L11_2.format
        L13_2 = L6_2.PlayerData
        L13_2 = L13_2.charinfo
        L13_2 = L13_2.firstname
        L14_2 = " "
        L15_2 = L6_2.PlayerData
        L15_2 = L15_2.charinfo
        L15_2 = L15_2.lastname
        L13_2 = L13_2 .. L14_2 .. L15_2
        L14_2 = A2_2.job
        L15_2 = A2_2.grade
        L11_2 = L11_2(L12_2, L13_2, L14_2, L15_2)
        L10_2.description = L11_2
        L10_2.color = 1791423
        L9_2.embed = L10_2
        L9_2.source = A0_2
        L8_2(L9_2)
        L8_2 = A1_2
        L9_2 = {}
        L10_2 = L6_2.PlayerData
        L10_2 = L10_2.charinfo
        L10_2 = L10_2.firstname
        L9_2.firstname = L10_2
        L10_2 = L6_2.PlayerData
        L10_2 = L10_2.charinfo
        L10_2 = L10_2.lastname
        L9_2.lastname = L10_2
        L10_2 = L6_2.PlayerData
        L10_2 = L10_2.citizenid
        L9_2.citizenid = L10_2
        L9_2.image = L7_2
        L10_2 = L6_2.PlayerData
        L10_2 = L10_2.job
        L10_2 = L10_2.grade
        L10_2 = L10_2.name
        L9_2.grade = L10_2
        L8_2(L9_2)
      end
    end
  else
    L7_2 = Config
    L7_2 = L7_2.Framework
    if "qbcore" == L7_2 then
    else
      L7_2 = Config
      L7_2 = L7_2.Framework
      if "esx" == L7_2 then
        L7_2 = ESX
        L7_2 = L7_2.GetJobs
        L7_2 = L7_2()
        L8_2 = A2_2.job
        L7_2 = L7_2[L8_2]
        if L7_2 then
          L7_2 = ESX
          L7_2 = L7_2.GetJobs
          L7_2 = L7_2()
          L8_2 = A2_2.job
          L7_2 = L7_2[L8_2]
          L7_2 = L7_2.grades
          L8_2 = A2_2.grade
          if 0 == L8_2 then
            L8_2 = "0"
            if L8_2 then
              goto lbl_479
            end
          end
          L8_2 = A2_2.grade
          ::lbl_479::
          L7_2 = L7_2[L8_2]
          if L7_2 then
            L7_2 = CreateLog
            L8_2 = {}
            L8_2.type = "Management"
            L9_2 = {}
            L10_2 = Config
            L10_2 = L10_2.LogsTranslations
            L10_2 = L10_2.UpdatePlayer
            L10_2 = L10_2.title
            L9_2.title = L10_2
            L10_2 = Config
            L10_2 = L10_2.LogsTranslations
            L10_2 = L10_2.UpdatePlayer
            L10_2 = L10_2.message
            L11_2 = L10_2
            L10_2 = L10_2.format
            L12_2 = A2_2.citizenid
            L13_2 = A2_2.job
            L14_2 = A2_2.grade
            L10_2 = L10_2(L11_2, L12_2, L13_2, L14_2)
            L9_2.description = L10_2
            L9_2.color = 1791423
            L8_2.embed = L9_2
            L8_2.source = A0_2
            L7_2(L8_2)
            L7_2 = A1_2
            L8_2 = MySQL
            L8_2 = L8_2.awaitUpdate
            L9_2 = "UPDATE users SET job = ?, job_grade = ? WHERE identifier = ?"
            L10_2 = {}
            L11_2 = A2_2.job
            L12_2 = A2_2.grade
            if not L12_2 then
              L12_2 = "0"
            end
            L13_2 = A2_2.citizenid
            L10_2[1] = L11_2
            L10_2[2] = L12_2
            L10_2[3] = L13_2
            L8_2 = L8_2(L9_2, L10_2)
            L8_2 = L8_2 > 0
            L7_2(L8_2)
        end
        else
          L7_2 = A1_2
          L8_2 = false
          L7_2(L8_2)
        end
      end
    end
  end
  ::lbl_530::
end
L0_1(L1_1, L2_1)
