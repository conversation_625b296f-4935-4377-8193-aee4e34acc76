@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap');

* {
    margin: 0;
}

body {
    display: block;
    padding: 0;
    margin: 0;
    user-select: none;
    background-color: transparent !important;
    overflow: hidden;
}

::-webkit-scrollbar {
    width: 0.2vw;
    background: rgba(255, 255, 255, 0.17);
}

::-webkit-scrollbar-thumb {
	background: #FFF;
}

#mainDivEffect {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    display: none;
    /* background: linear-gradient(90deg, rgba(0, 0, 0, 0.70) 0%, rgba(0, 0, 0, 0.00) 29.77%), linear-gradient(270deg, rgba(0, 0, 0, 0.60) 0%, rgba(0, 0, 0, 0.00) 28.05%), linear-gradient(90deg, rgba(0, 0, 0, 0.29) 0%, rgba(0, 0, 0, 0.00) 51.02%), linear-gradient(270deg, rgba(0, 0, 0, 0.29) 0%, rgba(0, 0, 0, 0.00) 48.98%), linear-gradient(0deg, rgba(0, 0, 0, 0.60) 0%, rgba(0, 0, 0, 0.00) 42.87%), linear-gradient(0deg, rgba(0, 0, 0, 0.29) 0%, rgba(0, 0, 0, 0.00) 50%); */
    /* background: linear-gradient(90deg, rgba(21, 23, 29, 0.9) 0%, rgba(20, 22, 27, 0) 29.77%), linear-gradient(270deg, rgba(18, 19, 22, 0.9) 0%, rgba(17, 19, 24, 0) 28.05%), linear-gradient(90deg, rgba(21, 24, 31, 0.49) 0%, rgba(24, 26, 31, 0) 51.02%), linear-gradient(270deg, rgba(9, 10, 14, 0.49) 0%, rgba(15, 16, 19, 0) 48.98%), linear-gradient(0deg, rgba(16, 18, 22, 0.9) 0%, rgba(17, 19, 24, 0) 42.87%), linear-gradient(0deg, rgba(21, 24, 31, 0.49) 0%, rgba(27, 29, 34, 0) 50%); */
    /* background: linear-gradient(271deg, rgba(82, 203, 255, 0.20) 0.87%, rgba(36, 40, 50, 0.00) 37.29%), linear-gradient(270deg, rgba(82, 203, 255, 0.15) 0%, rgba(36, 40, 50, 0.00) 39.51%); */
    /* background: linear-gradient(271deg, rgba(36, 40, 50, 0.00) 0.87%, rgba(36, 40, 50, 0.50) 37.29%), linear-gradient(270deg, #242832 0%, rgba(36, 40, 50, 0.00) 39.51%); */
    /* background: linear-gradient(271deg, rgba(12, 14, 17, 0.4) 0.87%, rgba(15, 16, 20, 0) 57.29%), linear-gradient(270deg, #0e0f13f6 0%, rgba(20, 22, 26, 0) 61.51%); */
    z-index: -1;
    transform: rotate(180deg);
}

@font-face {
    font-family: ttfirs;
    src: url(fonts/ttfirs.ttf);
}

@font-face {
    font-family: 'Gilroy-Regular';
    font-weight: 400;
    font-style: normal;
    font-display: block;
    src: url('fonts/gilroy-regular-webfont.woff') format('woff');
}

@font-face {
    font-family: 'Gilroy-Medium';
    font-weight: 500;
    font-style: italic;
    font-display: block;
    src: url('fonts/gilroy-medium-webfont.woff') format('woff');
}

@font-face {
    font-family: 'Gilroy-SemiBold';
    src: url('fonts/gilroy-semibold-webfont.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: block;
}

@font-face {
    font-family: 'Gilroy-UltraLight';
    src: url('fonts/gilroy-ultralight-webfont.woff') format('woff');
    font-weight: 200;
    font-style: normal;
    font-display: block;
}

@font-face {
    font-family: 'Gilroy-BlackItalic';
    src: url('fonts/gilroy-blackitalic-webfont.woff') format('woff');
    font-weight: 900;
    font-style: italic;
    font-display: block;
}

@font-face {
    font-family: 'Gilroy-RegularItalic';
    src: url('fonts/gilroy-regularitalic-webfont.woff') format('woff');
    font-weight: 400;
    font-style: italic;
    font-display: block;
}

@font-face {
    font-family: AGENCYB;
    src: url(fonts/AGENCYB.ttf);
}

@font-face {
    font-family: AGENCYR;
    src: url(fonts/AGENCYR.ttf);
}

#pedDiv {
    width: 35%;
    height: 80%;
    position: absolute;
    left: 30%;
    right: 0;
    margin: auto;
    bottom: 0;
    /* background-color: red; */
}

#pedDiv2 {
    width: 25%;
    height: 80%;
    position: absolute;
    left: 17%;
    bottom: 0;
    display: flex;
    /* background-color: red; */
    z-index: -1;
}

#pedDiv3 {
    width: 25%;
    height: 80%;
    position: absolute;
    right: 15%;
    bottom: 0;
    display: flex;
    /* background-color: red; */
    z-index: -1;
}

.mainDiv {
    width: 29%;
    height: 94%;
    position: absolute;
    left: 2%;
    top: 0;
    bottom: 0;
    margin: auto;
    display: none;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    z-index: 2;
}

.mainDivLeft {
    width: 83%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    border-radius: 2px;
    background: rgba(6, 6, 6, 0.95);
    gap: 0.5vw;
}

.mainDivRight {
    width: 15%;
    height: 100%;
    position: relative;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background: rgba(6, 6, 6, 0.95);
    gap: 0.45vw;
}

.mainDivRightButton {
    width: 85%;
    height: 10%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 1px;
    border: 0.5px solid rgba(255, 255, 255, 0.25);
    background: rgba(255, 255, 255, 0.05);
}

.mainDivRightButton:hover path {
    fill: #00FFEA;
}

.mainDivRightButtonSelected {
    border: 0.5px solid rgba(0, 255, 234, 0.55);
    background: radial-gradient(74.18% 50% at 50% 50%, rgba(0, 255, 234, 0.14) 0%, rgba(0, 153, 140, 0.14) 100%);
}

.mainDivRightButtonSelected path {
    fill: #00FFEA;
}

.mainDivRightButton:hover {
    cursor: pointer;
    border: 0.5px solid rgba(0, 255, 234, 0.55);
    background: radial-gradient(74.18% 50% at 50% 50%, rgba(0, 255, 234, 0.14) 0%, rgba(0, 153, 140, 0.14) 100%);
}

#MDLTop {
    width: 96%;
    height: 16.3%;
    position: relative;
    background-image: url(files/menu/topBG.png);
    background-size: cover;
    background-repeat: no-repeat;
}

#MDLTopEffect {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    background: radial-gradient(55.15% 50% at 50% 50%, rgba(6, 6, 6, 0.95) 0%, rgba(6, 6, 6, 0.20) 100%);
    color: #FFF;
    text-transform: uppercase;
}

#MDLBottom {
    width: 96%;
    height: 4.5%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
}

.MDLBottomBtn {
    width: 32%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Gilroy-Regular';
    border-radius: 2px;
    text-transform: capitalize;
    font-size: 0.85vw;
}

.MDLBottomBtnRed {
    color: #FF3C00;
    border: 0.5px solid rgba(255, 60, 0, 0.55);
    background: radial-gradient(74.18% 50% at 50% 50%, rgba(255, 60, 0, 0.14) 0%, rgba(255, 60, 0, 0.14) 100%);
}

.MDLBottomBtnRed:hover {
    cursor: pointer;
    background: radial-gradient(74.18% 50% at 50% 50%, rgba(255, 60, 0, 0.24) 0%, rgba(255, 60, 0, 0.24) 100%);
}

.MDLBottomBtnGreen {
    color: #00FFEA;
    border: 0.5px solid rgba(0, 255, 234, 0.55);
    background: radial-gradient(74.18% 50% at 50% 50%, rgba(0, 255, 234, 0.14) 0%, rgba(0, 153, 140, 0.14) 100%);
}

.MDLBottomBtnGreen:hover {
    cursor: pointer;
    background: radial-gradient(74.18% 50% at 50% 50%, rgba(0, 255, 234, 0.24) 0%, rgba(0, 153, 140, 0.24) 100%);
}

.MDLBottomBtnPink {
    color: #FF54E8;
    border: 0.5px solid rgba(255, 84, 232, 0.55);
    background: radial-gradient(63.04% 63.04% at 50% 50%, rgba(255, 84, 232, 0.14) 0%, rgba(183, 48, 165, 0.14) 100%);
}

.MDLBottomBtnPink:hover {
    cursor: pointer;
    background: radial-gradient(63.04% 63.04% at 50% 50%, rgba(255, 84, 232, 0.24) 0%, rgba(183, 48, 165, 0.24) 100%);
}

#MDLCenter {
    width: 96%;
    height: 75%;
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
    overflow-y: scroll;
    overflow-x: hidden;
    gap: 0.5vw;
    /* background-color: red; */
}

.MDLCDiv {
    width: 98%;
    height: fit-content;
    /* max-height: 15.6vh; */
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    /* transition: max-height 0.8s ease; */
    gap: 0.45vw;
}

.MDLCDivTop {
    width: 100%;
    height: fit-content;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    /* background: rgba(255, 255, 255, 0.05); */
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.15) 0%, rgba(150, 150, 150, 0.05) 100%);
    color: #FFF;
    font-family: 'Gilroy-Medium';
    padding-top: 0.25vw;
    padding-bottom: 0.25vw;
}

.MDLCDivTopSpan {
    width: 18.7vw;
}

#MDLCDivTExpandDiv {
    width: fit-content;
    height: fit-content;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
    color: #FFF;
    /* text-shadow: 0px 0px 10.2px rgba(93, 224, 177, 0.99); */
    font-family: 'Gilroy-Medium';
    text-align: center;
    font-size: 0.6vw;
    gap: 0.3vw;
}

.MDLCDivTExpandDivBtn {
    width: fit-content;
    height: fit-content;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    background: rgba(255, 255, 255, 0.12);
    font-size: 0.4vw;
    text-align: center;
    padding-left: 0.23vw;
    padding-right: 0.23vw;
    padding-top: 0.25vw;
    padding-bottom: 0.25vw;
}

.MDLCDivTExpandDivBtn:hover {
    cursor: pointer;
}

.MDLCDivBottom {
    width: 21.35vw;
    height: fit-content;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 0.5vw;
    background: radial-gradient(74.18% 50% at 50% 50%, rgba(255, 255, 255, 0.05) 0%, rgba(150, 150, 150, 0.05) 100%);
    border: 0.5px solid #ffffff54;
    padding-left: 0.5vw;
    padding-right: 0.5vw;
    padding-top: 0.55vw;
    padding-bottom: 0.55vw;
}

.MDLCDivBottomInside {
    width: 100%;
    height: fit-content;
    max-height: 10.6vh;
    transition: max-height 0.8s ease;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: flex-start;
    /* padding-left: 0.5vw;
    padding-right: 0.5vw;
    padding-top: 0.52vw;
    padding-bottom: 0.52vw; */
    gap: 0.5vw 0.5vw;
    overflow-x: hidden;
    overflow-y: scroll;
}

.MDLCDivBDiv {
    width: 4.75vw;
    height: fit-content;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 1.071px;
    border: 0.5px solid rgba(255, 255, 255, 0.25);
    background: rgba(255, 255, 255, 0.05);
    padding-top: 2.84vw;
    padding-bottom: 2.84vw;
    overflow: hidden;
}

.MDLCDivBDiv:hover {
    cursor: pointer;
    border: 0.5px solid rgba(0, 255, 234, 0.55);
    background: radial-gradient(74.18% 50% at 50% 50%, rgba(0, 255, 234, 0.14) 0%, rgba(0, 153, 140, 0.14) 100%);
}

.MDLCDivBDivSelected {
    border: 0.5px solid rgba(0, 255, 234, 0.55);
    background: radial-gradient(74.18% 50% at 50% 50%, rgba(0, 255, 234, 0.14) 0%, rgba(0, 153, 140, 0.14) 100%);
}

.MDLCDBVariations {
    width: 100%;
    height: fit-content;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: row;
    gap: 0.6vw;
    color: #FFF;
    font-size: 0.8vw;
    font-family: 'Gilroy-Regular';
}

.MDLCDBVariationInputDiv {
    width: 42%;
    height: fit-content;
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
    gap: 0.5vw;
    color: #FFF;
    font-size: 0.7vw;
    font-family: 'Gilroy-Regular';
    padding-top: 0.5vw;
    padding-bottom: 0.5vw;
    padding-left: 0.6vw;
    padding-right: 0.6vw;
    border-radius: 1.071px;
    border: 0.5px solid rgba(255, 255, 255, 0.25);
    background: rgba(255, 255, 255, 0.05);
}

.MDLCDBVariationDiv {
    width: 8.9vw;
    height: fit-content;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    border-radius: 1.071px;
    border: 0.5px solid rgba(255, 255, 255, 0.25);
    background: rgba(255, 255, 255, 0.05);
    padding-left: 0.6vw;
    padding-right: 0.6vw;
    padding-top: 0.5vw;
    padding-bottom: 0.5vw;
    color: #FFF;
    font-size: 0.8vw;
    font-family: 'Gilroy-Regular';
}

.MDLCDBVariationDiv2 {
    width: 21.5vw;
    height: fit-content;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    border-radius: 1.071px;
    border: 0.5px solid rgba(255, 255, 255, 0.25);
    background: rgba(255, 255, 255, 0.05);
    padding-left: 0.6vw;
    padding-right: 0.6vw;
    padding-top: 0.5vw;
    padding-bottom: 0.5vw;
    color: #FFF;
    font-size: 0.8vw;
    font-family: 'Gilroy-Regular';
}

.MDLCDBVDBtn {
    width: 19%;
    height: fit-content;
    position: relative;
    border-radius: 1px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 0.5px solid rgba(255, 255, 255, 0.25);
    background: rgba(255, 255, 255, 0.05);
    padding-bottom: 0.5vw;
    padding-top: 0.5vw;
    font-size: 0.5vw;
}

.MDLCDBVDBtn:hover, .MDLCDBVDBtn2:hover {
    cursor: pointer;
}

.MDLCDBVDBtn2 {
    width: 8%;
    height: fit-content;
    position: relative;
    border-radius: 1px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 0.5px solid rgba(255, 255, 255, 0.25);
    background: rgba(255, 255, 255, 0.05);
    padding-bottom: 0.5vw;
    padding-top: 0.5vw;
    font-size: 0.5vw;
}

.MDLCDivBDiv img {
    position: absolute;
    top: 10%;
    width: 3.5vw;
}

.MDLCDivBDivBigIMGSelected img {
    position: absolute;
    top: 3%;
    width: 5.2vw;
}

.MDLCDivBDivBigIMG img {
    position: absolute;
    width: 3.9vw;
}

.MDLCDivBDivBigIMG:hover img {
    position: absolute;
    top: 3%;
    transition: 200ms ease-in-out;
    width: 5.2vw;
}

.MDLCDivBDivBigIMGSelected:hover img {
    position: absolute;
    top: 3%;
    width: 5.2vw;
}

.MDLCDivBDivBigIMG2 img {
    position: absolute;
    top: -8%;
    width: 6.5vw;
}

.MDLCDivBDivBigIMG2Selected img {
    position: absolute;
    top: -29%;
    width: 9vw;
}

.MDLCDivBDivBigIMG2Selected:hover img {
    position: absolute;
    top: -35%;
    width: 10vw;
}

.MDLCDivBDivBigIMG2:hover img {
    position: absolute;
    top: -36%;
    transition: 200ms ease-in-out;
    width: 10vw;
}

.MDLCDivBDivBigIMG3 img {
    position: absolute;
    top: -20%;
    width: 6vw;
}

.MDLCDivBDivBigIMG3Selected img {
    position: absolute;
    top: -35%;
    transition: 200ms ease-in-out;
    width: 7vw;
}

.MDLCDivBDivBigIMG3Selected:hover img {
    position: absolute;
    top: -35%;
    transition: 200ms ease-in-out;
    width: 7vw;
}

.MDLCDivBDivBigIMG3:hover img {
    position: absolute;
    top: -35%;
    transition: 200ms ease-in-out;
    width: 7vw;
}

.MDLCDivBDivBigIMGMask img {
    position: absolute;
    top: 0;
    bottom: 0;
    margin: auto;
    width: 6vw;
}

.MDLCDivBDivBigIMGMaskSelected img {
    position: absolute;
    top: 17%;
    width: 10vw;
    bottom: 0;
    margin: auto;
}

.MDLCDivBDivBigIMGMaskSelected:hover img {
    position: absolute;
    top: 17%;
    width: 10vw;
    bottom: 0;
    margin: auto;
}

.MDLCDivBDivBigIMGMask:hover img {
    position: absolute;
    top: 17%;
    transition: 200ms ease-in-out;
    width: 10vw;
}

.MDLCDivBDivBigIMGGlasses img {
    position: absolute;
    top: 0;
    bottom: 0;
    margin: auto;
    width: 6vw;
    left: -3%;
}

.MDLCDivBDivBigIMGGlasses:hover img {
    position: absolute;
    top: 12%;
    transition: 200ms ease-in-out;
    width: 9vw;
    left: -31%;
}

#mainDivBottomLeftBottomDivSpanDiv {
    width: 87%;
    height: fit-content;
    position: absolute;
    bottom: 3%;
    left: 0;
    right: 0;
    margin: auto;
    text-align: right;
    color: #FFF;
    /* text-shadow: 0px 0px 10.2px rgba(93, 224, 177, 0.99); */
    font-weight: 500;
    font-family: 'Gilroy-Regular';
    font-size: 0.6vw;
}

#mainDivBottomLeftBottomDivSpanDiv2 {
    width: 87%;
    height: fit-content;
    position: absolute;
    text-align: right;
    color: #FFF;
    /* text-shadow: 0px 0px 10.2px rgba(93, 224, 177, 0.99); */
    font-weight: 500;
    font-family: 'Gilroy-Regular';
    font-size: 0.6vw;
}

#mainDivDialogBG {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 2;
    display: none;
}

#mainDivDialog {
    width: fit-content;
    height: 14%;
    position: absolute;
    left: 0;
    right: 0;
    /* top: 0; */
    /* bottom: 0; */
    bottom: 2%;
    margin: auto;
    border-radius: 15px;
    background: rgba(26, 26, 26, 0);
    display: none;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding-left: 1.5vw;
    padding-right: 1.5vw;
    gap: 0.6vw;
    z-index: 3;
}

#mainDivDialog h4 {
    color: #FFF;
    font-family: 'Gilroy-SemiBold';
    font-weight: 600;
    font-size: 0.9vw;
}

#mainDivDialog span {
    color: rgba(255, 255, 255, 0.48);
    font-family: 'Gilroy-Regular';
    font-weight: 500;
    font-size: 0.6vw;
}

#mainDivDialogButtons {
    width: fit-content;
    height: 24%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
    font-family: 'Gilroy-Medium';
    font-weight: 500;
    font-size: 0.6vw;
    gap: 0.4vw;
}

.mainDivDialogButtonGreen {
    width: 31%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    border: 0.5px solid rgba(0, 255, 234, 0.55);
    background: radial-gradient(74.18% 50% at 50% 50%, rgba(0, 255, 234, 0.14) 0%, rgba(0, 153, 140, 0.14) 100%);
    color: #00FFEA;
    padding-left: 1.4vw;
    padding-right: 1.4vw;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.mainDivDialogButtonGreen:hover {
    cursor: pointer;
    background: radial-gradient(74.18% 50% at 50% 50%, rgba(0, 255, 234, 0.24) 0%, rgba(0, 153, 140, 0.24) 100%);
}

.mainDivDialogButtonRed {
    width: 31%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    border: 0.5px solid rgba(255, 60, 0, 0.55);
    background: radial-gradient(74.18% 50% at 50% 50%, rgba(255, 60, 0, 0.14) 0%, rgba(255, 60, 0, 0.14) 100%);
    color: #FF3C00;
    padding-left: 1.4vw;
    padding-right: 1.4vw;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.mainDivDialogButtonRed:hover {
    cursor: pointer;
    background: radial-gradient(74.18% 50% at 50% 50%, rgba(255, 60, 0, 0.24) 0%, rgba(255, 60, 0, 0.24) 100%);
}

#mainDivOutsideButtons {
    width: 27%;
    height: 71.5%;
    position: absolute;
    left: 26.8%;
    display: none;
    top: 3%;
    /* bottom: 0; */
    /* margin: auto; */
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
    gap: 0.4vw;
    z-index: 2;
    /* background-color: red; */
}

.mainDivOutsideButtonDiv {
    width: 100%;
    height: 34px; /* Sabit yükseklik */
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: row;
    gap: 0.4vw;
    /* background-color: red; */
}

.mainDivOutsideButton {
    width: 34px; /* Sabit genişlik */
    height: 34px; /* Sabit yükseklik */
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.6vw;
    border-radius: 1.071px;
    border: 0.5px solid rgba(255, 255, 255, 0.25);
    background: rgba(0, 0, 0, 0.65);
    flex-shrink: 0;
    color: rgba(255, 255, 255, 0.55);
}

.mainDivOutsideButton i {
    font-size: 0.5vw; /* İkon boyutunu sabit tut */
}

.mainDivOutsideButton:hover {
    cursor: pointer;
    border: 0.5px solid rgba(0, 255, 234, 0.55);
    background: radial-gradient(74.18% 50% at 50% 50%, rgba(0, 255, 234, 0.14) 0%, rgba(0, 153, 140, 0.14) 100%);
    color: #00FFEA;
}

.mainDivOutsideButtonActive {
    border: 0.5px solid rgba(0, 255, 234, 0.55);
    background: radial-gradient(74.18% 50% at 50% 50%, rgba(0, 255, 234, 0.14) 0%, rgba(0, 153, 140, 0.14) 100%);
    color: #00FFEA;
}

#mouseInfosDiv {
    position: absolute;
    right: 2%;
    top: 7%;
    width: 220px;
    /* background: rgba(0, 0, 0, 0.6); */
    border-radius: 2px;
    background: rgba(6, 6, 6, 0.95);
    padding: 12px;
    font-family: 'Gilroy-Medium', sans-serif;
    font-size: 13px;
    color: #fff;
    display: none;
    flex-direction: column;
    gap: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.mouseInfo {
    display: flex;
    align-items: center;
    gap: 10px;
    border-radius: 1px;
    border: 0.5px solid rgba(255, 255, 255, 0.25);
    background: rgba(255, 255, 255, 0.05);
    padding: 8px 12px;
    border-radius: 6px;
}

.mouseInfo i {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.55);
    min-width: 18px;
}

.mainDivBottomLeftBottomDivBottomInputSlider, .mainDivBottomLeftBottomDivBottomInputSlider2 {
    width: 100%;
    height: 5px !important;
    -webkit-appearance: none;
    appearance: none;
    -webkit-transition: .2s;
    transition: opacity .2s;
    background: radial-gradient(124.16% 111.18% at 50% 50%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.00)100%);
}

.mainDivBottomLeftBottomDivBottomInputSlider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 14px;
    height: 9px;
    background: #FFF;
    cursor: e-resize;
}

.mainDivBottomLeftBottomDivBottomInputSlider2::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 14px;
    height: 9px;
    background: #FFF;
    cursor: e-resize;
}

.MDLCDivBDivColor {
    width: 5.45%;
    height: fit-content;
    position: relative;
    display: flex;
    padding-top: 0.55vw;
    padding-bottom: 0.55vw;
    border-radius: 0.964px;
    border: 0.482px solid rgba(255, 255, 255, 0.25);
    background: rgba(255, 255, 255, 0.05);
}

.MDLCDivBDivColor:hover {
    transition: 200ms ease-in-out;
    cursor: pointer;
    border: 0.5px solid rgba(0, 255, 234, 0.55);
}

.MDLCDivBottomInput {
    width: 100%;
    height: fit-content;
    outline: 0;
    position: relative;
    text-align: center;
    border-radius: 1px;
    border: 0.5px solid rgba(255, 255, 255, 0.25);
    background: rgba(255, 255, 255, 0.05);
    color: #FFF;
    font-family: 'Gilroy-Regular';
    padding-top: 0.5vw;
    padding-bottom: 0.5vw;
}

.MDLCDivBottomInput::placeholder {
    font-family: 'Gilroy-Regular';
    color: #ffffff73;
}

#animPosInfoDiv {
    width: fit-content;
    height: 5%;
    position: absolute;
    right: 2%;
    bottom: -10%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
    border-radius: 6px;
    background: rgba(6, 6, 6, 0.95);
    padding-left: 0.7vw;
    padding-right: 0.7vw;
    gap: 0.6vw;
    /* padding: 0.6vw; */
}

#APIDKeyDiv {
    width: fit-content;
    height: 55%;
    border-radius: 2px;
    border: 1px solid rgba(255, 255, 255, 0.25);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    overflow: hidden;
}
  
#APIDKeyDivLeft {
    width: fit-content;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    font-family: 'Gilroy-Regular';
    padding-left: 0.6vw;
    padding-right: 0.6vw;
    font-size: 0.65vw;
    padding-top: 0.1vw;
}
  
#APIDKeyDivLeft span {
    color: rgba(255, 255, 255, 0.50);
}
  
#APIDKeyDivRight {
    width: fit-content;
    height: 100%;
    border-radius: 0px 1px 1px 0px;
    background: rgba(255, 255, 255, 0.05);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    /* color: rgba(255, 255, 255, 0.40); */
    font-family: "Inter", sans-serif;
    font-optical-sizing: auto;
    font-weight: 400;
    font-style: normal;
    font-variation-settings: "slnt" 0;
    font-size: 0.7vw;
    padding-left: 0.6vw;
    padding-right: 0.6vw;
}
  
#APIDKeyDivRight span {
    color: #00FFEA;
    /* text-shadow: 0px 0px 25.954px rgba(93, 224, 177, 0.74); */
}