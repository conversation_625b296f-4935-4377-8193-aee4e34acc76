{"manifest": {"name": "through2", "version": "2.0.5", "description": "A tiny wrapper around Node streams2 Transform to avoid explicit subclassing noise", "main": "through2.js", "scripts": {"test": "node test/test.js | faucet"}, "repository": {"type": "git", "url": "https://github.com/rvagg/through2.git"}, "keywords": ["stream", "streams2", "through", "transform"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/rvagg"}, "license": "MIT", "dependencies": {"readable-stream": "~2.3.6", "xtend": "~4.0.1"}, "devDependencies": {"bl": "~2.0.1", "faucet": "0.0.1", "nyc": "~13.1.0", "safe-buffer": "~5.1.2", "stream-spigot": "~3.0.6", "tape": "~4.9.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-through2-2.0.5-01c1e39eb31d07cb7d03a96a70823260b23132cd-integrity\\node_modules\\through2\\package.json", "readmeFilename": "README.md", "readme": "# through2\n\n[![NPM](https://nodei.co/npm/through2.png?downloads&downloadRank)](https://nodei.co/npm/through2/)\n\n**A tiny wrapper around Node streams.Transform (Streams2/3) to avoid explicit subclassing noise**\n\nInspired by [<PERSON>](https://github.com/dominictarr)'s [through](https://github.com/dominictarr/through) in that it's so much easier to make a stream out of a function than it is to set up the prototype chain properly: `through(function (chunk) { ... })`.\n\nNote: As 2.x.x this module starts using **Streams3** instead of Stream2. To continue using a Streams2 version use `npm install through2@0` to fetch the latest version of 0.x.x. More information about Streams2 vs Streams3 and recommendations see the article **[Why I don't use Node's core 'stream' module](http://r.va.gg/2014/06/why-i-dont-use-nodes-core-stream-module.html)**.\n\n```js\nfs.createReadStream('ex.txt')\n  .pipe(through2(function (chunk, enc, callback) {\n    for (var i = 0; i < chunk.length; i++)\n      if (chunk[i] == 97)\n        chunk[i] = 122 // swap 'a' for 'z'\n\n    this.push(chunk)\n\n    callback()\n   }))\n  .pipe(fs.createWriteStream('out.txt'))\n  .on('finish', () => doSomethingSpecial())\n```\n\nOr object streams:\n\n```js\nvar all = []\n\nfs.createReadStream('data.csv')\n  .pipe(csv2())\n  .pipe(through2.obj(function (chunk, enc, callback) {\n    var data = {\n        name    : chunk[0]\n      , address : chunk[3]\n      , phone   : chunk[10]\n    }\n    this.push(data)\n\n    callback()\n  }))\n  .on('data', (data) => {\n    all.push(data)\n  })\n  .on('end', () => {\n    doSomethingSpecial(all)\n  })\n```\n\nNote that `through2.obj(fn)` is a convenience wrapper around `through2({ objectMode: true }, fn)`.\n\n## API\n\n<b><code>through2([ options, ] [ transformFunction ] [, flushFunction ])</code></b>\n\nConsult the **[stream.Transform](http://nodejs.org/docs/latest/api/stream.html#stream_class_stream_transform)** documentation for the exact rules of the `transformFunction` (i.e. `this._transform`) and the optional `flushFunction` (i.e. `this._flush`).\n\n### options\n\nThe options argument is optional and is passed straight through to `stream.Transform`. So you can use `objectMode:true` if you are processing non-binary streams (or just use `through2.obj()`).\n\nThe `options` argument is first, unlike standard convention, because if I'm passing in an anonymous function then I'd prefer for the options argument to not get lost at the end of the call:\n\n```js\nfs.createReadStream('/tmp/important.dat')\n  .pipe(through2({ objectMode: true, allowHalfOpen: false },\n    (chunk, enc, cb) => {\n      cb(null, 'wut?') // note we can use the second argument on the callback\n                       // to provide data as an alternative to this.push('wut?')\n    }\n  )\n  .pipe(fs.createWriteStream('/tmp/wut.txt'))\n```\n\n### transformFunction\n\nThe `transformFunction` must have the following signature: `function (chunk, encoding, callback) {}`. A minimal implementation should call the `callback` function to indicate that the transformation is done, even if that transformation means discarding the chunk.\n\nTo queue a new chunk, call `this.push(chunk)`&mdash;this can be called as many times as required before the `callback()` if you have multiple pieces to send on.\n\nAlternatively, you may use `callback(err, chunk)` as shorthand for emitting a single chunk or an error.\n\nIf you **do not provide a `transformFunction`** then you will get a simple pass-through stream.\n\n### flushFunction\n\nThe optional `flushFunction` is provided as the last argument (2nd or 3rd, depending on whether you've supplied options) is called just prior to the stream ending. Can be used to finish up any processing that may be in progress.\n\n```js\nfs.createReadStream('/tmp/important.dat')\n  .pipe(through2(\n    (chunk, enc, cb) => cb(null, chunk), // transform is a noop\n    function (cb) { // flush function\n      this.push('tacking on an extra buffer to the end');\n      cb();\n    }\n  ))\n  .pipe(fs.createWriteStream('/tmp/wut.txt'));\n```\n\n<b><code>through2.ctor([ options, ] transformFunction[, flushFunction ])</code></b>\n\nInstead of returning a `stream.Transform` instance, `through2.ctor()` returns a **constructor** for a custom Transform. This is useful when you want to use the same transform logic in multiple instances.\n\n```js\nvar FToC = through2.ctor({objectMode: true}, function (record, encoding, callback) {\n  if (record.temp != null && record.unit == \"F\") {\n    record.temp = ( ( record.temp - 32 ) * 5 ) / 9\n    record.unit = \"C\"\n  }\n  this.push(record)\n  callback()\n})\n\n// Create instances of FToC like so:\nvar converter = new FToC()\n// Or:\nvar converter = FToC()\n// Or specify/override options when you instantiate, if you prefer:\nvar converter = FToC({objectMode: true})\n```\n\n## See Also\n\n  - [through2-map](https://github.com/brycebaril/through2-map) - Array.prototype.map analog for streams.\n  - [through2-filter](https://github.com/brycebaril/through2-filter) - Array.prototype.filter analog for streams.\n  - [through2-reduce](https://github.com/brycebaril/through2-reduce) - Array.prototype.reduce analog for streams.\n  - [through2-spy](https://github.com/brycebaril/through2-spy) - Wrapper for simple stream.PassThrough spies.\n  - the [mississippi stream utility collection](https://github.com/maxogden/mississippi) includes `through2` as well as many more useful stream modules similar to this one\n\n## License\n\n**through2** is Copyright (c) Rod Vagg [@rvagg](https://twitter.com/rvagg) and additional contributors and licensed under the MIT license. All rights not explicitly granted in the MIT license are reserved. See the included LICENSE file for more details.\n", "licenseText": "# The MIT License (MIT)\n\n**Copyright (c) <PERSON> (the \"Original Author\") and additional contributors**\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON><PERSON>NFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/through2/-/through2-2.0.5.tgz#01c1e39eb31d07cb7d03a96a70823260b23132cd", "type": "tarball", "reference": "https://registry.yarnpkg.com/through2/-/through2-2.0.5.tgz", "hash": "01c1e39eb31d07cb7d03a96a70823260b23132cd", "integrity": "sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ==", "registry": "npm", "packageName": "through2", "cacheIntegrity": "sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ== sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0="}, "registry": "npm", "hash": "01c1e39eb31d07cb7d03a96a70823260b23132cd"}