{"manifest": {"name": "is-glob", "description": "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a better user experience.", "version": "3.1.0", "homepage": "https://github.com/jonschlinkert/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://tuvistavie.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-glob.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-glob/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-extglob": "^2.1.0"}, "devDependencies": {"gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "is", "match", "matches", "pattern", "regex", "regular", "string", "test"], "verb": {"layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["assemble", "base", "update", "verb"]}, "reflinks": ["assemble", "bach", "base", "composer", "gulp", "has-glob", "is-valid-glob", "micromatch", "npm", "scaffold", "verb", "vinyl"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-glob-3.1.0-7ba5ae24217804ac70707b96922567486cc3e84a-integrity\\node_modules\\is-glob\\package.json", "readmeFilename": "README.md", "readme": "# is-glob [![NPM version](https://img.shields.io/npm/v/is-glob.svg?style=flat)](https://www.npmjs.com/package/is-glob) [![NPM downloads](https://img.shields.io/npm/dm/is-glob.svg?style=flat)](https://npmjs.org/package/is-glob) [![Build Status](https://img.shields.io/travis/jonschlinkert/is-glob.svg?style=flat)](https://travis-ci.org/jonschlinkert/is-glob)\n\n> Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a better user experience.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save is-glob\n```\n\nYou might also be interested in [is-valid-glob](https://github.com/jonschlinkert/is-valid-glob) and [has-glob](https://github.com/jonschlinkert/has-glob).\n\n## Usage\n\n```js\nvar isGlob = require('is-glob');\n```\n\n**True**\n\nPatterns that have glob characters or regex patterns will return `true`:\n\n```js\nisGlob('!foo.js');\nisGlob('*.js');\nisGlob('**/abc.js');\nisGlob('abc/*.js');\nisGlob('abc/(aaa|bbb).js');\nisGlob('abc/[a-z].js');\nisGlob('abc/{a,b}.js');\nisGlob('abc/?.js');\n//=> true\n```\n\nExtglobs\n\n```js\nisGlob('abc/@(a).js');\nisGlob('abc/!(a).js');\nisGlob('abc/+(a).js');\nisGlob('abc/*(a).js');\nisGlob('abc/?(a).js');\n//=> true\n```\n\n**False**\n\nEscaped globs or extglobs return `false`:\n\n```js\nisGlob('abc/\\\\@(a).js');\nisGlob('abc/\\\\!(a).js');\nisGlob('abc/\\\\+(a).js');\nisGlob('abc/\\\\*(a).js');\nisGlob('abc/\\\\?(a).js');\nisGlob('\\\\!foo.js');\nisGlob('\\\\*.js');\nisGlob('\\\\*\\\\*/abc.js');\nisGlob('abc/\\\\*.js');\nisGlob('abc/\\\\(aaa|bbb).js');\nisGlob('abc/\\\\[a-z].js');\nisGlob('abc/\\\\{a,b}.js');\nisGlob('abc/\\\\?.js');\n//=> false\n```\n\nPatterns that do not have glob patterns return `false`:\n\n```js\nisGlob('abc.js');\nisGlob('abc/def/ghi.js');\nisGlob('foo.js');\nisGlob('abc/@.js');\nisGlob('abc/+.js');\nisGlob();\nisGlob(null);\n//=> false\n```\n\nArrays are also `false` (If you want to check if an array has a glob pattern, use [has-glob](https://github.com/jonschlinkert/has-glob)):\n\n```js\nisGlob(['**/*.js']);\nisGlob(['foo.js']);\n//=> false\n```\n\n## About\n\n### Related projects\n\n* [assemble](https://www.npmjs.com/package/assemble): Get the rocks out of your socks! Assemble makes you fast at creating web projects… [more](https://github.com/assemble/assemble) | [homepage](https://github.com/assemble/assemble \"Get the rocks out of your socks! Assemble makes you fast at creating web projects. Assemble is used by thousands of projects for rapid prototyping, creating themes, scaffolds, boilerplates, e-books, UI components, API documentation, blogs, building websit\")\n* [base](https://www.npmjs.com/package/base): base is the foundation for creating modular, unit testable and highly pluggable node.js applications, starting… [more](https://github.com/node-base/base) | [homepage](https://github.com/node-base/base \"base is the foundation for creating modular, unit testable and highly pluggable node.js applications, starting with a handful of common methods, like `set`, `get`, `del` and `use`.\")\n* [update](https://www.npmjs.com/package/update): Be scalable! Update is a new, open source developer framework and CLI for automating updates… [more](https://github.com/update/update) | [homepage](https://github.com/update/update \"Be scalable! Update is a new, open source developer framework and CLI for automating updates of any kind in code projects.\")\n* [verb](https://www.npmjs.com/package/verb): Documentation generator for GitHub projects. Verb is extremely powerful, easy to use, and is used… [more](https://github.com/verbose/verb) | [homepage](https://github.com/verbose/verb \"Documentation generator for GitHub projects. Verb is extremely powerful, easy to use, and is used on hundreds of projects of all sizes to generate everything from API docs to readmes.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor**<br/> | \n| --- | --- |\n| 40 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 1 | [tuvistavie](https://github.com/tuvistavie) |\n\n### Building docs\n\n_(This document was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme) (a [verb](https://github.com/verbose/verb) generator), please don't edit the readme directly. Any changes to the readme must be made in [.verb.md](.verb.md).)_\n\nTo generate the readme and API documentation with [verb](https://github.com/verbose/verb):\n\n```sh\n$ npm install -g verb verb-generate-readme && verb\n```\n\n### Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm install -d && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT license](https://github.com/jonschlinkert/is-glob/blob/master/LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.1.31, on October 12, 2016._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2016, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-glob/-/is-glob-3.1.0.tgz#7ba5ae24217804ac70707b96922567486cc3e84a", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-glob/-/is-glob-3.1.0.tgz", "hash": "7ba5ae24217804ac70707b96922567486cc3e84a", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "registry": "npm", "packageName": "is-glob", "cacheIntegrity": "sha512-UFpDDrPgM6qpnFNI+rh/p3bUaq9hKLZN8bMUWzxmcnZVS3omf4IPK+BrewlnWjO1WmUsMYuSjKh4UJuV4+Lqmw== sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo="}, "registry": "npm", "hash": "7ba5ae24217804ac70707b96922567486cc3e84a"}