{"name": "koa-compose", "description": "compose Koa middleware", "repository": "koajs/compose", "version": "3.2.1", "keywords": ["koa", "middleware", "compose"], "files": ["index.js"], "dependencies": {"any-promise": "^1.1.0"}, "devDependencies": {"co": "^4.6.0", "istanbul": "^0.4.2", "matcha": "^0.7.0", "mocha": "^3.1.2", "should": "^2.0.0", "standard": "^8.4.0"}, "scripts": {"bench": "matcha bench/bench.js", "lint": "standard index.js test/*.js", "test": "mocha --require should --reporter spec", "test-cov": "node ./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --require should", "test-travis": "node ./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --require should"}, "publishConfig": {"tag": "next"}, "license": "MIT"}