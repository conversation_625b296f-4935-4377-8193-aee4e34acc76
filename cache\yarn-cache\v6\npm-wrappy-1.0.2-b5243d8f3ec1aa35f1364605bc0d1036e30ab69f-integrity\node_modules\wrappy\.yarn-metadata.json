{"manifest": {"name": "wrappy", "version": "1.0.2", "description": "Callback wrapping utility", "main": "wrappy.js", "files": ["wrappy.js"], "dependencies": {}, "devDependencies": {"tap": "^2.3.1"}, "scripts": {"test": "tap --coverage test/*.js"}, "repository": {"type": "git", "url": "https://github.com/npm/wrappy"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "bugs": {"url": "https://github.com/npm/wrappy/issues"}, "homepage": "https://github.com/npm/wrappy", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-wrappy-1.0.2-b5243d8f3ec1aa35f1364605bc0d1036e30ab69f-integrity\\node_modules\\wrappy\\package.json", "readmeFilename": "README.md", "readme": "# wrappy\n\nCallback wrapping utility\n\n## USAGE\n\n```javascript\nvar wrappy = require(\"wrappy\")\n\n// var wrapper = wrappy(wrapperFunction)\n\n// make sure a cb is called only once\n// See also: http://npm.im/once for this specific use case\nvar once = wrappy(function (cb) {\n  var called = false\n  return function () {\n    if (called) return\n    called = true\n    return cb.apply(this, arguments)\n  }\n})\n\nfunction printBoo () {\n  console.log('boo')\n}\n// has some rando property\nprintBoo.iAmBooPrinter = true\n\nvar onlyPrintOnce = once(printBoo)\n\nonlyPrintOnce() // prints 'boo'\nonlyPrintOnce() // does nothing\n\n// random property is retained!\nassert.equal(onlyPrintOnce.iAmBooPrinter, true)\n```\n", "licenseText": "The ISC License\n\nCopyright (c) <PERSON> and Contributors\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f", "type": "tarball", "reference": "https://registry.yarnpkg.com/wrappy/-/wrappy-1.0.2.tgz", "hash": "b5243d8f3ec1aa35f1364605bc0d1036e30ab69f", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "registry": "npm", "packageName": "wrappy", "cacheIntegrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ== sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}, "registry": "npm", "hash": "b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"}