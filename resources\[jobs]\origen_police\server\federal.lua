local L0_1, L1_1, L2_1, L3_1
L0_1 = {}
L1_1 = Citizen
L1_1 = L1_1.CreateThread
function L2_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L0_2 = MySQL
  L0_2 = L0_2.awaitQuery
  L1_2 = "SELECT * FROM origen_police_federal"
  L0_2 = L0_2(L1_2)
  L1_2 = 1
  L2_2 = #L0_2
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = L0_2[L4_2]
    L6_2 = L5_2.citizenid
    L5_2 = L0_1
    L7_2 = L0_2[L4_2]
    L5_2[L6_2] = L7_2
  end
end
L1_1(L2_1)
L1_1 = FW_CreateCallback
L2_1 = "origen_police:callback:GetFederal"
function L3_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2
  if not A2_2 then
    L3_2 = FW_GetPlayer
    L4_2 = A0_2
    L3_2 = L3_2(L4_2)
    if L3_2 then
      L4_2 = L3_2.PlayerData
      if L4_2 then
        L4_2 = L3_2.PlayerData
        L4_2 = L4_2.citizenid
        if L4_2 then
          L4_2 = L3_2.PlayerData
          A2_2 = L4_2.citizenid
      end
    end
    else
      L4_2 = A1_2
      L5_2 = {}
      L4_2(L5_2)
    end
  else
    L3_2 = A1_2
    L4_2 = L0_1
    L4_2 = L4_2[A2_2]
    if not L4_2 then
      L4_2 = {}
    end
    L3_2(L4_2)
  end
end
L1_1(L2_1, L3_1)
L1_1 = RegisterServerEvent
L2_1 = "origen_police:server:setfederal"
function L3_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  L3_2 = FW_GetPlayer
  L4_2 = source
  L3_2 = L3_2(L4_2)
  if L3_2 then
    L4_2 = L3_2.PlayerData
    if L4_2 then
      if A0_2 then
        L4_2 = L3_2.Functions
        L4_2 = L4_2.SetMetaData
        L5_2 = "federal"
        L6_2 = 1
        L4_2(L5_2, L6_2)
        L4_2 = L3_2.PlayerData
        L5_2 = L4_2.citizenid
        L4_2 = L0_1
        L4_2 = L4_2[L5_2]
        if not L4_2 then
          L4_2 = Config
          L4_2 = L4_2.Framework
          if "esx" == L4_2 then
            L4_2 = "users"
            if L4_2 then
              goto lbl_30
            end
          end
          L4_2 = "players"
          ::lbl_30::
          L5_2 = Config
          L5_2 = L5_2.Framework
          if "esx" == L5_2 then
            L5_2 = "identifier"
            if L5_2 then
              goto lbl_38
            end
          end
          L5_2 = "citizenid"
          ::lbl_38::
          L6_2 = MySQL
          L6_2 = L6_2.awaitQuery
          L7_2 = "SELECT image FROM "
          L8_2 = L4_2
          L9_2 = " WHERE "
          L10_2 = L5_2
          L11_2 = " = ?"
          L7_2 = L7_2 .. L8_2 .. L9_2 .. L10_2 .. L11_2
          L8_2 = {}
          L9_2 = L3_2.PlayerData
          L9_2 = L9_2.citizenid
          L8_2[1] = L9_2
          L6_2 = L6_2(L7_2, L8_2)
          L7_2 = L6_2[1]
          if L7_2 then
            L7_2 = L6_2[1]
            L6_2 = L7_2.image
          else
            L6_2 = nil
          end
          L7_2 = L3_2.PlayerData
          L8_2 = L7_2.citizenid
          L7_2 = L0_1
          L9_2 = {}
          L10_2 = L3_2.PlayerData
          L10_2 = L10_2.citizenid
          L9_2.citizenid = L10_2
          L9_2.time = A0_2
          L9_2.initial = A0_2
          L10_2 = L3_2.PlayerData
          L10_2 = L10_2.charinfo
          L10_2 = L10_2.firstname
          L11_2 = " "
          L12_2 = L3_2.PlayerData
          L12_2 = L12_2.charinfo
          L12_2 = L12_2.lastname
          L10_2 = L10_2 .. L11_2 .. L12_2
          L9_2.name = L10_2
          L10_2 = os
          L10_2 = L10_2.time
          L10_2 = L10_2()
          L10_2 = L10_2 * 1000
          L9_2.date = L10_2
          L10_2 = A1_2 or L10_2
          if not A1_2 then
            L10_2 = "NP"
          end
          L9_2.danger = L10_2
          L10_2 = A2_2 or L10_2
          if not A2_2 then
            L10_2 = "Mission Row"
          end
          L9_2.joinedfrom = L10_2
          L9_2.online = true
          L9_2.image = L6_2
          L10_2 = MySQL
          L10_2 = L10_2.awaitInsert
          L11_2 = "INSERT INTO origen_police_federal (citizenid, time, initial, name, danger, joinedfrom) VALUES (?, ?, ?, ?, ?, ?)"
          L12_2 = {}
          L13_2 = L3_2.PlayerData
          L13_2 = L13_2.citizenid
          L14_2 = A0_2
          L15_2 = A0_2
          L16_2 = L3_2.PlayerData
          L16_2 = L16_2.charinfo
          L16_2 = L16_2.firstname
          L17_2 = " "
          L18_2 = L3_2.PlayerData
          L18_2 = L18_2.charinfo
          L18_2 = L18_2.lastname
          L16_2 = L16_2 .. L17_2 .. L18_2
          L17_2 = A1_2 or L17_2
          if not A1_2 then
            L17_2 = "NP"
          end
          L18_2 = A2_2 or L18_2
          if not A2_2 then
            L18_2 = "Mission Row"
          end
          L12_2[1] = L13_2
          L12_2[2] = L14_2
          L12_2[3] = L15_2
          L12_2[4] = L16_2
          L12_2[5] = L17_2
          L12_2[6] = L18_2
          L10_2(L11_2, L12_2)
          L7_2[L8_2] = L9_2
        else
          L4_2 = L3_2.PlayerData
          L5_2 = L4_2.citizenid
          L4_2 = L0_1
          L4_2 = L4_2[L5_2]
          L4_2.online = true
        end
      else
        L4_2 = L3_2.Functions
        L4_2 = L4_2.SetMetaData
        L5_2 = "federal"
        L6_2 = 0
        L4_2(L5_2, L6_2)
        L4_2 = L3_2.PlayerData
        L5_2 = L4_2.citizenid
        L4_2 = L0_1
        L4_2[L5_2] = nil
        L4_2 = MySQL
        L4_2 = L4_2.awaitQuery
        L5_2 = "DELETE FROM origen_police_federal WHERE citizenid = ?"
        L6_2 = {}
        L7_2 = L3_2.PlayerData
        L7_2 = L7_2.citizenid
        L6_2[1] = L7_2
        L4_2(L5_2, L6_2)
        L4_2 = CreateLog
        L5_2 = {}
        L5_2.type = "Federal"
        L6_2 = {}
        L7_2 = Config
        L7_2 = L7_2.LogsTranslations
        L7_2 = L7_2.ExitFederal
        L7_2 = L7_2.title
        L6_2.title = L7_2
        L7_2 = Config
        L7_2 = L7_2.LogsTranslations
        L7_2 = L7_2.ExitFederal
        L7_2 = L7_2.message
        L6_2.description = L7_2
        L6_2.color = 1791423
        L5_2.embed = L6_2
        L6_2 = L3_2.PlayerData
        L6_2 = L6_2.source
        L5_2.source = L6_2
        L4_2(L5_2)
      end
    end
  end
end
L1_1(L2_1, L3_1)
L1_1 = RegisterServerEvent
L2_1 = "origen_police:server:updatemins"
function L3_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2
  L1_2 = FW_GetPlayer
  L2_2 = source
  L1_2 = L1_2(L2_2)
  if L1_2 then
    L2_2 = L1_2.PlayerData
    if L2_2 then
      L2_2 = L1_2.PlayerData
      L2_2 = L2_2.citizenid
      if L2_2 then
        L2_2 = L1_2.PlayerData
        L3_2 = L2_2.citizenid
        L2_2 = L0_1
        L2_2 = L2_2[L3_2]
        if L2_2 then
          L2_2 = L1_2.PlayerData
          L3_2 = L2_2.citizenid
          L2_2 = L0_1
          L2_2 = L2_2[L3_2]
          L2_2.time = A0_2
          L2_2 = MySQL
          L2_2 = L2_2.awaitUpdate
          L3_2 = "UPDATE origen_police_federal SET time = ? WHERE citizenid = ?"
          L4_2 = {}
          L5_2 = A0_2
          L6_2 = L1_2.PlayerData
          L6_2 = L6_2.citizenid
          L4_2[1] = L5_2
          L4_2[2] = L6_2
          L2_2(L3_2, L4_2)
        end
      end
    end
  end
end
RegisterCommand("celda", function(source, args)
  local player = FW_GetPlayer(source)
  if not player then return end

  local targetId = Config.Framework == "esx" and args.id or args[1]
  local time = Config.Framework == "esx" and args.tiempo or args[2]
  local dangerous = Config.Framework == "esx" and args.dangerous or args[3]

  -- Check if player can use command
  local canOpen = CanOpenTablet(player.PlayerData.job.name)[1]
  local isOnDuty = player.PlayerData.job.onduty
  
  if Config.Framework == "esx" then
      isOnDuty = exports.origen_police:IsPlayerOnDuty(source)
  end

  if not (canOpen and isOnDuty) then return end

  -- Process command
  local target = FW_GetPlayer(tonumber(targetId))
  if not target then
      return TriggerClientEvent(
          "origen_police:ShowNotification",
          source,
          Config.Translations.PlayerNotConnected
      )
  end

  if tonumber(time) then
      TriggerClientEvent(
          "origen_police:client:sendCitizenToFederal",
          source,
          tonumber(targetId),
          tonumber(time),
          dangerous
      )

      if not Config.OwnPrisionSystem then
          TriggerClientEvent("SetFederal", tonumber(targetId), tonumber(time), dangerous)
      end

      -- Create log entry
      CreateLog({
          type = "Federal",
          embed = {
              title = Config.LogsTranslations.EnterFederal.title,
              description = Config.LogsTranslations.EnterFederal.message:format(
                  time,
                  player.PlayerData.charinfo.firstname .. " " .. player.PlayerData.charinfo.lastname
              ),
              color = 1791423
          },
          source = targetId
      })
  end
end)
L1_1(L2_1, L3_1)
L1_1 = FW_CreateCallback
L2_1 = "origen_police:server:GetFederalList"
function L3_1(A0_2, A1_2)
  local L2_2, L3_2
  L2_2 = A1_2
  L3_2 = Config
  L3_2 = L3_2.OwnPrisionSystem
  if not L3_2 then
    L3_2 = L0_1
    if L3_2 then
      goto lbl_11
    end
  end
  L3_2 = GetFederalList
  L3_2 = L3_2()
  ::lbl_11::
  L2_2(L3_2)
end
L1_1(L2_1, L3_1)
L1_1 = AddEventHandler
L2_1 = "playerDropped"
function L3_1(A0_2)
  local L1_2, L2_2, L3_2
  L1_2 = FW_GetPlayer
  L2_2 = source
  L1_2 = L1_2(L2_2)
  if L1_2 then
    L2_2 = L1_2.PlayerData
    if L2_2 then
      L2_2 = L1_2.PlayerData
      L2_2 = L2_2.citizenid
      if L2_2 then
        L2_2 = L1_2.PlayerData
        L3_2 = L2_2.citizenid
        L2_2 = L0_1
        L2_2 = L2_2[L3_2]
        if L2_2 then
          L2_2 = L1_2.PlayerData
          L3_2 = L2_2.citizenid
          L2_2 = L0_1
          L2_2 = L2_2[L3_2]
          L2_2.online = false
        end
      end
    end
  end
end
L1_1(L2_1, L3_1)
L1_1 = AddEventHandler
L2_1 = "origen_police:client:OnPlayerUnload"
function L3_1()
  local L0_2, L1_2, L2_2
  L0_2 = FW_GetPlayer
  L1_2 = source
  L0_2 = L0_2(L1_2)
  if nil ~= L0_2 then
    L1_2 = L0_2.PlayerData
    if nil ~= L1_2 then
      L1_2 = L0_2.PlayerData
      L1_2 = L1_2.citizenid
      if nil ~= L1_2 then
        goto lbl_14
      end
    end
  end
  do return end
  ::lbl_14::
  L1_2 = L0_2.PlayerData
  L2_2 = L1_2.citizenid
  L1_2 = L0_1
  L1_2 = L1_2[L2_2]
  if L1_2 then
    L1_2 = L0_2.PlayerData
    L2_2 = L1_2.citizenid
    L1_2 = L0_1
    L1_2 = L1_2[L2_2]
    L1_2.online = false
  end
end
L1_1(L2_1, L3_1)
L1_1 = FW_CreateCallback
L2_1 = "origen_police:server:releasefederal"
function L3_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2
  L3_2 = FW_GetPlayerFromCitizenid
  L4_2 = A2_2.citizenid
  L3_2 = L3_2(L4_2)
  L4_2 = Config
  L4_2 = L4_2.OwnPrisionSystem
  if L4_2 then
    L4_2 = A1_2
    L5_2 = ReleaseFederal
    L6_2 = A2_2.citizenid
    L7_2 = L3_2.PlayerData
    L7_2 = L7_2.source
    L5_2, L6_2, L7_2 = L5_2(L6_2, L7_2)
    return L4_2(L5_2, L6_2, L7_2)
  end
  if L3_2 then
    L4_2 = TriggerClientEvent
    L5_2 = "SetFederal"
    L6_2 = L3_2.PlayerData
    L6_2 = L6_2.source
    L4_2(L5_2, L6_2)
  end
  L5_2 = A2_2.citizenid
  L4_2 = L0_1
  L4_2[L5_2] = nil
  L4_2 = MySQL
  L4_2 = L4_2.awaitQuery
  L5_2 = "DELETE FROM origen_police_federal WHERE citizenid = ?"
  L6_2 = {}
  L7_2 = A2_2.citizenid
  L6_2[1] = L7_2
  L4_2(L5_2, L6_2)
  L4_2 = A1_2
  L5_2 = true
  L4_2(L5_2)
end
L1_1(L2_1, L3_1)
