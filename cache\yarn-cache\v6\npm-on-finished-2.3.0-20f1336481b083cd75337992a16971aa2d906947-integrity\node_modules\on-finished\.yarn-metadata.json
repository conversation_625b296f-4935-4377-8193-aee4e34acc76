{"manifest": {"name": "on-finished", "description": "Execute a callback when a request closes, finishes, or errors", "version": "2.3.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/on-finished.git"}, "dependencies": {"ee-first": "1.1.1"}, "devDependencies": {"istanbul": "0.3.9", "mocha": "2.2.5"}, "engines": {"node": ">= 0.8"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-on-finished-2.3.0-20f1336481b083cd75337992a16971aa2d906947-integrity\\node_modules\\on-finished\\package.json", "readmeFilename": "README.md", "readme": "# on-finished\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nExecute a callback when a HTTP request closes, finishes, or errors.\n\n## Install\n\n```sh\n$ npm install on-finished\n```\n\n## API\n\n```js\nvar onFinished = require('on-finished')\n```\n\n### onFinished(res, listener)\n\nAttach a listener to listen for the response to finish. The listener will\nbe invoked only once when the response finished. If the response finished\nto an error, the first argument will contain the error. If the response\nhas already finished, the listener will be invoked.\n\nListening to the end of a response would be used to close things associated\nwith the response, like open files.\n\nListener is invoked as `listener(err, res)`.\n\n```js\nonFinished(res, function (err, res) {\n  // clean up open fds, etc.\n  // err contains the error is request error'd\n})\n```\n\n### onFinished(req, listener)\n\nAttach a listener to listen for the request to finish. The listener will\nbe invoked only once when the request finished. If the request finished\nto an error, the first argument will contain the error. If the request\nhas already finished, the listener will be invoked.\n\nListening to the end of a request would be used to know when to continue\nafter reading the data.\n\nListener is invoked as `listener(err, req)`.\n\n```js\nvar data = ''\n\nreq.setEncoding('utf8')\nres.on('data', function (str) {\n  data += str\n})\n\nonFinished(req, function (err, req) {\n  // data is read unless there is err\n})\n```\n\n### onFinished.isFinished(res)\n\nDetermine if `res` is already finished. This would be useful to check and\nnot even start certain operations if the response has already finished.\n\n### onFinished.isFinished(req)\n\nDetermine if `req` is already finished. This would be useful to check and\nnot even start certain operations if the request has already finished.\n\n## Special Node.js requests\n\n### HTTP CONNECT method\n\nThe meaning of the `CONNECT` method from RFC 7231, section 4.3.6:\n\n> The CONNECT method requests that the recipient establish a tunnel to\n> the destination origin server identified by the request-target and,\n> if successful, thereafter restrict its behavior to blind forwarding\n> of packets, in both directions, until the tunnel is closed.  Tunnels\n> are commonly used to create an end-to-end virtual connection, through\n> one or more proxies, which can then be secured using TLS (Transport\n> Layer Security, [RFC5246]).\n\nIn Node.js, these request objects come from the `'connect'` event on\nthe HTTP server.\n\nWhen this module is used on a HTTP `CONNECT` request, the request is\nconsidered \"finished\" immediately, **due to limitations in the Node.js\ninterface**. This means if the `CONNECT` request contains a request entity,\nthe request will be considered \"finished\" even before it has been read.\n\nThere is no such thing as a response object to a `CONNECT` request in\nNode.js, so there is no support for for one.\n\n### HTTP Upgrade request\n\nThe meaning of the `Upgrade` header from RFC 7230, section 6.1:\n\n> The \"Upgrade\" header field is intended to provide a simple mechanism\n> for transitioning from HTTP/1.1 to some other protocol on the same\n> connection.\n\nIn Node.js, these request objects come from the `'upgrade'` event on\nthe HTTP server.\n\nWhen this module is used on a HTTP request with an `Upgrade` header, the\nrequest is considered \"finished\" immediately, **due to limitations in the\nNode.js interface**. This means if the `Upgrade` request contains a request\nentity, the request will be considered \"finished\" even before it has been\nread.\n\nThere is no such thing as a response object to a `Upgrade` request in\nNode.js, so there is no support for for one.\n\n## Example\n\nThe following code ensures that file descriptors are always closed\nonce the response finishes.\n\n```js\nvar destroy = require('destroy')\nvar http = require('http')\nvar onFinished = require('on-finished')\n\nhttp.createServer(function onRequest(req, res) {\n  var stream = fs.createReadStream('package.json')\n  stream.pipe(res)\n  onFinished(res, function (err) {\n    destroy(stream)\n  })\n})\n```\n\n## License\n\n[MIT](LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/on-finished.svg\n[npm-url]: https://npmjs.org/package/on-finished\n[node-version-image]: https://img.shields.io/node/v/on-finished.svg\n[node-version-url]: http://nodejs.org/download/\n[travis-image]: https://img.shields.io/travis/jshttp/on-finished/master.svg\n[travis-url]: https://travis-ci.org/jshttp/on-finished\n[coveralls-image]: https://img.shields.io/coveralls/jshttp/on-finished/master.svg\n[coveralls-url]: https://coveralls.io/r/jshttp/on-finished?branch=master\n[downloads-image]: https://img.shields.io/npm/dm/on-finished.svg\n[downloads-url]: https://npmjs.org/package/on-finished\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2013 <PERSON> <<EMAIL>>\nCopyright (c) 2014 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR CO<PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/on-finished/-/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947", "type": "tarball", "reference": "https://registry.yarnpkg.com/on-finished/-/on-finished-2.3.0.tgz", "hash": "20f1336481b083cd75337992a16971aa2d906947", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "registry": "npm", "packageName": "on-finished", "cacheIntegrity": "sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww== sha1-IPEzZIGwg811M3mSoWlxqi2QaUc="}, "registry": "npm", "hash": "20f1336481b083cd75337992a16971aa2d906947"}