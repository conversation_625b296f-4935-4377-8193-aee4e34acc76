/*
	MIT License http://www.opensource.org/licenses/mit-license.php
	Author <PERSON> @sokra
*/
"use strict";
const Dependency = require("../Dependency");

class ModuleDependency extends Dependency {
	/**
	 * @param {string} request request path which needs resolving
	 */
	constructor(request) {
		super();
		this.request = request;
		this.userRequest = request;
	}

	getResourceIdentifier() {
		return `module${this.request}`;
	}
}

module.exports = ModuleDependency;
