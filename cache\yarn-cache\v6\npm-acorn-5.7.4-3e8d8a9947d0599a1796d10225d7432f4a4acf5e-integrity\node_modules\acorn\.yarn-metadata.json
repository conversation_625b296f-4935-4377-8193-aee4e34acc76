{"manifest": {"name": "acorn", "description": "ECMAScript parser", "homepage": "https://github.com/acornjs/acorn", "main": "dist/acorn.js", "module": "dist/acorn.es.js", "version": "5.7.4", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://marijnhaverbeke.nl"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://rreverser.com/"}, {"name": "<PERSON>", "email": "http://adrianheine.de"}], "repository": {"type": "git", "url": "https://github.com/acornjs/acorn.git"}, "license": "MIT", "scripts": {"prepare": "npm run build && node test/run.js && node test/lint.js", "test": "node test/run.js && node test/lint.js", "pretest": "npm run build:main && npm run build:loose", "test:test262": "node bin/run_test262.js", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js && rollup -c rollup/config.loose_es.js", "build:bin": "rollup -c rollup/config.bin.js", "lint": "eslint src/"}, "bin": {"acorn": "bin\\acorn"}, "devDependencies": {"eslint": "^4.10.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.2.0", "eslint-plugin-node": "^5.2.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "rollup": "^0.45.0", "rollup-plugin-buble": "^0.16.0", "test262": "git+https://github.com/tc39/test262.git#3bfad28cc302fd4455badcfcbca7c5bb7ce41a72", "test262-parser-runner": "^0.4.0", "unicode-11.0.0": "^0.7.7"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-acorn-5.7.4-3e8d8a9947d0599a1796d10225d7432f4a4acf5e-integrity\\node_modules\\acorn\\package.json", "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kehel<PERSON> Gallaba"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "laosb"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "naoh"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "ReadmeCritic"}, {"name": "r-e-d"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>yu"}, {"name": "zsjforcn"}], "readmeFilename": "README.md", "readme": "# Acorn\n\n[![Build Status](https://travis-ci.org/acornjs/acorn.svg?branch=master)](https://travis-ci.org/acornjs/acorn)\n[![NPM version](https://img.shields.io/npm/v/acorn.svg)](https://www.npmjs.com/package/acorn)\n[![CDNJS](https://img.shields.io/cdnjs/v/acorn.svg)](https://cdnjs.com/libraries/acorn)  \n[Author funding status: ![maintainer happiness](https://marijnhaverbeke.nl/fund/status_s.png?force)](https://marijnhaverbeke.nl/fund/)\n\nA tiny, fast JavaScript parser, written completely in JavaScript.\n\n## Community\n\nAcorn is open source software released under an\n[MIT license](https://github.com/acornjs/acorn/blob/master/LICENSE).\n\nYou are welcome to\n[report bugs](https://github.com/acornjs/acorn/issues) or create pull\nrequests on [github](https://github.com/acornjs/acorn). For questions\nand discussion, please use the\n[Tern discussion forum](https://discuss.ternjs.net).\n\n## Installation\n\nThe easiest way to install acorn is with [`npm`][npm].\n\n[npm]: https://www.npmjs.com/\n\n```sh\nnpm install acorn\n```\n\nAlternately, you can download the source and build acorn yourself:\n\n```sh\ngit clone https://github.com/acornjs/acorn.git\ncd acorn\nnpm install\nnpm run build\n```\n\n## Components\n\nWhen run in a CommonJS (node.js) or AMD environment, exported values\nappear in the interfaces exposed by the individual files, as usual.\nWhen loaded in the browser (Acorn works in any JS-enabled browser more\nrecent than IE5) without any kind of module management, a single\nglobal object `acorn` will be defined, and all the exported properties\nwill be added to that.\n\n### Main parser\n\nThis is implemented in `dist/acorn.js`, and is what you get when you\n`require(\"acorn\")` in node.js.\n\n**parse**`(input, options)` is used to parse a JavaScript program.\nThe `input` parameter is a string, `options` can be undefined or an\nobject setting some of the options listed below. The return value will\nbe an abstract syntax tree object as specified by the\n[ESTree spec][estree].\n\nWhen  encountering   a  syntax   error,  the   parser  will   raise  a\n`SyntaxError` object with a meaningful  message. The error object will\nhave a `pos` property that indicates the character offset at which the\nerror occurred,  and a `loc`  object that contains a  `{line, column}`\nobject referring to that same position.\n\n[estree]: https://github.com/estree/estree\n\n- **ecmaVersion**: Indicates the ECMAScript version to parse. Must be\n  either 3, 5, 6 (2015), 7 (2016), 8 (2017), 9 (2018) or 10 (2019, partial\n  support). This influences support for strict mode, the set of\n  reserved words, and support for new syntax features. Default is 7.\n\n  **NOTE**: Only 'stage 4' (finalized) ECMAScript features are being\n  implemented by Acorn.\n\n- **sourceType**: Indicate the mode the code should be parsed in. Can be\n  either `\"script\"` or `\"module\"`. This influences global strict mode\n  and parsing of `import` and `export` declarations.\n\n- **onInsertedSemicolon**: If given a callback, that callback will be\n  called whenever a missing semicolon is inserted by the parser. The\n  callback will be given the character offset of the point where the\n  semicolon is inserted as argument, and if `locations` is on, also a\n  `{line, column}` object representing this position.\n\n- **onTrailingComma**: Like `onInsertedSemicolon`, but for trailing\n  commas.\n\n- **allowReserved**: If `false`, using a reserved word will generate\n  an error. Defaults to `true` for `ecmaVersion` 3, `false` for higher\n  versions. When given the value `\"never\"`, reserved words and\n  keywords can also not be used as property names (as in Internet\n  Explorer's old parser).\n\n- **allowReturnOutsideFunction**: By default, a return statement at\n  the top level raises an error. Set this to `true` to accept such\n  code.\n\n- **allowImportExportEverywhere**: By default, `import` and `export`\n  declarations can only appear at a program's top level. Setting this\n  option to `true` allows them anywhere where a statement is allowed.\n  \n- **allowAwaitOutsideFunction**: By default, `await` expressions can only appear inside `async` functions. Setting this option to `true` allows to have top-level `await` expressions. They are still not allowed in non-`async` functions, though.\n\n- **allowHashBang**: When this is enabled (off by default), if the\n  code starts with the characters `#!` (as in a shellscript), the\n  first line will be treated as a comment.\n\n- **locations**: When `true`, each node has a `loc` object attached\n  with `start` and `end` subobjects, each of which contains the\n  one-based line and zero-based column numbers in `{line, column}`\n  form. Default is `false`.\n\n- **onToken**: If a function is passed for this option, each found\n  token will be passed in same format as tokens returned from\n  `tokenizer().getToken()`.\n\n  If array is passed, each found token is pushed to it.\n\n  Note that you are not allowed to call the parser from the\n  callback—that will corrupt its internal state.\n\n- **onComment**: If a function is passed for this option, whenever a\n  comment is encountered the function will be called with the\n  following parameters:\n\n  - `block`: `true` if the comment is a block comment, false if it\n    is a line comment.\n  - `text`: The content of the comment.\n  - `start`: Character offset of the start of the comment.\n  - `end`: Character offset of the end of the comment.\n\n  When the `locations` options is on, the `{line, column}` locations\n  of the comment’s start and end are passed as two additional\n  parameters.\n\n  If array is passed for this option, each found comment is pushed\n  to it as object in Esprima format:\n\n  ```javascript\n  {\n    \"type\": \"Line\" | \"Block\",\n    \"value\": \"comment text\",\n    \"start\": Number,\n    \"end\": Number,\n    // If `locations` option is on:\n    \"loc\": {\n      \"start\": {line: Number, column: Number}\n      \"end\": {line: Number, column: Number}\n    },\n    // If `ranges` option is on:\n    \"range\": [Number, Number]\n  }\n  ```\n\n  Note that you are not allowed to call the parser from the\n  callback—that will corrupt its internal state.\n\n- **ranges**: Nodes have their start and end characters offsets\n  recorded in `start` and `end` properties (directly on the node,\n  rather than the `loc` object, which holds line/column data. To also\n  add a [semi-standardized][range] `range` property holding a\n  `[start, end]` array with the same numbers, set the `ranges` option\n  to `true`.\n\n- **program**: It is possible to parse multiple files into a single\n  AST by passing the tree produced by parsing the first file as the\n  `program` option in subsequent parses. This will add the toplevel\n  forms of the parsed file to the \"Program\" (top) node of an existing\n  parse tree.\n\n- **sourceFile**: When the `locations` option is `true`, you can pass\n  this option to add a `source` attribute in every node’s `loc`\n  object. Note that the contents of this option are not examined or\n  processed in any way; you are free to use whatever format you\n  choose.\n\n- **directSourceFile**: Like `sourceFile`, but a `sourceFile` property\n  will be added (regardless of the `location` option) directly to the\n  nodes, rather than the `loc` object.\n\n- **preserveParens**: If this option is `true`, parenthesized expressions\n  are represented by (non-standard) `ParenthesizedExpression` nodes\n  that have a single `expression` property containing the expression\n  inside parentheses.\n\n[range]: https://bugzilla.mozilla.org/show_bug.cgi?id=745678\n\n**parseExpressionAt**`(input, offset, options)` will parse a single\nexpression in a string, and return its AST. It will not complain if\nthere is more of the string left after the expression.\n\n**getLineInfo**`(input, offset)` can be used to get a `{line,\ncolumn}` object for a given program string and character offset.\n\n**tokenizer**`(input, options)` returns an object with a `getToken`\nmethod that can be called repeatedly to get the next token, a `{start,\nend, type, value}` object (with added `loc` property when the\n`locations` option is enabled and `range` property when the `ranges`\noption is enabled). When the token's type is `tokTypes.eof`, you\nshould stop calling the method, since it will keep returning that same\ntoken forever.\n\nIn ES6 environment, returned result can be used as any other\nprotocol-compliant iterable:\n\n```javascript\nfor (let token of acorn.tokenizer(str)) {\n  // iterate over the tokens\n}\n\n// transform code to array of tokens:\nvar tokens = [...acorn.tokenizer(str)];\n```\n\n**tokTypes** holds an object mapping names to the token type objects\nthat end up in the `type` properties of tokens.\n\n#### Note on using with [Escodegen][escodegen]\n\nEscodegen supports generating comments from AST, attached in\nEsprima-specific format. In order to simulate same format in\nAcorn, consider following example:\n\n```javascript\nvar comments = [], tokens = [];\n\nvar ast = acorn.parse('var x = 42; // answer', {\n\t// collect ranges for each node\n\tranges: true,\n\t// collect comments in Esprima's format\n\tonComment: comments,\n\t// collect token ranges\n\tonToken: tokens\n});\n\n// attach comments using collected information\nescodegen.attachComments(ast, comments, tokens);\n\n// generate code\nconsole.log(escodegen.generate(ast, {comment: true}));\n// > 'var x = 42;    // answer'\n```\n\n[escodegen]: https://github.com/estools/escodegen\n\n### dist/acorn_loose.js ###\n\nThis file implements an error-tolerant parser. It exposes a single\nfunction. The loose parser is accessible in node.js via `require(\"acorn/dist/acorn_loose\")`.\n\n**parse_dammit**`(input, options)` takes the same arguments and\nreturns the same syntax tree as the `parse` function in `acorn.js`,\nbut never raises an error, and will do its best to parse syntactically\ninvalid code in as meaningful a way as it can. It'll insert identifier\nnodes with name `\"✖\"` as placeholders in places where it can't make\nsense of the input. Depends on `acorn.js`, because it uses the same\ntokenizer.\n\n### dist/walk.js ###\n\nImplements an abstract syntax tree walker. Will store its interface in\n`acorn.walk` when loaded without a module system.\n\n**simple**`(node, visitors, base, state)` does a 'simple' walk over\na tree. `node` should be the AST node to walk, and `visitors` an\nobject with properties whose names correspond to node types in the\n[ESTree spec][estree]. The properties should contain functions\nthat will be called with the node object and, if applicable the state\nat that point. The last two arguments are optional. `base` is a walker\nalgorithm, and `state` is a start state. The default walker will\nsimply visit all statements and expressions and not produce a\nmeaningful state. (An example of a use of state is to track scope at\neach point in the tree.)\n\n```js\nconst acorn = require(\"acorn\")\nconst walk = require(\"acorn/dist/walk\")\n\nwalk.simple(acorn.parse(\"let x = 10\"), {\n  Literal(node) {\n    console.log(`Found a literal: ${node.value}`)\n  }\n})\n```\n\n**ancestor**`(node, visitors, base, state)` does a 'simple' walk over\na tree, building up an array of ancestor nodes (including the current node)\nand passing the array to the callbacks as a third parameter.\n\n```js\nconst acorn = require(\"acorn\")\nconst walk = require(\"acorn/dist/walk\")\n\nwalk.ancestor(acorn.parse(\"foo('hi')\"), {\n  Literal(_, ancestors) {\n    console.log(\"This literal's ancestors are:\",\n                ancestors.map(n => n.type))\n  }\n})\n```\n\n**recursive**`(node, state, functions, base)` does a 'recursive'\nwalk, where the walker functions are responsible for continuing the\nwalk on the child nodes of their target node. `state` is the start\nstate, and `functions` should contain an object that maps node types\nto walker functions. Such functions are called with `(node, state, c)`\narguments, and can cause the walk to continue on a sub-node by calling\nthe `c` argument on it with `(node, state)` arguments. The optional\n`base` argument provides the fallback walker functions for node types\nthat aren't handled in the `functions` object. If not given, the\ndefault walkers will be used.\n\n**make**`(functions, base)` builds a new walker object by using the\nwalker functions in `functions` and filling in the missing ones by\ntaking defaults from `base`.\n\n**full**`(node, callback, base, state)` does a 'full'\nwalk over a tree, calling the callback with the arguments (node, state, type)\nfor each node\n\n**fullAncestor**`(node, callback, base, state)` does a 'full' walk over\na tree, building up an array of ancestor nodes (including the current node)\nand passing the array to the callbacks as a third parameter.\n\n```js\nconst acorn = require(\"acorn\")\nconst walk = require(\"acorn/dist/walk\")\n\nwalk.full(acorn.parse(\"1 + 1\"), node => {\n  console.log(`There's a ${node.type} node at ${node.ch}`)\n})\n```\n\n**findNodeAt**`(node, start, end, test, base, state)` tries to\nlocate a node in a tree at the given start and/or end offsets, which\nsatisfies the predicate `test`. `start` and `end` can be either `null`\n(as wildcard) or a number. `test` may be a string (indicating a node\ntype) or a function that takes `(nodeType, node)` arguments and\nreturns a boolean indicating whether this node is interesting. `base`\nand `state` are optional, and can be used to specify a custom walker.\nNodes are tested from inner to outer, so if two nodes match the\nboundaries, the inner one will be preferred.\n\n**findNodeAround**`(node, pos, test, base, state)` is a lot like\n`findNodeAt`, but will match any node that exists 'around' (spanning)\nthe given position.\n\n**findNodeAfter**`(node, pos, test, base, state)` is similar to\n`findNodeAround`, but will match all nodes *after* the given position\n(testing outer nodes before inner nodes).\n\n## Command line interface\n\nThe `bin/acorn` utility can be used to parse a file from the command\nline. It accepts as arguments its input file and the following\noptions:\n\n- `--ecma3|--ecma5|--ecma6|--ecma7|--ecma8|--ecma9|--ecma10`: Sets the ECMAScript version\n  to parse. Default is version 7.\n\n- `--module`: Sets the parsing mode to `\"module\"`. Is set to `\"script\"` otherwise.\n\n- `--locations`: Attaches a \"loc\" object to each node with \"start\" and\n  \"end\" subobjects, each of which contains the one-based line and\n  zero-based column numbers in `{line, column}` form.\n\n- `--allow-hash-bang`: If the code starts with the characters #! (as in a shellscript), the first line will be treated as a comment.\n\n- `--compact`: No whitespace is used in the AST output.\n\n- `--silent`: Do not output the AST, just return the exit status.\n\n- `--help`: Print the usage information and quit.\n\nThe utility spits out the syntax tree as JSON data.\n\n## Build system\n\nAcorn is written in ECMAScript 6, as a set of small modules, in the\nproject's `src` directory, and compiled down to bigger ECMAScript 3\nfiles in `dist` using [Browserify](http://browserify.org) and\n[Babel](http://babeljs.io/). If you are already using Babel, you can\nconsider including the modules directly.\n\nThe command-line test runner (`npm test`) uses the ES6 modules. The\nbrowser-based test page (`test/index.html`) uses the compiled modules.\nThe `bin/build-acorn.js` script builds the latter from the former.\n\nIf you are working on Acorn, you'll probably want to try the code out\ndirectly, without an intermediate build step. In your scripts, you can\nregister the Babel require shim like this:\n\n    require(\"babel-core/register\")\n\nThat will allow you to directly `require` the ES6 modules.\n\n## Plugins\n\nAcorn is designed support allow plugins which, within reasonable\nbounds, redefine the way the parser works. Plugins can add new token\ntypes and new tokenizer contexts (if necessary), and extend methods in\nthe parser object. This is not a clean, elegant API—using it requires\nan understanding of Acorn's internals, and plugins are likely to break\nwhenever those internals are significantly changed. But still, it is\n_possible_, in this way, to create parsers for JavaScript dialects\nwithout forking all of Acorn. And in principle it is even possible to\ncombine such plugins, so that if you have, for example, a plugin for\nparsing types and a plugin for parsing JSX-style XML literals, you\ncould load them both and parse code with both JSX tags and types.\n\nA plugin should register itself by adding a property to\n`acorn.plugins`, which holds a function. Calling `acorn.parse`, a\n`plugins` option can be passed, holding an object mapping plugin names\nto configuration values (or just `true` for plugins that don't take\noptions). After the parser object has been created, the initialization\nfunctions for the chosen plugins are called with `(parser,\nconfigValue)` arguments. They are expected to use the `parser.extend`\nmethod to extend parser methods. For example, the `readToken` method\ncould be extended like this:\n\n```javascript\nparser.extend(\"readToken\", function(nextMethod) {\n  return function(code) {\n    console.log(\"Reading a token!\")\n    return nextMethod.call(this, code)\n  }\n})\n```\n\nThe `nextMethod` argument passed to `extend`'s second argument is the\nprevious value of this method, and should usually be called through to\nwhenever the extended method does not handle the call itself.\n\nSimilarly, the loose parser allows plugins to register themselves via\n`acorn.pluginsLoose`.  The extension mechanism is the same as for the\nnormal parser:\n\n```javascript\nlooseParser.extend(\"readToken\", function(nextMethod) {\n  return function() {\n    console.log(\"Reading a token in the loose parser!\")\n    return nextMethod.call(this)\n  }\n})\n```\n\n### Existing plugins\n\n - [`acorn-jsx`](https://github.com/RReverser/acorn-jsx): Parse [Facebook JSX syntax extensions](https://github.com/facebook/jsx)\n - [`acorn-objj`](https://github.com/cappuccino/acorn-objj): [Objective-J](http://www.cappuccino-project.org/learn/objective-j.html) language parser built as Acorn plugin\n \n Plugins for ECMAScript proposals:\n \n - [`acorn-stage3`](https://github.com/acornjs/acorn-stage3): Parse most stage 3 proposals, bundling:\n   - [`acorn-async-iteration`](https://github.com/acornjs/acorn-async-iteration): Parse [async iteration proposal](https://github.com/tc39/proposal-async-iteration)\n   - [`acorn-bigint`](https://github.com/acornjs/acorn-bigint): Parse [BigInt proposal](https://github.com/tc39/proposal-bigint)\n   - [`acorn-class-fields`](https://github.com/acornjs/acorn-class-fields): Parse [class fields proposal](https://github.com/tc39/proposal-class-fields)\n   - [`acorn-dynamic-import`](https://github.com/kesne/acorn-dynamic-import): Parse [import() proposal](https://github.com/tc39/proposal-dynamic-import)\n   - [`acorn-import-meta`](https://github.com/acornjs/acorn-import-meta): Parse [import.meta proposal](https://github.com/tc39/proposal-import-meta)\n   - [`acorn-numeric-separator`](https://github.com/acornjs/acorn-numeric-separator): Parse [numeric separator proposal](https://github.com/tc39/proposal-numeric-separator)\n   - [`acorn-optional-catch-binding`](https://github.com/acornjs/acorn-optional-catch-binding): Parse [optional catch binding proposal](https://github.com/tc39/proposal-optional-catch-binding)\n   - [`acorn-private-methods`](https://github.com/acornjs/acorn-private-methods): parse [private methods, getters and setters proposal](https://github.com/tc39/proposal-private-methods)\n   - [`acorn5-object-spread`](https://github.com/adrianheine/acorn5-object-spread): Parse [Object Rest/Spread Properties proposal](https://github.com/tc39/proposal-object-rest-spread)\n - [`acorn-object-rest-spread`](https://github.com/victor-homyakov/acorn-object-rest-spread): Parse [Object Rest/Spread Properties proposal](https://github.com/tc39/proposal-object-rest-spread)\n - [`acorn-es7`](https://github.com/angelozerr/acorn-es7): Parse [decorator syntax proposal](https://github.com/wycats/javascript-decorators)\n - [`acorn-static-class-property-initializer`](https://github.com/victor-homyakov/acorn-static-class-property-initializer): Partial support for static class properties from [ES Class Fields & Static Properties Proposal](https://github.com/tc39/proposal-class-public-fields) to support static property initializers in [React components written as ES6+ classes](https://babeljs.io/blog/2015/07/07/react-on-es6-plus)\n", "licenseText": "Copyright (C) 2012-2018 by various contributors (see AUTHORS)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/acorn/-/acorn-5.7.4.tgz#3e8d8a9947d0599a1796d10225d7432f4a4acf5e", "type": "tarball", "reference": "https://registry.yarnpkg.com/acorn/-/acorn-5.7.4.tgz", "hash": "3e8d8a9947d0599a1796d10225d7432f4a4acf5e", "integrity": "sha512-1D++VG7BhrtvQpNbBzovKNc1FLGGEE/oGe7b9xJm/RFHMBeUaUGpluV9RLjZa47YFdPcDAenEYuq9pQPcMdLJg==", "registry": "npm", "packageName": "acorn", "cacheIntegrity": "sha512-1D++VG7BhrtvQpNbBzovKNc1FLGGEE/oGe7b9xJm/RFHMBeUaUGpluV9RLjZa47YFdPcDAenEYuq9pQPcMdLJg== sha1-Po2KmUfQWZoXltECJddDL0pKz14="}, "registry": "npm", "hash": "3e8d8a9947d0599a1796d10225d7432f4a4acf5e"}