{"manifest": {"name": "bytes", "description": "Utility to parse a string bytes to bytes and vice-versa", "version": "3.1.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Théo FIDRY", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "repository": {"type": "git", "url": "https://github.com/visionmedia/bytes.js.git"}, "devDependencies": {"eslint": "5.12.1", "mocha": "5.2.0", "nyc": "13.1.0"}, "files": ["History.md", "LICENSE", "Readme.md", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint .", "test": "mocha --check-leaks --reporter spec", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-bytes-3.1.0-f6cf7933a360e0588fa9fde85651cdc7f805d1f6-integrity\\node_modules\\bytes\\package.json", "readmeFilename": "Readme.md", "readme": "# Bytes utility\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nUtility to parse a string bytes (ex: `1TB`) to bytes (`1099511627776`) and vice-versa.\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```bash\n$ npm install bytes\n```\n\n## Usage\n\n```js\nvar bytes = require('bytes');\n```\n\n#### bytes.format(number value, [options]): string｜null\n\nFormat the given value in bytes into a string. If the value is negative, it is kept as such. If it is a float, it is\n rounded.\n\n**Arguments**\n\n| Name    | Type     | Description        |\n|---------|----------|--------------------|\n| value   | `number` | Value in bytes     |\n| options | `Object` | Conversion options |\n\n**Options**\n\n| Property          | Type   | Description                                                                             |\n|-------------------|--------|-----------------------------------------------------------------------------------------|\n| decimalPlaces | `number`｜`null` | Maximum number of decimal places to include in output. Default value to `2`. |\n| fixedDecimals | `boolean`｜`null` | Whether to always display the maximum number of decimal places. Default value to `false` |\n| thousandsSeparator | `string`｜`null` | Example of values: `' '`, `','` and `.`... Default value to `''`. |\n| unit | `string`｜`null` | The unit in which the result will be returned (B/KB/MB/GB/TB). Default value to `''` (which means auto detect). |\n| unitSeparator | `string`｜`null` | Separator to use between number and unit. Default value to `''`. |\n\n**Returns**\n\n| Name    | Type             | Description                                     |\n|---------|------------------|-------------------------------------------------|\n| results | `string`｜`null` | Return null upon error. String value otherwise. |\n\n**Example**\n\n```js\nbytes(1024);\n// output: '1KB'\n\nbytes(1000);\n// output: '1000B'\n\nbytes(1000, {thousandsSeparator: ' '});\n// output: '1 000B'\n\nbytes(1024 * 1.7, {decimalPlaces: 0});\n// output: '2KB'\n\nbytes(1024, {unitSeparator: ' '});\n// output: '1 KB'\n\n```\n\n#### bytes.parse(string｜number value): number｜null\n\nParse the string value into an integer in bytes. If no unit is given, or `value`\nis a number, it is assumed the value is in bytes.\n\nSupported units and abbreviations are as follows and are case-insensitive:\n\n  * `b` for bytes\n  * `kb` for kilobytes\n  * `mb` for megabytes\n  * `gb` for gigabytes\n  * `tb` for terabytes\n  * `pb` for petabytes\n\nThe units are in powers of two, not ten. This means 1kb = 1024b according to this parser.\n\n**Arguments**\n\n| Name          | Type   | Description        |\n|---------------|--------|--------------------|\n| value   | `string`｜`number` | String to parse, or number in bytes.   |\n\n**Returns**\n\n| Name    | Type        | Description             |\n|---------|-------------|-------------------------|\n| results | `number`｜`null` | Return null upon error. Value in bytes otherwise. |\n\n**Example**\n\n```js\nbytes('1KB');\n// output: 1024\n\nbytes('1024');\n// output: 1024\n\nbytes(1024);\n// output: 1KB\n```\n\n## License \n\n[MIT](LICENSE)\n\n[coveralls-image]: https://badgen.net/coveralls/c/github/visionmedia/bytes.js/master\n[coveralls-url]: https://coveralls.io/r/visionmedia/bytes.js?branch=master\n[downloads-image]: https://badgen.net/npm/dm/bytes\n[downloads-url]: https://npmjs.org/package/bytes\n[npm-image]: https://badgen.net/npm/node/bytes\n[npm-url]: https://npmjs.org/package/bytes\n[travis-image]: https://badgen.net/travis/visionmedia/bytes.js/master\n[travis-url]: https://travis-ci.org/visionmedia/bytes.js\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2012-2014 <PERSON><PERSON> <<EMAIL>>\nCopyright (c) 2015 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/bytes/-/bytes-3.1.0.tgz#f6cf7933a360e0588fa9fde85651cdc7f805d1f6", "type": "tarball", "reference": "https://registry.yarnpkg.com/bytes/-/bytes-3.1.0.tgz", "hash": "f6cf7933a360e0588fa9fde85651cdc7f805d1f6", "integrity": "sha512-zauLjrfCG+xvoyaqLoV8bLVXXNGC4JqlxFCutSDWA6fJrTo2ZuvLYTqZ7aHBLZSMOopbzwv8f+wZcVzfVTI2Dg==", "registry": "npm", "packageName": "bytes", "cacheIntegrity": "sha512-zauLjrfCG+xvoyaqLoV8bLVXXNGC4JqlxFCutSDWA6fJrTo2ZuvLYTqZ7aHBLZSMOopbzwv8f+wZcVzfVTI2Dg== sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY="}, "registry": "npm", "hash": "f6cf7933a360e0588fa9fde85651cdc7f805d1f6"}