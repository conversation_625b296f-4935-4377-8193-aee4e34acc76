Config.Federal = {
    Spawn = vector4(1752.7030, 2504.3557, 45.5651, 28.7221),
    Center = vector3(1693.4309, 2562.5601, 45.5646),
    Exits = {
        ["Mission Row"] = vector4(480.9431, -977.4430, 27.9842, 3.83),
        ["La Mesa"] = vector4(814.8126, -1312.6895, 26.1861, 85.3),
        ["Sandy Shores"] = vector4(1841.3585, 3671.2607, 33.8726, 208.0081),
        ["Paleto Bay"] = vector4(-423.8767, 5993.2266, 31.4892, 312.2403),
        ["Boling Broke"] = vector4(1848.2015, 2602.6997, 45.5993, 273.2811)
    },
    CamCoords = vector4(1828.8406, 2603.9912, 55.2284, 269.3494),
    BusCoords = vector4(1852.8319, 2608.4592, 45.6727, 86.9280),
    Finish = vector4(1804.9783, 2608.8894, 45.5665, 92.3074)
}

Config.RemoveInventoryFederal = false
Config.TimePerMonth = 60 -- Time in seconds, if you put 2 months, it will be 120 seconds(2 minutes)
Config.OwnPrisionSystem = false -- enable this ones to enable the functions below
Config.BusModel = "pbus"
Config.PedWalkOnAnim = true -- If you want the ped to walk when the prision animation is played, enter/exit
Config.ForcedExit = { -- If you want to force players to always exit in this position, enable this and set the coords
    enabled = false, -- enable this to force the exit
    coords = vector4(480.9431, -977.4430, 27.9842, 3.83)
}