ExecuteSql = function(query, parameters, cb)
    local promise = promise:new()
    while isBusy do
        Citizen.Wait(0)
    end
    local isBusy = true
    if GetResourceState("oxmysql") == 'started' then
        exports.oxmysql:execute(query, parameters, function(data)
            promise:resolve(data)
            isBusy = false

            if cb then
                cb(data)
            end
        end)
    elseif GetResourceState("ghmattimysql") == 'started' then
        exports.ghmattimysql:execute(query, parameters, function(data)
            promise:resolve(data)
            isBusy = false

            if cb then
                cb(data)
            end
        end)
    elseif GetResourceState("mysql-async") == 'started' then
        MySQL.Async.fetchAll(query, parameters, function(data)
            promise:resolve(data)
            isBusy = false
            if cb then
                cb(data)
            end
        end)
    end
    return Citizen.Await(promise)
end

GetPlayer = function(source)
    local Player = nil
    if CoreName == 'qb-core' then
        Player = QBCore.Functions.GetPlayer(source)
    elseif CoreName == 'qbx_core' then
        Player = QBX:GetPlayer(source)
    elseif CoreName == 'es_extended' then
        Player = ESX.GetPlayerFromId(source)
    end

    if Player then
        return Player
    end

    return false
end

GetIdentifier = function(source)
    if CoreName == 'qb-core' then
        return GetPlayerIdentifierByType(source, 'license') or 'unknown'
    elseif CoreName == 'qbx_core' then
        return GetPlayerIdentifierByType(source, 'license2') or 'unknown'
    end
end

loadHouseData = function(src)
    local HouseGarages = {}
    local Houses = {}
    local result = MySQL.query.await('SELECT * FROM houselocations', {})
    if result[1] ~= nil then
        for _, v in pairs(result) do
            local owned = false
            if tonumber(v.owned) == 1 then
                owned = true
            end
            local garage = v.garage ~= nil and json.decode(v.garage) or {}
            Houses[v.name] = {
                coords = json.decode(v.coords),
                owned = owned,
                price = v.price,
                locked = true,
                adress = v.label,
                tier = v.tier,
                garage = garage,
                decorations = {},
            }
            HouseGarages[v.name] = {
                label = v.label,
                takeVehicle = garage,
            }
        end
    end
    TriggerClientEvent("qb-garages:client:houseGarageConfig", src, HouseGarages)
    TriggerClientEvent("qb-houses:client:setHouseConfig", src, Houses)
end

lib.callback.register('0r-multicharacterv2:server:deleteCharacter', function(source, cid)
    for k, v in pairs(Config.DeleteTable) do
        local column = v.column
        local type = v.type
        local table = v.table

        if type == 'citizenid' then
            ExecuteSql('DELETE FROM '..table..' WHERE '..column..' = ?', { cid })
        elseif type == 'license' then
            local license = GetIdentifier(source)
            ExecuteSql('DELETE FROM '..table..' WHERE '..column..' = ?', { license })
        elseif type == 'steam' then
            local steam = GetPlayerIdentifiers(source)[1]
            ExecuteSql('DELETE FROM '..table..' WHERE '..column..' = ?', { cid })
        elseif type == 'discord' then
            local discord = GetPlayerIdentifiers(source)[2]
            ExecuteSql('DELETE FROM '..table..' WHERE '..column..' = ?', { cid })
        end
    end
end)

local function defaultQBMetaData(player, item)
    local data = {}
    if item == "id_card" then
        data = {
            citizenid = player.citizenid,
            firstname = player.charinfo.firstname,
            lastname = player.charinfo.lastname,
            birthdate = player.charinfo.birthdate,
            gender = player.charinfo.gender,
            nationality = player.charinfo.nationality,
        }
    elseif item == "driver_license" then
        data = {
            firstname = player.charinfo.firstname,
            lastname = player.charinfo.lastname,
            birthdate = player.charinfo.birthdate,
            type = "Class C Driver License",
        }
    end
    return data
end

local function customIDCard(src, item)
    if GetResourceState('um-idcard') == 'started' then
        exports['um-idcard']:CreateMetaLicense(src, item)
    elseif GetResourceState('bl_idcard') == 'started' then
        TriggerClientEvent('bl_idcard:client:openID', src, item)
    elseif GetResourceState('qbx_idcard') == 'started' then
        exports['qbx_idcard']:CreateMetaLicense(src, item)
    end
end

local function addItemToInventory(src, item, amount, metadata, pFunction)
    if GetResourceState('ox_inventory') == 'started' then
        exports.ox_inventory:AddItem(src, item, amount, metadata)
        return
    elseif GetResourceState('qb-inventory') == 'started' then
        exports['qb-inventory']:AddItem(src, item, amount, false, metadata)
        return
    elseif GetResourceState('ps-inventory') == 'started' then
        exports['ps-inventory']:AddItem(src, item, amount, false, metadata)
        return
    elseif GetResourceState('lj-inventory') == 'started' then
        exports['lj-inventory']:AddItem(src, item, amount, false, metadata)
        return
    elseif GetResourceState('codem-inventory') == 'started' then
        exports['codem-inventory']:AddItem(src, item, amount, false, metadata)
        return
    elseif GetResourceState('tgiann-inventory') == 'started' then
        exports['tgiann-inventory']:AddItem(src, item, amount, false, metadata)
        return
    elseif GetResourceState('qs-inventory') == 'started' then
        exports['qs-inventory']:AddItem(src, item, amount, false, metadata)
        return
    end
end

function GiveStarterItems(src)
    local Player = GetPlayer(src)
    if not Player then return end

    local starterItems = Config.StarterItems

    for i = 1, #starterItems do
        local data = starterItems[i]
        local metadata = {}

        if data.item == 'id_card' or data.item == 'driver_license' then
            if data.customExport then
                customIDCard(src, data.item)
            else
                metadata = defaultQBMetaData(Player.PlayerData, data.item)
            end
        end

        if not data.customExport then
            addItemToInventory(src, data.item, data.amount, metadata, not qbox and Player.Functions or nil)
        end
    end
end