{"manifest": {"name": "keygrip", "version": "1.1.0", "description": "Key signing and verification for rotated credentials", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/crypto-utils/keygrip.git"}, "dependencies": {"tsscmp": "1.0.6"}, "devDependencies": {"mocha": "6.1.4", "nyc": "14.0.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-keygrip-1.1.0-871b1681d5e159c62a445b0c74b615e0917e7226-integrity\\node_modules\\keygrip\\package.json", "readmeFilename": "README.md", "readme": "# keygrip\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nKeygrip is a [node.js](http://nodejs.org/) module for signing and verifying data (such as cookies or URLs) through a rotating credential system, in which new server keys can be added and old ones removed regularly, without invalidating client credentials.\n\n## Install\n\n    $ npm install keygrip\n\n## API\n\n### keys = new Keygrip([keylist], [hmacAlgorithm], [encoding])\n\nThis creates a new Keygrip based on the provided keylist, an array of secret keys used for SHA1 HMAC digests. `keylist` is obligatory. `hmacAlgorithm` defaults to `'sha1'` and `encoding` defaults to `'base64'`.\n\nNote that the `new` operator is also optional, so all of the following will work when `Keygrip = require(\"keygrip\")`:\n\n```javascript\nkeys = new Keygrip([\"SEKRIT2\", \"SEKRIT1\"])\nkeys = Keygrip([\"SEKRIT2\", \"SEKRIT1\"])\nkeys = require(\"keygrip\")()\nkeys = Keygrip([\"SEKRIT2\", \"SEKRIT1\"], 'sha256', 'hex')\nkeys = Keygrip([\"SEKRIT2\", \"SEKRIT1\"], 'sha256')\nkeys = Keygrip([\"SEKRIT2\", \"SEKRIT1\"], undefined, 'hex')\n```\n\nThe keylist is an array of all valid keys for signing, in descending order of freshness; new keys should be `unshift`ed into the array and old keys should be `pop`ped.\n\nThe tradeoff here is that adding more keys to the keylist allows for more granular freshness for key validation, at the cost of a more expensive worst-case scenario for old or invalid hashes.\n\nKeygrip keeps a reference to this array to automatically reflect any changes. This reference is stored using a closure to prevent external access.\n\n### keys.sign(data)\n\nThis creates a SHA1 HMAC based on the _first_ key in the keylist, and outputs it as a 27-byte url-safe base64 digest (base64 without padding, replacing `+` with `-` and `/` with `_`).\n\n### keys.index(data, digest)\n\nThis loops through all of the keys currently in the keylist until the digest of the current key matches the given digest, at which point the current index is returned. If no key is matched, `-1` is returned.\n\nThe idea is that if the index returned is greater than `0`, the data should be re-signed to prevent premature credential invalidation, and enable better performance for subsequent challenges.\n\n### keys.verify(data, digest)\n\nThis uses `index` to return `true` if the digest matches any existing keys, and `false` otherwise.\n\n## Example\n\n```javascript\n// ./test.js\nvar assert = require(\"assert\")\n  , Keygrip = require(\"keygrip\")\n  , keylist, keys, hash, index\n\n// but we're going to use our list.\n// (note that the 'new' operator is optional)\nkeylist = [\"SEKRIT3\", \"SEKRIT2\", \"SEKRIT1\"]\nkeys = Keygrip(keylist)\n// .sign returns the hash for the first key\n// all hashes are SHA1 HMACs in url-safe base64\nhash = keys.sign(\"bieberschnitzel\")\nassert.ok(/^[\\w\\-]{27}$/.test(hash))\n\n// .index returns the index of the first matching key\nindex = keys.index(\"bieberschnitzel\", hash)\nassert.equal(index, 0)\n\n// .verify returns the a boolean indicating a matched key\nmatched = keys.verify(\"bieberschnitzel\", hash)\nassert.ok(matched)\n\nindex = keys.index(\"bieberschnitzel\", \"o_O\")\nassert.equal(index, -1)\n\n// rotate a new key in, and an old key out\nkeylist.unshift(\"SEKRIT4\")\nkeylist.pop()\n\n// if index > 0, it's time to re-sign\nindex = keys.index(\"bieberschnitzel\", hash)\nassert.equal(index, 1)\nhash = keys.sign(\"bieberschnitzel\")\n```\n\n## License\n\n[MIT](LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/keygrip.svg\n[npm-url]: https://npmjs.org/package/keygrip\n[travis-image]: https://img.shields.io/travis/crypto-utils/keygrip/master.svg\n[travis-url]: https://travis-ci.org/crypto-utils/keygrip\n[coveralls-image]: https://img.shields.io/coveralls/crypto-utils/keygrip/master.svg\n[coveralls-url]: https://coveralls.io/r/crypto-utils/keygrip\n[downloads-image]: https://img.shields.io/npm/dm/keygrip.svg\n[downloads-url]: https://npmjs.org/package/keygrip\n[node-version-image]: https://img.shields.io/node/v/keygrip.svg\n[node-version-url]: https://nodejs.org/en/download/\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2011-2014 <PERSON> <<EMAIL>> (http://jedschmidt.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/keygrip/-/keygrip-1.1.0.tgz#871b1681d5e159c62a445b0c74b615e0917e7226", "type": "tarball", "reference": "https://registry.yarnpkg.com/keygrip/-/keygrip-1.1.0.tgz", "hash": "871b1681d5e159c62a445b0c74b615e0917e7226", "integrity": "sha512-iYSchDJ+liQ8iwbSI2QqsQOvqv58eJCEanyJPJi+Khyu8smkcKSFUCbPwzFcL7YVtZ6eONjqRX/38caJ7QjRAQ==", "registry": "npm", "packageName": "keygrip", "cacheIntegrity": "sha512-iYSchDJ+liQ8iwbSI2QqsQOvqv58eJCEanyJPJi+Khyu8smkcKSFUCbPwzFcL7YVtZ6eONjqRX/38caJ7QjRAQ== sha1-hxsWgdXhWcYqRFsMdLYV4JF+ciY="}, "registry": "npm", "hash": "871b1681d5e159c62a445b0c74b615e0917e7226"}