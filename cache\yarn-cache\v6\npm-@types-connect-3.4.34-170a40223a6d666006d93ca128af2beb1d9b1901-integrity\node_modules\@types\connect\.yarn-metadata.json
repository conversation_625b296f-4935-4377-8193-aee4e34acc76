{"manifest": {"name": "@types/connect", "version": "3.4.34", "description": "TypeScript definitions for connect", "license": "MIT", "contributors": [{"name": "Maxime LUCE", "url": "https://github.com/SomaticIT"}, {"name": "<PERSON>", "url": "https://github.com/EvanHahn"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/connect"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "2fd2f6a2f7b9371cdb60b971639b4d26989e6035dc30fb0ad12e72645dcb002d", "typeScriptVersion": "3.3", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-connect-3.4.34-170a40223a6d666006d93ca128af2beb1d9b1901-integrity\\node_modules\\@types\\connect\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/connect`\n\n# Summary\nThis package contains type definitions for connect (https://github.com/senchalabs/connect).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/connect.\n\n### Additional Details\n * Last updated: Tue, 08 Dec 2020 20:44:59 GMT\n * Dependencies: [@types/node](https://npmjs.com/package/@types/node)\n * Global values: none\n\n# Credits\nThese definitions were written by [<PERSON><PERSON> LUCE](https://github.com/SomaticIT), and [<PERSON>](https://github.com/EvanHahn).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/connect/-/connect-3.4.34.tgz#170a40223a6d666006d93ca128af2beb1d9b1901", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/connect/-/connect-3.4.34.tgz", "hash": "170a40223a6d666006d93ca128af2beb1d9b1901", "integrity": "sha512-ePPA/JuI+X0vb+gSWlPKOY0NdNAie/rPUqX2GUPpbZwiKTkSPhjXWuee47E4MtE54QVzGCQMQkAL6JhV2E1+cQ==", "registry": "npm", "packageName": "@types/connect", "cacheIntegrity": "sha512-ePPA/JuI+X0vb+gSWlPKOY0NdNAie/rPUqX2GUPpbZwiKTkSPhjXWuee47E4MtE54QVzGCQMQkAL6JhV2E1+cQ== sha1-FwpAIjptZmAG2TyhKK8r6x2bGQE="}, "registry": "npm", "hash": "170a40223a6d666006d93ca128af2beb1d9b1901"}