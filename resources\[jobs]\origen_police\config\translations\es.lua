if Config.Language ~= "es" then return end

Translations = {
    ["Reference"] = "REFERENCIAS",
    ["Icons"] = "ICONOS",
    ["Colors"] = "COLORES",
    ["RadialComm"] = "COMUNICACIÓN RADIAL",
    ["Radio"] = "RADIO",
    ["Disconnected"] = "DESCONECTADO",
    ["SouthUnits"] = "UNIDADES DEL SUR",
    ["NorthUnits"] = "UNIDADES DEL NORTE",
    ["SpecialUnits"] = "UNIDADES ESPECIALES",
    ["EMSUnits"] = "UNIDADES DE EMS",
    ["Interaction"] = "INTERACCIÓN",
    ["CitizenInteraction"] = "INTERACCIÓN CON CIUDADANOS",
    ["Search"] = "BÚSQUEDA",
    ["Wifes"] = "ESPOSAS",
    ["Escort"] = "ESCOLTA",
    ["PutInVehicle"] = "ENTRAR/SALIR DEL VEHÍCULO",
    ["JumpTo"] = "SALTAR A",
    ["HealWounds"] = "CURAR HERIDAS",
    ["PutTakeAnkle"] = "Poner/quitar tobillera",
    ["LogsDate"] = "FECHAS DE REGISTROS",
    ["Localize"] = "LOCALIZAR",
    ["Tase"] = "TASEAR",
    ["revive"] = "Revivir",
    ["VehicleInteraction"] = "INTERACCIÓN CON VEHÍCULOS",
    ["VehicleInformation"] = "INFORMACIÓN DEL VEHÍCULO",
    ["SeizeVehicle"] = "INCAUTAR VEHÍCULO",
    ["CallTow"] = "Llamar a grua",
    ["ForceLock"] = "FORZAR CIERRE",
    ["StopTraffic"] = "DETENER EL TRÁFICO",
    ["ReduceTraffic"] = "REDUCIR EL TRÁFICO",
    ["ResumeTraffic"] = "REANUDAR EL TRÁFICO",
    ["Availabel"] = "DISPONIBLE",
    ["WeaponsConfiguration"] = "CONFIGURACIÓN DE ARMAS",
    ["ShowHideWeapons"] = "Mostrar/Ocultar Armas",
    ["PistolPos"] = "POSICIÓN DE PISTOLA",
    ["RiflePos"] = "POSICIÓN DE RIFLE",
    ["Front"] = "FRONTAL",
    ["Behind"] = "DETRÁS",
    ["WaistCart"] = "CARTUCHERA DE CINTURA",
    ["NormalCart"] = "CARTUCHERA NORMAL",
    ["ChestCart"] = "CARTUCHERA DE PECHO",
    ["ThighCart"] = "CARTUCHERA DE MUSLO",
    ["LegCart"] = "CARTUCHERA DE PIERNA",
    ["SeparateLegCart"] = "CARTUCHERA DE PIERNA SEPARADA",
    ["Chest"] = "PECHO",
    ["Back"] = "ESPALDA",
    ["PoliceObjects"] = "OBJETOS DE LA POLICÍA",
    ["Cone"] = "CONO",
    ["Barrier"] = "BARRERA",
    ["Sign"] = "SEÑAL",
    ["Spikes"] = "PINCHOS",
    ["Radar"] = "RADAR",
    ["Delete"] = "ELIMINAR",
    ["Emergencies"] = "EMERGENCIAS",
    ["NoAlertRecived"] = "No se han recibido alertas",
    ["Settings"] = "AJUSTES",
    ["Guide"] = "Guia de Uso",
    ["General"] = "General",
    ["AlertsCode"] = "Alertas 488, 487, 487-V",
    ["DrugTrafficking"] = "Tráfico de Drogas",
    ["VehicleRobs"] = "Robos de Vehículos",
    ["Alerts215"] = "Alertas 215 / Armas",
    ["Radars"] = "Radares",
    ["KeyToAlert"] = "Clave para ir a la alerta",
    ["DeleteAlertKey"] = "Clave de Eliminación de Alerta",
    ["EmergencyOpenKey"] = "Clave de Apertura de Emergencias",
    ["Equipment"] = "Equipamiento",
    ["Cone"] = "Cono",
    ["ConeDesc"] = "El cono",
    ["Barriers"] = "Barreras",
    ["BarriersDesc"] = "Las barreras",
    ["TrafficLights"] = "Semáforos",
    ["TrafficLightsDesc"] = "Las señales de tráfico",
    ["Spikes"] = "Pinchos",
    ["SpikesDesc"] = "Los pinchos",
    ["Radar"] = "Radar",
    ["RadarDesc"] = "El radar",
    ["K9Title"] = "Control de K9",
    ["K9Follow"] = "Seguir",
    ["K9FollowDesc"] = "Ordenar al K9 que te siga",
    ["K9DontMove"] = "No Moverse",
    ["K9DontMoveDesc"] = "Ordenar al K9 que se quede en su lugar",
    ["K9Sit"] = "Sentarse",
    ["K9SitDesc"] = "Ordenar al K9 que se siente",
    ["K9LieDown"] = "Tumbarse",
    ["K9LieDownDesc"] = "Ordenar al K9 que se tumbe",
    ["K9SearhArea"] = "Buscar en el Área",
    ["K9SearhAreaDesc"] = "Ordenar al K9 que busque en el área",
    ["K9ReturnCar"] = "Volver al Coche",
    ["K9ReturnCarDesc"] = "Ordenar al K9 que vuelva al coche",
    
    -- RADIALS MSG --
    ['10.8'] = 'A la espera de asignación',
    ['10.10'] = 'Realiza su 10.10 ¡Buen servicio!',
    ['Cod 7'] = 'Realiza una parada técnica',
    ['254-V'] = 'Inicia 254-V a un %s [ %s ]  en %s',
    ['487-V'] = 'Se encuentra en visual del %s [$s] del último 487-V"',
    ['Cod 2'] = 'Inicia patrullaje ordinario',
    ['10.22'] = 'Se dirige a comisaría',
    ['6-Adam'] = 'Acude como 6-Adam',
    ['10.98'] = '10.98 a la última incidencia, procedemos con el 10.95 para posterior Cód 2',
    ['Veh 488'] = 'Vehíclo implicado en el 488 de %s: %s [%s]',
    ['Veh 487'] = 'Vehíclo implicado en el 487 de %s: %s [%s]',
    ['Veh Alt'] = 'Vehíclo implicado en un altercado %s [%s] en %s',
    ['10.6'] = 'Realiza una parada de tráfico a un %s [%s] en %s',
    
    -- ME COMMANDS --
    ['10-20ME'] = 'Lleva su mano hacia la radio y pulsa el botón de la ubicación',
    ['QRRME'] = 'Lleva su mano hacia la radio y pulsa el botón del pánico',
    ['Agentatrisk'] = 'Agente en peligro',
    ['domyfinguer'] = 'Al cabo de unos segundos saldría el resultado: %s',
    ['VehicleinofME'] = 'Abre la puerta del vehículo, mete a la persona en el vehículo, le pone el cinturon y cierra la puerta',
    ['VehicleofinME'] = 'Abre la puerta del vehículo, saca a la persona, le quita el cinturon y cierra la puerta',

    -- NOTIFY --
    ['noSeat'] = 'No hay asientos libres',

    -- Police Equipment
    ['PoliceEquipment'] = 'Equipamiento Policial',
    ['Equipment'] = 'Equipamiento',
    ['EquipmentDesc'] = 'Accede al equipamiento policial',
    ['LeaveEquipment'] = 'Dejar Equipamiento',
    ['LeaveEquipmentDesc'] = 'Deja tu equipamiento policial aquí',
    ['PoliceInventory'] = 'Inventario Policial',
    ['PoliceInventoryDesc'] = 'Para dejar comida, agua, etc',
    ['EvidenceProof'] = 'Evidencia / Pruebas',
    ['EvidenceProofDesc'] = 'Accede a las evidencias / pruebas',

    -- Holster
    ['DoHide'] = "do se vería como oculta algo debajo de su ropa",
    ['DoShow'] = "do se vería como hage un gesto dejando ver sus armas",
    ['SomethingWrong'] = "Parece que ha habido un error",
    ['HipHolster'] = 'Has cambiado la posición de la pistola a la cadera.',
    ['BackHolster'] = 'Has cambiado la posición de la pistola a la espalda.',
    ['LegHolster'] = 'Has cambiado la posición de la pistola a la pierna.',
    ['UpperHolster'] = 'Has cambiado la posición de la pistola en el pecho.',
    ['UnderPantsHolster'] = 'Has cambiado la posición de la pistola a los calzoncillos.',
    ['LongHolsterBack'] = 'Has cambiado la posición de las armas largas a la espalda.',
    ['LongHolsterFront'] = 'Has cambiado la posición de las armas largas al frente.',
    ["NoPersonNear"] = 'No hay nadie cerca.',

    ["VehicleRob"] = "Robo de vehículo",
    ["VehicleRobDesc"] = "Se ha robado un vehículo",
    ["Call911"] = "Llamada desde el 911",
    ["ForensicTitle"] = "Informe de Análisis Forense",
    ["ForensicDesc"] = "A través de este informe presente, el Departamento Científico de la Policía de San Andreas muestra el análisis completo de las pruebas adjuntas, el tiempo aproximado del hecho o en caso de no conocer el momento de la recopilación de las pruebas y/o su análisis.",
    ["EvidenceOf"] = "Prueba de",
    ["ApproximateTime"] = "Tiempo aproximado",
    ["MinutesAnd"] = "minutos y",
    ["SecondAprox"] = "Segundos aproximados",
    ["Shot"] = "disparo",
    ["Calibre"] = "Calibre",
    ["Identifier"] = "Identificador",
    ["Model"] = "Modelo",
    ["Amount"] = "Cantidad",

    ["January"] = "Enero",
    ["February"] = "Febrero",
    ["March"] = "Marzo",
    ["April"] = "Abril",
    ["May"] = "Mayo",
    ["June"] = "Junio",
    ["July"] = "Julio",
    ["August"] = "Agosto",
    ["September"] = "Septiembre",
    ["October"] = "Octubre",
    ["November"] = "Noviembre",
    ["December"] = "Diciembre",

    ["Shoot"] = "Disparo",
    ["BloodRemains"] = "Restos de sangre",
    ["BulletImpact"] = "Impacto de bala",
    ["VehicleBody"] = "Restos de carrocería",
    ["Fingerprint"] = "Huella dactilar",
    ["Weapon"] = "Arma",
    ["Drug"] = "Droga",
    ["Fingerprints"] = "Huellas dactilares",
    ["Of"] = "de",
    ["Speeding"] = "Exceso de velocidad",
    ["PlateCod9"] = "Matrícula en Cód-9",
    ["215"] = "Cod 215 - Disparo",

    ["ExistVehicleInSpawn"] = "Hay un vehículo ocupando la plaza, espera a que la plaza esté libre",
    ["MustLook"] = "Tienes que estar dentro de un vehiculo o mirandolo",
    ["ExistHelicopterInSpawn"] = "Hay un helicoptero ocupando la plaza, espera a que la plaza esté libre",
    ["ExistBoatInSpawn"] = "Hay un barco ocupando la plaza, espera a que la plaza esté libre",
    ["VehicleConfiscated"] = "Has confiscado un vehículo",
    ["CouldntOpenLock"] = "La cerradura no se pudo abrir",
    ["NoEvidence"] = "No tienes evidencias para analizar",
    ["RespectRol"] = "Por favor, respeta el rol de entorno",
    ["CantUncuff"] = "No puedes desesposar ahora mismo",
    ["CantDoThis"] = "No puedes hacer eso",
    ["HasToBeCuffed"] = "El jugador tiene que estar esposado para realizar esa acción",
    ["NotCuffed"] = "El jugador no está esposado",
    ["PersonFar"] = "El jugador está demasiado lejos",
    ["InvalidK9Veh"] = "No tienes el vehiculo especial de la unidad K9",
    ["AlreadyCallK9"] = "Ya has llamado a la unidad K9",
    ["K9Found"] = "Parece que la unidad K9 ha encontrado algo",
    ["K9NotFound"] = "La unidad K9 no ha encontrado nada relevante",
    ["CantFast"] = "No puedes hacer eso tan rapido",
    ["MustEnterNumber"] = "Debes introducir un número",
    ["InvNotSupported"] = "Tu sistema de inventario no está soportado actualmente",
    ["ChangedCloth"] = "Te has cambiado de ropa",
    ["NoFederalClothAvailable"] = "No hay ropa de preso para tu genero disponible",
    ["PedCantChangeCloth"] = "Las peds no se pueden poner/cambiar las ropas de preso",
    ["CantSendEmpty"] = "No puedes enviar los argumentos vacio a",
    ["PertenencesPickUp"] = "Has cogido tus pertenencias",
    ["LeavePertenences"] = "Has dejado tus pertenencias",
    ["NoPhotoSpace"] = "No tienes espacio para hacer la foto",
    ["NoSpaceInInv"] = "No tienes espacio en tu inventario para almacenar eso",
    ["ObtainedFingerpritns"] = "Has obtenido evidencias de huellas dactilares",
    ["NoFingerFound"] = "No has encontrado ninguna huella dactilar",
    ["EvidenceNotCategorized"] = "Esa evidencia no esta categorizada aún",
    ["PlayerNotConnected"] = "El jugador no esta conectado o no has puesto un tiempo valido",
    ["NewLimitation"] = "Se ha creado una nueva limitación",
    ["UpdatedLimitation"] = "Se ha actualizado una limitación",
    ["LimitationRemoved"] = "Se ha eliminado una limitación",
    ["CantFindLimitation"] = "No se ha podido encontrar la limitación",
    ["ProblemCreateNote"] = "Ha habido un problema al intentar crear la nota",
    ["ProblemCreateReport"] = "Ha habido un problema al intentar crear el informe",
    ["EnterMaxSpeed"] = "Introduce la velocidad maxima",
    ["Speed"] = "Velocidad",
    ["Assign"] = "Asignar",
    ["RemainSentance"] = "Condena restante:",
    ["Month"] = "MESES",
    ["InvalidVeh"] = "Vehículo invalido para realizar esta acción",

    ["AgentAlert"] = "AGENTE",
    ["VehicleAlert"] = "VEHÍCULO",
    ["PlateAlert"] = "MATRÍCULA",
    ["SpeedAlert"] = "VELOCIDAD",
    ["WeaponAlert"] = "ARMA",
    ["ErrorOccurred"] = "Se ha producido un error",
    ["NoTabPermission"] = "NO TIENES PERMISOS PARA ACCEDER A ESTA PESTAÑA",
    ["AssignedByDispatch"] = "Asignado por Despacho",
    ["VehicleMods"] = "Modificaciones del vehículo",
    ["Enabled"] = "Activado",
    ["Disabled"] = "Desactivado",

    ['camera'] = {
        ['takepick'] = 'Hacer foto', -- Only if you use origen_notify
        ['zoom'] = 'Zoom', -- Only if you use origen_notify
        ['cancel'] = 'Cancelar', -- Only if you use origen_notify
        ['fullText'] = '%s - Hacer foto\n%s - Zoom\n%s - Cancelar',
    },

    ['objects'] = {
        ['place'] = 'Colocar objeto', -- Only if you use origen_notify
        ['prev'] = 'Objeto anterior', -- Only if you use origen_notify
        ['next'] = 'Siguiente objeto', -- Only if you use origen_notify
        ['cancel'] = 'Cancelar', -- Only if you use origen_notify
        ['fullText'] = '%s - Colocar objeto\n%s - Objeto anterior\n%s - Siguiente objeto\n%s - Cancelar',
    },

    ['CollectEvidence'] = 'Recoger evidencias',
    ['ClearEvidence'] = 'Limpiar evidencias',
    ['EnterPlate'] = 'Ingresar la matrícula',
    ['ImpoundedVehicles'] = 'Vehículos confiscados',
    ['RequestVeh'] = 'Solicitar vehículo',
    ['Heliport'] = 'Heliopuerto',
    ['TakeOutHeli'] = 'Sacar el helicóptero',
    ['Pier'] = 'Muelle',
    ['TakeOutBoat'] = 'Sacar el bote',
    ['ConfiscateVehicle'] = 'Vehículo confiscado',
    ['PoliceFacilities'] = 'Instalaciones policiales',
    ['Confiscated'] = 'Confiscado',
    ['k9Attack'] = 'Ataque',
    ['ClosestAlert'] = 'Estás demasiado cerca del aviso',

    -- Comandos
    ['OpenPoliceCad'] = 'Abrir tablet policial',
    ['VehicleRadar'] = 'Radar de vehículos',
    ['LockRadar'] = 'Bloquear radar',
    ['MoveRadar'] = 'Mover radar',
    ['NextAlert'] = 'Siguiente alerta de despacho',
    ['PreviousAlert'] = 'Alerta de despacho anterior',
    ['K9Menu'] = 'Menú de control K9',
    ['SirensKey'] = 'Activar sirenas',
    ['LightsSirens'] = 'Activar luces',
    ['HandCuff'] = 'Policía: Esposar / Desesposar',
    ['QRR'] = 'Policía: QRR',
    ['Ten20'] = 'Policía: 10-20',
    ['Tackle'] = 'Policía: Hacer un placaje',
    ['VehicleInto'] = 'Meter en el vehículo',
    ['QuickAccess'] = 'Abrir el menú de acceso rápido',
    ['Minimap'] = 'Modo minimapa',
    ['TalkRadio'] = 'Hablar en tu radio',

    ['CantUseItem'] = "No puedes usar este item",

    ["InvalidVehicleToConfiscate"] = "Vehículo no válido para confiscar",
    ["TowTruckOnWay"] = "Una grúa va en camino",
    ["TowTruckArrived"] = "La grúa ha llegado y el vehículo ha sido cargado.",
    ["VehicleCannotBeFound"] = "No se puede encontrar el vehículo...",
    ['NoMoney'] = "No tienes suficiente dinero.",
    ['PoliceBill'] = 'Multa de policia',

    -- Tablet

    ["Home"] = "INICIO",
    ["Dispatch"] = "DISPATCH",
    ["CitizenSearch"] = "BUSCAR CIUDADANO",
    ["Reports"] = "DENUNCIAS",
    ["Cameras"] = "CÁMARAS",
    ["Polices"] = "POLICIAS",
    ["Vehicles"] = "VEHÍCULOS",
    ["CriminalCode"] = "CÓDIGO CRIMINAL",
    ["CriminalCodeAbrev"] = "C. CRIMINAL",
    ["SearchCapture"] = "BUSCA Y CAPTURA",
    ["Debtors"] = "DEUDORES",
    ["FederalManagement"] = "ADMINISTRACIÓN FEDERAL",
    ["AgentManagement"] = "GESTIÓN DE AGENTES",
    ["SecurityCameras"] = "CÁMARAS DE SEGURIDAD",
    ["PublicServices"] = "SERVICIOS PÚBLICOS",
    ["NoPoliceDuty"] = "NO HAY AGENTES DE SERVICIO",
    ["PoliceOnDuty"] = "AGENTE DE SERVICIO",
    ["PoliceSOnDuty"] = "AGENTES DE SERVICIO",
    ["OutDuty"] = "FUERA DE SERVICIO",
    ["InDuty"] = "EN SERVICIO",
    ["TimeControl"] = "CONTROL DE TIEMPO",
    ["Radio"] = "RADIO",
    ["Duty"] = "SERVICIO",
    ["WelcomeTitle"] = "BIENVENIDO A LA RED INTERNA DE LA POLICÍA",
    ["WelcomeTitleAmbulance"] = "BIENVENIDO A LA RED INTERNA DE PARAMÉDICOS",
    ["WelcomeDescAmbulance"] = "Bienvenido a la aplicación oficial de paramédicos. Esta aplicación ha sido diseñada para mejorar la eficiencia y la comunicación en el trabajo diario de los paramédicos.",
    ["WelcomeDesc"] = "Bienvenido a la aplicación oficial de la policía y el sheriff. Esta aplicación ha sido diseñada para mejorar la eficiencia y la comunicación en el trabajo diario de los agentes de policía y sheriffs.",
    ["NotInDuty"] = "NO ESTÁS EN SERVICIO",
    ["AgentsOnDuty"] = "AGENTES DE SERVICIO",
    ["DeptAbrev"] = "DEPT.",
    ["Rank"] = "RANGO",
    ["Agent"] = "AGENTE",
    ["Status"] = "ESTADO",
    ["LocAbrev"] = "POS.",
    ["BroadcastSAFD"] = "TRANSMITIR SAFD",
    ["BroadcastSapd"] = "TRANSMITIR SAPD",
    ["SouthUnits"] = "UNIDADES DEL SUR",
    ["Talk"] = "HABLAR",
    ["NorthUnits"] = "UNIDADES DEL NORTE",
    ["SpecialUnits"] = "UNIDADES ESPECIALES",
    ["EMSUnits"] = "UNIDADES EMS",
    ["NoUsersChannel"] = "NO HAY NINGÚN USUARIO EN ESTE CANAL",
    ["Available"] = "DISPONIBLE",
    ["NotAvailable"] = "NO DISPONIBLE",
    ["InternalRadio"] = "RADIO INTERNA",
    ["TypeMessage"] = "ESCRIBE TU MENSAJE...",
    ["Emergencies"] = "EMERGENCIAS",
    ["Notice"] = "AVISO",
    ["Title"] = "TÍTULO",
    ["Location"] = "LOCALIZACIÓN",
    ["Time"] = "TIEMPO",
    ["DetailedDesc"] = "DESCRIPCIÓN DETALLADA",
    ["Notes"] = "NOTAS",
    ["AddNoteToEmergency"] = "Añadir notas a la emergencia",
    ["SaveNote"] = "GUARDAR NOTA",
    ["SendToUnit"] = "ENVIAR A LA UNIDAD",
    ["AvailableUnits"] = "UNIDADES DISPONIBLES",
    ["LastAlerts"] = "ULTIMAS ALERTAS",
    ["RefAbrev"] = "REF.",
    ["Emergency"] = "EMERGENCIA",
    ["Ago"] = "HACE:",
    ["Units"] = "UNIDADES",
    ["NoRecived"] = "No se ha recibido ninguna alerta",
    ["DeleteAlert"] = "ELIMINAR ALERTA",
    ["TimeHistory"] = "HISTORIAL DE TIEMPO",
    ["Agent"] = "AGENTE",
    ["ClockIn"] = "ENTRADA",
    ["ClockOut"] = "SALIDA",
    ["Total"] = "TOTAL",
    ["ShowingRecords"] = "Mostrando registros de _START_ a _END_ de un total de _TOTAL_ registros",
    ["TopWorkers"] = "MEJORES TRABAJADORES",
    ["MinAbrev"] = "min",
    ["Cancel"] = "Cancelar",
    ["sProcessing"] = "Procesando...",
    ["sLengthMenu"] = "Mostrando 20 registros",
    ["sZeroRecords"] = "No se han encontrado resultados",
    ["sEmptyTable"] = "No hay datos disponibles en la tabla",
    ["sInfo"] = "Mostrando registros del _START_ al _END_ de un total de _TOTAL_ registros",
    ["sInfoEmpty"] = "Mostrando registros del 0 al 0 de un total de 0 registros",
    ["sInfoFiltered"] = "(filtrando de un total de _MAX_ registros)",
    ["sSearch"] = "Buscar:",
    ["sLoadingRecords"] = "Cargando...",
    ["oPaginateFirst"] = "Primero",
    ["oPaginateLast"] = "Ultimo",
    ["oPaginateNext"] = "Siguiente",
    ["oPaginatePrevious"] = "Anterior",
    ["sSortAscending"] = "] = Activar para ordenar la columna de manera ascendente",
    ["sSortDescending"] = "] = Activar para ordenar la columna de manera descendente",
    ["Citizens"] = "CIUDADANOS",
    ["CitizenSearch"] = "BUSCAR CIUDADANO",
    ["CitizenList"] = "LISTA DE CIUDADANOS",
    ["SearchCitizen"] = "Buscar un ciudadano...",
    ["PerformSearch"] = "REALIZA UNA BUSQUEDA PARA MOSTRAR RESULTADOS",
    ["CitizenProfile"] = "PERFIL DE CIUDADANO",
    ["SelectACitizen"] = "SELECCIONA UN CIUDADANO PARA CARGAR LA INFORMACIÓN",
    ["Name"] = "NOMBRE",
    ["Surname"] = "APELLIDO",
    ["Gender"] = "GÉNERO",
    ["Nationality"] = "NACIONALIDAD",
    ["Birthdate"] = "FECHA DE NACIMIENTO",
    ["Id"] = "ID",
    ["PhoneNumber"] = "NÚMERO DE TELÉFONO",
    ["BankAccount"] = "NÚMERO DE CUENTA BANCARIA",
    ["Job"] = "TRABAJO",
    ["InSearchCapture"] = "EN BUSCA Y CAPTURA",
    ["Dangerous"] = "PELIGROSO",
    ["Yes"] = "SI",
    ["No"] = "NO",
    ["NewNote"] = "NUEVA NOTA",
    ["NoRegisteredNotes"] = "NO HAY NOTAS REGISTRADAS",
    ["Fine"] = "MULTAS",
    ["AddFine"] = "AÑADIR MULTA",
    ["NoRegisteredFines"] = "No hay multas registradas",
    ["NoData"] = "No hay datos disponibles",
    ["Licenses"] = "LICENCIAS",
    ["Weapons"] = "ARMAS",
    ["Houses"] = "PROPIEDADES",
    ["NoteTitle"] = "Título de la nota",
    ["TextNote"] = "Texto de la nota",
    ["Save"] = "GUARDAR",
    ["More"] = "MÁS",	
    ["SearchCriminalCode"] = "Buscar en el código criminal...",
    ["Article"] = "ARTÍCULO",
    ["Description"] = "DESCRIPCIÓN",
    ["Amount"] = "CANTIDAD",
    ["Sentence"] = "SENTENCIA",
    ["Action"] = "ACCIONES",
    ["CustomFine"] = "MULTA PERSONALIZADA",
    ["FineConcepts"] = "CONCEPTOS DE MULTA",
    ["Concept"] = "CONCEPTO",
    ["Add"] = "AGREGAR",
    ["EnterConcept"] = "Añade el concepto de la multa",
    ["EnterAmount"] = "Añada la cantidad",
    ["EnterSentence"] = "Añada los meses de sentencia",
    ["ProcessFine"] = "PROCESAR MULTA",
    ["TotalSentence"] = "SENTENCIA TOTAL",
    ["Month"] = "meses",
    ["TotalAmount"] = "CANTIDAD TOTAL",
    ["ConfirmFine"] = "CONFIRMAR MULTA",
    ["FineAdded"] = "La multa ha sido añadida correctamente",
    ["NoArticle"] = "No le has añadido ningún artículo a la multa",
    ["ReportList"] = "LISTA DE DENUNCIAS",
    ["SearchReport"] = "Buscar una denuncia...",
    ["AllTags"] = "Todos los tags",
    ["NoResultFound"] = "Ningún resultado encontrado",
    ["NewReport"] = "NUEVA DENUNCIA",
    ["SelectReport"] = "SELECCIONA UNA DENUNCIA PARA CARGAR LA INFORMACIÓN",
    ["Report"] = "DENUNCIA",
    ["VehicleList"] = "LISTA DE VEHÍCULOS",
    ["TypeLicense"] = "Escribe la matrícula para buscar...",
    ["PerformSearchVehicle"] = "Realiza una búsqueda para mostrar resultados",
    ["VehicleData"] = "DATOS DEL VEHÍCULO",
    ["NewChapter"] = "NUEVO CAPÍTULO",
    ["NewArticle"] = "NUEVO ARTÍCULO",
    ["SearchCriminalCode"] = "Buscar en el código criminal...",
    ["Delete"] = "BORRAR",
    ["CreateNewChapter"] = "CREAR NUEVO CAPÍTULO",
    ["ChapterName"] = "Nombre del capítulo",
    ["SaveChapter"] = "GUARDAR CAPÍTULO",
    ["CreateNewArticle"] = "CREAR NUEVO ARTÍCULO",
    ["SelectChapter"] = "SELECCIONA UN CAPÍTULO",
    ["ArticleName"] = "NOMBRE DEL ARTÍCULO",
    ["EnterName"] = "Introduce el nombre del artículo",
    ["DescriptionArticle"] = "DESCRIPCIÓN DEL ARTÍCULO",
    ["EnterDescription"] = "Introduce la descripción del artículo",
    ["SaveArticle"] = "GUARDAR ARTÍCULO",
    ["DeleteArticle"] = "ELIMINAR ARTÍCULO",
    ["AreYouSureDeleteArticle"] = "¿Estás seguro de que quieres eliminar este artículo?",
    ["Remove"] = "ELIMINAR",
    ["DeleteChapter"] = "ELIMINAR CAPÍTULO",
    ["AreYouSureDeleteArticle"] = "¿Estás seguro de que quieres eliminar este capítulo?",
    ["SubjectsInSearch"] = "PERSONAS EN BUSCA Y CAPTURA",
    ["NoSubjectsInSearch"] = "NO HAY NINGUNA PERSONA EN BUSCA Y CAPTURA",
    ["Close"] = "CERRAR",
    ["DebtSubjects"] = "DEUDORES",
    ["FindSubject"] = "Encontrar un sujeto...",
    ["NoDebtors"] = "NO HAY NINGÚN DEUDOR",
    ["FederalManagement"] = "ADMINISTRACIÓN FEDERAL",
    ["AddConden"] = "AÑADE UNA NUEVA CONDENA",
    ["CitizenID"] = "ID del ciudadano",
    ["DangerousOrNot"] = "PELIGROSO O NO",
    ["NoFederals"] = "NO HAY PRESOS FEDERALES",
    ["SecurityCameras"] = "CÁMARAS DE SEGURIDAD",
    ["BusinessCameras"] = "CÁMARAS DE NEGOCIOS",
    ["VehicleCameras"] = "CÁMARAS DE VEHÍCULOS",
    ["BodyCam"] = "BODYCAMS",
    ["Meters"] = "metros",
    ["Refresh"] = "ACTUALIZAR",
    ["SingleCamera"] = "CÁMARA",
    ["PoliceManagement"] = "ADMINISTRACIÓN DE POLICÍAS",
    ["PoliceList"] = "LISTA DE POLICÍAS",
    ["LookAgent"] = "Buscar un agente...",
    ["GenerateBadge"] = "GENERAR PLACA",
    ["AddPolice"] = "AÑADIR POLICÍA",
    ["Range"] = "RANGO",
    ["PlateAbrev"] = "NÚMERO DE PLACA",
    ["Award"] = "MEDALLAS",
    ["AddAward"] = "AÑADIR MEDALLA",
    ["NoDecorations"] = "NO HAY CONDECORACIONES",
    ["Divisions"] = "DIVISIONES",
    ["SetDivision"] = "ESTABLECER DIVISIÓN",
    ["FirePolice"] = "DESPEDIR POLICÍA",
    ["PoliceFile"] = "FICHA POLICIAL",
    ["SelectAnAgent"] = "Selecciona un agente para ver su ficha policial",
    ["NoRegisteredReports"] = "No hay denuncias registradas",
    ["Jurisdiction"] = "JURISDICCIÓN",
    ["Informs"] = "INFORMES",
    ["Atention"] = "ATENCIÓN",
    ["ThisActionCantRevert"] = "ESTA ACCIÓN DESPEDIARÁ AL POLICÍA.",
    ["DoYouWishContinue"] = "¿QUIERES CONTINUAR?",
    ["Confirm"] = "CONFIRMAR",
    ["AddDivision"] = "AÑADIR DIVISIÓN",
    ["AddCondecoration"] = "AÑADIR CONDECORACIÓN",
    ["DoWantGenPlate"] = "¿QUIERES GENERAR ESTA PLACA?",
    ["YouMustOpenProfile"] = "TIENES QUE TENER ABIERTO EL PERFIL DE UN AGENTE PARA GENERAR LA PLACA",
    ["PoliceBadgeGenerated"] = "PLACA DE POLICÍA GENERADA",
    ["CheckInventory"] = "Comprueba tu inventario",
    ["NoPeopleNear"] = "NO HAY NINGUNA PERSONA CERCA.",
    ["ConnectedTo"] = "CONECTADO A",
    ["Disconnect"] = "DESCONECTAR",
    ["ShortCuts"] = "ATAJOS",
    ["AlternateMute"] = "ALTERNAR SILENCIO",
    ["TalkToCentral"] = "HABLAR CON CENTRAL",
    ["TalkToWaiting"] = "HABLAR EN ESPERA DE ASIGNACIÓN",
    ["TalkToPoliceSta"] = "HABLAR CON LA ESTACIÓN DE POLICÍA",
    ["TalkToTacs"] = "HABLAR CON TACS",
    ["TalkSafd"] = "HABLAR CON SAFD",
    ["BroadcastSAPD"] = "TRANSIMITIR EN SAPD",
    ["HowUploadImage"] = "¿COMO QUIERES SUBIR LA IMAGEN?",
    ["Photo"] = "FOTO",
    ["AddURL"] = "AÑADIR URL",
    ["Default0Months"] = "0 Meses",
    ["ChangePlateNumber"] = "CAMBIAR NÚMERO DE PLACA",
    ["PlateNumberAbrev"] = "Nº DE PLACA",
    ["PlateMin3"] = "La placa debe tener un mínimo de 4 caracteres",
    ["ReportName"] = "NOMBRE DE LA DENUNCIA",
    ["ReportID"] = "ID DE LA DENUNCIA",
    ["DateAndHour"] = "FECHA Y HORA",
    ["AgentInCharge"] = "AGENTE A CARGO",
    ["ReportDescription"] = "DESCRIPCIÓN DE LA DENUNCIA",
    ["EnterReportDesc"] = "Introduce la descripción de la denuncia",
    ["Evidences"] = "EVIDENCIAS",
    ["WithoutUbication"] = "Sin ubicación asignada",
    ["AddEvidence"] = "AÑADIR EVIDENCIA",
    ["PeopleInvolved"] = "PERSONAS INVOLUCRADAS",
    ["NoPeopleInvolved"] = "No hay personas involucradas",
    ["AddPeople"] = "AÑADIR PERSONA",
    ["AgentsInvolved"] = "AGENTES INVOLUCRADOS",
    ["AddAgent"] = "AÑADIR AGENTE",
    ["Tags"] = "TAGS",
    ["SelectLabel"] = "Selecciona una etiqueta",
    ["Victims"] = "VICITIMAS",
    ["AddVictim"] = "AÑADIR VÍCTIMA",
    ["AddVehicle"] = "AÑADIR VEHÍCULO",
    ["DestroyReport"] = "ELIMINAR DENUNCIA",
    ["NoPermission"] = "No tienes permiso para acceder a esta página",
    ["seconds"] = "segundos",
    ["minutes"] = "minutos",
    ["NoResult"] = "No se han encontrado resultados",
    ["RemainMonth"] = "Meses restantes",
    ["ServingSentance"] = "Cumpliendo condena",
    ["Release"] = "Liberar",
    ["Sleeping"] = "Durmiendo",
    ["IntroduceName"] = "Introduce un nombre",
    ["AlertAsigned"] = "Se ha asignado la alerta #%s a %s",

    ["NoPermission"] = "¡No tienes permisos para acceder!",
    ["NoPermissionPage"] = "No tienes permiso para acceder a esta página",
    ["MinimumCharacters"] = "Debes introducir al menos 3 caracteres",
    ["FindACitizen"] = "Buscar a un ciudadano...",
    ["LookForAgent"] = "Buscar a un agente...",
    ["EnterNameToSearch"] = "Ingresa un nombre en la barra de búsqueda para mostrar resultados",
    ["UnknownKey"] = "CLAVE NO RECONOCIDA",

    ["RadarOf"] = "Radar de",
    ["Velocity"] = "Velocidad",
    ["LicensePlate"] = "Matrícula",
    ["TrafficStop"] = "Parada de tráfico",
    ["SpeedReduction"] = "Reducción de velocidad",
    ["Color"] = "Color",
    ["NoRadio"] = "¡No tienes radio!",
    ["NoUsers"] = "No hay usuarios en este canal",
    ["NoPermissionMoveUsers"] = "NO TIENES PERMISOS PARA MOVER USUARIOS EN LA RADIO",
    ["ChangeRange"] = "Cambiar rango",
    ["Phone"] = "Teléfono",
    ["Model"] = "Modelo",
    ["Owner"] = "Propietario",
    ["SearchAndCapture"] = "Buscar y capturar",
    ["VehicleAnnotations"] = "Anotaciones del vehículo",
    ["EnterAnnotation"] = "Ingresa alguna anotación...",
    ["VehicleNotFound"] = "Vehículo no encontrado",
    ["VehicleSearchUpdated"] = "La búsqueda de vehículos se ha actualizado",
    ["VehicleDescriptionUpdated"] = "La descripción del vehículo se ha actualizado",
    ["NoPermissionsConfigured"] = "no tiene permisos configurados",

    ["Operations"] = "Operaciones",
    ["ShapesCreated"] = "Formas creadas",
    ["NoShapes"] = "No hay formas creadas",
    ["TitleTooShort"] = "El nombre debe tener más de 5 caracteres.",
    ["DeleteShape"] = "Eliminar forma",
    ["ConfirmDeleteShape"] = "¿Estás seguro de que deseas eliminar esta forma?",
    ["CreateNewShape"] = "CREAR UNA NUEVA FORMA",
 
    ['SelectReport'] = 'Seleccione un informe para cargar su información',
    ['NoEvidences'] = 'No hay evidencias en su inventario',
    ['NoLocation'] = 'Sin ubicación asignada',
    ['NoVehicleInvolved'] = 'No hay vehículos involucrados',
    ['NoVictimsInvolved'] = 'No hay víctimas involucradas',
    ['NoAgentsInvolved'] = 'No hay agentes involucrados',
    ['OpenCase'] = 'Abrir caso',
    ['CaseClosed'] = 'Caso cerrado',
    ['NullCase'] = 'Caso nulo',
    ["ThisActionRemoveEvidence"] = "Esta acción eliminará la evidencia de forma definitiva.",
    ["DoYouWantContinue"] = "¿Deseas continuar?",
    ['ThisActionEliminateReport'] = 'Esta acción eliminará el informe permanentemente, incluyendo la evidencia adjunta a él.',
    ['ThisWillAffectFines'] = 'Esto no afectará las multas, las cuales permanecerán en el sistema.',
    ['TotalPenalty'] = 'Total de multa',
    ['TotalAmount'] = 'Cantidad total',
    ['SendFine'] = 'MULTAR',
    ['EnterPlateEngine'] = 'Ingresa una matrícula en el buscador para ver los resultados',

    ['SelectCitizen'] = 'Selecciona un ciudadano',
    ['EnterURLImage'] = 'Introduce la URL de la imagen',
    ['SaveImage'] = 'Guardar imagen',
    ['SelectAnAgent2'] = 'Selecciona un agente',
    ['SelectVehicle'] = 'Selecciona un vehículo',
    ['EnterNameEngine'] = 'Introduce un nombre para el motor de búsqueda para mostrar los resultados',
    ['FindVehicles'] = 'Buscar vehículos...',
    ['SelectEvidence'] = 'Selecciona evidencia',
    ['NoEvidenceInv'] = 'No hay evidencia en tu inventario',

    ["AddLicense"] = "AGREGAR LICENCIA",
    ["AddNewLicense"] = "AGREGAR NUEVA LICENCIA",
    ['Expiration'] = 'Vencimiento',
    ['AddedLicense'] = 'Has agregado una licencia',

    ['ReferencesLocation'] = 'Localización GPS(referencias)',
    ['BodyCamera'] = 'Camara corporal',
    ['Animation'] = 'Animación',
    ['NoSignal'] = 'SIN SEÑAL',

    ['LowCaliber'] = 'Bajo calibre',
    ['ShotgunCaliber'] = 'Cartuchos de escopeta',
    ['MediumCaliber'] = 'Medio calibre',
    ['HighCaliber'] = 'Gran calibre',

    ['LicensesList'] = {
        ['Driver'] = 'Conductor',
        ['Weapon'] = 'Arma',
        ['Business'] = 'Negocio',
    },
    
    -- Condecorations
    ["Condecorations"] = {
        Valor = {
            id = 'Valor',
            name = 'Medalla al Valor',
            description =
                'La medalla al valor es la aplicación más alta de la ley otorgada a los oficiales y se otorga por actos individuales de extraordinario valor o heroísmo llevados a cabo en el cumplimiento del deber con un riesgo personal extremadamente alto y potencialmente mortal.',
            url = 'vV0Wm9A.png'
        },
        Preservacion = {
            id = 'Preservacion',
            name = 'Medalla de Preservación de la Vida',
            description =
                'La medalla de preservación de la vida puede ser otorgada a un oficial que se haya distinguido por el uso de tácticas excepcionales y el ejercicio de un buen juicio, más allá de las demandas normales del deber, para preservar la vida de otro durante un encuentro volátil o peligroso, al tiempo que protege la seguridad del público y sus oficiales.',
            url = '4Zmnp8u.png'
        },
        Policia = {
            id = 'Policia',
            name = 'Medalla de la Policía',
            description =
                'La Medalla de la Policía es un premio por valentía, generalmente otorgado a los oficiales por actos individuales de heroísmo en el cumplimiento del deber, aunque no más allá de la llamada del deber, como se requiere para la medalla al valor.',
            url = 'BwPTQWC.png'
        },
        Estrella = {
            id = 'Estrella',
            name = 'Estrella de la Policía',
            description =
                'La estrella de la policía es un premio por valentía, generalmente otorgado a los oficiales por realizar un juicio excepcional y/o usar tácticas hábiles para desactivar situaciones peligrosas y estresantes.',
            url = 'U4vBD1Z.png'
        },
        Salvavidas = {
            id = 'Salvavidas',
            name = 'Medalla de Salvavidas de la Policía',
            description =
                'La Medalla de Rescate de la Policía es un premio por valentía, generalmente otorgado a los oficiales por tomar medidas para rescatar o intentar rescatar a un compañero oficial o cualquier persona en peligro inminente.',
            url = 'TuL7fDQ.png'
        },
        Distinguido = {
            id = 'Distinguido',
            name = 'Medalla Distinguida del Servicio de Policía',
            description =
                'La medalla distinguida del servicio policial es el servicio más alto para el departamento y puede otorgarse a los empleados para distinguir un servicio excepcional en un deber de gran responsabilidad o importancia crítica para la aplicación de la ley.',
            url = 'rojxaCL.png'
        },
        Meritorio = {
            id = 'Meritorio',
            name = 'Medalla del Servicio Meritorio de la Policía',
            description =
                'La medalla del servicio policial meritorio se otorga a los empleados para distinguir un servicio excepcional en un deber de gran responsabilidad o importancia crítica para el cumplimiento de la ley, pero en menor medida que el requerido para la medalla de servicio policial distinguido.',
            url = 'cHAlfOj.png'
        },
        LogroMeritorio = {
            id = 'LogroMeritorio',
            name = 'LOGRO MERITORIO DE LA POLICÍA',
            description =
                'La medalla de logro policial está diseñada principalmente para reconocer a los empleados civiles. La medalla se otorga por logros sostenidos, a largo plazo y notables o por un logro significativo y notable en la ejecución de tareas administrativas, de oficina o artesanales.',
            url = 'laujeQV.png'
        },
        DistinguidoComision = {
            id = 'DistinguidoComision',
            name = 'Medalla Distinguida del Servicio de la Comisión de Policía',
            description =
                'La Medalla Distinguida del Servicio de la Comisión de Policía se otorga a los oficiales que se distinguen por realizar un servicio excepcional para el SAPD o por desempeñarse en una situación de emergencia estresante con buen juicio y valentía.',
            url = 'YCOtC5l.png'
        },
        IntegridadComision = {
            id = 'IntegridadComision',
            name = 'Medalla de Integridad de la Comisión de Policía',
            description =
                'La Medalla de Integridad de la Comisión de Policía se otorga a los empleados que muestran un acto de integridad ejemplar, especialmente cuando ese acto requiere carácter excepcional, fuerza y coraje moral contra obstáculos sustanciales.',
            url = 'Ia6hPav.png'
        },
        Comunitaria = {
            id = 'Comunitaria',
            name = 'Medalla de la Policía Comunitaria',
            description =
                'La Medalla de la Policía Comunitaria se otorga al personal que ha resuelto un problema comunitario importante, incluyendo a la comunidad en el proceso de resolución de problemas y/o ha mostrado un compromiso con la filosofía comunitaria de la policía de SAPD.',
            url = 'bDkoKfS.png'
        },
        RelacionesHumanas = {
            id = 'RelacionesHumanas',
            name = 'Medalla de Relaciones Humanas',
            description =
                'La Medalla de Relaciones Humanas se otorga a los oficiales que han mostrado gran compasión en sus actividades diarias y han ido más allá del llamado del deber en su respuesta a otros seres humanos.',
            url = 'IMlJLE4.png'
        },
        Service2 = {
            id = 'Service2',
            name = 'Servicio durante 2 meses',
            description =
                'Este premio se entrega a aquellos miembros que han prestado servicio en San Andreas durante más de 2 meses en la Policía de San Andreas.',
            url = '22OMcKF.png'
        },
        Service4 = {
            id = 'Service4',
            name = 'Servicio por 4 meses',
            description =
                'Este premio se entrega a aquellos miembros que han prestado servicio en San Andreas durante más de 4 meses en la Policía de San Andreas.',
            url = 'waOO0p1.png'
        },
        Service6 = {
            id = 'Service6',
            name = 'Servicio por 6 meses',
            description =
                'Este premio se entrega a aquellos miembros que han prestado servicio en San Andreas durante más de 6 meses.',
            url = 'zw1TPMg.png'
        },
        Service8 = {
            id = 'Service8',
            name = 'Servicio por 8 meses',
            description =
                'Este premio se entrega a aquellos miembros que han prestado servicio en San Andreas durante más de 8 meses.',
            url = 'oVvluyF.png'
        },
        Service10 = {
            id = 'Service10',
            name = 'Servicio por 10 meses',
            description =
                'Este premio se entrega a aquellos miembros que han prestado servicio en San Andreas durante más de 10 meses.',
            url = '9E01TG1.png'
        },
        Service12 = {
            id = 'Service12',
            name = 'Servicio por 12 meses',
            description =
                'Esta condecoración se entrega a aquellos miembros que han prestado servicio en San Andreas durante más de un año.',
            url = 'FTz1dTx.png'
        },
    },
    -- Divisions
    ["DivisionsData"] = {
        IAA = {
            id = 'IAA',
            name = 'Asuntos Internos',
            url = 't764YV8.png'
        },
        FIB = {
            id = 'FIB',
            name = 'Bureau Federal de Investigaciones (FIB)',
            url = 'BtEEw1S.png'
        },
        SWAT = {
            id = 'SWAT',
            name = 'Unidad de Armas y Tácticas Especiales (SWAT)',
            url = 'v4dW751.png'
        },
        HPD = {
            id = 'HPD',
            name = 'División de Patrulla de Carreteras (HPD)',
            url = 'scWMKjL.png'
        },
        IRD = {
            id = 'IRD',
            name = 'Departamento de Instrucción y Reclutamiento (IRD)',
            url = 'OCEBbrB.png'
        },
        UNP = {
            id = 'UNP',
            name = 'Unidad de Negociación Policial (PNU)',
            url = 'DlGNQiV.png'
        },
        UM = {
            id = 'UM',
            name = 'Unidad Marítima (MU)',
            url = 'DlGNQiV.png'
        }
    },
}

LogsTranslations = {
    ['Identifiers'] = 'IDENTIFICADORES',
    ['ID'] = 'ID',
    ['Name'] = 'Nombre',
    
    ['Alert'] = {
        title = 'Alerta enviada',
        message = 'Se ha enviado una alerta con el comando `%s`.\nMensaje: %s',
    },
    ['ClockOut'] = {
        title = 'Salida del servicio',
        message = 'El jugador ha finalizado el servicio.\nHora de entrada: `%s`\nHora de salida: `%s`\nTotal: `%s` minutos',
    },
    ['CreateShape'] = {
        title = 'Forma creada',
        message = 'El jugador creó una forma.\nNombre: `%s`',
    },
    ['DeleteShape'] = {
        title = 'Forma eliminada',
        message = 'El jugador eliminó una forma.\nNombre: `%s`',
    },
    ['CreateNote'] = {
        title = 'Nota creada',
        message = 'El jugador creó una nota.\nTítulo: `%s`\nDescripción: `%s`\nAutor: `%s`',
    },
    ['DeleteNote'] = {
        title = 'Nota eliminada',
        message = 'El jugador eliminó una nota.\nID de la nota: `%s`',
    },
    ['CreateReport'] = {
        title = 'Informe creado',
        message = 'El jugador creó un informe.\nTítulo: `%s`\nDescripción: `%s`\nAutor: `%s`',
    },
    ['DeleteReport'] = {
        title = 'Informe eliminado',
        message = 'El jugador eliminó un informe.\nID del informe: `%s`',
    },
    ['SetBadge'] = {
        title = 'Establecer placa policial',
        message = 'El jugador estableció una placa policial.\nOficial: `%s`\nPlaca: `%s`',
    },
    ['FirePolice'] = {
        title = 'Oficial despedido',
        message = 'El jugador despidió a un oficial.\nOficial: `%s`',
    },
    ['HirePolice'] = {
        title = 'Oficial contratado',
        message = 'El jugador contrató a un jugador.\nNombre: `%s`\nPuesto: `%s`\nGrado: `%s`',
    },
    ['UpdatePlayer'] = {
        title = 'Jugador actualizado',
        message = 'El jugador despidió/contrató a un jugador.\nIdentificador de jugador: `%s`\nPuesto: `%s`\nGrado: `%s`',
    },
    ['NewPhoto'] = {
        title = 'Nueva foto',
        message = 'El jugador tomó una nueva foto.',
    },
    ['EnterFederal'] = {
        title = 'Entrar en federal',
        message = 'El oficial ha enviado a un prisionero a federal.\nTiempo: `%s minutos`\nOficial: `%s`',
    },
    ['ExitFederal'] = {
        title = 'Salida de federal',
        message = 'El jugador ha sido liberado.',
    },
    ['AddBill'] = {
        title = 'Factura agregada',
        message = 'Se ha creado una nueva factura.\n\nOficial: `%s`\nCantidad: `%s`\nMeses: `%s`\nConceptos: %s',
    },
    ['PayBill'] = {
        title = 'Factura pagada',
        message = 'El jugador ha pagado una factura.\nCantidad: `%s`\nID de factura: `%s`',
    },
    ['UseBodyCam'] = {
        title = 'Bodycam utilizada',
        message = 'El jugador ha usado una cámara.\nNombre de la cámara: `%s`',
    },
    ['UseCarCam'] = {
        title = 'Cámara de vehículo utilizada',
        message = 'El jugador ha usado una cámara.\nPlaca del coche: `%s`',
    },    
}