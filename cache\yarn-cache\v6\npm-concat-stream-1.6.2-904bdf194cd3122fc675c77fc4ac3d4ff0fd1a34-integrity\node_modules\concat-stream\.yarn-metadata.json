{"manifest": {"name": "concat-stream", "version": "1.6.2", "description": "writable stream that concatenates strings or binary data and calls a callback with the result", "tags": ["stream", "simple", "util", "utility"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "http://github.com/maxogden/concat-stream.git"}, "bugs": {"url": "http://github.com/maxogden/concat-stream/issues"}, "engines": {"node": ">= 0.8"}, "main": "index.js", "files": ["index.js"], "scripts": {"test": "tape test/*.js test/server/*.js"}, "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}, "devDependencies": {"tape": "^4.6.3"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-concat-stream-1.6.2-904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34-integrity\\node_modules\\concat-stream\\package.json", "readmeFilename": "readme.md", "readme": "# concat-stream\n\nWritable stream that concatenates all the data from a stream and calls a callback with the result. Use this when you want to collect all the data from a stream into a single buffer.\n\n[![Build Status](https://travis-ci.org/maxogden/concat-stream.svg?branch=master)](https://travis-ci.org/maxogden/concat-stream)\n\n[![NPM](https://nodei.co/npm/concat-stream.png)](https://nodei.co/npm/concat-stream/)\n\n### description\n\nStreams emit many buffers. If you want to collect all of the buffers, and when the stream ends concatenate all of the buffers together and receive a single buffer then this is the module for you.\n\nOnly use this if you know you can fit all of the output of your stream into a single Buffer (e.g. in RAM).\n\nThere are also `objectMode` streams that emit things other than Buffers, and you can concatenate these too. See below for details.\n\n## Related\n\n`concat-stream` is part of the [mississippi stream utility collection](https://github.com/maxogden/mississippi) which includes more useful stream modules similar to this one.\n\n### examples\n\n#### Buffers\n\n```js\nvar fs = require('fs')\nvar concat = require('concat-stream')\n\nvar readStream = fs.createReadStream('cat.png')\nvar concatStream = concat(gotPicture)\n\nreadStream.on('error', handleError)\nreadStream.pipe(concatStream)\n\nfunction gotPicture(imageBuffer) {\n  // imageBuffer is all of `cat.png` as a node.js Buffer\n}\n\nfunction handleError(err) {\n  // handle your error appropriately here, e.g.:\n  console.error(err) // print the error to STDERR\n  process.exit(1) // exit program with non-zero exit code\n}\n\n```\n\n#### Arrays\n\n```js\nvar write = concat(function(data) {})\nwrite.write([1,2,3])\nwrite.write([4,5,6])\nwrite.end()\n// data will be [1,2,3,4,5,6] in the above callback\n```\n\n#### Uint8Arrays\n\n```js\nvar write = concat(function(data) {})\nvar a = new Uint8Array(3)\na[0] = 97; a[1] = 98; a[2] = 99\nwrite.write(a)\nwrite.write('!')\nwrite.end(Buffer.from('!!1'))\n```\n\nSee `test/` for more examples\n\n# methods\n\n```js\nvar concat = require('concat-stream')\n```\n\n## var writable = concat(opts={}, cb)\n\nReturn a `writable` stream that will fire `cb(data)` with all of the data that\nwas written to the stream. Data can be written to `writable` as strings,\nBuffers, arrays of byte integers, and Uint8Arrays. \n\nBy default `concat-stream` will give you back the same data type as the type of the first buffer written to the stream. Use `opts.encoding` to set what format `data` should be returned as, e.g. if you if you don't want to rely on the built-in type checking or for some other reason.\n\n* `string` - get a string\n* `buffer` - get back a Buffer\n* `array` - get an array of byte integers\n* `uint8array`, `u8`, `uint8` - get back a Uint8Array\n* `object`, get back an array of Objects\n\nIf you don't specify an encoding, and the types can't be inferred (e.g. you write things that aren't in the list above), it will try to convert concat them into a `Buffer`.\n\nIf nothing is written to `writable` then `data` will be an empty array `[]`.\n\n# error handling\n\n`concat-stream` does not handle errors for you, so you must handle errors on whatever streams you pipe into `concat-stream`. This is a general rule when programming with node.js streams: always handle errors on each and every stream. Since `concat-stream` is not itself a stream it does not emit errors.\n\nWe recommend using [`end-of-stream`](https://npmjs.org/end-of-stream) or [`pump`](https://npmjs.org/pump) for writing error tolerant stream code.\n\n# license\n\nMIT LICENSE\n", "licenseText": "The MIT License\n\nCopyright (c) 2013 Max Ogden\n\nPermission is hereby granted, free of charge, \nto any person obtaining a copy of this software and \nassociated documentation files (the \"Software\"), to \ndeal in the Software without restriction, including \nwithout limitation the rights to use, copy, modify, \nmerge, publish, distribute, sublicense, and/or sell \ncopies of the Software, and to permit persons to whom \nthe Software is furnished to do so, \nsubject to the following conditions:\n\nThe above copyright notice and this permission notice \nshall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, \nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES \nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. \nIN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR \nANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, \nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE \nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/concat-stream/-/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34", "type": "tarball", "reference": "https://registry.yarnpkg.com/concat-stream/-/concat-stream-1.6.2.tgz", "hash": "904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34", "integrity": "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==", "registry": "npm", "packageName": "concat-stream", "cacheIntegrity": "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw== sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ="}, "registry": "npm", "hash": "904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"}