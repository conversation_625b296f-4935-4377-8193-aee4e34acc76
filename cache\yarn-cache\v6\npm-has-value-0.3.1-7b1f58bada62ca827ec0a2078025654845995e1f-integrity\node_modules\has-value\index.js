/*!
 * has-value <https://github.com/jonschlinkert/has-value>
 *
 * Copyright (c) 2014-2016, <PERSON>.
 * Licensed under the MIT License.
 */

'use strict';

var isObject = require('isobject');
var hasValues = require('has-values');
var get = require('get-value');

module.exports = function(obj, prop, noZero) {
  if (isObject(obj)) {
    return hasValues(get(obj, prop), noZero);
  }
  return hasValues(obj, prop);
};
