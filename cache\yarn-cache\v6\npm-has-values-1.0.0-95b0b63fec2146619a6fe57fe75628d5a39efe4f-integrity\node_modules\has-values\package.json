{"name": "has-values", "description": "Returns true if any values exist, false if empty. Works for booleans, functions, numbers, strings, nulls, objects and arrays. ", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/has-values", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/has-values", "bugs": {"url": "https://github.com/jonschlinkert/has-values/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "devDependencies": {"gulp-format-md": "^0.1.12", "mocha": "^3.4.1"}, "keywords": ["array", "boolean", "empty", "find", "function", "has", "hasOwn", "javascript", "js", "key", "keys", "node.js", "null", "number", "object", "properties", "property", "string", "type", "util", "utilities", "utility", "value", "values"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["has-value", "kind-of", "is-number", "is-plain-object", "isobject"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}}