{"name": "web", "homepage": "web/build", "private": true, "type": "module", "version": "0.1.0", "scripts": {"start": "vite", "start:game": "vite build --watch", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-brands-svg-icons": "^6.5.2", "@fortawesome/free-regular-svg-icons": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.2", "classnames": "^2.5.1", "lucide-react": "^0.400.0", "random-words": "^2.0.1", "react": "^18.2.0", "react-dom": "^18.3.1"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.19", "eslint": "^8.54.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.38", "react-router-dom": "^6.24.0", "tailwindcss": "^3.4.4", "typescript": "^5.2.2", "vite": "^5.0.0"}}