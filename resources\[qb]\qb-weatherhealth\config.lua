Config = {}

-- <PERSON><PERSON><PERSON><PERSON><PERSON> nastavení
Config.Debug = false
Config.UpdateInterval = 300000 -- 5 minut v milisekundách
Config.PlayerHealthCheckInterval = 60000 -- 1 minuta pro hráče
Config.NPCHealthCheckInterval = 120000 -- 2 minuty pro NPC
Config.MaxNPCDistance = 200.0 -- Maximální vzdálenost pro sledování NPC

-- Weather Temperature Index (teplotní indexy)
Config.WeatherTemperature = {
    ['EXTRASUNNY'] = 30,    -- <PERSON><PERSON><PERSON> horko
    ['CLEAR'] = 25,         -- Te<PERSON>lo
    ['NEUTRAL'] = 20,       -- Neu<PERSON><PERSON><PERSON>í
    ['SMOG'] = 18,          -- Smog
    ['FOGGY'] = 15,         -- <PERSON>lha
    ['OVERCAST'] = 12,      -- <PERSON><PERSON><PERSON><PERSON>
    ['CLOUDS'] = 15,        -- <PERSON><PERSON><PERSON><PERSON>
    ['CLEARING'] = 18,      -- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se
    ['RAIN'] = 8,           -- D<PERSON><PERSON><PERSON>
    ['THUNDER'] = 5,        -- Bou<PERSON>e
    ['SNOW'] = -5,          -- Sníh
    ['BLIZZARD'] = -15,     -- <PERSON><PERSON><PERSON>
    ['SNOWLIGHT'] = -2,     -- Lehký sníh
    ['XMAS'] = -5,          -- Vánoce
    ['HALLOWEEN'] = 10,     -- Halloween
}

-- Clothing Warmth Values (hodnoty tepla oblečení)
Config.ClothingWarmth = {
    -- Horní část těla (component 11)
    torso = {
        [0] = 0,   -- Nahé tělo
        [1] = 2,   -- Tričko
        [2] = 4,   -- Košile
        [3] = 6,   -- Svetr
        [4] = 8,   -- Bunda
        [5] = 10,  -- Zimní bunda
        [6] = 12,  -- Kabát
        [7] = 15,  -- Zimní kabát

        -- Nové zimní oblečení
        [24] = 7,  -- Hoodie
        [25] = 7,  -- Hoodie (female)
        [31] = 9,  -- Jacket
        [33] = 9,  -- Jacket (female)
        [42] = 8,  -- Sweater
        [44] = 8,  -- Sweater (female)
        [61] = 6,  -- Light jacket
        [63] = 6,  -- Light jacket (female)
        [84] = 8,  -- Windbreaker
        [86] = 8,  -- Windbreaker (female)

        -- Těžké zimní oblečení
        [91] = 18,  -- Heavy winter coat
        [93] = 18,  -- Heavy winter coat (female)
        [97] = 16,  -- Puffer jacket
        [99] = 16,  -- Puffer jacket (female)
        [104] = 20, -- Ski jacket
        [106] = 20, -- Ski jacket (female)
        [110] = 17, -- Heavy coat
        [112] = 17, -- Heavy coat (female)
        [115] = 22, -- Winter parka
        [117] = 22, -- Winter parka (female)
        [120] = 19, -- Insulated jacket
        [122] = 19, -- Insulated jacket (female)
    },
    -- Nohy (component 4)
    legs = {
        [0] = 0,   -- Nahé nohy
        [1] = 1,   -- Šortky
        [2] = 3,   -- Kalhoty
        [3] = 5,   -- Teplé kalhoty
        [4] = 7,   -- Zimní kalhoty
        [6] = 4,   -- Jeans
        [7] = 5,   -- Warm pants (female)
        [8] = 8,   -- Thermal pants (female)
        [9] = 4,   -- Jeans
        [10] = 6,  -- Warm pants
        [12] = 10, -- Thermal/winter pants
    },
    -- Boty (component 6)
    shoes = {
        [0] = 0,   -- Bosé nohy
        [1] = 1,   -- Sandály
        [2] = 2,   -- Tenisky
        [3] = 4,   -- Boty
        [4] = 6,   -- Zimní boty
        [7] = 3,   -- Sneakers
        [8] = 3,   -- Sneakers (female)
        [10] = 5,  -- Boots
        [11] = 5,  -- Boots (female)
        [12] = 7,  -- Winter shoes
        [13] = 8,  -- Winter boots
        [14] = 8,  -- Winter boots (female)
    },
    -- Čepice/klobouky (prop 0)
    hat = {
        [0] = 0,   -- Bez čepice
        [1] = 2,   -- Kšiltovka
        [2] = 4,   -- Čepice
        [3] = 6,   -- Zimní čepice
        [8] = 5,   -- Beanie
        [9] = 5,   -- Beanie (female)
        [18] = 3,  -- Cap
        [19] = 3,  -- Cap (female)
        [21] = 8,  -- Winter hat
        [22] = 8,  -- Winter hat (female)
    }
}

-- NPC Outfit Configurations podle počasí
Config.NPCOutfits = {
    ['hot'] = { -- Horké počasí (25°C+)
        male = {
            {torso = 1, legs = 1, shoes = 1, hat = 0}, -- Tričko, šortky, tenisky
            {torso = 2, legs = 1, shoes = 2, hat = 1}, -- Košile, šortky, tenisky, kšiltovka
        },
        female = {
            {torso = 1, legs = 1, shoes = 1, hat = 0}, -- Tričko, šortky, sandály
            {torso = 2, legs = 1, shoes = 2, hat = 1}, -- Košile, šortky, tenisky, kšiltovka
        }
    },
    ['warm'] = { -- Teplé počasí (15-25°C)
        male = {
            {torso = 2, legs = 2, shoes = 2, hat = 0}, -- Košile, kalhoty, tenisky
            {torso = 3, legs = 2, shoes = 3, hat = 0}, -- Svetr, kalhoty, boty
        },
        female = {
            {torso = 2, legs = 2, shoes = 2, hat = 0}, -- Košile, kalhoty, tenisky
            {torso = 3, legs = 2, shoes = 3, hat = 0}, -- Svetr, kalhoty, boty
        }
    },
    ['cold'] = { -- Chladné počasí (5-15°C)
        male = {
            {torso = 24, legs = 9, shoes = 7, hat = 8}, -- Hoodie, jeans, sneakers, beanie
            {torso = 31, legs = 10, shoes = 10, hat = 18}, -- Jacket, warm pants, boots, cap
            {torso = 42, legs = 4, shoes = 12, hat = 8}, -- Sweater, pants, winter shoes, beanie
            {torso = 61, legs = 9, shoes = 7, hat = 0}, -- Light jacket, jeans, sneakers
            {torso = 84, legs = 10, shoes = 10, hat = 8}, -- Windbreaker, warm pants, boots, beanie
        },
        female = {
            {torso = 25, legs = 6, shoes = 8, hat = 9}, -- Hoodie, jeans, sneakers, beanie
            {torso = 33, legs = 7, shoes = 11, hat = 19}, -- Jacket, warm pants, boots, cap
            {torso = 44, legs = 4, shoes = 13, hat = 9}, -- Sweater, pants, winter shoes, beanie
            {torso = 63, legs = 6, shoes = 8, hat = 0}, -- Light jacket, jeans, sneakers
            {torso = 86, legs = 7, shoes = 11, hat = 9}, -- Windbreaker, warm pants, boots, beanie
        }
    },
    ['freezing'] = { -- Mrazivé počasí (-15 až 5°C) - SNOW/XMAS/BLIZZARD
        male = {
            {torso = 91, legs = 12, shoes = 13, hat = 21}, -- Heavy winter coat, thermal pants, winter boots, winter hat
            {torso = 97, legs = 12, shoes = 13, hat = 21}, -- Puffer jacket, thermal pants, winter boots, winter hat
            {torso = 104, legs = 12, shoes = 13, hat = 21}, -- Ski jacket, thermal pants, winter boots, winter hat
            {torso = 110, legs = 12, shoes = 13, hat = 21}, -- Heavy coat, thermal pants, winter boots, winter hat
            {torso = 115, legs = 12, shoes = 13, hat = 21}, -- Winter parka, thermal pants, winter boots, winter hat
            {torso = 120, legs = 12, shoes = 13, hat = 21}, -- Insulated jacket, thermal pants, winter boots, winter hat
        },
        female = {
            {torso = 93, legs = 8, shoes = 14, hat = 22}, -- Heavy winter coat, thermal pants, winter boots, winter hat
            {torso = 99, legs = 8, shoes = 14, hat = 22}, -- Puffer jacket, thermal pants, winter boots, winter hat
            {torso = 106, legs = 8, shoes = 14, hat = 22}, -- Ski jacket, thermal pants, winter boots, winter hat
            {torso = 112, legs = 8, shoes = 14, hat = 22}, -- Heavy coat, thermal pants, winter boots, winter hat
            {torso = 117, legs = 8, shoes = 14, hat = 22}, -- Winter parka, thermal pants, winter boots, winter hat
            {torso = 122, legs = 8, shoes = 14, hat = 22}, -- Insulated jacket, thermal pants, winter boots, winter hat
        }
    }
}

-- Health Effects Configuration
Config.HealthEffects = {
    -- Teplotní tolerance (rozdíl mezi teplotou a oblečením)
    temperatureTolerance = {
        perfect = {min = -5, max = 5},      -- Perfektní teplota
        comfortable = {min = -10, max = 10}, -- Pohodlná teplota
        uncomfortable = {min = -15, max = 15}, -- Nepohodlná teplota
        dangerous = {min = -25, max = 25},   -- Nebezpečná teplota
    },
    
    -- Rychlost změny zdraví (za minutu)
    healthChangeRate = {
        perfect = 0,        -- Žádná změna
        comfortable = 0,    -- Žádná změna
        uncomfortable = -1, -- -1 zdraví za minutu
        dangerous = -3,     -- -3 zdraví za minutu
        critical = -5,      -- -5 zdraví za minutu
    },
    
    -- Nemoci a jejich efekty
    diseases = {
        cold = {
            name = "Nachlazení",
            symptoms = {"cough", "sneeze"},
            healthLoss = 2,
            duration = 600000, -- 10 minut
            effects = {
                walkSpeed = 0.8,    -- 80% rychlosti chůze
                runSpeed = 0.7,     -- 70% rychlosti běhu
            }
        },
        flu = {
            name = "Chřipka",
            symptoms = {"cough", "fever", "weakness"},
            healthLoss = 5,
            duration = 1200000, -- 20 minut
            effects = {
                walkSpeed = 0.6,    -- 60% rychlosti chůze
                runSpeed = 0.5,     -- 50% rychlosti běhu
                blurredVision = true,
            }
        },
        heatstroke = {
            name = "Úpal",
            symptoms = {"weakness", "dizziness"},
            healthLoss = 4,
            duration = 900000, -- 15 minut
            effects = {
                walkSpeed = 0.7,    -- 70% rychlosti chůze
                runSpeed = 0.6,     -- 60% rychlosti běhu
                thirstIncrease = 2, -- Zvýšená žízeň
            }
        }
    }
}

-- NPC Behavior Configuration
Config.NPCBehavior = {
    -- Chování podle času
    timeBasedBehavior = {
        day = {start = 6, stop = 18, activity = 1.0},      -- Den - plná aktivita
        evening = {start = 18, stop = 22, activity = 0.7}, -- Večer - snížená aktivita
        night = {start = 22, stop = 6, activity = 0.3},    -- Noc - minimální aktivita
    },
    
    -- Chování podle počasí
    weatherBehavior = {
        ['RAIN'] = {
            seekShelter = true,
            activityReduction = 0.5,
            shelterTypes = {"building", "canopy", "overhang"}
        },
        ['THUNDER'] = {
            seekShelter = true,
            activityReduction = 0.3,
            shelterTypes = {"building"}
        },
        ['SNOW'] = {
            seekWarmth = true,
            activityReduction = 0.6,
            warmthSources = {"fire", "heater", "building"}
        },
        ['BLIZZARD'] = {
            seekWarmth = true,
            activityReduction = 0.2,
            warmthSources = {"building"}
        }
    }
}

-- Animation Configuration
Config.Animations = {
    -- Animace pro nemoci
    symptoms = {
        cough = {
            dict = "timetable@gardener@smoking_joint",
            anim = "idle_cough",
            duration = 3000,
            chance = 15 -- 15% šance každou minutu
        },
        sneeze = {
            dict = "anim@mp_player_intcelebrationfemale@sneeze",
            anim = "sneeze",
            duration = 2000,
            chance = 10 -- 10% šance každou minutu
        },
        shiver = {
            dict = "amb@world_human_hang_out_street@male_c@idle_a",
            anim = "idle_b",
            duration = 4000,
            chance = 30 -- 30% šance každou minutu
        },
        wipe_sweat = {
            dict = "anim@mp_player_intcelebrationfemale@wipe_sweat",
            anim = "wipe_sweat",
            duration = 3000,
            chance = 25 -- 25% šance každou minutu
        }
    },
    
    -- Animace pro počasí
    weather = {
        cold_idle = {
            dict = "amb@world_human_hang_out_street@male_c@idle_a",
            anim = "idle_b",
            loop = true
        },
        hot_idle = {
            dict = "amb@world_human_hang_out_street@female_arms_crossed@idle_a",
            anim = "idle_a",
            loop = true
        }
    },

    -- NPC animace podle počasí
    npc_weather = {
        cold = {
            {dict = "amb@world_human_drinking@coffee@male@idle_a", anim = "idle_c", duration = 15000, chance = 40}, -- Pití kávy
            {dict = "amb@world_human_smoking@male@male_a@enter", anim = "enter", duration = 20000, chance = 25}, -- Kouření
            {dict = "amb@world_human_stand_fire@male@idle_a", anim = "idle_a", duration = 30000, chance = 30}, -- Ohřívání u ohně
            {dict = "amb@world_human_hang_out_street@male_c@idle_a", anim = "idle_b", duration = 10000, chance = 35}, -- Třesení
        },
        hot = {
            {dict = "amb@world_human_drinking@coffee@male@idle_a", anim = "idle_c", duration = 10000, chance = 30}, -- Pití studeného nápoje
            {dict = "anim@mp_player_intcelebrationfemale@wipe_sweat", anim = "wipe_sweat", duration = 3000, chance = 40}, -- Otírání potu
            {dict = "amb@world_human_hang_out_street@female_arms_crossed@idle_a", anim = "idle_a", duration = 8000, chance = 25}, -- Stání ve stínu
        },
        rain = {
            {dict = "amb@world_human_stand_impatient@male@no_var@base", anim = "base", duration = 5000, chance = 50}, -- Netrpělivost
            {dict = "amb@world_human_hang_out_street@male_c@idle_a", anim = "idle_b", duration = 8000, chance = 30}, -- Schovávání
        },
        normal = {
            {dict = "amb@world_human_drinking@coffee@male@idle_a", anim = "idle_c", duration = 12000, chance = 20}, -- Občasná káva
            {dict = "amb@world_human_smoking@male@male_a@enter", anim = "enter", duration = 15000, chance = 15}, -- Občasné kouření
            {dict = "amb@world_human_hang_out_street@male_c@idle_a", anim = "idle_a", duration = 10000, chance = 10}, -- Normální stání
        }
    }
}

-- Notification Messages
Config.Messages = {
    player = {
        cold_warning = "Začínáš mít zima. Měl bys se obléct tepleji.",
        hot_warning = "Je ti horko. Měl bys se obléct lehčeji.",
        getting_sick = "Necítíš se dobře. Možná bys měl navštívit lékaře.",
        disease_contracted = "Onemocněl jsi: %s",
        disease_cured = "Cítíš se lépe. %s byla vyléčena.",
        weather_change = "Počasí se změnilo na %s. Zkontroluj své oblečení."
    },
    npc = {
        sick_npc = "NPC vypadá nemocně.",
        dead_npc = "NPC zemřel na následky nemoci."
    }
}

-- Items for treatment
Config.TreatmentItems = {
    medicine = {
        item = "medicine",
        label = "Lék",
        cureChance = 80, -- 80% šance na vyléčení
        diseases = {"cold", "flu"}
    },
    heatpack = {
        item = "heatpack",
        label = "Ohřívací sáček",
        warmthBonus = 10,
        duration = 300000 -- 5 minut
    },
    coolpack = {
        item = "coolpack",
        label = "Chladící sáček",
        coolBonus = 10,
        duration = 300000 -- 5 minut
    }
}
