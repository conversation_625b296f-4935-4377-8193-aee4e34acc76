{"manifest": {"name": "path-dirname", "version": "1.0.2", "description": "Node.js path.dirname() ponyfill", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/es128/path-dirname.git"}, "author": {"name": "<PERSON><PERSON>"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["dirname", "dir", "path", "paths", "file", "built-in", "util", "utils", "core", "stdlib", "ponyfill", "polyfill", "shim"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-path-dirname-1.0.2-cc33d24d525e099a5388c0336c6e32b9160609e0-integrity\\node_modules\\path-dirname\\package.json", "readmeFilename": "readme.md", "readme": "# path-dirname [![Build Status](https://travis-ci.org/es128/path-dirname.svg?branch=master)](https://travis-ci.org/es128/path-dirname)\n\n> Node.js [`path.dirname()`](https://nodejs.org/api/path.html#path_path_dirname_path) [ponyfill](https://ponyfill.com)\n\nThis was needed in order to expose `path.posix.dirname()` on Node.js v0.10\n\n## Install\n\n```\n$ npm install --save path-dirname\n```\n\n\n## Usage\n\n```js\nconst pathDirname = require('path-dirname');\n\npathDirname('/home/<USER>');\n//=> '/home'\npathDirname('C:\\\\Users\\\\<USER>\\\\Users'\npathDirname('foo');\n//=> '.'\npathDirname('foo/bar');\n//=> 'foo'\n\n//Using posix version for consistent output when dealing with glob escape chars\npathDirname.win32('C:\\\\Users\\\\<USER>\\\\*bar');\n//=> 'C:\\\\Users\\\\<USER>\\\\Users\\\\foo/\\\\*bar');\n//=> 'C:\\\\Users\\\\<USER>", "licenseText": "\nThe MIT License (MIT)\n\nCopyright (c) <PERSON><PERSON> and Node.js contributors. All rights reserved.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/path-dirname/-/path-dirname-1.0.2.tgz#cc33d24d525e099a5388c0336c6e32b9160609e0", "type": "tarball", "reference": "https://registry.yarnpkg.com/path-dirname/-/path-dirname-1.0.2.tgz", "hash": "cc33d24d525e099a5388c0336c6e32b9160609e0", "integrity": "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=", "registry": "npm", "packageName": "path-dirname", "cacheIntegrity": "sha512-ALzNPpyNq9AqXMBjeymIjFDAkAFH06mHJH/cSBHAgU0s4vfpBn6b2nf8tiRLvagKD8RbTpq2FKTBg7cl9l3c7Q== sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA="}, "registry": "npm", "hash": "cc33d24d525e099a5388c0336c6e32b9160609e0"}