{"manifest": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "scripts": {"test": "mocha test.js", "browser": "zuul --no-coverage --ui mocha-bdd --local 8080 -- test.js"}, "version": "0.11.10", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "license": "MIT", "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "devDependencies": {"mocha": "2.2.1", "zuul": "^3.10.3"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-process-0.11.10-7332300e840161bda3e69a1d1d91a7d4bc16f182-integrity\\node_modules\\process\\package.json", "readmeFilename": "README.md", "readme": "# process\n\n```require('process');``` just like any other module.\n\nWorks in node.js and browsers via the browser.js shim provided with the module.\n\n## browser implementation\n\nThe goal of this module is not to be a full-fledged alternative to the builtin process module. This module mostly exists to provide the nextTick functionality and little more. We keep this module lean because it will often be included by default by tools like browserify when it detects a module has used the `process` global.\n\nIt also exposes a \"browser\" member (i.e. `process.browser`) which is `true` in this implementation but `undefined` in node. This can be used in isomorphic code that adjusts it's behavior depending on which environment it's running in. \n\nIf you are looking to provide other process methods, I suggest you monkey patch them onto the process global in your app. A list of user created patches is below.\n\n* [hrtime](https://github.com/kumavis/browser-process-hrtime)\n* [stdout](https://github.com/kumavis/browser-stdout)\n\n## package manager notes\n\nIf you are writing a bundler to package modules for client side use, make sure you use the ```browser``` field hint in package.json.\n\nSee https://gist.github.com/4339901 for details.\n\nThe [browserify](https://github.com/substack/node-browserify) module will properly handle this field when bundling your files.\n\n\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2013 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/process/-/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182", "type": "tarball", "reference": "https://registry.yarnpkg.com/process/-/process-0.11.10.tgz", "hash": "7332300e840161bda3e69a1d1d91a7d4bc16f182", "integrity": "sha1-czIwDoQBYb2j5podHZGn1LwW8YI=", "registry": "npm", "packageName": "process", "cacheIntegrity": "sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A== sha1-czIwDoQBYb2j5podHZGn1LwW8YI="}, "registry": "npm", "hash": "7332300e840161bda3e69a1d1d91a7d4bc16f182"}