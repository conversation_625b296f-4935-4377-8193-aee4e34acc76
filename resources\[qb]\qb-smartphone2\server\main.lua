local QBCore = exports['qb-core']:GetCoreObject()

-- Globální proměnné
local PhoneData = {}
local ActiveCalls = {}
local BluetoothConnections = {}

-- Inicializace při startu serveru
CreateThread(function()
    -- Registrace telefonu jako item
    QBCore.Functions.CreateUseableItem(Config.PhoneItem, function(source, item)
        local Player = QBCore.Functions.GetPlayer(source)
        if not Player then return end
        
        TriggerClientEvent('qb-smartphone2:client:openPhone', source)
    end)
    
    -- <PERSON><PERSON><PERSON> <PERSON><PERSON>
    if Config.BatteryEnabled then
        QBCore.Functions.CreateUseableItem(Config.ChargerItem, function(source, item)
            local Player = QBCore.Functions.GetPlayer(source)
            if not Player then return end
            
            TriggerClientEvent('qb-smartphone2:client:chargePhone', source)
        end)
    end
    
    print('^2[QB-Smartphone2]^7 Server started successfully!')
end)

-- Event handlery
RegisterNetEvent('QBCore:Server:PlayerLoaded', function(Player)
    local src = Player.PlayerData.source
    local citizenid = Player.PlayerData.citizenid
    local phoneNumber = Player.PlayerData.charinfo.phone
    
    -- Inicializace dat telefonu
    InitializePhoneData(src, citizenid, phoneNumber)
    
    -- Synchronizace telefonního čísla
    TriggerClientEvent('qb-smartphone2:client:setPhoneNumber', src, phoneNumber)
end)

RegisterNetEvent('QBCore:Server:OnPlayerUnload', function(src)
    if PhoneData[src] then
        PhoneData[src] = nil
    end
end)

-- Funkce pro inicializaci dat telefonu
function InitializePhoneData(src, citizenid, phoneNumber)
    PhoneData[src] = {
        citizenid = citizenid,
        phoneNumber = phoneNumber,
        isOpen = false,
        batteryLevel = 100,
        settings = {}
    }
    
    -- Načtení nastavení z databáze
    MySQL.Async.fetchAll('SELECT * FROM phone_settings WHERE citizenid = ?', {citizenid}, function(result)
        if result[1] then
            PhoneData[src].settings = result[1]
        else
            -- Vytvoření výchozího nastavení
            MySQL.Async.execute('INSERT INTO phone_settings (citizenid) VALUES (?)', {citizenid})
            PhoneData[src].settings = {
                wallpaper = 'default.jpg',
                theme = 'light',
                ringtone = 'default',
                notification_sound = 1,
                vibration = 1,
                battery_level = 100,
                pin_code = nil,
                fingerprint_enabled = 0,
                bluetooth_enabled = 1,
                widgets = nil,
                app_layout = nil
            }
        end
        
        -- Odeslání dat klientovi
        TriggerClientEvent('qb-smartphone2:client:receivePhoneData', src, PhoneData[src])
    end)
end

-- Callback pro získání dat telefonu
QBCore.Functions.CreateCallback('qb-smartphone2:server:getPhoneData', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb(false) return end
    
    local citizenid = Player.PlayerData.citizenid
    
    MySQL.Async.fetchAll('SELECT * FROM phone_settings WHERE citizenid = ?', {citizenid}, function(result)
        if result[1] then
            cb(result[1])
        else
            cb(false)
        end
    end)
end)

-- Callback pro kontrolu vlastnictví telefonu
QBCore.Functions.CreateCallback('qb-smartphone2:server:hasPhone', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb(false) return end
    
    local hasPhone = Player.Functions.GetItemByName(Config.PhoneItem)
    cb(hasPhone ~= nil)
end)

-- Event pro otevření telefonu
RegisterNetEvent('qb-smartphone2:server:openPhone', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local hasPhone = Player.Functions.GetItemByName(Config.PhoneItem)
    if not hasPhone then
        TriggerClientEvent('QBCore:Notify', src, 'Nemáš telefon!', 'error')
        return
    end
    
    if PhoneData[src] then
        PhoneData[src].isOpen = true
        TriggerClientEvent('qb-smartphone2:client:openPhone', src)
    end
end)

-- Event pro zavření telefonu
RegisterNetEvent('qb-smartphone2:server:closePhone', function()
    local src = source
    if PhoneData[src] then
        PhoneData[src].isOpen = false
    end
end)

-- Event pro uložení nastavení
RegisterNetEvent('qb-smartphone2:server:saveSettings', function(settings)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    MySQL.Async.execute([[
        UPDATE phone_settings SET 
        wallpaper = ?, theme = ?, ringtone = ?, notification_sound = ?, 
        vibration = ?, pin_code = ?, fingerprint_enabled = ?, 
        bluetooth_enabled = ?, widgets = ?, app_layout = ?
        WHERE citizenid = ?
    ]], {
        settings.wallpaper, settings.theme, settings.ringtone, 
        settings.notification_sound, settings.vibration, settings.pin_code,
        settings.fingerprint_enabled, settings.bluetooth_enabled,
        json.encode(settings.widgets), json.encode(settings.app_layout),
        citizenid
    })
    
    if PhoneData[src] then
        PhoneData[src].settings = settings
    end
end)

-- Event pro aktualizaci baterie
RegisterNetEvent('qb-smartphone2:server:updateBattery', function(batteryLevel)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    
    MySQL.Async.execute('UPDATE phone_settings SET battery_level = ? WHERE citizenid = ?', {
        batteryLevel, citizenid
    })
    
    if PhoneData[src] then
        PhoneData[src].batteryLevel = batteryLevel
    end
    
    -- Pokud je baterie vybitá, zavři telefon
    if batteryLevel <= 0 then
        TriggerClientEvent('qb-smartphone2:client:phoneShutdown', src)
    end
end)

-- Funkce pro odeslání notifikace
function SendNotification(phoneNumber, title, message, app, data)
    local Player = QBCore.Functions.GetPlayerByPhone(phoneNumber)
    if not Player then return false end
    
    local src = Player.PlayerData.source
    
    TriggerClientEvent('qb-smartphone2:client:receiveNotification', src, {
        title = title,
        message = message,
        app = app,
        data = data or {},
        timestamp = os.time()
    })
    
    return true
end

-- Export funkce pro jiné scripty
exports('SendNotification', SendNotification)

-- Funkce pro získání online hráčů s telefonem
function GetOnlinePlayersWithPhone()
    local players = {}
    for src, data in pairs(PhoneData) do
        local Player = QBCore.Functions.GetPlayer(src)
        if Player then
            table.insert(players, {
                source = src,
                citizenid = data.citizenid,
                phoneNumber = data.phoneNumber,
                name = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname
            })
        end
    end
    return players
end

exports('GetOnlinePlayersWithPhone', GetOnlinePlayersWithPhone)

-- Funkce pro kontrolu, zda má hráč telefon
function HasPhone(source)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return false end
    
    local hasPhone = Player.Functions.GetItemByName(Config.PhoneItem)
    return hasPhone ~= nil
end

exports('HasPhone', HasPhone)

-- Funkce pro získání telefonního čísla hráče
function GetPlayerPhoneNumber(source)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return nil end
    
    return Player.PlayerData.charinfo.phone
end

exports('GetPlayerPhoneNumber', GetPlayerPhoneNumber)

-- Bluetooth systém
RegisterNetEvent('qb-smartphone2:server:bluetoothConnect', function(targetSource)
    local src = source
    
    if not BluetoothConnections[src] then
        BluetoothConnections[src] = {}
    end
    
    if not BluetoothConnections[targetSource] then
        BluetoothConnections[targetSource] = {}
    end
    
    table.insert(BluetoothConnections[src], targetSource)
    table.insert(BluetoothConnections[targetSource], src)
    
    TriggerClientEvent('qb-smartphone2:client:bluetoothConnected', src, targetSource)
    TriggerClientEvent('qb-smartphone2:client:bluetoothConnected', targetSource, src)
end)

RegisterNetEvent('qb-smartphone2:server:bluetoothDisconnect', function(targetSource)
    local src = source
    
    if BluetoothConnections[src] then
        for i, connection in ipairs(BluetoothConnections[src]) do
            if connection == targetSource then
                table.remove(BluetoothConnections[src], i)
                break
            end
        end
    end
    
    if BluetoothConnections[targetSource] then
        for i, connection in ipairs(BluetoothConnections[targetSource]) do
            if connection == src then
                table.remove(BluetoothConnections[targetSource], i)
                break
            end
        end
    end
    
    TriggerClientEvent('qb-smartphone2:client:bluetoothDisconnected', src, targetSource)
    TriggerClientEvent('qb-smartphone2:client:bluetoothDisconnected', targetSource, src)
end)

-- Cleanup při odpojení hráče
AddEventHandler('playerDropped', function()
    local src = source
    
    if BluetoothConnections[src] then
        for _, connection in ipairs(BluetoothConnections[src]) do
            TriggerClientEvent('qb-smartphone2:client:bluetoothDisconnected', connection, src)
            
            if BluetoothConnections[connection] then
                for i, conn in ipairs(BluetoothConnections[connection]) do
                    if conn == src then
                        table.remove(BluetoothConnections[connection], i)
                        break
                    end
                end
            end
        end
        BluetoothConnections[src] = nil
    end
    
    if PhoneData[src] then
        PhoneData[src] = nil
    end
end)
