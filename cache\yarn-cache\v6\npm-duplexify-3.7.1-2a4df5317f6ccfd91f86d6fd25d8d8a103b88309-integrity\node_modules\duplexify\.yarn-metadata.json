{"manifest": {"name": "duplexify", "version": "3.7.1", "description": "Turn a writable and readable stream into a streams2 duplex stream with support for async initialization and streams1/streams2 input", "main": "index.js", "dependencies": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}, "devDependencies": {"concat-stream": "^1.5.2", "tape": "^4.0.0", "through2": "^2.0.0"}, "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "git://github.com/mafintosh/duplexify"}, "keywords": ["duplex", "streams2", "streams", "stream", "writable", "readable", "async"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/duplexify/issues"}, "homepage": "https://github.com/mafintosh/duplexify", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-duplexify-3.7.1-2a4df5317f6ccfd91f86d6fd25d8d8a103b88309-integrity\\node_modules\\duplexify\\package.json", "readmeFilename": "README.md", "readme": "# duplexify\n\nTurn a writeable and readable stream into a single streams2 duplex stream.\n\nSimilar to [duplexer2](https://github.com/deoxxa/duplexer2) except it supports both streams2 and streams1 as input\nand it allows you to set the readable and writable part asynchronously using `setReadable(stream)` and `setWritable(stream)`\n\n```\nnpm install duplexify\n```\n\n[![build status](http://img.shields.io/travis/mafintosh/duplexify.svg?style=flat)](http://travis-ci.org/mafintosh/duplexify)\n\n## Usage\n\nUse `duplexify(writable, readable, streamOptions)` (or `duplexify.obj(writable, readable)` to create an object stream)\n\n``` js\nvar duplexify = require('duplexify')\n\n// turn writableStream and readableStream into a single duplex stream\nvar dup = duplexify(writableStream, readableStream)\n\ndup.write('hello world') // will write to writableStream\ndup.on('data', function(data) {\n  // will read from readableStream\n})\n```\n\nYou can also set the readable and writable parts asynchronously\n\n``` js\nvar dup = duplexify()\n\ndup.write('hello world') // write will buffer until the writable\n                         // part has been set\n\n// wait a bit ...\ndup.setReadable(readableStream)\n\n// maybe wait some more?\ndup.setWritable(writableStream)\n```\n\nIf you call `setReadable` or `setWritable` multiple times it will unregister the previous readable/writable stream.\nTo disable the readable or writable part call `setReadable` or `setWritable` with `null`.\n\nIf the readable or writable streams emits an error or close it will destroy both streams and bubble up the event.\nYou can also explicitly destroy the streams by calling `dup.destroy()`. The `destroy` method optionally takes an\nerror object as argument, in which case the error is emitted as part of the `error` event.\n\n``` js\ndup.on('error', function(err) {\n  console.log('readable or writable emitted an error - close will follow')\n})\n\ndup.on('close', function() {\n  console.log('the duplex stream is destroyed')\n})\n\ndup.destroy() // calls destroy on the readable and writable part (if present)\n```\n\n## HTTP request example\n\nTurn a node core http request into a duplex stream is as easy as\n\n``` js\nvar duplexify = require('duplexify')\nvar http = require('http')\n\nvar request = function(opts) {\n  var req = http.request(opts)\n  var dup = duplexify(req)\n  req.on('response', function(res) {\n    dup.setReadable(res)\n  })\n  return dup\n}\n\nvar req = request({\n  method: 'GET',\n  host: 'www.google.com',\n  port: 80\n})\n\nreq.end()\nreq.pipe(process.stdout)\n```\n\n## License\n\nMIT\n\n## Related\n\n`duplexify` is part of the [mississippi stream utility collection](https://github.com/maxogden/mississippi) which includes more useful stream modules similar to this one.\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 Mathias Buus\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/duplexify/-/duplexify-3.7.1.tgz#2a4df5317f6ccfd91f86d6fd25d8d8a103b88309", "type": "tarball", "reference": "https://registry.yarnpkg.com/duplexify/-/duplexify-3.7.1.tgz", "hash": "2a4df5317f6ccfd91f86d6fd25d8d8a103b88309", "integrity": "sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g==", "registry": "npm", "packageName": "duplexify", "cacheIntegrity": "sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g== sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk="}, "registry": "npm", "hash": "2a4df5317f6ccfd91f86d6fd25d8d8a103b88309"}