{"manifest": {"name": "bindings", "description": "Helper module for loading your native module's .node file", "keywords": ["native", "addon", "bindings", "gyp", "waf", "c", "c++"], "version": "1.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://tootallnate.net"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-bindings.git"}, "main": "./bindings.js", "bugs": {"url": "https://github.com/TooTallNate/node-bindings/issues"}, "homepage": "https://github.com/TooTallNate/node-bindings", "license": "MIT", "dependencies": {"file-uri-to-path": "1.0.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-bindings-1.5.0-10353c9e945334bc0511a6d90b38fbc7c9c504df-integrity\\node_modules\\bindings\\package.json", "readmeFilename": "README.md", "readme": "node-bindings\n=============\n### Helper module for loading your native module's `.node` file\n\nThis is a helper module for authors of Node.js native addon modules.\nIt is basically the \"swiss army knife\" of `require()`ing your native module's\n`.node` file.\n\nThroughout the course of Node's native addon history, addons have ended up being\ncompiled in a variety of different places, depending on which build tool and which\nversion of node was used. To make matters worse, now the `gyp` build tool can\nproduce either a __Release__ or __Debug__ build, each being built into different\nlocations.\n\nThis module checks _all_ the possible locations that a native addon would be built\nat, and returns the first one that loads successfully.\n\n\nInstallation\n------------\n\nInstall with `npm`:\n\n``` bash\n$ npm install --save bindings\n```\n\nOr add it to the `\"dependencies\"` section of your `package.json` file.\n\n\nExample\n-------\n\n`require()`ing the proper bindings file for the current node version, platform\nand architecture is as simple as:\n\n``` js\nvar bindings = require('bindings')('binding.node')\n\n// Use your bindings defined in your C files\nbindings.your_c_function()\n```\n\n\nNice Error Output\n-----------------\n\nWhen the `.node` file could not be loaded, `node-bindings` throws an Error with\na nice error message telling you exactly what was tried. You can also check the\n`err.tries` Array property.\n\n```\nError: Could not load the bindings file. Tried:\n → /Users/<USER>/ref/build/binding.node\n → /Users/<USER>/ref/build/Debug/binding.node\n → /Users/<USER>/ref/build/Release/binding.node\n → /Users/<USER>/ref/out/Debug/binding.node\n → /Users/<USER>/ref/Debug/binding.node\n → /Users/<USER>/ref/out/Release/binding.node\n → /Users/<USER>/ref/Release/binding.node\n → /Users/<USER>/ref/build/default/binding.node\n → /Users/<USER>/ref/compiled/0.8.2/darwin/x64/binding.node\n    at bindings (/Users/<USER>/ref/node_modules/bindings/bindings.js:84:13)\n    at Object.<anonymous> (/Users/<USER>/ref/lib/ref.js:5:47)\n    at Module._compile (module.js:449:26)\n    at Object.Module._extensions..js (module.js:467:10)\n    at Module.load (module.js:356:32)\n    at Function.Module._load (module.js:312:12)\n    ...\n```\n\nThe searching for the `.node` file will originate from the first directory in which has a `package.json` file is found.\n\nLicense\n-------\n\n(The MIT License)\n\nCopyright (c) 2012 Nathan Rajlich &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2012 <PERSON> &<PERSON>t;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/bindings/-/bindings-1.5.0.tgz#10353c9e945334bc0511a6d90b38fbc7c9c504df", "type": "tarball", "reference": "https://registry.yarnpkg.com/bindings/-/bindings-1.5.0.tgz", "hash": "10353c9e945334bc0511a6d90b38fbc7c9c504df", "integrity": "sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==", "registry": "npm", "packageName": "bindings", "cacheIntegrity": "sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ== sha1-EDU8npRTNLwFEabZCzj7x8nFBN8="}, "registry": "npm", "hash": "10353c9e945334bc0511a6d90b38fbc7c9c504df"}