{"manifest": {"name": "normalize-path", "description": "Normalize slashes in a file path to be posix/unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes, unless disabled.", "version": "3.0.0", "homepage": "https://github.com/jonschlinkert/normalize-path", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/Blaine<PERSON><PERSON>litz"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/normalize-path.git"}, "bugs": {"url": "https://github.com/jonschlinkert/normalize-path/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^1.0.0", "minimist": "^1.2.0", "mocha": "^3.5.3"}, "keywords": ["absolute", "backslash", "delimiter", "file", "file-path", "filepath", "fix", "forward", "fp", "fs", "normalize", "path", "relative", "separator", "slash", "slashes", "trailing", "unix", "u<PERSON>"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"description": "Other useful path-related libraries:", "list": ["contains-path", "is-absolute", "is-relative", "parse-filepath", "path-ends-with", "path-ends-with", "unixify"]}, "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-normalize-path-3.0.0-0dcd69ff23a1c9b11fd0978316644a0388216a65-integrity\\node_modules\\normalize-path\\package.json", "readmeFilename": "README.md", "readme": "# normalize-path [![NPM version](https://img.shields.io/npm/v/normalize-path.svg?style=flat)](https://www.npmjs.com/package/normalize-path) [![NPM monthly downloads](https://img.shields.io/npm/dm/normalize-path.svg?style=flat)](https://npmjs.org/package/normalize-path) [![NPM total downloads](https://img.shields.io/npm/dt/normalize-path.svg?style=flat)](https://npmjs.org/package/normalize-path) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/normalize-path.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/normalize-path)\n\n> Normalize slashes in a file path to be posix/unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes, unless disabled.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save normalize-path\n```\n\n## Usage\n\n```js\nconst normalize = require('normalize-path');\n\nconsole.log(normalize('\\\\foo\\\\bar\\\\baz\\\\')); \n//=> '/foo/bar/baz'\n```\n\n**win32 namespaces**\n\n```js\nconsole.log(normalize('\\\\\\\\?\\\\UNC\\\\Server01\\\\user\\\\docs\\\\Letter.txt')); \n//=> '//?/UNC/Server01/user/docs/Letter.txt'\n\nconsole.log(normalize('\\\\\\\\.\\\\CdRomX')); \n//=> '//./CdRomX'\n```\n\n**Consecutive slashes**\n\nCondenses multiple consecutive forward slashes (except for leading slashes in win32 namespaces) to a single slash.\n\n```js\nconsole.log(normalize('.//foo//bar///////baz/')); \n//=> './foo/bar/baz'\n```\n\n### Trailing slashes\n\nBy default trailing slashes are removed. Pass `false` as the last argument to disable this behavior and _**keep** trailing slashes_:\n\n```js\nconsole.log(normalize('foo\\\\bar\\\\baz\\\\', false)); //=> 'foo/bar/baz/'\nconsole.log(normalize('./foo/bar/baz/', false)); //=> './foo/bar/baz/'\n```\n\n## Release history\n\n### v3.0\n\nNo breaking changes in this release.\n\n* a check was added to ensure that [win32 namespaces](https://msdn.microsoft.com/library/windows/desktop/aa365247(v=vs.85).aspx#namespaces) are handled properly by win32 `path.parse()` after a path has been normalized by this library.\n* a minor optimization was made to simplify how the trailing separator was handled\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nOther useful path-related libraries:\n\n* [contains-path](https://www.npmjs.com/package/contains-path): Return true if a file path contains the given path. | [homepage](https://github.com/jonschlinkert/contains-path \"Return true if a file path contains the given path.\")\n* [is-absolute](https://www.npmjs.com/package/is-absolute): Returns true if a file path is absolute. Does not rely on the path module… [more](https://github.com/jonschlinkert/is-absolute) | [homepage](https://github.com/jonschlinkert/is-absolute \"Returns true if a file path is absolute. Does not rely on the path module and can be used as a polyfill for node.js native `path.isAbolute`.\")\n* [is-relative](https://www.npmjs.com/package/is-relative): Returns `true` if the path appears to be relative. | [homepage](https://github.com/jonschlinkert/is-relative \"Returns `true` if the path appears to be relative.\")\n* [parse-filepath](https://www.npmjs.com/package/parse-filepath): Pollyfill for node.js `path.parse`, parses a filepath into an object. | [homepage](https://github.com/jonschlinkert/parse-filepath \"Pollyfill for node.js `path.parse`, parses a filepath into an object.\")\n* [path-ends-with](https://www.npmjs.com/package/path-ends-with): Return `true` if a file path ends with the given string/suffix. | [homepage](https://github.com/jonschlinkert/path-ends-with \"Return `true` if a file path ends with the given string/suffix.\")\n* [unixify](https://www.npmjs.com/package/unixify): Convert Windows file paths to unix paths. | [homepage](https://github.com/jonschlinkert/unixify \"Convert Windows file paths to unix paths.\")\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 35 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 1 | [phated](https://github.com/phated) |\n\n### Author\n\n**Jon Schlinkert**\n\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on April 19, 2018._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2018, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/normalize-path/-/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65", "type": "tarball", "reference": "https://registry.yarnpkg.com/normalize-path/-/normalize-path-3.0.0.tgz", "hash": "0dcd69ff23a1c9b11fd0978316644a0388216a65", "integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==", "registry": "npm", "packageName": "normalize-path", "cacheIntegrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA== sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU="}, "registry": "npm", "hash": "0dcd69ff23a1c9b11fd0978316644a0388216a65"}