-- Main client script for Weather Health System
local QBCore = exports['qb-core']:GetCoreObject()

-- Global variables
local currentWeather = 'CLEAR'
local currentTime = 12
local isSystemActive = false
local playerData = {}

-- Weather sync variables
local lastWeatherCheck = 0
local weatherCheckInterval = 30000 -- 30 seconds

-- Initialize system
CreateThread(function()
    while not QBCore do
        Wait(100)
    end
    
    -- Wait for player to be loaded
    while not QBCore.Functions.GetPlayerData() do
        Wait(100)
    end
    
    playerData = QBCore.Functions.GetPlayerData()
    isSystemActive = true
    
    Utils.Debug("Weather Health System initialized")
    QBCore.Functions.Notify(Lang:t('info.system_started'), 'success')
    
    -- Start main loops
    StartWeatherMonitoring()
    StartPlayerHealthMonitoring()
    StartNPCManagement()
end)

-- Weather monitoring loop
function StartWeatherMonitoring()
    CreateThread(function()
        while isSystemActive do
            local gameTime = GetGameTimer()
            
            if gameTime - lastWeatherCheck > weatherCheckInterval then
                CheckWeatherChanges()
                lastWeatherCheck = gameTime
            end
            
            Wait(1000)
        end
    end)
end

-- Check for weather changes
function CheckWeatherChanges()
    local newWeather = GetCurrentWeatherType()
    local newTime = GetClockHours()
    
    if newWeather ~= currentWeather then
        local oldWeather = currentWeather
        currentWeather = newWeather
        
        Utils.Debug("Weather changed from %s to %s", oldWeather, newWeather)
        
        -- Notify player about weather change
        local weatherName = Lang:t('weather.' .. string.lower(newWeather))
        QBCore.Functions.Notify(Utils.FormatMessage(Config.Messages.player.weather_change, weatherName), 'primary')
        
        -- Trigger weather change events
        TriggerEvent('weatherhealth:client:weatherChanged', newWeather, oldWeather)
        TriggerServerEvent('weatherhealth:server:weatherChanged', newWeather, oldWeather)
    end
    
    if newTime ~= currentTime then
        currentTime = newTime
        TriggerEvent('weatherhealth:client:timeChanged', newTime)
    end
end

-- Get current weather type
function GetCurrentWeatherType()
    -- Try to get weather from qb-weathersync first
    if GetResourceState('qb-weathersync') == 'started' then
        -- Since qb-weathersync doesn't export getCurrentWeather, we'll use native
        local weatherHash = GetPrevWeatherTypeHashName()

        -- Convert hash to weather name
        local availableWeathers = {
            'EXTRASUNNY', 'CLEAR', 'NEUTRAL', 'SMOG', 'FOGGY', 'OVERCAST',
            'CLOUDS', 'CLEARING', 'RAIN', 'THUNDER', 'SNOW', 'BLIZZARD',
            'SNOWLIGHT', 'XMAS', 'HALLOWEEN'
        }

        for _, weatherType in ipairs(availableWeathers) do
            if GetHashKey(weatherType) == weatherHash then
                return weatherType
            end
        end
    end

    return 'CLEAR' -- Default fallback
end

-- Player health monitoring
function StartPlayerHealthMonitoring()
    CreateThread(function()
        while isSystemActive do
            if playerData and playerData.citizenid then
                CheckPlayerHealthStatus()
            end
            
            Wait(Config.PlayerHealthCheckInterval or 60000)
        end
    end)
end

-- Check player health status
function CheckPlayerHealthStatus()
    local ped = PlayerPedId()
    if not DoesEntityExist(ped) then return end
    
    -- Skip if player is in interior
    if Utils.IsPlayerInInterior() then
        return
    end
    
    -- Get current clothing
    local clothing = Utils.GetPlayerClothing(ped)
    local clothingWarmth = Utils.CalculateClothingWarmth(clothing)
    
    -- Calculate temperature comfort
    local comfort = Utils.CalculateTemperatureComfort(currentWeather, clothing)
    
    Utils.Debug("Player comfort level: %s (warmth: %d, weather: %s)", comfort, clothingWarmth, currentWeather)
    
    -- Send data to server for processing
    TriggerServerEvent('weatherhealth:server:updatePlayerHealth', {
        weather = currentWeather,
        clothing = clothing,
        clothingWarmth = clothingWarmth,
        comfort = comfort,
        time = currentTime
    })
    
    -- Show warnings based on comfort level
    ShowComfortWarnings(comfort)
end

-- Show comfort warnings to player
function ShowComfortWarnings(comfort)
    if comfort == 'dangerous' or comfort == 'critical' then
        local temp = Config.WeatherTemperature[currentWeather] or 20
        
        if temp < 10 then
            QBCore.Functions.Notify(Lang:t('warnings.getting_cold'), 'error')
        elseif temp > 25 then
            QBCore.Functions.Notify(Lang:t('warnings.getting_hot'), 'error')
        end
    elseif comfort == 'uncomfortable' then
        local temp = Config.WeatherTemperature[currentWeather] or 20
        
        if temp < 15 then
            QBCore.Functions.Notify(Lang:t('warnings.getting_cold'), 'primary')
        elseif temp > 20 then
            QBCore.Functions.Notify(Lang:t('warnings.getting_hot'), 'primary')
        end
    end
end

-- NPC Management
function StartNPCManagement()
    CreateThread(function()
        while isSystemActive do
            ManageNearbyNPCs()
            Wait(Config.NPCHealthCheckInterval or 120000)
        end
    end)
end

-- Manage nearby NPCs
function ManageNearbyNPCs()
    local playerPed = PlayerPedId()
    local playerPos = GetEntityCoords(playerPed)
    
    -- Get all peds in range
    local peds = GetGamePool('CPed')
    local managedNPCs = {}
    
    for _, ped in ipairs(peds) do
        if DoesEntityExist(ped) and not IsPedAPlayer(ped) then
            local pedPos = GetEntityCoords(ped)
            local distance = Utils.GetDistance(playerPos, pedPos)
            
            if distance <= (Config.MaxNPCDistance or 200.0) then
                table.insert(managedNPCs, ped)
            end
        end
    end
    
    Utils.Debug("Managing %d NPCs in range", #managedNPCs)
    
    -- Process each NPC
    for _, npc in ipairs(managedNPCs) do
        ProcessNPC(npc)
    end
end

-- Process individual NPC
function ProcessNPC(ped)
    if not DoesEntityExist(ped) then return end
    
    local pedModel = GetEntityModel(ped)
    local pedPos = GetEntityCoords(ped)
    local pedId = tostring(ped) -- Use entity handle as ID
    
    -- Determine NPC gender
    local gender = IsPedMale(ped) and 'male' or 'female'
    
    -- Get appropriate outfit for current weather
    local outfit = Utils.GetRandomNPCOutfit(currentWeather, gender)
    
    if outfit then
        -- Check if NPC needs outfit change
        local currentOutfit = Utils.GetPlayerClothing(ped)
        local needsChange = false
        
        -- Simple check - if torso doesn't match, change outfit
        if currentOutfit.torso ~= outfit.torso then
            needsChange = true
        end
        
        if needsChange then
            Utils.SetNPCClothing(ped, outfit)
            Utils.Debug("Updated NPC clothing for weather %s", currentWeather)
        end
    end
    
    -- Send NPC data to server for health processing
    TriggerServerEvent('weatherhealth:server:updateNPCHealth', {
        npcId = pedId,
        model = pedModel,
        position = pedPos,
        weather = currentWeather,
        time = currentTime,
        gender = gender
    })
end

-- Event handlers
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    playerData = QBCore.Functions.GetPlayerData()
    isSystemActive = true
    Utils.Debug("Player loaded, system activated")
end)

RegisterNetEvent('QBCore:Client:OnPlayerUnload', function()
    isSystemActive = false
    Utils.Debug("Player unloaded, system deactivated")
end)

RegisterNetEvent('QBCore:Player:SetPlayerData', function(val)
    playerData = val
end)

-- Weather health specific events
RegisterNetEvent('weatherhealth:client:diseaseContracted', function(diseaseType, severity)
    local diseaseName = Lang:t('diseases.' .. diseaseType)
    QBCore.Functions.Notify(Utils.FormatMessage(Lang:t('diseases.contracted'), diseaseName), 'error')
    
    -- Apply disease effects
    ApplyDiseaseEffects(diseaseType, severity)
end)

RegisterNetEvent('weatherhealth:client:diseaseCured', function(diseaseType)
    local diseaseName = Lang:t('diseases.' .. diseaseType)
    QBCore.Functions.Notify(Utils.FormatMessage(Lang:t('diseases.cured'), diseaseName), 'success')
    
    -- Remove disease effects
    RemoveDiseaseEffects(diseaseType)
end)

RegisterNetEvent('weatherhealth:client:playSymptomAnimation', function(symptom)
    PlaySymptomAnimation(symptom)
end)

-- Apply disease effects to player
function ApplyDiseaseEffects(diseaseType, severity)
    local disease = Config.HealthEffects.diseases[diseaseType]
    if not disease then return end
    
    Utils.Debug("Applying disease effects: %s (severity: %d)", diseaseType, severity)
    
    -- Apply movement speed effects
    if disease.effects.walkSpeed then
        SetPedMoveRateOverride(PlayerPedId(), disease.effects.walkSpeed)
    end
    
    -- Apply visual effects
    if disease.effects.blurredVision then
        SetTimecycleModifier("drug_flying_base")
        SetTimecycleModifierStrength(0.3)
    end
    
    -- Start symptom animations
    if disease.symptoms then
        for _, symptom in ipairs(disease.symptoms) do
            TriggerEvent('weatherhealth:client:startSymptomLoop', symptom)
        end
    end
end

-- Remove disease effects
function RemoveDiseaseEffects(diseaseType)
    local disease = Config.HealthEffects.diseases[diseaseType]
    if not disease then return end
    
    Utils.Debug("Removing disease effects: %s", diseaseType)
    
    -- Reset movement speed
    SetPedMoveRateOverride(PlayerPedId(), 1.0)
    
    -- Clear visual effects
    ClearTimecycleModifier()
    
    -- Stop symptom animations
    if disease.symptoms then
        for _, symptom in ipairs(disease.symptoms) do
            TriggerEvent('weatherhealth:client:stopSymptomLoop', symptom)
        end
    end
end

-- Commands
RegisterCommand('weatherhealth', function(source, args)
    if args[1] == 'status' then
        local ped = PlayerPedId()
        local clothing = Utils.GetPlayerClothing(ped)
        local warmth = Utils.CalculateClothingWarmth(clothing)
        local comfort = Utils.CalculateTemperatureComfort(currentWeather, clothing)
        local temp = Config.WeatherTemperature[currentWeather] or 20
        local playerHealth = exports['qb-weatherhealth']:getPlayerHealthData()

        local status = string.format(
            "Weather Health Status:\nWeather: %s (%d°C)\nClothing Warmth: %d\nComfort: %s\nHealth: %d\nImmunity: %d\nDisease: %s",
            currentWeather, temp, warmth, comfort,
            playerHealth and playerHealth.health or 100,
            playerHealth and playerHealth.immunityLevel or 50,
            playerHealth and playerHealth.currentDisease or "None"
        )

        QBCore.Functions.Notify(status, 'primary', 8000)
    elseif args[1] == 'debug' then
        Config.Debug = not Config.Debug
        QBCore.Functions.Notify("Debug mode: " .. (Config.Debug and "ON" or "OFF"), 'primary')
    elseif args[1] == 'testshiver' then
        exports['qb-weatherhealth']:playSymptomAnimation('shiver')
        QBCore.Functions.Notify("Testing shiver animation", 'primary')
    end
end)

-- Exports
exports('getCurrentWeather', function()
    return currentWeather
end)

exports('getPlayerComfort', function()
    local ped = PlayerPedId()
    local clothing = Utils.GetPlayerClothing(ped)
    return Utils.CalculateTemperatureComfort(currentWeather, clothing)
end)

exports('isSystemActive', function()
    return isSystemActive
end)
