local L0_1, L1_1, L2_1, L3_1, L4_1
L0_1 = Config
L0_1 = L0_1.IgnoreSettings
if L0_1 then
  return
end
function L0_1(A0_2)
  local L1_2
  if not A0_2 then
    L1_2 = {}
    return L1_2
  end
  L1_2 = A0_2.Options
  if L1_2 then
    L1_2 = A0_2.Options
    if L1_2 then
      goto lbl_14
    end
  end
  L1_2 = {}
  ::lbl_14::
  return L1_2
end
GetStationOption = L0_1
function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L1_2 = pairs
  L2_2 = A0_2
  L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2)
  for L5_2, L6_2 in L1_2, L2_2, L3_2, L4_2 do
    L7_2 = L6_2[1]
    if L7_2 then
      L7_2 = L6_2[1]
      L7_2 = L7_2.coords
      if L7_2 then
        L7_2 = L6_2[1]
        L7_2 = L7_2.coords
        L7_2 = L7_2[1]
        if 0 ~= L7_2 then
          L7_2 = L6_2[1]
          L7_2 = L7_2.coords
          L7_2 = L7_2[2]
          if 0 ~= L7_2 then
            L7_2 = L6_2[1]
            L7_2 = L7_2.coords
            L7_2 = L7_2[3]
            if 0 ~= L7_2 then
              L7_2 = {}
              L8_2 = L6_2[1]
              L8_2 = L8_2.coords
              L8_2 = L8_2[1]
              L9_2 = L6_2[1]
              L9_2 = L9_2.coords
              L9_2 = L9_2[2]
              L10_2 = L6_2[1]
              L10_2 = L10_2.coords
              L10_2 = L10_2[3]
              L7_2[1] = L8_2
              L7_2[2] = L9_2
              L7_2[3] = L10_2
              return L7_2
            end
          end
        end
      end
    end
  end
  L1_2 = {}
  L2_2 = 0
  L3_2 = 0
  L4_2 = 0
  L1_2[1] = L2_2
  L1_2[2] = L3_2
  L1_2[3] = L4_2
  return L1_2
end
GetStationCoords = L0_1
function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L1_2 = {}
  L2_2 = pairs
  L3_2 = A0_2
  L2_2, L3_2, L4_2, L5_2 = L2_2(L3_2)
  for L6_2, L7_2 in L2_2, L3_2, L4_2, L5_2 do
    if "Options" ~= L6_2 then
      L1_2[L6_2] = L7_2
    end
  end
  return L1_2
end
GetMarkers = L0_1
L0_1 = {}
function L1_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  L0_2 = exports
  L0_2 = L0_2.origen_police
  L1_2 = L0_2
  L0_2 = L0_2.GetFileList
  L2_2 = "/config/police-stations"
  L0_2 = L0_2(L1_2, L2_2)
  L1_2 = {}
  L2_2 = 1
  L3_2 = #L0_2
  L4_2 = 1
  for L5_2 = L2_2, L3_2, L4_2 do
    L6_2 = L0_2[L5_2]
    L7_2 = L6_2
    L6_2 = L6_2.find
    L8_2 = ".json"
    L6_2 = L6_2(L7_2, L8_2)
    if not L6_2 then
    else
      L6_2 = json
      L6_2 = L6_2.decode
      L7_2 = exports
      L7_2 = L7_2.origen_police
      L8_2 = L7_2
      L7_2 = L7_2.ReadFile
      L9_2 = "/config/police-stations/"
      L10_2 = L0_2[L5_2]
      L9_2 = L9_2 .. L10_2
      L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L7_2(L8_2, L9_2)
      L6_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
      L7_2 = GetStationOption
      L8_2 = L6_2
      L7_2 = L7_2(L8_2)
      L8_2 = L7_2.inUse
      if L8_2 then
        L8_2 = L0_1
        L8_2 = #L8_2
        L9_2 = L8_2 + 1
        L8_2 = L0_1
        L10_2 = L0_2[L5_2]
        L11_2 = L10_2
        L10_2 = L10_2.gsub
        L12_2 = ".json"
        L13_2 = ""
        L10_2 = L10_2(L11_2, L12_2, L13_2)
        L8_2[L9_2] = L10_2
      end
    end
  end
end
InitMaps = L1_1
L1_1 = FW_CreateCallback
L2_1 = "origen_police:callback:GetStationsList"
function L3_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2
  L2_2 = exports
  L2_2 = L2_2.origen_police
  L3_2 = L2_2
  L2_2 = L2_2.GetFileList
  L4_2 = "/config/police-stations"
  L2_2 = L2_2(L3_2, L4_2)
  L3_2 = {}
  L4_2 = 1
  L5_2 = #L2_2
  L6_2 = 1
  for L7_2 = L4_2, L5_2, L6_2 do
    L8_2 = L2_2[L7_2]
    L9_2 = L8_2
    L8_2 = L8_2.find
    L10_2 = ".json"
    L8_2 = L8_2(L9_2, L10_2)
    if not L8_2 then
    else
      L8_2 = json
      L8_2 = L8_2.decode
      L9_2 = exports
      L9_2 = L9_2.origen_police
      L10_2 = L9_2
      L9_2 = L9_2.ReadFile
      L11_2 = "/config/police-stations/"
      L12_2 = L2_2[L7_2]
      L11_2 = L11_2 .. L12_2
      L9_2, L10_2, L11_2, L12_2, L13_2, L14_2 = L9_2(L10_2, L11_2)
      L8_2 = L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
      L9_2 = GetStationOption
      L10_2 = L8_2
      L9_2 = L9_2(L10_2)
      L10_2 = {}
      L11_2 = L2_2[L7_2]
      L12_2 = L11_2
      L11_2 = L11_2.gsub
      L13_2 = ".json"
      L14_2 = ""
      L11_2 = L11_2(L12_2, L13_2, L14_2)
      L10_2.name = L11_2
      L11_2 = L9_2.label
      if not L11_2 then
        L11_2 = L2_2[L7_2]
        L12_2 = L11_2
        L11_2 = L11_2.gsub
        L13_2 = ".json"
        L14_2 = ""
        L11_2 = L11_2(L12_2, L13_2, L14_2)
      end
      L10_2.label = L11_2
      L11_2 = L9_2.LastUserModified
      if not L11_2 then
        L11_2 = "NONE"
      end
      L10_2.author = L11_2
      L11_2 = L9_2.LastDateModified
      if not L11_2 then
        L11_2 = os
        L11_2 = L11_2.date
        L12_2 = "%d/%m/%Y - %H:%M"
        L11_2 = L11_2(L12_2)
      end
      L10_2.modified = L11_2
      L11_2 = GetStationCoords
      L12_2 = L8_2
      L11_2 = L11_2(L12_2)
      L10_2.coords = L11_2
      L11_2 = L9_2.inUse
      if not L11_2 then
        L11_2 = false
      end
      L10_2.active = L11_2
      L3_2[L7_2] = L10_2
    end
  end
  L4_2 = A1_2
  L5_2 = L3_2
  L4_2(L5_2)
end
L4_1 = AdminMDW
L1_1(L2_1, L3_1, L4_1)
L1_1 = FW_CreateCallback
L2_1 = "origen_police:callback:GetStation"
function L3_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2
  L3_2 = json
  L3_2 = L3_2.decode
  L4_2 = exports
  L4_2 = L4_2.origen_police
  L5_2 = L4_2
  L4_2 = L4_2.ReadFile
  L6_2 = "/config/police-stations/"
  L7_2 = A2_2.station
  L8_2 = ".json"
  L6_2 = L6_2 .. L7_2 .. L8_2
  L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2 = L4_2(L5_2, L6_2)
  L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
  L4_2 = {}
  L5_2 = GetStationOption
  L6_2 = L3_2
  L5_2 = L5_2(L6_2)
  L6_2 = L5_2.AllowedJobCat
  if not L6_2 then
    L6_2 = {}
  end
  L7_2 = {}
  L8_2 = pairs
  L9_2 = L6_2
  L8_2, L9_2, L10_2, L11_2 = L8_2(L9_2)
  for L12_2, L13_2 in L8_2, L9_2, L10_2, L11_2 do
    L14_2 = pairs
    L15_2 = Config
    L15_2 = L15_2.JobCategory
    L15_2 = L15_2[L13_2]
    L14_2, L15_2, L16_2, L17_2 = L14_2(L15_2)
    for L18_2, L19_2 in L14_2, L15_2, L16_2, L17_2 do
      L20_2 = L7_2[L13_2]
      if not L20_2 then
        L20_2 = {}
      end
      L7_2[L13_2] = L20_2
      L20_2 = L7_2[L13_2]
      L21_2 = L7_2[L13_2]
      L21_2 = #L21_2
      L21_2 = L21_2 + 1
      L22_2 = L19_2.name
      L20_2[L21_2] = L22_2
    end
  end
  L8_2 = A1_2
  L9_2 = {}
  L10_2 = A2_2.station
  L9_2.name = L10_2
  L10_2 = L5_2.label
  if not L10_2 then
    L10_2 = A2_2.station
  end
  L9_2.label = L10_2
  L10_2 = L5_2.LastUserModified
  if not L10_2 then
    L10_2 = "NONE"
  end
  L9_2.author = L10_2
  L10_2 = L5_2.LastDateModified
  if not L10_2 then
    L10_2 = os
    L10_2 = L10_2.date
    L11_2 = "%d/%m/%Y - %H:%M"
    L10_2 = L10_2(L11_2)
  end
  L9_2.modified = L10_2
  L10_2 = L5_2.inUse
  if not L10_2 then
    L10_2 = false
  end
  L9_2.active = L10_2
  L10_2 = GetMarkers
  L11_2 = L3_2
  L10_2 = L10_2(L11_2)
  L9_2.markers = L10_2
  L9_2.jobs = L7_2
  L8_2(L9_2)
end
L1_1(L2_1, L3_1)
L1_1 = FW_CreateCallback
L2_1 = "origen_police:callback:AddJobCatStation"
function L3_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L3_2 = json
  L3_2 = L3_2.decode
  L4_2 = exports
  L4_2 = L4_2.origen_police
  L5_2 = L4_2
  L4_2 = L4_2.ReadFile
  L6_2 = "/config/police-stations/"
  L7_2 = A2_2.station
  L8_2 = ".json"
  L6_2 = L6_2 .. L7_2 .. L8_2
  L4_2, L5_2, L6_2, L7_2, L8_2, L9_2 = L4_2(L5_2, L6_2)
  L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  if not L3_2 then
    L3_2 = {}
  end
  L4_2 = L3_2.Options
  if not L4_2 then
    L4_2 = {}
  end
  L3_2.Options = L4_2
  L4_2 = L3_2.Options
  L5_2 = L3_2.Options
  L5_2 = L5_2.AllowedJobCat
  if not L5_2 then
    L5_2 = {}
  end
  L4_2.AllowedJobCat = L5_2
  L4_2 = L3_2.Options
  L4_2 = L4_2.AllowedJobCat
  L5_2 = L3_2.Options
  L5_2 = L5_2.AllowedJobCat
  L5_2 = #L5_2
  L5_2 = L5_2 + 1
  L6_2 = A2_2.cat
  L4_2[L5_2] = L6_2
  L4_2 = L3_2.Options
  L5_2 = GetPlayerName
  L6_2 = A0_2
  L5_2 = L5_2(L6_2)
  L4_2.LastUserModified = L5_2
  L4_2 = L3_2.Options
  L5_2 = os
  L5_2 = L5_2.date
  L6_2 = "%d/%m/%Y - %H:%M"
  L5_2 = L5_2(L6_2)
  L4_2.LastDateModified = L5_2
  L4_2 = exports
  L4_2 = L4_2.origen_police
  L5_2 = L4_2
  L4_2 = L4_2.WriteFile
  L6_2 = "/config/police-stations/"
  L7_2 = A2_2.station
  L8_2 = ".json"
  L6_2 = L6_2 .. L7_2 .. L8_2
  L7_2 = json
  L7_2 = L7_2.encode
  L8_2 = L3_2
  L9_2 = {}
  L9_2.indent = true
  L7_2, L8_2, L9_2 = L7_2(L8_2, L9_2)
  L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
  L4_2 = A1_2
  L5_2 = true
  L4_2(L5_2)
end
L4_1 = AdminMDW
L1_1(L2_1, L3_1, L4_1)
L1_1 = FW_CreateCallback
L2_1 = "origen_police:callback:RemoveJobCatStation"
function L3_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L3_2 = json
  L3_2 = L3_2.decode
  L4_2 = exports
  L4_2 = L4_2.origen_police
  L5_2 = L4_2
  L4_2 = L4_2.ReadFile
  L6_2 = "/config/police-stations/"
  L7_2 = A2_2.station
  L8_2 = ".json"
  L6_2 = L6_2 .. L7_2 .. L8_2
  L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2 = L4_2(L5_2, L6_2)
  L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
  if not L3_2 then
    L3_2 = {}
  end
  L4_2 = L3_2.Options
  if not L4_2 then
    L4_2 = {}
  end
  L3_2.Options = L4_2
  L4_2 = L3_2.Options
  L5_2 = L3_2.Options
  L5_2 = L5_2.AllowedJobCat
  if not L5_2 then
    L5_2 = {}
  end
  L4_2.AllowedJobCat = L5_2
  L4_2 = 1
  L5_2 = L3_2.Options
  L5_2 = L5_2.AllowedJobCat
  L5_2 = #L5_2
  L6_2 = 1
  for L7_2 = L4_2, L5_2, L6_2 do
    L8_2 = L3_2.Options
    L8_2 = L8_2.AllowedJobCat
    L8_2 = L8_2[L7_2]
    L9_2 = A2_2.cat
    if L8_2 == L9_2 then
      L8_2 = table
      L8_2 = L8_2.remove
      L9_2 = L3_2.Options
      L9_2 = L9_2.AllowedJobCat
      L10_2 = L7_2
      L8_2(L9_2, L10_2)
      break
    end
  end
  L4_2 = L3_2.Options
  L5_2 = GetPlayerName
  L6_2 = A0_2
  L5_2 = L5_2(L6_2)
  L4_2.LastUserModified = L5_2
  L4_2 = L3_2.Options
  L5_2 = os
  L5_2 = L5_2.date
  L6_2 = "%d/%m/%Y - %H:%M"
  L5_2 = L5_2(L6_2)
  L4_2.LastDateModified = L5_2
  L4_2 = exports
  L4_2 = L4_2.origen_police
  L5_2 = L4_2
  L4_2 = L4_2.WriteFile
  L6_2 = "/config/police-stations/"
  L7_2 = A2_2.station
  L8_2 = ".json"
  L6_2 = L6_2 .. L7_2 .. L8_2
  L7_2 = json
  L7_2 = L7_2.encode
  L8_2 = L3_2
  L9_2 = {}
  L9_2.indent = true
  L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
  L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
  L4_2 = A1_2
  L5_2 = true
  L4_2(L5_2)
end
L4_1 = AdminMDW
L1_1(L2_1, L3_1, L4_1)
L1_1 = FW_CreateCallback
L2_1 = "origen_police:callback:SetActiveStation"
function L3_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L3_2 = json
  L3_2 = L3_2.decode
  L4_2 = exports
  L4_2 = L4_2.origen_police
  L5_2 = L4_2
  L4_2 = L4_2.ReadFile
  L6_2 = "/config/police-stations/"
  L7_2 = A2_2.station
  L8_2 = ".json"
  L6_2 = L6_2 .. L7_2 .. L8_2
  L4_2, L5_2, L6_2, L7_2, L8_2, L9_2 = L4_2(L5_2, L6_2)
  L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  if not L3_2 then
    L3_2 = {}
  end
  L4_2 = L3_2.Options
  if not L4_2 then
    L4_2 = {}
  end
  L3_2.Options = L4_2
  L4_2 = L3_2.Options
  L5_2 = A2_2.active
  L4_2.inUse = L5_2
  L4_2 = L3_2.Options
  L5_2 = GetPlayerName
  L6_2 = A0_2
  L5_2 = L5_2(L6_2)
  L4_2.LastUserModified = L5_2
  L4_2 = L3_2.Options
  L5_2 = os
  L5_2 = L5_2.date
  L6_2 = "%d/%m/%Y - %H:%M"
  L5_2 = L5_2(L6_2)
  L4_2.LastDateModified = L5_2
  L4_2 = exports
  L4_2 = L4_2.origen_police
  L5_2 = L4_2
  L4_2 = L4_2.WriteFile
  L6_2 = "/config/police-stations/"
  L7_2 = A2_2.station
  L8_2 = ".json"
  L6_2 = L6_2 .. L7_2 .. L8_2
  L7_2 = json
  L7_2 = L7_2.encode
  L8_2 = L3_2
  L9_2 = {}
  L9_2.indent = true
  L7_2, L8_2, L9_2 = L7_2(L8_2, L9_2)
  L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
  L4_2 = TriggerClientEvent
  L5_2 = "origen_police:client:ActiveStation"
  L6_2 = -1
  L7_2 = {}
  L8_2 = A2_2.station
  L7_2.station = L8_2
  L8_2 = A2_2.active
  L7_2.active = L8_2
  L4_2(L5_2, L6_2, L7_2)
  L4_2 = A1_2
  L5_2 = true
  L4_2(L5_2)
end
L4_1 = AdminMDW
L1_1(L2_1, L3_1, L4_1)
L1_1 = FW_CreateCallback
L2_1 = "origen_police:callback:UpdateMarkerPos"
function L3_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L3_2 = json
  L3_2 = L3_2.decode
  L4_2 = exports
  L4_2 = L4_2.origen_police
  L5_2 = L4_2
  L4_2 = L4_2.ReadFile
  L6_2 = "/config/police-stations/"
  L7_2 = A2_2.station
  L8_2 = ".json"
  L6_2 = L6_2 .. L7_2 .. L8_2
  L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2 = L4_2(L5_2, L6_2)
  L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
  if not L3_2 then
    L3_2 = {}
  end
  L4_2 = tonumber
  L5_2 = A2_2.markerIndex
  L4_2 = L4_2(L5_2)
  A2_2.markerIndex = L4_2
  L4_2 = A2_2.markerName
  L4_2 = L3_2[L4_2]
  L5_2 = A2_2.markerIndex
  L4_2 = L4_2[L5_2]
  L5_2 = A2_2.isMultiple
  if L5_2 then
    L5_2 = "spawn"
    if L5_2 then
      goto lbl_31
    end
  end
  L5_2 = "coords"
  ::lbl_31::
  L6_2 = {}
  L7_2 = A2_2.x
  L8_2 = A2_2.y
  L9_2 = A2_2.z
  L10_2 = A2_2.w
  L6_2[1] = L7_2
  L6_2[2] = L8_2
  L6_2[3] = L9_2
  L6_2[4] = L10_2
  L4_2[L5_2] = L6_2
  L4_2 = L3_2.Options
  L5_2 = GetPlayerName
  L6_2 = A0_2
  L5_2 = L5_2(L6_2)
  L4_2.LastUserModified = L5_2
  L4_2 = L3_2.Options
  L5_2 = os
  L5_2 = L5_2.date
  L6_2 = "%d/%m/%Y - %H:%M"
  L5_2 = L5_2(L6_2)
  L4_2.LastDateModified = L5_2
  L4_2 = exports
  L4_2 = L4_2.origen_police
  L5_2 = L4_2
  L4_2 = L4_2.WriteFile
  L6_2 = "/config/police-stations/"
  L7_2 = A2_2.station
  L8_2 = ".json"
  L6_2 = L6_2 .. L7_2 .. L8_2
  L7_2 = json
  L7_2 = L7_2.encode
  L8_2 = L3_2
  L9_2 = {}
  L9_2.indent = true
  L7_2, L8_2, L9_2, L10_2 = L7_2(L8_2, L9_2)
  L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
  L4_2 = TriggerClientEvent
  L5_2 = "origen_police:client:UpdateMarkerPos"
  L6_2 = -1
  L7_2 = A2_2
  L4_2(L5_2, L6_2, L7_2)
  L4_2 = A1_2
  L5_2 = true
  L4_2(L5_2)
end
L4_1 = AdminMDW
L1_1(L2_1, L3_1, L4_1)
L1_1 = FW_CreateCallback
L2_1 = "origen_police:callback:RemoveMarker"
function L3_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L3_2 = json
  L3_2 = L3_2.decode
  L4_2 = exports
  L4_2 = L4_2.origen_police
  L5_2 = L4_2
  L4_2 = L4_2.ReadFile
  L6_2 = "/config/police-stations/"
  L7_2 = A2_2.station
  L8_2 = ".json"
  L6_2 = L6_2 .. L7_2 .. L8_2
  L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2 = L4_2(L5_2, L6_2)
  L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
  if not L3_2 then
    L3_2 = {}
  end
  L4_2 = tonumber
  L5_2 = A2_2.markerIndex
  L4_2 = L4_2(L5_2)
  A2_2.markerIndex = L4_2
  L4_2 = A2_2.markerName
  L4_2 = L3_2[L4_2]
  L5_2 = A2_2.markerIndex
  L4_2 = L4_2[L5_2]
  L4_2 = L4_2.coords
  A2_2.coords = L4_2
  L5_2 = table
  L5_2 = L5_2.remove
  L6_2 = A2_2.markerName
  L6_2 = L3_2[L6_2]
  L7_2 = A2_2.markerIndex
  L5_2(L6_2, L7_2)
  L5_2 = L3_2.Options
  L6_2 = GetPlayerName
  L7_2 = A0_2
  L6_2 = L6_2(L7_2)
  L5_2.LastUserModified = L6_2
  L5_2 = L3_2.Options
  L6_2 = os
  L6_2 = L6_2.date
  L7_2 = "%d/%m/%Y - %H:%M"
  L6_2 = L6_2(L7_2)
  L5_2.LastDateModified = L6_2
  L5_2 = exports
  L5_2 = L5_2.origen_police
  L6_2 = L5_2
  L5_2 = L5_2.WriteFile
  L7_2 = "/config/police-stations/"
  L8_2 = A2_2.station
  L9_2 = ".json"
  L7_2 = L7_2 .. L8_2 .. L9_2
  L8_2 = json
  L8_2 = L8_2.encode
  L9_2 = L3_2
  L10_2 = {}
  L10_2.indent = true
  L8_2, L9_2, L10_2 = L8_2(L9_2, L10_2)
  L5_2(L6_2, L7_2, L8_2, L9_2, L10_2)
  L5_2 = TriggerClientEvent
  L6_2 = "origen_police:client:RemoveMarker"
  L7_2 = -1
  L8_2 = A2_2
  L5_2(L6_2, L7_2, L8_2)
  L5_2 = A1_2
  L6_2 = true
  L5_2(L6_2)
end
L4_1 = AdminMDW
L1_1(L2_1, L3_1, L4_1)
L1_1 = FW_CreateCallback
L2_1 = "origen_police:callback:CreateMarker"
function L3_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L3_2 = json
  L3_2 = L3_2.decode
  L4_2 = exports
  L4_2 = L4_2.origen_police
  L5_2 = L4_2
  L4_2 = L4_2.ReadFile
  L6_2 = "/config/police-stations/"
  L7_2 = A2_2.station
  L8_2 = ".json"
  L6_2 = L6_2 .. L7_2 .. L8_2
  L4_2, L5_2, L6_2, L7_2, L8_2, L9_2 = L4_2(L5_2, L6_2)
  L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  if not L3_2 then
    L3_2 = {}
  end
  L4_2 = tonumber
  L5_2 = A2_2.markerIndex
  L4_2 = L4_2(L5_2)
  A2_2.markerIndex = L4_2
  L4_2 = A2_2.markerName
  L5_2 = A2_2.markerName
  L5_2 = L3_2[L5_2]
  if not L5_2 then
    L5_2 = {}
  end
  L3_2[L4_2] = L5_2
  L4_2 = A2_2.markerName
  L4_2 = L3_2[L4_2]
  L5_2 = A2_2.markerName
  L5_2 = L3_2[L5_2]
  L5_2 = #L5_2
  L5_2 = L5_2 + 1
  L6_2 = {}
  L4_2[L5_2] = L6_2
  L4_2 = A2_2.markerName
  L4_2 = L3_2[L4_2]
  L5_2 = A2_2.markerName
  L5_2 = L3_2[L5_2]
  L5_2 = #L5_2
  L6_2 = A2_2.creatingData
  L4_2[L5_2] = L6_2
  L4_2 = L3_2.Options
  L5_2 = GetPlayerName
  L6_2 = A0_2
  L5_2 = L5_2(L6_2)
  L4_2.LastUserModified = L5_2
  L4_2 = L3_2.Options
  L5_2 = os
  L5_2 = L5_2.date
  L6_2 = "%d/%m/%Y - %H:%M"
  L5_2 = L5_2(L6_2)
  L4_2.LastDateModified = L5_2
  L4_2 = exports
  L4_2 = L4_2.origen_police
  L5_2 = L4_2
  L4_2 = L4_2.WriteFile
  L6_2 = "/config/police-stations/"
  L7_2 = A2_2.station
  L8_2 = ".json"
  L6_2 = L6_2 .. L7_2 .. L8_2
  L7_2 = json
  L7_2 = L7_2.encode
  L8_2 = L3_2
  L9_2 = {}
  L9_2.indent = true
  L7_2, L8_2, L9_2 = L7_2(L8_2, L9_2)
  L4_2(L5_2, L6_2, L7_2, L8_2, L9_2)
  L4_2 = TriggerClientEvent
  L5_2 = "origen_police:client:CreateMarker"
  L6_2 = -1
  L7_2 = A2_2
  L4_2(L5_2, L6_2, L7_2)
  L4_2 = A1_2
  L5_2 = true
  L4_2(L5_2)
end
L4_1 = AdminMDW
L1_1(L2_1, L3_1, L4_1)
L1_1 = FW_CreateCallback
L2_1 = "origen_police:callback:DeleteStation"
function L3_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2
  L3_2 = exports
  L3_2 = L3_2.origen_police
  L4_2 = L3_2
  L3_2 = L3_2.DeleteFile
  L5_2 = "/config/police-stations/"
  L6_2 = A2_2.station
  L7_2 = ".json"
  L5_2 = L5_2 .. L6_2 .. L7_2
  L3_2(L4_2, L5_2)
  L3_2 = A1_2
  L4_2 = true
  L3_2(L4_2)
end
L4_1 = AdminMDW
L1_1(L2_1, L3_1, L4_1)
L1_1 = InitMaps
L1_1()
L1_1 = FW_CreateCallback
L2_1 = "origen_police:callback:GetActiveMaps"
function L3_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2
  L3_2 = A1_2
  L4_2 = L0_1
  L3_2(L4_2)
end
L1_1(L2_1, L3_1)
