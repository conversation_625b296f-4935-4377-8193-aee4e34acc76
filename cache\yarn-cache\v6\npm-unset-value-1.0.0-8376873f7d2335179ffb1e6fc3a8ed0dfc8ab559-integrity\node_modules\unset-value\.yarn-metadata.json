{"manifest": {"name": "unset-value", "description": "Delete nested properties from an object using dot notation.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/unset-value", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"email": "<EMAIL>", "url": "https://github.com/wtgtybhertgeghgtwtg"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/unset-value.git"}, "bugs": {"url": "https://github.com/jonschlinkert/unset-value/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "*", "should": "*"}, "keywords": ["del", "delete", "key", "object", "omit", "prop", "property", "remove", "unset", "value"], "verb": {"related": {"list": ["get-value", "get-values", "omit-value", "put-value", "set-value", "union-value", "upsert-value"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-unset-value-1.0.0-8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559-integrity\\node_modules\\unset-value\\package.json", "readmeFilename": "README.md", "readme": "# unset-value [![NPM version](https://img.shields.io/npm/v/unset-value.svg?style=flat)](https://www.npmjs.com/package/unset-value) [![NPM monthly downloads](https://img.shields.io/npm/dm/unset-value.svg?style=flat)](https://npmjs.org/package/unset-value)  [![NPM total downloads](https://img.shields.io/npm/dt/unset-value.svg?style=flat)](https://npmjs.org/package/unset-value) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/unset-value.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/unset-value)\n\n> Delete nested properties from an object using dot notation.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save unset-value\n```\n\n## Usage\n\n```js\nvar unset = require('unset-value');\n\nvar obj = {a: {b: {c: 'd', e: 'f'}}};\nunset(obj, 'a.b.c');\nconsole.log(obj);\n//=> {a: {b: {e: 'f'}}};\n```\n\n## Examples\n\n### Updates the object when a property is deleted\n\n```js\nvar obj = {a: 'b'};\nunset(obj, 'a');\nconsole.log(obj);\n//=> {}\n```\n\n### Returns true when a property is deleted\n\n```js\nunset({a: 'b'}, 'a') // true\n```\n\n### Returns `true` when a property does not exist\n\nThis is consistent with `delete` behavior in that it does not\nthrow when a property does not exist.\n\n```js\nunset({a: {b: {c: 'd'}}}, 'd') // true\n```\n\n### delete nested values\n\n```js\nvar one = {a: {b: {c: 'd'}}};\nunset(one, 'a.b');\nconsole.log(one);\n//=> {a: {}}\n\nvar two = {a: {b: {c: 'd'}}};\nunset(two, 'a.b.c');\nconsole.log(two);\n//=> {a: {b: {}}}\n\nvar three = {a: {b: {c: 'd', e: 'f'}}};\nunset(three, 'a.b.c');\nconsole.log(three);\n//=> {a: {b: {e: 'f'}}}\n```\n\n### throws on invalid args\n\n```js\nunset();\n// 'expected an object.'\n```\n\n## About\n\n### Related projects\n\n* [get-value](https://www.npmjs.com/package/get-value): Use property paths (`a.b.c`) to get a nested value from an object. | [homepage](https://github.com/jonschlinkert/get-value \"Use property paths (`a.b.c`) to get a nested value from an object.\")\n* [get-values](https://www.npmjs.com/package/get-values): Return an array of all values from the given object. | [homepage](https://github.com/jonschlinkert/get-values \"Return an array of all values from the given object.\")\n* [omit-value](https://www.npmjs.com/package/omit-value): Omit properties from an object or deeply nested property of an object using object path… [more](https://github.com/jonschlinkert/omit-value) | [homepage](https://github.com/jonschlinkert/omit-value \"Omit properties from an object or deeply nested property of an object using object path notation.\")\n* [put-value](https://www.npmjs.com/package/put-value): Update only existing values from an object, works with dot notation paths like `a.b.c` and… [more](https://github.com/tunnckocore/put-value#readme) | [homepage](https://github.com/tunnckocore/put-value#readme \"Update only existing values from an object, works with dot notation paths like `a.b.c` and support deep nesting.\")\n* [set-value](https://www.npmjs.com/package/set-value): Create nested values and any intermediaries using dot notation (`'a.b.c'`) paths. | [homepage](https://github.com/jonschlinkert/set-value \"Create nested values and any intermediaries using dot notation (`'a.b.c'`) paths.\")\n* [union-value](https://www.npmjs.com/package/union-value): Set an array of unique values as the property of an object. Supports setting deeply… [more](https://github.com/jonschlinkert/union-value) | [homepage](https://github.com/jonschlinkert/union-value \"Set an array of unique values as the property of an object. Supports setting deeply nested properties using using object-paths/dot notation.\")\n* [upsert-value](https://www.npmjs.com/package/upsert-value): Update or set nested values and any intermediaries with dot notation (`'a.b.c'`) paths. | [homepage](https://github.com/doowb/upsert-value \"Update or set nested values and any intermediaries with dot notation (`'a.b.c'`) paths.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 6 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 2 | [wtgtybhertgeghgtwtg](https://github.com/wtgtybhertgeghgtwtg) |\n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.4.2, on February 25, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015, 2017, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/unset-value/-/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559", "type": "tarball", "reference": "https://registry.yarnpkg.com/unset-value/-/unset-value-1.0.0.tgz", "hash": "8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559", "integrity": "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=", "registry": "npm", "packageName": "unset-value", "cacheIntegrity": "sha512-PcA2tsuGSF9cnySLHTLSh2qrQiJ70mn+r+Glzxv2TWZblxsxCC52BDlZoPCsz7STd9pN7EZetkWZBAvk4cgZdQ== sha1-g3aHP30jNRef+x5vw6jtDfyKtVk="}, "registry": "npm", "hash": "8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"}