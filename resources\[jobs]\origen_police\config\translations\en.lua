if Config.Language ~= "en" then return end

Translations = {
    ["Reference"] = "REFERENCES",
    ["Icons"] = "ICONS",
    ["Colors"] = "COLORS",
    ["RadialComm"] = "RADIAL COMMUNICATIONS",
    ["Radio"] = "RADIO",
    ["Disconnected"] = "DISCONNECTED",
    ["SouthUnits"] = "SOUTH UNITS",
    ["NorthUnits"] = "NORTH UNITS",
    ["SpecialUnits"] = "SPECIAL UNITS",
    ["EMSUnits"] = "EMS UNITS",
    ["Interaction"] = "INTERACTION",
    ["CitizenInteraction"] = "CITIZEN INTERACTION",
    ["Search"] = "Search",
    ["Wifes"] = "HANDCUFF",
    ["Escort"] = "ESCORT",
    ["PutInVehicle"] = "PUT/GET IN VEHICLE",
    ["JumpTo"] = "JUMP TO",
    ["HealWounds"] = "TO HEAL WOUNDS",
    ["PutTakeAnkle"] = "Put/take ankle cuff",
    ["LogsDate"] = "LOGS DATES",
    ["Localize"] = "LOCALIZE",
    ["Tase"] = "TASE",
    ["revive"] = "Revive",
    ["VehicleInteraction"] = "INTERACTION WITH VEHICLES",
    ["VehicleInformation"] = "VEHICLE INFORMATION",
    ["SeizeVehicle"] = "SEIZE VEHICLE",
    ["CallTow"] = "Call tow truck",
    ["ForceLock"] = "FORCE LOCK",
    ["StopTraffic"] = "STOP TRAFFIC",
    ["ReduceTraffic"] = "REDUCE TRAFFIC",
    ["ResumeTraffic"] = "RESUME TRAFFIC",
    ["Availabel"] = "AVAILABLE",
    ["WeaponsConfiguration"] = "WEAPONS CONFIGURATION",
    ["ShowHideWeapons"] = "Show/Hide Weapons",
    ["PistolPos"] = "PISTOL POSITION",
    ["RiflePos"] = "RIFLE POSITION",
    ["Front"] = "FRONT",
    ["Behind"] = "BEHIND",
    ["WaistCart"] = "WAIST CARTRIDGE",
    ["NormalCart"] = "NORMAL CARTRIDGE",
    ["ChestCart"] = "CHEST CARTRIDGE",
    ["ThighCart"] = "THIGH CARTRIDGE",
    ["LegCart"] = "LEG CARTRIDGE",
    ["SeparateLegCart"] = "SEPARATE LEG CARTRIDGE",
    ["Chest"] = "CHEST",
    ["Back"] = "BACK",
    ["PoliceObjects"] = "POLICE OBJECTS",
    ["Cone"] = "CONE",
    ["Barrier"] = "BARRIER",
    ["Sign"] = "SIGN",
    ["Spikes"] = "SPIKES",
    ["Radar"] = "RADAR",
    ["Delete"] = "DELETE",
    ["Emergencies"] = "EMERGENCIES",
    ["NoAlertRecived"] = "There are no alerts received",
    ["Settings"] = "SETTINGS",
    ["Guide"] = "Guide of Use",
    ["General"] = "General",
    ["AlertsCode"] = "Alerts 488, 487, 487-V",
    ["DrugTrafficking"] = "Drug trafficking",
    ["VehicleRobs"] = "Vehicle robberies",
    ["Alerts215"] = "Alerts 215 / Weapons",
    ["Radars"] = "Radars",
    ["KeyToAlert"] = "Key to go to the alert",
    ["DeleteAlertKey"] = "Alert Deleye Key",
    ["EmergencyOpenKey"] = "Emergencies opening key",
    ["Equipment"] = "Equipment",
    ["Cone"] = "Cone",
    ["ConeDesc"] = "The cone",
    ["Barriers"] = "Barreras",
    ["BarriersDesc"] = "The barriers",
    ["TrafficLights"] = "Traffic lights",
    ["TrafficLightsDesc"] = "The traffic signs",
    ["Spikes"] = "Spikes",
    ["SpikesDesc"] = "The spikes",
    ["Radar"] = "Radar",
    ["RadarDesc"] = "The radar",
    ["K9Title"] = "K9 Control",
    ["K9Follow"] = "Follow",
    ["K9FollowDesc"] = "Order the K9 unit to follow yourself",
    ["K9DontMove"] = "Don't Move",
    ["K9DontMoveDesc"] = "Order the K9 unit to stay in the place",
    ["K9Sit"] = "Sit",
    ["K9SitDesc"] = "Order the K9 unit to sit",
    ["K9LieDown"] = "Lie Down",
    ["K9LieDownDesc"] = "Order the K9 unit to lie down",
    ["K9SearhArea"] = "Search in Area",
    ["K9SearhAreaDesc"] = "Order the K9 unit to search in the area",
    ["K9ReturnCar"] = "Return to the car",
    ["K9ReturnCarDesc"] = "Order the K9 unit to return to the car",
    
    -- RADIALS MSG --
    ['10.8'] = 'Awaiting assignment',
    ['10.10'] = 'Performing 10.10, Good service!',
    ['Cod 7'] = 'Making a technical stop',
    ['254-V'] = 'Initiating 254-V at %s [ %s ] in %s',
    ['487-V'] = 'Visual on last 487-V, %s [$s]',
    ['Cod 2'] = 'Starting regular patrol',
    ['10.22'] = 'Heading to the police station',
    ['6-Adam'] = 'Responding as 6-Adam',
    ['10.98'] = '10.98 to the last incident, proceeding with 10.95 for Code 2',
    ['Veh 488'] = 'Vehicle involved in 488 from %s: %s [%s]',
    ['Veh 487'] = 'Vehicle involved in 487 from %s: %s [%s]',
    ['Veh Alt'] = 'Vehicle involved in an altercation %s [%s] in %s',
    ['10.6'] = 'Performing a traffic stop on %s [%s] in %s',

    -- ME COMMANDS --
    ['10-20ME'] = 'Reaches for the radio and presses the location button',
    ['QRRME'] = 'Reaches for the radio and presses the panic button',
    ['Agentatrisk'] = 'Agent in danger',
    ['domyfinguer'] = 'After a few seconds, the result would come out: %s',
    ['VehicleinofME'] = 'Opens the vehicle door, puts the person in the vehicle, fastens their seatbelt, and closes the door',
    ['VehicleofinME'] = 'Opens the vehicle door, takes the person out, removes their seatbelt, and closes the door',

    -- NOTIFY --
    ['noSeat'] = 'No available seats',

    -- Police Equipment
    ['PoliceEquipment'] = 'Police Equipment',
    ['Equipment'] = 'Equipment',
    ['EquipmentDesc'] = 'Access Police Equipment',
    ['LeaveEquipment'] = 'Leave Equipment',
    ['LeaveEquipmentDesc'] = 'Leave your police equipment here',
    ['PoliceInventory'] = 'Police Inventory',
    ['PoliceInventoryDesc'] = 'To leave food, water, etc',
    ['EvidenceProof'] = 'Evidence / Proof',
    ['EvidenceProofDesc'] = 'Access evidence / proof',

    -- Holster
    ['DoHide'] = "do would look like he's hiding something under his clothes",
    ['DoShow'] = "do would look like he's making a gesture showing his weapons",
    ['SomethingWrong'] = "There seems to have been a mistake",
    ['HipHolster'] = "You've changed the position of the gun to hip.",
    ['BackHolster'] = 'You have changed the position of the pistol to the back',
    ['LegHolster'] = 'You have moved the pistol position to the leg',
    ['UpperHolster'] = 'You have moved the pistol position to the chest',
    ['UnderPantsHolster'] = 'You have changed the position of the pistol to the pants',
    ['LongHolsterBack'] = 'You have changed the position of the long guns to the back',
    ['LongHolsterFront'] = 'You have switched the position of the long guns to the front.',
    ["NoPersonNear"] = "There isn't anyone near",

    ["VehicleRob"] = "Vehicle Rob",
    ["VehicleRobDesc"] = "A vehicle has been robed",
    ["Call911"] = "Call from 911",
    ["ForensicTitle"] = "Forensic Analysis Report",
    ["ForensicDesc"] = "Through this present report, the Scientific Department of the San Andreas Police shows the complete analysis of the attached evidence, the approximate time of the fact or in case of not knowing the time of collecting the evidence and/or analysis of this.",
    ["EvidenceOf"] = "Evidence of",
    ["ApproximateTime"] = "Approximate time",
    ["MinutesAnd"] = "minutes and",
    ["SecondAprox"] = "Seconds approx",
    ["Shot"] = "Shot",
    ["Calibre"] = "Calibre",
    ["Identifier"] = "Identifier",
    ["Model"] = "Model",
    ["Amount"] = "Amount",

    ["January"] = "January",
    ["February"] = "February",
    ["March"] = "March",
    ["April"] = "April",
    ["May"] = "May",
    ["June"] = "June",
    ["July"] = "July",
    ["August"] = "August",
    ["September"] = "September",
    ["October"] = "October",
    ["November"] = "November",
    ["December"] = "December",

    ["Shoot"] = "Shoot",
    ["BloodRemains"] = "Blood remains",
    ["BulletImpact"] = "Bullet impact",
    ["VehicleBody"] = "Vehicle body remains",
    ["Fingerprint"] = "Fingerprint",
    ["Weapon"] = "Weapon",
    ["Drug"] = "Drug",
    ["Fingerprints"] = "Fingerprints",
    ["Of"] = "of",
    ["Speeding"] = "Speeding",
    ["PlateCod9"] = "Plate in Code-9",
    ["215"] = "Cod 215 - Shoot",

    ["ExistVehicleInSpawn"] = "There is another vehicle in the square, wait for it to leave",
    ["MustLook"] = "You must be in or looking at a vehicle",
    ["ExistHelicopterInSpawn"] = "There is another helicopter in the square, wait for it to leave",
    ["ExistBoatInSpawn"] = "There is another boat in the square, wait for it to leave",
    ["VehicleConfiscated"] = "You have confiscated the vehicle",
    ["CouldntOpenLock"] = "The lock couldn't be opened",
    ["NoEvidence"] = "You don't have any evidence to analyze",
    ["RespectRol"] = "Please respect the environment role",
    ["CantUncuff"] = "You can't uncuff him right now",
    ["CantDoThis"] = "You cant do this",
    ["HasToBeCuffed"] = "The player need to be cuffed to perform that action",
    ["NotCuffed"] = "The player isn't cuffed",
    ["PersonFar"] = "The player is far away",
    ["InvalidK9Veh"] = "You dont bring the vehicle of the K9 Unit",
    ["AlreadyCallK9"] = "You have already called the K9 Unit",
    ["K9Found"] = "Looks like the K9 found something",
    ["K9NotFound"] = "The K9 Unit didn't find anything",
    ["CantFast"] = "You can do that so fast",
    ["MustEnterNumber"] = "You must enter a number",
    ["InvNotSupported"] = "Your inventory system is not supported yet",
    ["ChangedCloth"] = "You have changed your clothes",
    ["NoFederalClothAvailable"] = "There isn't any federal cloth availabel right now for your gender",
    ["PedCantChangeCloth"] = "The peds can't change or wear federal clothes",
    ["CantSendEmpty"] = "You cannot send an empty",
    ["PertenencesPickUp"] = "You have picked up your pertenences",
    ["LeavePertenences"] = "You have leaved your pertenences",
    ["NoPhotoSpace"] = "You dont have space to take the photo",
    ["NoSpaceInInv"] = "You dont have space in your inventory to store that",
    ["ObtainedFingerpritns"] = "You have obtained evidence with fingerprint",
    ["NoFingerFound"] = "You didn't find any fingerprint",
    ["EvidenceNotCategorized"] = "That evidence is not categorized yet",
    ["PlayerNotConnected"] = "The player is not connected or you did not set a valid time",
    ["NewLimitation"] = "A new limitation has been created",
    ["UpdatedLimitation"] = "A limitation has been updated",
    ["LimitationRemoved"] = "A limitation has been removed",
    ["CantFindLimitation"] = "The limitation couldn't be finded",
    ["ProblemCreateNote"] = "There was a problem while creating the note",
    ["ProblemCreateReport"] = "There was a problem while creating the report",
    ["EnterMaxSpeed"] = "Enter the maximum speed",
    ["Speed"] = "Speed",
    ["Assign"] = "Assign",
    ["RemainSentance"] = "Remaining sentance:",
    ["Month"] = "months",
    ["InvalidVeh"] = "Invalid vehicle to perform this action",
    
    ["AgentAlert"] = "AGENT",
    ["VehicleAlert"] = "VEHICLE",
    ["PlateAlert"] = "PLATE",
    ["SpeedAlert"] = "SPEED",
    ["WeaponAlert"] = "WEAPON",
    ["ErrorOccurred"] = "An error has occurred",
    ["NoTabPermission"] = "YOU DON'T HAVE PERMISSIONS TO ACCESS THIS TAB",
    ["AssignedByDispatch"] = "Assigned by Dispatch",
    ["VehicleMods"] = "Vehicle modifications",
    ["Enabled"] = "Enabled",
    ["Disabled"] = "Disabled",

    ['camera'] = {
        ['takepick'] = 'Take picture', -- Only if you use origen_notify
        ['zoom'] = 'Zoom in/out', -- Only if you use origen_notify
        ['cancel'] = 'Cancel', -- Only if you use origen_notify
        ['fullText'] = '%s - Take Picture\n%s - Zoom\n%s - Cancel',
    },

    ['objects'] = {
        ['place'] = 'Place Object', -- Only if you use origen_notify
        ['prev'] = 'Previous Object', -- Only if you use origen_notify
        ['next'] = 'Next Object', -- Only if you use origen_notify
        ['cancel'] = 'Cancel', -- Only if you use origen_notify
        ['fullText'] = '%s -  Place Object\n%s - Previous Object\n%s - Next Object\n%s - Cancel',
    },

    ['CollectEvidence'] = 'Collect evidence',
    ['ClearEvidence'] = 'Clear evidence',
    ['EnterPlate'] = 'Enter the plate',
    ['ImpoundedVehicles'] = 'Impounded vehicles',
    ['RequestVeh'] = 'Request vehicle',
    ['Heliport'] = 'Heliport',
    ['TakeOutHeli'] = 'Take out helicopter',
    ['Pier'] = 'Pier',
    ['TakeOutBoat'] = 'Take out boat',
    ['ConfiscateVehicle'] = 'Confiscated vehicle',
    ['PoliceFacilities'] = 'Police facilities',
    ['Confiscated'] = 'Confiscated',
    ['k9Attack'] = 'Attack',
    ['ClosestAlert'] = "You're very near to the alert",

    -- Commands
    ['OpenPoliceCad'] = 'Open police cad',
    ['VehicleRadar'] = 'Vehicle radar',
    ['LockRadar'] = 'Lock radar',
    ['MoveRadar'] = 'Move radar',
    ['NextAlert'] = 'Next dispatch alert',
    ['PreviousAlert'] = 'Previous dispatch alert',
    ['K9Menu'] = 'K9 Control Menu',
    ['SirensKey'] = 'Activate sirens',
    ['LightsSirens'] = 'Activate lights',
    ['HandCuff'] = 'Police: Handcuff / Uncuff',
    ['QRR'] = 'Police: QRR',
    ['Ten20'] = 'Police: 10-20',
    ['Tackle'] = 'Police: Make a tackle',
    ['VehicleInto'] = 'Set ped into vehicle',
    ['QuickAccess'] = 'Open the quick access menu',
    ['Minimap'] = 'Minimap mode',
    ['TalkRadio'] = 'Talk on your radio',
    
    ['CantUseItem'] = "You can't use this item",

    ["InvalidVehicleToConfiscate"] = "Invalid vehicle to confiscate",
    ["TowTruckOnWay"] = "A tow truck is on its way",
    ["TowTruckArrived"] = "The tow truck has arrived and the vehicle has been loaded.",
    ["VehicleCannotBeFound"] = "The vehicle cannot be found...",
    ['NoMoney'] = "You don't have enough money.",
    ['PoliceBill'] = 'Police bill',

    -- Tablet 

    ["Home"] = "HOME",
    ["Dispatch"] = "DISPATCH",
    ["CitizenSearch"] = "CITIZEN SEARCH",
    ["Reports"] = "REPORTS",
    ["Cameras"] = "CAMERAS",
    ["Polices"] = "POLICES",
    ["Vehicles"] = "VEHICLES",
    ["CriminalCode"] = "CRIMINAL CODE",
    ["CriminalCodeAbrev"] = "C. CRIMINAL",
    ["SearchCapture"] = "SEARCH AND CAPTURE",
    ["Debtors"] = "DEBTORS",
    ["FederalManagement"] = "FEDERAL MANAGEMENT",
    ["AgentManagement"] = "AGENT MANAGEMENT",
    ["SecurityCameras"] = "SECURITY CAMERAS",
    ["PublicServices"] = "PUBLIC SERVICES",
    ["NoAmbulanceDuty"] = "NO PARAMEDIC ON DUTY",
    ["NoPoliceDuty"] = "NO AGENT ON DUTY",
    ["PoliceOnDuty"] = "AGENT ON DUTY",
    ["PoliceSOnDuty"] = "AGENTS ON DUTY",
    ["OutDuty"] = "OUT OF DUTY",
    ["InDuty"] = "IN DUTY",
    ["TimeControl"] = "TIME CONTROL",
    ["Radio"] = "RADIO",
    ["Duty"] = "DUTY",
    ["WelcomeTitle"] = "WELCOME TO THE INTERNAL POLICE NETWORK",
    ["WelcomeTitleAmbulance"] = "WELCOME TO THE INTERNAL PAREMEDIC NETWORK",
    ["WelcomeDescAmbulance"] = "Welcome to the official paramedic application. This application has been designed to help improve efficiency and communication in the daily work of paramedics.",
    ["WelcomeDesc"] = "Welcome to the official police and sheriff application. This application has been designed to help improve efficiency and communication in the daily work of police officers and sheriffs.",
    ["NotInDuty"] = "YOU ARE NOT IN DUTY",
    ["AgentsOnDuty"] = "AGENTS ON DUTY",
    ["DeptAbrev"] = "DEPT.",
    ["Rank"] = "RANK",
    ["Agent"] = "AGENT",
    ["Status"] = "STATUS",
    ["LocAbrev"] = "LOC.",
    ["BroadcastSAFD"] = "BROADCAST SAFD",
    ["BroadcastSapd"] = "BROADCAST SAPD",
    ["SouthUnits"] = "SOUTH UNITS",
    ["Talk"] = "TALK",
    ["NorthUnits"] = "NORTH UNITS",
    ["SpecialUnits"] = "SPECIAL UNITS",
    ["EMSUnits"] = "EMS UNITS",
    ["NoUsersChannel"] = "NO USERS IN THIS CHANNEL",
    ["Available"] = "AVAILABLE",
    ["NotAvailable"] = "NOT AVAILABLE",
    ["InternalRadio"] = "INTERNAL RADIO",
    ["TypeMessage"] = "TYPE YOUR MESSAGE...",
    ["Emergencies"] = "EMERGENCIES",
    ["Notice"] = "NOTICE",
    ["Title"] = "TITLE",
    ["Location"] = "LOCATION",
    ["Time"] = "TIME",
    ["DetailedDesc"] = "DETAILED DESCRIPTION",
    ["Notes"] = "NOTES",
    ["AddNoteToEmergency"] = "ADD NOTE TO EMERGENCY",
    ["SaveNote"] = "SAVE NOTE",
    ["SendToUnit"] = "SEND TO UNIT",
    ["AvailableUnits"] = "AVAILABLE UNITS",
    ["LastAlerts"] = "LAST ALERTS",
    ["RefAbrev"] = "REF.",
    ["Emergency"] = "EMERGENCY",
    ["Ago"] = "AGO:",
    ["Units"] = "UNITS",
    ["NoRecived"] = "NO ALERTS RECEIVED",
    ["DeleteAlert"] = "DELETE ALERT",
    ["TimeHistory"] = "TIME HISTORY",
    ["Agent"] = "AGENT",
    ["ClockIn"] = "CLOCK IN",
    ["ClockOut"] = "CLOCK OUT",
    ["Total"] = "TOTAL",
    ["ShowingRecords"] = "Showing records from START to END of a total of TOTAL records",
    ["TopWorkers"] = "TOP WORKERS",
    ["MinAbrev"] = "min",
    ["Cancel"] = "Cancel",
    ["sProcessing"] = "Processing...",
    ["sLengthMenu"] = "Showing 20 records",
    ["sZeroRecords"] = "No results found",
    ["sEmptyTable"] = "No data available in table",
    ["sInfo"] = "Showing records from START to END of a total of TOTAL records",
    ["sInfoEmpty"] = "Showing records from 0 to 0 of a total of 0 records",
    ["sInfoFiltered"] = "(filtering from a total of MAX records)",
    ["sSearch"] = "Search:",
    ["sLoadingRecords"] = "Loading...",
    ["oPaginateFirst"] = "First",
    ["oPaginateLast"] = "Last",
    ["oPaginateNext"] = "Next",
    ["oPaginatePrevious"] = "Previous",
    ["sSortAscending"] = "] = Activate to sort column ascending",
    ["sSortDescending"] = "] = Activate to sort column descending",
    ["Citizens"] = "CITIZENS",
    ["CitizenSearch"] = "CITIZEN SEARCH",
    ["CitizenList"] = "CITIZEN LIST",
    ["SearchCitizen"] = "Search for a citizen...",
    ["PerformSearch"] = "PERFORM A SEARCH TO SHOW RESULTS",
    ["CitizenProfile"] = "CITIZEN PROFILE",
    ["SelectACitizen"] = "SELECT A CITIZEN TO LOAD INFORMATION",
    ["Name"] = "NAME",
    ["Surname"] = "SURNAME",
    ["Gender"] = "GENDER",
    ["Nationality"] = "NATIONALITY",
    ["Birthdate"] = "BIRTHDATE",
    ["Id"] = "ID",
    ["PhoneNumber"] = "PHONE NUMBER",
    ["BankAccount"] = "BANK ACCOUNT NUMBER",
    ["Job"] = "JOB",
    ["InSearchCapture"] = "IN SEARCH AND CAPTURE",
    ["Dangerous"] = "DANGEROUS",
    ["Yes"] = "YES",
    ["No"] = "NO",
    ["NewNote"] = "NEW NOTE",
    ["NoRegisteredNotes"] = "NO NOTES REGISTERED",
    ["Fine"] = "FINES",
    ["AddFine"] = "ADD FINE",
    ["NoRegisteredFines"] = "No fines registered",
    ["NoData"] = "No data available",
    ["Licenses"] = "LICENSES",
    ["Weapons"] = "WEAPONS",
    ["Houses"] = "PROPERTIES",
    ["NoteTitle"] = "Note title",
    ["TextNote"] = "Note text",
    ["Save"] = "SAVE",
    ["More"] = "MORE",
    ["SearchCriminalCode"] = "Search in criminal code...",
    ["Article"] = "ARTICLE",
    ["Description"] = "DESCRIPTION",
    ["Amount"] = "AMOUNT",
    ["Sentence"] = "SENTENCE",
    ["Action"] = "ACTIONS",
    ["CustomFine"] = "CUSTOM FINE",
    ["FineConcepts"] = "FINE CONCEPTS",
    ["Concept"] = "CONCEPT",
    ["Add"] = "ADD",
    ["EnterConcept"] = "Add the fine concept",
    ["EnterAmount"] = "Add the amount",
    ["EnterSentence"] = "Add the sentence months",
    ["ProcessFine"] = "PROCESS FINE",
    ["TotalSentence"] = "TOTAL SENTENCE",
    ["Month"] = "MONTHS",
    ["TotalAmount"] = "TOTAL AMOUNT",
    ["ConfirmFine"] = "CONFIRM FINE",
    ["FineAdded"] = "The fine has been added successfully",
    ["NoArticle"] = "You haven't added any article to the fine",
    ["ReportList"] = "REPORT LIST",
    ["SearchReport"] = "Search a report...",
    ["AllTags"] = "All tags",
    ["NoResultFound"] = "No result found",
    ["NewReport"] = "NEW REPORT",
    ["SelectReport"] = "SELECT A REPORT TO LOAD INFORMATION",
    ["Report"] = "REPORT",
    ["VehicleList"] = "VEHICLE LIST",
    ["TypeLicense"] = "Type the license to search...",
    ["PerformSearchVehicle"] = "Perform a search to show results",
    ["VehicleData"] = "VEHICLE DATA",
    ["NewChapter"] = "NEW CHAPTER",
    ["NewArticle"] = "NEW ARTICLE",
    ["SearchCriminalCode"] = "Search in criminal code...",
    ["Delete"] = "DELETE",
    ["CreateNewChapter"] = "CREATE NEW CHAPTER",
    ["ChapterName"] = "Chapter name",
    ["SaveChapter"] = "SAVE CHAPTER",
    ["CreateNewArticle"] = "CREATE NEW ARTICLE",
    ["SelectChapter"] = "SELECT A CHAPTER",
    ["ArticleName"] = "ARTICLE NAME",
    ["EnterName"] = "Enter the article name",
    ["DescriptionArticle"] = "ARTICLE DESCRIPTION",
    ["EnterDescription"] = "Enter the article description",
    ["SaveArticle"] = "SAVE ARTICLE",
    ["DeleteArticle"] = "DELETE ARTICLE",
    ["AreYouSureDeleteArticle"] = "Are you sure you want to delete this article?",
    ["Remove"] = "REMOVE",
    ["DeleteChapter"] = "DELETE CHAPTER",
    ["AreYouSureDeleteArticle"] = "Are you sure you want to delete this chapter?",
    ["SubjectsInSearch"] = "PEOPLE IN SEARCH AND CAPTURE",
    ["NoSubjectsInSearch"] = "NO PEOPLE IN SEARCH AND CAPTURE",
    ["Close"] = "CLOSE",
    ["DebtSubjects"] = "DEBTORS",
    ["FindSubject"] = "Find a subject...",
    ["NoDebtors"] = "NO DEBTORS",
    ["FederalManagement"] = "FEDERAL MANAGEMENT",
    ["AddConden"] = "ADD A NEW CONDEMNATION",
    ["CitizenID"] = "Citizen ID",
    ["DangerousOrNot"] = "DANGEROUS OR NOT",
    ["NoFederals"] = "NO FEDERAL PRISONERS",
    ["SecurityCameras"] = "SECURITY CAMERAS",
    ["BusinessCameras"] = "BUSINESS CAMERAS",
    ["VehicleCameras"] = "VEHICLE CAMERAS",
    ["BodyCam"] = "BODYCAMS",
    ["Meters"] = "meters",
    ["Refresh"] = "REFRESH",
    ["SingleCamera"] = "CAMERA",
    ["PoliceManagement"] = "POLICE MANAGEMENT",
    ["PoliceList"] = "POLICE LIST",
    ["LookAgent"] = "Look for an agent...",
    ["GenerateBadge"] = "GENERATE BADGE",
    ["AddPolice"] = "ADD POLICE",
    ["Range"] = "RANGE",
    ["PlateAbrev"] = "PLATE NUMBER",
    ["Award"] = "AWARDS",
    ["AddAward"] = "ADD AWARD",
    ["NoDecorations"] = "NO DECORATIONS",
    ["Divisions"] = "DIVISIONS",
    ["SetDivision"] = "SET DIVISION",
    ["FirePolice"] = "FIRE POLICE",
    ["PoliceFile"] = "POLICE FILE",
    ["SelectAnAgent"] = "Select an agent to view their police file",
    ["NoRegisteredReports"] = "No reports registered",
    ["Jurisdiction"] = "JURISDICTION",
    ["Informs"] = "INFORMS",
    ["Atention"] = "ATTENTION",
    ["ThisActionCantRevert"] = "THIS ACTION WILL FIRE THE POLICE OFFICER. DO YOU WISH TO CONTINUE?",
    ["DoYouWishContinue"] = "DO YOU WISH TO CONTINUE?",
    ["Confirm"] = "CONFIRM",
    ["AddDivision"] = "ADD DIVISION",
    ["AddCondecoration"] = "ADD DECORATION",
    ["DoWantGenPlate"] = "DO YOU WANT TO GENERATE THIS BADGE?",
    ["YouMustOpenProfile"] = "YOU MUST HAVE AN AGENT'S PROFILE OPEN TO GENERATE THE BADGE",
    ["PoliceBadgeGenerated"] = "POLICE BADGE GENERATED",
    ["CheckInventory"] = "CHECK YOUR INVENTORY",
    ["NoPeopleNear"] = "NO PEOPLE NEARBY.",
    ["ConnectedTo"] = "CONNECTED TO",
    ["Disconnect"] = "DISCONNECT",
    ["ShortCuts"] = "SHORTCUTS",
    ["AlternateMute"] = "ALTERNATE MUTE",
    ["TalkToCentral"] = "TALK TO CENTRAL",
    ["TalkToWaiting"] = "TALK WHILE WAITING FOR ASSIGNMENT",
    ["TalkToPoliceSta"] = "TALK TO POLICE STATION",
    ["TalkToTacs"] = "TALK TO TACS",
    ["TalkSafd"] = "TALK TO SAFD",
    ["BroadcastSAPD"] = "BROADCAST IN SAPD",
    ["HowUploadImage"] = "HOW DO YOU WANT TO UPLOAD THE IMAGE?",
    ["Photo"] = "PHOTO",
    ["AddURL"] = "ADD URL",
    ["Default0Months"] = "0 Months",
    ["ChangePlateNumber"] = "CHANGE PLATE NUMBER",
    ["PlateNumberAbrev"] = "PLATE NUMBER",
    ["PlateMin3"] = "The plate must have a minimum of 4 characters",
    ["ReportName"] = "REPORT NAME",
    ["ReportID"] = "REPORT ID",
    ["DateAndHour"] = "DATE AND HOUR",
    ["AgentInCharge"] = "AGENT IN CHARGE",
    ["ReportDescription"] = "REPORT DESCRIPTION",
    ["EnterReportDesc"] = "Enter the report description",
    ["Evidences"] = "EVIDENCES",
    ["WithoutUbication"] = "Without assigned location",
    ["AddEvidence"] = "ADD EVIDENCE",
    ["PeopleInvolved"] = "PEOPLE INVOLVED",
    ["NoPeopleInvolved"] = "No people involved",
    ["AddPeople"] = "ADD PERSON",
    ["AgentsInvolved"] = "AGENTS INVOLVED",
    ['NoAgentsInvolved'] = 'There are no agents involved',
    ["AddAgent"] = "ADD AGENT",
    ["Tags"] = "TAGS",
    ["SelectLabel"] = "Select a label",
    ["Victims"] = "VICTIMS",
    ["AddVictim"] = "ADD VICTIM",
    ['NoVictimsInvolved'] = 'There are no victims involved',
    ["AddVehicle"] = "ADD VEHICLE",
    ['NoVehicleInvolved'] = 'No vehicles involved',
    ["DestroyReport"] = "DESTROY REPORT",
    ["seconds"] = "seconds",
    ["minutes"] = "minutes",
    ["NoResult"] = "No results found",
    ["RemainMonth"] = "Remaining months",
    ["ServingSentance"] = "Serving sentance",
    ["Release"] = "Release",
    ["Sleeping"] = "Sleeping",
    ["IntroduceName"] = "Enter a name",
    ["AlertAsigned"] = "The alert #%s has been assigned to %s",
    ['NoLocation'] = 'No assigned location',

    ["NoPermission"] = "You don't have permissions to access!",
    ["NoPermissionPage"] = "You don't have permission to access this page",
    ["MinimumCharacters"] = "You must introduce at least 3 characters",
    ["FindACitizen"] = "Find a citizen...",
    ["LookForAgent"] = "Look for agent...",
    ["EnterNameToSearch"] = "Enter a name in the search bar to display results",
    ["UnknownKey"] = "UNRECOGNISED KEY",

    ["RadarOf"] = "Radar of",
    ["Velocity"] = "Speed",
    ["LicensePlate"] = "Plate",
    ["TrafficStop"] = "Traffic stop",
    ["SpeedReduction"] = "Speed reduction",
    ["Color"] = "Color",
    ["NoRadio"] = "You don't have a radio!",
    ["NoUsers"] = "There are no users on this channel",
    ["NoPermissionMoveUsers"] = "YOU DON'T HAVE PERMISSIONS TO MOVE USERS IN RADIO",
    ["ChangeRange"] = "Change range",
    ["Phone"] = "Phone",
    ["Model"] = "Model",
    ["Owner"] = "Owner",
    ["SearchAndCapture"] = "Search and capture",
    ["VehicleAnnotations"] = "Vehicle annotations",
    ["EnterAnnotation"] = "Enter some annotation...",
    ["VehicleNotFound"] = "Vehicle not found",
    ["VehicleSearchUpdated"] = "Vehicle search has been updated",
    ["VehicleDescriptionUpdated"] = "Vehicle description has been updated",
    ["NoPermissionsConfigured"] = "has no permissions configured",

    ["Operations"] = "Operations",
    ["ShapesCreated"] = "Shapes created",
    ["NoShapes"] = "No shapes created",
    ["TitleTooShort"] = "Name must be more than 5 characters.",
    ["DeleteShape"] = "Delete shape",
    ["ConfirmDeleteShape"] = "Are you sure you want to delete this shape?",
    ["CreateNewShape"] = "CREATE A NEW SHAPE",
    ['SelectReport'] = 'Select a report to load your information',
    ['NoEvidences'] = 'There is no evidence in your inventory',
    ['OpenCase'] = 'Open case',
    ['CaseClosed'] = 'Case closed',
    ['NullCase'] = 'Null case',
    ["ThisActionRemoveEvidence"] = "This action will remove evidence definitively.",
    ["DoYouWantContinue"] = "Do you want to continue?",
    ['ThisActionEliminateReport'] = 'This action will eliminate the report permanently, including the evidence attached to it.',
    ['ThisWillAffectFines'] = 'This will not affect the fines, which will remain in the system.',
    ['TotalPenalty'] = 'Total penalty',
    ['TotalAmount'] = 'Total amount',
    ['SendFine'] = 'FINE',
    ['EnterPlateEngine'] = 'Enter a plate in the search engine to show the results',
    
    ['SelectCitizen'] = 'Select a citizen',
    ['EnterURLImage'] = 'Enter the URL of the image',
    ['SaveImage'] = 'Save image',
    ['SelectAnAgent2'] = 'Select an agent',
    ['SelectVehicle'] = 'Select a vehicle',
    ['EnterNameEngine'] = 'Introduces a name to the search engine to show the results',
    ['FindVehicles'] = 'Find vehicles...',
    ['SelectEvidence'] = 'Select evidence',
    ['NoEvidenceInv'] = 'There is no evidence in your inventory',

    ["AddLicense"] = "ADD LICENSE",
    ["AddNewLicense"] = "ADD NEW LICENSE",
    ['Expiration'] = 'Expiration',
    ['AddedLicense'] = 'You have added a license',

    ['ReferencesLocation'] = 'References Location',
    ['BodyCamera'] = 'Body Camera',
    ['Animation'] = 'Animation',
    ['NoSignal'] = 'WITHOUT SIGNAL',

    ['LowCaliber'] = 'Low caliber',
    ['ShotgunCaliber'] = 'Shogun caliber',
    ['MediumCaliber'] = 'Medium caliber',
    ['HighCaliber'] = 'High caliber',

    ['LicensesList'] = {
        ['Driver'] = 'Driver',
        ['Weapon'] = 'Weapon',
        ['Business'] = 'Business',
    },
    
    -- Condecorations
    ["Condecorations"] = {
        Valor = {
            id = 'Valor',
            name = 'Medal to value',
            description =
                'The medal to value is the highest application of the law granted to the officers and is awarded by individual acts of extraordinary courage or heroism carried out in the fulfillment of duty with an extremely and potentially deadly personal risk',
            url = 'vV0Wm9A.png'
        },
        Preservacion = {
            id = 'Preservacion',
            name = 'Life preservation medal',
            description =
                'The medal of preservation of life can be awarded to an officer who has distinguished himself by using exceptional tactics and exercising a good judgment, beyond the normal demands of duty, to preserve the life of another during a volatile or dangerous encounter while protectingthe security and security of the public and their officials officers',
            url = '4Zmnp8u.png'
        },
        Policia = {
            id = 'Policia',
            name = 'Police Medal',
            description =
                'The Police Medal is a prize for courage, generally awarded to officers for individual acts of heroism in the fulfillment of duty, although not beyond the call of duty, as required for the medal to the value',
            url = 'BwPTQWC.png'
        },
        Estrella = {
            id = 'Estrella',
            name = 'Police Star',
            description =
                'The police star is a prize for courage, generally awarded to officers for performing with an exceptional trial and/or using business tactics to deactivate dangerous and stressful situations',
            url = 'U4vBD1Z.png'
        },
        Salvavidas = {
            id = 'Lifejacket',
            name = 'Police lifeguard medal',
            description =
                'The Police Rescue Medal is a prize for courage, generally awarded to officers to take measures to rescue or try to rescue an official partner or any person from an imminent danger',
            url = 'TuL7fDQ.png'
        },
        Distinguido = {
            id = 'Distinguido',
            name = 'Distinguished Police Service Medal',
            description =
                'The distinguished police service medal is the highest service for the department and can be awarded to employees to distinguish an exceptional service in a duty of great responsibility or critical importance for the application of the law',
            url = 'rojxaCL.png'
        },
        Meritorio = {
            id = 'Meritorio',
            name = 'Police meritorious service medal',
            description =
                'The police for the police service medal is awarded to employees to distinguish an exceptional service in a duty of great responsibility or critical importance for compliance with the law, but to a lesser extent than that required for the service medalPolice distinguished',
            url = 'cHAlfOj.png'
        },
        LogroMeritorio = {
            id = 'LogroMeritorio',
            name = 'MERRIOUS ACHIEVEMENT OF THE POLICE',
            description =
                'The police achievement medal is mainly designed to recognize civilian employees.The medal is awarded for sustained, long -term and notable achievements or for a single significant and notable achievement in the execution of administrative, office or artisanal tasks',
            url = 'laujeQV.png'
        },
        DistinguidoComision = {
            id = 'DistinguidoComision',
            name = 'Distinguished Service Medal of the Police Commission',
            description =
                'The Distinguished Service Medal of the Police Commission is awarded to the officers to distinguish themselves for performing an exceptional service to the SAPD or performing in a stressful emergency situation with good judgment and courage',
            url = 'YCOtC5l.png'
        },
        IntegridadComision = {
            id = 'IntegridadComision',
            name = 'Integrity Medal of the Police Commission',
            description =
                'The Police Commission Integrity Medal is awarded to employees who show an act of exemplary integrity, especially when that act requires exceptional character, strength and moral courage against substantial obstacles',
            url = 'Ia6hPav.png'
        },
        Comunitaria = {
            id = 'Comunitaria',
            name = 'Community Police Medal',
            description =
                'The Community Police Medal is awarded to personnel who have solved an important community problem, including the community in the problem solving process and/or shown a commitment to SAPD community police philosophy',
            url = 'bDkoKfS.png'
        },
        RelacionesHumanas = {
            id = 'RelacionesHumanas',
            name = 'Human Relations Medal',
            description =
                'The Human Relations Medal is awarded to officers who have shown great compassion in their daily activities and have gone beyond the call of duty in their response to other human beings',
            url = 'IMlJLE4.png'
        },
        Service2 = {
            id = 'Service2',
            name = 'Servicio durante 2 meses',
            description =
                'This award is delivered to those members who has made a service of San Andreas exceeding 2 months in the San Andreas Police',
            url = '22OMcKF.png'
        },
        Service4 = {
            id = 'Service4',
            name = 'Service for 4 months',
            description =
                'This award is delivered to those members who has made a service of San Andreas exceeding 4 months in the San Andreas Police',
            url = 'waOO0p1.png'
        },
        Service6 = {
            id = 'Service6',
            name = 'Service for 6 months',
            description =
                'This award is delivered to those members who has made a service of San Andreas exceeding 6 months',
            url = 'zw1TPMg.png'
        },
        Service8 = {
            id = 'Service8',
            name = 'Service for 8 months',
            description =
                'This award is delivered to those members who has made a service of San Andreas exceeding 8 months',
            url = 'oVvluyF.png'
        },
        Service10 = {
            id = 'Service10',
            name = 'Service for 10 months',
            description =
                'This award is delivered to those members who has made a service of San Andreas exceeding 10 months',
            url = '9E01TG1.png'
        },
        Service12 = {
            id = 'Service12',
            name = 'Service for 12 months',
            description =
                'This decoration is delivered to those members who has made a service of San Andreas exceeds more than one year',
            url = 'FTz1dTx.png'
        },
        Lifejacket = {
            id = 'Lifejacket',
            name = 'Police lifeguard medal',
            description =
                'The Police Rescue Medal is a prize for courage, generally awarded to officers to take measures to rescue or try to rescue an official partner or any person from an imminent danger',
            url = 'TuL7fDQ.png'
        },
    },
    -- Divisions
    ["DivisionsData"] = {
        IAA = {
            id = 'IAA',
            name = 'Internal Affairs',
            url = 't764YV8.png'
        },
        FIB = {
            id = 'FIB',
            name = 'Federal Investigation Bureau (FIB)',
            url = 'BtEEw1S.png'
        },
        SWAT = {
            id = 'SWAT',
            name = 'Special Weapons And Tactics (SWAT)',
            url = 'v4dW751.png'
        },
        HPD = {
            id = 'HPD',
            name = 'Highway Patrol Division (HPD)',
            url = 'scWMKjL.png'
        },
        IRD = {
            id = 'IRD',
            name = 'Instruction and Recruitment Department (IRD)',
            url = 'OCEBbrB.png'
        },
        UNP = {
            id = 'UNP',
            name = 'Police negotiation unit (PNU)',
            url = 'DlGNQiV.png'
        },
        UM = {
            id = 'UM',
            name = 'Maritime unit (MU)',
            url = 'DlGNQiV.png'
        }
    },
}

LogsTranslations = {
    ['Identifiers'] = 'IDENTIFIERS',
    ['ID'] = 'ID',
    ['Name'] = 'Name',
    
    ['Alert'] = {
        title = 'Alert sended',
        message = 'An alert has been sent with the command `%s`.\nMessage: %s',
    },
    ['ClockOut'] = {
        title = 'Clock Out',
        message = 'The player has ended the service.\nClockin: `%s`\nClockout: `%s`\nTotal: `%s` minutes',
    },
    ['CreateShape'] = {
        title = 'Created shape',
        message = 'The player created a shape.\nName: `%s`',
    },
    ['DeleteShape'] = {
        title = 'Deleted shape',
        message = 'The player deleted a shape.\nName: `%s`',
    },

    ['CreateNote'] = {
        title = 'Created note',
        message = 'The player created a note.\nTitle: `%s`\nDescription: `%s`\nAutor: `%s`',
    },
    ['DeleteNote'] = {
        title = 'Deleted note',
        message = 'The player deleted a note.\nNoteID: `%s`',
    },

    ['CreateReport'] = {
        title = 'Created report',
        message = 'The player created a report.\nTitle: `%s`\nDescription: `%s`\nAutor: `%s`',
    },
    ['DeleteReport'] = {
        title = 'Deleted report',
        message = 'The player deleted a report.\nNoteID: `%s`',
    },
    ['SetBadge'] = {
        title = 'Set police badge',
        message = 'The player set a police badge.\nOfficer: `%s`\nBadge: `%s`',
    },
    ['FirePolice'] = {
        title = 'Police fired',
        message = 'The player fire a police.\nOfficer: `%s`',
    },
    ['HirePolice'] = {
        title = 'Police hired',
        message = 'The player hired a player.\nName: `%s`\nJob: `%s`\nGrade: `%s`',
    },
    ['UpdatePlayer'] = {
        title = 'Player updated',
        message = 'The player fire/hired a player.\nPlayer identifier: `%s`\nJob: `%s`\nGrade: `%s`',
    },

    ['NewPhoto'] = {
        title = 'New photo',
        message = 'The player took a new photo.',
    },

    ['EnterFederal'] = {
        title = 'Enter to federal',
        message = 'The officer has sent a prisoner to federal.\nTime: `%s minutes`\nOfficer: `%s`',
    },

    ['ExitFederal'] = {
        title = 'Left the federal',
        message = 'The player has been released.',
    },

    ['AddBill'] = {
        title = 'Added bill',
        message = 'A new bill has been created.\n\nOfficer: `%s`\nAmount: `%s`\nMonths: `%s`\nConcepts: %s',
    },
    ['PayBill'] = {
        title = 'Bill paid',
        message = 'The player has paid a bill.\nAmount: `%s`\nBill ID: `%s`',
    },

    ['UseBodyCam'] = {
        title = 'Bodycam used',
        message = 'The player has used a cam.\nCamera name: `%s`',
    },

    ['UseCarCam'] = {
        title = 'Vehicle camera used',
        message = 'The player has used a cam.\nCar plate: `%s`',
    },
}