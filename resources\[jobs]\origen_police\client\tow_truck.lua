local L0_1, L1_1, L2_1, L3_1
function L0_1(A0_2)
  local L1_2, L2_2
  while true do
    L1_2 = HasAnimDictLoaded
    L2_2 = A0_2
    L1_2 = L1_2(L2_2)
    if L1_2 then
      break
    end
    L1_2 = RequestAnimDict
    L2_2 = A0_2
    L1_2(L2_2)
    L1_2 = Citizen
    L1_2 = L1_2.Wait
    L2_2 = 0
    L1_2(L2_2)
  end
end
L1_1 = AddEventHandler
L2_1 = "origen_police:request_tow_truck"
function L3_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2
  L0_2 = GetVehicleInCamera
  L0_2 = L0_2()
  L1_2 = GetEntityCoords
  L2_2 = L0_2
  L1_2 = L1_2(L2_2)
  if 0 ~= L0_2 then
    L2_2 = GetEntityCoords
    L3_2 = PlayerPedId
    L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2 = L3_2()
    L2_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
    L2_2 = L1_2 - L2_2
    L2_2 = #L2_2
    if L2_2 < 5 then
      L2_2 = IsEntityPositionFrozen
      L3_2 = L0_2
      L2_2 = L2_2(L3_2)
      if L2_2 then
        L2_2 = ShowNotification
        L3_2 = Config
        L3_2 = L3_2.Translations
        L3_2 = L3_2.InvalidVehicleToConfiscate
        L2_2(L3_2)
        return
      end
      L2_2 = 0
      while true do
        L3_2 = NetworkHasControlOfEntity
        L4_2 = L0_2
        L3_2 = L3_2(L4_2)
        if not (not L3_2 and L2_2 < 100) then
          break
        end
        L3_2 = DoesEntityExist
        L4_2 = L0_2
        L3_2 = L3_2(L4_2)
        if not L3_2 then
          break
        end
        L3_2 = Citizen
        L3_2 = L3_2.Wait
        L4_2 = 100
        L3_2(L4_2)
        L3_2 = NetworkRequestControlOfEntity
        L4_2 = L0_2
        L3_2(L4_2)
        L2_2 = L2_2 + 1
      end
      L3_2 = DoesEntityExist
      L4_2 = L0_2
      L3_2 = L3_2(L4_2)
      if L3_2 then
        L3_2 = NetworkHasControlOfEntity
        L4_2 = L0_2
        L3_2 = L3_2(L4_2)
        if L3_2 then
          L3_2 = PlayerPedId
          L3_2 = L3_2()
          ped = L3_2
          L3_2 = GetEntityCoords
          L4_2 = ped
          L3_2 = L3_2(L4_2)
          pedCoords = L3_2
          L3_2 = GetOffsetFromEntityInWorldCoords
          L4_2 = ped
          L5_2 = 0.0
          L6_2 = 5.0
          L7_2 = 0.0
          L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2)
          L4_2 = GetTowTruck
          L5_2 = L0_2
          L4_2(L5_2)
          L4_2 = TowTruckDrivers
          L5_2 = math
          L5_2 = L5_2.random
          L6_2 = TowTruckDrivers
          L6_2 = #L6_2
          L5_2 = L5_2(L6_2)
          L4_2 = L4_2[L5_2]
          L5_2 = GetHashKey
          L6_2 = L4_2
          L5_2 = L5_2(L6_2)
          L6_2 = GetHashKey
          L7_2 = towTruckModelsPick
          L7_2 = L7_2.model
          L6_2 = L6_2(L7_2)
          L7_2 = RequestModel
          L8_2 = L5_2
          L7_2(L8_2)
          L7_2 = RequestModel
          L8_2 = L6_2
          L7_2(L8_2)
          while true do
            L7_2 = HasModelLoaded
            L8_2 = L5_2
            L7_2 = L7_2(L8_2)
            if not L7_2 then
              L7_2 = RequestModel
              L8_2 = L5_2
              L7_2 = L7_2(L8_2)
              if L7_2 then
                goto lbl_117
              end
            end
            L7_2 = HasModelLoaded
            L8_2 = L6_2
            L7_2 = L7_2(L8_2)
            if L7_2 then
              break
            end
            L7_2 = RequestModel
            L8_2 = L6_2
            L7_2 = L7_2(L8_2)
            if not L7_2 then
              break
            end
            ::lbl_117::
            L7_2 = RequestModel
            L8_2 = L5_2
            L7_2(L8_2)
            L7_2 = RequestModel
            L8_2 = L6_2
            L7_2(L8_2)
            L7_2 = Citizen
            L7_2 = L7_2.Wait
            L8_2 = 0
            L7_2(L8_2)
          end
          L7_2 = DoesEntityExist
          L8_2 = L0_2
          L7_2 = L7_2(L8_2)
          if L7_2 then
            L7_2 = DoesEntityExist
            L8_2 = towTruck
            L7_2 = L7_2(L8_2)
            if L7_2 then
              L7_2 = DeleteTowTruck
              L8_2 = towTruck
              L9_2 = towTruckDriver
              L7_2(L8_2, L9_2)
            end
            L7_2 = SpawnTowTruck
            L8_2 = pedCoords
            L8_2 = L8_2.x
            L9_2 = pedCoords
            L9_2 = L9_2.y
            L10_2 = pedCoords
            L10_2 = L10_2.z
            L11_2 = L6_2
            L12_2 = L5_2
            L7_2(L8_2, L9_2, L10_2, L11_2, L12_2)
            L7_2 = PlayRadioAnim
            L8_2 = ped
            L7_2(L8_2)
            L7_2 = GoToTarget
            L8_2 = L1_2.x
            L9_2 = L1_2.y
            L10_2 = L1_2.z
            L11_2 = towTruck
            L12_2 = towTruckDriver
            L13_2 = L6_2
            L14_2 = L0_2
            L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
          end
        end
      end
    end
  end
end
L1_1(L2_1, L3_1)
function L1_1(A0_2, A1_2, A2_2, A3_2, A4_2)
  local L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  L5_2 = GetClosestVehicleNodeWithHeading
  L6_2 = SpawnRadius
  L6_2 = A0_2 + L6_2
  L7_2 = SpawnRadius
  L7_2 = A1_2 + L7_2
  L8_2 = A2_2
  L9_2 = 0
  L10_2 = 3
  L11_2 = 0
  L5_2, L6_2, L7_2 = L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2)
  if L5_2 then
    L8_2 = HasModelLoaded
    L9_2 = A3_2
    L8_2 = L8_2(L9_2)
    if L8_2 then
      L8_2 = HasModelLoaded
      L9_2 = A3_2
      L8_2 = L8_2(L9_2)
      if L8_2 then
        L8_2 = CreateVehicle
        L9_2 = A3_2
        L10_2 = L6_2
        L11_2 = L7_2
        L12_2 = true
        L13_2 = false
        L8_2 = L8_2(L9_2, L10_2, L11_2, L12_2, L13_2)
        towTruck = L8_2
        L8_2 = ClearAreaOfVehicles
        L9_2 = GetEntityCoords
        L10_2 = towTruck
        L9_2 = L9_2(L10_2)
        L10_2 = 5000
        L11_2 = false
        L12_2 = false
        L13_2 = false
        L14_2 = false
        L15_2 = false
        L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
        L8_2 = SetVehicleOnGroundProperly
        L9_2 = towTruck
        L8_2(L9_2)
        L8_2 = CreatePedInsideVehicle
        L9_2 = towTruck
        L10_2 = 26
        L11_2 = A4_2
        L12_2 = -1
        L13_2 = true
        L14_2 = false
        L8_2 = L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
        towTruckDriver = L8_2
        L8_2 = AddBlipForEntity
        L9_2 = towTruck
        L8_2 = L8_2(L9_2)
        towTruckBlip = L8_2
        L8_2 = SetBlipSprite
        L9_2 = towTruckBlip
        L10_2 = 613
        L8_2(L9_2, L10_2)
        L8_2 = SetBlipFlashes
        L9_2 = towTruckBlip
        L10_2 = true
        L8_2(L9_2, L10_2)
        L8_2 = SetBlipColour
        L9_2 = towTruckBlip
        L10_2 = 29
        L8_2(L9_2, L10_2)
      end
    end
  end
end
SpawnTowTruck = L1_1
function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2
  L2_2 = SetEntityAsMissionEntity
  L3_2 = A0_2
  L4_2 = false
  L5_2 = false
  L2_2(L3_2, L4_2, L5_2)
  L2_2 = DeleteEntity
  L3_2 = A0_2
  L2_2(L3_2)
  L2_2 = SetEntityAsMissionEntity
  L3_2 = A1_2
  L4_2 = false
  L5_2 = false
  L2_2(L3_2, L4_2, L5_2)
  L2_2 = DeleteEntity
  L3_2 = A1_2
  L2_2(L3_2)
  L2_2 = RemoveBlip
  L3_2 = towTruckBlip
  L2_2(L3_2)
end
DeleteTowTruck = L1_1
function L1_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2)
  local L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  L7_2 = TaskVehicleDriveToCoord
  L8_2 = A4_2
  L9_2 = A3_2
  L10_2 = A0_2
  L11_2 = A1_2
  L12_2 = A2_2
  L13_2 = 17.0
  L14_2 = 0
  L15_2 = A5_2
  L16_2 = DrivingStyle
  L17_2 = 1
  L18_2 = true
  L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
  L7_2 = ShowNotification
  L8_2 = Config
  L8_2 = L8_2.Translations
  L8_2 = L8_2.TowTruckOnWay
  L7_2(L8_2)
  enroute = true
  while true do
    L7_2 = enroute
    if true ~= L7_2 then
      break
    end
    L7_2 = Citizen
    L7_2 = L7_2.Wait
    L8_2 = 500
    L7_2(L8_2)
    L7_2 = GetEntityCoords
    L8_2 = A6_2
    L7_2 = L7_2(L8_2)
    L8_2 = GetEntityCoords
    L9_2 = A3_2
    L8_2 = L8_2(L9_2)
    L7_2 = L7_2 - L8_2
    L7_2 = #L7_2
    if L7_2 < 20 then
      L7_2 = Citizen
      L7_2 = L7_2.Wait
      L8_2 = 5000
      L7_2(L8_2)
      L7_2 = PickupTarget
      L8_2 = A3_2
      L9_2 = A4_2
      L10_2 = A6_2
      L7_2(L8_2, L9_2, L10_2)
      break
    end
  end
end
GoToTarget = L1_1
function L1_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  enroute = false
  L3_2 = AttachEntityToEntity
  L4_2 = A2_2
  L5_2 = A0_2
  L6_2 = 20
  L7_2 = towTruckModelsPick
  L7_2 = L7_2.offset
  L7_2 = L7_2.x
  L8_2 = towTruckModelsPick
  L8_2 = L8_2.offset
  L8_2 = L8_2.y
  L9_2 = towTruckModelsPick
  L9_2 = L9_2.offset
  L9_2 = L9_2.z
  L10_2 = 0.0
  L11_2 = 0.0
  L12_2 = 0.0
  L13_2 = false
  L14_2 = false
  L15_2 = false
  L16_2 = false
  L17_2 = 20
  L18_2 = true
  L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
  L3_2 = ShowNotification
  L4_2 = Config
  L4_2 = L4_2.Translations
  L4_2 = L4_2.TowTruckArrived
  L3_2(L4_2)
  L3_2 = Citizen
  L3_2 = L3_2.Wait
  L4_2 = 5000
  L3_2(L4_2)
  L3_2 = SetVehicleDoorsShut
  L4_2 = A0_2
  L5_2 = false
  L3_2(L4_2, L5_2)
  L3_2 = StartVehicleHorn
  L4_2 = A0_2
  L5_2 = 100
  L6_2 = 0
  L7_2 = false
  L3_2(L4_2, L5_2, L6_2, L7_2)
  L3_2 = TaskVehicleDriveToCoord
  L4_2 = A1_2
  L5_2 = A0_2
  L6_2 = TowTruckDestination
  L7_2 = 17.0
  L8_2 = 0
  L9_2 = truckhash
  L10_2 = DrivingStyle
  L11_2 = 10
  L12_2 = true
  L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L3_2 = SetEntityAsNoLongerNeeded
  L4_2 = A2_2
  L3_2(L4_2)
  L3_2 = SetEntityAsNoLongerNeeded
  L4_2 = A0_2
  L3_2(L4_2)
  L3_2 = SetPedAsNoLongerNeeded
  L4_2 = A1_2
  L3_2(L4_2)
  L3_2 = RemoveBlip
  L4_2 = towTruckBlip
  L3_2(L4_2)
  while true do
    L3_2 = DoesEntityExist
    L4_2 = A0_2
    L3_2 = L3_2(L4_2)
    if not L3_2 then
      break
    end
    L3_2 = DoesEntityExist
    L4_2 = A1_2
    L3_2 = L3_2(L4_2)
    if not L3_2 then
      break
    end
    L3_2 = DoesEntityExist
    L4_2 = A2_2
    L3_2 = L3_2(L4_2)
    if not L3_2 then
      break
    end
    L3_2 = GetEntityCoords
    L4_2 = A0_2
    L3_2 = L3_2(L4_2)
    L4_2 = TowTruckDestination
    L3_2 = L3_2 - L4_2
    L3_2 = #L3_2
    if L3_2 < 15 then
      L3_2 = DeleteEntity
      L4_2 = A0_2
      L3_2(L4_2)
      L3_2 = DeleteEntity
      L4_2 = A1_2
      L3_2(L4_2)
      L3_2 = DeleteEntity
      L4_2 = A2_2
      L3_2(L4_2)
      break
    end
    L3_2 = Wait
    L4_2 = 500
    L3_2(L4_2)
  end
  towTruck = nil
  towTruckDriver = nil
  targetVeh = nil
end
PickupTarget = L1_1
function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2
  L2_2 = IsPedSittingInAnyVehicle
  L3_2 = A0_2
  L2_2 = L2_2(L3_2)
  if L2_2 then
    L2_2 = GetVehiclePedIsIn
    L3_2 = A0_2
    L4_2 = false
    L2_2 = L2_2(L3_2, L4_2)
    towedVehicle = L2_2
  else
    L2_2 = GetVehicleInDirection
    L3_2 = GetEntityCoords
    L4_2 = A0_2
    L3_2 = L3_2(L4_2)
    L4_2 = A1_2
    L2_2 = L2_2(L3_2, L4_2)
    towedVehicle = L2_2
  end
  L2_2 = DoesEntityExist
  L3_2 = towedVehicle
  L2_2 = L2_2(L3_2)
  if L2_2 then
    L2_2 = towedVehicle
    return L2_2
  else
    L2_2 = ShowNotification
    L3_2 = Config
    L3_2 = L3_2.Translations
    L3_2 = L3_2.VehicleCannotBeFound
    L4_2 = "error"
    L2_2(L3_2, L4_2)
  end
end
GetTargetVehicle = L1_1
function L1_1(A0_2)
  local L1_2, L2_2
  L1_2 = GetVehicleClass
  L2_2 = A0_2
  L1_2 = L1_2(L2_2)
  targetVehClass = L1_2
  L1_2 = targetVehClass
  if 13 ~= L1_2 then
    L1_2 = targetVehClass
    if 8 ~= L1_2 then
      goto lbl_15
    end
  end
  L1_2 = TowTruckModels
  L1_2 = L1_2.boxtrucks
  towTruckModelsPick = L1_2
  goto lbl_18
  ::lbl_15::
  L1_2 = TowTruckModels
  L1_2 = L1_2.flatbeds
  towTruckModelsPick = L1_2
  ::lbl_18::
end
GetTowTruck = L1_1
function L1_1(A0_2)
  local L1_2, L2_2
  L1_2 = Citizen
  L1_2 = L1_2.CreateThread
  function L2_2()
    local L0_3, L1_3, L2_3, L3_3, L4_3, L5_3, L6_3, L7_3, L8_3, L9_3, L10_3, L11_3
    L0_3 = RequestAnimDict
    L1_3 = arrests
    L0_3(L1_3)
    L0_3 = TaskPlayAnim
    L1_3 = A0_2
    L2_3 = "random@arrests"
    L3_3 = "generic_radio_enter"
    L4_3 = 1.5
    L5_3 = 2.0
    L6_3 = -1
    L7_3 = 50
    L8_3 = 2.0
    L9_3 = 0
    L10_3 = 0
    L11_3 = 0
    L0_3(L1_3, L2_3, L3_3, L4_3, L5_3, L6_3, L7_3, L8_3, L9_3, L10_3, L11_3)
    L0_3 = Citizen
    L0_3 = L0_3.Wait
    L1_3 = 6000
    L0_3(L1_3)
    L0_3 = ClearPedTasks
    L1_3 = A0_2
    L0_3(L1_3)
  end
  L1_2(L2_2)
end
PlayRadioAnim = L1_1
