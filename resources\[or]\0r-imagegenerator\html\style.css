@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300&display=swap');

#container {
    margin: 0;
    position: absolute;
    bottom: 10%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(45deg, rgba(0, 255, 199, 0.7), rgba(0, 128, 128, 0.7));
    width: 20%;
    height: 5%;
    text-align: center;
    border-radius: 15px;
    border: 2px solid rgba(0, 255, 199, 0.7);
    box-shadow: 0 0 20px rgba(0, 255, 199, 0.5);
    overflow: hidden;
    align-items: center;
    justify-content: center;
}

#container p {
    color: #FFF;
    font-size: 1.5em;
    line-height: 5%;
    font-family: 'Open Sans', sans-serif;
    animation: fadeIn 2s ease-in-out infinite alternate;
}

@keyframes fadeIn {
    from {
        opacity: 0.5;
    }
    to {
        opacity: 1;
    }
}

#container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(4, 255, 199, 0.1);
    transform: scaleX(0);
    transform-origin: bottom right;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: scaleX(0);
    }
    50% {
        transform: scaleX(1);
    }
    100% {
        transform: scaleX(0);
    }
}