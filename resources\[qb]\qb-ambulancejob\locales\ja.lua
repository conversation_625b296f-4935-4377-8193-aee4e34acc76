local Translations = {
    error = {
        canceled = 'キャンセル',
        bled_out = '出血している...',
        impossible = '動けない...',
        no_player = '近くにプレイヤーがいない',
        no_firstaid = '救急キットが必要です',
        no_bandage = '包帯が必要です',
        beds_taken = 'ベッドは満床です...',
        possessions_taken = 'すべての持ち物が奪われた...',
        not_enough_money = '十分なお金を持っていない...',
        cant_help = 'この人を助けられない...',
        not_ems = '救急隊員ではないか、サインインしていません',
        not_online = 'プレイヤーはオフライン'
    },
    success = {
        revived = 'この人を蘇生した',
        healthy_player = 'プレイヤーは健康です',
        helped_player = 'この人を助けた',
        wounds_healed = '傷が癒えた！',
        being_helped = '救助されています...'
    },
    info = {
        civ_died = '民間人死亡',
        civ_down = '民間人負傷',
        civ_call = '民間人からの通報',
        self_death = '本人またはNPC',
        wep_unknown = '不明',
        respawn_txt = '~r~%{deathtime}~s~ 秒後にリスポーン可能',
        respawn_revive = '[~r~E~s~] を %{holdtime} 秒間押し続けるとリスポーン( $~r~%{cost}~s~ と持ち物を全て失う)',
        bleed_out = '出血して動けない。 ~r~%{time}~s~ 秒内に救助を呼ぶ必要がある',
        bleed_out_help = '出血中: 残り ~r~%{time}~s~ 秒間救助を呼べます',
        request_help = ' [~r~G~s~] 救急隊に連絡',
        help_requested = '救急隊に連絡済み',
        amb_plate = 'AMBU',  -- Should only be 4 characters long due to the last 4 being a random 4 digits
        heli_plate = 'LIFE', -- Should only be 4 characters long due to the last 4 being a random 4 digits
        status = '状態: ',
        is_staus = '%{status}',
        healthy = '完治しました！',
        safe = 'Hospital Safe',
        pb_hospital = 'Pillbox病院',
        pain_message = '%{limb}が%{severity}',
        many_places = 'いたるところが痛い...',
        bleed_alert = '%{bleedstate}',
        ems_alert = '救急隊アラート - %{text}',
        mr = 'Mr.',
        mrs = 'Mrs.',
        dr_needed = 'Pillbox病院で良しを募集中',
        ems_report = '救急隊レポート',
        message_sent = 'メッセージを送信しました',
        check_health = 'プレイヤーを診察する',
        heal_player = 'プレイヤーを回復する',
        revive_player = 'プレイヤーを蘇生する',
        revive_player_a = '他のプレイヤーまたは自分を蘇生 (Admin専用)',
        player_id = 'プレイヤーID (オプション)',
        pain_level = '自分や他のプレイヤーの痛みレベルを設定 (Admin専用)',
        kill = '他のプレイヤーまたは自分を殺す (Admin専用)',
        heal_player_a = '他のプレイヤーまたは自分を回復する (Admin専用)',
    },
    mail = {
        sender = 'Pillbox病院',
        subject = '病院費用',
        message = '%{lastname}さん。<br />いつもご利用ありがとうございます。<br /><br />前回ご利用した際の病院費用は以下の通りとなります。<br />合計額: <strong>$%{costs}</strong><br /><br />一日も早いご回復をお祈り申し上げます。'
    },
    states = {
        irritated = 'ヒリヒリする',
        quite_painful = 'かなり痛い',
        painful = '痛い',
        really_painful = 'とても痛い',
        little_bleed = '少し出血している...',
        bleed = '出血している...',
        lot_bleed = '大出血してる...',
        big_bleed = '大量出血してる...',
    },
    menu = {
        amb_vehicles = '救急車両',
        status = '健康状態',
        close = '⬅ メニューを閉じる',
    },
    text = {
        pstash_button = '[E] - インベントリ',
        pstash = 'インベントリ',
        onduty_button = '[E] - 出勤する',
        offduty_button = '[E] - 退勤する',
        duty = '出退勤切り替え',
        armory_button = '[E] - 武器庫',
        armory = '武器庫',
        veh_button = '[E] - 車両を掴む / 収納する',
        heli_button = '[E] - ヘリコプターを掴む / 収納する',
        elevator_roof = '[E] - エレベーターで屋上へ上がる',
        elevator_main = '[E] - エレベーターで降りる',
        bed_out = '[E] - ベッドから起きる',
        call_doc = '[E] - 医師に連絡',
        call = '連絡',
        check_in = '[E] チェックイン',
        check = 'チェックイン',
        lie_bed = '[E] - ベッドに横になる'
    },
    body = {
        head = '頭',
        neck = '首',
        spine = '背骨',
        upper_body = '上半身',
        lower_body = '下半身',
        left_arm = '左腕',
        left_hand = '左手',
        left_fingers = '左手の指',
        left_leg = '左脚',
        left_foot = '左足',
        right_arm = '右腕',
        right_hand = '右手',
        right_fingers = '右手の指',
        right_leg = '右脚',
        right_foot = '右足',
    },
    progress = {
        ifaks = 'IFAKを使用中...',
        bandage = '包帯を使用中...',
        painkillers = 'ペインキラーを使用中...',
        revive = '蘇生中...',
        healing = '傷を治療中...',
        checking_in = 'チェックイン中...',
    },
    logs = {
        death_log_title = '%{playername} (%{playerid}) が死んだ',
        death_log_message = '%{killername} は %{playername} を **%{weaponlabel}** (%{weaponname}) で殺した',
    }
}

if GetConvar('qb_locale', 'en') == 'ja' then
    Lang = Locale:new({
        phrases = Translations,
        warnOnMissing = true,
        fallbackLang = Lang,
    })
end
