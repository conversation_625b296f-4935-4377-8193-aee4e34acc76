local L0_1, L1_1, L2_1, L3_1, L4_1, L5_1, L6_1, L7_1, L8_1, L9_1, L10_1, L11_1, L12_1, L13_1, L14_1, L15_1
L0_1 = {}
L1_1 = true
L2_1 = Config
L2_1 = L2_1.RealWeapons
L3_1 = "backhandgun"
L4_1 = "assault"
L5_1 = nil
L6_1 = {}
L7_1 = true
L8_1 = true
L9_1 = false
L10_1 = false
L11_1 = Config
L11_1 = L11_1.Holster
if not L11_1 then
  return
end
function L11_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L0_2 = FW_GetPlayerData
  L1_2 = false
  L0_2 = L0_2(L1_2)
  if nil ~= L0_2 then
    L1_2 = L0_2.job
    if nil ~= L1_2 then
      L1_2 = L0_2.job
      L1_2 = L1_2.name
      if nil ~= L1_2 then
        goto lbl_15
      end
    end
  end
  L1_2 = false
  do return L1_2 end
  ::lbl_15::
  L1_2 = CanOpenTablet
  L2_2 = L0_2.job
  L2_2 = L2_2.name
  L1_2 = L1_2(L2_2)
  L1_2 = L1_2[2]
  if not L1_2 then
    L2_2 = false
    return L2_2
  end
  L2_2 = pairs
  L3_2 = Config
  L3_2 = L3_2.JobCategory
  L3_2 = L3_2[L1_2]
  L2_2, L3_2, L4_2, L5_2 = L2_2(L3_2)
  for L6_2, L7_2 in L2_2, L3_2, L4_2, L5_2 do
    L8_2 = L0_2.job
    L8_2 = L8_2.name
    L9_2 = L7_2.name
    if L8_2 == L9_2 then
      L8_2 = true
      return L8_2
    end
  end
  L2_2 = false
  return L2_2
end
IsHolsterAllowed = L11_1
L11_1 = RegisterNetEvent
L12_1 = "origen_armas:unarm"
function L13_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2
  L0_2 = PlayerPedId
  L0_2 = L0_2()
  L1_2 = L7_1
  if L1_2 then
    L1_2 = SetCurrentPedWeapon
    L2_2 = L0_2
    L3_2 = GetHashKey
    L4_2 = "WEAPON_UNARMED"
    L3_2 = L3_2(L4_2)
    L4_2 = true
    L1_2(L2_2, L3_2, L4_2)
    L1_2 = SetPedCanSwitchWeapon
    L2_2 = L0_2
    L3_2 = true
    L1_2(L2_2, L3_2)
    L1_2 = false
    L7_1 = L1_2
    L1_2 = RemoveGears
    L1_2()
    L1_2 = ClearPedTasks
    L2_2 = L0_2
    L1_2(L2_2)
    L1_2 = ExecuteCommand
    L2_2 = Config
    L2_2 = L2_2.Translations
    L2_2 = L2_2.DoHide
    L1_2(L2_2)
  else
    L1_2 = true
    L7_1 = L1_2
    L1_2 = SetGears
    L1_2()
    L1_2 = SetPedCanSwitchWeapon
    L2_2 = L0_2
    L3_2 = true
    L1_2(L2_2, L3_2)
    L1_2 = ExecuteCommand
    L2_2 = Config
    L2_2 = L2_2.Translations
    L2_2 = L2_2.DoShow
    L1_2(L2_2)
  end
end
L11_1(L12_1, L13_1)
L11_1 = false
L12_1 = RegisterNetEvent
L13_1 = "origen_police:client:OnJobUpdate"
function L14_1(A0_2)
  local L1_2, L2_2
  L1_2 = IsHolsterAllowed
  L1_2 = L1_2()
  if L1_2 then
    L1_2 = "handguns"
    L3_1 = L1_2
  end
  L1_2 = RemoveGears
  L1_2()
  L1_2 = Wait
  L2_2 = 2000
  L1_2(L2_2)
end
L12_1(L13_1, L14_1)
L12_1 = RegisterNetEvent
L13_1 = "origen_police:client:OnPlayerLoaded"
function L14_1()
  local L0_2, L1_2, L2_2, L3_2
  L0_2 = FW_GetPlayerData
  L1_2 = true
  L0_2 = L0_2(L1_2)
  L1_2 = Wait
  L2_2 = 2000
  L1_2(L2_2)
  L1_2 = true
  L11_1 = L1_2
  L1_2 = L0_2.metadata
  if nil == L1_2 then
    return
  end
  L1_2 = L0_2.metadata
  L1_2 = L1_2.handgunflag
  L2_2 = L0_2.metadata
  L2_2 = L2_2.rifleflag
  L3_2 = L1_2 or L3_2
  if not L1_2 or not L1_2 then
    L3_2 = L3_1
  end
  L3_1 = L3_2
  L3_2 = L2_2 or L3_2
  if not L2_2 or not L2_2 then
    L3_2 = L4_1
  end
  L4_1 = L3_2
end
L12_1(L13_1, L14_1)
L12_1 = {}
function L13_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L3_2 = L11_1
  if not L3_2 then
    L3_2 = false
    return L3_2
  end
  L3_2 = L12_1.data
  if L3_2 then
    L3_2 = GetGameTimer
    L3_2 = L3_2()
    L4_2 = L12_1.time
    L3_2 = L3_2 - L4_2
    if not (L3_2 > 10) then
      goto lbl_26
    end
  end
  L3_2 = {}
  L4_2 = GetGameTimer
  L4_2 = L4_2()
  L3_2.time = L4_2
  L4_2 = GetPlayerItems
  L5_2 = nil
  L4_2 = L4_2(L5_2)
  L3_2.data = L4_2
  L12_1 = L3_2
  ::lbl_26::
  L3_2 = pairs
  L4_2 = L12_1.data
  L3_2, L4_2, L5_2, L6_2 = L3_2(L4_2)
  for L7_2, L8_2 in L3_2, L4_2, L5_2, L6_2 do
    L9_2 = GetHashKey
    L10_2 = L8_2.name
    L9_2 = L9_2(L10_2)
    if L9_2 == A1_2 then
      L9_2 = true
      return L9_2
    end
  end
  L3_2 = false
  return L3_2
end
HasPedGotWeapon = L13_1
L13_1 = Citizen
L13_1 = L13_1.CreateThread
function L14_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L0_2 = Config
  L0_2 = L0_2.Holster
  if not L0_2 then
    return
  end
  while true do
    L0_2 = L1_1
    if L0_2 then
      break
    end
    L0_2 = Citizen
    L0_2 = L0_2.Wait
    L1_2 = 500
    L0_2(L1_2)
  end
  L0_2 = Citizen
  L0_2 = L0_2.Wait
  L1_2 = 5000
  L0_2(L1_2)
  L0_2 = PlayerPedId
  L0_2 = L0_2()
  L1_2 = SetPedCanSwitchWeapon
  L2_2 = L0_2
  L3_2 = true
  L1_2(L2_2, L3_2)
  L1_2 = Config
  L1_2 = L1_2.RealWeapons
  L2_1 = L1_2
  L1_2 = Config
  L1_2 = L1_2.WeaponCategoryOffsets
  L6_1 = L1_2
  while true do
    L1_2 = Citizen
    L1_2 = L1_2.Wait
    L2_2 = 1500
    L1_2(L2_2)
    L1_2 = PlayerPedId
    L1_2 = L1_2()
    L0_2 = L1_2
    L1_2 = L7_1
    if L1_2 then
      L1_2 = L11_1
      if L1_2 then
        L1_2 = 1
        L2_2 = L2_1
        L2_2 = #L2_2
        L3_2 = 1
        for L4_2 = L1_2, L2_2, L3_2 do
          L5_2 = GetHashKey
          L6_2 = L2_1
          L6_2 = L6_2[L4_2]
          L6_2 = L6_2.name
          L5_2 = L5_2(L6_2)
          L6_2 = HasPedGotWeapon
          L7_2 = L0_2
          L8_2 = L5_2
          L9_2 = false
          L6_2 = L6_2(L7_2, L8_2, L9_2)
          if L6_2 then
            L6_2 = L2_1
            L6_2 = L6_2[L4_2]
            L7_2 = L6_2.name
            L6_2 = L0_1
            L6_2 = L6_2[L7_2]
            if not L6_2 then
              L6_2 = GetSelectedPedWeapon
              L7_2 = L0_2
              L6_2 = L6_2(L7_2)
              if L5_2 ~= L6_2 then
                L6_2 = L2_1
                L6_2 = L6_2[L4_2]
                L6_2 = L6_2.model
                if nil ~= L6_2 then
                  L6_2 = SetGear
                  L7_2 = L2_1
                  L7_2 = L7_2[L4_2]
                  L7_2 = L7_2.name
                  L6_2(L7_2)
                end
            end
            else
              L6_2 = GetSelectedPedWeapon
              L7_2 = L0_2
              L6_2 = L6_2(L7_2)
              if L5_2 == L6_2 then
                L6_2 = RemoveGear
                L7_2 = L2_1
                L7_2 = L7_2[L4_2]
                L7_2 = L7_2.name
                L6_2(L7_2)
              end
            end
          else
            L6_2 = L2_1
            L6_2 = L6_2[L4_2]
            L7_2 = L6_2.name
            L6_2 = L0_1
            L6_2 = L6_2[L7_2]
            if L6_2 then
              L6_2 = RemoveGear
              L7_2 = L2_1
              L7_2 = L7_2[L4_2]
              L7_2 = L7_2.name
              L6_2(L7_2)
            end
          end
        end
    end
    else
      L1_2 = RemoveGears
      L1_2()
    end
  end
end
L13_1(L14_1)
function L13_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L1_2 = {}
  L2_2 = pairs
  L3_2 = L0_1
  L2_2, L3_2, L4_2, L5_2 = L2_2(L3_2)
  for L6_2, L7_2 in L2_2, L3_2, L4_2, L5_2 do
    if L6_2 ~= A0_2 then
      L1_2[L6_2] = L7_2
    else
      L8_2 = DeleteEntity
      L9_2 = L7_2
      L8_2(L9_2)
    end
  end
  L0_1 = L1_2
end
RemoveGear = L13_1
function L13_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L0_2 = pairs
  L1_2 = L0_1
  L0_2, L1_2, L2_2, L3_2 = L0_2(L1_2)
  for L4_2, L5_2 in L0_2, L1_2, L2_2, L3_2 do
    L6_2 = DeleteEntity
    L7_2 = L5_2
    L6_2(L7_2)
  end
  L0_2 = {}
  L0_1 = L0_2
end
RemoveGears = L13_1
function L13_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L1_2 = 1
  L2_2 = L6_1
  L2_2 = #L2_2
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = L6_1
    L5_2 = L5_2[L4_2]
    L5_2 = L5_2.category
    if L5_2 == A0_2 then
      L5_2 = L6_1
      L5_2 = L5_2[L4_2]
      L5_2 = L5_2.bone
      L6_2 = L6_1
      L6_2 = L6_2[L4_2]
      L6_2 = L6_2.x
      L7_2 = L6_1
      L7_2 = L7_2[L4_2]
      L7_2 = L7_2.y
      L8_2 = L6_1
      L8_2 = L8_2[L4_2]
      L8_2 = L8_2.z
      L9_2 = L6_1
      L9_2 = L9_2[L4_2]
      L9_2 = L9_2.xRot
      L10_2 = L6_1
      L10_2 = L10_2[L4_2]
      L10_2 = L10_2.yRot
      L11_2 = L6_1
      L11_2 = L11_2[L4_2]
      L11_2 = L11_2.zRot
      return L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
    end
  end
end
GetCoords = L13_1
function L13_1(weaponName)
    -- Initial setup and checks
    if not IsHolsterAllowed() then return end
    
    local bone, offsetX, offsetY, offsetZ, rotX, rotY, rotZ = nil, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0
    local playerPed = PlayerPedId()
    local weaponModel = nil
    local playerData = FW_GetPlayerData(false)
    local weaponData = nil
    
    -- Find matching weapon in config
    for i = 1, #L2_1 do
        local currentWeapon = L2_1[i]
        if currentWeapon.name == weaponName then
            -- Handle handguns and revolvers
            if currentWeapon.category == "handguns" or currentWeapon.category == "revolver" then
                if weaponData then
                    if not L10_1 then
                        L5_1 = "handguns"
                        L3_1 = "handguns"
                    end
                else
                    L5_1 = L3_1
                end
            -- Handle other weapon types
            else
                local isLongWeapon = currentWeapon.category == "machine" or
                                   currentWeapon.category == "assault" or
                                   currentWeapon.category == "shotgun" or
                                   currentWeapon.category == "sniper" or
                                   currentWeapon.category == "heavy"
                
                if isLongWeapon then
                    L5_1 = L4_1
                else
                    L5_1 = currentWeapon.category
                end
            end
            
            -- Get weapon coordinates and model
            bone, offsetX, offsetY, offsetZ, rotX, rotY, rotZ = GetCoords(L5_1)
            weaponModel = currentWeapon.model
            break
        end
    end
    
    -- Create and setup weapon object
    local weaponObject = CreateObject(weaponModel, x, y, z, true, true, false)
    local boneIndex = GetPedBoneIndex(playerPed, bone)
    GetWorldPositionOfEntityBone(playerPed, boneIndex)
    
    -- Configure weapon object
    SetEntityCollision(weaponObject, false, false)
    
    -- Attach weapon to player
    AttachEntityToEntity(
        weaponObject,   -- Weapon object
        playerPed,      -- Player ped
        boneIndex,      -- Attachment point
        offsetX, offsetY, offsetZ,  -- Position offsets
        rotX, rotY, rotZ,          -- Rotation
        false, false, false, false, -- Flags
        2,    -- Rotation order
        true  -- Use soft pinning
    )
    
    -- Final setup
    SetEntityCollision(weaponObject, false, false)
    L0_1[weaponName] = weaponObject
end
SetGear = L13_1
function L13_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L0_2 = PlayerPedId
  L0_2 = L0_2()
  L1_2 = 1
  L2_2 = L2_1
  L2_2 = #L2_2
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = HasPedGotWeapon
    L6_2 = L0_2
    L7_2 = GetHashKey
    L8_2 = L2_1
    L8_2 = L8_2[L4_2]
    L8_2 = L8_2.name
    L7_2 = L7_2(L8_2)
    L8_2 = false
    L5_2 = L5_2(L6_2, L7_2, L8_2)
    if L5_2 then
      L5_2 = SetGear
      L6_2 = L2_1
      L6_2 = L6_2[L4_2]
      L6_2 = L6_2.name
      L5_2(L6_2)
    end
  end
end
SetGears = L13_1
L13_1 = RegisterCommand
L14_1 = "holster"
function L15_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2
  L2_2 = IsHolsterAllowed
  L2_2 = L2_2()
  if L2_2 then
    L2_2 = A1_2[1]
    if nil == L2_2 then
      L2_2 = FW_Notify
      L3_2 = Config
      L3_2 = L3_2.Translations
      L3_2 = L3_2.SomethingWrong
      return L2_2(L3_2)
    else
      L2_2 = A1_2[1]
      if "handguns" ~= L2_2 then
        L2_2 = A1_2[1]
        if "waisthandgun" ~= L2_2 then
          goto lbl_29
        end
      end
      L2_2 = A1_2[1]
      L3_1 = L2_2
      L2_2 = FW_Notify
      L3_2 = Config
      L3_2 = L3_2.Translations
      L3_2 = L3_2.HipHolster
      L2_2(L3_2)
      goto lbl_100
      ::lbl_29::
      L2_2 = A1_2[1]
      if "backhandgun" == L2_2 then
        L2_2 = A1_2[1]
        L3_1 = L2_2
        L2_2 = FW_Notify
        L3_2 = Config
        L3_2 = L3_2.Translations
        L3_2 = L3_2.BackHolster
        L2_2(L3_2)
      else
        L2_2 = A1_2[1]
        if "leghandgun" ~= L2_2 then
          L2_2 = A1_2[1]
          if "hiphandgun" ~= L2_2 then
            L2_2 = A1_2[1]
            if "handguns2" ~= L2_2 then
              goto lbl_57
            end
          end
        end
        L2_2 = A1_2[1]
        L3_1 = L2_2
        L2_2 = FW_Notify
        L3_2 = Config
        L3_2 = L3_2.Translations
        L3_2 = L3_2.LegHolster
        L2_2(L3_2)
        goto lbl_100
        ::lbl_57::
        L2_2 = A1_2[1]
        if "chesthandgun" == L2_2 then
          L2_2 = A1_2[1]
          L3_1 = L2_2
          L2_2 = FW_Notify
          L3_2 = Config
          L3_2 = L3_2.Translations
          L3_2 = L3_2.UpperHolster
          L2_2(L3_2)
        else
          L2_2 = A1_2[1]
          if "boxers" == L2_2 then
            L2_2 = A1_2[1]
            L3_1 = L2_2
            L2_2 = FW_Notify
            L3_2 = Config
            L3_2 = L3_2.Translations
            L3_2 = L3_2.UnderPantsHolster
            L2_2(L3_2)
          else
            L2_2 = A1_2[1]
            if "assault" == L2_2 then
              L2_2 = A1_2[1]
              L4_1 = L2_2
              L2_2 = FW_Notify
              L3_2 = Config
              L3_2 = L3_2.Translations
              L3_2 = L3_2.LongHolsterBack
              L2_2(L3_2)
            else
              L2_2 = A1_2[1]
              if "tacticalrifle" == L2_2 then
                L2_2 = A1_2[1]
                L4_1 = L2_2
                L2_2 = FW_Notify
                L3_2 = Config
                L3_2 = L3_2.Translations
                L3_2 = L3_2.LongHolsterFront
                L2_2(L3_2)
              end
            end
          end
        end
      end
    end
    ::lbl_100::
    L2_2 = TriggerServerEvent
    L3_2 = "origen_police:SetMetaData"
    L4_2 = "rifleflag"
    L5_2 = L4_1
    L2_2(L3_2, L4_2, L5_2)
    L2_2 = TriggerServerEvent
    L3_2 = "origen_police:SetMetaData"
    L4_2 = "handgunflag"
    L5_2 = L3_1
    L2_2(L3_2, L4_2, L5_2)
    L2_2 = RemoveGears
    L2_2()
    L2_2 = true
    L10_1 = L2_2
  end
end
L13_1(L14_1, L15_1)
function L13_1(A0_2)
  local L1_2, L2_2
  while true do
    L1_2 = HasAnimDictLoaded
    L2_2 = A0_2
    L1_2 = L1_2(L2_2)
    if L1_2 then
      break
    end
    L1_2 = RequestAnimDict
    L2_2 = A0_2
    L1_2(L2_2)
    L1_2 = Citizen
    L1_2 = L1_2.Wait
    L2_2 = 100
    L1_2(L2_2)
  end
end
loadAnimDict = L13_1
function L13_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L2_2 = IsEntityDead
  L3_2 = A0_2
  L2_2 = L2_2(L3_2)
  if L2_2 then
    L2_2 = false
    L9_1 = L2_2
    L2_2 = false
    return L2_2
  else
    L2_2 = 1
    L3_2 = L2_1
    L3_2 = #L3_2
    L4_2 = 1
    for L5_2 = L2_2, L3_2, L4_2 do
      L6_2 = GetHashKey
      L7_2 = L2_1
      L7_2 = L7_2[L5_2]
      L7_2 = L7_2.name
      L6_2 = L6_2(L7_2)
      L7_2 = GetSelectedPedWeapon
      L8_2 = A0_2
      L7_2 = L7_2(L8_2)
      if L6_2 == L7_2 then
        L6_2 = true
        return L6_2
      end
    end
    L2_2 = false
    return L2_2
  end
end
CheckWeapon = L13_1
function L13_1(A0_2)
  local L1_2, L2_2
  while true do
    L1_2 = HasAnimDictLoaded
    L2_2 = A0_2
    L1_2 = L1_2(L2_2)
    if L1_2 then
      break
    end
    L1_2 = RequestAnimDict
    L2_2 = A0_2
    L1_2(L2_2)
    L1_2 = Citizen
    L1_2 = L1_2.Wait
    L2_2 = 0
    L1_2(L2_2)
  end
end
loadAnimDict2 = L13_1
L13_1 = Citizen
L13_1 = L13_1.CreateThread
-- Animation configuration
local AnimConfig = {
  backhandgun = {
      dict = "reaction@intimidation@1h",
      intro = "intro",
      outro = "outro",
      weaponVisible = {1, 1, 1, 1}
  },
  boxers = {
      dict = "combat@combat_reactions@pistol_1h_gang",
      anim = "0",
      weaponVisible = {1, 1, 1, 1}
  },
  chesthandgun = {
      dict = "combat@combat_reactions@pistol_1h_gang",
      anim = "0",
      weaponVisible = {1, 1, 1, 1}
  },
  leghandgun = {
      dict = "reaction@male_stand@big_variations@d",
      anim = "react_big_variations_m",
      weaponVisible = {1, 1, 1, 1}
  },
  default = {
      dict = "rcmjosh4",
      anim = "josh_leadout_cop2",
      weaponVisible = {0, 1, 1, 1},
      useTaskPlayAnim = true
  },
  tacticalrifle = {
      dict = "combat@combat_reactions@pistol_1h_hillbilly",
      anim = "0",
      weaponVisible = {1, 1, 1, 1}
  }
}

-- Load all required animation dictionaries
local function loadRequiredAnims()
  local anims = {
      "rcmjosh4",
      "reaction@intimidation@cop@unarmed",
      "reaction@intimidation@1h",
      "combat@combat_reactions@pistol_1h_gang",
      "combat@combat_reactions@pistol_1h_hillbilly",
      "reaction@male_stand@big_variations@d"
  }
  
  for _, anim in ipairs(anims) do
      loadAnimDict(anim)
      loadAnimDict2(anim)
  end
end

-- Check if ped can perform animations
local function canPerformAnimations(ped)
  if IsPedInAnyVehicle(ped, true) then return false end
  
  local parachuteState = GetPedParachuteState(ped)
  if parachuteState ~= -1 and parachuteState ~= 0 then return false end
  
  return not IsPedInParachuteFreeFall(ped)
end

-- Play weapon animation
local function playAnimation(ped, animType, heading, isHolstering)
  local config = AnimConfig[animType] or AnimConfig.default
  
  -- Set weapon visibility
  SetPedCurrentWeaponVisible(ped, table.unpack(config.weaponVisible))
  if config == AnimConfig.default then
      SetPedCurrentWeaponVisible(ped, 1, 1, 1, 1)
  end
  
  local coords = GetEntityCoords(ped, true)
  local animName = isHolstering and (config.outro or "outro") or (config.intro or config.anim or "intro")
  local animSpeed = isHolstering and 0.125 or Config.AnimationSpeed.backhandgun.animSpeed
  
  -- Play appropriate animation
  if config.useTaskPlayAnim then
      TaskPlayAnim(ped, config.dict, animName, 8.0, 2.0, -1, 48, 10, 0, 0, 0)
  else
      TaskPlayAnimAdvanced(
          ped, config.dict, animName,
          coords, 0, 0, heading,
          8.0, 3.0, -1, 50,
          animSpeed, 0, 0
      )
  end
  
  -- Wait and clear
  Citizen.Wait(Config.AnimationSpeed.backhandgun.clearAnimWait)
  ClearPedTasks(ped)
end

-- Handle vehicle entry
local function handleVehicleEntry(ped)
  local vehicle = GetVehiclePedIsTryingToEnter(ped)
  if vehicle == 0 then
      L8_1 = false
  else
      SetCurrentPedWeapon(ped, GetHashKey("WEAPON_UNARMED"), true)
  end
end

-- Main thread function
function L14_1()
  if not Config.Holster then return end
  
  loadRequiredAnims()
  local heading = 0
  local weaponGroup = nil
  local lastWeapon = nil
  
  Citizen.Wait(0)
  
  while true do
      local ped = PlayerPedId()
      Citizen.Wait(50)
      heading = GetEntityHeading(ped)
      
      if IsPedInAnyVehicle(ped, true) then
          L8_1 = true
      elseif canPerformAnimations(ped) then
          weaponGroup = GetWeapontypeGroup(GetSelectedPedWeapon(ped))
          local currentWeapon = GetSelectedPedWeapon(PlayerPedId())
          
          if CheckWeapon(ped) then
              -- Handle handguns/revolvers
              if weaponGroup == 416676503 or weaponGroup == 690389602 then
                  if L8_1 then
                      L9_1 = true
                      playAnimation(ped, L3_1, heading, false)
                      L8_1 = false
                      lastWeapon = GetSelectedPedWeapon(ped)
                  else
                      L9_1 = false
                  end
              -- Handle other weapons
              else
                  if L8_1 then
                      L9_1 = true
                      playAnimation(ped, L4_1 == "tacticalrifle" and "tacticalrifle" or "default", heading, false)
                      L8_1 = false
                      lastWeapon = GetSelectedPedWeapon(ped)
                  else
                      L9_1 = false
                  end
              end
          -- Handle holstering
          else
              lastWeaponHash = currentWeapon
              local currentGroup = GetWeapontypeGroup(lastWeapon)
              
              if (currentGroup == 416676503 or currentGroup == 690389602) and not L8_1 then
                  playAnimation(ped, L3_1, heading, true)
                  L8_1 = true
              end
          end
      else
          handleVehicleEntry(ped)
      end
      
      if L8_1 then
          Citizen.Wait(500)
      end
  end
end
L13_1(L14_1)
L13_1 = AddEventHandler
L14_1 = "onResourceStop"
function L15_1(A0_2)
  local L1_2
  L1_2 = GetCurrentResourceName
  L1_2 = L1_2()
  if A0_2 == L1_2 then
    L1_2 = RemoveGears
    L1_2()
  end
end
L13_1(L14_1, L15_1)
