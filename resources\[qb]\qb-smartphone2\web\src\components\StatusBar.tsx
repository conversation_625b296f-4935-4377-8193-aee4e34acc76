import React from 'react';
import { usePhone } from '../contexts/PhoneContext';
import { useTheme } from '../contexts/ThemeContext';
import { formatTime, getBatteryColor, getSignalBars } from '../utils/misc';
import './StatusBar.css';

const StatusBar: React.FC = () => {
  const { phoneData } = usePhone();
  const { colors } = useTheme();
  
  const currentTime = new Date();
  const timeString = formatTime(currentTime);
  
  const batteryLevel = phoneData?.batteryLevel || 100;
  const isCharging = phoneData?.isCharging || false;
  const signalStrength = 85; // Mock signal strength
  
  const batteryColor = getBatteryColor(batteryLevel);
  const signalBars = getSignalBars(signalStrength);

  return (
    <div className="status-bar" style={{ color: colors.text }}>
      {/* Left side - Time */}
      <div className="status-left">
        <span className="status-time">{timeString}</span>
      </div>
      
      {/* Right side - Icons */}
      <div className="status-right">
        {/* Signal strength */}
        <div className="signal-indicator">
          {[...Array(4)].map((_, index) => (
            <div
              key={index}
              className={`signal-bar ${index < signalBars ? 'active' : ''}`}
              style={{
                backgroundColor: index < signalBars ? colors.text : 'rgba(255, 255, 255, 0.3)'
              }}
            />
          ))}
        </div>
        
        {/* WiFi icon */}
        <div className="wifi-indicator">
          <i className="fas fa-wifi" style={{ color: colors.text }} />
        </div>
        
        {/* Battery */}
        <div className="battery-indicator">
          {isCharging && (
            <i className="fas fa-bolt charging-icon" style={{ color: colors.success }} />
          )}
          <div className="battery-container">
            <div 
              className="battery-fill"
              style={{
                width: `${batteryLevel}%`,
                backgroundColor: batteryColor
              }}
            />
            <div className="battery-tip" style={{ backgroundColor: colors.text }} />
          </div>
          <span className="battery-percentage">{batteryLevel}%</span>
        </div>
      </div>
    </div>
  );
};

export default StatusBar;
