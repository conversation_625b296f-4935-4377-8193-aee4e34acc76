function CreateCode(code, slots)
    local result = ExecuteSql('SELECT * FROM `0r_multicharacterv2-codes` WHERE code = ?', { code })

    if result and result[1] then
        return print('^1[0r-multicharacterv2]^7 Code already exists')
    end

    ExecuteSql('INSERT INTO `0r_multicharacterv2-codes` (code, slots, active) VALUES (?, ?, ?)', { code, slots, 1 })
    print('^2[0r-multicharacterv2]^7 Code created successfully! Code: ' .. code .. ' Slots: ' .. slots)
end

function ReqCode(code)
    local result = ExecuteSql('SELECT * FROM `0r_multicharacterv2-codes` WHERE code = ? AND active = 1', { code })

    if result and result[1] then
        return result[1].slots
    else
        return false
    end
end