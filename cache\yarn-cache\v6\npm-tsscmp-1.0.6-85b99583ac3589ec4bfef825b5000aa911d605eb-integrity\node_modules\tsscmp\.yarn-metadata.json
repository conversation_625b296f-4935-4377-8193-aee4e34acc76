{"manifest": {"name": "tsscmp", "version": "1.0.6", "description": "Timing safe string compare using double HMAC", "main": "lib/index.js", "dependencies": {}, "devDependencies": {}, "scripts": {"test": "node test/unit && node test/benchmark"}, "repository": {"type": "git", "url": "https://github.com/suryagh/tsscmp.git"}, "keywords": ["timing safe string compare", "double hmac string compare", "safe string compare", "hmac"], "author": {"name": "suryagh"}, "publishConfig": {"registry": "https://registry.npmjs.org"}, "engines": {"node": ">=0.6.x"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-tsscmp-1.0.6-85b99583ac3589ec4bfef825b5000aa911d605eb-integrity\\node_modules\\tsscmp\\package.json", "readmeFilename": "README.md", "readme": "# Timing safe string compare using double HMAC\n\n[![Node.js Version](https://img.shields.io/node/v/tsscmp.svg?style=flat-square)](https://nodejs.org/en/download)\n[![npm](https://img.shields.io/npm/v/tsscmp.svg?style=flat-square)](https://npmjs.org/package/tsscmp)\n[![NPM Downloads](https://img.shields.io/npm/dm/tsscmp.svg?style=flat-square)](https://npmjs.org/package/tsscmp)\n[![Build Status](https://img.shields.io/travis/suryagh/tsscmp/master.svg?style=flat-square)](https://travis-ci.org/suryagh/tsscmp)\n[![Build Status](https://img.shields.io/appveyor/ci/suryagh/tsscmp/master.svg?style=flat-square&label=windows)](https://ci.appveyor.com/project/suryagh/tsscmp)\n[![Dependency Status](http://img.shields.io/david/suryagh/tsscmp.svg?style=flat-square)](https://david-dm.org/suryagh/tsscmp)\n[![npm-license](http://img.shields.io/npm/l/tsscmp.svg?style=flat-square)](LICENSE)\n\n\nPrevents [timing attacks](http://codahale.com/a-lesson-in-timing-attacks/) using Brad Hill's\n[Double HMAC pattern](https://www.nccgroup.trust/us/about-us/newsroom-and-events/blog/2011/february/double-hmac-verification/)\nto perform secure string comparison. Double HMAC avoids the timing atacks by blinding the\ntiming channel using random time per attempt comparison against iterative brute force attacks.\n\n\n## Install\n\n```\nnpm install tsscmp\n```\n## Why\nTo compare secret values like **authentication tokens**, **passwords** or\n**capability urls** so that timing information is not\nleaked to the attacker.\n\n## Example\n\n```js\nvar timingSafeCompare = require('tsscmp');\n\nvar sessionToken = '127e6fbfe24a750e72930c';\nvar givenToken = '127e6fbfe24a750e72930c';\n\nif (timingSafeCompare(sessionToken, givenToken)) {\n  console.log('good token');\n} else {\n  console.log('bad token');\n}\n```\n##License: \n[MIT](LICENSE)\n\n**Credits to:**  [@jsha](https://github.com/jsha) |\n[@bnoordhuis](https://github.com/bnoordhuis) |\n[@suryagh](https://github.com/suryagh) |\n ", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/tsscmp/-/tsscmp-1.0.6.tgz#85b99583ac3589ec4bfef825b5000aa911d605eb", "type": "tarball", "reference": "https://registry.yarnpkg.com/tsscmp/-/tsscmp-1.0.6.tgz", "hash": "85b99583ac3589ec4bfef825b5000aa911d605eb", "integrity": "sha512-LxhtAkPDTkVCMQjt2h6eBVY28KCjikZqZfMcC15YBeNjkgUpdCfBu5HoiOTDu86v6smE8yOjyEktJ8hlbANHQA==", "registry": "npm", "packageName": "tsscmp", "cacheIntegrity": "sha512-LxhtAkPDTkVCMQjt2h6eBVY28KCjikZqZfMcC15YBeNjkgUpdCfBu5HoiOTDu86v6smE8yOjyEktJ8hlbANHQA== sha1-hbmVg6w1iexL/vgltQAKqRHWBes="}, "registry": "npm", "hash": "85b99583ac3589ec4bfef825b5000aa911d605eb"}