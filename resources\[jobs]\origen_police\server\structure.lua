local L0_1, L1_1, L2_1, L3_1, L4_1, L5_1, L6_1, L7_1, L8_1, L9_1, L10_1, L11_1, L12_1, L13_1
function L0_1(A0_2)
  local L1_2, L2_2, L3_2
  L1_2 = string
  L1_2 = L1_2.format
  L2_2 = "SHOW TABLES LIKE '%s'"
  L3_2 = A0_2
  L1_2 = L1_2(L2_2, L3_2)
  L2_2 = MySQL
  L2_2 = L2_2.awaitScalar
  L3_2 = L1_2
  L2_2 = L2_2(L3_2)
  return L2_2
end
tableExists = L0_1
L0_1 = Config
L0_1 = L0_1.DatabaseStructureCheck
if not L0_1 then
  return
end
L0_1 = {}
L1_1 = {}
L2_1 = "users"
L3_1 = "owned_vehicles"
L4_1 = "job_grades"
L1_1[1] = L2_1
L1_1[2] = L3_1
L1_1[3] = L4_1
L0_1.esx = L1_1
L1_1 = {}
L2_1 = "players"
L3_1 = "player_vehicles"
L1_1[1] = L2_1
L1_1[2] = L3_1
L0_1.qbcore = L1_1
L1_1 = {}
L2_1 = {}
L3_1 = "image"
L4_1 = "dangerous"
L5_1 = "wanted"
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L1_1.users = L2_1
L2_1 = {}
L3_1 = "wanted"
L4_1 = "billPrice"
L5_1 = "description"
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L1_1.owned_vehicles = L2_1
L2_1 = {}
L3_1 = "type"
L2_1[1] = L3_1
L1_1.job_grades = L2_1
L2_1 = {}
L3_1 = "image"
L4_1 = "dangerous"
L5_1 = "wanted"
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L1_1.players = L2_1
L2_1 = {}
L3_1 = "wanted"
L4_1 = "billPrice"
L5_1 = "description"
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L1_1.player_vehicles = L2_1
L2_1 = {}
L3_1 = "id"
L4_1 = "title"
L5_1 = "description"
L6_1 = "price"
L7_1 = "month"
L8_1 = "cap"
L9_1 = "job"
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L1_1.origen_police_penalc = L2_1
L2_1 = {}
L3_1 = "citizenid"
L4_1 = "time"
L5_1 = "initial"
L6_1 = "name"
L7_1 = "date"
L8_1 = "danger"
L9_1 = "joinedfrom"
L10_1 = "image"
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L2_1[8] = L10_1
L1_1.origen_police_federal = L2_1
L2_1 = {}
L3_1 = "citizenid"
L4_1 = "title"
L5_1 = "description"
L6_1 = "author"
L7_1 = "date"
L8_1 = "fixed"
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L1_1.origen_police_notes = L2_1
L2_1 = {}
L3_1 = "citizenid"
L4_1 = "name"
L5_1 = "clockin"
L6_1 = "clockout"
L7_1 = "minutes"
L8_1 = "job"
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L1_1.origen_police_clocks = L2_1
L2_1 = {}
L3_1 = "title"
L4_1 = "description"
L5_1 = "author"
L6_1 = "cops"
L7_1 = "implicated"
L8_1 = "date"
L9_1 = "evidences"
L10_1 = "tags"
L11_1 = "location"
L12_1 = "victims"
L13_1 = "vehicles"
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L2_1[8] = L10_1
L2_1[9] = L11_1
L2_1[10] = L12_1
L2_1[11] = L13_1
L1_1.origen_police_reports = L2_1
L2_1 = {}
L3_1 = "citizenid"
L4_1 = "title"
L5_1 = "concepts"
L6_1 = "price"
L7_1 = "job"
L8_1 = "author"
L9_1 = "payed"
L10_1 = "date"
L11_1 = "months"
L12_1 = "reportid"
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L2_1[6] = L8_1
L2_1[7] = L9_1
L2_1[8] = L10_1
L2_1[9] = L11_1
L2_1[10] = L12_1
L1_1.origen_police_bills = L2_1
L2_1 = {}
L3_1 = "id"
L4_1 = "data"
L2_1[1] = L3_1
L2_1[2] = L4_1
L1_1.origen_metadata = L2_1
L2_1 = {}
L3_1 = "id"
L4_1 = "type"
L5_1 = "title"
L6_1 = "data"
L7_1 = "radius"
L2_1[1] = L3_1
L2_1[2] = L4_1
L2_1[3] = L5_1
L2_1[4] = L6_1
L2_1[5] = L7_1
L1_1.origen_police_shapes = L2_1
L2_1 = pairs
L3_1 = L0_1
L2_1, L3_1, L4_1, L5_1 = L2_1(L3_1)
for L6_1, L7_1 in L2_1, L3_1, L4_1, L5_1 do
  L8_1 = Config
  L8_1 = L8_1.Framework
  if L6_1 == L8_1 then
  else
    L8_1 = 1
    L9_1 = L0_1[L6_1]
    L9_1 = #L9_1
    L10_1 = 1
    for L11_1 = L8_1, L9_1, L10_1 do
      L12_1 = L0_1[L6_1]
      L12_1 = L12_1[L11_1]
      L1_1[L12_1] = nil
    end
  end
end
L2_1 = IsDuplicityVersion
L2_1 = L2_1()
if L2_1 then
  L2_1 = CreateThread
  function L3_1()
    local L0_2, L1_2
    L0_2 = checkTables
    L1_2 = L1_1
    L0_2(L1_2)
  end
  L2_1(L3_1)
end
function L2_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2
  L1_2 = pairs
  L2_2 = A0_2
  L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2)
  for L5_2, L6_2 in L1_2, L2_2, L3_2, L4_2 do
    L7_2 = tableExists
    L8_2 = L5_2
    L7_2 = L7_2(L8_2)
    if L7_2 then
      L7_2 = checkColumns
      L8_2 = L5_2
      L9_2 = L6_2
      L7_2 = L7_2(L8_2, L9_2)
      L8_2 = #L7_2
      if L8_2 > 0 then
        L8_2 = Debuger
        L9_2 = "Missing columns in the table: "
        L10_2 = L5_2
        L11_2 = ":"
        L9_2 = L9_2 .. L10_2 .. L11_2
        L8_2(L9_2)
        L8_2 = ipairs
        L9_2 = L7_2
        L8_2, L9_2, L10_2, L11_2 = L8_2(L9_2)
        for L12_2, L13_2 in L8_2, L9_2, L10_2, L11_2 do
          L14_2 = Debuger
          L15_2 = "- "
          L16_2 = L13_2
          L15_2 = L15_2 .. L16_2
          L14_2(L15_2)
        end
      end
    else
      L7_2 = Debuger
      L8_2 = "The table "
      L9_2 = L5_2
      L10_2 = " does not exist."
      L8_2 = L8_2 .. L9_2 .. L10_2
      L7_2(L8_2)
    end
  end
end
checkTables = L2_1
function L2_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L2_2 = type
  L3_2 = A0_2
  L2_2 = L2_2(L3_2)
  if "string" ~= L2_2 then
    return
  end
  L2_2 = L1_1
  L2_2 = L2_2[A0_2]
  if L2_2 then
    L2_2 = Debuger
    L3_2 = "The table "
    L4_2 = A0_2
    L5_2 = " exist in SQLTables"
    L3_2 = L3_2 .. L4_2 .. L5_2
    L2_2(L3_2)
    return
  end
  L2_2 = {}
  L3_2 = ipairs
  L4_2 = A1_2
  L3_2, L4_2, L5_2, L6_2 = L3_2(L4_2)
  for L7_2, L8_2 in L3_2, L4_2, L5_2, L6_2 do
    L9_2 = #L2_2
    L9_2 = L9_2 + 1
    L2_2[L9_2] = L8_2
    L9_2 = L1_1
    L9_2[A0_2] = L2_2
  end
  L3_2 = checkTables
  L4_2 = L1_1
  L3_2(L4_2)
end
AddTableData = L2_1
function L2_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2
  L2_2 = {}
  L3_2 = ipairs
  L4_2 = A1_2
  L3_2, L4_2, L5_2, L6_2 = L3_2(L4_2)
  for L7_2, L8_2 in L3_2, L4_2, L5_2, L6_2 do
    L9_2 = string
    L9_2 = L9_2.format
    L10_2 = "SHOW COLUMNS FROM %s LIKE '%s'"
    L11_2 = A0_2
    L12_2 = L8_2
    L9_2 = L9_2(L10_2, L11_2, L12_2)
    L10_2 = MySQL
    L10_2 = L10_2.awaitScalar
    L11_2 = L9_2
    L10_2 = L10_2(L11_2)
    if not L10_2 then
      L11_2 = #L2_2
      L11_2 = L11_2 + 1
      L2_2[L11_2] = L8_2
    end
  end
  return L2_2
end
checkColumns = L2_1
L2_1 = exports
L3_1 = "AddTableData"
L4_1 = AddTableData
L2_1(L3_1, L4_1)
L2_1 = exports
L3_1 = "checkTables"
L4_1 = checkTables
L2_1(L3_1, L4_1)
