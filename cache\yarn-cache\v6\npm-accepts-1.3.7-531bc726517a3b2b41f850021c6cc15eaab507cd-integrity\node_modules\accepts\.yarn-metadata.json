{"manifest": {"name": "accepts", "description": "Higher-level content negotiation", "version": "1.3.7", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/accepts.git"}, "dependencies": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}, "devDependencies": {"deep-equal": "1.0.1", "eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.17.2", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "mocha": "6.1.4", "nyc": "14.0.0"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "keywords": ["content", "negotiation", "accept", "accepts"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-accepts-1.3.7-531bc726517a3b2b41f850021c6cc15eaab507cd-integrity\\node_modules\\accepts\\package.json", "readmeFilename": "README.md", "readme": "# accepts\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nHigher level content negotiation based on [negotiator](https://www.npmjs.com/package/negotiator).\nExtracted from [koa](https://www.npmjs.com/package/koa) for general use.\n\nIn addition to negotiator, it allows:\n\n- Allows types as an array or arguments list, ie `(['text/html', 'application/json'])`\n  as well as `('text/html', 'application/json')`.\n- Allows type shorthands such as `json`.\n- Returns `false` when no types match\n- Treats non-existent headers as `*`\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install accepts\n```\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar accepts = require('accepts')\n```\n\n### accepts(req)\n\nCreate a new `Accepts` object for the given `req`.\n\n#### .charset(charsets)\n\nReturn the first accepted charset. If nothing in `charsets` is accepted,\nthen `false` is returned.\n\n#### .charsets()\n\nReturn the charsets that the request accepts, in the order of the client's\npreference (most preferred first).\n\n#### .encoding(encodings)\n\nReturn the first accepted encoding. If nothing in `encodings` is accepted,\nthen `false` is returned.\n\n#### .encodings()\n\nReturn the encodings that the request accepts, in the order of the client's\npreference (most preferred first).\n\n#### .language(languages)\n\nReturn the first accepted language. If nothing in `languages` is accepted,\nthen `false` is returned.\n\n#### .languages()\n\nReturn the languages that the request accepts, in the order of the client's\npreference (most preferred first).\n\n#### .type(types)\n\nReturn the first accepted type (and it is returned as the same text as what\nappears in the `types` array). If nothing in `types` is accepted, then `false`\nis returned.\n\nThe `types` array can contain full MIME types or file extensions. Any value\nthat is not a full MIME types is passed to `require('mime-types').lookup`.\n\n#### .types()\n\nReturn the types that the request accepts, in the order of the client's\npreference (most preferred first).\n\n## Examples\n\n### Simple type negotiation\n\nThis simple example shows how to use `accepts` to return a different typed\nrespond body based on what the client wants to accept. The server lists it's\npreferences in order and will get back the best match between the client and\nserver.\n\n```js\nvar accepts = require('accepts')\nvar http = require('http')\n\nfunction app (req, res) {\n  var accept = accepts(req)\n\n  // the order of this list is significant; should be server preferred order\n  switch (accept.type(['json', 'html'])) {\n    case 'json':\n      res.setHeader('Content-Type', 'application/json')\n      res.write('{\"hello\":\"world!\"}')\n      break\n    case 'html':\n      res.setHeader('Content-Type', 'text/html')\n      res.write('<b>hello, world!</b>')\n      break\n    default:\n      // the fallback is text/plain, so no need to specify it above\n      res.setHeader('Content-Type', 'text/plain')\n      res.write('hello, world!')\n      break\n  }\n\n  res.end()\n}\n\nhttp.createServer(app).listen(3000)\n```\n\nYou can test this out with the cURL program:\n```sh\ncurl -I -H'Accept: text/html' http://localhost:3000/\n```\n\n## License\n\n[MIT](LICENSE)\n\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/accepts/master\n[coveralls-url]: https://coveralls.io/r/jshttp/accepts?branch=master\n[node-version-image]: https://badgen.net/npm/node/accepts\n[node-version-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/accepts\n[npm-url]: https://npmjs.org/package/accepts\n[npm-version-image]: https://badgen.net/npm/v/accepts\n[travis-image]: https://badgen.net/travis/jshttp/accepts/master\n[travis-url]: https://travis-ci.org/jshttp/accepts\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2014 <PERSON> <<EMAIL>>\nCopyright (c) 2015 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR CO<PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/accepts/-/accepts-1.3.7.tgz#531bc726517a3b2b41f850021c6cc15eaab507cd", "type": "tarball", "reference": "https://registry.yarnpkg.com/accepts/-/accepts-1.3.7.tgz", "hash": "531bc726517a3b2b41f850021c6cc15eaab507cd", "integrity": "sha512-Il80Qs2WjYlJIBNzNkK6KYqlVMTbZLXgHx2oT0pU/fjRHyEp+PEfEPY0R3WCwAGVOtauxh1hOxNgIf5bv7dQpA==", "registry": "npm", "packageName": "accepts", "cacheIntegrity": "sha512-Il80Qs2WjYlJIBNzNkK6KYqlVMTbZLXgHx2oT0pU/fjRHyEp+PEfEPY0R3WCwAGVOtauxh1hOxNgIf5bv7dQpA== sha1-UxvHJlF6OytB+FACHGzBXqq1B80="}, "registry": "npm", "hash": "531bc726517a3b2b41f850021c6cc15eaab507cd"}