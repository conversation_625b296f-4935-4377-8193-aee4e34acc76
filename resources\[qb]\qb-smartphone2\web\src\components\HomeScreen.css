.home-screen {
  width: 100%;
  height: 100%;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.home-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(1px);
  z-index: 1;
}

.home-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  padding-top: 40px;
}

/* Time Widget */
.time-widget {
  text-align: center;
  color: white;
  margin-bottom: 30px;
}

.time-large {
  font-size: 48px;
  font-weight: 200;
  letter-spacing: -1px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  margin-bottom: 4px;
}

.date-small {
  font-size: 16px;
  font-weight: 500;
  opacity: 0.9;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
  text-transform: capitalize;
}

/* Quick Actions */
.quick-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 40px;
}

.quick-action {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.quick-action:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.quick-action:active {
  transform: scale(0.95);
}

.quick-action .badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #FF3B30;
  color: white;
  border-radius: 10px;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  border: 2px solid white;
}

/* App Grid */
.app-grid-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.app-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 0 10px;
  flex: 1;
  align-content: start;
}

.app-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.app-item:hover {
  transform: scale(1.05);
}

.app-item:active {
  transform: scale(0.95);
}

.app-icon {
  width: 60px;
  height: 60px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  margin-bottom: 8px;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.app-icon:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.app-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #FF3B30;
  color: white;
  border-radius: 10px;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  border: 2px solid white;
}

.app-name {
  color: white;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  max-width: 70px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Page Indicators */
.page-indicators {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin: 20px 0;
}

.page-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-dot:hover {
  transform: scale(1.2);
}

.page-dot.active {
  transform: scale(1.3);
}

/* Dock */
.dock {
  height: 80px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin: 0 -10px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.dock-item {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dock-item:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.dock-item:active {
  transform: scale(0.95);
}

.dock-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #FF3B30;
  color: white;
  border-radius: 10px;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  border: 2px solid white;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .home-content {
    padding: 16px;
    padding-top: 30px;
  }
  
  .time-large {
    font-size: 36px;
  }
  
  .date-small {
    font-size: 14px;
  }
  
  .app-grid {
    gap: 16px;
    padding: 0 5px;
  }
  
  .app-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
    border-radius: 12px;
  }
  
  .app-name {
    font-size: 11px;
    max-width: 60px;
  }
  
  .dock {
    height: 70px;
    gap: 25px;
    border-radius: 20px;
  }
  
  .dock-item {
    width: 45px;
    height: 45px;
    font-size: 18px;
  }
  
  .quick-actions {
    gap: 16px;
    margin-bottom: 30px;
  }
  
  .quick-action {
    width: 45px;
    height: 45px;
    font-size: 18px;
  }
}
