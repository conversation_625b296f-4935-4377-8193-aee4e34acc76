{"name": "upper-case", "version": "1.1.3", "description": "Upper case a string", "main": "upper-case.js", "typings": "upper-case.d.ts", "files": ["upper-case.js", "upper-case.d.ts", "LICENSE"], "scripts": {"lint": "standard", "test-std": "mocha -- -R spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail", "test": "npm run lint && npm run test-cov"}, "standard": {"ignore": ["coverage/**", "node_modules/**", "bower_components/**"]}, "repository": {"type": "git", "url": "git://github.com/blakeembrey/upper-case.git"}, "keywords": ["cases", "upper", "uppercase", "case"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/blakeembrey/upper-case/issues"}, "homepage": "https://github.com/blakeembrey/upper-case", "devDependencies": {"istanbul": "^0.3.5", "mocha": "^2.1.0", "pre-commit": "^1.0.2", "standard": "^2.4.5"}}