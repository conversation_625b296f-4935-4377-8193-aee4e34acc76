{"name": "@types/cookies", "version": "0.7.6", "description": "TypeScript definitions for cookies", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "j<PERSON><PERSON>", "url": "https://github.com/jkeylu", "githubUsername": "j<PERSON>lu"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cookies"}, "scripts": {}, "dependencies": {"@types/connect": "*", "@types/express": "*", "@types/keygrip": "*", "@types/node": "*"}, "typesPublisherContentHash": "09a4522e334d7a4d84019482a9f1b00d7b41792842dc6238a27404c92cde2a72", "typeScriptVersion": "3.3"}