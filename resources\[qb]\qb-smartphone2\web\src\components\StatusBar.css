.status-bar {
  height: 44px;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  background: transparent;
  position: relative;
  z-index: 90;
  margin-top: 8px;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-time {
  font-weight: 700;
  letter-spacing: -0.5px;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Signal indicator */
.signal-indicator {
  display: flex;
  align-items: flex-end;
  gap: 2px;
  height: 12px;
}

.signal-bar {
  width: 3px;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.signal-bar:nth-child(1) { height: 3px; }
.signal-bar:nth-child(2) { height: 6px; }
.signal-bar:nth-child(3) { height: 9px; }
.signal-bar:nth-child(4) { height: 12px; }

.signal-bar.active {
  opacity: 1;
}

.signal-bar:not(.active) {
  opacity: 0.3;
}

/* WiFi indicator */
.wifi-indicator {
  font-size: 12px;
  opacity: 0.9;
}

/* Battery indicator */
.battery-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.charging-icon {
  font-size: 10px;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.battery-container {
  position: relative;
  width: 24px;
  height: 12px;
  border: 1.5px solid currentColor;
  border-radius: 2px;
  display: flex;
  align-items: center;
  opacity: 0.9;
}

.battery-fill {
  height: 100%;
  border-radius: 1px;
  transition: all 0.3s ease;
  min-width: 2px;
}

.battery-tip {
  position: absolute;
  right: -3px;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 6px;
  border-radius: 0 1px 1px 0;
  opacity: 0.9;
}

.battery-percentage {
  font-size: 11px;
  font-weight: 600;
  opacity: 0.8;
  min-width: 28px;
  text-align: right;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .status-bar {
    height: 38px;
    padding: 0 16px;
    font-size: 13px;
  }
  
  .battery-percentage {
    display: none;
  }
  
  .status-right {
    gap: 6px;
  }
}
