local Translations = {
    error = {
        blips_deactivated = 'Blips desativados',
        names_deactivated = 'Nomes desativados',
        changed_perm_failed = 'Escolha um grupo!',
        missing_reason = 'Você deve fornecer uma razão!',
        invalid_reason_length_ban = 'Você deve fornecer uma Razão e definir um Tempo para o banimento!',
        no_store_vehicle_garage = 'Você não pode guardar este veículo na sua garagem..',
        no_vehicle = 'Você não está em um veículo..',
        no_weapon = 'Você não tem uma arma nas mãos..',
        no_free_seats = 'O veículo não tem assentos livres!',
        failed_vehicle_owner = 'Este veículo já é seu..',
        not_online = 'Este jogador não está online',
        no_receive_report = 'Você não está recebendo relatórios',
        failed_set_speed = 'Você não definiu uma velocidade.. (`fast` para super-corrida, `normal` para normal)',
        failed_set_model = 'Você não definiu um modelo..',
        failed_entity_copy = 'Nenhuma informação de entidade freeaim disponível para copiar para a área de transferência!',
    },
    success = {
        blips_activated = 'Blips ativados',
        names_activated = 'Nomes ativados',
        coords_copied = 'Coordenadas copiadas para a área de transferência!',
        heading_copied = 'Direção copiada para a área de transferência!',
        changed_perm = 'Grupo de autoridade alterado',
        entered_vehicle = 'Entrou no veículo',
        success_vehicle_owner = 'Agora, o veículo é seu!',
        receive_reports = 'Você está recebendo relatórios',
        entity_copy = 'Informações da entidade freeaim copiadas para a área de transferência!',
        spawn_weapon = 'Você gerou uma arma',
        noclip_enabled = 'Modo noclip ativado',
        noclip_disabled = 'Modo noclip desativado',
    },
    info = {
        ped_coords = 'Coordenadas do Ped:',
        vehicle_dev_data = 'Dados de Desenvolvimento do Veículo:',
        ent_id = 'ID da Entidade:',
        net_id = 'ID de Rede:',
        net_id_not_registered = 'Não registrado',
        model = 'Modelo',
        hash = 'Hash',
        eng_health = 'Saúde do Motor:',
        body_health = 'Saúde da Carroçaria:',
        go_to = 'Ir para',
        remove = 'Remover',
        confirm = 'Confirmar',
        reason_title = 'Razão',
        length = 'Tempo',
        options = 'Opções',
        position = 'Posição',
        your_position = 'para a sua posição',
        open = 'Abrir',
        inventories = 'inventários',
        reason = 'você precisa fornecer uma razão',
        give = 'dar',
        id = 'ID:',
        player_name = 'Nome do Jogador',
        obj = 'Obj',
        ammoforthe = '+%{value} Munição para %{weapon}',
        kicked_server = 'Você foi expulso do servidor',
        check_discord = '🔸 Verifique o nosso Discord para mais informações: ',
        banned = 'Você foi banido:',
        ban_perm = '\n\nSeu banimento é permanente.\n🔸 Verifique o nosso Discord para mais informações: ',
        ban_expires = '\n\nBanimento expira em: ',
        rank_level = 'Seu Nível de Permissão agora é ',
        admin_report = 'Relatório de Administrador - ',
        staffchat = 'CHAT DA EQUIPE - ',
        warning_chat_message = '^8ATENÇÃO ^7 Você recebeu um aviso de',
        warning_staff_message = '^8ATENÇÃO ^7 Você emitiu um aviso para',
        no_reason_specified = 'Nenhuma razão especificada',
        server_restart = 'Reinício do servidor, verifique o nosso Discord para mais informações: ',
        entity_view_distance = 'Distância de visualização da entidade definida para: %{distance} metros',
        entity_view_info = 'Informações da Entidade',
        entity_view_title = 'Modo Freeaim da Entidade',
        entity_freeaim_delete = 'Excluir Entidade',
        entity_freeaim_freeze = 'Congelar Entidade',
        entity_frozen = 'Congelada',
        entity_unfrozen = 'Descongelada',
        model_hash = 'Hash do Modelo:',
        obj_name = 'Nome do Objeto:',
        ent_owner = 'Proprietário da Entidade:',
        cur_health = 'Saúde Atual:',
        max_health = 'Saúde Máxima:',
        armour = 'Armadura:',
        rel_group = 'Grupo de Relação:',
        rel_to_player = 'Relação com o Jogador:',
        rel_group_custom = 'Relação Personalizada',
        veh_acceleration = 'Aceleração:',
        veh_cur_gear = 'Marcha Atual:',
        veh_speed_kph = 'Kph:',
        veh_speed_mph = 'Mph:',
        veh_rpm = 'Rpm:',
        dist_to_obj = 'Distância:',
        obj_heading = 'Direção:',
        obj_coords = 'Coordenadas:',
        obj_rot = 'Rotação:',
        obj_velocity = 'Velocidade:',
        obj_unknown = 'Desconhecido',
        you_have = 'Você tem ',
        freeaim_entity = ' a entidade freeaim',
        entity_del = 'Entidade excluída',
        entity_del_error = 'Erro ao excluir a entidade',
    },
    menu = {
        admin_menu = 'Menu de Administrador',
        admin_options = 'Opções de Administrador',
        online_players = 'Jogadores Online',
        manage_server = 'Gerenciar Servidor',
        weather_conditions = 'Opções de Clima Disponíveis',
        dealer_list = 'Lista de Concessionárias',
        ban = 'Banir',
        kick = 'Expulsar',
        permissions = 'Permissões',
        developer_options = 'Opções de Desenvolvedor',
        vehicle_options = 'Opções de Veículo',
        vehicle_categories = 'Categorias de Veículos',
        vehicle_models = 'Modelos de Veículos',
        player_management = 'Gerenciamento de Jogadores',
        server_management = 'Gerenciamento de Servidor',
        vehicles = 'Veículos',
        noclip = 'NoClip',
        revive = 'Reviver',
        invisible = 'Invisível',
        god = 'Modo Deus',
        names = 'Nomes',
        blips = 'Blips',
        weather_options = 'Opções de Clima',
        server_time = 'Hora do Servidor',
        time = 'Tempo',
        copy_vector3 = 'Copiar vector3',
        copy_vector4 = 'Copiar vector4',
        display_coords = 'Mostrar Coordenadas',
        copy_heading = 'Copiar Direção',
        vehicle_dev_mode = 'Modo de Desenvolvimento de Veículo',
        spawn_vehicle = 'Gerar Veículo',
        fix_vehicle = 'Reparar Veículo',
        buy = 'Comprar',
        remove_vehicle = 'Remover Veículo',
        edit_dealer = 'Editar Concessionária',
        dealer_name = 'Nome da Concessionária',
        category_name = 'Nome da Categoria',
        kill = 'Matar',
        freeze = 'Congelar',
        spectate = 'Espectar',
        bring = 'Trazer',
        sit_in_vehicle = 'Sentar no Veículo',
        open_inv = 'Abrir Inventário',
        give_clothing_menu = 'Dar Menu de Roupas',
        hud_dev_mode = 'Modo de Desenvolvimento (qb-hud)',
        entity_view_options = 'Modo de Visualização da Entidade',
        entity_view_distance = 'Definir Distância de Visualização',
        entity_view_freeaim = 'Modo Freeaim da Entidade',
        entity_view_peds = 'Mostrar Peds',
        entity_view_vehicles = 'Mostrar Veículos',
        entity_view_objects = 'Mostrar Objetos',
        entity_view_freeaim_copy = 'Copiar Informações da Entidade Freeaim',
        spawn_weapons = 'Gerar Armas',
        max_mods = 'Máximo de Modificações do Carro',
    },
    desc = {
        admin_options_desc = 'Opções de Administração Variadas',
        player_management_desc = 'Ver Lista de Jogadores',
        server_management_desc = 'Opções Variadas de Servidor',
        vehicles_desc = 'Opções de Veículos',
        dealer_desc = 'Lista de Concessionárias Existente',
        noclip_desc = 'Ativar/Desativar NoClip',
        revive_desc = 'Reviver Você Mesmo',
        invisible_desc = 'Ativar/Desativar Invisibilidade',
        god_desc = 'Ativar/Desativar Modo Deus',
        names_desc = 'Ativar/Desativar Nomes acima da cabeça',
        blips_desc = 'Ativar/Desativar Blips para jogadores nos mapas',
        weather_desc = 'Alterar o Clima',
        developer_desc = 'Opções de Desenvolvimento Variadas',
        vector3_desc = 'Copiar vector3 para a área de transferência',
        vector4_desc = 'Copiar vector4 para a área de transferência',
        display_coords_desc = 'Mostrar Coordenadas na Tela',
        copy_heading_desc = 'Copiar Direção para a área de transferência',
        vehicle_dev_mode_desc = 'Mostrar Informações do Veículo',
        delete_laser_desc = 'Ativar/Desativar Laser',
        spawn_vehicle_desc = 'Gerar um veículo',
        fix_vehicle_desc = 'Reparar o veículo em que você está',
        buy_desc = 'Comprar o veículo gratuitamente',
        remove_vehicle_desc = 'Remover o veículo mais próximo',
        dealergoto_desc = 'Ir para a concessionária',
        dealerremove_desc = 'Remover a concessionária',
        kick_reason = 'Razão do Expulsar',
        confirm_kick = 'Confirmar o expulsar',
        ban_reason = 'Razão do Banir',
        confirm_ban = 'Confirmar o banir',
        sit_in_veh_desc = 'Sentar em',
        sit_in_veh_desc2 = "'s veículo",
        clothing_menu_desc = 'Dar o menu de roupas para',
        hud_dev_mode_desc = 'Ativar/Desativar Modo de Desenvolvimento',
        entity_view_desc = 'Ver informações sobre entidades',
        entity_view_freeaim_desc = 'Ativar/Desativar informações da entidade via freeaim',
        entity_view_peds_desc = 'Ativar/Desativar informações de peds no mundo',
        entity_view_vehicles_desc = 'Ativar/Desativar informações de veículos no mundo',
        entity_view_objects_desc = 'Ativar/Desativar informações de objetos no mundo',
        entity_view_freeaim_copy_desc = 'Copiar informações da entidade freeaim para a área de transferência',
        spawn_weapons_desc = 'Gerar Qualquer Arma',
        max_mod_desc = 'Máximo de modificações para o seu veículo atual',
    },
    time = {
        ban_length = 'Duração do Banimento',
        onehour = '1 hora',
        sixhour = '6 horas',
        twelvehour = '12 horas',
        oneday = '1 dia',
        threeday = '3 dias',
        oneweek = '1 semana',
        onemonth = '1 mês',
        threemonth = '3 meses',
        sixmonth = '6 meses',
        oneyear = '1 ano',
        permanent = 'Permanente',
        self = 'Por Conta Própria',
        changed = 'Tempo alterado para %{time} hs 00 min',
    },
    weather = {
        extra_sunny = 'Extra Ensolarado',
        extra_sunny_desc = 'Estou Derretendo!',
        clear = 'Limpo',
        clear_desc = 'O Dia Perfeito!',
        neutral = 'Neutro',
        neutral_desc = 'Apenas um Dia Normal!',
        smog = 'Neblina',
        smog_desc = 'Máquina de Fumaça!',
        foggy = 'Nevoeiro',
        foggy_desc = 'Máquina de Fumaça x2!',
        overcast = 'Nublado',
        overcast_desc = 'Não Muito Ensolarado!',
        clouds = 'Nuvens',
        clouds_desc = 'Onde Está o Sol?',
        clearing = 'Limpo',
        clearing_desc = 'As Nuvens Começam a se Dissipar!',
        rain = 'Chuva',
        rain_desc = 'Faça Chover!',
        thunder = 'Trovão',
        thunder_desc = 'Corra e Se Esconda!',
        snow = 'Neve',
        snow_desc = 'Está Frio Aqui Fora?',
        blizzard = 'Nevasca',
        blizzed_desc = 'Máquina de Neve?',
        light_snow = 'Neve Leve',
        light_snow_desc = 'Começando a Parecer o Natal!',
        heavy_snow = 'Neve Pesada (NATAL)',
        heavy_snow_desc = 'Luta de Bolas de Neve!',
        halloween = 'Halloween',
        halloween_desc = 'O Que Foi Esse Barulho?!',
        weather_changed = 'Clima Alterado Para: %{value}',
    },
    commands = {
        blips_for_player = 'Mostrar blips para jogadores (Apenas para Admins)',
        player_name_overhead = 'Mostrar nome do jogador acima da cabeça (Apenas para Admins)',
        coords_dev_command = 'Ativar exibição de coordenadas para coisas de desenvolvimento (Apenas para Admins)',
        toogle_noclip = 'Alternar noclip (Apenas para Admins)',
        save_vehicle_garage = 'Salvar Veículo na sua Garagem (Apenas para Admins)',
        make_announcement = 'Fazer um Anúncio (Apenas para Admins)',
        open_admin = 'Abrir Menu de Administrador (Apenas para Admins)',
        staffchat_message = 'Enviar uma mensagem para toda a equipe (Apenas para Admins)',
        nui_focus = 'Dar o foco NUI a um jogador (Apenas para Admins)',
        warn_a_player = 'Avisar um jogador (Apenas para Admins)',
        check_player_warning = 'Verificar avisos de jogador (Apenas para Admins)',
        delete_player_warning = 'Excluir avisos de jogador (Apenas para Admins)',
        reply_to_report = 'Responder a um relatório (Apenas para Admins)',
        change_ped_model = 'Mudar o modelo do ped (Apenas para Admins)',
        set_player_foot_speed = 'Definir a velocidade do pé do jogador (Apenas para Admins)',
        report_toggle = 'Alternar relatórios recebidos (Apenas para Admins)',
        kick_all = 'Expulsar todos os jogadores',
        ammo_amount_set = 'Definir a quantidade de munição (Apenas para Admins)',
    }
}

if GetConvar('qb_locale', 'en') == 'pt-br' then
    Lang = Locale:new({
        phrases = Translations,
        warnOnMissing = true,
        fallbackLang = Lang,
    })
end
