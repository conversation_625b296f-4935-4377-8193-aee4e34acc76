local QBCore = exports['qb-core']:GetCoreObject()

-- Callback pro získání bankovn<PERSON><PERSON>
QBCore.Functions.CreateCallback('qb-smartphone2:server:getBankData', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb(nil) return end
    
    local bankData = {
        accountNumber = Player.PlayerData.charinfo.account,
        balance = Player.PlayerData.money.bank or 0,
        cash = Player.PlayerData.money.cash or 0,
        crypto = Player.PlayerData.money.crypto or 0
    }
    
    cb(bankData)
end)

-- Callback pro získání bankovní historie
QBCore.Functions.CreateCallback('qb-smartphone2:server:getBankHistory', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb({}) return end
    
    local citizenid = Player.PlayerData.citizenid
    
    -- Pokud máš qb-banking nebo jiný banking systém, pou<PERSON><PERSON>j jeho tabulky
    -- <PERSON><PERSON> vlastní tabulku pro transakce
    MySQL.Async.fetchAll([[
        SELECT * FROM bank_statements 
        WHERE citizenid = ? 
        ORDER BY date DESC 
        LIMIT 50
    ]], {citizenid}, function(result)
        cb(result or {})
    end)
end)

-- Event pro převod peněz
RegisterNetEvent('qb-smartphone2:server:transferMoney', function(transferData)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local amount = tonumber(transferData.amount)
    local targetAccount = transferData.targetAccount
    local description = transferData.description or 'Převod z telefonu'
    
    -- Validace
    if not amount or amount <= 0 then
        TriggerClientEvent('QBCore:Notify', src, 'Neplatná částka!', 'error')
        return
    end
    
    if amount > Config.MaxTransferAmount then
        TriggerClientEvent('QBCore:Notify', src, 'Částka překračuje limit!', 'error')
        return
    end
    
    local playerBalance = Player.PlayerData.money.bank or 0
    if amount > playerBalance then
        TriggerClientEvent('QBCore:Notify', src, 'Nedostatečný zůstatek!', 'error')
        return
    end
    
    -- Najdi cílového hráče podle čísla účtu
    local TargetPlayer = QBCore.Functions.GetPlayerByAccountNumber(targetAccount)
    if not TargetPlayer then
        TriggerClientEvent('QBCore:Notify', src, 'Cílový účet nenalezen!', 'error')
        return
    end
    
    -- Proveď převod
    Player.Functions.RemoveMoney('bank', amount, 'phone-transfer')
    TargetPlayer.Functions.AddMoney('bank', amount, 'phone-transfer-received')
    
    -- Zaznamenej transakci
    local senderName = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname
    local receiverName = TargetPlayer.PlayerData.charinfo.firstname .. ' ' .. TargetPlayer.PlayerData.charinfo.lastname
    
    -- Pro odesílatele
    MySQL.Async.execute([[
        INSERT INTO bank_statements (citizenid, account, amount, type, description, balance) 
        VALUES (?, ?, ?, ?, ?, ?)
    ]], {
        Player.PlayerData.citizenid,
        Player.PlayerData.charinfo.account,
        -amount,
        'transfer_out',
        'Převod pro ' .. receiverName .. ': ' .. description,
        Player.PlayerData.money.bank
    })
    
    -- Pro příjemce
    MySQL.Async.execute([[
        INSERT INTO bank_statements (citizenid, account, amount, type, description, balance) 
        VALUES (?, ?, ?, ?, ?, ?)
    ]], {
        TargetPlayer.PlayerData.citizenid,
        TargetPlayer.PlayerData.charinfo.account,
        amount,
        'transfer_in',
        'Převod od ' .. senderName .. ': ' .. description,
        TargetPlayer.PlayerData.money.bank
    })
    
    TriggerClientEvent('QBCore:Notify', src, 'Převod byl úspěšný!', 'success')
    TriggerClientEvent('QBCore:Notify', TargetPlayer.PlayerData.source, 'Obdržel jsi převod ' .. amount .. '$ od ' .. senderName, 'success')
    
    -- Odeslání notifikace příjemci
    exports['qb-smartphone2']:SendNotification(TargetPlayer.PlayerData.charinfo.phone, 'Bankovní převod', 'Obdržel jsi ' .. amount .. '$ od ' .. senderName, 'banking', {
        amount = amount,
        sender = senderName
    })
end)

-- Event pro výběr z bankomatu
RegisterNetEvent('qb-smartphone2:server:withdrawMoney', function(amount)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    amount = tonumber(amount)
    
    if not amount or amount <= 0 then
        TriggerClientEvent('QBCore:Notify', src, 'Neplatná částka!', 'error')
        return
    end
    
    local playerBalance = Player.PlayerData.money.bank or 0
    if amount > playerBalance then
        TriggerClientEvent('QBCore:Notify', src, 'Nedostatečný zůstatek!', 'error')
        return
    end
    
    -- Proveď výběr
    Player.Functions.RemoveMoney('bank', amount, 'phone-withdrawal')
    Player.Functions.AddMoney('cash', amount, 'phone-withdrawal')
    
    -- Zaznamenej transakci
    MySQL.Async.execute([[
        INSERT INTO bank_statements (citizenid, account, amount, type, description, balance) 
        VALUES (?, ?, ?, ?, ?, ?)
    ]], {
        Player.PlayerData.citizenid,
        Player.PlayerData.charinfo.account,
        -amount,
        'withdrawal',
        'Výběr z telefonu',
        Player.PlayerData.money.bank
    })
    
    TriggerClientEvent('QBCore:Notify', src, 'Vybral jsi ' .. amount .. '$', 'success')
end)

-- Event pro vklad do banky
RegisterNetEvent('qb-smartphone2:server:depositMoney', function(amount)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    amount = tonumber(amount)
    
    if not amount or amount <= 0 then
        TriggerClientEvent('QBCore:Notify', src, 'Neplatná částka!', 'error')
        return
    end
    
    local playerCash = Player.PlayerData.money.cash or 0
    if amount > playerCash then
        TriggerClientEvent('QBCore:Notify', src, 'Nemáš dostatek hotovosti!', 'error')
        return
    end
    
    -- Proveď vklad
    Player.Functions.RemoveMoney('cash', amount, 'phone-deposit')
    Player.Functions.AddMoney('bank', amount, 'phone-deposit')
    
    -- Zaznamenej transakci
    MySQL.Async.execute([[
        INSERT INTO bank_statements (citizenid, account, amount, type, description, balance) 
        VALUES (?, ?, ?, ?, ?, ?)
    ]], {
        Player.PlayerData.citizenid,
        Player.PlayerData.charinfo.account,
        amount,
        'deposit',
        'Vklad z telefonu',
        Player.PlayerData.money.bank
    })
    
    TriggerClientEvent('QBCore:Notify', src, 'Vložil jsi ' .. amount .. '$', 'success')
end)

-- Event pro QR platbu
RegisterNetEvent('qb-smartphone2:server:processQRPayment', function(qrData)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local amount = tonumber(qrData.amount)
    local targetNumber = qrData.phoneNumber
    local description = qrData.description or 'QR platba'
    
    if not amount or amount <= 0 then
        TriggerClientEvent('QBCore:Notify', src, 'Neplatná částka!', 'error')
        return
    end
    
    local playerBalance = Player.PlayerData.money.bank or 0
    if amount > playerBalance then
        TriggerClientEvent('QBCore:Notify', src, 'Nedostatečný zůstatek!', 'error')
        return
    end
    
    -- Najdi cílového hráče podle telefonního čísla
    local TargetPlayer = QBCore.Functions.GetPlayerByPhone(targetNumber)
    if not TargetPlayer then
        TriggerClientEvent('QBCore:Notify', src, 'Příjemce není online!', 'error')
        return
    end
    
    -- Proveď platbu
    Player.Functions.RemoveMoney('bank', amount, 'qr-payment')
    TargetPlayer.Functions.AddMoney('bank', amount, 'qr-payment-received')
    
    local senderName = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname
    local receiverName = TargetPlayer.PlayerData.charinfo.firstname .. ' ' .. TargetPlayer.PlayerData.charinfo.lastname
    
    TriggerClientEvent('QBCore:Notify', src, 'QR platba byla úspěšná!', 'success')
    TriggerClientEvent('QBCore:Notify', TargetPlayer.PlayerData.source, 'Obdržel jsi QR platbu ' .. amount .. '$ od ' .. senderName, 'success')
    
    -- Odeslání notifikace příjemci
    exports['qb-smartphone2']:SendNotification(TargetPlayer.PlayerData.charinfo.phone, 'QR Platba', 'Obdržel jsi ' .. amount .. '$ od ' .. senderName, 'banking', {
        amount = amount,
        sender = senderName,
        type = 'qr_payment'
    })
end)

-- Callback pro generování QR kódu
QBCore.Functions.CreateCallback('qb-smartphone2:server:generateQRCode', function(source, cb, qrData)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then cb(nil) return end
    
    local qrCode = {
        id = 'qr_' .. math.random(100000, 999999),
        phoneNumber = Player.PlayerData.charinfo.phone,
        amount = qrData.amount,
        description = qrData.description,
        expires = os.time() + 300, -- 5 minut
        created_by = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname
    }
    
    cb(qrCode)
end)

-- Callback pro ověření QR kódu
QBCore.Functions.CreateCallback('qb-smartphone2:server:verifyQRCode', function(source, cb, qrId)
    -- V reálné implementaci bys měl ukládat QR kódy do databáze nebo cache
    -- Pro jednoduchost zde jen vracíme true
    cb(true)
end)

-- Event pro platbu účtu (faktury)
RegisterNetEvent('qb-smartphone2:server:payBill', function(billData)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local amount = tonumber(billData.amount)
    local billType = billData.type
    local description = billData.description or 'Platba účtu'
    
    if not amount or amount <= 0 then
        TriggerClientEvent('QBCore:Notify', src, 'Neplatná částka!', 'error')
        return
    end
    
    local playerBalance = Player.PlayerData.money.bank or 0
    if amount > playerBalance then
        TriggerClientEvent('QBCore:Notify', src, 'Nedostatečný zůstatek!', 'error')
        return
    end
    
    -- Proveď platbu
    Player.Functions.RemoveMoney('bank', amount, 'bill-payment')
    
    -- Zaznamenej transakci
    MySQL.Async.execute([[
        INSERT INTO bank_statements (citizenid, account, amount, type, description, balance) 
        VALUES (?, ?, ?, ?, ?, ?)
    ]], {
        Player.PlayerData.citizenid,
        Player.PlayerData.charinfo.account,
        -amount,
        'bill_payment',
        'Platba účtu: ' .. description,
        Player.PlayerData.money.bank
    })
    
    TriggerClientEvent('QBCore:Notify', src, 'Účet byl zaplacen!', 'success')
    
    -- Integrace s jinými systémy (např. qb-phone bills)
    if billType == 'phone' then
        -- Zaplatit telefonní účet
    elseif billType == 'electricity' then
        -- Zaplatit elektřinu
    end
end)

-- Export funkce pro jiné scripty
exports('TransferMoney', function(senderSource, targetAccount, amount, description)
    local Player = QBCore.Functions.GetPlayer(senderSource)
    if not Player then return false end
    
    TriggerEvent('qb-smartphone2:server:transferMoney', {
        targetAccount = targetAccount,
        amount = amount,
        description = description
    })
    
    return true
end)

exports('GetBankBalance', function(source)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return 0 end
    
    return Player.PlayerData.money.bank or 0
end)

exports('ProcessPayment', function(payerSource, receiverPhone, amount, description)
    local PayerPlayer = QBCore.Functions.GetPlayer(payerSource)
    local ReceiverPlayer = QBCore.Functions.GetPlayerByPhone(receiverPhone)
    
    if not PayerPlayer or not ReceiverPlayer then return false end
    
    local payerBalance = PayerPlayer.PlayerData.money.bank or 0
    if amount > payerBalance then return false end
    
    PayerPlayer.Functions.RemoveMoney('bank', amount, 'phone-payment')
    ReceiverPlayer.Functions.AddMoney('bank', amount, 'phone-payment-received')
    
    return true
end)
