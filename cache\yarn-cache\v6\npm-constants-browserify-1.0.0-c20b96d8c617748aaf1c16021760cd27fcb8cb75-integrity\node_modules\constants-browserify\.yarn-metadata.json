{"manifest": {"name": "constants-browserify", "description": "node's constants module for the browser", "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/constants-browserify.git"}, "homepage": "https://github.com/juliangruber/constants-browserify", "main": "constants.json", "dependencies": {}, "keywords": ["constants", "node", "browser", "browserify"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "scripts": {"test": "node test.js"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://netflix.com"}], "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-constants-browserify-1.0.0-c20b96d8c617748aaf1c16021760cd27fcb8cb75-integrity\\node_modules\\constants-browserify\\package.json", "readmeFilename": "README.md", "readme": "\n# constants-browserify\n\nNode's `constants` module for the browser.\n\n[![downloads](https://img.shields.io/npm/dm/constants-browserify.svg)](https://www.npmjs.org/package/constants-browserify)\n\n## Usage\n\nTo use with browserify cli:\n\n```bash\n$ browserify -r constants:constants-browserify script.js\n```\n\nTo use with browserify api:\n\n```js\nbrowserify()\n  .require('constants-browserify', { expose: 'constants' })\n  .add(__dirname + '/script.js')\n  .bundle()\n  // ...\n```\n\n## Installation\n\nWith [npm](http://npmjs.org) do\n\n```bash\n$ npm install constants-browserify\n```\n\n## License\n\nCopyright (c) 2013 <PERSON>l<PERSON>;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/constants-browserify/-/constants-browserify-1.0.0.tgz#c20b96d8c617748aaf1c16021760cd27fcb8cb75", "type": "tarball", "reference": "https://registry.yarnpkg.com/constants-browserify/-/constants-browserify-1.0.0.tgz", "hash": "c20b96d8c617748aaf1c16021760cd27fcb8cb75", "integrity": "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=", "registry": "npm", "packageName": "constants-browserify", "cacheIntegrity": "sha512-xFxOwqIzR/e1k1gLiWEophSCMqXcwVHIH7akf7b/vxcUeGunlj3hvZaaqxwHsTgn+IndtkQJgSztIDWeumWJDQ== sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U="}, "registry": "npm", "hash": "c20b96d8c617748aaf1c16021760cd27fcb8cb75"}