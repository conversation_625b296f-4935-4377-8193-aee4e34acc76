{"manifest": {"name": "setprot<PERSON>of", "version": "1.2.0", "description": "A small polyfill for Object.setprototypeof", "main": "index.js", "typings": "index.d.ts", "scripts": {"test": "standard && mocha", "testallversions": "npm run node010 && npm run node4 && npm run node6 && npm run node9 && npm run node11", "testversion": "docker run -it --rm -v $(PWD):/usr/src/app -w /usr/src/app node:${NODE_VER} npm install mocha@${MOCHA_VER:-latest} && npm t", "node010": "NODE_VER=0.10 MOCHA_VER=3 npm run testversion", "node4": "NODE_VER=4 npm run testversion", "node6": "NODE_VER=6 npm run testversion", "node9": "NODE_VER=9 npm run testversion", "node11": "NODE_VER=11 npm run testversion", "prepublishOnly": "npm t", "postpublish": "git push origin && git push origin --tags"}, "repository": {"type": "git", "url": "https://github.com/wesleytodd/setprototypeof.git"}, "keywords": ["polyfill", "object", "setprot<PERSON>of"], "author": {"name": "<PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/wesleytodd/setprototypeof/issues"}, "homepage": "https://github.com/wesleytodd/setprototypeof", "devDependencies": {"mocha": "^6.1.4", "standard": "^13.0.2"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-setprototypeof-1.2.0-66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424-integrity\\node_modules\\setprototypeof\\package.json", "readmeFilename": "README.md", "readme": "# Polyfill for `Object.setPrototypeOf`\n\n[![NPM Version](https://img.shields.io/npm/v/setprototypeof.svg)](https://npmjs.org/package/setprototypeof)\n[![NPM Downloads](https://img.shields.io/npm/dm/setprototypeof.svg)](https://npmjs.org/package/setprototypeof)\n[![js-standard-style](https://img.shields.io/badge/code%20style-standard-brightgreen.svg)](https://github.com/standard/standard)\n\nA simple cross platform implementation to set the prototype of an instianted object.  Supports all modern browsers and at least back to IE8.\n\n## Usage:\n\n```\n$ npm install --save setprototypeof\n```\n\n```javascript\nvar setPrototypeOf = require('setprototypeof')\n\nvar obj = {}\nsetPrototypeOf(obj, {\n  foo: function () {\n    return 'bar'\n  }\n})\nobj.foo() // bar\n```\n\nTypeScript is also supported:\n\n```typescript\nimport setPrototypeOf from 'setprototypeof'\n```\n", "licenseText": "Copyright (c) 2015, <PERSON>\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING AL<PERSON> IMPLIED WARRANTIES OF\nMERCHANTABILITY AND F<PERSON>NESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY\nSPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, <PERSON>ATA OR PROFITS, WHETHER IN AN ACTION\nOF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN\nCONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/setprototypeof/-/setprototypeof-1.2.0.tgz#66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424", "type": "tarball", "reference": "https://registry.yarnpkg.com/setprototypeof/-/setprototypeof-1.2.0.tgz", "hash": "66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424", "integrity": "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==", "registry": "npm", "packageName": "setprot<PERSON>of", "cacheIntegrity": "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw== sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ="}, "registry": "npm", "hash": "66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424"}