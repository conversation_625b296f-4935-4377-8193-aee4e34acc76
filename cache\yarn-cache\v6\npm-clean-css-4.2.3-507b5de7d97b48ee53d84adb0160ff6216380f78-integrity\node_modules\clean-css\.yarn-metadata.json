{"manifest": {"name": "clean-css", "version": "4.2.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jakub<PERSON><PERSON>owicz"}, "description": "A well-tested CSS minifier", "license": "MIT", "keywords": ["css", "minifier"], "homepage": "https://github.com/jakubpawlowicz/clean-css", "repository": {"type": "git", "url": "https://github.com/jakubpawlowicz/clean-css.git"}, "bugs": {"url": "https://github.com/jakubpawlowicz/clean-css/issues"}, "main": "index.js", "files": ["lib", "History.md", "index.js", "LICENSE"], "scripts": {"browserify": "browserify --standalone CleanCSS index.js | uglifyjs --compress --mangle -o cleancss-browser.js", "bench": "node ./test/bench.js", "check": "jshint .", "prepublish": "npm run check", "test": "vows"}, "dependencies": {"source-map": "~0.6.0"}, "devDependencies": {"browserify": "^14.0.0", "http-proxy": "1.x", "jshint": "2.x", "nock": "9.x", "server-destroy": "1.x", "uglify-js": ">=2.6.1", "vows": "0.8.x"}, "engines": {"node": ">= 4.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-clean-css-4.2.3-507b5de7d97b48ee53d84adb0160ff6216380f78-integrity\\node_modules\\clean-css\\package.json", "readmeFilename": "README.md", "readme": "<h1 align=\"center\">\n  <br/>\n  <img src=\"https://cdn.rawgit.com/jakubpawlowicz/clean-css/master/logo.v2.svg\" alt=\"clean-css logo\" width=\"525px\"/>\n  <br/>\n  <br/>\n</h1>\n\n[![NPM version](https://img.shields.io/npm/v/clean-css.svg?style=flat)](https://www.npmjs.com/package/clean-css)\n[![Linux Build Status](https://img.shields.io/travis/jakubpawlowicz/clean-css/master.svg?style=flat&label=Linux%20build)](https://travis-ci.org/jakubpawlowicz/clean-css)\n[![Windows Build status](https://img.shields.io/appveyor/ci/jakubpawlowicz/clean-css/master.svg?style=flat&label=Windows%20build)](https://ci.appveyor.com/project/jakubpawlowicz/clean-css/branch/master)\n[![Dependency Status](https://img.shields.io/david/jakubpawlowicz/clean-css.svg?style=flat)](https://david-dm.org/jakubpawlowicz/clean-css)\n[![NPM Downloads](https://img.shields.io/npm/dm/clean-css.svg)](https://npmcharts.com/compare/clean-css?minimal=true)\n[![Twitter](https://img.shields.io/badge/<EMAIL>)](https://twitter.com/cleancss)\n\nclean-css is a fast and efficient CSS optimizer for [Node.js](http://nodejs.org/) platform and [any modern browser](https://jakubpawlowicz.github.io/clean-css).\n\nAccording to [tests](http://goalsmashers.github.io/css-minification-benchmark/) it is one of the best available.\n\n**Table of Contents**\n\n- [Node.js version support](#nodejs-version-support)\n- [Install](#install)\n- [Use](#use)\n  * [Important: 4.0 breaking changes](#important-40-breaking-changes)\n  * [What's new in version 4.1](#whats-new-in-version-41)\n  * [What's new in version 4.2](#whats-new-in-version-42)\n  * [Constructor options](#constructor-options)\n  * [Compatibility modes](#compatibility-modes)\n  * [Fetch option](#fetch-option)\n  * [Formatting options](#formatting-options)\n  * [Inlining options](#inlining-options)\n  * [Optimization levels](#optimization-levels)\n    + [Level 0 optimizations](#level-0-optimizations)\n    + [Level 1 optimizations](#level-1-optimizations)\n    + [Level 2 optimizations](#level-2-optimizations)\n  * [Minify method](#minify-method)\n  * [Promise interface](#promise-interface)\n  * [CLI utility](#cli-utility)\n- [FAQ](#faq)\n  * [How to optimize multiple files?](#how-to-optimize-multiple-files)\n  * [How to process remote `@import`s correctly?](#how-to-process-remote-imports-correctly)\n  * [How to apply arbitrary transformations to CSS properties?](#how-to-apply-arbitrary-transformations-to-css-properties)\n  * [How to specify a custom rounding precision?](#how-to-specify-a-custom-rounding-precision)\n  * [How to keep a CSS fragment intact?](#how-to-keep-a-css-fragment-intact)\n  * [How to preserve a comment block?](#how-to-preserve-a-comment-block)\n  * [How to rebase relative image URLs?](#how-to-rebase-relative-image-urls)\n  * [How to work with source maps?](#how-to-work-with-source-maps)\n  * [How to apply level 1 & 2 optimizations at the same time?](#how-to-apply-level-1--2-optimizations-at-the-same-time)\n  * [What level 2 optimizations do?](#what-level-2-optimizations-do)\n  * [How to use clean-css with build tools?](#how-to-use-clean-css-with-build-tools)\n  * [How to use clean-css from web browser?](#how-to-use-clean-css-from-web-browser)\n- [Contributing](#contributing)\n  * [How to get started?](#how-to-get-started)\n- [Acknowledgments](#acknowledgments)\n- [License](#license)\n\n# Node.js version support\n\nclean-css requires Node.js 4.0+ (tested on Linux, OS X, and Windows)\n\n# Install\n\n```\nnpm install --save-dev clean-css\n```\n\n# Use\n\n```js\nvar CleanCSS = require('clean-css');\nvar input = 'a{font-weight:bold;}';\nvar options = { /* options */ };\nvar output = new CleanCSS(options).minify(input);\n```\n\n## Important: 4.0 breaking changes\n\nclean-css 4.0 introduces some breaking changes:\n\n* API and CLI interfaces are split, so API stays in this repository while CLI moves to [clean-css-cli](https://github.com/jakubpawlowicz/clean-css-cli);\n* `root`, `relativeTo`, and `target` options are replaced by a single `rebaseTo` option - this means that rebasing URLs and import inlining is much simpler but may not be (YMMV) as powerful as in 3.x;\n* `debug` option is gone as stats are always provided in output object under `stats` property;\n* `roundingPrecision` is disabled by default;\n* `roundingPrecision` applies to **all** units now, not only `px` as in 3.x;\n* `processImport` and `processImportFrom` are merged into `inline` option which defaults to `local`. Remote `@import` rules are **NOT** inlined by default anymore;\n* splits `inliner: { request: ..., timeout: ... }` option into `inlineRequest` and `inlineTimeout` options;\n* remote resources without a protocol, e.g. `//fonts.googleapis.com/css?family=Domine:700`, are not inlined anymore;\n* changes default Internet Explorer compatibility from 9+ to 10+, to revert the old default use `{ compatibility: 'ie9' }` flag;\n* renames `keepSpecialComments` to `specialComments`;\n* moves `roundingPrecision` and `specialComments` to level 1 optimizations options, see examples;\n* moves `mediaMerging`, `restructuring`, `semanticMerging`, and `shorthandCompacting` to level 2 optimizations options, see examples below;\n* renames `shorthandCompacting` option to `mergeIntoShorthands`;\n* level 1 optimizations are the new default, up to 3.x it was level 2;\n* `keepBreaks` option is replaced with `{ format: 'keep-breaks' }` to ease transition;\n* `sourceMap` option has to be a boolean from now on - to specify an input source map pass it a 2nd argument to `minify` method or via a hash instead;\n* `aggressiveMerging` option is removed as aggressive merging is replaced by smarter override merging.\n\n## What's new in version 4.1\n\nclean-css 4.1 introduces the following changes / features:\n\n* `inline: false` as an alias to `inline: ['none']`;\n* `multiplePseudoMerging` compatibility flag controlling merging of rules with multiple pseudo classes / elements;\n* `removeEmpty` flag in level 1 optimizations controlling removal of rules and nested blocks;\n* `removeEmpty` flag in level 2 optimizations controlling removal of rules and nested blocks;\n* `compatibility: { selectors: { mergeLimit: <number> } }` flag in compatibility settings controlling maximum number of selectors in a single rule;\n* `minify` method improved signature accepting a list of hashes for a predictable traversal;\n* `selectorsSortingMethod` level 1 optimization allows `false` or `'none'` for disabling selector sorting;\n* `fetch` option controlling a function for handling remote requests;\n* new `font` shorthand and `font-*` longhand optimizers;\n* removal of `optimizeFont` flag in level 1 optimizations due to new `font` shorthand optimizer;\n* `skipProperties` flag in level 2 optimizations controlling which properties won't be optimized;\n* new `animation` shorthand and `animation-*` longhand optimizers;\n* `removeUnusedAtRules` level 2 optimization controlling removal of unused `@counter-style`, `@font-face`, `@keyframes`, and `@namespace` at rules;\n* the [web interface](https://jakubpawlowicz.github.io/clean-css) gets an improved settings panel with \"reset to defaults\", instant option changes, and settings being persisted across sessions.\n\n## What's new in version 4.2\n\nclean-css 4.2 introduces the following changes / features:\n\n* Adds `process` method for compatibility with optimize-css-assets-webpack-plugin;\n* new `transition` property optimizer;\n* preserves any CSS content between `/* clean-css ignore:start */` and `/* clean-css ignore:end */` comments;\n* allows filtering based on selector in `transform` callback, see [example](#how-to-apply-arbitrary-transformations-to-css-properties);\n* adds configurable line breaks via `format: { breakWith: 'lf' }` option.\n\n## Constructor options\n\nclean-css constructor accepts a hash as a parameter with the following options available:\n\n* `compatibility` - controls compatibility mode used; defaults to `ie10+`; see [compatibility modes](#compatibility-modes) for examples;\n* `fetch` - controls a function for handling remote requests; see [fetch option](#fetch-option) for examples (since 4.1.0);\n* `format` - controls output CSS formatting; defaults to `false`; see [formatting options](#formatting-options) for examples;\n* `inline` - controls `@import` inlining rules; defaults to `'local'`; see [inlining options](#inlining-options) for examples;\n* `inlineRequest` - controls extra options for inlining remote `@import` rules, can be any of [HTTP(S) request options](https://nodejs.org/api/http.html#http_http_request_options_callback);\n* `inlineTimeout` - controls number of milliseconds after which inlining a remote `@import` fails; defaults to 5000;\n* `level` - controls optimization level used; defaults to `1`; see [optimization levels](#optimization-levels) for examples;\n* `rebase` - controls URL rebasing; defaults to `true`;\n* `rebaseTo` - controls a directory to which all URLs are rebased, most likely the directory under which the output file will live; defaults to the current directory;\n* `returnPromise` - controls whether `minify` method returns a Promise object or not; defaults to `false`; see [promise interface](#promise-interface) for examples;\n* `sourceMap` - controls whether an output source map is built; defaults to `false`;\n* `sourceMapInlineSources` - controls embedding sources inside a source map's `sourcesContent` field; defaults to false.\n\n## Compatibility modes\n\nThere is a certain number of compatibility mode shortcuts, namely:\n\n* `new CleanCSS({ compatibility: '*' })` (default) - Internet Explorer 10+ compatibility mode\n* `new CleanCSS({ compatibility: 'ie9' })` - Internet Explorer 9+ compatibility mode\n* `new CleanCSS({ compatibility: 'ie8' })` - Internet Explorer 8+ compatibility mode\n* `new CleanCSS({ compatibility: 'ie7' })` - Internet Explorer 7+ compatibility mode\n\nEach of these modes is an alias to a [fine grained configuration](https://github.com/jakubpawlowicz/clean-css/blob/master/lib/options/compatibility.js), with the following options available:\n\n```js\nnew CleanCSS({\n  compatibility: {\n    colors: {\n      opacity: true // controls `rgba()` / `hsla()` color support\n    },\n    properties: {\n      backgroundClipMerging: true, // controls background-clip merging into shorthand\n      backgroundOriginMerging: true, // controls background-origin merging into shorthand\n      backgroundSizeMerging: true, // controls background-size merging into shorthand\n      colors: true, // controls color optimizations\n      ieBangHack: false, // controls keeping IE bang hack\n      ieFilters: false, // controls keeping IE `filter` / `-ms-filter`\n      iePrefixHack: false, // controls keeping IE prefix hack\n      ieSuffixHack: false, // controls keeping IE suffix hack\n      merging: true, // controls property merging based on understandability\n      shorterLengthUnits: false, // controls shortening pixel units into `pc`, `pt`, or `in` units\n      spaceAfterClosingBrace: true, // controls keeping space after closing brace - `url() no-repeat` into `url()no-repeat`\n      urlQuotes: false, // controls keeping quoting inside `url()`\n      zeroUnits: true // controls removal of units `0` value\n    },\n    selectors: {\n      adjacentSpace: false, // controls extra space before `nav` element\n      ie7Hack: true, // controls removal of IE7 selector hacks, e.g. `*+html...`\n      mergeablePseudoClasses: [':active', ...], // controls a whitelist of mergeable pseudo classes\n      mergeablePseudoElements: ['::after', ...], // controls a whitelist of mergeable pseudo elements\n      mergeLimit: 8191, // controls maximum number of selectors in a single rule (since 4.1.0)\n      multiplePseudoMerging: true // controls merging of rules with multiple pseudo classes / elements (since 4.1.0)\n    },\n    units: {\n      ch: true, // controls treating `ch` as a supported unit\n      in: true, // controls treating `in` as a supported unit\n      pc: true, // controls treating `pc` as a supported unit\n      pt: true, // controls treating `pt` as a supported unit\n      rem: true, // controls treating `rem` as a supported unit\n      vh: true, // controls treating `vh` as a supported unit\n      vm: true, // controls treating `vm` as a supported unit\n      vmax: true, // controls treating `vmax` as a supported unit\n      vmin: true // controls treating `vmin` as a supported unit\n    }\n  }\n})\n```\n\nYou can also use a string when setting a compatibility mode, e.g.\n\n```js\nnew CleanCSS({\n  compatibility: 'ie9,-properties.merging' // sets compatibility to IE9 mode with disabled property merging\n})\n```\n\n## Fetch option\n\nThe `fetch` option accepts a function which handles remote resource fetching, e.g.\n\n```js\nvar request = require('request');\nvar source = '@import url(http://example.com/path/to/stylesheet.css);';\nnew CleanCSS({\n  fetch: function (uri, inlineRequest, inlineTimeout, callback) {\n    request(uri, function (error, response, body) {\n      if (error) {\n        callback(error, null);\n      } else if (response && response.statusCode != 200) {\n        callback(response.statusCode, null);\n      } else {\n        callback(null, body);\n      }\n    });\n  }\n}).minify(source);\n```\n\nThis option provides a convenient way of overriding the default fetching logic if it doesn't support a particular feature, say CONNECT proxies.\n\nUnless given, the default [loadRemoteResource](https://github.com/jakubpawlowicz/clean-css/blob/master/lib/reader/load-remote-resource.js) logic is used.\n\n## Formatting options\n\nBy default output CSS is formatted without any whitespace unless a `format` option is given.\nFirst of all there are two shorthands:\n\n```js\nnew CleanCSS({\n  format: 'beautify' // formats output in a really nice way\n})\n```\n\nand\n\n```js\nnew CleanCSS({\n  format: 'keep-breaks' // formats output the default way but adds line breaks for improved readability\n})\n```\n\nhowever `format` option also accept a fine-grained set of options:\n\n```js\nnew CleanCSS({\n  format: {\n    breaks: { // controls where to insert breaks\n      afterAtRule: false, // controls if a line break comes after an at-rule; e.g. `@charset`; defaults to `false`\n      afterBlockBegins: false, // controls if a line break comes after a block begins; e.g. `@media`; defaults to `false`\n      afterBlockEnds: false, // controls if a line break comes after a block ends, defaults to `false`\n      afterComment: false, // controls if a line break comes after a comment; defaults to `false`\n      afterProperty: false, // controls if a line break comes after a property; defaults to `false`\n      afterRuleBegins: false, // controls if a line break comes after a rule begins; defaults to `false`\n      afterRuleEnds: false, // controls if a line break comes after a rule ends; defaults to `false`\n      beforeBlockEnds: false, // controls if a line break comes before a block ends; defaults to `false`\n      betweenSelectors: false // controls if a line break comes between selectors; defaults to `false`\n    },\n    breakWith: '\\n', // controls the new line character, can be `'\\r\\n'` or `'\\n'` (aliased as `'windows'` and `'unix'` or `'crlf'` and `'lf'`); defaults to system one, so former on Windows and latter on Unix\n    indentBy: 0, // controls number of characters to indent with; defaults to `0`\n    indentWith: 'space', // controls a character to indent with, can be `'space'` or `'tab'`; defaults to `'space'`\n    spaces: { // controls where to insert spaces\n      aroundSelectorRelation: false, // controls if spaces come around selector relations; e.g. `div > a`; defaults to `false`\n      beforeBlockBegins: false, // controls if a space comes before a block begins; e.g. `.block {`; defaults to `false`\n      beforeValue: false // controls if a space comes before a value; e.g. `width: 1rem`; defaults to `false`\n    },\n    wrapAt: false // controls maximum line length; defaults to `false`\n  }\n})\n```\n\n## Inlining options\n\n`inline` option whitelists which `@import` rules will be processed, e.g.\n\n```js\nnew CleanCSS({\n  inline: ['local'] // default; enables local inlining only\n})\n```\n\n```js\nnew CleanCSS({\n  inline: ['none'] // disables all inlining\n})\n```\n\n```js\n// introduced in clean-css 4.1.0\n\nnew CleanCSS({\n  inline: false // disables all inlining (alias to `['none']`)\n})\n```\n\n```js\nnew CleanCSS({\n  inline: ['all'] // enables all inlining, same as ['local', 'remote']\n})\n```\n\n```js\nnew CleanCSS({\n  inline: ['local', 'mydomain.example.com'] // enables local inlining plus given remote source\n})\n```\n\n```js\nnew CleanCSS({\n  inline: ['local', 'remote', '!fonts.googleapis.com'] // enables all inlining but from given remote source\n})\n```\n\n## Optimization levels\n\nThe `level` option can be either `0`, `1` (default), or `2`, e.g.\n\n```js\nnew CleanCSS({\n  level: 2\n})\n```\n\nor a fine-grained configuration given via a hash.\n\nPlease note that level 1 optimization options are generally safe while level 2 optimizations should be safe for most users.\n\n### Level 0 optimizations\n\nLevel 0 optimizations simply means \"no optimizations\". Use it when you'd like to inline imports and / or rebase URLs but skip everything else.\n\n### Level 1 optimizations\n\nLevel 1 optimizations (default) operate on single properties only, e.g. can remove units when not required, turn rgb colors to a shorter hex representation, remove comments, etc\n\nHere is a full list of available options:\n\n```js\nnew CleanCSS({\n  level: {\n    1: {\n      cleanupCharsets: true, // controls `@charset` moving to the front of a stylesheet; defaults to `true`\n      normalizeUrls: true, // controls URL normalization; defaults to `true`\n      optimizeBackground: true, // controls `background` property optimizations; defaults to `true`\n      optimizeBorderRadius: true, // controls `border-radius` property optimizations; defaults to `true`\n      optimizeFilter: true, // controls `filter` property optimizations; defaults to `true`\n      optimizeFont: true, // controls `font` property optimizations; defaults to `true`\n      optimizeFontWeight: true, // controls `font-weight` property optimizations; defaults to `true`\n      optimizeOutline: true, // controls `outline` property optimizations; defaults to `true`\n      removeEmpty: true, // controls removing empty rules and nested blocks; defaults to `true`\n      removeNegativePaddings: true, // controls removing negative paddings; defaults to `true`\n      removeQuotes: true, // controls removing quotes when unnecessary; defaults to `true`\n      removeWhitespace: true, // controls removing unused whitespace; defaults to `true`\n      replaceMultipleZeros: true, // contols removing redundant zeros; defaults to `true`\n      replaceTimeUnits: true, // controls replacing time units with shorter values; defaults to `true`\n      replaceZeroUnits: true, // controls replacing zero values with units; defaults to `true`\n      roundingPrecision: false, // rounds pixel values to `N` decimal places; `false` disables rounding; defaults to `false`\n      selectorsSortingMethod: 'standard', // denotes selector sorting method; can be `'natural'` or `'standard'`, `'none'`, or false (the last two since 4.1.0); defaults to `'standard'`\n      specialComments: 'all', // denotes a number of /*! ... */ comments preserved; defaults to `all`\n      tidyAtRules: true, // controls at-rules (e.g. `@charset`, `@import`) optimizing; defaults to `true`\n      tidyBlockScopes: true, // controls block scopes (e.g. `@media`) optimizing; defaults to `true`\n      tidySelectors: true, // controls selectors optimizing; defaults to `true`,\n      semicolonAfterLastProperty: false, // controls removing trailing semicolons in rule; defaults to `false` - means remove\n      transform: function () {} // defines a callback for fine-grained property optimization; defaults to no-op\n    }\n  }\n});\n```\n\nThere is an `all` shortcut for toggling all options at the same time, e.g.\n\n```js\nnew CleanCSS({\n  level: {\n    1: {\n      all: false, // set all values to `false`\n      tidySelectors: true // turns on optimizing selectors\n    }\n  }\n});\n```\n\n### Level 2 optimizations\n\nLevel 2 optimizations operate at rules or multiple properties level, e.g. can remove duplicate rules, remove properties redefined further down a stylesheet, or restructure rules by moving them around.\n\nPlease note that if level 2 optimizations are turned on then, unless explicitely disabled, level 1 optimizations are applied as well.\n\nHere is a full list of available options:\n\n```js\nnew CleanCSS({\n  level: {\n    2: {\n      mergeAdjacentRules: true, // controls adjacent rules merging; defaults to true\n      mergeIntoShorthands: true, // controls merging properties into shorthands; defaults to true\n      mergeMedia: true, // controls `@media` merging; defaults to true\n      mergeNonAdjacentRules: true, // controls non-adjacent rule merging; defaults to true\n      mergeSemantically: false, // controls semantic merging; defaults to false\n      overrideProperties: true, // controls property overriding based on understandability; defaults to true\n      removeEmpty: true, // controls removing empty rules and nested blocks; defaults to `true`\n      reduceNonAdjacentRules: true, // controls non-adjacent rule reducing; defaults to true\n      removeDuplicateFontRules: true, // controls duplicate `@font-face` removing; defaults to true\n      removeDuplicateMediaBlocks: true, // controls duplicate `@media` removing; defaults to true\n      removeDuplicateRules: true, // controls duplicate rules removing; defaults to true\n      removeUnusedAtRules: false, // controls unused at rule removing; defaults to false (available since 4.1.0)\n      restructureRules: false, // controls rule restructuring; defaults to false\n      skipProperties: [] // controls which properties won't be optimized, defaults to `[]` which means all will be optimized (since 4.1.0)\n    }\n  }\n});\n```\n\nThere is an `all` shortcut for toggling all options at the same time, e.g.\n\n```js\nnew CleanCSS({\n  level: {\n    2: {\n      all: false, // sets all values to `false`\n      removeDuplicateRules: true // turns on removing duplicate rules\n    }\n  }\n});\n```\n\n## Minify method\n\nOnce configured clean-css provides a `minify` method to optimize a given CSS, e.g.\n\n```js\nvar output = new CleanCSS(options).minify(source);\n```\n\nThe output of the `minify` method is a hash with following fields:\n\n```js\nconsole.log(output.styles); // optimized output CSS as a string\nconsole.log(output.sourceMap); // output source map if requested with `sourceMap` option\nconsole.log(output.errors); // a list of errors raised\nconsole.log(output.warnings); // a list of warnings raised\nconsole.log(output.stats.originalSize); // original content size after import inlining\nconsole.log(output.stats.minifiedSize); // optimized content size\nconsole.log(output.stats.timeSpent); // time spent on optimizations in milliseconds\nconsole.log(output.stats.efficiency); // `(originalSize - minifiedSize) / originalSize`, e.g. 0.25 if size is reduced from 100 bytes to 75 bytes\n```\n\nThe `minify` method also accepts an input source map, e.g.\n\n```js\nvar output = new CleanCSS(options).minify(source, inputSourceMap);\n```\n\nor a callback invoked when optimizations are finished, e.g.\n\n```js\nnew CleanCSS(options).minify(source, function (error, output) {\n  // `output` is the same as in the synchronous call above\n});\n```\n\n## Promise interface\n\nIf you prefer clean-css to return a Promise object then you need to explicitely ask for it, e.g.\n\n```js\nnew CleanCSS({ returnPromise: true })\n  .minify(source)\n  .then(function (output) { console.log(output.styles); })\n  .catch(function (error) { // deal with errors });\n```\n\n## CLI utility\n\nClean-css has an associated command line utility that can be installed separately using `npm install clean-css-cli`. For more detailed information, please visit https://github.com/jakubpawlowicz/clean-css-cli.\n\n# FAQ\n\n## How to optimize multiple files?\n\nIt can be done either by passing an array of paths, or, when sources are already available, a hash or an array of hashes:\n\n```js\nnew CleanCSS().minify(['path/to/file/one', 'path/to/file/two']);\n```\n\n```js\nnew CleanCSS().minify({\n  'path/to/file/one': {\n    styles: 'contents of file one'\n  },\n  'path/to/file/two': {\n    styles: 'contents of file two'\n  }\n});\n```\n\n```js\nnew CleanCSS().minify([\n  {'path/to/file/one': {styles: 'contents of file one'}},\n  {'path/to/file/two': {styles: 'contents of file two'}}\n]);\n```\n\nPassing an array of hashes allows you to explicitly specify the order in which the input files are concatenated. Whereas when you use a single hash the order is determined by the [traversal order of object properties](http://2ality.com/2015/10/property-traversal-order-es6.html) - available since 4.1.0.\n\nImportant note - any `@import` rules already present in the hash will be resolved in memory.\n\n## How to process remote `@import`s correctly?\n\nIn order to inline remote `@import` statements you need to provide a callback to minify method as fetching remote assets is an asynchronous operation, e.g.:\n\n```js\nvar source = '@import url(http://example.com/path/to/remote/styles);';\nnew CleanCSS({ inline: ['remote'] }).minify(source, function (error, output) {\n  // output.styles\n});\n```\n\nIf you don't provide a callback, then remote `@import`s will be left as is.\n\n## How to apply arbitrary transformations to CSS properties?\n\nIf clean-css doesn't perform a particular property optimization, you can use `transform` callback to apply it:\n\n```js\nvar source = '.block{background-image:url(/path/to/image.png)}';\nvar output = new CleanCSS({\n  level: {\n    1: {\n      transform: function (propertyName, propertyValue, selector /* `selector` available since 4.2.0-pre */) {\n        if (propertyName == 'background-image' && propertyValue.indexOf('/path/to') > -1) {\n          return propertyValue.replace('/path/to', '../valid/path/to');\n        }\n      }\n    }\n  }\n}).minify(source);\n\nconsole.log(output.styles); # => .block{background-image:url(../valid/path/to/image.png)}\n```\n\nNote: returning `false` from `transform` callback will drop a property.\n\n## How to specify a custom rounding precision?\n\nThe level 1 `roundingPrecision` optimization option accept a string with per-unit rounding precision settings, e.g.\n\n```js\nnew CleanCSS({\n  level: {\n    1: {\n      roundingPrecision: 'all=3,px=5'\n    }\n  }\n}).minify(source)\n```\n\nwhich sets all units rounding precision to 3 digits except `px` unit precision of 5 digits.\n\n## How to keep a CSS fragment intact?\n\nNote: available in the current master, to be released in 4.2.0.\n\nWrap the CSS fragment in special comments which instruct clean-css to preserve it, e.g.\n\n```css\n.block-1 {\n  color: red\n}\n/* clean-css ignore:start */\n.block-special {\n  color: transparent\n}\n/* clean-css ignore:end */\n.block-2 {\n  margin: 0\n}\n```\n\nOptimizing this CSS will result in the following output:\n\n```css\n.block-1{color:red}\n.block-special {\n  color: transparent\n}\n.block-2{margin:0}\n```\n\n## How to preserve a comment block?\n\nUse the `/*!` notation instead of the standard one `/*`:\n\n```css\n/*!\n  Important comments included in optimized output.\n*/\n```\n\n## How to rebase relative image URLs?\n\nclean-css will handle it automatically for you in the following cases:\n\n* when full paths to input files are passed in as options;\n* when correct paths are passed in via a hash;\n* when `rebaseTo` is used with any of above two.\n\n## How to work with source maps?\n\nTo generate a source map, use `sourceMap: true` option, e.g.:\n\n```js\nnew CleanCSS({ sourceMap: true, rebaseTo: pathToOutputDirectory })\n  .minify(source, function (error, output) {\n    // access output.sourceMap for SourceMapGenerator object\n    // see https://github.com/mozilla/source-map/#sourcemapgenerator for more details\n});\n```\n\nYou can also pass an input source map directly as a 2nd argument to `minify` method:\n\n```js\nnew CleanCSS({ sourceMap: true, rebaseTo: pathToOutputDirectory })\n  .minify(source, inputSourceMap, function (error, output) {\n    // access output.sourceMap to access SourceMapGenerator object\n    // see https://github.com/mozilla/source-map/#sourcemapgenerator for more details\n});\n```\n\nor even multiple input source maps at once:\n\n```js\nnew CleanCSS({ sourceMap: true, rebaseTo: pathToOutputDirectory }).minify({\n  'path/to/source/1': {\n    styles: '...styles...',\n    sourceMap: '...source-map...'\n  },\n  'path/to/source/2': {\n    styles: '...styles...',\n    sourceMap: '...source-map...'\n  }\n}, function (error, output) {\n  // access output.sourceMap as above\n});\n```\n\n## How to apply level 1 & 2 optimizations at the same time?\n\nUsing the hash configuration specifying both optimization levels, e.g.\n\n```js\nnew CleanCSS({\n  level: {\n    1: {\n      all: true,\n      normalizeUrls: false\n    },\n    2: {\n      restructureRules: true\n    }\n  }\n})\n```\n\nwill apply level 1 optimizations, except url normalization, and default level 2 optimizations with rule restructuring.\n\n## What level 2 optimizations do?\n\nAll level 2 optimizations are dispatched [here](https://github.com/jakubpawlowicz/clean-css/blob/master/lib/optimizer/level-2/optimize.js#L67), and this is what they do:\n\n* `recursivelyOptimizeBlocks` - does all the following operations on a nested block, like `@media` or `@keyframe`;\n* `recursivelyOptimizeProperties` - optimizes properties in rulesets and flat at-rules, like @font-face, by splitting them into components (e.g. `margin` into `margin-(bottom|left|right|top)`), optimizing, and restoring them back. You may want to use `mergeIntoShorthands` option to control whether you want to turn multiple components into shorthands;\n* `removeDuplicates` - gets rid of duplicate rulesets with exactly the same set of properties, e.g. when including a Sass / Less partial twice for no good reason;\n* `mergeAdjacent` - merges adjacent rulesets with the same selector or rules;\n* `reduceNonAdjacent` - identifies which properties are overridden in same-selector non-adjacent rulesets, and removes them;\n* `mergeNonAdjacentBySelector` - identifies same-selector non-adjacent rulesets which can be moved (!) to be merged, requires all intermediate rulesets to not redefine the moved properties, or if redefined to have the same value;\n* `mergeNonAdjacentByBody` - same as the one above but for same-selector non-adjacent rulesets;\n* `restructure` - tries to reorganize different-selector different-rules rulesets so they take less space, e.g. `.one{padding:0}.two{margin:0}.one{margin-bottom:3px}` into `.two{margin:0}.one{padding:0;margin-bottom:3px}`;\n* `removeDuplicateFontAtRules` - removes duplicated `@font-face` rules;\n* `removeDuplicateMediaQueries` - removes duplicated `@media` nested blocks;\n* `mergeMediaQueries` - merges non-adjacent `@media` at-rules by the same rules as `mergeNonAdjacentBy*` above;\n\n## How to use clean-css with build tools?\n\nThere is a number of 3rd party plugins to popular build tools:\n\n* [Broccoli](https://github.com/broccolijs/broccoli#broccoli): [broccoli-clean-css](https://github.com/shinnn/broccoli-clean-css)\n* [Brunch](http://brunch.io/): [clean-css-brunch](https://github.com/brunch/clean-css-brunch)\n* [Grunt](http://gruntjs.com): [grunt-contrib-cssmin](https://github.com/gruntjs/grunt-contrib-cssmin)\n* [Gulp](http://gulpjs.com/): [gulp-clean-css](https://github.com/scniro/gulp-clean-css)\n* [Gulp](http://gulpjs.com/): [using vinyl-map as a wrapper - courtesy of @sogko](https://github.com/jakubpawlowicz/clean-css/issues/342)\n* [component-builder2](https://github.com/component/builder2.js): [builder-clean-css](https://github.com/poying/builder-clean-css)\n* [Metalsmith](http://metalsmith.io): [metalsmith-clean-css](https://github.com/aymericbeaumet/metalsmith-clean-css)\n* [Lasso](https://github.com/lasso-js/lasso): [lasso-clean-css](https://github.com/yomed/lasso-clean-css)\n* [Start](https://github.com/start-runner/start): [start-clean-css](https://github.com/start-runner/clean-css)\n\n## How to use clean-css from web browser?\n\n* https://jakubpawlowicz.github.io/clean-css/ (official web interface)\n* http://refresh-sf.com/\n* http://adamburgess.github.io/clean-css-online/\n\n# Contributing\n\nSee [CONTRIBUTING.md](https://github.com/jakubpawlowicz/clean-css/blob/master/CONTRIBUTING.md).\n\n## How to get started?\n\nFirst clone the sources:\n\n```bash\n<NAME_EMAIL>:jakubpawlowicz/clean-css.git\n```\n\nthen install dependencies:\n\n```bash\ncd clean-css\nnpm install\n```\n\nthen use any of the following commands to verify your copy:\n\n```bash\nnpm run bench # for clean-css benchmarks (see [test/bench.js](https://github.com/jakubpawlowicz/clean-css/blob/master/test/bench.js) for details)\nnpm run browserify # to create the browser-ready clean-css version\nnpm run check # to lint JS sources with [JSHint](https://github.com/jshint/jshint/)\nnpm test # to run all tests\n```\n\n# Acknowledgments\n\nSorted alphabetically by GitHub handle:\n\n* [@abarre](https://github.com/abarre) (Anthony Barre) for improvements to `@import` processing;\n* [@alexlamsl](https://github.com/alexlamsl) (Alex Lam S.L.) for testing early clean-css 4 versions, reporting bugs, and suggesting numerous improvements.\n* [@altschuler](https://github.com/altschuler) (Simon Altschuler) for fixing `@import` processing inside comments;\n* [@ben-eb](https://github.com/ben-eb) (Ben Briggs) for sharing ideas about CSS optimizations;\n* [@davisjam](https://github.com/davisjam) (Jamie Davis) for disclosing ReDOS vulnerabilities;\n* [@facelessuser](https://github.com/facelessuser) (Isaac) for pointing out a flaw in clean-css' stateless mode;\n* [@grandrath](https://github.com/grandrath) (Martin Grandrath) for improving `minify` method source traversal in ES6;\n* [@jmalonzo](https://github.com/jmalonzo) (Jan Michael Alonzo) for a patch removing node.js' old `sys` package;\n* [@lukeapage](https://github.com/lukeapage) (Luke Page) for suggestions and testing the source maps feature;\n  Plus everyone else involved in [#125](https://github.com/jakubpawlowicz/clean-css/issues/125) for pushing it forward;\n* [@madwizard-thomas](https://github.com/madwizard-thomas) for sharing ideas about `@import` inlining and URL rebasing.\n* [@ngyikp](https://github.com/ngyikp) (Ng Yik Phang) for testing early clean-css 4 versions, reporting bugs, and suggesting numerous improvements.\n* [@wagenet](https://github.com/wagenet) (Peter Wagenet) for suggesting improvements to `@import` inlining behavior;\n* [@venemo](https://github.com/venemo) (Timur Kristóf) for an outstanding contribution of advanced property optimizer for 2.2 release;\n* [@vvo](https://github.com/vvo) (Vincent Voyer) for a patch with better empty element regex and for inspiring us to do many performance improvements in 0.4 release;\n* [@xhmikosr](https://github.com/xhmikosr) for suggesting new features, like option to remove special comments and strip out URLs quotation, and pointing out numerous improvements like JSHint, media queries, etc.\n\n# License\n\nclean-css is released under the [MIT License](https://github.com/jakubpawlowicz/clean-css/blob/master/LICENSE).\n", "licenseText": "Copyright (C) 2017 JakubPawlowicz.com\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is furnished\nto do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\nDAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,\nARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\nDEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/clean-css/-/clean-css-4.2.3.tgz#507b5de7d97b48ee53d84adb0160ff6216380f78", "type": "tarball", "reference": "https://registry.yarnpkg.com/clean-css/-/clean-css-4.2.3.tgz", "hash": "507b5de7d97b48ee53d84adb0160ff6216380f78", "integrity": "sha512-VcMWDN54ZN/DS+g58HYL5/n4Zrqe8vHJpGA8KdgUXFU4fuP/aHNw8eld9SyEIyabIMJX/0RaY/fplOo5hYLSFA==", "registry": "npm", "packageName": "clean-css", "cacheIntegrity": "sha512-VcMWDN54ZN/DS+g58HYL5/n4Zrqe8vHJpGA8KdgUXFU4fuP/aHNw8eld9SyEIyabIMJX/0RaY/fplOo5hYLSFA== sha1-UHtd59l7SO5T2ErbAWD/YhY4D3g="}, "registry": "npm", "hash": "507b5de7d97b48ee53d84adb0160ff6216380f78"}