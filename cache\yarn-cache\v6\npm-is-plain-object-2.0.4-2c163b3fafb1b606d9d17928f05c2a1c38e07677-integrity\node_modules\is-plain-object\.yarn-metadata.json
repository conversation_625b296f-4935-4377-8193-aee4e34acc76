{"manifest": {"name": "is-plain-object", "description": "Returns true if an object was created by the `Object` constructor.", "version": "2.0.4", "homepage": "https://github.com/jonschlinkert/is-plain-object", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON>", "url": "http://onokumus.com"}, {"name": "<PERSON>", "url": "https://svachon.com"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-plain-object.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "license": "MIT", "files": ["index.d.ts", "index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"browserify": "browserify index.js --standalone isPlainObject | uglifyjs --compress --mangle -o browser/is-plain-object.js", "test_browser": "mocha-phantomjs test/browser.html", "test_node": "mocha", "test": "npm run test_node && npm run browserify && npm run test_browser"}, "dependencies": {"isobject": "^3.0.1"}, "devDependencies": {"browserify": "^14.4.0", "chai": "^4.0.2", "gulp-format-md": "^1.0.0", "mocha": "^3.4.2", "mocha-phantomjs": "^4.1.0", "phantomjs": "^2.1.7", "uglify-js": "^3.0.24"}, "keywords": ["check", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "object", "plain", "type", "typeof", "value"], "types": "index.d.ts", "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-number", "isobject", "kind-of"]}, "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-plain-object-2.0.4-2c163b3fafb1b606d9d17928f05c2a1c38e07677-integrity\\node_modules\\is-plain-object\\package.json", "readmeFilename": "README.md", "readme": "# is-plain-object [![NPM version](https://img.shields.io/npm/v/is-plain-object.svg?style=flat)](https://www.npmjs.com/package/is-plain-object) [![NPM monthly downloads](https://img.shields.io/npm/dm/is-plain-object.svg?style=flat)](https://npmjs.org/package/is-plain-object) [![NPM total downloads](https://img.shields.io/npm/dt/is-plain-object.svg?style=flat)](https://npmjs.org/package/is-plain-object) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/is-plain-object.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/is-plain-object)\n\n> Returns true if an object was created by the `Object` constructor.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save is-plain-object\n```\n\nUse [isobject](https://github.com/jonschlinkert/isobject) if you only want to check if the value is an object and not an array or null.\n\n## Usage\n\n```js\nvar isPlainObject = require('is-plain-object');\n```\n\n**true** when created by the `Object` constructor.\n\n```js\nisPlainObject(Object.create({}));\n//=> true\nisPlainObject(Object.create(Object.prototype));\n//=> true\nisPlainObject({foo: 'bar'});\n//=> true\nisPlainObject({});\n//=> true\n```\n\n**false** when not created by the `Object` constructor.\n\n```js\nisPlainObject(1);\n//=> false\nisPlainObject(['foo', 'bar']);\n//=> false\nisPlainObject([]);\n//=> false\nisPlainObject(new Foo);\n//=> false\nisPlainObject(null);\n//=> false\nisPlainObject(Object.create(null));\n//=> false\n```\n\n## About\n\n### Related projects\n\n* [is-number](https://www.npmjs.com/package/is-number): Returns true if the value is a number. comprehensive tests. | [homepage](https://github.com/jonschlinkert/is-number \"Returns true if the value is a number. comprehensive tests.\")\n* [isobject](https://www.npmjs.com/package/isobject): Returns true if the value is an object and not an array or null. | [homepage](https://github.com/jonschlinkert/isobject \"Returns true if the value is an object and not an array or null.\")\n* [kind-of](https://www.npmjs.com/package/kind-of): Get the native type of a value. | [homepage](https://github.com/jonschlinkert/kind-of \"Get the native type of a value.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 17 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 6 | [stevenvachon](https://github.com/stevenvachon) |\n| 3 | [onokumus](https://github.com/onokumus) |\n| 1 | [wtgtybhertgeghgtwtg](https://github.com/wtgtybhertgeghgtwtg) |\n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on July 11, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-plain-object/-/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-plain-object/-/is-plain-object-2.0.4.tgz", "hash": "2c163b3fafb1b606d9d17928f05c2a1c38e07677", "integrity": "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==", "registry": "npm", "packageName": "is-plain-object", "cacheIntegrity": "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og== sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc="}, "registry": "npm", "hash": "2c163b3fafb1b606d9d17928f05c2a1c38e07677"}