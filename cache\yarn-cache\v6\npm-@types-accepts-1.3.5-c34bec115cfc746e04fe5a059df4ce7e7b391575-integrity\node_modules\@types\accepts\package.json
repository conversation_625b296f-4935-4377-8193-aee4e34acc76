{"name": "@types/accepts", "version": "1.3.5", "description": "TypeScript definitions for accepts", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/bomret", "githubUsername": "bomret"}, {"name": "Brice BERNARD", "url": "https://github.com/brikou", "githubUsername": "brikou"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "0c1b369f923466e74bec6da148c76a05cc138923a142ea737eff8cf2b29b559f", "typeScriptVersion": "2.0"}