{"manifest": {"name": "qs", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "homepage": "https://github.com/ljharb/qs", "version": "6.9.4", "repository": {"type": "git", "url": "https://github.com/ljharb/qs.git"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "main": "lib/index.js", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "engines": {"node": ">=0.6"}, "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^16.0.0", "browserify": "^16.5.1", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "has-symbols": "^1.0.1", "iconv-lite": "^0.5.1", "mkdirp": "^0.5.4", "object-inspect": "^1.7.0", "qs-iconv": "^1.0.4", "safe-publish-latest": "^1.1.4", "safer-buffer": "^2.1.2", "tape": "^5.0.0"}, "scripts": {"prepublish": "safe-publish-latest && npm run dist", "pretest": "npm run --silent readme && npm run --silent lint", "test": "npm run --silent coverage", "tests-only": "node test", "posttest": "npx aud --production", "readme": "evalmd README.md", "postlint": "eclint check * lib/* test/*", "lint": "eslint lib/*.js test/*.js", "coverage": "covert test", "dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js"}, "license": "BSD-3-<PERSON><PERSON>", "greenkeeper": {"ignore": ["iconv-lite", "mkdirp"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-qs-6.9.4-9090b290d1f91728d3c22e54843ca44aea5ab687-integrity\\node_modules\\qs\\package.json", "readmeFilename": "README.md", "readme": "# qs <sup>[![Version Badge][2]][1]</sup>\n\n[![Build Status][3]][4]\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\nA querystring parsing and stringifying library with some added security.\n\nLead Maintainer: [<PERSON>](https://github.com/ljharb)\n\nThe **qs** module was originally created and maintained by [<PERSON><PERSON>aychuk](https://github.com/visionmedia/node-querystring).\n\n## Usage\n\n```javascript\nvar qs = require('qs');\nvar assert = require('assert');\n\nvar obj = qs.parse('a=c');\nassert.deepEqual(obj, { a: 'c' });\n\nvar str = qs.stringify(obj);\nassert.equal(str, 'a=c');\n```\n\n### Parsing Objects\n\n[](#preventEval)\n```javascript\nqs.parse(string, [options]);\n```\n\n**qs** allows you to create nested objects within your query strings, by surrounding the name of sub-keys with square brackets `[]`.\nFor example, the string `'foo[bar]=baz'` converts to:\n\n```javascript\nassert.deepEqual(qs.parse('foo[bar]=baz'), {\n    foo: {\n        bar: 'baz'\n    }\n});\n```\n\nWhen using the `plainObjects` option the parsed value is returned as a null object, created via `Object.create(null)` and as such you should be aware that prototype methods will not exist on it and a user may set those names to whatever value they like:\n\n```javascript\nvar nullObject = qs.parse('a[hasOwnProperty]=b', { plainObjects: true });\nassert.deepEqual(nullObject, { a: { hasOwnProperty: 'b' } });\n```\n\nBy default parameters that would overwrite properties on the object prototype are ignored, if you wish to keep the data from those fields either use `plainObjects` as mentioned above, or set `allowPrototypes` to `true` which will allow user input to overwrite those properties. *WARNING* It is generally a bad idea to enable this option as it can cause problems when attempting to use the properties that have been overwritten. Always be careful with this option.\n\n```javascript\nvar protoObject = qs.parse('a[hasOwnProperty]=b', { allowPrototypes: true });\nassert.deepEqual(protoObject, { a: { hasOwnProperty: 'b' } });\n```\n\nURI encoded strings work too:\n\n```javascript\nassert.deepEqual(qs.parse('a%5Bb%5D=c'), {\n    a: { b: 'c' }\n});\n```\n\nYou can also nest your objects, like `'foo[bar][baz]=foobarbaz'`:\n\n```javascript\nassert.deepEqual(qs.parse('foo[bar][baz]=foobarbaz'), {\n    foo: {\n        bar: {\n            baz: 'foobarbaz'\n        }\n    }\n});\n```\n\nBy default, when nesting objects **qs** will only parse up to 5 children deep. This means if you attempt to parse a string like\n`'a[b][c][d][e][f][g][h][i]=j'` your resulting object will be:\n\n```javascript\nvar expected = {\n    a: {\n        b: {\n            c: {\n                d: {\n                    e: {\n                        f: {\n                            '[g][h][i]': 'j'\n                        }\n                    }\n                }\n            }\n        }\n    }\n};\nvar string = 'a[b][c][d][e][f][g][h][i]=j';\nassert.deepEqual(qs.parse(string), expected);\n```\n\nThis depth can be overridden by passing a `depth` option to `qs.parse(string, [options])`:\n\n```javascript\nvar deep = qs.parse('a[b][c][d][e][f][g][h][i]=j', { depth: 1 });\nassert.deepEqual(deep, { a: { b: { '[c][d][e][f][g][h][i]': 'j' } } });\n```\n\nThe depth limit helps mitigate abuse when **qs** is used to parse user input, and it is recommended to keep it a reasonably small number.\n\nFor similar reasons, by default **qs** will only parse up to 1000 parameters. This can be overridden by passing a `parameterLimit` option:\n\n```javascript\nvar limited = qs.parse('a=b&c=d', { parameterLimit: 1 });\nassert.deepEqual(limited, { a: 'b' });\n```\n\nTo bypass the leading question mark, use `ignoreQueryPrefix`:\n\n```javascript\nvar prefixed = qs.parse('?a=b&c=d', { ignoreQueryPrefix: true });\nassert.deepEqual(prefixed, { a: 'b', c: 'd' });\n```\n\nAn optional delimiter can also be passed:\n\n```javascript\nvar delimited = qs.parse('a=b;c=d', { delimiter: ';' });\nassert.deepEqual(delimited, { a: 'b', c: 'd' });\n```\n\nDelimiters can be a regular expression too:\n\n```javascript\nvar regexed = qs.parse('a=b;c=d,e=f', { delimiter: /[;,]/ });\nassert.deepEqual(regexed, { a: 'b', c: 'd', e: 'f' });\n```\n\nOption `allowDots` can be used to enable dot notation:\n\n```javascript\nvar withDots = qs.parse('a.b=c', { allowDots: true });\nassert.deepEqual(withDots, { a: { b: 'c' } });\n```\n\nIf you have to deal with legacy browsers or services, there's\nalso support for decoding percent-encoded octets as iso-8859-1:\n\n```javascript\nvar oldCharset = qs.parse('a=%A7', { charset: 'iso-8859-1' });\nassert.deepEqual(oldCharset, { a: '§' });\n```\n\nSome services add an initial `utf8=✓` value to forms so that old\nInternet Explorer versions are more likely to submit the form as\nutf-8. Additionally, the server can check the value against wrong\nencodings of the checkmark character and detect that a query string\nor `application/x-www-form-urlencoded` body was *not* sent as\nutf-8, eg. if the form had an `accept-charset` parameter or the\ncontaining page had a different character set.\n\n**qs** supports this mechanism via the `charsetSentinel` option.\nIf specified, the `utf8` parameter will be omitted from the\nreturned object. It will be used to switch to `iso-8859-1`/`utf-8`\nmode depending on how the checkmark is encoded.\n\n**Important**: When you specify both the `charset` option and the\n`charsetSentinel` option, the `charset` will be overridden when\nthe request contains a `utf8` parameter from which the actual\ncharset can be deduced. In that sense the `charset` will behave\nas the default charset rather than the authoritative charset.\n\n```javascript\nvar detectedAsUtf8 = qs.parse('utf8=%E2%9C%93&a=%C3%B8', {\n    charset: 'iso-8859-1',\n    charsetSentinel: true\n});\nassert.deepEqual(detectedAsUtf8, { a: 'ø' });\n\n// Browsers encode the checkmark as &#10003; when submitting as iso-8859-1:\nvar detectedAsIso8859_1 = qs.parse('utf8=%26%2310003%3B&a=%F8', {\n    charset: 'utf-8',\n    charsetSentinel: true\n});\nassert.deepEqual(detectedAsIso8859_1, { a: 'ø' });\n```\n\nIf you want to decode the `&#...;` syntax to the actual character,\nyou can specify the `interpretNumericEntities` option as well:\n\n```javascript\nvar detectedAsIso8859_1 = qs.parse('a=%26%239786%3B', {\n    charset: 'iso-8859-1',\n    interpretNumericEntities: true\n});\nassert.deepEqual(detectedAsIso8859_1, { a: '☺' });\n```\n\nIt also works when the charset has been detected in `charsetSentinel`\nmode.\n\n### Parsing Arrays\n\n**qs** can also parse arrays using a similar `[]` notation:\n\n```javascript\nvar withArray = qs.parse('a[]=b&a[]=c');\nassert.deepEqual(withArray, { a: ['b', 'c'] });\n```\n\nYou may specify an index as well:\n\n```javascript\nvar withIndexes = qs.parse('a[1]=c&a[0]=b');\nassert.deepEqual(withIndexes, { a: ['b', 'c'] });\n```\n\nNote that the only difference between an index in an array and a key in an object is that the value between the brackets must be a number\nto create an array. When creating arrays with specific indices, **qs** will compact a sparse array to only the existing values preserving\ntheir order:\n\n```javascript\nvar noSparse = qs.parse('a[1]=b&a[15]=c');\nassert.deepEqual(noSparse, { a: ['b', 'c'] });\n```\n\nNote that an empty string is also a value, and will be preserved:\n\n```javascript\nvar withEmptyString = qs.parse('a[]=&a[]=b');\nassert.deepEqual(withEmptyString, { a: ['', 'b'] });\n\nvar withIndexedEmptyString = qs.parse('a[0]=b&a[1]=&a[2]=c');\nassert.deepEqual(withIndexedEmptyString, { a: ['b', '', 'c'] });\n```\n\n**qs** will also limit specifying indices in an array to a maximum index of `20`. Any array members with an index of greater than `20` will\ninstead be converted to an object with the index as the key. This is needed to handle cases when someone sent, for example, `a[999999999]` and it will take significant time to iterate over this huge array.\n\n```javascript\nvar withMaxIndex = qs.parse('a[100]=b');\nassert.deepEqual(withMaxIndex, { a: { '100': 'b' } });\n```\n\nThis limit can be overridden by passing an `arrayLimit` option:\n\n```javascript\nvar withArrayLimit = qs.parse('a[1]=b', { arrayLimit: 0 });\nassert.deepEqual(withArrayLimit, { a: { '1': 'b' } });\n```\n\nTo disable array parsing entirely, set `parseArrays` to `false`.\n\n```javascript\nvar noParsingArrays = qs.parse('a[]=b', { parseArrays: false });\nassert.deepEqual(noParsingArrays, { a: { '0': 'b' } });\n```\n\nIf you mix notations, **qs** will merge the two items into an object:\n\n```javascript\nvar mixedNotation = qs.parse('a[0]=b&a[b]=c');\nassert.deepEqual(mixedNotation, { a: { '0': 'b', b: 'c' } });\n```\n\nYou can also create arrays of objects:\n\n```javascript\nvar arraysOfObjects = qs.parse('a[][b]=c');\nassert.deepEqual(arraysOfObjects, { a: [{ b: 'c' }] });\n```\n\nSome people use comma to join array, **qs** can parse it:\n```javascript\nvar arraysOfObjects = qs.parse('a=b,c', { comma: true })\nassert.deepEqual(arraysOfObjects, { a: ['b', 'c'] })\n```\n(_this cannot convert nested objects, such as `a={b:1},{c:d}`_)\n\n### Stringifying\n\n[](#preventEval)\n```javascript\nqs.stringify(object, [options]);\n```\n\nWhen stringifying, **qs** by default URI encodes output. Objects are stringified as you would expect:\n\n```javascript\nassert.equal(qs.stringify({ a: 'b' }), 'a=b');\nassert.equal(qs.stringify({ a: { b: 'c' } }), 'a%5Bb%5D=c');\n```\n\nThis encoding can be disabled by setting the `encode` option to `false`:\n\n```javascript\nvar unencoded = qs.stringify({ a: { b: 'c' } }, { encode: false });\nassert.equal(unencoded, 'a[b]=c');\n```\n\nEncoding can be disabled for keys by setting the `encodeValuesOnly` option to `true`:\n```javascript\nvar encodedValues = qs.stringify(\n    { a: 'b', c: ['d', 'e=f'], f: [['g'], ['h']] },\n    { encodeValuesOnly: true }\n);\nassert.equal(encodedValues,'a=b&c[0]=d&c[1]=e%3Df&f[0][0]=g&f[1][0]=h');\n```\n\nThis encoding can also be replaced by a custom encoding method set as `encoder` option:\n\n```javascript\nvar encoded = qs.stringify({ a: { b: 'c' } }, { encoder: function (str) {\n    // Passed in values `a`, `b`, `c`\n    return // Return encoded string\n}})\n```\n\n_(Note: the `encoder` option does not apply if `encode` is `false`)_\n\nAnalogue to the `encoder` there is a `decoder` option for `parse` to override decoding of properties and values:\n\n```javascript\nvar decoded = qs.parse('x=z', { decoder: function (str) {\n    // Passed in values `x`, `z`\n    return // Return decoded string\n}})\n```\n\nYou can encode keys and values using different logic by using the type argument provided to the encoder:\n\n```javascript\nvar encoded = qs.stringify({ a: { b: 'c' } }, { encoder: function (str, defaultEncoder, charset, type) {\n    if (type === 'key') {\n        return // Encoded key\n    } else if (type === 'value') {\n        return // Encoded value\n    }\n}})\n```\n\nThe type argument is also provided to the decoder:\n\n```javascript\nvar decoded = qs.parse('x=z', { decoder: function (str, defaultEncoder, charset, type) {\n    if (type === 'key') {\n        return // Decoded key\n    } else if (type === 'value') {\n        return // Decoded value\n    }\n}})\n```\n\nExamples beyond this point will be shown as though the output is not URI encoded for clarity. Please note that the return values in these cases *will* be URI encoded during real usage.\n\nWhen arrays are stringified, by default they are given explicit indices:\n\n```javascript\nqs.stringify({ a: ['b', 'c', 'd'] });\n// 'a[0]=b&a[1]=c&a[2]=d'\n```\n\nYou may override this by setting the `indices` option to `false`:\n\n```javascript\nqs.stringify({ a: ['b', 'c', 'd'] }, { indices: false });\n// 'a=b&a=c&a=d'\n```\n\nYou may use the `arrayFormat` option to specify the format of the output array:\n\n```javascript\nqs.stringify({ a: ['b', 'c'] }, { arrayFormat: 'indices' })\n// 'a[0]=b&a[1]=c'\nqs.stringify({ a: ['b', 'c'] }, { arrayFormat: 'brackets' })\n// 'a[]=b&a[]=c'\nqs.stringify({ a: ['b', 'c'] }, { arrayFormat: 'repeat' })\n// 'a=b&a=c'\nqs.stringify({ a: ['b', 'c'] }, { arrayFormat: 'comma' })\n// 'a=b,c'\n```\n\nWhen objects are stringified, by default they use bracket notation:\n\n```javascript\nqs.stringify({ a: { b: { c: 'd', e: 'f' } } });\n// 'a[b][c]=d&a[b][e]=f'\n```\n\nYou may override this to use dot notation by setting the `allowDots` option to `true`:\n\n```javascript\nqs.stringify({ a: { b: { c: 'd', e: 'f' } } }, { allowDots: true });\n// 'a.b.c=d&a.b.e=f'\n```\n\nEmpty strings and null values will omit the value, but the equals sign (=) remains in place:\n\n```javascript\nassert.equal(qs.stringify({ a: '' }), 'a=');\n```\n\nKey with no values (such as an empty object or array) will return nothing:\n\n```javascript\nassert.equal(qs.stringify({ a: [] }), '');\nassert.equal(qs.stringify({ a: {} }), '');\nassert.equal(qs.stringify({ a: [{}] }), '');\nassert.equal(qs.stringify({ a: { b: []} }), '');\nassert.equal(qs.stringify({ a: { b: {}} }), '');\n```\n\nProperties that are set to `undefined` will be omitted entirely:\n\n```javascript\nassert.equal(qs.stringify({ a: null, b: undefined }), 'a=');\n```\n\nThe query string may optionally be prepended with a question mark:\n\n```javascript\nassert.equal(qs.stringify({ a: 'b', c: 'd' }, { addQueryPrefix: true }), '?a=b&c=d');\n```\n\nThe delimiter may be overridden with stringify as well:\n\n```javascript\nassert.equal(qs.stringify({ a: 'b', c: 'd' }, { delimiter: ';' }), 'a=b;c=d');\n```\n\nIf you only want to override the serialization of `Date` objects, you can provide a `serializeDate` option:\n\n```javascript\nvar date = new Date(7);\nassert.equal(qs.stringify({ a: date }), 'a=1970-01-01T00:00:00.007Z'.replace(/:/g, '%3A'));\nassert.equal(\n    qs.stringify({ a: date }, { serializeDate: function (d) { return d.getTime(); } }),\n    'a=7'\n);\n```\n\nYou may use the `sort` option to affect the order of parameter keys:\n\n```javascript\nfunction alphabeticalSort(a, b) {\n    return a.localeCompare(b);\n}\nassert.equal(qs.stringify({ a: 'c', z: 'y', b : 'f' }, { sort: alphabeticalSort }), 'a=c&b=f&z=y');\n```\n\nFinally, you can use the `filter` option to restrict which keys will be included in the stringified output.\nIf you pass a function, it will be called for each key to obtain the replacement value. Otherwise, if you\npass an array, it will be used to select properties and array indices for stringification:\n\n```javascript\nfunction filterFunc(prefix, value) {\n    if (prefix == 'b') {\n        // Return an `undefined` value to omit a property.\n        return;\n    }\n    if (prefix == 'e[f]') {\n        return value.getTime();\n    }\n    if (prefix == 'e[g][0]') {\n        return value * 2;\n    }\n    return value;\n}\nqs.stringify({ a: 'b', c: 'd', e: { f: new Date(123), g: [2] } }, { filter: filterFunc });\n// 'a=b&c=d&e[f]=123&e[g][0]=4'\nqs.stringify({ a: 'b', c: 'd', e: 'f' }, { filter: ['a', 'e'] });\n// 'a=b&e=f'\nqs.stringify({ a: ['b', 'c', 'd'], e: 'f' }, { filter: ['a', 0, 2] });\n// 'a[0]=b&a[2]=d'\n```\n\n### Handling of `null` values\n\nBy default, `null` values are treated like empty strings:\n\n```javascript\nvar withNull = qs.stringify({ a: null, b: '' });\nassert.equal(withNull, 'a=&b=');\n```\n\nParsing does not distinguish between parameters with and without equal signs. Both are converted to empty strings.\n\n```javascript\nvar equalsInsensitive = qs.parse('a&b=');\nassert.deepEqual(equalsInsensitive, { a: '', b: '' });\n```\n\nTo distinguish between `null` values and empty strings use the `strictNullHandling` flag. In the result string the `null`\nvalues have no `=` sign:\n\n```javascript\nvar strictNull = qs.stringify({ a: null, b: '' }, { strictNullHandling: true });\nassert.equal(strictNull, 'a&b=');\n```\n\nTo parse values without `=` back to `null` use the `strictNullHandling` flag:\n\n```javascript\nvar parsedStrictNull = qs.parse('a&b=', { strictNullHandling: true });\nassert.deepEqual(parsedStrictNull, { a: null, b: '' });\n```\n\nTo completely skip rendering keys with `null` values, use the `skipNulls` flag:\n\n```javascript\nvar nullsSkipped = qs.stringify({ a: 'b', c: null}, { skipNulls: true });\nassert.equal(nullsSkipped, 'a=b');\n```\n\nIf you're communicating with legacy systems, you can switch to `iso-8859-1`\nusing the `charset` option:\n\n```javascript\nvar iso = qs.stringify({ æ: 'æ' }, { charset: 'iso-8859-1' });\nassert.equal(iso, '%E6=%E6');\n```\n\nCharacters that don't exist in `iso-8859-1` will be converted to numeric\nentities, similar to what browsers do:\n\n```javascript\nvar numeric = qs.stringify({ a: '☺' }, { charset: 'iso-8859-1' });\nassert.equal(numeric, 'a=%26%239786%3B');\n```\n\nYou can use the `charsetSentinel` option to announce the character by\nincluding an `utf8=✓` parameter with the proper encoding if the checkmark,\nsimilar to what Ruby on Rails and others do when submitting forms.\n\n```javascript\nvar sentinel = qs.stringify({ a: '☺' }, { charsetSentinel: true });\nassert.equal(sentinel, 'utf8=%E2%9C%93&a=%E2%98%BA');\n\nvar isoSentinel = qs.stringify({ a: 'æ' }, { charsetSentinel: true, charset: 'iso-8859-1' });\nassert.equal(isoSentinel, 'utf8=%26%2310003%3B&a=%E6');\n```\n\n### Dealing with special character sets\n\nBy default the encoding and decoding of characters is done in `utf-8`,\nand `iso-8859-1` support is also built in via the `charset` parameter.\n\nIf you wish to encode querystrings to a different character set (i.e.\n[Shift JIS](https://en.wikipedia.org/wiki/Shift_JIS)) you can use the\n[`qs-iconv`](https://github.com/martinheidegger/qs-iconv) library:\n\n```javascript\nvar encoder = require('qs-iconv/encoder')('shift_jis');\nvar shiftJISEncoded = qs.stringify({ a: 'こんにちは！' }, { encoder: encoder });\nassert.equal(shiftJISEncoded, 'a=%82%B1%82%F1%82%C9%82%BF%82%CD%81I');\n```\n\nThis also works for decoding of query strings:\n\n```javascript\nvar decoder = require('qs-iconv/decoder')('shift_jis');\nvar obj = qs.parse('a=%82%B1%82%F1%82%C9%82%BF%82%CD%81I', { decoder: decoder });\nassert.deepEqual(obj, { a: 'こんにちは！' });\n```\n\n### RFC 3986 and RFC 1738 space encoding\n\nRFC3986 used as default option and encodes ' ' to *%20* which is backward compatible.\nIn the same time, output can be stringified as per RFC1738 with ' ' equal to '+'.\n\n```\nassert.equal(qs.stringify({ a: 'b c' }), 'a=b%20c');\nassert.equal(qs.stringify({ a: 'b c' }, { format : 'RFC3986' }), 'a=b%20c');\nassert.equal(qs.stringify({ a: 'b c' }, { format : 'RFC1738' }), 'a=b+c');\n```\n\n## Security\n\nPlease email [@ljharb](https://github.com/ljharb) or see https://tidelift.com/security if you have a potential security vulnerability to report.\n\n## qs for enterprise\n\nAvailable as part of the Tidelift Subscription\n\nThe maintainers of qs and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-qs?utm_source=npm-qs&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n\n[1]: https://npmjs.org/package/qs\n[2]: http://versionbadg.es/ljharb/qs.svg\n[3]: https://api.travis-ci.org/ljharb/qs.svg\n[4]: https://travis-ci.org/ljharb/qs\n[5]: https://david-dm.org/ljharb/qs.svg\n[6]: https://david-dm.org/ljharb/qs\n[7]: https://david-dm.org/ljharb/qs/dev-status.svg\n[8]: https://david-dm.org/ljharb/qs?type=dev\n[9]: https://ci.testling.com/ljharb/qs.png\n[10]: https://ci.testling.com/ljharb/qs\n[11]: https://nodei.co/npm/qs.png?downloads=true&stars=true\n[license-image]: http://img.shields.io/npm/l/qs.svg\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/qs.svg\n[downloads-url]: http://npm-stat.com/charts.html?package=qs\n", "licenseText": "BSD 3-Clause License\n\nCopyright (c) 2014, <PERSON> and other [contributors](https://github.com/ljharb/qs/graphs/contributors)\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are met:\n\n1. Redistributions of source code must retain the above copyright notice, this\n   list of conditions and the following disclaimer.\n\n2. Redistributions in binary form must reproduce the above copyright notice,\n   this list of conditions and the following disclaimer in the documentation\n   and/or other materials provided with the distribution.\n\n3. Neither the name of the copyright holder nor the names of its\n   contributors may be used to endorse or promote products derived from\n   this software without specific prior written permission.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\nAND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\nIMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\nDISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE\nFOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON>Q<PERSON><PERSON>IAL\nDAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\nSERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\nCAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\nOR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\nOF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/qs/-/qs-6.9.4.tgz#9090b290d1f91728d3c22e54843ca44aea5ab687", "type": "tarball", "reference": "https://registry.yarnpkg.com/qs/-/qs-6.9.4.tgz", "hash": "9090b290d1f91728d3c22e54843ca44aea5ab687", "integrity": "sha512-A1kFqHekCTM7cz0udomYUoYNWjBebHm/5wzU/XqrBRBNWectVH0QIiN+NEcZ0Dte5hvzHwbr8+XQmguPhJ6WdQ==", "registry": "npm", "packageName": "qs", "cacheIntegrity": "sha512-A1kFqHekCTM7cz0udomYUoYNWjBebHm/5wzU/XqrBRBNWectVH0QIiN+NEcZ0Dte5hvzHwbr8+XQmguPhJ6WdQ== sha1-kJCykNH5FyjTwi5UhDykSupatoc="}, "registry": "npm", "hash": "9090b290d1f91728d3c22e54843ca44aea5ab687"}