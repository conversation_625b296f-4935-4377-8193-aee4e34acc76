# This workflow warns and then closes issues and PRs that have had no activity for a specified amount of time.
#
# You can adjust the behavior by modifying this file.
# For more information, see:
# https://github.com/actions/stale
name: Mark stale issues and pull requests

on:
  schedule:
  - cron: '41 15 * * *'

jobs:
  stale:

    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write

    steps:
    - uses: actions/stale@v5
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        stale-issue-message: 'This issue has had 60 days of inactivity & will close within 7 days'
        stale-pr-message: 'This PR has had 60 days of inactivity & will close within 7 days'
        close-issue-label: 'Stale Closed'
        close-pr-label: 'Stale Closed'
        exempt-issue-labels: 'Suggestion'
        exempt-pr-labels: 'Suggestion'
