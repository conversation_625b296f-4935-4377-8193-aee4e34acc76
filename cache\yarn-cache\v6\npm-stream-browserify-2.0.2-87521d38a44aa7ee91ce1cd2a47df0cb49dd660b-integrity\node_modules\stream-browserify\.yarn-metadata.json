{"manifest": {"name": "stream-browserify", "version": "2.0.2", "description": "the stream module from node core for browsers", "main": "index.js", "dependencies": {"inherits": "~2.0.1", "readable-stream": "^2.0.2"}, "devDependencies": {"safe-buffer": "^5.1.2", "tape": "^4.2.0", "typedarray": "~0.0.6"}, "scripts": {"test": "tape test/*.js"}, "repository": {"type": "git", "url": "git://github.com/browserify/stream-browserify.git"}, "homepage": "https://github.com/browserify/stream-browserify", "keywords": ["stream", "browser", "browserify"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/3.5", "firefox/10", "firefox/nightly", "chrome/10", "chrome/latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-stream-browserify-2.0.2-87521d38a44aa7ee91ce1cd2a47df0cb49dd660b-integrity\\node_modules\\stream-browserify\\package.json", "readmeFilename": "readme.markdown", "readme": "# stream-browserify\n\nthe stream module from node core, for browsers!\n\n[![build status](https://secure.travis-ci.org/browserify/stream-browserify.svg)](http://travis-ci.org/browserify/stream-browserify)\n\n# methods\n\nConsult the node core\n[documentation on streams](http://nodejs.org/docs/latest/api/stream.html).\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install stream-browserify\n```\n\nbut if you are using browserify you will get this module automatically when you\ndo `require('stream')`.\n\n# license\n\nMIT\n", "licenseText": "This software is released under the MIT license:\n\nCopyright (c) James <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/stream-browserify/-/stream-browserify-2.0.2.tgz#87521d38a44aa7ee91ce1cd2a47df0cb49dd660b", "type": "tarball", "reference": "https://registry.yarnpkg.com/stream-browserify/-/stream-browserify-2.0.2.tgz", "hash": "87521d38a44aa7ee91ce1cd2a47df0cb49dd660b", "integrity": "sha512-nX6hmklHs/gr2FuxYDltq8fJA1GDlxKQCz8O/IM4atRqBH8OORmBNgfvW5gG10GT/qQ9u0CzIvr2X5Pkt6ntqg==", "registry": "npm", "packageName": "stream-browserify", "cacheIntegrity": "sha512-nX6hmklHs/gr2FuxYDltq8fJA1GDlxKQCz8O/IM4atRqBH8OORmBNgfvW5gG10GT/qQ9u0CzIvr2X5Pkt6ntqg== sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs="}, "registry": "npm", "hash": "87521d38a44aa7ee91ce1cd2a47df0cb49dd660b"}