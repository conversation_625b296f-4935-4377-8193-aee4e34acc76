{"manifest": {"name": "create-hash", "version": "1.2.0", "description": "create hashes for browserify", "browser": "browser.js", "main": "index.js", "scripts": {"standard": "standard", "test": "npm run-script standard && npm run-script unit", "unit": "node test.js | tspec"}, "repository": {"type": "git", "url": "**************:crypto-browserify/createHash.git"}, "keywords": ["crypto"], "author": {}, "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/createHash/issues"}, "homepage": "https://github.com/crypto-browserify/createHash", "devDependencies": {"hash-test-vectors": "^1.3.2", "safe-buffer": "^5.0.1", "standard": "^10.0.2", "tap-spec": "^2.1.2", "tape": "^4.6.3"}, "dependencies": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-create-hash-1.2.0-889078af11a63756bcfb59bd221996be3a9ef196-integrity\\node_modules\\create-hash\\package.json", "readmeFilename": "README.md", "readme": "# create-hash\n\n[![Build Status](https://travis-ci.org/crypto-browserify/createHash.svg)](https://travis-ci.org/crypto-browserify/createHash)\n\nNode style hashes for use in the browser, with native hash functions in node.\n\nAPI is the same as hashes in node:\n```js\nvar createHash = require('create-hash')\nvar hash = createHash('sha224')\nhash.update('synchronous write') // optional encoding parameter\nhash.digest() // synchronously get result with optional encoding parameter\n\nhash.write('write to it as a stream')\nhash.end() // remember it's a stream\nhash.read() // only if you ended it as a stream though\n```\n\nTo get the JavaScript version even in node do `require('create-hash/browser')`\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2017 crypto-browserify contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/create-hash/-/create-hash-1.2.0.tgz#889078af11a63756bcfb59bd221996be3a9ef196", "type": "tarball", "reference": "https://registry.yarnpkg.com/create-hash/-/create-hash-1.2.0.tgz", "hash": "889078af11a63756bcfb59bd221996be3a9ef196", "integrity": "sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg==", "registry": "npm", "packageName": "create-hash", "cacheIntegrity": "sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg== sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY="}, "registry": "npm", "hash": "889078af11a63756bcfb59bd221996be3a9ef196"}