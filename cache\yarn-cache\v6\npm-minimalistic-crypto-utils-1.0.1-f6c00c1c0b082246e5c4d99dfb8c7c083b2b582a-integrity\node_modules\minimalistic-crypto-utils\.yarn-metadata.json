{"manifest": {"name": "minimalistic-crypto-utils", "version": "1.0.1", "description": "Minimalistic tools for JS crypto modules", "main": "lib/utils.js", "scripts": {"test": "mocha --reporter=spec test/*-test.js"}, "repository": {"type": "git", "url": "git+ssh://**************/indutny/minimalistic-crypto-utils.git"}, "keywords": ["minimalistic", "utils", "crypto"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/indutny/minimalistic-crypto-utils/issues"}, "homepage": "https://github.com/indutny/minimalistic-crypto-utils#readme", "devDependencies": {"mocha": "^3.2.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-minimalistic-crypto-utils-1.0.1-f6c00c1c0b082246e5c4d99dfb8c7c083b2b582a-integrity\\node_modules\\minimalistic-crypto-utils\\package.json", "readmeFilename": "README.md", "readme": "# minimalistic-crypto-utils\n[![Build Status](https://secure.travis-ci.org/indutny/minimalistic-crypto-utils.svg)](http://travis-ci.org/indutny/minimalistic-crypto-utils)\n[![NPM version](https://badge.fury.io/js/minimalistic-crypto-utils.svg)](http://badge.fury.io/js/minimalistic-crypto-utils)\n\nVery minimal utils that are required in order to write reasonable JS-only\ncrypto module.\n\n## Usage\n\n```js\nconst utils = require('minimalistic-crypto-utils');\n\nutils.toArray('abcd', 'hex');\nutils.encode([ 1, 2, 3, 4 ], 'hex');\nutils.toHex([ 1, 2, 3, 4 ]);\n```\n\n#### LICENSE\n\nThis software is licensed under the MIT License.\n\nCopyright Fedor Indutny, 2017.\n\nPermission is hereby granted, free of charge, to any person obtaining a\ncopy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to permit\npersons to whom the Software is furnished to do so, subject to the\nfollowing conditions:\n\nThe above copyright notice and this permission notice shall be included\nin all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\nOR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\nNO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\nDAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\nOTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\nUSE OR OTHER DEALINGS IN THE SOFTWARE.\n\n[0]: http://tools.ietf.org/html/rfc6979\n[1]: https://github.com/indutny/bn.js\n[2]: https://github.com/indutny/hash.js\n[3]: https://github.com/bitchan/eccrypto\n[4]: https://github.com/wanderer/secp256k1-node\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz#f6c00c1c0b082246e5c4d99dfb8c7c083b2b582a", "type": "tarball", "reference": "https://registry.yarnpkg.com/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz", "hash": "f6c00c1c0b082246e5c4d99dfb8c7c083b2b582a", "integrity": "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=", "registry": "npm", "packageName": "minimalistic-crypto-utils", "cacheIntegrity": "sha512-JIYlbt6g8i5jKfJ3xz7rF0LXmv2TkDxBLUkiBeZ7bAx4GnnNMr8xFpGnOxn6GhTEHx3SjRrZEoU+j04prX1ktg== sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo="}, "registry": "npm", "hash": "f6c00c1c0b082246e5c4d99dfb8c7c083b2b582a"}