{"manifest": {"name": "schema-utils", "version": "1.0.0", "description": "webpack Validation Utils", "main": "src/index.js", "files": ["src"], "engines": {"node": ">= 4"}, "scripts": {"lint": "eslint --cache src test", "test": "jest --env node --verbose --coverage", "clean": "del-cli coverage", "commits": "commitlint --from $(git rev-list --tags --max-count=1)", "release": "npm run commits && standard-version"}, "dependencies": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}, "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-conventional": "^7.0.0", "@webpack-contrib/eslint-config-webpack": "^2.0.0", "del-cli": "^1.0.0", "eslint": "^5.0.0", "eslint-plugin-import": "^2.0.0", "eslint-plugin-prettier": "^2.0.0", "jest": "^21.0.0", "prettier": "^1.0.0", "standard-version": "^4.0.0"}, "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "homepage": "https://github.com/webpack-contrib/schema-utils", "repository": {"type": "git", "url": "https://github.com/webpack-contrib/schema-utils"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-schema-utils-1.0.0-0b79a93204d7b600d4b2850d1f66c2a34951c770-integrity\\node_modules\\schema-utils\\package.json", "readmeFilename": "README.md", "readme": "[![npm][npm]][npm-url]\n[![node][node]][node-url]\n[![deps][deps]][deps-url]\n[![test][test]][test-url]\n[![coverage][cover]][cover-url]\n[![chat][chat]][chat-url]\n\n<div align=\"center\">\n  <a href=\"http://json-schema.org\">\n    <img width=\"160\" height=\"160\"\n      src=\"https://raw.githubusercontent.com/webpack-contrib/schema-utils/master/docs/logo.png\">\n  </a>\n  <a href=\"https://github.com/webpack/webpack\">\n    <img width=\"200\" height=\"200\"\n      src=\"https://webpack.js.org/assets/icon-square-big.svg\">\n  </a>\n  <h1>Schema Utils</h1>\n</div>\n\n<h2 align=\"center\">Install</h2>\n\n```bash\nnpm i schema-utils\n```\n\n<h2 align=\"center\">Usage</h2>\n\n### `validateOptions`\n\n**`schema.json`**\n```js\n{\n  \"type\": \"object\",\n  \"properties\": {\n    // Options...\n  },\n  \"additionalProperties\": false\n}\n```\n\n#### Error Messages (Custom)\n\n**`schema.json`**\n```js\n{\n  \"type\": \"object\",\n  \"properties\": {\n    \"option\": {\n      \"type\": [ \"boolean\" ]\n    }\n  },\n  // Overrides the default err.message for option\n  \"errorMessage\": {\n    \"option\": \"should be {Boolean} (https:/github.com/org/repo#anchor)\"\n  }\n  \"additionalProperties\": false\n}\n```\n\n```js\nimport schema from 'path/to/schema.json'\nimport validateOptions from 'schema-utils'\n\nvalidateOptions(schema, options, 'Loader/Plugin Name')\n```\n\n<h2 align=\"center\">Examples</h2>\n\n**schema.json**\n```json\n{\n  \"type\": \"object\",\n  \"properties\": {\n    \"name\": {\n      \"type\": \"string\"\n    },\n    \"test\": {\n      \"anyOf\": [\n        { \"type\": \"array\" },\n        { \"type\": \"string\" },\n        { \"instanceof\": \"RegExp\" }\n      ]\n    },\n    \"transform\": {\n      \"instanceof\": \"Function\"\n    },\n    \"sourceMap\": {\n      \"type\": \"boolean\"\n    }\n  },\n  \"additionalProperties\": false\n}\n```\n\n### `Loader`\n\n```js\nimport { getOptions } from 'loader-utils'\nimport validateOptions from 'schema-utils'\n\nimport schema from 'path/to/schema.json'\n\nfunction loader (src, map) {\n  const options = getOptions(this) || {}\n\n  validateOptions(schema, options, 'Loader Name')\n\n  // Code...\n}\n```\n\n### `Plugin`\n\n```js\nimport validateOptions from 'schema-utils'\n\nimport schema from 'path/to/schema.json'\n\nclass Plugin {\n  constructor (options) {\n    validateOptions(schema, options, 'Plugin Name')\n\n    this.options = options\n  }\n\n  apply (compiler) {\n    // Code...\n  }\n}\n```\n\n\n[npm]: https://img.shields.io/npm/v/schema-utils.svg\n[npm-url]: https://npmjs.com/package/schema-utils\n\n[node]: https://img.shields.io/node/v/schema-utils.svg\n[node-url]: https://nodejs.org\n\n[deps]: https://david-dm.org/webpack-contrib/schema-utils.svg\n[deps-url]: https://david-dm.org/webpack-contrib/schema-utils\n\n[test]: http://img.shields.io/travis/webpack-contrib/schema-utils.svg\n[test-url]: https://travis-ci.org/webpack-contrib/schema-utils\n\n[cover]: https://codecov.io/gh/webpack-contrib/schema-utils/branch/master/graph/badge.svg\n[cover-url]: https://codecov.io/gh/webpack-contrib/schema-utils\n\n[chat]: https://img.shields.io/badge/gitter-webpack%2Fwebpack-brightgreen.svg\n[chat-url]: https://gitter.im/webpack/webpack\n", "licenseText": "Copyright JS Foundation and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/schema-utils/-/schema-utils-1.0.0.tgz#0b79a93204d7b600d4b2850d1f66c2a34951c770", "type": "tarball", "reference": "https://registry.yarnpkg.com/schema-utils/-/schema-utils-1.0.0.tgz", "hash": "0b79a93204d7b600d4b2850d1f66c2a34951c770", "integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==", "registry": "npm", "packageName": "schema-utils", "cacheIntegrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g== sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A="}, "registry": "npm", "hash": "0b79a93204d7b600d4b2850d1f66c2a34951c770"}