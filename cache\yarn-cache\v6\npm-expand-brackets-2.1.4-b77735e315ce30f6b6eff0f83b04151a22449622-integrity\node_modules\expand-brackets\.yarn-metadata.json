{"manifest": {"name": "expand-brackets", "description": "Expand POSIX bracket expressions (character classes) in glob patterns.", "version": "2.1.4", "homepage": "https://github.com/jonschlinkert/expand-brackets", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "url": "https://github.com/eush77"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kolarik.sk"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/expand-brackets.git"}, "bugs": {"url": "https://github.com/jonschlinkert/expand-brackets/issues"}, "license": "MIT", "files": ["index.js", "lib"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^0.1.1", "gulp-format-md": "^0.1.10", "helper-changelog": "^0.3.0", "minimatch": "^3.0.3", "mocha": "^3.0.2", "multimatch": "^2.1.0", "yargs-parser": "^4.0.0"}, "keywords": ["bracket", "brackets", "character class", "expand", "expression", "posix"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "helpers": ["helper-changelog"], "related": {"list": ["braces", "extglob", "micromatch", "nanomatch"]}, "reflinks": ["micromatch", "verb", "verb-generate-readme"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-expand-brackets-2.1.4-b77735e315ce30f6b6eff0f83b04151a22449622-integrity\\node_modules\\expand-brackets\\package.json", "readmeFilename": "README.md", "readme": "# expand-brackets [![NPM version](https://img.shields.io/npm/v/expand-brackets.svg?style=flat)](https://www.npmjs.com/package/expand-brackets) [![NPM monthly downloads](https://img.shields.io/npm/dm/expand-brackets.svg?style=flat)](https://npmjs.org/package/expand-brackets)  [![NPM total downloads](https://img.shields.io/npm/dt/expand-brackets.svg?style=flat)](https://npmjs.org/package/expand-brackets) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/expand-brackets.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/expand-brackets) [![Windows Build Status](https://img.shields.io/appveyor/ci/jonschlinkert/expand-brackets.svg?style=flat&label=AppVeyor)](https://ci.appveyor.com/project/jonschlinkert/expand-brackets)\n\n> Expand POSIX bracket expressions (character classes) in glob patterns.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save expand-brackets\n```\n\n## Usage\n\n```js\nvar brackets = require('expand-brackets');\nbrackets(string[, options]);\n```\n\n**Params**\n\nThe main export is a function that takes the following parameters:\n\n* `pattern` **{String}**: the pattern to convert\n* `options` **{Object}**: optionally supply an options object\n* `returns` **{String}**: returns a string that can be used to create a regex\n\n**Example**\n\n```js\nconsole.log(brackets('[![:lower:]]'));\n//=> '[^a-z]'\n```\n\n## API\n\n### [brackets](index.js#L29)\n\nParses the given POSIX character class `pattern` and returns a\nstring that can be used for creating regular expressions for matching.\n\n**Params**\n\n* `pattern` **{String}**\n* `options` **{Object}**\n* `returns` **{Object}**\n\n### [.match](index.js#L54)\n\nTakes an array of strings and a POSIX character class pattern, and returns a new array with only the strings that matched the pattern.\n\n**Example**\n\n```js\nvar brackets = require('expand-brackets');\nconsole.log(brackets.match(['1', 'a', 'ab'], '[[:alpha:]]'));\n//=> ['a']\n\nconsole.log(brackets.match(['1', 'a', 'ab'], '[[:alpha:]]+'));\n//=> ['a', 'ab']\n```\n\n**Params**\n\n* `arr` **{Array}**: Array of strings to match\n* `pattern` **{String}**: POSIX character class pattern(s)\n* `options` **{Object}**\n* `returns` **{Array}**\n\n### [.isMatch](index.js#L100)\n\nReturns true if the specified `string` matches the given brackets `pattern`.\n\n**Example**\n\n```js\nvar brackets = require('expand-brackets');\n\nconsole.log(brackets.isMatch('a.a', '[[:alpha:]].[[:alpha:]]'));\n//=> true\nconsole.log(brackets.isMatch('1.2', '[[:alpha:]].[[:alpha:]]'));\n//=> false\n```\n\n**Params**\n\n* `string` **{String}**: String to match\n* `pattern` **{String}**: Poxis pattern\n* `options` **{String}**\n* `returns` **{Boolean}**\n\n### [.matcher](index.js#L123)\n\nTakes a POSIX character class pattern and returns a matcher function. The returned function takes the string to match as its only argument.\n\n**Example**\n\n```js\nvar brackets = require('expand-brackets');\nvar isMatch = brackets.matcher('[[:lower:]].[[:upper:]]');\n\nconsole.log(isMatch('a.a'));\n//=> false\nconsole.log(isMatch('a.A'));\n//=> true\n```\n\n**Params**\n\n* `pattern` **{String}**: Poxis pattern\n* `options` **{String}**\n* `returns` **{Boolean}**\n\n### [.makeRe](index.js#L145)\n\nCreate a regular expression from the given `pattern`.\n\n**Example**\n\n```js\nvar brackets = require('expand-brackets');\nvar re = brackets.makeRe('[[:alpha:]]');\nconsole.log(re);\n//=> /^(?:[a-zA-Z])$/\n```\n\n**Params**\n\n* `pattern` **{String}**: The pattern to convert to regex.\n* `options` **{Object}**\n* `returns` **{RegExp}**\n\n### [.create](index.js#L187)\n\nParses the given POSIX character class `pattern` and returns an object with the compiled `output` and optional source `map`.\n\n**Example**\n\n```js\nvar brackets = require('expand-brackets');\nconsole.log(brackets('[[:alpha:]]'));\n// { options: { source: 'string' },\n//   input: '[[:alpha:]]',\n//   state: {},\n//   compilers:\n//    { eos: [Function],\n//      noop: [Function],\n//      bos: [Function],\n//      not: [Function],\n//      escape: [Function],\n//      text: [Function],\n//      posix: [Function],\n//      bracket: [Function],\n//      'bracket.open': [Function],\n//      'bracket.inner': [Function],\n//      'bracket.literal': [Function],\n//      'bracket.close': [Function] },\n//   output: '[a-zA-Z]',\n//   ast:\n//    { type: 'root',\n//      errors: [],\n//      nodes: [ [Object], [Object], [Object] ] },\n//   parsingErrors: [] }\n```\n\n**Params**\n\n* `pattern` **{String}**\n* `options` **{Object}**\n* `returns` **{Object}**\n\n## Options\n\n### options.sourcemap\n\nGenerate a source map for the given pattern.\n\n**Example**\n\n```js\nvar res = brackets('[:alpha:]', {sourcemap: true});\n\nconsole.log(res.map);\n// { version: 3,\n//   sources: [ 'brackets' ],\n//   names: [],\n//   mappings: 'AAAA,MAAS',\n//   sourcesContent: [ '[:alpha:]' ] }\n```\n\n### POSIX Character classes\n\nThe following named POSIX bracket expressions are supported:\n\n* `[:alnum:]`: Alphanumeric characters (`a-zA-Z0-9]`)\n* `[:alpha:]`: Alphabetic characters (`a-zA-Z]`)\n* `[:blank:]`: Space and tab (`[ t]`)\n* `[:digit:]`: Digits (`[0-9]`)\n* `[:lower:]`: Lowercase letters (`[a-z]`)\n* `[:punct:]`: Punctuation and symbols. (`[!\"#$%&'()*+, -./:;<=>?@ [\\]^_``{|}~]`)\n* `[:upper:]`: Uppercase letters (`[A-Z]`)\n* `[:word:]`: Word characters (letters, numbers and underscores) (`[A-Za-z0-9_]`)\n* `[:xdigit:]`: Hexadecimal digits (`[A-Fa-f0-9]`)\n\nSee [posix-character-classes](https://github.com/jonschlinkert/posix-character-classes) for more details.\n\n**Not supported**\n\n* [equivalence classes](https://www.gnu.org/software/gawk/manual/html_node/Bracket-Expressions.html) are not supported\n* [POSIX.2 collating symbols](https://www.gnu.org/software/gawk/manual/html_node/Bracket-Expressions.html) are not supported\n\n## Changelog\n\n### v2.0.0\n\n**Breaking changes**\n\n* The main export now returns the compiled string, instead of the object returned from the compiler\n\n**Added features**\n\n* Adds a `.create` method to do what the main function did before v2.0.0\n\n### v0.2.0\n\nIn addition to performance and matching improvements, the v0.2.0 refactor adds complete POSIX character class support, with the exception of equivalence classes and POSIX.2 collating symbols which are not relevant to node.js usage.\n\n**Added features**\n\n* parser is exposed, so that expand-brackets parsers can be used by upstream parsers (like [micromatch](https://github.com/jonschlinkert/micromatch))\n* compiler is exposed, so that expand-brackets compilers can be used by upstream compilers\n* source maps\n\n**source map example**\n\n```js\nvar brackets = require('expand-brackets');\nvar res = brackets('[:alpha:]');\nconsole.log(res.map);\n\n{ version: 3,\n     sources: [ 'brackets' ],\n     names: [],\n     mappings: 'AAAA,MAAS',\n     sourcesContent: [ '[:alpha:]' ] }\n```\n\n## About\n\n### Related projects\n\n* [braces](https://www.npmjs.com/package/braces): Fast, comprehensive, bash-like brace expansion implemented in JavaScript. Complete support for the Bash 4.3 braces… [more](https://github.com/jonschlinkert/braces) | [homepage](https://github.com/jonschlinkert/braces \"Fast, comprehensive, bash-like brace expansion implemented in JavaScript. Complete support for the Bash 4.3 braces specification, without sacrificing speed.\")\n* [extglob](https://www.npmjs.com/package/extglob): Extended glob support for JavaScript. Adds (almost) the expressive power of regular expressions to glob… [more](https://github.com/jonschlinkert/extglob) | [homepage](https://github.com/jonschlinkert/extglob \"Extended glob support for JavaScript. Adds (almost) the expressive power of regular expressions to glob patterns.\")\n* [micromatch](https://www.npmjs.com/package/micromatch): Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. | [homepage](https://github.com/jonschlinkert/micromatch \"Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.\")\n* [nanomatch](https://www.npmjs.com/package/nanomatch): Fast, minimal glob matcher for node.js. Similar to micromatch, minimatch and multimatch, but complete Bash… [more](https://github.com/jonschlinkert/nanomatch) | [homepage](https://github.com/jonschlinkert/nanomatch \"Fast, minimal glob matcher for node.js. Similar to micromatch, minimatch and multimatch, but complete Bash 4.3 wildcard support only (no support for exglobs, posix brackets or braces)\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor**<br/> | \n| --- | --- |\n| 66 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 2 | [MartinKolarik](https://github.com/MartinKolarik) |\n| 2 | [es128](https://github.com/es128) |\n| 1 | [eush77](https://github.com/eush77) |\n\n### Building docs\n\n_(This document was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme) (a [verb](https://github.com/verbose/verb) generator), please don't edit the readme directly. Any changes to the readme must be made in [.verb.md](.verb.md).)_\n\nTo generate the readme and API documentation with [verb](https://github.com/verbose/verb):\n\n```sh\n$ npm install -g verb verb-generate-readme && verb\n```\n\n### Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm install -d && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT license](https://github.com/jonschlinkert/expand-brackets/blob/master/LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.2.0, on December 12, 2016._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015-2016, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/expand-brackets/-/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622", "type": "tarball", "reference": "https://registry.yarnpkg.com/expand-brackets/-/expand-brackets-2.1.4.tgz", "hash": "b77735e315ce30f6b6eff0f83b04151a22449622", "integrity": "sha1-t3c14xXOMPa27/D4OwQVGiJEliI=", "registry": "npm", "packageName": "expand-brackets", "cacheIntegrity": "sha512-w/ozOKR9Obk3qoWeY/WDi6MFta9AoMR+zud60mdnbniMcBxRuFJyDt2LdX/14A1UABeqk+Uk+LDfUpvoGKppZA== sha1-t3c14xXOMPa27/D4OwQVGiJEliI="}, "registry": "npm", "hash": "b77735e315ce30f6b6eff0f83b04151a22449622"}