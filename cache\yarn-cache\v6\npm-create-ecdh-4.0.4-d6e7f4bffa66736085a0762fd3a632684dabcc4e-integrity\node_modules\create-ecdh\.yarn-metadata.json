{"manifest": {"name": "create-ecdh", "version": "4.0.4", "description": "createECDH but browserifiable", "main": "index.js", "browser": "browser.js", "scripts": {"test": "standard && node test.js | tspec"}, "repository": {"type": "git", "url": "https://github.com/crypto-browserify/createECDH.git"}, "keywords": ["diffie", "hellman", "di<PERSON><PERSON><PERSON><PERSON>", "ECDH"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/createECDH/issues"}, "homepage": "https://github.com/crypto-browserify/createECDH", "dependencies": {"bn.js": "^4.1.0", "elliptic": "^6.5.3"}, "devDependencies": {"tap-spec": "^1.0.1", "tape": "^3.0.1", "standard": "^5.4.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-create-ecdh-4.0.4-d6e7f4bffa66736085a0762fd3a632684dabcc4e-integrity\\node_modules\\create-ecdh\\package.json", "readmeFilename": "readme.md", "readme": "createECDH [![Build Status](https://travis-ci.org/crypto-browserify/createECDH.svg)](https://travis-ci.org/crypto-browserify/createECDH)\n====\n\nIn io.js or node >= 0.11 this module is just a shortcut to crypto.createECDH.  In node <= 0.11 or the browser this is a pure JavaScript implimentation, more specifically a wrapper around [elliptic](https://github.com/indutny/elliptic), to give it the same API as node. `secp256k1`, `secp224r1` (aka p224), `prime256v1` (aka p256, secp256r1), `prime192v1` (aka p192, secp192r1), `secp384r1` (aka p384), `secp521r1` (aka p521)  curves all work in both this library and node (though only the highlighted name will work in node).\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017 createECDH contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/create-ecdh/-/create-ecdh-4.0.4.tgz#d6e7f4bffa66736085a0762fd3a632684dabcc4e", "type": "tarball", "reference": "https://registry.yarnpkg.com/create-ecdh/-/create-ecdh-4.0.4.tgz", "hash": "d6e7f4bffa66736085a0762fd3a632684dabcc4e", "integrity": "sha512-mf+TCx8wWc9VpuxfP2ht0iSISLZnt0JgWlrOKZiNqyUZWnjIaCIVNQArMHnCZKfEYRg6IM7A+NeJoN8gf/Ws0A==", "registry": "npm", "packageName": "create-ecdh", "cacheIntegrity": "sha512-mf+TCx8wWc9VpuxfP2ht0iSISLZnt0JgWlrOKZiNqyUZWnjIaCIVNQArMHnCZKfEYRg6IM7A+NeJoN8gf/Ws0A== sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4="}, "registry": "npm", "hash": "d6e7f4bffa66736085a0762fd3a632684dabcc4e"}