{"manifest": {"name": "chalk", "version": "2.4.2", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/chalk/chalk.git"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && tsc --project types && flow --max-warnings=0 && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js", "templates.js", "types/index.d.ts", "index.js.flow"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "devDependencies": {"ava": "*", "coveralls": "^3.0.0", "execa": "^0.9.0", "flow-bin": "^0.68.0", "import-fresh": "^2.0.0", "matcha": "^0.7.0", "nyc": "^11.0.2", "resolve-from": "^4.0.0", "typescript": "^2.5.3", "xo": "*"}, "types": "types/index.d.ts", "xo": {"envs": ["node", "mocha"], "ignores": ["test/_flow.js"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-chalk-2.4.2-cd42541677a54333cf541a49108c1432b44c9424-integrity\\node_modules\\chalk\\package.json", "readmeFilename": "readme.md", "readme": "<h1 align=\"center\">\n\t<br>\n\t<br>\n\t<img width=\"320\" src=\"media/logo.svg\" alt=\"Chalk\">\n\t<br>\n\t<br>\n\t<br>\n</h1>\n\n> Terminal string styling done right\n\n[![Build Status](https://travis-ci.org/chalk/chalk.svg?branch=master)](https://travis-ci.org/chalk/chalk) [![Coverage Status](https://coveralls.io/repos/github/chalk/chalk/badge.svg?branch=master)](https://coveralls.io/github/chalk/chalk?branch=master) [![](https://img.shields.io/badge/unicorn-approved-ff69b4.svg)](https://www.youtube.com/watch?v=9auOCbH5Ns4) [![XO code style](https://img.shields.io/badge/code_style-XO-5ed9c7.svg)](https://github.com/xojs/xo) [![Mentioned in Awesome Node.js](https://awesome.re/mentioned-badge.svg)](https://github.com/sindresorhus/awesome-nodejs)\n\n### [See what's new in Chalk 2](https://github.com/chalk/chalk/releases/tag/v2.0.0)\n\n<img src=\"https://cdn.rawgit.com/chalk/ansi-styles/8261697c95bf34b6c7767e2cbe9941a851d59385/screenshot.svg\" alt=\"\" width=\"900\">\n\n\n## Highlights\n\n- Expressive API\n- Highly performant\n- Ability to nest styles\n- [256/Truecolor color support](#256-and-truecolor-color-support)\n- Auto-detects color support\n- Doesn't extend `String.prototype`\n- Clean and focused\n- Actively maintained\n- [Used by ~23,000 packages](https://www.npmjs.com/browse/depended/chalk) as of December 31, 2017\n\n\n## Install\n\n```console\n$ npm install chalk\n```\n\n<a href=\"https://www.patreon.com/sindresorhus\">\n\t<img src=\"https://c5.patreon.com/external/logo/<EMAIL>\" width=\"160\">\n</a>\n\n\n## Usage\n\n```js\nconst chalk = require('chalk');\n\nconsole.log(chalk.blue('Hello world!'));\n```\n\nChalk comes with an easy to use composable API where you just chain and nest the styles you want.\n\n```js\nconst chalk = require('chalk');\nconst log = console.log;\n\n// Combine styled and normal strings\nlog(chalk.blue('Hello') + ' World' + chalk.red('!'));\n\n// Compose multiple styles using the chainable API\nlog(chalk.blue.bgRed.bold('Hello world!'));\n\n// Pass in multiple arguments\nlog(chalk.blue('Hello', 'World!', 'Foo', 'bar', 'biz', 'baz'));\n\n// Nest styles\nlog(chalk.red('Hello', chalk.underline.bgBlue('world') + '!'));\n\n// Nest styles of the same type even (color, underline, background)\nlog(chalk.green(\n\t'I am a green line ' +\n\tchalk.blue.underline.bold('with a blue substring') +\n\t' that becomes green again!'\n));\n\n// ES2015 template literal\nlog(`\nCPU: ${chalk.red('90%')}\nRAM: ${chalk.green('40%')}\nDISK: ${chalk.yellow('70%')}\n`);\n\n// ES2015 tagged template literal\nlog(chalk`\nCPU: {red ${cpu.totalPercent}%}\nRAM: {green ${ram.used / ram.total * 100}%}\nDISK: {rgb(255,131,0) ${disk.used / disk.total * 100}%}\n`);\n\n// Use RGB colors in terminal emulators that support it.\nlog(chalk.keyword('orange')('Yay for orange colored text!'));\nlog(chalk.rgb(123, 45, 67).underline('Underlined reddish color'));\nlog(chalk.hex('#DEADED').bold('Bold gray!'));\n```\n\nEasily define your own themes:\n\n```js\nconst chalk = require('chalk');\n\nconst error = chalk.bold.red;\nconst warning = chalk.keyword('orange');\n\nconsole.log(error('Error!'));\nconsole.log(warning('Warning!'));\n```\n\nTake advantage of console.log [string substitution](https://nodejs.org/docs/latest/api/console.html#console_console_log_data_args):\n\n```js\nconst name = 'Sindre';\nconsole.log(chalk.green('Hello %s'), name);\n//=> 'Hello Sindre'\n```\n\n\n## API\n\n### chalk.`<style>[.<style>...](string, [string...])`\n\nExample: `chalk.red.bold.underline('Hello', 'world');`\n\nChain [styles](#styles) and call the last one as a method with a string argument. Order doesn't matter, and later styles take precedent in case of a conflict. This simply means that `chalk.red.yellow.green` is equivalent to `chalk.green`.\n\nMultiple arguments will be separated by space.\n\n### chalk.enabled\n\nColor support is automatically detected, as is the level (see `chalk.level`). However, if you'd like to simply enable/disable Chalk, you can do so via the `.enabled` property.\n\nChalk is enabled by default unless explicitly disabled via the constructor or `chalk.level` is `0`.\n\nIf you need to change this in a reusable module, create a new instance:\n\n```js\nconst ctx = new chalk.constructor({enabled: false});\n```\n\n### chalk.level\n\nColor support is automatically detected, but you can override it by setting the `level` property. You should however only do this in your own code as it applies globally to all Chalk consumers.\n\nIf you need to change this in a reusable module, create a new instance:\n\n```js\nconst ctx = new chalk.constructor({level: 0});\n```\n\nLevels are as follows:\n\n0. All colors disabled\n1. Basic color support (16 colors)\n2. 256 color support\n3. Truecolor support (16 million colors)\n\n### chalk.supportsColor\n\nDetect whether the terminal [supports color](https://github.com/chalk/supports-color). Used internally and handled for you, but exposed for convenience.\n\nCan be overridden by the user with the flags `--color` and `--no-color`. For situations where using `--color` is not possible, add the environment variable `FORCE_COLOR=1` to forcefully enable color or `FORCE_COLOR=0` to forcefully disable. The use of `FORCE_COLOR` overrides all other color support checks.\n\nExplicit 256/Truecolor mode can be enabled using the `--color=256` and `--color=16m` flags, respectively.\n\n\n## Styles\n\n### Modifiers\n\n- `reset`\n- `bold`\n- `dim`\n- `italic` *(Not widely supported)*\n- `underline`\n- `inverse`\n- `hidden`\n- `strikethrough` *(Not widely supported)*\n- `visible` (Text is emitted only if enabled)\n\n### Colors\n\n- `black`\n- `red`\n- `green`\n- `yellow`\n- `blue` *(On Windows the bright version is used since normal blue is illegible)*\n- `magenta`\n- `cyan`\n- `white`\n- `gray` (\"bright black\")\n- `redBright`\n- `greenBright`\n- `yellowBright`\n- `blueBright`\n- `magentaBright`\n- `cyanBright`\n- `whiteBright`\n\n### Background colors\n\n- `bgBlack`\n- `bgRed`\n- `bgGreen`\n- `bgYellow`\n- `bgBlue`\n- `bgMagenta`\n- `bgCyan`\n- `bgWhite`\n- `bgBlackBright`\n- `bgRedBright`\n- `bgGreenBright`\n- `bgYellowBright`\n- `bgBlueBright`\n- `bgMagentaBright`\n- `bgCyanBright`\n- `bgWhiteBright`\n\n\n## Tagged template literal\n\nChalk can be used as a [tagged template literal](http://exploringjs.com/es6/ch_template-literals.html#_tagged-template-literals).\n\n```js\nconst chalk = require('chalk');\n\nconst miles = 18;\nconst calculateFeet = miles => miles * 5280;\n\nconsole.log(chalk`\n  There are {bold 5280 feet} in a mile.\n  In {bold ${miles} miles}, there are {green.bold ${calculateFeet(miles)} feet}.\n`);\n```\n\nBlocks are delimited by an opening curly brace (`{`), a style, some content, and a closing curly brace (`}`).\n\nTemplate styles are chained exactly like normal Chalk styles. The following two statements are equivalent:\n\n```js\nconsole.log(chalk.bold.rgb(10, 100, 200)('Hello!'));\nconsole.log(chalk`{bold.rgb(10,100,200) Hello!}`);\n```\n\nNote that function styles (`rgb()`, `hsl()`, `keyword()`, etc.) may not contain spaces between parameters.\n\nAll interpolated values (`` chalk`${foo}` ``) are converted to strings via the `.toString()` method. All curly braces (`{` and `}`) in interpolated value strings are escaped.\n\n\n## 256 and Truecolor color support\n\nChalk supports 256 colors and [Truecolor](https://gist.github.com/XVilka/8346728) (16 million colors) on supported terminal apps.\n\nColors are downsampled from 16 million RGB values to an ANSI color format that is supported by the terminal emulator (or by specifying `{level: n}` as a Chalk option). For example, Chalk configured to run at level 1 (basic color support) will downsample an RGB value of #FF0000 (red) to 31 (ANSI escape for red).\n\nExamples:\n\n- `chalk.hex('#DEADED').underline('Hello, world!')`\n- `chalk.keyword('orange')('Some orange text')`\n- `chalk.rgb(15, 100, 204).inverse('Hello!')`\n\nBackground versions of these models are prefixed with `bg` and the first level of the module capitalized (e.g. `keyword` for foreground colors and `bgKeyword` for background colors).\n\n- `chalk.bgHex('#DEADED').underline('Hello, world!')`\n- `chalk.bgKeyword('orange')('Some orange text')`\n- `chalk.bgRgb(15, 100, 204).inverse('Hello!')`\n\nThe following color models can be used:\n\n- [`rgb`](https://en.wikipedia.org/wiki/RGB_color_model) - Example: `chalk.rgb(255, 136, 0).bold('Orange!')`\n- [`hex`](https://en.wikipedia.org/wiki/Web_colors#Hex_triplet) - Example: `chalk.hex('#FF8800').bold('Orange!')`\n- [`keyword`](https://www.w3.org/wiki/CSS/Properties/color/keywords) (CSS keywords) - Example: `chalk.keyword('orange').bold('Orange!')`\n- [`hsl`](https://en.wikipedia.org/wiki/HSL_and_HSV) - Example: `chalk.hsl(32, 100, 50).bold('Orange!')`\n- [`hsv`](https://en.wikipedia.org/wiki/HSL_and_HSV) - Example: `chalk.hsv(32, 100, 100).bold('Orange!')`\n- [`hwb`](https://en.wikipedia.org/wiki/HWB_color_model)  - Example: `chalk.hwb(32, 0, 50).bold('Orange!')`\n- `ansi16`\n- `ansi256`\n\n\n## Windows\n\nIf you're on Windows, do yourself a favor and use [`cmder`](http://cmder.net/) instead of `cmd.exe`.\n\n\n## Origin story\n\n[colors.js](https://github.com/Marak/colors.js) used to be the most popular string styling module, but it has serious deficiencies like extending `String.prototype` which causes all kinds of [problems](https://github.com/yeoman/yo/issues/68) and the package is unmaintained. Although there are other packages, they either do too much or not enough. Chalk is a clean and focused alternative.\n\n\n## Related\n\n- [chalk-cli](https://github.com/chalk/chalk-cli) - CLI for this module\n- [ansi-styles](https://github.com/chalk/ansi-styles) - ANSI escape codes for styling strings in the terminal\n- [supports-color](https://github.com/chalk/supports-color) - Detect whether a terminal supports color\n- [strip-ansi](https://github.com/chalk/strip-ansi) - Strip ANSI escape codes\n- [strip-ansi-stream](https://github.com/chalk/strip-ansi-stream) - Strip ANSI escape codes from a stream\n- [has-ansi](https://github.com/chalk/has-ansi) - Check if a string has ANSI escape codes\n- [ansi-regex](https://github.com/chalk/ansi-regex) - Regular expression for matching ANSI escape codes\n- [wrap-ansi](https://github.com/chalk/wrap-ansi) - Wordwrap a string with ANSI escape codes\n- [slice-ansi](https://github.com/chalk/slice-ansi) - Slice a string with ANSI escape codes\n- [color-convert](https://github.com/qix-/color-convert) - Converts colors between different models\n- [chalk-animation](https://github.com/bokub/chalk-animation) - Animate strings in the terminal\n- [gradient-string](https://github.com/bokub/gradient-string) - Apply color gradients to strings\n- [chalk-pipe](https://github.com/LitoMore/chalk-pipe) - Create chalk style schemes with simpler style strings\n- [terminal-link](https://github.com/sindresorhus/terminal-link) - Create clickable links in the terminal\n\n\n## Maintainers\n\n- [Sindre Sorhus](https://github.com/sindresorhus)\n- [Josh Junon](https://github.com/qix-)\n\n\n## License\n\nMIT\n", "licenseText": "MIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/chalk/-/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424", "type": "tarball", "reference": "https://registry.yarnpkg.com/chalk/-/chalk-2.4.2.tgz", "hash": "cd42541677a54333cf541a49108c1432b44c9424", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "registry": "npm", "packageName": "chalk", "cacheIntegrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ== sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="}, "registry": "npm", "hash": "cd42541677a54333cf541a49108c1432b44c9424"}