{"name": "hash-base", "version": "3.1.0", "description": "abstract base class for hash-streams", "keywords": ["hash", "stream"], "homepage": "https://github.com/crypto-browserify/hash-base", "bugs": {"url": "https://github.com/crypto-browserify/hash-base/issues"}, "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/fanatid)", "files": ["index.js"], "main": "index.js", "repository": {"type": "git", "url": "https://github.com/crypto-browserify/hash-base.git"}, "scripts": {"coverage": "nyc node test/*.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "node test/*.js"}, "dependencies": {"inherits": "^2.0.4", "readable-stream": "^3.6.0", "safe-buffer": "^5.2.0"}, "devDependencies": {"nyc": "^15.0.1", "standard": "^14.3.3", "tape": "^5.0.0"}, "engines": {"node": ">=4"}}