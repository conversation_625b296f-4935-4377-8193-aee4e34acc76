{"manifest": {"name": "parseurl", "description": "parse a url with memoization", "version": "1.3.3", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "repository": {"type": "git", "url": "https://github.com/pillarjs/parseurl.git"}, "license": "MIT", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.17.1", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "fast-url-parser": "1.1.3", "istanbul": "0.4.5", "mocha": "6.1.3"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint .", "test": "mocha --check-leaks --bail --reporter spec test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec test/"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-parseurl-1.3.3-9da19e7bee8d12dff0513ed5b76957793bc2e8d4-integrity\\node_modules\\parseurl\\package.json", "readmeFilename": "README.md", "readme": "# parseurl\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-image]][node-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nParse a URL with memoization.\n\n## Install\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install parseurl\n```\n\n## API\n\n```js\nvar parseurl = require('parseurl')\n```\n\n### parseurl(req)\n\nParse the URL of the given request object (looks at the `req.url` property)\nand return the result. The result is the same as `url.parse` in Node.js core.\nCalling this function multiple times on the same `req` where `req.url` does\nnot change will return a cached parsed object, rather than parsing again.\n\n### parseurl.original(req)\n\nParse the original URL of the given request object and return the result.\nThis works by trying to parse `req.originalUrl` if it is a string, otherwise\nparses `req.url`. The result is the same as `url.parse` in Node.js core.\nCalling this function multiple times on the same `req` where `req.originalUrl`\ndoes not change will return a cached parsed object, rather than parsing again.\n\n## Benchmark\n\n```bash\n$ npm run-script bench\n\n> parseurl@1.3.3 bench nodejs-parseurl\n> node benchmark/index.js\n\n  http_parser@2.8.0\n  node@10.6.0\n  v8@6.7.288.46-node.13\n  uv@1.21.0\n  zlib@1.2.11\n  ares@1.14.0\n  modules@64\n  nghttp2@1.32.0\n  napi@3\n  openssl@1.1.0h\n  icu@61.1\n  unicode@10.0\n  cldr@33.0\n  tz@2018c\n\n> node benchmark/fullurl.js\n\n  Parsing URL \"http://localhost:8888/foo/bar?user=tj&pet=fluffy\"\n\n  4 tests completed.\n\n  fasturl            x 2,207,842 ops/sec ±3.76% (184 runs sampled)\n  nativeurl - legacy x   507,180 ops/sec ±0.82% (191 runs sampled)\n  nativeurl - whatwg x   290,044 ops/sec ±1.96% (189 runs sampled)\n  parseurl           x   488,907 ops/sec ±2.13% (192 runs sampled)\n\n> node benchmark/pathquery.js\n\n  Parsing URL \"/foo/bar?user=tj&pet=fluffy\"\n\n  4 tests completed.\n\n  fasturl            x 3,812,564 ops/sec ±3.15% (188 runs sampled)\n  nativeurl - legacy x 2,651,631 ops/sec ±1.68% (189 runs sampled)\n  nativeurl - whatwg x   161,837 ops/sec ±2.26% (189 runs sampled)\n  parseurl           x 4,166,338 ops/sec ±2.23% (184 runs sampled)\n\n> node benchmark/samerequest.js\n\n  Parsing URL \"/foo/bar?user=tj&pet=fluffy\" on same request object\n\n  4 tests completed.\n\n  fasturl            x  3,821,651 ops/sec ±2.42% (185 runs sampled)\n  nativeurl - legacy x  2,651,162 ops/sec ±1.90% (187 runs sampled)\n  nativeurl - whatwg x    175,166 ops/sec ±1.44% (188 runs sampled)\n  parseurl           x 14,912,606 ops/sec ±3.59% (183 runs sampled)\n\n> node benchmark/simplepath.js\n\n  Parsing URL \"/foo/bar\"\n\n  4 tests completed.\n\n  fasturl            x 12,421,765 ops/sec ±2.04% (191 runs sampled)\n  nativeurl - legacy x  7,546,036 ops/sec ±1.41% (188 runs sampled)\n  nativeurl - whatwg x    198,843 ops/sec ±1.83% (189 runs sampled)\n  parseurl           x 24,244,006 ops/sec ±0.51% (194 runs sampled)\n\n> node benchmark/slash.js\n\n  Parsing URL \"/\"\n\n  4 tests completed.\n\n  fasturl            x 17,159,456 ops/sec ±3.25% (188 runs sampled)\n  nativeurl - legacy x 11,635,097 ops/sec ±3.79% (184 runs sampled)\n  nativeurl - whatwg x    240,693 ops/sec ±0.83% (189 runs sampled)\n  parseurl           x 42,279,067 ops/sec ±0.55% (190 runs sampled)\n```\n\n## License\n\n  [MIT](LICENSE)\n\n[coveralls-image]: https://badgen.net/coveralls/c/github/pillarjs/parseurl/master\n[coveralls-url]: https://coveralls.io/r/pillarjs/parseurl?branch=master\n[node-image]: https://badgen.net/npm/node/parseurl\n[node-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/parseurl\n[npm-url]: https://npmjs.org/package/parseurl\n[npm-version-image]: https://badgen.net/npm/v/parseurl\n[travis-image]: https://badgen.net/travis/pillarjs/parseurl/master\n[travis-url]: https://travis-ci.org/pillarjs/parseurl\n", "licenseText": "\n(The MIT License)\n\nCopyright (c) 2014 <PERSON> <<EMAIL>>\nCopyright (c) 2014-2017 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR CO<PERSON>YRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/parseurl/-/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4", "type": "tarball", "reference": "https://registry.yarnpkg.com/parseurl/-/parseurl-1.3.3.tgz", "hash": "9da19e7bee8d12dff0513ed5b76957793bc2e8d4", "integrity": "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==", "registry": "npm", "packageName": "parseurl", "cacheIntegrity": "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ== sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="}, "registry": "npm", "hash": "9da19e7bee8d12dff0513ed5b76957793bc2e8d4"}