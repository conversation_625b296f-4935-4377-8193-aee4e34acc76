{"name": "object.getownpropertydescriptors", "version": "2.1.1", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ES2017 spec-compliant shim for `Object.getOwnPropertyDescriptors` that works in ES5.", "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "posttest": "npx aud --production", "tests-only": "nyc tape 'test/**/*.js'", "lint": "eslint .", "postlint": "es-shim-api --bound"}, "repository": {"type": "git", "url": "git://github.com/es-shims/object.getownpropertydescriptors.git"}, "keywords": ["Object.getOwnPropertyDescriptors", "descriptor", "property descriptor", "ES8", "ES2017", "shim", "polyfill", "getOwnPropertyDescriptor", "es-shim API"], "dependencies": {"call-bind": "^1.0.0", "define-properties": "^1.1.3", "es-abstract": "^1.18.0-next.1"}, "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^17.2.0", "aud": "^1.1.2", "eslint": "^7.8.1", "functions-have-names": "^1.2.1", "has-strict-mode": "^1.0.0", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.0.1"}, "testling": {"files": ["test/index.js", "test/shimmed.js"], "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/5.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/12.0..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.8"}}