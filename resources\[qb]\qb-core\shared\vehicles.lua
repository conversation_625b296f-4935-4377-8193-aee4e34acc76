QBShared = QBShared or {}
QBShared.Vehicles = QBShared.Vehicles or {}

local Vehicles = {
    --- Compacts (0)
    {
        model = 'asbo',        -- This has to match the spawn code of the vehicle
        name = 'Asbo',         -- This is the display of the vehicle
        brand = 'Maxwell',     -- This is the vehicle's brand
        price = 4000,          -- The price that the vehicle sells for
        category = 'compacts', -- Catgegory of the vehilce, stick with GetVehicleClass() options https://docs.fivem.net/natives/?_0x29439776AAA00A62
        type = 'automobile',   -- Vehicle type, refer here https://docs.fivem.net/natives/?_0x6AE51D4B & here https://docs.fivem.net/natives/?_0xA273060E
        shop = 'pdm',          -- Can be a single shop or multiple shops. For multiple shops for example {'shopname1','shopname2','shopname3'}
    },
    { model = 'blista',          name = 'Blista',                        brand = 'Dinka',           price = 13000,   category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'brioso',          name = 'Brioso R/A',                    brand = 'Grotti',          price = 20000,   category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'club',            name = 'Club',                          brand = 'BF',              price = 8000,    category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'dilettante',      name = 'Dilettante',                    brand = 'Karin',           price = 9000,    category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'dilettante2',     name = 'Dilettante Patrol',             brand = 'Karin',           price = 12000,   category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'kanjo',           name = 'Blista Kanjo',                  brand = 'Dinka',           price = 12000,   category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'issi2',           name = 'Issi',                          brand = 'Weeny',           price = 7000,    category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'issi3',           name = 'Issi Classic',                  brand = 'Weeny',           price = 5000,    category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'issi4',           name = 'Issi Arena',                    brand = 'Weeny',           price = 80000,   category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'issi5',           name = 'Issi Future Shock',             brand = 'Weeny',           price = 80000,   category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'issi6',           name = 'Issi Nightmare',                brand = 'Weeny',           price = 80000,   category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'panto',           name = 'Panto',                         brand = 'Benefactor',      price = 3200,    category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'prairie',         name = 'Prairie',                       brand = 'Bollokan',        price = 30000,   category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'rhapsody',        name = 'Rhapsody',                      brand = 'Declasse',        price = 10000,   category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'brioso2',         name = 'Brioso 300',                    brand = 'Grotti',          price = 12000,   category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'weevil',          name = 'Weevil',                        brand = 'BF',              price = 9000,    category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'issi7',           name = 'Issi Sport',                    brand = 'Weeny',           price = 100000,  category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'blista2',         name = 'Blista Compact',                brand = 'Dinka',           price = 18950,   category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'blista3',         name = 'Blista Go Go Monkey',           brand = 'Dinka',           price = 15000,   category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'brioso3',         name = 'Brioso 300 Widebody',           brand = 'Grotti',          price = 125000,  category = 'compacts',       type = 'automobile', shop = 'pdm' },
    { model = 'boor',            name = 'Boor',                          brand = 'Karin',           price = 23000,   category = 'compacts',       type = 'automobile', shop = 'pdm' },
    --- Sedans (1)
    { model = 'asea',            name = 'Asea',                          brand = 'Declasse',        price = 2500,    category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'asterope',        name = 'Asterope',                      brand = 'Karin',           price = 11000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'cog55',           name = 'Cognoscenti 55',                brand = 'Enus',            price = 22000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'cognoscenti',     name = 'Cognoscenti',                   brand = 'Enus',            price = 22500,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'emperor',         name = 'Emperor',                       brand = 'Albany',          price = 4250,    category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'fugitive',        name = 'Fugitive',                      brand = 'Cheval',          price = 20000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'glendale',        name = 'Glendale',                      brand = 'Benefactor',      price = 3400,    category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'glendale2',       name = 'Glendale Custom',               brand = 'Benefactor',      price = 12000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'ingot',           name = 'Ingot',                         brand = 'Vulcar',          price = 4999,    category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'intruder',        name = 'Intruder',                      brand = 'Karin',           price = 11250,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'premier',         name = 'Premier',                       brand = 'Declasse',        price = 12000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'primo',           name = 'Primo',                         brand = 'Albany',          price = 5000,    category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'primo2',          name = 'Primo Custom',                  brand = 'Albany',          price = 14500,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'regina',          name = 'Regina',                        brand = 'Dundreary',       price = 7000,    category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'stafford',        name = 'Stafford',                      brand = 'Enus',            price = 30000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'stanier',         name = 'Stanier',                       brand = 'Vapid',           price = 19000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'stratum',         name = 'Stratum',                       brand = 'Zirconium',       price = 15000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'stretch',         name = 'Stretch',                       brand = 'Dundreary',       price = 19000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'superd',          name = 'Super Diamond',                 brand = 'Enus',            price = 17000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'surge',           name = 'Surge',                         brand = 'Cheval',          price = 20000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'tailgater',       name = 'Tailgater',                     brand = 'Obey',            price = 22000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'warrener',        name = 'Warrener',                      brand = 'Vulcar',          price = 4000,    category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'washington',      name = 'Washington',                    brand = 'Albany',          price = 7000,    category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'tailgater2',      name = 'Tailgater S',                   brand = 'Obey',            price = 51000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'cinquemila',      name = 'Lampadati',                     brand = 'Cinquemila',      price = 125000,  category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'iwagen',          name = 'Obey',                          brand = 'I-Wagen',         price = 225000,  category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'astron',          name = 'Astron',                        brand = 'Pfister',         price = 150000,  category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'baller7',         name = 'Baller ST',                     brand = 'Gallivanter',     price = 145000,  category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'comet7',          name = 'Comet',                         brand = 'S2 Cabrio',       price = 25000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'deity',           name = 'Deity',                         brand = 'Enus',            price = 505000,  category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'jubilee',         name = 'Jubilee',                       brand = 'Enus',            price = 485000,  category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'oracle',          name = 'Oracle',                        brand = 'Übermacht',       price = 22000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'schafter2',       name = 'Schafter',                      brand = 'Benefactor',      price = 16000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'warrener2',       name = 'Warrener HKR',                  brand = 'Vulcar',          price = 30000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'rhinehart',       name = 'Rhinehart',                     brand = 'Übermacht',       price = 105000,  category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'eudora',          name = 'Eudora',                        brand = 'Willard',         price = 17000,   category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'asterope2',       name = 'Asterope GZ',                   brand = 'Karin',           price = 459000,  category = 'sedans',         type = 'automobile', shop = 'pdm' },
    { model = 'impaler5',        name = 'Impaler SZ',                    brand = 'Declasse',        price = 768000,  category = 'sedans',         type = 'automobile', shop = 'pdm' },
    --- SUV (2)
    { model = 'baller',          name = 'Baller',                        brand = 'Gallivanter',     price = 22000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'baller2',         name = 'Baller II',                     brand = 'Gallivanter',     price = 15000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'baller3',         name = 'Baller LE',                     brand = 'Gallivanter',     price = 15000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'baller4',         name = 'Baller LE LWB',                 brand = 'Gallivanter',     price = 29000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'baller5',         name = 'Baller LE (Armored)',           brand = 'Gallivanter',     price = 78000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'baller6',         name = 'Baller LE LWB (Armored)',       brand = 'Gallivanter',     price = 82000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'bjxl',            name = 'BeeJay XL',                     brand = 'Karin',           price = 19000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'cavalcade',       name = 'Cavalcade',                     brand = 'Albany',          price = 14000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'cavalcade2',      name = 'Cavalcade II',                  brand = 'Albany',          price = 16500,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'contender',       name = 'Contender',                     brand = 'Vapid',           price = 35000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'dubsta',          name = 'Dubsta',                        brand = 'Benefactor',      price = 19000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'dubsta2',         name = 'Dubsta Luxury',                 brand = 'Benefactor',      price = 19500,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'fq2',             name = 'FQ2',                           brand = 'Fathom',          price = 18500,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'granger',         name = 'Granger',                       brand = 'Declasse',        price = 22000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'gresley',         name = 'Gresley',                       brand = 'Bravado',         price = 25000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'habanero',        name = 'Habanero',                      brand = 'Emperor',         price = 20000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'huntley',         name = 'Huntley S',                     brand = 'Enus',            price = 24500,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'landstalker',     name = 'Landstalker',                   brand = 'Dundreary',       price = 12000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'landstalker2',    name = 'Landstalker XL',                brand = 'Dundreary',       price = 26000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'novak',           name = 'Novak',                         brand = 'Lampadati',       price = 70000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'patriot',         name = 'Patriot',                       brand = 'Mammoth',         price = 21000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'patriot2',        name = 'Patriot Stretch',               brand = 'Mammoth',         price = 21000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'radi',            name = 'Radius',                        brand = 'Vapid',           price = 18000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'rebla',           name = 'Rebla GTS',                     brand = 'Übermacht',       price = 21000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'rocoto',          name = 'Rocoto',                        brand = 'Obey',            price = 13000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'seminole',        name = 'Seminole',                      brand = 'Canis',           price = 20000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'seminole2',       name = 'Seminole Frontier',             brand = 'Canis',           price = 13000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'serrano',         name = 'Serrano',                       brand = 'Benefactor',      price = 48000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'toros',           name = 'Toros',                         brand = 'Pegassi',         price = 65000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'xls',             name = 'XLS',                           brand = 'Benefactor',      price = 17000,   category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'granger2',        name = 'Granger 3600LX',                brand = 'Declasse',        price = 221000,  category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'patriot3',        name = 'Patriot Military',              brand = 'Mil-Spec',        price = 270000,  category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'aleutian',        name = 'Aleutian',                      brand = 'Vapid',           price = 183500,  category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'baller8',         name = 'Baller ST-D',                   brand = 'Gallivanter',     price = 171500,  category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'cavalcade3',      name = 'Cavalcade XL',                  brand = 'Albany',          price = 166500,  category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'dorado',          name = 'Dorado',                        brand = 'Bravado',         price = 137500,  category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'vivanite',        name = 'Vivanite',                      brand = 'Karin',           price = 160500,  category = 'suvs',           type = 'automobile', shop = 'pdm' },
    { model = 'castigator',      name = 'Castigator',                    brand = 'Canis',           price = 160500,  category = 'suvs',           type = 'automobile', shop = 'pdm' },
    --- Coupes (3)
    { model = 'cogcabrio',       name = 'Cognoscenti Cabrio',            brand = 'Enus',            price = 30000,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'exemplar',        name = 'Exemplar',                      brand = 'Dewbauchee',      price = 40000,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'f620',            name = 'F620',                          brand = 'Ocelot',          price = 32500,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'felon',           name = 'Felon',                         brand = 'Lampadati',       price = 31000,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'felon2',          name = 'Felon GT',                      brand = 'Lampadati',       price = 37000,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'jackal',          name = 'Jackal',                        brand = 'Ocelot',          price = 19000,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'oracle2',         name = 'Oracle XS',                     brand = 'Übermacht',       price = 28000,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'sentinel',        name = 'Sentinel',                      brand = 'Übermacht',       price = 30000,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'sentinel2',       name = 'Sentinel XS',                   brand = 'Übermacht',       price = 33000,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'windsor',         name = 'Windsor',                       brand = 'Enus',            price = 27000,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'windsor2',        name = 'Windsor Drop',                  brand = 'Enus',            price = 34000,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'zion',            name = 'Zion',                          brand = 'Übermacht',       price = 22000,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'zion2',           name = 'Zion Cabrio',                   brand = 'Übermacht',       price = 28000,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'previon',         name = 'Previon',                       brand = 'Karin',           price = 149000,  category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'champion',        name = 'Champion',                      brand = 'Dewbauchee',      price = 205000,  category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'futo',            name = 'Futo',                          brand = 'Karin',           price = 17500,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'sentinel3',       name = 'Sentinel Classic',              brand = 'Übermacht',       price = 70000,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'kanjosj',         name = 'Kanjo SJ',                      brand = 'Dinka',           price = 143000,  category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'postlude',        name = 'Postlude',                      brand = 'Dinka',           price = 90000,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'tahoma',          name = 'Tahoma Coupe',                  brand = 'Declasse',        price = 12000,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'broadway',        name = 'Broadway',                      brand = 'Classique',       price = 20000,   category = 'coupes',         type = 'automobile', shop = 'pdm' },
    { model = 'fr36',            name = 'FR36',                          brand = 'Fathom',          price = 161000,  category = 'coupes',         type = 'automobile', shop = 'pdm' },
    --- Muscle (4)
    { model = 'blade',           name = 'Blade',                         brand = 'Vapid',           price = 23500,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'buccaneer',       name = 'Buccaneer',                     brand = 'Albany',          price = 22500,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'buccaneer2',      name = 'Buccaneer Rider',               brand = 'Albany',          price = 24500,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'chino',           name = 'Chino',                         brand = 'Vapid',           price = 5000,    category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'chino2',          name = 'Chino Luxe',                    brand = 'Vapid',           price = 8000,    category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'clique',          name = 'Clique',                        brand = 'Vapid',           price = 20000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'coquette3',       name = 'Coquette BlackFin',             brand = 'Invetero',        price = 180000,  category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'deviant',         name = 'Deviant',                       brand = 'Schyster',        price = 70000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'dominator',       name = 'Dominator',                     brand = 'Vapid',           price = 62500,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'dominator2',      name = 'Pißwasser Dominator',           brand = 'Vapid',           price = 50000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'dominator3',      name = 'Dominator GTX',                 brand = 'Vapid',           price = 70000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'dominator4',      name = 'Dominator Arena',               brand = 'Vapid',           price = 200000,  category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'dominator7',      name = 'Dominator ASP',                 brand = 'Vapid',           price = 110000,  category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'dominator8',      name = 'Dominator GTT',                 brand = 'Vapid',           price = 80000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'dukes',           name = 'Dukes',                         brand = 'Imponte',         price = 23500,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'dukes2',          name = 'Duke O\'Death',                 brand = 'Imponte',         price = 60000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'dukes3',          name = 'Beater Dukes',                  brand = 'Imponte',         price = 45000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'faction',         name = 'Faction',                       brand = 'Willard',         price = 17000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'faction2',        name = 'Faction Rider',                 brand = 'Willard',         price = 19000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'faction3',        name = 'Faction Custom Donk',           brand = 'Willard',         price = 35000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'ellie',           name = 'Ellie',                         brand = 'Vapid',           price = 42250,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'gauntlet',        name = 'Gauntlet',                      brand = 'Bravado',         price = 28500,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'gauntlet2',       name = 'Redwood Gauntlet',              brand = 'Bravado',         price = 70000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'gauntlet3',       name = 'Classic Gauntlet',              brand = 'Bravado',         price = 75000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'gauntlet4',       name = 'Gauntlet Hellfire',             brand = 'Bravado',         price = 80000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'gauntlet5',       name = 'Gauntlet Classic Custom',       brand = 'Bravado',         price = 120000,  category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'hermes',          name = 'Hermes',                        brand = 'Albany',          price = 535000,  category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'hotknife',        name = 'Hotknife',                      brand = 'Vapid',           price = 90000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'hustler',         name = 'Hustler',                       brand = 'Vapid',           price = 95000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'impaler',         name = 'Impaler',                       brand = 'Vapid',           price = 95000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'impaler2',        name = 'Impaler Arena',                 brand = 'Vapid',           price = 95000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'impaler3',        name = 'Impaler Future Shock',          brand = 'Vapid',           price = 95000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'impaler4',        name = 'Impaler Nightmare',             brand = 'Vapid',           price = 95000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'imperator',       name = 'Imperator Arena',               brand = 'Vapid',           price = 95000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'imperator2',      name = 'imperator Future Shock',        brand = 'Vapid',           price = 95000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'imperator3',      name = 'Imperator Nightmare',           brand = 'Vapid',           price = 95000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'lurcher',         name = 'Lurcher',                       brand = 'Bravado',         price = 21000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'nightshade',      name = 'Nightshade',                    brand = 'Imponte',         price = 70000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'phoenix',         name = 'Phoenix',                       brand = 'Imponte',         price = 65000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'picador',         name = 'Picador',                       brand = 'Cheval',          price = 20000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'ratloader2',      name = 'Ratloader',                     brand = 'Ratloader2',      price = 20000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'ruiner',          name = 'Ruiner',                        brand = 'Imponte',         price = 29000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'ruiner2',         name = 'Ruiner 2000',                   brand = 'Imponte',         price = 50000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'sabregt',         name = 'Sabre GT Turbo',                brand = 'Declasse',        price = 23000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'sabregt2',        name = 'Sabre GT Turbo Custom',         brand = 'Declasse',        price = 26500,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'slamvan',         name = 'Slam Van',                      brand = 'Vapid',           price = 30000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'slamvan2',        name = 'Lost Slam Van',                 brand = 'Vapid',           price = 90000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'slamvan3',        name = 'Slam Van Custom',               brand = 'Vapid',           price = 17000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'stalion',         name = 'Stallion',                      brand = 'Declasse',        price = 33000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'stalion2',        name = 'Stallion Burgershot',           brand = 'Declasse',        price = 40000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'tampa',           name = 'Tampa',                         brand = 'Declasse',        price = 24500,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'tulip',           name = 'Tulip',                         brand = 'Declasse',        price = 80000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'vamos',           name = 'Vamos',                         brand = 'Declasse',        price = 30000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'vigero',          name = 'Vigero',                        brand = 'Declasse',        price = 39500,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'virgo',           name = 'Virgo',                         brand = 'Albany',          price = 22000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'virgo2',          name = 'Virgo Custom Classic',          brand = 'Dundreary',       price = 21000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'virgo3',          name = 'Virgo Classic',                 brand = 'Dundreary',       price = 21000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'voodoo',          name = 'Voodoo',                        brand = 'Declasse',        price = 13000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'yosemite',        name = 'Yosemite',                      brand = 'Declasse',        price = 19500,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'yosemite2',       name = 'Yosemite Drift',                brand = 'Declasse',        price = 55000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'buffalo4',        name = 'Buffalo STX',                   brand = 'Bravado',         price = 345000,  category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'manana',          name = 'Manana',                        brand = 'Albany',          price = 12800,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'manana2',         name = 'Manana Custom',                 brand = 'Albany',          price = 24000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'tampa2',          name = 'Drift Tampa',                   brand = 'Declasse',        price = 80000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'ruiner4',         name = 'Ruiner ZZ-8',                   brand = 'Imponte',         price = 85000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'vigero2',         name = 'Vigero ZX',                     brand = 'Declasse',        price = 105000,  category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'weevil2',         name = 'Weevil Custom',                 brand = 'BF',              price = 95000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'buffalo5',        name = 'Buffalo EVX',                   brand = 'Bravado',         price = 214000,  category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'tulip2',          name = 'Tulip M-100',                   brand = 'Declasse',        price = 80000,   category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'clique2',         name = 'Clique Wagon',                  brand = 'Vapid',           price = 102500,  category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'brigham',         name = 'Brigham',                       brand = 'Albany',          price = 149900,  category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'greenwood',       name = 'Greenwood',                     brand = 'Bravado',         price = 105000,  category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'dominator9',      name = 'Dominator GT',                  brand = 'Vapid',           price = 219500,  category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'impaler6',        name = 'Impaler LX',                    brand = 'Declasse',        price = 146500,  category = 'muscle',         type = 'automobile', shop = 'pdm' },
    { model = 'vigero3',         name = 'Vigero ZX Convertible',         brand = 'Declasse',        price = 229500,  category = 'muscle',         type = 'automobile', shop = 'pdm' },
    --- Sports Classic (5)
    { model = 'ardent',          name = 'Ardent',                        brand = 'Ocelot',          price = 30000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'btype',           name = 'Roosevelt',                     brand = 'Albany',          price = 75000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'btype2',          name = 'Franken Stange',                brand = 'Albany',          price = 87000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'btype3',          name = 'Roosevelt Valor',               brand = 'Albany',          price = 63000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'casco',           name = 'Casco',                         brand = 'Lampadati',       price = 100000,  category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'deluxo',          name = 'Deluxo',                        brand = 'Imponte',         price = 55000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'dynasty',         name = 'Dynasty',                       brand = 'Weeny',           price = 25000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'fagaloa',         name = 'Fagaloa',                       brand = 'Vulcar',          price = 13000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'feltzer3',        name = 'Stirling GT',                   brand = 'Benefactor',      price = 115000,  category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'gt500',           name = 'GT500',                         brand = 'Grotti',          price = 130000,  category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'infernus2',       name = 'Infernus Classic',              brand = 'Pegassi',         price = 245000,  category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'jb700',           name = 'JB 700',                        brand = 'Dewbauchee',      price = 240000,  category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'jb7002',          name = 'JB 700W',                       brand = 'Dewbauchee',      price = 40000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'mamba',           name = 'Mamba',                         brand = 'Declasse',        price = 140000,  category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'michelli',        name = 'Michelli GT',                   brand = 'Lampadati',       price = 30000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'monroe',          name = 'Monroe',                        brand = 'Pegassi',         price = 115000,  category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'nebula',          name = 'Nebula',                        brand = 'Vulcar',          price = 22000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'peyote',          name = 'Peyote',                        brand = 'Vapid',           price = 23500,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'peyote3',         name = 'Peyote Custom',                 brand = 'Vapid',           price = 48000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'pigalle',         name = 'Pigalle',                       brand = 'Lampadati',       price = 92000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'rapidgt3',        name = 'Rapid GT Classic',              brand = 'Dewbauchee',      price = 90000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'retinue',         name = 'Retinue',                       brand = 'Vapid',           price = 32000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'retinue2',        name = 'Retinue MKII',                  brand = 'Vapid',           price = 38000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'savestra',        name = 'Savestra',                      brand = 'Annis',           price = 67000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'stinger',         name = 'Stinger',                       brand = 'Grotti',          price = 39500,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'stingergt',       name = 'Stinger GT',                    brand = 'Grotti',          price = 70000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'stromberg',       name = 'Stromberg',                     brand = 'Ocelot',          price = 80000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'swinger',         name = 'Swinger',                       brand = 'Ocelot',          price = 221000,  category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'torero',          name = 'Torero',                        brand = 'Pegassi',         price = 84000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'tornado',         name = 'Tornado',                       brand = 'Declasse',        price = 21000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'tornado2',        name = 'Tornado Convertible',           brand = 'Declasse',        price = 22000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'tornado5',        name = 'Tornado Custom',                brand = 'Declasse',        price = 22000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'turismo2',        name = 'Turismo Classic',               brand = 'Grotti',          price = 170000,  category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'viseris',         name = 'Viseris',                       brand = 'Lampadati',       price = 210000,  category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'z190',            name = '190Z',                          brand = 'Karin',           price = 78000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'ztype',           name = 'Z-Type',                        brand = 'Truffade',        price = 270000,  category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'zion3',           name = 'Zion Classic',                  brand = 'Übermacht',       price = 45000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'cheburek',        name = 'Cheburek',                      brand = 'Rune',            price = 7000,    category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'toreador',        name = 'Toreador',                      brand = 'Pegassi',         price = 50000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'peyote2',         name = 'Peyote Gasser',                 brand = 'Vapid',           price = 40000,   category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'coquette2',       name = 'Coquette Classic',              brand = 'Invetero',        price = 165000,  category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'envisage',        name = 'Envisage',                      brand = 'Bollokan',        price = 190000,  category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    { model = 'driftnebula',     name = 'Nebula Turbo',                  brand = 'Vulcar',          price = 100000,  category = 'sportsclassics', type = 'automobile', shop = 'pdm' },
    --- Sports (6)
    { model = 'alpha',           name = 'Alpha',                         brand = 'Albany',          price = 53000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'banshee',         name = 'Banshee',                       brand = 'Bravado',         price = 56000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'bestiagts',       name = 'Bestia GTS',                    brand = 'Grotti',          price = 37000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'buffalo',         name = 'Buffalo',                       brand = 'Bravado',         price = 18750,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'buffalo2',        name = 'Buffalo S',                     brand = 'Bravado',         price = 24500,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'carbonizzare',    name = 'Carbonizzare',                  brand = 'Grotti',          price = 155000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'comet2',          name = 'Comet',                         brand = 'Pfister',         price = 130000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'comet3',          name = 'Comet Retro Custom',            brand = 'Pfister',         price = 175000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'comet4',          name = 'Comet Safari',                  brand = 'Pfister',         price = 110000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'comet5',          name = 'Comet SR',                      brand = 'Pfister',         price = 155000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'coquette',        name = 'Coquette',                      brand = 'Invetero',        price = 145000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'coquette4',       name = 'Coquette D10',                  brand = 'Invetero',        price = 220000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'drafter',         name = '8F Drafter',                    brand = 'Obey',            price = 80000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'elegy',           name = 'Elegy Retro Custom',            brand = 'Annis',           price = 145000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'elegy2',          name = 'Elegy RH8',                     brand = 'Annis',           price = 150000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'feltzer2',        name = 'Feltzer',                       brand = 'Benefactor',      price = 97000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'flashgt',         name = 'Flash GT',                      brand = 'Vapid',           price = 48000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'furoregt',        name = 'Furore GT',                     brand = 'Lampadati',       price = 78000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'gb200',           name = 'GB 200',                        brand = 'Vapid',           price = 140000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'komoda',          name = 'Komoda',                        brand = 'Lampadati',       price = 55000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'imorgon',         name = 'Imorgon',                       brand = 'Överflöd',        price = 120000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'italigto',        name = 'Itali GTO',                     brand = 'Progen',          price = 260000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'jugular',         name = 'Jugular',                       brand = 'Ocelot',          price = 80000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'jester',          name = 'Jester',                        brand = 'Dinka',           price = 132250,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'jester2',         name = 'Jester Racecar',                brand = 'Dinka',           price = 210000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'jester3',         name = 'Jester Classic',                brand = 'Dinka',           price = 85000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'khamelion',       name = 'Khamelion',                     brand = 'Hijak',           price = 90000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'kuruma',          name = 'Kuruma',                        brand = 'Karin',           price = 72000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'kuruma2',         name = 'kuruma2',                       brand = 'Karin2',          price = 72000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'locust',          name = 'Locust',                        brand = 'Ocelot',          price = 200000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'lynx',            name = 'Lynx',                          brand = 'Ocelot',          price = 150000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'massacro',        name = 'Massacro',                      brand = 'Dewbauchee',      price = 110000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'massacro2',       name = 'Massacro Racecar',              brand = 'Dewbauchee',      price = 80000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'neo',             name = 'Neo',                           brand = 'Vysser',          price = 230000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'neon',            name = 'Neon',                          brand = 'Pfister',         price = 220000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'ninef',           name = '9F',                            brand = 'Obey',            price = 95000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'ninef2',          name = '9F Cabrio',                     brand = 'Obey',            price = 105000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'omnis',           name = 'Omnis',                         brand = 'Wow',             price = 90000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'paragon',         name = 'Paragon',                       brand = 'Enus',            price = 60000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'pariah',          name = 'Pariah',                        brand = 'Ocelot',          price = 90000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'penumbra',        name = 'Penumbra',                      brand = 'Maibatsu',        price = 22000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'penumbra2',       name = 'Penumbra FF',                   brand = 'Maibatsu',        price = 30000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'rapidgt',         name = 'Rapid GT',                      brand = 'Dewbauchee',      price = 86000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'rapidgt2',        name = 'Rapid GT Convertible',          brand = 'Dewbauchee',      price = 92000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'raptor',          name = 'Raptor',                        brand = 'BF',              price = 90000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'revolter',        name = 'Revolter',                      brand = 'Übermacht',       price = 95000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'ruston',          name = 'Ruston',                        brand = 'Hijak',           price = 130000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'schafter3',       name = 'Schafter V12',                  brand = 'Benefactor',      price = 35000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'schafter4',       name = 'Schafter LWB',                  brand = 'Benefactor',      price = 21000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'schlagen',        name = 'Schlagen GT',                   brand = 'Benefactor',      price = 160000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'schwarzer',       name = 'Schwartzer',                    brand = 'Benefactor',      price = 47000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'seven70',         name = 'Seven-70',                      brand = 'Dewbauchee',      price = 140000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'specter',         name = 'Specter',                       brand = 'Dewbauchee',      price = 160000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'streiter',        name = 'Streiter',                      brand = 'Benefactor',      price = 40000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'sugoi',           name = 'Sugoi',                         brand = 'Dinka',           price = 85000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'sultan',          name = 'Sultan',                        brand = 'Karin',           price = 50000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'sultan2',         name = 'Sultan Custom',                 brand = 'Karin',           price = 55000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'surano',          name = 'Surano',                        brand = 'Benefactor',      price = 80000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'tropos',          name = 'Tropos Rallye',                 brand = 'Lampadati',       price = 65000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'verlierer2',      name = 'Verlierer',                     brand = 'Bravado',         price = 90500,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'vstr',            name = 'V-STR',                         brand = 'Albany',          price = 80000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'italirsx',        name = 'Itali RSX',                     brand = 'Progen',          price = 260000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'zr350',           name = 'ZR350',                         brand = 'Annis',           price = 38000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'calico',          name = 'Calico GTF',                    brand = 'Karin',           price = 39000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'futo2',           name = 'Futo GTX',                      brand = 'Karin',           price = 39000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'euros',           name = 'Euros',                         brand = 'Annis',           price = 80000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'jester4',         name = 'Jester RR',                     brand = 'Dinka',           price = 240000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'remus',           name = 'Remus',                         brand = 'Annis',           price = 48000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'comet6',          name = 'Comet S2',                      brand = 'Pfister',         price = 230000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'growler',         name = 'Growler',                       brand = 'Pfister',         price = 205000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'vectre',          name = 'Vectre',                        brand = 'Emperor',         price = 80000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'cypher',          name = 'Cypher',                        brand = 'Übermacht',       price = 155000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'sultan3',         name = 'Sultan Classic Custom',         brand = 'Karin',           price = 56000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'rt3000',          name = 'RT3000',                        brand = 'Dinka',           price = 65000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'sultanrs',        name = 'Sultan RS',                     brand = 'Karin',           price = 76500,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'visione',         name = 'Visione',                       brand = 'Grotti',          price = 750000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'cheetah2',        name = 'Cheetah Classic',               brand = 'Grotti',          price = 195000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'stingertt',       name = 'Itali GTO Stinger TT',          brand = 'Maibatsu',        price = 238000,  category = 'sports',         type = 'automobile', shop = 'pdm' },
    { model = 'omnisegt',        name = 'Omnis e-GT',                    brand = 'Obey',            price = 185000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'sentinel4',       name = 'Sentinel Classic Widebody',     brand = 'Übermacht',       price = 140000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'sm722',           name = 'SM722',                         brand = 'Benefactor',      price = 125000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'tenf',            name = '10F',                           brand = 'Obey',            price = 185000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'tenf2',           name = '10F Widebody',                  brand = 'Obey',            price = 215000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'everon2',         name = 'Everon Hotring',                brand = 'Karin',           price = 80000,   category = 'sports',         type = 'automobile', shop = 'pdm' },
    { model = 'issi8',           name = 'Issi Rally',                    brand = 'Weeny',           price = 10000,   category = 'sports',         type = 'automobile', shop = 'pdm' },
    { model = 'corsita',         name = 'Corsita',                       brand = 'Lampadati',       price = 90000,   category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'gauntlet6',       name = 'Hotring Hellfire',              brand = 'Bravado',         price = 181000,  category = 'sports',         type = 'automobile', shop = 'pdm' },
    { model = 'coureur',         name = 'La Coureuse',                   brand = 'Penaud',          price = 199000,  category = 'sports',         type = 'automobile', shop = 'pdm' },
    { model = 'r300',            name = '300R',                          brand = 'Annis',           price = 56000,   category = 'sports',         type = 'automobile', shop = 'pdm' },
    { model = 'panthere',        name = 'Panthere',                      brand = 'Toundra',         price = 55000,   category = 'sports',         type = 'automobile', shop = 'pdm' },
    { model = 'driftsentinel',   name = 'Drift Sentinel Classic',        brand = 'Ubermacht',       price = 150000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'paragon3',        name = 'Paragon S',                     brand = 'Enus',            price = 220000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'eurosX32',        name = 'Euros X32',                     brand = 'Annis',           price = 180000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'vorschlaghammer', name = 'Vorschlaghammer',               brand = 'Pfister',         price = 250000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'driftcypher',     name = 'Drift Cypher',                  brand = 'Ubermacht',       price = 160000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'coquette5',       name = 'Coquette D1',                   brand = 'Invetero',        price = 220000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'pipistrello',     name = 'Pipistrello',                   brand = 'Overflod',        price = 240000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'niobe',           name = 'Niobe',                         brand = 'Ubermacht',       price = 180000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    { model = 'driftvorschlag',  name = 'Vorschlaghammer',               brand = 'Pfister',         price = 250000,  category = 'sports',         type = 'automobile', shop = 'luxury' },
    --- Super (7)
    { model = 'adder',           name = 'Adder',                         brand = 'Truffade',        price = 280000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'autarch',         name = 'Autarch',                       brand = 'Överflöd',        price = 224000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'banshee2',        name = 'Banshee 900R',                  brand = 'Bravado',         price = 120000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'bullet',          name = 'Bullet',                        brand = 'Vapid',           price = 120000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'cheetah',         name = 'Cheetah',                       brand = 'Grotti',          price = 214000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'cyclone',         name = 'Cyclone',                       brand = 'Coil',            price = 300000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'entity2',         name = 'Entity XXR',                    brand = 'Överflöd',        price = 164000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'entityxf',        name = 'Entity XF',                     brand = 'Överflöd',        price = 180000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'emerus',          name = 'Emerus',                        brand = 'Progen',          price = 220000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'fmj',             name = 'FMJ',                           brand = 'Vapid',           price = 125000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'furia',           name = 'Furia',                         brand = 'Grotti',          price = 230000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'gp1',             name = 'GP1',                           brand = 'Progen',          price = 110000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'infernus',        name = 'Infernus',                      brand = 'Pegassi',         price = 235000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'italigtb',        name = 'Itali GTB',                     brand = 'Progen',          price = 170000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'italigtb2',       name = 'Itali GTB Custom',              brand = 'Progen',          price = 250000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'krieger',         name = 'Krieger',                       brand = 'Benefactor',      price = 222000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'le7b',            name = 'RE-7B',                         brand = 'Annis',           price = 260000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'nero',            name = 'Nero',                          brand = 'Truffade',        price = 200000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'nero2',           name = 'Nero Custom',                   brand = 'Truffade',        price = 260000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'osiris',          name = 'Osiris',                        brand = 'Pegassi',         price = 220000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'penetrator',      name = 'Penetrator',                    brand = 'Ocelot',          price = 130000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'pfister811',      name = '811',                           brand = 'Pfister',         price = 220000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'prototipo',       name = 'X80 Proto',                     brand = 'Grotti',          price = 235000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'reaper',          name = 'Reaper',                        brand = 'Pegassi',         price = 100000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 's80',             name = 'S80RR',                         brand = 'Annis',           price = 205000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'sc1',             name = 'SC1',                           brand = 'Übermacht',       price = 90000,   category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'sheava',          name = 'ETR1',                          brand = 'Emperor',         price = 220000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 't20',             name = 'T20',                           brand = 'Progen',          price = 1650000, category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'taipan',          name = 'Taipan',                        brand = 'Cheval',          price = 1850000, category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'tempesta',        name = 'Tempesta',                      brand = 'Pegassi',         price = 120000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'tezeract',        name = 'Tezeract',                      brand = 'Pegassi',         price = 220000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'thrax',           name = 'Thrax',                         brand = 'Truffade',        price = 180000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'tigon',           name = 'Tigon',                         brand = 'Lampadati',       price = 240000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'turismor',        name = 'Turismo R',                     brand = 'Grotti',          price = 140000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'tyrant',          name = 'Tyrant',                        brand = 'Överflöd',        price = 2100000, category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'tyrus',           name = 'Tyrus',                         brand = 'Progen',          price = 230000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'vacca',           name = 'Vacca',                         brand = 'Pegassi',         price = 105000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'vagner',          name = 'Vagner',                        brand = 'Dewbauchee',      price = 1660000, category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'voltic',          name = 'Voltic',                        brand = 'Coil',            price = 120000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'voltic2',         name = 'Rocket Voltic',                 brand = 'Coil',            price = 9830400, category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'xa21',            name = 'XA-21',                         brand = 'Ocelot',          price = 180000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'zentorno',        name = 'Zentorno',                      brand = 'Pegassi',         price = 340000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'zorrusso',        name = 'Zorrusso',                      brand = 'Pegassi',         price = 277000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'ignus',           name = 'Ignus',                         brand = 'Pegassi',         price = 1120000, category = 'super',          type = 'automobile', shop = 'pdm' },
    { model = 'zeno',            name = 'Zeno',                          brand = 'Överflöd',        price = 1350000, category = 'super',          type = 'automobile', shop = 'pdm' },
    { model = 'deveste',         name = 'Deveste',                       brand = 'Principe',        price = 234000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'lm87',            name = 'LM87',                          brand = 'Benefactor',      price = 155000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'torero2',         name = 'Torero XO',                     brand = 'Pegassi',         price = 245000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'entity3',         name = 'Entity MT',                     brand = 'Overflod',        price = 200000,  category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'virtue',          name = 'Virtue',                        brand = 'Ocelot',          price = 72000,   category = 'super',          type = 'automobile', shop = 'luxury' },
    { model = 'turismo3',        name = 'Turismo Omaggio',               brand = 'Grotti',          price = 284500,  category = 'super',          type = 'automobile', shop = 'luxury' },
    --- Motorcycles (8)
    { model = 'akuma',           name = 'Akuma',                         brand = 'Dinka',           price = 55000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'avarus',          name = 'Avarus',                        brand = 'LCC',             price = 20000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'bagger',          name = 'Bagger',                        brand = 'WMC',             price = 13500,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'bati',            name = 'Bati 801',                      brand = 'Pegassi',         price = 24000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'bati2',           name = 'Bati 801RR',                    brand = 'Pegassi',         price = 19000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'bf400',           name = 'BF400',                         brand = 'Nagasaki',        price = 22000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'carbonrs',        name = 'Carbon RS',                     brand = 'Nagasaki',        price = 22000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'chimera',         name = 'Chimera',                       brand = 'Nagasaki',        price = 21000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'cliffhanger',     name = 'Cliffhanger',                   brand = 'Western',         price = 28500,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'daemon',          name = 'Daemon',                        brand = 'WMC',             price = 14000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'daemon2',         name = 'Daemon Custom',                 brand = 'Western',         price = 23000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'defiler',         name = 'Defiler',                       brand = 'Shitzu',          price = 30000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'deathbike',       name = 'Deathbike Apocalypse',          brand = 'Deathbike',       price = 30000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'deathbike2',      name = 'Deathbike Future Shock',        brand = 'Deathbike',       price = 30000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'deathbike3',      name = 'Deathbike Nightmare',           brand = 'Deathbike',       price = 30000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'diablous',        name = 'Diablous',                      brand = 'Principe',        price = 30000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'diablous2',       name = 'Diablous Custom',               brand = 'Principe',        price = 38000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'double',          name = 'Double-T',                      brand = 'Dinka',           price = 28000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'enduro',          name = 'Enduro',                        brand = 'Dinka',           price = 5500,    category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'esskey',          name = 'Esskey',                        brand = 'Pegassi',         price = 12000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'faggio',          name = 'Faggio Sport',                  brand = 'Pegassi',         price = 2000,    category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'faggio2',         name = 'Faggio',                        brand = 'Pegassi',         price = 1900,    category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'faggio3',         name = 'Faggio Mod',                    brand = 'Pegassi',         price = 2500,    category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'fcr',             name = 'FCR 1000',                      brand = 'Pegassi',         price = 5000,    category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'fcr2',            name = 'FCR 1000 Custom',               brand = 'Pegassi',         price = 19000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'gargoyle',        name = 'Gargoyle',                      brand = 'Western',         price = 32000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'hakuchou',        name = 'Hakuchou',                      brand = 'Shitzu',          price = 17000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'hakuchou2',       name = 'Hakuchou Drag',                 brand = 'Shitzu',          price = 45000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'hexer',           name = 'Hexer',                         brand = 'LCC',             price = 16000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'innovation',      name = 'Innovation',                    brand = 'LLC',             price = 33500,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'lectro',          name = 'Lectro',                        brand = 'Principe',        price = 28000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'manchez',         name = 'Manchez',                       brand = 'Maibatsu',        price = 8300,    category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'nemesis',         name = 'Nemesis',                       brand = 'Principe',        price = 20000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'nightblade',      name = 'Nightblade',                    brand = 'WMC',             price = 23000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'oppressor',       name = 'Oppressor',                     brand = 'Pegassi',         price = 9999999, category = 'motorcycles',    type = 'bike',       shop = 'luxury' },
    { model = 'pcj',             name = 'PCJ-600',                       brand = 'Shitzu',          price = 15000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'ratbike',         name = 'Rat Bike',                      brand = 'Western',         price = 3000,    category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'ruffian',         name = 'Ruffian',                       brand = 'Pegassi',         price = 25000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'sanchez',         name = 'Sanchez Livery',                brand = 'Maibatsu',        price = 5300,    category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'sanchez2',        name = 'Sanchez',                       brand = 'Maibatsu',        price = 5300,    category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'sanctus',         name = 'Sanctus',                       brand = 'LCC',             price = 35000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'shotaro',         name = 'Shotaro',                       brand = 'Nagasaki',        price = 320000,  category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'sovereign',       name = 'Sovereign',                     brand = 'WMC',             price = 8000,    category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'stryder',         name = 'Stryder',                       brand = 'Nagasaki',        price = 50000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'thrust',          name = 'Thrust',                        brand = 'Dinka',           price = 22000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'vader',           name = 'Vader',                         brand = 'Shitzu',          price = 7200,    category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'vindicator',      name = 'Vindicator',                    brand = 'Dinka',           price = 19000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'vortex',          name = 'Vortex',                        brand = 'Pegassi',         price = 31000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'wolfsbane',       name = 'Wolfsbane',                     brand = 'Western',         price = 14000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'zombiea',         name = 'Zombie Bobber',                 brand = 'Western',         price = 28000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'zombieb',         name = 'Zombie Chopper',                brand = 'Western',         price = 27000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'manchez2',        name = 'Manchez Scout',                 brand = 'Maibatsu',        price = 14000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'shinobi',         name = 'Shinobi',                       brand = 'Nagasaki',        price = 25000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'reever',          name = 'Reever',                        brand = 'Western',         price = 25000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'manchez3',        name = 'Manchez Scout Classic',         brand = 'Maibatsu',        price = 15000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'powersurge',      name = 'Powersurge',                    brand = 'Western',         price = 7000,    category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    { model = 'pizzaboy',        name = 'Pizza Boy',                     brand = 'Pegassi',         price = 50000,   category = 'motorcycles',    type = 'bike',       shop = 'pdm' },
    --- Off-Road (9)
    { model = 'bfinjection',     name = 'Bf Injection',                  brand = 'Annis',           price = 9000,    category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'bifta',           name = 'Bifta',                         brand = 'Annis',           price = 15500,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'blazer',          name = 'Blazer',                        brand = 'Annis',           price = 7500,    category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'blazer2',         name = 'Blazer Lifeguard',              brand = 'Nagasaki',        price = 7000,    category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'blazer3',         name = 'Blazer Hot Rod',                brand = 'Nagasaki',        price = 7000,    category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'blazer4',         name = 'Blazer Sport',                  brand = 'Annis',           price = 9250,    category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'blazer5',         name = 'Blazer Aqua',                   brand = 'Nagasaki',        price = 40000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'brawler',         name = 'Brawler',                       brand = 'Annis',           price = 40000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'caracara',        name = 'Caracara',                      brand = 'Vapid',           price = 60000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'caracara2',       name = 'Caracara 4x4',                  brand = 'Vapid',           price = 80000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'dubsta3',         name = 'Dubsta 6x6',                    brand = 'Annis',           price = 34000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'dune',            name = 'Dune Buggy',                    brand = 'Annis',           price = 14000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'everon',          name = 'Everon',                        brand = 'Karin',           price = 60000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'freecrawler',     name = 'Freecrawler',                   brand = 'Canis',           price = 24000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'hellion',         name = 'Hellion',                       brand = 'Annis',           price = 38000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'kalahari',        name = 'Kalahari',                      brand = 'Canis',           price = 14000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'kamacho',         name = 'Kamacho',                       brand = 'Canis',           price = 50000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'mesa3',           name = 'Mesa Merryweather',             brand = 'Canis',           price = 400000,  category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'outlaw',          name = 'Outlaw',                        brand = 'Nagasaki',        price = 15000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'rancherxl',       name = 'Rancher XL',                    brand = 'Declasse',        price = 24000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'rebel2',          name = 'Rebel',                         brand = 'Vapid',           price = 20000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'riata',           name = 'Riata',                         brand = 'Vapid',           price = 380000,  category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'sandking',        name = 'Sandking XL',                   brand = 'Vapid',           price = 25000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'sandking2',       name = 'Sandking SWB',                  brand = 'Vapid',           price = 38000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'trophytruck',     name = 'Trophy Truck',                  brand = 'Vapid',           price = 60000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'trophytruck2',    name = 'Desert Raid',                   brand = 'Vapid',           price = 80000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'vagrant',         name = 'Vagrant',                       brand = 'Maxwell',         price = 50000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'verus',           name = 'Verus',                         brand = 'Dinka',           price = 20000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'winky',           name = 'Winky',                         brand = 'Vapid',           price = 10000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'yosemite3',       name = 'Yosemite Rancher',              brand = 'Declasse',        price = 425000,  category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'mesa',            name = 'Mesa',                          brand = 'Canis',           price = 12000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'ratel',           name = 'Ratel',                         brand = 'Vapid',           price = 199000,  category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'l35',             name = 'Walton L35',                    brand = 'Declasse',        price = 167000,  category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'monstrociti',     name = 'MonstroCiti',                   brand = 'Maibatsu',        price = 48000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'draugur',         name = 'Draugur',                       brand = 'Declasse',        price = 99000,   category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'terminus',        name = 'Terminus',                      brand = 'Canis',           price = 187750,  category = 'offroad',        type = 'automobile', shop = 'pdm' },
    { model = 'yosemite4',       name = 'Yosemite 1500',                 brand = 'Declasse',        price = 187750,  category = 'offroad',        type = 'automobile', shop = 'pdm' },
    --- Industrial (10)
    { model = 'guardian',        name = 'Guardian',                      brand = 'Vapid',           price = 30000,   category = 'industrial',     type = 'automobile', shop = 'truck' },
    { model = 'mixer2',          name = 'Mixer II',                      brand = 'HVY',             price = 30000,   category = 'industrial',     type = 'automobile', shop = 'truck' },
    { model = 'tiptruck2',       name = 'Tipper II',                     brand = 'Brute',           price = 30000,   category = 'industrial',     type = 'automobile', shop = 'truck' },
    { model = 'tiptruck',        name = 'Tipper',                        brand = 'Brute',           price = 30000,   category = 'industrial',     type = 'automobile', shop = 'truck' },
    { model = 'rubble',          name = 'Rubble',                        brand = 'Jobuilt',         price = 30000,   category = 'industrial',     type = 'automobile', shop = 'truck' },
    { model = 'mixer',           name = 'Mixer',                         brand = 'HVY',             price = 30000,   category = 'industrial',     type = 'automobile', shop = 'truck' },
    { model = 'flatbed',         name = 'Flatbed Truck',                 brand = 'MTL',             price = 30000,   category = 'industrial',     type = 'automobile', shop = 'truck' },
    { model = 'dump',            name = 'Dump Truck',                    brand = 'HVY',             price = 30000,   category = 'industrial',     type = 'automobile', shop = 'truck' },
    { model = 'bulldozer',       name = 'Dozer',                         brand = 'HVY',             price = 30000,   category = 'industrial',     type = 'automobile', shop = 'truck' },
    { model = 'handler',         name = 'Dock Handler',                  brand = 'HVY',             price = 30000,   category = 'industrial',     type = 'automobile', shop = 'truck' },
    { model = 'cutter',          name = 'Cutter',                        brand = 'HVY',             price = 30000,   category = 'industrial',     type = 'automobile', shop = 'truck' },
    --- Utility (11)
    { model = 'slamtruck',       name = 'Slam Truck',                    brand = 'Vapid',           price = 100000,  category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'caddy3',          name = 'Caddy (Bunker)',                brand = 'Nagasaki',        price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'caddy2',          name = 'Caddy (Civilian)',              brand = 'Nagasaki',        price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'caddy3',          name = 'Caddy (Golf)',                  brand = 'Nagasaki',        price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'utillitruck',     name = 'Utility Truck (Cherry Picker)', brand = 'Brute',           price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'utillitruck2',    name = 'Utility Truck (Van)',           brand = 'Brute',           price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'utillitruck3',    name = 'Utility Truck (Contender)',     brand = 'Vapid',           price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'tractor',         name = 'Tractor',                       brand = 'Stanley',         price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'tractor2',        name = 'Fieldmaster',                   brand = 'Stanley',         price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'tractor3',        name = 'Fieldmaster',                   brand = 'Stanley',         price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'towtruck',        name = 'Tow Truck (Large)',             brand = 'Vapid',           price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'towtruck2',       name = 'Tow Truck (Small)',             brand = 'Vapid',           price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'scrap',           name = 'Scrap Truck',                   brand = 'Vapid',           price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'sadler',          name = 'Sadler',                        brand = 'Vapid',           price = 20000,   category = 'utility',        type = 'automobile', shop = 'pdm' },
    { model = 'ripley',          name = 'Ripley',                        brand = 'HVY',             price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'mower',           name = 'Lawn Mower',                    brand = 'Jacksheepe',      price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'forklift',        name = 'Forklift',                      brand = 'HVY',             price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'docktug',         name = 'Docktug',                       brand = 'HVY',             price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'airtug',          name = 'Airtug',                        brand = 'HVY',             price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'trailers5',       name = 'Trailer (Christmas)',           brand = 'Unknown',         price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    { model = 'tvtrailer2',      name = 'Trailer (TV)',                  brand = 'Unknown',         price = 30000,   category = 'utility',        type = 'automobile', shop = 'truck' },
    --- Vans (12)
    { model = 'bison',           name = 'Bison',                         brand = 'Bravado',         price = 18000,   category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'bobcatxl',        name = 'Bobcat XL Open',                brand = 'Vapid',           price = 13500,   category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'burrito3',        name = 'Burrito',                       brand = 'Declasse',        price = 4000,    category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'gburrito2',       name = 'Burrito Custom',                brand = 'Declasse',        price = 11500,   category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'rumpo',           name = 'Rumpo',                         brand = 'Bravado',         price = 9000,    category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'journey',         name = 'Journey',                       brand = 'Zirconium',       price = 6500,    category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'minivan',         name = 'Minivan',                       brand = 'Vapid',           price = 7000,    category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'minivan2',        name = 'Minivan Custom',                brand = 'Vapid',           price = 10000,   category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'paradise',        name = 'Paradise',                      brand = 'Bravado',         price = 9000,    category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'rumpo3',          name = 'Rumpo Custom',                  brand = 'Bravado',         price = 19500,   category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'speedo',          name = 'Speedo',                        brand = 'Vapid',           price = 10000,   category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'speedo4',         name = 'Speedo Custom',                 brand = 'Vapid',           price = 15000,   category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'surfer',          name = 'Surfer',                        brand = 'BF',              price = 9000,    category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'youga3',          name = 'Youga Classic 4x4',             brand = 'Bravado',         price = 15000,   category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'youga',           name = 'Youga',                         brand = 'Bravado',         price = 8000,    category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'youga2',          name = 'Youga Classic',                 brand = 'Bravado',         price = 14500,   category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'youga4',          name = 'Youga Custom',                  brand = 'Bravado',         price = 85000,   category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'moonbeam',        name = 'Moonbeam',                      brand = 'Declasse',        price = 13000,   category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'moonbeam2',       name = 'Moonbeam Custom',               brand = 'Declasse',        price = 15000,   category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'boxville',        name = 'Boxville LSDWP',                brand = 'Brute',           price = 45000,   category = 'vans',           type = 'automobile', shop = 'truck' },
    { model = 'boxville2',       name = 'Boxville Go Postal',            brand = 'Brute',           price = 45000,   category = 'vans',           type = 'automobile', shop = 'truck' },
    { model = 'boxville3',       name = 'Boxville Humane Labs',          brand = 'Brute',           price = 45000,   category = 'vans',           type = 'automobile', shop = 'truck' },
    { model = 'boxville4',       name = 'Boxville Post OP',              brand = 'Brute',           price = 45000,   category = 'vans',           type = 'automobile', shop = 'truck' },
    { model = 'boxville5',       name = 'Armored Boxville',              brand = 'Brute',           price = 45000,   category = 'vans',           type = 'automobile', shop = 'none' },
    { model = 'pony',            name = 'Pony',                          brand = 'Brute',           price = 45000,   category = 'vans',           type = 'automobile', shop = 'truck' },
    { model = 'pony2',           name = 'Pony (Smoke on the water)',     brand = 'Brute',           price = 45000,   category = 'vans',           type = 'automobile', shop = 'truck' },
    { model = 'journey2',        name = 'Journey II',                    brand = 'Zirconium',       price = 7000,    category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'surfer3',         name = 'Surfer Custom',                 brand = 'BF',              price = 15000,   category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'speedo5',         name = 'Speedo Custom',                 brand = 'Vapid',           price = 238000,  category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'mule2',           name = 'Mule',                          brand = 'Maibatsu',        price = 40000,   category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'mule3',           name = 'Mule',                          brand = 'Maibatsu',        price = 40000,   category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'taco',            name = 'Taco Truck',                    brand = 'Brute',           price = 45000,   category = 'vans',           type = 'automobile', shop = 'pdm' },
    { model = 'boxville6',       name = 'Boxville (LSDS)',               brand = 'Brute',           price = 47500,   category = 'vans',           type = 'automobile', shop = 'pdm' },
    --- Cycles (13)
    { model = 'bmx',             name = 'BMX',                           brand = 'Bike',            price = 160,     category = 'cycles',         type = 'bike',       shop = 'pdm' },
    { model = 'cruiser',         name = 'Cruiser',                       brand = 'Bike',            price = 510,     category = 'cycles',         type = 'bike',       shop = 'pdm' },
    { model = 'fixter',          name = 'Fixter',                        brand = 'Bike',            price = 225,     category = 'cycles',         type = 'bike',       shop = 'pdm' },
    { model = 'scorcher',        name = 'Scorcher',                      brand = 'Bike',            price = 280,     category = 'cycles',         type = 'bike',       shop = 'pdm' },
    { model = 'tribike',         name = 'Whippet Race Bike',             brand = 'Bike',            price = 500,     category = 'cycles',         type = 'bike',       shop = 'pdm' },
    { model = 'tribike2',        name = 'Endurex Race Bike',             brand = 'Bike',            price = 700,     category = 'cycles',         type = 'bike',       shop = 'pdm' },
    { model = 'tribike3',        name = 'Tri-Cycles Race Bike',          brand = 'Bike',            price = 520,     category = 'cycles',         type = 'bike',       shop = 'pdm' },
    { model = 'inductor',        name = 'Inductor',                      brand = 'Coil',            price = 5000,    category = 'cycles',         type = 'bike',       shop = 'pdm' },
    { model = 'inductor2',       name = 'Junk Energy Inductor',          brand = 'Coil',            price = 5000,    category = 'cycles',         type = 'bike',       shop = 'pdm' },
    --- Boats (14)
    { model = 'avisa',           name = 'Avisa',                         brand = 'Kraken Subs',     price = 40000,   category = 'boats',          type = 'boat',       shop = 'none' },
    { model = 'patrolboat',      name = 'Kurtz 31 Patrol Boat',          brand = 'Unknown',         price = 40000,   category = 'boats',          type = 'boat',       shop = 'none' },
    { model = 'longfin',         name = 'Longfin',                       brand = 'Shitzu',          price = 40000,   category = 'boats',          type = 'boat',       shop = 'boats' },
    { model = 'tug',             name = 'Tug',                           brand = 'Buckingham',      price = 40000,   category = 'boats',          type = 'boat',       shop = 'none' },
    { model = 'toro',            name = 'Toro',                          brand = 'Lampadati',       price = 40000,   category = 'boats',          type = 'boat',       shop = 'boats' },
    { model = 'toro2',           name = 'Toro Yacht',                    brand = 'Lampadati',       price = 40000,   category = 'boats',          type = 'boat',       shop = 'boats' },
    { model = 'submersible2',    name = 'Kraken',                        brand = 'Kraken Subs',     price = 40000,   category = 'boats',          type = 'boat',       shop = 'none' },
    { model = 'speeder',         name = 'Speeder',                       brand = 'Pegassi',         price = 40000,   category = 'boats',          type = 'boat',       shop = 'boats' },
    { model = 'speeder2',        name = 'Speeder Yacht',                 brand = 'Pegassi',         price = 40000,   category = 'boats',          type = 'boat',       shop = 'boats' },
    { model = 'tropic',          name = 'Tropic',                        brand = 'Shitzu',          price = 40000,   category = 'boats',          type = 'boat',       shop = 'boats' },
    { model = 'tropic2',         name = 'Tropic Yacht',                  brand = 'Shitzu',          price = 40000,   category = 'boats',          type = 'boat',       shop = 'boats' },
    { model = 'suntrap',         name = 'Suntrap',                       brand = 'Shitzu',          price = 40000,   category = 'boats',          type = 'boat',       shop = 'boats' },
    { model = 'submersible',     name = 'Submersible',                   brand = 'Kraken Subs',     price = 40000,   category = 'boats',          type = 'boat',       shop = 'none' },
    { model = 'squalo',          name = 'Squalo',                        brand = 'Shitzu',          price = 40000,   category = 'boats',          type = 'boat',       shop = 'boats' },
    { model = 'seashark',        name = 'Seashark',                      brand = 'Speedophile',     price = 40000,   category = 'boats',          type = 'boat',       shop = 'boats' },
    { model = 'seashark3',       name = 'Seashark Yacht',                brand = 'Speedophile',     price = 40000,   category = 'boats',          type = 'boat',       shop = 'boats' },
    { model = 'marquis',         name = 'Marquis',                       brand = 'Dinka',           price = 40000,   category = 'boats',          type = 'boat',       shop = 'boats' },
    { model = 'jetmax',          name = 'Jetmax',                        brand = 'Shitzu',          price = 40000,   category = 'boats',          type = 'boat',       shop = 'boats' },
    { model = 'dinghy',          name = 'Dinghy 2-Seater',               brand = 'Nagasaki',        price = 40000,   category = 'boats',          type = 'boat',       shop = 'boats' },
    { model = 'dinghy2',         name = 'Dinghy 4-Seater',               brand = 'Nagasaki',        price = 40000,   category = 'boats',          type = 'boat',       shop = 'boats' },
    { model = 'dinghy3',         name = 'Dinghy (Heist)',                brand = 'Nagasaki',        price = 40000,   category = 'boats',          type = 'boat',       shop = 'boats' },
    { model = 'dinghy4',         name = 'Dinghy Yacht',                  brand = 'Nagasaki',        price = 40000,   category = 'boats',          type = 'boat',       shop = 'boats' },
    --- Helicopters (15)
    { model = 'conada2',         name = 'Weaponized Conada',             brand = 'Buckingham',      price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'none' },
    { model = 'conada',          name = 'Conada',                        brand = 'Buckingham',      price = 115000,  category = 'helicopters',    type = 'heli',       shop = 'air' },
    { model = 'seasparrow2',     name = 'Sparrow',                       brand = 'Unknown',         price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'air' },
    { model = 'annihilator2',    name = 'Annihilator Stealth',           brand = 'Western Company', price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'none' },
    { model = 'seasparrow',      name = 'Sea Sparrow',                   brand = 'Unknown',         price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'air' },
    { model = 'akula',           name = 'Akula',                         brand = 'Unknown',         price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'none' },
    { model = 'hunter',          name = 'FH-1 Hunter',                   brand = 'Unknown',         price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'none' },
    { model = 'havok',           name = 'Havok',                         brand = 'Nagasaki',        price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'air' },
    { model = 'volatus',         name = 'Volatus',                       brand = 'Buckingham',      price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'air' },
    { model = 'supervolito2',    name = 'SuperVolito Carbon',            brand = 'Buckingham',      price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'air' },
    { model = 'supervolito',     name = 'SuperVolito',                   brand = 'Buckingham',      price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'air' },
    { model = 'swift2',          name = 'Swift Deluxe',                  brand = 'Buckingham',      price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'air' },
    { model = 'valkyrie',        name = 'Valkyrie',                      brand = 'Buckingham',      price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'none' },
    { model = 'savage',          name = 'Savage',                        brand = 'Unknown',         price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'none' },
    { model = 'swift',           name = 'Swift',                         brand = 'Buckingham',      price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'air' },
    { model = 'annihilator',     name = 'Annihilator',                   brand = 'Western Company', price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'none' },
    { model = 'cargobob2',       name = 'Cargobob Jetsam',               brand = 'Western Company', price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'none' },
    { model = 'skylift',         name = 'Skylift',                       brand = 'HVY',             price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'none' },
    { model = 'maverick',        name = 'Maverick',                      brand = 'Buckingham',      price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'air' },
    { model = 'frogger',         name = 'Frogger',                       brand = 'Maibatsu',        price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'air' },
    { model = 'frogger2',        name = 'Frogger',                       brand = 'Maibatsu',        price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'air' },
    { model = 'cargobob',        name = 'Cargobob',                      brand = 'Western Company', price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'none' },
    { model = 'cargobob3',       name = 'Cargobob',                      brand = 'Western Company', price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'none' },
    { model = 'seasparrow3',     name = 'Sparrow (Prop)',                brand = 'Unknown',         price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'none' },
    { model = 'buzzard',         name = 'Buzzard Attack Chopper',        brand = 'Nagasaki',        price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'none' },
    { model = 'buzzard2',        name = 'Buzzard',                       brand = 'Nagasaki',        price = 52000,   category = 'helicopters',    type = 'heli',       shop = 'none' },
    --- Planes (16)
    { model = 'streamer216',     name = 'Streamer216',                   brand = 'Mammoth',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'raiju',           name = 'F-160 Raiju',                   brand = 'Mammoth',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'alkonost',        name = 'RO-86 Alkonost',                brand = 'Unknown',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'strikeforce',     name = 'B-11 Strikeforce',              brand = 'Unknown',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'blimp3',          name = 'Blimp',                         brand = 'Unknown',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'avenger',         name = 'Avenger',                       brand = 'Mammoth',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'avenger2',        name = 'Avenger',                       brand = 'Mammoth',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'volatol',         name = 'Volatol',                       brand = 'Unknown',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'nokota',          name = 'P-45 Nokota',                   brand = 'Unknown',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'seabreeze',       name = 'Seabreeze',                     brand = 'Western Company', price = 45000,   category = 'planes',         type = 'plane',      shop = 'air' },
    { model = 'pyro',            name = 'Pyro',                          brand = 'Buckingham',      price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'mogul',           name = 'Mogul',                         brand = 'Mammoth',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'howard',          name = 'Howard NX-25',                  brand = 'Unknown',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'bombushka',       name = 'RM-10 Bombushka',               brand = 'Unknown',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'molotok',         name = 'V-65 Molotok',                  brand = 'Unknown',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'microlight',      name = 'Ultralight',                    brand = 'Unknown',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'air' },
    { model = 'tula',            name = 'Tula',                          brand = 'Mammoth',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'rogue',           name = 'Rogue',                         brand = 'Western Company', price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'starling',        name = 'LF-22 Starling',                brand = 'Unknown',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'alphaz1',         name = 'Alpha-Z1',                      brand = 'Buckingham',      price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'nimbus',          name = 'Nimbus',                        brand = 'Buckingham',      price = 45000,   category = 'planes',         type = 'plane',      shop = 'air' },
    { model = 'luxor2',          name = 'Luxor Deluxe',                  brand = 'Buckingham',      price = 45000,   category = 'planes',         type = 'plane',      shop = 'air' },
    { model = 'velum2',          name = 'Velum 5-seater',                brand = 'JoBuilt',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'air' },
    { model = 'hydra',           name = 'Hydra',                         brand = 'Mammoth',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'blimp2',          name = 'Xero Blimp',                    brand = 'Unknown',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'dodo',            name = 'Dodo',                          brand = 'Mammoth',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'air' },
    { model = 'miljet',          name = 'Miljet',                        brand = 'Buckingham',      price = 45000,   category = 'planes',         type = 'plane',      shop = 'air' },
    { model = 'besra',           name = 'Besra',                         brand = 'Western Company', price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'vestra',          name = 'Vestra',                        brand = 'Buckingham',      price = 45000,   category = 'planes',         type = 'plane',      shop = 'air' },
    { model = 'cargoplane',      name = 'Cargo Plane',                   brand = 'JoBuilt',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'air' },
    { model = 'velum',           name = 'Velum',                         brand = 'JoBuilt',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'air' },
    { model = 'titan',           name = 'Titan',                         brand = 'Unknown',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'shamal',          name = 'Shamal',                        brand = 'Buckingham',      price = 45000,   category = 'planes',         type = 'plane',      shop = 'air' },
    { model = 'lazer',           name = 'P-996 Lazer',                   brand = 'JoBuilt',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'mammatus',        name = 'Mammatus',                      brand = 'JoBuilt',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'air' },
    { model = 'stunt',           name = 'Mallard',                       brand = 'Western Company', price = 45000,   category = 'planes',         type = 'plane',      shop = 'air' },
    { model = 'luxor',           name = 'Luxor',                         brand = 'Buckingham',      price = 45000,   category = 'planes',         type = 'plane',      shop = 'air' },
    { model = 'jet',             name = 'Jet',                           brand = 'Unknown',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    { model = 'duster',          name = 'Duster',                        brand = 'Western Company', price = 45000,   category = 'planes',         type = 'plane',      shop = 'air' },
    { model = 'cuban800',        name = 'Cuban 800',                     brand = 'Western Company', price = 45000,   category = 'planes',         type = 'plane',      shop = 'air' },
    { model = 'blimp',           name = 'Atomic Blimp',                  brand = 'Unknown',         price = 45000,   category = 'planes',         type = 'plane',      shop = 'none' },
    --- Service (17)
    { model = 'brickade',        name = 'Brickade',                      brand = 'MTL',             price = 100000,  category = 'service',        type = 'automobile', shop = 'none' },
    { model = 'brickade2',       name = 'Brickade 6x6',                  brand = 'MTL',             price = 100000,  category = 'service',        type = 'automobile', shop = 'none' },
    { model = 'pbus2',           name = 'Festival Bus',                  brand = 'Unknown',         price = 100000,  category = 'service',        type = 'automobile', shop = 'none' },
    { model = 'wastelander',     name = 'Wastelander',                   brand = 'MTL',             price = 100000,  category = 'service',        type = 'automobile', shop = 'none' },
    { model = 'rallytruck',      name = 'Dune',                          brand = 'MTL',             price = 100000,  category = 'service',        type = 'automobile', shop = 'none' },
    { model = 'metrotrain',      name = 'Metro Train',                   brand = 'Unknown',         price = 100000,  category = 'service',        type = 'automobile', shop = 'none' },
    { model = 'freight',         name = 'Freight Train',                 brand = 'Unknown',         price = 100000,  category = 'service',        type = 'automobile', shop = 'none' },
    { model = 'cablecar',        name = 'Cable Car',                     brand = 'Unknown',         price = 100000,  category = 'service',        type = 'automobile', shop = 'none' },
    { model = 'trash',           name = 'Trashmaster',                   brand = 'JoBuilt',         price = 100000,  category = 'service',        type = 'automobile', shop = 'none' },
    { model = 'trash2',          name = 'Trashmaster',                   brand = 'JoBuilt',         price = 100000,  category = 'service',        type = 'automobile', shop = 'none' },
    { model = 'tourbus',         name = 'Tour Bus',                      brand = 'Brute',           price = 100000,  category = 'service',        type = 'automobile', shop = 'none' },
    { model = 'taxi',            name = 'Taxi',                          brand = 'Vapid',           price = 100000,  category = 'service',        type = 'automobile', shop = 'none' },
    { model = 'rentalbus',       name = 'Rental Shuttle Bus',            brand = 'Brute',           price = 100000,  category = 'service',        type = 'automobile', shop = 'none' },
    { model = 'coach',           name = 'Dashound',                      brand = 'Brute',           price = 100000,  category = 'service',        type = 'automobile', shop = 'none' },
    { model = 'bus',             name = 'Bus',                           brand = 'Brute',           price = 100000,  category = 'service',        type = 'automobile', shop = 'none' },
    { model = 'airbus',          name = 'Airport Bus',                   brand = 'Brute',           price = 100000,  category = 'service',        type = 'automobile', shop = 'none' },
    --- Emergency (18)
    { model = 'riot',            name = 'Police Riot',                   brand = 'Brute',           price = 100000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'riot2',           name = 'RCV',                           brand = 'Unknown',         price = 100000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'pbus',            name = 'Police Prison Bus',             brand = 'Vapid',           price = 100000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'police',          name = 'Police Cruiser',                brand = 'Vapid',           price = 100000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'police2',         name = 'Police Buffalo',                brand = 'Vapid',           price = 100000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'police3',         name = 'Police Interceptor',            brand = 'Vapid',           price = 100000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'police4',         name = 'Unmarked Cruiser',              brand = 'Vapid',           price = 100000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'sheriff',         name = 'Sheriff SUV',                   brand = 'Declasse',        price = 100000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'sheriff2',        name = 'Sheriff Cruiser',               brand = 'Vapid',           price = 100000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'policeold1',      name = 'Police Rancher',                brand = 'Declasse',        price = 110000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'policeold2',      name = 'Police Roadcruiser',            brand = 'Albany',          price = 110000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'policet',         name = 'Police Transporter',            brand = 'Vapid',           price = 110000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'policeb',         name = 'Police Bike',                   brand = 'Vapid',           price = 110000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'polmav',          name = 'Police Maverick',               brand = 'Buckingham',      price = 110000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'ambulance',       name = 'Ambulance',                     brand = 'Brute',           price = 110000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'firetruk',        name = 'Fire Truck',                    brand = 'MTL',             price = 110000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'lguard',          name = 'Lifeguard',                     brand = 'Declasse',        price = 110000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'seashark2',       name = 'Seashark Lifeguard',            brand = 'Speedophile',     price = 40000,   category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'pranger',         name = 'Park Ranger',                   brand = 'Declasse',        price = 40000,   category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'fbi',             name = 'FIB Buffalo',                   brand = 'Bravado',         price = 40000,   category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'fbi2',            name = 'FIB Granger',                   brand = 'Declasse',        price = 40000,   category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'predator',        name = 'Police Predator',               brand = 'Unknown',         price = 40000,   category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'polgauntlet',     name = 'Gauntlet Interceptor',          brand = 'Bravado',         price = 40000,   category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'police5',         name = 'Stanier LE Cruiser',            brand = 'Vapid',           price = 40000,   category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'polimpaler5',     name = 'Impaler SZ Cruiser',            brand = 'Declasse',        price = 80000,   category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'polimpaler6',     name = 'Impaler LX Cruiser',            brand = 'Declasse',        price = 90000,   category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'poldominator10',  name = 'Dominator FX Interceptor',      brand = 'Vapid',           price = 230000,  category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'policet3',        name = 'Burrito (Bail Enforcement)',    brand = 'Declasse',        price = 60000,   category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'polgreenwood',    name = 'Greenwood Cruiser',             brand = 'Bravado',         price = 80000,   category = 'emergency',      type = 'automobile', shop = 'none' },
    { model = 'poldorado',       name = 'Dorado Cruiser',                brand = 'Vapid',           price = 80000,   category = 'emergency',      type = 'automobile', shop = 'none' },
    --- Military (19)
    { model = 'vetir',           name = 'Vetir',                         brand = 'Unknown',         price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    { model = 'kosatka',         name = 'Kosatka',                       brand = 'Rune',            price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    { model = 'minitank',        name = 'RC Tank',                       brand = 'Unknown',         price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    { model = 'scarab',          name = 'Scarab',                        brand = 'HVY',             price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    { model = 'terbyte',         name = 'Terrorbyte',                    brand = 'Benefactor',      price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    { model = 'thruster',        name = 'Thruster',                      brand = 'Mammoth',         price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    { model = 'khanjali',        name = 'TM-02 Khanjali Tank',           brand = 'Unknown',         price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    { model = 'chernobog',       name = 'Chernobog',                     brand = 'HVY',             price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    { model = 'barrage',         name = 'Barrage',                       brand = 'HVY',             price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    { model = 'trailerlarge',    name = 'Mobile Operations Center',      brand = 'Unknown',         price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    { model = 'halftrack',       name = 'Half-track',                    brand = 'Bravado',         price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    { model = 'apc',             name = 'APC Tank',                      brand = 'HVY',             price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    { model = 'trailersmall2',   name = 'Anti-Aircraft Trailer',         brand = 'Vom Feuer',       price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    { model = 'rhino',           name = 'Rhino Tank',                    brand = 'Unknown',         price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    { model = 'crusader',        name = 'Crusader',                      brand = 'Canis',           price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    { model = 'barracks',        name = 'Barracks',                      brand = 'HVY',             price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    { model = 'barracks2',       name = 'Barracks Semi',                 brand = 'HVY',             price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    { model = 'barracks3',       name = 'Barracks',                      brand = 'HVY',             price = 100000,  category = 'military',       type = 'automobile', shop = 'none' },
    --- Commercial (20)
    { model = 'cerberus',        name = 'Apocalypse Cerberus',           brand = 'MTL',             price = 100000,  category = 'commercial',     type = 'automobile', shop = 'none' },
    { model = 'pounder2',        name = 'Pounder Custom',                brand = 'MTL',             price = 55000,   category = 'commercial',     type = 'automobile', shop = 'truck' },
    { model = 'mule4',           name = 'Mule Custom',                   brand = 'Maibatsu',        price = 40000,   category = 'commercial',     type = 'automobile', shop = 'truck' },
    { model = 'phantom3',        name = 'Phantom Custom',                brand = 'Jobuilt',         price = 110000,  category = 'commercial',     type = 'automobile', shop = 'truck' },
    { model = 'hauler2',         name = 'Hauler Custom',                 brand = 'Jobuilt',         price = 100000,  category = 'commercial',     type = 'automobile', shop = 'truck' },
    { model = 'phantom2',        name = 'Phantom Wedge',                 brand = 'Jobuilt',         price = 100000,  category = 'commercial',     type = 'automobile', shop = 'none' },
    { model = 'mule5',           name = 'Mule (Heist)',                  brand = 'Maibatsu',        price = 40000,   category = 'commercial',     type = 'automobile', shop = 'truck' },
    { model = 'stockade',        name = 'Stockade',                      brand = 'Brute',           price = 100000,  category = 'commercial',     type = 'automobile', shop = 'truck' },
    { model = 'pounder',         name = 'Pounder',                       brand = 'MTL',             price = 55000,   category = 'commercial',     type = 'automobile', shop = 'truck' },
    { model = 'phantom',         name = 'Phantom',                       brand = 'Jobuilt',         price = 100000,  category = 'commercial',     type = 'automobile', shop = 'truck' },
    { model = 'packer',          name = 'Packer',                        brand = 'MTL',             price = 100000,  category = 'commercial',     type = 'automobile', shop = 'truck' },
    { model = 'mule',            name = 'Mule',                          brand = 'Maibatsu',        price = 40000,   category = 'commercial',     type = 'automobile', shop = 'truck' },
    { model = 'hauler',          name = 'Hauler',                        brand = 'Jobuilt',         price = 100000,  category = 'commercial',     type = 'automobile', shop = 'truck' },
    { model = 'biff',            name = 'Biff',                          brand = 'Brute',           price = 100000,  category = 'commercial',     type = 'automobile', shop = 'truck' },
    { model = 'benson',          name = 'Benson',                        brand = 'Vapid',           price = 55000,   category = 'commercial',     type = 'automobile', shop = 'truck' },
    { model = 'benson2',         name = 'Benson (Cluckin Bell)',         brand = 'Vapid',           price = 55000,   category = 'commercial',     type = 'automobile', shop = 'truck' },
    { model = 'phantom4',        name = 'Phantom (Christmas)',           brand = 'Vapid',           price = 100000,  category = 'commercial',     type = 'automobile', shop = 'truck' },
    --- Trains (21)
    --- Open Wheel (22)
    { model = 'openwheel2',      name = 'DR1',                           brand = 'Declasse',        price = 100000,  category = 'openwheel',      type = 'automobile', shop = 'none' },
    { model = 'openwheel1',      name = 'BR8',                           brand = 'Benefactor',      price = 100000,  category = 'openwheel',      type = 'automobile', shop = 'none' },
    { model = 'formula2',        name = 'R88',                           brand = 'Ocelot',          price = 100000,  category = 'openwheel',      type = 'automobile', shop = 'none' },
    { model = 'formula',         name = 'PR4',                           brand = 'Progen',          price = 100000,  category = 'openwheel',      type = 'automobile', shop = 'none' },
}

QBShared.VehicleHashes = QBShared.VehicleHashes or {}
for i = 1, #Vehicles do
    local hash = joaat(Vehicles[i].model)
    QBShared.Vehicles[Vehicles[i].model] = {
        spawncode = Vehicles[i].model,
        name = Vehicles[i].name,
        brand = Vehicles[i].brand,
        model = Vehicles[i].model,
        price = Vehicles[i].price,
        category = Vehicles[i].category,
        hash = hash,
        type = Vehicles[i].type,
        shop = Vehicles[i].shop
    }
    QBShared.VehicleHashes[hash] = QBShared.Vehicles[Vehicles[i].model]
end
