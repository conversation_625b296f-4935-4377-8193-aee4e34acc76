{"manifest": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.18", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is.git"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "devDependencies": {"eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.17.2", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "mocha": "6.1.4", "nyc": "14.0.0"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "keywords": ["content", "type", "checking"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-type-is-1.6.18-4e552cd05df09467dcbc4ef739de89f2cf37c131-integrity\\node_modules\\type-is\\package.json", "readmeFilename": "README.md", "readme": "# type-is\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nInfer the content-type of a request.\n\n### Install\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install type-is\n```\n\n## API\n\n```js\nvar http = require('http')\nvar typeis = require('type-is')\n\nhttp.createServer(function (req, res) {\n  var istext = typeis(req, ['text/*'])\n  res.end('you ' + (istext ? 'sent' : 'did not send') + ' me text')\n})\n```\n\n### typeis(request, types)\n\nChecks if the `request` is one of the `types`. If the request has no body,\neven if there is a `Content-Type` header, then `null` is returned. If the\n`Content-Type` header is invalid or does not matches any of the `types`, then\n`false` is returned. Otherwise, a string of the type that matched is returned.\n\nThe `request` argument is expected to be a Node.js HTTP request. The `types`\nargument is an array of type strings.\n\nEach type in the `types` array can be one of the following:\n\n- A file extension name such as `json`. This name will be returned if matched.\n- A mime type such as `application/json`.\n- A mime type with a wildcard such as `*/*` or `*/json` or `application/*`.\n  The full mime type will be returned if matched.\n- A suffix such as `+json`. This can be combined with a wildcard such as\n  `*/vnd+json` or `application/*+json`. The full mime type will be returned\n  if matched.\n\nSome examples to illustrate the inputs and returned value:\n\n<!-- eslint-disable no-undef -->\n\n```js\n// req.headers.content-type = 'application/json'\n\ntypeis(req, ['json']) // => 'json'\ntypeis(req, ['html', 'json']) // => 'json'\ntypeis(req, ['application/*']) // => 'application/json'\ntypeis(req, ['application/json']) // => 'application/json'\n\ntypeis(req, ['html']) // => false\n```\n\n### typeis.hasBody(request)\n\nReturns a Boolean if the given `request` has a body, regardless of the\n`Content-Type` header.\n\nHaving a body has no relation to how large the body is (it may be 0 bytes).\nThis is similar to how file existence works. If a body does exist, then this\nindicates that there is data to read from the Node.js request stream.\n\n<!-- eslint-disable no-undef -->\n\n```js\nif (typeis.hasBody(req)) {\n  // read the body, since there is one\n\n  req.on('data', function (chunk) {\n    // ...\n  })\n}\n```\n\n### typeis.is(mediaType, types)\n\nChecks if the `mediaType` is one of the `types`. If the `mediaType` is invalid\nor does not matches any of the `types`, then `false` is returned. Otherwise, a\nstring of the type that matched is returned.\n\nThe `mediaType` argument is expected to be a\n[media type](https://tools.ietf.org/html/rfc6838) string. The `types` argument\nis an array of type strings.\n\nEach type in the `types` array can be one of the following:\n\n- A file extension name such as `json`. This name will be returned if matched.\n- A mime type such as `application/json`.\n- A mime type with a wildcard such as `*/*` or `*/json` or `application/*`.\n  The full mime type will be returned if matched.\n- A suffix such as `+json`. This can be combined with a wildcard such as\n  `*/vnd+json` or `application/*+json`. The full mime type will be returned\n  if matched.\n\nSome examples to illustrate the inputs and returned value:\n\n<!-- eslint-disable no-undef -->\n\n```js\nvar mediaType = 'application/json'\n\ntypeis.is(mediaType, ['json']) // => 'json'\ntypeis.is(mediaType, ['html', 'json']) // => 'json'\ntypeis.is(mediaType, ['application/*']) // => 'application/json'\ntypeis.is(mediaType, ['application/json']) // => 'application/json'\n\ntypeis.is(mediaType, ['html']) // => false\n```\n\n## Examples\n\n### Example body parser\n\n```js\nvar express = require('express')\nvar typeis = require('type-is')\n\nvar app = express()\n\napp.use(function bodyParser (req, res, next) {\n  if (!typeis.hasBody(req)) {\n    return next()\n  }\n\n  switch (typeis(req, ['urlencoded', 'json', 'multipart'])) {\n    case 'urlencoded':\n      // parse urlencoded body\n      throw new Error('implement urlencoded body parsing')\n    case 'json':\n      // parse json body\n      throw new Error('implement json body parsing')\n    case 'multipart':\n      // parse multipart body\n      throw new Error('implement multipart body parsing')\n    default:\n      // 415 error code\n      res.statusCode = 415\n      res.end()\n      break\n  }\n})\n```\n\n## License\n\n[MIT](LICENSE)\n\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/type-is/master\n[coveralls-url]: https://coveralls.io/r/jshttp/type-is?branch=master\n[node-version-image]: https://badgen.net/npm/node/type-is\n[node-version-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/type-is\n[npm-url]: https://npmjs.org/package/type-is\n[npm-version-image]: https://badgen.net/npm/v/type-is\n[travis-image]: https://badgen.net/travis/jshttp/type-is/master\n[travis-url]: https://travis-ci.org/jshttp/type-is\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2014 <PERSON> <<EMAIL>>\nCopyright (c) 2014-2015 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR CO<PERSON>YRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/type-is/-/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131", "type": "tarball", "reference": "https://registry.yarnpkg.com/type-is/-/type-is-1.6.18.tgz", "hash": "4e552cd05df09467dcbc4ef739de89f2cf37c131", "integrity": "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==", "registry": "npm", "packageName": "type-is", "cacheIntegrity": "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g== sha1-TlUs0F3wlGfcvE73Od6J8s83wTE="}, "registry": "npm", "hash": "4e552cd05df09467dcbc4ef739de89f2cf37c131"}