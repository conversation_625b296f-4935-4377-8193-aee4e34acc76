{"manifest": {"name": "domutils", "version": "1.7.0", "description": "utilities for working with htmlparser2's dom", "main": "index.js", "scripts": {"test": "mocha test/tests/**.js && jshint index.js test/**/*.js lib/*.js"}, "repository": {"type": "git", "url": "git://github.com/FB55/domutils.git"}, "keywords": ["dom", "htmlparser2"], "dependencies": {"dom-serializer": "0", "domelementtype": "1"}, "devDependencies": {"htmlparser2": "~3.9.2", "domhandler": "2", "jshint": "~2.9.4", "mocha": "~3.2.0"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "jshintConfig": {"proto": true, "unused": true, "eqnull": true, "undef": true, "quotmark": "double", "eqeqeq": true, "trailing": true, "node": true, "globals": {"describe": true, "it": true, "beforeEach": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-domutils-1.7.0-56ea341e834e06e6748af7a1cb25da67ea9f8c2a-integrity\\node_modules\\domutils\\package.json", "readmeFilename": "readme.md", "readme": "Utilities for working with htmlparser2's dom\n\n[![Build Status](https://travis-ci.org/fb55/domutils.svg?branch=master)](https://travis-ci.org/fb55/domutils)\n", "licenseText": "Copyright (c) <PERSON>\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n\nRedistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.\n\nRedistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.\n\nTHIS IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERC<PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS,\nEVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/domutils/-/domutils-1.7.0.tgz#56ea341e834e06e6748af7a1cb25da67ea9f8c2a", "type": "tarball", "reference": "https://registry.yarnpkg.com/domutils/-/domutils-1.7.0.tgz", "hash": "56ea341e834e06e6748af7a1cb25da67ea9f8c2a", "integrity": "sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg==", "registry": "npm", "packageName": "domutils", "cacheIntegrity": "sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg== sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo="}, "registry": "npm", "hash": "56ea341e834e06e6748af7a1cb25da67ea9f8c2a"}