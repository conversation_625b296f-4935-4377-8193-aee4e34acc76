{"manifest": {"name": "promise-inflight", "version": "1.0.1", "description": "One promise for multiple requests in flight to avoid async duplication", "main": "inflight.js", "files": ["inflight.js"], "license": "ISC", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "devDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/iarna/promise-inflight.git"}, "bugs": {"url": "https://github.com/iarna/promise-inflight/issues"}, "homepage": "https://github.com/iarna/promise-inflight#readme", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-promise-inflight-1.0.1-98472870bf228132fcbdd868129bad12c3c029e3-integrity\\node_modules\\promise-inflight\\package.json", "readmeFilename": "README.md", "readme": "# promise-inflight\n\nOne promise for multiple requests in flight to avoid async duplication\n\n## USAGE\n\n```javascript\nconst inflight = require('promise-inflight')\n\n// some request that does some stuff\nfunction req(key) {\n  // key is any random string.  like a url or filename or whatever.\n  return inflight(key, () => {\n    // this is where you'd fetch the url or whatever\n    return Promise.delay(100)\n  })\n}\n\n// only assigns a single setTimeout\n// when it dings, all thens get called with the same result.  (There's only\n// one underlying promise.)\nreq('foo').then(…)\nreq('foo').then(…)\nreq('foo').then(…)\nreq('foo').then(…)\n```\n\n## SEE ALSO\n\n* [inflight](https://npmjs.com/package/inflight) - For the callback based function on which this is based.\n\n## STILL NEEDS\n\nTests!\n", "licenseText": "Copyright (c) 2017, <PERSON> <<EMAIL>>\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\nOR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/promise-inflight/-/promise-inflight-1.0.1.tgz#98472870bf228132fcbdd868129bad12c3c029e3", "type": "tarball", "reference": "https://registry.yarnpkg.com/promise-inflight/-/promise-inflight-1.0.1.tgz", "hash": "98472870bf228132fcbdd868129bad12c3c029e3", "integrity": "sha1-mEcocL8igTL8vdhoEputEsPAKeM=", "registry": "npm", "packageName": "promise-inflight", "cacheIntegrity": "sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g== sha1-mEcocL8igTL8vdhoEputEsPAKeM="}, "registry": "npm", "hash": "98472870bf228132fcbdd868129bad12c3c029e3"}