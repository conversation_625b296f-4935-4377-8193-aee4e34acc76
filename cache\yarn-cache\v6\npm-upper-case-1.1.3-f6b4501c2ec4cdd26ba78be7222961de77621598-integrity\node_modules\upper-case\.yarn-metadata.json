{"manifest": {"name": "upper-case", "version": "1.1.3", "description": "Upper case a string", "main": "upper-case.js", "typings": "upper-case.d.ts", "files": ["upper-case.js", "upper-case.d.ts", "LICENSE"], "scripts": {"lint": "standard", "test-std": "mocha -- -R spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail", "test": "npm run lint && npm run test-cov"}, "standard": {"ignore": ["coverage/**", "node_modules/**", "bower_components/**"]}, "repository": {"type": "git", "url": "git://github.com/blakeembrey/upper-case.git"}, "keywords": ["cases", "upper", "uppercase", "case"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/blakeembrey/upper-case/issues"}, "homepage": "https://github.com/blakeembrey/upper-case", "devDependencies": {"istanbul": "^0.3.5", "mocha": "^2.1.0", "pre-commit": "^1.0.2", "standard": "^2.4.5"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-upper-case-1.1.3-f6b4501c2ec4cdd26ba78be7222961de77621598-integrity\\node_modules\\upper-case\\package.json", "readmeFilename": "README.md", "readme": "# Upper Case\n\n[![NPM version][npm-image]][npm-url]\n[![NPM downloads][downloads-image]][downloads-url]\n[![Build status][travis-image]][travis-url]\n[![Test coverage][coveralls-image]][coveralls-url]\n\nUpper case a string.\n\nSupports Unicode (non-ASCII characters) and non-string entities, such as objects with a `toString` property, numbers and booleans. Empty values (`null` and `undefined`) will result in an empty string.\n\n## Installation\n\n```\nnpm install upper-case --save\n```\n\n## Usage\n\n```js\nvar upperCase = require('upper-case')\n\nupperCase(null)           //=> \"\"\nupperCase('string')       //=> \"STRING\"\nupperCase('string', 'tr') //=> \"STRİNG\"\n\nupperCase({ toString: function () { return 'test' } }) //=> \"TEST\"\n```\n\n## Typings\n\nIncludes a [TypeScript definition](upper-case.d.ts).\n\n## License\n\nMIT\n\n[npm-image]: https://img.shields.io/npm/v/upper-case.svg?style=flat\n[npm-url]: https://npmjs.org/package/upper-case\n[downloads-image]: https://img.shields.io/npm/dm/upper-case.svg?style=flat\n[downloads-url]: https://npmjs.org/package/upper-case\n[travis-image]: https://img.shields.io/travis/blakeembrey/upper-case.svg?style=flat\n[travis-url]: https://travis-ci.org/blakeembrey/upper-case\n[coveralls-image]: https://img.shields.io/coveralls/blakeembrey/upper-case.svg?style=flat\n[coveralls-url]: https://coveralls.io/r/blakeembrey/upper-case?branch=master\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 <PERSON> (<EMAIL>)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/upper-case/-/upper-case-1.1.3.tgz#f6b4501c2ec4cdd26ba78be7222961de77621598", "type": "tarball", "reference": "https://registry.yarnpkg.com/upper-case/-/upper-case-1.1.3.tgz", "hash": "f6b4501c2ec4cdd26ba78be7222961de77621598", "integrity": "sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg=", "registry": "npm", "packageName": "upper-case", "cacheIntegrity": "sha512-WRbjgmYzgXkCV7zNVpy5YgrHgbBv126rMALQQMrmzOVC4GM2waQ9x7xtm8VU+1yF2kWyPzI9zbZ48n4vSxwfSA== sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg="}, "registry": "npm", "hash": "f6b4501c2ec4cdd26ba78be7222961de77621598"}