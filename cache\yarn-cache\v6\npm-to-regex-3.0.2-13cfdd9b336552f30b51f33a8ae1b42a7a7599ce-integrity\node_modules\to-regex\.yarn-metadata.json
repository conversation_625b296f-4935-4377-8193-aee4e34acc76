{"manifest": {"name": "to-regex", "description": "Generate a regex from a string or array of strings.", "version": "3.0.2", "homepage": "https://github.com/jonschlinkert/to-regex", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/to-regex.git"}, "bugs": {"url": "https://github.com/jonschlinkert/to-regex/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["match", "regex", "regular expression", "test", "to"], "verb": {"toc": {"method": "preWrite"}, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["has-glob", "is-glob", "path-regex", "to-regex-range"]}, "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-to-regex-3.0.2-13cfdd9b336552f30b51f33a8ae1b42a7a7599ce-integrity\\node_modules\\to-regex\\package.json", "readmeFilename": "README.md", "readme": "# to-regex [![NPM version](https://img.shields.io/npm/v/to-regex.svg?style=flat)](https://www.npmjs.com/package/to-regex) [![NPM monthly downloads](https://img.shields.io/npm/dm/to-regex.svg?style=flat)](https://npmjs.org/package/to-regex) [![NPM total downloads](https://img.shields.io/npm/dt/to-regex.svg?style=flat)](https://npmjs.org/package/to-regex) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/to-regex.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/to-regex)\n\n> Generate a regex from a string or array of strings.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n- [Install](#install)\n- [Usage](#usage)\n- [Options](#options)\n  * [options.contains](#optionscontains)\n  * [options.negate](#optionsnegate)\n  * [options.nocase](#optionsnocase)\n  * [options.flags](#optionsflags)\n  * [options.cache](#optionscache)\n  * [options.safe](#optionssafe)\n- [About](#about)\n  * [Related projects](#related-projects)\n  * [Author](#author)\n  * [License](#license)\n\n_(TOC generated by [verb](https://github.com/verbose/verb) using [markdown-toc](https://github.com/jonschlinkert/markdown-toc))_\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save to-regex\n```\n\n## Usage\n\n```js\nvar toRegex = require('to-regex');\n\nconsole.log(toRegex('foo'));\n//=> /^(?:foo)$/\n\nconsole.log(toRegex('foo', {negate: true}));\n//=> /^(?:(?:(?!^(?:foo)$).)*)$/\n\nconsole.log(toRegex('foo', {contains: true}));\n//=> /(?:foo)/\n\nconsole.log(toRegex(['foo', 'bar'], {negate: true}));\n//=> /^(?:(?:(?!^(?:(?:foo)|(?:bar))$).)*)$/\n\nconsole.log(toRegex(['foo', 'bar'], {negate: true, contains: true}));\n//=> /^(?:(?:(?!(?:(?:foo)|(?:bar))).)*)$/\n```\n\n## Options\n\n### options.contains\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\nGenerate a regex that will match any string that _contains_ the given pattern. By default, regex is strict will only return true for exact matches.\n\n```js\nvar toRegex = require('to-regex');\nconsole.log(toRegex('foo', {contains: true}));\n//=> /(?:foo)/\n```\n\n### options.negate\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\nCreate a regex that will match everything except the given pattern.\n\n```js\nvar toRegex = require('to-regex');\nconsole.log(toRegex('foo', {negate: true}));\n//=> /^(?:(?:(?!^(?:foo)$).)*)$/\n```\n\n### options.nocase\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\nAdds the `i` flag, to enable case-insensitive matching.\n\n```js\nvar toRegex = require('to-regex');\nconsole.log(toRegex('foo', {nocase: true}));\n//=> /^(?:foo)$/i\n```\n\nAlternatively you can pass the flags you want directly on [options.flags](#options.flags).\n\n### options.flags\n\n**Type**: `String`\n\n**Default**: `undefined`\n\nDefine the flags you want to use on the generated regex.\n\n```js\nvar toRegex = require('to-regex');\nconsole.log(toRegex('foo', {flags: 'gm'}));\n//=> /^(?:foo)$/gm\nconsole.log(toRegex('foo', {flags: 'gmi', nocase: true})); //<= handles redundancy\n//=> /^(?:foo)$/gmi\n```\n\n### options.cache\n\n**Type**: `Boolean`\n\n**Default**: `true`\n\nGenerated regex is cached based on the provided string and options. As a result, runtime compilation only happens once per pattern (as long as options are also the same), which can result in dramatic speed improvements.\n\nThis also helps with debugging, since adding options and pattern are added to the generated regex.\n\n**Disable caching**\n\n```js\ntoRegex('foo', {cache: false});\n```\n\n### options.safe\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\nCheck the generated regular expression with [safe-regex](https://github.com/substack/safe-regex) and throw an error if the regex is potentially unsafe.\n\n**Examples**\n\n```js\nconsole.log(toRegex('(x+x+)+y'));\n//=> /^(?:(x+x+)+y)$/\n\n// The following would throw an error\ntoRegex('(x+x+)+y', {safe: true});\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [has-glob](https://www.npmjs.com/package/has-glob): Returns `true` if an array has a glob pattern. | [homepage](https://github.com/jonschlinkert/has-glob \"Returns `true` if an array has a glob pattern.\")\n* [is-glob](https://www.npmjs.com/package/is-glob): Returns `true` if the given string looks like a glob pattern or an extglob pattern… [more](https://github.com/jonschlinkert/is-glob) | [homepage](https://github.com/jonschlinkert/is-glob \"Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet\")\n* [path-regex](https://www.npmjs.com/package/path-regex): Regular expression for matching the parts of a file path. | [homepage](https://github.com/regexps/path-regex \"Regular expression for matching the parts of a file path.\")\n* [to-regex-range](https://www.npmjs.com/package/to-regex-range): Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than… [more](https://github.com/micromatch/to-regex-range) | [homepage](https://github.com/micromatch/to-regex-range \"Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.\")\n\n### Author\n\n**Jon Schlinkert**\n\n* [linkedin/in/jonschlinkert](https://linkedin.com/in/jonschlinkert)\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on February 24, 2018._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016-2018, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/to-regex/-/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce", "type": "tarball", "reference": "https://registry.yarnpkg.com/to-regex/-/to-regex-3.0.2.tgz", "hash": "13cfdd9b336552f30b51f33a8ae1b42a7a7599ce", "integrity": "sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw==", "registry": "npm", "packageName": "to-regex", "cacheIntegrity": "sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw== sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4="}, "registry": "npm", "hash": "13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"}