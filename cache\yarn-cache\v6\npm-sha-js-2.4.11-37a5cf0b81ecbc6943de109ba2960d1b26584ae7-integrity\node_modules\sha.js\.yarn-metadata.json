{"manifest": {"name": "sha.js", "description": "Streamable SHA hashes in pure javascript", "version": "2.4.11", "homepage": "https://github.com/crypto-browserify/sha.js", "repository": {"type": "git", "url": "git://github.com/crypto-browserify/sha.js.git"}, "dependencies": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}, "devDependencies": {"buffer": "~2.3.2", "hash-test-vectors": "^1.3.1", "standard": "^10.0.2", "tape": "~2.3.2", "typedarray": "0.0.6"}, "bin": {"sha.js": "bin.js"}, "scripts": {"prepublish": "npm ls && npm run unit", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "set -e; for t in test/*.js; do node $t; done;"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "license": "(MIT AND BSD-3-<PERSON><PERSON>)", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-sha-js-2.4.11-37a5cf0b81ecbc6943de109ba2960d1b26584ae7-integrity\\node_modules\\sha.js\\package.json", "readmeFilename": "README.md", "readme": "# sha.js\n[![NPM Package](https://img.shields.io/npm/v/sha.js.svg?style=flat-square)](https://www.npmjs.org/package/sha.js)\n[![Build Status](https://img.shields.io/travis/crypto-browserify/sha.js.svg?branch=master&style=flat-square)](https://travis-ci.org/crypto-browserify/sha.js)\n[![Dependency status](https://img.shields.io/david/crypto-browserify/sha.js.svg?style=flat-square)](https://david-dm.org/crypto-browserify/sha.js#info=dependencies)\n\n[![js-standard-style](https://cdn.rawgit.com/feross/standard/master/badge.svg)](https://github.com/feross/standard)\n\nNode style `SHA` on pure JavaScript.\n\n```js\nvar shajs = require('sha.js')\n\nconsole.log(shajs('sha256').update('42').digest('hex'))\n// => 73475cb40a568e8da8a045ced110137e159f890ac4da883b6b17dc651b3a8049\nconsole.log(new shajs.sha256().update('42').digest('hex'))\n// => 73475cb40a568e8da8a045ced110137e159f890ac4da883b6b17dc651b3a8049\n\nvar sha256stream = shajs('sha256')\nsha256stream.end('42')\nconsole.log(sha256stream.read().toString('hex'))\n// => 73475cb40a568e8da8a045ced110137e159f890ac4da883b6b17dc651b3a8049\n```\n\n## supported hashes\n`sha.js` currently implements:\n\n  - SHA (SHA-0) -- **legacy, do not use in new systems**\n  - SHA-1 -- **legacy, do not use in new systems**\n  - SHA-224\n  - SHA-256\n  - SHA-384\n  - SHA-512\n\n\n## Not an actual stream\nNote, this doesn't actually implement a stream, but wrapping this in a stream is trivial.\nIt does update incrementally, so you can hash things larger than RAM, as it uses a constant amount of memory (except when using base64 or utf8 encoding, see code comments).\n\n\n## Acknowledgements\nThis work is derived from Paul Johnston's [A JavaScript implementation of the Secure Hash Algorithm](http://pajhome.org.uk/crypt/md5/sha1.html).\n\n\n## LICENSE [MIT](LICENSE)\n", "licenseText": "Copyright (c) 2013-2018 sha.js contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPY<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n\nCopyright (c) 1998 - 2009, Paul Johnston & Contributors\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are met:\n\nRedistributions of source code must retain the above copyright notice, this\nlist of conditions and the following disclaimer.\n\nRedistributions in binary form must reproduce the above copyright notice, this\nlist of conditions and the following disclaimer in the documentation and/or\nother materials provided with the distribution.\n\nNeither the name of the author nor the names of its contributors may be used to\nendorse or promote products derived from this software without specific prior\nwritten permission.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\nANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\nWARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\nDISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR\nANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\nLOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON\nANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\nSOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/sha.js/-/sha.js-2.4.11.tgz#37a5cf0b81ecbc6943de109ba2960d1b26584ae7", "type": "tarball", "reference": "https://registry.yarnpkg.com/sha.js/-/sha.js-2.4.11.tgz", "hash": "37a5cf0b81ecbc6943de109ba2960d1b26584ae7", "integrity": "sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ==", "registry": "npm", "packageName": "sha.js", "cacheIntegrity": "sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ== sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc="}, "registry": "npm", "hash": "37a5cf0b81ecbc6943de109ba2960d1b26584ae7"}