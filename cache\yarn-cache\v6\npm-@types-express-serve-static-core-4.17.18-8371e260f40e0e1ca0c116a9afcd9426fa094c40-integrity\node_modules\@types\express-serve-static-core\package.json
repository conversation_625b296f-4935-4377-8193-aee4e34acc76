{"name": "@types/express-serve-static-core", "version": "4.17.18", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/19majkel94", "githubUsername": "19majkel94"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/kacepe", "githubUsername": "ka<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/micksatana", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/samijaber", "githubUsername": "sami<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JoseLion", "githubUsername": "JoseLion"}, {"name": "<PERSON>", "url": "https://github.com/dwrss", "githubUsername": "dwrss"}, {"name": "<PERSON>", "url": "https://github.com/andoshin11", "githubUsername": "andoshin11"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-serve-static-core"}, "scripts": {}, "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*"}, "typesPublisherContentHash": "60873d177898f88d23027366f6d52aec9d27759ca997fea4f7c6b9729356fc20", "typeScriptVersion": "3.3"}