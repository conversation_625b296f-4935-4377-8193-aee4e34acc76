fx_version 'cerulean'
game 'gta5'

author 'DDCZ'
description 'Dynamic Weather-Aware NPC & Player Health System (DWANPHS)'
version '1.0.0'

shared_scripts {
    '@qb-core/shared/locale.lua',
    'locales/en.lua',
    'shared/utils.lua',
    'config.lua'
}

client_scripts {
    'client/main.lua',
    'client/npc.lua',
    'client/player.lua',
    'client/animations.lua'
}

server_scripts {
    '@oxmysql/lib/MySQL.lua',
    'server/main.lua',
    'server/database.lua',
    'server/items.lua',
    'server/commands.lua',
    'test_commands.lua'
}

dependencies {
    'qb-core',
    'qb-weathersync',
    'oxmysql'
}

lua54 'yes'
