{"name": "@types/connect", "version": "3.4.34", "description": "TypeScript definitions for connect", "license": "MIT", "contributors": [{"name": "Maxime LUCE", "url": "https://github.com/SomaticIT", "githubUsername": "SomaticIT"}, {"name": "<PERSON>", "url": "https://github.com/EvanHahn", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/connect"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "2fd2f6a2f7b9371cdb60b971639b4d26989e6035dc30fb0ad12e72645dcb002d", "typeScriptVersion": "3.3"}