{"name": "@types/keygrip", "version": "1.0.2", "description": "TypeScript definitions for keygrip", "license": "MIT", "contributors": [{"name": "j<PERSON><PERSON>", "url": "https://github.com/jkeylu", "githubUsername": "j<PERSON>lu"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/keygrip"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "85791e9272f5401dc3416aead8d95149b11fbcc20d9d5b22ef8f75aef9021382", "typeScriptVersion": "2.8"}