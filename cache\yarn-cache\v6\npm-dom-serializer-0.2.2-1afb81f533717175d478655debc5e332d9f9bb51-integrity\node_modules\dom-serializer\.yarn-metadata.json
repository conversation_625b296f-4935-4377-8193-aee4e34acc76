{"manifest": {"name": "dom-serializer", "version": "0.2.2", "description": "render dom nodes to string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "./index.js", "files": ["index.js", "index.d.ts", "foreignNames.json"], "dependencies": {"domelementtype": "^2.0.1", "entities": "^2.0.0"}, "devDependencies": {"cheerio": "^1.0.0-rc.2", "expect.js": "~0.3.1", "htmlparser2": "^3.10.0", "lodash": "^4.17.11", "mocha": "^6.2.0", "xyz": "^3.0.0"}, "scripts": {"test": "mocha test.js"}, "prettier": {"singleQuote": true}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-dom-serializer-0.2.2-****************************************-integrity\\node_modules\\dom-serializer\\package.json", "readmeFilename": "README.md", "readme": "Renders a DOM node or an array of DOM nodes to a string.\n", "licenseText": "License\n\n(The MIT License)\n\nCopyright (c) 2014 The cheeriojs contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the 'Software'), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/dom-serializer/-/dom-serializer-0.2.2.tgz#****************************************", "type": "tarball", "reference": "https://registry.yarnpkg.com/dom-serializer/-/dom-serializer-0.2.2.tgz", "hash": "****************************************", "integrity": "sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g==", "registry": "npm", "packageName": "dom-serializer", "cacheIntegrity": "sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g== sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E="}, "registry": "npm", "hash": "****************************************"}