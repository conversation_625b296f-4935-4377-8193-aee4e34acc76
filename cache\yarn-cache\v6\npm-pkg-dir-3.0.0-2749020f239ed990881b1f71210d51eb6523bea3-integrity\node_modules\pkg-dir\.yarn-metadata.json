{"manifest": {"name": "pkg-dir", "version": "3.0.0", "description": "Find the root directory of a Node.js project or npm package", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/pkg-dir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["package", "json", "root", "npm", "entry", "find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "dependencies": {"find-up": "^3.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-pkg-dir-3.0.0-2749020f239ed990881b1f71210d51eb6523bea3-integrity\\node_modules\\pkg-dir\\package.json", "readmeFilename": "readme.md", "readme": "# pkg-dir [![Build Status](https://travis-ci.org/sindresorhus/pkg-dir.svg?branch=master)](https://travis-ci.org/sindresorhus/pkg-dir)\n\n> Find the root directory of a Node.js project or npm package\n\n\n## Install\n\n```\n$ npm install pkg-dir\n```\n\n\n## Usage\n\n```\n/\n└── Users\n    └── sindresorhus\n        └── foo\n            ├── package.json\n            └── bar\n                ├── baz\n                └── example.js\n```\n\n```js\n// example.js\nconst pkgDir = require('pkg-dir');\n\n(async () => {\n\tconst rootDir = await pkgDir(__dirname);\n\n\tconsole.log(rootDir);\n\t//=> '/Users/<USER>/foo'\n})();\n```\n\n\n## API\n\n### pkgDir([cwd])\n\nReturns a `Promise` for either the project root path or `null` if it couldn't be found.\n\n### pkgDir.sync([cwd])\n\nReturns the project root path or `null`.\n\n#### cwd\n\nType: `string`<br>\nDefault: `process.cwd()`\n\nDirectory to start from.\n\n\n## Related\n\n- [pkg-dir-cli](https://github.com/sindresorhus/pkg-dir-cli) - CLI for this module\n- [pkg-up](https://github.com/sindresorhus/pkg-up) - Find the closest package.json file\n- [find-up](https://github.com/sindresorhus/find-up) - Find a file by walking up parent directories\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com)\n", "licenseText": "MIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/pkg-dir/-/pkg-dir-3.0.0.tgz#2749020f239ed990881b1f71210d51eb6523bea3", "type": "tarball", "reference": "https://registry.yarnpkg.com/pkg-dir/-/pkg-dir-3.0.0.tgz", "hash": "2749020f239ed990881b1f71210d51eb6523bea3", "integrity": "sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw==", "registry": "npm", "packageName": "pkg-dir", "cacheIntegrity": "sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw== sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM="}, "registry": "npm", "hash": "2749020f239ed990881b1f71210d51eb6523bea3"}