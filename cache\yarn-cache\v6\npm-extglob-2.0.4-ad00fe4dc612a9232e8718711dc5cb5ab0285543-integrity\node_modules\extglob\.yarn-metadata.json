{"manifest": {"name": "extglob", "description": "Extended glob support for JavaScript. Adds (almost) the expressive power of regular expressions to glob patterns.", "version": "2.0.4", "homepage": "https://github.com/micromatch/extglob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "Devon Govett", "url": "http://badassjs.com"}, {"name": "<PERSON><PERSON>", "url": "https://www.isiahmeadows.com"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "http://mattbierner.com"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://shinnn.github.io"}], "repository": {"type": "git", "url": "https://github.com/micromatch/extglob.git"}, "bugs": {"url": "https://github.com/micromatch/extglob/issues"}, "license": "MIT", "files": ["index.js", "lib"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-eslint": "^4.0.0", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.1", "helper-changelog": "^0.3.0", "is-windows": "^1.0.1", "micromatch": "^3.0.4", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.0", "multimatch": "^2.1.0"}, "keywords": ["bash", "extended", "extglob", "glob", "globbing", "ksh", "match", "pattern", "patterns", "regex", "test", "wildcard"], "lintDeps": {"devDependencies": {"files": {"options": {"ignore": ["benchmark/**/*.js"]}}}}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "related": {"list": ["braces", "expand-brackets", "expand-range", "fill-range", "micromatch"]}, "helpers": ["helper-changelog"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-extglob-2.0.4-ad00fe4dc612a9232e8718711dc5cb5ab0285543-integrity\\node_modules\\extglob\\package.json", "readmeFilename": "README.md", "readme": "# extglob [![NPM version](https://img.shields.io/npm/v/extglob.svg?style=flat)](https://www.npmjs.com/package/extglob) [![NPM monthly downloads](https://img.shields.io/npm/dm/extglob.svg?style=flat)](https://npmjs.org/package/extglob) [![NPM total downloads](https://img.shields.io/npm/dt/extglob.svg?style=flat)](https://npmjs.org/package/extglob) [![Linux Build Status](https://img.shields.io/travis/micromatch/extglob.svg?style=flat&label=Travis)](https://travis-ci.org/micromatch/extglob) [![Windows Build Status](https://img.shields.io/appveyor/ci/micromatch/extglob.svg?style=flat&label=AppVeyor)](https://ci.appveyor.com/project/micromatch/extglob)\n\n> Extended glob support for JavaScript. Adds (almost) the expressive power of regular expressions to glob patterns.\n\nPlease consider following this project's author, [Jon Schlinkert](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save extglob\n```\n\n* Convert an extglob string to a regex-compatible string.\n* More complete (and correct) support than [minimatch](https://github.com/isaacs/minimatch) (minimatch fails a large percentage of the extglob tests)\n* Handles [negation patterns](#extglob-patterns)\n* Handles [nested patterns](#extglob-patterns)\n* Organized code base, easy to maintain and make changes when edge cases arise\n* As you can see by the [benchmarks](#benchmarks), extglob doesn't pay with speed for it's completeness, accuracy and quality.\n\n**Heads up!**: This library only supports extglobs, to handle full glob patterns and other extended globbing features use [micromatch](https://github.com/jonschlinkert/micromatch) instead.\n\n## Usage\n\nThe main export is a function that takes a string and options, and returns an object with the parsed AST and the compiled `.output`, which is a regex-compatible string that can be used for matching.\n\n```js\nvar extglob = require('extglob');\nconsole.log(extglob('!(xyz)*.js'));\n```\n\n## Extglob cheatsheet\n\nExtended globbing patterns can be defined as follows (as described by the [bash man page](https://www.gnu.org/software/bash/manual/html_node/Pattern-Matching.html)):\n\n| **pattern** | **regex equivalent** | **description** | \n| --- | --- | --- |\n| `?(pattern-list)` | `(...|...)?` | Matches zero or one occurrence of the given pattern(s) |\n| `*(pattern-list)` | `(...|...)*` | Matches zero or more occurrences of the given pattern(s) |\n| `+(pattern-list)` | `(...|...)+` | Matches one or more occurrences of the given pattern(s) |\n| `@(pattern-list)` | `(...|...)` <sup class=\"footnote-ref\"><a href=\"#fn1\" id=\"fnref1\">[1]</a></sup> | Matches one of the given pattern(s) |\n| `!(pattern-list)` | N/A | Matches anything except one of the given pattern(s) |\n\n## API\n\n### [extglob](index.js#L36)\n\nConvert the given `extglob` pattern into a regex-compatible string. Returns an object with the compiled result and the parsed AST.\n\n**Params**\n\n* `pattern` **{String}**\n* `options` **{Object}**\n* `returns` **{String}**\n\n**Example**\n\n```js\nvar extglob = require('extglob');\nconsole.log(extglob('*.!(*a)'));\n//=> '(?!\\\\.)[^/]*?\\\\.(?!(?!\\\\.)[^/]*?a\\\\b).*?'\n```\n\n### [.match](index.js#L56)\n\nTakes an array of strings and an extglob pattern and returns a new array that contains only the strings that match the pattern.\n\n**Params**\n\n* `list` **{Array}**: Array of strings to match\n* `pattern` **{String}**: Extglob pattern\n* `options` **{Object}**\n* `returns` **{Array}**: Returns an array of matches\n\n**Example**\n\n```js\nvar extglob = require('extglob');\nconsole.log(extglob.match(['a.a', 'a.b', 'a.c'], '*.!(*a)'));\n//=> ['a.b', 'a.c']\n```\n\n### [.isMatch](index.js#L111)\n\nReturns true if the specified `string` matches the given extglob `pattern`.\n\n**Params**\n\n* `string` **{String}**: String to match\n* `pattern` **{String}**: Extglob pattern\n* `options` **{String}**\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nvar extglob = require('extglob');\n\nconsole.log(extglob.isMatch('a.a', '*.!(*a)'));\n//=> false\nconsole.log(extglob.isMatch('a.b', '*.!(*a)'));\n//=> true\n```\n\n### [.contains](index.js#L150)\n\nReturns true if the given `string` contains the given pattern. Similar to `.isMatch` but the pattern can match any part of the string.\n\n**Params**\n\n* `str` **{String}**: The string to match.\n* `pattern` **{String}**: Glob pattern to use for matching.\n* `options` **{Object}**\n* `returns` **{Boolean}**: Returns true if the patter matches any part of `str`.\n\n**Example**\n\n```js\nvar extglob = require('extglob');\nconsole.log(extglob.contains('aa/bb/cc', '*b'));\n//=> true\nconsole.log(extglob.contains('aa/bb/cc', '*d'));\n//=> false\n```\n\n### [.matcher](index.js#L184)\n\nTakes an extglob pattern and returns a matcher function. The returned function takes the string to match as its only argument.\n\n**Params**\n\n* `pattern` **{String}**: Extglob pattern\n* `options` **{String}**\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nvar extglob = require('extglob');\nvar isMatch = extglob.matcher('*.!(*a)');\n\nconsole.log(isMatch('a.a'));\n//=> false\nconsole.log(isMatch('a.b'));\n//=> true\n```\n\n### [.create](index.js#L214)\n\nConvert the given `extglob` pattern into a regex-compatible string. Returns an object with the compiled result and the parsed AST.\n\n**Params**\n\n* `str` **{String}**\n* `options` **{Object}**\n* `returns` **{String}**\n\n**Example**\n\n```js\nvar extglob = require('extglob');\nconsole.log(extglob.create('*.!(*a)').output);\n//=> '(?!\\\\.)[^/]*?\\\\.(?!(?!\\\\.)[^/]*?a\\\\b).*?'\n```\n\n### [.capture](index.js#L248)\n\nReturns an array of matches captured by `pattern` in `string`, or `null` if the pattern did not match.\n\n**Params**\n\n* `pattern` **{String}**: Glob pattern to use for matching.\n* `string` **{String}**: String to match\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Boolean}**: Returns an array of captures if the string matches the glob pattern, otherwise `null`.\n\n**Example**\n\n```js\nvar extglob = require('extglob');\nextglob.capture(pattern, string[, options]);\n\nconsole.log(extglob.capture('test/*.js', 'test/foo.js'));\n//=> ['foo']\nconsole.log(extglob.capture('test/*.js', 'foo/bar.css'));\n//=> null\n```\n\n### [.makeRe](index.js#L281)\n\nCreate a regular expression from the given `pattern` and `options`.\n\n**Params**\n\n* `pattern` **{String}**: The pattern to convert to regex.\n* `options` **{Object}**\n* `returns` **{RegExp}**\n\n**Example**\n\n```js\nvar extglob = require('extglob');\nvar re = extglob.makeRe('*.!(*a)');\nconsole.log(re);\n//=> /^[^\\/]*?\\.(?![^\\/]*?a)[^\\/]*?$/\n```\n\n## Options\n\nAvailable options are based on the options from Bash (and the option names used in bash).\n\n### options.nullglob\n\n**Type**: `boolean`\n\n**Default**: `undefined`\n\nWhen enabled, the pattern itself will be returned when no matches are found.\n\n### options.nonull\n\nAlias for [options.nullglob](#optionsnullglob), included for parity with minimatch.\n\n### options.cache\n\n**Type**: `boolean`\n\n**Default**: `undefined`\n\nFunctions are memoized based on the given glob patterns and options. Disable memoization by setting `options.cache` to false.\n\n### options.failglob\n\n**Type**: `boolean`\n\n**Default**: `undefined`\n\nThrow an error is no matches are found.\n\n## Benchmarks\n\nLast run on December 21, 2017\n\n```sh\n# negation-nested (49 bytes)\n  extglob x 2,228,255 ops/sec ±0.98% (89 runs sampled)\n  minimatch x 207,875 ops/sec ±0.61% (91 runs sampled)\n\n  fastest is extglob (by 1072% avg)\n\n# negation-simple (43 bytes)\n  extglob x 2,205,668 ops/sec ±1.00% (91 runs sampled)\n  minimatch x 311,923 ops/sec ±1.25% (91 runs sampled)\n\n  fastest is extglob (by 707% avg)\n\n# range-false (57 bytes)\n  extglob x 2,263,877 ops/sec ±0.40% (94 runs sampled)\n  minimatch x 271,372 ops/sec ±1.02% (91 runs sampled)\n\n  fastest is extglob (by 834% avg)\n\n# range-true (56 bytes)\n  extglob x 2,161,891 ops/sec ±0.41% (92 runs sampled)\n  minimatch x 268,265 ops/sec ±1.17% (91 runs sampled)\n\n  fastest is extglob (by 806% avg)\n\n# star-simple (46 bytes)\n  extglob x 2,211,081 ops/sec ±0.49% (92 runs sampled)\n  minimatch x 343,319 ops/sec ±0.59% (91 runs sampled)\n\n  fastest is extglob (by 644% avg)\n\n```\n\n## Differences from Bash\n\nThis library has complete parity with Bash 4.3 with only a couple of minor differences.\n\n* In some cases Bash returns true if the given string \"contains\" the pattern, whereas this library returns true if the string is an exact match for the pattern. You can relax this by setting `options.contains` to true.\n* This library is more accurate than Bash and thus does not fail some of the tests that Bash 4.3 still lists as failing in their unit tests\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [braces](https://www.npmjs.com/package/braces): Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support… [more](https://github.com/micromatch/braces) | [homepage](https://github.com/micromatch/braces \"Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.\")\n* [expand-brackets](https://www.npmjs.com/package/expand-brackets): Expand POSIX bracket expressions (character classes) in glob patterns. | [homepage](https://github.com/jonschlinkert/expand-brackets \"Expand POSIX bracket expressions (character classes) in glob patterns.\")\n* [expand-range](https://www.npmjs.com/package/expand-range): Fast, bash-like range expansion. Expand a range of numbers or letters, uppercase or lowercase. Used… [more](https://github.com/jonschlinkert/expand-range) | [homepage](https://github.com/jonschlinkert/expand-range \"Fast, bash-like range expansion. Expand a range of numbers or letters, uppercase or lowercase. Used by [micromatch].\")\n* [fill-range](https://www.npmjs.com/package/fill-range): Fill in a range of numbers or letters, optionally passing an increment or `step` to… [more](https://github.com/jonschlinkert/fill-range) | [homepage](https://github.com/jonschlinkert/fill-range \"Fill in a range of numbers or letters, optionally passing an increment or `step` to use, or create a regex-compatible range with `options.toRegex`\")\n* [micromatch](https://www.npmjs.com/package/micromatch): Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. | [homepage](https://github.com/micromatch/micromatch \"Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.\")\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 49 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 2 | [isiahmeadows](https://github.com/isiahmeadows) |\n| 1 | [doowb](https://github.com/doowb) |\n| 1 | [devongovett](https://github.com/devongovett) |\n| 1 | [mjbvz](https://github.com/mjbvz) |\n| 1 | [shinnn](https://github.com/shinnn) |\n\n### Author\n\n**Jon Schlinkert**\n\n* [linkedin/in/jonschlinkert](https://linkedin.com/in/jonschlinkert)\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on December 21, 2017._\n\n<hr class=\"footnotes-sep\">\n<section class=\"footnotes\">\n<ol class=\"footnotes-list\">\n<li id=\"fn1\"  class=\"footnote-item\">`@` isn \"'t a RegEx character.\" <a href=\"#fnref1\" class=\"footnote-backref\">↩</a>\n\n</li>\n</ol>\n</section>", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015-2017, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/extglob/-/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543", "type": "tarball", "reference": "https://registry.yarnpkg.com/extglob/-/extglob-2.0.4.tgz", "hash": "ad00fe4dc612a9232e8718711dc5cb5ab0285543", "integrity": "sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw==", "registry": "npm", "packageName": "extglob", "cacheIntegrity": "sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw== sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM="}, "registry": "npm", "hash": "ad00fe4dc612a9232e8718711dc5cb5ab0285543"}