{"manifest": {"name": "@webassemblyjs/wasm-parser", "version": "1.7.11", "keywords": ["webassembly", "javascript", "ast", "parser", "wasm"], "description": "WebAssembly binary format parser", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "mocha"}, "author": {"name": "<PERSON>"}, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/helper-api-error": "1.7.11", "@webassemblyjs/helper-wasm-bytecode": "1.7.11", "@webassemblyjs/ieee754": "1.7.11", "@webassemblyjs/leb128": "1.7.11", "@webassemblyjs/utf8": "1.7.11"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "devDependencies": {"@webassemblyjs/helper-buffer": "1.7.11", "@webassemblyjs/helper-test-framework": "1.7.11", "@webassemblyjs/wasm-gen": "1.7.11", "@webassemblyjs/wast-parser": "1.7.11", "wabt": "1.0.0-nightly.20180421"}, "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@webassemblyjs-wasm-parser-1.7.11-6e3d20fa6a3519f6b084ef9391ad58211efb0a1a-integrity\\node_modules\\@webassemblyjs\\wasm-parser\\package.json", "readmeFilename": "README.md", "readme": "# @webassemblyjs/wasm-parser\n\n> WebAssembly binary format parser\n\n## Installation\n\n```sh\nyarn add @webassemblyjs/wasm-parser\n```\n\n## Usage\n\n```js\nimport { decode } from \"@webassemblyjs/wasm-parser\";\n\nconst decoderOpts = {};\n\nconst ast = decode(binary, decoderOpts);\n```\n\n### Decoder options\n\n- `dump`: print dump information while decoding (default `false`)\n- `ignoreCodeSection`: ignore the code section (default `false`)\n- `ignoreDataSection`: ignore the data section (default `false`)\n\n", "licenseText": "MIT License\n\nCopyright (c) 2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.11.tgz#6e3d20fa6a3519f6b084ef9391ad58211efb0a1a", "type": "tarball", "reference": "https://registry.yarnpkg.com/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.11.tgz", "hash": "6e3d20fa6a3519f6b084ef9391ad58211efb0a1a", "integrity": "sha512-6lmXRTrrZjYD8Ng8xRyvyXQJYUQKYSXhJqXOBLw24rdiXsHAOlvw5PhesjdcaMadU/pyPQOJ5dHreMjBxwnQKg==", "registry": "npm", "packageName": "@webassemblyjs/wasm-parser", "cacheIntegrity": "sha512-6lmXRTrrZjYD8Ng8xRyvyXQJYUQKYSXhJqXOBLw24rdiXsHAOlvw5PhesjdcaMadU/pyPQOJ5dHreMjBxwnQKg== sha1-bj0g+mo1GfawhO+Tka1YIR77Cho="}, "registry": "npm", "hash": "6e3d20fa6a3519f6b084ef9391ad58211efb0a1a"}