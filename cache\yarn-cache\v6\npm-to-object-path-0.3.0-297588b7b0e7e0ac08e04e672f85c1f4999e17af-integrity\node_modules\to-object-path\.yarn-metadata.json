{"manifest": {"name": "to-object-path", "description": "Create an object path from a list or array of strings.", "version": "0.3.0", "homepage": "https://github.com/jonschlinkert/to-object-path", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/to-object-path.git"}, "bugs": {"url": "https://github.com/jonschlinkert/to-object-path/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"kind-of": "^3.0.2"}, "devDependencies": {"base": "^0.6.7", "mocha": "*"}, "keywords": ["dot", "nested", "notation", "object", "path", "stringify"], "verb": {"related": {"list": ["get-value", "set-value", "has-value", "omit-value", "unset-value"]}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-to-object-path-0.3.0-297588b7b0e7e0ac08e04e672f85c1f4999e17af-integrity\\node_modules\\to-object-path\\package.json", "readmeFilename": "README.md", "readme": "# to-object-path [![NPM version](https://badge.fury.io/js/to-object-path.svg)](http://badge.fury.io/js/to-object-path)\n\n> Create an object path from a list or array of strings.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/)\n\n```sh\n$ npm i to-object-path --save\n```\n\n## Usage\n\n```js\nvar toPath = require('to-object-path');\n\ntoPath('foo', 'bar', 'baz');\ntoPath('foo', ['bar', 'baz']);\n//=> 'foo.bar.baz'\n```\n\nAlso supports passing an arguments object (without having to slice args):\n\n```js\nfunction foo()\n  return toPath(arguments);\n}\n\nfoo('foo', 'bar', 'baz');\nfoo('foo', ['bar', 'baz']);\n//=> 'foo.bar.baz'\n```\n\nVisit the [example](./example.js) to see how this could be used in an application.\n\n## Related projects\n\n* [get-value](https://www.npmjs.com/package/get-value): Use property paths (`  a.b.c`) to get a nested value from an object. | [homepage](https://github.com/jonschlinkert/get-value)\n* [has-value](https://www.npmjs.com/package/has-value): Returns true if a value exists, false if empty. Works with deeply nested values using… [more](https://www.npmjs.com/package/has-value) | [homepage](https://github.com/jonschlinkert/has-value)\n* [omit-value](https://www.npmjs.com/package/omit-value): Omit properties from an object or deeply nested property of an object using object path… [more](https://www.npmjs.com/package/omit-value) | [homepage](https://github.com/jonschlinkert/omit-value)\n* [set-value](https://www.npmjs.com/package/set-value): Create nested values and any intermediaries using dot notation (`'a.b.c'`) paths. | [homepage](https://github.com/jonschlinkert/set-value)\n* [unset-value](https://www.npmjs.com/package/unset-value): Delete nested properties from an object using dot notation. | [homepage](https://github.com/jonschlinkert/unset-value)\n\n## Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm i -d && npm test\n```\n\n## Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/to-object-path/issues/new).\n\n## Author\n\n**Jon Schlinkert**\n\n+ [github/jonschlinkert](https://github.com/jonschlinkert)\n+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n## License\n\nCopyright © 2015 Jon Schlinkert\nReleased under the MIT license.\n\n***\n\n_This file was generated by [verb-cli](https://github.com/assemble/verb-cli) on October 28, 2015._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015-2016, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/to-object-path/-/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af", "type": "tarball", "reference": "https://registry.yarnpkg.com/to-object-path/-/to-object-path-0.3.0.tgz", "hash": "297588b7b0e7e0ac08e04e672f85c1f4999e17af", "integrity": "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=", "registry": "npm", "packageName": "to-object-path", "cacheIntegrity": "sha512-9mWHdnGRuh3onocaHzukyvCZhzvr6tiflAy/JRFXcJX0TjgfWA9pk9t8CMbzmBE4Jfw58pXbkngtBtqYxzNEyg== sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68="}, "registry": "npm", "hash": "297588b7b0e7e0ac08e04e672f85c1f4999e17af"}