{"manifest": {"name": "base", "description": "base is the foundation for creating modular, unit testable and highly pluggable node.js applications, starting with a handful of common methods, like `set`, `get`, `del` and `use`.", "version": "0.11.2", "homepage": "https://github.com/node-base/base", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "maintainers": [{"name": "<PERSON>", "url": "https://github.com/doowb"}, {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}], "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "https://github.com/criticalmash"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "tunnckoCore", "url": "https://i.am.charlike.online"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}], "repository": {"type": "git", "url": "https://github.com/node-base/base.git"}, "bugs": {"url": "https://github.com/node-base/base/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "devDependencies": {"gulp": "^3.9.1", "gulp-eslint": "^4.0.0", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "helper-coverage": "^0.1.3", "mocha": "^3.5.0", "should": "^13.0.1", "through2": "^2.0.3", "verb-generate-readme": "^0.6.0"}, "keywords": ["base", "boilerplate", "cache", "del", "get", "inherit", "methods", "set", "starter", "unset", "visit"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "helpers": ["helper-coverage"], "related": {"description": "There are a number of different plugins available for extending base. Let us know if you create your own!", "hightlight": "generate", "list": ["base-cwd", "base-data", "base-fs", "base-generators", "base-option", "base-pipeline", "base-pkg", "base-plugins", "base-questions", "base-store", "base-task"]}, "reflinks": ["assemble", "boilerplate", "cache-base", "class-utils", "generate", "scaffold", "static-extend", "verb"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-base-0.11.2-7bde5ced145b6d551a90db87f83c558b4eb48a8f-integrity\\node_modules\\base\\package.json", "readmeFilename": "README.md", "readme": "<p align=\"center\">\n  <a href=\"https://github.com/node-base/base\">\n    <img height=\"250\" width=\"250\" src=\"https://raw.githubusercontent.com/node-base/base/master/docs/logo.png\">\n  </a>\n</p>\n\n# base [![NPM version](https://img.shields.io/npm/v/base.svg?style=flat)](https://www.npmjs.com/package/base) [![NPM monthly downloads](https://img.shields.io/npm/dm/base.svg?style=flat)](https://npmjs.org/package/base)  [![NPM total downloads](https://img.shields.io/npm/dt/base.svg?style=flat)](https://npmjs.org/package/base) [![Linux Build Status](https://img.shields.io/travis/node-base/base.svg?style=flat&label=Travis)](https://travis-ci.org/node-base/base)\n\n> base is the foundation for creating modular, unit testable and highly pluggable node.js applications, starting with a handful of common methods, like `set`, `get`, `del` and `use`.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save base\n```\n\n## What is Base?\n\nBase is a framework for rapidly creating high quality node.js applications, using plugins like building blocks.\n\n### Guiding principles\n\nThe core team follows these principles to help guide API decisions:\n\n* **Compact API surface**: The smaller the API surface, the easier the library will be to learn and use.\n* **Easy to extend**: Implementors can use any npm package, and write plugins in pure JavaScript. If you're building complex apps, Base simplifies inheritance.\n* **Easy to test**: No special setup should be required to unit test `Base` or base plugins\n\n### Minimal API surface\n\n[The API](#api) was designed to provide only the minimum necessary functionality for creating a useful application, with or without [plugins](#plugins).\n\n**Base core**\n\nBase itself ships with only a handful of [useful methods](#api), such as:\n\n* `.set`: for setting values on the instance\n* `.get`: for getting values from the instance\n* `.has`: to check if a property exists on the instance\n* `.define`: for setting non-enumerable values on the instance\n* `.use`: for adding plugins\n\n**Be generic**\n\nWhen deciding on method to add or remove, we try to answer these questions:\n\n1. Will all or most Base applications need this method?\n2. Will this method encourage practices or enforce conventions that are beneficial to implementors?\n3. Can or should this be done in a plugin instead?\n\n### Composability\n\n**Plugin system**\n\nIt couldn't be easier to extend Base with any features or custom functionality you can think of.\n\nBase plugins are just functions that take an instance of `Base`:\n\n```js\nvar base = new Base();\n\nfunction plugin(base) {\n  // do plugin stuff, in pure JavaScript\n}\n// use the plugin\nbase.use(plugin);\n```\n\n**Inheritance**\n\nEasily inherit Base using `.extend`:\n\n```js\nvar Base = require('base');\n\nfunction MyApp() {\n  Base.call(this);\n}\nBase.extend(MyApp);\n\nvar app = new MyApp();\napp.set('a', 'b');\napp.get('a');\n//=> 'b';\n```\n\n**Inherit or instantiate with a namespace**\n\nBy default, the `.get`, `.set` and `.has` methods set and get values from the root of the `base` instance. You can customize this using the `.namespace` method exposed on the exported function. For example:\n\n```js\nvar Base = require('base');\n// get and set values on the `base.cache` object\nvar base = Base.namespace('cache');\n\nvar app = base();\napp.set('foo', 'bar');\nconsole.log(app.cache.foo);\n//=> 'bar'\n```\n\n## API\n\n**Usage**\n\n```js\nvar Base = require('base');\nvar app = new Base();\napp.set('foo', 'bar');\nconsole.log(app.foo);\n//=> 'bar'\n```\n\n### [Base](index.js#L44)\n\nCreate an instance of `Base` with the given `config` and `options`.\n\n**Params**\n\n* `config` **{Object}**: If supplied, this object is passed to [cache-base](https://github.com/jonschlinkert/cache-base) to merge onto the the instance upon instantiation.\n* `options` **{Object}**: If supplied, this object is used to initialize the `base.options` object.\n\n**Example**\n\n```js\n// initialize with `config` and `options`\nvar app = new Base({isApp: true}, {abc: true});\napp.set('foo', 'bar');\n\n// values defined with the given `config` object will be on the root of the instance\nconsole.log(app.baz); //=> undefined\nconsole.log(app.foo); //=> 'bar'\n// or use `.get`\nconsole.log(app.get('isApp')); //=> true\nconsole.log(app.get('foo')); //=> 'bar'\n\n// values defined with the given `options` object will be on `app.options\nconsole.log(app.options.abc); //=> true\n```\n\n### [.is](index.js#L107)\n\nSet the given `name` on `app._name` and `app.is*` properties. Used for doing lookups in plugins.\n\n**Params**\n\n* `name` **{String}**\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\napp.is('foo');\nconsole.log(app._name);\n//=> 'foo'\nconsole.log(app.isFoo);\n//=> true\napp.is('bar');\nconsole.log(app.isFoo);\n//=> true\nconsole.log(app.isBar);\n//=> true\nconsole.log(app._name);\n//=> 'bar'\n```\n\n### [.isRegistered](index.js#L145)\n\nReturns true if a plugin has already been registered on an instance.\n\nPlugin implementors are encouraged to use this first thing in a plugin\nto prevent the plugin from being called more than once on the same\ninstance.\n\n**Params**\n\n* `name` **{String}**: The plugin name.\n* `register` **{Boolean}**: If the plugin if not already registered, to record it as being registered pass `true` as the second argument.\n* `returns` **{Boolean}**: Returns true if a plugin is already registered.\n\n**Events**\n\n* `emits`: `plugin` Emits the name of the plugin being registered. Useful for unit tests, to ensure plugins are only registered once.\n\n**Example**\n\n```js\nvar base = new Base();\nbase.use(function(app) {\n  if (app.isRegistered('myPlugin')) return;\n  // do stuff to `app`\n});\n\n// to also record the plugin as being registered\nbase.use(function(app) {\n  if (app.isRegistered('myPlugin', true)) return;\n  // do stuff to `app`\n});\n```\n\n### [.use](index.js#L175)\n\nDefine a plugin function to be called immediately upon init. Plugins are chainable and expose the following arguments to the plugin function:\n\n* `app`: the current instance of `Base`\n* `base`: the [first ancestor instance](#base) of `Base`\n\n**Params**\n\n* `fn` **{Function}**: plugin function to call\n* `returns` **{Object}**: Returns the item instance for chaining.\n\n**Example**\n\n```js\nvar app = new Base()\n  .use(foo)\n  .use(bar)\n  .use(baz)\n```\n\n### [.define](index.js#L197)\n\nThe `.define` method is used for adding non-enumerable property on the instance. Dot-notation is **not supported** with `define`.\n\n**Params**\n\n* `key` **{String}**: The name of the property to define.\n* `value` **{any}**\n* `returns` **{Object}**: Returns the instance for chaining.\n\n**Example**\n\n```js\n// arbitrary `render` function using lodash `template`\napp.define('render', function(str, locals) {\n  return _.template(str)(locals);\n});\n```\n\n### [.mixin](index.js#L222)\n\nMix property `key` onto the Base prototype. If base is inherited using `Base.extend` this method will be overridden by a new `mixin` method that will only add properties to the prototype of the inheriting application.\n\n**Params**\n\n* `key` **{String}**\n* `val` **{Object|Array}**\n* `returns` **{Object}**: Returns the `base` instance for chaining.\n\n**Example**\n\n```js\napp.mixin('foo', function() {\n  // do stuff\n});\n```\n\n### [.base](index.js#L268)\n\nGetter/setter used when creating nested instances of `Base`, for storing a reference to the first ancestor instance. This works by setting an instance of `Base` on the `parent` property of a \"child\" instance. The `base` property defaults to the current instance if no `parent` property is defined.\n\n**Example**\n\n```js\n// create an instance of `Base`, this is our first (\"base\") instance\nvar first = new Base();\nfirst.foo = 'bar'; // arbitrary property, to make it easier to see what's happening later\n\n// create another instance\nvar second = new Base();\n// create a reference to the first instance (`first`)\nsecond.parent = first;\n\n// create another instance\nvar third = new Base();\n// create a reference to the previous instance (`second`)\n// repeat this pattern every time a \"child\" instance is created\nthird.parent = second;\n\n// we can always access the first instance using the `base` property\nconsole.log(first.base.foo);\n//=> 'bar'\nconsole.log(second.base.foo);\n//=> 'bar'\nconsole.log(third.base.foo);\n//=> 'bar'\n// and now you know how to get to third base ;)\n```\n\n### [#use](index.js#L293)\n\nStatic method for adding global plugin functions that will be added to an instance when created.\n\n**Params**\n\n* `fn` **{Function}**: Plugin function to use on each instance.\n* `returns` **{Object}**: Returns the `Base` constructor for chaining\n\n**Example**\n\n```js\nBase.use(function(app) {\n  app.foo = 'bar';\n});\nvar app = new Base();\nconsole.log(app.foo);\n//=> 'bar'\n```\n\n### [#extend](index.js#L337)\n\nStatic method for inheriting the prototype and static methods of the `Base` class. This method greatly simplifies the process of creating inheritance-based applications. See [static-extend](https://github.com/jonschlinkert/static-extend) for more details.\n\n**Params**\n\n* `Ctor` **{Function}**: constructor to extend\n* `methods` **{Object}**: Optional prototype properties to mix in.\n* `returns` **{Object}**: Returns the `Base` constructor for chaining\n\n**Example**\n\n```js\nvar extend = cu.extend(Parent);\nParent.extend(Child);\n\n// optional methods\nParent.extend(Child, {\n  foo: function() {},\n  bar: function() {}\n});\n```\n\n### [#mixin](index.js#L379)\n\nUsed for adding methods to the `Base` prototype, and/or to the prototype of child instances. When a mixin function returns a function, the returned function is pushed onto the `.mixins` array, making it available to be used on inheriting classes whenever `Base.mixins()` is called (e.g. `Base.mixins(Child)`).\n\n**Params**\n\n* `fn` **{Function}**: Function to call\n* `returns` **{Object}**: Returns the `Base` constructor for chaining\n\n**Example**\n\n```js\nBase.mixin(function(proto) {\n  proto.foo = function(msg) {\n    return 'foo ' + msg;\n  };\n});\n```\n\n### [#mixins](index.js#L401)\n\nStatic method for running global mixin functions against a child constructor. Mixins must be registered before calling this method.\n\n**Params**\n\n* `Child` **{Function}**: Constructor function of a child class\n* `returns` **{Object}**: Returns the `Base` constructor for chaining\n\n**Example**\n\n```js\nBase.extend(Child);\nBase.mixins(Child);\n```\n\n### [#inherit](index.js#L420)\n\nSimilar to `util.inherit`, but copies all static properties, prototype properties, and getters/setters from `Provider` to `Receiver`. See [class-utils](https://github.com/jonschlinkert/class-utils#inherit) for more details.\n\n**Params**\n\n* `Receiver` **{Function}**: Receiving (child) constructor\n* `Provider` **{Function}**: Providing (parent) constructor\n* `returns` **{Object}**: Returns the `Base` constructor for chaining\n\n**Example**\n\n```js\nBase.inherit(Foo, Bar);\n```\n\n## In the wild\n\nThe following node.js applications were built with `Base`:\n\n* [assemble](https://github.com/assemble/assemble)\n* [verb](https://github.com/verbose/verb)\n* [generate](https://github.com/generate/generate)\n* [scaffold](https://github.com/jonschlinkert/scaffold)\n* [boilerplate](https://github.com/jonschlinkert/boilerplate)\n\n## Test coverage\n\n```\nStatements   : 98.91% ( 91/92 )\nBranches     : 92.86% ( 26/28 )\nFunctions    : 100% ( 17/17 )\nLines        : 98.9% ( 90/91 )\n```\n\n## History\n\n### v0.11.2\n\n* fixes https://github.com/micromatch/micromatch/issues/99\n\n### v0.11.0\n\n**Breaking changes**\n\n* Static `.use` and `.run` methods are now non-enumerable\n\n### v0.9.0\n\n**Breaking changes**\n\n* `.is` no longer takes a function, a string must be passed\n* all remaining `.debug` code has been removed\n* `app._namespace` was removed (related to `debug`)\n* `.plugin`, `.use`, and `.define` no longer emit events\n* `.assertPlugin` was removed\n* `.lazy` was removed\n\n## About\n\n### Related projects\n\n* [base-cwd](https://www.npmjs.com/package/base-cwd): Base plugin that adds a getter/setter for the current working directory. | [homepage](https://github.com/node-base/base-cwd \"Base plugin that adds a getter/setter for the current working directory.\")\n* [base-data](https://www.npmjs.com/package/base-data): adds a `data` method to base-methods. | [homepage](https://github.com/node-base/base-data \"adds a `data` method to base-methods.\")\n* [base-fs](https://www.npmjs.com/package/base-fs): base-methods plugin that adds vinyl-fs methods to your 'base' application for working with the file… [more](https://github.com/node-base/base-fs) | [homepage](https://github.com/node-base/base-fs \"base-methods plugin that adds vinyl-fs methods to your 'base' application for working with the file system, like src, dest, copy and symlink.\")\n* [base-generators](https://www.npmjs.com/package/base-generators): Adds project-generator support to your `base` application. | [homepage](https://github.com/node-base/base-generators \"Adds project-generator support to your `base` application.\")\n* [base-option](https://www.npmjs.com/package/base-option): Adds a few options methods to base, like `option`, `enable` and `disable`. See the readme… [more](https://github.com/node-base/base-option) | [homepage](https://github.com/node-base/base-option \"Adds a few options methods to base, like `option`, `enable` and `disable`. See the readme for the full API.\")\n* [base-pipeline](https://www.npmjs.com/package/base-pipeline): base-methods plugin that adds pipeline and plugin methods for dynamically composing streaming plugin pipelines. | [homepage](https://github.com/node-base/base-pipeline \"base-methods plugin that adds pipeline and plugin methods for dynamically composing streaming plugin pipelines.\")\n* [base-pkg](https://www.npmjs.com/package/base-pkg): Plugin for adding a `pkg` method that exposes pkg-store to your base application. | [homepage](https://github.com/node-base/base-pkg \"Plugin for adding a `pkg` method that exposes pkg-store to your base application.\")\n* [base-plugins](https://www.npmjs.com/package/base-plugins): Adds 'smart plugin' support to your base application. | [homepage](https://github.com/node-base/base-plugins \"Adds 'smart plugin' support to your base application.\")\n* [base-questions](https://www.npmjs.com/package/base-questions): Plugin for base-methods that adds methods for prompting the user and storing the answers on… [more](https://github.com/node-base/base-questions) | [homepage](https://github.com/node-base/base-questions \"Plugin for base-methods that adds methods for prompting the user and storing the answers on a project-by-project basis.\")\n* [base-store](https://www.npmjs.com/package/base-store): Plugin for getting and persisting config values with your base-methods application. Adds a 'store' object… [more](https://github.com/node-base/base-store) | [homepage](https://github.com/node-base/base-store \"Plugin for getting and persisting config values with your base-methods application. Adds a 'store' object that exposes all of the methods from the data-store library. Also now supports sub-stores!\")\n* [base-task](https://www.npmjs.com/package/base-task): base plugin that provides a very thin wrapper around [https://github.com/doowb/composer](https://github.com/doowb/composer) for adding task methods to… [more](https://github.com/node-base/base-task) | [homepage](https://github.com/node-base/base-task \"base plugin that provides a very thin wrapper around <https://github.com/doowb/composer> for adding task methods to your application.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor** |  \n| --- | --- |  \n| 141 | [jonschlinkert](https://github.com/jonschlinkert) |  \n| 30  | [doowb](https://github.com/doowb) |  \n| 3   | [charlike](https://github.com/charlike) |  \n| 1   | [criticalmash](https://github.com/criticalmash) |  \n| 1   | [wtgtybhertgeghgtwtg](https://github.com/wtgtybhertgeghgtwtg) |  \n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on September 07, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015-2017, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/base/-/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f", "type": "tarball", "reference": "https://registry.yarnpkg.com/base/-/base-0.11.2.tgz", "hash": "7bde5ced145b6d551a90db87f83c558b4eb48a8f", "integrity": "sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg==", "registry": "npm", "packageName": "base", "cacheIntegrity": "sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg== sha1-e95c7RRbbVUakNuH+DxVi060io8="}, "registry": "npm", "hash": "7bde5ced145b6d551a90db87f83c558b4eb48a8f"}