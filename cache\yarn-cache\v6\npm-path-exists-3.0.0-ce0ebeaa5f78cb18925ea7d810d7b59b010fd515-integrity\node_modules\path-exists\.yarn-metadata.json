{"manifest": {"name": "path-exists", "version": "3.0.0", "description": "Check if a path exists", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/path-exists.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["path", "exists", "exist", "file", "filepath", "fs", "filesystem", "file-system", "access", "stat"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-path-exists-3.0.0-ce0ebeaa5f78cb18925ea7d810d7b59b010fd515-integrity\\node_modules\\path-exists\\package.json", "readmeFilename": "readme.md", "readme": "# path-exists [![Build Status](https://travis-ci.org/sindresorhus/path-exists.svg?branch=master)](https://travis-ci.org/sindresorhus/path-exists)\n\n> Check if a path exists\n\nBecause [`fs.exists()`](https://nodejs.org/api/fs.html#fs_fs_exists_path_callback) is being [deprecated](https://github.com/iojs/io.js/issues/103), but there's still a genuine use-case of being able to check if a path exists for other purposes than doing IO with it.\n\nNever use this before handling a file though:\n\n> In particular, checking if a file exists before opening it is an anti-pattern that leaves you vulnerable to race conditions: another process may remove the file between the calls to `fs.exists()` and `fs.open()`. Just open the file and handle the error when it's not there.\n\n\n## Install\n\n```\n$ npm install --save path-exists\n```\n\n\n## Usage\n\n```js\n// foo.js\nconst pathExists = require('path-exists');\n\npathExists('foo.js').then(exists => {\n\tconsole.log(exists);\n\t//=> true\n});\n```\n\n\n## API\n\n### pathExists(path)\n\nReturns a promise for a boolean of whether the path exists.\n\n### pathExists.sync(path)\n\nReturns a boolean of whether the path exists.\n\n\n## Related\n\n- [path-exists-cli](https://github.com/sindresorhus/path-exists-cli) - CLI for this module\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com)\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND <PERSON><PERSON>NFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/path-exists/-/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515", "type": "tarball", "reference": "https://registry.yarnpkg.com/path-exists/-/path-exists-3.0.0.tgz", "hash": "ce0ebeaa5f78cb18925ea7d810d7b59b010fd515", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "registry": "npm", "packageName": "path-exists", "cacheIntegrity": "sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ== sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="}, "registry": "npm", "hash": "ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"}