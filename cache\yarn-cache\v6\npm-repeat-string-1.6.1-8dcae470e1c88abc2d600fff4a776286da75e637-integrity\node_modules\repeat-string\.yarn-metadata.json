{"manifest": {"name": "repeat-string", "description": "Repeat the given string n times. Fastest implementation for repeating a string.", "version": "1.6.1", "homepage": "https://github.com/jonschlinkert/repeat-string", "author": {"name": "<PERSON>", "url": "http://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/doowb"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://linus.unnebäck.se"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tbusser.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "wooorm.com"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/repeat-string.git"}, "bugs": {"url": "https://github.com/jonschlinkert/repeat-string/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10"}, "scripts": {"test": "mocha"}, "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^0.2.5", "gulp-format-md": "^0.1.11", "isobject": "^2.1.0", "mocha": "^3.1.2", "repeating": "^3.0.0", "text-table": "^0.2.0", "yargs-parser": "^4.0.2"}, "keywords": ["fast", "fastest", "fill", "left", "left-pad", "multiple", "pad", "padding", "repeat", "repeating", "repetition", "right", "right-pad", "string", "times"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["repeat-element"]}, "helpers": ["./benchmark/helper.js"], "reflinks": ["verb"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-repeat-string-1.6.1-8dcae470e1c88abc2d600fff4a776286da75e637-integrity\\node_modules\\repeat-string\\package.json", "readmeFilename": "README.md", "readme": "# repeat-string [![NPM version](https://img.shields.io/npm/v/repeat-string.svg?style=flat)](https://www.npmjs.com/package/repeat-string) [![NPM monthly downloads](https://img.shields.io/npm/dm/repeat-string.svg?style=flat)](https://npmjs.org/package/repeat-string)  [![NPM total downloads](https://img.shields.io/npm/dt/repeat-string.svg?style=flat)](https://npmjs.org/package/repeat-string) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/repeat-string.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/repeat-string)\n\n> Repeat the given string n times. Fastest implementation for repeating a string.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save repeat-string\n```\n\n## Usage\n\n### [repeat](index.js#L41)\n\nRepeat the given `string` the specified `number` of times.\n\n**Example:**\n\n**Example**\n\n```js\nvar repeat = require('repeat-string');\nrepeat('A', 5);\n//=> AAAAA\n```\n\n**Params**\n\n* `string` **{String}**: The string to repeat\n* `number` **{Number}**: The number of times to repeat the string\n* `returns` **{String}**: Repeated string\n\n## Benchmarks\n\nRepeat string is significantly faster than the native method (which is itself faster than [repeating](https://github.com/sindresorhus/repeating)):\n\n```sh\n# 2x\nrepeat-string  █████████████████████████  (26,953,977 ops/sec)\nrepeating      █████████                  (9,855,695 ops/sec)\nnative         ██████████████████         (19,453,895 ops/sec)\n\n# 3x\nrepeat-string  █████████████████████████  (19,445,252 ops/sec)\nrepeating      ███████████                (8,661,565 ops/sec)\nnative         ████████████████████       (16,020,598 ops/sec)\n\n# 10x\nrepeat-string  █████████████████████████  (23,792,521 ops/sec)\nrepeating      █████████                  (8,571,332 ops/sec)\nnative         ███████████████            (14,582,955 ops/sec)\n\n# 50x\nrepeat-string  █████████████████████████  (23,640,179 ops/sec)\nrepeating      █████                      (5,505,509 ops/sec)\nnative         ██████████                 (10,085,557 ops/sec)\n\n# 250x\nrepeat-string  █████████████████████████  (23,489,618 ops/sec)\nrepeating      ████                       (3,962,937 ops/sec)\nnative         ████████                   (7,724,892 ops/sec)\n\n# 2000x\nrepeat-string  █████████████████████████  (20,315,172 ops/sec)\nrepeating      ████                       (3,297,079 ops/sec)\nnative         ███████                    (6,203,331 ops/sec)\n\n# 20000x\nrepeat-string  █████████████████████████  (23,382,915 ops/sec)\nrepeating      ███                        (2,980,058 ops/sec)\nnative         █████                      (5,578,808 ops/sec)\n```\n\n**Run the benchmarks**\n\nInstall dev dependencies:\n\n```sh\nnpm i -d && node benchmark\n```\n\n## About\n\n### Related projects\n\n[repeat-element](https://www.npmjs.com/package/repeat-element): Create an array by repeating the given value n times. | [homepage](https://github.com/jonschlinkert/repeat-element \"Create an array by repeating the given value n times.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Contributors\n\n| **Commits** | **Contributor**<br/> | \n| --- | --- |\n| 51 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 2 | [LinusU](https://github.com/LinusU) |\n| 2 | [tbusser](https://github.com/tbusser) |\n| 1 | [doowb](https://github.com/doowb) |\n| 1 | [wooorm](https://github.com/wooorm) |\n\n### Building docs\n\n_(This document was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme) (a [verb](https://github.com/verbose/verb) generator), please don't edit the readme directly. Any changes to the readme must be made in [.verb.md](.verb.md).)_\n\nTo generate the readme and API documentation with [verb](https://github.com/verbose/verb):\n\n```sh\n$ npm install -g verb verb-generate-readme && verb\n```\n\n### Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm install -d && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2016, [Jon Schlinkert](http://github.com/jonschlinkert).\nReleased under the [MIT license](https://github.com/jonschlinkert/repeat-string/blob/master/LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.2.0, on October 23, 2016._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2016, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/repeat-string/-/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637", "type": "tarball", "reference": "https://registry.yarnpkg.com/repeat-string/-/repeat-string-1.6.1.tgz", "hash": "8dcae470e1c88abc2d600fff4a776286da75e637", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc=", "registry": "npm", "packageName": "repeat-string", "cacheIntegrity": "sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w== sha1-jcrkcOHIirwtYA//Sndihtp15jc="}, "registry": "npm", "hash": "8dcae470e1c88abc2d600fff4a776286da75e637"}