<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="style.css" rel="stylesheet" type="text/css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://res-fa-server.github.io/css/all.css" rel="stylesheet" type="text/css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRA<PERSON>@24,400,0,0" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;500;600;700;800&family=Poppins:wght@100;200;300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap" rel="stylesheet">
    <script src="https://kit.fontawesome.com/ec73b5a333.js" crossorigin="anonymous"></script>
    <link href="https://fonts.googleapis.com/css2?family=Barlow:wght@100;200;300;400;500;600;700&family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Manrope:wght@200;300;400;500;600;700;800&family=Rubik:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js"></script>
    <link rel="stylesheet" href="https://www.w3schools.com/w3css/4/w3.css">
    <title>0R-Core</title>
</head>
<body>

    <div class="text"></div>

    <div class="NotifyPart">
    </div>

    <audio id="notificationSound">
        <source src="sound.mp3" type="audio/mpeg">
        Tarayıcınız sesi desteklemiyor.
    </audio>

    <!-- <div class="Bunlar">
        <div class="LeftBoar">
            <span class="material-symbols-outlined">
                check_circle
            </span>
        </div>
        <div class="RightBoar">
            <h1>SUCCESSFULL</h1>
            <p>Başarıyla Allaha Sövdün Başarıyla Allaha Sövdün Başarıyla Allaha SövdünBaşarıyla Allaha Sövdün Başarıyla Allaha SövdünBaşarıyla Allaha Sövdün Başarıyla Allaha SövdünBaşarıyla Allaha Sövdün Başarıyla Allaha SövdünBaşarıyla Allaha Sövdün Başarıyla Allaha Sövdün</p>
        </div>
    </div> -->

    <!-- <div class="NewContainer">
        <div class="ContainerNot">
            <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" fill="none">
                <path d="M16.34 27.74L29.735 14.345L27.075 11.685L16.34 22.42L10.925 17.005L8.265 19.665L16.34 27.74ZM19 38C16.3717 38 13.9017 37.5013 11.59 36.5037C9.27833 35.5062 7.2675 34.1525 5.5575 32.4425C3.8475 30.7325 2.49375 28.7217 1.49625 26.41C0.49875 24.0983 0 21.6283 0 19C0 16.3717 0.49875 13.9017 1.49625 11.59C2.49375 9.27833 3.8475 7.2675 5.5575 5.5575C7.2675 3.8475 9.27833 2.49375 11.59 1.49625C13.9017 0.49875 16.3717 0 19 0C21.6283 0 24.0983 0.49875 26.41 1.49625C28.7217 2.49375 30.7325 3.8475 32.4425 5.5575C34.1525 7.2675 35.5062 9.27833 36.5037 11.59C37.5013 13.9017 38 16.3717 38 19C38 21.6283 37.5013 24.0983 36.5037 26.41C35.5062 28.7217 34.1525 30.7325 32.4425 32.4425C30.7325 34.1525 28.7217 35.5062 26.41 36.5037C24.0983 37.5013 21.6283 38 19 38ZM19 34.2C23.2433 34.2 26.8375 32.7275 29.7825 29.7825C32.7275 26.8375 34.2 23.2433 34.2 19C34.2 14.7567 32.7275 11.1625 29.7825 8.2175C26.8375 5.2725 23.2433 3.8 19 3.8C14.7567 3.8 11.1625 5.2725 8.2175 8.2175C5.2725 11.1625 3.8 14.7567 3.8 19C3.8 23.2433 5.2725 26.8375 8.2175 29.7825C11.1625 32.7275 14.7567 34.2 19 34.2Z" fill="#26CB90"/>
            </svg>
        </div>
        <div class="ContainerNext">
            <h3>SUCCESSFULL</h3>
            <p>You have successfully purchased.</p>
        </div>
    </div> -->

    <script src="index.js" type="text/javascript"></script>
</body>
</html>