body {
    font-family: '<PERSON>pins', sans-serif;
}

.textInfo {
    position: absolute;
    top: 50%;
    float: left;
    color: white;
}

#doorlock-container {
    position: absolute;
    flex-wrap: wrap;
    top: 45vh;
    padding: 0.6em;
    width: fit-content;
    color: white;
    border-radius: 0.3em;
    left: -15%;
}

.slide_in {
    animation: slide-in 1s forwards;
}

.slide_out {
    animation: slide-out 1s forwards;
}

@keyframes slide-in {
    0% {
        left: -15%;
    }
    100% {
        left: 0.5%;
    }
}

@keyframes slide-out {
    0% {
        left: 0.5%;
    }
    100% {
        left: -15%;
    }
}