{"manifest": {"name": "@types/cookies", "version": "0.7.6", "description": "TypeScript definitions for cookies", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "j<PERSON><PERSON>", "url": "https://github.com/jkeylu"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cookies"}, "scripts": {}, "dependencies": {"@types/connect": "*", "@types/express": "*", "@types/keygrip": "*", "@types/node": "*"}, "typesPublisherContentHash": "09a4522e334d7a4d84019482a9f1b00d7b41792842dc6238a27404c92cde2a72", "typeScriptVersion": "3.3", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-cookies-0.7.6-71212c5391a976d3bae57d4b09fac20fc6bda504-integrity\\node_modules\\@types\\cookies\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/cookies`\n\n# Summary\nThis package contains type definitions for cookies (https://github.com/pillarjs/cookies).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookies.\n\n### Additional Details\n * Last updated: Tu<PERSON>, 22 Dec 2020 21:35:03 GMT\n * Dependencies: [@types/keygrip](https://npmjs.com/package/@types/keygrip), [@types/express](https://npmjs.com/package/@types/express), [@types/connect](https://npmjs.com/package/@types/connect), [@types/node](https://npmjs.com/package/@types/node)\n * Global values: none\n\n# Credits\nThese definitions were written by [<PERSON>](https://github.com/<PERSON>), [j<PERSON><PERSON>](https://github.com/jkeylu), and [BendingBender](https://github.com/BendingBender).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/cookies/-/cookies-0.7.6.tgz#71212c5391a976d3bae57d4b09fac20fc6bda504", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/cookies/-/cookies-0.7.6.tgz", "hash": "71212c5391a976d3bae57d4b09fac20fc6bda504", "integrity": "sha512-FK4U5Qyn7/Sc5ih233OuHO0qAkOpEcD/eG6584yEiLKizTFRny86qHLe/rej3HFQrkBuUjF4whFliAdODbVN/w==", "registry": "npm", "packageName": "@types/cookies", "cacheIntegrity": "sha512-FK4U5Qyn7/Sc5ih233OuHO0qAkOpEcD/eG6584yEiLKizTFRny86qHLe/rej3HFQrkBuUjF4whFliAdODbVN/w== sha1-cSEsU5GpdtO65X1LCfrCD8a9pQQ="}, "registry": "npm", "hash": "71212c5391a976d3bae57d4b09fac20fc6bda504"}