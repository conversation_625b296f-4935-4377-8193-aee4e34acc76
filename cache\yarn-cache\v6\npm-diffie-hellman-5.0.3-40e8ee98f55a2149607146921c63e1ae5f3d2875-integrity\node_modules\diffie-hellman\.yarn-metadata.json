{"manifest": {"name": "diffie-hellman", "version": "5.0.3", "description": "pure js diffie-hellman", "main": "index.js", "browser": "browser.js", "scripts": {"test": "node test.js | tspec"}, "repository": {"type": "git", "url": "https://github.com/crypto-browserify/diffie-hellman.git"}, "keywords": ["diffie", "hellman", "di<PERSON><PERSON><PERSON><PERSON>", "dh"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/diffie-hellman/issues"}, "homepage": "https://github.com/crypto-browserify/diffie-hellman", "dependencies": {"bn.js": "^4.1.0", "miller-rabin": "^4.0.0", "randombytes": "^2.0.0"}, "devDependencies": {"tap-spec": "^1.0.1", "tape": "^3.0.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-diffie-hellman-5.0.3-40e8ee98f55a2149607146921c63e1ae5f3d2875-integrity\\node_modules\\diffie-hellman\\package.json", "readmeFilename": "readme.md", "readme": "diffie hellman [![Build Status](https://travis-ci.org/crypto-browserify/diffie-hellman.svg)](https://travis-ci.org/crypto-browserify/diffie-hellman)\n====\n\npure js diffie-hellman, same api as node, most hard parts thanks to [bn.js](https://www.npmjs.org/package/bn.js) by [@indutny](https://github.com/indutny).  In node just returns an object with `crypto.createDiffieHellman` and `crypto.getDiffieHellman` in the browser returns a shim. To require the pure JavaScript one in node `require('diffie-hellman/browser');`;", "licenseText": "Copyright (c) 2017 Calvin <PERSON>f\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/diffie-hellman/-/diffie-hellman-5.0.3.tgz#40e8ee98f55a2149607146921c63e1ae5f3d2875", "type": "tarball", "reference": "https://registry.yarnpkg.com/diffie-hellman/-/diffie-hellman-5.0.3.tgz", "hash": "40e8ee98f55a2149607146921c63e1ae5f3d2875", "integrity": "sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg==", "registry": "npm", "packageName": "diffie-hellman", "cacheIntegrity": "sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg== sha1-QOjumPVaIUlgcUaSHGPhrl89KHU="}, "registry": "npm", "hash": "40e8ee98f55a2149607146921c63e1ae5f3d2875"}