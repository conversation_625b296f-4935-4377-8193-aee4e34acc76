{"manifest": {"name": "define-property", "description": "Define a non-enumerable property on an object.", "version": "0.2.5", "homepage": "https://github.com/jonschlinkert/define-property", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/define-property.git"}, "bugs": {"url": "https://github.com/jonschlinkert/define-property/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*", "should": "^7.0.4"}, "keywords": ["define", "define-property", "enumerable", "key", "non", "non-enumerable", "object", "prop", "property", "value"], "verb": {"related": {"list": ["mixin-deep", "mixin-object", "delegate-object", "forward-object"]}}, "dependencies": {"is-descriptor": "^0.1.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-define-property-0.2.5-c35b1ef918ec3c990f9a5bc57be04aacec5c8116-integrity\\node_modules\\define-property\\package.json", "readmeFilename": "README.md", "readme": "# define-property [![NPM version](https://badge.fury.io/js/define-property.svg)](http://badge.fury.io/js/define-property)\n\n> Define a non-enumerable property on an object.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/)\n\n```sh\n$ npm i define-property --save\n```\n\n## Usage\n\n**Params**\n\n* `obj`: The object on which to define the property.\n* `prop`: The name of the property to be defined or modified.\n* `descriptor`: The descriptor for the property being defined or modified.\n\n```js\nvar define = require('define-property');\nvar obj = {};\ndefine(obj, 'foo', function(val) {\n  return val.toUpperCase();\n});\n\nconsole.log(obj);\n//=> {}\n\nconsole.log(obj.foo('bar'));\n//=> 'BAR'\n```\n\n**get/set**\n\n```js\ndefine(obj, 'foo', {\n  get: function() {},\n  set: function() {}\n});\n```\n\n## Related projects\n\n* [delegate-object](https://www.npmjs.com/package/delegate-object): Copy properties from an object to another object, where properties with function values will be… [more](https://www.npmjs.com/package/delegate-object) | [homepage](https://github.com/doowb/delegate-object)\n* [forward-object](https://www.npmjs.com/package/forward-object): Copy properties from an object to another object, where properties with function values will be… [more](https://www.npmjs.com/package/forward-object) | [homepage](https://github.com/doowb/forward-object)\n* [mixin-deep](https://www.npmjs.com/package/mixin-deep): Deeply mix the properties of objects into the first object. Like merge-deep, but doesn't clone. | [homepage](https://github.com/jonschlinkert/mixin-deep)\n* [mixin-object](https://www.npmjs.com/package/mixin-object): Mixin the own and inherited properties of other objects onto the first object. Pass an… [more](https://www.npmjs.com/package/mixin-object) | [homepage](https://github.com/jonschlinkert/mixin-object)\n\n## Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm i -d && npm test\n```\n\n## Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/define-property/issues/new).\n\n## Author\n\n**Jon Schlinkert**\n\n+ [github/jonschlinkert](https://github.com/jonschlinkert)\n+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n## License\n\nCopyright © 2015 Jon Schlinkert\nReleased under the MIT license.\n\n***\n\n_This file was generated by [verb-cli](https://github.com/assemble/verb-cli) on August 31, 2015._\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/define-property/-/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116", "type": "tarball", "reference": "https://registry.yarnpkg.com/define-property/-/define-property-0.2.5.tgz", "hash": "c35b1ef918ec3c990f9a5bc57be04aacec5c8116", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "registry": "npm", "packageName": "define-property", "cacheIntegrity": "sha512-Rr7ADjQZenceVOAKop6ALkkRAmH1A4Gx9hV/7ZujPUN2rkATqFO0JZLZInbAjpZYoJ1gUx8MRMQVkYemcbMSTA== sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY="}, "registry": "npm", "hash": "c35b1ef918ec3c990f9a5bc57be04aacec5c8116"}