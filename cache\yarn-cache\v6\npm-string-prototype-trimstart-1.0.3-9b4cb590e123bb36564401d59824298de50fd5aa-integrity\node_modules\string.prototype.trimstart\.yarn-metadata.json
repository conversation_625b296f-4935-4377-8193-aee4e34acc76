{"manifest": {"name": "string.prototype.trimstart", "version": "1.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "k<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ES2019 spec-compliant String.prototype.trimStart shim.", "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "lint": "eslint .", "postlint": "es-shim-api --bound", "pretest": "npm run lint", "test": "npm run tests-only", "posttest": "aud --production", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/es-shims/String.prototype.trimStart.git"}, "keywords": ["es6", "es7", "es8", "javascript", "prototype", "polyfill", "utility", "trim", "trimLeft", "trimRight", "trimStart", "trimEnd", "tc39"], "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^17.2.0", "aud": "^1.1.3", "auto-changelog": "^2.2.1", "eslint": "^7.14.0", "functions-have-names": "^1.2.1", "has-strict-mode": "^1.0.0", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.0.1"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "dependencies": {"call-bind": "^1.0.0", "define-properties": "^1.1.3"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-string-prototype-trimstart-1.0.3-9b4cb590e123bb36564401d59824298de50fd5aa-integrity\\node_modules\\string.prototype.trimstart\\package.json", "readmeFilename": "README.md", "readme": "String.prototype.trimStart <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![Build Status][travis-svg]][travis-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\n[![browser support][testling-svg]][testling-url]\n\nAn ES2019-spec-compliant `String.prototype.trimStart` shim. Invoke its \"shim\" method to shim `String.prototype.trimStart` if it is unavailable.\n\nThis package implements the [es-shim API](https://github.com/es-shims/api) interface. It works in an ES3-supported environment and complies with the [spec](http://www.ecma-international.org/ecma-262/6.0/#sec-object.assign). In an ES6 environment, it will also work properly with `Symbol`s.\n\nMost common usage:\n```js\nvar trimStart = require('string.prototype.trimstart');\n\nassert(trimStart(' \\t\\na \\t\\n') === 'a \\t\\n');\n\nif (!String.prototype.trimStart) {\n\ttrimStart.shim();\n}\n\nassert(trimStart(' \\t\\na \\t\\n') === ' \\t\\na \\t\\n'.trimStart());\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.com/package/string.prototype.trimstart\n[npm-version-svg]: http://vb.teelaun.ch/es-shims/String.prototype.trimStart.svg\n[travis-svg]: https://travis-ci.org/es-shims/String.prototype.trimStart.svg\n[travis-url]: https://travis-ci.org/es-shims/String.prototype.trimStart\n[deps-svg]: https://david-dm.org/es-shims/String.prototype.trimStart.svg\n[deps-url]: https://david-dm.org/es-shims/String.prototype.trimStart\n[dev-deps-svg]: https://david-dm.org/es-shims/String.prototype.trimStart/dev-status.svg\n[dev-deps-url]: https://david-dm.org/es-shims/String.prototype.trimStart#info=devDependencies\n[testling-svg]: https://ci.testling.com/es-shims/String.prototype.trimStart.png\n[testling-url]: https://ci.testling.com/es-shims/String.prototype.trimStart\n[npm-badge-png]: https://nodei.co/npm/string.prototype.trimstart.png?downloads=true&stars=true\n[license-image]: http://img.shields.io/npm/l/string.prototype.trimstart.svg\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/string.prototype.trimstart.svg\n[downloads-url]: http://npm-stat.com/charts.html?package=string.prototype.trimstart\n", "licenseText": "MIT License\n\nCopyright (c) 2017 <PERSON><PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/string.prototype.trimstart/-/string.prototype.trimstart-1.0.3.tgz#9b4cb590e123bb36564401d59824298de50fd5aa", "type": "tarball", "reference": "https://registry.yarnpkg.com/string.prototype.trimstart/-/string.prototype.trimstart-1.0.3.tgz", "hash": "9b4cb590e123bb36564401d59824298de50fd5aa", "integrity": "sha512-oBIBUy5lea5tt0ovtOFiEQaBkoBBkyJhZXzJYrSmDo5IUUqbOPvVezuRs/agBIdZ2p2Eo1FD6bD9USyBLfl3xg==", "registry": "npm", "packageName": "string.prototype.trimstart", "cacheIntegrity": "sha512-oBIBUy5lea5tt0ovtOFiEQaBkoBBkyJhZXzJYrSmDo5IUUqbOPvVezuRs/agBIdZ2p2Eo1FD6bD9USyBLfl3xg== sha1-m0y1kOEjuzZWRAHVmCQpjeUP1ao="}, "registry": "npm", "hash": "9b4cb590e123bb36564401d59824298de50fd5aa"}