{"manifest": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.19.5", "title": "URI.js - Mutating U<PERSON>s", "author": {"name": "<PERSON>", "url": "http://rodneyrehm.de"}, "repository": {"type": "git", "url": "https://github.com/medialize/URI.js.git"}, "license": "MIT", "description": "URI.js is a Javascript library for working with URLs.", "keywords": ["uri", "url", "urn", "uri mutation", "url mutation", "uri manipulation", "url manipulation", "uri template", "url template", "unified resource locator", "unified resource identifier", "query string", "RFC 3986", "RFC3986", "RFC 6570", "RFC6570", "jquery-plugin", "ecosystem:jquery"], "categories": ["Parsers & Compilers", "Utilities"], "main": "./src/URI", "homepage": "http://medialize.github.io/URI.js/", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://fgribreau.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://justinmchase.com"}], "files": ["src/URI.js", "src/IPv6.js", "src/SecondLevelDomains.js", "src/punycode.js", "src/URITemplate.js", "src/jquery.URI.js", "src/URI.min.js", "src/jquery.URI.min.js", "src/URI.fragmentQuery.js", "src/URI.fragmentURI.js", "LICENSE.txt"], "npmName": "<PERSON><PERSON><PERSON><PERSON>", "npmFileMap": [{"basePath": "/src/", "files": ["*.js"]}, {"basePath": "/", "files": ["LICENSE.txt"]}], "devDependencies": {"jshint-stylish": "~0.1.5", "grunt": "~0.4.2", "grunt-contrib-jshint": "~0.8.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-urijs-1.19.5-119683ab4b2fb0bd637e5ea6dd9117bcac68d3e4-integrity\\node_modules\\urijs\\package.json", "readmeFilename": "README.md", "readme": "# URI.js #\n\n[![CDNJS](https://img.shields.io/cdnjs/v/URI.js.svg)](https://cdnjs.com/libraries/URI.js)\n* [About](http://medialize.github.io/URI.js/)\n* [Understanding URIs](http://medialize.github.io/URI.js/about-uris.html)\n* [Documentation](http://medialize.github.io/URI.js/docs.html)\n* [jQuery URI Plugin](http://medialize.github.io/URI.js/jquery-uri-plugin.html)\n* [Author](http://rodneyrehm.de/en/)\n* [Changelog](./CHANGELOG.md)\n\n---\n\n> **IMPORTANT:** You **may not need URI.js** anymore! Modern browsers provide the [URL](https://developer.mozilla.org/en-US/docs/Web/API/URL) and [URLSearchParams](https://developer.mozilla.org/en-US/docs/Web/API/URLSearchParams) interfaces.\n\n---\n\n> **NOTE:** The npm package name changed to `urijs`\n\n---\n\nI always want to shoot myself in the head when looking at code like the following:\n\n```javascript\nvar url = \"http://example.org/foo?bar=baz\";\nvar separator = url.indexOf('?') > -1 ? '&' : '?';\n\nurl += separator + encodeURIComponent(\"foo\") + \"=\" + encodeURIComponent(\"bar\");\n```\n\nThings are looking up with [URL](https://developer.mozilla.org/en/docs/Web/API/URL) and the [URL spec](http://url.spec.whatwg.org/) but until we can safely rely on that API, have a look at URI.js for a clean and simple API for mutating URIs:\n\n```javascript\nvar url = new URI(\"http://example.org/foo?bar=baz\");\nurl.addQuery(\"foo\", \"bar\");\n```\n\nURI.js is here to help with that.\n\n\n## API Example ##\n\n```javascript\n// mutating URLs\nURI(\"http://example.org/foo.html?hello=world\")\n  .username(\"rodneyrehm\")\n    // -> http://<EMAIL>/foo.html?hello=world\n  .username(\"\")\n    // -> http://example.org/foo.html?hello=world\n  .directory(\"bar\")\n    // -> http://example.org/bar/foo.html?hello=world\n  .suffix(\"xml\")\n    // -> http://example.org/bar/foo.xml?hello=world\n  .query(\"\")\n    // -> http://example.org/bar/foo.xml\n  .tld(\"com\")\n    // -> http://example.com/bar/foo.xml\n  .query({ foo: \"bar\", hello: [\"world\", \"mars\"] });\n    // -> http://example.com/bar/foo.xml?foo=bar&hello=world&hello=mars\n\n// cleaning things up\nURI(\"?&foo=bar&&foo=bar&foo=baz&\")\n  .normalizeQuery();\n    // -> ?foo=bar&foo=baz\n\n// working with relative paths\nURI(\"/foo/bar/baz.html\")\n  .relativeTo(\"/foo/bar/world.html\");\n    // -> ./baz.html\n\nURI(\"/foo/bar/baz.html\")\n  .relativeTo(\"/foo/bar/sub/world.html\")\n    // -> ../baz.html\n  .absoluteTo(\"/foo/bar/sub/world.html\");\n    // -> /foo/bar/baz.html\n\n// URI Templates\nURI.expand(\"/foo/{dir}/{file}\", {\n  dir: \"bar\",\n  file: \"world.html\"\n});\n// -> /foo/bar/world.html\n```\n\nSee the [About Page](http://medialize.github.io/URI.js/) and [API Docs](http://medialize.github.io/URI.js/docs.html) for more stuff.\n\n## Using URI.js ##\n\nURI.js (without plugins) has a gzipped weight of about 7KB - if you include all extensions you end up at about 13KB. So unless you *need* second level domain support and use URI templates, we suggest you don't include them in your build. If you don't need a full featured URI mangler, it may be worth looking into the much smaller parser-only alternatives [listed below](#alternatives).\n\nURI.js is available through [npm](https://www.npmjs.com/package/urijs), [bower](http://bower.io/search/?q=urijs), [bowercdn](http://bowercdn.net/package/urijs), [cdnjs](https://cdnjs.com/libraries/URI.js) and manually from the [build page](http://medialize.github.io/URI.js/build.html):\n\n```bash\n# using bower\nbower install uri.js\n\n# using npm\nnpm install urijs\n```\n\n### Browser ###\n\nI guess you'll manage to use the [build tool](http://medialize.github.io/URI.js/build.html) or follow the [instructions below](#minify) to combine and minify the various files into URI.min.js - and I'm fairly certain you know how to `<script src=\".../URI.min.js\"></script>` that sucker, too.\n\n### Node.js and NPM ###\n\nInstall with `npm install urijs` or add `\"urijs\"` to the dependencies in your `package.json`.\n\n```javascript\n// load URI.js\nvar URI = require('urijs');\n// load an optional module (e.g. URITemplate)\nvar URITemplate = require('urijs/src/URITemplate');\n\nURI(\"/foo/bar/baz.html\")\n  .relativeTo(\"/foo/bar/sub/world.html\")\n    // -> ../baz.html\n```\n\n### RequireJS ###\n\nClone the URI.js repository or use a package manager to get URI.js into your project.\n\n```javascript\nrequire.config({\n  paths: {\n    urijs: 'where-you-put-uri.js/src'\n  }\n});\n\nrequire(['urijs/URI'], function(URI) {\n  console.log(\"URI.js and dependencies: \", URI(\"//amazon.co.uk\").is('sld') ? 'loaded' : 'failed');\n});\nrequire(['urijs/URITemplate'], function(URITemplate) {\n  console.log(\"URITemplate.js and dependencies: \", URITemplate._cache ? 'loaded' : 'failed');\n});\n```\n\n## Minify ##\n\nSee the [build tool](http://medialize.github.io/URI.js/build.html) or use [Google Closure Compiler](http://closure-compiler.appspot.com/home):\n\n```\n// ==ClosureCompiler==\n// @compilation_level SIMPLE_OPTIMIZATIONS\n// @output_file_name URI.min.js\n// @code_url http://medialize.github.io/URI.js/src/IPv6.js\n// @code_url http://medialize.github.io/URI.js/src/punycode.js\n// @code_url http://medialize.github.io/URI.js/src/SecondLevelDomains.js\n// @code_url http://medialize.github.io/URI.js/src/URI.js\n// @code_url http://medialize.github.io/URI.js/src/URITemplate.js\n// ==/ClosureCompiler==\n```\n\n\n## Resources ##\n\nDocuments specifying how URLs work:\n\n* [URL - Living Standard](http://url.spec.whatwg.org/)\n* [RFC 3986 - Uniform Resource Identifier (URI): Generic Syntax](http://tools.ietf.org/html/rfc3986)\n* [RFC 3987 - Internationalized Resource Identifiers (IRI)](http://tools.ietf.org/html/rfc3987)\n* [RFC 2732 - Format for Literal IPv6 Addresses in URL's](http://tools.ietf.org/html/rfc2732)\n* [RFC 2368 - The `mailto:` URL Scheme](https://www.ietf.org/rfc/rfc2368.txt)\n* [RFC 2141 - URN Syntax](https://www.ietf.org/rfc/rfc2141.txt)\n* [IANA URN Namespace Registry](http://www.iana.org/assignments/urn-namespaces/urn-namespaces.xhtml)\n* [Punycode: A Bootstring encoding of Unicode for Internationalized Domain Names in Applications (IDNA)](http://tools.ietf.org/html/rfc3492)\n* [application/x-www-form-urlencoded](http://www.w3.org/TR/REC-html40/interact/forms.html#form-content-type) (Query String Parameters) and [application/x-www-form-urlencoded encoding algorithm](http://www.whatwg.org/specs/web-apps/current-work/multipage/association-of-controls-and-forms.html#application/x-www-form-urlencoded-encoding-algorithm)\n* [What every web developer must know about URL encoding](http://blog.lunatech.com/2009/02/03/what-every-web-developer-must-know-about-url-encoding)\n\nInformal stuff\n\n* [Parsing URLs for Fun and Profit](http://tools.ietf.org/html/draft-abarth-url-01)\n* [Naming URL components](http://tantek.com/2011/238/b1/many-ways-slice-url-name-pieces)\n\nHow other environments do things\n\n* [Java URI Class](http://docs.oracle.com/javase/7/docs/api/java/net/URI.html)\n* [Java Inet6Address Class](http://docs.oracle.com/javase/1.5.0/docs/api/java/net/Inet6Address.html)\n* [Node.js URL API](http://nodejs.org/docs/latest/api/url.html)\n\n[Discussion on Hacker News](https://news.ycombinator.com/item?id=3398837)\n\n### Forks / Code-borrow ###\n\n* [node-dom-urls](https://github.com/passy/node-dom-urls) passy's partial implementation of the W3C URL Spec Draft for Node\n* [urlutils](https://github.com/cofounders/urlutils) cofounders' `window.URL` constructor for Node\n\n### Alternatives ###\n\nIf you don't like URI.js, you may like one of the following libraries. (If yours is not listed, drop me a line…)\n\n#### Polyfill ####\n\n* [DOM-URL-Polyfill](https://github.com/arv/DOM-URL-Polyfill/) arv's polyfill of the [DOM URL spec](https://dvcs.w3.org/hg/url/raw-file/tip/Overview.html#interface-urlutils) for browsers\n* [inexorabletash](https://github.com/inexorabletash/polyfill/#whatwg-url-api) inexorabletash's [WHATWG URL API](http://url.spec.whatwg.org/)\n\n#### URL Manipulation ####\n\n* [The simple <a> URL Mutation \"Hack\"](http://jsfiddle.net/rodneyrehm/KkGUJ/) ([jsPerf comparison](http://jsperf.com/idl-attributes-vs-uri-js))\n* [URL.js](https://github.com/ericf/urljs)\n* [furl (Python)](https://github.com/gruns/furl)\n* [mediawiki Uri](https://svn.wikimedia.org/viewvc/mediawiki/trunk/phase3/resources/mediawiki/mediawiki.Uri.js?view=markup) (needs mw and jQuery)\n* [jurlp](https://github.com/tombonner/jurlp)\n* [jsUri](https://github.com/derek-watson/jsUri)\n\n#### URL Parsers ####\n\n* [The simple <a> URL Mutation \"Hack\"](http://jsfiddle.net/rodneyrehm/KkGUJ/) ([jsPerf comparison](http://jsperf.com/idl-attributes-vs-uri-js))\n* [URI Parser](http://blog.stevenlevithan.com/archives/parseuri)\n* [jQuery-URL-Parser](https://github.com/allmarkedup/jQuery-URL-Parser)\n* [Google Closure Uri](https://google.github.io/closure-library/api/class_goog_Uri.html)\n* [URI.js by Gary Court](https://github.com/garycourt/uri-js)\n\n#### URI Template ####\n\n* [uri-template](https://github.com/rezigned/uri-template.js) (supporting extraction as well) by Rezigne\n* [uri-templates](https://github.com/geraintluff/uri-templates) (supporting extraction as well) by Geraint Luff\n* [uri-templates](https://github.com/marc-portier/uri-templates) by Marc Portier\n* [uri-templates](https://github.com/geraintluff/uri-templates) by Geraint Luff (including reverse operation)\n* [URI Template JS](https://github.com/fxa/uritemplate-js) by Franz Antesberger\n* [Temple](https://github.com/brettstimmerman/temple) by Brett Stimmerman\n* ([jsperf comparison](http://jsperf.com/uri-templates/2))\n\n#### Various ####\n\n* [TLD.js](https://github.com/oncletom/tld.js) - second level domain names\n* [Public Suffix](http://mxr.mozilla.org/mozilla-central/source/netwerk/dns/effective_tld_names.dat?raw=1) - second level domain names\n* [uri-collection](https://github.com/scivey/uri-collection) - underscore based utility for working with many URIs\n\n## Authors ##\n\n* [Rodney Rehm](https://github.com/rodneyrehm)\n* [Various Contributors](https://github.com/medialize/URI.js/graphs/contributors)\n\n\n## Contains Code From ##\n\n* [punycode.js](http://mths.be/punycode) - Mathias Bynens\n* [IPv6.js](http://intermapper.com/support/tools/IPV6-Validator.aspx) - Rich Brown - (rewrite of the original)\n\n\n## License ##\n\nURI.js is published under the [MIT license](http://www.opensource.org/licenses/mit-license). Until version 1.13.2 URI.js was also published under the [GPL v3](http://opensource.org/licenses/GPL-3.0) license - but as this dual-licensing causes more questions than helps anyone, it was dropped with version 1.14.0.\n\n\n## Changelog ##\n\nmoved to [Changelog](./CHANGELOG.md)\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2011 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/urijs/-/urijs-1.19.5.tgz#119683ab4b2fb0bd637e5ea6dd9117bcac68d3e4", "type": "tarball", "reference": "https://registry.yarnpkg.com/urijs/-/urijs-1.19.5.tgz", "hash": "119683ab4b2fb0bd637e5ea6dd9117bcac68d3e4", "integrity": "sha512-48z9VGWwdCV5KfizHsE05DWS5fhK6gFlx5MjO7xu0Krc5FGPWzjlXEVV0nPMrdVuP7xmMHiPZ2HoYZwKOFTZOg==", "registry": "npm", "packageName": "<PERSON><PERSON><PERSON><PERSON>", "cacheIntegrity": "sha512-48z9VGWwdCV5KfizHsE05DWS5fhK6gFlx5MjO7xu0Krc5FGPWzjlXEVV0nPMrdVuP7xmMHiPZ2HoYZwKOFTZOg== sha1-EZaDq0svsL1jfl6m3ZEXvKxo0+Q="}, "registry": "npm", "hash": "119683ab4b2fb0bd637e5ea6dd9117bcac68d3e4"}