{"name": "terser-webpack-plugin", "version": "1.4.5", "description": "Terser plugin for webpack", "license": "MIT", "repository": "webpack-contrib/terser-webpack-plugin", "author": "webpack Contrib Team", "homepage": "https://github.com/webpack-contrib/terser-webpack-plugin", "bugs": "https://github.com/webpack-contrib/terser-webpack-plugin/issues", "main": "dist/cjs.js", "engines": {"node": ">= 6.9.0"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:js": "eslint --cache src test", "lint": "npm-run-all -l -p \"lint:**\"", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test npm run test:coverage", "defaults": "webpack-defaults"}, "files": ["dist"], "peerDependencies": {"webpack": "^4.0.0"}, "dependencies": {"cacache": "^12.0.2", "find-cache-dir": "^2.1.0", "is-wsl": "^1.1.0", "schema-utils": "^1.0.0", "serialize-javascript": "^4.0.0", "source-map": "^0.6.1", "terser": "^4.1.2", "webpack-sources": "^1.4.0", "worker-farm": "^1.7.0"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/preset-env": "^7.5.5", "@commitlint/cli": "^8.1.0", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/defaults": "^5.0.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.8.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^5.2.0", "del": "^4.1.1", "del-cli": "^1.1.0", "eslint": "^6.1.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-import": "^2.18.2", "husky": "^3.0.2", "jest": "^24.8.0", "jest-junit": "^7.0.0", "lint-staged": "^9.2.1", "memory-fs": "^0.4.1", "npm-run-all": "^4.1.5", "prettier": "^1.18.2", "standard-version": "^7.0.0", "uglify-js": "^3.6.0", "webpack": "^4.38.0"}, "keywords": ["uglify", "uglify-js", "uglify-es", "terser", "webpack", "webpack-plugin", "minification", "compress", "compressor", "min", "minification", "minifier", "minify", "optimize", "optimizer"]}