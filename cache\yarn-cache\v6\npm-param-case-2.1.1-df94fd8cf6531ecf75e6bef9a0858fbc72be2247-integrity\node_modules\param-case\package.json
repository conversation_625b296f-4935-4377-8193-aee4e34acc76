{"name": "param-case", "version": "2.1.1", "description": "Param case a string", "main": "param-case.js", "typings": "param-case.d.ts", "files": ["param-case.js", "param-case.d.ts", "LICENSE"], "scripts": {"lint": "standard", "test-std": "mocha -- -R spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail", "test": "npm run lint && npm run test-cov"}, "repository": {"type": "git", "url": "git://github.com/blakeembrey/param-case.git"}, "keywords": ["param", "case", "dash", "hyphen"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/blakeembrey/param-case/issues"}, "homepage": "https://github.com/blakeembrey/param-case", "devDependencies": {"istanbul": "^0.4.3", "mocha": "^3.2.0", "standard": "^9.0.1"}, "dependencies": {"no-case": "^2.2.0"}}