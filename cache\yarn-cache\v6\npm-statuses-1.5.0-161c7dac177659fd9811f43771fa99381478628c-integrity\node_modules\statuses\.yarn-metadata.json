{"manifest": {"name": "statuses", "description": "HTTP status utility", "version": "1.5.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "repository": {"type": "git", "url": "https://github.com/jshttp/statuses.git"}, "license": "MIT", "keywords": ["http", "status", "code"], "files": ["HISTORY.md", "index.js", "codes.json", "LICENSE"], "devDependencies": {"csv-parse": "1.2.4", "eslint": "4.19.1", "eslint-config-standard": "11.0.0", "eslint-plugin-import": "2.9.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "6.0.1", "eslint-plugin-promise": "3.7.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "1.21.5", "raw-body": "2.3.2", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build.js", "fetch": "node scripts/fetch-apache.js && node scripts/fetch-iana.js && node scripts/fetch-nginx.js && node scripts/fetch-node.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "update": "npm run fetch && npm run build"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-statuses-1.5.0-****************************************-integrity\\node_modules\\statuses\\package.json", "readmeFilename": "README.md", "readme": "# Statuses\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nHTTP status utility for node.\n\nThis module provides a list of status codes and messages sourced from\na few different projects:\n\n  * The [IANA Status Code Registry](https://www.iana.org/assignments/http-status-codes/http-status-codes.xhtml)\n  * The [Node.js project](https://nodejs.org/)\n  * The [NGINX project](https://www.nginx.com/)\n  * The [Apache HTTP Server project](https://httpd.apache.org/)\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install statuses\n```\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar status = require('statuses')\n```\n\n### var code = status(Integer || String)\n\nIf `Integer` or `String` is a valid HTTP code or status message, then the\nappropriate `code` will be returned. Otherwise, an error will be thrown.\n\n<!-- eslint-disable no-undef -->\n\n```js\nstatus(403) // => 403\nstatus('403') // => 403\nstatus('forbidden') // => 403\nstatus('Forbidden') // => 403\nstatus(306) // throws, as it's not supported by node.js\n```\n\n### status.STATUS_CODES\n\nReturns an object which maps status codes to status messages, in\nthe same format as the\n[Node.js http module](https://nodejs.org/dist/latest/docs/api/http.html#http_http_status_codes).\n\n### status.codes\n\nReturns an array of all the status codes as `Integer`s.\n\n### var msg = status[code]\n\nMap of `code` to `status message`. `undefined` for invalid `code`s.\n\n<!-- eslint-disable no-undef, no-unused-expressions -->\n\n```js\nstatus[404] // => 'Not Found'\n```\n\n### var code = status[msg]\n\nMap of `status message` to `code`. `msg` can either be title-cased or\nlower-cased. `undefined` for invalid `status message`s.\n\n<!-- eslint-disable no-undef, no-unused-expressions -->\n\n```js\nstatus['not found'] // => 404\nstatus['Not Found'] // => 404\n```\n\n### status.redirect[code]\n\nReturns `true` if a status code is a valid redirect status.\n\n<!-- eslint-disable no-undef, no-unused-expressions -->\n\n```js\nstatus.redirect[200] // => undefined\nstatus.redirect[301] // => true\n```\n\n### status.empty[code]\n\nReturns `true` if a status code expects an empty body.\n\n<!-- eslint-disable no-undef, no-unused-expressions -->\n\n```js\nstatus.empty[200] // => undefined\nstatus.empty[204] // => true\nstatus.empty[304] // => true\n```\n\n### status.retry[code]\n\nReturns `true` if you should retry the rest.\n\n<!-- eslint-disable no-undef, no-unused-expressions -->\n\n```js\nstatus.retry[501] // => undefined\nstatus.retry[503] // => true\n```\n\n[npm-image]: https://img.shields.io/npm/v/statuses.svg\n[npm-url]: https://npmjs.org/package/statuses\n[node-version-image]: https://img.shields.io/node/v/statuses.svg\n[node-version-url]: https://nodejs.org/en/download\n[travis-image]: https://img.shields.io/travis/jshttp/statuses.svg\n[travis-url]: https://travis-ci.org/jshttp/statuses\n[coveralls-image]: https://img.shields.io/coveralls/jshttp/statuses.svg\n[coveralls-url]: https://coveralls.io/r/jshttp/statuses?branch=master\n[downloads-image]: https://img.shields.io/npm/dm/statuses.svg\n[downloads-url]: https://npmjs.org/package/statuses\n", "licenseText": "\nThe MIT License (MIT)\n\nCopyright (c) 2014 <PERSON> <<EMAIL>>\nCopyright (c) 2016 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/statuses/-/statuses-1.5.0.tgz#****************************************", "type": "tarball", "reference": "https://registry.yarnpkg.com/statuses/-/statuses-1.5.0.tgz", "hash": "****************************************", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=", "registry": "npm", "packageName": "statuses", "cacheIntegrity": "sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA== sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="}, "registry": "npm", "hash": "****************************************"}