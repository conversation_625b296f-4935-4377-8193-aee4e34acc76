/* ================================================
   DDCZ-VEHICLE Modern UI Styles
   ================================================ */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: transparent;
    overflow: hidden;
    user-select: none;
}

.vehicle-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(15px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 1;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.vehicle-container.hidden {
    opacity: 0;
    pointer-events: none;
}

.vehicle-menu {
    background: linear-gradient(145deg, #1e1e2e, #2a2a3e);
    border-radius: 20px;
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    width: 480px;
    max-width: 95vw;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.vehicle-container.hidden .vehicle-menu {
    transform: scale(0.95) translateY(20px);
}

/* Custom Scrollbar */
.vehicle-menu::-webkit-scrollbar {
    width: 6px;
}

.vehicle-menu::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.vehicle-menu::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 3px;
}

/* Header */
.menu-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    padding: 20px;
    border-radius: 20px 20px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.menu-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: headerShine 3s ease-in-out infinite;
}

@keyframes headerShine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.header-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.vehicle-icon {
    font-size: 2rem;
    animation: iconBounce 2s ease-in-out infinite;
}

@keyframes iconBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

.header-text h2 {
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.header-text span {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    font-weight: 400;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 1.5rem;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

/* Vehicle Info */
.vehicle-info {
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 15px;
}

.info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    min-width: 120px;
}

.info-label {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 5px;
}

.info-item span:last-child {
    color: white;
    font-weight: 600;
    font-size: 0.95rem;
}

.status-indicator {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem !important;
    font-weight: 500 !important;
}

.status-indicator.engine-on {
    background: rgba(39, 174, 96, 0.2);
    color: #27ae60;
    border: 1px solid rgba(39, 174, 96, 0.3);
}

.status-indicator.engine-off {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

/* Control Sections */
.control-section {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.control-section:last-child {
    border-bottom: none;
}

.section-title {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.subsection-title {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.95rem;
    font-weight: 500;
    margin-bottom: 12px;
    margin-top: 20px;
}

.subsection-title:first-child {
    margin-top: 0;
}

/* Control Grid */
.control-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.control-btn {
    background: linear-gradient(145deg, #2a2a3e, #3a3a4e);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
    text-align: left;
}

.control-btn:hover {
    background: linear-gradient(145deg, #3a3a4e, #4a4a5e);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.control-btn.active {
    background: linear-gradient(145deg, #667eea, #764ba2);
    border-color: rgba(255, 255, 255, 0.3);
}

.btn-icon {
    font-size: 1.5rem;
    min-width: 30px;
    text-align: center;
}

.btn-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.btn-title {
    font-weight: 600;
    font-size: 0.95rem;
}

.btn-subtitle {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
}

/* Doors Grid */
.doors-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

.door-btn {
    background: linear-gradient(145deg, #2a2a3e, #3a3a4e);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 12px 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    color: white;
    min-height: 70px;
}

.door-btn:hover {
    background: linear-gradient(145deg, #3a3a4e, #4a4a5e);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.door-btn.open {
    background: linear-gradient(145deg, #f39c12, #e67e22);
    border-color: rgba(255, 255, 255, 0.3);
}

.door-icon {
    font-size: 1.3rem;
}

.door-label {
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
}

/* Lights Grid */
.lights-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

.light-btn {
    background: linear-gradient(145deg, #2a2a3e, #3a3a4e);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 12px 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    color: white;
    min-height: 70px;
}

.light-btn:hover {
    background: linear-gradient(145deg, #3a3a4e, #4a4a5e);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.light-btn.active {
    background: linear-gradient(145deg, #f1c40f, #f39c12);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 20px rgba(241, 196, 15, 0.4);
}

.light-icon {
    font-size: 1.3rem;
}

.light-label {
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
}

/* Restricted Section */
.restricted-section {
    background: linear-gradient(145deg, #2c1810, #3d2318);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 15px;
    margin: 0 20px 20px 20px;
    padding: 20px;
}

.restricted-section .section-title {
    color: #ffc107;
}

/* Extras Grid */
.extras-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
}

.extra-btn {
    background: linear-gradient(145deg, #2a2a3e, #3a3a4e);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 10px 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    color: white;
    font-size: 0.75rem;
    min-height: 60px;
}

.extra-btn:hover {
    background: linear-gradient(145deg, #3a3a4e, #4a4a5e);
    transform: translateY(-1px);
}

.extra-btn.active {
    background: linear-gradient(145deg, #27ae60, #2ecc71);
    border-color: rgba(255, 255, 255, 0.3);
}

.extra-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Livery Selector */
.livery-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 15px;
}

.livery-nav-btn {
    background: linear-gradient(145deg, #667eea, #764ba2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.livery-nav-btn:hover {
    background: linear-gradient(145deg, #764ba2, #667eea);
    transform: scale(1.1);
}

.livery-nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.livery-display {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.2rem;
    font-weight: 600;
    color: white;
    min-width: 80px;
    justify-content: center;
}

.livery-separator {
    color: rgba(255, 255, 255, 0.5);
}

/* Action Buttons */
.action-buttons {
    padding: 20px;
    display: flex;
    gap: 12px;
}

.action-btn {
    flex: 1;
    background: linear-gradient(145deg, #2a2a3e, #3a3a4e);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: white;
    font-weight: 500;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.save-btn {
    background: linear-gradient(145deg, #27ae60, #2ecc71);
}

.save-btn:hover {
    background: linear-gradient(145deg, #2ecc71, #27ae60);
}

.reset-btn {
    background: linear-gradient(145deg, #e74c3c, #c0392b);
}

.reset-btn:hover {
    background: linear-gradient(145deg, #c0392b, #e74c3c);
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.vehicle-menu {
    animation: slideInUp 0.3s ease-out;
}

.control-btn.pulse {
    animation: pulse 0.6s ease-in-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .vehicle-menu {
        width: 95vw;
        margin: 10px;
    }

    .control-grid {
        grid-template-columns: 1fr;
    }

    .doors-grid,
    .lights-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .extras-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .action-buttons {
        flex-direction: column;
    }
}

/* Hidden class */
.hidden {
    display: none !important;
}
