-- Item usage handlers for Weather Health System

local QBCore = exports['qb-core']:GetCoreObject()

-- Medicine item usage
QBCore.Functions.CreateUseableItem('medicine', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    local healthData = exports['qb-weatherhealth']:getPlayerHealthData(citizenid)
    
    if not healthData or not healthData.diseaseType then
        TriggerClientEvent('QBCore:Notify', source, Lang:t('treatments.no_disease'), 'primary')
        return
    end
    
    -- Check if medicine can treat current disease
    local treatment = Config.TreatmentItems.medicine
    if treatment.diseases and not table.contains(treatment.diseases, healthData.diseaseType) then
        TriggerClientEvent('QBCore:Notify', source, Lang:t('treatments.treatment_failed'), 'error')
        return
    end
    
    -- Remove item
    Player.Functions.RemoveItem('medicine', 1)
    TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['medicine'], 'remove')
    
    -- Trigger treatment on client
    TriggerClientEvent('weatherhealth:client:useTreatment', source, 'medicine')
end)

-- Heat pack item usage
QBCore.Functions.CreateUseableItem('heatpack', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Remove item
    Player.Functions.RemoveItem('heatpack', 1)
    TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['heatpack'], 'remove')
    
    -- Apply warmth bonus
    local treatment = Config.TreatmentItems.heatpack
    TriggerClientEvent('weatherhealth:client:applyTemperatureBonus', source, 'warmth', treatment.warmthBonus, treatment.duration)
    TriggerClientEvent('QBCore:Notify', source, Lang:t('treatments.heatpack_used'), 'success')
end)

-- Cool pack item usage
QBCore.Functions.CreateUseableItem('coolpack', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Remove item
    Player.Functions.RemoveItem('coolpack', 1)
    TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['coolpack'], 'remove')
    
    -- Apply cooling bonus
    local treatment = Config.TreatmentItems.coolpack
    TriggerClientEvent('weatherhealth:client:applyTemperatureBonus', source, 'cool', treatment.coolBonus, treatment.duration)
    TriggerClientEvent('QBCore:Notify', source, Lang:t('treatments.coolpack_used'), 'success')
end)

-- Thermometer item usage
QBCore.Functions.CreateUseableItem('thermometer', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    local healthData = exports['qb-weatherhealth']:getPlayerHealthData(citizenid)
    
    if healthData then
        local temperature = 36.5 -- Normal body temperature
        
        if healthData.diseaseType then
            local disease = Config.HealthEffects.diseases[healthData.diseaseType]
            if disease and table.contains(disease.symptoms, 'fever') then
                temperature = 38.5 + (healthData.diseaseSeverity or 0) * 0.5
            elseif healthData.diseaseType == 'hypothermia' then
                temperature = 35.0 - (healthData.diseaseSeverity or 0) * 0.5
            end
        end
        
        TriggerClientEvent('QBCore:Notify', source, string.format('Body Temperature: %.1f°C', temperature), 'primary')
    else
        TriggerClientEvent('QBCore:Notify', source, 'Body Temperature: 36.5°C (Normal)', 'primary')
    end
end)

-- Vitamin C item usage
QBCore.Functions.CreateUseableItem('vitamin_c', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end

    -- Remove item
    Player.Functions.RemoveItem('vitamin_c', 1)
    TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['vitamin_c'], 'remove')

    -- Boost immunity
    local citizenid = Player.PlayerData.citizenid
    local healthData = exports['qb-weatherhealth']:getPlayerHealthData(citizenid)

    if healthData then
        local oldImmunity = healthData.immunityLevel or 50
        healthData.immunityLevel = math.min(100, oldImmunity + 10)

        -- Save to database immediately
        exports['qb-weatherhealth']:savePlayerHealth(citizenid, healthData)

        TriggerClientEvent('QBCore:Notify', source, string.format('Immunity boosted! %d → %d (+10)', oldImmunity, healthData.immunityLevel), 'success')
        Utils.Debug("Player %s immunity boosted from %d to %d", Player.PlayerData.name, oldImmunity, healthData.immunityLevel)
    end
end)

-- Flu vaccine item usage
QBCore.Functions.CreateUseableItem('flu_vaccine', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    local healthData = exports['qb-weatherhealth']:getPlayerHealthData(citizenid)
    
    if healthData and healthData.diseaseType == 'flu' then
        TriggerClientEvent('QBCore:Notify', source, 'Cannot use vaccine while infected with flu!', 'error')
        return
    end
    
    -- Remove item
    Player.Functions.RemoveItem('flu_vaccine', 1)
    TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['flu_vaccine'], 'remove')
    
    -- Provide flu immunity for 24 hours (game time)
    if healthData then
        healthData.fluImmunity = os.time() + (24 * 60 * 60) -- 24 hours
        TriggerClientEvent('QBCore:Notify', source, 'Flu vaccine administered! Protected for 24 hours.', 'success')
    end
end)

-- Hand warmer item usage
QBCore.Functions.CreateUseableItem('hand_warmer', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Remove item
    Player.Functions.RemoveItem('hand_warmer', 1)
    TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['hand_warmer'], 'remove')
    
    -- Apply small warmth bonus
    TriggerClientEvent('weatherhealth:client:applyTemperatureBonus', source, 'warmth', 3, 180000) -- 3 minutes
    TriggerClientEvent('QBCore:Notify', source, 'Hand warmer activated!', 'success')
end)

-- Cooling towel item usage
QBCore.Functions.CreateUseableItem('cooling_towel', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Remove item
    Player.Functions.RemoveItem('cooling_towel', 1)
    TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['cooling_towel'], 'remove')
    
    -- Apply cooling effect
    TriggerClientEvent('weatherhealth:client:applyTemperatureBonus', source, 'cool', 5, 300000) -- 5 minutes
    TriggerClientEvent('QBCore:Notify', source, 'Cooling towel applied!', 'success')
end)

-- Energy drink item usage
QBCore.Functions.CreateUseableItem('energy_drink', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Remove item
    Player.Functions.RemoveItem('energy_drink', 1)
    TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['energy_drink'], 'remove')
    
    -- Restore some stamina and reduce disease effects temporarily
    local citizenid = Player.PlayerData.citizenid
    local healthData = exports['qb-weatherhealth']:getPlayerHealthData(citizenid)
    
    if healthData and healthData.diseaseType then
        -- Temporarily reduce disease severity
        TriggerClientEvent('weatherhealth:client:temporaryRelief', source, 300000) -- 5 minutes
        TriggerClientEvent('QBCore:Notify', source, 'Energy drink consumed! Temporary relief from symptoms.', 'success')
    else
        TriggerClientEvent('QBCore:Notify', source, 'Energy drink consumed! Feeling energized.', 'success')
    end
end)

-- Hot tea item usage
QBCore.Functions.CreateUseableItem('hot_tea', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Remove item
    Player.Functions.RemoveItem('hot_tea', 1)
    TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['hot_tea'], 'remove')
    
    -- Apply warmth and slight healing
    TriggerClientEvent('weatherhealth:client:applyTemperatureBonus', source, 'warmth', 4, 240000) -- 4 minutes
    
    local citizenid = Player.PlayerData.citizenid
    local healthData = exports['qb-weatherhealth']:getPlayerHealthData(citizenid)
    
    if healthData then
        healthData.health = math.min(100, (healthData.health or 100) + 5)
        TriggerClientEvent('QBCore:Notify', source, 'Hot tea consumed! Feeling warmer and better.', 'success')
    end
end)

-- Ice pack item usage
QBCore.Functions.CreateUseableItem('ice_pack', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    local healthData = exports['qb-weatherhealth']:getPlayerHealthData(citizenid)
    
    if healthData and healthData.diseaseType == 'heatstroke' then
        -- Remove item
        Player.Functions.RemoveItem('ice_pack', 1)
        TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['ice_pack'], 'remove')
        
        -- Cure heatstroke
        TriggerClientEvent('weatherhealth:client:cureDisease', source, 'heatstroke')
        TriggerClientEvent('QBCore:Notify', source, 'Ice pack applied! Heat stroke treated.', 'success')
    else
        TriggerClientEvent('QBCore:Notify', source, 'Ice pack is only effective for heat stroke treatment.', 'error')
    end
end)

-- Emergency blanket item usage
QBCore.Functions.CreateUseableItem('blanket', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Remove item
    Player.Functions.RemoveItem('blanket', 1)
    TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['blanket'], 'remove')
    
    -- Apply significant warmth bonus
    TriggerClientEvent('weatherhealth:client:applyTemperatureBonus', source, 'warmth', 15, 600000) -- 10 minutes
    TriggerClientEvent('QBCore:Notify', source, 'Emergency blanket deployed! Significant warmth provided.', 'success')
end)

-- First aid kit item usage
QBCore.Functions.CreateUseableItem('first_aid_kit', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    local healthData = exports['qb-weatherhealth']:getPlayerHealthData(citizenid)
    
    if not healthData or not healthData.diseaseType then
        TriggerClientEvent('QBCore:Notify', source, 'No medical condition to treat.', 'primary')
        return
    end
    
    -- Remove item
    Player.Functions.RemoveItem('first_aid_kit', 1)
    TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['first_aid_kit'], 'remove')
    
    -- High chance to cure any disease
    local cureChance = 90
    if math.random(1, 100) <= cureChance then
        TriggerClientEvent('weatherhealth:client:cureDisease', source, healthData.diseaseType)
        TriggerClientEvent('QBCore:Notify', source, 'First aid treatment successful!', 'success')
    else
        TriggerClientEvent('QBCore:Notify', source, 'First aid treatment helped but did not cure the condition.', 'primary')
        -- Reduce severity instead
        if healthData.diseaseSeverity > 1 then
            healthData.diseaseSeverity = healthData.diseaseSeverity - 1
        end
    end
end)

-- Utility function
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

-- Additional medical items
QBCore.Functions.CreateUseableItem('antibiotics', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end

    local citizenid = Player.PlayerData.citizenid
    local healthData = exports['qb-weatherhealth']:getPlayerHealthData(citizenid)

    if not healthData or not healthData.diseaseType then
        TriggerClientEvent('QBCore:Notify', source, 'No serious infection to treat', 'primary')
        return
    end

    -- Antibiotics work on flu and serious infections
    if healthData.diseaseType == 'flu' or healthData.diseaseType == 'hypothermia' then
        Player.Functions.RemoveItem('antibiotics', 1)
        TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['antibiotics'], 'remove')

        -- High cure chance for antibiotics
        if math.random(1, 100) <= 95 then
            TriggerClientEvent('weatherhealth:client:cureDisease', source, healthData.diseaseType)
            TriggerClientEvent('QBCore:Notify', source, 'Antibiotics successfully treated the infection!', 'success')
        else
            TriggerClientEvent('QBCore:Notify', source, 'Antibiotics helped but did not cure completely', 'primary')
        end
    else
        TriggerClientEvent('QBCore:Notify', source, 'Antibiotics are not effective for this condition', 'error')
    end
end)

QBCore.Functions.CreateUseableItem('pain_killers', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end

    Player.Functions.RemoveItem('pain_killers', 1)
    TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['pain_killers'], 'remove')

    -- Temporary relief from disease effects
    TriggerClientEvent('weatherhealth:client:temporaryRelief', source, 600000) -- 10 minutes
    TriggerClientEvent('QBCore:Notify', source, 'Pain killers provide temporary relief from symptoms', 'success')
end)

QBCore.Functions.CreateUseableItem('cough_syrup', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end

    local citizenid = Player.PlayerData.citizenid
    local healthData = exports['qb-weatherhealth']:getPlayerHealthData(citizenid)

    if healthData and (healthData.diseaseType == 'cold' or healthData.diseaseType == 'flu') then
        Player.Functions.RemoveItem('cough_syrup', 1)
        TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['cough_syrup'], 'remove')

        -- Reduce cough symptoms
        TriggerClientEvent('weatherhealth:client:reduceSymptom', source, 'cough', 300000) -- 5 minutes
        TriggerClientEvent('QBCore:Notify', source, 'Cough syrup reduces coughing', 'success')
    else
        TriggerClientEvent('QBCore:Notify', source, 'No cough symptoms to treat', 'primary')
    end
end)

-- Weather protection items
QBCore.Functions.CreateUseableItem('weather_radio', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end

    local currentWeather = exports['qb-weatherhealth']:getCurrentWeather()
    local temp = Config.WeatherTemperature[currentWeather] or 20

    local forecast = string.format(
        "Weather Radio Forecast:\nCurrent: %s\nTemperature: %d°C\nRecommendation: %s",
        currentWeather,
        temp,
        temp < 10 and "Dress warmly!" or temp > 25 and "Dress lightly!" or "Comfortable weather"
    )

    TriggerClientEvent('QBCore:Notify', source, forecast, 'primary', 8000)
end)

QBCore.Functions.CreateUseableItem('emergency_flare', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end

    Player.Functions.RemoveItem('emergency_flare', 1)
    TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['emergency_flare'], 'remove')

    -- Notify nearby players
    TriggerClientEvent('weatherhealth:client:emergencyFlare', -1, GetEntityCoords(GetPlayerPed(source)))
    TriggerClientEvent('QBCore:Notify', source, 'Emergency flare activated! Help is on the way.', 'success')
end)

QBCore.Functions.CreateUseableItem('survival_kit', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end

    Player.Functions.RemoveItem('survival_kit', 1)
    TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['survival_kit'], 'remove')

    -- Give multiple survival items
    Player.Functions.AddItem('medicine', 2)
    Player.Functions.AddItem('heatpack', 3)
    Player.Functions.AddItem('blanket', 1)
    Player.Functions.AddItem('energy_drink', 2)

    TriggerClientEvent('QBCore:Notify', source, 'Survival kit unpacked! Items added to inventory.', 'success')
end)

QBCore.Functions.CreateUseableItem('portable_heater', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end

    Player.Functions.RemoveItem('portable_heater', 1)
    TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['portable_heater'], 'remove')

    -- Provide significant warmth for extended time
    TriggerClientEvent('weatherhealth:client:applyTemperatureBonus', source, 'warmth', 20, 1800000) -- 30 minutes
    TriggerClientEvent('QBCore:Notify', source, 'Portable heater activated! Significant warmth for 30 minutes.', 'success')
end)

QBCore.Functions.CreateUseableItem('cooling_vest', function(source, item)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end

    Player.Functions.RemoveItem('cooling_vest', 1)
    TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items['cooling_vest'], 'remove')

    -- Provide cooling for extended time
    TriggerClientEvent('weatherhealth:client:applyTemperatureBonus', source, 'cool', 15, 1800000) -- 30 minutes
    TriggerClientEvent('QBCore:Notify', source, 'Cooling vest activated! Temperature regulation for 30 minutes.', 'success')
end)

print("^2[WeatherHealth]^7 Item handlers loaded")
