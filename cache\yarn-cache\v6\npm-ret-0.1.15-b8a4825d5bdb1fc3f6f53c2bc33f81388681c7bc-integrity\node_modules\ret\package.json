{"name": "ret", "description": "Tokenizes a string that represents a regular expression.", "keywords": ["regex", "regexp", "regular expression", "parser", "tokenizer"], "version": "0.1.15", "repository": {"type": "git", "url": "git://github.com/fent/ret.js.git"}, "author": "<PERSON><PERSON> (https://github.com/fent)", "main": "./lib/index.js", "files": ["lib"], "scripts": {"test": "istanbul cover vows -- --spec test/*-test.js"}, "directories": {"lib": "./lib"}, "devDependencies": {"istanbul": "*", "vows": "*"}, "engines": {"node": ">=0.12"}, "license": "MIT"}