{"manifest": {"name": "object.pick", "description": "Returns a filtered copy of an object with only the specified keys, similar to `_.pick` from lodash / underscore.", "version": "1.3.0", "homepage": "https://github.com/jonschlinkert/object.pick", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/object.pick.git"}, "bugs": {"url": "https://github.com/jonschlinkert/object.pick/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"isobject": "^3.0.1"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.1.2", "vinyl": "^2.0.0"}, "keywords": ["object", "pick"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["extend-shallow", "get-value", "mixin-deep", "set-value"], "highlight": "object.omit"}, "reflinks": ["verb"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-object-pick-1.3.0-87a10ac4c1694bd2e1cbf53591a66141fb5dd747-integrity\\node_modules\\object.pick\\package.json", "readmeFilename": "README.md", "readme": "# object.pick [![NPM version](https://img.shields.io/npm/v/object.pick.svg?style=flat)](https://www.npmjs.com/package/object.pick) [![NPM monthly downloads](https://img.shields.io/npm/dm/object.pick.svg?style=flat)](https://npmjs.org/package/object.pick)  [![NPM total downloads](https://img.shields.io/npm/dt/object.pick.svg?style=flat)](https://npmjs.org/package/object.pick) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/object.pick.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/object.pick)\n\n> Returns a filtered copy of an object with only the specified keys, similar to `_.pick` from lodash / underscore.\n\nYou might also be interested in [object.omit](https://github.com/jonschlinkert/object.omit).\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save object.pick\n```\n\n## benchmarks\n\nThis is the [fastest implementation](http://jsperf.com/pick-props) I tested. Pull requests welcome!\n\n## Usage\n\n```js\nvar pick = require('object.pick');\n\npick({a: 'a', b: 'b'}, 'a')\n//=> {a: 'a'}\n\npick({a: 'a', b: 'b', c: 'c'}, ['a', 'b'])\n//=> {a: 'a', b: 'b'}\n```\n\n## About\n\n### Related projects\n\n* [extend-shallow](https://www.npmjs.com/package/extend-shallow): Extend an object with the properties of additional objects. node.js/javascript util. | [homepage](https://github.com/jonschlinkert/extend-shallow \"Extend an object with the properties of additional objects. node.js/javascript util.\")\n* [get-value](https://www.npmjs.com/package/get-value): Use property paths (`a.b.c`) to get a nested value from an object. | [homepage](https://github.com/jonschlinkert/get-value \"Use property paths (`a.b.c`) to get a nested value from an object.\")\n* [mixin-deep](https://www.npmjs.com/package/mixin-deep): Deeply mix the properties of objects into the first object. Like merge-deep, but doesn't clone. | [homepage](https://github.com/jonschlinkert/mixin-deep \"Deeply mix the properties of objects into the first object. Like merge-deep, but doesn't clone.\")\n* [set-value](https://www.npmjs.com/package/set-value): Create nested values and any intermediaries using dot notation (`'a.b.c'`) paths. | [homepage](https://github.com/jonschlinkert/set-value \"Create nested values and any intermediaries using dot notation (`'a.b.c'`) paths.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Building docs\n\n_(This document was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme) (a [verb](https://github.com/verbose/verb) generator), please don't edit the readme directly. Any changes to the readme must be made in [.verb.md](.verb.md).)_\n\nTo generate the readme and API documentation with [verb](https://github.com/verbose/verb):\n\n```sh\n$ npm install -g verb verb-generate-readme && verb\n```\n\n### Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm install -d && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT license](https://github.com/jonschlinkert/object.pick/blob/master/LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.2.0, on October 27, 2016._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2016, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/object.pick/-/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747", "type": "tarball", "reference": "https://registry.yarnpkg.com/object.pick/-/object.pick-1.3.0.tgz", "hash": "87a10ac4c1694bd2e1cbf53591a66141fb5dd747", "integrity": "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=", "registry": "npm", "packageName": "object.pick", "cacheIntegrity": "sha512-tqa/UMy/CCoYmj+H5qc07qvSL9dqcs/WZENZ1JbtWBlATP+iVOe778gE6MSijnyCnORzDuX6hU+LA4SZ09YjFQ== sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c="}, "registry": "npm", "hash": "87a10ac4c1694bd2e1cbf53591a66141fb5dd747"}