if Config.Language ~= "fr" then return end

Translations = {
    ["Reference"] = "RÉFÉRENCES",
    ["Icons"] = "ICÔNES",
    ["Colors"] = "COULEURS",
    ["RadialComm"] = "COMMUNICATION RADIALE",
    ["Radio"] = "RADIO",
    ["Disconnected"] = "DÉCONNECTÉ",
    ["SouthUnits"] = "UNITÉS SUD",
    ["NorthUnits"] = "UNITÉS NORD",
    ["SpecialUnits"] = "UNITÉS SPÉCIALES",
    ["EMSUnits"] = "UNITÉS EMS",
    ["Interaction"] = "INTERACTION",
    ["CitizenInteraction"] = "INTERACTION AVEC LE CITOYEN",
    ["Search"] = "Recherche",
    ["Wifes"] = "MENOTTER",
    ["Escort"] = "ESCORTER",
    ["PutInVehicle"] = "MONTER/DESCENDRE DU VÉHICULE",
    ["JumpTo"] = "SAUTER À",
    ["HealWounds"] = "SOIGNER LES BLESSURES",
    ["PutTakeAnkle"] = "Mettre/prendre le brassard de cheville",
    ["LogsDate"] = "DATES DES JOURNAUX",
    ["Localize"] = "LOCALISER",
    ["Tase"] = "TASE",
    ["revive"] = "Relancer",
    ["VehicleInteraction"] = "INTERACTION AVEC LES VÉHICULES",
    ["VehicleInformation"] = "INFORMATION SUR LE VÉHICULE",
    ["SeizeVehicle"] = "SAISIR LE VÉHICULE",
    ["CallTow"] = "Appelez la dépanneuse",
    ["ForceLock"] = "FORCE LOCK",
    ["StopTraffic"] = "ARRÊTER LA CIRCULATION",
    ["ReduceTraffic"] = "RÉDUIRE LA CIRCULATION",
    ["ResumeTraffic"] = "REPRENDRE LA CIRCULATION",
    ["Availabel"] = "DISPONIBLE",
    ["WeaponsConfiguration"] = "CONFIGURATION DES ARMES",
    ["ShowHideWeapons"] = "Afficher/Masquer les armes",
    ["PistolPos"] = "POSITION DU PISTOLET",
    ["RiflePos"] = "POSITION DU FUSIL",
    ["Front"] = "DEVANT",
    ["Behind"] = "DERIÈRE",
    ["WaistCart"] = "CARTOUCHE À LA TAILLE",
    ["NormalCart"] = "CARTOUCHE NORMALE",
    ["ChestCart"] = "CARTOUCHE DE POITRINE",
    ["ThighCart"] = "CARTOUCHE DE CUISSE",
    ["LegCart"] = "CARTOUCHE DE JAMBE",
    ["SeparateLegCart"] = "CARTOUCHE DE JAMBE SÉPARÉE",
    ["Chest"] = "POITRINE",
    ["Back"] = "DOS",
    ["PoliceObjects"] = "OBJETS DE POLICE",
    ["Cone"] = "CÔNE",
    ["Barrier"] = "BARRIÈRE",
    ["Sign"] = "PANNEAU",
    ["Spikes"] = "CLAIRONS",
    ["Radar"] = "RADAR",
    ["Delete"] = "SUPPRIMER",
    ["Emergencies"] = "URGENCES",
    ["NoAlertRecived"] = "Aucune alerte reçue",
    ["Settings"] = "PARAMÈTRES",
    ["Guide"] = "Guide d'utilisation",
    ["General"] = "Général",
    ["AlertsCode"] = "Alertes 488, 487, 487-V",
    ["DrugTrafficking"] = "Trafic de drogue",
    ["VehicleRobs"] = "Cambriolages de véhicules",
    ["Alerts215"] = "Alertes 215 / Armes",
    ["Radars"] = "Radars",
    ["KeyToAlert"] = "Clé pour aller à l'alerte",
    ["DeleteAlertKey"] = "Clé de suppression d'alerte",
    ["EmergencyOpenKey"] = "Clé d'ouverture des urgences",
    ["Equipment"] = "Équipement",
    ["Cone"] = "Cône",
    ["ConeDesc"] = "Le cône",
    ["Barriers"] = "Barrières",
    ["BarriersDesc"] = "Les barrières",
    ["TrafficLights"] = "Feux de circulation",
    ["TrafficLightsDesc"] = "Les panneaux de signalisation routière",
    ["Spikes"] = "Claireons",
    ["SpikesDesc"] = "Les clairons",
    ["Radar"] = "Radar",
    ["RadarDesc"] = "Le radar",
    ["K9Title"] = "Contrôle K9",
    ["K9Follow"] = "Suivre",
    ["K9FollowDesc"] = "Ordonner à l'unité K9 de vous suivre",
    ["K9DontMove"] = "Ne bouge pas",
    ["K9DontMoveDesc"] = "Ordonner à l'unité K9 de rester en place",
    ["K9Sit"] = "Assis",
    ["K9SitDesc"] = "Ordonner à l'unité K9 de s'asseoir",
    ["K9LieDown"] = "Coucher",
    ["K9LieDownDesc"] = "Ordonner à l'unité K9 de se coucher",
    ["K9SearhArea"] = "Rechercher dans la zone",
    ["K9SearhAreaDesc"] = "Ordonner à l'unité K9 de rechercher dans la zone",
    ["K9ReturnCar"] = "Retourner à la voiture",
    ["K9ReturnCarDesc"] = "Ordonner à l'unité K9 de retourner à la voiture",

    -- RADIALS MSG --
    ['10.8'] = "En attente d'affectation",
    ['10.10'] = 'Effectuant un 10.10, bon service !',
    ['Cod 7'] = 'Faisant une pause technique',
    ['254-V'] = 'Initiation du 254-V à %s [ %s ] dans %s',
    ['487-V'] = 'Visuel sur le dernier 487-V, %s [$s]',
    ['Cod 2'] = 'Début de patrouille régulière',
    ['10.22'] = 'Direction du poste de police',
    ['6-Adam'] = 'Répondant en tant que 6-Adam',
    ['10.98'] = '10.98 vers le dernier incident, procédant avec le 10.95 pour le Code 2',
    ['Veh 488'] = 'Véhicule impliqué dans un 488 de %s : %s [%s]',
    ['Veh 487'] = 'Véhicule impliqué dans un 487 de %s : %s [%s]',
    ['Veh Alt'] = 'Véhicule impliqué dans une altercation %s [%s] dans %s',
    ['10.6'] = 'Effectuant un contrôle de la circulation sur %s [%s] dans %s',

    -- COMMANDES ME --
    ['10-20ME'] = 'Tend la main vers la radio et appuie sur le bouton de localisation',
    ['QRRME'] = 'Tend la main vers la radio et appuie sur le bouton de panique',
    ['Agentatrisk'] = 'Agent en danger',
    ['domyfinguer'] = 'Après quelques secondes, le résultat sortirait : %s',
    ['VehicleinofME'] = 'Ouvre la porte du véhicule, fait monter la personne, attache sa ceinture et ferme la porte',
    ['VehicleofinME'] = 'Ouvre la porte du véhicule, fait descendre la personne, enlève sa ceinture et ferme la porte',

    -- NOTIFIER --
    ['noSeat'] = 'Pas de places disponibles',

    -- Équipement de police
    ['PoliceEquipment'] = 'Équipement de police',
    ['Equipment'] = 'Équipement',
    ['EquipmentDesc'] = "Accéder à l'équipement de police",
    ['LeaveEquipment'] = "Laisser l'équipement",
    ['LeaveEquipmentDesc'] = 'Laisser votre équipement de police ici',
    ['PoliceInventory'] = 'Inventaire de police',
    ['PoliceInventoryDesc'] = "Pour laisser de la nourriture, de l'eau, etc",
    ['EvidenceProof'] = 'Preuves / Preuves',
    ['EvidenceProofDesc'] = 'Accéder aux preuves / preuves',

    -- Holster
    ['DoHide'] = "do qu'il cache quelque chose sous ses vêtements",
    ['DoShow'] = "do qu'il fait un geste montrant ses armes",
    ['SomethingWrong'] = "Il semble y avoir eu une erreur",
    ['HipHolster'] = "Vous avez changé la position du pistolet à la hanche.",
    ['BackHolster'] = "Vous avez changé la position du pistolet à l'arrière",
    ['LegHolster'] = 'Vous avez déplacé la position du pistolet à la jambe',
    ['UpperHolster'] = 'Vous avez déplacé la position du pistolet à la poitrine',
    ['UnderPantsHolster'] = 'Vous avez changé la position du pistolet sous les pantalons',
    ['LongHolsterBack'] = "Vous avez changé la position des armes longues à l'arrière",
    ['LongHolsterFront'] = "Vous avez changé la position des armes longues à l'avant.",
    ["NoPersonNear"] = "Il n'y a personne à proximité",

    ["VehicleRob"] = "Vol de véhicule",
    ["VehicleRobDesc"] = "Un véhicule a été volé",
    ["Call911"] = "Appel du 911",
    ["ForensicTitle"] = "Rapport d'analyse médico-légale",
    ["ForensicDesc"] = "À travers ce rapport, le Département scientifique de la police de San Andreas montre l'analyse complète des preuves jointes, le moment approximatif du fait ou en cas d'ignorance de l'heure de collecte des preuves et/ou d'analyse de celles-ci.",
    ["EvidenceOf"] = "Preuves de",
    ["ApproximateTime"] = "Temps approximatif",
    ["MinutesAnd"] = "minutes et",
    ["SecondAprox"] = "Secondes approx",
    ["Shot"] = "Tir",
    ["Calibre"] = "Calibre",
    ["Identifier"] = "Identifiant",
    ["Model"] = "Modèle",
    ["Amount"] = "Quantité",

    ["January"] = "Janvier",
    ["February"] = "Février",
    ["March"] = "Mars",
    ["April"] = "Avril",
    ["May"] = "Mai",
    ["June"] = "Juin",
    ["July"] = "Juillet",
    ["August"] = "Août",
    ["September"] = "Septembre",
    ["October"] = "Octobre",
    ["November"] = "Novembre",
    ["December"] = "Décembre",

    ["Shoot"] = "Tirer",
    ["BloodRemains"] = "Restes de sang",
    ["BulletImpact"] = "Impact de balle",
    ["VehicleBody"] = "Restes de carrosserie de véhicule",
    ["Fingerprint"] = "Empreinte digitale",
    ["Weapon"] = "Arme",
    ["Drug"] = "Drogue",
    ["Fingerprints"] = "Empreintes digitales",
    ["Of"] = "de",
    ["Speeding"] = "Excès de vitesse",
    ["PlateCod9"] = "Plaque en Code-9",
    ["215"] = "Cod 215 - Tir",

    ["ExistVehicleInSpawn"] = "Il y a un autre véhicule sur la place, attendez qu'il parte",
    ["MustLook"] = "Vous devez être dans ou regarder un véhicule",
    ["ExistHelicopterInSpawn"] = "Il y a un autre hélicoptère sur la place, attendez qu'il parte",
    ["ExistBoatInSpawn"] = "Il y a un autre bateau sur la place, attendez qu'il parte",
    ["VehicleConfiscated"] = "Vous avez saisi le véhicule",
    ["CouldntOpenLock"] = "La serrure n'a pas pu être ouverte",
    ["NoEvidence"] = "Vous n'avez aucune preuve à analyser",
    ["RespectRol"] = "Veuillez respecter le rôle environnemental",
    ["CantUncuff"] = "Vous ne pouvez pas le détacher pour le moment",
    ["CantDoThis"] = "Vous ne pouvez pas faire ça",
    ["HasToBeCuffed"] = "Le joueur doit être menotté pour effectuer cette action",
    ["NotCuffed"] = "Le joueur n'est pas menotté",
    ["PersonFar"] = "Le joueur est loin",
    ["InvalidK9Veh"] = "Vous n'avez pas le véhicule de l'unité K9",
    ["AlreadyCallK9"] = "Vous avez déjà appelé l'unité K9",
    ["K9Found"] = "On dirait que l'unité K9 a trouvé quelque chose",
    ["K9NotFound"] = "L'unité K9 n'a rien trouvé",
    ["CantFast"] = "Vous ne pouvez pas faire ça si vite",
    ["MustEnterNumber"] = "Vous devez entrer un nombre",
    ["InvNotSupported"] = "Votre système d'inventaire n'est pas encore pris en charge",
    ["ChangedCloth"] = "Vous avez changé de vêtements",
    ["NoFederalClothAvailable"] = "Il n'y a actuellement aucun vêtement fédéral disponible pour votre genre",
    ["PedCantChangeCloth"] = "Les piétons ne peuvent pas changer ou porter des vêtements fédéraux",
    ["CantSendEmpty"] = "Vous ne pouvez pas envoyer un vide",
    ["PertenencesPickUp"] = "Vous avez ramassé vos affaires",
    ["LeavePertenences"] = "Vous avez laissé vos affaires",
    ["NoPhotoSpace"] = "Vous n'avez pas d'espace pour prendre la photo",
    ["NoSpaceInInv"] = "Vous n'avez pas d'espace dans votre inventaire pour stocker cela",
    ["ObtainedFingerpritns"] = "Vous avez obtenu des preuves avec des empreintes digitales",
    ["NoFingerFound"] = "Vous n'avez trouvé aucune empreinte digitale",
    ["EvidenceNotCategorized"] = "Cette preuve n'est pas encore catégorisée",
    ["PlayerNotConnected"] = "Le joueur n'est pas connecté ou vous n'avez pas défini une heure valide",
    ["NewLimitation"] = "Une nouvelle limitation a été créée",
    ["UpdatedLimitation"] = "Une limitation a été mise à jour",
    ["LimitationRemoved"] = "Une limitation a été supprimée",
    ["CantFindLimitation"] = "La limitation n'a pas pu être trouvée",
    ["ProblemCreateNote"] = "Il y a eu un problème lors de la création de la note",
    ["ProblemCreateReport"] = "Il y a eu un problème lors de la création du rapport",
    ["EnterMaxSpeed"] = "Entrez la vitesse maximale",
    ["Speed"] = "Vitesse",
    ["Assign"] = "Attribuer",
    ["RemainSentance"] = "Phrase restante:",
    ["Month"] = "mois",
    ["InvalidVeh"] = "Véhicule invalide pour effectuer cette action",
    
    ["AgentAlert"] = "AGENT",
    ["VehicleAlert"] = "VÉHICULE",
    ["PlateAlert"] = "PLAQUE",
    ["SpeedAlert"] = "VITESSE",
    ["WeaponAlert"] = "ARME",
    ["ErrorOccurred"] = "Une erreur s'est produite",
    ["NoTabPermission"] = "VOUS N'AVEZ PAS L'AUTORISATION D'ACCÉDER À CET ONGLET",
    ["AssignedByDispatch"] = "Assigné par la Dispatch",
    ["VehicleMods"] = "Modifications du véhicule",
    ["Enabled"] = "Activé",
    ["Disabled"] = "Désactivé",

    ['camera'] = {
        ['takepick'] = 'Prendre une photo', -- Only if you use origen_notify
        ['zoom'] = 'Zoom agrandir', -- Only if you use origen_notify
        ['cancel'] = 'Annuler', -- Only if you use origen_notify
        ['fullText'] = '%s - Prendre une photo\n%s - Zoom\n%s - Annuler',
    },
    
    ['objects'] = {
        ['place'] = 'Placer objet', -- Only if you use origen_notify
        ['prev'] = 'Objet précédent', -- Only if you use origen_notify
        ['next'] = 'Objet suivant', -- Only if you use origen_notify
        ['cancel'] = 'Annuler', -- Only if you use origen_notify
        ['fullText'] = '%s - Placer objet\n%s - Objet précédent\n%s - Objet suivant\n%s - Annuler',
    },

    ['CollectEvidence'] = 'Collecter des preuves',
    ['ClearEvidence'] = 'Effacer les preuves',
    ['EnterPlate'] = 'Entrer la plaque',
    ['ImpoundedVehicles'] = 'Véhicules confisqués',
    ['RequestVeh'] = 'Demander un véhicule',
    ['Heliport'] = 'Héliport',
    ['TakeOutHeli'] = 'Sortir l\'hélicoptère',
    ['Pier'] = 'Quai',
    ['TakeOutBoat'] = 'Sortir le bateau',
    ['ConfiscateVehicle'] = 'Véhicule confisqué',
    ['PoliceFacilities'] = 'Installations de police',
    ['Confiscated'] = 'Confisqué',
    ['k9Attack'] = 'Attaque',
    ['ClosestAlert'] = "Vous êtes trop près de l'avertissement",

    -- Commandes
    ['OpenPoliceCad'] = 'Ouvrir cad policier',
    ['VehicleRadar'] = 'Radar de véhicules',
    ['LockRadar'] = 'Verrouiller le radar',
    ['MoveRadar'] = 'Déplacer le radar',
    ['NextAlert'] = 'Alerte de dispatch suivante',
    ['PreviousAlert'] = 'Alerte de dispatch précédente',
    ['K9Menu'] = 'Menu de contrôle K9',
    ['SirensKey'] = 'Activer les sirènes',
    ['LightsSirens'] = 'Activer les feux',
    ['HandCuff'] = 'Police: Menotter / Déménotter',
    ['QRR'] = 'Police: QRR',
    ['Ten20'] = 'Police: 10-20',
    ['Tackle'] = 'Police: Faire une charge',
    ['VehicleInto'] = 'Mettre le pédiatre dans le véhicule',
    ['QuickAccess'] = 'Ouvrir le menu d\'accès rapide',
    ['Minimap'] = 'Mode minimap',
    ['TalkRadio'] = 'Parler à la radio',

    ['CantUseItem'] = "Vous ne pouvez pas utiliser cet objet",

    ["InvalidVehicleToConfiscate"] = "Véhicule invalide à confisquer",
    ["TowTruckOnWay"] = "Une dépanneuse est en route",
    ["TowTruckArrived"] = "La dépanneuse est arrivée et le véhicule a été chargé.",
    ["VehicleCannotBeFound"] = "Le véhicule est introuvable...",
    ['NoMoney'] = "Vous n'avez pas assez d'argent.",
    ['PoliceBill'] = 'Amende de police',

    -- Translations
    
    ["Home"] = "ACCUEIL",
    ["Dispatch"] = "DÉSPATCHE",
    ["CitizenSearch"] = "RECHERCHE DE CITOYENS",
    ["Reports"] = "RAPPORTS",
    ["Cameras"] = "CAMÉRAS",
    ["Polices"] = "POLICES",
    ["Vehicles"] = "VÉHICULES",
    ["CriminalCode"] = "CODE PÉNAL",
    ["CriminalCodeAbrev"] = "C. PÉNAL",
    ["SearchCapture"] = "RECHERCHE ET CAPTURE",
    ["Debtors"] = "DÉBITEURS",
    ["FederalManagement"] = "GESTION FÉDÉRALE",
    ["AgentManagement"] = "GESTION D'AGENT",
    ["SecurityCameras"] = "CAMÉRAS DE SÉCURITÉ",
    ["PublicServices"] = "SERVICES PUBLICS",
    ["NoPoliceDuty"] = "PAS D'AGENTS DE SERVICE",
    ["PoliceOnDuty"] = "AGENT DE SERVICE",
    ["PoliceSOnDuty"] = "AGENTS DE SERVICE",
    ["OutDuty"] = "HORS SERVICE",
    ["InDuty"] = "EN SERVICE",
    ["TimeControl"] = "CONTROLE DU TEMPS",
    ["Radio"] = "RADIO",
    ["Duty"] = "SERVICE",
    ["WelcomeTitle"] = "BIENVENUE SUR LE RÉSEAU INTERNE DE LA POLICE",
    ["WelcomeTitleAmbulance"] = "BIENVENUE SUR LE RÉSEAU INTERNE DES PARAMÉDICS",
    ["WelcomeDescAmbulance"] = "Bienvenue sur l'application officielle des paramédics. Cette application a été conçue pour améliorer l'efficacité et la communication dans le travail quotidien des paramédics.",
    ["WelcomeDesc"] = "Bienvenue sur l'application officielle de la police et du shérif. Cette application a été conçue pour améliorer l'efficacité et la communication dans le travail quotidien des agents de police et des shérifs.",
    ["NotInDuty"] = "VOUS N'ÊTES PAS EN SERVICE",
    ["AgentsOnDuty"] = "AGENTS EN SERVICE",
    ["DeptAbrev"] = "DÉPT.",
    ["Rank"] = "GRADE",
    ["Agent"] = "AGENT",
    ["Status"] = "STATUT",
    ["LocAbrev"] = "LIEU",
    ["BroadcastSAFD"] = "DIFFUSION SAFD",
    ["BroadcastSapd"] = "DIFFUSION SAPD",
    ["SouthUnits"] = "UNITÉS SUD",
    ["Talk"] = "PARLER",
    ["NorthUnits"] = "UNITÉS NORD",
    ["SpecialUnits"] = "UNITÉS SPÉCIALES",
    ["EMSUnits"] = "UNITÉS EMS",
    ["NoUsersChannel"] = "AUCUN UTILISATEUR DANS CE CANAL",
    ["Available"] = "DISPONIBLE",
    ["NotAvailable"] = "NON DISPONIBLE",
    ["InternalRadio"] = "RADIO INTERNE",
    ["TypeMessage"] = "SAISISSEZ VOTRE MESSAGE...",
    ["Emergencies"] = "URGENCES",
    ["Notice"] = "AVIS",
    ["Title"] = "TITRE",
    ["Location"] = "EMPLACEMENT",
    ["Time"] = "HEURE",
    ["DetailedDesc"] = "DESCRIPTION DÉTAILLÉE",
    ["Notes"] = "NOTES",
    ["AddNoteToEmergency"] = "AJOUTER UNE NOTE À L'URGENCE",
    ["SaveNote"] = "ENREGISTRER LA NOTE",
    ["SendToUnit"] = "ENVOYER À L'UNITÉ",
    ["AvailableUnits"] = "UNITÉS DISPONIBLES",
    ["LastAlerts"] = "DERNIERS ALERTES",
    ["RefAbrev"] = "RÉF.",
    ["Emergency"] = "URGENCE",
    ["Ago"] = "IL Y A :",
    ["Units"] = "UNITÉS",
    ["NoRecived"] = "AUCUN ALERTES REÇUES",
    ["DeleteAlert"] = "SUPPRIMER L'ALERTE",
    ["TimeHistory"] = "HISTORIQUE DU TEMPS",
    ["Agent"] = "AGENT",
    ["ClockIn"] = "ENREGISTRER L'HEURE D'ARRIVÉE",
    ["ClockOut"] = "ENREGISTRER L'HEURE DE DÉPART",
    ["Total"] = "TOTAL",
    ["ShowingRecords"] = "Affichage des enregistrements de DÉBUT à FIN sur un total de TOTAL enregistrements",
    ["TopWorkers"] = "MEILLEURS TRAVAILLEURS",
    ["MinAbrev"] = "min",
    ["Cancel"] = "Annuler",
    ["sProcessing"] = "Traitement...",
    ["sLengthMenu"] = "Affichage de 20 enregistrements",
    ["sZeroRecords"] = "Aucun résultat trouvé",
    ["sEmptyTable"] = "Aucune donnée disponible dans le tableau",
    ["sInfo"] = "Affichage des enregistrements de DÉBUT à FIN sur un total de TOTAL enregistrements",
    ["sInfoEmpty"] = "Affichage des enregistrements de 0 à 0 sur un total de 0 enregistrements",
    ["sInfoFiltered"] = "(filtrage à partir d'un total de MAX enregistrements)",
    ["sSearch"] = "Rechercher :",
    ["sLoadingRecords"] = "Chargement...",
    ["oPaginateFirst"] = "Premier",
    ["oPaginateLast"] = "Dernier",
    ["oPaginateNext"] = "Suivant",
    ["oPaginatePrevious"] = "Précédent",
    ["sSortAscending"] = "] = Activer pour trier la colonne par ordre croissant",
    ["sSortDescending"] = "] = Activer pour trier la colonne par ordre décroissant",
    ["Citizens"] = "CITOYENS",
    ["CitizenSearch"] = "RECHERCHE DE CITOYEN",
    ["CitizenList"] = "LISTE DES CITOYENS",
    ["SearchCitizen"] = "Rechercher un citoyen...",
    ["PerformSearch"] = "RÉALISER UNE RECHERCHE POUR AFFICHER LES RÉSULTATS",
    ["CitizenProfile"] = "PROFIL DU CITOYEN",
    ["SelectACitizen"] = "SÉLECTIONNEZ UN CITOYEN POUR CHARGER LES INFORMATIONS",
    ["Name"] = "NOM",
    ["Surname"] = "NOM DE FAMILLE",
    ["Gender"] = "GENRE",
    ["Nationality"] = "NATIONALITÉ",
    ["Birthdate"] = "DATE DE NAISSANCE",
    ["Id"] = "ID",
    ["PhoneNumber"] = "NUMÉRO DE TÉLÉPHONE",
    ["BankAccount"] = "NUMÉRO DE COMPTE BANCAIRE",
    ["Job"] = "EMPLOI",
    ["InSearchCapture"] = "EN RECHERCHE ET CAPTURE",
    ["Dangerous"] = "DANGEREUX",
    ["Yes"] = "OUI",
    ["No"] = "NON",
    ["NewNote"] = "NOUVELLE NOTE",
    ["NoRegisteredNotes"] = "AUCUNE NOTE ENREGISTRÉE",
    ["Fine"] = "AMENDES",
    ["AddFine"] = "AJOUTER UNE AMENDE",
    ["NoRegisteredFines"] = "Aucune amende enregistrée",
    ["NoData"] = "Aucune donnée disponible",
    ["Licenses"] = "LICENCES",
    ["Weapons"] = "ARMES",
    ["Houses"] = "PROPRIÉTÉS",
    ["NoteTitle"] = "Titre de la note",
    ["TextNote"] = "Texte de la note",
    ["Save"] = "ENREGISTRER",
    ["More"] = "PLUS",
    ["SearchCriminalCode"] = "Rechercher dans le code pénal...",
    ["Article"] = "ARTICLE",
    ["Description"] = "DESCRIPTION",
    ["Amount"] = "MONTANT",
    ["Sentence"] = "PEINE",
    ["Action"] = "ACTIONS",
    ["CustomFine"] = "AMENDE PERSONNALISÉE",
    ["FineConcepts"] = "CONCEPTS D'AMENDE",
    ["Concept"] = "CONCEPT",
    ["Add"] = "AJOUTER",
    ["EnterConcept"] = "Ajouter le concept d'amende",
    ["EnterAmount"] = "Ajouter le montant",
    ["EnterSentence"] = "Ajouter la durée de la peine en mois",
    ["ProcessFine"] = "TRAITER L'AMENDE",
    ["TotalSentence"] = "PEINE TOTALE",
    ["Month"] = "MOIS",
    ["TotalAmount"] = "MONTANT TOTAL",
    ["ConfirmFine"] = "CONFIRMER L'AMENDE",
    ["FineAdded"] = "L'amende a été ajoutée avec succès",
    ["NoArticle"] = "Vous n'avez ajouté aucun article à l'amende",
    ["ReportList"] = "LISTE DES RAPPORTS",
    ["SearchReport"] = "Rechercher un rapport...",
    ["AllTags"] = "Toutes les étiquettes",
    ["NoResultFound"] = "Aucun résultat trouvé",
    ["NewReport"] = "NOUVEAU RAPPORT",
    ["SelectReport"] = "SÉLECTIONNEZ UN RAPPORT POUR CHARGER LES INFORMATIONS",
    ["Report"] = "RAPPORT",
    ["VehicleList"] = "LISTE DES VÉHICULES",
    ["TypeLicense"] = "Tapez la plaque pour rechercher...",
    ["PerformSearchVehicle"] = "Réaliser une recherche pour afficher les résultats",
    ["VehicleData"] = "DONNÉES DU VÉHICULE",
    ["NewChapter"] = "NOUVEAU CHAPITRE",
    ["NewArticle"] = "NOUVEL ARTICLE",
    ["SearchCriminalCode"] = "Rechercher dans le code pénal...",
    ["Delete"] = "SUPPRIMER",
    ["CreateNewChapter"] = "CRÉER UN NOUVEAU CHAPITRE",
    ["ChapterName"] = "Nom du chapitre",
    ["SaveChapter"] = "ENREGISTRER LE CHAPITRE",
    ["CreateNewArticle"] = "CRÉER UN NOUVEL ARTICLE",
    ["SelectChapter"] = "SÉLECTIONNEZ UN CHAPITRE",
    ["ArticleName"] = "NOM DE L'ARTICLE",
    ["EnterName"] = "Entrez le nom de l'article",
    ["DescriptionArticle"] = "DESCRIPTION DE L'ARTICLE",
    ["EnterDescription"] = "Entrez la description de l'article",
    ["SaveArticle"] = "ENREGISTRER L'ARTICLE",
    ["DeleteArticle"] = "SUPPRIMER L'ARTICLE",
    ["AreYouSureDeleteArticle"] = "Êtes-vous sûr de vouloir supprimer cet article ?",
    ["Remove"] = "SUPPRIMER",
    ["DeleteChapter"] = "SUPPRIMER LE CHAPITRE",
    ["AreYouSureDeleteArticle"] = "Êtes-vous sûr de vouloir supprimer ce chapitre ?",
    ["SubjectsInSearch"] = "PERSONNES EN RECHERCHE ET CAPTURE",
    ["NoSubjectsInSearch"] = "AUCUNE PERSONNE EN RECHERCHE ET CAPTURE",
    ["Close"] = "FERMER",
    ["DebtSubjects"] = "DÉBITEURS",
    ["FindSubject"] = "Trouver une personne...",
    ["NoDebtors"] = "AUCUN DÉBITEUR",
    ["FederalManagement"] = "GESTION FÉDÉRALE",
    ["AddConden"] = "AJOUTER UNE NOUVELLE CONDAMNATION",
    ["CitizenID"] = "ID du citoyen",
    ["DangerousOrNot"] = "DANGEREUX OU NON",
    ["NoFederals"] = "AUCUN PRISONNIER FÉDÉRAL",
    ["SecurityCameras"] = "CAMÉRAS DE SÉCURITÉ",
    ["BusinessCameras"] = "CAMÉRAS D'ENTREPRISE",
    ["VehicleCameras"] = "CAMÉRAS DE VÉHICULES",
    ["BodyCam"] = "BODYCAMS",
    ["Meters"] = "mètres",
    ["Refresh"] = "RAFRAÎCHIR",
    ["SingleCamera"] = "CAMÉRA",
    ["PoliceManagement"] = "GESTION DE LA POLICE",
    ["PoliceList"] = "LISTE DE LA POLICE",
    ["LookAgent"] = "Rechercher un agent...",
    ["GenerateBadge"] = "GÉNÉRER UN BADGE",
    ["AddPolice"] = "AJOUTER UN POLICIER",
    ["Range"] = "PLAGE",
    ["PlateAbrev"] = "NUMÉRO DE PLAQUE",
    ["Award"] = "RÉCOMPENSES",
    ["AddAward"] = "AJOUTER UNE RÉCOMPENSE",
    ["NoDecorations"] = "AUCUNE DÉCORATION",
    ["Divisions"] = "DIVISIONS",
    ["SetDivision"] = "DÉFINIR LA DIVISION",
    ["FirePolice"] = "POLICE DES INCENDIES",
    ["PoliceFile"] = "DOSSIER DE LA POLICE",
    ["SelectAnAgent"] = "Sélectionnez un agent pour voir son dossier de police",
    ["NoRegisteredReports"] = "Aucun rapport enregistré",
    ["Jurisdiction"] = "JURIDICTION",
    ["Informs"] = "INFORMATIONS",
    ["Atention"] = "ATTENTION",
    ["ThisActionCantRevert"] = "CETTE ACTION VA LICENCIER LE POLICIER. SOUHAITEZ-VOUS CONTINUER ?",
    ["DoYouWishContinue"] = "SOUHAITEZ-VOUS CONTINUER ?",
    ["Confirm"] = "CONFIRMER",
    ["AddDivision"] = "AJOUTER UNE DIVISION",
    ["AddCondecoration"] = "AJOUTER UNE DÉCORATION",
    ["DoWantGenPlate"] = "VOULEZ-VOUS GÉNÉRER CET INSIGNE ?",
    ["YouMustOpenProfile"] = "VOUS DEVEZ AVOIR LE PROFIL D'UN AGENT OUVERT POUR GÉNÉRER L'INSIGNE",
    ["PoliceBadgeGenerated"] = "INSIGNE DE POLICE GÉNÉRÉ",
    ["CheckInventory"] = "VÉRIFIER VOTRE INVENTAIRE",
    ["NoPeopleNear"] = "AUCUNE PERSONNE À PROXIMITÉ.",
    ["ConnectedTo"] = "CONNECTÉ À",
    ["Disconnect"] = "DÉCONNECTER",
    ["ShortCuts"] = "RACCOURCIS",
    ["AlternateMute"] = "MUTATION ALTERNATIVE",
    ["TalkToCentral"] = "PARLER AU CENTRAL",
    ["TalkToWaiting"] = "PARLER EN ATTENDANT UNE MISSION",
    ["TalkToPoliceSta"] = "PARLER À LA STATION DE POLICE",
    ["TalkToTacs"] = "PARLER À TACS",
    ["TalkSafd"] = "PARLER À SAFD",
    ["BroadcastSAPD"] = "DIFFUSER DANS LE SAPD",
    ["HowUploadImage"] = "COMMENT SOUHAITEZ-VOUS TÉLÉCHARGER L'IMAGE ?",
    ["Photo"] = "PHOTO",
    ["AddURL"] = "AJOUTER UNE URL",
    ["Default0Months"] = "0 Mois",
    ["ChangePlateNumber"] = "CHANGER LE NUMÉRO DE PLAQUE",
    ["PlateNumberAbrev"] = "NUMÉRO DE PLAQUE",
    ["PlateMin3"] = "La plaque doit avoir un minimum de 4 caractères",
    ["ReportName"] = "NOM DU RAPPORT",
    ["ReportID"] = "ID DU RAPPORT",
    ["DateAndHour"] = "DATE ET HEURE",
    ["AgentInCharge"] = "AGENT RESPONSABLE",
    ["ReportDescription"] = "DESCRIPTION DU RAPPORT",
    ["EnterReportDesc"] = "Entrez la description du rapport",
    ["Evidences"] = "PREUVES",
    ["WithoutUbication"] = "Sans emplacement attribué",
    ["AddEvidence"] = "AJOUTER UNE PREUVE",
    ["PeopleInvolved"] = "PERSONNES IMPLIQUÉES",
    ["NoPeopleInvolved"] = "Aucune personne impliquée",
    ["AddPeople"] = "AJOUTER UNE PERSONNE",
    ["AgentsInvolved"] = "AGENTS IMPLIQUÉS",
    ["AddAgent"] = "AJOUTER UN AGENT",
    ["Tags"] = "ÉTIQUETTES",
    ["SelectLabel"] = "Sélectionner une étiquette",
    ["Victims"] = "VICTIMES",
    ["AddVictim"] = "AJOUTER UNE VICTIME",
    ["AddVehicle"] = "AJOUTER UN VÉHICULE",
    ["DestroyReport"] = "DÉTRUIRE LE RAPPORT",
    ["NoPermission"] = "Vous n'avez pas la permission d'accéder à cette page",
    ["seconds"] = "secondes",
    ["minutes"] = "minutes",
    ["NoResult"] = "Aucun résultat trouvé",
    ["RemainMonth"] = "Mois restants",
    ["ServingSentance"] = "Purger sa peine",
    ["Release"] = "Libérer",
    ["Sleeping"] = "Dormir",
    ["IntroduceName"] = "Saisir un nom",
    ["AlertAsigned"] = "L'alerte #%s a été attribuée à %s",

    ["NoPermission"] = "Vous n'avez pas l'autorisation d'accéder !",
    ["NoPermissionPage"] = "Vous n'avez pas la permission d'accéder à cette page",
    ["MinimumCharacters"] = "Vous devez entrer au moins 3 caractères",
    ["FindACitizen"] = "Trouver un citoyen...",
    ["LookForAgent"] = "Rechercher un agent...",
    ["EnterNameToSearch"] = "Saisissez un nom dans la barre de recherche pour afficher les résultats",
    ["UnknownKey"] = "CLÉ NON RECONNUE",

    ["RadarOf"] = "Radar de",
    ["Velocity"] = "Vitesse",
    ["LicensePlate"] = "Plaque d'immatriculation",
    ["TrafficStop"] = "Arrêt de la circulation",
    ["SpeedReduction"] = "Réduction de vitesse",
    ["Color"] = "Couleur",
    ["NoRadio"] = "Vous n'avez pas de radio !",
    ["NoUsers"] = "Il n'y a aucun utilisateur sur ce canal",
    ["NoPermissionMoveUsers"] = "VOUS N'AVEZ PAS L'AUTORISATION DE DÉPLACER DES UTILISATEURS DANS LA RADIO",
    ["ChangeRange"] = "Changer la portée",
    ["Phone"] = "Téléphone",
    ["Model"] = "Modèle",
    ["Owner"] = "Propriétaire",
    ["SearchAndCapture"] = "Recherche et capture",
    ["VehicleAnnotations"] = "Annotations du véhicule",
    ["EnterAnnotation"] = "Saisissez une annotation...",
    ["VehicleNotFound"] = "Véhicule non trouvé",
    ["VehicleSearchUpdated"] = "La recherche de véhicules a été mise à jour",
    ["VehicleDescriptionUpdated"] = "La description du véhicule a été mise à jour",
    ["NoPermissionsConfigured"] = "n'a pas de permissions configurées",
    
    ["Operations"] = "Opérations",
    ["ShapesCreated"] = "Formes créées",
    ["NoShapes"] = "Aucune forme créée",
    ["TitleTooShort"] = "Le nom doit comporter plus de 5 caractères.",
    ["DeleteShape"] = "Supprimer la forme",
    ["ConfirmDeleteShape"] = "Êtes-vous sûr de vouloir supprimer cette forme?",
    ["CreateNewShape"] = "CRÉER UNE NOUVELLE FORME",

    ['SelectReport'] = "Sélectionnez un rapport pour charger vos informations",
    ['NoEvidences'] = "Il n'y a pas de preuves dans votre inventaire",
    ['NoLocation'] = "Aucun emplacement attribué",
    ['NoVehicleInvolved'] = "Aucun véhicule impliqué",
    ['NoVictimsInvolved'] = "Il n'y a pas de victimes impliquées",
    ['NoAgentsInvolved'] = "Il n'y a pas d'agents impliqués",
    ['OpenCase'] = 'Ouvrir le dossier',
    ['CaseClosed'] = 'Affaire close',
    ['NullCase'] = 'Dossier nul',
    ["ThisActionRemoveEvidence"] = "Cette action supprimera définitivement les preuves.",
    ["DoYouWantContinue"] = "Voulez-vous continuer ?",
    ['ThisActionEliminateReport'] = 'Cette action supprimera le rapport définitivement, y compris les preuves qui y sont attachées.',
    ['ThisWillAffectFines'] = "Cela n'affectera pas les amendes, qui resteront dans le système.?",
    ['TotalPenalty'] = 'Amende totale',
    ['TotalAmount'] = 'Montant total',
    ['SendFine'] = 'AMENDE',
    ['EnterPlateEngine'] = 'Saisissez une plaque dans le moteur de recherche pour afficher les résultats',
    
    ['SelectCitizen'] = 'Sélectionnez un citoyen',
    ['EnterURLImage'] = "Entrez l'URL de l'image",
    ['SaveImage'] = "Enregistrer l'image",
    ['SelectAnAgent2'] = 'Sélectionnez un agent',
    ['SelectVehicle'] = 'Sélectionnez un véhicule',
    ['EnterNameEngine'] = 'Entrez un nom pour le moteur de recherche pour afficher les résultats',
    ['FindVehicles'] = 'Rechercher des véhicules...',
    ['SelectEvidence'] = 'Sélectionnez des preuves',
    ['NoEvidenceInv'] = "Il n'y a pas de preuves dans votre inventaire",
    
    ["AddLicense"] = "AJOUTER UNE LICENCE",
    ["AddNewLicense"] = "AJOUTER UNE NOUVELLE LICENCE",
    ['Expiration'] = 'Expiration',
    ['AddedLicense'] = 'Vous avez ajouté une licence',

    ['ReferencesLocation'] = 'Emplacement des références',
    ['BodyCamera'] = 'Caméra corporelle',
    ['Animation'] = 'Animation',
    ['NoSignal'] = 'SANS SIGNAL',

    ['LowCaliber'] = 'Basse calibre',
    ['ShotgunCaliber'] = 'Cartouches de fusil de chasse',
    ['MediumCaliber'] = 'Moyenne calibre',
    ['HighCaliber'] = 'Haut calibre',

    ['LicensesList'] = {
        ['Driver'] = 'Chauffeur',
        ['Weapon'] = 'Arme',
        ['Business'] = 'Entreprise',
    },

    -- Condecorations
    ["Condecorations"] = {
        Valor = {
            id = 'Valor',
            name = 'Médaille de la Valeur',
            description =
                "La médaille de la valeur est la plus haute application de la loi accordée aux officiers et est décernée pour des actes individuels d'une bravoure extraordinaire ou d'un héroïsme accomplis dans l'accomplissement du devoir avec un risque personnel extrêmement élevé et potentiellement mortel.",
            url = 'vV0Wm9A.png'
        },
        Preservacion = {
            id = 'Preservacion',
            name = 'Médaille de préservation de la vie',
            description =
                "La médaille de préservation de la vie peut être décernée à un officier qui s'est distingué en utilisant des tactiques exceptionnelles et en faisant preuve d'un bon jugement, au-delà des exigences normales du devoir, pour préserver la vie d'un autre lors d'une rencontre volatile ou dangereuse tout en protégeant la sécurité du public et de ses officiers.",
            url = '4Zmnp8u.png'
        },
        Policia = {
            id = 'Policia',
            name = 'Médaille de la Police',
            description =
                "La médaille de la police est une récompense pour la bravoure, généralement décernée aux officiers pour des actes individuels d'héroïsme dans l'accomplissement du devoir, bien que pas au-delà de l'appel du devoir, comme requis pour la médaille de la valeur.",
            url = 'BwPTQWC.png'
        },
        Estrella = {
            id = 'Estrella',
            name = 'Étoile de la Police',
            description =
                "L'étoile de la police est une récompense pour la bravoure, généralement décernée aux officiers pour avoir fait preuve d'un jugement exceptionnel et/ou utilisé des tactiques professionnelles pour désactiver des situations dangereuses et stressantes.",
            url = 'U4vBD1Z.png'
        },
        Salvavidas = {
            id = 'Lifejacket',
            name = 'Médaille de sauvetage de la police',
            description =
                "La médaille de sauvetage de la police est une récompense pour la bravoure, généralement décernée aux officiers pour prendre des mesures pour secourir ou tenter de secourir un partenaire officier ou toute personne en danger imminent.",
            url = 'TuL7fDQ.png'
        },
        Distinguido = {
            id = 'Distinguido',
            name = 'Médaille de service distinguée de la police',
            description =
                "La médaille de service distinguée de la police est le service le plus élevé pour le département et peut être décernée aux employés pour distinguer un service exceptionnel dans un devoir de grande responsabilité ou d'importance cruciale pour l'application de la loi.",
            url = 'rojxaCL.png'
        },
        Meritorio = {
            id = 'Meritorio',
            name = 'Médaille du service méritoire de la police',
            description =
                "La médaille du service de la police méritoire est décernée aux employés pour distinguer un service exceptionnel dans un devoir de grande responsabilité ou d'importance cruciale pour le respect de la loi, mais dans une moindre mesure que celle requise pour la médaille du service de la police distinguée.",
            url = 'cHAlfOj.png'
        },
        LogroMeritorio = {
            id = 'LogroMeritorio',
            name = 'RÉALISATION MÉRITOIRE DE LA POLICE',
            description =
                "La médaille d'accomplissement de la police est principalement conçue pour reconnaître les employés civils. La médaille est décernée pour des réalisations soutenues, à long terme et notables ou pour une réalisation significative et notable dans l'exécution de tâches administratives, de bureau ou artisanales.",
            url = 'laujeQV.png'
        },
        DistinguidoComision = {
            id = 'DistinguidoComision',
            name = 'Médaille de service distinguée de la Commission de police',
            description =
                "La Médaille de service distinguée de la Commission de police est décernée aux officiers qui se distinguent par un service exceptionnel pour le SAPD ou qui se comportent dans une situation d'urgence stressante avec bon jugement et courage.",
            url = 'YCOtC5l.png'
        },
        IntegridadComision = {
            id = 'IntegridadComision',
            name = "Médaille d'intégrité de la Commission de police",
            description =
                "La Médaille d'intégrité de la Commission de police est décernée aux employés qui font preuve d'un acte d'intégrité exemplaire, surtout lorsque cet acte nécessite un caractère exceptionnel, une force et un courage moral face à des obstacles substantiels.",
            url = 'Ia6hPav.png'
        },
        Comunitaria = {
            id = 'Comunitaria',
            name = 'Médaille de la Police communautaire',
            description =
                "La Médaille de la Police communautaire est décernée au personnel qui a résolu un problème communautaire important, en incluant la communauté dans le processus de résolution des problèmes et/ou en montrant un engagement envers la philosophie de la police communautaire de SAPD.",
            url = 'bDkoKfS.png'
        },
        RelacionesHumanas = {
            id = 'RelacionesHumanas',
            name = 'Médaille des Relations humaines',
            description =
                "La Médaille des Relations humaines est décernée aux officiers qui ont montré une grande compassion dans leurs activités quotidiennes et sont allés au-delà de l'appel du devoir dans leur réponse envers les autres êtres humains.",
            url = 'IMlJLE4.png'
        },
        Service2 = {
            id = 'Service2',
            name = 'Service pendant 2 mois',
            description =
                "Cette récompense est décernée aux membres qui ont effectué un service à San Andreas dépassant 2 mois dans la police de San Andreas.",
            url = '22OMcKF.png'
        },
        Service4 = {
            id = 'Service4',
            name = 'Service pendant 4 mois',
            description =
                "Cette récompense est décernée aux membres qui ont effectué un service à San Andreas dépassant 4 mois dans la police de San Andreas.",
            url = 'waOO0p1.png'
        },
        Service6 = {
            id = 'Service6',
            name = 'Service pendant 6 mois',
            description =
                "Cette récompense est décernée aux membres qui ont effectué un service à San Andreas dépassant 6 mois.",
            url = 'zw1TPMg.png'
        },
        Service8 = {
            id = 'Service8',
            name = 'Service pendant 8 mois',
            description =
                "Cette récompense est décernée aux membres qui ont effectué un service à San Andreas dépassant 8 mois.",
            url = 'oVvluyF.png'
        },
        Service10 = {
            id = 'Service10',
            name = 'Service pendant 10 mois',
            description =
                "Cette récompense est décernée aux membres qui ont effectué un service à San Andreas dépassant 10 mois.",
            url = '9E01TG1.png'
        },
        Service12 = {
            id = 'Service12',
            name = 'Service pendant 12 mois',
            description =
                "Cette décoration est remise aux membres qui ont effectué un service à San Andreas dépassant un an.",
            url = 'FTz1dTx.png'
        },
        Lifejacket = {
            id = 'Lifejacket',
            name = 'Médaille de sauvetage de la police',
            description =
                "La médaille de sauvetage de la police est une récompense pour la bravoure, généralement décernée aux officiers pour prendre des mesures de sauvetage ou tenter de sauver un partenaire officier ou toute personne en danger imminent.",
            url = 'TuL7fDQ.png'
        },
    },
    -- Divisions
    ["DivisionsData"] = {
        IAA = {
            id = 'IAA',
            name = 'Affaires internes',
            url = 't764YV8.png'
        },
        FIB = {
            id = 'FIB',
            name = "Bureau fédéral d'enquête (FIB)",
            url = 'BtEEw1S.png'
        },
        SWAT = {
            id = 'SWAT',
            name = 'Unité des armes et tactiques spéciales (SWAT)',
            url = 'v4dW751.png'
        },
        HPD = {
            id = 'HPD',
            name = 'Division de la patrouille routière (HPD)',
            url = 'scWMKjL.png'
        },
        IRD = {
            id = 'IRD',
            name = "Département de l'instruction et du recrutement (IRD)",
            url = 'OCEBbrB.png'
        },
        UNP = {
            id = 'UNP',
            name = 'Unité de négociation de la police (PNU)',
            url = 'DlGNQiV.png'
        },
        UM = {
            id = 'UM',
            name = 'Unité maritime (MU)',
            url = 'DlGNQiV.png'
        }
    },
}

LogsTranslations = {
    ['Identifiers'] = 'IDENTIFIANTS',
    ['ID'] = 'ID',
    ['Name'] = 'Nom',

    ['Alert'] = {
        title = 'Alerte envoyée',
        message = 'Une alerte a été envoyée avec la commande `%s`.\nMessage : %s',
    },
    ['ClockOut'] = {
        title = 'Déconnexion',
        message = 'Le joueur a terminé le service.\nHeure de connexion : `%s`\nHeure de déconnexion : `%s`\nTotal : `%s` minutes',
    },
    ['CreateShape'] = {
        title = 'Forme créée',
        message = 'Le joueur a créé une forme.\nNom : `%s`',
    },
    ['DeleteShape'] = {
        title = 'Forme supprimée',
        message = 'Le joueur a supprimé une forme.\nNom : `%s`',
    },

    ['CreateNote'] = {
        title = 'Note créée',
        message = 'Le joueur a créé une note.\nTitre : `%s`\nDescription : `%s`\nAuteur : `%s`',
    },
    ['DeleteNote'] = {
        title = 'Note supprimée',
        message = 'Le joueur a supprimé une note.\nID de la note : `%s`',
    },

    ['CreateReport'] = {
        title = 'Rapport créé',
        message = 'Le joueur a créé un rapport.\nTitre : `%s`\nDescription : `%s`\nAuteur : `%s`',
    },
    ['DeleteReport'] = {
        title = 'Rapport supprimé',
        message = 'Le joueur a supprimé un rapport.\nID du rapport : `%s`',
    },
    ['SetBadge'] = {
        title = 'Attribuer un badge de police',
        message = 'Le joueur a attribué un badge de police.\nAgent : `%s`\nBadge : `%s`',
    },
    ['FirePolice'] = {
        title = 'Agent licencié',
        message = 'Le joueur a licencié un agent.\nAgent : `%s`',
    },
    ['HirePolice'] = {
        title = 'Agent embauché',
        message = 'Le joueur a embauché un joueur.\nNom : `%s`\nEmploi : `%s`\nGrade : `%s`',
    },
    ['UpdatePlayer'] = {
        title = 'Joueur mis à jour',
        message = 'Le joueur a licencié/embauché un joueur.\nIdentifiant du joueur : `%s`\nEmploi : `%s`\nGrade : `%s`',
    },
    ['NewPhoto'] = {
        title = 'Nouvelle photo',
        message = 'Le joueur a pris une nouvelle photo.',
    },
    ['EnterFederal'] = {
        title = 'Entrée en fédéral',
        message = 'L\'agent a envoyé un prisonnier au fédéral.\nTemps : `%s minutes`\nAgent : `%s`',
    },
    ['ExitFederal'] = {
        title = 'Sortie du fédéral',
        message = 'Le joueur a été libéré.',
    },
    ['AddBill'] = {
        title = 'Facture ajoutée',
        message = 'Une nouvelle facture a été créée.\n\nAgent : `%s`\nMontant : `%s`\nMois : `%s`\nConcepts : %s',
    },
    ['PayBill'] = {
        title = 'Facture payée',
        message = 'Le joueur a payé une facture.\nMontant : `%s`\nID de facture : `%s`',
    },
    ['UseBodyCam'] = {
        title = 'Caméra corporelle utilisée',
        message = 'Le joueur a utilisé une caméra.\nNom de la caméra : `%s`',
    },
    ['UseCarCam'] = {
        title = 'Caméra de véhicule utilisée',
        message = 'Le joueur a utilisé une caméra.\nPlaque d\'immatriculation de la voiture : `%s`',
    },    
}