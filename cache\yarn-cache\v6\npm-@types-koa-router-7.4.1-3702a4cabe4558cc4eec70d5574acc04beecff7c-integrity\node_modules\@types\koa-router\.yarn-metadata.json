{"manifest": {"name": "@types/koa-router", "version": "7.4.1", "description": "TypeScript definitions for koa-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/hellopao"}, {"name": "<PERSON>", "url": "https://github.com/schfkt"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romain-faust"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/falinor"}, {"name": "<PERSON>", "url": "https://github.com/yves<PERSON><PERSON>mann"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/koa-router"}, "scripts": {}, "dependencies": {"@types/koa": "*"}, "typesPublisherContentHash": "930258b5c1441b693860d0a6e749ac18d935ca0cbbabb93e2dedc05549d23b24", "typeScriptVersion": "3.0", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-koa-router-7.4.1-3702a4cabe4558cc4eec70d5574acc04beecff7c-integrity\\node_modules\\@types\\koa-router\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/koa-router`\n\n# Summary\nThis package contains type definitions for koa-router (https://github.com/alexmingoia/koa-router#readme).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/koa-router.\n\n### Additional Details\n * Last updated: Thu, 14 May 2020 17:53:38 GMT\n * Dependencies: [@types/koa](https://npmjs.com/package/@types/koa)\n * Global values: none\n\n# Credits\nThese definitions were written by [<PERSON>](https://github.com/hellopao), [<PERSON>](https://github.com/schfkt), [<PERSON><PERSON><PERSON><PERSON>](https://github.com/<PERSON><PERSON>Q<PERSON>), [<PERSON><PERSON>](https://github.com/romain-faust), [<PERSON>](https://github.com/<PERSON>), [<PERSON>](https://github.com/falinor), and [<PERSON>](https://github.com/y<PERSON><PERSON><PERSON><PERSON>).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/koa-router/-/koa-router-7.4.1.tgz#3702a4cabe4558cc4eec70d5574acc04beecff7c", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/koa-router/-/koa-router-7.4.1.tgz", "hash": "3702a4cabe4558cc4eec70d5574acc04beecff7c", "integrity": "sha512-Hg78TXz78QYfEgdq3nTeRmQFEwJKZljsXb/DhtexmyrpRDRnl59oMglh9uPj3/WgKor0woANrYTnxA8gaWGK2A==", "registry": "npm", "packageName": "@types/koa-router", "cacheIntegrity": "sha512-Hg78TXz78QYfEgdq3nTeRmQFEwJKZljsXb/DhtexmyrpRDRnl59oMglh9uPj3/WgKor0woANrYTnxA8gaWGK2A== sha1-NwKkyr5FWMxO7HDVV0rMBL7s/3w="}, "registry": "npm", "hash": "3702a4cabe4558cc4eec70d5574acc04beecff7c"}