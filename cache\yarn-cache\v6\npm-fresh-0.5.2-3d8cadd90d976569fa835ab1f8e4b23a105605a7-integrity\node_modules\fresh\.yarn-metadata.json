{"manifest": {"name": "fresh", "description": "HTTP response freshness testing", "version": "0.5.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["fresh", "http", "conditional", "cache"], "repository": {"type": "git", "url": "https://github.com/jshttp/fresh.git"}, "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "5.1.1", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-fresh-0.5.2-3d8cadd90d976569fa835ab1f8e4b23a105605a7-integrity\\node_modules\\fresh\\package.json", "readmeFilename": "README.md", "readme": "# fresh\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nHTTP response freshness testing\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```\n$ npm install fresh\n```\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar fresh = require('fresh')\n```\n\n### fresh(reqHeaders, resHeaders)\n\nCheck freshness of the response using request and response headers.\n\nWhen the response is still \"fresh\" in the client's cache `true` is\nreturned, otherwise `false` is returned to indicate that the client\ncache is now stale and the full response should be sent.\n\nWhen a client sends the `Cache-Control: no-cache` request header to\nindicate an end-to-end reload request, this module will return `false`\nto make handling these requests transparent.\n\n## Known Issues\n\nThis module is designed to only follow the HTTP specifications, not\nto work-around all kinda of client bugs (especially since this module\ntypically does not recieve enough information to understand what the\nclient actually is).\n\nThere is a known issue that in certain versions of Safari, Safari\nwill incorrectly make a request that allows this module to validate\nfreshness of the resource even when Safari does not have a\nrepresentation of the resource in the cache. The module\n[jumanji](https://www.npmjs.com/package/jumanji) can be used in\nan Express application to work-around this issue and also provides\nlinks to further reading on this Safari bug.\n\n## Example\n\n### API usage\n\n<!-- eslint-disable no-redeclare, no-undef -->\n\n```js\nvar reqHeaders = { 'if-none-match': '\"foo\"' }\nvar resHeaders = { 'etag': '\"bar\"' }\nfresh(reqHeaders, resHeaders)\n// => false\n\nvar reqHeaders = { 'if-none-match': '\"foo\"' }\nvar resHeaders = { 'etag': '\"foo\"' }\nfresh(reqHeaders, resHeaders)\n// => true\n```\n\n### Using with Node.js http server\n\n```js\nvar fresh = require('fresh')\nvar http = require('http')\n\nvar server = http.createServer(function (req, res) {\n  // perform server logic\n  // ... including adding ETag / Last-Modified response headers\n\n  if (isFresh(req, res)) {\n    // client has a fresh copy of resource\n    res.statusCode = 304\n    res.end()\n    return\n  }\n\n  // send the resource\n  res.statusCode = 200\n  res.end('hello, world!')\n})\n\nfunction isFresh (req, res) {\n  return fresh(req.headers, {\n    'etag': res.getHeader('ETag'),\n    'last-modified': res.getHeader('Last-Modified')\n  })\n}\n\nserver.listen(3000)\n```\n\n## License\n\n[MIT](LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/fresh.svg\n[npm-url]: https://npmjs.org/package/fresh\n[node-version-image]: https://img.shields.io/node/v/fresh.svg\n[node-version-url]: https://nodejs.org/en/\n[travis-image]: https://img.shields.io/travis/jshttp/fresh/master.svg\n[travis-url]: https://travis-ci.org/jshttp/fresh\n[coveralls-image]: https://img.shields.io/coveralls/jshttp/fresh/master.svg\n[coveralls-url]: https://coveralls.io/r/jshttp/fresh?branch=master\n[downloads-image]: https://img.shields.io/npm/dm/fresh.svg\n[downloads-url]: https://npmjs.org/package/fresh\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2012 <PERSON><PERSON> <<EMAIL>>\nCopyright (c) 2016-2017 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/fresh/-/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7", "type": "tarball", "reference": "https://registry.yarnpkg.com/fresh/-/fresh-0.5.2.tgz", "hash": "3d8cadd90d976569fa835ab1f8e4b23a105605a7", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=", "registry": "npm", "packageName": "fresh", "cacheIntegrity": "sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q== sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="}, "registry": "npm", "hash": "3d8cadd90d976569fa835ab1f8e4b23a105605a7"}