{"manifest": {"name": "readable-stream", "version": "3.6.0", "description": "Streams3, a user-land copy of the stream library from Node.js", "main": "readable.js", "engines": {"node": ">= 6"}, "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "devDependencies": {"@babel/cli": "^7.2.0", "@babel/core": "^7.2.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.2.0", "airtap": "0.0.9", "assert": "^1.4.0", "bl": "^2.0.0", "deep-strict-equal": "^0.2.0", "events.once": "^2.0.2", "glob": "^7.1.2", "gunzip-maybe": "^1.4.1", "hyperquest": "^2.1.3", "lolex": "^2.6.0", "nyc": "^11.0.0", "pump": "^3.0.0", "rimraf": "^2.6.2", "tap": "^12.0.0", "tape": "^4.9.0", "tar-fs": "^1.16.2", "util-promisify": "^2.1.0"}, "scripts": {"test": "tap -J --no-esm test/parallel/*.js test/ours/*.js", "ci": "TAP=1 tap --no-esm test/parallel/*.js test/ours/*.js | tee test.tap", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --open --local -- test/browser.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "update-browser-errors": "babel -o errors-browser.js errors.js"}, "repository": {"type": "git", "url": "git://github.com/nodejs/readable-stream"}, "keywords": ["readable", "stream", "pipe"], "browser": {"util": false, "worker_threads": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "./lib/internal/streams/from.js": "./lib/internal/streams/from-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "nyc": {"include": ["lib/**.js"]}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-readable-stream-3.6.0-337bbda3adc0706bd3e024426a286d4b4b2c9198-integrity\\node_modules\\readable-stream\\package.json", "readmeFilename": "README.md", "readme": "# readable-stream\n\n***Node.js core streams for userland*** [![Build Status](https://travis-ci.com/nodejs/readable-stream.svg?branch=master)](https://travis-ci.com/nodejs/readable-stream)\n\n\n[![NPM](https://nodei.co/npm/readable-stream.png?downloads=true&downloadRank=true)](https://nodei.co/npm/readable-stream/)\n[![NPM](https://nodei.co/npm-dl/readable-stream.png?&months=6&height=3)](https://nodei.co/npm/readable-stream/)\n\n\n[![Sauce Test Status](https://saucelabs.com/browser-matrix/readabe-stream.svg)](https://saucelabs.com/u/readabe-stream)\n\n```bash\nnpm install --save readable-stream\n```\n\nThis package is a mirror of the streams implementations in Node.js.\n\nFull documentation may be found on the [Node.js website](https://nodejs.org/dist/v10.19.0/docs/api/stream.html).\n\nIf you want to guarantee a stable streams base, regardless of what version of\nNode you, or the users of your libraries are using, use **readable-stream** *only* and avoid the *\"stream\"* module in Node-core, for background see [this blogpost](http://r.va.gg/2014/06/why-i-dont-use-nodes-core-stream-module.html).\n\nAs of version 2.0.0 **readable-stream** uses semantic versioning.\n\n## Version 3.x.x\n\nv3.x.x of `readable-stream` is a cut from Node 10. This version supports Node 6, 8, and 10, as well as evergreen browsers, IE 11 and latest Safari. The breaking changes introduced by v3 are composed by the combined breaking changes in [Node v9](https://nodejs.org/en/blog/release/v9.0.0/) and [Node v10](https://nodejs.org/en/blog/release/v10.0.0/), as follows:\n\n1. Error codes: https://github.com/nodejs/node/pull/13310,\n   https://github.com/nodejs/node/pull/13291,\n   https://github.com/nodejs/node/pull/16589,\n   https://github.com/nodejs/node/pull/15042,\n   https://github.com/nodejs/node/pull/15665,\n   https://github.com/nodejs/readable-stream/pull/344\n2. 'readable' have precedence over flowing\n   https://github.com/nodejs/node/pull/18994\n3. make virtual methods errors consistent\n   https://github.com/nodejs/node/pull/18813\n4. updated streams error handling\n   https://github.com/nodejs/node/pull/18438\n5. writable.end should return this.\n   https://github.com/nodejs/node/pull/18780\n6. readable continues to read when push('')\n   https://github.com/nodejs/node/pull/18211\n7. add custom inspect to BufferList\n   https://github.com/nodejs/node/pull/17907\n8. always defer 'readable' with nextTick\n   https://github.com/nodejs/node/pull/17979\n\n## Version 2.x.x\nv2.x.x of `readable-stream` is a cut of the stream module from Node 8 (there have been no semver-major changes from Node 4 to 8). This version supports all Node.js versions from 0.8, as well as evergreen browsers and IE 10 & 11.\n\n### Big Thanks\n\nCross-browser Testing Platform and Open Source <3 Provided by [Sauce Labs][sauce]\n\n# Usage\n\nYou can swap your `require('stream')` with `require('readable-stream')`\nwithout any changes, if you are just using one of the main classes and\nfunctions.\n\n```js\nconst {\n  Readable,\n  Writable,\n  Transform,\n  Duplex,\n  pipeline,\n  finished\n} = require('readable-stream')\n````\n\nNote that `require('stream')` will return `Stream`, while\n`require('readable-stream')` will return `Readable`. We discourage using\nwhatever is exported directly, but rather use one of the properties as\nshown in the example above.\n\n# Streams Working Group\n\n`readable-stream` is maintained by the Streams Working Group, which\noversees the development and maintenance of the Streams API within\nNode.js. The responsibilities of the Streams Working Group include:\n\n* Addressing stream issues on the Node.js issue tracker.\n* Authoring and editing stream documentation within the Node.js project.\n* Reviewing changes to stream subclasses within the Node.js project.\n* Redirecting changes to streams from the Node.js project to this\n  project.\n* Assisting in the implementation of stream providers within Node.js.\n* Recommending versions of `readable-stream` to be included in Node.js.\n* Messaging about the future of streams to give the community advance\n  notice of changes.\n\n<a name=\"members\"></a>\n## Team Members\n\n* **Calvin Metcalf** ([@calvinmetcalf](https://github.com/calvinmetcalf)) &lt;<EMAIL>&gt;\n  - Release GPG key: F3EF5F62A87FC27A22E643F714CE4FF5015AA242\n* **Mathias Buus** ([@mafintosh](https://github.com/mafintosh)) &lt;<EMAIL>&gt;\n* **Matteo Collina** ([@mcollina](https://github.com/mcollina)) &lt;<EMAIL>&gt;\n  - Release GPG key: 3ABC01543F22DD2239285CDD818674489FBC127E\n* **Irina Shestak** ([@lrlna](https://github.com/lrlna)) &lt;<EMAIL>&gt;\n* **Yoshua Wyuts** ([@yoshuawuyts](https://github.com/yoshuawuyts)) &lt;<EMAIL>&gt;\n\n[sauce]: https://saucelabs.com\n", "licenseText": "Node.js is licensed for use as follows:\n\n\"\"\"\nCopyright Node.js contributors. All rights reserved.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n\"\"\"\n\nThis license applies to parts of Node.js originating from the\nhttps://github.com/joyent/node repository:\n\n\"\"\"\nCopyright Joyent, Inc. and other Node contributors. All rights reserved.\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n\"\"\"\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/readable-stream/-/readable-stream-3.6.0.tgz#337bbda3adc0706bd3e024426a286d4b4b2c9198", "type": "tarball", "reference": "https://registry.yarnpkg.com/readable-stream/-/readable-stream-3.6.0.tgz", "hash": "337bbda3adc0706bd3e024426a286d4b4b2c9198", "integrity": "sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA==", "registry": "npm", "packageName": "readable-stream", "cacheIntegrity": "sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA== sha1-M3u9o63AcGvT4CRCaihtS0sskZg="}, "registry": "npm", "hash": "337bbda3adc0706bd3e024426a286d4b4b2c9198"}