{"name": "assign-symbols", "description": "Assign the enumerable es6 Symbol properties from an object (or objects) to the first object passed on the arguments. Can be used as a supplement to other extend, assign or merge methods as a polyfill for the Symbols part of the es6 Object.assign method.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/assign-symbols", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/assign-symbols", "bugs": {"url": "https://github.com/jonschlinkert/assign-symbols/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "^3.0.0"}, "keywords": ["assign", "symbols"], "verb": {"related": {"list": ["assign-deep", "mixin-deep", "merge-deep", "extend-shallow", "clone-deep"]}}}