{"manifest": {"name": "@types/node", "version": "14.14.20", "description": "TypeScript definitions for Node.js", "license": "MIT", "contributors": [{"name": "Microsoft TypeScript", "url": "https://github.com/Microsoft"}, {"name": "DefinitelyTyped", "url": "https://github.com/DefinitelyTyped"}, {"name": "<PERSON>", "url": "https://github.com/jkomyno"}, {"name": "<PERSON>.", "url": "https://github.com/a-tarasyuk"}, {"name": "Alvis <PERSON>", "url": "https://github.com/alvis"}, {"name": "<PERSON>", "url": "https://github.com/r3nya"}, {"name": "<PERSON>", "url": "https://github.com/btoueg"}, {"name": "<PERSON>", "url": "https://github.com/bruno<PERSON>ufler"}, {"name": "Chigozirim C.", "url": "https://github.com/smac89"}, {"name": "<PERSON>", "url": "https://github.com/touffy"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/DeividasBakanas"}, {"name": "<PERSON>", "url": "https://github.com/eyqs"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Flarna"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>-<PERSON>-CK"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/KSXGitHub"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/hoo29"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kjin"}, {"name": "<PERSON>", "url": "https://github.com/ajafff"}, {"name": "Lishude", "url": "https://github.com/islishude"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mwiktorczyk"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/mohsen1"}, {"name": "<PERSON>", "url": "https://github.com/n-e"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/galkin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/parambirs"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick"}, {"name": "<PERSON>", "url": "https://github.com/ThomasdenH"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/WilcoBakker"}, {"name": "wwwy3y3", "url": "https://github.com/wwwy3y3"}, {"name": "<PERSON>", "url": "https://github.com/samuela"}, {"name": "<PERSON>", "url": "https://github.com/kuehlein"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/j-oliveras"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/bhongy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/chyzwar"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/trivikr"}, {"name": "<PERSON>", "url": "https://github.com/nguymin4"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/yoursunny"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/qwelias"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON>-<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/victorperin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZYSzys"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=3.4": {"*": ["ts3.4/*"]}, "<=3.6": {"*": ["ts3.6/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "3ec100758f9a98f40ade23fd5781e233afe6427322127911c2b9a87c70fe396b", "typeScriptVersion": "3.3", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-node-14.14.20-f7974863edd21d1f8a494a73e8e2b3658615c340-integrity\\node_modules\\@types\\node\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/node`\n\n# Summary\nThis package contains type definitions for Node.js (http://nodejs.org/).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node.\n\n### Additional Details\n * Last updated: Mon, 04 Jan 2021 20:50:23 GMT\n * Dependencies: none\n * Global values: `<PERSON><PERSON><PERSON>`, `__dirname`, `__filename`, `clearImmediate`, `clearInterval`, `clearTimeout`, `console`, `exports`, `global`, `module`, `process`, `queueMicrotask`, `require`, `setImmediate`, `setInterval`, `setTimeout`\n\n# Credits\nThese definitions were written by [Microsoft TypeScript](https://github.com/Microsoft), [DefinitelyTyped](https://github.com/DefinitelyTyped), [<PERSON>](https://github.com/jkomyno), [<PERSON>](https://github.com/a-ta<PERSON><PERSON><PERSON>), [<PERSON><PERSON>](https://github.com/alvis), [<PERSON>](https://github.com/r3nya), [<PERSON>](https://github.com/btoueg), [<PERSON>heufler](https://github.com/brunoscheufler), [Chigozirim C.](https://github.com/smac89), [David Junger](https://github.com/touffy), [Deividas Bakanas](https://github.com/DeividasBakanas), [Eugene Y. Q. Shen](https://github.com/eyqs), [Flarna](https://github.com/Flarna), [Hannes Magnusson](https://github.com/Hannes-Magnusson-CK), [Hoàng Văn Khải](https://github.com/KSXGitHub), [Huw](https://github.com/hoo29), [Kelvin Jin](https://github.com/kjin), [Klaus Meinhardt](https://github.com/ajafff), [Lishude](https://github.com/islishude), [Mariusz Wiktorczyk](https://github.com/mwiktorczyk), [Mohsen Azimi](https://github.com/mohsen1), [Nicolas Even](https://github.com/n-e), [Nikita Galkin](https://github.com/galkin), [Parambir Singh](https://github.com/parambirs), [Sebastian Silbermann](https://github.com/eps1lon), [Simon Schick](https://github.com/SimonSchick), [Thomas den Hollander](https://github.com/ThomasdenH), [Wilco Bakker](https://github.com/WilcoBakker), [wwwy3y3](https://github.com/wwwy3y3), [Samuel Ainsworth](https://github.com/samuela), [Kyle Uehlein](https://github.com/kuehlein), [Jordi Oliveras Rovira](https://github.com/j-oliveras), [Thanik Bhongbhibhat](https://github.com/bhongy), [Marcin Kopacz](https://github.com/chyzwar), [Trivikram Kamat](https://github.com/trivikr), [Minh Son Nguyen](https://github.com/nguymin4), [Junxiao Shi](https://github.com/yoursunny), [Ilia Baryshnikov](https://github.com/qwelias), [ExE Boss](https://github.com/ExE-Boss), [Surasak Chaisurin](https://github.com/Ryan-Willpower), [Piotr Błażejewicz](https://github.com/peterblazejewicz), [Anna Henningsen](https://github.com/addaleax), [Jason Kwok](https://github.com/JasonHK), [Victor Perin](https://github.com/victorperin), and [Yongsheng Zhang](https://github.com/ZYSzys).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/node/-/node-14.14.20.tgz#f7974863edd21d1f8a494a73e8e2b3658615c340", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/node/-/node-14.14.20.tgz", "hash": "f7974863edd21d1f8a494a73e8e2b3658615c340", "integrity": "sha512-Y93R97Ouif9JEOWPIUyU+eyIdyRqQR0I8Ez1dzku4hDx34NWh4HbtIc3WNzwB1Y9ULvNGeu5B8h8bVL5cAk4/A==", "registry": "npm", "packageName": "@types/node", "cacheIntegrity": "sha512-Y93R97Ouif9JEOWPIUyU+eyIdyRqQR0I8Ez1dzku4hDx34NWh4HbtIc3WNzwB1Y9ULvNGeu5B8h8bVL5cAk4/A== sha1-95dIY+3SHR+KSUpz6OKzZYYVw0A="}, "registry": "npm", "hash": "f7974863edd21d1f8a494a73e8e2b3658615c340"}