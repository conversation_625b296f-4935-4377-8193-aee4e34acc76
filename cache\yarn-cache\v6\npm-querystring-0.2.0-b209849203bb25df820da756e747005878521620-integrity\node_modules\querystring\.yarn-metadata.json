{"manifest": {"name": "querystring", "id": "querystring", "version": "0.2.0", "description": "Node's querystring module for all engines.", "keywords": ["commonjs", "query", "querystring"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/Gozala/querystring.git", "web": "https://github.com/Gozala/querystring"}, "bugs": {"url": "http://github.com/Gozala/querystring/issues/"}, "devDependencies": {"test": "~0.x.0", "phantomify": "~0.x.0", "retape": "~0.x.0", "tape": "~0.1.5"}, "engines": {"node": ">=0.4.x"}, "scripts": {"test": "npm run test-node && npm run test-browser && npm run test-tap", "test-browser": "node ./node_modules/phantomify/bin/cmd.js ./test/common-index.js", "test-node": "node ./test/common-index.js", "test-tap": "node ./test/tap-index.js"}, "testling": {"files": "test/tap-index.js", "browsers": {"iexplore": [9, 10], "chrome": [16, 20, 25, "canary"], "firefox": [10, 15, 16, 17, 18, "nightly"], "safari": [5, 6], "opera": [12]}}, "licenses": [{"type": "MIT", "url": "https://github.com/Gozala/enchain/License.md"}], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-querystring-0.2.0-b209849203bb25df820da756e747005878521620-integrity\\node_modules\\querystring\\package.json", "readmeFilename": "Readme.md", "readme": "# querystring\n\n[![Build Status](https://secure.travis-ci.org/Gozala/querystring.png)](http://travis-ci.org/Gozala/querystring)\n\n\n[![Browser support](http://ci.testling.com/Gozala/querystring.png)](http://ci.testling.com/Gozala/querystring)\n\n\n\nNode's querystring module for all engines.\n\n## Install ##\n\n    npm install querystring\n\n", "license": "MIT", "licenseText": "\nCopyright 2012 <PERSON><PERSON><PERSON>. All rights reserved.\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/querystring/-/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620", "type": "tarball", "reference": "https://registry.yarnpkg.com/querystring/-/querystring-0.2.0.tgz", "hash": "b209849203bb25df820da756e747005878521620", "integrity": "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=", "registry": "npm", "packageName": "querystring", "cacheIntegrity": "sha512-X/xY82scca2tau62i9mDyU9K+I+djTMUsvwf7xnUX5GLvVzgJybOJf4Y6o9Zx3oJK/LSXg5tTZBjwzqVPaPO2g== sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA="}, "registry": "npm", "hash": "b209849203bb25df820da756e747005878521620"}