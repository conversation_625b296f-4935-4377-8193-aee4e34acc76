# ❄️ WINTER CLOTHING TEST - Weather Health System

## 🧥 Nové zimn<PERSON> o<PERSON> pro NPC

### ✅ Co bylo <PERSON>:

#### **Chlad<PERSON><PERSON> (5-15°C):**
- **Hoodie** (torso 24/25) - teplo 7
- **Jacket** (torso 31/33) - teplo 9  
- **Sweater** (torso 42/44) - teplo 8
- **Light jacket** (torso 61/63) - teplo 6
- **Windbreaker** (torso 84/86) - teplo 8

#### **Mrazi<PERSON><PERSON> po<PERSON> (SNOW/XMAS/BLIZZARD):**
- **Heavy winter coat** (torso 91/93) - teplo 18
- **Puffer jacket** (torso 97/99) - teplo 16
- **Ski jacket** (torso 104/106) - teplo 20
- **Heavy coat** (torso 110/112) - teplo 17
- **Winter parka** (torso 115/117) - teplo 22
- **Insulated jacket** (torso 120/122) - te<PERSON><PERSON> 19

#### **Do<PERSON><PERSON><PERSON><PERSON>:**
- **Thermal pants** (legs 12/8) - teplo 10/8
- **Winter boots** (shoes 13/14) - teplo 8
- **Winter hat** (hat 21/22) - teplo 8
- **<PERSON>ie** (hat 8/9) - teplo 5

---

## 🧪 Testovací postupy:

### Test 1: Z<PERSON>ladní zimní oblečení
```bash
1. /testweatherscenario cold
2. /checknpcclothing
3. Měli byste vidět NPC s:
   - Hoodie (torso 24/25)
   - Jacket (torso 31/33)
   - Sweater (torso 42/44)
   - Jeans (legs 9/6)
   - Boots (shoes 10/11)
   - Beanie (hat 8/9)
```

### Test 2: Extrémní zimní oblečení (SNOW)
```bash
1. /testweatherscenario snow
2. /checknpcclothing
3. Měli byste vidět NPC s:
   - Heavy winter coat (torso 91/93)
   - Puffer jacket (torso 97/99)
   - Ski jacket (torso 104/106)
   - Thermal pants (legs 12/8)
   - Winter boots (shoes 13/14)
   - Winter hat (hat 21/22)
```

### Test 3: XMAS speciální oblečení
```bash
1. /weather xmas
2. /updatenpcs
3. /checknpcclothing
4. NPC by měli mít nejtepleší oblečení:
   - Winter parka (torso 115/117) - teplo 22
   - Thermal pants (legs 12/8) - teplo 10/8
   - Winter boots (shoes 13/14) - teplo 8
   - Winter hat (hat 21/22) - teplo 8
```

### Test 4: Okamžitá změna oblečení
```bash
1. /weather clear (normální počasí)
2. Počkejte 10 sekund
3. /weather snow
4. NPC by se měli OKAMŽITĚ obléct teple
5. /checknpcclothing - ověřte změnu
```

---

## 🎯 Očekávané výsledky:

### **Chladné počasí (5-15°C):**
- NPC nosí **hoodie, jacket, sweater**
- Celkové teplo oblečení: **15-25**
- Mají **beanie nebo cap**
- Nosí **jeans a boots**

### **Mrazivé počasí (SNOW/XMAS):**
- NPC nosí **heavy winter coat, puffer jacket, ski jacket**
- Celkové teplo oblečení: **35-50**
- Mají **winter hat**
- Nosí **thermal pants a winter boots**

### **Speciální funkce:**
- **SNOW/XMAS počasí** automaticky aktivuje 'freezing' kategorii
- **Okamžitá změna** oblečení při změně počasí
- **Různorodost** - každý NPC má jiné oblečení

---

## 🔧 Nové příkazy:

### Admin příkazy:
- `/checknpcclothing` - Zobrazí oblečení 5 nejbližších NPC
- `/updatenpcs` - Force update všech NPC outfitů
- `/testweatherscenario snow` - Nastaví sněhové počasí

### Debug informace:
```
NPC 1 (male): Torso=91, Legs=12, Shoes=13, Hat=21, Warmth=45
NPC 2 (female): Torso=93, Legs=8, Shoes=14, Hat=22, Warmth=44
```

---

## 📊 Clothing Component Mapping:

### **Torso (Component 11):**
```
Lehké: 24,25 (Hoodie), 31,33 (Jacket), 42,44 (Sweater)
Střední: 61,63 (Light jacket), 84,86 (Windbreaker)
Těžké: 91,93 (Heavy coat), 97,99 (Puffer), 104,106 (Ski)
Extra: 110,112 (Heavy), 115,117 (Parka), 120,122 (Insulated)
```

### **Legs (Component 4):**
```
Normální: 6,7 (Jeans), 9,10 (Warm pants)
Zimní: 12,8 (Thermal pants)
```

### **Shoes (Component 6):**
```
Normální: 7,8 (Sneakers), 10,11 (Boots)
Zimní: 12 (Winter shoes), 13,14 (Winter boots)
```

### **Hat (Prop 0):**
```
Lehké: 8,9 (Beanie), 18,19 (Cap)
Zimní: 21,22 (Winter hat)
```

---

## ✅ Kontrolní seznam:

- [ ] NPC se oblékají podle počasí
- [ ] SNOW/XMAS aktivuje nejteplejší oblečení
- [ ] Okamžitá změna při weather change
- [ ] Různorodost oblečení mezi NPC
- [ ] Správné tepelné hodnoty
- [ ] Debug příkazy fungují

**Zimní oblečení je nyní plně funkční!** ❄️🧥
