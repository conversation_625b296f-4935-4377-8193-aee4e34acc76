{"name": "ylru", "description": "Extends LRU base on hashlru", "version": "1.2.1", "homepage": "https://github.com/node-modules/ylru", "repository": {"type": "git", "url": "git://github.com/node-modules/ylru.git"}, "dependencies": {}, "devDependencies": {"beautify-benchmark": "^0.2.4", "benchmark": "^2.1.3", "egg-bin": "^1.10.0", "eslint": "^3.12.2", "eslint-config-egg": "^3.2.0", "hashlru": "^1.0.3", "ko-sleep": "^1.0.2", "lru-cache": "^4.0.2"}, "main": "index.js", "files": ["index.js"], "scripts": {"lint": "eslint test *.js", "test": "npm run lint -- --fix && npm run test-local", "test-local": "egg-bin test", "cov": "egg-bin cov", "ci": "npm run lint && npm run cov", "autod": "autod"}, "author": "fengmk2", "engines": {"node": ">= 4.0.0"}, "license": "MIT"}