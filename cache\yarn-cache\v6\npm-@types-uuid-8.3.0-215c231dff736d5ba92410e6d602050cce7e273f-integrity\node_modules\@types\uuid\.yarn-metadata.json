{"manifest": {"name": "@types/uuid", "version": "8.3.0", "description": "TypeScript definitions for uuid", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/iamolivinius"}, {"name": "<PERSON>", "url": "https://github.com/felipeochoa"}, {"name": "<PERSON>", "url": "https://github.com/cjbarth"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LinusU"}, {"name": "<PERSON>", "url": "https://github.com/ctavan"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/uuid"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "ad6e3cc91784463861502ee3a21d504b2b1708a3cbda624b0d0b662a24946484", "typeScriptVersion": "3.0", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-uuid-8.3.0-215c231dff736d5ba92410e6d602050cce7e273f-integrity\\node_modules\\@types\\uuid\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/uuid`\n\n# Summary\nThis package contains type definitions for uuid (https://github.com/uuidjs/uuid).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/uuid.\n\n### Additional Details\n * Last updated: Wed, 12 Aug 2020 06:13:39 GMT\n * Dependencies: none\n * Global values: none\n\n# Credits\nThese definitions were written by [<PERSON>](https://github.com/iamolivinius), [<PERSON>](https://github.com/felipeochoa), [<PERSON>](https://github.com/cjbarth), [<PERSON><PERSON>](https://github.com/LinusU), and [<PERSON>](https://github.com/ctavan).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/uuid/-/uuid-8.3.0.tgz#215c231dff736d5ba92410e6d602050cce7e273f", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/uuid/-/uuid-8.3.0.tgz", "hash": "215c231dff736d5ba92410e6d602050cce7e273f", "integrity": "sha512-eQ9qFW/fhfGJF8WKHGEHZEyVWfZxrT+6CLIJGBcZPfxUh/+BnEj+UCGYMlr9qZuX/2AltsvwrGqp0LhEW8D0zQ==", "registry": "npm", "packageName": "@types/uuid", "cacheIntegrity": "sha512-eQ9qFW/fhfGJF8WKHGEHZEyVWfZxrT+6CLIJGBcZPfxUh/+BnEj+UCGYMlr9qZuX/2AltsvwrGqp0LhEW8D0zQ== sha1-IVwjHf9zbVupJBDm1gIFDM5+Jz8="}, "registry": "npm", "hash": "215c231dff736d5ba92410e6d602050cce7e273f"}