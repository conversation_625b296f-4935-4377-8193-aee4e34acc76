{"name": "memory-fs", "version": "0.5.0", "description": "A simple in-memory filesystem. Holds data in a javascript object.", "main": "lib/MemoryFileSystem.js", "directories": {"test": "test"}, "files": ["lib/"], "scripts": {"test": "mocha", "lint": "eslint lib/*", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly && npm run lint"}, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}, "repository": {"type": "git", "url": "https://github.com/webpack/memory-fs.git"}, "keywords": ["fs", "memory"], "author": "<PERSON> @sokra", "license": "MIT", "bugs": {"url": "https://github.com/webpack/memory-fs/issues"}, "homepage": "https://github.com/webpack/memory-fs", "devDependencies": {"bl": "^1.0.0", "codecov.io": "^0.1.4", "coveralls": "^2.11.2", "eslint": "^4.0.0", "istanbul": "0.4.5", "mocha": "3.2.0", "should": "^4.0.4"}, "dependencies": {"errno": "^0.1.3", "readable-stream": "^2.0.1"}}