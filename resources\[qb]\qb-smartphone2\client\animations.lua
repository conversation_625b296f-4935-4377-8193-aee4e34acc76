local QBCore = exports['qb-core']:GetCoreObject()

-- Lok<PERSON>lní proměnné pro animace
local currentAnimation = nil
local phoneObject = nil
local isAnimating = false

-- Animační slovník
local AnimationDict = {
    ['open'] = {
        dict = 'cellphone@',
        anim = 'cellphone_text_read_base',
        flag = 49,
        duration = -1
    },
    ['close'] = {
        dict = 'cellphone@',
        anim = 'cellphone_text_read_base_exit',
        flag = 49,
        duration = 1000
    },
    ['call'] = {
        dict = 'cellphone@',
        anim = 'cellphone_call_listen_base',
        flag = 49,
        duration = -1
    },
    ['call_end'] = {
        dict = 'cellphone@',
        anim = 'cellphone_call_listen_exit',
        flag = 49,
        duration = 1000
    },
    ['text'] = {
        dict = 'cellphone@',
        anim = 'cellphone_text_in',
        flag = 49,
        duration = -1
    },
    ['photo'] = {
        dict = 'cellphone@',
        anim = 'cellphone_photo_idle',
        flag = 49,
        duration = -1
    },
    ['selfie'] = {
        dict = 'cellphone@in_car@ds',
        anim = 'cellphone_selfie',
        flag = 49,
        duration = -1
    }
}

-- Funkce pro přehrání animace
function PlayPhoneAnimation(animationType, duration)
    if isAnimating then
        StopPhoneAnimation()
    end
    
    local ped = PlayerPedId()
    local animData = AnimationDict[animationType]
    
    if not animData then
        print('Neznámý typ animace: ' .. tostring(animationType))
        return false
    end
    
    -- Načtení animačního slovníku
    RequestAnimDict(animData.dict)
    while not HasAnimDictLoaded(animData.dict) do
        Wait(0)
    end
    
    -- Vytvoření telefonu jako prop
    if not phoneObject and (animationType == 'open' or animationType == 'call' or animationType == 'text' or animationType == 'photo') then
        CreatePhoneObject()
    end
    
    -- Přehrání animace
    TaskPlayAnim(ped, animData.dict, animData.anim, 3.0, -1, duration or animData.duration, animData.flag, 0, false, false, false)
    
    currentAnimation = animationType
    isAnimating = true
    
    -- Automatické ukončení animace po určité době
    if duration and duration > 0 then
        SetTimeout(duration, function()
            if currentAnimation == animationType then
                StopPhoneAnimation()
            end
        end)
    end
    
    return true
end

-- Funkce pro zastavení animace
function StopPhoneAnimation()
    if not isAnimating then return end
    
    local ped = PlayerPedId()
    
    -- Zastavení animace
    ClearPedTasks(ped)
    
    -- Smazání telefonu
    if phoneObject then
        DeleteEntity(phoneObject)
        phoneObject = nil
    end
    
    currentAnimation = nil
    isAnimating = false
end

-- Funkce pro vytvoření telefonu jako objektu
function CreatePhoneObject()
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)

    -- Model telefonu
    local phoneModel = `prop_phone_01`
    RequestModel(phoneModel)
    while not HasModelLoaded(phoneModel) do
        Wait(0)
    end

    -- Vytvoření objektu
    phoneObject = CreateObject(phoneModel, coords.x, coords.y, coords.z, true, true, false)

    -- Připojení k ruce hráče
    local boneIndex = GetPedBoneIndex(ped, 28422) -- pravá ruka
    AttachEntityToEntity(phoneObject, ped, boneIndex, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, true, true, false, true, 1, true)

    SetModelAsNoLongerNeeded(phoneModel)
end

-- Funkce pro změnu animace bez zastavení
function ChangePhoneAnimation(newAnimationType)
    if not isAnimating then
        return PlayPhoneAnimation(newAnimationType)
    end
    
    local ped = PlayerPedId()
    local animData = AnimationDict[newAnimationType]
    
    if not animData then
        return false
    end
    
    -- Načtení nového animačního slovníku
    RequestAnimDict(animData.dict)
    while not HasAnimDictLoaded(animData.dict) do
        Wait(0)
    end
    
    -- Přechod na novou animaci
    TaskPlayAnim(ped, animData.dict, animData.anim, 3.0, -1, animData.duration, animData.flag, 0, false, false, false)
    
    currentAnimation = newAnimationType
    
    return true
end

-- Funkce pro kontrolu, zda je animace aktivní
function IsPhoneAnimationActive()
    return isAnimating
end

-- Funkce pro získání současné animace
function GetCurrentPhoneAnimation()
    return currentAnimation
end

-- Event handlery
RegisterNetEvent('qb-smartphone2:client:playAnimation', function(animationType, duration)
    PlayPhoneAnimation(animationType, duration)
end)

RegisterNetEvent('qb-smartphone2:client:stopAnimation', function()
    StopPhoneAnimation()
end)

RegisterNetEvent('qb-smartphone2:client:changeAnimation', function(animationType)
    ChangePhoneAnimation(animationType)
end)

-- Automatické animace při různých akcích
RegisterNetEvent('qb-smartphone2:client:openPhone', function()
    PlayPhoneAnimation('open')
end)

RegisterNetEvent('qb-smartphone2:client:closePhone', function()
    PlayPhoneAnimation('close', 1000)
    SetTimeout(1000, function()
        StopPhoneAnimation()
    end)
end)

RegisterNetEvent('qb-smartphone2:client:incomingCall', function(callData)
    PlayPhoneAnimation('call')
    
    -- Vibrace telefonu
    if Config.VibrationEnabled then
        CreateThread(function()
            while currentAnimation == 'call' do
                SetPadShake(0, 100, 200)
                Wait(1000)
            end
        end)
    end
end)

RegisterNetEvent('qb-smartphone2:client:callAccepted', function()
    ChangePhoneAnimation('call')
end)

RegisterNetEvent('qb-smartphone2:client:callEnded', function()
    if currentAnimation == 'call' then
        PlayPhoneAnimation('call_end', 1000)
        SetTimeout(1000, function()
            if GetCurrentPhoneAnimation() == 'call_end' then
                StopPhoneAnimation()
            end
        end)
    end
end)

-- NUI Callbacks pro animace
RegisterNUICallback('startTyping', function(data, cb)
    if isAnimating and currentAnimation ~= 'text' then
        ChangePhoneAnimation('text')
    end
    cb('ok')
end)

RegisterNUICallback('stopTyping', function(data, cb)
    if currentAnimation == 'text' then
        ChangePhoneAnimation('open')
    end
    cb('ok')
end)

RegisterNUICallback('openCamera', function(data, cb)
    ChangePhoneAnimation('photo')
    cb('ok')
end)

RegisterNUICallback('takeSelfie', function(data, cb)
    ChangePhoneAnimation('selfie')
    cb('ok')
end)

-- Cleanup při odpojení nebo unload
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        StopPhoneAnimation()
    end
end)

AddEventHandler('playerDropped', function()
    StopPhoneAnimation()
end)

-- Export funkce pro jiné scripty
exports('PlayPhoneAnimation', PlayPhoneAnimation)
exports('StopPhoneAnimation', StopPhoneAnimation)
exports('ChangePhoneAnimation', ChangePhoneAnimation)
exports('IsPhoneAnimationActive', IsPhoneAnimationActive)
exports('GetCurrentPhoneAnimation', GetCurrentPhoneAnimation)
