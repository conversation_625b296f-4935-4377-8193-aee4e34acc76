{"manifest": {"name": "randombytes", "version": "2.1.0", "description": "random bytes from browserify stand alone", "main": "index.js", "scripts": {"test": "standard && node test.js | tspec", "phantom": "zuul --phantom -- test.js", "local": "zuul --local --no-coverage -- test.js"}, "repository": {"type": "git", "url": "**************:crypto-browserify/randombytes.git"}, "keywords": ["crypto", "random"], "author": {}, "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/randombytes/issues"}, "homepage": "https://github.com/crypto-browserify/randombytes", "browser": "browser.js", "devDependencies": {"phantomjs": "^1.9.9", "standard": "^10.0.2", "tap-spec": "^2.1.2", "tape": "^4.6.3", "zuul": "^3.7.2"}, "dependencies": {"safe-buffer": "^5.1.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-randombytes-2.1.0-df6f84372f0270dc65cdf6291349ab7a473d4f2a-integrity\\node_modules\\randombytes\\package.json", "readmeFilename": "README.md", "readme": "randombytes\n===\n\n[![Version](http://img.shields.io/npm/v/randombytes.svg)](https://www.npmjs.org/package/randombytes) [![Build Status](https://travis-ci.org/crypto-browserify/randombytes.svg?branch=master)](https://travis-ci.org/crypto-browserify/randombytes)\n\nrandombytes from node that works in the browser.  In node you just get crypto.randomBytes, but in the browser it uses .crypto/msCrypto.getRandomValues\n\n```js\nvar randomBytes = require('randombytes');\nrandomBytes(16);//get 16 random bytes\nrandomBytes(16, function (err, resp) {\n  // resp is 16 random bytes\n});\n```\n", "licenseText": "MIT License\n\nCopyright (c) 2017 crypto-browserify\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/randombytes/-/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a", "type": "tarball", "reference": "https://registry.yarnpkg.com/randombytes/-/randombytes-2.1.0.tgz", "hash": "df6f84372f0270dc65cdf6291349ab7a473d4f2a", "integrity": "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==", "registry": "npm", "packageName": "randombytes", "cacheIntegrity": "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ== sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo="}, "registry": "npm", "hash": "df6f84372f0270dc65cdf6291349ab7a473d4f2a"}