-- Main server script for Weather Health System
local QBCore = exports['qb-core']:GetCoreObject()

-- Server variables
local playerHealthData = {}
local npcHealthData = {}
local currentWeather = 'CLEAR'
local weatherStats = {
    totalPlayers = 0,
    affectedPlayers = 0,
    sickPlayers = 0,
    totalNPCs = 0,
    sickNPCs = 0,
    deadNPCs = 0
}

-- Initialize system
CreateThread(function()
    while not QBCore do
        Wait(100)
    end
    
    -- Initialize database
    InitializeDatabase()
    
    -- Start server loops
    StartHealthMonitoring()
    StartDataSaving()
    
    print("^2[WeatherHealth]^7 Server system initialized")
end)

-- Initialize database tables
function InitializeDatabase()
    -- Check if tables exist, create if not
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `weather_player_health` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `citizenid` varchar(50) NOT NULL,
            `health_status` int(11) DEFAULT 100,
            `disease_type` varchar(50) DEFAULT NULL,
            `disease_start_time` timestamp NULL DEFAULT NULL,
            `disease_severity` int(11) DEFAULT 0,
            `last_weather_check` timestamp DEFAULT CURRENT_TIMESTAMP,
            `temperature_comfort` varchar(20) DEFAULT 'comfortable',
            `clothing_warmth` int(11) DEFAULT 0,
            `immunity_level` int(11) DEFAULT 50,
            `treatment_history` text DEFAULT NULL,
            `last_treatment_time` timestamp NULL DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `citizenid` (`citizenid`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]])
    
    print("^2[WeatherHealth]^7 Database initialized")
end

-- Start health monitoring loop
function StartHealthMonitoring()
    CreateThread(function()
        while true do
            ProcessPlayerHealthUpdates()
            ProcessNPCHealthUpdates()
            UpdateWeatherStats()
            
            Wait(60000) -- Every minute
        end
    end)
end

-- Start data saving loop
function StartDataSaving()
    CreateThread(function()
        while true do
            SaveAllPlayerData()
            CleanupOldData()
            
            Wait(300000) -- Every 5 minutes
        end
    end)
end

-- Process player health updates
function ProcessPlayerHealthUpdates()
    for citizenid, healthData in pairs(playerHealthData) do
        local Player = QBCore.Functions.GetPlayerByCitizenId(citizenid)
        if Player then
            ProcessIndividualPlayerHealth(Player, healthData)
        else
            -- Player offline, remove from active tracking
            playerHealthData[citizenid] = nil
        end
    end
end

-- Process individual player health
function ProcessIndividualPlayerHealth(Player, healthData)
    if not healthData.lastUpdate then return end
    
    local currentTime = os.time()
    local timeSinceUpdate = currentTime - healthData.lastUpdate
    
    -- Skip if updated too recently
    if timeSinceUpdate < 60 then return end
    
    local comfort = healthData.comfort or 'comfortable'
    local healthChange = Config.HealthEffects.healthChangeRate[comfort] or 0
    
    -- Apply health changes
    if healthChange ~= 0 then
        local newHealth = math.max(0, math.min(100, healthData.health + healthChange))
        healthData.health = newHealth
        
        -- Update player metadata
        Player.Functions.SetMetaData('health_status', newHealth)
        
        -- Check for disease contraction
        if newHealth < 70 and not healthData.diseaseType then
            CheckDiseaseContraction(Player, healthData, comfort)
        end
        
        -- Check for disease recovery
        if healthData.diseaseType and newHealth > 90 then
            CheckDiseaseRecovery(Player, healthData)
        end
    end
    
    healthData.lastUpdate = currentTime
end

-- Check for disease contraction
function CheckDiseaseContraction(Player, healthData, comfort)
    local contractionChance = 0
    
    if comfort == 'dangerous' then
        contractionChance = 15 -- 15% chance
    elseif comfort == 'critical' then
        contractionChance = 30 -- 30% chance
    else
        return
    end
    
    -- Factor in immunity level
    local immunityFactor = (healthData.immunityLevel or 50) / 100
    contractionChance = contractionChance * (1 - immunityFactor)
    
    if math.random(1, 100) <= contractionChance then
        local diseaseType = DetermineDiseaseType(healthData.weather, comfort)
        ContractPlayerDisease(Player, healthData, diseaseType)
    end
end

-- Determine disease type based on conditions
function DetermineDiseaseType(weather, comfort)
    local temp = Config.WeatherTemperature[weather] or 20
    
    if temp < 5 and comfort == 'critical' then
        return 'hypothermia'
    elseif temp > 30 and comfort == 'critical' then
        return 'heatstroke'
    elseif temp < 15 then
        return math.random() < 0.5 and 'cold' or 'flu'
    else
        return 'cold'
    end
end

-- Contract player disease
function ContractPlayerDisease(Player, healthData, diseaseType)
    healthData.diseaseType = diseaseType
    healthData.diseaseStartTime = os.time()
    healthData.diseaseSeverity = 1
    
    -- Notify client
    TriggerClientEvent('weatherhealth:client:contractDisease', Player.PlayerData.source, diseaseType, 1)
    
    -- Log the event
    LogHealthEvent(Player.PlayerData.citizenid, 'disease_contracted', diseaseType)
    
    print(string.format("^3[WeatherHealth]^7 Player %s contracted %s", 
          Player.PlayerData.name, diseaseType))
end

-- Check for disease recovery
function CheckDiseaseRecovery(Player, healthData)
    if not healthData.diseaseType then return end
    
    local recoveryChance = 20 -- 20% base chance
    local immunityBonus = (healthData.immunityLevel or 50) / 10
    recoveryChance = recoveryChance + immunityBonus
    
    if math.random(1, 100) <= recoveryChance then
        CurePlayerDisease(Player, healthData)
    end
end

-- Cure player disease
function CurePlayerDisease(Player, healthData)
    local diseaseType = healthData.diseaseType
    
    healthData.diseaseType = nil
    healthData.diseaseStartTime = nil
    healthData.diseaseSeverity = 0
    
    -- Boost immunity slightly
    healthData.immunityLevel = math.min(100, (healthData.immunityLevel or 50) + 5)
    
    -- Notify client
    TriggerClientEvent('weatherhealth:client:cureDisease', Player.PlayerData.source, diseaseType)
    
    -- Log the event
    LogHealthEvent(Player.PlayerData.citizenid, 'disease_cured', diseaseType)
    
    print(string.format("^2[WeatherHealth]^7 Player %s recovered from %s", 
          Player.PlayerData.name, diseaseType))
end

-- Process NPC health updates
function ProcessNPCHealthUpdates()
    for npcId, npcData in pairs(npcHealthData) do
        ProcessIndividualNPCHealth(npcId, npcData)
    end
end

-- Process individual NPC health
function ProcessIndividualNPCHealth(npcId, npcData)
    if not npcData.lastUpdate then return end
    
    local currentTime = os.time()
    local timeSinceUpdate = currentTime - npcData.lastUpdate
    
    -- Skip if updated too recently
    if timeSinceUpdate < 120 then return end -- 2 minutes for NPCs
    
    -- Calculate health change based on weather suitability
    local healthChange = CalculateNPCHealthChange(npcData)
    
    if healthChange ~= 0 then
        npcData.health = math.max(0, math.min(100, npcData.health + healthChange))
        
        -- Check for disease contraction
        if npcData.health < 60 and not npcData.diseaseType then
            ContractNPCDisease(npcId, npcData)
        end
        
        -- Check for death
        if npcData.health <= 0 then
            KillNPC(npcId, npcData)
        end
    end
    
    npcData.lastUpdate = currentTime
end

-- Calculate NPC health change
function CalculateNPCHealthChange(npcData)
    local weather = npcData.weather or 'CLEAR'
    local temp = Config.WeatherTemperature[weather] or 20
    
    -- Simple calculation - NPCs are less adaptable than players
    if temp < 0 or temp > 35 then
        return -3 -- Severe weather
    elseif temp < 10 or temp > 30 then
        return -1 -- Harsh weather
    else
        return 0 -- Comfortable weather
    end
end

-- Contract NPC disease
function ContractNPCDisease(npcId, npcData)
    local diseaseTypes = {'cold', 'flu'}
    local diseaseType = diseaseTypes[math.random(#diseaseTypes)]
    
    npcData.diseaseType = diseaseType
    npcData.diseaseStartTime = os.time()
    
    print(string.format("^3[WeatherHealth]^7 NPC %s contracted %s", npcId, diseaseType))
end

-- Kill NPC due to illness
function KillNPC(npcId, npcData)
    npcData.isDead = true
    npcData.deathTime = os.time()
    
    -- Log NPC death
    LogHealthEvent(npcId, 'npc_death', npcData.diseaseType or 'unknown')
    
    print(string.format("^1[WeatherHealth]^7 NPC %s died from illness", npcId))
end

-- Update weather statistics
function UpdateWeatherStats()
    weatherStats.totalPlayers = 0
    weatherStats.affectedPlayers = 0
    weatherStats.sickPlayers = 0
    weatherStats.totalNPCs = 0
    weatherStats.sickNPCs = 0
    weatherStats.deadNPCs = 0
    
    -- Count players
    for _, healthData in pairs(playerHealthData) do
        weatherStats.totalPlayers = weatherStats.totalPlayers + 1
        
        if healthData.comfort and healthData.comfort ~= 'comfortable' and healthData.comfort ~= 'perfect' then
            weatherStats.affectedPlayers = weatherStats.affectedPlayers + 1
        end
        
        if healthData.diseaseType then
            weatherStats.sickPlayers = weatherStats.sickPlayers + 1
        end
    end
    
    -- Count NPCs
    for _, npcData in pairs(npcHealthData) do
        weatherStats.totalNPCs = weatherStats.totalNPCs + 1
        
        if npcData.diseaseType then
            weatherStats.sickNPCs = weatherStats.sickNPCs + 1
        end
        
        if npcData.isDead then
            weatherStats.deadNPCs = weatherStats.deadNPCs + 1
        end
    end
end

-- Save all player data to database
function SaveAllPlayerData()
    for citizenid, healthData in pairs(playerHealthData) do
        SavePlayerHealthData(citizenid, healthData)
    end
end

-- Save individual player health data
function SavePlayerHealthData(citizenid, healthData)
    MySQL.insert('INSERT INTO weather_player_health (citizenid, health_status, disease_type, disease_start_time, disease_severity, temperature_comfort, clothing_warmth, immunity_level, last_weather_check) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW()) ON DUPLICATE KEY UPDATE health_status = VALUES(health_status), disease_type = VALUES(disease_type), disease_start_time = VALUES(disease_start_time), disease_severity = VALUES(disease_severity), temperature_comfort = VALUES(temperature_comfort), clothing_warmth = VALUES(clothing_warmth), immunity_level = VALUES(immunity_level), last_weather_check = VALUES(last_weather_check), updated_at = NOW()', {
        citizenid,
        healthData.health or 100,
        healthData.diseaseType,
        healthData.diseaseStartTime and os.date('%Y-%m-%d %H:%M:%S', healthData.diseaseStartTime) or nil,
        healthData.diseaseSeverity or 0,
        healthData.comfort or 'comfortable',
        healthData.clothingWarmth or 0,
        healthData.immunityLevel or 50
    })
end

-- Load player health data from database
function LoadPlayerHealthData(citizenid)
    local result = MySQL.single.await('SELECT * FROM weather_player_health WHERE citizenid = ?', {citizenid})
    
    if result then
        return {
            health = result.health_status or 100,
            diseaseType = result.disease_type,
            diseaseStartTime = result.disease_start_time and os.time(os.date("*t", result.disease_start_time)) or nil,
            diseaseSeverity = result.disease_severity or 0,
            comfort = result.temperature_comfort or 'comfortable',
            clothingWarmth = result.clothing_warmth or 0,
            immunityLevel = result.immunity_level or 50,
            lastUpdate = os.time()
        }
    else
        return {
            health = 100,
            diseaseType = nil,
            diseaseStartTime = nil,
            diseaseSeverity = 0,
            comfort = 'comfortable',
            clothingWarmth = 0,
            immunityLevel = 50,
            lastUpdate = os.time()
        }
    end
end

-- Log health events
function LogHealthEvent(identifier, eventType, details)
    MySQL.insert('INSERT INTO weather_logs (weather_type, temperature, time_hour, health_incidents, created_at) VALUES (?, ?, ?, 1, NOW()) ON DUPLICATE KEY UPDATE health_incidents = health_incidents + 1', {
        currentWeather,
        Config.WeatherTemperature[currentWeather] or 20,
        os.date('%H')
    })
end

-- Cleanup old data
function CleanupOldData()
    -- Remove old logs (older than 30 days)
    MySQL.execute('DELETE FROM weather_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)')
    
    -- Remove old treatment records (older than 7 days)
    MySQL.execute('DELETE FROM weather_treatments WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)')
end

-- Event handlers
RegisterNetEvent('QBCore:Server:OnPlayerLoaded', function()
    local Player = QBCore.Functions.GetPlayer(source)
    if Player then
        local citizenid = Player.PlayerData.citizenid
        playerHealthData[citizenid] = LoadPlayerHealthData(citizenid)
        
        print(string.format("^2[WeatherHealth]^7 Loaded health data for %s", Player.PlayerData.name))
    end
end)

RegisterNetEvent('QBCore:Server:OnPlayerUnload', function(source)
    local Player = QBCore.Functions.GetPlayer(source)
    if Player then
        local citizenid = Player.PlayerData.citizenid
        if playerHealthData[citizenid] then
            SavePlayerHealthData(citizenid, playerHealthData[citizenid])
            playerHealthData[citizenid] = nil
        end
    end
end)

-- Weather health specific events
RegisterNetEvent('weatherhealth:server:updatePlayerHealth', function(data)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    if not playerHealthData[citizenid] then
        playerHealthData[citizenid] = LoadPlayerHealthData(citizenid)
    end
    
    local healthData = playerHealthData[citizenid]
    healthData.weather = data.weather
    healthData.clothing = data.clothing
    healthData.clothingWarmth = data.clothingWarmth
    healthData.comfort = data.comfort
    healthData.lastUpdate = os.time()
end)

RegisterNetEvent('weatherhealth:server:updateNPCHealth', function(data)
    local npcId = data.npcId
    
    if not npcHealthData[npcId] then
        npcHealthData[npcId] = {
            health = 100,
            diseaseType = nil,
            diseaseStartTime = nil,
            isDead = false,
            lastUpdate = os.time()
        }
    end
    
    local npcData = npcHealthData[npcId]
    npcData.model = data.model
    npcData.position = data.position
    npcData.weather = data.weather
    npcData.time = data.time
    npcData.gender = data.gender
    npcData.lastUpdate = os.time()
end)

RegisterNetEvent('weatherhealth:server:weatherChanged', function(newWeather, oldWeather)
    currentWeather = newWeather

    -- Log weather change
    MySQL.insert('INSERT INTO weather_logs (weather_type, temperature, time_hour, affected_players, affected_npcs, created_at) VALUES (?, ?, ?, ?, ?, NOW())', {
        newWeather,
        Config.WeatherTemperature[newWeather] or 20,
        os.date('%H'),
        weatherStats.affectedPlayers,
        weatherStats.sickNPCs
    })
end)

RegisterNetEvent('weatherhealth:server:applyHealthLoss', function(healthLoss)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end

    local currentHealth = Player.PlayerData.metadata.health or 100
    local newHealth = math.max(0, currentHealth - healthLoss)

    Player.Functions.SetMetaData('health', newHealth)
end)

RegisterNetEvent('weatherhealth:server:updateThirst', function(newThirst)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end

    Player.Functions.SetMetaData('thirst', newThirst)
end)

RegisterNetEvent('weatherhealth:server:recordTreatment', function(itemName, diseaseType, success)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end

    local citizenid = Player.PlayerData.citizenid
    local healthData = playerHealthData[citizenid]

    if healthData then
        local healthBefore = healthData.health
        local healthAfter = success and math.min(100, healthBefore + 20) or healthBefore

        MySQL.insert('INSERT INTO weather_treatments (citizenid, treatment_type, item_used, disease_treated, treatment_result, health_before, health_after, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())', {
            citizenid,
            'item_treatment',
            itemName,
            diseaseType,
            success and 'success' or 'failed',
            healthBefore,
            healthAfter
        })

        if success then
            healthData.health = healthAfter
        end
    end
end)

-- Export functions
exports('getPlayerHealthData', function(citizenid)
    return playerHealthData[citizenid]
end)

exports('getWeatherStats', function()
    return weatherStats
end)

exports('getCurrentWeather', function()
    return currentWeather
end)
