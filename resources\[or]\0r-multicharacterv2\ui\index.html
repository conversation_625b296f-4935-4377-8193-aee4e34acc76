<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Document</title>

        <!-- CSS only -->
        <link rel="stylesheet" href="./index.css">
        <script src="https://cdn.tailwindcss.com"></script>

        <!-- JS only -->
        <script src="./assets/api/vue.global.js"></script>
        <script src="./assets/api/vuex.global.js"></script>

        <!-- FA only -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"/>
    </head>
    <body>
        <v-app data-app>
            <div id="app" class="w-[100vw] h-[100vh] hidden overflow-hidden" style="background: linear-gradient(90deg, rgba(14, 14, 14, 0.79) 0%, rgba(14, 14, 14, 0) 49.51%, rgba(14, 14, 14, 0.79) 99.01%);">
                <div v-if="isLoading" class="w-full h-full flex flex-col items-center relative">
                    <img id="server-logo" src="./assets/image/logo.png" alt="logo" class="w-[6.6667vw] h-[6.6667vw] object-contain absolute -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2 z-[2]" draggable="false">
                    <div class="w-full h-full flex flex-col items-center">
                        <div id="top-bg" class="w-full h-1/2 bg-[#000000]"></div>
                        <div id="bottom-bg" class="w-full h-1/2 bg-[#000000]"></div>
                    </div>
                </div>
                <div v-if="!isLoading && pages.multicharacter" class="w-full h-full flex items-center justify-between">
                    <div v-if="notifications.length !== 0" id="top-side" class="absolute top-[1.0417vw] -translate-x-1/2 left-1/2 flex flex-col items-center gap-y-[.2604vw]">
                        <div v-for="notification in notifications" :key="notification.id" :id="`notification-${notification.id}`" class="w-[21.875vw] min-h-[2.6042vw] rounded-[.2604vw] flex items-center justify-between px-[.7813vw] py-[.6771vw] gap-x-[.5208vw] notify-in" :class="notification.type == 'error' ? 'bg-[#D9534F]' : notification.type == 'success' ? 'bg-[#FFF]/70' : 'bg-[#EFBF20]'">
                            <img :src="`./assets/svg/${notification.type}.svg`" :alt="notification.type" class="w-[1.25vw] h-[1.25vw] object-contain">
                            <p class="font-['Satoshi-Medium'] text-[.6771vw] tracking-[-5%] text-[#121212]/80 leading-tight">{{ notification.message }}</p>
                        </div>
                    </div>
                    <div id="left-side" class="h-full px-[1.6146vw] py-[1.5104vw] flex-col items-start justify-between hidden">
                        <div v-if="!isNewCharacter" class="px-[.9375vw] flex flex-col items-start h-full justify-between py-[2.2917vw]">
                            <div class="flex flex-col items-start gap-y-[.625vw]">
                                <div class="flex items-center gap-x-[.8854vw]">
                                    <div class="flex flex-col items-start">
                                        <div class="flex items-center gap-x-[.5208vw]">
                                            <p class="font-['Satoshi-Medium'] text-[2.1875vw] leading-none text-[#FFFFFF] uppercase">{{ activeCharacter.charinfo.firstname }}</p>
                                            <p class="font-['Satoshi-Light'] text-[2.1875vw] leading-none text-[#FFFFFF] uppercase">{{ activeCharacter.charinfo.lastname }}</p>
                                        </div>
                                        <p class="font-['Satoshi-Regular'] text-[.9375vw] leading-none" :class="settings.clear ? `text-[#FFF]/80` : `text-[${settings.color.hex}]`">ID {{ settings.streamer ? '*******' : activeCharacter.citizenid }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-col items-start gap-y-[2.0833vw]">
                                <div>
                                    <div class="flex flex-col items-start">
                                        <p class="font-['Satoshi-Regular'] text-[16.1491px] leading-tight text-[#A5A5A5] uppercase">{{ locales['your'] }}</p>
                                        <p class="font-['Satoshi-Regular'] text-[.8411vw] leading-tight text-[#FFFFFF] uppercase">{{ locales['characterDetails'] }}</p>
                                    </div>
                                    <div class="grid gap-y-[.5208vw] mt-[.625vw] gap-x-[.8333vw]" style="grid-template-columns: repeat(2, max-content);">
                                        <div class="flex items-center gap-x-[.3125vw] min-w-[6.0083vw]">
                                            <div class="w-[2.1875vw] h-[2.1875vw] bg-[#ffffff17] flex items-center justify-center rounded-[.2083vw]">
                                                <img src="./assets/svg/birthday.svg" alt="birthday" class="w-[.9375vw] h-auto object-contain" draggable="false">
                                            </div>
                                            <div class="flex flex-col items-start gap-y-[.1563vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.5208vw] leading-none text-[#FFFFFF]/50 uppercase">{{ locales['birthday'] }}</p>
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-none text-[#CFCFCF] uppercase">{{ activeCharacter.charinfo.birthdate }}</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-x-[.3125vw] min-w-[6.0083vw]">
                                            <div class="w-[2.1875vw] h-[2.1875vw] bg-[#ffffff17] flex items-center justify-center rounded-[.2083vw]">
                                                <img src="./assets/svg/phone.svg" alt="phone" class="w-[1.1458vw] h-auto object-contain" draggable="false">
                                            </div>
                                            <div class="flex flex-col items-start gap-y-[.1563vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.5208vw] leading-none text-[#FFFFFF]/50 uppercase">{{ locales['phoneNumber'] }}</p>
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-none text-[#CFCFCF] uppercase">{{ settings.streamer ? '*******' : activeCharacter.charinfo.phone }}</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-x-[.3125vw] min-w-[6.0083vw]">
                                            <div class="w-[2.1875vw] h-[2.1875vw] bg-[#ffffff17] flex items-center justify-center rounded-[.2083vw]">
                                                <img src="./assets/svg/gender.svg" alt="gender" class="w-[1.0417vw] h-auto object-contain" draggable="false">
                                            </div>
                                            <div class="flex flex-col items-start gap-y-[.1563vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.5208vw] leading-none text-[#FFFFFF]/50 uppercase">{{ locales['gender'] }}</p>
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-none text-[#CFCFCF] uppercase">{{ activeCharacter.charinfo.gender == 0 ? locales.male : locales.female }}</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-x-[.3125vw] min-w-[6.0083vw]">
                                            <div class="w-[2.1875vw] h-[2.1875vw] bg-[#ffffff17] flex items-center justify-center rounded-[.2083vw]">
                                                <img src="./assets/svg/nationality.svg" alt="nationality" class="w-[.8333vw] h-auto object-contain" draggable="false">
                                            </div>
                                            <div class="flex flex-col items-start gap-y-[.1563vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.5208vw] leading-none text-[#FFFFFF]/50 uppercase">{{ locales['nationality'] }}</p>
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-none text-[#CFCFCF] uppercase">{{ activeCharacter.charinfo.nationality }}</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-x-[.3125vw] min-w-[6.0083vw]">
                                            <div class="w-[2.1875vw] h-[2.1875vw] bg-[#ffffff17] flex items-center justify-center rounded-[.2083vw]">
                                                <img src="./assets/svg/height.svg" alt="height" class="w-[.9375vw] h-auto object-contain" draggable="false">
                                            </div>
                                            <div class="flex flex-col items-start gap-y-[.1563vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.5208vw] leading-none text-[#FFFFFF]/50 uppercase">{{ locales['height'] }}</p>
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-none text-[#CFCFCF] uppercase">{{ activeCharacter.charinfo.heigth ?? 'N/A' }} CM</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-x-[.3125vw] min-w-[6.0083vw]">
                                            <div class="w-[2.1875vw] h-[2.1875vw] bg-[#ffffff17] flex items-center justify-center rounded-[.2083vw]">
                                                <img src="./assets/svg/clock.svg" alt="clock" class="w-[1.0417vw] h-auto object-contain" draggable="false">
                                            </div>
                                            <div class="flex flex-col items-start gap-y-[.1563vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.5208vw] leading-none text-[#FFFFFF]/50 uppercase">{{ locales['lastLogin'] }}</p>
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-none text-[#CFCFCF] uppercase">{{ formatDate(activeCharacter.last_updated) }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex flex-col items-start">
                                        <p class="font-['Satoshi-Regular'] text-[.8411vw] leading-tight text-[#A5A5A5] uppercase">{{ locales['character'] }}</p>
                                        <p class="font-['Satoshi-Regular'] text-[.8411vw] leading-tight text-[#FFFFFF] uppercase">{{ locales['jobInfo'] }}</p>
                                    </div>
                                    <div class="grid gap-y-[.5208vw] mt-[.625vw] gap-x-[.8333vw]" style="grid-template-columns: repeat(2, max-content);">
                                        <div class="flex items-center gap-x-[.3125vw] min-w-[6.0083vw]">
                                            <div class="w-[2.1875vw] h-[2.1875vw] bg-[#ffffff17] flex items-center justify-center rounded-[.2083vw]">
                                                <img src="./assets/svg/bank.svg" alt="bank" class="w-[1.1458vw] h-auto object-contain" draggable="false">
                                            </div>
                                            <div class="flex flex-col items-start gap-y-[.1563vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.5208vw] leading-none text-[#FFFFFF]/50 uppercase">{{ locales['job'] }}</p>
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-none text-[#CFCFCF] uppercase">{{ activeCharacter.job.label }}</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-x-[.3125vw] min-w-[6.0083vw]">
                                            <div class="w-[2.1875vw] h-[2.1875vw] bg-[#ffffff17] flex items-center justify-center rounded-[.2083vw]">
                                                <img src="./assets/svg/cash.svg" alt="cash" class="w-[1.1458vw] h-auto object-contain" draggable="false">
                                            </div>
                                            <div class="flex flex-col items-start gap-y-[.1563vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.5208vw] leading-none text-[#FFFFFF]/50 uppercase">{{ locales['rank'] }}</p>
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-none text-[#CFCFCF] uppercase">{{ activeCharacter.job.grade.name }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex flex-col items-start">
                                        <p class="font-['Satoshi-Regular'] text-[.8411vw] leading-tight text-[#A5A5A5] uppercase">{{ locales['character'] }}</p>
                                        <p class="font-['Satoshi-Regular'] text-[.8411vw] leading-tight text-[#FFFFFF] uppercase">{{ locales['financeInfo'] }}</p>
                                    </div>
                                    <div class="grid gap-y-[.5208vw] mt-[.625vw] gap-x-[.8333vw]" style="grid-template-columns: repeat(2, max-content);">
                                        <div class="flex items-center gap-x-[.3125vw] min-w-[6.0083vw]">
                                            <div class="w-[2.1875vw] h-[2.1875vw] bg-[#ffffff17] flex items-center justify-center rounded-[.2083vw]">
                                                <img src="./assets/svg/bank.svg" alt="bank" class="w-[1.1458vw] h-auto object-contain" draggable="false">
                                            </div>
                                            <div class="flex flex-col items-start gap-y-[.1563vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.5208vw] leading-none text-[#FFFFFF]/50 uppercase">{{ locales['bank'] }}</p>
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-none text-[#CFCFCF] uppercase">{{ formatCash(activeCharacter.money.bank) }}</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-x-[.3125vw] min-w-[6.0083vw]">
                                            <div class="w-[2.1875vw] h-[2.1875vw] bg-[#ffffff17] flex items-center justify-center rounded-[.2083vw]">
                                                <img src="./assets/svg/cash.svg" alt="cash" class="w-[1.1458vw] h-auto object-contain" draggable="false">
                                            </div>
                                            <div class="flex flex-col items-start gap-y-[.1563vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.5208vw] leading-none text-[#FFFFFF]/50 uppercase">{{ locales['cash'] }}</p>
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-none text-[#CFCFCF] uppercase">{{ formatCash(activeCharacter.money.cash) }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div v-if="isDeletable" class="flex items-center gap-x-[.6771vw]">
                                    <div class="w-[10.7708vw] h-[2.5vw] flex items-center justify-center relative cursor-pointer" :class="settings.clear ? 'bg-[#FFF]/15' : ''" :style="!settings.clear ? `filter: drop-shadow(0vw 0vw 1.4063vw ${settings.color.shadow});` : ''" @click="playCharacter();">
                                        <p class="font-['Satoshi-Medium'] text-[.8333vw] leading-none text-[#FFFFFF] relative z-[1] uppercase" style="text-shadow: 0vw 0vw 1.433vw rgba(0, 0, 0, 0.47);">{{ locales['play'] }}</p>
                                        <img v-if="!settings.clear" :src="`./assets/image/${settings.color.name}-button.png`" alt="green-button" class="w-full h-full object-contain absolute top-0 left-0" draggable="false">
                                    </div>
                                    <div class="w-[10.7708vw] h-[2.5vw] flex items-center justify-center relative cursor-pointer" :class="settings.clear ? 'bg-[#FFF]/15' : ''" @click="deleteCharacter();">
                                        <p class="font-['Satoshi-Medium'] text-[.8333vw] leading-none text-[#FFFFFF] relative z-[1] uppercase" style="text-shadow: 0vw 0vw 1.433vw rgba(0, 0, 0, 0.47);">{{ locales['delete'] }}</p>
                                        <img v-if="!settings.clear" src="./assets/image/black-button.png" alt="black-button" class="w-full h-full object-contain absolute top-0 left-0" draggable="false">
                                    </div>
                                </div>
                                <div v-if="!isDeletable" class="flex items-center gap-x-[.6771vw]">
                                    <div class="w-[26.0938vw] h-[2.5vw] flex items-center justify-center relative cursor-pointer" :class="settings.clear ? 'bg-[#FFF]/15' : ''" :style="!settings.clear ? `filter: drop-shadow(0vw 0vw 1.4063vw ${settings.color.shadow});` : ''" @click="playCharacter();">
                                        <p class="font-['Satoshi-Medium'] text-[.8333vw] leading-none text-[#FFFFFF] relative z-[1] uppercase" style="text-shadow: 0vw 0vw 1.433vw rgba(0, 0, 0, 0.47);">{{ locales['play'] }}</p>
                                        <img v-if="!settings.clear" :src="`./assets/image/large-${settings.color.name}-button.png`" alt="green-button" class="w-full h-full object-contain absolute top-0 left-0" draggable="false">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="isNewCharacter" class="px-[.9375vw] flex flex-col items-start h-full gap-y-[1.4583vw] py-[2.2917vw]">
                            <div class="flex flex-col items-start gap-y-[.4167vw]">
                                <div class="flex items-center gap-x-[.4167vw]">
                                    <p class="font-['Satoshi-Medium'] text-[1.7vw] leading-none text-[#FFFFFF] uppercase">{{ locales['create'] }}</p>
                                    <p class="font-['Satoshi-Light'] text-[1.7vw] leading-none text-[#FFFFFF] uppercase">{{ locales['character'] }}</p>
                                </div>
                                <p class="text-['Satoshi-Light'] text-[.7292vw] leading-tight text-[#FFF] max-w-[21.7188vw]">{{ locales['createDesc'] }}</p>
                            </div>
                            <div class="flex flex-col gap-y-[.7292vw] h-full justify-between">
                                <div class="flex flex-col items-start gap-y-[.7292vw]">
                                    <div class="flex flex-col items-start gap-y-[.4167vw]">
                                        <div class="flex items-center gap-x-[.5208vw]">
                                            <img src="./assets/svg/id-card.svg" alt="id-card" class="w-[1.1458vw] h-[1.1458vw] object-contain">
                                            <p class="font-['Satoshi-Medium'] text-[.7292vw] text-[#FFF] leading-none">{{ locales['firstAndLastName'] }}</p>
                                        </div>
                                        <div class="flex flex-col items-start gap-y-[.4167vw]">
                                            <div class="flex flex-col items-center gap-y-[.4167vw]">
                                                <div class="w-[16.3125vw] h-[1.875vw] bg-[#FFF]/10 rounded-[.1563vw]">
                                                    <input type="text" class="w-full h-full bg-transparent font-['Satoshi-Regular'] text-[.6771vw] text-[#FFF] leading-none px-[.4167vw] outline-none placeholder:text-[#fff]/70" :placeholder="locales['firstname']" v-model="registerContext.firstname" @input="registerContext.firstname = $event.target.value">
                                                </div>
                                            </div>
                                            <div class="flex flex-col items-center gap-y-[.4167vw]">
                                                <div class="w-[16.3125vw] h-[1.875vw] bg-[#FFF]/10 rounded-[.1563vw]">
                                                    <input type="text" class="w-full h-full bg-transparent font-['Satoshi-Regular'] text-[.6771vw] text-[#FFF] leading-none px-[.4167vw] outline-none placeholder:text-[#fff]/70" :placeholder="locales['lastname']" v-model="registerContext.lastname" @input="registerContext.lastname = $event.target.value">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex flex-col items-start gap-y-[.4167vw]">
                                        <div class="flex items-center gap-x-[.5208vw]">
                                            <img src="./assets/svg/ruler.svg" alt="darulerte" class="w-[1.1458vw] h-[1.1458vw] object-contain">
                                            <p class="font-['Satoshi-Medium'] text-[.7292vw] text-[#FFF] leading-none">{{ locales['height'] }}</p>
                                        </div>
                                        <div class="flex flex-col items-center gap-y-[.4167vw]">
                                            <div class="w-[16.3125vw] h-[1.875vw] bg-[#FFF]/10 rounded-[.1563vw] flex items-center">
                                                <input type="number" class="w-full h-full bg-transparent font-['Satoshi-Regular'] text-[.6771vw] text-[#FFF] leading-none px-[.4167vw] outline-none placeholder:text-[#fff]/70" placeholder="180" v-model="registerContext.heigth" @input="registerContext.heigth = $event.target.value">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex flex-col items-start gap-y-[.4167vw]">
                                        <div class="flex items-center gap-x-[.5208vw]">
                                            <img src="./assets/svg/date.svg" alt="date" class="w-[1.1458vw] h-[1.1458vw] object-contain">
                                            <p class="font-['Satoshi-Medium'] text-[.7292vw] text-[#FFF] leading-none">{{ locales['birthday'] }}</p>
                                        </div>
                                        <div class="flex flex-col items-center gap-y-[.4167vw]">
                                            <div class="w-[16.3125vw] h-[1.875vw] bg-[#FFF]/10 rounded-[.1563vw] flex items-center">
                                                <input type="date" class="w-full h-full bg-transparent font-['Satoshi-Regular'] text-[.6771vw] text-[#FFF] leading-none px-[.4167vw] outline-none placeholder:text-[#fff]/70" placeholder="180" v-model="registerContext.birthdate" @input="registerContext.birthdate = $event.target.value">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex flex-col items-start gap-y-[.4167vw]">
                                        <div class="flex items-center gap-x-[.5208vw]">
                                            <img src="./assets/svg/gender.svg" alt="gender" class="w-[1.1458vw] h-[1.1458vw] object-contain">
                                            <p class="font-['Satoshi-Medium'] text-[.7292vw] text-[#FFF] leading-none">{{ locales['gender'] }}</p>
                                        </div>
                                        <div class="w-[16.3125vw] flex tems-center justify-between gap-y-[.4167vw] gap-x-[.5208vw]">
                                            <div class="w-full h-[1.875vw] bg-[#FFF]/10 rounded-[.1563vw] flex items-center justify-between px-[.3125vw]">
                                                <div class="w-max h-full flex items-center gap-x-[.3125vw]">
                                                    <img src="./assets/svg/male.svg" alt="male" class="w-[1.1458vw] h-[1.1458vw] object-contain">
                                                    <p class="bg-transparent font-['Satoshi-Regular'] text-[.6771vw] text-[#FFF] leading-none outline-none text-[#fff]/70">{{ locales['male'] }}</p>
                                                </div>
                                                <div class="bg-[#FFF]/20 w-[1.4vw] h-[1.4vw] rounded-[.1042vw] flex items-center justify-center" @click="setGender('m')">
                                                    <img v-if="registerContext.gender == 'm'" src="./assets/svg/x.svg" alt="x" class="w-[.9375vw] h-auto">
                                                </div>
                                            </div>
                                            <div class="w-full h-[1.875vw] bg-[#FFF]/10 rounded-[.1563vw] flex items-center justify-between px-[.3125vw]">
                                                <div class="w-max h-full flex items-center gap-x-[.3125vw]">
                                                    <img src="./assets/svg/female.svg" alt="female" class="w-[1.1458vw] h-[1.1458vw] object-contain">
                                                    <p class="bg-transparent font-['Satoshi-Regular'] text-[.6771vw] text-[#FFF] leading-none outline-none text-[#fff]/70">{{ locales['female'] }}</p>
                                                </div>
                                                <div class="bg-[#FFF]/20 w-[1.4vw] h-[1.4vw] rounded-[.1042vw] flex items-center justify-center" @click="setGender('f')">
                                                    <img v-if="registerContext.gender == 'f'" src="./assets/svg/x.svg" alt="x" class="w-[.9375vw] h-auto">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex flex-col items-start gap-y-[.4167vw] relative">
                                        <div class="flex items-center gap-x-[.5208vw]">
                                            <img src="./assets/svg/flag.svg" alt="gender" class="w-[1.1458vw] h-[1.1458vw] object-contain">
                                            <p class="font-['Satoshi-Medium'] text-[.7292vw] text-[#FFF] leading-none">{{ locales['natioanlity'] }}</p>
                                        </div>
                                        <div class="flex flex-col items-center gap-y-[.4167vw]">
                                            <div class="w-[16.3125vw] h-[1.875vw] bg-[#FFF]/10 rounded-[.1563vw] flex items-center justify-between" @click="selectNationality = !selectNationality">
                                                <p class="bg-transparent font-['Satoshi-Regular'] text-[.6771vw] leading-none px-[.4167vw] outline-none" :class="registerContext.nationality ? 'text-[#FFF]' : 'text-[#FFF]/70'">{{ registerContext.nationality ? registerContext.nationality : locales['selectNationality'] }}</p>
                                                <img src="./assets/svg/arrow-down.svg" alt="arrow-down" class="w-[1.1vw] h-[1.1vw] object-contain mx-[.2083vw]">
                                            </div>
                                        </div>
                                        <div v-show="selectNationality" class="bg-[#FFF]/10 rounded-[.1563vw] w-[16.3125vw] h-[8.75vw] overflow-hidden overflow-y-auto flex flex-col items-start justify-start z-10 gap-y-[.2083vw] p-[.1563vw] absolute bottom-[-8.9583vw] left-0 transition-all duration-300">
                                            <div class="flex items-center justify-between px-[.5208vw] bg-[#FFF]/20 w-full h-[1.5625vw]">
                                                <input type="text" v-model="searchedNationalities" class="w-full h-full bg-transparent border-none outline-none text-[#CFCFCF] font-['Satoshi-Regular'] text-[.6771vw] placeholder:text-[#FFF]/50 py-[.4167vw]" :placeholder="locales['search']" />
                                                <img src="./assets/svg/search.svg" alt="search" class="w-[.7813vw] h-[.7813vw] object-contain" />
                                            </div>
                                            <div v-for="(nationalitie, index) in filteredNationalities" class="flex items-center px-[.5208vw] hover:bg-[#FFF]/20 w-full h-[1.5625vw] py-[.4167vw]" @click="changeNationality(nationalitie);">
                                                <p class="font-['Satoshi-Regular'] text-[.6771vw] text-[#CFCFCF] leading-none">{{ nationalitie }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="w-[16.3125vw] h-[2.5vw] relative flex items-center justify-center" :class="settings.clear ? 'bg-[#FFF]/15' : ''" :style="!settings.clear ? `filter: drop-shadow(0vw 0vw 1.4063vw ${settings.color.shadow});` : ''" @click="createCharacter();">
                                    <p class="font-['Satoshi-Medium'] text-[.7047vw] text-[#FFFFFF] leading-none relative z-[1]" style="text-shadow: 0vw 0vw 1.1146vw rgba(0, 0, 0, 0.47);">{{ locales['createNewCharacter'] }}</p>
                                    <img v-if="!settings.clear" :src="`./assets/image/large-${settings.color.name}-button.png`" alt="large-button" class="w-[16.3125vw] h-[2.5vw] absolute top-0 left-0" draggable="false"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="right-side" class="flex-col items-end self-end h-full justify-between px-[1.25vw] py-[1.5104vw] gap-y-[.625vw] hidden">
                        <div class="flex flex-col items-end gap-y-[.625vw]">
                            <div class="flex items-end gap-x-[.625vw]">
                                <div class="w-[1.0417vw] h-[1.0417vw] cursor-pointer opacity-50 transition-all duration-300 hover:opacity-100" @click="togglePhotoMode();">
                                    <img src="./assets/svg/camera.svg" alt="camera" class="w-[1.0417vw] h-[1.0417vw]">
                                </div>
                                <div class="w-[1.0417vw] h-[1.0417vw] cursor-pointer opacity-50 transition-all duration-300 hover:opacity-100" @click="toggleSettings();">
                                    <img src="./assets/svg/settings.svg" alt="settings" class="w-[1.0417vw] h-[1.0417vw]">
                                </div>
                            </div>
                            <div id="settings" class="w-[11.7708vw] h-max rounded-[.3125vw] p-[.625vw] flex flex-col items-start" style="display: none;background: linear-gradient(180deg, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.5) 100%);">
                                <div class="flex items-center w-full justify-between">
                                    <div class="flex items-center gap-x-[.3125vw]">
                                        <img src="./assets/svg/stream.svg" alt="stream" class="w-[.9375vw] h-[.9375vw] object-contain">
                                        <div class="flex items-center gap-x-[.2604vw]">
                                            <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['streamer'] }}</p>
                                            <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['mode'] }}</p>
                                        </div>
                                    </div>
                                    <div
                                        class="w-[2.1875vw] h-[.9896vw] rounded-[.5729vw] relative cursor-pointer transition-colors duration-300"
                                        :class="settings.streamer ? 'bg-[#FFF]' : 'bg-[#FFFFFF29]'"
                                        @click="changeStreamerMode();"
                                    >
                                        <div
                                        class="w-[.7813vw] h-[.7813vw] rounded-full absolute transition-all duration-300"
                                        :class="settings.streamer 
                                            ? 'right-[.1042vw] top-[.1042vw] bg-[#000]' 
                                            : 'left-[.1042vw] top-[.1042vw] bg-[#FFF]'"
                                        ></div>
                                    </div>
                                </div>
                                <div class="w-full h-[.0521vw] bg-[#D9D9D914] rounded-full my-[.625vw]"></div>
                                <div class="flex items-center w-full justify-between">
                                    <div class="flex items-center gap-x-[.3125vw]">
                                        <img src="./assets/svg/music-note.svg" alt="music-note" class="w-[.9375vw] h-[.9375vw] object-contain">
                                        <div class="flex items-center gap-x-[.2604vw]">
                                            <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['mute'] }}</p>
                                            <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['music'] }}</p>
                                        </div>
                                    </div>
                                    <div
                                        class="w-[2.1875vw] h-[.9896vw] rounded-[.5729vw] relative cursor-pointer transition-colors duration-300"
                                        :class="settings.mutedMusic ? 'bg-[#FFF]' : 'bg-[#FFFFFF29]'"
                                        @click="changeMuteMusic();"
                                    >
                                        <div
                                        class="w-[.7813vw] h-[.7813vw] rounded-full absolute transition-all duration-300"
                                        :class="settings.mutedMusic 
                                            ? 'right-[.1042vw] top-[.1042vw] bg-[#000]' 
                                            : 'left-[.1042vw] top-[.1042vw] bg-[#FFF]'"
                                        ></div>
                                    </div>
                                </div>
                                <div class="w-full h-[.0521vw] bg-[#D9D9D914] rounded-full my-[.625vw]"></div>
                                <div class="flex items-center w-full justify-between">
                                    <div class="flex items-center gap-x-[.3125vw]">
                                        <img src="./assets/svg/user.svg" alt="user" class="w-[.9375vw] h-[.9375vw] object-contain">
                                        <div class="flex items-center gap-x-[.2604vw]">
                                            <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['focus'] }}</p>
                                            <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['player'] }}</p>
                                        </div>
                                    </div>
                                    <div
                                        class="w-[2.1875vw] h-[.9896vw] rounded-[.5729vw] relative cursor-pointer transition-colors duration-300"
                                        :class="settings.focus ? 'bg-[#FFF]' : 'bg-[#FFFFFF29]'"
                                        @click="changeFocusPlayer();"
                                    >
                                        <div
                                        class="w-[.7813vw] h-[.7813vw] rounded-full absolute transition-all duration-300"
                                        :class="settings.focus 
                                            ? 'right-[.1042vw] top-[.1042vw] bg-[#000]' 
                                            : 'left-[.1042vw] top-[.1042vw] bg-[#FFF]'"
                                        ></div>
                                    </div>
                                </div>
                                <div class="w-full h-[.0521vw] bg-[#D9D9D914] rounded-full my-[.625vw]"></div>
                                <div class="w-full flex flex-col items-center gap-y-[.4167vw]">
                                    <div class="flex items-center w-full justify-between">
                                        <div class="flex items-center gap-x-[.3125vw]">
                                            <img src="./assets/svg/camera.svg" alt="camera" class="w-[.9375vw] h-[.9375vw] object-contain">
                                            <div class="flex items-center gap-x-[.2604vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['filter'] }}</p>
                                                <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['mode'] }}</p>
                                            </div>
                                        </div>
                                        <div
                                            class="w-[2.1875vw] h-[.9896vw] rounded-[.5729vw] relative cursor-pointer transition-colors duration-300"
                                            :class="settings.filter.status ? 'bg-[#FFF]' : 'bg-[#FFFFFF29]'"
                                            @click="changeSetting('filter', false, 'status');"
                                        >
                                            <div
                                            class="w-[.7813vw] h-[.7813vw] rounded-full absolute transition-all duration-300"
                                            :class="settings.filter.status
                                                ? 'right-[.1042vw] top-[.1042vw] bg-[#000]' 
                                                : 'left-[.1042vw] top-[.1042vw] bg-[#FFF]'"
                                            ></div>
                                        </div>
                                    </div>
                                    <div class="flex items-center w-full justify-between">
                                        <div class="flex items-center gap-x-[.3125vw]">
                                            <img src="./assets/svg/type.svg" alt="type" class="w-[.9375vw] h-[.9375vw] object-contain">
                                            <div class="flex items-center gap-x-[.2604vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['filter'] }}</p>
                                                <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['type'] }}</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-x-[1.25vw]">
                                            <img src="./assets/svg/left-arrow.svg" alt="left-arrow" class="w-[.374vw] h-[.6875vw] object-contain" @click="changeFilter('prev');">
                                            <img src="./assets/svg/right-arrow.svg" alt="right-arrow" class="w-[.374vw] h-[.6875vw] object-contain" @click="changeFilter('next');">
                                        </div>
                                    </div>
                                </div>
                                <div class="w-full h-[.0521vw] bg-[#D9D9D914] rounded-full my-[.625vw]"></div>
                                <div class="w-full flex flex-col items-center gap-y-[.4167vw]">
                                    <div class="flex items-center w-full justify-between">
                                        <div class="flex items-center gap-x-[.3125vw]">
                                            <img src="./assets/svg/stars.svg" alt="stars" class="w-[.9375vw] h-[.9375vw] object-contain">
                                            <div class="flex items-center gap-x-[.2604vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['particle'] }}</p>
                                                <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['show'] }}</p>
                                            </div>
                                        </div>
                                        <div
                                            class="w-[2.1875vw] h-[.9896vw] rounded-[.5729vw] relative cursor-pointer transition-colors duration-300"
                                            :class="settings.particle.status ? 'bg-[#FFF]' : 'bg-[#FFFFFF29]'"
                                            @click="changeSetting('particle', false, 'status');"
                                        >
                                            <div
                                            class="w-[.7813vw] h-[.7813vw] rounded-full absolute transition-all duration-300"
                                            :class="settings.particle.status 
                                                ? 'right-[.1042vw] top-[.1042vw] bg-[#000]' 
                                                : 'left-[.1042vw] top-[.1042vw] bg-[#FFF]'"
                                            ></div>
                                        </div>
                                    </div>
                                    <div class="flex items-center w-full justify-between">
                                        <div class="flex items-center gap-x-[.3125vw]">
                                            <img src="./assets/svg/type.svg" alt="type" class="w-[.9375vw] h-[.9375vw] object-contain">
                                            <div class="flex items-center gap-x-[.2604vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['particle'] }}</p>
                                                <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['type'] }}</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-x-[1.25vw]">
                                            <img src="./assets/svg/left-arrow.svg" alt="left-arrow" class="w-[.374vw] h-[.6875vw] object-contain" @click="changeParticle('prev');">
                                            <img src="./assets/svg/right-arrow.svg" alt="right-arrow" class="w-[.374vw] h-[.6875vw] object-contain" @click="changeParticle('next');">
                                        </div>
                                    </div>
                                </div>
                                <div class="w-full h-[.0521vw] bg-[#D9D9D914] rounded-full my-[.625vw]"></div>
                                <div class="w-full flex flex-col items-center gap-y-[.4167vw]">
                                    <div class="flex items-center w-full justify-between">
                                        <div class="flex items-center gap-x-[.3125vw]">
                                            <img src="./assets/svg/stars.svg" alt="stars" class="w-[.9375vw] h-[.9375vw] object-contain">
                                            <div class="flex items-center gap-x-[.2604vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['animation'] }}</p>
                                                <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['mode'] }}</p>
                                            </div>
                                        </div>
                                        <div
                                            class="w-[2.1875vw] h-[.9896vw] rounded-[.5729vw] relative cursor-pointer transition-colors duration-300"
                                            :class="settings.animations.status ? 'bg-[#FFF]' : 'bg-[#FFFFFF29]'"
                                            @click="changeSetting('animations', false, 'status');"
                                        >
                                            <div
                                            class="w-[.7813vw] h-[.7813vw] rounded-full absolute transition-all duration-300"
                                            :class="settings.animations.status 
                                                ? 'right-[.1042vw] top-[.1042vw] bg-[#000]' 
                                                : 'left-[.1042vw] top-[.1042vw] bg-[#FFF]'"
                                            ></div>
                                        </div>
                                    </div>
                                    <div class="flex items-center w-full justify-between">
                                        <div class="flex items-center gap-x-[.3125vw]">
                                            <img src="./assets/svg/type.svg" alt="type" class="w-[.9375vw] h-[.9375vw] object-contain">
                                            <div class="flex items-center gap-x-[.2604vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['animation'] }}</p>
                                                <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['type'] }}</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-x-[1.25vw]">
                                            <img src="./assets/svg/left-arrow.svg" alt="left-arrow" class="w-[.374vw] h-[.6875vw] object-contain" @click="changeAnimation('prev');">
                                            <img src="./assets/svg/right-arrow.svg" alt="right-arrow" class="w-[.374vw] h-[.6875vw] object-contain" @click="changeAnimation('next');">
                                        </div>
                                    </div>
                                </div>
                                <div class="w-full h-[.0521vw] bg-[#D9D9D914] rounded-full my-[.625vw]"></div>
                                <div class="w-full flex flex-col items-center gap-y-[.4167vw]">
                                    <div class="flex items-center w-full justify-between">
                                        <div class="flex items-center gap-x-[.3125vw]">
                                            <img src="./assets/svg/stars.svg" alt="stars" class="w-[.9375vw] h-[.9375vw] object-contain">
                                            <div class="flex items-center gap-x-[.2604vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['scenario'] }}</p>
                                                <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['mode'] }}</p>
                                            </div>
                                        </div>
                                        <div
                                            class="w-[2.1875vw] h-[.9896vw] rounded-[.5729vw] relative cursor-pointer transition-colors duration-300"
                                            :class="settings.animations.scenario.status  ? 'bg-[#FFF]' : 'bg-[#FFFFFF29]'"
                                            @click="changeSetting('animations', 'scenario', 'status');"
                                        >
                                            <div
                                            class="w-[.7813vw] h-[.7813vw] rounded-full absolute transition-all duration-300"
                                            :class="settings.animations.scenario.status 
                                                ? 'right-[.1042vw] top-[.1042vw] bg-[#000]' 
                                                : 'left-[.1042vw] top-[.1042vw] bg-[#FFF]'"
                                            ></div>
                                        </div>
                                    </div>
                                    <div class="flex items-center w-full justify-between">
                                        <div class="flex items-center gap-x-[.3125vw]">
                                            <img src="./assets/svg/type.svg" alt="type" class="w-[.9375vw] h-[.9375vw] object-contain">
                                            <div class="flex items-center gap-x-[.2604vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['scenario'] }}</p>
                                                <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['type'] }}</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-x-[1.25vw]">
                                            <img src="./assets/svg/left-arrow.svg" alt="left-arrow" class="w-[.374vw] h-[.6875vw] object-contain" @click="changeScenario('prev');">
                                            <img src="./assets/svg/right-arrow.svg" alt="right-arrow" class="w-[.374vw] h-[.6875vw] object-contain" @click="changeScenario('next');">
                                        </div>
                                    </div>
                                </div>
                                <div class="w-full h-[.0521vw] bg-[#D9D9D914] rounded-full my-[.625vw]"></div>
                                <div class="w-full flex flex-col items-center gap-y-[.4167vw]">
                                    <div class="flex items-center w-full justify-between">
                                        <div class="flex items-center gap-x-[.3125vw]">
                                            <img src="./assets/svg/stars.svg" alt="stars" class="w-[.9375vw] h-[.9375vw] object-contain">
                                            <div class="flex items-center gap-x-[.2604vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['weather'] }}</p>
                                                <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['mode'] }}</p>
                                            </div>
                                        </div>
                                        <div
                                            class="w-[2.1875vw] h-[.9896vw] rounded-[.5729vw] relative cursor-pointer transition-colors duration-300"
                                            :class="settings.weather.status ? 'bg-[#FFF]' : 'bg-[#FFFFFF29]'"
                                            @click="changeSetting('weather', false, 'status');"
                                        >
                                            <div
                                            class="w-[.7813vw] h-[.7813vw] rounded-full absolute transition-all duration-300"
                                            :class="settings.weather.status 
                                                ? 'right-[.1042vw] top-[.1042vw] bg-[#000]' 
                                                : 'left-[.1042vw] top-[.1042vw] bg-[#FFF]'"
                                            ></div>
                                        </div>
                                    </div>
                                    <div class="flex items-center w-full justify-between">
                                        <div class="flex items-center gap-x-[.3125vw]">
                                            <img src="./assets/svg/type.svg" alt="type" class="w-[.9375vw] h-[.9375vw] object-contain">
                                            <div class="flex items-center gap-x-[.2604vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['weather'] }}</p>
                                                <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['type'] }}</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-x-[1.25vw]">
                                            <img src="./assets/svg/left-arrow.svg" alt="left-arrow" class="w-[.374vw] h-[.6875vw] object-contain" @click="changeWeather('prev');">
                                            <img src="./assets/svg/right-arrow.svg" alt="right-arrow" class="w-[.374vw] h-[.6875vw] object-contain" @click="changeWeather('next');">
                                        </div>
                                    </div>
                                </div>
                                <div class="w-full h-[.0521vw] bg-[#D9D9D914] rounded-full my-[.625vw]"></div>
                                <div class="w-full flex flex-col items-center gap-y-[.4167vw]">
                                    <div class="flex items-center w-full justify-between">
                                        <div class="flex items-center gap-x-[.3125vw]">
                                            <img src="./assets/svg/stars.svg" alt="stars" class="w-[.9375vw] h-[.9375vw] object-contain">
                                            <div class="flex items-center gap-x-[.2604vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['time'] }}</p>
                                                <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['mode'] }}</p>
                                            </div>
                                        </div>
                                        <div
                                            class="w-[2.1875vw] h-[.9896vw] rounded-[.5729vw] relative cursor-pointer transition-colors duration-300"
                                            :class="settings.time.status ? 'bg-[#FFF]' : 'bg-[#FFFFFF29]'"
                                            @click="changeSetting('time', false, 'status');"
                                        >
                                            <div
                                            class="w-[.7813vw] h-[.7813vw] rounded-full absolute transition-all duration-300"
                                            :class="settings.time.status 
                                                ? 'right-[.1042vw] top-[.1042vw] bg-[#000]' 
                                                : 'left-[.1042vw] top-[.1042vw] bg-[#FFF]'"
                                            ></div>
                                        </div>
                                    </div>
                                    <div class="flex items-center w-full justify-between">
                                        <div class="flex items-center gap-x-[.3125vw]">
                                            <img src="./assets/svg/type.svg" alt="type" class="w-[.9375vw] h-[.9375vw] object-contain">
                                            <div class="flex items-center gap-x-[.2604vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['time'] }}</p>
                                                <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['type'] }}</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-x-[1.25vw]">
                                            <img src="./assets/svg/left-arrow.svg" alt="left-arrow" class="w-[.374vw] h-[.6875vw] object-contain" @click="changeTime('prev');">
                                            <img src="./assets/svg/right-arrow.svg" alt="right-arrow" class="w-[.374vw] h-[.6875vw] object-contain" @click="changeTime('next');">
                                        </div>
                                    </div>
                                </div>
                                <div class="w-full h-[.0521vw] bg-[#D9D9D914] rounded-full my-[.625vw]"></div>
                                <div class="flex items-center w-full justify-between">
                                    <div class="flex items-center gap-x-[.3125vw]">
                                        <img src="./assets/svg/type.svg" alt="type" class="w-[.9375vw] h-[.9375vw] object-contain">
                                        <div class="flex items-center gap-x-[.2604vw]">
                                            <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['background'] }}</p>
                                            <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['type'] }}</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-x-[1.25vw]">
                                        <img src="./assets/svg/left-arrow.svg" alt="left-arrow" class="w-[.374vw] h-[.6875vw] object-contain" @click="changeBackground('prev');">
                                        <img src="./assets/svg/right-arrow.svg" alt="right-arrow" class="w-[.374vw] h-[.6875vw] object-contain" @click="changeBackground('next');">
                                    </div>
                                </div>
                                <div class="w-full h-[.0521vw] bg-[#D9D9D914] rounded-full my-[.625vw]"></div>
                                <div class="w-full flex flex-col items-center gap-y-[.4167vw]">
                                    <div class="flex items-center w-full justify-between">
                                        <div class="flex items-center gap-x-[.3125vw]">
                                            <img src="./assets/svg/camera.svg" alt="camera" class="w-[.9375vw] h-[.9375vw] object-contain">
                                            <div class="flex items-center gap-x-[.2604vw]">
                                                <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['clean'] }}</p>
                                                <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['mode'] }}</p>
                                            </div>
                                        </div>
                                        <div
                                            class="w-[2.1875vw] h-[.9896vw] rounded-[.5729vw] relative cursor-pointer transition-colors duration-300"
                                            :class="settings.clear ? 'bg-[#FFF]' : 'bg-[#FFFFFF29]'"
                                            @click="changeClearMode();"
                                        >
                                            <div
                                            class="w-[.7813vw] h-[.7813vw] rounded-full absolute transition-all duration-300"
                                            :class="settings.clear
                                                ? 'right-[.1042vw] top-[.1042vw] bg-[#000]' 
                                                : 'left-[.1042vw] top-[.1042vw] bg-[#FFF]'"
                                            ></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="w-full h-[.0521vw] bg-[#D9D9D914] rounded-full my-[.625vw]"></div>
                                <div class="flex items-center w-full justify-between">
                                    <div class="flex items-center gap-x-[.3125vw]">
                                        <img src="./assets/svg/colors.svg" alt="colors" class="w-[.9375vw] h-[.9375vw] object-contain">
                                        <div class="flex items-center gap-x-[.2604vw]">
                                            <p class="font-['Satoshi-Bold'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['color'] }}</p>
                                            <p class="font-['Satoshi-Regular'] text-[.625vw] leading-tight text-[#FFFFFF]">{{ locales['mode'] }}</p>
                                        </div>
                                    </div>
                                    <div
                                        class="w-[2.1875vw] h-[.9896vw] rounded-[.5729vw] flex items-center justify-center relative cursor-pointer transition-colors duration-300 border-[.0521vw] border-solid border-[#FFFFFF]"
                                    >
                                        <div
                                            class="w-[1.9792vw] h-[.7813vw] rounded-full absolute transition-all duration-300"
                                            :class="`bg-[${settings.color.hex}]`"
                                            @click="changeTheme();"
                                        ></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-col items-end gap-y-[.625vw]">
                            <div class="flex items-start gap-x-[.2604vw]">
                                <div class="flex items-start gap-x-[.2604vw]">
                                    <div v-for="(number, index) in characterSlot" class="flex items-center gap-x-[.3125vw]">
                                        <div v-if="characters[index]" class="w-[2.1875vw] h-[2.1875vw] min-h-[42px] min-w-[42px] bg-[#FFFFFF]/10 flex items-center justify-center relative" @click="selectCharacter(index);changePage('character');">
                                            <p class="text-[#FFF] font-['Satoshi-Medium'] text-[.8333vw] leading-none">{{ index+1 }}</p>
                                        </div>
                                        <div v-else class="w-[2.1875vw] h-[2.1875vw] min-h-[42px] min-w-[42px] bg-[#FFFFFF]/10 flex items-center justify-center" @click="changePage('new-character');">
                                            <img src="./assets/svg/plus.svg" alt="plus" class="w-[.7292vw] h-[.7292vw]">
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center justify-center bg-[#FFFFFF]/50 border-[.0521vw] border-solid border-[#FFFFFF]/35 w-[2.1875vw] h-[2.1875vw] min-h-[42px] min-w-[42px]" @click="toggleKeyInput(true);">
                                    <img src="./assets/svg/key.svg" alt="key" class="w-[1.0417vw] h-[1.0417vw] object-contain">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="center-side" class="w-max h-max absolute -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2 flex items-center justify-center z-10">
                        <div v-if="popups.key" id="key-popup" class="w-[20.3125vw] h-max p-[.4167vw] rounded-[.3125vw] animation-fade-in" style="background: linear-gradient(180deg, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.5) 100%);">
                            <div class="flex items-center w-full justify-between">
                                <div class="flex items-center gap-x-[.3125vw] mb-[.3125vw]">
                                    <img src="./assets/svg/key.svg" alt="key" class="w-[1.25vw] h-[1.25vw] object-contain">
                                    <p class="text-[.8854vw] text-[#FFF] font-['Satoshi-Bold'] leading-tight">{{ locales['slotKey'] }}</p>
                                </div>
                                <div class="w-[1.25vw] h-[1.25vw] bg-red-500/20 rounded-[.2083vw] transition-all duration-300 hover:bg-red-500/70 cursor-pointer flex items-center justify-center" @click="toggleKeyInput(false);">
                                    <img src="./assets/svg/x.svg" alt="x" class="w-[.7292vw] h-[.7292vw] object-contain">
                                </div>
                            </div>
                            <p class="w-full text-[#FFF]/70 font-['Satoshi-Regular'] leading-tight text-[.7292vw]">{{ locales['slotKeyDesc'] }}</p>
                            <input id="slot-key" type="text" class="w-full h-[2.1875vw] bg-[#FFF]/10 rounded-[.3125vw] p-[.3125vw] text-[.7813vw] font-['Satoshi-Regular'] text-[#FFF] leading-none mt-[.625vw] border-none outline-none" placeholder="tbx-......." v-model="slotKey">
                            <div class="w-full h-[2.1875vw] bg-[#FFF]/20 flex items-center justify-center mt-[.2083vw] rounded-[.2083vw] transition-all duration-300 hover:bg-[#FFF]/70 cursor-pointer" @click="createSlotKey();">
                                <p class="text-[#FFF] font-['Satoshi-Medium'] text-[.7813vw] leading-tight">{{ locales['checkCode'] }}</p>
                            </div>
                        </div>
                        <div v-if="popups.delete" id="delete-popup" class="w-[20.3125vw] h-max p-[.4167vw] rounded-[.3125vw]" style="background: linear-gradient(180deg, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.5) 100%);">
                            <div class="flex items-center gap-x-[.3125vw] mb-[.3125vw]">
                                <img src="./assets/svg/trash.svg" alt="trash" class="w-[1.1458vw] h-[1.1458vw] object-contain">
                                <p class="text-[.8854vw] text-[#FFF] font-['Satoshi-Bold'] leading-tight">{{ locales['deleteCharacter'] }}</p>
                            </div>
                            <p class="w-full text-[#FFF]/70 font-['Satoshi-Regular'] leading-tight text-[.7292vw]">{{ locales['deleteCharacterDesc'] }}</p>
                            <div class="flex items-center gap-x-[.4167vw] mt-[.3125vw]">
                                <div class="w-full h-[2.1875vw] bg-[#FFF]/20 flex items-center justify-center mt-[.2083vw] rounded-[.2083vw] transition-all duration-300 hover:bg-[#FFF]/70 cursor-pointer" @click="deleteCharacter(true);">
                                    <p class="text-[#FFF] font-['Satoshi-Medium'] text-[.7813vw] leading-tight">{{ locales['delete'] }}</p>
                                </div>
                                <div class="w-full h-[2.1875vw] bg-[#FFF]/20 flex items-center justify-center mt-[.2083vw] rounded-[.2083vw] transition-all duration-300 hover:bg-[#FFF]/70 cursor-pointer" @click="deleteCharacter(false);">
                                    <p class="text-[#FFF] font-['Satoshi-Medium'] text-[.7813vw] leading-tight">{{ locales['cancel'] }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="!isLoading && pages.spawnSelector" class="w-full h-full flex items-start justify-between p-[4.4583vw]">
                    <div class="w-1/2 h-full flex flex-col items-start gap-y-[.4167vw]">
                        <div class="flex items-center gap-x-[.5208vw]">
                            <img src="./assets/svg/spawn-selector.svg" alt="spawn-selec" class="w-[1.3542vw] h-[1.3542vw] object-contain">
                            <p class="text-[1.25vw] font-['Satoshi-Bold'] text-[#FFF] leading-tight">{{ locales['spawnSelector'] }}</p>
                        </div>
                        <p class="text-[.7292vw] font-['Satoshi-Regular'] text-[#FFF]/70 leading-tight max-w-[13.25vw]">{{ locales['spawnSelectorDesc'] }}</p>
                    </div>
                    <div class="w-1/2 h-full flex items-start justify-end">
                        <div class="flex flex-col items-start gap-y-[.4167vw]">
                            <div class="flex items-center gap-x-[.5208vw] cursor-pointer" :class="selectedLocation == 'last-location' ? 'opacity-100' : 'opacity-50'" @click="selectLocation('last-location')">
                                <i class="fa-solid fa-location-dot text-[#FFF] text-[1.1458vw]"></i>
                                <div class="flex flex-col items-start">
                                    <p class="text-[.8333vw] font-['Satoshi-Bold'] text-[#FFF] leading-tight">{{ locales['last'] }}</p>
                                    <p class="text-[.6771vw] font-['Satoshi-Regular'] text-[#FFF]/70 leading-tight max-w-[12.0833vw]">{{ locales['location'] }}</p>
                                </div>
                            </div>
                            <div v-for="(location, index) in spawnLocations" :key="location.id" class="flex items-center gap-x-[.5208vw] cursor-pointer" :class="selectedLocation && selectedLocation.id == location.id ? 'opacity-100' : 'opacity-50'" @click="selectLocation(location)">
                                <i :class="location.icon" class="text-[#FFF] text-[1.1458vw]"></i>
                                <div class="flex flex-col items-start">
                                    <p class="text-[.8333vw] font-['Satoshi-Bold'] text-[#FFF] leading-tight">{{ location.name }}</p>
                                    <p class="text-[.6771vw] font-['Satoshi-Regular'] text-[#FFF]/70 leading-tight max-w-[12.0833vw]">{{ location.subname }}</p>
                                </div>
                            </div>

                            <div class="w-[13.462vw] h-[2.3438vw] bg-[#FFF]/15 flex items-center justify-center cursor-pointer mt-[.8333vw]" @click="spawnLocation();">
                                <p class="text-[.9375vw] text-[#FFF] font-['Satoshi-Bold'] leading-none">{{ locales['spawn'] }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </v-app>

        <script type="text/javascript" src="./assets/api/profanity.js"></script>
        <script src="./index.js" type="module"></script>
    </body>
</html>
