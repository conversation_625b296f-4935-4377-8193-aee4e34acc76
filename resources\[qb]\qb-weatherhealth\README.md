# QBCore Weather Health System (DWANPHS)

**Dynamic Weather-Aware NPC & Player Health System** je komplexní systém pro QBCore, který přidává realistické zdravotní mechaniky založené na počasí a oblečení.

## 🌟 Hlavní funkce

### Pro hráče:
- **Dynamický zdravotní systém** - zdraví se mění podle počasí a oblečení
- **Systém nemocí** - nachlazen<PERSON>, chřipka, úpal, podchlazení
- **Léčebný systém** - léky, ohřívací/chlad<PERSON><PERSON><PERSON> s<PERSON>
- **Vizuální efekty** - roz<PERSON><PERSON><PERSON> vid<PERSON>, animace symptomů
- **Varování** - upozornění na nevhodné oblečení

### Pro NPC:
- **Inteligentní oblékání** - NPC se oblékají podle počasí
- **Behaviorální změny** - hledání úkrytu v <PERSON><PERSON><PERSON>, tepla v zimě
- **Zdravotní systém** - NPC mohou onemocnět a zemřít
- **Realistické animace** - kašlání, třesení, pocení

## 📋 Požadavky

- **QBCore Framework**
- **qb-weathersync** (pro synchronizaci počasí)
- **oxmysql** (pro databázi)
- **MySQL/MariaDB** databáze

## 🚀 Instalace

1. **Stáhněte a umístěte** resource do složky `resources/[qb]/`

2. **Importujte databázi**:
   ```sql
   -- Spusťte soubor sql/install.sql ve vaší databázi
   ```

3. **Items jsou již přidány** do `qb-core/shared/items.lua`
   - Pokud máte upravený items.lua, podívejte se do `install.md` pro ruční přidání

4. **Přidejte do server.cfg**:
   ```
   ensure qb-weatherhealth
   ```

5. **Restartujte server**:
   ```
   restart qb-core
   ensure qb-weatherhealth
   ```

## ⚙️ Konfigurace

Hlavní konfigurace se nachází v `config.lua`:

### Teplotní indexy počasí
```lua
Config.WeatherTemperature = {
    ['EXTRASUNNY'] = 30,    -- Velmi horko
    ['CLEAR'] = 25,         -- Teplo
    ['RAIN'] = 8,           -- Déšť
    ['SNOW'] = -5,          -- Sníh
    -- ...
}
```

### Hodnoty tepla oblečení
```lua
Config.ClothingWarmth = {
    torso = {
        [0] = 0,   -- Nahé tělo
        [1] = 2,   -- Tričko
        [4] = 8,   -- Bunda
        [7] = 15,  -- Zimní kabát
    },
    -- ...
}
```

### Nemoci a jejich efekty
```lua
Config.HealthEffects.diseases = {
    cold = {
        name = "Nachlazení",
        symptoms = {"cough", "sneeze"},
        healthLoss = 2,
        duration = 600000, -- 10 minut
        effects = {
            walkSpeed = 0.8,    -- 80% rychlosti chůze
            runSpeed = 0.7,     -- 70% rychlosti běhu
        }
    },
    -- ...
}
```

## 🎮 Použití

### Příkazy pro hráče:
- `/weatherhealth status` - zobrazí aktuální stav
- `/weatherhealth debug` - zapne/vypne debug režim
- `/healthstatus` - zobrazí zdravotní stav

### Léčebné předměty:
- `medicine` - léčí nachlazení a chřipku
- `heatpack` - poskytuje teplo na 5 minut
- `coolpack` - poskytuje chlazení na 5 minut

### Automatické funkce:
- Systém automaticky sleduje počasí a oblečení
- NPC se automaticky přizpůsobují počasí
- Zdraví se mění podle komfortu hráče

## 🔧 API & Exporty

### Client exporty:
```lua
-- Získání aktuálního počasí
local weather = exports['qb-weatherhealth']:getCurrentWeather()

-- Kontrola, zda je hráč nemocný
local isSick = exports['qb-weatherhealth']:isPlayerSick()

-- Získání komfortu hráče
local comfort = exports['qb-weatherhealth']:getPlayerComfort()
```

### Server exporty:
```lua
-- Získání zdravotních dat hráče
local healthData = exports['qb-weatherhealth']:getPlayerHealthData(citizenid)

-- Získání statistik počasí
local stats = exports['qb-weatherhealth']:getWeatherStats()
```

## 📊 Databázové tabulky

- `weather_player_health` - zdravotní data hráčů
- `weather_npc_data` - data o NPC
- `weather_logs` - logy počasí a událostí
- `weather_treatments` - historie léčby
- `weather_clothing_stats` - statistiky oblečení

## 🎯 Herní mechaniky

### Teplotní komfort:
- **Perfect** - ideální teplota, žádné změny
- **Comfortable** - pohodlná teplota
- **Uncomfortable** - nepohodlná, -1 zdraví/min
- **Dangerous** - nebezpečná, -3 zdraví/min
- **Critical** - kritická, -5 zdraví/min

### Chování NPC:
- **Déšť** - hledají úkryt
- **Sníh** - hledají teplo
- **Noc** - snížená aktivita
- **Nemoc** - animace symptomů

### Systém imunity:
- Začíná na 50%
- Zvyšuje se po vyléčení nemocí
- Ovlivňuje šanci na onemocnění

## 🐛 Řešení problémů

### Časté problémy:

1. **Systém nefunguje**
   - Zkontrolujte, zda je `qb-weathersync` spuštěn
   - Ověřte databázové připojení

2. **NPC se neoblékají**
   - Zkontrolujte vzdálenost v `Config.MaxNPCDistance`
   - Ověřte, zda jsou NPC v dosahu

3. **Animace nefungují**
   - Zkontrolujte, zda nejsou blokovány jinými skripty
   - Ověřte animační slovníky

## 📝 Changelog

### v1.0.0
- Počáteční vydání
- Základní systém počasí a zdraví
- NPC management
- Systém nemocí a léčby
- Databázová integrace

## 👥 Podpora

Pro podporu a hlášení chyb:
- GitHub Issues
- QBCore Discord
- FiveM Forums

## 📄 Licence

Tento projekt je licencován pod MIT licencí.

## 🙏 Poděkování

- QBCore týmu za framework
- FiveM komunitě za podporu
- Všem testerům a přispěvatelům

---

**Vytvořeno s ❤️ pro QBCore komunitu**
