local L0_1, L1_1, L2_1
L0_1 = {}
Trackers = L0_1
L0_1 = Citizen
L0_1 = L0_1.CreateThread
function L1_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L0_2 = MySQL
  L0_2 = L0_2.awaitQuery
  L1_2 = "SELECT * FROM origen_police_ankle"
  L0_2 = L0_2(L1_2)
  L1_2 = Wait
  L2_2 = 2000
  L1_2(L2_2)
  if not L0_2 then
    return
  end
  L1_2 = pairs
  L2_2 = L0_2
  L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2)
  for L5_2, L6_2 in L1_2, L2_2, L3_2, L4_2 do
    L7_2 = FW_GetPlayerFromCitizenid
    L8_2 = L6_2.citizenid
    L7_2 = L7_2(L8_2)
    if L7_2 then
      L8_2 = L7_2.PlayerData
      if L8_2 then
        L8_2 = L7_2.PlayerData
        L8_2 = L8_2.source
        if L8_2 then
          L8_2 = Trackers
          L9_2 = L7_2.PlayerData
          L9_2 = L9_2.source
          L8_2[L9_2] = L6_2
          L8_2 = TriggerClientEvent
          L9_2 = "origen_police:client:SetAnkleCuff"
          L10_2 = L7_2.PlayerData
          L10_2 = L10_2.source
          L11_2 = true
          L8_2(L9_2, L10_2, L11_2)
        end
      end
    end
  end
end
L0_1(L1_1)
function L0_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  L1_2 = pairs
  L2_2 = Trackers
  L1_2, L2_2, L3_2, L4_2 = L1_2(L2_2)
  for L5_2, L6_2 in L1_2, L2_2, L3_2, L4_2 do
    L7_2 = L6_2.citizenid
    if L7_2 == A0_2 then
      L7_2 = FW_GetPlayer
      L8_2 = L5_2
      L7_2 = L7_2(L8_2)
      L7_2 = L7_2.PlayerData
      L7_2 = L7_2.citizenid
      if L7_2 == A0_2 then
        L7_2 = FW_GetPlayerFromCitizenid
        L8_2 = L6_2.policeOwner
        L7_2 = L7_2(L8_2)
        L8_2 = L7_2.PlayerData
        L8_2 = L8_2.charinfo
        L8_2 = L8_2.firstname
        L9_2 = " "
        L10_2 = L7_2.PlayerData
        L10_2 = L10_2.charinfo
        L10_2 = L10_2.lastname
        L11_2 = " ("
        L12_2 = L7_2.PlayerData
        L12_2 = L12_2.metadata
        L12_2 = L12_2.police_badge
        if not L12_2 then
          L12_2 = "0000"
        end
        L13_2 = ")"
        L8_2 = L8_2 .. L9_2 .. L10_2 .. L11_2 .. L12_2 .. L13_2
        L6_2.policeLabel = L8_2
        L8_2 = {}
        L9_2 = table
        L9_2 = L9_2.unpack
        L10_2 = GetEntityCoords
        L11_2 = GetPlayerPed
        L12_2 = L5_2
        L11_2, L12_2, L13_2 = L11_2(L12_2)
        L10_2, L11_2, L12_2, L13_2 = L10_2(L11_2, L12_2, L13_2)
        L9_2, L10_2, L11_2, L12_2, L13_2 = L9_2(L10_2, L11_2, L12_2, L13_2)
        L8_2[1] = L9_2
        L8_2[2] = L10_2
        L8_2[3] = L11_2
        L8_2[4] = L12_2
        L8_2[5] = L13_2
        L6_2.coords = L8_2
        L6_2.targetId = L5_2
        return L6_2
      end
    end
  end
  L1_2 = nil
  return L1_2
end
GetTrackerFromCitizenid = L0_1
L0_1 = RegisterNetEvent
L1_1 = "origen_police:server:anklecuff"
function L2_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L1_2 = source
  L2_2 = Trackers
  L2_2 = L2_2[A0_2]
  L2_2 = nil ~= L2_2
  if not L2_2 then
    L3_2 = MySQL
    L3_2 = L3_2.awaitInsert
    L4_2 = "INSERT INTO origen_police_ankle (citizenid, policeOwner) VALUES (@citizenid, @police)"
    L5_2 = {}
    L6_2 = FW_GetPlayer
    L7_2 = A0_2
    L6_2 = L6_2(L7_2)
    L6_2 = L6_2.PlayerData
    L6_2 = L6_2.citizenid
    L5_2["@citizenid"] = L6_2
    L6_2 = FW_GetPlayer
    L7_2 = L1_2
    L6_2 = L6_2(L7_2)
    L6_2 = L6_2.PlayerData
    L6_2 = L6_2.citizenid
    L5_2["@police"] = L6_2
    L3_2(L4_2, L5_2)
    L3_2 = Trackers
    L4_2 = {}
    L5_2 = FW_GetPlayer
    L6_2 = A0_2
    L5_2 = L5_2(L6_2)
    L5_2 = L5_2.PlayerData
    L5_2 = L5_2.citizenid
    L4_2.citizenid = L5_2
    L5_2 = FW_GetPlayer
    L6_2 = L1_2
    L5_2 = L5_2(L6_2)
    L5_2 = L5_2.PlayerData
    L5_2 = L5_2.citizenid
    L4_2.policeOwner = L5_2
    L5_2 = os
    L5_2 = L5_2.time
    L5_2 = L5_2()
    L4_2.date = L5_2
    L3_2[A0_2] = L4_2
  else
    L3_2 = Trackers
    L3_2[A0_2] = nil
    L3_2 = MySQL
    L3_2 = L3_2.awaitQuery
    L4_2 = "DELETE FROM origen_police_ankle WHERE citizenid = @citizenid"
    L5_2 = {}
    L6_2 = FW_GetPlayer
    L7_2 = A0_2
    L6_2 = L6_2(L7_2)
    L6_2 = L6_2.PlayerData
    L6_2 = L6_2.citizenid
    L5_2["@citizenid"] = L6_2
    L3_2(L4_2, L5_2)
  end
  L3_2 = GetPlayerPed
  L4_2 = A0_2
  L3_2 = L3_2(L4_2)
  L4_2 = FreezeEntityPosition
  L5_2 = L3_2
  L6_2 = true
  L4_2(L5_2, L6_2)
  L4_2 = Wait
  L5_2 = 5000
  L4_2(L5_2)
  L4_2 = TriggerClientEvent
  L5_2 = "origen_police:client:SetAnkleCuff"
  L6_2 = A0_2
  L7_2 = not L2_2
  L4_2(L5_2, L6_2, L7_2)
  L4_2 = FreezeEntityPosition
  L5_2 = L3_2
  L6_2 = false
  L4_2(L5_2, L6_2)
end
L0_1(L1_1, L2_1)
L0_1 = RegisterNetEvent
L1_1 = "origen_police:server:ankleshock"
function L2_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2
  L1_2 = Trackers
  L2_2 = tonumber
  L3_2 = A0_2
  L2_2 = L2_2(L3_2)
  L1_2 = L1_2[L2_2]
  if not L1_2 then
    L1_2 = print
    L2_2 = "No tracker on this player"
    return L1_2(L2_2)
  end
  L1_2 = MySQL
  L1_2 = L1_2.awaitInsert
  L2_2 = "UPDATE origen_police_ankle SET lastShock = CURRENT_TIMESTAMP WHERE citizenid = ?"
  L3_2 = {}
  L4_2 = FW_GetPlayer
  L5_2 = A0_2
  L4_2 = L4_2(L5_2)
  L4_2 = L4_2.PlayerData
  L4_2 = L4_2.citizenid
  L3_2[1] = L4_2
  L1_2(L2_2, L3_2)
  L1_2 = Trackers
  L2_2 = tonumber
  L3_2 = A0_2
  L2_2 = L2_2(L3_2)
  L1_2 = L1_2[L2_2]
  L2_2 = os
  L2_2 = L2_2.time
  L2_2 = L2_2()
  L2_2 = L2_2 * 1000
  L1_2.lastShock = L2_2
  L1_2 = TriggerClientEvent
  L2_2 = "origen_police:client:ankleshock"
  L3_2 = A0_2
  L1_2(L2_2, L3_2)
end
L0_1(L1_1, L2_1)
L0_1 = RegisterNetEvent
L1_1 = "origen_police:server:GetAnkleCuff"
function L2_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2
  L0_2 = source
  L1_2 = FW_GetPlayer
  L2_2 = L0_2
  L1_2 = L1_2(L2_2)
  L2_2 = MySQL
  L2_2 = L2_2.awaitQuery
  L3_2 = "SELECT * FROM origen_police_ankle WHERE citizenid = ? LIMIT 1"
  L4_2 = {}
  L5_2 = L1_2.PlayerData
  L5_2 = L5_2.citizenid
  L4_2[1] = L5_2
  L2_2 = L2_2(L3_2, L4_2)
  if not L2_2 then
    return
  end
  L3_2 = L2_2[1]
  if not L3_2 then
    return
  end
  L2_2 = L2_2[1]
  L3_2 = Trackers
  L3_2[L0_2] = L2_2
  L3_2 = TriggerClientEvent
  L4_2 = "origen_police:client:SetAnkleCuff"
  L5_2 = L0_2
  L6_2 = true
  L3_2(L4_2, L5_2, L6_2)
end
L0_1(L1_1, L2_1)
