{"manifest": {"name": "function-bind", "version": "1.1.1", "description": "Implementation of Function.prototype.bind", "keywords": ["function", "bind", "shim", "es5"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/Raynos/function-bind.git"}, "main": "index", "homepage": "https://github.com/Raynos/function-bind", "contributors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb"}], "bugs": {"url": "https://github.com/Raynos/function-bind/issues", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^12.2.1", "covert": "^1.1.0", "eslint": "^4.5.0", "jscs": "^3.0.7", "tape": "^4.8.0"}, "license": "MIT", "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "posttest": "npm run coverage -- --quiet", "tests-only": "node test", "coverage": "covert test/*.js", "lint": "npm run jscs && npm run eslint", "jscs": "jscs *.js */*.js", "eslint": "eslint *.js */*.js"}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-function-bind-1.1.1-a56899d3ea3c9bab874bb9773b7c5ede92f4895d-integrity\\node_modules\\function-bind\\package.json", "readmeFilename": "README.md", "readme": "# function-bind\n\n<!--\n    [![build status][travis-svg]][travis-url]\n    [![NPM version][npm-badge-svg]][npm-url]\n    [![Coverage Status][5]][6]\n    [![gemnasium Dependency Status][7]][8]\n    [![Dependency status][deps-svg]][deps-url]\n    [![Dev Dependency status][dev-deps-svg]][dev-deps-url]\n-->\n\n<!-- [![browser support][11]][12] -->\n\nImplementation of function.prototype.bind\n\n## Example\n\nI mainly do this for unit tests I run on phantomjs.\nPhantomJS does not have Function.prototype.bind :(\n\n```js\nFunction.prototype.bind = require(\"function-bind\")\n```\n\n## Installation\n\n`npm install function-bind`\n\n## Contributors\n\n - Raynos\n\n## MIT Licenced\n\n  [travis-svg]: https://travis-ci.org/Raynos/function-bind.svg\n  [travis-url]: https://travis-ci.org/Raynos/function-bind\n  [npm-badge-svg]: https://badge.fury.io/js/function-bind.svg\n  [npm-url]: https://npmjs.org/package/function-bind\n  [5]: https://coveralls.io/repos/Raynos/function-bind/badge.png\n  [6]: https://coveralls.io/r/Raynos/function-bind\n  [7]: https://gemnasium.com/Raynos/function-bind.png\n  [8]: https://gemnasium.com/Raynos/function-bind\n  [deps-svg]: https://david-dm.org/Raynos/function-bind.svg\n  [deps-url]: https://david-dm.org/Raynos/function-bind\n  [dev-deps-svg]: https://david-dm.org/Raynos/function-bind/dev-status.svg\n  [dev-deps-url]: https://david-dm.org/Raynos/function-bind#info=devDependencies\n  [11]: https://ci.testling.com/Raynos/function-bind.png\n  [12]: https://ci.testling.com/Raynos/function-bind\n", "licenseText": "Copyright (c) 2013 Raynos.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/function-bind/-/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d", "type": "tarball", "reference": "https://registry.yarnpkg.com/function-bind/-/function-bind-1.1.1.tgz", "hash": "a56899d3ea3c9bab874bb9773b7c5ede92f4895d", "integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==", "registry": "npm", "packageName": "function-bind", "cacheIntegrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A== sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0="}, "registry": "npm", "hash": "a56899d3ea3c9bab874bb9773b7c5ede92f4895d"}