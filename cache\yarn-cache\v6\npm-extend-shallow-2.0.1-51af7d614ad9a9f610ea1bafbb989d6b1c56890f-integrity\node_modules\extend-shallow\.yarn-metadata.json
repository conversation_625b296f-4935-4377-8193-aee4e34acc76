{"manifest": {"name": "extend-shallow", "description": "Extend an object with the properties of additional objects. node.js/javascript util.", "version": "2.0.1", "homepage": "https://github.com/jonschlinkert/extend-shallow", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/extend-shallow.git"}, "bugs": {"url": "https://github.com/jonschlinkert/extend-shallow/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-extendable": "^0.1.0"}, "devDependencies": {"array-slice": "^0.2.3", "benchmarked": "^0.1.4", "chalk": "^1.0.0", "for-own": "^0.1.3", "glob": "^5.0.12", "is-plain-object": "^2.0.1", "kind-of": "^2.0.0", "minimist": "^1.1.1", "mocha": "^2.2.5", "should": "^7.0.1"}, "keywords": ["assign", "extend", "javascript", "js", "keys", "merge", "obj", "object", "prop", "properties", "property", "props", "shallow", "util", "utility", "utils", "value"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-extend-shallow-2.0.1-51af7d614ad9a9f610ea1bafbb989d6b1c56890f-integrity\\node_modules\\extend-shallow\\package.json", "readmeFilename": "README.md", "readme": "# extend-shallow [![NPM version](https://badge.fury.io/js/extend-shallow.svg)](http://badge.fury.io/js/extend-shallow)  [![Build Status](https://travis-ci.org/jonschlinkert/extend-shallow.svg)](https://travis-ci.org/jonschlinkert/extend-shallow)\n\n> Extend an object with the properties of additional objects. node.js/javascript util.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/)\n\n```sh\n$ npm i extend-shallow --save\n```\n\n## Usage\n\n```js\nvar extend = require('extend-shallow');\n\nextend({a: 'b'}, {c: 'd'})\n//=> {a: 'b', c: 'd'}\n```\n\nPass an empty object to shallow clone:\n\n```js\nvar obj = {};\nextend(obj, {a: 'b'}, {c: 'd'})\n//=> {a: 'b', c: 'd'}\n```\n\n## Related\n\n* [extend-shallow](https://github.com/jonschlinkert/extend-shallow): Extend an object with the properties of additional objects. node.js/javascript util.\n* [for-own](https://github.com/jonschlinkert/for-own): Iterate over the own enumerable properties of an object, and return an object with properties… [more](https://github.com/jonschlinkert/for-own)\n* [for-in](https://github.com/jonschlinkert/for-in): Iterate over the own and inherited enumerable properties of an objecte, and return an object… [more](https://github.com/jonschlinkert/for-in)\n* [is-plain-object](https://github.com/jonschlinkert/is-plain-object): Returns true if an object was created by the `Object` constructor.\n* [isobject](https://github.com/jonschlinkert/isobject): Returns true if the value is an object and not an array or null.\n* [kind-of](https://github.com/jonschlinkert/kind-of): Get the native type of a value.\n\n## Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm i -d && npm test\n```\n\n## Author\n\n**Jon Schlinkert**\n\n+ [github/jonschlinkert](https://github.com/jonschlinkert)\n+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n## License\n\nCopyright © 2015 Jon Schlinkert\nReleased under the MIT license.\n\n***\n\n_This file was generated by [verb-cli](https://github.com/assemble/verb-cli) on June 29, 2015._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2015, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/extend-shallow/-/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f", "type": "tarball", "reference": "https://registry.yarnpkg.com/extend-shallow/-/extend-shallow-2.0.1.tgz", "hash": "51af7d614ad9a9f610ea1bafbb989d6b1c56890f", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "registry": "npm", "packageName": "extend-shallow", "cacheIntegrity": "sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug== sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8="}, "registry": "npm", "hash": "51af7d614ad9a9f610ea1bafbb989d6b1c56890f"}