[{"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-target/@PolyZone/BoxZone.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-target/@PolyZone/CircleZone.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-target/@PolyZone/ComboZone.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-target/@PolyZone/EntityZone.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-target/@PolyZone/client.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-target/client.lua", "mt": 1749053849, "s": 41940, "i": "MDICAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-target/data/bones.lua", "mt": 1749053849, "s": 4416, "i": "MTICAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-target/data/debug.lua", "mt": 1749053849, "s": 1819, "i": "MjICAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-target/fxmanifest.lua", "mt": 1749053849, "s": 511, "i": "MzICAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-target/html/css/style.css", "mt": 1749053849, "s": 2227, "i": "NDICAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-target/html/index.html", "mt": 1749053849, "s": 507, "i": "NTICAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-target/html/js/app.js", "mt": 1749053849, "s": 4617, "i": "NjICAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-target/init.lua", "mt": 1749053849, "s": 6624, "i": "NzICAAAABwAAAAAAAAAAAA=="}]