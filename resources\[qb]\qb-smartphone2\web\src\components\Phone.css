.phone-wrapper {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

.phone-container {
  width: 375px;
  height: 812px;
  background: linear-gradient(145deg, #2c2c2c, #1a1a1a);
  border-radius: 40px;
  padding: 8px;
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.phone-container.light {
  background: linear-gradient(145deg, #f0f0f0, #e0e0e0);
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.phone-frame {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 32px;
  overflow: hidden;
  background: #000;
}

.phone-container.light .phone-frame {
  background: #fff;
}

.phone-notch {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 150px;
  height: 30px;
  background: #000;
  border-radius: 0 0 15px 15px;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.phone-container.light .phone-notch {
  background: #fff;
}

.notch-speaker {
  width: 50px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.phone-container.light .notch-speaker {
  background: rgba(0, 0, 0, 0.3);
}

.notch-camera {
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.phone-container.light .notch-camera {
  background: rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 0, 0, 0.1);
}

.phone-screen {
  width: 100%;
  height: 100%;
  border-radius: 32px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  background: #000;
}

.phone-container.light .phone-screen {
  background: #fff;
}

.home-indicator {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 134px;
  height: 5px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2.5px;
  z-index: 100;
}

.phone-container.light .home-indicator {
  background: rgba(0, 0, 0, 0.3);
}

.power-button {
  position: absolute;
  right: -3px;
  top: 180px;
  width: 6px;
  height: 80px;
  background: linear-gradient(90deg, #333, #555);
  border-radius: 3px 0 0 3px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.power-button:hover {
  background: linear-gradient(90deg, #444, #666);
}

.power-button:active {
  background: linear-gradient(90deg, #222, #444);
}

.volume-buttons {
  position: absolute;
  left: -3px;
  top: 160px;
}

.volume-up,
.volume-down {
  width: 6px;
  height: 50px;
  background: linear-gradient(270deg, #333, #555);
  border-radius: 0 3px 3px 0;
  margin-bottom: 8px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.volume-up:hover,
.volume-down:hover {
  background: linear-gradient(270deg, #444, #666);
}

.volume-up:active,
.volume-down:active {
  background: linear-gradient(270deg, #222, #444);
}

/* Phone content area */
.phone-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

/* Responsive design for smaller screens */
@media (max-width: 480px) {
  .phone-container {
    width: 320px;
    height: 693px;
    border-radius: 35px;
    padding: 6px;
  }
  
  .phone-frame {
    border-radius: 29px;
  }
  
  .phone-screen {
    border-radius: 29px;
  }
  
  .phone-notch {
    width: 130px;
    height: 26px;
    border-radius: 0 0 13px 13px;
  }
  
  .notch-speaker {
    width: 40px;
    height: 3px;
  }
  
  .notch-camera {
    width: 6px;
    height: 6px;
  }
  
  .home-indicator {
    width: 114px;
    height: 4px;
    bottom: 6px;
  }
}

/* Animation classes */
.phone-shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
  25% { transform: translate(-50%, -50%) rotate(1deg); }
  75% { transform: translate(-50%, -50%) rotate(-1deg); }
}

.phone-glow {
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 0 30px rgba(0, 122, 255, 0.3);
}

/* Battery charging animation */
.phone-charging {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}
