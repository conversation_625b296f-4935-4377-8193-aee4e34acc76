{"manifest": {"name": "minimalistic-assert", "version": "1.0.1", "description": "minimalistic-assert ===", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/calvinmetcalf/minimalistic-assert.git"}, "author": {}, "license": "ISC", "bugs": {"url": "https://github.com/calvinmetcalf/minimalistic-assert/issues"}, "homepage": "https://github.com/calvinmetcalf/minimalistic-assert", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-minimalistic-assert-1.0.1-2e194de044626d4a10e7f7fbc00ce73e83e4d5c7-integrity\\node_modules\\minimalistic-assert\\package.json", "readmeFilename": "readme.md", "readme": "minimalistic-assert\n===\n\nvery minimalistic assert module.\n", "licenseText": "Copyright 2015 Calvin <PERSON>f\n\nPermission to use, copy, modify, and/or distribute this software for any purpose\nwith or without fee is hereby granted, provided that the above copyright notice\nand this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS <PERSON>OFTWARE INCLUDING AL<PERSON> IMPLIED WARRANTIES OF MERCHANTABILITY AND\nF<PERSON>NESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, <PERSON>ATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE\nOR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz#2e194de044626d4a10e7f7fbc00ce73e83e4d5c7", "type": "tarball", "reference": "https://registry.yarnpkg.com/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "hash": "2e194de044626d4a10e7f7fbc00ce73e83e4d5c7", "integrity": "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==", "registry": "npm", "packageName": "minimalistic-assert", "cacheIntegrity": "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A== sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc="}, "registry": "npm", "hash": "2e194de044626d4a10e7f7fbc00ce73e83e4d5c7"}