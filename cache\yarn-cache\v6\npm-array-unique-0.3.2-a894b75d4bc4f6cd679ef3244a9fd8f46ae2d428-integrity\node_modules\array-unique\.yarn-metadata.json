{"manifest": {"name": "array-unique", "description": "Remove duplicate values from an array. Fastest ES5 implementation.", "version": "0.3.2", "homepage": "https://github.com/jonschlinkert/array-unique", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/array-unique.git"}, "bugs": {"url": "https://github.com/jonschlinkert/array-unique/issues"}, "license": "MIT", "files": ["index.js", "LICENSE", "README.md"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"array-uniq": "^1.0.2", "benchmarked": "^0.1.3", "gulp-format-md": "^0.1.9", "mocha": "^2.5.3", "should": "^10.0.0"}, "keywords": ["array", "unique"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["arr-diff", "arr-union", "arr-flatten", "arr-reduce", "arr-map", "arr-pluck"]}, "reflinks": ["verb", "verb-generate-readme"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-array-unique-0.3.2-a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428-integrity\\node_modules\\array-unique\\package.json", "readmeFilename": "README.md", "readme": "# array-unique [![NPM version](https://img.shields.io/npm/v/array-unique.svg?style=flat)](https://www.npmjs.com/package/array-unique) [![NPM downloads](https://img.shields.io/npm/dm/array-unique.svg?style=flat)](https://npmjs.org/package/array-unique) [![Build Status](https://img.shields.io/travis/jonschlinkert/array-unique.svg?style=flat)](https://travis-ci.org/jonschlinkert/array-unique)\n\nRemove duplicate values from an array. Fastest ES5 implementation.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save array-unique\n```\n\n## Usage\n\n```js\nvar unique = require('array-unique');\n\nvar arr = ['a', 'b', 'c', 'c'];\nconsole.log(unique(arr)) //=> ['a', 'b', 'c']\nconsole.log(arr)         //=> ['a', 'b', 'c']\n\n/* The above modifies the input array. To prevent that at a slight performance cost: */\nvar unique = require(\"array-unique\").immutable;\n\nvar arr = ['a', 'b', 'c', 'c'];\nconsole.log(unique(arr)) //=> ['a', 'b', 'c']\nconsole.log(arr)         //=> ['a', 'b', 'c', 'c']\n```\n\n## About\n\n### Related projects\n\n* [arr-diff](https://www.npmjs.com/package/arr-diff): Returns an array with only the unique values from the first array, by excluding all… [more](https://github.com/jonschlinkert/arr-diff) | [homepage](https://github.com/jonschlinkert/arr-diff \"Returns an array with only the unique values from the first array, by excluding all values from additional arrays using strict equality for comparisons.\")\n* [arr-flatten](https://www.npmjs.com/package/arr-flatten): Recursively flatten an array or arrays. This is the fastest implementation of array flatten. | [homepage](https://github.com/jonschlinkert/arr-flatten \"Recursively flatten an array or arrays. This is the fastest implementation of array flatten.\")\n* [arr-map](https://www.npmjs.com/package/arr-map): Faster, node.js focused alternative to JavaScript's native array map. | [homepage](https://github.com/jonschlinkert/arr-map \"Faster, node.js focused alternative to JavaScript's native array map.\")\n* [arr-pluck](https://www.npmjs.com/package/arr-pluck): Retrieves the value of a specified property from all elements in the collection. | [homepage](https://github.com/jonschlinkert/arr-pluck \"Retrieves the value of a specified property from all elements in the collection.\")\n* [arr-reduce](https://www.npmjs.com/package/arr-reduce): Fast array reduce that also loops over sparse elements. | [homepage](https://github.com/jonschlinkert/arr-reduce \"Fast array reduce that also loops over sparse elements.\")\n* [arr-union](https://www.npmjs.com/package/arr-union): Combines a list of arrays, returning a single array with unique values, using strict equality… [more](https://github.com/jonschlinkert/arr-union) | [homepage](https://github.com/jonschlinkert/arr-union \"Combines a list of arrays, returning a single array with unique values, using strict equality for comparisons.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Building docs\n\n_(This document was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme) (a [verb](https://github.com/verbose/verb) generator), please don't edit the readme directly. Any changes to the readme must be made in [.verb.md](.verb.md).)_\n\nTo generate the readme and API documentation with [verb](https://github.com/verbose/verb):\n\n```sh\n$ npm install -g verb verb-generate-readme && verb\n```\n\n### Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm install -d && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT license](https://github.com/jonschlinkert/array-unique/blob/master/LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.1.28, on July 31, 2016._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2016, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/array-unique/-/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428", "type": "tarball", "reference": "https://registry.yarnpkg.com/array-unique/-/array-unique-0.3.2.tgz", "hash": "a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428", "integrity": "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=", "registry": "npm", "packageName": "array-unique", "cacheIntegrity": "sha512-SleRWjh9JUud2wH1hPs9rZBZ33H6T9HOiL0uwGnGx9FpE6wKGyfWugmbkEOIs6qWrZhg0LWeLziLrEwQJhs5mQ== sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="}, "registry": "npm", "hash": "a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"}