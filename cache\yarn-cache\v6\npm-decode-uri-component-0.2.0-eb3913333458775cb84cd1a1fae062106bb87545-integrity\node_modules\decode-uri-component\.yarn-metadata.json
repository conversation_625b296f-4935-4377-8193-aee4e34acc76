{"manifest": {"name": "decode-uri-component", "version": "0.2.0", "description": "A better decodeURIComponent", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/SamVerschueren/decode-uri-component.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/SamVerschueren"}, "engines": {"node": ">=0.10"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js"], "keywords": ["decode", "uri", "component", "decodeuricomponent", "components", "decoder", "url"], "devDependencies": {"ava": "^0.17.0", "coveralls": "^2.13.1", "nyc": "^10.3.2", "xo": "^0.16.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-decode-uri-component-0.2.0-eb3913333458775cb84cd1a1fae062106bb87545-integrity\\node_modules\\decode-uri-component\\package.json", "readmeFilename": "readme.md", "readme": "# decode-uri-component\n\n[![Build Status](https://travis-ci.org/SamVerschueren/decode-uri-component.svg?branch=master)](https://travis-ci.org/SamVerschueren/decode-uri-component) [![Coverage Status](https://coveralls.io/repos/SamVerschueren/decode-uri-component/badge.svg?branch=master&service=github)](https://coveralls.io/github/SamVerschueren/decode-uri-component?branch=master)\n\n> A better [decodeURIComponent](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/decodeURIComponent)\n\n\n## Why?\n\n- Decodes `+` to a space.\n- Converts the [BOM](https://en.wikipedia.org/wiki/Byte_order_mark) to a [replacement character](https://en.wikipedia.org/wiki/Specials_(Unicode_block)#Replacement_character) `�`.\n- Does not throw with invalid encoded input.\n- Decodes as much of the string as possible.\n\n\n## Install\n\n```\n$ npm install --save decode-uri-component\n```\n\n\n## Usage\n\n```js\nconst decodeUriComponent = require('decode-uri-component');\n\ndecodeUriComponent('%25');\n//=> '%'\n\ndecodeUriComponent('%');\n//=> '%'\n\ndecodeUriComponent('st%C3%A5le');\n//=> 'ståle'\n\ndecodeUriComponent('%st%C3%A5le%');\n//=> '%ståle%'\n\ndecodeUriComponent('%%7Bst%C3%A5le%7D%');\n//=> '%{ståle}%'\n\ndecodeUriComponent('%7B%ab%%7C%de%%7D');\n//=> '{%ab%|%de%}'\n\ndecodeUriComponent('%FE%FF');\n//=> '\\uFFFD\\uFFFD'\n\ndecodeUriComponent('%C2');\n//=> '\\uFFFD'\n\ndecodeUriComponent('%C2%B5');\n//=> 'µ'\n```\n\n\n## API\n\n### decodeUriComponent(encodedURI)\n\n#### encodedURI\n\nType: `string`\n\nAn encoded component of a Uniform Resource Identifier.\n\n\n## License\n\nMIT © [Sam Verschueren](https://github.com/SamVerschueren)\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) <PERSON> <<EMAIL>> (github.com/SamVerschueren)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/decode-uri-component/-/decode-uri-component-0.2.0.tgz#eb3913333458775cb84cd1a1fae062106bb87545", "type": "tarball", "reference": "https://registry.yarnpkg.com/decode-uri-component/-/decode-uri-component-0.2.0.tgz", "hash": "eb3913333458775cb84cd1a1fae062106bb87545", "integrity": "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=", "registry": "npm", "packageName": "decode-uri-component", "cacheIntegrity": "sha512-hjf+xovcEn31w/EUYdTXQh/8smFL/dzYjohQGEIgjyNavaJfBY2p5F527Bo1VPATxv0VYTUC2bOcXvqFwk78Og== sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU="}, "registry": "npm", "hash": "eb3913333458775cb84cd1a1fae062106bb87545"}