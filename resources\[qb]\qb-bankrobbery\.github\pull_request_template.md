**Describe Pull request**
First, make sure you've read and are following the contribution guidelines and style guide and your code reflects that.
Write up a clear and concise description of what your pull request adds or fixes and if it's an added feature explain why you think it should be included in the core.

If your PR is to fix an issue mention that issue here

**Questions (please complete the following information):**
- Have you personally loaded this code into an updated qbcore project and checked all it's functionality? [yes/no] (Be honest)
- Does your code fit the style guidelines? [yes/no]
- Does your PR fit the contribution guidelines? [yes/no]
