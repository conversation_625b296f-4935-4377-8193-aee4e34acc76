{"manifest": {"name": "browserify-rsa", "version": "4.1.0", "description": "RSA for browserify", "bugs": {"url": "https://github.com/crypto-browserify/browserify-rsa/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "repository": {"type": "git", "url": "https://github.com:crypto-browserify/browserify-rsa.git"}, "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "dependencies": {"bn.js": "^5.0.0", "randombytes": "^2.0.1"}, "devDependencies": {"parse-asn1": "^5.0.0", "standard": "^6.0.8", "tape": "^4.5.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-browserify-rsa-4.1.0-b2fd06b5b75ae297f7ce2dc651f918f5be158c8d-integrity\\node_modules\\browserify-rsa\\package.json", "readmeFilename": "README.md", "readme": "# browserify-rsa\n\n[![NPM Package](https://img.shields.io/npm/v/browserify-rsa.svg?style=flat-square)](https://www.npmjs.org/package/browserify-rsa)\n[![Build Status](https://img.shields.io/travis/crypto-browserify/browserify-rsa.svg?branch=master&style=flat-square)](https://travis-ci.org/crypto-browserify/browserify-rsa)\n[![Dependency status](https://img.shields.io/david/crypto-browserify/browserify-rsa.svg?style=flat-square)](https://david-dm.org/crypto-browserify/browserify-rsa#info=dependencies)\n\n[![js-standard-style](https://cdn.rawgit.com/feross/standard/master/badge.svg)](https://github.com/feross/standard)\n\nRSA private decryption/signing using chinese remainder and blinding.\n\n## API\n\nGive it a message as a Buffer and a private key (as decoded by `ASN.1`) and it returns encrypted data as a Buffer.\n\n## LICENSE\n\nMIT\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2016 Calvin Metcalf & contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/browserify-rsa/-/browserify-rsa-4.1.0.tgz#b2fd06b5b75ae297f7ce2dc651f918f5be158c8d", "type": "tarball", "reference": "https://registry.yarnpkg.com/browserify-rsa/-/browserify-rsa-4.1.0.tgz", "hash": "b2fd06b5b75ae297f7ce2dc651f918f5be158c8d", "integrity": "sha512-AdEER0Hkspgno2aR97SAf6vi0y0k8NuOpGnVH3O99rcA5Q6sh8QxcngtHuJ6uXwnfAXNM4Gn1Gb7/MV1+Ymbog==", "registry": "npm", "packageName": "browserify-rsa", "cacheIntegrity": "sha512-AdEER0Hkspgno2aR97SAf6vi0y0k8NuOpGnVH3O99rcA5Q6sh8QxcngtHuJ6uXwnfAXNM4Gn1Gb7/MV1+Ymbog== sha1-sv0Gtbda4pf3zi3GUfkY9b4VjI0="}, "registry": "npm", "hash": "b2fd06b5b75ae297f7ce2dc651f918f5be158c8d"}