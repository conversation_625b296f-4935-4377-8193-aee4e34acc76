{"manifest": {"name": "deep-equal", "version": "1.0.1", "description": "node's assert.deepEqual algorithm", "main": "index.js", "scripts": {"test": "tape test/*.js"}, "devDependencies": {"tape": "^3.5.0"}, "repository": {"type": "git", "url": "http://github.com/substack/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-deep-equal-1.0.1-f5d260292b660e084eff4cdbc9f08ad3247448b5-integrity\\node_modules\\deep-equal\\package.json", "readmeFilename": "readme.markdown", "readme": "# deep-equal\n\nNode's `assert.deepEqual() algorithm` as a standalone module.\n\nThis module is around [5 times faster](https://gist.github.com/2790507)\nthan wrapping `assert.deepEqual()` in a `try/catch`.\n\n[![browser support](https://ci.testling.com/substack/node-deep-equal.png)](https://ci.testling.com/substack/node-deep-equal)\n\n[![build status](https://secure.travis-ci.org/substack/node-deep-equal.png)](https://travis-ci.org/substack/node-deep-equal)\n\n# example\n\n``` js\nvar equal = require('deep-equal');\nconsole.dir([\n    equal(\n        { a : [ 2, 3 ], b : [ 4 ] },\n        { a : [ 2, 3 ], b : [ 4 ] }\n    ),\n    equal(\n        { x : 5, y : [6] },\n        { x : 5, y : 6 }\n    )\n]);\n```\n\n# methods\n\n``` js\nvar deepEqual = require('deep-equal')\n```\n\n## deepEqual(a, b, opts)\n\nCompare objects `a` and `b`, returning whether they are equal according to a\nrecursive equality algorithm.\n\nIf `opts.strict` is `true`, use strict equality (`===`) to compare leaf nodes.\nThe default is to use coercive equality (`==`) because that's how\n`assert.deepEqual()` works by default.\n\n# install\n\nWith [npm](http://npmjs.org) do:\n\n```\nnpm install deep-equal\n```\n\n# test\n\nWith [npm](http://npmjs.org) do:\n\n```\nnpm test\n```\n\n# license\n\nMIT. Derived largely from node's assert module.\n", "licenseText": "This software is released under the MIT license:\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\n<PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/deep-equal/-/deep-equal-1.0.1.tgz#f5d260292b660e084eff4cdbc9f08ad3247448b5", "type": "tarball", "reference": "https://registry.yarnpkg.com/deep-equal/-/deep-equal-1.0.1.tgz", "hash": "f5d260292b660e084eff4cdbc9f08ad3247448b5", "integrity": "sha1-9dJgKStmDghO/0zbyfCK0yR0SLU=", "registry": "npm", "packageName": "deep-equal", "cacheIntegrity": "sha512-bHtC0iYvWhyaTzvV3CZgPeZQqCOBGyGsVV7v4eevpdkLHfiSrXUdBG+qAuSz4RI70sszvjQ1QSZ98An1yNwpSw== sha1-9dJgKStmDghO/0zbyfCK0yR0SLU="}, "registry": "npm", "hash": "f5d260292b660e084eff4cdbc9f08ad3247448b5"}