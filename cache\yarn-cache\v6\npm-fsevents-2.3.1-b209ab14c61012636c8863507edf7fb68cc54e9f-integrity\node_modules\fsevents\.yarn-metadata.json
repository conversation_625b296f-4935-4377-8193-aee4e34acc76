{"manifest": {"name": "fsevents", "version": "2.3.1", "description": "Native Access to MacOS FSEvents", "main": "fsevents.js", "types": "fsevents.d.ts", "os": ["darwin"], "files": ["fsevents.d.ts", "fsevents.js", "fsevents.node"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "build": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean", "test": "/bin/bash ./test.sh 2>/dev/null", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "https://github.com/fsevents/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "license": "MIT", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "homepage": "https://github.com/fsevents/fsevents", "devDependencies": {"node-gyp": "^6.1.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-fsevents-2.3.1-b209ab14c61012636c8863507edf7fb68cc54e9f-integrity\\node_modules\\fsevents\\package.json", "readmeFilename": "README.md", "readme": "# fsevents [![NPM](https://nodei.co/npm/fsevents.png)](https://nodei.co/npm/fsevents/)\n\nNative access to MacOS FSEvents in [Node.js](https://nodejs.org/)\n\nThe FSEvents API in MacOS allows applications to register for notifications of\nchanges to a given directory tree. It is a very fast and lightweight alternative\nto kqueue.\n\nThis is a low-level library. For a cross-platform file watching module that\nuses fsevents, check out [<PERSON><PERSON><PERSON>](https://github.com/paulmillr/chokidar).\n\n## Installation\n\nSupports only **Node.js v8.16 and higher**.\n\n```sh\nnpm install fsevents\n```\n\n## Usage\n\n```js\nconst fsevents = require('fsevents');\nconst stop = fsevents.watch(__dirname, (path, flags, id) => {\n  const info = fsevents.getInfo(path, flags, id);\n}); // To start observation\nstop(); // To end observation\n```\n\nThe callback passed as the second parameter to `.watch` get's called whenever the operating system detects a\na change in the file system. It takes three arguments:\n\n###### `fsevents.watch(dirname: string, (path: string, flags: number, id: string) => void): () => Promise<undefined>`\n\n * `path: string` - the item in the filesystem that have been changed\n * `flags: number` - a numeric value describing what the change was\n * `id: string` - an unique-id identifying this specific event\n\n Returns closer callback which when called returns a Promise resolving when the watcher process has been shut down.\n\n###### `fsevents.getInfo(path: string, flags: number, id: string): FsEventInfo`\n\nThe `getInfo` function takes the `path`, `flags` and `id` arguments and converts those parameters into a structure\nthat is easier to digest to determine what the change was.\n\nThe `FsEventsInfo` has the following shape:\n\n```js\n/**\n * @typedef {'created'|'modified'|'deleted'|'moved'|'root-changed'|'cloned'|'unknown'} FsEventsEvent\n * @typedef {'file'|'directory'|'symlink'} FsEventsType\n */\n{\n  \"event\": \"created\", // {FsEventsEvent}\n  \"path\": \"file.txt\",\n  \"type\": \"file\",    // {FsEventsType}\n  \"changes\": {\n    \"inode\": true,   // Had iNode Meta-Information changed\n    \"finder\": false, // Had Finder Meta-Data changed\n    \"access\": false, // Had access permissions changed\n    \"xattrs\": false  // Had xAttributes changed\n  },\n  \"flags\": 0x100000000\n}\n```\n\n## Changelog\n\n- v2.3 supports Apple Silicon ARM CPUs\n- v2 supports node 8.16+ and reduces package size massively\n- v1.2.8 supports node 6+\n- v1.2.7 supports node 4+\n\n## Troubleshooting\n\n- I'm getting `EBADPLATFORM` `Unsupported platform for fsevents` error.\n- It's fine, nothing is broken. fsevents is macos-only. Other platforms are skipped. If you want to hide this warning, report a bug to NPM bugtracker asking them to hide ebadplatform warnings by default.\n\n## License\n\nThe MIT License Copyright (C) 2010-2020 by Philipp Dunkel, Ben Noordhuis, Elan Shankar, Paul Miller — see LICENSE file.\n\nVisit our [GitHub page](https://github.com/fsevents/fsevents) and [NPM Page](https://npmjs.org/package/fsevents)\n", "licenseText": "MIT License\n-----------\n\nCopyright (C) 2010-2020 by <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR CO<PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/fsevents/-/fsevents-2.3.1.tgz#b209ab14c61012636c8863507edf7fb68cc54e9f", "type": "tarball", "reference": "https://registry.yarnpkg.com/fsevents/-/fsevents-2.3.1.tgz", "hash": "b209ab14c61012636c8863507edf7fb68cc54e9f", "integrity": "sha512-YR47Eg4hChJGAB1O3yEAOkGO+rlzutoICGqGo9EZ4lKWokzZRSyIW1QmTzqjtw8MJdj9srP869CuWw/hyzSiBw==", "registry": "npm", "packageName": "fsevents", "cacheIntegrity": "sha512-YR47Eg4hChJGAB1O3yEAOkGO+rlzutoICGqGo9EZ4lKWokzZRSyIW1QmTzqjtw8MJdj9srP869CuWw/hyzSiBw== sha1-sgmrFMYQEmNsiGNQft9/tozFTp8="}, "registry": "npm", "hash": "b209ab14c61012636c8863507edf7fb68cc54e9f"}