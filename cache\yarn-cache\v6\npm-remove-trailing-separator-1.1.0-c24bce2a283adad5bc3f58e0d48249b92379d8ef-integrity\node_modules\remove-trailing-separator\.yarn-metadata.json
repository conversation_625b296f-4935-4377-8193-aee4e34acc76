{"manifest": {"name": "remove-trailing-separator", "version": "1.1.0", "description": "Removes separators from the end of the string.", "main": "index.js", "files": ["index.js"], "scripts": {"lint": "xo", "pretest": "npm run lint", "test": "nyc ava", "report": "nyc report --reporter=html"}, "repository": {"type": "git", "url": "git+https://github.com/darsain/remove-trailing-separator.git"}, "keywords": ["remove", "strip", "trailing", "separator"], "author": {"name": "da<PERSON>in"}, "license": "ISC", "bugs": {"url": "https://github.com/darsain/remove-trailing-separator/issues"}, "homepage": "https://github.com/darsain/remove-trailing-separator#readme", "devDependencies": {"ava": "^0.16.0", "coveralls": "^2.11.14", "nyc": "^8.3.0", "xo": "^0.16.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-remove-trailing-separator-1.1.0-c24bce2a283adad5bc3f58e0d48249b92379d8ef-integrity\\node_modules\\remove-trailing-separator\\package.json", "readmeFilename": "readme.md", "readme": "# remove-trailing-separator\n\n[![NPM version][npm-img]][npm-url] [![Build Status: Linux][travis-img]][travis-url] [![Build Status: Windows][appveyor-img]][appveyor-url] [![Coverage Status][coveralls-img]][coveralls-url]\n\nRemoves all separators from the end of a string.\n\n## Install\n\n```\nnpm install remove-trailing-separator\n```\n\n## Examples\n\n```js\nconst removeTrailingSeparator = require('remove-trailing-separator');\n\nremoveTrailingSeparator('/foo/bar/')   // '/foo/bar'\nremoveTrailingSeparator('/foo/bar///') // '/foo/bar'\n\n// leaves only/last separator\nremoveTrailingSeparator('/')    // '/'\nremoveTrailingSeparator('///')  // '/'\n\n// returns empty string\nremoveTrailingSeparator('') // ''\n```\n\n## Notable backslash, or win32 separator behavior\n\n`\\` is considered a separator only on WIN32 systems. All POSIX compliant systems\nsee backslash as a valid file name character, so it would break POSIX compliance\nto remove it there.\n\nIn practice, this means that this code will return different things depending on\nwhat system it runs on:\n\n```js\nremoveTrailingSeparator('\\\\foo\\\\')\n// UNIX  => '\\\\foo\\\\'\n// WIN32 => '\\\\foo'\n```\n\n[npm-url]: https://npmjs.org/package/remove-trailing-separator\n[npm-img]: https://badge.fury.io/js/remove-trailing-separator.svg\n[travis-url]: https://travis-ci.org/darsain/remove-trailing-separator\n[travis-img]: https://travis-ci.org/darsain/remove-trailing-separator.svg?branch=master\n[appveyor-url]: https://ci.appveyor.com/project/darsain/remove-trailing-separator/branch/master\n[appveyor-img]: https://ci.appveyor.com/api/projects/status/wvg9a93rrq95n2xl/branch/master?svg=true\n[coveralls-url]: https://coveralls.io/github/darsain/remove-trailing-separator?branch=master\n[coveralls-img]: https://coveralls.io/repos/github/darsain/remove-trailing-separator/badge.svg?branch=master\n", "licenseText": "Permission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANT<PERSON>ILITY AND F<PERSON><PERSON>SS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, <PERSON><PERSON><PERSON> OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, <PERSON><PERSON><PERSON><PERSON>ENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz#c24bce2a283adad5bc3f58e0d48249b92379d8ef", "type": "tarball", "reference": "https://registry.yarnpkg.com/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "hash": "c24bce2a283adad5bc3f58e0d48249b92379d8ef", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8=", "registry": "npm", "packageName": "remove-trailing-separator", "cacheIntegrity": "sha512-/hS+Y0u3aOfIETiaiirUFwDBDzmXPvO+jAfKTitUngIPzdKc6Z0LoFjM/CK5PL4C+eKwHohlHAb6H0VFfmmUsw== sha1-wkvOKig62tW8P1jg1IJJuSN52O8="}, "registry": "npm", "hash": "c24bce2a283adad5bc3f58e0d48249b92379d8ef"}