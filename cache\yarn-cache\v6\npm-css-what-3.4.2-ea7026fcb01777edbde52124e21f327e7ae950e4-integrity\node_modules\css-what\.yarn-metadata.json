{"manifest": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "3.4.2", "funding": "https://github.com/sponsors/fb55", "repository": {"url": "https://github.com/fb55/css-what"}, "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib/**/*"], "scripts": {"test": "jest --coverage -u && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "dependencies": {}, "devDependencies": {"@types/jest": "^26.0.3", "@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "coveralls": "^3.0.5", "eslint": "^7.0.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-node": "^11.1.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.0.0", "typescript": "^4.0.2"}, "optionalDependencies": {}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-css-what-3.4.2-ea7026fcb01777edbde52124e21f327e7ae950e4-integrity\\node_modules\\css-what\\package.json", "readmeFilename": "readme.md", "readme": "# css-what [![Build Status](https://secure.travis-ci.org/fb55/css-what.svg?branch=master)](http://travis-ci.org/fb55/css-what)\n\na CSS selector parser\n\n## Example\n\n```js\nconst CSSwhat = require(\"css-what\")\nCSSwhat.parse(\"foo[bar]:baz\")\n\n~> [\n    [\n        { type: \"tag\", name: \"foo\" },\n        {\n            type: \"attribute\",\n            name: \"bar\",\n            action: \"exists\",\n            value: \"\",\n            ignoreCase: false\n        },\n        { type: \"pseudo\", name: \"baz\", data: null }\n    ]\n]\n```\n\n## API\n\n**`CSSwhat.parse(str, options)` - Parses `str`, optionally with the passed `options`.**\n\nThe function returns a two-dimensional array. The first array represents selectors separated by commas (eg. `sub1, sub2`), the second contains the relevant tokens for that selector. Possible token types are:\n\n| name             | attributes                              | example       | output                                                                                   |\n| ---------------- | --------------------------------------- | ------------- | ---------------------------------------------------------------------------------------- |\n| `tag`            | `name`                                  | `div`         | `{ type: 'tag', name: 'div' }`                                                           |\n| `universal`      | -                                       | `*`           | `{ type: 'universal' }`                                                                  |\n| `pseudo`         | `name`, `data`                          | `:name(data)` | `{ type: 'pseudo', name: 'name', data: 'data' }`                                         |\n| `pseudo`         | `name`, `data`                          | `:name`       | `{ type: 'pseudo', name: 'name', data: null }`                                           |\n| `pseudo-element` | `name`                                  | `::name`      | `{ type: 'pseudo-element', name: 'name' }`                                               |\n| `attribute`      | `name`, `action`, `value`, `ignoreCase` | `[attr]`      | `{ type: 'attribute', name: 'attr', action: 'exists', value: '', ignoreCase: false }`    |\n| `attribute`      | `name`, `action`, `value`, `ignoreCase` | `[attr=val]`  | `{ type: 'attribute', name: 'attr', action: 'equals', value: 'val', ignoreCase: false }` |\n| `attribute`      | `name`, `action`, `value`, `ignoreCase` | `[attr^=val]` | `{ type: 'attribute', name: 'attr', action: 'start', value: 'val', ignoreCase: false }`  |\n| `attribute`      | `name`, `action`, `value`, `ignoreCase` | `[attr$=val]` | `{ type: 'attribute', name: 'attr', action: 'end', value: 'val', ignoreCase: false }`    |\n| `child`          | -                                       | `>`           | `{ type: 'child' }`                                                                      |\n| `parent`         | -                                       | `<`           | `{ type: 'parent' }`                                                                     |\n| `sibling`        | -                                       | `~`           | `{ type: 'sibling' }`                                                                    |\n| `adjacent`       | -                                       | `+`           | `{ type: 'adjacent' }`                                                                   |\n| `descendant`     | -                                       |               | `{ type: 'descendant' }`                                                                 |\n\n**Options:**\n\n-   `lowerCaseTags`: When false, tag names will not be lowercased. Defaults to `true`.\n-   `lowerCaseAttributeNames`: When false, attribute names will not be lowercased. Defaults to `true`.\n-   `xmlMode`: When `true`, `xmlMode` implies both `lowerCaseTags` and `lowerCaseAttributeNames` are set to `false`.\n\n**`CSSwhat.stringify(selector)` - Turns `selector` back into a string.**\n\n---\n\nLicense: BSD-2-Clause\n\n## Security contact information\n\nTo report a security vulnerability, please use the [Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\n## `css-what` for enterprise\n\nAvailable as part of the Tidelift Subscription\n\nThe maintainers of `css-what` and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-css-what?utm_source=npm-css-what&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n", "licenseText": "Copyright (c) <PERSON>\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n\nRedistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.\n\nRedistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.\n\nTHIS IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERC<PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS,\nEVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/css-what/-/css-what-3.4.2.tgz#ea7026fcb01777edbde52124e21f327e7ae950e4", "type": "tarball", "reference": "https://registry.yarnpkg.com/css-what/-/css-what-3.4.2.tgz", "hash": "ea7026fcb01777edbde52124e21f327e7ae950e4", "integrity": "sha512-ACUm3L0/jiZTqfzRM3Hi9Q8eZqd6IK37mMWPLz9PJxkLWllYeRf+EHUSHYEtFop2Eqytaq1FizFVh7XfBnXCDQ==", "registry": "npm", "packageName": "css-what", "cacheIntegrity": "sha512-ACUm3L0/jiZTqfzRM3Hi9Q8eZqd6IK37mMWPLz9PJxkLWllYeRf+EHUSHYEtFop2Eqytaq1FizFVh7XfBnXCDQ== sha1-6nAm/LAXd+295SEk4h8yfnrpUOQ="}, "registry": "npm", "hash": "ea7026fcb01777edbde52124e21f327e7ae950e4"}