local L0_1, L1_1, L2_1, L3_1, L4_1, L5_1, L6_1, L7_1, L8_1, L9_1, L10_1, L11_1, L12_1, L13_1, L14_1
L0_1 = {}
L1_1 = false
L2_1 = false
L3_1 = {}
L4_1 = false
L5_1 = Citizen
L5_1 = L5_1.CreateThread
function L6_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2
  L0_2 = Public
  L0_2 = L0_2.Blips
  if L0_2 then
    L0_2 = pairs
    L1_2 = Public
    L1_2 = L1_2.Blips
    L0_2, L1_2, L2_2, L3_2 = L0_2(L1_2)
    for L4_2, L5_2 in L0_2, L1_2, L2_2, L3_2 do
      L6_2 = AddBlipForCoord
      L7_2 = L5_2.coords
      L7_2 = L7_2.xyz
      L6_2 = L6_2(L7_2)
      L7_2 = SetBlipSprite
      L8_2 = L6_2
      L9_2 = L5_2.sprite
      if not L9_2 then
        L9_2 = 1
      end
      L7_2(L8_2, L9_2)
      L7_2 = SetBlipColour
      L8_2 = L6_2
      L9_2 = L5_2.color
      if not L9_2 then
        L9_2 = 0
      end
      L7_2(L8_2, L9_2)
      L7_2 = SetBlipScale
      L8_2 = L6_2
      L9_2 = L5_2.size
      if not L9_2 then
        L9_2 = 0.8
      end
      L7_2(L8_2, L9_2)
      L7_2 = SetBlipAsShortRange
      L8_2 = L6_2
      L9_2 = true
      L7_2(L8_2, L9_2)
      L7_2 = BeginTextCommandSetBlipName
      L8_2 = "STRING"
      L7_2(L8_2)
      L7_2 = AddTextComponentSubstringPlayerName
      L8_2 = L5_2.name
      L7_2(L8_2)
      L7_2 = EndTextCommandSetBlipName
      L8_2 = L6_2
      L7_2(L8_2)
    end
  end
  L0_2 = Citizen
  L0_2 = L0_2.CreateThread
  function L1_2()
    local L0_3, L1_3, L2_3, L3_3, L4_3, L5_3, L6_3, L7_3, L8_3, L9_3
    L0_3 = Public
    L0_3 = L0_3.TrafficZones
    if L0_3 then
      L0_3 = Citizen
      L0_3 = L0_3.Wait
      L1_3 = 5000
      L0_3(L1_3)
      L0_3 = 1
      L1_3 = Public
      L1_3 = L1_3.TrafficZones
      L1_3 = #L1_3
      L2_3 = 1
      for L3_3 = L0_3, L1_3, L2_3 do
        L4_3 = onduty
        if L4_3 then
          L4_3 = Public
          L4_3 = L4_3.TrafficZones
          L4_3 = L4_3[L3_3]
          L5_3 = AddBlipForRadius
          L6_3 = Public
          L6_3 = L6_3.TrafficZones
          L6_3 = L6_3[L3_3]
          L6_3 = L6_3.coords
          L7_3 = 50.0
          L5_3 = L5_3(L6_3, L7_3)
          L4_3.blip = L5_3
          L4_3 = SetBlipAlpha
          L5_3 = Public
          L5_3 = L5_3.TrafficZones
          L5_3 = L5_3[L3_3]
          L5_3 = L5_3.coords
          L6_3 = 100
          L4_3(L5_3, L6_3)
        end
        L4_3 = Public
        L4_3 = L4_3.TrafficZones
        L4_3 = L4_3[L3_3]
        L4_3 = L4_3.type
        if "stop" == L4_3 then
          L4_3 = Public
          L4_3 = L4_3.TrafficZones
          L4_3 = L4_3[L3_3]
          L5_3 = AddRoadNodeSpeedZone
          L6_3 = Public
          L6_3 = L6_3.TrafficZones
          L6_3 = L6_3[L3_3]
          L6_3 = L6_3.coords
          L7_3 = 50.0
          L8_3 = 0.0
          L9_3 = false
          L5_3 = L5_3(L6_3, L7_3, L8_3, L9_3)
          L4_3.id = L5_3
          L4_3 = Public
          L4_3 = L4_3.TrafficZones
          L4_3 = L4_3[L3_3]
          L4_3 = L4_3.blip
          if L4_3 then
            L4_3 = SetBlipColour
            L5_3 = Public
            L5_3 = L5_3.TrafficZones
            L5_3 = L5_3[L3_3]
            L5_3 = L5_3.blip
            L6_3 = 6
            L4_3(L5_3, L6_3)
          end
        else
          L4_3 = Public
          L4_3 = L4_3.TrafficZones
          L4_3 = L4_3[L3_3]
          L4_3 = L4_3.type
          if "slow" == L4_3 then
            L4_3 = Public
            L4_3 = L4_3.TrafficZones
            L4_3 = L4_3[L3_3]
            L5_3 = AddRoadNodeSpeedZone
            L6_3 = Public
            L6_3 = L6_3.TrafficZones
            L6_3 = L6_3[L3_3]
            L6_3 = L6_3.coords
            L7_3 = 50.0
            L8_3 = 5.0
            L9_3 = false
            L5_3 = L5_3(L6_3, L7_3, L8_3, L9_3)
            L4_3.id = L5_3
            L4_3 = Public
            L4_3 = L4_3.TrafficZones
            L4_3 = L4_3[L3_3]
            L4_3 = L4_3.blip
            if L4_3 then
              L4_3 = SetBlipColour
              L5_3 = Public
              L5_3 = L5_3.TrafficZones
              L5_3 = L5_3[L3_3]
              L5_3 = L5_3.blip
              L6_3 = 44
              L4_3(L5_3, L6_3)
            end
          end
        end
      end
    end
  end
  L0_2(L1_2)
  while true do
    L0_2 = 2000
    L1_2 = PlayerPedId
    L1_2 = L1_2()
    L2_2 = GetEntityCoords
    L3_2 = L1_2
    L2_2 = L2_2(L3_2)
    L3_2 = Public
    L3_2 = L3_2.Markers
    if L3_2 then
      L3_2 = 1
      L4_2 = Public
      L4_2 = L4_2.Markers
      L4_2 = #L4_2
      L5_2 = 1
      for L6_2 = L3_2, L4_2, L5_2 do
        L7_2 = Public
        L7_2 = L7_2.Markers
        L7_2 = L7_2[L6_2]
        L8_2 = L7_2.coords
        L8_2 = L8_2.xyz
        L8_2 = L2_2 - L8_2
        L8_2 = #L8_2
        if L8_2 < 30 then
          L0_2 = 0
          L9_2 = Config
          L9_2 = L9_2.MarkersDraw
          if L9_2 then
            L9_2 = DrawMarker
            L10_2 = L7_2.sprite
            L11_2 = L7_2.coords
            L11_2 = L11_2.x
            L12_2 = L7_2.coords
            L12_2 = L12_2.y
            L13_2 = L7_2.coords
            L13_2 = L13_2.z
            L14_2 = 0.0
            L15_2 = 0.0
            L16_2 = 0.0
            L17_2 = 0.0
            L18_2 = 0.0
            L19_2 = 0.0
            L20_2 = 0.3
            L21_2 = 0.3
            L22_2 = 0.3
            L23_2 = L7_2.rgba
            L23_2 = L23_2.r
            L24_2 = L7_2.rgba
            L24_2 = L24_2.g
            L25_2 = L7_2.rgba
            L25_2 = L25_2.b
            L26_2 = L7_2.rgba
            L26_2 = L26_2.a
            L27_2 = false
            L28_2 = false
            L29_2 = 0
            L30_2 = true
            L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2)
          end
          L9_2 = L7_2.radius
          if not L9_2 then
            L9_2 = 1
          end
          if L8_2 < L9_2 then
            L9_2 = Config
            L9_2 = L9_2.CustomNotify
            if L9_2 then
              L9_2 = ShowHelpNotification
              L10_2 = "E"
              L11_2 = L7_2.text
              L9_2(L10_2, L11_2)
              L7_2.noty = true
            else
              L9_2 = Config
              L9_2 = L9_2.Framework
              if "qbcore" == L9_2 then
                L9_2 = L7_2.noty
                if not L9_2 then
                  L9_2 = exports
                  L9_2 = L9_2["qb-core"]
                  L10_2 = L9_2
                  L9_2 = L9_2.DrawText
                  L11_2 = "[E] - "
                  L12_2 = L7_2.text
                  L11_2 = L11_2 .. L12_2
                  L12_2 = "left"
                  L9_2(L10_2, L11_2, L12_2)
                  L7_2.noty = true
                end
              else
                L9_2 = Config
                L9_2 = L9_2.Framework
                if "esx" == L9_2 then
                  L9_2 = Framework
                  L9_2 = L9_2.ShowHelpNotification
                  L10_2 = "~INPUT_PICKUP~ "
                  L11_2 = L7_2.text
                  L10_2 = L10_2 .. L11_2
                  L11_2 = true
                  L9_2(L10_2, L11_2)
                end
              end
            end
            L9_2 = IsControlJustPressed
            L10_2 = 0
            L11_2 = 38
            L9_2 = L9_2(L10_2, L11_2)
            if L9_2 then
              L9_2 = TriggerEvent
              L10_2 = L7_2.event
              L11_2 = L6_2
              L12_2 = L7_2.station
              L9_2(L10_2, L11_2, L12_2)
            end
          else
            L9_2 = L7_2.noty
            if L9_2 then
              L9_2 = HideHelpNotification
              L9_2()
              L7_2.noty = nil
            end
          end
        else
          L9_2 = L7_2.noty
          if L9_2 then
            L9_2 = HideHelpNotification
            L9_2()
            L7_2.noty = nil
          end
        end
      end
    end
    L3_2 = Citizen
    L3_2 = L3_2.Wait
    L4_2 = L0_2
    L3_2(L4_2)
  end
end
L5_1(L6_1)
function L5_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2
  L0_2 = GetEntityCoords
  L1_2 = PlayerPedId
  L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2 = L1_2()
  L0_2 = L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L1_2 = 1
  L2_2 = Config
  L2_2 = L2_2.RestrictedAlertZones
  L2_2 = #L2_2
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = isInside
    L6_2 = Config
    L6_2 = L6_2.RestrictedAlertZones
    L6_2 = L6_2[L4_2]
    L7_2 = L0_2.x
    L8_2 = L0_2.y
    L5_2 = L5_2(L6_2, L7_2, L8_2)
    if L5_2 then
      L5_2 = true
      return L5_2
    end
  end
  L1_2 = false
  return L1_2
end
L6_1 = Citizen
L6_1 = L6_1.CreateThread
Citizen.CreateThread(function()
  while true do
      local waitTime = 500
      local playerPed = PlayerPedId()
      local playerCoords = GetEntityCoords(playerPed)

      -- Check if player is aiming or has relevant flag
      if IsPlayerFreeAiming(PlayerId()) or GetPedConfigFlag(playerPed, 78) then
          -- Check if in restricted zone
          if not L5_1() then
              waitTime = 0
              
              -- Handle shooting evidence
              if IsPedShooting(playerPed) then
                  local success, hitCoords, hitEntity = GetScreenCoords()
                  if success then
                      local weaponData = GetItemFromWeapon(FW_GetPlayerData(false), GetSelectedPedWeapon(playerPed))
                      L0_1 = weaponData

                      -- Process evidence if system enabled
                      if Config.EvidenceSystem and weaponData and weaponData.slot then
                          -- Add shooting evidence
                          table.insert(L3_1, {
                              type = "Shoot",
                              coords = GetEntityCoords(playerPed),
                              wp = weaponData.slot
                          })

                          -- Handle hit entity evidence
                          if DoesEntityExist(hitEntity) and not IsEntityPositionFrozen(hitEntity) then
                              if hitEntity and IsEntityAPed(hitEntity) and not IsPedAPlayer(hitEntity) then
                                  table.insert(L3_1, {
                                      type = "NpcBlood",
                                      coords = hitCoords,
                                      adn = IsPedHuman(hitEntity) and "Generate" or "Animal"
                                  })
                              else
                                  table.insert(L3_1, {
                                      type = "Impact",
                                      coords = hitCoords,
                                      wp = weaponData.slot
                                  })
                              end
                          end
                      end

                      -- Handle shoot alert if enabled
                      if Config.ShootAlert then
                          SendShootAlert(weaponData)
                      end
                  end
              end
          end
      else
          -- Process collected evidence
          if #L3_1 > 0 and Config.EvidenceSystem then
              local consolidatedEvidence = {}
              
              -- Consolidate nearby evidence
              for i = 1, #L3_1 do
                  local combined = false
                  for j = 1, #consolidatedEvidence do
                      if #(consolidatedEvidence[j].coords - L3_1[i].coords) < 1.1 and 
                         consolidatedEvidence[j].type == L3_1[i].type then
                          -- Handle NPC evidence
                          if not (L3_1[i].npc and consolidatedEvidence[j].npc and 
                                 L3_1[i].npc ~= consolidatedEvidence[j].npc) then
                              consolidatedEvidence[j].count = (consolidatedEvidence[j].count or 1) + 1
                              combined = true
                          end
                      end
                  end
                  
                  if not combined then
                      table.insert(consolidatedEvidence, L3_1[i])
                  end
              end
              
              TriggerServerEvent("origen_police:server:AddNewEvidence", consolidatedEvidence)
              L3_1 = {}
          end
      end

      -- Handle vehicle and health evidence
      if Config.EvidenceSystem then
          local vehicle = GetVehiclePedIsIn(playerPed, false)
          
          if vehicle ~= 0 then
              if GetPedInVehicleSeat(vehicle, -1) == PlayerPedId() then
                  -- Check for vehicle damage
                  if L2_1 then
                      local healthDiff = L2_1 - GetVehicleBodyHealth(vehicle)
                      if healthDiff > 5 then
                          TriggerServerEvent("origen_police:server:AddNewEvidence", {
                              type = "VehCrash",
                              model = GetVehicleModelName(vehicle),
                              color = {GetVehicleColor(vehicle)},
                              coords = GetEntityCoords(vehicle)
                          })
                      end
                  end
                  L2_1 = GetVehicleBodyHealth(vehicle)
              end
          else
              -- Handle player health evidence
              if L1_1 then
                  local healthDiff = L1_1 - GetEntityHealth(playerPed)
                  if healthDiff > 6 then
                      TriggerServerEvent("origen_police:server:AddNewEvidence", {
                          type = "BloodRest",
                          adn = true,
                          coords = GetEntityCoords(playerPed)
                      })
                  end
              end
              L1_1 = GetEntityHealth(playerPed)
              L2_1 = false
          end
      end

      Citizen.Wait(waitTime)
  end
end)
L6_1(L7_1)
L6_1 = Citizen
L6_1 = L6_1.CreateThread
function L7_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2
  L0_2 = false
  while true do
    L1_2 = Public
    L1_2 = L1_2.Radars
    if L1_2 then
      L1_2 = Public
      L1_2 = L1_2.Radars
      L1_2 = #L1_2
      if 0 ~= L1_2 then
        break
      end
    end
    L1_2 = Citizen
    L1_2 = L1_2.Wait
    L2_2 = 5000
    L1_2(L2_2)
  end
  while true do
    L1_2 = 1500
    L2_2 = GetVehiclePedIsIn
    L3_2 = PlayerPedId
    L3_2 = L3_2()
    L4_2 = false
    L2_2 = L2_2(L3_2, L4_2)
    L3_2 = Public
    L3_2 = L3_2.Radars
    L3_2 = #L3_2
    if L3_2 > 0 and 0 ~= L2_2 then
      L3_2 = GetPedInVehicleSeat
      L4_2 = L2_2
      L5_2 = -1
      L3_2 = L3_2(L4_2, L5_2)
      L4_2 = PlayerPedId
      L4_2 = L4_2()
      if L3_2 == L4_2 then
        L3_2 = GetEntityCoords
        L4_2 = L2_2
        L3_2 = L3_2(L4_2)
        L1_2 = 150
        if not L0_2 then
          L4_2 = 1
          L5_2 = Public
          L5_2 = L5_2.Radars
          L5_2 = #L5_2
          L6_2 = 1
          for L7_2 = L4_2, L5_2, L6_2 do
            L8_2 = Public
            L8_2 = L8_2.Radars
            L8_2 = L8_2[L7_2]
            L8_2 = L8_2.coords
            L8_2 = L3_2 - L8_2
            L8_2 = #L8_2
            if L8_2 < 22 then
              L8_2 = Public
              L8_2 = L8_2.Radars
              L8_2 = L8_2[L7_2]
              L0_2 = L8_2.coords
              L8_2 = Public
              L8_2 = L8_2.Radars
              L8_2 = L8_2[L7_2]
              L8_2 = L8_2.type
              if 1 == L8_2 then
                L8_2 = Config
                L8_2 = L8_2.SpeedType
                if "kmh" == L8_2 then
                  L8_2 = 3.6
                  if L8_2 then
                    goto lbl_76
                  end
                end
                L8_2 = 2.23694
                ::lbl_76::
                L9_2 = GetEntitySpeed
                L10_2 = L2_2
                L9_2 = L9_2(L10_2)
                L9_2 = L9_2 * L8_2
                L10_2 = Public
                L10_2 = L10_2.Radars
                L10_2 = L10_2[L7_2]
                L10_2 = L10_2.value
                if L9_2 > L10_2 then
                  L10_2 = GetVehicleClass
                  L11_2 = L2_2
                  L10_2 = L10_2(L11_2)
                  if 18 ~= L10_2 then
                    L10_2 = FW_Round
                    L11_2 = L9_2
                    L10_2 = L10_2(L11_2)
                    L9_2 = L10_2
                    L10_2 = TriggerServerEvent
                    L11_2 = "SendAlert:police"
                    L12_2 = {}
                    L13_2 = Public
                    L13_2 = L13_2.Radars
                    L13_2 = L13_2[L7_2]
                    L13_2 = L13_2.coords
                    L12_2.coords = L13_2
                    L13_2 = Config
                    L13_2 = L13_2.Translations
                    L13_2 = L13_2.Speeding
                    L12_2.title = L13_2
                    L12_2.type = "RADARES"
                    L13_2 = {}
                    L14_2 = GetLabelText
                    L15_2 = GetDisplayNameFromVehicleModel
                    L16_2 = GetEntityModel
                    L17_2 = L2_2
                    L16_2, L17_2 = L16_2(L17_2)
                    L15_2, L16_2, L17_2 = L15_2(L16_2, L17_2)
                    L14_2 = L14_2(L15_2, L16_2, L17_2)
                    L13_2.model = L14_2
                    L14_2 = {}
                    L15_2 = GetVehicleColor
                    L16_2 = L2_2
                    L15_2, L16_2, L17_2 = L15_2(L16_2)
                    L14_2[1] = L15_2
                    L14_2[2] = L16_2
                    L14_2[3] = L17_2
                    L13_2.color = L14_2
                    L14_2 = GetVehiclePlate
                    L15_2 = L2_2
                    L14_2 = L14_2(L15_2)
                    L13_2.plate = L14_2
                    L14_2 = L9_2
                    L15_2 = " kmh / ^"
                    L16_2 = Public
                    L16_2 = L16_2.Radars
                    L16_2 = L16_2[L7_2]
                    L16_2 = L16_2.value
                    L16_2 = L9_2 - L16_2
                    L17_2 = " kmh"
                    L14_2 = L14_2 .. L15_2 .. L16_2 .. L17_2
                    L13_2.speed = L14_2
                    L12_2.metadata = L13_2
                    L13_2 = Config
                    L13_2 = L13_2.PoliceJobCategory
                    L12_2.job = L13_2
                    L10_2(L11_2, L12_2)
                    L10_2 = SendNUIMessage
                    L11_2 = {}
                    L11_2.action = "RadarFlash"
                    L10_2(L11_2)
                  end
                end
              else
                L8_2 = Public
                L8_2 = L8_2.Radars
                L8_2 = L8_2[L7_2]
                L8_2 = L8_2.type
                if 2 == L8_2 then
                  L8_2 = GetVehiclePlate
                  L9_2 = L2_2
                  L8_2 = L8_2(L9_2)
                  L9_2 = L8_2
                  L8_2 = L8_2.find
                  L10_2 = Public
                  L10_2 = L10_2.Radars
                  L10_2 = L10_2[L7_2]
                  L10_2 = L10_2.value
                  L8_2 = L8_2(L9_2, L10_2)
                  if L8_2 then
                    L8_2 = TriggerServerEvent
                    L9_2 = "SendAlert:police"
                    L10_2 = {}
                    L11_2 = Public
                    L11_2 = L11_2.Radars
                    L11_2 = L11_2[L7_2]
                    L11_2 = L11_2.coords
                    L10_2.coords = L11_2
                    L11_2 = Config
                    L11_2 = L11_2.Translations
                    L11_2 = L11_2.PlateCod9
                    L10_2.title = L11_2
                    L10_2.type = "RADARES"
                    L11_2 = {}
                    L12_2 = GetLabelText
                    L13_2 = GetDisplayNameFromVehicleModel
                    L14_2 = GetEntityModel
                    L15_2 = L2_2
                    L14_2, L15_2, L16_2, L17_2 = L14_2(L15_2)
                    L13_2, L14_2, L15_2, L16_2, L17_2 = L13_2(L14_2, L15_2, L16_2, L17_2)
                    L12_2 = L12_2(L13_2, L14_2, L15_2, L16_2, L17_2)
                    L11_2.model = L12_2
                    L12_2 = {}
                    L13_2 = GetVehicleColor
                    L14_2 = L2_2
                    L13_2, L14_2, L15_2, L16_2, L17_2 = L13_2(L14_2)
                    L12_2[1] = L13_2
                    L12_2[2] = L14_2
                    L12_2[3] = L15_2
                    L12_2[4] = L16_2
                    L12_2[5] = L17_2
                    L11_2.color = L12_2
                    L12_2 = GetVehiclePlate
                    L13_2 = L2_2
                    L12_2 = L12_2(L13_2)
                    L11_2.plate = L12_2
                    L10_2.metadata = L11_2
                    L11_2 = Config
                    L11_2 = L11_2.PoliceJobCategory
                    L10_2.job = L11_2
                    L8_2(L9_2, L10_2)
                    L8_2 = SendNUIMessage
                    L9_2 = {}
                    L9_2.action = "RadarFlash"
                    L8_2(L9_2)
                  end
                end
              end
            end
          end
        else
          L4_2 = L3_2 - L0_2
          L4_2 = #L4_2
          if L4_2 > 22 then
            L0_2 = false
          end
        end
      end
    end
    L3_2 = Citizen
    L3_2 = L3_2.Wait
    L4_2 = L1_2
    L3_2(L4_2)
  end
end
L6_1(L7_1)
L6_1 = Citizen
L6_1 = L6_1.CreateThread
function L7_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  L0_2 = {}
  L1_2 = {}
  L1_2.name = "wheel_lf"
  L1_2.index = 0
  L2_2 = {}
  L2_2.name = "wheel_lr"
  L2_2.index = 4
  L3_2 = {}
  L3_2.name = "wheel_rf"
  L3_2.index = 1
  L4_2 = {}
  L4_2.name = "wheel_lm1"
  L4_2.index = 2
  L5_2 = {}
  L5_2.name = "wheel_rm1"
  L5_2.index = 3
  L6_2 = {}
  L6_2.name = "wheel_rr"
  L6_2.index = 5
  L7_2 = {}
  L7_2.name = "wheel_lm2"
  L7_2.index = 45
  L8_2 = {}
  L8_2.name = "wheel_rm2"
  L8_2.index = 47
  L0_2[1] = L1_2
  L0_2[2] = L2_2
  L0_2[3] = L3_2
  L0_2[4] = L4_2
  L0_2[5] = L5_2
  L0_2[6] = L6_2
  L0_2[7] = L7_2
  L0_2[8] = L8_2
  while true do
    L1_2 = 1500
    L2_2 = IsPedInAnyVehicle
    L3_2 = PlayerPedId
    L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L3_2()
    L2_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
    if L2_2 then
      L2_2 = GetVehiclePedIsIn
      L3_2 = PlayerPedId
      L3_2 = L3_2()
      L4_2 = false
      L2_2 = L2_2(L3_2, L4_2)
      L3_2 = GetPedInVehicleSeat
      L4_2 = L2_2
      L5_2 = -1
      L3_2 = L3_2(L4_2, L5_2)
      L4_2 = PlayerPedId
      L4_2 = L4_2()
      if L3_2 == L4_2 then
        L1_2 = 500
        L3_2 = GetClosestObjectOfType
        L4_2 = GetEntityCoords
        L5_2 = L2_2
        L4_2 = L4_2(L5_2)
        L5_2 = 50.0
        L6_2 = GetHashKey
        L7_2 = "p_ld_stinger_s"
        L6_2 = L6_2(L7_2)
        L7_2 = false
        L8_2 = false
        L9_2 = false
        L3_2 = L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
        if 0 ~= L3_2 then
          L1_2 = 10
          L4_2 = GetEntityCoords
          L5_2 = L3_2
          L4_2 = L4_2(L5_2)
          L5_2 = GetEntitySpeed
          L6_2 = L2_2
          L5_2 = L5_2(L6_2)
          if L5_2 > 15 then
            L5_2 = GetEntityCoords
            L6_2 = L2_2
            L5_2 = L5_2(L6_2)
            L5_2 = L4_2 - L5_2
            L5_2 = #L5_2
            if L5_2 < 5 then
              L5_2 = 1
              L6_2 = GetVehicleNumberOfWheels
              L7_2 = L2_2
              L6_2 = L6_2(L7_2)
              L7_2 = 1
              for L8_2 = L5_2, L6_2, L7_2 do
                L9_2 = IsVehicleTyreBurst
                L10_2 = L2_2
                L11_2 = L0_2[L8_2]
                L11_2 = L11_2.index
                L12_2 = false
                L9_2 = L9_2(L10_2, L11_2, L12_2)
                if not L9_2 then
                  L9_2 = SetVehicleTyreBurst
                  L10_2 = L2_2
                  L11_2 = L0_2[L8_2]
                  L11_2 = L11_2.index
                  L12_2 = true
                  L13_2 = 0
                  L9_2(L10_2, L11_2, L12_2, L13_2)
                end
              end
            end
          else
            L5_2 = 1
            L6_2 = GetVehicleNumberOfWheels
            L7_2 = L2_2
            L6_2 = L6_2(L7_2)
            L7_2 = 1
            for L8_2 = L5_2, L6_2, L7_2 do
              L9_2 = GetWorldPositionOfEntityBone
              L10_2 = L2_2
              L11_2 = GetEntityBoneIndexByName
              L12_2 = L2_2
              L13_2 = L0_2[L8_2]
              L13_2 = L13_2.name
              L11_2, L12_2, L13_2 = L11_2(L12_2, L13_2)
              L9_2 = L9_2(L10_2, L11_2, L12_2, L13_2)
              L9_2 = L4_2 - L9_2
              L9_2 = #L9_2
              if L9_2 < 1 then
                L9_2 = IsVehicleTyreBurst
                L10_2 = L2_2
                L11_2 = L0_2[L8_2]
                L11_2 = L11_2.index
                L12_2 = false
                L9_2 = L9_2(L10_2, L11_2, L12_2)
                if not L9_2 then
                  L9_2 = SetVehicleTyreBurst
                  L10_2 = L2_2
                  L11_2 = L0_2[L8_2]
                  L11_2 = L11_2.index
                  L12_2 = true
                  L13_2 = 0
                  L9_2(L10_2, L11_2, L12_2, L13_2)
                end
              end
            end
          end
        end
      end
    end
    L2_2 = Citizen
    L2_2 = L2_2.Wait
    L3_2 = L1_2
    L2_2(L3_2)
  end
end
L6_1(L7_1)
L6_1 = {}
L7_1 = true
L8_1 = 0
L9_1 = false
function L10_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  while true do
    L0_2 = FW_GetPlayerData
    L1_2 = false
    L0_2 = L0_2(L1_2)
    L1_2 = 3000
    while nil ~= L0_2 do
      L2_2 = GetSelectedPedWeapon
      L3_2 = PlayerPedId
      L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2 = L3_2()
      L2_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
      L3_2 = L9_1
      if not L3_2 and 0 ~= L2_2 then
        L3_2 = GetHashKey
        L4_2 = "weapon_flashlight"
        L3_2 = L3_2(L4_2)
        if L2_2 == L3_2 then
          L3_2 = true
          L9_1 = L3_2
          L3_2 = PlayerPedId
          L3_2 = L3_2()
          L4_2 = CanOpenTablet
          L5_2 = L0_2.job
          L5_2 = L5_2.name
          L4_2 = L4_2(L5_2)
          L4_2 = L4_2[1]
          L5_2 = GetGameTimer
          L5_2 = L5_2()
          L6_2 = L8_1
          L5_2 = L5_2 - L6_2
          L6_2 = 10000
          if L5_2 > L6_2 then
            L5_2 = FW_GetPlayerData
            L6_2 = false
            L5_2 = L5_2(L6_2)
            L0_2 = L5_2
            L5_2 = GetGameTimer
            L5_2 = L5_2()
            L8_1 = L5_2
          end
          if L4_2 then
            L5_2 = Config
            L5_2 = L5_2.Framework
            if "qbcore" == L5_2 then
              L5_2 = L0_2.job
              L5_2 = L5_2.onduty
              if L5_2 then
                goto lbl_64
              end
            end
            if L4_2 then
              L5_2 = exports
              L5_2 = L5_2.origen_police
              L6_2 = L5_2
              L5_2 = L5_2.IsOnDuty
              L5_2 = L5_2(L6_2)
              if L5_2 then
                goto lbl_64
              end
            end
          end
          L5_2 = false
          ::lbl_64::
          L6_2 = FW_TriggerCallback
          L7_2 = "origen_police:callback:GetEvidences"
          function L8_2(A0_3)
            local L1_3, L2_3, L3_3
            L1_3 = nil
            L2_3 = Citizen
            L2_3 = L2_3.CreateThread
            function L3_3()
              local L0_4, L1_4, L2_4, L3_4, L4_4, L5_4, L6_4, L7_4, L8_4, L9_4, L10_4, L11_4, L12_4, L13_4, L14_4, L15_4, L16_4, L17_4, L18_4, L19_4, L20_4, L21_4, L22_4, L23_4, L24_4, L25_4
              L0_4 = A0_3
              L6_1 = L0_4
              while true do
                L0_4 = L2_2
                L1_4 = GetHashKey
                L2_4 = "weapon_flashlight"
                L1_4 = L1_4(L2_4)
                if L0_4 ~= L1_4 then
                  break
                end
                L0_4 = 1000
                L1_4 = GetSelectedPedWeapon
                L2_4 = PlayerPedId
                L2_4, L3_4, L4_4, L5_4, L6_4, L7_4, L8_4, L9_4, L10_4, L11_4, L12_4, L13_4, L14_4, L15_4, L16_4, L17_4, L18_4, L19_4, L20_4, L21_4, L22_4, L23_4, L24_4, L25_4 = L2_4()
                L1_4 = L1_4(L2_4, L3_4, L4_4, L5_4, L6_4, L7_4, L8_4, L9_4, L10_4, L11_4, L12_4, L13_4, L14_4, L15_4, L16_4, L17_4, L18_4, L19_4, L20_4, L21_4, L22_4, L23_4, L24_4, L25_4)
                L2_2 = L1_4
                L1_4 = IsPlayerFreeAiming
                L2_4 = PlayerId
                L2_4, L3_4, L4_4, L5_4, L6_4, L7_4, L8_4, L9_4, L10_4, L11_4, L12_4, L13_4, L14_4, L15_4, L16_4, L17_4, L18_4, L19_4, L20_4, L21_4, L22_4, L23_4, L24_4, L25_4 = L2_4()
                L1_4 = L1_4(L2_4, L3_4, L4_4, L5_4, L6_4, L7_4, L8_4, L9_4, L10_4, L11_4, L12_4, L13_4, L14_4, L15_4, L16_4, L17_4, L18_4, L19_4, L20_4, L21_4, L22_4, L23_4, L24_4, L25_4)
                if L1_4 then
                  L0_4 = 0
                  L1_4 = GetEntityCoords
                  L2_4 = L3_2
                  L1_4 = L1_4(L2_4)
                  L2_4 = 1
                  L3_4 = L6_1
                  L3_4 = #L3_4
                  L4_4 = 1
                  for L5_4 = L2_4, L3_4, L4_4 do
                    L6_4 = L6_1
                    L6_4 = L6_4[L5_4]
                    if L6_4 then
                      L6_4 = L6_1
                      L6_4 = L6_4[L5_4]
                      L6_4 = L6_4.coords
                      L6_4 = L1_4 - L6_4
                      L6_4 = #L6_4
                      L7_4 = Config
                      L7_4 = L7_4.EvidenceDrawDistance
                      if L6_4 < L7_4 then
                        L6_4 = DrawMarker
                        L7_4 = Config
                        L7_4 = L7_4.Evidences
                        L8_4 = L6_1
                        L8_4 = L8_4[L5_4]
                        L8_4 = L8_4.type
                        L7_4 = L7_4[L8_4]
                        L7_4 = L7_4.sprite
                        L8_4 = L6_1
                        L8_4 = L8_4[L5_4]
                        L8_4 = L8_4.coords
                        L9_4 = 0.0
                        L10_4 = 0.0
                        L11_4 = 0.0
                        L12_4 = 0.0
                        L13_4 = 0.0
                        L14_4 = 0.0
                        L15_4 = 0.2
                        L16_4 = 0.2
                        L17_4 = 0.2
                        L18_4 = Config
                        L18_4 = L18_4.Evidences
                        L19_4 = L6_1
                        L19_4 = L19_4[L5_4]
                        L19_4 = L19_4.type
                        L18_4 = L18_4[L19_4]
                        L18_4 = L18_4.color
                        L18_4 = L18_4.r
                        if not L18_4 then
                          L18_4 = L6_1
                          L18_4 = L18_4[L5_4]
                          L18_4 = L18_4.color
                          L18_4 = L18_4[1]
                        end
                        L19_4 = Config
                        L19_4 = L19_4.Evidences
                        L20_4 = L6_1
                        L20_4 = L20_4[L5_4]
                        L20_4 = L20_4.type
                        L19_4 = L19_4[L20_4]
                        L19_4 = L19_4.color
                        L19_4 = L19_4.g
                        if not L19_4 then
                          L19_4 = L6_1
                          L19_4 = L19_4[L5_4]
                          L19_4 = L19_4.color
                          L19_4 = L19_4[2]
                        end
                        L20_4 = Config
                        L20_4 = L20_4.Evidences
                        L21_4 = L6_1
                        L21_4 = L21_4[L5_4]
                        L21_4 = L21_4.type
                        L20_4 = L20_4[L21_4]
                        L20_4 = L20_4.color
                        L20_4 = L20_4.b
                        if not L20_4 then
                          L20_4 = L6_1
                          L20_4 = L20_4[L5_4]
                          L20_4 = L20_4.color
                          L20_4 = L20_4[3]
                        end
                        L21_4 = 255
                        L22_4 = false
                        L23_4 = false
                        L24_4 = 0
                        L25_4 = true
                        L6_4(L7_4, L8_4, L9_4, L10_4, L11_4, L12_4, L13_4, L14_4, L15_4, L16_4, L17_4, L18_4, L19_4, L20_4, L21_4, L22_4, L23_4, L24_4, L25_4)
                        L6_4 = L6_1
                        L6_4 = L6_4[L5_4]
                        L6_4 = L6_4.count
                        if L6_4 then
                          L6_4 = DrawText3D
                          L7_4 = L6_1
                          L7_4 = L7_4[L5_4]
                          L7_4 = L7_4.coords
                          L7_4 = L7_4.x
                          L8_4 = L6_1
                          L8_4 = L8_4[L5_4]
                          L8_4 = L8_4.coords
                          L8_4 = L8_4.y
                          L9_4 = L6_1
                          L9_4 = L9_4[L5_4]
                          L9_4 = L9_4.coords
                          L9_4 = L9_4.z
                          L9_4 = L9_4 + 0.2
                          L10_4 = "x"
                          L11_4 = L6_1
                          L11_4 = L11_4[L5_4]
                          L11_4 = L11_4.count
                          L10_4 = L10_4 .. L11_4
                          L6_4(L7_4, L8_4, L9_4, L10_4)
                        end
                        L6_4 = L6_1
                        L6_4 = L6_4[L5_4]
                        L6_4 = L6_4.coords
                        L6_4 = L1_4 - L6_4
                        L6_4 = #L6_4
                        L7_4 = 1.5
                        if L6_4 < L7_4 then
                          L6_4 = L5_2
                          if L6_4 then
                            L6_4 = Config
                            L6_4 = L6_4.Translations
                            L6_4 = L6_4.CollectEvidence
                            if L6_4 then
                              goto lbl_158
                            end
                          end
                          L6_4 = Config
                          L6_4 = L6_4.Translations
                          L6_4 = L6_4.ClearEvidence
                          ::lbl_158::
                          L7_4 = Config
                          L7_4 = L7_4.CustomNotify
                          if L7_4 then
                            L7_4 = ShowHelpNotification
                            L8_4 = "E"
                            L9_4 = L6_4
                            L7_4(L8_4, L9_4)
                            L7_4 = true
                            L1_3 = L7_4
                          else
                            L7_4 = Config
                            L7_4 = L7_4.Framework
                            if "qbcore" == L7_4 then
                              L7_4 = L1_3
                              if not L7_4 then
                                L7_4 = exports
                                L7_4 = L7_4["qb-core"]
                                L8_4 = L7_4
                                L7_4 = L7_4.DrawText
                                L9_4 = "[E] - "
                                L10_4 = L6_4
                                L9_4 = L9_4 .. L10_4
                                L10_4 = "left"
                                L7_4(L8_4, L9_4, L10_4)
                                L7_4 = true
                                L1_3 = L7_4
                              end
                            else
                              L7_4 = Framework
                              L7_4 = L7_4.ShowHelpNotification
                              L8_4 = "~INPUT_PICKUP~ "
                              L9_4 = L6_4
                              L8_4 = L8_4 .. L9_4
                              L9_4 = true
                              L7_4(L8_4, L9_4)
                            end
                          end
                          L7_4 = IsControlJustPressed
                          L8_4 = 0
                          L9_4 = 38
                          L7_4 = L7_4(L8_4, L9_4)
                          if L7_4 then
                            L7_4 = L5_2
                            if L7_4 then
                              L7_4 = TriggerServerEvent
                              L8_4 = "origen_police:server:GetEvidence"
                              L9_4 = L5_4
                              L10_4 = L6_1
                              L10_4 = L10_4[L5_4]
                              L7_4(L8_4, L9_4, L10_4)
                              L7_4 = CollectEvidenceAnim
                              L8_4 = L5_4
                              L7_4(L8_4)
                            else
                              L7_4 = TriggerServerEvent
                              L8_4 = "origen_police:server:RemoveEvidence"
                              L9_4 = L5_4
                              L7_4(L8_4, L9_4)
                              L7_4 = CollectEvidenceAnim
                              L8_4 = L5_4
                              L7_4(L8_4)
                            end
                          end
                        end
                      end
                    end
                  end
                else
                  L1_4 = L1_3
                  if L1_4 then
                    L1_4 = HideHelpNotification
                    L1_4()
                    L1_4 = nil
                    L1_3 = L1_4
                  end
                end
                L1_4 = Citizen
                L1_4 = L1_4.Wait
                L2_4 = L0_4
                L1_4(L2_4)
              end
              L0_4 = L1_3
              if L0_4 then
                L0_4 = HideHelpNotification
                L0_4()
                L0_4 = nil
                L1_3 = L0_4
              end
              L0_4 = {}
              L6_1 = L0_4
            end
            L2_3(L3_3)
          end
          L9_2 = GetEntityCoords
          L10_2 = L3_2
          L9_2, L10_2 = L9_2(L10_2)
          L6_2(L7_2, L8_2, L9_2, L10_2)
          L1_2 = 500
      end
      else
        L3_2 = GetHashKey
        L4_2 = "weapon_flashlight"
        L3_2 = L3_2(L4_2)
        if L2_2 ~= L3_2 then
          L3_2 = false
          L9_1 = L3_2
        end
        L1_2 = 3000
      end
      L3_2 = L7_1
      if not L3_2 then
        break
      end
      L3_2 = Wait
      L4_2 = L1_2
      L3_2(L4_2)
    end
    L2_2 = L7_1
    if not L2_2 then
      break
    end
  end
end
function L11_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  L1_2 = RequestAnimDict
  L2_2 = "pickup_object"
  L1_2(L2_2)
  while true do
    L1_2 = HasAnimDictLoaded
    L2_2 = "pickup_object"
    L1_2 = L1_2(L2_2)
    if L1_2 then
      break
    end
    L1_2 = Citizen
    L1_2 = L1_2.Wait
    L2_2 = 7
    L1_2(L2_2)
  end
  L1_2 = RequestAnimDict
  L2_2 = "pickup_object"
  L1_2(L2_2)
  while true do
    L1_2 = HasAnimDictLoaded
    L2_2 = "pickup_object"
    L1_2 = L1_2(L2_2)
    if L1_2 then
      break
    end
    L1_2 = Citizen
    L1_2 = L1_2.Wait
    L2_2 = 7
    L1_2(L2_2)
  end
  L1_2 = L6_1
  L1_2 = L1_2[A0_2]
  if L1_2 then
    L1_2 = L6_1
    L1_2 = L1_2[A0_2]
    L1_2 = L1_2.type
    if L1_2 then
      L1_2 = Config
      L1_2 = L1_2.Evidences
      L2_2 = L6_1
      L2_2 = L2_2[A0_2]
      L2_2 = L2_2.type
      L1_2 = L1_2[L2_2]
      L1_2 = L1_2.anim
      if L1_2 then
        L1_2 = TaskPlayAnim
        L2_2 = PlayerPedId
        L2_2 = L2_2()
        L3_2 = "pickup_object"
        L4_2 = "pickup_low"
        L5_2 = 8.0
        L6_2 = -8.0
        L7_2 = -1
        L8_2 = 1
        L9_2 = 0
        L10_2 = false
        L11_2 = false
        L12_2 = false
        L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
      end
  end
  else
    L1_2 = print
    L2_2 = "Something went wrong with the evidence type, DATA: "
    L3_2 = json
    L3_2 = L3_2.encode
    L4_2 = L6_1
    L4_2 = L4_2[A0_2]
    L3_2 = L3_2(L4_2)
    L4_2 = "ID: "
    L5_2 = A0_2
    L1_2(L2_2, L3_2, L4_2, L5_2)
  end
  L1_2 = GetGameTimer
  L1_2 = L1_2()
  L2_2 = L6_1
  L2_2 = L2_2[A0_2]
  L2_2 = L2_2.coords
  while true do
    L3_2 = SetIkTarget
    L4_2 = PlayerPedId
    L4_2 = L4_2()
    L5_2 = 4
    L6_2 = nil
    L7_2 = nil
    L8_2 = L2_2.x
    L9_2 = L2_2.y
    L10_2 = L2_2.z
    L11_2 = 0.0
    L12_2 = 50
    L13_2 = 200
    L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
    L3_2 = Wait
    L4_2 = 0
    L3_2(L4_2)
    L3_2 = GetGameTimer
    L3_2 = L3_2()
    L3_2 = L3_2 - L1_2
    L4_2 = 1000
    if L3_2 > L4_2 then
      break
    end
  end
  L3_2 = ClearPedTasks
  L4_2 = PlayerPedId
  L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2 = L4_2()
  L3_2(L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
end
CollectEvidenceAnim = L11_1
L11_1 = RegisterNetEvent
L12_1 = "origen_police:client:OnPlayerLoaded"
function L13_1()
  local L0_2, L1_2
  L0_2 = Config
  L0_2 = L0_2.EvidenceSystem
  if not L0_2 then
    return
  end
  L0_2 = L10_1
  L0_2()
end
L11_1(L12_1, L13_1)
L11_1 = RegisterNetEvent
L12_1 = "origen_police:client:rmevidence"
function L13_1(A0_2)
  local L1_2
  L1_2 = L6_1
  L1_2 = L1_2[A0_2]
  if L1_2 then
    L1_2 = L6_1
    L1_2[A0_2] = nil
  end
end
L11_1(L12_1, L13_1)
function L11_1(A0_2, A1_2, A2_2, A3_2, A4_2)
  local L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2
  L5_2 = World3dToScreen2d
  L6_2 = A0_2
  L7_2 = A1_2
  L8_2 = A2_2
  L5_2, L6_2, L7_2 = L5_2(L6_2, L7_2, L8_2)
  L8_2 = GetGameplayCamCoords
  L8_2 = L8_2()
  L9_2 = vector3
  L10_2 = A0_2
  L11_2 = A1_2
  L12_2 = A2_2
  L9_2 = L9_2(L10_2, L11_2, L12_2)
  L9_2 = L9_2 - L8_2
  L9_2 = #L9_2
  L10_2 = string
  L10_2 = L10_2.len
  L11_2 = A3_2
  L10_2 = L10_2(L11_2)
  L10_2 = L10_2 / 370
  if not A4_2 then
    A4_2 = 4
  end
  L11_2 = SetTextScale
  L12_2 = 0.38
  L13_2 = 0.38
  L11_2(L12_2, L13_2)
  L11_2 = SetTextFont
  L12_2 = A4_2
  L11_2(L12_2)
  L11_2 = SetTextProportional
  L12_2 = 1
  L11_2(L12_2)
  L11_2 = SetTextColour
  L12_2 = 255
  L13_2 = 255
  L14_2 = 255
  L15_2 = 215
  L11_2(L12_2, L13_2, L14_2, L15_2)
  L11_2 = SetTextEntry
  L12_2 = "STRING"
  L11_2(L12_2)
  L11_2 = SetTextCentre
  L12_2 = true
  L11_2(L12_2)
  L11_2 = AddTextComponentString
  L12_2 = A3_2
  L11_2(L12_2)
  L11_2 = SetTextDropshadow
  L12_2 = L10_2
  L13_2 = 255
  L14_2 = 0
  L15_2 = 0
  L16_2 = 255
  L11_2(L12_2, L13_2, L14_2, L15_2, L16_2)
  L11_2 = DrawText
  L12_2 = L6_2
  L13_2 = L7_2
  L11_2(L12_2, L13_2)
end
DrawText3D = L11_1
L11_1 = false
function L12_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L1_2 = Config
  L1_2 = L1_2.WeaponSilencierBlockShootAlert
  if L1_2 then
    L1_2 = IsPedCurrentWeaponSilenced
    L2_2 = PlayerPedId
    L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2 = L2_2()
    L1_2 = L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
    if L1_2 then
      return
    end
  end
  L1_2 = IsWeaponBlacklisted
  L2_2 = A0_2.name
  L1_2 = L1_2(L2_2)
  if L1_2 then
    return
  end
  L1_2 = L11_1
  if L1_2 then
    L1_2 = GetGameTimer
    L1_2 = L1_2()
    L2_2 = L11_1
    L1_2 = L1_2 - L2_2
    L2_2 = Config
    L2_2 = L2_2.ShootAlertCooldown
    if not (L1_2 > L2_2) then
      goto lbl_156
    end
  end
  L1_2 = IsBeingSeen
  L1_2 = L1_2()
  if L1_2 then
    L1_2 = CanOpenTablet
    L2_2 = FW_GetPlayerData
    L3_2 = false
    L2_2 = L2_2(L3_2)
    L2_2 = L2_2.job
    L2_2 = L2_2.name
    L1_2 = L1_2(L2_2)
    L1_2 = L1_2[1]
    if not L1_2 then
      L1_2 = GetGameTimer
      L1_2 = L1_2()
      L11_1 = L1_2
      L1_2 = GetWeaponAmmoType
      L2_2 = A0_2.name
      L1_2 = L1_2(L2_2)
      if L1_2 and "AMMO_BALL" ~= L1_2 and "AMMO_PETROLCAN" ~= L1_2 then
        L2_2 = L4_1
        if not L2_2 then
          L2_2 = math
          L2_2 = L2_2.randomseed
          L3_2 = GetGameTimer
          L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2 = L3_2()
          L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
          L2_2 = math
          L2_2 = L2_2.random
          L3_2 = -2
          L4_2 = 1
          L2_2 = L2_2(L3_2, L4_2)
          L2_2 = L2_2 > 0
          L3_2 = math
          L3_2 = L3_2.random
          L4_2 = 0
          L5_2 = 1
          L3_2 = L3_2(L4_2, L5_2)
          L3_2 = L3_2 > 0
          L4_2 = TriggerServerEvent
          L5_2 = "SendAlert:police"
          L6_2 = {}
          L7_2 = GetEntityCoords
          L8_2 = PlayerPedId
          L8_2, L9_2 = L8_2()
          L7_2 = L7_2(L8_2, L9_2)
          L6_2.coords = L7_2
          L7_2 = Config
          L7_2 = L7_2.Translations
          L7_2 = L7_2["215"]
          L6_2.title = L7_2
          L6_2.type = "215"
          L6_2.job = "police"
          L7_2 = {}
          if L2_2 then
            L8_2 = A0_2.label
            if L8_2 then
              goto lbl_105
            end
          end
          L8_2 = nil
          ::lbl_105::
          L7_2.weapon = L8_2
          if L3_2 then
            L8_2 = GetHashKey
            L9_2 = "AMMO_PISTOL"
            L8_2 = L8_2(L9_2)
            if L1_2 ~= L8_2 then
              L8_2 = GetHashKey
              L9_2 = "AMMO_SMG"
              L8_2 = L8_2(L9_2)
              if L1_2 ~= L8_2 then
                L8_2 = GetHashKey
                L9_2 = "AMMO_FLARE"
                L8_2 = L8_2(L9_2)
                if L1_2 ~= L8_2 then
                  goto lbl_127
                end
              end
            end
            L8_2 = Translations
            L8_2 = L8_2.LowCaliber
            if L8_2 then
              goto lbl_150
            end
            ::lbl_127::
            L8_2 = GetHashKey
            L9_2 = "AMMO_SHOTGUN"
            L8_2 = L8_2(L9_2)
            if L1_2 == L8_2 then
              L8_2 = Translations
              L8_2 = L8_2.ShotgunCaliber
              if L8_2 then
                goto lbl_150
              end
            end
            L8_2 = GetHashKey
            L9_2 = "AMMO_RIFLE"
            L8_2 = L8_2(L9_2)
            if L1_2 == L8_2 then
              L8_2 = Translations
              L8_2 = L8_2.MediumCaliber
              if L8_2 then
                goto lbl_150
              end
            end
            L8_2 = Translations
            L8_2 = L8_2.HighCaliber
            if L8_2 then
              goto lbl_150
            end
          end
          L8_2 = nil
          ::lbl_150::
          L7_2.ammotype = L8_2
          L6_2.metadata = L7_2
          L7_2 = Config
          L7_2 = L7_2.PoliceJobCategory
          L6_2.job = L7_2
          L4_2(L5_2, L6_2)
        end
      end
    end
  end
  ::lbl_156::
end
SendShootAlert = L12_1
L12_1 = RegisterNetEvent
L13_1 = "origen_police:client:robVehicle"
function L14_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  L0_2 = PlayerPedId
  L0_2 = L0_2()
  L1_2 = IsPedInAnyVehicle
  L2_2 = L0_2
  L3_2 = false
  L1_2 = L1_2(L2_2, L3_2)
  if L1_2 then
    L1_2 = GetEntityCoords
    L2_2 = L0_2
    L3_2 = true
    L1_2 = L1_2(L2_2, L3_2)
    L2_2 = Citizen
    L2_2 = L2_2.InvokeNative
    L3_2 = 3365332906397525184
    L4_2 = L1_2.x
    L5_2 = L1_2.y
    L6_2 = L1_2.z
    L7_2 = Citizen
    L7_2 = L7_2.PointerValueInt
    L7_2 = L7_2()
    L8_2 = Citizen
    L8_2 = L8_2.PointerValueInt
    L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L8_2()
    L2_2, L3_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L4_2 = {}
    if 0 ~= L2_2 then
      L5_2 = table
      L5_2 = L5_2.insert
      L6_2 = L4_2
      L7_2 = GetStreetNameFromHashKey
      L8_2 = L2_2
      L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L7_2(L8_2)
      L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    end
    if 0 ~= L3_2 then
      L5_2 = table
      L5_2 = L5_2.insert
      L6_2 = L4_2
      L7_2 = GetStreetNameFromHashKey
      L8_2 = L3_2
      L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L7_2(L8_2)
      L5_2(L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    end
    L5_2 = GetVehiclePedIsIn
    L6_2 = L0_2
    L7_2 = false
    L5_2 = L5_2(L6_2, L7_2)
    L6_2 = GetDisplayNameFromVehicleModel
    L7_2 = GetEntityModel
    L8_2 = L5_2
    L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2 = L7_2(L8_2)
    L6_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
    L7_2 = GetVehicleColor
    L8_2 = L5_2
    L7_2, L8_2, L9_2 = L7_2(L8_2)
    L10_2 = GetEntityCoords
    L11_2 = L0_2
    L10_2 = L10_2(L11_2)
    L11_2 = TriggerServerEvent
    L12_2 = "SendAlert:police"
    L13_2 = {}
    L14_2 = GetEntityCoords
    L15_2 = PlayerPedId
    L15_2, L16_2, L17_2, L18_2 = L15_2()
    L14_2 = L14_2(L15_2, L16_2, L17_2, L18_2)
    L13_2.coords = L14_2
    L14_2 = Config
    L14_2 = L14_2.Translations
    L14_2 = L14_2.VehicleRob
    L13_2.title = L14_2
    L13_2.type = "RADARS"
    L14_2 = Config
    L14_2 = L14_2.Translations
    L14_2 = L14_2.VehicleRobDesc
    L13_2.message = L14_2
    L14_2 = {}
    L14_2.model = L6_2
    L15_2 = {}
    L16_2 = GetVehicleColor
    L17_2 = GetVehiclePedIsIn
    L18_2 = PlayerPedId
    L18_2 = L18_2()
    L17_2, L18_2 = L17_2(L18_2)
    L16_2, L17_2, L18_2 = L16_2(L17_2, L18_2)
    L15_2[1] = L16_2
    L15_2[2] = L17_2
    L15_2[3] = L18_2
    L14_2.color = L15_2
    L15_2 = GetVehiclePlate
    L16_2 = GetVehiclePedIsIn
    L17_2 = PlayerPedId
    L17_2, L18_2 = L17_2()
    L16_2, L17_2, L18_2 = L16_2(L17_2, L18_2)
    L15_2 = L15_2(L16_2, L17_2, L18_2)
    L14_2.plate = L15_2
    L13_2.metadata = L14_2
    L14_2 = Config
    L14_2 = L14_2.PoliceJobCategory
    L13_2.job = L14_2
    L11_2(L12_2, L13_2)
  end
end
L12_1(L13_1, L14_1)
L12_1 = Config
L12_1 = L12_1.Commands
L12_1 = L12_1.ForceVehicle
if L12_1 then
  L12_1 = Config
  L12_1 = L12_1.Commands
  L12_1 = L12_1.ForceVehicle
  L12_1 = L12_1.cmd
  if "" ~= L12_1 then
    L12_1 = RegisterCommand
    L13_1 = Config
    L13_1 = L13_1.Commands
    L13_1 = L13_1.ForceVehicle
    L13_1 = L13_1.cmd
    function L14_1()
      local L0_2, L1_2
      L0_2 = TriggerEvent
      L1_2 = "origen_police:client:robVehicle"
      L0_2(L1_2)
    end
    L12_1(L13_1, L14_1)
  end
end
L12_1 = exports
L13_1 = "RobVehicle"
function L14_1()
  local L0_2, L1_2
  L0_2 = TriggerEvent
  L1_2 = "origen_police:client:robVehicle"
  L0_2(L1_2)
end
L12_1(L13_1, L14_1)
L12_1 = exports
L13_1 = "DisableAutoAlerts"
function L14_1(A0_2)
  local L1_2
  L4_1 = A0_2
end
L12_1(L13_1, L14_1)
