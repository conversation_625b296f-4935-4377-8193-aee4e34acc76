{"manifest": {"author": {"name": "<PERSON><PERSON>", "url": "http://www.joyent.com"}, "name": "util", "description": "Node.JS util module", "keywords": ["util"], "version": "0.10.3", "homepage": "https://github.com/defunctzombie/node-util", "repository": {"type": "git", "url": "git://github.com/defunctzombie/node-util"}, "main": "./util.js", "scripts": {"test": "node test/node/*.js && zuul test/browser/*.js"}, "dependencies": {"inherits": "2.0.1"}, "license": "MIT", "devDependencies": {"zuul": "~1.0.9"}, "browser": {"./support/isBuffer.js": "./support/isBufferBrowser.js"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-util-0.10.3-7afb1afe50805246489e3db7fe0ed379336ac0f9-integrity\\node_modules\\util\\package.json", "readmeFilename": "README.md", "readme": "# util\n\n[![Build Status](https://travis-ci.org/defunctzombie/node-util.png?branch=master)](https://travis-ci.org/defunctzombie/node-util)\n\nnode.js [util](http://nodejs.org/api/util.html) module as a module\n\n## install via [npm](npmjs.org)\n\n```shell\nnpm install util\n```\n\n## browser support\n\nThis module also works in modern browsers. If you need legacy browser support you will need to polyfill ES5 features.\n", "licenseText": "Copyright Joyent, Inc. and other Node contributors. All rights reserved.\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/util/-/util-0.10.3.tgz#7afb1afe50805246489e3db7fe0ed379336ac0f9", "type": "tarball", "reference": "https://registry.yarnpkg.com/util/-/util-0.10.3.tgz", "hash": "7afb1afe50805246489e3db7fe0ed379336ac0f9", "integrity": "sha1-evsa/lCAUkZInj23/g7TeTNqwPk=", "registry": "npm", "packageName": "util", "cacheIntegrity": "sha512-5KiHfsmkqacuKjkRkdV7SsfDJ2EGiPsK92s2MhNSY0craxjTdKTtqKsJaCWp4LW33ZZ0OPUv1WO/TFvNQRiQxQ== sha1-evsa/lCAUkZInj23/g7TeTNqwPk="}, "registry": "npm", "hash": "7afb1afe50805246489e3db7fe0ed379336ac0f9"}