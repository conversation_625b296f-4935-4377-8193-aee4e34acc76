{"manifest": {"name": "koa-convert", "version": "1.2.0", "keywords": ["koa", "middleware", "convert"], "description": "convert koa legacy generator-based middleware to promise-based middleware", "repository": {"type": "git", "url": "git+https://github.com/gyson/koa-convert.git"}, "main": "index.js", "scripts": {"test": "standard && mocha test.js"}, "author": {"name": "gyson", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/gyson/koa-convert/issues"}, "homepage": "https://github.com/gyson/koa-convert#readme", "dependencies": {"co": "^4.6.0", "koa-compose": "^3.0.0"}, "devDependencies": {"koa": "^2.0.0-alpha.2", "koa-v1": "^1.0.0", "mocha": "^2.3.3", "standard": "^5.3.1", "supertest": "^1.1.0"}, "engines": {"node": ">= 4"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-koa-convert-1.2.0-da40875df49de0539098d1700b50820cebcd21d0-integrity\\node_modules\\koa-convert\\package.json", "readmeFilename": "README.md", "readme": "\n# koa-convert\n\n[![npm version](https://img.shields.io/npm/v/koa-convert.svg)](https://npmjs.org/package/koa-convert)\n[![build status](https://travis-ci.org/gyson/koa-convert.svg)](https://travis-ci.org/gyson/koa-convert)\n\nConvert koa legacy ( 0.x & 1.x ) generator middleware to modern promise middleware ( 2.x ).\n\nIt could also convert modern promise middleware back to legacy generator middleware ( useful to help modern middleware support koa 0.x or 1.x ).\n\n## Note\n\nIt should be able to convert any legacy generator middleware to modern promise middleware ( or convert it back ).\n\nPlease let me know ( send a issue ) if you fail to do so.\n\n## Installation\n\n```\n$ npm install koa-convert\n```\n\n## Usage\n\n```js\nconst Koa = require('koa') // koa v2.x\nconst convert = require('koa-convert')\nconst app = new Koa()\n\napp.use(modernMiddleware)\n\napp.use(convert(legacyMiddleware))\n\napp.use(convert.compose(legacyMiddleware, modernMiddleware))\n\nfunction * legacyMiddleware (next) {\n  // before\n  yield next\n  // after\n}\n\nfunction modernMiddleware (ctx, next) {\n  // before\n  return next().then(() => {\n    // after\n  })\n}\n```\n\n## Distinguish legacy and modern middleware\n\nIn koa 0.x and 1.x ( without experimental flag ), `app.use` has an assertion that all ( legacy ) middleware must be generator function and it's tested with `fn.constructor.name == 'GeneratorFunction'` at [here](https://github.com/koajs/koa/blob/7fe29d92f1e826d9ce36029e1b9263b94cba8a7c/lib/application.js#L105).\n\nTherefore, we can distinguish legacy and modern middleware with `fn.constructor.name == 'GeneratorFunction'`.\n\n## Migration\n\n`app.use(legacyMiddleware)` is everywhere in 0.x and 1.x and it would be painful to manually change all of them to `app.use(convert(legacyMiddleware))`.\n\nYou can use following snippet to make migration easier.\n\n```js\nconst _use = app.use\napp.use = x => _use.call(app, convert(x))\n```\n\nThe above snippet will override `app.use` method and implicitly convert all legacy generator middleware to modern promise middleware.\n\nTherefore, you can have both `app.use(modernMiddleware)` and `app.use(legacyMiddleware)` and your 0.x or 1.x should work without modification.\n\nComplete example:\n\n```js\nconst Koa = require('koa') // v2.x\nconst convert = require('koa-convert')\nconst app = new Koa()\n\n// ---------- override app.use method ----------\n\nconst _use = app.use\napp.use = x => _use.call(app, convert(x))\n\n// ---------- end ----------\n\napp.use(modernMiddleware)\n\n// this will be converted to modern promise middleware implicitly\napp.use(legacyMiddleware)\n\nfunction * legacyMiddleware (next) {\n  // before\n  yield next\n  // after\n}\n\nfunction modernMiddleware (ctx, next) {\n  // before\n  return next().then(() => {\n    // after\n  })\n}\n```\n\n## API\n\n#### `convert()`\n\nConvert legacy generator middleware to modern promise middleware.\n\n```js\nmodernMiddleware = convert(legacyMiddleware)\n```\n\n#### `convert.compose()`\n\nConvert and compose multiple middleware (could mix legacy and modern ones) and return modern promise middleware.\n\n```js\ncomposedModernMiddleware = convert.compose(legacyMiddleware, modernMiddleware)\n// or\ncomposedModernMiddleware = convert.compose([legacyMiddleware, modernMiddleware])\n```\n\n#### `convert.back()`\n\nConvert modern promise middleware back to legacy generator middleware.\n\nThis is useful to help modern promise middleware support koa 0.x or 1.x.\n\n```js\nlegacyMiddleware = convert.back(modernMiddleware)\n```\n\n## License\n\nMIT\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015 yunsong\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/koa-convert/-/koa-convert-1.2.0.tgz#da40875df49de0539098d1700b50820cebcd21d0", "type": "tarball", "reference": "https://registry.yarnpkg.com/koa-convert/-/koa-convert-1.2.0.tgz", "hash": "da40875df49de0539098d1700b50820cebcd21d0", "integrity": "sha1-2kCHXfSd4FOQmNFwC1CCDOvNIdA=", "registry": "npm", "packageName": "koa-convert", "cacheIntegrity": "sha512-K9XqjmEDStGX09v3oxR7t5uPRy0jqJdvodHa6wxWTHrTfDq0WUNnYTOOUZN6g8OM8oZQXprQASbiIXG2Ez8ehA== sha1-2kCHXfSd4FOQmNFwC1CCDOvNIdA="}, "registry": "npm", "hash": "da40875df49de0539098d1700b50820cebcd21d0"}