{"name": "koa", "version": "2.13.1", "description": "Koa web app framework", "main": "lib/application.js", "exports": {".": {"require": "./lib/application.js", "import": "./dist/koa.mjs"}, "./": "./"}, "scripts": {"test": "egg-bin test test", "test-cov": "egg-bin cov test", "lint": "eslint benchmarks lib test", "bench": "make -C benchmarks", "authors": "git log --format='%aN <%aE>' | sort -u > AUTHORS", "build": "gen-esm-wrapper . ./dist/koa.mjs", "prepare": "npm run build"}, "repository": "koajs/koa", "keywords": ["web", "app", "http", "application", "framework", "middleware", "rack"], "license": "MIT", "dependencies": {"accepts": "^1.3.5", "cache-content-type": "^1.0.0", "content-disposition": "~0.5.2", "content-type": "^1.0.4", "cookies": "~0.8.0", "debug": "~3.1.0", "delegates": "^1.0.0", "depd": "^2.0.0", "destroy": "^1.0.4", "encodeurl": "^1.0.2", "escape-html": "^1.0.3", "fresh": "~0.5.2", "http-assert": "^1.3.0", "http-errors": "^1.6.3", "is-generator-function": "^1.0.7", "koa-compose": "^4.1.0", "koa-convert": "^1.2.0", "on-finished": "^2.3.0", "only": "~0.0.2", "parseurl": "^1.3.2", "statuses": "^1.5.0", "type-is": "^1.6.16", "vary": "^1.1.2"}, "devDependencies": {"egg-bin": "^4.13.0", "eslint": "^6.5.1", "eslint-config-koa": "^2.0.0", "eslint-config-standard": "^14.1.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-node": "^10.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "gen-esm-wrapper": "^1.0.6", "mm": "^2.5.0", "supertest": "^3.1.0"}, "engines": {"node": "^4.8.4 || ^6.10.1 || ^7.10.1 || >= 8.1.4"}, "files": ["dist", "lib"]}