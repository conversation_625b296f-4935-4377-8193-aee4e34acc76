{"manifest": {"name": "webpack-sources", "version": "1.4.3", "description": "Source code handling classes for webpack", "main": "./lib/index.js", "scripts": {"pretest": "npm run lint && npm run beautify-lint", "test": "mocha --full-trace --check-leaks", "travis": "npm run cover -- --report lcovonly", "lint": "eslint lib test", "beautify-lint": "beautify-lint lib/**.js test/**.js", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "dependencies": {"source-list-map": "^2.0.0", "source-map": "~0.6.1"}, "devDependencies": {"beautify-lint": "^1.0.3", "codecov.io": "^0.1.6", "coveralls": "^2.11.6", "eslint": "^4.18.2", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "js-beautify": "^1.5.10", "mocha": "^3.4.2", "should": "^11.2.1", "sourcemap-validator": "^1.1.0"}, "files": ["lib/"], "repository": {"type": "git", "url": "git+https://github.com/webpack/webpack-sources.git"}, "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "homepage": "https://github.com/webpack/webpack-sources#readme", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-webpack-sources-1.4.3-eedd8ec0b928fbf1cbfe994e22d2d890f330a933-integrity\\node_modules\\webpack-sources\\package.json", "readmeFilename": "README.md", "readme": "# webpack-sources\n\nContains multiple classes which represent a `Source`. A `Source` can be asked for source code, size, source map and hash.\n\n## `Source`\n\nBase class for all sources.\n\n### Public methods\n\nAll methods should be considered as expensive as they may need to do computations.\n\n#### `source`\n\n``` js\nSource.prototype.source() -> String | ArrayBuffer\n```\n\nReturns the represented source code as string.\n\n#### `size`\n\n``` js\nSource.prototype.size() -> Number\n```\n\nReturns the size in chars of the represented source code.\n\n#### `map`\n\n``` js\nSource.prototype.map(options: Object) -> Object | null\n```\n\nReturns the SourceMap of the represented source code as JSON. May return `null` if no SourceMap is available.\n\nThe `options` object can contain the following keys:\n\n* `columns: Boolean` (default `true`): If set to false the implementation may omit mappings for columns.\n* `module: Boolean` (default `true`): If set to false the implementation may omit inner mappings for modules.\n\n#### `sourceAndMap`\n\n``` js\nSource.prototype.sourceAndMap(options: Object) -> {\n\tsource: String,\n\tmap: Object\n}\n```\n\nReturns both, source code (like `Source.prototype.source()` and SourceMap (like `Source.prototype.map()`). This method could have better performance than calling `source()` and `map()` separately.\n\nSee `map()` for `options`.\n\n#### `updateHash`\n\n``` js\nSource.prototype.updateHash(hash: Hash) -> void\n```\n\nUpdates the provided `Hash` object with the content of the represented source code. (`Hash` is an object with an `update` method, which is called with string values)\n\n#### `node` (optional)\n\n``` js\nSource.prototype.node(options: Object) -> SourceNode\n```\n\nThis is an optional method. It may be `null` if not implemented.\n\nReturns a `SourceNode` (see source-map library) for the represented source code.\n\nSee `map()` for `options`.\n\n#### `listNode` (optional)\n\n``` js\nSource.prototype.listNode(options: Object) -> SourceNode\n```\n\nThis is an optional method. It may be `null` if not implemented.\n\nReturns a `SourceListMap` (see source-list-map library) for the represented source code.\n\nSee `map()` for `options`.\n\n## `RawSource`\n\nRepresents source code without SourceMap.\n\n``` js\nnew RawSource(sourceCode: String)\n```\n\n## `OriginalSource`\n\nRepresents source code, which is a copy of the original file.\n\n``` js\nnew OriginalSource(\n\tsourceCode: String,\n\tname: String\n)\n```\n\n* `sourceCode`: The source code.\n* `name`: The filename of the original source code.\n\nOriginalSource tries to create column mappings if requested, by splitting the source code at typical statement borders (`;`, `{`, `}`).\n\n## `SourceMapSource`\n\nRepresents source code with SourceMap, optionally having an additional SourceMap for the original source.\n\n``` js\nnew SourceMapSource(\n\tsourceCode: String,\n\tname: String,\n\tsourceMap: Object | String,\n\toriginalSource?: String,\n\tinnerSourceMap?: Object | String,\n\tremoveOriginalSource?: boolean\n)\n```\n\n* `sourceCode`: The source code.\n* `name`: The filename of the original source code.\n* `sourceMap`: The SourceMap for the source code.\n* `originalSource`: The source code of the original file. Can be omitted if the `sourceMap` already contains the original source code.\n* `innerSourceMap`: The SourceMap for the `originalSource`/`name`.\n* `removeOriginalSource`: Removes the source code for `name` from the final map, keeping only the deeper mappings for that file.\n\nThe `SourceMapSource` supports \"identity\" mappings for the `innerSourceMap`.\nWhen original source matches generated source for a mapping it's assumed to be mapped char by char allowing to keep finer mappings from `sourceMap`.\n\n## `LineToLineMappedSource`\n\nRepresents source code, which is mapped line by line to the original file.\n\n``` js\nnew LineToLineMappedSource(\n\tsourceCode: String,\n\tname: String,\n\toriginalSource: String\n)\n```\n\n* `sourceCode`: The source code.\n* `name`: The filename of the original source code.\n* `originalSource`: The original source code.\n\n## `CachedSource`\n\nDecorates a `Source` and caches returned results of `map`, `source`, `size` and `sourceAndMap` in memory. Every other operation is delegated to the wrapped `Source`.\n\n``` js\nnew CachedSource(source: Source)\n```\n\n## `PrefixSource`\n\nPrefix every line of the decorated `Source` with a provided string.\n\n``` js\nnew PrefixSource(\n\tprefix: String,\n\tsource: Source\n)\n```\n\n## `ConcatSource`\n\nConcatenate multiple `Source`s or strings to a single source.\n\n``` js\nnew ConcatSource(\n\t...items?: Source | String\n)\n```\n\n### Public methods\n\n#### `add`\n\n``` js\nConcatSource.prototype.add(item: Source | String)\n```\n\nAdds an item to the source.\n\n## `ReplaceSource`\n\nDecorates a `Source` with replacements and insertions of source code.\n\nThe `ReplaceSource` supports \"identity\" mappings for child source.\nWhen original source matches generated source for a mapping it's assumed to be mapped char by char allowing to split mappings at replacements/insertions.\n\n### Public methods\n\n#### `replace`\n\n``` js\nReplaceSource.prototype.replace(\n\tstart: Number,\n\tend: Number,\n\treplacement: String\n)\n```\n\nReplaces chars from `start` (0-indexed, inclusive) to `end` (0-indexed, inclusive) with `replacement`.\n\nLocations represents locations in the original source and are not influenced by other replacements or insertions.\n\n#### `insert`\n\n``` js\nReplaceSource.prototype.insert(\n\tpos: Number,\n\tinsertion: String\n)\n```\n\nInserts the `insertion` before char `pos` (0-indexed).\n\nLocation represents location in the original source and is not influenced by other replacements or insertions.\n\n#### `original`\n\nGet decorated `Source`.\n\n", "licenseText": "MIT License\n\nCopyright (c) 2017 JS Foundation and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/webpack-sources/-/webpack-sources-1.4.3.tgz#eedd8ec0b928fbf1cbfe994e22d2d890f330a933", "type": "tarball", "reference": "https://registry.yarnpkg.com/webpack-sources/-/webpack-sources-1.4.3.tgz", "hash": "eedd8ec0b928fbf1cbfe994e22d2d890f330a933", "integrity": "sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ==", "registry": "npm", "packageName": "webpack-sources", "cacheIntegrity": "sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ== sha1-7t2OwLko+/HL/plOItLYkPMwqTM="}, "registry": "npm", "hash": "eedd8ec0b928fbf1cbfe994e22d2d890f330a933"}