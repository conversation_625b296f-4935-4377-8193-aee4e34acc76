{"manifest": {"name": "builtin-status-codes", "main": "index.js", "browser": "browser.js", "version": "3.0.0", "description": "The map of HTTP status codes from the builtin http module", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/bendrucker/builtin-status-codes.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "bendrucker.me"}, "scripts": {"test": "standard && tape test.js", "build": "node build.js"}, "keywords": ["http", "status", "codes", "builtin", "map"], "devDependencies": {"tape": "^4.0.0", "standard": "^4.0.0"}, "files": ["index.js", "browser.js", "build.js"], "standard": {"ignore": ["browser.js"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-builtin-status-codes-3.0.0-85982878e21b98e1c66425e03d0174788f569ee8-integrity\\node_modules\\builtin-status-codes\\package.json", "readmeFilename": "readme.md", "readme": "# builtin-status-codes [![Build Status](https://travis-ci.org/bendrucker/builtin-status-codes.svg?branch=master)](https://travis-ci.org/bendrucker/builtin-status-codes)\n\n> The map of HTTP status codes from the builtin http module. Exposes the latest directly from `http` in Node, with a zero-dependencies version for the browser.\n\n\n## Install\n\n```\n$ npm install --save builtin-status-codes\n```\n\n\n## Usage\n\n```js\nvar codes = require('builtin-status-codes')\ncodes[100]\n//=> Continue\n```\n\n## Build\n\nTo create a new browser build:\n\n```sh\n$ npm run build\n```\n\n## License\n\nMIT © [<PERSON>](http://bendrucker.me)\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) <PERSON> <<EMAIL>> (bendrucker.me)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPY<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz#85982878e21b98e1c66425e03d0174788f569ee8", "type": "tarball", "reference": "https://registry.yarnpkg.com/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz", "hash": "85982878e21b98e1c66425e03d0174788f569ee8", "integrity": "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=", "registry": "npm", "packageName": "builtin-status-codes", "cacheIntegrity": "sha512-HpGFw18DgFWlncDfjTa2rcQ4W88O1mC8e8yZ2AvQY5KDaktSTwo+KRf6nHK6FRI5FyRyb/5T6+TSxfP7QyGsmQ== sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug="}, "registry": "npm", "hash": "85982878e21b98e1c66425e03d0174788f569ee8"}