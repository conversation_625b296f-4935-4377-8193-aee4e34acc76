{"manifest": {"name": "fs-write-stream-atomic", "version": "1.0.10", "description": "Like `fs.createWriteStream(...)`, but atomic.", "main": "index.js", "dependencies": {"graceful-fs": "^4.1.2", "iferr": "^0.1.5", "imurmurhash": "^0.1.4", "readable-stream": "1 || 2"}, "devDependencies": {"rimraf": "^2.4.4", "standard": "^5.4.1", "tap": "^2.3.1"}, "scripts": {"test": "standard && tap --coverage test/*.js"}, "repository": {"type": "git", "url": "https://github.com/npm/fs-write-stream-atomic"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "bugs": {"url": "https://github.com/npm/fs-write-stream-atomic/issues"}, "homepage": "https://github.com/npm/fs-write-stream-atomic", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-fs-write-stream-atomic-1.0.10-b47df53493ef911df75731e70a9ded0189db40c9-integrity\\node_modules\\fs-write-stream-atomic\\package.json", "readmeFilename": "README.md", "readme": "# fs-write-stream-atomic\n\nLike `fs.createWriteStream(...)`, but atomic.\n\nWrites to a tmp file and does an atomic `fs.rename` to move it into\nplace when it's done.\n\nFirst rule of debugging: **It's always a race condition.**\n\n## USAGE\n\n```javascript\nvar fsWriteStreamAtomic = require('fs-write-stream-atomic')\n// options are optional.\nvar write = fsWriteStreamAtomic('output.txt', options)\nvar read = fs.createReadStream('input.txt')\nread.pipe(write)\n\n// When the write stream emits a 'finish' or 'close' event,\n// you can be sure that it is moved into place, and contains\n// all the bytes that were written to it, even if something else\n// was writing to `output.txt` at the same time.\n```\n\n### `fsWriteStreamAtomic(filename, [options])`\n\n* `filename` {String} The file we want to write to\n* `options` {Object}\n  * `chown` {Object} User and group to set ownership after write\n    * `uid` {Number}\n    * `gid` {Number}\n  * `encoding` {String} default = 'utf8'\n  * `mode` {Number} default = `0666`\n  * `flags` {String} default = `'w'`\n\n", "licenseText": "The ISC License\n\nCopyright (c) <PERSON> and Contributors\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz#b47df53493ef911df75731e70a9ded0189db40c9", "type": "tarball", "reference": "https://registry.yarnpkg.com/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz", "hash": "b47df53493ef911df75731e70a9ded0189db40c9", "integrity": "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=", "registry": "npm", "packageName": "fs-write-stream-atomic", "cacheIntegrity": "sha512-gehEzmPn2nAwr39eay+x3X34Ra+M2QlVUTLhkXPjWdeO8RF9kszk116avgBJM3ZyNHgHXBNx+VmPaFC36k0PzA== sha1-tH31NJPvkR33VzHnCp3tAYnbQMk="}, "registry": "npm", "hash": "b47df53493ef911df75731e70a9ded0189db40c9"}