.lock-screen {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.lock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(2px);
  z-index: 1;
}

.lock-time-container {
  position: relative;
  z-index: 2;
  text-align: center;
  margin-top: 120px;
  color: white;
}

.lock-time {
  font-size: 64px;
  font-weight: 200;
  letter-spacing: -2px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  margin-bottom: 8px;
}

.lock-date {
  font-size: 18px;
  font-weight: 500;
  opacity: 0.9;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

.lock-notifications {
  flex: 1;
  position: relative;
  z-index: 2;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 40px;
}

.lock-unlock-area {
  position: relative;
  z-index: 2;
  padding: 40px 20px 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.lock-unlock-prompt {
  text-align: center;
  color: white;
}

.unlock-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 20px;
  opacity: 0.9;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.unlock-methods {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.unlock-method-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 20px;
  border: none;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  font-weight: 600;
  min-width: 80px;
}

.unlock-method-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.unlock-method-btn:active {
  transform: translateY(0);
}

.unlock-method-btn i {
  font-size: 20px;
  margin-bottom: 4px;
}

/* PIN Input */
.pin-input-container {
  text-align: center;
  color: white;
  width: 100%;
  max-width: 300px;
}

.pin-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.pin-error {
  color: #ff6b6b;
  font-size: 14px;
  margin-bottom: 16px;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.pin-locked {
  color: #ff6b6b;
  font-size: 16px;
  margin-bottom: 20px;
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.pin-dots {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 40px;
}

.pin-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.6);
  transition: all 0.2s ease;
}

.pin-dot.filled {
  transform: scale(1.1);
}

.pin-keypad {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  max-width: 240px;
  margin: 0 auto;
}

.pin-key {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 24px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pin-key:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.pin-key:active:not(:disabled) {
  transform: scale(0.95);
}

.pin-key:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pin-key i {
  font-size: 18px;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .lock-time {
    font-size: 48px;
    margin-bottom: 6px;
  }
  
  .lock-date {
    font-size: 16px;
  }
  
  .lock-time-container {
    margin-top: 80px;
  }
  
  .pin-key {
    width: 56px;
    height: 56px;
    font-size: 20px;
  }
  
  .pin-keypad {
    gap: 12px;
    max-width: 200px;
  }
  
  .unlock-methods {
    gap: 12px;
  }
  
  .unlock-method-btn {
    padding: 12px 16px;
    min-width: 70px;
  }
}
