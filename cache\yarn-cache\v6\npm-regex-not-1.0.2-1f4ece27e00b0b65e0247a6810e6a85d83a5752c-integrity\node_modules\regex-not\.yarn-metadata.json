{"manifest": {"name": "regex-not", "description": "Create a javascript regular expression for matching everything except for the given string.", "version": "1.0.2", "homepage": "https://github.com/jonschlinkert/regex-not", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/regex-not.git"}, "bugs": {"url": "https://github.com/jonschlinkert/regex-not/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["exec", "match", "negate", "negation", "not", "regex", "regular expression", "test"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["regex-cache", "to-regex"]}, "reflinks": ["verb", "verb-generate-readme"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-regex-not-1.0.2-1f4ece27e00b0b65e0247a6810e6a85d83a5752c-integrity\\node_modules\\regex-not\\package.json", "readmeFilename": "README.md", "readme": "# regex-not [![NPM version](https://img.shields.io/npm/v/regex-not.svg?style=flat)](https://www.npmjs.com/package/regex-not) [![NPM monthly downloads](https://img.shields.io/npm/dm/regex-not.svg?style=flat)](https://npmjs.org/package/regex-not) [![NPM total downloads](https://img.shields.io/npm/dt/regex-not.svg?style=flat)](https://npmjs.org/package/regex-not) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/regex-not.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/regex-not)\n\n> Create a javascript regular expression for matching everything except for the given string.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save regex-not\n```\n\n## Usage\n\n```js\nvar not = require('regex-not');\n```\n\nThe main export is a function that takes a string an options object.\n\n```js\nnot(string[, options]);\n```\n\n**Example**\n\n```js\nvar not = require('regex-not');\nconsole.log(not('foo'));\n//=> /^(?:(?!^(?:foo)$).)+$/\n```\n\n**Strict matching**\n\nBy default, the returned regex is for strictly (not) matching the exact given pattern (in other words, \"match this string if it does NOT _exactly equal_ `foo`\"):\n\n```js\nvar re = not('foo');\nconsole.log(re.test('foo'));     //=> false\nconsole.log(re.test('bar'));     //=> true\nconsole.log(re.test('foobar'));  //=> true\nconsole.log(re.test('barfoo'));  //=> true\n```\n\n### .create\n\nReturns a string to allow you to create your own regex:\n\n```js\nconsole.log(not.create('foo'));\n//=> '(?:(?!^(?:foo)$).)+'\n```\n\n### Options\n\n**options.contains**\n\nYou can relax strict matching by setting `options.contains` to true (in other words, \"match this string if it does NOT _contain_ `foo`\"):\n\n```js\nvar re = not('foo');\nconsole.log(re.test('foo', {contains: true}));     //=> false\nconsole.log(re.test('bar', {contains: true}));     //=> true\nconsole.log(re.test('foobar', {contains: true}));  //=> false\nconsole.log(re.test('barfoo', {contains: true}));  //=> false\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [regex-cache](https://www.npmjs.com/package/regex-cache): Memoize the results of a call to the RegExp constructor, avoiding repetitious runtime compilation of… [more](https://github.com/jonschlinkert/regex-cache) | [homepage](https://github.com/jonschlinkert/regex-cache \"Memoize the results of a call to the RegExp constructor, avoiding repetitious runtime compilation of the same string and options, resulting in surprising performance improvements.\")\n* [to-regex](https://www.npmjs.com/package/to-regex): Generate a regex from a string or array of strings. | [homepage](https://github.com/jonschlinkert/to-regex \"Generate a regex from a string or array of strings.\")\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 9 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 1 | [doowb](https://github.com/doowb) |\n| 1 | [EdwardBetts](https://github.com/EdwardBetts) |\n\n### Author\n\n**Jon Schlinkert**\n\n* [linkedin/in/jonschlinkert](https://linkedin.com/in/jonschlinkert)\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on February 19, 2018._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016, 2018, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/regex-not/-/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c", "type": "tarball", "reference": "https://registry.yarnpkg.com/regex-not/-/regex-not-1.0.2.tgz", "hash": "1f4ece27e00b0b65e0247a6810e6a85d83a5752c", "integrity": "sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A==", "registry": "npm", "packageName": "regex-not", "cacheIntegrity": "sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A== sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw="}, "registry": "npm", "hash": "1f4ece27e00b0b65e0247a6810e6a85d83a5752c"}