{"manifest": {"name": "infer-owner", "version": "1.0.4", "description": "Infer the owner of a path based on the owner of its nearest existing parent", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://izs.me"}, "license": "ISC", "scripts": {"test": "tap -J test/*.js --100", "snap": "TAP_SNAPSHOT=1 tap -J test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"mutate-fs": "^2.1.1", "tap": "^12.4.2"}, "main": "index.js", "repository": {"type": "git", "url": "https://github.com/npm/infer-owner"}, "publishConfig": {"access": "public"}, "files": ["index.js"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-infer-owner-1.0.4-c4cefcaa8e51051c2a40ba2ce8a3d27295af9467-integrity\\node_modules\\infer-owner\\package.json", "readmeFilename": "README.md", "readme": "# infer-owner\n\nInfer the owner of a path based on the owner of its nearest existing parent\n\n## USAGE\n\n```js\nconst inferOwner = require('infer-owner')\n\ninferOwner('/some/cache/folder/file').then(owner => {\n  // owner is {uid, gid} that should be attached to\n  // the /some/cache/folder/file, based on ownership\n  // of /some/cache/folder, /some/cache, /some, or /,\n  // whichever is the first to exist\n})\n\n// same, but not async\nconst owner = inferOwner.sync('/some/cache/folder/file')\n\n// results are cached!  to reset the cache (eg, to change\n// permissions for whatever reason), do this:\ninferOwner.clearCache()\n```\n\nThis module endeavors to be as performant as possible.  Parallel requests\nfor ownership of the same path will only stat the directories one time.\n\n## API\n\n* `inferOwner(path) -> Promise<{ uid, gid }>`\n\n    If the path exists, return its uid and gid.  If it does not, look to\n    its parent, then its grandparent, and so on.\n\n* `inferOwner(path) -> { uid, gid }`\n\n    Sync form of `inferOwner(path)`.\n\n* `inferOwner.clearCache()`\n\n    Delete all cached ownership information and in-flight tracking.\n", "licenseText": "The ISC License\n\nCopyright (c) npm, Inc. and Contributors\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WH<PERSON>HER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/infer-owner/-/infer-owner-1.0.4.tgz#c4cefcaa8e51051c2a40ba2ce8a3d27295af9467", "type": "tarball", "reference": "https://registry.yarnpkg.com/infer-owner/-/infer-owner-1.0.4.tgz", "hash": "c4cefcaa8e51051c2a40ba2ce8a3d27295af9467", "integrity": "sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A==", "registry": "npm", "packageName": "infer-owner", "cacheIntegrity": "sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A== sha1-xM78qo5RBRwqQLos6KPScpWvlGc="}, "registry": "npm", "hash": "c4cefcaa8e51051c2a40ba2ce8a3d27295af9467"}