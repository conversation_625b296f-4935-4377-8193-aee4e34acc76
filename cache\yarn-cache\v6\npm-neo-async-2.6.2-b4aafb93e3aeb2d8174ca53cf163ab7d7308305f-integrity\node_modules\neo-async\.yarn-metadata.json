{"manifest": {"name": "neo-async", "version": "2.6.2", "description": "Neo-Async is a drop-in replacement for Async, it almost fully covers its functionality and runs faster ", "main": "async.js", "license": "MIT", "keywords": ["async", "util"], "repository": {"type": "git", "url": "**************:suguru03/neo-async.git"}, "homepage": "https://github.com/suguru03/neo-async", "dependencies": {}, "devDependencies": {"aigle": "^1.14.0", "async": "^2.6.0", "benchmark": "^2.1.1", "bluebird": "^3.5.1", "codecov.io": "^0.1.6", "fs-extra": "^4.0.2", "func-comparator": "^0.7.2", "gulp": "^4.0.2", "gulp-bump": "^2.8.0", "gulp-exit": "0.0.2", "gulp-git": "^2.4.2", "gulp-jscs": "^4.0.0", "gulp-mocha": "^4.2.0", "gulp-tag-version": "^1.3.0", "gulp-util": "^3.0.7", "husky": "^1.2.0", "istanbul": "^0.4.3", "jsdoc": "^3.5.5", "jshint": "^2.9.5", "lint-staged": "^8.1.0", "lodash": "^4.16.6", "minimist": "^1.2.0", "mocha": "^3.5.3", "mocha-parallel-executor": "^0.3.0", "mocha.parallel": "^0.15.3", "prettier": "^1.15.2", "require-dir": "^0.3.0"}, "lint-staged": {"*.{js,ts}": ["prettier --write", "git add"]}, "prettier": {"printWidth": 100, "singleQuote": true}, "browser": "async.min.js", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-neo-async-2.6.2-b4aafb93e3aeb2d8174ca53cf163ab7d7308305f-integrity\\node_modules\\neo-async\\package.json", "readmeFilename": "README.md", "readme": "<h1 align=\"center\">Neo-Async</h1>\n\n<p align=\"center\">\n  <img src=\"https://raw.githubusercontent.com/wiki/suguru03/neo-async/images/neo_async_v2.png\" width=\"250px\" />\n</p>\n\n<p align=\"center\">\n  <a href=\"https://www.npmjs.com/package/neo-async\"><img alt=\"npm\" src=\"https://img.shields.io/npm/v/neo-async.svg\"></a>\n  <a href=\"https://travis-ci.org/suguru03/neo-async\"><img alt=\"Travis Status\" src=\"https://img.shields.io/travis/suguru03/neo-async.svg\"></a>\n  <a href=\"https://codecov.io/gh/suguru03/neo-async\"><img alt=\"Coverage Status\" src=\"https://img.shields.io/codecov/c/github/suguru03/neo-async/master.svg\"></a>\n  <a href=\"https://www.npmjs.com/package/neo-async\"><img alt=\"download\" src=\"https://img.shields.io/npm/dm/neo-async.svg\"></a>\n  <a href=\"https://lgtm.com/projects/g/suguru03/neo-async/context:javascript\"><img src=\"https://img.shields.io/lgtm/grade/javascript/g/suguru03/neo-async.svg?logo=lgtm&logoWidth=18\" alt=\"Code Quality: Javascript\" height=\"18\"></a>\n  <a href=\"https://lgtm.com/projects/g/suguru03/neo-async/alerts\"><img src=\"https://img.shields.io/lgtm/alerts/g/suguru03/neo-async.svg?logo=lgtm&logoWidth=18\" alt=\"Total Alerts\" height=\"18\"></a>\n  <a href=\"https://app.fossa.io/projects/git%2Bgithub.com%2Fsuguru03%2Fneo-async?ref=badge_shield\"><img src=\"https://app.fossa.io/api/projects/git%2Bgithub.com%2Fsuguru03%2Fneo-async.svg?type=shield\" alt=\"FOSSA\" height=\"18\"></a>\n</p>\n\nNeo-Async is thought to be used as a drop-in replacement for [Async](https://github.com/caolan/async), it almost fully covers its functionality and runs [faster](#benchmark).\n\nBenchmark is [here](#benchmark)!\n\nBluebird's benchmark is [here](https://github.com/suguru03/bluebird/tree/aigle/benchmark)!\n\n## Code Coverage\n![coverage](https://raw.githubusercontent.com/wiki/suguru03/neo-async/images/coverage.png)\n\n## Installation\n\n### In a browser\n```html\n<script src=\"async.min.js\"></script>\n```\n\n### In an AMD loader\n```js\nrequire(['async'], function(async) {});\n```\n\n### Promise and async/await\n\nI recommend to use [`Aigle`](https://github.com/suguru03/aigle).\n\nIt is optimized for Promise handling and has almost the same functionality as `neo-async`.\n\n### Node.js\n\n#### standard\n\n```bash\n$ npm install neo-async\n```\n```js\nvar async = require('neo-async');\n```\n\n#### replacement\n```bash\n$ npm install neo-async\n$ ln -s ./node_modules/neo-async ./node_modules/async\n```\n```js\nvar async = require('async');\n```\n\n### Bower\n\n```bash\nbower install neo-async\n```\n\n## Feature\n\n[JSDoc](http://suguru03.github.io/neo-async/doc/async.html)\n\n\\* not in Async\n\n### Collections\n\n- [`each`](http://suguru03.github.io/neo-async/doc/async.each.html)\n- [`eachSeries`](http://suguru03.github.io/neo-async/doc/async.eachSeries.html)\n- [`eachLimit`](http://suguru03.github.io/neo-async/doc/async.eachLimit.html)\n- [`forEach`](http://suguru03.github.io/neo-async/doc/async.each.html) -> [`each`](http://suguru03.github.io/neo-async/doc/async.each.html)\n- [`forEachSeries`](http://suguru03.github.io/neo-async/doc/async.eachSeries.html) -> [`eachSeries`](http://suguru03.github.io/neo-async/doc/async.eachSeries.html)\n- [`forEachLimit`](http://suguru03.github.io/neo-async/doc/async.eachLimit.html) -> [`eachLimit`](http://suguru03.github.io/neo-async/doc/async.eachLimit.html)\n- [`eachOf`](http://suguru03.github.io/neo-async/doc/async.each.html) -> [`each`](http://suguru03.github.io/neo-async/doc/async.each.html)\n- [`eachOfSeries`](http://suguru03.github.io/neo-async/doc/async.eachSeries.html) -> [`eachSeries`](http://suguru03.github.io/neo-async/doc/async.eachSeries.html)\n- [`eachOfLimit`](http://suguru03.github.io/neo-async/doc/async.eachLimit.html) -> [`eachLimit`](http://suguru03.github.io/neo-async/doc/async.eachLimit.html)\n- [`forEachOf`](http://suguru03.github.io/neo-async/doc/async.each.html) -> [`each`](http://suguru03.github.io/neo-async/doc/async.each.html)\n- [`forEachOfSeries`](http://suguru03.github.io/neo-async/doc/async.eachSeries.html) -> [`eachSeries`](http://suguru03.github.io/neo-async/doc/async.eachSeries.html)\n- [`eachOfLimit`](http://suguru03.github.io/neo-async/doc/async.eachLimit.html) -> [`forEachLimit`](http://suguru03.github.io/neo-async/doc/async.eachLimit.html)\n- [`map`](http://suguru03.github.io/neo-async/doc/async.map.html)\n- [`mapSeries`](http://suguru03.github.io/neo-async/doc/async.mapSeries.html)\n- [`mapLimit`](http://suguru03.github.io/neo-async/doc/async.mapLimit.html)\n- [`mapValues`](http://suguru03.github.io/neo-async/doc/async.mapValues.html)\n- [`mapValuesSeries`](http://suguru03.github.io/neo-async/doc/async.mapValuesSeries.html)\n- [`mapValuesLimit`](http://suguru03.github.io/neo-async/doc/async.mapValuesLimit.html)\n- [`filter`](http://suguru03.github.io/neo-async/doc/async.filter.html)\n- [`filterSeries`](http://suguru03.github.io/neo-async/doc/async.filterSeries.html)\n- [`filterLimit`](http://suguru03.github.io/neo-async/doc/async.filterLimit.html)\n- [`select`](http://suguru03.github.io/neo-async/doc/async.filter.html) -> [`filter`](http://suguru03.github.io/neo-async/doc/async.filter.html)\n- [`selectSeries`](http://suguru03.github.io/neo-async/doc/async.filterSeries.html) -> [`filterSeries`](http://suguru03.github.io/neo-async/doc/async.filterSeries.html)\n- [`selectLimit`](http://suguru03.github.io/neo-async/doc/async.filterLimit.html) -> [`filterLimit`](http://suguru03.github.io/neo-async/doc/async.filterLimit.html)\n- [`reject`](http://suguru03.github.io/neo-async/doc/async.reject.html)\n- [`rejectSeries`](http://suguru03.github.io/neo-async/doc/async.rejectSeries.html)\n- [`rejectLimit`](http://suguru03.github.io/neo-async/doc/async.rejectLimit.html)\n- [`detect`](http://suguru03.github.io/neo-async/doc/async.detect.html)\n- [`detectSeries`](http://suguru03.github.io/neo-async/doc/async.detectSeries.html)\n- [`detectLimit`](http://suguru03.github.io/neo-async/doc/async.detectLimit.html)\n- [`find`](http://suguru03.github.io/neo-async/doc/async.detect.html) -> [`detect`](http://suguru03.github.io/neo-async/doc/async.detect.html)\n- [`findSeries`](http://suguru03.github.io/neo-async/doc/async.detectSeries.html) -> [`detectSeries`](http://suguru03.github.io/neo-async/doc/async.detectSeries.html)\n- [`findLimit`](http://suguru03.github.io/neo-async/doc/async.detectLimit.html) -> [`detectLimit`](http://suguru03.github.io/neo-async/doc/async.detectLimit.html)\n- [`pick`](http://suguru03.github.io/neo-async/doc/async.pick.html) *\n- [`pickSeries`](http://suguru03.github.io/neo-async/doc/async.pickSeries.html) *\n- [`pickLimit`](http://suguru03.github.io/neo-async/doc/async.pickLimit.html) *\n- [`omit`](http://suguru03.github.io/neo-async/doc/async.omit.html) *\n- [`omitSeries`](http://suguru03.github.io/neo-async/doc/async.omitSeries.html) *\n- [`omitLimit`](http://suguru03.github.io/neo-async/doc/async.omitLimit.html) *\n- [`reduce`](http://suguru03.github.io/neo-async/doc/async.reduce.html)\n- [`inject`](http://suguru03.github.io/neo-async/doc/async.reduce.html) -> [`reduce`](http://suguru03.github.io/neo-async/doc/async.reduce.html)\n- [`foldl`](http://suguru03.github.io/neo-async/doc/async.reduce.html) -> [`reduce`](http://suguru03.github.io/neo-async/doc/async.reduce.html)\n- [`reduceRight`](http://suguru03.github.io/neo-async/doc/async.reduceRight.html)\n- [`foldr`](http://suguru03.github.io/neo-async/doc/async.reduceRight.html) -> [`reduceRight`](http://suguru03.github.io/neo-async/doc/async.reduceRight.html)\n- [`transform`](http://suguru03.github.io/neo-async/doc/async.transform.html)\n- [`transformSeries`](http://suguru03.github.io/neo-async/doc/async.transformSeries.html) *\n- [`transformLimit`](http://suguru03.github.io/neo-async/doc/async.transformLimit.html) *\n- [`sortBy`](http://suguru03.github.io/neo-async/doc/async.sortBy.html)\n- [`sortBySeries`](http://suguru03.github.io/neo-async/doc/async.sortBySeries.html) *\n- [`sortByLimit`](http://suguru03.github.io/neo-async/doc/async.sortByLimit.html) *\n- [`some`](http://suguru03.github.io/neo-async/doc/async.some.html)\n- [`someSeries`](http://suguru03.github.io/neo-async/doc/async.someSeries.html)\n- [`someLimit`](http://suguru03.github.io/neo-async/doc/async.someLimit.html)\n- [`any`](http://suguru03.github.io/neo-async/doc/async.some.html) -> [`some`](http://suguru03.github.io/neo-async/doc/async.some.html)\n- [`anySeries`](http://suguru03.github.io/neo-async/doc/async.someSeries.html) -> [`someSeries`](http://suguru03.github.io/neo-async/doc/async.someSeries.html)\n- [`anyLimit`](http://suguru03.github.io/neo-async/doc/async.someLimit.html) -> [`someLimit`](http://suguru03.github.io/neo-async/doc/async.someLimit.html)\n- [`every`](http://suguru03.github.io/neo-async/doc/async.every.html)\n- [`everySeries`](http://suguru03.github.io/neo-async/doc/async.everySeries.html)\n- [`everyLimit`](http://suguru03.github.io/neo-async/doc/async.everyLimit.html)\n- [`all`](http://suguru03.github.io/neo-async/doc/async.every.html) -> [`every`](http://suguru03.github.io/neo-async/doc/async.every.html)\n- [`allSeries`](http://suguru03.github.io/neo-async/doc/async.everySeries.html) -> [`every`](http://suguru03.github.io/neo-async/doc/async.everySeries.html)\n- [`allLimit`](http://suguru03.github.io/neo-async/doc/async.everyLimit.html) -> [`every`](http://suguru03.github.io/neo-async/doc/async.everyLimit.html)\n- [`concat`](http://suguru03.github.io/neo-async/doc/async.concat.html)\n- [`concatSeries`](http://suguru03.github.io/neo-async/doc/async.concatSeries.html)\n- [`concatLimit`](http://suguru03.github.io/neo-async/doc/async.concatLimit.html) *\n\n### Control Flow\n\n- [`parallel`](http://suguru03.github.io/neo-async/doc/async.parallel.html)\n- [`series`](http://suguru03.github.io/neo-async/doc/async.series.html)\n- [`parallelLimit`](http://suguru03.github.io/neo-async/doc/async.series.html)\n- [`tryEach`](http://suguru03.github.io/neo-async/doc/async.tryEach.html)\n- [`waterfall`](http://suguru03.github.io/neo-async/doc/async.waterfall.html)\n- [`angelFall`](http://suguru03.github.io/neo-async/doc/async.angelFall.html) *\n- [`angelfall`](http://suguru03.github.io/neo-async/doc/async.angelFall.html) -> [`angelFall`](http://suguru03.github.io/neo-async/doc/async.angelFall.html) *\n- [`whilst`](#whilst)\n- [`doWhilst`](#doWhilst)\n- [`until`](#until)\n- [`doUntil`](#doUntil)\n- [`during`](#during)\n- [`doDuring`](#doDuring)\n- [`forever`](#forever)\n- [`compose`](#compose)\n- [`seq`](#seq)\n- [`applyEach`](#applyEach)\n- [`applyEachSeries`](#applyEachSeries)\n- [`queue`](#queue)\n- [`priorityQueue`](#priorityQueue)\n- [`cargo`](#cargo)\n- [`auto`](#auto)\n- [`autoInject`](#autoInject)\n- [`retry`](#retry)\n- [`retryable`](#retryable)\n- [`iterator`](#iterator)\n- [`times`](http://suguru03.github.io/neo-async/doc/async.times.html)\n- [`timesSeries`](http://suguru03.github.io/neo-async/doc/async.timesSeries.html)\n- [`timesLimit`](http://suguru03.github.io/neo-async/doc/async.timesLimit.html)\n- [`race`](#race)\n\n### Utils\n- [`apply`](#apply)\n- [`setImmediate`](#setImmediate)\n- [`nextTick`](#nextTick)\n- [`memoize`](#memoize)\n- [`unmemoize`](#unmemoize)\n- [`ensureAsync`](#ensureAsync)\n- [`constant`](#constant)\n- [`asyncify`](#asyncify)\n- [`wrapSync`](#asyncify) -> [`asyncify`](#asyncify)\n- [`log`](#log)\n- [`dir`](#dir)\n- [`timeout`](http://suguru03.github.io/neo-async/doc/async.timeout.html)\n- [`reflect`](#reflect)\n- [`reflectAll`](#reflectAll)\n- [`createLogger`](#createLogger)\n\n## Mode\n- [`safe`](#safe) *\n- [`fast`](#fast) *\n\n## Benchmark\n\n[Benchmark: Async vs Neo-Async](http://suguru03.hatenablog.com/entry/2016/06/10/135559)\n\n### How to check\n\n```bash\n$ node perf\n```\n\n### Environment\n\n* Darwin 17.3.0 x64\n* Node.js v8.9.4\n* async v2.6.0\n* neo-async v2.5.0\n* benchmark v2.1.4\n\n### Result\n\nThe value is the ratio (Neo-Async/Async) of the average speed.\n\n#### Collections\n|function|benchmark|\n|---|--:|\n|each/forEach|2.43|\n|eachSeries/forEachSeries|1.75|\n|eachLimit/forEachLimit|1.68|\n|eachOf|3.29|\n|eachOfSeries|1.50|\n|eachOfLimit|1.59|\n|map|3.95|\n|mapSeries|1.81|\n|mapLimit|1.27|\n|mapValues|2.73|\n|mapValuesSeries|1.59|\n|mapValuesLimit|1.23|\n|filter|3.00|\n|filterSeries|1.74|\n|filterLimit|1.17|\n|reject|4.59|\n|rejectSeries|2.31|\n|rejectLimit|1.58|\n|detect|4.30|\n|detectSeries|1.86|\n|detectLimit|1.32|\n|reduce|1.82|\n|transform|2.46|\n|sortBy|4.08|\n|some|2.19|\n|someSeries|1.83|\n|someLimit|1.32|\n|every|2.09|\n|everySeries|1.84|\n|everyLimit|1.35|\n|concat|3.79|\n|concatSeries|4.45|\n\n#### Control Flow\n|funciton|benchmark|\n|---|--:|\n|parallel|2.93|\n|series|1.96|\n|waterfall|1.29|\n|whilst|1.00|\n|doWhilst|1.12|\n|until|1.12|\n|doUntil|1.12|\n|during|1.18|\n|doDuring|2.42|\n|times|4.25|\n|auto|1.97|\n\n\n## License\n[![FOSSA Status](https://app.fossa.io/api/projects/git%2Bgithub.com%2Fsuguru03%2Fneo-async.svg?type=large)](https://app.fossa.io/projects/git%2Bgithub.com%2Fsuguru03%2Fneo-async?ref=badge_large)\n", "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON><PERSON><PERSON> Motegi\nBased on Async.js, Copyright Caolan McMahon\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/neo-async/-/neo-async-2.6.2.tgz#b4aafb93e3aeb2d8174ca53cf163ab7d7308305f", "type": "tarball", "reference": "https://registry.yarnpkg.com/neo-async/-/neo-async-2.6.2.tgz", "hash": "b4aafb93e3aeb2d8174ca53cf163ab7d7308305f", "integrity": "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==", "registry": "npm", "packageName": "neo-async", "cacheIntegrity": "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw== sha1-tKr7k+OustgXTKU88WOrfXMIMF8="}, "registry": "npm", "hash": "b4aafb93e3aeb2d8174ca53cf163ab7d7308305f"}