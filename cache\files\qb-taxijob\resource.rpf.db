[{"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/@PolyZone/BoxZone.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/@PolyZone/CircleZone.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/@PolyZone/ComboZone.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/@PolyZone/EntityZone.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/@PolyZone/client.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/@qb-core/shared/locale.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/client/main.lua", "mt": 1749054007, "s": 35286, "i": "cDgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/config.lua", "mt": 1749054007, "s": 13988, "i": "cTgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/fxmanifest.lua", "mt": 1749054007, "s": 611, "i": "cjgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/html/meter.css", "mt": 1749054007, "s": 2159, "i": "czgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/html/meter.html", "mt": 1749054007, "s": 1831, "i": "dDgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/html/meter.js", "mt": 1749054007, "s": 2075, "i": "dTgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/locales/ar.lua", "mt": 1749054007, "s": 1471, "i": "djgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/locales/da.lua", "mt": 1749054007, "s": 1192, "i": "dzgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/locales/de.lua", "mt": 1749054007, "s": 2078, "i": "eDgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/locales/en.lua", "mt": 1749054007, "s": 1871, "i": "eTgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/locales/es.lua", "mt": 1749054007, "s": 1427, "i": "ejgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/locales/fa.lua", "mt": 1749054007, "s": 1257, "i": "ezgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/locales/fi.lua", "mt": 1749054007, "s": 1178, "i": "fDgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/locales/fr.lua", "mt": 1749054007, "s": 1420, "i": "fTgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/locales/it.lua", "mt": 1749054007, "s": 1364, "i": "fjgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/locales/nl.lua", "mt": 1749054007, "s": 2023, "i": "fzgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/locales/pt-br.lua", "mt": 1749054007, "s": 1349, "i": "gDgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/locales/pt.lua", "mt": 1749054007, "s": 1258, "i": "gTgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/locales/sv.lua", "mt": 1749054007, "s": 1181, "i": "gjgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/locales/th.lua", "mt": 1749054007, "s": 1981, "i": "gzgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/locales/tr.lua", "mt": 1749054007, "s": 1183, "i": "hDgCAAAABwAAAAAAAAAAAA=="}, {"n": "D:/txData/QBCore_4070F0.base/resources//[qb]/qb-taxijob/locales/ua.lua", "mt": 1749054007, "s": 2521, "i": "hTgCAAAABwAAAAAAAAAAAA=="}]