<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="stylesheet" href="style.css" />
        <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.2/css/all.css">
        <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.2/css/sharp-thin.css">
        <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.2/css/sharp-solid.css">
        <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.2/css/sharp-regular.css">
        <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.2/css/sharp-light.css">
        <script src="nui://game/ui/jquery.js" type="text/javascript"></script>
    </head>
    <body>
        <div id="mainDivEffect"></div>
        <div class="mainDiv" id="mainDiv-Menu">
            <div class="mainDivLeft">
                <div id="MDLTop">
                    <div id="MDLTopEffect">
                        <span style="font-family: 'Gilroy-UltraLight'; letter-spacing: 0.5vw; font-weight: 265; font-size: 0.8vw; margin-left: 0.9vw; margin-top: 2%;"></span>
                        <h4 style="font-family: 'Gilroy-BlackItalic'; font-weight: 900; font-size: 1.4vw;"></h4>
                        <span style="font-family: 'Gilroy-RegularItalic'; font-weight: 400; color: rgba(255, 255, 255, 0.85); text-transform: capitalize; width: 55%; font-size: 0.7vw;"></span>
                    </div>
                </div>
                <div id="MDLCenter"></div>
                <div id="MDLBottom">
                    <div class="MDLBottomBtn MDLBottomBtnRed" onclick="goBack2()"><span>Back</span></div>
                    <div class="MDLBottomBtn MDLBottomBtnGreen" onclick="nextPage2()"><span>Next</span></div>
                    <div class="MDLBottomBtn MDLBottomBtnPink" id="MDLBottomBtnPink" style="width: 31%;" onclick="openPaymentDialog()"><span class="pay"></span><span style="margin-left: 2%;">(</span><span id="paymentSpan"></span><span>)</span></div>
                </div>
                <!-- <div id="mainDivDialogBG"></div>
                <div id="mainDivDialog">
                    <h4 class="are_you_sure"></h4>
                    <span id="mainDivDialogSpan" class="confirm_character_creation"></span>
                    <div id="mainDivDialogButtons">
                        <div class="mainDivDialogButtonGreen" onclick="finalizeCharacter()"></div>
                        <div class="mainDivDialogButtonRed" onclick="discardCharacterCreation()"></div>
                        <div class="mainDivDialogButtonRed" onclick="closeDialog()"></div>
                    </div>
                </div> -->
            </div>
            <div class="mainDivRight" style="display: none;">
                <div class="mainDivRightButton" id="MDRB-All" onclick="removeCloth('All')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M15.4375 5.62044C14.2852 5.78194 13.2483 6.36076 12.5602 7.22657C11.8331 8.14169 11.3685 9.37544 11.4638 10.1384C11.6015 11.2404 12.703 11.8754 13.6626 11.4059C14.1157 11.1842 14.3234 10.8631 14.4947 10.119C14.6573 9.41276 14.8134 9.10788 15.1299 8.87869C16.0419 8.21801 17.3454 8.79488 17.4779 9.91788C17.5597 10.6101 17.2339 11.1311 16.4729 11.5247C15.2321 12.1666 14.586 13.173 14.5161 14.5729L14.4828 15.2397L9.31949 17.2221C6.47974 18.3125 3.98749 19.2922 3.78124 19.3992C3.00824 19.8003 2.30956 20.7258 2.09606 21.6314C1.97418 22.1485 1.97456 23.2281 2.09681 23.7467C2.38462 24.9677 3.28581 25.9097 4.53124 26.2913C4.88924 26.4011 5.40824 26.4063 16 26.4063C26.5917 26.4063 27.1107 26.4011 27.4687 26.2913C28.7142 25.9097 29.6154 24.9677 29.9032 23.7467C30.0254 23.2281 30.0258 22.1485 29.9039 21.6314C29.6687 20.6338 28.9655 19.7574 28.0526 19.3243C27.7659 19.1882 25.2742 18.2111 22.5156 17.1529L17.5 15.2289V14.8784C17.5 14.4391 17.5962 14.2968 18.0331 14.0901C18.87 13.6943 19.6256 12.9061 20.0669 11.9688C21.4097 9.11663 19.4323 5.80376 16.2672 5.60307C15.9999 5.58613 15.6266 5.59394 15.4375 5.62044ZM10.625 19.9311C7.58624 21.0969 5.22056 22.0383 5.14062 22.1134C5.02106 22.2258 4.99999 22.3103 4.99999 22.6783C4.99999 23.0114 5.02756 23.1452 5.11949 23.2587L5.23893 23.4063H16H26.7611L26.8805 23.2587C27.0444 23.0563 27.0511 22.3236 26.8906 22.1358C26.8204 22.0536 24.8893 21.2816 21.5 19.9809C18.5953 18.8661 16.1625 17.9376 16.0937 17.9175C16.0134 17.8941 14.0602 18.6133 10.625 19.9311Z" fill="white" fill-opacity="0.55"/>
                    </svg>
                </div>
                <div class="mainDivRightButton MDRB-Hat" onclick="removeCloth('Hat', '0')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
                        <g clip-path="url(#clip0_55_481)">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M14.5898 5.72275C14.5092 5.73552 13.8632 5.82575 13.1543 5.92331C9.4308 6.43577 6.71211 7.12495 6.02375 7.73087C5.49201 8.19892 5.08402 9.41392 4.60584 11.9531C4.25574 13.8121 3.76988 17.3312 3.85267 17.4083C3.90054 17.4528 5.25072 17.7023 6.1816 17.8386C10.8428 18.5212 17.1119 18.6018 22.1191 18.0437C23.6723 17.8705 25.9622 17.5078 26.1208 17.4097C26.2317 17.3412 25.7754 13.9488 25.3672 11.8067C24.9045 9.3787 24.4879 8.16646 23.9612 7.71564C23.2334 7.09267 20.5679 6.43548 16.4424 5.86191C15.2219 5.69222 14.9241 5.66984 14.5898 5.72275ZM26.8652 19.0822C23.8856 19.8455 18.0699 20.2681 12.7834 20.1055C8.30621 19.9678 5.38519 19.652 2.85254 19.0319L2.33597 18.9054L1.25586 19.9843C0.661833 20.5777 0.136188 21.1466 0.0878483 21.2485C-0.125784 21.6987 0.0702702 22.1977 0.545641 22.4135C1.2923 22.7524 3.25783 23.2567 4.98353 23.5521C7.72361 24.0211 10.3754 24.2415 13.9453 24.2971C19.7297 24.3871 25.2273 23.7958 28.5907 22.7217C29.5733 22.4079 29.7218 22.326 29.906 21.9965C30.0491 21.7405 30.1208 21.2828 30.0068 21.3532C29.9783 21.3709 29.9245 21.3118 29.8874 21.2221C29.8502 21.1324 29.3264 20.5703 28.7234 19.973L27.6269 18.8871L26.8652 19.0822ZM0.0158952 21.6211C0.0158952 21.7661 0.027321 21.8255 0.0413249 21.7529C0.0552702 21.6804 0.0552702 21.5618 0.0413249 21.4893C0.027321 21.4167 0.0158952 21.4761 0.0158952 21.6211Z" fill="white" fill-opacity="0.55"/>
                        </g>
                        <defs>
                            <clipPath id="clip0_55_481">
                                <rect width="30" height="30" fill="white"/>
                            </clipPath>
                        </defs>
                    </svg>
                </div>
                <div class="mainDivRightButton MDRB-Masks" onclick="removeCloth('Masks', '1')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M20.2442 2.32384C18.248 2.44402 16.5045 2.72521 15.1758 3.14134C14.8052 3.25742 14.4058 3.4015 14.2882 3.46144C13.9529 3.63247 13.8106 3.91912 13.781 4.48273L13.7559 4.96091L14.4852 4.96572C15.3044 4.97111 15.5554 5.04031 15.7602 5.31734C15.8301 5.41185 16.1699 6.0916 16.5154 6.82783C17.0746 8.01933 17.1659 8.1787 17.3462 8.2782C17.4577 8.33972 17.602 8.43898 17.6669 8.4988C17.8372 8.65583 18.1863 8.78902 18.4278 8.78902C18.6857 8.78902 18.9214 8.69122 19.3272 8.41583C19.7075 8.15779 20.0524 8.12931 20.376 8.32935C20.5901 8.4616 20.7973 8.82798 20.7995 9.07812C20.8016 9.30611 20.5833 9.69347 20.346 9.88296C19.8466 10.2817 19.447 10.4507 18.7324 10.5655L18.3011 10.6347L19.2258 12.5976C19.7343 13.6772 20.1599 14.5852 20.1716 14.6153C20.1838 14.6468 20.4029 14.6231 20.6873 14.5594C21.7453 14.3227 23.0558 14.4471 24.1049 14.8839C24.9065 15.2176 25.8133 15.904 26.0238 16.3365C26.309 16.9224 25.9061 17.5781 25.2607 17.5781C24.9326 17.5781 24.7917 17.5119 24.4113 17.1789C23.5412 16.4176 22.3201 16.0836 21.1778 16.2946C20.9866 16.33 20.8137 16.3691 20.7936 16.3814C20.7735 16.3938 20.7932 16.5561 20.8372 16.742C21.1752 18.1686 21.1742 20.3532 20.8349 21.8151C20.784 22.0347 20.7422 22.2493 20.7422 22.2921C20.7422 22.34 20.9283 22.4202 21.2256 22.5005C21.8394 22.6663 22.1915 22.6375 23.0567 22.3507C26.8297 21.1 29.5014 17.7861 29.9375 13.8157C29.9766 13.4608 29.9996 11.5324 29.9988 8.68876C29.9975 4.37046 29.9917 4.11816 29.8884 3.89064C29.7405 3.56509 29.5332 3.43267 28.7985 3.19454C26.9654 2.6004 24.3733 2.27169 21.71 2.29566C21.065 2.30146 20.4053 2.31417 20.2442 2.32384ZM13.6817 6.77896C9.53906 7.21507 3.72404 9.89034 0.604921 12.795C0.131249 13.2361 -0.0390831 13.5642 0.0191591 13.9232C0.0635732 14.1967 3.72076 22.0059 4.13191 22.705C4.60289 23.506 5.07867 24.1069 5.77986 24.7863C6.75439 25.7306 7.61824 26.3208 8.76328 26.8245C10.0504 27.3908 11.0108 27.5975 12.5391 27.6372C13.8432 27.671 14.6947 27.5766 15.1994 27.342C15.8226 27.0525 16.8577 25.9911 17.6094 24.8709C19.483 22.0787 19.8338 18.5083 18.5398 15.4011C18.2231 14.6409 14.6902 7.16872 14.6003 7.06917C14.4064 6.85449 14.046 6.74064 13.6817 6.77896ZM23.8618 8.31997C23.4592 8.56548 23.3108 9.13701 23.5485 9.52689C23.8247 9.97976 24.6565 10.4455 25.37 10.5467C26.3633 10.6876 27.5963 10.212 28.0141 9.52689C28.3601 8.95941 27.9214 8.20337 27.2462 8.20361C26.9846 8.20372 26.9401 8.22423 26.423 8.58394C26.2039 8.73634 26.1184 8.75972 25.7808 8.75954C25.4435 8.75943 25.3577 8.73593 25.1392 8.58376C24.6233 8.22458 24.578 8.20372 24.3165 8.20361C24.1563 8.20355 23.9778 8.24925 23.8618 8.31997ZM13.1251 12.5644C12.371 12.7746 11.6254 13.3866 11.2782 14.0807C10.9697 14.6973 11.0302 15.1664 11.4538 15.4439C11.9616 15.7767 12.5609 15.5701 12.826 14.9708C13.0592 14.4437 13.6014 14.1714 14.1833 14.2894C14.6479 14.3836 14.8075 14.3724 15.0653 14.2275C15.3052 14.0926 15.5274 13.7315 15.5274 13.4765C15.5274 13.2665 15.3067 12.8545 15.1336 12.741C14.7562 12.4937 13.7089 12.4017 13.1251 12.5644ZM6.51053 15.6785C5.70281 15.8957 4.97877 16.4847 4.60775 17.2263C4.49051 17.4607 4.39459 17.7288 4.39459 17.8222C4.39459 18.2324 4.73766 18.6515 5.13439 18.7259C5.58932 18.8112 5.93771 18.582 6.21094 18.0177C6.45515 17.5132 6.97623 17.2978 7.62521 17.4332C8.14383 17.5415 8.50189 17.4121 8.7331 17.0329C8.94902 16.6788 8.83418 16.1196 8.49609 15.8789C8.11148 15.6049 7.14615 15.5076 6.51053 15.6785ZM14.6425 18.5313C14.5006 18.6261 14.3593 18.8295 14.1504 19.2394C13.8016 19.9241 13.512 20.2945 13.0372 20.6629C12.6684 20.949 12.0476 21.2645 11.6016 21.3925C11.4566 21.4341 11.0347 21.469 10.6641 21.4701C10.1211 21.4716 9.90592 21.4457 9.55564 21.3368C8.87607 21.1253 8.43603 21.285 8.23307 21.8163C8.05512 22.2823 8.27232 22.7411 8.7723 22.9555C9.72088 23.3623 11.305 23.3795 12.4543 22.9955C13.9076 22.51 15.1005 21.4348 15.7692 20.008C16.0223 19.4678 16.0531 19.1859 15.8935 18.8683C15.6663 18.416 15.0598 18.2527 14.6425 18.5313Z" fill="white" fill-opacity="0.55"/>
                    </svg>
                </div>
                <div class="mainDivRightButton MDRB-Glasses" onclick="removeCloth('Glasses', '1')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="31" viewBox="0 0 30 31" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M5.18551 10.5723C3.90547 10.7032 2.56749 10.9492 1.5527 11.2401C0.707544 11.4824 0.761685 11.39 0.761685 12.5909C0.761685 13.7363 0.755064 13.7163 1.18989 13.8879C1.33538 13.9453 1.47231 14.0387 1.49405 14.0953C1.51584 14.152 1.55592 14.6342 1.58311 15.167C1.69239 17.3094 2.06223 18.5437 2.83215 19.3351C3.26463 19.7797 3.61122 19.9826 4.2786 20.1819C4.95125 20.3827 5.76741 20.4649 7.08911 20.4649C8.94618 20.4649 10.0352 20.2535 11.1865 19.6697C11.7003 19.4092 11.8723 19.2823 12.2732 18.8681C12.5687 18.5628 12.8211 18.2343 12.9381 18.0026C13.1458 17.5913 13.5892 16.0525 13.7726 15.1065C13.8722 14.5931 13.9166 14.4757 14.065 14.3345C14.2356 14.1723 14.2581 14.168 14.9385 14.168C15.3226 14.168 15.7045 14.1946 15.7871 14.227C15.9792 14.3025 16.1474 14.5885 16.1905 14.9129C16.256 15.4047 16.8215 17.4617 16.9915 17.8259C17.639 19.2134 19.0841 20.096 21.175 20.3808C22.1873 20.5188 24.5162 20.4677 25.3125 20.2902C27.3896 19.8271 28.2283 18.429 28.4134 15.1211C28.4418 14.6136 28.4831 14.152 28.5052 14.0953C28.5274 14.0387 28.6631 13.9459 28.8071 13.8891C29.2173 13.7272 29.2382 13.6657 29.2382 12.6241C29.2382 11.9028 29.2195 11.6849 29.1484 11.5799C29.0409 11.4214 28.5804 11.271 27.4425 11.0227C23.9414 10.2587 20.4422 10.4357 17.1679 11.5422L16.4941 11.7699L15.1172 11.7912C13.5927 11.8149 13.7476 11.8388 12.1456 11.3316C11.2987 11.0634 10.2673 10.8327 9.24336 10.6822C8.61524 10.5899 5.76729 10.5128 5.18551 10.5723Z" fill="white" fill-opacity="0.55"/>
                    </svg>
                </div>
                <div class="mainDivRightButton MDRB-Jacket" onclick="removeCloth('Jacket', '11')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 26 26" fill="none">
                        <g clip-path="url(#clip0_55_493)">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M6.82204 0.981651C5.70048 1.51704 4.60361 2.06796 4.38454 2.20594C3.51481 2.75381 2.66676 3.79183 2.28935 4.77044C2.09776 5.26723 0.710565 10.5452 0.712037 10.7716C0.712647 10.8661 0.752662 10.9931 0.800905 11.0539C0.849197 11.1147 1.85985 11.723 3.04686 12.4057L5.20506 13.647L5.23106 18.5159C5.24904 21.8804 5.27443 23.4323 5.31317 23.5385C5.6886 24.568 6.18057 24.8998 8.02342 25.3664C10.5939 26.0173 13.2563 26.1628 15.8183 25.7925C17.2138 25.5907 18.9075 25.1732 19.5366 24.876C20.0589 24.6292 20.458 24.1648 20.6871 23.5376C20.7255 23.4324 20.751 21.8658 20.7689 18.517L20.7949 13.6493L22.9531 12.4077C24.1401 11.7248 25.1508 11.116 25.1991 11.0547C25.2473 10.9935 25.2873 10.8661 25.2879 10.7716C25.2893 10.5649 23.9176 5.31603 23.7361 4.83361C23.4585 4.09561 22.9782 3.37599 22.3975 2.82816C21.9546 2.41043 21.5441 2.13134 20.9123 1.81848C19.7223 1.22921 17.1792 0.0411315 17.0545 0.0161979C16.9702 -0.000661473 16.7083 0.0584479 16.3683 0.171131C15.193 0.560522 14.2669 0.7086 13 0.709667C11.7521 0.710733 10.4706 0.493135 9.40528 0.099276C9.25761 0.0446862 9.0748 0.00187759 8.99903 0.00411196C8.92332 0.00639712 7.94364 0.446315 6.82204 0.981651ZM9.73206 1.46702C9.79492 1.73382 10.0962 2.31867 10.3293 2.6262C10.7576 3.19145 11.4623 3.65005 12.1875 3.83546C12.6159 3.94494 13.3867 3.94413 13.8157 3.83368C14.9086 3.5524 15.7998 2.74665 16.1817 1.69457C16.3188 1.31676 16.3391 1.32676 15.7327 1.47265C15.1662 1.60895 14.0659 1.76409 13.3808 1.80426C12.4917 1.85641 11.2024 1.71519 10.1637 1.45183L9.70083 1.33443L9.73206 1.46702Z" fill="white" fill-opacity="0.55"/>
                        </g>
                        <defs>
                            <clipPath id="clip0_55_493">
                                <rect width="26" height="26" fill="white"/>
                            </clipPath>
                        </defs>
                    </svg>
                </div>
                <div class="mainDivRightButton MDRB-Bag" onclick="removeCloth('Bag', '5')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="30" viewBox="0 0 24 30" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.0516 0.0611116C9.90489 0.273316 9.00176 0.723237 8.16359 1.49973C7.45988 2.15165 7.02866 2.82498 6.6963 3.79075L6.52473 4.2893L6.50348 6.41544L6.48229 8.54158L4.23126 8.54194C2.8311 8.54211 1.8928 8.56575 1.7489 8.60446C1.43458 8.68904 1.05705 9.03057 0.984768 9.29574C0.952803 9.41322 0.703138 13.4288 0.430045 18.2192C-0.12607 27.975 -0.119048 27.3692 0.314293 28.2258C0.50021 28.5933 0.651438 28.7896 0.979622 29.0895C1.41478 29.4872 1.66166 29.6359 2.24974 29.8542L2.58561 29.979L11.7573 29.9959C18.2312 30.0079 21.0448 29.9938 21.3226 29.9483C22.301 29.7877 23.2964 29.0482 23.7145 28.1714C24.1131 27.3358 24.1213 27.8896 23.567 18.2485C23.2925 13.4742 23.0404 9.46336 23.0067 9.3355C22.9284 9.03813 22.5726 8.69121 22.2595 8.60698C22.1038 8.5651 21.2222 8.54211 19.7637 8.54194L17.5086 8.54158L17.5081 6.67938C17.5079 5.65513 17.4793 4.64561 17.4445 4.43593C17.2308 3.14487 16.3471 1.76907 15.2398 1.00359C14.7371 0.65608 13.9509 0.299181 13.3616 0.151026C12.7371 -0.00592795 11.6438 -0.0485095 11.0516 0.0611116ZM11.0598 2.25453C10.2317 2.49225 9.47887 3.08986 9.07986 3.82629C8.70609 4.51604 8.6717 4.76297 8.67073 6.76735L8.66982 8.54158H12.004H15.3381L15.3154 6.65005C15.2945 4.89523 15.2833 4.73065 15.1606 4.37305C14.8468 3.45878 14.1256 2.71401 13.1904 2.3384C12.6878 2.13652 11.6177 2.09441 11.0598 2.25453Z" fill="white" fill-opacity="0.55"/>
                    </svg>
                </div>
                <div class="mainDivRightButton MDRB-Hairs" onclick="removeCloth('Hairs', '2')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="21" viewBox="0 0 24 21" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M9.39669 0.0984306C7.68306 0.376721 6.19206 1.11793 5.52209 2.02463C5.21306 2.44288 4.98629 2.95587 4.88793 3.45962C4.84593 3.67455 4.77304 3.8701 4.72594 3.89406C4.67883 3.91809 4.424 3.9987 4.15961 4.07323C3.53561 4.2492 2.70575 4.67906 2.17674 5.10032C1.01123 6.02854 0.239471 7.56479 0.0398014 9.35423C-0.0315821 9.99412 -0.00243995 11.3974 0.0912959 11.8357L0.133778 12.0341L0.412462 11.698C0.565804 11.5131 0.70773 11.3791 0.727919 11.4003C0.748168 11.4213 0.764692 11.5698 0.764692 11.73C0.764692 12.2713 0.946877 14.0778 1.09739 15.0286C1.37416 16.7769 1.85539 18.4729 2.41372 19.6673C2.70521 20.2909 3.15676 21.049 3.20603 20.9975C3.22334 20.9794 3.18356 20.7574 3.11752 20.5041C2.37004 17.6375 2.26843 14.8188 2.84401 12.9135C3.25326 11.5588 4.01174 10.5597 5.05798 9.99738L5.49132 9.76451L5.93711 10.0544C6.94447 10.7095 8.29403 11.2711 9.52155 11.5462C10.4375 11.7514 11.9992 11.7838 12.9323 11.6168C13.8547 11.4517 14.5909 11.1985 15.6062 10.6972C16.5921 10.2105 16.9415 10.1003 17.499 10.1003C18.6723 10.1003 19.6155 10.8269 20.3084 12.2646C20.9707 13.6385 21.3864 15.808 21.5585 18.7884C21.5944 19.4091 21.635 19.9287 21.6487 19.9431C21.7047 20.0015 22.5242 18.1096 22.7509 17.3986C23.211 15.9554 23.4602 14.1998 23.4024 12.8091C23.3862 12.4184 23.3932 12.122 23.418 12.1504C23.4428 12.1787 23.563 12.4834 23.6851 12.8274C23.9731 13.6388 24.0274 13.7286 23.9892 13.3311C23.9726 13.1586 23.9293 12.6928 23.8929 12.296C23.5688 8.76114 22.2426 5.70326 20.2304 3.85084C19.125 2.83322 17.8302 2.16472 16.2371 1.78913L15.6363 1.64741L16.0569 1.52025C16.7595 1.3077 16.8135 1.29835 17.559 1.26021L18.2801 1.22332L17.6149 0.961721C15.8136 0.253385 14.0185 -0.0119193 12.5551 0.213925C11.8299 0.325844 11.0013 0.58111 10.4971 0.847858C10.3262 0.938258 10.1759 1.00118 10.163 0.987693C10.1501 0.974268 10.1223 0.873391 10.1013 0.763605C10.0674 0.586819 10.094 0.531613 10.3331 0.281992L10.6032 0L10.2655 0.00608526C10.0799 0.0094102 9.68889 0.0510032 9.39669 0.0984306Z" fill="white" fill-opacity="0.55"/>
                    </svg>
                </div>
                <div class="mainDivRightButton MDRB-Shoes" onclick="removeCloth('Shoes', '6')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8.93198 6.51193C8.75625 6.59724 7.30858 8.56042 7.21782 8.83646C7.06178 9.31119 7.28496 9.66697 8.132 10.2937C8.3669 10.4674 8.54309 10.6354 8.52358 10.667C8.46235 10.766 8.06368 10.9445 7.70151 11.0352C6.85969 11.2458 5.93303 11.2067 5.03414 10.9227C4.32411 10.6985 4.07356 10.4373 3.57504 9.40212C3.34776 8.93009 3.11479 8.52691 3.03844 8.47347C2.72473 8.25375 2.33479 8.42232 1.8208 8.99988C0.44432 10.5465 -0.0735897 12.7946 0.0200431 16.8165C0.0662736 18.8017 0.140395 19.1456 0.611371 19.5592C1.01045 19.9096 1.1785 19.9309 3.94571 19.9823C5.32635 20.008 6.69745 20.0452 6.99258 20.065C7.49473 20.0986 12.0915 20.2443 17.4023 20.3948C18.6752 20.4308 20.7714 20.4644 22.0605 20.4695C24.3093 20.4782 24.4291 20.4731 25.0195 20.3434C27.8992 19.7108 29.6774 18.2667 29.5781 16.641C29.5653 16.4312 29.5042 16.1544 29.4422 16.0254C29.2034 15.5287 28.5321 15.0363 27.6562 14.7153C27.4789 14.6504 26.7539 14.4233 26.0449 14.2106C24.158 13.6445 23.9954 13.5822 22.6322 12.9023C21.4083 12.2919 21.387 12.2781 21.3499 12.0734C21.303 11.8143 21.1414 11.4871 20.9953 11.3548C20.6773 11.0673 20.1185 10.997 19.7126 11.1935L19.4664 11.3127L19.0665 11.1194C18.7204 10.9522 18.6613 10.9005 18.6275 10.7357C18.5648 10.4287 18.3128 10.104 18.0322 9.96808C17.7255 9.8196 17.4305 9.81005 17.1102 9.93814C16.8812 10.0298 16.8636 10.028 16.5007 9.87369L16.127 9.7149L16.0996 9.42925C16.0225 8.62693 15.1499 8.18912 14.42 8.58644L14.1464 8.73533L11.7627 7.59035C10.3343 6.90427 9.31354 6.44695 9.2158 6.44935C9.1261 6.45152 8.99836 6.4797 8.93198 6.51193ZM13.623 12.1223C13.2563 12.352 7.29487 17.6995 7.29487 17.7988C7.29487 17.8923 7.37959 17.8988 8.34955 17.8796C9.07922 17.8653 9.44034 17.8356 9.52143 17.7834C9.70717 17.6637 15.1338 12.7336 15.2605 12.5694C15.4131 12.3718 15.3623 12.3323 14.7324 12.158C14.1037 11.9841 13.8564 11.9761 13.623 12.1223ZM16.6601 13.3741C14.9023 14.9078 11.8359 17.7359 11.8359 17.8235C11.8359 17.9215 11.9061 17.9297 12.746 17.9297C13.3762 17.9297 13.7141 17.9055 13.8448 17.8509C14.025 17.7756 18.3973 13.8291 18.5685 13.5873C18.6164 13.5196 18.6286 13.4514 18.5978 13.4239C18.5116 13.3472 17.4206 13.0665 17.2083 13.0665C17.0562 13.0665 16.9344 13.1348 16.6601 13.3741ZM29.4795 18.7877C28.9681 19.9102 27.1894 20.9124 25.0576 21.2794C24.3814 21.3959 24.2104 21.4002 21.6503 21.3662C18.1945 21.3203 9.45598 21.0704 5.94721 20.9173C5.23823 20.8863 4.10075 20.8607 3.41942 20.8602C2.2718 20.8595 2.13827 20.8482 1.60301 20.7064C0.628012 20.448 0.591918 20.442 0.451703 20.5158C0.256703 20.6185 0.236195 20.6769 0.235199 21.1325C0.234203 21.5944 0.380688 22.1532 0.573754 22.4244C0.743852 22.6633 1.19754 22.8929 1.64233 22.965C1.85086 22.9989 2.65424 23.0268 3.42768 23.0271C4.20112 23.0274 5.44037 23.0535 6.18159 23.0851C9.41567 23.2227 18.0887 23.4731 21.6796 23.5325C24.3035 23.576 24.8234 23.5431 25.9269 23.2644C27.4893 22.8698 28.8444 22.0971 29.4653 21.2467C29.8296 20.7478 29.9633 20.3711 29.9667 19.834C29.9697 19.3664 29.8702 19.033 29.6374 18.7303C29.5591 18.6285 29.5506 18.6315 29.4795 18.7877Z" fill="white" fill-opacity="0.55"/>
                    </svg>
                </div>
                <div class="mainDivRightButton MDRB-Pants" onclick="removeCloth('Pants', '4')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="31" height="30" viewBox="0 0 31 30" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M5.50978 1.75781V3.51562H10.959H16.4082V1.75781V0H10.959H5.50978V1.75781ZM5.49162 8.51074C5.45741 12.1895 5.39366 12.9708 4.96438 14.9707C4.89523 15.293 4.82318 15.639 4.80423 15.7396L4.7699 15.9224L5.18525 15.9624C7.95618 16.2289 10.1818 18.1322 10.8154 20.7772C10.9699 21.4225 10.9704 22.7068 10.8164 23.3496C10.5805 24.3346 10.0705 25.3254 9.43691 26.0304C9.26744 26.2191 9.14102 26.3852 9.15598 26.3996C9.17093 26.4141 9.59609 26.4528 10.1008 26.4856C11.9611 26.6065 13.058 26.8865 18.2549 28.5674C21.5936 29.6472 22.5988 29.9212 23.4366 29.9798C24.4983 30.0541 25.4486 29.7016 26.2234 28.9462C27.1544 28.0385 27.504 26.8395 27.1871 25.6404C26.8971 24.5433 26.3516 23.948 24.7637 22.996C23.2731 22.1023 21.7587 21.1328 20.9881 20.5789C18.5719 18.8422 17.3509 17.224 16.779 15C16.4662 13.7838 16.4509 13.536 16.4246 9.27246L16.4 5.27344H10.9608H5.52165L5.49162 8.51074ZM4.1789 17.8975C4.03644 18.2601 3.81235 19.1002 3.7561 19.4824C3.42189 21.7546 4.5058 24.085 6.49767 25.3765C6.7912 25.5669 7.05319 25.7227 7.07983 25.7227C7.23803 25.7227 7.93178 25.087 8.26297 24.6387C8.59919 24.1835 8.79451 23.8023 8.96937 23.2603C9.15168 22.6949 9.16609 21.572 8.99868 20.9766C8.59961 19.5573 7.5071 18.4243 6.08437 17.9542C5.70844 17.8301 5.44906 17.7883 4.92085 17.7669L4.24109 17.7393L4.1789 17.8975Z" fill="white" fill-opacity="0.55"/>
                    </svg>
                </div>
            </div>
        </div>
        <div id="mouseInfosDiv">
            <div class="mouseInfo">
                <i class="fa-solid fa-arrows-up-down"></i>
                <span class="adjust_camera"></span>
            </div>
            <div class="mouseInfo">
                <i class="fa-solid fa-arrows-rotate"></i>
                <span class="click_to_rotate"></span>
            </div>
            <div class="mouseInfo">
                <i class="fa-solid fa-camera-rotate"></i>
                <span class="use_arrow_to_rotate"></span>
            </div>
            <div class="mouseInfo">
                <i class="fa-solid fa-hand-pointer"></i>
                <span class="click_to_adjust_height"></span>
            </div>
            <div class="mouseInfo">
                <i class="fa-solid fa-magnifying-glass-plus"></i>
                <span class="scroll_to_adjust_zoom"></span>
            </div>
        </div>  
        <div id="pedDiv"></div>
        <div id="pedDiv2"></div>
        <div id="pedDiv3"></div>
        <div id="mainDivDialog">
            <h4 class="are_you_sure"></h4>
            <span id="mainDivDialogSpan" class="confirm_character_creation"></span>
            <div id="mainDivDialogButtons">
                <div class="mainDivDialogButtonGreen" onclick="finalizeCharacter()"></div>
                <div class="mainDivDialogButtonRed" onclick="discardCharacterCreation()"></div>
                <div class="mainDivDialogButtonRed" onclick="closeDialog()"></div>
            </div>
        </div>
        <div id="mainDivOutsideButtons">
            <div class="mainDivOutsideButtonDiv" id="mainDivOutsideButtonDiv-ClothCompare">
                <div id="mainDivOutsideButton-ClothCompareMenu" class="mainDivOutsideButton" onclick="showClothCompareMenu()"><i class="fa-solid fa-mitten"></i></div>
                <div id="mainDivOutsideButton-ClothCompare1" class="mainDivOutsideButton mainDivOutsideButton-ClothCompare1" style="display: none;" onclick="saveClothingToCompare(1)"><i class="fa-solid fa-square-1"></i></div>
                <div id="mainDivOutsideButton-ClothCompare2" class="mainDivOutsideButton mainDivOutsideButton-ClothCompare2" style="display: none;" onclick="saveClothingToCompare(2)"><i class="fa-solid fa-square-2"></i></div>
                <div id="mainDivOutsideButton-ClothCompareConfirm" class="mainDivOutsideButton mainDivOutsideButton-ClothCompareConfirm" style="display: none;" onclick="confirmCompare()"><i class="fa-solid fa-code-compare"></i></div>
            </div>
            <div id="mainDivOutsideButton-showMouseInfos" class="mainDivOutsideButton" onclick="showMouseInfos()"><i class="fa-solid fa-computer-mouse-scrollwheel"></i></div>
            <div id="mainDivOutsideButton-showClothRemover" class="mainDivOutsideButton" onclick="showClothRemover()"><i class="fa-solid fa-clothes-hanger"></i></div>
            <div id="mainDivOutsideButton-3DMenu" class="mainDivOutsideButton" onclick="enable3DMenu()"><i class="fa-solid fa-cube"></i></div>
            <div id="mainDivOutsideButton-OutfitMenu" class="mainDivOutsideButton" onclick="openOutfitMenu()"><i class="fa-solid fa-user-tie"></i></div>
        </div>
        <div id="animPosInfoDiv">
            <div id="APIDKeyDiv">
                <div id="APIDKeyDivLeft"><span>ESC</span></div>
                <div id="APIDKeyDivRight"><span>Close Comparing Clothes</span></div>
            </div>
            <div id="APIDKeyDiv">
                <div id="APIDKeyDivLeft"><span>Enter</span></div>
                <div id="APIDKeyDivRight"><span>Confirm Selected Cloth</span></div>
            </div>
        </div>
        <script src="index.js"></script>
    </body>
</html>