{"manifest": {"name": "@types/accepts", "version": "1.3.5", "description": "TypeScript definitions for accepts", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/bomret"}, {"name": "Brice BERNARD", "url": "https://github.com/brikou"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "0c1b369f923466e74bec6da148c76a05cc138923a142ea737eff8cf2b29b559f", "typeScriptVersion": "2.0", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-accepts-1.3.5-c34bec115cfc746e04fe5a059df4ce7e7b391575-integrity\\node_modules\\@types\\accepts\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/accepts`\n\n# Summary\nThis package contains type definitions for accepts (https://github.com/jshttp/accepts).\n\n# Details\nFiles were exported from https://www.github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/accepts\n\nAdditional Details\n * Last updated: Tue, 13 Feb 2018 02:54:18 GMT\n * Dependencies: http, node\n * Global values: none\n\n# Credits\nThese definitions were written by <PERSON> <https://github.com/bomret>, Brice BERNARD <https://github.com/brikou>.\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation. All rights reserved.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/accepts/-/accepts-1.3.5.tgz#c34bec115cfc746e04fe5a059df4ce7e7b391575", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/accepts/-/accepts-1.3.5.tgz", "hash": "c34bec115cfc746e04fe5a059df4ce7e7b391575", "integrity": "sha512-jOdnI/3qTpHABjM5cx1Hc0sKsPoYCp+DP/GJRGtDlPd7fiV9oXGGIcjW/ZOxLIvjGz8MA+uMZI9metHlgqbgwQ==", "registry": "npm", "packageName": "@types/accepts", "cacheIntegrity": "sha512-jOdnI/3qTpHABjM5cx1Hc0sKsPoYCp+DP/GJRGtDlPd7fiV9oXGGIcjW/ZOxLIvjGz8MA+uMZI9metHlgqbgwQ== sha1-w0vsEVz8dG4E/loFnfTOfns5FXU="}, "registry": "npm", "hash": "c34bec115cfc746e04fe5a059df4ce7e7b391575"}