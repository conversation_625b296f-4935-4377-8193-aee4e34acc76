{"manifest": {"name": "@citizenfx/server", "version": "1.0.3404-1", "description": "Typings for the CitizenFX server JS API.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": {}, "license": "ISC", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@citizenfx-server-1.0.3404-1-8f902c170795d542128d49f7b0387af08112c533-integrity\\node_modules\\@citizenfx\\server\\package.json", "readmeFilename": "README.md", "readme": "# FiveM server typings"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@citizenfx/server/-/server-1.0.3404-1.tgz#8f902c170795d542128d49f7b0387af08112c533", "type": "tarball", "reference": "https://registry.yarnpkg.com/@citizenfx/server/-/server-1.0.3404-1.tgz", "hash": "8f902c170795d542128d49f7b0387af08112c533", "integrity": "sha512-Xa7g1U0olTyYhxtiHntt703co0RbMUxHKa3LGQk/4dz2KLcEXJSDZz75m8gnIdJG3a1lyT8PmOxEZJORO989qw==", "registry": "npm", "packageName": "@citizenfx/server", "cacheIntegrity": "sha512-Xa7g1U0olTyYhxtiHntt703co0RbMUxHKa3LGQk/4dz2KLcEXJSDZz75m8gnIdJG3a1lyT8PmOxEZJORO989qw== sha1-j5AsFweV1UISjUn3sDh68IESxTM="}, "registry": "npm", "hash": "8f902c170795d542128d49f7b0387af08112c533"}