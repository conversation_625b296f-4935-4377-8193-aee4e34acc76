{"manifest": {"name": "source-map-resolve", "version": "0.5.3", "author": {"name": "<PERSON>"}, "license": "MIT", "description": "Resolve the source map and/or sources for a generated file.", "keywords": ["source map", "sourcemap", "source", "map", "sourceMappingURL", "resolve", "resolver", "locate", "locator", "find", "finder"], "repository": {"type": "git", "url": "https://github.com/lydell/source-map-resolve.git"}, "main": "lib/source-map-resolve-node.js", "browser": "source-map-resolve.js", "files": ["lib", "source-map-resolve.js"], "scripts": {"lint": "jshint lib/ test/", "unit": "node test/source-map-resolve.js && node test/windows.js", "test": "npm run lint && npm run unit", "build": "node generate-source-map-resolve.js"}, "dependencies": {"atob": "^2.1.2", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}, "devDependencies": {"Base64": "1.1.0", "jshint": "2.10.3", "setimmediate": "1.0.5", "simple-asyncify": "1.0.0", "tape": "4.12.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-source-map-resolve-0.5.3-190866bece7553e1f8f267a2ee82c606b5509a1a-integrity\\node_modules\\source-map-resolve\\package.json", "readmeFilename": "readme.md", "readme": "Overview [![Build Status](https://travis-ci.org/lydell/source-map-resolve.svg?branch=master)](https://travis-ci.org/lydell/source-map-resolve)\n========\n\nResolve the source map and/or sources for a generated file.\n\n```js\nvar sourceMapResolve = require(\"source-map-resolve\")\nvar sourceMap        = require(\"source-map\")\n\nvar code = [\n  \"!function(){...}();\",\n  \"/*# sourceMappingURL=foo.js.map */\"\n].join(\"\\n\")\n\nsourceMapResolve.resolveSourceMap(code, \"/js/foo.js\", fs.readFile, function(error, result) {\n  if (error) {\n    return notifyFailure(error)\n  }\n  result\n  // {\n  //   map: {file: \"foo.js\", mappings: \"...\", sources: [\"/coffee/foo.coffee\"], names: []},\n  //   url: \"/js/foo.js.map\",\n  //   sourcesRelativeTo: \"/js/foo.js.map\",\n  //   sourceMappingURL: \"foo.js.map\"\n  // }\n\n  sourceMapResolve.resolveSources(result.map, result.sourcesRelativeTo, fs.readFile, function(error, result) {\n    if (error) {\n      return notifyFailure(error)\n    }\n    result\n    // {\n    //   sourcesResolved: [\"/coffee/foo.coffee\"],\n    //   sourcesContent: [\"<contents of /coffee/foo.coffee>\"]\n    // }\n  })\n})\n\nsourceMapResolve.resolve(code, \"/js/foo.js\", fs.readFile, function(error, result) {\n  if (error) {\n    return notifyFailure(error)\n  }\n  result\n  // {\n  //   map: {file: \"foo.js\", mappings: \"...\", sources: [\"/coffee/foo.coffee\"], names: []},\n  //   url: \"/js/foo.js.map\",\n  //   sourcesRelativeTo: \"/js/foo.js.map\",\n  //   sourceMappingURL: \"foo.js.map\",\n  //   sourcesResolved: [\"/coffee/foo.coffee\"],\n  //   sourcesContent: [\"<contents of /coffee/foo.coffee>\"]\n  // }\n  result.map.sourcesContent = result.sourcesContent\n  var map = new sourceMap.sourceMapConsumer(result.map)\n  map.sourceContentFor(\"/coffee/foo.coffee\")\n  // \"<contents of /coffee/foo.coffee>\"\n})\n```\n\n\nInstallation\n============\n\n- `npm install source-map-resolve`\n- `bower install source-map-resolve`\n- `component install lydell/source-map-resolve`\n\nWorks with CommonJS, AMD and browser globals, through UMD.\n\nNote: This module requires `setImmediate` and `atob`.\nUse polyfills if needed, such as:\n\n- <https://github.com/NobleJS/setImmediate>\n- <https://github.com/davidchambers/Base64.js>\n\n\nUsage\n=====\n\n### `sourceMapResolve.resolveSourceMap(code, codeUrl, read, callback)` ###\n\n- `code` is a string of code that may or may not contain a sourceMappingURL\n  comment. Such a comment is used to resolve the source map.\n- `codeUrl` is the url to the file containing `code`. If the sourceMappingURL\n  is relative, it is resolved against `codeUrl`.\n- `read(url, callback)` is a function that reads `url` and responds using\n  `callback(error, content)`. In Node.js you might want to use `fs.readFile`,\n  while in the browser you might want to use an asynchronus `XMLHttpRequest`.\n- `callback(error, result)` is a function that is invoked with either an error\n  or `null` and the result.\n\nThe result is an object with the following properties:\n\n- `map`: The source map for `code`, as an object (not a string).\n- `url`: The url to the source map. If the source map came from a data uri,\n  this property is `null`, since then there is no url to it.\n- `sourcesRelativeTo`: The url that the sources of the source map are relative\n  to. Since the sources are relative to the source map, and the url to the\n  source map is provided as the `url` property, this property might seem\n  superfluos. However, remember that the `url` property can be `null` if the\n  source map came from a data uri. If so, the sources are relative to the file\n  containing the data uri—`codeUrl`. This property will be identical to the\n  `url` property or `codeUrl`, whichever is appropriate. This way you can\n  conveniently resolve the sources without having to think about where the\n  source map came from.\n- `sourceMappingURL`: The url of the sourceMappingURL comment in `code`.\n\nIf `code` contains no sourceMappingURL, the result is `null`.\n\n### `sourceMapResolve.resolveSources(map, mapUrl, read, [options], callback)` ###\n\n- `map` is a source map, as an object (not a string).\n- `mapUrl` is the url to the file containing `map`. Relative sources in the\n  source map, if any, are resolved against `mapUrl`.\n- `read(url, callback)` is a function that reads `url` and responds using\n  `callback(error, content)`. In Node.js you might want to use `fs.readFile`,\n  while in the browser you might want to use an asynchronus `XMLHttpRequest`.\n- `options` is an optional object with any of the following properties:\n  - `sourceRoot`: Override the `sourceRoot` property of the source map, which\n    might only be relevant when resolving sources in the browser. This lets you\n    bypass it when using the module outside of a browser, if needed. Pass a\n    string to replace the `sourceRoot` property with, or `false` to ignore it.\n    Defaults to `undefined`.\n- `callback(error, result)` is a function that is invoked with either an error\n  or `null` and the result.\n\nThe result is an object with the following properties:\n\n- `sourcesResolved`: The same as `map.sources`, except all the sources are\n  fully resolved.\n- `sourcesContent`: An array with the contents of all sources in `map.sources`,\n  in the same order as `map.sources`. If getting the contents of a source fails,\n  an error object is put into the array instead.\n\n### `sourceMapResolve.resolve(code, codeUrl, read, [options], callback)` ###\n\nThe arguments are identical to `sourceMapResolve.resolveSourceMap`, except that\nyou may also provide the same `options` as in `sourceMapResolve.resolveSources`.\n\nThis is a convenience method that first resolves the source map and then its\nsources. You could also do this by first calling\n`sourceMapResolve.resolveSourceMap` and then `sourceMapResolve.resolveSources`.\n\nThe result is identical to `sourceMapResolve.resolveSourceMap`, with the\nproperties from `sourceMapResolve.resolveSources` merged into it.\n\nThere is one extra feature available, though. If `code` is `null`, `codeUrl` is\ntreated as a url to the source map instead of to `code`, and will be read. This\nis handy if you _sometimes_ get the source map url from the `SourceMap: <url>`\nheader (see the [Notes] section). In this case, the `sourceMappingURL` property\nof the result is `null`.\n\n\n[Notes]: #notes\n\n### `sourceMapResolve.*Sync()` ###\n\nThere are also sync versions of the three previous functions. They are identical\nto the async versions, except:\n\n- They expect a sync reading function. In Node.js you might want to use\n  `fs.readFileSync`, while in the browser you might want to use a synchronus\n  `XMLHttpRequest`.\n- They throw errors and return the result instead of using a callback.\n\n`sourceMapResolve.resolveSourcesSync` also accepts `null` as the `read`\nparameter. The result is the same as when passing a function as the `read\nparameter`, except that the `sourcesContent` property of the result will be an\nempty array. In other words, the sources aren’t read. You only get the\n`sourcesResolved` property. (This only supported in the synchronus version, since\nthere is no point doing it asynchronusly.)\n\n### `sourceMapResolve.parseMapToJSON(string, [data])` ###\n\nThe spec says that if a source map (as a string) starts with `)]}'`, it should\nbe stripped off. This is to prevent XSSI attacks. This function does that and\nreturns the result of `JSON.parse`ing what’s left.\n\nIf this function throws `error`, `error.sourceMapData === data`.\n\n### Errors\n\nAll errors passed to callbacks or thrown by this module have a `sourceMapData`\nproperty that contain as much as possible of the intended result of the function\nup until the error occurred.\n\nNote that while the `map` property of result objects always is an object,\n`error.sourceMapData.map` will be a string if parsing that string fails.\n\n\nNote\n====\n\nThis module resolves the source map for a given generated file by looking for a\nsourceMappingURL comment. The spec defines yet a way to provide the URL to the\nsource map: By sending the `SourceMap: <url>` header along with the generated\nfile. Since this module doesn’t retrive the generated code for you (instead\n_you_ give the generated code to the module), it’s up to you to look for such a\nheader when you retrieve the file (should the need arise).\n\n\nDevelopment\n===========\n\nTests\n-----\n\nFirst off, run `npm install` to install testing modules and browser polyfills.\n\n`npm test` lints the code and runs the test suite in Node.js.\n\nx-package.json5\n---------------\n\npackage.json, component.json and bower.json are all generated from\nx-package.json5 by using [`xpkg`]. Only edit x-package.json5, and remember to\nrun `xpkg` before commiting!\n\n[`xpkg`]: https://github.com/kof/node-xpkg\n\nGenerating the browser version\n------------------------------\n\nsource-map-resolve.js is generated from source-map-resolve-node.js and\nsource-map-resolve-template.js. Only edit the two latter files, _not_\nsource-map-resolve.js! To generate it, run `npm run build`.\n\n\nLicense\n=======\n\n[MIT](LICENSE).\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014, 2015, 2016, 2017, 2018, 2019 <PERSON>\nCopyright (c) 2019 ZHAO Jinxiang\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPY<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/source-map-resolve/-/source-map-resolve-0.5.3.tgz#190866bece7553e1f8f267a2ee82c606b5509a1a", "type": "tarball", "reference": "https://registry.yarnpkg.com/source-map-resolve/-/source-map-resolve-0.5.3.tgz", "hash": "190866bece7553e1f8f267a2ee82c606b5509a1a", "integrity": "sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw==", "registry": "npm", "packageName": "source-map-resolve", "cacheIntegrity": "sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw== sha1-GQhmvs51U+H48mei7oLGBrVQmho="}, "registry": "npm", "hash": "190866bece7553e1f8f267a2ee82c606b5509a1a"}