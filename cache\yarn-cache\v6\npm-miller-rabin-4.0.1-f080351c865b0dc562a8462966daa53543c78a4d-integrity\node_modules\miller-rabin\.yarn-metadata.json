{"manifest": {"name": "miller-rabin", "version": "4.0.1", "description": "<PERSON> algorithm for primality test", "main": "lib/mr.js", "bin": {"miller-rabin": "bin\\miller-rabin"}, "scripts": {"test": "mocha --reporter=spec test/**/*-test.js"}, "repository": {"type": "git", "url": "**************:indutny/miller-rabin"}, "keywords": ["prime", "miller-rabin", "bignumber"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/indutny/miller-rabin/issues"}, "homepage": "https://github.com/indutny/miller-rabin", "devDependencies": {"mocha": "^2.0.1"}, "dependencies": {"bn.js": "^4.0.0", "brorand": "^1.0.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-miller-rabin-4.0.1-f080351c865b0dc562a8462966daa53543c78a4d-integrity\\node_modules\\miller-rabin\\package.json", "readmeFilename": "README.md", "readme": "# Miller-Rabin\n\n#### LICENSE\n\nThis software is licensed under the MIT License.\n\nCopyright Fedor Indutny, 2014.\n\nPermission is hereby granted, free of charge, to any person obtaining a\ncopy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to permit\npersons to whom the Software is furnished to do so, subject to the\nfollowing conditions:\n\nThe above copyright notice and this permission notice shall be included\nin all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\nOR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN\nNO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\nDAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\nOTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\nUSE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/miller-rabin/-/miller-rabin-4.0.1.tgz#f080351c865b0dc562a8462966daa53543c78a4d", "type": "tarball", "reference": "https://registry.yarnpkg.com/miller-rabin/-/miller-rabin-4.0.1.tgz", "hash": "f080351c865b0dc562a8462966daa53543c78a4d", "integrity": "sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA==", "registry": "npm", "packageName": "miller-rabin", "cacheIntegrity": "sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA== sha1-8IA1HIZbDcViqEYpZtqlNUPHik0="}, "registry": "npm", "hash": "f080351c865b0dc562a8462966daa53543c78a4d"}