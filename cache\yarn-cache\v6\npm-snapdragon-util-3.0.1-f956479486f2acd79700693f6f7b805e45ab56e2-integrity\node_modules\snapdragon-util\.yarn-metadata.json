{"manifest": {"name": "snapdragon-util", "description": "Utilities for the snapdragon parser/compiler.", "version": "3.0.1", "homepage": "https://github.com/jonschlinkert/snapdragon-util", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/snapdragon-util.git"}, "bugs": {"url": "https://github.com/jonschlinkert/snapdragon-util/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"kind-of": "^3.2.0"}, "devDependencies": {"define-property": "^1.0.0", "gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.12", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.0", "isobject": "^3.0.0", "mocha": "^3.3.0", "snapdragon": "^0.11.0", "snapdragon-node": "^1.0.6"}, "keywords": ["capture", "compile", "compiler", "convert", "match", "parse", "parser", "plugin", "render", "snapdragon", "snapdragonplugin", "transform", "util"], "verb": {"toc": "collapsible", "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-snapdragon-util-3.0.1-f956479486f2acd79700693f6f7b805e45ab56e2-integrity\\node_modules\\snapdragon-util\\package.json", "readmeFilename": "README.md", "readme": "# snapdragon-util [![NPM version](https://img.shields.io/npm/v/snapdragon-util.svg?style=flat)](https://www.npmjs.com/package/snapdragon-util) [![NPM monthly downloads](https://img.shields.io/npm/dm/snapdragon-util.svg?style=flat)](https://npmjs.org/package/snapdragon-util) [![NPM total downloads](https://img.shields.io/npm/dt/snapdragon-util.svg?style=flat)](https://npmjs.org/package/snapdragon-util) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/snapdragon-util.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/snapdragon-util)\n\n> Utilities for the snapdragon parser/compiler.\n\n<details>\n<summary><strong>Table of Contents</strong></summary>\n\n- [Install](#install)\n- [Usage](#usage)\n- [API](#api)\n- [Release history](#release-history)\n  * [[3.0.0] - 2017-05-01](#300---2017-05-01)\n  * [[0.1.0]](#010)\n- [About](#about)\n\n</details>\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save snapdragon-util\n```\n\nInstall with [yarn](https://yarnpkg.com):\n\n```sh\n$ yarn add snapdragon-util\n```\n\n## Usage\n\n```js\nvar util = require('snapdragon-util');\n```\n\n## API\n\n### [.isNode](index.js#L21)\n\nReturns true if the given value is a node.\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nvar Node = require('snapdragon-node');\nvar node = new Node({type: 'foo'});\nconsole.log(utils.isNode(node)); //=> true\nconsole.log(utils.isNode({})); //=> false\n```\n\n### [.noop](index.js#L37)\n\nEmit an empty string for the given `node`.\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `returns` **{undefined}**\n\n**Example**\n\n```js\n// do nothing for beginning-of-string\nsnapdragon.compiler.set('bos', utils.noop);\n```\n\n### [.identity](index.js#L53)\n\nAppdend `node.val` to `compiler.output`, exactly as it was created by the parser.\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `returns` **{undefined}**\n\n**Example**\n\n```js\nsnapdragon.compiler.set('text', utils.identity);\n```\n\n### [.append](index.js#L76)\n\nPreviously named `.emit`, this method appends the given `val` to `compiler.output` for the given node. Useful when you know what value should be appended advance, regardless of the actual value of `node.val`.\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `returns` **{Function}**: Returns a compiler middleware function.\n\n**Example**\n\n```js\nsnapdragon.compiler\n  .set('i', function(node) {\n    this.mapVisit(node);\n  })\n  .set('i.open', utils.append('<i>'))\n  .set('i.close', utils.append('</i>'))\n```\n\n### [.toNoop](index.js#L99)\n\nUsed in compiler middleware, this onverts an AST node into an empty `text` node and deletes `node.nodes` if it exists. The advantage of this method is that, as opposed to completely removing the node, indices will not need to be re-calculated in sibling nodes, and nothing is appended to the output.\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `nodes` **{Array}**: Optionally pass a new `nodes` value, to replace the existing `node.nodes` array.\n\n**Example**\n\n```js\nutils.toNoop(node);\n// convert `node.nodes` to the given value instead of deleting it\nutils.toNoop(node, []);\n```\n\n### [.visit](index.js#L128)\n\nVisit `node` with the given `fn`. The built-in `.visit` method in snapdragon automatically calls registered compilers, this allows you to pass a visitor function.\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `fn` **{Function}**\n* `returns` **{Object}**: returns the node after recursively visiting all child nodes.\n\n**Example**\n\n```js\nsnapdragon.compiler.set('i', function(node) {\n  utils.visit(node, function(childNode) {\n    // do stuff with \"childNode\"\n    return childNode;\n  });\n});\n```\n\n### [.mapVisit](index.js#L155)\n\nMap [visit](#visit) the given `fn` over `node.nodes`. This is called by [visit](#visit), use this method if you do not want `fn` to be called on the first node.\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `options` **{Object}**\n* `fn` **{Function}**\n* `returns` **{Object}**: returns the node\n\n**Example**\n\n```js\nsnapdragon.compiler.set('i', function(node) {\n  utils.mapVisit(node, function(childNode) {\n    // do stuff with \"childNode\"\n    return childNode;\n  });\n});\n```\n\n### [.addOpen](index.js#L194)\n\nUnshift an `*.open` node onto `node.nodes`.\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `Node` **{Function}**: (required) Node constructor function from [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node).\n* `filter` **{Function}**: Optionaly specify a filter function to exclude the node.\n* `returns` **{Object}**: Returns the created opening node.\n\n**Example**\n\n```js\nvar Node = require('snapdragon-node');\nsnapdragon.parser.set('brace', function(node) {\n  var match = this.match(/^{/);\n  if (match) {\n    var parent = new Node({type: 'brace'});\n    utils.addOpen(parent, Node);\n    console.log(parent.nodes[0]):\n    // { type: 'brace.open', val: '' };\n\n    // push the parent \"brace\" node onto the stack\n    this.push(parent);\n\n    // return the parent node, so it's also added to the AST\n    return brace;\n  }\n});\n```\n\n### [.addClose](index.js#L244)\n\nPush a `*.close` node onto `node.nodes`.\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `Node` **{Function}**: (required) Node constructor function from [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node).\n* `filter` **{Function}**: Optionaly specify a filter function to exclude the node.\n* `returns` **{Object}**: Returns the created closing node.\n\n**Example**\n\n```js\nvar Node = require('snapdragon-node');\nsnapdragon.parser.set('brace', function(node) {\n  var match = this.match(/^}/);\n  if (match) {\n    var parent = this.parent();\n    if (parent.type !== 'brace') {\n      throw new Error('missing opening: ' + '}');\n    }\n\n    utils.addClose(parent, Node);\n    console.log(parent.nodes[parent.nodes.length - 1]):\n    // { type: 'brace.close', val: '' };\n\n    // no need to return a node, since the parent\n    // was already added to the AST\n    return;\n  }\n});\n```\n\n### [.wrapNodes](index.js#L274)\n\nWraps the given `node` with `*.open` and `*.close` nodes.\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `Node` **{Function}**: (required) Node constructor function from [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node).\n* `filter` **{Function}**: Optionaly specify a filter function to exclude the node.\n* `returns` **{Object}**: Returns the node\n\n### [.pushNode](index.js#L299)\n\nPush the given `node` onto `parent.nodes`, and set `parent` as `node.parent.\n\n**Params**\n\n* `parent` **{Object}**\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `returns` **{Object}**: Returns the child node\n\n**Example**\n\n```js\nvar parent = new Node({type: 'foo'});\nvar node = new Node({type: 'bar'});\nutils.pushNode(parent, node);\nconsole.log(parent.nodes[0].type) // 'bar'\nconsole.log(node.parent.type) // 'foo'\n```\n\n### [.unshiftNode](index.js#L325)\n\nUnshift `node` onto `parent.nodes`, and set `parent` as `node.parent.\n\n**Params**\n\n* `parent` **{Object}**\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `returns` **{undefined}**\n\n**Example**\n\n```js\nvar parent = new Node({type: 'foo'});\nvar node = new Node({type: 'bar'});\nutils.unshiftNode(parent, node);\nconsole.log(parent.nodes[0].type) // 'bar'\nconsole.log(node.parent.type) // 'foo'\n```\n\n### [.popNode](index.js#L354)\n\nPop the last `node` off of `parent.nodes`. The advantage of using this method is that it checks for `node.nodes` and works with any version of `snapdragon-node`.\n\n**Params**\n\n* `parent` **{Object}**\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `returns` **{Number|Undefined}**: Returns the length of `node.nodes` or undefined.\n\n**Example**\n\n```js\nvar parent = new Node({type: 'foo'});\nutils.pushNode(parent, new Node({type: 'foo'}));\nutils.pushNode(parent, new Node({type: 'bar'}));\nutils.pushNode(parent, new Node({type: 'baz'}));\nconsole.log(parent.nodes.length); //=> 3\nutils.popNode(parent);\nconsole.log(parent.nodes.length); //=> 2\n```\n\n### [.shiftNode](index.js#L382)\n\nShift the first `node` off of `parent.nodes`. The advantage of using this method is that it checks for `node.nodes` and works with any version of `snapdragon-node`.\n\n**Params**\n\n* `parent` **{Object}**\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `returns` **{Number|Undefined}**: Returns the length of `node.nodes` or undefined.\n\n**Example**\n\n```js\nvar parent = new Node({type: 'foo'});\nutils.pushNode(parent, new Node({type: 'foo'}));\nutils.pushNode(parent, new Node({type: 'bar'}));\nutils.pushNode(parent, new Node({type: 'baz'}));\nconsole.log(parent.nodes.length); //=> 3\nutils.shiftNode(parent);\nconsole.log(parent.nodes.length); //=> 2\n```\n\n### [.removeNode](index.js#L409)\n\nRemove the specified `node` from `parent.nodes`.\n\n**Params**\n\n* `parent` **{Object}**\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `returns` **{Object|undefined}**: Returns the removed node, if successful, or undefined if it does not exist on `parent.nodes`.\n\n**Example**\n\n```js\nvar parent = new Node({type: 'abc'});\nvar foo = new Node({type: 'foo'});\nutils.pushNode(parent, foo);\nutils.pushNode(parent, new Node({type: 'bar'}));\nutils.pushNode(parent, new Node({type: 'baz'}));\nconsole.log(parent.nodes.length); //=> 3\nutils.removeNode(parent, foo);\nconsole.log(parent.nodes.length); //=> 2\n```\n\n### [.isType](index.js#L443)\n\nReturns true if `node.type` matches the given `type`. Throws a `TypeError` if `node` is not an instance of `Node`.\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `type` **{String}**\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nvar Node = require('snapdragon-node');\nvar node = new Node({type: 'foo'});\nconsole.log(utils.isType(node, 'foo')); // false\nconsole.log(utils.isType(node, 'bar')); // true\n```\n\n### [.hasType](index.js#L486)\n\nReturns true if the given `node` has the given `type` in `node.nodes`. Throws a `TypeError` if `node` is not an instance of `Node`.\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `type` **{String}**\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nvar Node = require('snapdragon-node');\nvar node = new Node({\n  type: 'foo',\n  nodes: [\n    new Node({type: 'bar'}),\n    new Node({type: 'baz'})\n  ]\n});\nconsole.log(utils.hasType(node, 'xyz')); // false\nconsole.log(utils.hasType(node, 'baz')); // true\n```\n\n### [.firstOfType](index.js#L519)\n\nReturns the first node from `node.nodes` of the given `type`\n\n**Params**\n\n* `nodes` **{Array}**\n* `type` **{String}**\n* `returns` **{Object|undefined}**: Returns the first matching node or undefined.\n\n**Example**\n\n```js\nvar node = new Node({\n  type: 'foo',\n  nodes: [\n    new Node({type: 'text', val: 'abc'}),\n    new Node({type: 'text', val: 'xyz'})\n  ]\n});\n\nvar textNode = utils.firstOfType(node.nodes, 'text');\nconsole.log(textNode.val);\n//=> 'abc'\n```\n\n### [.findNode](index.js#L556)\n\nReturns the node at the specified index, or the first node of the given `type` from `node.nodes`.\n\n**Params**\n\n* `nodes` **{Array}**\n* `type` **{String|Number}**: Node type or index.\n* `returns` **{Object}**: Returns a node or undefined.\n\n**Example**\n\n```js\nvar node = new Node({\n  type: 'foo',\n  nodes: [\n    new Node({type: 'text', val: 'abc'}),\n    new Node({type: 'text', val: 'xyz'})\n  ]\n});\n\nvar nodeOne = utils.findNode(node.nodes, 'text');\nconsole.log(nodeOne.val);\n//=> 'abc'\n\nvar nodeTwo = utils.findNode(node.nodes, 1);\nconsole.log(nodeTwo.val);\n//=> 'xyz'\n```\n\n### [.isOpen](index.js#L584)\n\nReturns true if the given node is an \"*.open\" node.\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nvar Node = require('snapdragon-node');\nvar brace = new Node({type: 'brace'});\nvar open = new Node({type: 'brace.open'});\nvar close = new Node({type: 'brace.close'});\n\nconsole.log(utils.isOpen(brace)); // false\nconsole.log(utils.isOpen(open)); // true\nconsole.log(utils.isOpen(close)); // false\n```\n\n### [.isClose](index.js#L607)\n\nReturns true if the given node is a \"*.close\" node.\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nvar Node = require('snapdragon-node');\nvar brace = new Node({type: 'brace'});\nvar open = new Node({type: 'brace.open'});\nvar close = new Node({type: 'brace.close'});\n\nconsole.log(utils.isClose(brace)); // false\nconsole.log(utils.isClose(open)); // false\nconsole.log(utils.isClose(close)); // true\n```\n\n### [.hasOpen](index.js#L633)\n\nReturns true if `node.nodes` **has** an `.open` node\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nvar Node = require('snapdragon-node');\nvar brace = new Node({\n  type: 'brace',\n  nodes: []\n});\n\nvar open = new Node({type: 'brace.open'});\nconsole.log(utils.hasOpen(brace)); // false\n\nbrace.pushNode(open);\nconsole.log(utils.hasOpen(brace)); // true\n```\n\n### [.hasClose](index.js#L663)\n\nReturns true if `node.nodes` **has** a `.close` node\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nvar Node = require('snapdragon-node');\nvar brace = new Node({\n  type: 'brace',\n  nodes: []\n});\n\nvar close = new Node({type: 'brace.close'});\nconsole.log(utils.hasClose(brace)); // false\n\nbrace.pushNode(close);\nconsole.log(utils.hasClose(brace)); // true\n```\n\n### [.hasOpenAndClose](index.js#L697)\n\nReturns true if `node.nodes` has both `.open` and `.close` nodes\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nvar Node = require('snapdragon-node');\nvar brace = new Node({\n  type: 'brace',\n  nodes: []\n});\n\nvar open = new Node({type: 'brace.open'});\nvar close = new Node({type: 'brace.close'});\nconsole.log(utils.hasOpen(brace)); // false\nconsole.log(utils.hasClose(brace)); // false\n\nbrace.pushNode(open);\nbrace.pushNode(close);\nconsole.log(utils.hasOpen(brace)); // true\nconsole.log(utils.hasClose(brace)); // true\n```\n\n### [.addType](index.js#L719)\n\nPush the given `node` onto the `state.inside` array for the given type. This array is used as a specialized \"stack\" for only the given `node.type`.\n\n**Params**\n\n* `state` **{Object}**: The `compiler.state` object or custom state object.\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `returns` **{Array}**: Returns the `state.inside` stack for the given type.\n\n**Example**\n\n```js\nvar state = { inside: {}};\nvar node = new Node({type: 'brace'});\nutils.addType(state, node);\nconsole.log(state.inside);\n//=> { brace: [{type: 'brace'}] }\n```\n\n### [.removeType](index.js#L759)\n\nRemove the given `node` from the `state.inside` array for the given type. This array is used as a specialized \"stack\" for only the given `node.type`.\n\n**Params**\n\n* `state` **{Object}**: The `compiler.state` object or custom state object.\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `returns` **{Array}**: Returns the `state.inside` stack for the given type.\n\n**Example**\n\n```js\nvar state = { inside: {}};\nvar node = new Node({type: 'brace'});\nutils.addType(state, node);\nconsole.log(state.inside);\n//=> { brace: [{type: 'brace'}] }\nutils.removeType(state, node);\n//=> { brace: [] }\n```\n\n### [.isEmpty](index.js#L788)\n\nReturns true if `node.val` is an empty string, or `node.nodes` does not contain any non-empty text nodes.\n\n**Params**\n\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `fn` **{Function}**\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nvar node = new Node({type: 'text'});\nutils.isEmpty(node); //=> true\nnode.val = 'foo';\nutils.isEmpty(node); //=> false\n```\n\n### [.isInsideType](index.js#L833)\n\nReturns true if the `state.inside` stack for the given type exists and has one or more nodes on it.\n\n**Params**\n\n* `state` **{Object}**\n* `type` **{String}**\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nvar state = { inside: {}};\nvar node = new Node({type: 'brace'});\nconsole.log(utils.isInsideType(state, 'brace')); //=> false\nutils.addType(state, node);\nconsole.log(utils.isInsideType(state, 'brace')); //=> true\nutils.removeType(state, node);\nconsole.log(utils.isInsideType(state, 'brace')); //=> false\n```\n\n### [.isInside](index.js#L867)\n\nReturns true if `node` is either a child or grand-child of the given `type`, or `state.inside[type]` is a non-empty array.\n\n**Params**\n\n* `state` **{Object}**: Either the `compiler.state` object, if it exists, or a user-supplied state object.\n* `node` **{Object}**: Instance of [snapdragon-node](https://github.com/jonschlinkert/snapdragon-node)\n* `type` **{String}**: The `node.type` to check for.\n* `returns` **{Boolean}**\n\n**Example**\n\n```js\nvar state = { inside: {}};\nvar node = new Node({type: 'brace'});\nvar open = new Node({type: 'brace.open'});\nconsole.log(utils.isInside(state, open, 'brace')); //=> false\nutils.pushNode(node, open);\nconsole.log(utils.isInside(state, open, 'brace')); //=> true\n```\n\n### [.last](index.js#L915)\n\nGet the last `n` element from the given `array`. Used for getting\na node from `node.nodes.`\n\n**Params**\n\n* `array` **{Array}**\n* `n` **{Number}**\n* `returns` **{undefined}**\n\n### [.arrayify](index.js#L935)\n\nCast the given `val` to an array.\n\n**Params**\n\n* `val` **{any}**\n* `returns` **{Array}**\n\n**Example**\n\n```js\nconsole.log(utils.arraify(''));\n//=> []\nconsole.log(utils.arraify('foo'));\n//=> ['foo']\nconsole.log(utils.arraify(['foo']));\n//=> ['foo']\n```\n\n### [.stringify](index.js#L948)\n\nConvert the given `val` to a string by joining with `,`. Useful\nfor creating a cheerio/CSS/DOM-style selector from a list of strings.\n\n**Params**\n\n* `val` **{any}**\n* `returns` **{Array}**\n\n### [.trim](index.js#L961)\n\nEnsure that the given value is a string and call `.trim()` on it,\nor return an empty string.\n\n**Params**\n\n* `str` **{String}**\n* `returns` **{String}**\n\n## Release history\n\nChangelog entries are classified using the following labels from [keep-a-changelog](https://github.com/olivierlacan/keep-a-changelog):\n\n* `added`: for new features\n* `changed`: for changes in existing functionality\n* `deprecated`: for once-stable features removed in upcoming releases\n* `removed`: for deprecated features removed in this release\n* `fixed`: for any bug fixes\n\nCustom labels used in this changelog:\n\n* `dependencies`: bumps dependencies\n* `housekeeping`: code re-organization, minor edits, or other changes that don't fit in one of the other categories.\n\n### [3.0.0] - 2017-05-01\n\n**Changed**\n\n* `.emit` was renamed to [.append](#append)\n* `.addNode` was renamed to [.pushNode](#pushNode)\n* `.getNode` was renamed to [.findNode](#findNode)\n* `.isEmptyNodes` was renamed to [.isEmpty](#isEmpty): also now works with `node.nodes` and/or `node.val`\n\n**Added**\n\n* [.identity](#identity)\n* [.removeNode](#removeNode)\n* [.shiftNode](#shiftNode)\n* [.popNode](#popNode)\n\n### [0.1.0]\n\nFirst release.\n\n## About\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\nPlease read the [contributing guide](.github/contributing.md) for advice on opening issues, pull requests, and coding standards.\n\n### Building docs\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n### Running tests\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on May 01, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2017, <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/snapdragon-util/-/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2", "type": "tarball", "reference": "https://registry.yarnpkg.com/snapdragon-util/-/snapdragon-util-3.0.1.tgz", "hash": "f956479486f2acd79700693f6f7b805e45ab56e2", "integrity": "sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ==", "registry": "npm", "packageName": "snapdragon-util", "cacheIntegrity": "sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ== sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI="}, "registry": "npm", "hash": "f956479486f2acd79700693f6f7b805e45ab56e2"}