{"manifest": {"name": "emojis-list", "description": "Complete list of standard emojis.", "homepage": "https://github.com/Kikobeats/emojis-list", "version": "2.1.0", "main": "./index.js", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Kikobeats"}, "repository": {"type": "git", "url": "git+https://github.com/kikobeats/emojis-list.git"}, "bugs": {"url": "https://github.com/Kikobeats/emojis-list/issues"}, "keywords": ["archive", "complete", "emoji", "list", "standard"], "devDependencies": {"acho": "latest", "browserify": "latest", "cheerio": "latest", "got": ">=5 <6", "gulp": "latest", "gulp-header": "latest", "gulp-uglify": "latest", "gulp-util": "latest", "standard": "latest", "vinyl-buffer": "latest", "vinyl-source-stream": "latest"}, "engines": {"node": ">= 0.10"}, "files": ["index.js"], "scripts": {"pretest": "standard update.js", "test": "echo 'YOLO'", "update": "node update"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-emojis-list-2.1.0-4daa4d9db00f9819880c79fa457ae5b09a1fd389-integrity\\node_modules\\emojis-list\\package.json", "readmeFilename": "README.md", "readme": "# emojis-list\n\n[![Dependency status](http://img.shields.io/david/Kikobeats/emojis-list.svg?style=flat-square)](https://david-dm.org/Kikobeats/emojis-list)\n[![Dev Dependencies Status](http://img.shields.io/david/dev/Kikobeats/emojis-list.svg?style=flat-square)](https://david-dm.org/Kikobeats/emojis-list#info=devDependencies)\n[![NPM Status](http://img.shields.io/npm/dm/emojis-list.svg?style=flat-square)](https://www.npmjs.org/package/emojis-list)\n[![Donate](https://img.shields.io/badge/donate-paypal-blue.svg?style=flat-square)](https://paypal.me/kikobeats)\n\n> Complete list of standard Unicode Hex Character Code that represent emojis.\n\n**NOTE**: The lists is related with the Unicode Hex Character Code. The representation of the emoji depend of the system. Will be possible that the system don't have all the representations.\n\n## Install\n\n```bash\nnpm install emojis-list --save\n```\n\nIf you want to use in the browser (powered by [Browserify](http://browserify.org/)):\n\n```bash\nbower install emojis-list --save\n```\n\nand later link in your HTML:\n\n```html\n<script src=\"bower_components/emojis-list/dist/emojis-list.js\"></script>\n```\n\n## Usage\n\n```\nvar emojis = require('emojis-list');\nconsole.log(emojis[0]);\n// => 🀄\n```\n\n## Related\n\n* [emojis-unicode](https://github.com/Kikobeats/emojis-unicode) – Complete list of standard Unicode codes that represent emojis.\n* [emojis-keywords](https://github.com/Kikobeats/emojis-keywords) – Complete list of am emoji shortcuts.\n* [is-emoji-keyword](https://github.com/Kikobeats/is-emoji-keyword) – Check if a word is a emoji shortcut.\n* [is-standard-emoji](https://github.com/kikobeats/is-standard-emoji) – Simply way to check if a emoji is a standard emoji.\n* [trim-emoji](https://github.com/Kikobeats/trim-emoji) – Deletes ':' from the begin and the end of an emoji shortcut.\n\n## License\n\nMIT © [Kiko Beats](http://www.kikobeats.com)\n", "licenseText": "The MIT License (MIT)\n\nCopyright © 2015 <PERSON>ko Beats\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the “Software”), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/emojis-list/-/emojis-list-2.1.0.tgz#4daa4d9db00f9819880c79fa457ae5b09a1fd389", "type": "tarball", "reference": "https://registry.yarnpkg.com/emojis-list/-/emojis-list-2.1.0.tgz", "hash": "4daa4d9db00f9819880c79fa457ae5b09a1fd389", "integrity": "sha1-TapNnbAPmBmIDHn6RXrlsJof04k=", "registry": "npm", "packageName": "emojis-list", "cacheIntegrity": "sha512-knHEZMgs8BB+MInokmNTg/OyPlAddghe1YBgNwJBc5zsJi/uyIcXoSDsL/W9ymOsBoBGdPIHXYJ9+qKFwRwDng== sha1-TapNnbAPmBmIDHn6RXrlsJof04k="}, "registry": "npm", "hash": "4daa4d9db00f9819880c79fa457ae5b09a1fd389"}