Pro testování oprav qb-inventory:

1. V server konzoli spusť:
   stop qb-inventory
   
2. Počkej 3 sekundy

3. <PERSON>pusť:
   start qb-inventory
   
4. P<PERSON>ip<PERSON>j se na server a spusť:
   /testinv
   
5. Zkontroluj konzoli - měly by se zobrazit ✓ SUCCESS zprávy místo ✗ FAILED

6. <PERSON><PERSON><PERSON> vše fungu<PERSON>, spusť:
   /loadinv
   
7. Zkontroluj, jestli se inventory načítá bez chyb

Očekávan<PERSON> výsle<PERSON>ky:
- Ž<PERSON>dn<PERSON> "attempt to index a nil value" chyby
- Všechny testy by měly projít
- Inventory by se měl načítat a ukládat správně

Pokud stále vidíš chyby, pošli mi přesný error message z konzole.
