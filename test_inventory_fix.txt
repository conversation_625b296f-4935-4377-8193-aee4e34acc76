Pro testování oprav qb-inventory:

1. V server konzoli spusť:
   stop qb-inventory

2. Počkej 3 sekundy

3. <PERSON>pus<PERSON>:
   start qb-inventory

4. P<PERSON>ipoj se na server a nejdříve spusť:
   /testcore

5. <PERSON><PERSON><PERSON><PERSON><PERSON>, jestli Core je připraven. Pak spusť:
   /testinv

6. <PERSON>kon<PERSON><PERSON><PERSON> konzoli - měly by se zobrazit ✓ SUCCESS zprávy místo ✗ FAILED

7. <PERSON>kud vše funguje, spusť:
   /loadinv

8. <PERSON><PERSON><PERSON><PERSON>j, jestli se inventory načítá bez chyb

9. Otestuj základní inventory operace:
   - Otevři inventory (TAB nebo I)
   - Zkus přesunout item
   - Zkus použít item

Očekávané výsledky:
- Ž<PERSON><PERSON><PERSON> "attempt to index a nil value" chyby v client ani server konzoli
- Core by měl být připraven
- Všechny testy by mě<PERSON> projít
- Inventory by se měl načítat a ukládat správně
- UI by se m<PERSON><PERSON> otevírat bez chyb

Pokud st<PERSON><PERSON> v<PERSON><PERSON><PERSON> chyby, pošli mi:
1. <PERSON><PERSON><PERSON>dek /testcore
2. <PERSON><PERSON><PERSON><PERSON> /testinv
3. Jakékoliv error messages z konzole
