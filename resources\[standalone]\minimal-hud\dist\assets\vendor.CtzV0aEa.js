var n,t,e,r,o,i,l,c,a,u,s,f,d={},p=[],h=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,v=Array.isArray;function b(n,t){for(var e in t)n[e]=t[e];return n}function m(n){n&&n.parentNode&&n.parentNode.removeChild(n)}function g(t,e,r){var o,i,l,c={};for(l in e)"key"==l?o=e[l]:"ref"==l?i=e[l]:c[l]=e[l];if(arguments.length>2&&(c.children=arguments.length>3?n.call(arguments,2):r),"function"==typeof t&&null!=t.defaultProps)for(l in t.defaultProps)void 0===c[l]&&(c[l]=t.defaultProps[l]);return y(t,c,o,i,null)}function y(n,r,o,i,l){var c={type:n,props:r,key:o,ref:i,_:null,k:null,C:0,M:null,S:null,constructor:void 0,j:l??++e,O:-1,L:0};return null==l&&null!=t.vnode&&t.vnode(c),c}function w(n){return n.children}function x(n,t){this.props=n,this.context=t}function _(n,t){if(null==t)return n.k?_(n.k,n.O+1):null;for(var e;t<n._.length;t++)if(null!=(e=n._[t])&&null!=e.M)return e.M;return"function"==typeof n.type?_(n):null}function k(n){var t,e;if(null!=(n=n.k)&&null!=n.S){for(n.M=n.S.base=null,t=0;t<n._.length;t++)if(null!=(e=n._[t])&&null!=e.M){n.M=n.S.base=e.M;break}return k(n)}}function z(n){(!n.A&&(n.A=!0)&&r.push(n)&&!C.T++||o!==t.debounceRendering)&&((o=t.debounceRendering)||i)(C)}function C(){var n,e,o,i,c,a,u,s;for(r.sort(l);n=r.shift();)n.A&&(e=r.length,i=void 0,a=(c=(o=n).j).M,u=[],s=[],o.N&&((i=b({},c)).j=c.j+1,t.vnode&&t.vnode(i),T(o.N,i,c,o.B,o.N.namespaceURI,32&c.L?[a]:null,u,a??_(c),!!(32&c.L),s),i.j=c.j,i.k._[i.O]=i,N(u,i,s),i.M!=a&&k(i)),r.length>e&&r.sort(l));C.T=0}function M(n,t,e,r,o,i,l,c,a,u,s){var f,h,b,m,g,x,k=r&&r._||p,z=t.length;for(a=function(n,t,e,r,o){var i,l,c,a,u,s=e.length,f=s,d=0;for(n._=new Array(o),i=0;i<o;i++)null!=(l=t[i])&&"boolean"!=typeof l&&"function"!=typeof l?(a=i+d,(l=n._[i]="string"==typeof l||"number"==typeof l||"bigint"==typeof l||l.constructor==String?y(null,l,null,null,null):v(l)?y(w,{children:l},null,null,null):void 0===l.constructor&&l.C>0?y(l.type,l.props,l.key,l.ref?l.ref:null,l.j):l).k=n,l.C=n.C+1,c=null,-1!==(u=l.O=O(l,e,a,f))&&(f--,(c=e[u])&&(c.L|=2)),null==c||null===c.j?(-1==u&&d--,"function"!=typeof l.type&&(l.L|=4)):u!=a&&(u==a-1?d--:u==a+1?d++:(u>a?d--:d++,l.L|=4))):n._[i]=null;if(f)for(i=0;i<s;i++)null!=(c=e[i])&&!(2&c.L)&&(c.M==r&&(r=_(c)),H(c,c));return r}(e,t,k,a,z),f=0;f<z;f++)null!=(b=e._[f])&&(h=-1===b.O?d:k[b.O]||d,b.O=f,x=T(n,b,h,o,i,l,c,a,u,s),m=b.M,b.ref&&h.ref!=b.ref&&(h.ref&&B(h.ref,null,b),s.push(b.ref,b.S||m,b)),null==g&&null!=m&&(g=m),4&b.L||h._===b._?a=S(b,a,n):"function"==typeof b.type&&void 0!==x?a=x:m&&(a=m.nextSibling),b.L&=-7);return e.M=g,a}function S(n,t,e){var r,o;if("function"==typeof n.type){for(r=n._,o=0;r&&o<r.length;o++)r[o]&&(r[o].k=n,t=S(r[o],t,e));return t}n.M!=t&&(t&&n.type&&!e.contains(t)&&(t=_(n)),e.insertBefore(n.M,t||null),t=n.M);do{t=t&&t.nextSibling}while(null!=t&&8==t.nodeType);return t}function j(n,t){return t=t||[],null==n||"boolean"==typeof n||(v(n)?n.some((function(n){j(n,t)})):t.push(n)),t}function O(n,t,e,r){var o,i,l=n.key,c=n.type,a=t[e];if(null===a||a&&l==a.key&&c===a.type&&!(2&a.L))return e;if(r>(null==a||2&a.L?0:1))for(o=e-1,i=e+1;o>=0||i<t.length;){if(o>=0){if((a=t[o])&&!(2&a.L)&&l==a.key&&c===a.type)return o;o--}if(i<t.length){if((a=t[i])&&!(2&a.L)&&l==a.key&&c===a.type)return i;i++}}return-1}function E(n,t,e){"-"==t[0]?n.setProperty(t,e??""):n[t]=null==e?"":"number"!=typeof e||h.test(t)?e:e+"px"}function L(n,t,e,r,o){var i;n:if("style"==t)if("string"==typeof e)n.style.cssText=e;else{if("string"==typeof r&&(n.style.cssText=r=""),r)for(t in r)e&&t in e||E(n.style,t,"");if(e)for(t in e)r&&e[t]===r[t]||E(n.style,t,e[t])}else if("o"==t[0]&&"n"==t[1])i=t!=(t=t.replace(c,"$1")),t=t.toLowerCase()in n||"onFocusOut"==t||"onFocusIn"==t?t.toLowerCase().slice(2):t.slice(2),n.l||(n.l={}),n.l[t+i]=e,e?r?e.u=r.u:(e.u=a,n.addEventListener(t,i?s:u,i)):n.removeEventListener(t,i?s:u,i);else{if("http://www.w3.org/2000/svg"==o)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in n)try{n[t]=e??"";break n}catch(l){}"function"==typeof e||(null==e||!1===e&&"-"!=t[4]?n.removeAttribute(t):n.setAttribute(t,"popover"==t&&1==e?"":e))}}function A(n){return function(e){if(this.l){var r=this.l[e.type+n];if(null==e.t)e.t=a++;else if(e.t<r.u)return;return r(t.event?t.event(e):e)}}}function T(e,r,o,i,l,c,a,u,s,f){var p,h,g,y,k,z,C,S,j,O,E,A,T,N,B,H,P,R=r.type;if(void 0!==r.constructor)return null;128&o.L&&(s=!!(32&o.L),c=[u=r.M=o.M]),(p=t.C)&&p(r);n:if("function"==typeof R)try{if(S=r.props,j="prototype"in R&&R.prototype.render,O=(p=R.contextType)&&i[p.S],E=p?O?O.props.value:p.k:i,o.S?C=(h=r.S=o.S).k=h.H:(j?r.S=h=new R(S,E):(r.S=h=new x(S,E),h.constructor=R,h.render=I),O&&O.sub(h),h.props=S,h.state||(h.state={}),h.context=E,h.B=i,g=h.A=!0,h.I=[],h.P=[]),j&&null==h.R&&(h.R=h.state),j&&null!=R.getDerivedStateFromProps&&(h.R==h.state&&(h.R=b({},h.R)),b(h.R,R.getDerivedStateFromProps(S,h.R))),y=h.props,k=h.state,h.j=r,g)j&&null==R.getDerivedStateFromProps&&null!=h.componentWillMount&&h.componentWillMount(),j&&null!=h.componentDidMount&&h.I.push(h.componentDidMount);else{if(j&&null==R.getDerivedStateFromProps&&S!==y&&null!=h.componentWillReceiveProps&&h.componentWillReceiveProps(S,E),!h.M&&(null!=h.shouldComponentUpdate&&!1===h.shouldComponentUpdate(S,h.R,E)||r.j==o.j)){for(r.j!=o.j&&(h.props=S,h.state=h.R,h.A=!1),r.M=o.M,r._=o._,r._.some((function(n){n&&(n.k=r)})),A=0;A<h.P.length;A++)h.I.push(h.P[A]);h.P=[],h.I.length&&a.push(h);break n}null!=h.componentWillUpdate&&h.componentWillUpdate(S,h.R,E),j&&null!=h.componentDidUpdate&&h.I.push((function(){h.componentDidUpdate(y,k,z)}))}if(h.context=E,h.props=S,h.N=e,h.M=!1,T=t.T,N=0,j){for(h.state=h.R,h.A=!1,T&&T(r),p=h.render(h.props,h.state,h.context),B=0;B<h.P.length;B++)h.I.push(h.P[B]);h.P=[]}else do{h.A=!1,T&&T(r),p=h.render(h.props,h.state,h.context),h.state=h.R}while(h.A&&++N<25);h.state=h.R,null!=h.getChildContext&&(i=b(b({},i),h.getChildContext())),j&&!g&&null!=h.getSnapshotBeforeUpdate&&(z=h.getSnapshotBeforeUpdate(y,k)),u=M(e,v(H=null!=p&&p.type===w&&null==p.key?p.props.children:p)?H:[H],r,o,i,l,c,a,u,s,f),h.base=r.M,r.L&=-161,h.I.length&&a.push(h),C&&(h.H=h.k=null)}catch(V){if(r.j=null,s||null!=c)if(V.then){for(r.L|=s?160:128;u&&8==u.nodeType&&u.nextSibling;)u=u.nextSibling;c[c.indexOf(u)]=null,r.M=u}else for(P=c.length;P--;)m(c[P]);else r.M=o.M,r._=o._;t.M(V,r,o)}else null==c&&r.j==o.j?(r._=o._,r.M=o.M):u=r.M=function(e,r,o,i,l,c,a,u,s){var f,p,h,b,g,y,w,x=o.props,k=r.props,z=r.type;if("svg"==z?l="http://www.w3.org/2000/svg":"math"==z?l="http://www.w3.org/1998/Math/MathML":l||(l="http://www.w3.org/1999/xhtml"),null!=c)for(f=0;f<c.length;f++)if((g=c[f])&&"setAttribute"in g==!!z&&(z?g.localName==z:3==g.nodeType)){e=g,c[f]=null;break}if(null==e){if(null==z)return document.createTextNode(k);e=document.createElementNS(l,z,k.is&&k),u&&(t.V&&t.V(r,c),u=!1),c=null}if(null===z)x===k||u&&e.data===k||(e.data=k);else{if(c=c&&n.call(e.childNodes),x=o.props||d,!u&&null!=c)for(x={},f=0;f<e.attributes.length;f++)x[(g=e.attributes[f]).name]=g.value;for(f in x)if(g=x[f],"children"==f);else if("dangerouslySetInnerHTML"==f)h=g;else if(!(f in k)){if("value"==f&&"defaultValue"in k||"checked"==f&&"defaultChecked"in k)continue;L(e,f,null,g,l)}for(f in k)g=k[f],"children"==f?b=g:"dangerouslySetInnerHTML"==f?p=g:"value"==f?y=g:"checked"==f?w=g:u&&"function"!=typeof g||x[f]===g||L(e,f,g,x[f],l);if(p)u||h&&(p.$===h.$||p.$===e.innerHTML)||(e.innerHTML=p.$),r._=[];else if(h&&(e.innerHTML=""),M(e,v(b)?b:[b],r,o,i,"foreignObject"==z?"http://www.w3.org/1999/xhtml":l,c,a,c?c[0]:o._&&_(o,0),u,s),null!=c)for(f=c.length;f--;)m(c[f]);u||(f="value","progress"==z&&null==y?e.removeAttribute("value"):void 0!==y&&(y!==e[f]||"progress"==z&&!y||"option"==z&&y!==x[f])&&L(e,f,y,x[f],l),f="checked",void 0!==w&&w!==e[f]&&L(e,f,w,x[f],l))}return e}(o.M,r,o,i,l,c,a,s,f);return(p=t.diffed)&&p(r),128&r.L?void 0:u}function N(n,e,r){for(var o=0;o<r.length;o++)B(r[o],r[++o],r[++o]);t.S&&t.S(e,n),n.some((function(e){try{n=e.I,e.I=[],n.some((function(n){n.call(e)}))}catch(r){t.M(r,e.j)}}))}function B(n,e,r){try{if("function"==typeof n){var o="function"==typeof n.L;o&&n.L(),o&&null==e||(n.L=n(e))}else n.current=e}catch(i){t.M(i,r)}}function H(n,e,r){var o,i;if(t.unmount&&t.unmount(n),(o=n.ref)&&(o.current&&o.current!==n.M||B(o,null,e)),null!=(o=n.S)){if(o.componentWillUnmount)try{o.componentWillUnmount()}catch(l){t.M(l,e)}o.base=o.N=null}if(o=n._)for(i=0;i<o.length;i++)o[i]&&H(o[i],e,r||"function"!=typeof n.type);r||m(n.M),n.S=n.k=n.M=void 0}function I(n,t,e){return this.constructor(n,e)}function P(e,r,o){var i,l,c,a;r==document&&(r=document.documentElement),t.k&&t.k(e,r),l=(i="function"==typeof o)?null:o&&o._||r._,c=[],a=[],T(r,e=(!i&&o||r)._=g(w,null,[e]),l||d,d,r.namespaceURI,!i&&o?[o]:l?null:r.firstChild?n.call(r.childNodes):null,c,!i&&o?o:l?l.M:r.firstChild,i,a),N(c,e,a)}function R(n,t){P(n,t,R)}function V(t,e,r){var o,i,l,c,a=b({},t.props);for(l in t.type&&t.type.defaultProps&&(c=t.type.defaultProps),e)"key"==l?o=e[l]:"ref"==l?i=e[l]:a[l]=void 0===e[l]&&void 0!==c?c[l]:e[l];return arguments.length>2&&(a.children=arguments.length>3?n.call(arguments,2):r),y(t.type,a,o||t.key,i||t.ref,null)}function $(n,t){var e={S:t="__cC"+f++,k:n,Consumer:function(n,t){return n.children(t)},Provider:function(n){var e,r;return this.getChildContext||(e=new Set,(r={})[t]=this,this.getChildContext=function(){return r},this.componentWillUnmount=function(){e=null},this.shouldComponentUpdate=function(n){this.props.value!==n.value&&e.forEach((function(n){n.M=!0,z(n)}))},this.sub=function(n){e.add(n);var t=n.componentWillUnmount;n.componentWillUnmount=function(){e&&e.delete(n),t&&t.call(n)}}),n.children}};return e.Provider.k=e.Consumer.contextType=e}n=p.slice,t={M:function(n,t,e,r){for(var o,i,l;t=t.k;)if((o=t.S)&&!o.k)try{if((i=o.constructor)&&null!=i.getDerivedStateFromError&&(o.setState(i.getDerivedStateFromError(n)),l=o.A),null!=o.componentDidCatch&&(o.componentDidCatch(n,r||{}),l=o.A),l)return o.H=o}catch(c){n=c}throw n}},e=0,x.prototype.setState=function(n,t){var e;e=null!=this.R&&this.R!==this.state?this.R:this.R=b({},this.state),"function"==typeof n&&(n=n(b({},e),this.props)),n&&b(e,n),null!=n&&this.j&&(t&&this.P.push(t),z(this))},x.prototype.forceUpdate=function(n){this.j&&(this.M=!0,n&&this.I.push(n),z(this))},x.prototype.render=w,r=[],i="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,l=function(n,t){return n.j.C-t.j.C},C.T=0,c=/(PointerCapture)$|Capture$/i,a=0,u=A(!1),s=A(!0),f=0;var F,W=0;function D(n,e,r,o,i,l){e||(e={});var c,a,u=e;if("ref"in u)for(a in u={},e)"ref"==a?c=e[a]:u[a]=e[a];var s={type:n,props:u,key:r,ref:c,_:null,k:null,C:0,M:null,S:null,constructor:void 0,j:--W,O:-1,L:0,F:i,W:l};if("function"==typeof n&&(c=n.defaultProps))for(a in c)void 0===u[a]&&(u[a]=c[a]);return t.vnode&&t.vnode(s),s}null!=(F="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0)&&F.D&&F.D.attachPreact("10.25.4",t,{Fragment:w,Component:x});var U={};function Z(n){return n.type===w?"Fragment":"function"==typeof n.type?n.type.displayName||n.type.name:"string"==typeof n.type?n.type:"#text"}var G=[],q=[];function J(n){return"function"==typeof n.type&&n.type!=w}function X(n){for(var t=[n],e=n;null!=e.U;)t.push(e.U),e=e.U;return t.reduce((function(n,t){n+="  in "+Z(t);var e=t.F;return e&&(n+=" (at "+e.fileName+":"+e.lineNumber+")"),n+"\n"}),"")}var Y="function"==typeof WeakMap;function K(n){var t=[];return n._?(n._.forEach((function(n){n&&"function"==typeof n.type?t.push.apply(t,K(n)):n&&"string"==typeof n.type&&t.push(n.type)})),t):t}function Q(n){return n?"function"==typeof n.type?null==n.k?null!=n.M&&null!=n.M.parentNode?n.M.parentNode.localName:"":Q(n.k):n.type:""}var nn=x.prototype.setState;function tn(n){return"table"===n||"tfoot"===n||"tbody"===n||"thead"===n||"td"===n||"tr"===n||"th"===n}x.prototype.setState=function(n,t){return null==this.j&&this.state,nn.call(this,n,t)};var en=/^(address|article|aside|blockquote|details|div|dl|fieldset|figcaption|figure|footer|form|h1|h2|h3|h4|h5|h6|header|hgroup|hr|main|menu|nav|ol|p|pre|search|section|table|ul)$/,rn=x.prototype.forceUpdate;function on(n){var t=n.props,e=Z(n),r="";for(var o in t)if(t.hasOwnProperty(o)&&"children"!==o){var i=t[o];"function"==typeof i&&(i="function "+(i.displayName||i.name)+"() {}"),i=Object(i)!==i||i.toString?i+"":Object.prototype.toString.call(i),r+=" "+o+"="+JSON.stringify(i)}var l=t.children;return"<"+e+r+(l&&l.length?">..</"+e+">":" />")}x.prototype.forceUpdate=function(n){return null==this.j||this.N,rn.call(this,n)},t.V=function(n,t){n.type,t.map((function(n){return n&&n.localName})).filter(Boolean)},function(){var n,e,r,o,i;n=t.C,e=t.diffed,r=t.k,o=t.vnode,i=t.T,t.diffed=function(n){J(n)&&q.pop(),G.pop(),e&&e(n)},t.C=function(t){J(t)&&G.push(t),n&&n(t)},t.k=function(n,t){q=[],r&&r(n,t)},t.vnode=function(n){n.U=q.length>0?q[q.length-1]:null,o&&o(n)},t.T=function(n){J(n)&&q.push(n),i&&i(n)};var l=!1,c=t.C,a=t.diffed,u=t.vnode,s=t.T,f=t.M,d=t.k,p=t.I,h=Y?{useEffect:new WeakMap,useLayoutEffect:new WeakMap,lazyPropTypes:new WeakMap}:null,v=[];t.M=function(n,t,e,r){if(t&&t.S&&"function"==typeof n.then){var o=n;n=new Error("Missing Suspense. The throwing component was: "+Z(t));for(var i=t;i;i=i.k)if(i.S&&i.S.S){n=o;break}if(n instanceof Error)throw n}try{(r=r||{}).componentStack=X(t),f(n,t,e,r),"function"!=typeof n.then&&setTimeout((function(){throw n}))}catch(l){throw l}},t.k=function(n,t){if(!t)throw new Error("Undefined parent passed to render(), this is the second argument.\nCheck if the element is available in the DOM/has the correct id.");var e;switch(t.nodeType){case 1:case 11:case 9:e=!0;break;default:e=!1}if(!e){var r=Z(n);throw new Error("Expected a valid HTML node as a second argument to render.\tReceived "+t+" instead: render(<"+r+" />, "+t+");")}d&&d(n,t)},t.C=function(t){var e=t.type;if(l=!0,void 0===e)throw new Error("Undefined component passed to createElement()\n\nYou likely forgot to export your component or might have mixed up default and named imports"+on(t)+"\n\n"+X(t));if(null!=e&&"object"==typeof e){if(void 0!==e._&&void 0!==e.M)throw new Error("Invalid type passed to createElement(): "+e+"\n\nDid you accidentally pass a JSX literal as JSX twice?\n\n  let My"+Z(t)+" = "+on(e)+";\n  let vnode = <My"+Z(t)+" />;\n\nThis usually happens when you export a JSX literal and not the component.\n\n"+X(t));throw new Error("Invalid type passed to createElement(): "+(Array.isArray(e)?"array":e))}if(void 0!==t.ref&&"function"!=typeof t.ref&&"object"!=typeof t.ref&&!("$$typeof"in t))throw new Error('Component\'s "ref" property should be a function, or an object created by createRef(), but got ['+typeof t.ref+"] instead\n"+on(t)+"\n\n"+X(t));if("string"==typeof t.type)for(var r in t.props)if("o"===r[0]&&"n"===r[1]&&"function"!=typeof t.props[r]&&null!=t.props[r])throw new Error("Component's \""+r+'" property should be a function, but got ['+typeof t.props[r]+"] instead\n"+on(t)+"\n\n"+X(t));if("function"==typeof t.type&&t.type.propTypes){if("Lazy"===t.type.displayName&&h&&!h.lazyPropTypes.has(t.type))try{t.type(),h.lazyPropTypes.set(t.type,!0)}catch(n){}var o=t.props;t.type.Z&&delete(o=function(n,t){for(var e in t)n[e]=t[e];return n}({},o)).ref,function(n,t,e,r){Object.keys(n).forEach((function(e){var o;try{o=n[e](t,e,r,"prop",null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(i){o=i}o&&!(o.message in U)&&(U[o.message]=!0)}))}(t.type.propTypes,o,0,Z(t))}c&&c(t)};var b,m=0;t.T=function(n){s&&s(n),l=!0;var t=n.S;if(t===b?m++:m=1,m>=25)throw new Error("Too many re-renders. This is limited to prevent an infinite loop which may lock up your browser. The component causing this is: "+Z(n));b=t},t.I=function(n,t,e){if(!n||!l)throw new Error("Hook can only be invoked from render methods.");p&&p(n,t,e)};var g=function(n,t){return{get:function(){var e="get"+n+t;v&&v.indexOf(e)<0&&v.push(e)},set:function(){var e="set"+n+t;v&&v.indexOf(e)<0&&v.push(e)}}},y={nodeName:g("nodeName","use vnode.type"),attributes:g("attributes","use vnode.props"),children:g("children","use vnode.props.children")},w=Object.create({},y);t.vnode=function(n){var t=n.props;if(null!==n.type&&null!=t&&("F"in t||"W"in t)){var e=n.props={};for(var r in t){var o=t[r];"__source"===r?n.F=o:"__self"===r?n.W=o:e[r]=o}}n.__proto__=w,u&&u(n)},t.diffed=function(n){var t,e=n.type,r=n.k;if(n._&&n._.forEach((function(t){if("object"==typeof t&&t&&void 0===t.type){var e=Object.keys(t).join(",");throw new Error("Objects are not valid as a child. Encountered an object with the keys {"+e+"}.\n\n"+X(n))}})),n.S===b&&(m=0),"string"==typeof e&&(tn(e)||"p"===e||"a"===e||"button"===e)){var o=Q(r);if(""!==o&&tn(e))"table"===e&&"td"!==o&&tn(o);else if("p"===e){var i=K(n).filter((function(n){return en.test(n)}));i.length}else"a"!==e&&"button"!==e||K(n).indexOf(e)}if(l=!1,a&&a(n),null!=n._)for(var c=[],u=0;u<n._.length;u++){var s=n._[u];if(s&&null!=s.key){var f=s.key;if(-1!==c.indexOf(f))break;c.push(f)}}if(null!=n.S&&null!=n.S.G){var d=n.S.G.k;if(d)for(var p=0;p<d.length;p+=1){var h=d[p];if(h.G)for(var v=0;v<h.G.length;v++)(t=h.G[v])!=t&&Z(n)}}}}();var ln,cn,an,un,sn=0,fn=[],dn=t,pn=dn.C,hn=dn.T,vn=dn.diffed,bn=dn.S,mn=dn.unmount,gn=dn.k;function yn(n,t){dn.I&&dn.I(cn,n,sn||t),sn=0;var e=cn.G||(cn.G={k:[],I:[]});return n>=e.k.length&&e.k.push({}),e.k[n]}function wn(n){return sn=1,xn(In,n)}function xn(n,t,e){var r=yn(ln++,2);if(r.t=n,!r.S&&(r.k=[e?e(t):In(void 0,t),function(n){var t=r.q?r.q[0]:r.k[0],e=r.t(t,n);t!==e&&(r.q=[e,r.k[1]],r.S.setState({}))}],r.S=cn,!cn.u)){var o=function(n,t,e){if(!r.S.G)return!0;var o=r.S.G.k.filter((function(n){return!!n.S}));if(o.every((function(n){return!n.q})))return!i||i.call(this,n,t,e);var l=r.S.props!==n;return o.forEach((function(n){if(n.q){var t=n.k[0];n.k=n.q,n.q=void 0,t!==n.k[0]&&(l=!0)}})),i&&i.call(this,n,t,e)||l};cn.u=!0;var i=cn.shouldComponentUpdate,l=cn.componentWillUpdate;cn.componentWillUpdate=function(n,t,e){if(this.M){var r=i;i=void 0,o(n,t,e),i=r}l&&l.call(this,n,t,e)},cn.shouldComponentUpdate=o}return r.q||r.k}function _n(n,t){var e=yn(ln++,3);!dn.R&&Hn(e.G,t)&&(e.k=n,e.i=t,cn.G.I.push(e))}function kn(n,t){var e=yn(ln++,4);!dn.R&&Hn(e.G,t)&&(e.k=n,e.i=t,cn.I.push(e))}function zn(n){return sn=5,Mn((function(){return{current:n}}),[])}function Cn(n,t,e){sn=6,kn((function(){return"function"==typeof n?(n(t()),function(){return n(null)}):n?(n.current=t(),function(){return n.current=null}):void 0}),null==e?e:e.concat(n))}function Mn(n,t){var e=yn(ln++,7);return Hn(e.G,t)&&(e.k=n(),e.G=t,e.I=n),e.k}function Sn(n,t){return sn=8,Mn((function(){return n}),t)}function jn(n){var t=cn.context[n.S],e=yn(ln++,9);return e.c=n,t?(null==e.k&&(e.k=!0,t.sub(cn)),t.props.value):n.k}function On(n,t){dn.useDebugValue&&dn.useDebugValue(t?t(n):n)}function En(){var n=yn(ln++,11);if(!n.k){for(var t=cn.j;null!==t&&!t.V&&null!==t.k;)t=t.k;var e=t.V||(t.V=[0,0]);n.k="P"+e[0]+"-"+e[1]++}return n.k}function Ln(){for(var n;n=fn.shift();)if(n.N&&n.G)try{n.G.I.forEach(Nn),n.G.I.forEach(Bn),n.G.I=[]}catch(t){n.G.I=[],dn.M(t,n.j)}}dn.C=function(n){cn=null,pn&&pn(n)},dn.k=function(n,t){n&&t._&&t._.V&&(n.V=t._.V),gn&&gn(n,t)},dn.T=function(n){hn&&hn(n),ln=0;var t=(cn=n.S).G;t&&(an===cn?(t.I=[],cn.I=[],t.k.forEach((function(n){n.q&&(n.k=n.q),n.i=n.q=void 0}))):(t.I.forEach(Nn),t.I.forEach(Bn),t.I=[],ln=0)),an=cn},dn.diffed=function(n){vn&&vn(n);var t=n.S;t&&t.G&&(t.G.I.length&&(1!==fn.push(t)&&un===dn.requestAnimationFrame||((un=dn.requestAnimationFrame)||Tn)(Ln)),t.G.k.forEach((function(n){n.i&&(n.G=n.i),n.i=void 0}))),an=cn=null},dn.S=function(n,t){t.some((function(n){try{n.I.forEach(Nn),n.I=n.I.filter((function(n){return!n.k||Bn(n)}))}catch(e){t.some((function(n){n.I&&(n.I=[])})),t=[],dn.M(e,n.j)}})),bn&&bn(n,t)},dn.unmount=function(n){mn&&mn(n);var t,e=n.S;e&&e.G&&(e.G.k.forEach((function(n){try{Nn(n)}catch(e){t=e}})),e.G=void 0,t&&dn.M(t,e.j))};var An="function"==typeof requestAnimationFrame;function Tn(n){var t,e=function(){clearTimeout(r),An&&cancelAnimationFrame(t),setTimeout(n)},r=setTimeout(e,100);An&&(t=requestAnimationFrame(e))}function Nn(n){var t=cn,e=n.S;"function"==typeof e&&(n.S=void 0,e()),cn=t}function Bn(n){var t=cn;n.S=n.k(),cn=t}function Hn(n,t){return!n||n.length!==t.length||t.some((function(t,e){return t!==n[e]}))}function In(n,t){return"function"==typeof t?t(n):t}function Pn(n,t){for(var e in t)n[e]=t[e];return n}function Rn(n,t){for(var e in n)if("__source"!==e&&!(e in t))return!0;for(var r in t)if("__source"!==r&&n[r]!==t[r])return!0;return!1}function Vn(n,t){var e=t(),r=wn({t:{k:e,u:t}}),o=r[0].t,i=r[1];return kn((function(){o.k=e,o.u=t,$n(o)&&i({t:o})}),[n,e,t]),_n((function(){return $n(o)&&i({t:o}),n((function(){$n(o)&&i({t:o})}))}),[n]),e}function $n(n){var t,e,r=n.u,o=n.k;try{var i=r();return!((t=o)===(e=i)&&(0!==t||1/t==1/e)||t!=t&&e!=e)}catch(l){return!0}}function Fn(n){n()}function Wn(n){return n}function Dn(){return[!1,Fn]}var Un=kn;function Zn(n,t){this.props=n,this.context=t}(Zn.prototype=new x).isPureReactComponent=!0,Zn.prototype.shouldComponentUpdate=function(n,t){return Rn(this.props,n)||Rn(this.state,t)};var Gn=t.C;t.C=function(n){n.type&&n.type.Z&&n.ref&&(n.props.ref=n.ref,n.ref=null),Gn&&Gn(n)};var qn="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref")||3911,Jn=function(n,t){return null==n?null:j(j(n).map(t))},Xn={map:Jn,forEach:Jn,count:function(n){return n?j(n).length:0},only:function(n){var t=j(n);if(1!==t.length)throw"Children.only";return t[0]},toArray:j},Yn=t.M;t.M=function(n,t,e,r){if(n.then)for(var o,i=t;i=i.k;)if((o=i.S)&&o.S)return null==t.M&&(t.M=e.M,t._=e._),o.S(n,t);Yn(n,t,e,r)};var Kn=t.unmount;function Qn(n,t,e){return n&&(n.S&&n.S.G&&(n.S.G.k.forEach((function(n){"function"==typeof n.S&&n.S()})),n.S.G=null),null!=(n=Pn({},n)).S&&(n.S.N===e&&(n.S.N=t),n.S=null),n._=n._&&n._.map((function(n){return Qn(n,t,e)}))),n}function nt(n,t,e){return n&&e&&(n.j=null,n._=n._&&n._.map((function(n){return nt(n,t,e)})),n.S&&n.S.N===t&&(n.M&&e.appendChild(n.M),n.S.M=!0,n.S.N=e)),n}function tt(){this.L=0,this.o=null,this.C=null}function et(n){var t=n.k.S;return t&&t.J&&t.J(n)}function rt(){this.i=null,this.l=null}t.unmount=function(n){var t=n.S;t&&t.X&&t.X(),t&&32&n.L&&(n.type=null),Kn&&Kn(n)},(tt.prototype=new x).S=function(n,t){var e=t.S,r=this;null==r.o&&(r.o=[]),r.o.push(e);var o=et(r.j),i=!1,l=function(){i||(i=!0,e.X=null,o?o(c):c())};e.X=l;var c=function(){if(! --r.L){if(r.state.J){var n=r.state.J;r.j._[0]=nt(n,n.S.N,n.S.Y)}var t;for(r.setState({J:r.C=null});t=r.o.pop();)t.forceUpdate()}};r.L++||32&t.L||r.setState({J:r.C=r.j._[0]}),n.then(l,l)},tt.prototype.componentWillUnmount=function(){this.o=[]},tt.prototype.render=function(n,t){if(this.C){if(this.j._){var e=document.createElement("div"),r=this.j._[0].S;this.j._[0]=Qn(this.C,e,r.Y=r.N)}this.C=null}var o=t.J&&g(w,null,n.fallback);return o&&(o.L&=-33),[g(w,null,t.J?null:n.children),o]};var ot=function(n,t,e){if(++e[1]===e[0]&&n.l.delete(t),n.props.revealOrder&&("t"!==n.props.revealOrder[0]||!n.l.size))for(e=n.i;e;){for(;e.length>3;)e.pop()();if(e[1]<e[0])break;n.i=e=e[2]}};function it(n){return this.getChildContext=function(){return n.context},n.children}function lt(n){var t=this,e=n.h;t.componentWillUnmount=function(){P(null,t.v),t.v=null,t.h=null},t.h&&t.h!==e&&t.componentWillUnmount(),t.v||(t.h=e,t.v={nodeType:1,parentNode:e,childNodes:[],contains:function(){return!0},appendChild:function(n){this.childNodes.push(n),t.h.appendChild(n)},insertBefore:function(n,e){this.childNodes.push(n),t.h.insertBefore(n,e)},removeChild:function(n){this.childNodes.splice(this.childNodes.indexOf(n)>>>1,1),t.h.removeChild(n)}}),P(g(it,{context:t.context},n.j),t.v)}(rt.prototype=new x).J=function(n){var t=this,e=et(t.j),r=t.l.get(n);return r[0]++,function(o){var i=function(){t.props.revealOrder?(r.push(o),ot(t,n,r)):o()};e?e(i):i()}},rt.prototype.render=function(n){this.i=null,this.l=new Map;var t=j(n.children);n.revealOrder&&"b"===n.revealOrder[0]&&t.reverse();for(var e=t.length;e--;)this.l.set(t[e],this.i=[1,0,this.i]);return n.children},rt.prototype.componentDidUpdate=rt.prototype.componentDidMount=function(){var n=this;this.l.forEach((function(t,e){ot(n,e,t)}))};var ct="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,at=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,ut=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,st=/[A-Z0-9]/g,ft="undefined"!=typeof document,dt=function(n){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/:/fil|che|ra/).test(n)};x.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach((function(n){Object.defineProperty(x.prototype,n,{configurable:!0,get:function(){return this["UNSAFE_"+n]},set:function(t){Object.defineProperty(this,n,{configurable:!0,writable:!0,value:t})}})}));var pt=t.event;function ht(){}function vt(){return this.cancelBubble}function bt(){return this.defaultPrevented}t.event=function(n){return pt&&(n=pt(n)),n.persist=ht,n.isPropagationStopped=vt,n.isDefaultPrevented=bt,n.nativeEvent=n};var mt,gt={enumerable:!1,configurable:!0,get:function(){return this.class}},yt=t.vnode;t.vnode=function(n){"string"==typeof n.type&&function(n){var t=n.props,e=n.type,r={},o=-1===e.indexOf("-");for(var i in t){var l=t[i];if(!("value"===i&&"defaultValue"in t&&null==l||ft&&"children"===i&&"noscript"===e||"class"===i||"className"===i)){var c=i.toLowerCase();"defaultValue"===i&&"value"in t&&null==t.value?i="value":"download"===i&&!0===l?l="":"translate"===c&&"no"===l?l=!1:"o"===c[0]&&"n"===c[1]?"ondoubleclick"===c?i="ondblclick":"onchange"!==c||"input"!==e&&"textarea"!==e||dt(t.type)?"onfocus"===c?i="onfocusin":"onblur"===c?i="onfocusout":ut.test(i)&&(i=c):c=i="oninput":o&&at.test(i)?i=i.replace(st,"-$&").toLowerCase():null===l&&(l=void 0),"oninput"===c&&r[i=c]&&(i="oninputCapture"),r[i]=l}}"select"==e&&r.multiple&&Array.isArray(r.value)&&(r.value=j(t.children).forEach((function(n){n.props.selected=-1!=r.value.indexOf(n.props.value)}))),"select"==e&&null!=r.defaultValue&&(r.value=j(t.children).forEach((function(n){n.props.selected=r.multiple?-1!=r.defaultValue.indexOf(n.props.value):r.defaultValue==n.props.value}))),t.class&&!t.className?(r.class=t.class,Object.defineProperty(r,"className",gt)):(t.className&&!t.class||t.class&&t.className)&&(r.class=r.className=t.className),n.props=r}(n),n.$$typeof=ct,yt&&yt(n)};var wt=t.T;t.T=function(n){wt&&wt(n),mt=n.S};var xt=t.diffed;function _t(n){return!!n&&n.$$typeof===ct}t.diffed=function(n){xt&&xt(n);var t=n.props,e=n.M;null!=e&&"textarea"===n.type&&"value"in t&&t.value!==e.value&&(e.value=null==t.value?"":t.value),mt=null};var kt={useState:wn,useId:En,useReducer:xn,useEffect:_n,useLayoutEffect:kn,useInsertionEffect:Un,useTransition:Dn,useDeferredValue:Wn,useSyncExternalStore:Vn,startTransition:Fn,useRef:zn,useImperativeHandle:Cn,useMemo:Mn,useCallback:Sn,useContext:jn,useDebugValue:On,version:"18.3.1",Children:Xn,render:function(n,t,e){return null==t._&&(t.textContent=""),P(n,t),"function"==typeof e&&e(),n?n.S:null},hydrate:function(n,t,e){return R(n,t),"function"==typeof e&&e(),n?n.S:null},unmountComponentAtNode:function(n){return!!n._&&(P(null,n),!0)},createPortal:function(n,t){var e=g(lt,{j:n,h:t});return e.containerInfo=t,e},createElement:g,createContext:$,createFactory:function(n){return g.bind(null,n)},cloneElement:function(n){return _t(n)?V.apply(null,arguments):n},createRef:function(){return{current:null}},Fragment:w,isValidElement:_t,isElement:_t,isFragment:function(n){return _t(n)&&n.type===w},isMemo:function(n){return!!n&&!!n.displayName&&("string"==typeof n.displayName||n.displayName instanceof String)&&n.displayName.startsWith("Memo(")},findDOMNode:function(n){return n&&(n.base||1===n.nodeType&&n)||null},Component:x,PureComponent:Zn,memo:function(n,t){function e(n){var e=this.props.ref,r=e==n.ref;return!r&&e&&(e.call?e(null):e.current=null),t?!t(this.props,n)||!r:Rn(this.props,n)}function r(t){return this.shouldComponentUpdate=e,g(n,t)}return r.displayName="Memo("+(n.displayName||n.name)+")",r.prototype.isReactComponent=!0,r.Z=!0,r},forwardRef:function(n){function t(t){var e=Pn({},t);return delete e.ref,n(e,t.ref||null)}return t.$$typeof=qn,t.render=t,t.prototype.isReactComponent=t.Z=!0,t.displayName="ForwardRef("+(n.displayName||n.name)+")",t},flushSync:function(n,t){return n(t)},unstable_batchedUpdates:function(n,t){return n(t)},StrictMode:w,Suspense:tt,SuspenseList:rt,lazy:function(n){var t,e,r;function o(o){if(t||(t=n()).then((function(n){e=n.default||n}),(function(n){r=n})),r)throw r;if(!e)throw t;return g(e,o)}return o.displayName="Lazy",o.Z=!0,o},K:{ReactCurrentDispatcher:{current:{readContext:function(n){return mt.B[n.S].props.value},useCallback:Sn,useContext:jn,useDebugValue:On,useDeferredValue:Wn,useEffect:_n,useId:En,useImperativeHandle:Cn,useInsertionEffect:Un,useLayoutEffect:kn,useMemo:Mn,useReducer:xn,useRef:zn,useState:wn,useSyncExternalStore:Vn,useTransition:Dn}}}};const zt={};let Ct=0;function Mt(n,t){const e="atom"+ ++Ct,r={toString(){return"production"!==(zt?"production":void 0)&&this.debugLabel?e+":"+this.debugLabel:e}};return"function"==typeof n?r.read=n:(r.init=n,r.read=St,r.write=jt),r}function St(n){return n(this)}function jt(n,t,e){return t(this,"function"==typeof e?e(n(this)):e)}const Ot=(n,t)=>n.unstable_is?n.unstable_is(t):t===n,Et=n=>"init"in n,Lt=n=>!!n.write,At=Symbol("production"!==(zt?"production":void 0)?"CONTINUE_PROMISE":""),Tt="pending",Nt=new WeakMap,Bt=n=>"v"in n||"e"in n,Ht=n=>{if("e"in n)throw n.e;if("production"!==(zt?"production":void 0)&&!("v"in n))throw new Error("[Bug] atom state is not initialized");return n.v},It=n=>{const t=n.v;return"object"==typeof(e=t)&&null!==e&&At in e&&t.status===Tt?t:null;var e},Pt=(n,t,e)=>{e.p.has(n)||(e.p.add(n),t.then((()=>{e.p.delete(n)}),(()=>{e.p.delete(n)})))},Rt=(n,t,e,r,o)=>{var i;if("production"!==(zt?"production":void 0)&&r===t)throw new Error("[Bug] atom cannot depend on itself");e.d.set(r,o.n);const l=It(e);l&&Pt(t,l,o),null==(i=o.m)||i.t.add(t),n&&Ft(n,r,t)},Vt=()=>[new Map,new Map,new Set],$t=(n,t,e)=>{n[0].has(t)||n[0].set(t,new Set),n[1].set(t,e)},Ft=(n,t,e)=>{const r=n[0].get(t);r&&r.add(e)},Wt=(n,t)=>{n[2].add(t)},Dt=n=>{for(;n[1].size||n[2].size;){n[0].clear();const t=new Set(n[1].values());n[1].clear();const e=new Set(n[2]);n[2].clear(),t.forEach((n=>{var t;return null==(t=n.m)?void 0:t.l.forEach((n=>n()))})),e.forEach((n=>n()))}},Ut=n=>{let t;"production"!==(zt?"production":void 0)&&(t=new Set);const e=(t,e,r,o=()=>{},i=()=>{})=>{const l="v"in e,c=e.v,a=It(e);if("function"==typeof(null==(u=r)?void 0:u.then))if(a)a!==r&&(a[At](r,o),++e.n);else{const l=((n,t,e)=>{if(!Nt.has(n)){let r;const o=new Promise(((i,l)=>{let c=n;const a=n=>t=>{c===n&&(o.status="fulfilled",o.value=t,i(t),e())},u=n=>t=>{c===n&&(o.status="rejected",o.reason=t,l(t),e())};n.then(a(n),u(n)),r=(n,e)=>{n&&(Nt.set(n,o),c=n,n.then(a(n),u(n)),t(),t=e)}}));o.status=Tt,o[At]=r,Nt.set(n,o)}return Nt.get(n)})(r,o,i);if(l.status===Tt)for(const r of e.d.keys())Pt(t,l,n(r,e));e.v=l,delete e.e}else a&&a[At](Promise.resolve(r),o),e.v=r,delete e.e;var u;l&&Object.is(c,e.v)||++e.n},r=(t,o,i,a)=>{if(!(null==a?void 0:a(o))&&Bt(i)){if(i.m)return i;if(Array.from(i.d).every((([e,o])=>r(t,e,n(e,i),a).n===o)))return i}i.d.clear();let u=!0;const s=l=>{if(Ot(o,l)){const t=n(l,i);if(!Bt(t)){if(!Et(l))throw new Error("no atom init");e(l,t,l.init)}return Ht(t)}const s=r(t,l,n(l,i),a);if(u)Rt(t,o,i,l,s);else{const n=Vt();Rt(n,o,i,l,s),c(n,o,i),Dt(n)}return Ht(s)};let f,d;const p={get signal(){return f||(f=new AbortController),f.signal},get setSelf(){return"production"!==(zt?"production":void 0)&&Lt(o),!d&&Lt(o)&&(d=(...n)=>{if(!u)return l(o,...n)}),d}};try{const n=o.read(s,p);return e(o,i,n,(()=>null==f?void 0:f.abort()),(()=>{if(i.m){const n=Vt();c(n,o,i),Dt(n)}})),i}catch(h){return delete i.v,i.e=h,++i.n,i}finally{u=!1}},o=(t,e,o)=>{const i=[],l=new Set,a=(e,r)=>{if(!l.has(e)){l.add(e);for(const[o,i]of((t,e,r)=>{var o,i;const l=new Map;for(const c of(null==(o=r.m)?void 0:o.t)||[])l.set(c,n(c,r));for(const c of r.p)l.set(c,n(c,r));return null==(i=((n,t)=>n[0].get(t))(t,e))||i.forEach((t=>{l.set(t,n(t,r))})),l})(t,e,r))e!==o&&a(o,i);i.push([e,r,r.n])}};a(e,o);const u=new Set([e]),s=n=>l.has(n);for(let n=i.length-1;n>=0;--n){const[e,o,a]=i[n];let f=!1;for(const n of o.d.keys())if(n!==e&&u.has(n)){f=!0;break}f&&(r(t,e,o,s),c(t,e,o),a!==o.n&&($t(t,e,o),u.add(e))),l.delete(e)}},i=(t,l,a,...u)=>l.write((e=>Ht(r(t,e,n(e,a)))),((r,...u)=>{const s=n(r,a);let f;if(Ot(l,r)){if(!Et(r))throw new Error("atom not writable");const n="v"in s,i=s.v,l=u[0];e(r,s,l),c(t,r,s),n&&Object.is(i,s.v)||($t(t,r,s),o(t,r,s))}else f=i(t,r,s,...u);return Dt(t),f}),...u),l=(t,...e)=>{const r=Vt(),o=i(r,t,n(t),...e);return Dt(r),o},c=(t,e,r)=>{if(r.m&&!It(r)){for(const o of r.d.keys())r.m.d.has(o)||(a(t,o,n(o,r)).t.add(e),r.m.d.add(o));for(const o of r.m.d||[])if(!r.d.has(o)){r.m.d.delete(o);const i=u(t,o,n(o,r));null==i||i.t.delete(e)}}},a=(e,o,l)=>{if(!l.m){r(e,o,l);for(const t of l.d.keys())a(e,t,n(t,l)).t.add(o);if(l.m={l:new Set,d:new Set(l.d.keys()),t:new Set},"production"!==(zt?"production":void 0)&&t.add(o),Lt(o)&&o.onMount){const n=l.m,{onMount:t}=o;Wt(e,(()=>{const r=t(((...n)=>i(e,o,l,...n)));r&&(n.u=r)}))}}return l.m},u=(e,r,o)=>{if(!o.m||o.m.l.size||Array.from(o.m.t).some((t=>{var e;return null==(e=n(t,o).m)?void 0:e.d.has(r)})))return o.m;{const i=o.m.u;i&&Wt(e,i),delete o.m,"production"!==(zt?"production":void 0)&&t.delete(r);for(const t of o.d.keys()){const i=u(e,t,n(t,o));null==i||i.t.delete(r)}const l=It(o);l&&l[At](void 0,(()=>{}))}},s={get:t=>Ht(r(void 0,t,n(t))),set:l,sub:(t,e)=>{const r=Vt(),o=n(t),i=a(r,t,o);Dt(r);const l=i.l;return l.add(e),()=>{l.delete(e);const n=Vt();u(n,t,o),Dt(n)}},unstable_derive:t=>Ut(...t(n))};if("production"!==(zt?"production":void 0)){const r={dev4_get_internal_weak_map:()=>({get:t=>{const e=n(t);if(0!==e.n)return e}}),dev4_get_mounted_atoms:()=>t,dev4_restore_atoms:t=>{const r=Vt();for(const[i,l]of t)if(Et(i)){const t=n(i),a="v"in t,u=t.v;e(i,t,l),c(r,i,t),a&&Object.is(u,t.v)||($t(r,i,t),o(r,i,t))}Dt(r)}};Object.assign(s,r)}return s};let Zt;const Gt={},qt=$(void 0),Jt=n=>jn(qt)||(Zt||(Zt=(()=>{const n=new WeakMap;return Ut((t=>{let e=n.get(t);return e||(e={d:new Map,p:new Set,n:0},n.set(t,e)),e}))})(),"production"!==(zt?"production":void 0)&&(globalThis.__JOTAI_DEFAULT_STORE__||(globalThis.__JOTAI_DEFAULT_STORE__=Zt),globalThis.__JOTAI_DEFAULT_STORE__)),Zt),Xt=kt.use||(n=>{if("pending"===n.status)throw n;if("fulfilled"===n.status)return n.value;throw"rejected"===n.status?n.reason:(n.status="pending",n.then((t=>{n.status="fulfilled",n.value=t}),(t=>{n.status="rejected",n.reason=t})),n)});function Yt(n,t){const e=Jt(),[[r,o,i],l]=xn((t=>{const r=e.get(n);return Object.is(t[0],r)&&t[1]===e&&t[2]===n?t:[r,e,n]}),void 0,(()=>[e.get(n),e,n]));let c=r;return o===e&&i===n||(l(),c=e.get(n)),_n((()=>{const t=e.sub(n,(()=>{l()}));return l(),t}),[e,n,void 0]),On(c),"function"==typeof(null==(a=c)?void 0:a.then)?Xt(c):c;var a}function Kt(n,t){const e=Jt();return Sn(((...t)=>{if("production"!==(Gt?"production":void 0)&&!("write"in n))throw new Error("not writable atom");return e.set(n,...t)}),[e,n])}function Qt(n,t){return[Yt(n),Kt(n)]}var ne={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},te=kt.createContext&&kt.createContext(ne),ee=["attr","size","title"];function re(){return re=Object.assign?Object.assign.bind():function(n){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r])}return n},re.apply(this,arguments)}function oe(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable}))),e.push.apply(e,r)}return e}function ie(n){for(var t=1;t<arguments.length;t++){var e=null!=arguments[t]?arguments[t]:{};t%2?oe(Object(e),!0).forEach((function(t){var r,o,i,l;r=n,o=t,i=e[t],(o="symbol"==typeof(l=function(n,t){if("object"!=typeof n||!n)return n;var e=n[Symbol.toPrimitive];if(void 0!==e){var r=e.call(n,t);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(n)}(o,"string"))?l:l+"")in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):oe(Object(e)).forEach((function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))}))}return n}function le(n){return n&&n.map(((n,t)=>kt.createElement(n.tag,ie({key:t},n.attr),le(n.child))))}function ce(n){return t=>kt.createElement(ae,re({attr:ie({},n.attr)},t),le(n.child))}function ae(n){var t=t=>{var e,{attr:r,size:o,title:i}=n,l=function(n,t){if(null==n)return{};var e,r,o=function(n,t){if(null==n)return{};var e={};for(var r in n)if(Object.prototype.hasOwnProperty.call(n,r)){if(t.indexOf(r)>=0)continue;e[r]=n[r]}return e}(n,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);for(r=0;r<i.length;r++)e=i[r],t.indexOf(e)>=0||Object.prototype.propertyIsEnumerable.call(n,e)&&(o[e]=n[e])}return o}(n,ee),c=o||t.size||"1em";return t.className&&(e=t.className),n.className&&(e=(e?e+" ":"")+n.className),kt.createElement("svg",re({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,r,l,{className:e,style:ie(ie({color:n.color||t.color},t.style),n.style),height:c,width:c,xmlns:"http://www.w3.org/2000/svg"}),i&&kt.createElement("title",null,i),n.children)};return void 0!==te?kt.createElement(te.Consumer,null,(n=>t(n))):t(ne)}function ue(n){return ce({attr:{viewBox:"0 0 496 512"},child:[{tag:"path",attr:{d:"M225.38 233.37c-12.5 12.5-12.5 32.76 0 45.25 12.49 12.5 32.76 12.5 45.25 0 12.5-12.5 12.5-32.76 0-45.25-12.5-12.49-32.76-12.49-45.25 0zM248 8C111.03 8 0 119.03 0 256s111.03 248 248 248 248-111.03 248-248S384.97 8 248 8zm126.14 148.05L308.17 300.4a31.938 31.938 0 0 1-15.77 15.77l-144.34 65.97c-16.65 7.61-33.81-9.55-26.2-26.2l65.98-144.35a31.938 31.938 0 0 1 15.77-15.77l144.34-65.97c16.65-7.6 33.8 9.55 26.19 26.2z"},child:[]}]})(n)}function se(n){return ce({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M323.56 51.2c-20.8 19.3-39.58 39.59-56.22 59.97C240.08 73.62 206.28 35.53 168 0 69.74 91.17 0 209.96 0 281.6 0 408.85 100.29 512 224 512s224-103.15 224-230.4c0-53.27-51.98-163.14-124.44-230.4zm-19.47 340.65C282.43 407.01 255.72 416 226.86 416 154.71 416 96 368.26 96 290.75c0-38.61 24.31-72.63 72.79-130.75 6.93 7.98 98.83 125.34 98.83 125.34l58.63-66.88c4.14 6.85 7.91 13.55 11.27 19.97 27.35 52.19 15.81 118.97-33.43 153.42z"},child:[]}]})(n)}function fe(n){return ce({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M336 448H16c-8.8 0-16 7.2-16 16v32c0 8.8 7.2 16 16 16h320c8.8 0 16-7.2 16-16v-32c0-8.8-7.2-16-16-16zm157.2-340.7l-81-81c-6.2-6.2-16.4-6.2-22.6 0l-11.3 11.3c-6.2 6.2-6.2 16.4 0 22.6L416 97.9V160c0 28.1 20.9 51.3 48 55.2V376c0 13.2-10.8 24-24 24s-24-10.8-24-24v-32c0-48.6-39.4-88-88-88h-8V64c0-35.3-28.7-64-64-64H96C60.7 0 32 28.7 32 64v352h288V304h8c22.1 0 40 17.9 40 40v27.8c0 37.7 27 72 64.5 75.9 43 4.3 79.5-29.5 79.5-71.7V152.6c0-17-6.8-33.3-18.8-45.3zM256 192H96V64h160v128z"},child:[]}]})(n)}function de(n){return ce({attr:{viewBox:"0 0 256 256",fill:"currentColor"},child:[{tag:"path",attr:{d:"M256,120v48a16,16,0,0,1-16,16H227.31L192,219.31A15.86,15.86,0,0,1,180.69,224H103.31A15.86,15.86,0,0,1,92,219.31L52.69,180A15.86,15.86,0,0,1,48,168.69V148H24v24a8,8,0,0,1-16,0V108a8,8,0,0,1,16,0v24H48V80A16,16,0,0,1,64,64h60V40H100a8,8,0,0,1,0-16h64a8,8,0,0,1,0,16H140V64h40.69A15.86,15.86,0,0,1,192,68.69L227.31,104H240A16,16,0,0,1,256,120Z"},child:[]}]})(n)}function pe(n){return ce({attr:{viewBox:"0 0 256 256",fill:"currentColor"},child:[{tag:"path",attr:{d:"M160,80a8,8,0,0,1,8-8h72a8,8,0,0,1,0,16H168A8,8,0,0,1,160,80Zm80,88H168a8,8,0,0,0,0,16h72a8,8,0,0,0,0-16Zm0-64H168a8,8,0,0,0,0,16h72a8,8,0,0,0,0-16Zm0,32H168a8,8,0,0,0,0,16h72a8,8,0,0,0,0-16ZM128,48H88.9C44.62,48,8.33,83.62,8,127.39A80,80,0,0,0,88,208h40a16,16,0,0,0,16-16V64A16,16,0,0,0,128,48Z"},child:[]}]})(n)}function he(n){return ce({attr:{viewBox:"0 0 256 256",fill:"currentColor"},child:[{tag:"path",attr:{d:"M128,112a44,44,0,1,1,44-44A44.05,44.05,0,0,1,128,112Zm72,104H77.16L197.29,110a8.17,8.17,0,0,0,1.1-1.19,8.07,8.07,0,0,0,1.61-5.08A8,8,0,0,0,186.71,98l-24.54,21.65A80,80,0,0,0,49,179.25a8.33,8.33,0,0,0-.1,1.1L48,223.83A8,8,0,0,0,56,232H200a8,8,0,0,0,0-16Zm-11.88-73a8,8,0,0,0-6.25,1.94L119.47,200H200a8,8,0,0,0,8-8,79.6,79.6,0,0,0-14.27-45.62A8,8,0,0,0,188.12,143Z"},child:[]}]})(n)}function ve(n){return ce({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M184 0c30.9 0 56 25.1 56 56l0 400c0 30.9-25.1 56-56 56c-28.9 0-52.7-21.9-55.7-50.1c-5.2 1.4-10.7 2.1-16.3 2.1c-35.3 0-64-28.7-64-64c0-7.4 1.3-14.6 3.6-21.2C21.4 367.4 0 338.2 0 304c0-31.9 18.7-59.5 45.8-72.3C37.1 220.8 32 207 32 192c0-30.7 21.6-56.3 50.4-62.6C80.8 123.9 80 118 80 112c0-29.9 20.6-55.1 48.3-62.1C131.3 21.9 155.1 0 184 0zM328 0c28.9 0 52.6 21.9 55.7 49.9c27.8 7 48.3 32.1 48.3 62.1c0 6-.8 11.9-2.4 17.4c28.8 6.2 50.4 31.9 50.4 62.6c0 15-5.1 28.8-13.8 39.7C493.3 244.5 512 272.1 512 304c0 34.2-21.4 63.4-51.6 74.8c2.3 6.6 3.6 13.8 3.6 21.2c0 35.3-28.7 64-64 64c-5.6 0-11.1-.7-16.3-2.1c-3 28.2-26.8 50.1-55.7 50.1c-30.9 0-56-25.1-56-56l0-400c0-30.9 25.1-56 56-56z"},child:[]}]})(n)}function be(n){return ce({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M61.1 224C45 224 32 211 32 194.9c0-1.9 .2-3.7 .6-5.6C37.9 168.3 78.8 32 256 32s218.1 136.3 223.4 157.3c.5 1.9 .6 3.7 .6 5.6c0 16.1-13 29.1-29.1 29.1L61.1 224zM144 128a16 16 0 1 0 -32 0 16 16 0 1 0 32 0zm240 16a16 16 0 1 0 0-32 16 16 0 1 0 0 32zM272 96a16 16 0 1 0 -32 0 16 16 0 1 0 32 0zM16 304c0-26.5 21.5-48 48-48l384 0c26.5 0 48 21.5 48 48s-21.5 48-48 48L64 352c-26.5 0-48-21.5-48-48zm16 96c0-8.8 7.2-16 16-16l416 0c8.8 0 16 7.2 16 16l0 16c0 35.3-28.7 64-64 64L96 480c-35.3 0-64-28.7-64-64l0-16z"},child:[]}]})(n)}function me(n){return ce({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zm50.7-186.9L162.4 380.6c-19.4 7.5-38.5-11.6-31-31l55.5-144.3c3.3-8.5 9.9-15.1 18.4-18.4l144.3-55.5c19.4-7.5 38.5 11.6 31 31L325.1 306.7c-3.2 8.5-9.9 15.1-18.4 18.4zM288 256a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"},child:[]}]})(n)}function ge(n){return ce({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M32 0C23.1 0 14.6 3.7 8.6 10.2S-.6 25.4 .1 34.3L28.9 437.7c3 41.9 37.8 74.3 79.8 74.3l166.6 0c42 0 76.8-32.4 79.8-74.3L383.9 34.3c.6-8.9-2.4-17.6-8.5-24.1S360.9 0 352 0L32 0zM73 156.5L66.4 64l251.3 0L311 156.5l-24.2 12.1c-19.4 9.7-42.2 9.7-61.6 0c-20.9-10.4-45.5-10.4-66.4 0c-19.4 9.7-42.2 9.7-61.6 0L73 156.5z"},child:[]}]})(n)}function ye(n){return ce({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M215.7 499.2C267 435 384 279.4 384 192C384 86 298 0 192 0S0 86 0 192c0 87.4 117 243 168.3 307.2c12.3 15.3 35.1 15.3 47.4 0zM192 128a64 64 0 1 1 0 128 64 64 0 1 1 0-128z"},child:[]}]})(n)}function we(n){return ce({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M384 476.1L192 421.2l0-385.3L384 90.8l0 385.3zm32-1.2l0-386.5L543.1 37.5c15.8-6.3 32.9 5.3 32.9 22.3l0 334.8c0 9.8-6 18.6-15.1 22.3L416 474.8zM15.1 95.1L160 37.2l0 386.5L32.9 474.5C17.1 480.8 0 469.2 0 452.2L0 117.4c0-9.8 6-18.6 15.1-22.3z"},child:[]}]})(n)}function xe(n){return ce({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M192 0C139 0 96 43 96 96l0 160c0 53 43 96 96 96s96-43 96-96l0-160c0-53-43-96-96-96zM64 216c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 40c0 89.1 66.2 162.7 152 174.4l0 33.6-48 0c-13.3 0-24 10.7-24 24s10.7 24 24 24l72 0 72 0c13.3 0 24-10.7 24-24s-10.7-24-24-24l-48 0 0-33.6c85.8-11.7 152-85.3 152-174.4l0-40c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 40c0 70.7-57.3 128-128 128s-128-57.3-128-128l0-40z"},child:[]}]})(n)}function _e(n){return ce({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M320 48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM125.7 175.5c9.9-9.9 23.4-15.5 37.5-15.5c1.9 0 3.8 .1 5.6 .3L137.6 254c-9.3 28 1.7 58.8 26.8 74.5l86.2 53.9-25.4 88.8c-4.9 17 5 34.7 22 39.6s34.7-5 39.6-22l28.7-100.4c5.9-20.6-2.6-42.6-20.7-53.9L238 299l30.9-82.4 5.1 12.3C289 264.7 323.9 288 362.7 288l21.3 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-21.3 0c-12.9 0-24.6-7.8-29.5-19.7l-6.3-15c-14.6-35.1-44.1-61.9-80.5-73.1l-48.7-15c-11.1-3.4-22.7-5.2-34.4-5.2c-31 0-60.8 12.3-82.7 34.3L57.4 153.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l23.1-23.1zM91.2 352L32 352c-17.7 0-32 14.3-32 32s14.3 32 32 32l69.6 0c19 0 36.2-11.2 43.9-28.5L157 361.6l-9.5-6c-17.5-10.9-30.5-26.8-37.9-44.9L91.2 352z"},child:[]}]})(n)}function ke(n){return ce({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M309.5 178.4L447.9 297.1c-1.6 .9-3.2 2-4.8 3c-18 12.4-40.1 20.3-59.2 20.3c-19.6 0-40.8-7.7-59.2-20.3c-22.1-15.5-51.6-15.5-73.7 0c-17.1 11.8-38 20.3-59.2 20.3c-10.1 0-21.1-2.2-31.9-6.2C163.1 193.2 262.2 96 384 96l64 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-64 0c-26.9 0-52.3 6.6-74.5 18.4zM160 160A64 64 0 1 1 32 160a64 64 0 1 1 128 0zM306.5 325.9C329 341.4 356.5 352 384 352c26.9 0 55.4-10.8 77.4-26.1c0 0 0 0 0 0c11.9-8.5 28.1-7.8 39.2 1.7c14.4 11.9 32.5 21 50.6 25.2c17.2 4 27.9 21.2 23.9 38.4s-21.2 27.9-38.4 23.9c-24.5-5.7-44.9-16.5-58.2-25C449.5 405.7 417 416 384 416c-31.9 0-60.6-9.9-80.4-18.9c-5.8-2.7-11.1-5.3-15.6-7.7c-4.5 2.4-9.7 5.1-15.6 7.7c-19.8 9-48.5 18.9-80.4 18.9c-33 0-65.5-10.3-94.5-25.8c-13.4 8.4-33.7 19.3-58.2 25c-17.2 4-34.4-6.7-38.4-23.9s6.7-34.4 23.9-38.4c18.1-4.2 36.2-13.3 50.6-25.2c11.1-9.4 27.3-10.1 39.2-1.7c0 0 0 0 0 0C136.7 341.2 165.1 352 192 352c27.5 0 55-10.6 77.5-26.1c11.1-7.9 25.9-7.9 37 0z"},child:[]}]})(n)}const ze=n=>{const t=je(n),{conflictingClassGroups:e,conflictingClassGroupModifiers:r}=n;return{getClassGroupId:n=>{const e=n.split("-");return""===e[0]&&1!==e.length&&e.shift(),Ce(e,t)||Se(n)},getConflictingClassGroupIds:(n,t)=>{const o=e[n]||[];return t&&r[n]?[...o,...r[n]]:o}}},Ce=(n,t)=>{var e;if(0===n.length)return t.classGroupId;const r=n[0],o=t.nextPart.get(r),i=o?Ce(n.slice(1),o):void 0;if(i)return i;if(0===t.validators.length)return;const l=n.join("-");return null==(e=t.validators.find((({validator:n})=>n(l))))?void 0:e.classGroupId},Me=/^\[(.+)\]$/,Se=n=>{if(Me.test(n)){const t=Me.exec(n)[1],e=null==t?void 0:t.substring(0,t.indexOf(":"));if(e)return"arbitrary.."+e}},je=n=>{const{theme:t,prefix:e}=n,r={nextPart:new Map,validators:[]};return Ae(Object.entries(n.classGroups),e).forEach((([n,e])=>{Oe(e,r,n,t)})),r},Oe=(n,t,e,r)=>{n.forEach((n=>{if("string"!=typeof n){if("function"==typeof n)return Le(n)?void Oe(n(r),t,e,r):void t.validators.push({validator:n,classGroupId:e});Object.entries(n).forEach((([n,o])=>{Oe(o,Ee(t,n),e,r)}))}else(""===n?t:Ee(t,n)).classGroupId=e}))},Ee=(n,t)=>{let e=n;return t.split("-").forEach((n=>{e.nextPart.has(n)||e.nextPart.set(n,{nextPart:new Map,validators:[]}),e=e.nextPart.get(n)})),e},Le=n=>n.isThemeGetter,Ae=(n,t)=>t?n.map((([n,e])=>[n,e.map((n=>"string"==typeof n?t+n:"object"==typeof n?Object.fromEntries(Object.entries(n).map((([n,e])=>[t+n,e]))):n))])):n,Te=n=>{if(n<1)return{get:()=>{},set:()=>{}};let t=0,e=new Map,r=new Map;const o=(o,i)=>{e.set(o,i),t++,t>n&&(t=0,r=e,e=new Map)};return{get(n){let t=e.get(n);return void 0!==t?t:void 0!==(t=r.get(n))?(o(n,t),t):void 0},set(n,t){e.has(n)?e.set(n,t):o(n,t)}}},Ne=n=>{const{separator:t,experimentalParseClassName:e}=n,r=1===t.length,o=t[0],i=t.length,l=n=>{const e=[];let l,c=0,a=0;for(let f=0;f<n.length;f++){let u=n[f];if(0===c){if(u===o&&(r||n.slice(f,f+i)===t)){e.push(n.slice(a,f)),a=f+i;continue}if("/"===u){l=f;continue}}"["===u?c++:"]"===u&&c--}const u=0===e.length?n:n.substring(a),s=u.startsWith("!");return{modifiers:e,hasImportantModifier:s,baseClassName:s?u.substring(1):u,maybePostfixModifierPosition:l&&l>a?l-a:void 0}};return e?n=>e({className:n,parseClassName:l}):l},Be=n=>{if(n.length<=1)return n;const t=[];let e=[];return n.forEach((n=>{"["===n[0]?(t.push(...e.sort(),n),e=[]):e.push(n)})),t.push(...e.sort()),t},He=/\s+/;function Ie(){let n,t,e=0,r="";for(;e<arguments.length;)(n=arguments[e++])&&(t=Pe(n))&&(r&&(r+=" "),r+=t);return r}const Pe=n=>{if("string"==typeof n)return n;let t,e="";for(let r=0;r<n.length;r++)n[r]&&(t=Pe(n[r]))&&(e&&(e+=" "),e+=t);return e};function Re(n,...t){let e,r,o,i=function(c){const a=t.reduce(((n,t)=>t(n)),n());return e=(n=>({cache:Te(n.cacheSize),parseClassName:Ne(n),...ze(n)}))(a),r=e.cache.get,o=e.cache.set,i=l,l(c)};function l(n){const t=r(n);if(t)return t;const i=((n,t)=>{const{parseClassName:e,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=[],l=n.trim().split(He);let c="";for(let a=l.length-1;a>=0;a-=1){const n=l[a],{modifiers:t,hasImportantModifier:u,baseClassName:s,maybePostfixModifierPosition:f}=e(n);let d=Boolean(f),p=r(d?s.substring(0,f):s);if(!p){if(!d){c=n+(c.length>0?" "+c:c);continue}if(p=r(s),!p){c=n+(c.length>0?" "+c:c);continue}d=!1}const h=Be(t).join(":"),v=u?h+"!":h,b=v+p;if(i.includes(b))continue;i.push(b);const m=o(p,d);for(let e=0;e<m.length;++e){const n=m[e];i.push(v+n)}c=n+(c.length>0?" "+c:c)}return c})(n,e);return o(n,i),i}return function(){return i(Ie.apply(null,arguments))}}const Ve=n=>{const t=t=>t[n]||[];return t.isThemeGetter=!0,t},$e=/^\[(?:([a-z-]+):)?(.+)\]$/i,Fe=/^\d+\/\d+$/,We=new Set(["px","full","screen"]),De=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ue=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Ze=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Ge=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,qe=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Je=n=>Ye(n)||We.has(n)||Fe.test(n),Xe=n=>sr(n,"length",fr),Ye=n=>Boolean(n)&&!Number.isNaN(Number(n)),Ke=n=>sr(n,"number",Ye),Qe=n=>Boolean(n)&&Number.isInteger(Number(n)),nr=n=>n.endsWith("%")&&Ye(n.slice(0,-1)),tr=n=>$e.test(n),er=n=>De.test(n),rr=new Set(["length","size","percentage"]),or=n=>sr(n,rr,dr),ir=n=>sr(n,"position",dr),lr=new Set(["image","url"]),cr=n=>sr(n,lr,hr),ar=n=>sr(n,"",pr),ur=()=>!0,sr=(n,t,e)=>{const r=$e.exec(n);return!!r&&(r[1]?"string"==typeof t?r[1]===t:t.has(r[1]):e(r[2]))},fr=n=>Ue.test(n)&&!Ze.test(n),dr=()=>!1,pr=n=>Ge.test(n),hr=n=>qe.test(n),vr=Re((()=>{const n=Ve("colors"),t=Ve("spacing"),e=Ve("blur"),r=Ve("brightness"),o=Ve("borderColor"),i=Ve("borderRadius"),l=Ve("borderSpacing"),c=Ve("borderWidth"),a=Ve("contrast"),u=Ve("grayscale"),s=Ve("hueRotate"),f=Ve("invert"),d=Ve("gap"),p=Ve("gradientColorStops"),h=Ve("gradientColorStopPositions"),v=Ve("inset"),b=Ve("margin"),m=Ve("opacity"),g=Ve("padding"),y=Ve("saturate"),w=Ve("scale"),x=Ve("sepia"),_=Ve("skew"),k=Ve("space"),z=Ve("translate"),C=()=>["auto",tr,t],M=()=>[tr,t],S=()=>["",Je,Xe],j=()=>["auto",Ye,tr],O=()=>["","0",tr],E=()=>[Ye,tr];return{cacheSize:500,separator:":",theme:{colors:[ur],spacing:[Je,Xe],blur:["none","",er,tr],brightness:E(),borderColor:[n],borderRadius:["none","","full",er,tr],borderSpacing:M(),borderWidth:S(),contrast:E(),grayscale:O(),hueRotate:E(),invert:O(),gap:M(),gradientColorStops:[n],gradientColorStopPositions:[nr,Xe],inset:C(),margin:C(),opacity:E(),padding:M(),saturate:E(),scale:E(),sepia:O(),skew:E(),space:M(),translate:M()},classGroups:{aspect:[{aspect:["auto","square","video",tr]}],container:["container"],columns:[{columns:[er]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",tr]}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[v]}],"inset-x":[{"inset-x":[v]}],"inset-y":[{"inset-y":[v]}],start:[{start:[v]}],end:[{end:[v]}],top:[{top:[v]}],right:[{right:[v]}],bottom:[{bottom:[v]}],left:[{left:[v]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Qe,tr]}],basis:[{basis:C()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",tr]}],grow:[{grow:O()}],shrink:[{shrink:O()}],order:[{order:["first","last","none",Qe,tr]}],"grid-cols":[{"grid-cols":[ur]}],"col-start-end":[{col:["auto",{span:["full",Qe,tr]},tr]}],"col-start":[{"col-start":j()}],"col-end":[{"col-end":j()}],"grid-rows":[{"grid-rows":[ur]}],"row-start-end":[{row:["auto",{span:[Qe,tr]},tr]}],"row-start":[{"row-start":j()}],"row-end":[{"row-end":j()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",tr]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",tr]}],gap:[{gap:[d]}],"gap-x":[{"gap-x":[d]}],"gap-y":[{"gap-y":[d]}],"justify-content":[{justify:["normal","start","end","center","between","around","evenly","stretch"]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal","start","end","center","between","around","evenly","stretch","baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[g]}],px:[{px:[g]}],py:[{py:[g]}],ps:[{ps:[g]}],pe:[{pe:[g]}],pt:[{pt:[g]}],pr:[{pr:[g]}],pb:[{pb:[g]}],pl:[{pl:[g]}],m:[{m:[b]}],mx:[{mx:[b]}],my:[{my:[b]}],ms:[{ms:[b]}],me:[{me:[b]}],mt:[{mt:[b]}],mr:[{mr:[b]}],mb:[{mb:[b]}],ml:[{ml:[b]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",tr,t]}],"min-w":[{"min-w":[tr,t,"min","max","fit"]}],"max-w":[{"max-w":[tr,t,"none","full","min","max","fit","prose",{screen:[er]},er]}],h:[{h:[tr,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[tr,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[tr,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[tr,t,"auto","min","max","fit"]}],"font-size":[{text:["base",er,Xe]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Ke]}],"font-family":[{font:[ur]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",tr]}],"line-clamp":[{"line-clamp":["none",Ye,Ke]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Je,tr]}],"list-image":[{"list-image":["none",tr]}],"list-style-type":[{list:["none","disc","decimal",tr]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[n]}],"placeholder-opacity":[{"placeholder-opacity":[m]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[n]}],"text-opacity":[{"text-opacity":[m]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:["solid","dashed","dotted","double","none","wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Je,Xe]}],"underline-offset":[{"underline-offset":["auto",Je,tr]}],"text-decoration-color":[{decoration:[n]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:M()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",tr]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",tr]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[m]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",ir]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",or]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},cr]}],"bg-color":[{bg:[n]}],"gradient-from-pos":[{from:[h]}],"gradient-via-pos":[{via:[h]}],"gradient-to-pos":[{to:[h]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[c]}],"border-w-x":[{"border-x":[c]}],"border-w-y":[{"border-y":[c]}],"border-w-s":[{"border-s":[c]}],"border-w-e":[{"border-e":[c]}],"border-w-t":[{"border-t":[c]}],"border-w-r":[{"border-r":[c]}],"border-w-b":[{"border-b":[c]}],"border-w-l":[{"border-l":[c]}],"border-opacity":[{"border-opacity":[m]}],"border-style":[{border:["solid","dashed","dotted","double","none","hidden"]}],"divide-x":[{"divide-x":[c]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[c]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[m]}],"divide-style":[{divide:["solid","dashed","dotted","double","none"]}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["","solid","dashed","dotted","double","none"]}],"outline-offset":[{"outline-offset":[Je,tr]}],"outline-w":[{outline:[Je,Xe]}],"outline-color":[{outline:[n]}],"ring-w":[{ring:S()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[n]}],"ring-opacity":[{"ring-opacity":[m]}],"ring-offset-w":[{"ring-offset":[Je,Xe]}],"ring-offset-color":[{"ring-offset":[n]}],shadow:[{shadow:["","inner","none",er,ar]}],"shadow-color":[{shadow:[ur]}],opacity:[{opacity:[m]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],filter:[{filter:["","none"]}],blur:[{blur:[e]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",er,tr]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[s]}],invert:[{invert:[f]}],saturate:[{saturate:[y]}],sepia:[{sepia:[x]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[e]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[s]}],"backdrop-invert":[{"backdrop-invert":[f]}],"backdrop-opacity":[{"backdrop-opacity":[m]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[x]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[l]}],"border-spacing-x":[{"border-spacing-x":[l]}],"border-spacing-y":[{"border-spacing-y":[l]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",tr]}],duration:[{duration:E()}],ease:[{ease:["linear","in","out","in-out",tr]}],delay:[{delay:E()}],animate:[{animate:["none","spin","ping","pulse","bounce",tr]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[Qe,tr]}],"translate-x":[{"translate-x":[z]}],"translate-y":[{"translate-y":[z]}],"skew-x":[{"skew-x":[_]}],"skew-y":[{"skew-y":[_]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",tr]}],accent:[{accent:["auto",n]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",tr]}],"caret-color":[{caret:[n]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":M()}],"scroll-mx":[{"scroll-mx":M()}],"scroll-my":[{"scroll-my":M()}],"scroll-ms":[{"scroll-ms":M()}],"scroll-me":[{"scroll-me":M()}],"scroll-mt":[{"scroll-mt":M()}],"scroll-mr":[{"scroll-mr":M()}],"scroll-mb":[{"scroll-mb":M()}],"scroll-ml":[{"scroll-ml":M()}],"scroll-p":[{"scroll-p":M()}],"scroll-px":[{"scroll-px":M()}],"scroll-py":[{"scroll-py":M()}],"scroll-ps":[{"scroll-ps":M()}],"scroll-pe":[{"scroll-pe":M()}],"scroll-pt":[{"scroll-pt":M()}],"scroll-pr":[{"scroll-pr":M()}],"scroll-pb":[{"scroll-pb":M()}],"scroll-pl":[{"scroll-pl":M()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",tr]}],fill:[{fill:[n,"none"]}],"stroke-w":[{stroke:[Je,Xe,Ke]}],stroke:[{stroke:[n,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}));function br(n){return ce({attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M21.881 5.223a.496.496 0 0 0-.747-.412c-.672.392-1.718.898-2.643.898-.421 0-.849-.064-1.289-.198a5.712 5.712 0 0 1-.808-.309c-1.338-.639-2.567-1.767-3.696-2.889a1.008 1.008 0 0 0-.698-.29 1.008 1.008 0 0 0-.698.29c-1.129 1.122-2.358 2.25-3.696 2.889h-.001a5.655 5.655 0 0 1-.807.309c-.44.134-.869.198-1.289.198-.925 0-1.971-.507-2.643-.898a.496.496 0 0 0-.747.412c-.061 1.538-.077 4.84.688 7.444 1.399 4.763 4.48 7.976 8.91 9.292l.14.041.14-.014V22v-.014H12l.143.014.14-.041c4.43-1.316 7.511-4.529 8.91-9.292.765-2.604.748-5.906.688-7.444z"},child:[]}]})(n)}function mr(n){return ce({attr:{version:"1",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M2.2 9.4c0 1.3.2 3.3 2 5.1 1.6 1.6 6.9 5.2 7.1 5.4.2.1.4.2.6.2s.4-.1.6-.2c.2-.2 5.5-3.7 7.1-5.4 1.8-1.8 2-3.8 2-5.1 0-3-2.4-5.4-5.4-5.4-1.6 0-3.2.9-4.2 2.3-1-1.4-2.6-2.3-4.4-2.3-2.9 0-5.4 2.4-5.4 5.4z"},child:[]}]})(n)}export{zn as A,br as B,P as D,fe as F,de as P,kt as R,Mn as T,Qt as a,Mt as b,D as c,se as d,pe as e,he as f,ue as g,me as h,ye as i,we as j,w as k,Kt as l,mr as m,xe as n,be as o,ge as p,Sn as q,ke as r,_e as s,vr as t,Yt as u,ve as v,wn as w,_n as y};
