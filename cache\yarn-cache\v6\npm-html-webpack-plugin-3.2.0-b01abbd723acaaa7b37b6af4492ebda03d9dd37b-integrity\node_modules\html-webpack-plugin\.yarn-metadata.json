{"manifest": {"name": "html-webpack-plugin", "version": "3.2.0", "license": "MIT", "description": "Simplifies creation of HTML files to serve your webpack bundles", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/ampedandwired"}, "main": "index.js", "files": ["lib/", "index.js", "default_index.ejs"], "scripts": {"pretest": "semistandard", "commit": "git-cz", "build-examples": "node examples/build-examples.js", "test": "jasmine", "release": "standard-version"}, "semistandard": {"ignore": ["examples/*/dist/**/*.*"]}, "devDependencies": {"appcache-webpack-plugin": "^1.3.0", "commitizen": "2.9.6", "css-loader": "^0.26.1", "cz-conventional-changelog": "2.1.0", "dir-compare": "1.3.0", "es6-promise": "^4.0.5", "extract-text-webpack-plugin": "^1.0.1", "file-loader": "^0.9.0", "html-loader": "^0.4.4", "jade": "^1.11.0", "jade-loader": "^0.8.0", "jasmine": "^2.5.2", "jasmine-diff-matchers": "^2.0.0", "rimraf": "^2.5.4", "semistandard": "8.0.0", "standard-version": "^4.3.0", "style-loader": "^0.13.1", "underscore-template-loader": "^0.7.3", "url-loader": "^0.5.7", "webpack": "^1.14.0", "webpack-recompilation-simulator": "^1.3.0"}, "dependencies": {"html-minifier": "^3.2.3", "loader-utils": "^0.2.16", "lodash": "^4.17.3", "pretty-error": "^2.0.2", "tapable": "^1.0.0", "toposort": "^1.0.0", "util.promisify": "1.0.0"}, "peerDependencies": {"webpack": "^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0"}, "keywords": ["webpack", "plugin", "html", "html-webpack-plugin"], "bugs": {"url": "https://github.com/jantimon/html-webpack-plugin/issues"}, "homepage": "https://github.com/jantimon/html-webpack-plugin", "repository": {"type": "git", "url": "https://github.com/jantimon/html-webpack-plugin.git"}, "engines": {"node": ">=6.9"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-html-webpack-plugin-3.2.0-b01abbd723acaaa7b37b6af4492ebda03d9dd37b-integrity\\node_modules\\html-webpack-plugin\\package.json", "readmeFilename": "README.md", "readme": "[![npm][npm]][npm-url]\n[![node][node]][node-url]\n![npm](https://img.shields.io/npm/dw/html-webpack-plugin.svg)\n[![deps][deps]][deps-url]\n[![tests][tests]][tests-url]\n[![Backers on Open Collective](https://opencollective.com/html-webpack-plugin/backers/badge.svg)](#backers)\n [![Sponsors on Open Collective](https://opencollective.com/html-webpack-plugin/sponsors/badge.svg)](#sponsors) \n\n<div align=\"center\">\n  <img width=\"200\" height=\"200\" src=\"https://worldvectorlogo.com/logos/html5.svg\">\n  <a href=\"https://github.com/webpack/webpack\">\n    <img width=\"200\" height=\"200\"\n      src=\"https://webpack.js.org/assets/icon-square-big.svg\">\n  </a>\n  <div>\n    <img width=\"100\" height=\"100\" title=\"Webpack Plugin\" src=\"http://michael-ciniawsky.github.io/postcss-load-plugins/logo.svg\">\n  </div>\n  <h1>HTML Webpack Plugin</h1>\n  <p>Plugin that simplifies creation of HTML files to serve your bundles</p>\n</div>\n\n<h2 align=\"center\">Install</h2>\n\n\n```bash\n  npm i --save-dev html-webpack-plugin\n```\n\n```bash\n  yarn add --dev html-webpack-plugin\n```\n\n\n\nThis is a [webpack](http://webpack.js.org/) plugin that simplifies creation of HTML files to serve your `webpack` bundles. This is especially useful for `webpack` bundles that include a hash in the filename which changes every compilation. You can either let the plugin generate an HTML file for you, supply\nyour own template using `lodash` templates or use your own loader.\n\n### `Plugins`\n\nThe `html-webpack-plugin` provides [hooks](https://github.com/jantimon/html-webpack-plugin#events) to extend it to your needs. There are already some really powerful plugins which can be integrated with zero configuration\n\n * [webpack-subresource-integrity](https://www.npmjs.com/package/webpack-subresource-integrity) for enhanced asset security\n * [appcache-webpack-plugin](https://github.com/lettertwo/appcache-webpack-plugin) for iOS and Android offline usage\n * [favicons-webpack-plugin](https://github.com/jantimon/favicons-webpack-plugin) which generates favicons and icons for iOS, Android and desktop browsers\n * [html-webpack-harddisk-plugin](https://github.com/jantimon/html-webpack-harddisk-plugin) can be used to always write to disk the html file, useful when webpack-dev-server / HMR are being used\n * [html-webpack-inline-source-plugin](https://github.com/DustinJackson/html-webpack-inline-source-plugin) to inline your assets in the resulting HTML file\n * [html-webpack-inline-svg-plugin](https://github.com/thegc/html-webpack-inline-svg-plugin) to inline SVGs in the resulting HTML file.\n * [html-webpack-exclude-assets-plugin](https://github.com/jamesjieye/html-webpack-exclude-assets-plugin) for excluding assets using regular expressions\n * [html-webpack-include-assets-plugin](https://github.com/jharris4/html-webpack-include-assets-plugin) for including lists of js or css file paths (such as those copied by the copy-webpack-plugin).\n * [script-ext-html-webpack-plugin](https://github.com/numical/script-ext-html-webpack-plugin) to add `async`, `defer` or `module` attributes to your `<script>` elements, or even inline them\n * [style-ext-html-webpack-plugin](https://github.com/numical/style-ext-html-webpack-plugin) to convert your `<link>`s to external stylesheets into `<style>` elements containing internal CSS\n * [resource-hints-webpack-plugin](https://github.com/jantimon/resource-hints-webpack-plugin) to add resource hints for faster initial page loads using `<link rel='preload'>` and `<link rel='prefetch'>`\n * [preload-webpack-plugin](https://github.com/GoogleChrome/preload-webpack-plugin) for automatically wiring up asynchronous (and other types) of JavaScript chunks using `<link rel='preload'>` helping with lazy-loading\n * [link-media-html-webpack-plugin](https://github.com/yaycmyk/link-media-html-webpack-plugin) allows for injected stylesheet `<link />` tags to have their media attribute set automatically; useful for providing specific desktop/mobile/print etc. stylesheets that the browser will conditionally download\n * [inline-chunk-manifest-html-webpack-plugin](https://github.com/jouni-kantola/inline-chunk-manifest-html-webpack-plugin) for inlining webpack's chunk manifest. Default extracts manifest and inlines in `<head>`\n * [html-webpack-inline-style-plugin](https://github.com/djaax/html-webpack-inline-style-plugin) for inlining styles to HTML elements using [juice](https://github.com/Automattic/juice). Useful for email generation automatisation.\n * [html-webpack-exclude-empty-assets-plugin](https://github.com/KnisterPeter/html-webpack-exclude-empty-assets-plugin) removes empty assets from being added to the html. This fixes some problems with extract-text-plugin with webpack 4.\n * [webpack-concat-plugin](https://github.com/hxlniada/webpack-concat-plugin) for concat and uglify files that needn't to be webpack bundles(for legacy files) and inject to html-webpack-plugin.\n \n\n<h2 align=\"center\">Usage</h2>\n\nThe plugin will generate an HTML5 file for you that includes all your `webpack`\nbundles in the body using `script` tags. Just add the plugin to your `webpack`\nconfig as follows:\n\n**webpack.config.js**\n```js\nconst HtmlWebpackPlugin = require('html-webpack-plugin')\n\nmodule.exports = {\n  entry: 'index.js',\n  output: {\n    path: __dirname + '/dist',\n    filename: 'index_bundle.js'\n  },\n  plugins: [\n    new HtmlWebpackPlugin()\n  ]\n}\n```\n\nThis will generate a file `dist/index.html` containing the following\n\n```html\n<!DOCTYPE html>\n<html>\n  <head>\n    <meta charset=\"UTF-8\">\n    <title>Webpack App</title>\n  </head>\n  <body>\n    <script src=\"index_bundle.js\"></script>\n  </body>\n</html>\n```\n\nIf you have multiple `webpack` entry points, they will all be included with `script` tags in the generated HTML.\n\nIf you have any CSS assets in webpack's output (for example, CSS extracted with the [ExtractTextPlugin](https://github.com/webpack/extract-text-webpack-plugin))\nthen these will be included with `<link>` tags in the HTML head.\n\nIf you have plugins that make use of it, `html-webpack-plugin` should be ordered first before any of the integrated plugins.\n\n<h2 align=\"center\">Options</h2>\n\nYou can pass a hash of configuration options to `html-webpack-plugin`.\nAllowed values are as follows\n\n|Name|Type|Default|Description|\n|:--:|:--:|:-----:|:----------|\n|**[`title`](#)**|`{String}`|``|The title to use for the generated HTML document|\n|**[`filename`](#)**|`{String}`|`'index.html'`|The file to write the HTML to. Defaults to `index.html`. You can specify a subdirectory here too (eg: `assets/admin.html`)|\n|**[`template`](#)**|`{String}`|``|`webpack` require path to the template. Please see the [docs](https://github.com/jantimon/html-webpack-plugin/blob/master/docs/template-option.md) for details|\n|**[`templateParameters`](#)**|`{Boolean\\|Object\\|Function}`|``| Allows to overwrite the parameters used in the template |\n|**[`inject`](#)**|`{Boolean\\|String}`|`true`|`true \\|\\| 'head' \\|\\| 'body' \\|\\| false` Inject all assets into the given `template` or `templateContent`. When passing `true` or `'body'` all javascript resources will be placed at the bottom of the body element. `'head'` will place the scripts in the head element|\n|**[`favicon`](#)**|`{String}`|``|Adds the given favicon path to the output HTML|\n|**[`meta`](#)**|`{Object}`|`{}`|Allows to inject `meta`-tags. E.g. `meta: {viewport: 'width=device-width, initial-scale=1, shrink-to-fit=no'}`|\n|**[`minify`](#)**|`{Boolean\\|Object}`|`true`|Pass [html-minifier](https://github.com/kangax/html-minifier#options-quick-reference)'s options as object to minify the output|\n|**[`hash`](#)**|`{Boolean}`|`false`|If `true` then append a unique `webpack` compilation hash to all included scripts and CSS files. This is useful for cache busting|\n|**[`cache`](#)**|`{Boolean}`|`true`|Emit the file only if it was changed|\n|**[`showErrors`](#)**|`{Boolean}`|`true`|Errors details will be written into the HTML page|\n|**[`chunks`](#)**|`{?}`|`?`|Allows you to add only some chunks (e.g only the unit-test chunk)|\n|**[`chunksSortMode`](#plugins)**|`{String\\|Function}`|`auto`|Allows to control how chunks should be sorted before they are included to the HTML. Allowed values are `'none' \\| 'auto' \\| 'dependency' \\| 'manual' \\| {Function}`|\n|**[`excludeChunks`](#)**|`{Array.<string>}`|``|Allows you to skip some chunks (e.g don't add the unit-test chunk)|\n|**[`xhtml`](#)**|`{Boolean}`|`false`|If `true` render the `link` tags as self-closing (XHTML compliant)|\n\nHere's an example webpack config illustrating how to use these options\n\n**webpack.config.js**\n```js\n{\n  entry: 'index.js',\n  output: {\n    path: __dirname + '/dist',\n    filename: 'index_bundle.js'\n  },\n  plugins: [\n    new HtmlWebpackPlugin({\n      title: 'My App',\n      filename: 'assets/admin.html'\n    })\n  ]\n}\n```\n\n### `Generating Multiple HTML Files`\n\nTo generate more than one HTML file, declare the plugin more than\nonce in your plugins array\n\n**webpack.config.js**\n```js\n{\n  entry: 'index.js',\n  output: {\n    path: __dirname + '/dist',\n    filename: 'index_bundle.js'\n  },\n  plugins: [\n    new HtmlWebpackPlugin(), // Generates default index.html\n    new HtmlWebpackPlugin({  // Also generate a test.html\n      filename: 'test.html',\n      template: 'src/assets/test.html'\n    })\n  ]\n}\n```\n\n### `Writing Your Own Templates`\n\nIf the default generated HTML doesn't meet your needs you can supply\nyour own template. The easiest way is to use the `template` option and pass a custom HTML file.\nThe html-webpack-plugin will automatically inject all necessary CSS, JS, manifest\nand favicon files into the markup.\n\n```js\nplugins: [\n  new HtmlWebpackPlugin({\n    title: 'Custom template',\n    // Load a custom template (lodash by default see the FAQ for details)\n    template: 'index.html'\n  })\n]\n```\n\n**index.html**\n```html\n<!DOCTYPE html>\n<html>\n  <head>\n    <meta http-equiv=\"Content-type\" content=\"text/html; charset=utf-8\"/>\n    <title><%= htmlWebpackPlugin.options.title %></title>\n  </head>\n  <body>\n  </body>\n</html>\n```\n\nIf you already have a template loader, you can use it to parse the template.\nPlease note that this will also happen if you specifiy the html-loader and use `.html` file as template.\n\n**webpack.config.js**\n```js\nmodule: {\n  loaders: [\n    { test: /\\.hbs$/, loader: \"handlebars\" }\n  ]\n},\nplugins: [\n  new HtmlWebpackPlugin({\n    title: 'Custom template using Handlebars',\n    template: 'index.hbs'\n  })\n]\n```\n\nYou can use the `lodash` syntax out of the box. If the `inject` feature doesn't fit your needs and you want full control over the asset placement use the [default template](https://github.com/jaketrent/html-webpack-template/blob/86f285d5c790a6c15263f5cc50fd666d51f974fd/index.html) of the [html-webpack-template project](https://github.com/jaketrent/html-webpack-template) as a starting point for writing your own.\n\nThe following variables are available in the template:\n- `htmlWebpackPlugin`: data specific to this plugin\n  - `htmlWebpackPlugin.files`: a massaged representation of the\n    `assetsByChunkName` attribute of webpack's [stats](https://github.com/webpack/docs/wiki/node.js-api#stats)\n    object. It contains a mapping from entry point name to the bundle filename, eg:\n    ```json\n    \"htmlWebpackPlugin\": {\n      \"files\": {\n        \"css\": [ \"main.css\" ],\n        \"js\": [ \"assets/head_bundle.js\", \"assets/main_bundle.js\"],\n        \"chunks\": {\n          \"head\": {\n            \"entry\": \"assets/head_bundle.js\",\n            \"css\": [ \"main.css\" ]\n          },\n          \"main\": {\n            \"entry\": \"assets/main_bundle.js\",\n            \"css\": []\n          },\n        }\n      }\n    }\n    ```\n    If you've set a publicPath in your webpack config this will be reflected\n    correctly in this assets hash.\n\n  - `htmlWebpackPlugin.options`: the options hash that was passed to\n     the plugin. In addition to the options actually used by this plugin,\n     you can use this hash to pass arbitrary data through to your template.\n\n- `webpack`: the webpack [stats](https://github.com/webpack/docs/wiki/node.js-api#stats)\n  object. Note that this is the stats object as it was at the time the HTML template\n  was emitted and as such may not have the full set of stats that are available\n  after the webpack run is complete.\n\n- `webpackConfig`: the webpack configuration that was used for this compilation. This\n  can be used, for example, to get the `publicPath` (`webpackConfig.output.publicPath`).\n\n- `compilation`: the webpack [compilation](https://webpack.js.org/api/compilation/) object.\n  This can be used, for example, to get the contents of processed assets and inline them\n  directly in the page, through `compilation.assets[...].source()`\n  (see [the inline template example](examples/inline/template.jade)).\n\n\n### `Filtering Chunks`\n\nTo include only certain chunks you can limit the chunks being used\n\n**webpack.config.js**\n```js\nplugins: [\n  new HtmlWebpackPlugin({\n    chunks: ['app']\n  })\n]\n```\n\nIt is also possible to exclude certain chunks by setting the `excludeChunks` option\n\n**webpack.config.js**\n```js\nplugins: [\n  new HtmlWebpackPlugin({\n    excludeChunks: [ 'dev-helper' ]\n  })\n]\n```\n\n### `Events`\n\nTo allow other [plugins](https://github.com/webpack/docs/wiki/plugins) to alter the HTML this plugin executes the following events:\n\n#### `Sync`\n\n* `html-webpack-plugin-alter-chunks`\n\n#### `Async`\n\n* `html-webpack-plugin-before-html-generation`\n* `html-webpack-plugin-before-html-processing`\n* `html-webpack-plugin-alter-asset-tags`\n* `html-webpack-plugin-after-html-processing`\n* `html-webpack-plugin-after-emit`\n\nExample implementation: [html-webpack-harddisk-plugin](https://github.com/jantimon/html-webpack-harddisk-plugin)\n\n**plugin.js**\n```js\nfunction MyPlugin(options) {\n  // Configure your plugin with options...\n}\n\nMyPlugin.prototype.apply = function (compiler) {\n  compiler.plugin('compilation', (compilation) => {\n    console.log('The compiler is starting a new compilation...');\n\n    compilation.plugin(\n      'html-webpack-plugin-before-html-processing',\n      (data, cb) => {\n        data.html += 'The Magic Footer'\n\n        cb(null, data)\n      }\n    )\n  })\n}\n\nmodule.exports = MyPlugin\n```\n\n**webpack.config.js**\n```js\nplugins: [\n  new MyPlugin({ options: '' })\n]\n```\n\nNote that the callback must be passed the HtmlWebpackPluginData in order to pass this onto any other plugins listening on the same `html-webpack-plugin-before-html-processing` event\n\n<h2 align=\"center\">Maintainers</h2>\n\n<table>\n  <tbody>\n    <tr>\n      <td align=\"center\">\n        <img width=\"150\" height=\"150\"\n        src=\"https://avatars3.githubusercontent.com/u/4113649?v=3&s=150\">\n        </br>\n        <a href=\"https://github.com/jantimon\">Jan Nicklas</a>\n      </td>\n      <td align=\"center\">\n        <img width=\"150\" height=\"150\"\n        src=\"https://avatars2.githubusercontent.com/u/4112409?v=3&s=150\">\n        </br>\n        <a href=\"https://github.com/mastilver\">Thomas Sileghem</a>\n      </td>\n    </tr>\n  <tbody>\n</table>\n\n\n[npm]: https://img.shields.io/npm/v/html-webpack-plugin.svg\n[npm-url]: https://npmjs.com/package/html-webpack-plugin\n\n[node]: https://img.shields.io/node/v/html-webpack-plugin.svg\n[node-url]: https://nodejs.org\n\n[deps]: https://david-dm.org/jantimon/html-webpack-plugin.svg\n[deps-url]: https://david-dm.org/jantimon/html-webpack-plugin\n\n[tests]: http://img.shields.io/travis/jantimon/html-webpack-plugin.svg\n[tests-url]: https://travis-ci.org/jantimon/html-webpack-plugin\n\n\n## Contributors\n\nThis project exists thanks to all the people who contribute.\n\nYou're free to contribute to this project by submitting [issues](https://github.com/jantimon/html-webpack-plugin/issues) and/or [pull requests](https://github.com/jantimon/html-webpack-plugin/pulls). This project is test-driven, so keep in mind that every change and new feature should be covered by tests.\n\nThis project uses the [semistandard code style](https://github.com/Flet/semistandard).\n\n<a href=\"https://github.com/jantimon/html-webpack-plugin/graphs/contributors\"><img src=\"https://opencollective.com/html-webpack-plugin/contributors.svg?width=890&button=false\" /></a>\n\n\n## Backers\n\nThank you to all our backers! 🙏 [Become a backer](https://opencollective.com/html-webpack-plugin#backer)\n\n<a href=\"https://opencollective.com/html-webpack-plugin/backer/0/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/backer/0/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/backer/1/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/backer/1/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/backer/2/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/backer/2/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/backer/3/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/backer/3/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/backer/4/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/backer/4/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/backer/5/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/backer/5/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/backer/6/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/backer/6/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/backer/7/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/backer/7/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/backer/8/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/backer/8/avatar.svg?requireActive=false\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/backer/9/website?requireActive=false\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/backer/9/avatar.svg?requireActive=false\"></a>\n\n## Sponsors\n\nSupport this project by becoming a sponsor. Your logo will show up here with a link to your website. [Become a sponsor](https://opencollective.com/html-webpack-plugin#sponsor)\n\n<a href=\"https://opencollective.com/html-webpack-plugin/sponsor/0/website\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/sponsor/0/avatar.svg\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/sponsor/1/website\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/sponsor/1/avatar.svg\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/sponsor/2/website\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/sponsor/2/avatar.svg\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/sponsor/3/website\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/sponsor/3/avatar.svg\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/sponsor/4/website\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/sponsor/4/avatar.svg\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/sponsor/5/website\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/sponsor/5/avatar.svg\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/sponsor/6/website\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/sponsor/6/avatar.svg\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/sponsor/7/website\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/sponsor/7/avatar.svg\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/sponsor/8/website\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/sponsor/8/avatar.svg\"></a>\n<a href=\"https://opencollective.com/html-webpack-plugin/sponsor/9/website\" target=\"_blank\"><img src=\"https://opencollective.com/html-webpack-plugin/sponsor/9/avatar.svg\"></a>\n\n\n", "licenseText": "Copyright JS Foundation and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/html-webpack-plugin/-/html-webpack-plugin-3.2.0.tgz#b01abbd723acaaa7b37b6af4492ebda03d9dd37b", "type": "tarball", "reference": "https://registry.yarnpkg.com/html-webpack-plugin/-/html-webpack-plugin-3.2.0.tgz", "hash": "b01abbd723acaaa7b37b6af4492ebda03d9dd37b", "integrity": "sha1-sBq71yOsqqeze2r0SS69oD2d03s=", "registry": "npm", "packageName": "html-webpack-plugin", "cacheIntegrity": "sha512-Br4ifmjQojUP4EmHnRBoUIYcZ9J7M4bTMcm7u6xoIAIuq2Nte4TzXX0533owvkQKQD1WeMTTTyD4Ni4QKxS0Bg== sha1-sBq71yOsqqeze2r0SS69oD2d03s="}, "registry": "npm", "hash": "b01abbd723acaaa7b37b6af4492ebda03d9dd37b"}