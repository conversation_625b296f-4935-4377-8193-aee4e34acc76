{"manifest": {"name": "make-dir", "version": "2.1.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/make-dir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && nyc ava && tsd-check"}, "files": ["index.js", "index.d.ts"], "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"pify": "^4.0.1", "semver": "^5.6.0"}, "devDependencies": {"@types/graceful-fs": "^4.1.3", "@types/node": "^11.10.4", "ava": "^1.2.0", "codecov": "^3.0.0", "graceful-fs": "^4.1.11", "nyc": "^13.1.0", "path-type": "^3.0.0", "tempy": "^0.2.1", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-make-dir-2.1.0-5f0310e18b8be898cc07009295a30ae41e91e6f5-integrity\\node_modules\\make-dir\\package.json", "readmeFilename": "readme.md", "readme": "# make-dir [![Build Status](https://travis-ci.org/sindresorhus/make-dir.svg?branch=master)](https://travis-ci.org/sindresorhus/make-dir) [![codecov](https://codecov.io/gh/sindresorhus/make-dir/branch/master/graph/badge.svg)](https://codecov.io/gh/sindresorhus/make-dir)\n\n> Make a directory and its parents if needed - Think `mkdir -p`\n\n\n## Advantages over [`mkdirp`](https://github.com/substack/node-mkdirp)\n\n- Promise API *(Async/await ready!)*\n- Fixes many `mkdirp` issues: [#96](https://github.com/substack/node-mkdirp/pull/96) [#70](https://github.com/substack/node-mkdirp/issues/70) [#66](https://github.com/substack/node-mkdirp/issues/66)\n- 100% test coverage\n- CI-tested on macOS, Linux, and Windows\n- Actively maintained\n- Doesn't bundle a CLI\n- Uses native the `fs.mkdir/mkdirSync` [`recursive` option](https://nodejs.org/dist/latest/docs/api/fs.html#fs_fs_mkdir_path_options_callback) in Node.js >=10.12.0 unless [overridden](#fs)\n\n\n## Install\n\n```\n$ npm install make-dir\n```\n\n\n## Usage\n\n```\n$ pwd\n/Users/<USER>/fun\n$ tree\n.\n```\n\n```js\nconst makeDir = require('make-dir');\n\n(async () => {\n\tconst path = await makeDir('unicorn/rainbow/cake');\n\n\tconsole.log(path);\n\t//=> '/Users/<USER>/fun/unicorn/rainbow/cake'\n})();\n```\n\n```\n$ tree\n.\n└── unicorn\n    └── rainbow\n        └── cake\n```\n\nMultiple directories:\n\n```js\nconst makeDir = require('make-dir');\n\n(async () => {\n\tconst paths = await Promise.all([\n\t\tmakeDir('unicorn/rainbow'),\n\t\tmakeDir('foo/bar')\n\t]);\n\n\tconsole.log(paths);\n\t/*\n\t[\n\t\t'/Users/<USER>/fun/unicorn/rainbow',\n\t\t'/Users/<USER>/fun/foo/bar'\n\t]\n\t*/\n})();\n```\n\n\n## API\n\n### makeDir(path, [options])\n\nReturns a `Promise` for the path to the created directory.\n\n### makeDir.sync(path, [options])\n\nReturns the path to the created directory.\n\n#### path\n\nType: `string`\n\nDirectory to create.\n\n#### options\n\nType: `Object`\n\n##### mode\n\nType: `integer`<br>\nDefault: `0o777 & (~process.umask())`\n\nDirectory [permissions](https://x-team.com/blog/file-system-permissions-umask-node-js/).\n\n##### fs\n\nType: `Object`<br>\nDefault: `require('fs')`\n\nUse a custom `fs` implementation. For example [`graceful-fs`](https://github.com/isaacs/node-graceful-fs).\n\nUsing a custom `fs` implementation will block the use of the native `recursive` option if `fs.mkdir` or `fs.mkdirSync` is not the native function.\n\n\n## Related\n\n- [make-dir-cli](https://github.com/sindresorhus/make-dir-cli) - CLI for this module\n- [del](https://github.com/sindresorhus/del) - Delete files and directories\n- [globby](https://github.com/sindresorhus/globby) - User-friendly glob matching\n- [cpy](https://github.com/sindresorhus/cpy) - Copy files\n- [cpy-cli](https://github.com/sindresorhus/cpy-cli) - Copy files on the command-line\n- [move-file](https://github.com/sindresorhus/move-file) - Move a file\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com)\n", "licenseText": "MIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/make-dir/-/make-dir-2.1.0.tgz#5f0310e18b8be898cc07009295a30ae41e91e6f5", "type": "tarball", "reference": "https://registry.yarnpkg.com/make-dir/-/make-dir-2.1.0.tgz", "hash": "5f0310e18b8be898cc07009295a30ae41e91e6f5", "integrity": "sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==", "registry": "npm", "packageName": "make-dir", "cacheIntegrity": "sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA== sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU="}, "registry": "npm", "hash": "5f0310e18b8be898cc07009295a30ae41e91e6f5"}