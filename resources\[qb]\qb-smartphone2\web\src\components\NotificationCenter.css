/* Notification Banner */
.notification-banner {
  position: absolute;
  top: 50px;
  left: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 200;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.notification-banner:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.notification-banner:active {
  transform: translateY(0);
}

.notification-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.notification-message {
  font-size: 12px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.notification-time {
  font-size: 11px;
  flex-shrink: 0;
  margin-left: 8px;
}

.notification-close {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.notification-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Notification Center */
.notification-center {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 300;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.notification-center-header {
  padding: 60px 20px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.notification-center-header h2 {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
}

.notification-center-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.clear-all-btn {
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.clear-all-btn:hover {
  background: rgba(0, 122, 255, 0.1);
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Notification List */
.notification-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px 20px;
}

.no-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
}

.no-notifications i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-notifications p {
  font-size: 16px;
  margin: 0;
  opacity: 0.7;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.notification-item:hover {
  background: rgba(255, 255, 255, 0.05) !important;
}

.notification-item.unread {
  border-left: 3px solid #007AFF;
}

.notification-item-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.notification-item-content {
  flex: 1;
  min-width: 0;
}

.notification-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 4px;
  gap: 8px;
}

.notification-item-title {
  font-size: 15px;
  font-weight: 600;
  line-height: 1.2;
  flex: 1;
  min-width: 0;
}

.notification-item-time {
  font-size: 12px;
  flex-shrink: 0;
  line-height: 1.2;
}

.notification-item-message {
  font-size: 14px;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-item-remove {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  flex-shrink: 0;
  margin-left: 8px;
}

.notification-item-remove:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Pull down indicator */
.notification-pull-indicator {
  position: absolute;
  top: 35px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 4px;
  border-radius: 2px;
  opacity: 0.3;
  cursor: pointer;
  z-index: 100;
  transition: all 0.2s ease;
}

.notification-pull-indicator:hover {
  opacity: 0.6;
  transform: translateX(-50%) scale(1.1);
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .notification-banner {
    top: 45px;
    left: 8px;
    right: 8px;
    padding: 10px 12px;
  }
  
  .notification-icon {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .notification-title {
    font-size: 13px;
  }
  
  .notification-message {
    font-size: 11px;
  }
  
  .notification-time {
    font-size: 10px;
  }
  
  .notification-center-header {
    padding: 50px 16px 16px;
  }
  
  .notification-center-header h2 {
    font-size: 20px;
  }
  
  .notification-list {
    padding: 0 16px 16px;
  }
  
  .notification-item {
    padding: 12px;
  }
  
  .notification-item-icon {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }
  
  .notification-item-title {
    font-size: 14px;
  }
  
  .notification-item-message {
    font-size: 13px;
  }
}
