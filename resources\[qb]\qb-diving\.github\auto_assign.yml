# Set to true to add reviewers to pull requests
addReviewers: true

# Set to true to add assignees to pull requests
addAssignees: author

# A list of reviewers to be added to pull requests (GitHub user name)
reviewers:
  - /maintenance

# A list of keywords to be skipped the process that add reviewers if pull requests include it
skipKeywords:
  - wip

# A number of reviewers added to the pull request
# Set 0 to add all the reviewers (default: 0)
numberOfReviewers: 0