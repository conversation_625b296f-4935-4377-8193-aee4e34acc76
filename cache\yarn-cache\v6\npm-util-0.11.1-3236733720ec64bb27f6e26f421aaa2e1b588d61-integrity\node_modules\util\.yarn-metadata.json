{"manifest": {"author": {"name": "<PERSON><PERSON>", "url": "http://www.joyent.com"}, "name": "util", "description": "Node.JS util module", "keywords": ["util"], "version": "0.11.1", "homepage": "https://github.com/defunctzombie/node-util", "repository": {"type": "git", "url": "git://github.com/defunctzombie/node-util"}, "main": "./util.js", "files": ["util.js", "support"], "scripts": {"test": "node test/node/index.js", "test:browsers": "airtap test/browser/index.js"}, "dependencies": {"inherits": "2.0.3"}, "license": "MIT", "devDependencies": {"airtap": "~0.1.0", "is-async-supported": "~1.2.0", "run-series": "~1.1.4", "tape": "~4.9.0"}, "browser": {"./support/isBuffer.js": "./support/isBufferBrowser.js"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-util-0.11.1-**********ec64bb27f6e26f421aaa2e1b588d61-integrity\\node_modules\\util\\package.json", "readmeFilename": "README.md", "readme": "# util [![Build Status](https://travis-ci.org/defunctzombie/node-util.png?branch=master)](https://travis-ci.org/defunctzombie/node-util)\n\n> Node.js's [util][util] module for all engines.\n\nThis implements the Node.js [`util`][util] module for environments that do not have it, like browsers.\n\n## Install\n\nYou usually do not have to install `util` yourself. If your code runs in Node.js, `util` is built in. If your code runs in the browser, bundlers like [browserify](https://github.com/browserify/browserify) or [webpack](https://github.com/webpack/webpack) also include the `util` module.\n\nBut if none of those apply, with npm do:\n\n```shell\nnpm install util\n```\n\n## Usage\n\n```javascript\nvar util = require('util')\nvar EventEmitter = require('events')\n\nfunction MyClass() { EventEmitter.call(this) }\nutil.inherits(MyClass, EventEmitter)\n```\n\n## Browser Support\n\nThe `util` module uses ES5 features. If you need to support very old browsers like IE8, use a shim like [`es5-shim`](https://www.npmjs.com/package/es5-shim). You need both the shim and the sham versions of `es5-shim`.\n\nTo use `util.promisify` and `util.callbackify`, Promises must already be available. If you need to support browsers like IE11 that do not support Promises, use a shim. [es6-promise](https://github.com/stefanpenner/es6-promise) is a popular one but there are many others available on npm.\n\n## API\n\nSee the [Node.js util docs][util].  `util` currently supports the Node 8 LTS API. However, some of the methods are outdated. The `inspect` and `format` methods included in this module are a lot more simple and barebones than the ones in Node.js.\n\n## Contributing\n\nPRs are very welcome! The main way to contribute to `util` is by porting features, bugfixes and tests from Node.js. Ideally, code contributions to this module are copy-pasted from Node.js and transpiled to ES5, rather than reimplemented from scratch. Matching the Node.js code as closely as possible makes maintenance simpler when new changes land in Node.js.\nThis module intends to provide exactly the same API as Node.js, so features that are not available in the core `util` module will not be accepted. Feature requests should instead be directed at [nodejs/node](https://github.com/nodejs/node) and will be added to this module once they are implemented in Node.js.\n\nIf there is a difference in behaviour between Node.js's `util` module and this module, please open an issue!\n\n## License\n\n[MIT](./LICENSE)\n\n[util]: https://nodejs.org/docs/latest-v8.x/api/util.html\n", "licenseText": "Copyright Joyent, Inc. and other Node contributors. All rights reserved.\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/util/-/util-0.11.1.tgz#**********ec64bb27f6e26f421aaa2e1b588d61", "type": "tarball", "reference": "https://registry.yarnpkg.com/util/-/util-0.11.1.tgz", "hash": "**********ec64bb27f6e26f421aaa2e1b588d61", "integrity": "sha512-HShAsny+zS2TZfaXxD9tYj4HQGlBezXZMZuM/S5PKLLoZkShZiGk9o5CzukI1LVHZvjdvZ2Sj1aW/Ndn2NB/HQ==", "registry": "npm", "packageName": "util", "cacheIntegrity": "sha512-HShAsny+zS2TZfaXxD9tYj4HQGlBezXZMZuM/S5PKLLoZkShZiGk9o5CzukI1LVHZvjdvZ2Sj1aW/Ndn2NB/HQ== sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE="}, "registry": "npm", "hash": "**********ec64bb27f6e26f421aaa2e1b588d61"}