{"name": "koa-router", "description": "Router middleware for koa. Provides RESTful resource routing.", "repository": {"type": "git", "url": "https://github.com/alexmingoia/koa-router.git"}, "main": "lib/router.js", "files": ["lib"], "author": "<PERSON> <<EMAIL>>", "version": "7.4.0", "keywords": ["koa", "middleware", "router", "route"], "dependencies": {"debug": "^3.1.0", "http-errors": "^1.3.1", "koa-compose": "^3.0.0", "methods": "^1.0.1", "path-to-regexp": "^1.1.1", "urijs": "^1.19.0"}, "devDependencies": {"expect.js": "^0.3.1", "gulp": "^3.8.11", "jsdoc-to-markdown": "^1.1.1", "koa": "2.2.0", "mocha": "^2.0.1", "should": "^6.0.3", "supertest": "^1.0.1"}, "scripts": {"test": "NODE_ENV=test mocha test/**/*.js", "docs": "NODE_ENV=test node node_modules/gulp/bin/gulp.js docs"}, "engines": {"node": ">= 4"}, "license": "MIT"}