{"manifest": {"name": "ajv-errors", "version": "1.0.1", "description": "Custom error messages in JSON-Schema for Ajv validator", "main": "index.js", "files": ["lib"], "scripts": {"build": "node node_modules/ajv/scripts/compile-dots.js node_modules/ajv/lib lib", "eslint": "eslint *.js spec", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "nyc npm run test-spec", "test": "npm run eslint && npm run build && npm run test-cov", "prepublish": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-errors.git"}, "keywords": ["ajv", "json-schema", "validator", "error", "messages"], "author": {}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-errors/issues"}, "homepage": "https://github.com/epoberezkin/ajv-errors#readme", "peerDependencies": {"ajv": ">=5.0.0"}, "devDependencies": {"ajv": "^5.0.0", "coveralls": "^2.11.16", "dot": "^1.1.1", "eslint": "^3.17.0", "glob": "^7.1.1", "js-beautify": "^1.6.12", "mocha": "^3.2.0", "nyc": "^10.1.2", "pre-commit": "^1.2.2"}, "nyc": {"exclude": ["**/spec/**", "node_modules"], "reporter": ["lcov", "text-summary"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-ajv-errors-1.0.1-f35986aceb91afadec4102fbd85014950cefa64d-integrity\\node_modules\\ajv-errors\\package.json", "readmeFilename": "README.md", "readme": "# ajv-errors\nCustom error messages in JSON-Schema for Ajv validator\n\n[![Build Status](https://travis-ci.org/epoberezkin/ajv-errors.svg?branch=master)](https://travis-ci.org/epoberezkin/ajv-errors)\n[![npm version](https://badge.fury.io/js/ajv-errors.svg)](http://badge.fury.io/js/ajv-errors)\n[![Coverage Status](https://coveralls.io/repos/github/epoberezkin/ajv-errors/badge.svg?branch=master)](https://coveralls.io/github/epoberezkin/ajv-errors?branch=master)\n[![Gitter](https://img.shields.io/gitter/room/ajv-validator/ajv.svg)](https://gitter.im/ajv-validator/ajv)\n\n\n## Contents\n\n- [Install](#install)\n- [Usage](#usage)\n  - [Single message](#single-message)\n  - [Messages for keywords](#messages-for-keywords)\n  - [Messages for properties and items](#messages-for-properties-and-items)\n  - [Default message](#default-message)\n- [Templates](#templates)\n- [Options](#options)\n- [License](#license)\n\n\n## Install\n\n```\nnpm install ajv-errors\n```\n\n\n## Usage\n\nAdd the keyword `errorMessages` to Ajv instance:\n\n```javascript\nvar Ajv = require('ajv');\nvar ajv = new Ajv({allErrors: true, jsonPointers: true});\n// Ajv options allErrors and jsonPointers are required\nrequire('ajv-errors')(ajv /*, {singleError: true} */);\n```\n\nSee [Options](#options) below.\n\n\n### Single message\n\nReplace all errors in the current schema and subschemas with a single message:\n\n```javascript\nvar schema = {\n  type: 'object',\n  required: ['foo'],\n  properties: {\n    foo: { type: 'integer' }\n  },\n  additionalProperties: false,\n  errorMessage: 'should be an object with an integer property foo only'\n};\n\nvar validate = ajv.compile(schema);\nconsole.log(validate({foo: 'a', bar: 2})); // false\nconsole.log(validate.errors); // processed errors\n```\n\nProcessed errors:\n\n```javascript\n[\n  {\n    keyword: 'errorMessage',\n    message: 'should be an object with an integer property foo only',\n    // ...\n    params: {\n      errors: [\n        { keyword: 'additionalProperties', dataPath: '' /* , ... */ },\n        { keyword: 'type', dataPath: '.foo' /* , ... */ }\n      ]\n    }\n  }\n]\n```\n\n\n### Messages for keywords\n\nReplace errors for certain keywords in the current schema only:\n\n```javascript\nvar schema = {\n  type: 'object',\n  required: ['foo'],\n  properties: {\n    foo: { type: 'integer' }\n  },\n  additionalProperties: false,\n  errorMessage: {\n    type: 'should be an object', // will not replace internal \"type\" error for the property \"foo\"\n    required: 'should have property foo',\n    additionalProperties: 'should not have properties other than foo'\n  }\n};\n\nvar validate = ajv.compile(schema);\nconsole.log(validate({foo: 'a', bar: 2})); // false\nconsole.log(validate.errors); // processed errors\n```\n\nProcessed errors:\n\n```javascript\n[\n  {\n    // original error\n    keyword: type,\n    dataPath: '/foo',\n    // ...\n    message: 'should be integer'\n  },\n  {\n    // generated error\n    keyword: 'errorMessage',\n    message: 'should not have properties other than foo',\n    // ...\n    params: {\n      errors: [\n        { keyword: 'additionalProperties' /* , ... */ }\n      ]\n    },\n  }\n]\n```\n\nFor keywords \"required\" and \"dependencies\" it is possible to specify different messages for different properties:\n\n```javascript\nvar schema = {\n  type: 'object',\n  required: ['foo', 'bar'],\n  properties: {\n    foo: { type: 'integer' },\n    bar: { type: 'string' }\n  },\n  errorMessage: {\n    type: 'should be an object', // will not replace internal \"type\" error for the property \"foo\"\n    required: {\n      foo: 'should have an integer property \"foo\"',\n      bar: 'should have a string property \"bar\"'\n    }\n  }\n};\n```\n\n\n### Messages for properties and items\n\nReplace errors for properties / items (and deeper), regardless where in schema they were created:\n\n```javascript\nvar schema = {\n  type: 'object',\n  required: ['foo', 'bar'],\n  allOf: [{\n    properties: {\n      foo: { type: 'integer', minimum: 2 },\n      bar: { type: 'string', minLength: 2 }\n    },\n    additionalProperties: false\n  }],\n  errorMessage: {\n    properties: {\n      foo: 'data.foo should be integer >= 2',\n      bar: 'data.bar should be string with length >= 2'\n    }\n  }\n};\n\nvar validate = ajv.compile(schema);\nconsole.log(validate({foo: 1, bar: 'a'})); // false\nconsole.log(validate.errors); // processed errors\n```\n\nProcessed errors:\n\n```javascript\n[\n  {\n    keyword: 'errorMessage',\n    message: 'data.foo should be integer >= 2',\n    dataPath: '/foo',\n    // ...\n    params: {\n      errors: [\n        { keyword: 'minimum' /* , ... */ }\n      ]\n    },\n  },\n  {\n    keyword: 'errorMessage',\n    message: 'data.bar should be string with length >= 2',\n    dataPath: '/bar',\n    // ...\n    params: {\n      errors: [\n        { keyword: 'minLength' /* , ... */ }\n      ]\n    },\n  }\n]\n```\n\n\n### Default message\n\nWhen the value of keyword `errorMessage` is an object you can specify a message that will be used if any error appears that is not specified by keywords/properties/items:\n\n```javascript\nvar schema = {\n  type: 'object',\n  required: ['foo', 'bar'],\n  allOf: [{\n    properties: {\n      foo: { type: 'integer', minimum: 2 },\n      bar: { type: 'string', minLength: 2 }\n    },\n    additionalProperties: false\n  }],\n  errorMessage: {\n    type: 'data should be an object',\n    properties: {\n      foo: 'data.foo should be integer >= 2',\n      bar: 'data.bar should be string with length >= 2'\n    },\n    _: 'data should have properties \"foo\" and \"bar\" only'\n  }\n};\n\nvar validate = ajv.compile(schema);\nconsole.log(validate({})); // false\nconsole.log(validate.errors); // processed errors\n```\n\nProcessed errors:\n\n```javascript\n[\n  {\n    keyword: 'errorMessage',\n    message: 'data should be an object with properties \"foo\" and \"bar\" only',\n    dataPath: '',\n    // ...\n    params: {\n      errors: [\n        { keyword: 'required' /* , ... */ },\n        { keyword: 'required' /* , ... */ }\n      ]\n    },\n  }\n]\n```\n\nThe message in property `_` of `errorMessage` replaces the same errors that would have been replaced if `errorMessage` were a string.\n\n\n## Templates\n\nCustom error messages used in `errorMessage` keyword can be templates using [JSON-pointers](https://tools.ietf.org/html/rfc6901) or [relative JSON-pointers](http://tools.ietf.org/html/draft-luff-relative-json-pointer-00) to data being validated, in which case the value will be interpolated. Also see [examples](https://gist.github.com/geraintluff/5911303) of relative JSON-pointers.\n\nThe syntax to interpolate a value is `${<pointer>}`.\n\nThe values used in messages will be JSON-stringified:\n- to differentiate between `false` and `\"false\"`, etc.\n- to support structured values.\n\nExample:\n\n```json\n{\n  \"type\": \"object\",\n  \"properties\": {\n    \"size\": {\n      \"type\": \"number\",\n      \"minimum\": 4\n    }\n  },\n  \"errorMessage\": {\n    \"properties\": {\n      \"size\": \"size should be a number bigger or equal to 4, current value is ${/size}\"\n    }\n  }\n}\n```\n\n\n## Options\n\nDefaults:\n\n```javascript\n{\n  keepErrors: false,\n  singleError: false\n}\n```\n\n- _keepErrors_: keep original errors. Default is to remove matched errors (they will still be available in `params.errors` property of generated error). If an error was matched and included in the error generated by `errorMessage` keyword it will have property `emUsed: true`.\n- _singleError_: create one error for all keywords used in `errorMessage` keyword (error messages defined for properties and items are not merged because they have different dataPaths). Multiple error messages are concatenated. Option values:\n  - `false` (default): create multiple errors, one for each message\n  - `true`: create single error, messages are concatenated using `\"; \"`\n  - non-empty string: this string is used as a separator to concatenate messages\n\n\n## Supporters\n\n[<img src=\"https://media.licdn.com/mpr/mpr/shrinknp_400_400/AAEAAQAAAAAAAAwEAAAAJDg1YzBlYzFjLTA3YWYtNGEzOS1iMTdjLTQ0MTU1NWZjOGM0ZQ.jpg\" width=\"48\" height=\"48\">](https://www.linkedin.com/in/rogerkepler/) [Roger Kepler](https://www.linkedin.com/in/rogerkepler/)\n\n\n## License\n\n[MIT](https://github.com/epoberezkin/ajv-errors/blob/master/LICENSE)\n", "licenseText": "MIT License\n\nCopyright (c) 2017 <PERSON><PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/ajv-errors/-/ajv-errors-1.0.1.tgz#f35986aceb91afadec4102fbd85014950cefa64d", "type": "tarball", "reference": "https://registry.yarnpkg.com/ajv-errors/-/ajv-errors-1.0.1.tgz", "hash": "f35986aceb91afadec4102fbd85014950cefa64d", "integrity": "sha512-DCRfO/4nQ+89p/RK43i8Ezd41EqdGIU4ld7nGF8OQ14oc/we5rEntLCUa7+jrn3nn83BosfwZA0wb4pon2o8iQ==", "registry": "npm", "packageName": "ajv-errors", "cacheIntegrity": "sha512-DCRfO/4nQ+89p/RK43i8Ezd41EqdGIU4ld7nGF8OQ14oc/we5rEntLCUa7+jrn3nn83BosfwZA0wb4pon2o8iQ== sha1-81mGrOuRr63sQQL72FAUlQzvpk0="}, "registry": "npm", "hash": "f35986aceb91afadec4102fbd85014950cefa64d"}