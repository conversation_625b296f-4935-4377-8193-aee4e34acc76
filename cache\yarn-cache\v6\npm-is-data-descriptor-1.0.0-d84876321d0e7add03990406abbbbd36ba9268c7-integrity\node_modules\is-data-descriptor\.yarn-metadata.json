{"manifest": {"name": "is-data-descriptor", "description": "Returns true if a value has the characteristics of a valid JavaScript data descriptor.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/is-data-descriptor", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-data-descriptor.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-data-descriptor/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"kind-of": "^6.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["accessor", "check", "data", "descriptor", "get", "getter", "is", "keys", "object", "properties", "property", "set", "setter", "type", "valid", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-accessor-descriptor", "is-data-descriptor", "is-descriptor", "isobject"]}, "lint": {"reflinks": true}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-is-data-descriptor-1.0.0-d84876321d0e7add03990406abbbbd36ba9268c7-integrity\\node_modules\\is-data-descriptor\\package.json", "readmeFilename": "README.md", "readme": "# is-data-descriptor [![NPM version](https://img.shields.io/npm/v/is-data-descriptor.svg?style=flat)](https://www.npmjs.com/package/is-data-descriptor) [![NPM monthly downloads](https://img.shields.io/npm/dm/is-data-descriptor.svg?style=flat)](https://npmjs.org/package/is-data-descriptor) [![NPM total downloads](https://img.shields.io/npm/dt/is-data-descriptor.svg?style=flat)](https://npmjs.org/package/is-data-descriptor) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/is-data-descriptor.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/is-data-descriptor)\n\n> Returns true if a value has the characteristics of a valid JavaScript data descriptor.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save is-data-descriptor\n```\n\n## Usage\n\n```js\nvar isDataDesc = require('is-data-descriptor');\n```\n\n## Examples\n\n`true` when the descriptor has valid properties with valid values.\n\n```js\n// `value` can be anything\nisDataDesc({value: 'foo'})\nisDataDesc({value: function() {}})\nisDataDesc({value: true})\n//=> true\n```\n\n`false` when not an object\n\n```js\nisDataDesc('a')\n//=> false\nisDataDesc(null)\n//=> false\nisDataDesc([])\n//=> false\n```\n\n`false` when the object has invalid properties\n\n```js\nisDataDesc({value: 'foo', bar: 'baz'})\n//=> false\nisDataDesc({value: 'foo', bar: 'baz'})\n//=> false\nisDataDesc({value: 'foo', get: function(){}})\n//=> false\nisDataDesc({get: function(){}, value: 'foo'})\n//=> false\n```\n\n`false` when a value is not the correct type\n\n```js\nisDataDesc({value: 'foo', enumerable: 'foo'})\n//=> false\nisDataDesc({value: 'foo', configurable: 'foo'})\n//=> false\nisDataDesc({value: 'foo', writable: 'foo'})\n//=> false\n```\n\n## Valid properties\n\nThe only valid data descriptor properties are the following:\n\n* `configurable` (required)\n* `enumerable` (required)\n* `value` (optional)\n* `writable` (optional)\n\nTo be a valid data descriptor, either `value` or `writable` must be defined.\n\n**Invalid properties**\n\nA descriptor may have additional _invalid_ properties (an error will **not** be thrown).\n\n```js\nvar foo = {};\n\nObject.defineProperty(foo, 'bar', {\n  enumerable: true,\n  whatever: 'blah', // invalid, but doesn't cause an error\n  get: function() {\n    return 'baz';\n  }\n});\n\nconsole.log(foo.bar);\n//=> 'baz'\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [is-accessor-descriptor](https://www.npmjs.com/package/is-accessor-descriptor): Returns true if a value has the characteristics of a valid JavaScript accessor descriptor. | [homepage](https://github.com/jonschlinkert/is-accessor-descriptor \"Returns true if a value has the characteristics of a valid JavaScript accessor descriptor.\")\n* [is-data-descriptor](https://www.npmjs.com/package/is-data-descriptor): Returns true if a value has the characteristics of a valid JavaScript data descriptor. | [homepage](https://github.com/jonschlinkert/is-data-descriptor \"Returns true if a value has the characteristics of a valid JavaScript data descriptor.\")\n* [is-descriptor](https://www.npmjs.com/package/is-descriptor): Returns true if a value has the characteristics of a valid JavaScript descriptor. Works for… [more](https://github.com/jonschlinkert/is-descriptor) | [homepage](https://github.com/jonschlinkert/is-descriptor \"Returns true if a value has the characteristics of a valid JavaScript descriptor. Works for data descriptors and accessor descriptors.\")\n* [isobject](https://www.npmjs.com/package/isobject): Returns true if the value is an object and not an array or null. | [homepage](https://github.com/jonschlinkert/isobject \"Returns true if the value is an object and not an array or null.\")\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 21 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 2 | [realityking](https://github.com/realityking) |\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on November 01, 2017._", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015-2017, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz#d84876321d0e7add03990406abbbbd36ba9268c7", "type": "tarball", "reference": "https://registry.yarnpkg.com/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "hash": "d84876321d0e7add03990406abbbbd36ba9268c7", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "registry": "npm", "packageName": "is-data-descriptor", "cacheIntegrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ== sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc="}, "registry": "npm", "hash": "d84876321d0e7add03990406abbbbd36ba9268c7"}