-- ================================================
-- DDCZ-VEHICLE Client Script
-- ================================================

local QBCore = exports['qb-core']:GetCoreObject()
local PlayerData = {}
local isMenuOpen = false
local currentVehicle = nil
local vehicleData = {}

-- ================================================
-- Events
-- ================================================

RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    PlayerData = QBCore.Functions.GetPlayerData()
end)

RegisterNetEvent('QBCore:Client:OnPlayerUnload', function()
    PlayerData = {}
end)

RegisterNetEvent('QBCore:Client:OnJobUpdate', function(JobInfo)
    PlayerData.job = JobInfo
end)

-- ================================================
-- Commands
-- ================================================

RegisterCommand('vehicle', function()
    toggleVehicleMenu()
end, false)

RegisterCommand('vehiclemenu', function()
    toggleVehicleMenu()
end, false)

-- ================================================
-- Key Mappings
-- ================================================

RegisterKeyMapping('vehicle', 'Otevřít Vehicle Menu', 'keyboard', Config.DefaultKeybind)

-- ================================================
-- Main Functions
-- ================================================

function toggleVehicleMenu()
    if isMenuOpen then
        closeVehicleMenu()
    else
        openVehicleMenu()
    end
end

function openVehicleMenu()
    local ped = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(ped, false)
    
    -- Check if player is in vehicle
    if vehicle == 0 then
        -- Check if player is near a vehicle
        vehicle = GetClosestVehicle(GetEntityCoords(ped), Config.MaxDistance, 0, 71)
        
        if vehicle == 0 or #(GetEntityCoords(ped) - GetEntityCoords(vehicle)) > Config.MaxDistance then
            QBCore.Functions.Notify(Config.Messages.no_vehicle, 'error', 3000)
            return
        end
    end
    
    -- Check if vehicle is blacklisted
    local vehicleModel = GetEntityModel(vehicle)
    for _, blacklisted in pairs(Config.BlacklistedVehicles) do
        if vehicleModel == blacklisted then
            QBCore.Functions.Notify('Toto vozidlo nelze ovládat!', 'error', 3000)
            return
        end
    end
    
    currentVehicle = vehicle
    vehicleData = getVehicleData(vehicle)
    
    if Config.Debug then
        print('[DDCZ-VEHICLE] Opening menu for vehicle: ' .. vehicleData.model)
    end
    
    isMenuOpen = true
    SetNuiFocus(true, true)
    SendNUIMessage({
        type = 'openMenu',
        vehicleData = vehicleData
    })
end

function closeVehicleMenu()
    if not isMenuOpen then return end
    
    isMenuOpen = false
    SetNuiFocus(false, false)
    SendNUIMessage({
        type = 'closeMenu'
    })
    
    if Config.Debug then
        print('[DDCZ-VEHICLE] Menu closed')
    end
end

function getVehicleData(vehicle)
    local model = GetEntityModel(vehicle)
    local displayName = GetDisplayNameFromVehicleModel(model)
    local plate = GetVehicleNumberPlateText(vehicle)
    local engineHealth = GetVehicleEngineHealth(vehicle)
    local engineOn = GetIsVehicleEngineRunning(vehicle)
    
    -- Check if player has restricted access (police/EMS)
    local hasRestrictedAccess = false
    if PlayerData and PlayerData.job then
        hasRestrictedAccess = Config.HasRestrictedAccess(GetPlayerServerId(PlayerId()))
    end
    
    return {
        model = displayName,
        plate = plate,
        engineHealth = engineHealth,
        engineOn = engineOn,
        hasRestrictedAccess = hasRestrictedAccess,
        displayName = displayName
    }
end

-- ================================================
-- Vehicle Control Functions
-- ================================================

function toggleVehicleDoor(vehicle, doorId, forceOpen)
    if not DoesEntityExist(vehicle) then return end
    
    local doorAngle = GetVehicleDoorAngleRatio(vehicle, doorId)
    
    if forceOpen ~= nil then
        if forceOpen and doorAngle < 0.1 then
            SetVehicleDoorOpen(vehicle, doorId, false, false)
        elseif not forceOpen and doorAngle > 0.1 then
            SetVehicleDoorShut(vehicle, doorId, false)
        end
    else
        if doorAngle > 0.1 then
            SetVehicleDoorShut(vehicle, doorId, false)
        else
            SetVehicleDoorOpen(vehicle, doorId, false, false)
        end
    end
    
    -- Sync with server
    if Config.SyncVehicleChanges then
        TriggerServerEvent('ddcz-vehicle:server:syncDoor', VehToNet(vehicle), doorId, doorAngle < 0.1)
    end
end

function toggleVehicleEngine(vehicle)
    if not DoesEntityExist(vehicle) then return end
    
    local engineOn = GetIsVehicleEngineRunning(vehicle)
    SetVehicleEngineOn(vehicle, not engineOn, false, true)
    
    -- Update NUI
    SendNUIMessage({
        type = 'updateVehicleData',
        vehicleData = getVehicleData(vehicle)
    })
    
    -- Notification
    if engineOn then
        QBCore.Functions.Notify(Config.Messages.engine_off, 'primary', 2000)
    else
        QBCore.Functions.Notify(Config.Messages.engine_on, 'success', 2000)
    end
    
    -- Sync with server
    if Config.SyncVehicleChanges then
        TriggerServerEvent('ddcz-vehicle:server:syncEngine', VehToNet(vehicle), not engineOn)
    end
end

function toggleVehicleLocks(vehicle)
    if not DoesEntityExist(vehicle) then return end
    
    local lockStatus = GetVehicleDoorLockStatus(vehicle)
    
    if lockStatus == 1 then -- Unlocked
        SetVehicleDoorsLocked(vehicle, 2) -- Locked
        QBCore.Functions.Notify('Vozidlo zamčeno', 'primary', 2000)
    else -- Locked
        SetVehicleDoorsLocked(vehicle, 1) -- Unlocked
        QBCore.Functions.Notify('Vozidlo odemčeno', 'success', 2000)
    end
    
    -- Sync with server
    if Config.SyncVehicleChanges then
        TriggerServerEvent('ddcz-vehicle:server:syncLocks', VehToNet(vehicle), lockStatus == 1)
    end
end

function toggleAllWindows(vehicle)
    if not DoesEntityExist(vehicle) then return end
    
    -- Check if any window is up
    local anyWindowUp = false
    for i = 0, 3 do
        if not IsVehicleWindowIntact(vehicle, i) then
            anyWindowUp = true
            break
        end
    end
    
    -- Toggle all windows
    for i = 0, 3 do
        if anyWindowUp then
            FixVehicleWindow(vehicle, i)
        else
            SmashVehicleWindow(vehicle, i)
        end
    end
    
    QBCore.Functions.Notify(anyWindowUp and 'Okna zavřena' or 'Okna otevřena', 'primary', 2000)
end

function toggleVehicleLight(vehicle, lightType)
    if not DoesEntityExist(vehicle) then return end
    
    if lightType == 'headlights' then
        local lightsOn = IsVehicleLightOn(vehicle, 0) or IsVehicleLightOn(vehicle, 1)
        SetVehicleLights(vehicle, lightsOn and 1 or 2)
        
    elseif lightType == 'interior' then
        SetVehicleInteriorlight(vehicle, not IsVehicleInteriorLightOn(vehicle))
        
    elseif lightType == 'left' then
        SetVehicleIndicatorLights(vehicle, 1, not GetVehicleIndicatorLights(vehicle))
        
    elseif lightType == 'right' then
        SetVehicleIndicatorLights(vehicle, 0, not GetVehicleIndicatorLights(vehicle))
        
    elseif lightType == 'hazard' then
        local hazardOn = GetVehicleIndicatorLights(vehicle) == 3
        SetVehicleIndicatorLights(vehicle, 1, not hazardOn)
        SetVehicleIndicatorLights(vehicle, 0, not hazardOn)
        
    elseif lightType == 'taxi' then
        SetTaxiLights(vehicle, not IsTaxiLightOn(vehicle))
    end
end

function toggleVehicleExtra(vehicle, extraId)
    if not DoesEntityExist(vehicle) then return end
    
    local extraOn = IsVehicleExtraTurnedOn(vehicle, extraId)
    SetVehicleExtra(vehicle, extraId, extraOn and 1 or 0)
    
    -- Notification
    local message = extraOn and Config.Messages.extra_disabled or Config.Messages.extra_enabled
    QBCore.Functions.Notify(message:format(extraId), 'primary', 2000)
    
    -- Sync with server
    if Config.SyncVehicleChanges then
        TriggerServerEvent('ddcz-vehicle:server:syncExtra', VehToNet(vehicle), extraId, not extraOn)
    end
end

function changeVehicleLivery(vehicle, livery)
    if not DoesEntityExist(vehicle) then return end
    
    SetVehicleLivery(vehicle, livery)
    
    -- Notification
    QBCore.Functions.Notify(Config.Messages.livery_changed:format(livery), 'success', 2000)
    
    -- Sync with server
    if Config.SyncVehicleChanges then
        TriggerServerEvent('ddcz-vehicle:server:syncLivery', VehToNet(vehicle), livery)
    end
end

-- ================================================
-- NUI Callbacks
-- ================================================

RegisterNUICallback('closeMenu', function(data, cb)
    closeVehicleMenu()
    cb('ok')
end)

RegisterNUICallback('vehicleAction', function(data, cb)
    if not currentVehicle or not DoesEntityExist(currentVehicle) then
        cb('error')
        return
    end

    local action = data.action

    if action == 'engine' then
        toggleVehicleEngine(currentVehicle)
    elseif action == 'lock' then
        toggleVehicleLocks(currentVehicle)
    elseif action == 'windows' then
        toggleAllWindows(currentVehicle)
    elseif action == 'trunk' then
        toggleVehicleDoor(currentVehicle, 5) -- Trunk is door 5
    end

    cb('ok')
end)

RegisterNUICallback('toggleDoor', function(data, cb)
    if not currentVehicle or not DoesEntityExist(currentVehicle) then
        cb('error')
        return
    end

    toggleVehicleDoor(currentVehicle, data.doorId, data.open)
    cb('ok')
end)

RegisterNUICallback('toggleLight', function(data, cb)
    if not currentVehicle or not DoesEntityExist(currentVehicle) then
        cb('error')
        return
    end

    toggleVehicleLight(currentVehicle, data.lightType)
    cb('ok')
end)

RegisterNUICallback('toggleExtra', function(data, cb)
    if not currentVehicle or not DoesEntityExist(currentVehicle) then
        cb('error')
        return
    end

    -- Check if player has permission
    if not PlayerData or not PlayerData.job then
        QBCore.Functions.Notify(Config.Messages.no_permission, 'error', 3000)
        cb('error')
        return
    end

    local hasAccess = false
    for _, job in pairs(Config.RestrictedJobs) do
        if PlayerData.job.name == job then
            hasAccess = true
            break
        end
    end

    if not hasAccess then
        QBCore.Functions.Notify(Config.Messages.no_permission, 'error', 3000)
        cb('error')
        return
    end

    toggleVehicleExtra(currentVehicle, data.extraId)
    cb('ok')
end)

RegisterNUICallback('changeLivery', function(data, cb)
    if not currentVehicle or not DoesEntityExist(currentVehicle) then
        cb('error')
        return
    end

    -- Check if player has permission
    if not PlayerData or not PlayerData.job then
        QBCore.Functions.Notify(Config.Messages.no_permission, 'error', 3000)
        cb('error')
        return
    end

    local hasAccess = false
    for _, job in pairs(Config.RestrictedJobs) do
        if PlayerData.job.name == job then
            hasAccess = true
            break
        end
    end

    if not hasAccess then
        QBCore.Functions.Notify(Config.Messages.no_permission, 'error', 3000)
        cb('error')
        return
    end

    changeVehicleLivery(currentVehicle, data.livery)
    cb('ok')
end)

RegisterNUICallback('saveSettings', function(data, cb)
    if not currentVehicle or not DoesEntityExist(currentVehicle) then
        cb('error')
        return
    end

    -- Here you could save vehicle settings to database if needed
    QBCore.Functions.Notify('Nastavení vozidla uloženo!', 'success', 2000)
    cb('ok')
end)

RegisterNUICallback('resetSettings', function(data, cb)
    if not currentVehicle or not DoesEntityExist(currentVehicle) then
        cb('error')
        return
    end

    -- Reset all doors
    for i = 0, 5 do
        SetVehicleDoorShut(currentVehicle, i, false)
    end

    -- Reset lights
    SetVehicleLights(currentVehicle, 0)
    SetVehicleIndicatorLights(currentVehicle, 0, false)
    SetVehicleIndicatorLights(currentVehicle, 1, false)

    -- Reset extras (if player has permission)
    if PlayerData and PlayerData.job then
        local hasAccess = false
        for _, job in pairs(Config.RestrictedJobs) do
            if PlayerData.job.name == job then
                hasAccess = true
                break
            end
        end

        if hasAccess then
            for i = 1, Config.MaxExtras do
                SetVehicleExtra(currentVehicle, i, 1) -- Turn off all extras
            end
        end
    end

    QBCore.Functions.Notify('Nastavení vozidla resetováno!', 'primary', 2000)
    cb('ok')
end)

-- ================================================
-- Utility Functions
-- ================================================

-- Auto close menu when player exits vehicle
CreateThread(function()
    while true do
        Wait(1000)

        if isMenuOpen and currentVehicle then
            local ped = PlayerPedId()
            local vehicle = GetVehiclePedIsIn(ped, false)
            local distance = #(GetEntityCoords(ped) - GetEntityCoords(currentVehicle))

            if Config.AutoCloseOnExit and (vehicle ~= currentVehicle and distance > Config.MaxDistance) then
                closeVehicleMenu()
            end
        end
    end
end)

-- Update livery info when menu is open
CreateThread(function()
    while true do
        Wait(2000)

        if isMenuOpen and currentVehicle and DoesEntityExist(currentVehicle) then
            local maxLiveries = GetVehicleLiveryCount(currentVehicle)
            local currentLivery = GetVehicleLivery(currentVehicle)

            if maxLiveries > 0 then
                SendNUIMessage({
                    type = 'updateLivery',
                    current = currentLivery,
                    max = maxLiveries - 1
                })
            end
        end
    end
end)

-- ================================================
-- Synchronization Events
-- ================================================

RegisterNetEvent('ddcz-vehicle:client:syncDoor', function(vehicleNetId, doorId, open, sourcePlayer)
    if sourcePlayer == GetPlayerServerId(PlayerId()) then return end -- Don't sync to self

    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    if not DoesEntityExist(vehicle) then return end

    if open then
        SetVehicleDoorOpen(vehicle, doorId, false, false)
    else
        SetVehicleDoorShut(vehicle, doorId, false)
    end
end)

RegisterNetEvent('ddcz-vehicle:client:syncEngine', function(vehicleNetId, engineOn, sourcePlayer)
    if sourcePlayer == GetPlayerServerId(PlayerId()) then return end -- Don't sync to self

    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    if not DoesEntityExist(vehicle) then return end

    SetVehicleEngineOn(vehicle, engineOn, false, true)
end)

RegisterNetEvent('ddcz-vehicle:client:syncLocks', function(vehicleNetId, locked, sourcePlayer)
    if sourcePlayer == GetPlayerServerId(PlayerId()) then return end -- Don't sync to self

    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    if not DoesEntityExist(vehicle) then return end

    SetVehicleDoorsLocked(vehicle, locked and 2 or 1)
end)

RegisterNetEvent('ddcz-vehicle:client:syncExtra', function(vehicleNetId, extraId, enabled, sourcePlayer)
    if sourcePlayer == GetPlayerServerId(PlayerId()) then return end -- Don't sync to self

    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    if not DoesEntityExist(vehicle) then return end

    SetVehicleExtra(vehicle, extraId, enabled and 0 or 1)
end)

RegisterNetEvent('ddcz-vehicle:client:syncLivery', function(vehicleNetId, livery, sourcePlayer)
    if sourcePlayer == GetPlayerServerId(PlayerId()) then return end -- Don't sync to self

    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    if not DoesEntityExist(vehicle) then return end

    SetVehicleLivery(vehicle, livery)
end)

RegisterNetEvent('ddcz-vehicle:client:forceClose', function()
    if isMenuOpen then
        closeVehicleMenu()
    end
end)

RegisterNetEvent('ddcz-vehicle:client:receiveConfig', function(serverConfig)
    -- Update config from server
    for k, v in pairs(serverConfig) do
        Config[k] = v
    end
end)

RegisterNetEvent('ddcz-vehicle:client:reloadConfig', function()
    -- Force close menu and reload
    if isMenuOpen then
        closeVehicleMenu()
    end

    QBCore.Functions.Notify('Vehicle config reloadován!', 'success', 2000)
end)
