{"manifest": {"name": "object.assign", "version": "4.1.2", "author": {"name": "<PERSON>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run lint && es-shim-api --bound", "test": "npm run tests-only && npm run test:ses", "posttest": "aud --production", "tests-only": "npm run test:implementation && npm run test:shim", "test:native": "nyc node test/native", "test:shim": "nyc node test/shimmed", "test:implementation": "nyc node test", "test:ses": "node test/ses-compat", "lint": "eslint .", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "prepublish": "safe-publish-latest && npm run build"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object.assign.git"}, "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "dependencies": {"call-bind": "^1.0.0", "define-properties": "^1.1.3", "has-symbols": "^1.0.1", "object-keys": "^1.1.1"}, "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^17.2.0", "aud": "^1.1.2", "browserify": "^16.5.2", "eslint": "^7.12.1", "for-each": "^0.3.3", "functions-have-names": "^1.2.1", "has": "^1.0.3", "is": "^3.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "ses": "^0.10.4", "tape": "^5.0.1"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-object-assign-4.1.2-0ed54a342eceb37b38ff76eb831a0e788cb63940-integrity\\node_modules\\object.assign\\package.json", "readmeFilename": "README.md", "readme": "#object.assign <sup>[![Version Badge][npm-version-svg]][npm-url]</sup>\n\n[![Build Status][travis-svg]][travis-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][npm-url]\n\n[![browser support][testling-png]][testling-url]\n\nAn Object.assign shim. Invoke its \"shim\" method to shim Object.assign if it is unavailable.\n\nThis package implements the [es-shim API](https://github.com/es-shims/api) interface. It works in an ES3-supported environment and complies with the [spec](http://www.ecma-international.org/ecma-262/6.0/#sec-object.assign). In an ES6 environment, it will also work properly with `Symbol`s.\n\nTakes a minimum of 2 arguments: `target` and `source`.\nTakes a variable sized list of source arguments - at least 1, as many as you want.\nThrows a TypeError if the `target` argument is `null` or `undefined`.\n\nMost common usage:\n```js\nvar assign = require('object.assign').getPolyfill(); // returns native method if compliant\n\t/* or */\nvar assign = require('object.assign/polyfill')(); // returns native method if compliant\n```\n\n## Example\n\n```js\nvar assert = require('assert');\n\n// Multiple sources!\nvar target = { a: true };\nvar source1 = { b: true };\nvar source2 = { c: true };\nvar sourceN = { n: true };\n\nvar expected = {\n\ta: true,\n\tb: true,\n\tc: true,\n\tn: true\n};\n\nassign(target, source1, source2, sourceN);\nassert.deepEqual(target, expected); // AWESOME!\n```\n\n```js\nvar target = {\n\ta: true,\n\tb: true,\n\tc: true\n};\nvar source1 = {\n\tc: false,\n\td: false\n};\nvar sourceN = {\n\te: false\n};\n\nvar assigned = assign(target, source1, sourceN);\nassert.equal(target, assigned); // returns the target object\nassert.deepEqual(assigned, {\n\ta: true,\n\tb: true,\n\tc: false,\n\td: false,\n\te: false\n});\n```\n\n```js\n/* when Object.assign is not present */\ndelete Object.assign;\nvar shimmedAssign = require('object.assign').shim();\n\t/* or */\nvar shimmedAssign = require('object.assign/shim')();\n\nassert.equal(shimmedAssign, assign);\n\nvar target = {\n\ta: true,\n\tb: true,\n\tc: true\n};\nvar source = {\n\tc: false,\n\td: false,\n\te: false\n};\n\nvar assigned = assign(target, source);\nassert.deepEqual(Object.assign(target, source), assign(target, source));\n```\n\n```js\n/* when Object.assign is present */\nvar shimmedAssign = require('object.assign').shim();\nassert.equal(shimmedAssign, Object.assign);\n\nvar target = {\n\ta: true,\n\tb: true,\n\tc: true\n};\nvar source = {\n\tc: false,\n\td: false,\n\te: false\n};\n\nassert.deepEqual(Object.assign(target, source), assign(target, source));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[npm-url]: https://npmjs.org/package/object.assign\n[npm-version-svg]: http://versionbadg.es/ljharb/object.assign.svg\n[travis-svg]: https://travis-ci.org/ljharb/object.assign.svg\n[travis-url]: https://travis-ci.org/ljharb/object.assign\n[deps-svg]: https://david-dm.org/ljharb/object.assign.svg?theme=shields.io\n[deps-url]: https://david-dm.org/ljharb/object.assign\n[dev-deps-svg]: https://david-dm.org/ljharb/object.assign/dev-status.svg?theme=shields.io\n[dev-deps-url]: https://david-dm.org/ljharb/object.assign#info=devDependencies\n[testling-png]: https://ci.testling.com/ljharb/object.assign.png\n[testling-url]: https://ci.testling.com/ljharb/object.assign\n[npm-badge-png]: https://nodei.co/npm/object.assign.png?downloads=true&stars=true\n[license-image]: http://img.shields.io/npm/l/object.assign.svg\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/object.assign.svg\n[downloads-url]: http://npm-stat.com/charts.html?package=object.assign\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 Jordan Harband\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/object.assign/-/object.assign-4.1.2.tgz#0ed54a342eceb37b38ff76eb831a0e788cb63940", "type": "tarball", "reference": "https://registry.yarnpkg.com/object.assign/-/object.assign-4.1.2.tgz", "hash": "0ed54a342eceb37b38ff76eb831a0e788cb63940", "integrity": "sha512-ixT2L5THXsApyiUPYKmW+2EHpXXe5Ii3M+f4e+aJFAHao5amFRW6J0OO6c/LU8Be47utCx2GL89hxGB6XSmKuQ==", "registry": "npm", "packageName": "object.assign", "cacheIntegrity": "sha512-ixT2L5THXsApyiUPYKmW+2EHpXXe5Ii3M+f4e+aJFAHao5amFRW6J0OO6c/LU8Be47utCx2GL89hxGB6XSmKuQ== sha1-DtVKNC7Os3s4/3brgxoOeIy2OUA="}, "registry": "npm", "hash": "0ed54a342eceb37b38ff76eb831a0e788cb63940"}