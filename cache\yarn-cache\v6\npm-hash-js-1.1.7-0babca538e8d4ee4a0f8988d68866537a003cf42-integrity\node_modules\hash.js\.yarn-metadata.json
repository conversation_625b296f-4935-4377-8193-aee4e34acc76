{"manifest": {"name": "hash.js", "version": "1.1.7", "description": "Various hash functions that could be run by both browser and node", "main": "lib/hash.js", "typings": "lib/hash.d.ts", "scripts": {"test": "mocha --reporter=spec test/*-test.js && npm run lint", "lint": "eslint lib/*.js lib/**/*.js lib/**/**/*.js test/*.js"}, "repository": {"type": "git", "url": "**************:indutny/hash.js"}, "keywords": ["hash", "sha256", "sha224", "hmac"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/indutny/hash.js/issues"}, "homepage": "https://github.com/indutny/hash.js", "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}, "devDependencies": {"eslint": "^4.19.1", "mocha": "^5.2.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-hash-js-1.1.7-0babca538e8d4ee4a0f8988d68866537a003cf42-integrity\\node_modules\\hash.js\\package.json", "readmeFilename": "README.md", "readme": "# hash.js [![Build Status](https://secure.travis-ci.org/indutny/hash.js.svg)](http://travis-ci.org/indutny/hash.js)\n\nJust a bike-shed.\n\n## Install\n\n```sh\nnpm install hash.js\n```\n\n## Usage\n\n```js\nvar hash = require('hash.js')\nhash.sha256().update('abc').digest('hex')\n```\n\n## Selective hash usage\n\n```js\nvar sha512 = require('hash.js/lib/hash/sha/512');\nsha512().update('abc').digest('hex');\n```\n\n#### LICENSE\n\nThis software is licensed under the MIT License.\n\nCopyright Fedor Indutny, 2014.\n\nPermission is hereby granted, free of charge, to any person obtaining a\ncopy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to permit\npersons to whom the Software is furnished to do so, subject to the\nfollowing conditions:\n\nThe above copyright notice and this permission notice shall be included\nin all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\nOR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\nNO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\nDAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\nOTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\nUSE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/hash.js/-/hash.js-1.1.7.tgz#0babca538e8d4ee4a0f8988d68866537a003cf42", "type": "tarball", "reference": "https://registry.yarnpkg.com/hash.js/-/hash.js-1.1.7.tgz", "hash": "0babca538e8d4ee4a0f8988d68866537a003cf42", "integrity": "sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA==", "registry": "npm", "packageName": "hash.js", "cacheIntegrity": "sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA== sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I="}, "registry": "npm", "hash": "0babca538e8d4ee4a0f8988d68866537a003cf42"}