{"manifest": {"name": "cache-content-type", "version": "1.0.1", "description": "Create a full Content-Type header given a MIME type or extension and catch the result", "main": "index.js", "files": ["index.js"], "scripts": {"test": "egg-bin test", "cov": "egg-bin cov", "ci": "eslint . && npm run cov"}, "dependencies": {"mime-types": "^2.1.18", "ylru": "^1.2.0"}, "devDependencies": {"egg-bin": "^4.7.1", "egg-ci": "^1.8.0", "eslint": "^5.1.0", "eslint-config-egg": "^7.0.0", "mm": "^2.2.0"}, "repository": {"type": "git", "url": "https://github.com/node-modules/cache-content-type.git"}, "keywords": ["mime", "content-type", "lru"], "engines": {"node": ">= 6.0.0"}, "ci": {"version": "6, 8, 10"}, "author": {"name": "dead_horse"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-cache-content-type-1.0.1-035cde2b08ee2129f4a8315ea8f00a00dba1453c-integrity\\node_modules\\cache-content-type\\package.json", "readmeFilename": "README.md", "readme": "## cache-content-type\n\nThe same as [mime-types](https://github.com/jshttp/mime-types)'s contentType method, but with result cached.\n\n### Install\n\n```bash\nnpm i cache-content-type\n```\n\n### Usage\n\n```js\nconst getType = require('cache-content-type');\nconst contentType = getType('html');\nassert(contentType === 'text/html; charset=utf-8');\n```\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/cache-content-type/-/cache-content-type-1.0.1.tgz#035cde2b08ee2129f4a8315ea8f00a00dba1453c", "type": "tarball", "reference": "https://registry.yarnpkg.com/cache-content-type/-/cache-content-type-1.0.1.tgz", "hash": "035cde2b08ee2129f4a8315ea8f00a00dba1453c", "integrity": "sha512-IKufZ1o4Ut42YUrZSo8+qnMTrFuKkvyoLXUywKz9GJ5BrhOFGhLdkx9sG4KAnVvbY6kEcSFjLQul+DVmBm2bgA==", "registry": "npm", "packageName": "cache-content-type", "cacheIntegrity": "sha512-IKufZ1o4Ut42YUrZSo8+qnMTrFuKkvyoLXUywKz9GJ5BrhOFGhLdkx9sG4KAnVvbY6kEcSFjLQul+DVmBm2bgA== sha1-A1zeKwjuISn0qDFeqPAKANuhRTw="}, "registry": "npm", "hash": "035cde2b08ee2129f4a8315ea8f00a00dba1453c"}