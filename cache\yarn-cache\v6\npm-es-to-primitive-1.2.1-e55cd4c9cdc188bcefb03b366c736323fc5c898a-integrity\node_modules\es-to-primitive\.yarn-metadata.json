{"manifest": {"name": "es-to-primitive", "version": "1.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ECMAScript “ToPrimitive” algorithm. Provides ES5 and ES2015 versions.", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "posttest": "npx aud", "tests-only": "node --es-staging test", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "eslint ."}, "repository": {"type": "git", "url": "git://github.com/ljharb/es-to-primitive.git"}, "keywords": ["primitive", "abstract", "ecmascript", "es5", "es6", "es2015", "toPrimitive", "coerce", "type", "object", "string", "number", "boolean", "symbol", "null", "undefined"], "dependencies": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}, "devDependencies": {"@ljharb/eslint-config": "^15.0.0", "covert": "^1.1.1", "eslint": "^6.6.0", "foreach": "^2.0.5", "function.prototype.name": "^1.1.1", "has-symbols": "^1.0.0", "object-inspect": "^1.6.0", "object-is": "^1.0.1", "replace": "^1.1.1", "semver": "^6.3.0", "tape": "^4.11.0"}, "testling": {"files": "test", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-es-to-primitive-1.2.1-e55cd4c9cdc188bcefb03b366c736323fc5c898a-integrity\\node_modules\\es-to-primitive\\package.json", "readmeFilename": "README.md", "readme": "# es-to-primitive <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![Build Status][travis-svg]][travis-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nECMAScript “ToPrimitive” algorithm. Provides ES5 and ES2015 versions.\nWhen different versions of the spec conflict, the default export will be the latest version of the abstract operation.\nAlternative versions will also be available under an `es5`/`es2015` exported property if you require a specific version.\n\n## Example\n\n```js\nvar toPrimitive = require('es-to-primitive');\nvar assert = require('assert');\n\nassert(toPrimitive(function () {}) === String(function () {}));\n\nvar date = new Date();\nassert(toPrimitive(date) === String(date));\n\nassert(toPrimitive({ valueOf: function () { return 3; } }) === 3);\n\nassert(toPrimitive(['a', 'b', 3]) === String(['a', 'b', 3]));\n\nvar sym = Symbol();\nassert(toPrimitive(Object(sym)) === sym);\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/es-to-primitive\n[npm-version-svg]: http://versionbadg.es/ljharb/es-to-primitive.svg\n[travis-svg]: https://travis-ci.org/ljharb/es-to-primitive.svg\n[travis-url]: https://travis-ci.org/ljharb/es-to-primitive\n[deps-svg]: https://david-dm.org/ljharb/es-to-primitive.svg\n[deps-url]: https://david-dm.org/ljharb/es-to-primitive\n[dev-deps-svg]: https://david-dm.org/ljharb/es-to-primitive/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/es-to-primitive#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/es-to-primitive.png?downloads=true&stars=true\n[license-image]: http://img.shields.io/npm/l/es-to-primitive.svg\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/es-to-primitive.svg\n[downloads-url]: http://npm-stat.com/charts.html?package=es-to-primitive\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015 Jordan Harband\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/es-to-primitive/-/es-to-primitive-1.2.1.tgz#e55cd4c9cdc188bcefb03b366c736323fc5c898a", "type": "tarball", "reference": "https://registry.yarnpkg.com/es-to-primitive/-/es-to-primitive-1.2.1.tgz", "hash": "e55cd4c9cdc188bcefb03b366c736323fc5c898a", "integrity": "sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==", "registry": "npm", "packageName": "es-to-primitive", "cacheIntegrity": "sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA== sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo="}, "registry": "npm", "hash": "e55cd4c9cdc188bcefb03b366c736323fc5c898a"}