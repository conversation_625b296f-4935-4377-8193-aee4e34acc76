{"manifest": {"name": "glob-parent", "version": "5.1.1", "description": "Extract the non-magic parent path from a glob string.", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "https://gulpjs.com/"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/gulpjs/glob-parent.git"}, "license": "ISC", "engines": {"node": ">= 6"}, "main": "index.js", "files": ["LICENSE", "index.js"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only", "azure-pipelines": "nyc mocha --async-only --reporter xunit -O output=test.xunit", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "dependencies": {"is-glob": "^4.0.1"}, "devDependencies": {"coveralls": "^3.0.11", "eslint": "^2.13.1", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "mocha": "^6.0.2", "nyc": "^13.3.0"}, "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-glob-parent-5.1.1-b6c1ef417c4e5663ea498f1c45afac6916bbc229-integrity\\node_modules\\glob-parent\\package.json", "readmeFilename": "README.md", "readme": "<p align=\"center\">\n  <a href=\"https://gulpjs.com\">\n    <img height=\"257\" width=\"114\" src=\"https://raw.githubusercontent.com/gulpjs/artwork/master/gulp-2x.png\">\n  </a>\n</p>\n\n# glob-parent\n\n[![NPM version][npm-image]][npm-url] [![Downloads][downloads-image]][npm-url] [![Azure Pipelines Build Status][azure-pipelines-image]][azure-pipelines-url] [![Travis Build Status][travis-image]][travis-url] [![AppVeyor Build Status][appveyor-image]][appveyor-url] [![Coveralls Status][coveralls-image]][coveralls-url] [![Gitter chat][gitter-image]][gitter-url]\n\nExtract the non-magic parent path from a glob string.\n\n## Usage\n\n```js\nvar globParent = require('glob-parent');\n\nglobParent('path/to/*.js'); // 'path/to'\nglobParent('/root/path/to/*.js'); // '/root/path/to'\nglobParent('/*.js'); // '/'\nglobParent('*.js'); // '.'\nglobParent('**/*.js'); // '.'\nglobParent('path/{to,from}'); // 'path'\nglobParent('path/!(to|from)'); // 'path'\nglobParent('path/?(to|from)'); // 'path'\nglobParent('path/+(to|from)'); // 'path'\nglobParent('path/*(to|from)'); // 'path'\nglobParent('path/@(to|from)'); // 'path'\nglobParent('path/**/*'); // 'path'\n\n// if provided a non-glob path, returns the nearest dir\nglobParent('path/foo/bar.js'); // 'path/foo'\nglobParent('path/foo/'); // 'path/foo'\nglobParent('path/foo'); // 'path' (see issue #3 for details)\n```\n\n## API\n\n### `globParent(maybeGlobString, [options])`\n\nTakes a string and returns the part of the path before the glob begins. Be aware of Escaping rules and Limitations below.\n\n#### options\n\n```js\n{\n  // Disables the automatic conversion of slashes for Windows\n  flipBackslashes: true\n}\n```\n\n## Escaping\n\nThe following characters have special significance in glob patterns and must be escaped if you want them to be treated as regular path characters:\n\n- `?` (question mark) unless used as a path segment alone\n- `*` (asterisk)\n- `|` (pipe)\n- `(` (opening parenthesis)\n- `)` (closing parenthesis)\n- `{` (opening curly brace)\n- `}` (closing curly brace)\n- `[` (opening bracket)\n- `]` (closing bracket)\n\n**Example**\n\n```js\nglobParent('foo/[bar]/') // 'foo'\nglobParent('foo/\\\\[bar]/') // 'foo/[bar]'\n```\n\n## Limitations\n\n### Braces & Brackets\nThis library attempts a quick and imperfect method of determining which path\nparts have glob magic without fully parsing/lexing the pattern. There are some\nadvanced use cases that can trip it up, such as nested braces where the outer\npair is escaped and the inner one contains a path separator. If you find\nyourself in the unlikely circumstance of being affected by this or need to\nensure higher-fidelity glob handling in your library, it is recommended that you\npre-process your input with [expand-braces] and/or [expand-brackets].\n\n### Windows\nBackslashes are not valid path separators for globs. If a path with backslashes\nis provided anyway, for simple cases, glob-parent will replace the path\nseparator for you and return the non-glob parent path (now with\nforward-slashes, which are still valid as Windows path separators).\n\nThis cannot be used in conjunction with escape characters.\n\n```js\n// BAD\nglobParent('C:\\\\Program Files \\\\(x86\\\\)\\\\*.ext') // 'C:/Program Files /(x86/)'\n\n// GOOD\nglobParent('C:/Program Files\\\\(x86\\\\)/*.ext') // 'C:/Program Files (x86)'\n```\n\nIf you are using escape characters for a pattern without path parts (i.e.\nrelative to `cwd`), prefix with `./` to avoid confusing glob-parent.\n\n```js\n// BAD\nglobParent('foo \\\\[bar]') // 'foo '\nglobParent('foo \\\\[bar]*') // 'foo '\n\n// GOOD\nglobParent('./foo \\\\[bar]') // 'foo [bar]'\nglobParent('./foo \\\\[bar]*') // '.'\n```\n\n## License\n\nISC\n\n[expand-braces]: https://github.com/jonschlinkert/expand-braces\n[expand-brackets]: https://github.com/jonschlinkert/expand-brackets\n\n[downloads-image]: https://img.shields.io/npm/dm/glob-parent.svg\n[npm-url]: https://www.npmjs.com/package/glob-parent\n[npm-image]: https://img.shields.io/npm/v/glob-parent.svg\n\n[azure-pipelines-url]: https://dev.azure.com/gulpjs/gulp/_build/latest?definitionId=2&branchName=master\n[azure-pipelines-image]: https://dev.azure.com/gulpjs/gulp/_apis/build/status/glob-parent?branchName=master\n\n[travis-url]: https://travis-ci.org/gulpjs/glob-parent\n[travis-image]: https://img.shields.io/travis/gulpjs/glob-parent.svg?label=travis-ci\n\n[appveyor-url]: https://ci.appveyor.com/project/gulpjs/glob-parent\n[appveyor-image]: https://img.shields.io/appveyor/ci/gulpjs/glob-parent.svg?label=appveyor\n\n[coveralls-url]: https://coveralls.io/r/gulpjs/glob-parent\n[coveralls-image]: https://img.shields.io/coveralls/gulpjs/glob-parent/master.svg\n\n[gitter-url]: https://gitter.im/gulpjs/gulp\n[gitter-image]: https://badges.gitter.im/gulpjs/gulp.svg\n", "licenseText": "The ISC License\n\nCopyright (c) 2015, 2019 <PERSON><PERSON>\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, <PERSON><PERSON><PERSON><PERSON><PERSON> IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/glob-parent/-/glob-parent-5.1.1.tgz#b6c1ef417c4e5663ea498f1c45afac6916bbc229", "type": "tarball", "reference": "https://registry.yarnpkg.com/glob-parent/-/glob-parent-5.1.1.tgz", "hash": "b6c1ef417c4e5663ea498f1c45afac6916bbc229", "integrity": "sha512-FnI+VGOpnlGHWZxthPGR+QhR78fuiK0sNLkHQv+bL9fQi57lNNdquIbna/WrfROrolq8GK5Ek6BiMwqL/voRYQ==", "registry": "npm", "packageName": "glob-parent", "cacheIntegrity": "sha512-FnI+VGOpnlGHWZxthPGR+QhR78fuiK0sNLkHQv+bL9fQi57lNNdquIbna/WrfROrolq8GK5Ek6BiMwqL/voRYQ== sha1-tsHvQXxOVmPqSY8cRa+saRa7wik="}, "registry": "npm", "hash": "b6c1ef417c4e5663ea498f1c45afac6916bbc229"}