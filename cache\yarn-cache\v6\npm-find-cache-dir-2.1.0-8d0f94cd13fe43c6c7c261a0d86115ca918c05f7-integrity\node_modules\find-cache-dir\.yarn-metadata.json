{"manifest": {"name": "find-cache-dir", "version": "2.1.0", "description": "Finds the common standard cache directory", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/avajs/find-cache-dir.git"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js"], "keywords": ["cache", "directory", "dir", "caching", "find", "search"], "dependencies": {"commondir": "^1.0.1", "make-dir": "^2.0.0", "pkg-dir": "^3.0.0"}, "devDependencies": {"ava": "^1.3.1", "coveralls": "^3.0.3", "del": "^4.0.0", "nyc": "^13.3.0", "xo": "^0.24.0"}, "nyc": {"reporter": ["lcov", "text"]}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-find-cache-dir-2.1.0-8d0f94cd13fe43c6c7c261a0d86115ca918c05f7-integrity\\node_modules\\find-cache-dir\\package.json", "readmeFilename": "readme.md", "readme": "# find-cache-dir [![Build Status](https://travis-ci.org/avajs/find-cache-dir.svg?branch=master)](https://travis-ci.org/avajs/find-cache-dir) [![Coverage Status](https://coveralls.io/repos/github/avajs/find-cache-dir/badge.svg?branch=master)](https://coveralls.io/github/avajs/find-cache-dir?branch=master)\n\n> Finds the common standard cache directory\n\nThe [`nyc`](https://github.com/istanbuljs/nyc) and [`AVA`](https://ava.li) projects decided to standardize on a common directory structure for storing cache information:\n\n```sh\n# nyc\n./node_modules/.cache/nyc\n\n# ava\n./node_modules/.cache/ava\n\n# your-module\n./node_modules/.cache/your-module\n```\n\nThis module makes it easy to correctly locate the cache directory according to this shared spec. If this pattern becomes ubiquitous, clearing the cache for multiple dependencies becomes easy and consistent:\n\n```\nrm -rf ./node_modules/.cache\n```\n\nIf you decide to adopt this pattern, please file a PR adding your name to the list of adopters below.\n\n\n## Install\n\n```\n$ npm install find-cache-dir\n```\n\n\n## Usage\n\n```js\nconst findCacheDir = require('find-cache-dir');\n\nfindCacheDir({name: 'unicorns'});\n//=> '/user/path/node-modules/.cache/unicorns'\n```\n\n\n## API\n\n### findCacheDir([options])\n\nFinds the cache directory using the supplied options. The algorithm tries to find a `package.json` file, searching every parent directory of the `cwd` specified (or implied from other options). It returns a `string` containing the absolute path to the cache directory, or `null` if `package.json` was never found.\n\n#### options\n\nType: `Object`\n\n##### name\n\n*Required*<br>\nType: `string`\n\nShould be the same as your project name in `package.json`.\n\n##### files\n\nType: `string[]` `string`\n\nAn array of files that will be searched for a common parent directory. This common parent directory will be used in lieu of the `cwd` option below.\n\n##### cwd\n\nType: `string`<br>\nDefault `process.cwd()`\n\nDirectory to start searching for a `package.json` from.\n\n##### create\n\nType: `boolean`<br>\nDefault `false`\n\nIf `true`, the directory will be created synchronously before returning.\n\n##### thunk\n\nType: `boolean`<br>\nDefault `false`\n\nIf `true`, this modifies the return type to be a function that is a thunk for `path.join(theFoundCacheDirectory)`.\n\n```js\nconst thunk = findCacheDir({name: 'foo', thunk: true});\n\nthunk();\n//=> '/some/path/node_modules/.cache/foo'\n\nthunk('bar.js')\n//=> '/some/path/node_modules/.cache/foo/bar.js'\n\nthunk('baz', 'quz.js')\n//=> '/some/path/node_modules/.cache/foo/baz/quz.js'\n```\n\nThis is helpful for actually putting actual files in the cache!\n\n\n## Adopters\n\n- [`AVA`](https://ava.li)\n- [`nyc`](https://github.com/istanbuljs/nyc)\n- [`babel-loader`](https://github.com/babel/babel-loader)\n- [`eslint-loader`](https://github.com/MoOx/eslint-loader)\n- [`Phenomic`](https://phenomic.io)\n- [`javascripthon-loader`](https://github.com/Beg-in/javascripthon-loader)\n\n\n## License\n\nMIT\n", "licenseText": "MIT License\n\nCopyright (c) <PERSON> <<EMAIL>> (github.com/jamestalmage)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPY<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/find-cache-dir/-/find-cache-dir-2.1.0.tgz#8d0f94cd13fe43c6c7c261a0d86115ca918c05f7", "type": "tarball", "reference": "https://registry.yarnpkg.com/find-cache-dir/-/find-cache-dir-2.1.0.tgz", "hash": "8d0f94cd13fe43c6c7c261a0d86115ca918c05f7", "integrity": "sha512-Tq6PixE0w/VMFfCgbONnkiQIVol/JJL7nRMi20fqzA4NRs9AfeqMGeRdPi3wIhYkxjeBaWh2rxwapn5Tu3IqOQ==", "registry": "npm", "packageName": "find-cache-dir", "cacheIntegrity": "sha512-Tq6PixE0w/VMFfCgbONnkiQIVol/JJL7nRMi20fqzA4NRs9AfeqMGeRdPi3wIhYkxjeBaWh2rxwapn5Tu3IqOQ== sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc="}, "registry": "npm", "hash": "8d0f94cd13fe43c6c7c261a0d86115ca918c05f7"}