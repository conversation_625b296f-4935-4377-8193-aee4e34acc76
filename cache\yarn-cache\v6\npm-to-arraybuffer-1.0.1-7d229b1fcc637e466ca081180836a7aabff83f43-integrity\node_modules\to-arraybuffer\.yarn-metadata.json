{"manifest": {"name": "to-arraybuffer", "version": "1.0.1", "description": "Get an ArrayBuffer from a Buffer as fast as possible", "main": "index.js", "scripts": {"test": "npm run test-node && ([ -n \"${TRAVIS_PULL_REQUEST}\" -a \"${TRAVIS_PULL_REQUEST}\" != 'false' ] || npm run test-browser)", "test-node": "tape test.js", "test-browser": "zuul --no-coverage -- test.js", "test-browser-local": "zuul --local 8080 --no-coverage -- test.js"}, "repository": {"type": "git", "url": "git://github.com/jhiesey/to-arraybuffer.git"}, "keywords": ["buffer", "to", "arraybuffer", "fast", "read", "only"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jhiesey/to-arraybuffer/issues"}, "homepage": "https://github.com/jhiesey/to-arraybuffer#readme", "devDependencies": {"tape": "^4.4.0", "zuul": "^3.9.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-to-arraybuffer-1.0.1-7d229b1fcc637e466ca081180836a7aabff83f43-integrity\\node_modules\\to-arraybuffer\\package.json", "readmeFilename": "README.md", "readme": "# to-arraybuffer [![Build Status](https://travis-ci.org/jhiesey/to-arraybuffer.svg?branch=master)](https://travis-ci.org/jhiesey/to-arraybuffer)\n\n[![Sauce Test Status](https://saucelabs.com/browser-matrix/to-arraybuffer.svg)](https://saucelabs.com/u/to-arraybuffer)\n\nConvert from a Buffer to an ArrayBuffer as fast as possible.\n\nNote that in some cases the returned ArrayBuffer is backed by the same memory as the original\nBuffer (but in other cases it is a copy), so **modifying the ArrayBuffer is not recommended**.\n\nThis module is designed to work both in node.js and in all browsers with ArrayBuffer support\nwhen using [the Buffer implementation provided by Browserify](https://www.npmjs.com/package/buffer).\n\n## Usage\n\n``` js\nvar toArrayBuffer = require('to-arraybuffer')\n\nvar buffer = new Buffer(100)\n// Fill the buffer with some data\n\nvar ab = toArrayBuffer(buffer)\n// `ab` now contains the same data as `buffer`\n```\n\n## License\n\nMIT", "licenseText": "The MIT License\n\nCopyright (c) 2016 John <PERSON>\n\nPermission is hereby granted, free of charge, \nto any person obtaining a copy of this software and \nassociated documentation files (the \"Software\"), to \ndeal in the Software without restriction, including \nwithout limitation the rights to use, copy, modify, \nmerge, publish, distribute, sublicense, and/or sell \ncopies of the Software, and to permit persons to whom \nthe Software is furnished to do so, \nsubject to the following conditions:\n\nThe above copyright notice and this permission notice \nshall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, \nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES \nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. \nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR \nANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, \nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE \nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz#7d229b1fcc637e466ca081180836a7aabff83f43", "type": "tarball", "reference": "https://registry.yarnpkg.com/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz", "hash": "7d229b1fcc637e466ca081180836a7aabff83f43", "integrity": "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=", "registry": "npm", "packageName": "to-arraybuffer", "cacheIntegrity": "sha512-okFlQcoGTi4LQBG/PgSYblw9VOyptsz2KJZqc6qtgGdes8VktzUQkj4BI2blit072iS8VODNcMA+tvnS9dnuMA== sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M="}, "registry": "npm", "hash": "7d229b1fcc637e466ca081180836a7aabff83f43"}