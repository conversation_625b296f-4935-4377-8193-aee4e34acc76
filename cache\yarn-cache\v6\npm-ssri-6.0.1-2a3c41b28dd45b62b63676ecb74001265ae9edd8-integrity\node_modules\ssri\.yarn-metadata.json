{"manifest": {"name": "ssri", "version": "6.0.1", "description": "Standard Subresource Integrity library --  parses, serializes, generates, and verifies integrity metadata according to the SRI spec.", "main": "index.js", "files": ["*.js"], "scripts": {"prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "pretest": "standard", "release": "standard-version -s", "test": "tap -J --coverage test/*.js", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "repository": {"type": "git", "url": "https://github.com/zkat/ssri"}, "keywords": ["w3c", "web", "security", "integrity", "checksum", "hashing", "subresource integrity", "sri", "sri hash", "sri string", "sri generator", "html"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "dependencies": {"figgy-pudding": "^3.5.1"}, "devDependencies": {"nyc": "^11.4.1", "standard": "^10.0.3", "standard-version": "^4.3.0", "tap": "^11.1.0", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8"}, "config": {"nyc": {"exclude": ["node_modules/**", "test/**"]}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-ssri-6.0.1-2a3c41b28dd45b62b63676ecb74001265ae9edd8-integrity\\node_modules\\ssri\\package.json", "readmeFilename": "README.md", "readme": "# ssri [![npm version](https://img.shields.io/npm/v/ssri.svg)](https://npm.im/ssri) [![license](https://img.shields.io/npm/l/ssri.svg)](https://npm.im/ssri) [![Travis](https://img.shields.io/travis/zkat/ssri.svg)](https://travis-ci.org/zkat/ssri) [![AppVeyor](https://ci.appveyor.com/api/projects/status/github/zkat/ssri?svg=true)](https://ci.appveyor.com/project/zkat/ssri) [![Coverage Status](https://coveralls.io/repos/github/zkat/ssri/badge.svg?branch=latest)](https://coveralls.io/github/zkat/ssri?branch=latest)\n\n[`ssri`](https://github.com/zkat/ssri), short for Standard Subresource\nIntegrity, is a Node.js utility for parsing, manipulating, serializing,\ngenerating, and verifying [Subresource\nIntegrity](https://w3c.github.io/webappsec/specs/subresourceintegrity/) hashes.\n\n## Install\n\n`$ npm install --save ssri`\n\n## Table of Contents\n\n* [Example](#example)\n* [Features](#features)\n* [Contributing](#contributing)\n* [API](#api)\n  * Parsing & Serializing\n    * [`parse`](#parse)\n    * [`stringify`](#stringify)\n    * [`Integrity#concat`](#integrity-concat)\n    * [`Integrity#toString`](#integrity-to-string)\n    * [`Integrity#toJSON`](#integrity-to-json)\n    * [`Integrity#match`](#integrity-match)\n    * [`Integrity#pickAlgorithm`](#integrity-pick-algorithm)\n    * [`Integrity#hexDigest`](#integrity-hex-digest)\n  * Integrity Generation\n    * [`fromHex`](#from-hex)\n    * [`fromData`](#from-data)\n    * [`fromStream`](#from-stream)\n    * [`create`](#create)\n  * Integrity Verification\n    * [`checkData`](#check-data)\n    * [`checkStream`](#check-stream)\n    * [`integrityStream`](#integrity-stream)\n\n### Example\n\n```javascript\nconst ssri = require('ssri')\n\nconst integrity = 'sha512-9KhgCRIx/AmzC8xqYJTZRrnO8OW2Pxyl2DIMZSBOr0oDvtEFyht3xpp71j/r/pAe1DM+JI/A+line3jUBgzQ7A==?foo'\n\n// Parsing and serializing\nconst parsed = ssri.parse(integrity)\nssri.stringify(parsed) // === integrity (works on non-Integrity objects)\nparsed.toString() // === integrity\n\n// Async stream functions\nssri.checkStream(fs.createReadStream('./my-file'), integrity).then(...)\nssri.fromStream(fs.createReadStream('./my-file')).then(sri => {\n  sri.toString() === integrity\n})\nfs.createReadStream('./my-file').pipe(ssri.createCheckerStream(sri))\n\n// Sync data functions\nssri.fromData(fs.readFileSync('./my-file')) // === parsed\nssri.checkData(fs.readFileSync('./my-file'), integrity) // => 'sha512'\n```\n\n### Features\n\n* Parses and stringifies SRI strings.\n* Generates SRI strings from raw data or Streams.\n* Strict standard compliance.\n* `?foo` metadata option support.\n* Multiple entries for the same algorithm.\n* Object-based integrity hash manipulation.\n* Small footprint: no dependencies, concise implementation.\n* Full test coverage.\n* Customizable algorithm picker.\n\n### Contributing\n\nThe ssri team enthusiastically welcomes contributions and project participation!\nThere's a bunch of things you can do if you want to contribute! The [Contributor\nGuide](CONTRIBUTING.md) has all the information you need for everything from\nreporting bugs to contributing entire new features. Please don't hesitate to\njump in if you'd like to, or even ask us questions if something isn't clear.\n\n### API\n\n#### <a name=\"parse\"></a> `> ssri.parse(sri, [opts]) -> Integrity`\n\nParses `sri` into an `Integrity` data structure. `sri` can be an integrity\nstring, an `Hash`-like with `digest` and `algorithm` fields and an optional\n`options` field, or an `Integrity`-like object. The resulting object will be an\n`Integrity` instance that has this shape:\n\n```javascript\n{\n  'sha1': [{algorithm: 'sha1', digest: 'deadbeef', options: []}],\n  'sha512': [\n    {algorithm: 'sha512', digest: 'c0ffee', options: []},\n    {algorithm: 'sha512', digest: 'bad1dea', options: ['foo']}\n  ],\n}\n```\n\nIf `opts.single` is truthy, a single `Hash` object will be returned. That is, a\nsingle object that looks like `{algorithm, digest, options}`, as opposed to a\nlarger object with multiple of these.\n\nIf `opts.strict` is truthy, the resulting object will be filtered such that\nit strictly follows the Subresource Integrity spec, throwing away any entries\nwith any invalid components. This also means a restricted set of algorithms\nwill be used -- the spec limits them to `sha256`, `sha384`, and `sha512`.\n\nStrict mode is recommended if the integrity strings are intended for use in\nbrowsers, or in other situations where strict adherence to the spec is needed.\n\n##### Example\n\n```javascript\nssri.parse('sha512-9KhgCRIx/AmzC8xqYJTZRrnO8OW2Pxyl2DIMZSBOr0oDvtEFyht3xpp71j/r/pAe1DM+JI/A+line3jUBgzQ7A==?foo') // -> Integrity object\n```\n\n#### <a name=\"stringify\"></a> `> ssri.stringify(sri, [opts]) -> String`\n\nThis function is identical to [`Integrity#toString()`](#integrity-to-string),\nexcept it can be used on _any_ object that [`parse`](#parse) can handle -- that\nis, a string, an `Hash`-like, or an `Integrity`-like.\n\nThe `opts.sep` option defines the string to use when joining multiple entries\ntogether. To be spec-compliant, this _must_ be whitespace. The default is a\nsingle space (`' '`).\n\nIf `opts.strict` is true, the integrity string will be created using strict\nparsing rules. See [`ssri.parse`](#parse).\n\n##### Example\n\n```javascript\n// Useful for cleaning up input SRI strings:\nssri.stringify('\\n\\rsha512-foo\\n\\t\\tsha384-bar')\n// -> 'sha512-foo sha384-bar'\n\n// Hash-like: only a single entry.\nssri.stringify({\n  algorithm: 'sha512',\n  digest:'9KhgCRIx/AmzC8xqYJTZRrnO8OW2Pxyl2DIMZSBOr0oDvtEFyht3xpp71j/r/pAe1DM+JI/A+line3jUBgzQ7A==',\n  options: ['foo']\n})\n// ->\n// 'sha512-9KhgCRIx/AmzC8xqYJTZRrnO8OW2Pxyl2DIMZSBOr0oDvtEFyht3xpp71j/r/pAe1DM+JI/A+line3jUBgzQ7A==?foo'\n\n// Integrity-like: full multi-entry syntax. Similar to output of `ssri.parse`\nssri.stringify({\n  'sha512': [\n    {\n      algorithm: 'sha512',\n      digest:'9KhgCRIx/AmzC8xqYJTZRrnO8OW2Pxyl2DIMZSBOr0oDvtEFyht3xpp71j/r/pAe1DM+JI/A+line3jUBgzQ7A==',\n      options: ['foo']\n    }\n  ]\n})\n// ->\n// 'sha512-9KhgCRIx/AmzC8xqYJTZRrnO8OW2Pxyl2DIMZSBOr0oDvtEFyht3xpp71j/r/pAe1DM+JI/A+line3jUBgzQ7A==?foo'\n```\n\n#### <a name=\"integrity-concat\"></a> `> Integrity#concat(otherIntegrity, [opts]) -> Integrity`\n\nConcatenates an `Integrity` object with another IntegrityLike, or an integrity\nstring.\n\nThis is functionally equivalent to concatenating the string format of both\nintegrity arguments, and calling [`ssri.parse`](#ssri-parse) on the new string.\n\nIf `opts.strict` is true, the new `Integrity` will be created using strict\nparsing rules. See [`ssri.parse`](#parse).\n\n##### Example\n\n```javascript\n// This will combine the integrity checks for two different versions of\n// your index.js file so you can use a single integrity string and serve\n// either of these to clients, from a single `<script>` tag.\nconst desktopIntegrity = ssri.fromData(fs.readFileSync('./index.desktop.js'))\nconst mobileIntegrity = ssri.fromData(fs.readFileSync('./index.mobile.js'))\n\n// Note that browsers (and ssri) will succeed as long as ONE of the entries\n// for the *prioritized* algorithm succeeds. That is, in order for this fallback\n// to work, both desktop and mobile *must* use the same `algorithm` values.\ndesktopIntegrity.concat(mobileIntegrity)\n```\n\n#### <a name=\"integrity-to-string\"></a> `> Integrity#toString([opts]) -> String`\n\nReturns the string representation of an `Integrity` object. All hash entries\nwill be concatenated in the string by `opts.sep`, which defaults to `' '`.\n\nIf you want to serialize an object that didn't come from an `ssri` function,\nuse [`ssri.stringify()`](#stringify).\n\nIf `opts.strict` is true, the integrity string will be created using strict\nparsing rules. See [`ssri.parse`](#parse).\n\n##### Example\n\n```javascript\nconst integrity = 'sha512-9KhgCRIx/AmzC8xqYJTZRrnO8OW2Pxyl2DIMZSBOr0oDvtEFyht3xpp71j/r/pAe1DM+JI/A+line3jUBgzQ7A==?foo'\n\nssri.parse(integrity).toString() === integrity\n```\n\n#### <a name=\"integrity-to-json\"></a> `> Integrity#toJSON() -> String`\n\nReturns the string representation of an `Integrity` object. All hash entries\nwill be concatenated in the string by `' '`.\n\nThis is a convenience method so you can pass an `Integrity` object directly to `JSON.stringify`.\nFor more info check out [toJSON() behavior on mdn](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#toJSON%28%29_behavior).\n\n##### Example\n\n```javascript\nconst integrity = '\"sha512-9KhgCRIx/AmzC8xqYJTZRrnO8OW2Pxyl2DIMZSBOr0oDvtEFyht3xpp71j/r/pAe1DM+JI/A+line3jUBgzQ7A==?foo\"'\n\nJSON.stringify(ssri.parse(integrity)) === integrity\n```\n\n#### <a name=\"integrity-match\"></a> `> Integrity#match(sri, [opts]) -> Hash | false`\n\nReturns the matching (truthy) hash if `Integrity` matches the argument passed as\n`sri`, which can be anything that [`parse`](#parse) will accept. `opts` will be\npassed through to `parse` and [`pickAlgorithm()`](#integrity-pick-algorithm).\n\n##### Example\n\n```javascript\nconst integrity = 'sha512-9KhgCRIx/AmzC8xqYJTZRrnO8OW2Pxyl2DIMZSBOr0oDvtEFyht3xpp71j/r/pAe1DM+JI/A+line3jUBgzQ7A=='\n\nssri.parse(integrity).match(integrity)\n// Hash {\n//   digest: '9KhgCRIx/AmzC8xqYJTZRrnO8OW2Pxyl2DIMZSBOr0oDvtEFyht3xpp71j/r/pAe1DM+JI/A+line3jUBgzQ7A=='\n//   algorithm: 'sha512'\n// }\n\nssri.parse(integrity).match('sha1-deadbeef')\n// false\n```\n\n#### <a name=\"integrity-pick-algorithm\"></a> `> Integrity#pickAlgorithm([opts]) -> String`\n\nReturns the \"best\" algorithm from those available in the integrity object.\n\nIf `opts.pickAlgorithm` is provided, it will be passed two algorithms as\narguments. ssri will prioritize whichever of the two algorithms is returned by\nthis function. Note that the function may be called multiple times, and it\n**must** return one of the two algorithms provided. By default, ssri will make\na best-effort to pick the strongest/most reliable of the given algorithms. It\nmay intentionally deprioritize algorithms with known vulnerabilities.\n\n##### Example\n\n```javascript\nssri.parse('sha1-WEakDigEST sha512-yzd8ELD1piyANiWnmdnpCL5F52f10UfUdEkHywVZeqTt0ymgrxR63Qz0GB7TKPoeeZQmWCaz7T1').pickAlgorithm() // sha512\n```\n\n#### <a name=\"integrity-hex-digest\"></a> `> Integrity#hexDigest() -> String`\n\n`Integrity` is assumed to be either a single-hash `Integrity` instance, or a\n`Hash` instance. Returns its `digest`, converted to a hex representation of the\nbase64 data.\n\n##### Example\n\n```javascript\nssri.parse('sha1-deadbeef').hexDigest() // '75e69d6de79f'\n```\n\n#### <a name=\"from-hex\"></a> `> ssri.fromHex(hexDigest, algorithm, [opts]) -> Integrity`\n\nCreates an `Integrity` object with a single entry, based on a hex-formatted\nhash. This is a utility function to help convert existing shasums to the\nIntegrity format, and is roughly equivalent to something like:\n\n```javascript\nalgorithm + '-' + Buffer.from(hexDigest, 'hex').toString('base64')\n```\n\n`opts.options` may optionally be passed in: it must be an array of option\nstrings that will be added to all generated integrity hashes generated by\n`fromData`. This is a loosely-specified feature of SRIs, and currently has no\nspecified semantics besides being `?`-separated. Use at your own risk, and\nprobably avoid if your integrity strings are meant to be used with browsers.\n\nIf `opts.strict` is true, the integrity object will be created using strict\nparsing rules. See [`ssri.parse`](#parse).\n\nIf `opts.single` is true, a single `Hash` object will be returned.\n\n##### Example\n\n```javascript\nssri.fromHex('75e69d6de79f', 'sha1').toString() // 'sha1-deadbeef'\n```\n\n#### <a name=\"from-data\"></a> `> ssri.fromData(data, [opts]) -> Integrity`\n\nCreates an `Integrity` object from either string or `Buffer` data, calculating\nall the requested hashes and adding any specified options to the object.\n\n`opts.algorithms` determines which algorithms to generate hashes for. All\nresults will be included in a single `Integrity` object. The default value for\n`opts.algorithms` is `['sha512']`. All algorithm strings must be hashes listed\nin `crypto.getHashes()` for the host Node.js platform.\n\n`opts.options` may optionally be passed in: it must be an array of option\nstrings that will be added to all generated integrity hashes generated by\n`fromData`. This is a loosely-specified feature of SRIs, and currently has no\nspecified semantics besides being `?`-separated. Use at your own risk, and\nprobably avoid if your integrity strings are meant to be used with browsers.\n\nIf `opts.strict` is true, the integrity object will be created using strict\nparsing rules. See [`ssri.parse`](#parse).\n\n##### Example\n\n```javascript\nconst integrityObj = ssri.fromData('foobarbaz', {\n  algorithms: ['sha256', 'sha384', 'sha512']\n})\nintegrity.toString('\\n')\n// ->\n// sha256-l981iLWj8kurw4UbNy8Lpxqdzd7UOxS50Glhv8FwfZ0=\n// sha384-irnCxQ0CfQhYGlVAUdwTPC9bF3+YWLxlaDGM4xbYminxpbXEq+D+2GCEBTxcjES9\n// sha512-yzd8ELD1piyANiWnmdnpCL5F52f10UfUdEkHywVZeqTt0ymgrxR63Qz0GB7TKPoeeZQmWCaz7T1+9vBnypkYWg==\n```\n\n#### <a name=\"from-stream\"></a> `> ssri.fromStream(stream, [opts]) -> Promise<Integrity>`\n\nReturns a Promise of an Integrity object calculated by reading data from\na given `stream`.\n\nIt accepts both `opts.algorithms` and `opts.options`, which are documented as\npart of [`ssri.fromData`](#from-data).\n\nAdditionally, `opts.Promise` may be passed in to inject a Promise library of\nchoice. By default, ssri will use Node's built-in Promises.\n\nIf `opts.strict` is true, the integrity object will be created using strict\nparsing rules. See [`ssri.parse`](#parse).\n\n##### Example\n\n```javascript\nssri.fromStream(fs.createReadStream('index.js'), {\n  algorithms: ['sha1', 'sha512']\n}).then(integrity => {\n  return ssri.checkStream(fs.createReadStream('index.js'), integrity)\n}) // succeeds\n```\n\n#### <a name=\"create\"></a> `> ssri.create([opts]) -> <Hash>`\n\nReturns a Hash object with `update(<Buffer or string>[,enc])` and `digest()` methods.\n\n\nThe Hash object provides the same methods as [crypto class Hash](https://nodejs.org/dist/latest-v6.x/docs/api/crypto.html#crypto_class_hash).\n`digest()` accepts no arguments and returns an Integrity object calculated by reading data from\ncalls to update.\n\nIt accepts both `opts.algorithms` and `opts.options`, which are documented as\npart of [`ssri.fromData`](#from-data).\n\nIf `opts.strict` is true, the integrity object will be created using strict\nparsing rules. See [`ssri.parse`](#parse).\n\n##### Example\n\n```javascript\nconst integrity = ssri.create().update('foobarbaz').digest()\nintegrity.toString()\n// ->\n// sha512-yzd8ELD1piyANiWnmdnpCL5F52f10UfUdEkHywVZeqTt0ymgrxR63Qz0GB7TKPoeeZQmWCaz7T1+9vBnypkYWg==\n```\n\n#### <a name=\"check-data\"></a> `> ssri.checkData(data, sri, [opts]) -> Hash|false`\n\nVerifies `data` integrity against an `sri` argument. `data` may be either a\n`String` or a `Buffer`, and `sri` can be any subresource integrity\nrepresentation that [`ssri.parse`](#parse) can handle.\n\nIf verification succeeds, `checkData` will return the name of the algorithm that\nwas used for verification (a truthy value). Otherwise, it will return `false`.\n\nIf `opts.pickAlgorithm` is provided, it will be used by\n[`Integrity#pickAlgorithm`](#integrity-pick-algorithm) when deciding which of\nthe available digests to match against.\n\nIf `opts.error` is true, and verification fails, `checkData` will throw either\nan `EBADSIZE` or an `EINTEGRITY` error, instead of just returning false.\n\n##### Example\n\n```javascript\nconst data = fs.readFileSync('index.js')\nssri.checkData(data, ssri.fromData(data)) // -> 'sha512'\nssri.checkData(data, 'sha256-l981iLWj8kurw4UbNy8Lpxqdzd7UOxS50Glhv8FwfZ0')\nssri.checkData(data, 'sha1-BaDDigEST') // -> false\nssri.checkData(data, 'sha1-BaDDigEST', {error: true}) // -> Error! EINTEGRITY\n```\n\n#### <a name=\"check-stream\"></a> `> ssri.checkStream(stream, sri, [opts]) -> Promise<Hash>`\n\nVerifies the contents of `stream` against an `sri` argument. `stream` will be\nconsumed in its entirety by this process. `sri` can be any subresource integrity\nrepresentation that [`ssri.parse`](#parse) can handle.\n\n`checkStream` will return a Promise that either resolves to the\n`Hash` that succeeded verification, or, if the verification fails\nor an error happens with `stream`, the Promise will be rejected.\n\nIf the Promise is rejected because verification failed, the returned error will\nhave `err.code` as `EINTEGRITY`.\n\nIf `opts.size` is given, it will be matched against the stream size. An error\nwith `err.code` `EBADSIZE` will be returned by a rejection if the expected size\nand actual size fail to match.\n\nIf `opts.pickAlgorithm` is provided, it will be used by\n[`Integrity#pickAlgorithm`](#integrity-pick-algorithm) when deciding which of\nthe available digests to match against.\n\n##### Example\n\n```javascript\nconst integrity = ssri.fromData(fs.readFileSync('index.js'))\n\nssri.checkStream(\n  fs.createReadStream('index.js'),\n  integrity\n)\n// ->\n// Promise<{\n//   algorithm: 'sha512',\n//   digest: 'sha512-yzd8ELD1piyANiWnmdnpCL5F52f10UfUdEkHywVZeqTt0ymgrxR63Qz0GB7TKPoeeZQmWCaz7T1'\n// }>\n\nssri.checkStream(\n  fs.createReadStream('index.js'),\n  'sha256-l981iLWj8kurw4UbNy8Lpxqdzd7UOxS50Glhv8FwfZ0'\n) // -> Promise<Hash>\n\nssri.checkStream(\n  fs.createReadStream('index.js'),\n  'sha1-BaDDigEST'\n) // -> Promise<Error<{code: 'EINTEGRITY'}>>\n```\n\n#### <a name=\"integrity-stream\"></a> `> integrityStream([opts]) -> IntegrityStream`\n\nReturns a `Transform` stream that data can be piped through in order to generate\nand optionally check data integrity for piped data. When the stream completes\nsuccessfully, it emits `size` and `integrity` events, containing the total\nnumber of bytes processed and a calculated `Integrity` instance based on stream\ndata, respectively.\n\nIf `opts.algorithms` is passed in, the listed algorithms will be calculated when\ngenerating the final `Integrity` instance. The default is `['sha512']`.\n\nIf `opts.single` is passed in, a single `Hash` instance will be returned.\n\nIf `opts.integrity` is passed in, it should be an `integrity` value understood\nby [`parse`](#parse) that the stream will check the data against. If\nverification succeeds, the integrity stream will emit a `verified` event whose\nvalue is a single `Hash` object that is the one that succeeded verification. If\nverification fails, the stream will error with an `EINTEGRITY` error code.\n\nIf `opts.size` is given, it will be matched against the stream size. An error\nwith `err.code` `EBADSIZE` will be emitted by the stream if the expected size\nand actual size fail to match.\n\nIf `opts.pickAlgorithm` is provided, it will be passed two algorithms as\narguments. ssri will prioritize whichever of the two algorithms is returned by\nthis function. Note that the function may be called multiple times, and it\n**must** return one of the two algorithms provided. By default, ssri will make\na best-effort to pick the strongest/most reliable of the given algorithms. It\nmay intentionally deprioritize algorithms with known vulnerabilities.\n\n##### Example\n\n```javascript\nconst integrity = ssri.fromData(fs.readFileSync('index.js'))\nfs.createReadStream('index.js')\n.pipe(ssri.integrityStream({integrity}))\n```\n", "licenseText": "ISC License\n\nCopyright (c) npm, Inc.\n\nPermission to use, copy, modify, and/or distribute this software for\nany purpose with or without fee is hereby granted, provided that the\nabove copyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE COPYRIGHT HOLDER DISCLAIMS\nALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED\nWARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE\nCOPYRIGHT HOLDER BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR\nCONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS\nOF USE, <PERSON><PERSON><PERSON> OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE\nOR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE\nUSE OR PERFORMANCE OF THIS SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/ssri/-/ssri-6.0.1.tgz#2a3c41b28dd45b62b63676ecb74001265ae9edd8", "type": "tarball", "reference": "https://registry.yarnpkg.com/ssri/-/ssri-6.0.1.tgz", "hash": "2a3c41b28dd45b62b63676ecb74001265ae9edd8", "integrity": "sha512-3Wge10hNcT1Kur4PDFwEieXSCMCJs/7WvSACcrMYrNp+b8kDL1/0wJch5Ni2WrtwEa2IO8OsVfeKIciKCDx/QA==", "registry": "npm", "packageName": "ssri", "cacheIntegrity": "sha512-3Wge10hNcT1Kur4PDFwEieXSCMCJs/7WvSACcrMYrNp+b8kDL1/0wJch5Ni2WrtwEa2IO8OsVfeKIciKCDx/QA== sha1-KjxBso3UW2K2Nnbst0ABJlrp7dg="}, "registry": "npm", "hash": "2a3c41b28dd45b62b63676ecb74001265ae9edd8"}