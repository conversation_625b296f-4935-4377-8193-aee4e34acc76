{"manifest": {"name": "content-type", "description": "Create and parse HTTP Content-Type header", "version": "1.0.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["content-type", "http", "req", "res", "rfc7231"], "repository": {"type": "git", "url": "https://github.com/jshttp/content-type.git"}, "devDependencies": {"eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-node": "5.1.1", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "~1.21.5"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-content-type-1.0.4-e138cc75e040c727b1966fe5e5f8c9aee256fe3b-integrity\\node_modules\\content-type\\package.json", "readmeFilename": "README.md", "readme": "# content-type\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nCreate and parse HTTP Content-Type header according to RFC 7231\n\n## Installation\n\n```sh\n$ npm install content-type\n```\n\n## API\n\n```js\nvar contentType = require('content-type')\n```\n\n### contentType.parse(string)\n\n```js\nvar obj = contentType.parse('image/svg+xml; charset=utf-8')\n```\n\nParse a content type string. This will return an object with the following\nproperties (examples are shown for the string `'image/svg+xml; charset=utf-8'`):\n\n - `type`: The media type (the type and subtype, always lower case).\n   Example: `'image/svg+xml'`\n\n - `parameters`: An object of the parameters in the media type (name of parameter\n   always lower case). Example: `{charset: 'utf-8'}`\n\nThrows a `TypeError` if the string is missing or invalid.\n\n### contentType.parse(req)\n\n```js\nvar obj = contentType.parse(req)\n```\n\nParse the `content-type` header from the given `req`. Short-cut for\n`contentType.parse(req.headers['content-type'])`.\n\nThrows a `TypeError` if the `Content-Type` header is missing or invalid.\n\n### contentType.parse(res)\n\n```js\nvar obj = contentType.parse(res)\n```\n\nParse the `content-type` header set on the given `res`. Short-cut for\n`contentType.parse(res.getHeader('content-type'))`.\n\nThrows a `TypeError` if the `Content-Type` header is missing or invalid.\n\n### contentType.format(obj)\n\n```js\nvar str = contentType.format({type: 'image/svg+xml'})\n```\n\nFormat an object into a content type string. This will return a string of the\ncontent type for the given object with the following properties (examples are\nshown that produce the string `'image/svg+xml; charset=utf-8'`):\n\n - `type`: The media type (will be lower-cased). Example: `'image/svg+xml'`\n\n - `parameters`: An object of the parameters in the media type (name of the\n   parameter will be lower-cased). Example: `{charset: 'utf-8'}`\n\nThrows a `TypeError` if the object contains an invalid type or parameter names.\n\n## License\n\n[MIT](LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/content-type.svg\n[npm-url]: https://npmjs.org/package/content-type\n[node-version-image]: https://img.shields.io/node/v/content-type.svg\n[node-version-url]: http://nodejs.org/download/\n[travis-image]: https://img.shields.io/travis/jshttp/content-type/master.svg\n[travis-url]: https://travis-ci.org/jshttp/content-type\n[coveralls-image]: https://img.shields.io/coveralls/jshttp/content-type/master.svg\n[coveralls-url]: https://coveralls.io/r/jshttp/content-type\n[downloads-image]: https://img.shields.io/npm/dm/content-type.svg\n[downloads-url]: https://npmjs.org/package/content-type\n", "licenseText": "(The MIT License)\n\nCopyright (c) 2015 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/content-type/-/content-type-1.0.4.tgz#e138cc75e040c727b1966fe5e5f8c9aee256fe3b", "type": "tarball", "reference": "https://registry.yarnpkg.com/content-type/-/content-type-1.0.4.tgz", "hash": "e138cc75e040c727b1966fe5e5f8c9aee256fe3b", "integrity": "sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==", "registry": "npm", "packageName": "content-type", "cacheIntegrity": "sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA== sha1-4TjMdeBAxyexlm/l5fjJruJW/js="}, "registry": "npm", "hash": "e138cc75e040c727b1966fe5e5f8c9aee256fe3b"}