{"manifest": {"name": "mv", "version": "2.1.1", "description": "fs.rename but works across devices. same as the unix utility 'mv'", "main": "index.js", "scripts": {"test": "mocha test/test.js --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/andrewrk/node-mv.git"}, "keywords": ["mv", "move", "rename", "device", "recursive", "folder"], "author": {"name": "<PERSON>"}, "license": "MIT", "engines": {"node": ">=0.8.0"}, "devDependencies": {"mocha": "~2.2.5"}, "dependencies": {"mkdirp": "~0.5.1", "ncp": "~2.0.0", "rimraf": "~2.4.0"}, "bugs": {"url": "https://github.com/andrewrk/node-mv/issues"}, "homepage": "https://github.com/andrewrk/node-mv", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-mv-2.1.1-ae6ce0d6f6d5e0a4f7d893798d03c1ea9559b6a2-integrity\\node_modules\\mv\\package.json", "readmeFilename": "README.md", "readme": "[![Build Status](https://secure.travis-ci.org/andrewrk/node-mv.png)](http://travis-ci.org/andrewrk/node-mv)\n\nUsage:\n------\n\n```js\nvar mv = require('mv');\n\nmv('source/file', 'dest/file', function(err) {\n  // done. it tried fs.rename first, and then falls back to\n  // piping the source file to the dest file and then unlinking\n  // the source file.\n});\n```\n\nAnother example:\n\n```js\nmv('source/dir', 'dest/a/b/c/dir', {mkdirp: true}, function(err) {\n  // done. it first created all the necessary directories, and then\n  // tried fs.rename, then falls back to using ncp to copy the dir\n  // to dest and then rimraf to remove the source dir\n});\n```\n\nAnother example:\n\n```js\nmv('source/file', 'dest/file', {clobber: false}, function(err) {\n  // done. If 'dest/file' exists, an error is returned\n  // with err.code === 'EEXIST'.\n});\n```\n", "licenseText": "Copyright (c) 2014 <PERSON>\n\nPermission is hereby granted, free of charge, to any person\nobtaining a copy of this software and associated documentation files\n(the \"Software\"), to deal in the Software without restriction,\nincluding without limitation the rights to use, copy, modify, merge,\npublish, distribute, sublicense, and/or sell copies of the Software,\nand to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS\nBE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\nACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/mv/-/mv-2.1.1.tgz#ae6ce0d6f6d5e0a4f7d893798d03c1ea9559b6a2", "type": "tarball", "reference": "https://registry.yarnpkg.com/mv/-/mv-2.1.1.tgz", "hash": "ae6ce0d6f6d5e0a4f7d893798d03c1ea9559b6a2", "integrity": "sha1-rmzg1vbV4KT32JN5jQPB6pVZtqI=", "registry": "npm", "packageName": "mv", "cacheIntegrity": "sha512-at/ZndSy3xEGJ8i0ygALh8ru9qy7gWW1cmkaqBN29JmMlIvM//MEO9y1sk/avxuwnPcfhkejkLsuPxH81BrkSg== sha1-rmzg1vbV4KT32JN5jQPB6pVZtqI="}, "registry": "npm", "hash": "ae6ce0d6f6d5e0a4f7d893798d03c1ea9559b6a2"}