{"manifest": {"name": "param-case", "version": "2.1.1", "description": "Param case a string", "main": "param-case.js", "typings": "param-case.d.ts", "files": ["param-case.js", "param-case.d.ts", "LICENSE"], "scripts": {"lint": "standard", "test-std": "mocha -- -R spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail", "test": "npm run lint && npm run test-cov"}, "repository": {"type": "git", "url": "git://github.com/blakeembrey/param-case.git"}, "keywords": ["param", "case", "dash", "hyphen"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/blakeembrey/param-case/issues"}, "homepage": "https://github.com/blakeembrey/param-case", "devDependencies": {"istanbul": "^0.4.3", "mocha": "^3.2.0", "standard": "^9.0.1"}, "dependencies": {"no-case": "^2.2.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-param-case-2.1.1-df94fd8cf6531ecf75e6bef9a0858fbc72be2247-integrity\\node_modules\\param-case\\package.json", "readmeFilename": "README.md", "readme": "# Param Case\n\n[![NPM version][npm-image]][npm-url]\n[![NPM downloads][downloads-image]][downloads-url]\n[![Build status][travis-image]][travis-url]\n[![Test coverage][coveralls-image]][coveralls-url]\n\nParam case a string.\n\nSupports Unicode (non-ASCII characters) and non-string entities, such as objects with a `toString` property, numbers and booleans. Empty values (`null` and `undefined`) will result in an empty string.\n\n## Installation\n\n```\nnpm install param-case --save\n```\n\n## Usage\n\n```javascript\nvar paramCase = require('param-case')\n\nparamCase('string')        //=> \"string\"\nparamCase('camelCase')     //=> \"camel-case\"\nparamCase('sentence case') //=> \"sentence-case\"\n\nparamCase('MY STRING', 'tr') //=> \"my-strıng\"\n```\n\n## Typings\n\nIncludes a [TypeScript definition](param-case.d.ts).\n\n## License\n\nMIT\n\n[npm-image]: https://img.shields.io/npm/v/param-case.svg?style=flat\n[npm-url]: https://npmjs.org/package/param-case\n[downloads-image]: https://img.shields.io/npm/dm/param-case.svg?style=flat\n[downloads-url]: https://npmjs.org/package/param-case\n[travis-image]: https://img.shields.io/travis/blakeembrey/param-case.svg?style=flat\n[travis-url]: https://travis-ci.org/blakeembrey/param-case\n[coveralls-image]: https://img.shields.io/coveralls/blakeembrey/param-case.svg?style=flat\n[coveralls-url]: https://coveralls.io/r/blakeembrey/param-case?branch=master\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 <PERSON> (<EMAIL>)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/param-case/-/param-case-2.1.1.tgz#df94fd8cf6531ecf75e6bef9a0858fbc72be2247", "type": "tarball", "reference": "https://registry.yarnpkg.com/param-case/-/param-case-2.1.1.tgz", "hash": "df94fd8cf6531ecf75e6bef9a0858fbc72be2247", "integrity": "sha1-35T9jPZTHs915r75oIWPvHK+Ikc=", "registry": "npm", "packageName": "param-case", "cacheIntegrity": "sha512-eQE845L6ot89sk2N8liD8HAuH4ca6Vvr7VWAWwt7+kvvG5aBcPmmphQ68JsEG2qa9n1TykS2DLeMt363AAH8/w== sha1-35T9jPZTHs915r75oIWPvHK+Ikc="}, "registry": "npm", "hash": "df94fd8cf6531ecf75e6bef9a0858fbc72be2247"}