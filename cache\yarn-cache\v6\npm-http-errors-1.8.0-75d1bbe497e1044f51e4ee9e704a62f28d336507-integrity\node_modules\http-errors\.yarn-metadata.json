{"manifest": {"name": "http-errors", "description": "Create HTTP error objects", "version": "1.8.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/http-errors.git"}, "dependencies": {"depd": "~1.1.2", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "devDependencies": {"eslint": "6.8.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.22.0", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "mocha": "8.0.1", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md . && node ./scripts/lint-readme-list.js", "test": "mocha --reporter spec --bail", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "keywords": ["http", "error"], "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-http-errors-1.8.0-75d1bbe497e1044f51e4ee9e704a62f28d336507-integrity\\node_modules\\http-errors\\package.json", "readmeFilename": "README.md", "readme": "# http-errors\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][node-url]\n[![Node.js Version][node-image]][node-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nCreate HTTP errors for Express, Koa, Connect, etc. with ease.\n\n## Install\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```bash\n$ npm install http-errors\n```\n\n## Example\n\n```js\nvar createError = require('http-errors')\nvar express = require('express')\nvar app = express()\n\napp.use(function (req, res, next) {\n  if (!req.user) return next(createError(401, 'Please login to view this page.'))\n  next()\n})\n```\n\n## API\n\nThis is the current API, currently extracted from Koa and subject to change.\n\n### Error Properties\n\n- `expose` - can be used to signal if `message` should be sent to the client,\n  defaulting to `false` when `status` >= 500\n- `headers` - can be an object of header names to values to be sent to the\n  client, defaulting to `undefined`. When defined, the key names should all\n  be lower-cased\n- `message` - the traditional error message, which should be kept short and all\n  single line\n- `status` - the status code of the error, mirroring `statusCode` for general\n  compatibility\n- `statusCode` - the status code of the error, defaulting to `500`\n\n### createError([status], [message], [properties])\n\nCreate a new error object with the given message `msg`.\nThe error object inherits from `createError.HttpError`.\n\n<!-- eslint-disable no-undef, no-unused-vars -->\n\n```js\nvar err = createError(404, 'This video does not exist!')\n```\n\n- `status: 500` - the status code as a number\n- `message` - the message of the error, defaulting to node's text for that status code.\n- `properties` - custom properties to attach to the object\n\n### createError([status], [error], [properties])\n\nExtend the given `error` object with `createError.HttpError`\nproperties. This will not alter the inheritance of the given\n`error` object, and the modified `error` object is the\nreturn value.\n\n<!-- eslint-disable no-redeclare, no-undef, no-unused-vars -->\n\n```js\nfs.readFile('foo.txt', function (err, buf) {\n  if (err) {\n    if (err.code === 'ENOENT') {\n      var httpError = createError(404, err, { expose: false })\n    } else {\n      var httpError = createError(500, err)\n    }\n  }\n})\n```\n\n- `status` - the status code as a number\n- `error` - the error object to extend\n- `properties` - custom properties to attach to the object\n\n### createError.isHttpError(val)\n\nDetermine if the provided `val` is an `HttpError`. This will return `true`\nif the error inherits from the `HttpError` constructor of this module or\nmatches the \"duck type\" for an error this module creates. All outputs from\nthe `createError` factory will return `true` for this function, including\nif an non-`HttpError` was passed into the factory.\n\n### new createError\\[code || name\\](\\[msg]\\))\n\nCreate a new error object with the given message `msg`.\nThe error object inherits from `createError.HttpError`.\n\n<!-- eslint-disable no-undef, no-unused-vars -->\n\n```js\nvar err = new createError.NotFound()\n```\n\n- `code` - the status code as a number\n- `name` - the name of the error as a \"bumpy case\", i.e. `NotFound` or `InternalServerError`.\n\n#### List of all constructors\n\n|Status Code|Constructor Name             |\n|-----------|-----------------------------|\n|400        |BadRequest                   |\n|401        |Unauthorized                 |\n|402        |PaymentRequired              |\n|403        |Forbidden                    |\n|404        |NotFound                     |\n|405        |MethodNotAllowed             |\n|406        |NotAcceptable                |\n|407        |ProxyAuthenticationRequired  |\n|408        |RequestTimeout               |\n|409        |Conflict                     |\n|410        |Gone                         |\n|411        |LengthRequired               |\n|412        |PreconditionFailed           |\n|413        |PayloadTooLarge              |\n|414        |URITooLong                   |\n|415        |UnsupportedMediaType         |\n|416        |RangeNotSatisfiable          |\n|417        |ExpectationFailed            |\n|418        |ImATeapot                    |\n|421        |MisdirectedRequest           |\n|422        |UnprocessableEntity          |\n|423        |Locked                       |\n|424        |FailedDependency             |\n|425        |UnorderedCollection          |\n|426        |UpgradeRequired              |\n|428        |PreconditionRequired         |\n|429        |TooManyRequests              |\n|431        |RequestHeaderFieldsTooLarge  |\n|451        |UnavailableForLegalReasons   |\n|500        |InternalServerError          |\n|501        |NotImplemented               |\n|502        |BadGateway                   |\n|503        |ServiceUnavailable           |\n|504        |GatewayTimeout               |\n|505        |HTTPVersionNotSupported      |\n|506        |VariantAlsoNegotiates        |\n|507        |InsufficientStorage          |\n|508        |LoopDetected                 |\n|509        |BandwidthLimitExceeded       |\n|510        |NotExtended                  |\n|511        |NetworkAuthenticationRequired|\n\n## License\n\n[MIT](LICENSE)\n\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/http-errors/master\n[coveralls-url]: https://coveralls.io/r/jshttp/http-errors?branch=master\n[node-image]: https://badgen.net/npm/node/http-errors\n[node-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/http-errors\n[npm-url]: https://npmjs.org/package/http-errors\n[npm-version-image]: https://badgen.net/npm/v/http-errors\n[travis-image]: https://badgen.net/travis/jshttp/http-errors/master\n[travis-url]: https://travis-ci.org/jshttp/http-errors\n", "licenseText": "\nThe MIT License (MIT)\n\nCopyright (c) 2014 <PERSON> Ong <EMAIL>\nCopyright (c) 2016 <PERSON> <EMAIL>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/http-errors/-/http-errors-1.8.0.tgz#75d1bbe497e1044f51e4ee9e704a62f28d336507", "type": "tarball", "reference": "https://registry.yarnpkg.com/http-errors/-/http-errors-1.8.0.tgz", "hash": "75d1bbe497e1044f51e4ee9e704a62f28d336507", "integrity": "sha512-4I8r0C5JDhT5VkvI47QktDW75rNlGVsUf/8hzjCC/wkWI/jdTRmBb9aI7erSG82r1bjKY3F6k28WnsVxB1C73A==", "registry": "npm", "packageName": "http-errors", "cacheIntegrity": "sha512-4I8r0C5JDhT5VkvI47QktDW75rNlGVsUf/8hzjCC/wkWI/jdTRmBb9aI7erSG82r1bjKY3F6k28WnsVxB1C73A== sha1-ddG75JfhBE9R5O6ecEpi8o0zZQc="}, "registry": "npm", "hash": "75d1bbe497e1044f51e4ee9e704a62f28d336507"}