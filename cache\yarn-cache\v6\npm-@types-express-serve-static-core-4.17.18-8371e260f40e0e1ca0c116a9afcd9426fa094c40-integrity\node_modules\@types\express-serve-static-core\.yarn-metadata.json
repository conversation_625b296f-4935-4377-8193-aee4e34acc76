{"manifest": {"name": "@types/express-serve-static-core", "version": "4.17.18", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/19majkel94"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/kacepe"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/micksatana"}, {"name": "<PERSON>", "url": "https://github.com/samijaber"}, {"name": "<PERSON>", "url": "https://github.com/JoseLion"}, {"name": "<PERSON>", "url": "https://github.com/dwrss"}, {"name": "<PERSON>", "url": "https://github.com/andoshin11"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-serve-static-core"}, "scripts": {}, "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*"}, "typesPublisherContentHash": "60873d177898f88d23027366f6d52aec9d27759ca997fea4f7c6b9729356fc20", "typeScriptVersion": "3.3", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-@types-express-serve-static-core-4.17.18-8371e260f40e0e1ca0c116a9afcd9426fa094c40-integrity\\node_modules\\@types\\express-serve-static-core\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/express-serve-static-core`\n\n# Summary\nThis package contains type definitions for Express (http://expressjs.com).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core.\n\n### Additional Details\n * Last updated: Mon, 11 Jan 2021 22:13:26 GMT\n * Dependencies: [@types/range-parser](https://npmjs.com/package/@types/range-parser), [@types/qs](https://npmjs.com/package/@types/qs), [@types/node](https://npmjs.com/package/@types/node)\n * Global values: none\n\n# Credits\nThese definitions were written by [<PERSON>](https://github.com/b<PERSON><PERSON><PERSON>), [<PERSON><PERSON><PERSON>](https://github.com/19majkel94), [<PERSON><PERSON><PERSON>](https://github.com/kacepe), [<PERSON><PERSON>](https://github.com/micks<PERSON>na), [<PERSON>](https://github.com/samijaber), [<PERSON>](https://github.com/JoseLion), [<PERSON>](https://github.com/dwrss), and [<PERSON> Ando](https://github.com/andoshin11).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/express-serve-static-core/-/express-serve-static-core-4.17.18.tgz#8371e260f40e0e1ca0c116a9afcd9426fa094c40", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/express-serve-static-core/-/express-serve-static-core-4.17.18.tgz", "hash": "8371e260f40e0e1ca0c116a9afcd9426fa094c40", "integrity": "sha512-m4JTwx5RUBNZvky/JJ8swEJPKFd8si08pPF2PfizYjGZOKr/svUWPcoUmLow6MmPzhasphB7gSTINY67xn3JNA==", "registry": "npm", "packageName": "@types/express-serve-static-core", "cacheIntegrity": "sha512-m4JTwx5RUBNZvky/JJ8swEJPKFd8si08pPF2PfizYjGZOKr/svUWPcoUmLow6MmPzhasphB7gSTINY67xn3JNA== sha1-g3HiYPQODhygwRapr82UJvoJTEA="}, "registry": "npm", "hash": "8371e260f40e0e1ca0c116a9afcd9426fa094c40"}