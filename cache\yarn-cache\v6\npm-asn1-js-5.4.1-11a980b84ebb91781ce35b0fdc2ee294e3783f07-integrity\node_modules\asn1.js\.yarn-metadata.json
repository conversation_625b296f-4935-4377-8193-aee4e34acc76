{"manifest": {"name": "asn1.js", "version": "5.4.1", "description": "ASN.1 encoder and decoder", "main": "lib/asn1.js", "scripts": {"lint-2560": "eslint --fix rfc/2560/*.js rfc/2560/test/*.js", "lint-5280": "eslint --fix rfc/5280/*.js rfc/5280/test/*.js", "lint": "eslint --fix lib/*.js lib/**/*.js lib/**/**/*.js && npm run lint-2560 && npm run lint-5280", "test": "mocha --reporter spec test/*-test.js && cd rfc/2560 && npm i && npm test && cd ../../rfc/5280 && npm i && npm test && cd ../../ && npm run lint"}, "repository": {"type": "git", "url": "**************:indutny/asn1.js"}, "keywords": ["asn.1", "der"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/indutny/asn1.js/issues"}, "homepage": "https://github.com/indutny/asn1.js", "devDependencies": {"eslint": "^4.10.0", "mocha": "^7.0.0"}, "dependencies": {"bn.js": "^4.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0", "safer-buffer": "^2.1.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-asn1-js-5.4.1-11a980b84ebb91781ce35b0fdc2ee294e3783f07-integrity\\node_modules\\asn1.js\\package.json", "readmeFilename": "README.md", "readme": "# ASN1.js\n\nASN.1 DER Encoder/Decoder and DSL.\n\n## Example\n\nDefine model:\n\n```javascript\nvar asn = require('asn1.js');\n\nvar Human = asn.define('Human', function() {\n  this.seq().obj(\n    this.key('firstName').octstr(),\n    this.key('lastName').octstr(),\n    this.key('age').int(),\n    this.key('gender').enum({ 0: 'male', 1: 'female' }),\n    this.key('bio').seqof(Bio)\n  );\n});\n\nvar Bio = asn.define('Bio', function() {\n  this.seq().obj(\n    this.key('time').gentime(),\n    this.key('description').octstr()\n  );\n});\n```\n\nEncode data:\n\n```javascript\nvar output = Human.encode({\n  firstName: 'Thomas',\n  lastName: '<PERSON>',\n  age: 28,\n  gender: 'male',\n  bio: [\n    {\n      time: +new Date('31 March 1999'),\n      description: 'freedom of mind'\n    }\n  ]\n}, 'der');\n```\n\nDecode data:\n\n```javascript\nvar human = Human.decode(output, 'der');\nconsole.log(human);\n/*\n{ firstName: <Buffer 54 68 6f 6d 61 73>,\n  lastName: <Buffer 41 6e 64 65 72 73 6f 6e>,\n  age: 28,\n  gender: 'male',\n  bio:\n   [ { time: 922820400000,\n       description: <Buffer 66 72 65 65 64 6f 6d 20 6f 66 20 6d 69 6e 64> } ] }\n*/\n```\n\n### Partial decode\n\nIts possible to parse data without stopping on first error. In order to do it,\nyou should call:\n\n```javascript\nvar human = Human.decode(output, 'der', { partial: true });\nconsole.log(human);\n/*\n{ result: { ... },\n  errors: [ ... ] }\n*/\n```\n\n#### LICENSE\n\nThis software is licensed under the MIT License.\n\nCopyright Fedor Indutny, 2017.\n\nPermission is hereby granted, free of charge, to any person obtaining a\ncopy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to permit\npersons to whom the Software is furnished to do so, subject to the\nfollowing conditions:\n\nThe above copyright notice and this permission notice shall be included\nin all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\nOR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\nNO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\nDAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\nOTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\nUSE OR OTHER DEALINGS IN THE SOFTWARE.\n", "licenseText": "MIT License\n\nCopyright (c) 2017 Fedor Indu<PERSON>ny\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/asn1.js/-/asn1.js-5.4.1.tgz#11a980b84ebb91781ce35b0fdc2ee294e3783f07", "type": "tarball", "reference": "https://registry.yarnpkg.com/asn1.js/-/asn1.js-5.4.1.tgz", "hash": "11a980b84ebb91781ce35b0fdc2ee294e3783f07", "integrity": "sha512-+I//4cYPccV8LdmBLiX8CYvf9Sp3vQsrqu2QNXRcrbiWvcx/UdlFiqUJJzxRQxgsZmvhXhn4cSKeSmoFjVdupA==", "registry": "npm", "packageName": "asn1.js", "cacheIntegrity": "sha512-+I//4cYPccV8LdmBLiX8CYvf9Sp3vQsrqu2QNXRcrbiWvcx/UdlFiqUJJzxRQxgsZmvhXhn4cSKeSmoFjVdupA== sha1-EamAuE67kXgc41sP3C7ilON4Pwc="}, "registry": "npm", "hash": "11a980b84ebb91781ce35b0fdc2ee294e3783f07"}