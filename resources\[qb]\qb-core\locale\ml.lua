local Translations = {
    error = {
        not_online                  = 'പ്ലെയർ ഓൺലൈനല്ല',
        wrong_format                = 'തെറ്റായ ഫോർമാറ്റ്',
        missing_args                = 'എല്ലാ വാദങ്ങളും നൽകിയിട്ടില്ല (x, y, z)',
        missing_args2               = 'എല്ലാ വാദങ്ങളും പൂരിപ്പിക്കണം!',
        no_access                   = 'ഈ കമാൻഡിലേക്ക് പ്രവേശനമില്ല',
        company_too_poor            = 'നിങ്ങളുടെ തൊഴിലുടമ തകർന്നിരിക്കുന്നു',
        item_not_exist              = 'ഇനം നിലവിലില്ല',
        too_heavy                   = 'ഇൻവെൻ്ററി വളരെ നിറഞ്ഞിരിക്കുന്നു',
        location_not_exist          = 'സ്ഥാനം നിലവിലില്ല',
        duplicate_license           = '[QBCORE] - ഡ്യൂപ്ലിക്കേറ്റ് റോക്ക്സ്റ്റാർ ലൈസൻസ് കണ്ടെത്തി',
        no_valid_license            = '[QBCORE] - സാധുവായ റോക്ക്സ്റ്റാർ ലൈസൻസ് കണ്ടെത്തിയില്ല',
        not_whitelisted             = '[QBCORE] - ഈ സെർവറിനായി നിങ്ങളെ വൈറ്റ്‌ലിസ്റ്റ് ചെയ്‌തിട്ടില്ല',
        server_already_open         = 'സെർവർ ഇതിനകം തുറന്നിരിക്കുന്നു',
        server_already_closed       = 'സെർവർ ഇതിനകം അടച്ചിരിക്കുന്നു',
        no_permission               = 'നിങ്ങൾക്ക് ഇതിന് അനുമതിയില്ല..',
        no_waypoint                 = 'വേപോയിൻ്റ് സെറ്റ് ഇല്ല.',
        tp_error                    = 'ടെലിപോർട്ടിംഗ് സമയത്ത് പിശക്.',
        connecting_database_error   = '[QBCORE] - സെർവറിലേക്ക് കണക്‌റ്റ് ചെയ്യുമ്പോൾ ഒരു ഡാറ്റാബേസ് പിശക് സംഭവിച്ചു. (SQL സെർവർ ഓണാണോ?)',
        connecting_database_timeout = '[QBCORE] - ഡാറ്റാബേസിലേക്കുള്ള കണക്ഷൻ കാലഹരണപ്പെട്ടു. (SQL സെർവർ ഓണാണോ?)',
    },
    success = {
        server_opened = 'സെർവർ തുറന്നിരിക്കുന്നു',
        server_closed = 'സെർവർ അടച്ചു',
        teleported_waypoint = 'വേപോയിൻ്റിലേക്ക് ടെലിപോർട്ട് ചെയ്തു.',
    },
    info = {
        received_paycheck = 'നിങ്ങളുടെ ശമ്പളം നിങ്ങൾക്ക് ലഭിച്ചു $%{value}',
        job_info = 'ജോലി: %{value} | ഗ്രേഡ്: %{value2} | കടമ: %{value3}',
        gang_info = 'സംഘം: %{value} | ഗ്രേഡ്: %{value2}',
        on_duty = 'നിങ്ങൾ ഇപ്പോൾ ഡ്യൂട്ടിയിലാണ്!',
        off_duty = 'നിങ്ങൾ ഇപ്പോൾ ഡ്യൂട്ടിക്ക് പുറത്താണ്!',
        checking_ban = 'ഹലോ %s. നിങ്ങളെ നിരോധിച്ചിട്ടുണ്ടോ എന്ന് ഞങ്ങൾ പരിശോധിക്കുന്നു.',
        join_server = 'സ്വാഗതം %s {Server Name}.',
        checking_whitelisted = 'ഹലോ %s. ഞങ്ങൾ നിങ്ങളുടെ അലവൻസ് പരിശോധിക്കുന്നു.',
        exploit_banned = 'വഞ്ചനയ്ക്ക് നിങ്ങളെ വിലക്കിയിട്ടുണ്ട്. കൂടുതൽ വിവരങ്ങൾക്ക് ഞങ്ങളുടെ ഡിസ്കോർഡ് പരിശോധിക്കുക: %{discord}',
        exploit_dropped = 'ചൂഷണത്തിന് നിങ്ങളെ പുറത്താക്കി',
    },
    command = {
        tp = {
            help = 'ടിപി ടു പ്ലെയർ അല്ലെങ്കിൽ കോർഡുകൾ (അഡ്മിൻ മാത്രം)',
            params = {
                x = { name = 'id/x', help = 'കളിക്കാരൻ്റെ ഐഡി അല്ലെങ്കിൽ X സ്ഥാനം' },
                y = { name = 'y', help = 'Y സ്ഥാനം' },
                z = { name = 'z', help = 'Z സ്ഥാനം' },
            },
        },
        tpm = { help = 'TP മാർക്കറിലേക്ക് (അഡ്മിൻ മാത്രം)' },
        togglepvp = { help = 'സെർവറിൽ PVP ടോഗിൾ ചെയ്യുക (അഡ്മിൻ മാത്രം)' },
        addpermission = {
            help = 'കളിക്കാർക്ക് അനുമതി നൽകുക (ദൈവം മാത്രം)',
            params = {
                id = { name = 'id', help = 'കളിക്കാരൻ്റെ ഐഡി' },
                permission = { name = 'അനുമതി', help = 'അനുമതി നില' },
            },
        },
        removepermission = {
            help = 'കളിക്കാരുടെ അനുമതികൾ നീക്കം ചെയ്യുക (ദൈവം മാത്രം)',
            params = {
                id = { name = 'id', help = 'കളിക്കാരൻ്റെ ഐഡി' },
                permission = { name = 'അനുമതി', help = 'അനുമതി നില' },
            },
        },
        openserver = { help = 'എല്ലാവർക്കുമായി സെർവർ തുറക്കുക (അഡ്മിൻ മാത്രം)' },
        closeserver = {
            help = 'അനുമതിയില്ലാത്ത ആളുകൾക്കായി സെർവർ അടയ്ക്കുക (അഡ്മിൻ മാത്രം)',
            params = {
                reason = { name = 'കാരണം', help = 'അടയ്ക്കാനുള്ള കാരണം (ഓപ്ഷണൽ)' },
            },
        },
        car = {
            help = 'സ്പോൺ വെഹിക്കിൾ (അഡ്മിൻ മാത്രം)',
            params = {
                model = { name = 'മാതൃക', help = 'വാഹനത്തിൻ്റെ മോഡൽ പേര്' },
            },
        },
        dv = { help = 'വാഹനം ഇല്ലാതാക്കുക (അഡ്മിൻ മാത്രം)' },
        dvall = { help = 'എല്ലാ വാഹനങ്ങളും ഇല്ലാതാക്കുക (അഡ്മിൻ മാത്രം)' },
        dvp = { help = 'എല്ലാ പെഡുകളും ഇല്ലാതാക്കുക (അഡ്മിൻ മാത്രം)' },
        dvo = { help = 'എല്ലാ ഒബ്ജക്റ്റുകളും ഇല്ലാതാക്കുക (അഡ്മിൻ മാത്രം)' },
        givemoney = {
            help = 'ഒരു കളിക്കാരന് പണം നൽകുക (അഡ്മിൻ മാത്രം)',
            params = {
                id = { name = 'id', help = 'പ്ലെയർ ഐഡി' },
                moneytype = { name = 'പണത്തിൻ്റെ തരം', help = 'പണത്തിൻ്റെ തരം (പണം, ബാങ്ക്, ക്രിപ്റ്റോ)' },
                amount = { name = 'തുക', help = 'ആകെ തുക' },
            },
        },
        setmoney = {
            help = 'കളിക്കാരുടെ പണം സജ്ജീകരിക്കുക (അഡ്മിൻ മാത്രം)',
            params = {
                id = { name = 'id', help = 'പ്ലെയർ ഐഡി' },
                moneytype = { name = 'പണത്തിൻ്റെ തരം', help = 'പണത്തിൻ്റെ തരം (പണം, ബാങ്ക്, ക്രിപ്റ്റോ)' },
                amount = { name = 'തുക', help = 'ആകെ തുക' },
            },
        },
        job = { help = 'നിങ്ങളുടെ ജോലി പരിശോധിക്കുക' },
        setjob = {
            help = 'കളിക്കാരുടെ ജോലി സജ്ജീകരിക്കുക (അഡ്മിൻ മാത്രം)',
            params = {
                id = { name = 'id', help = 'പ്ലെയർ ഐഡി' },
                job = { name = 'ജോലി', help = 'ജോലിയുടെ പേര്' },
                grade = { name = 'ഗ്രേഡ്', help = 'ജോലി ഗ്രേഡ്' },
            },
        },
        gang = { help = 'നിങ്ങളുടെ സംഘത്തെ പരിശോധിക്കുക' },
        setgang = {
            help = 'ഒരു കളിക്കാരുടെ സംഘത്തെ സജ്ജമാക്കുക (അഡ്മിൻ മാത്രം)',
            params = {
                id = { name = 'id', help = 'പ്ലെയർ ഐഡി' },
                gang = { name = 'സംഘം', help = 'സംഘത്തിൻ്റെ പേര്' },
                grade = { name = 'ഗ്രേഡ്', help = 'ഗാംഗ് ഗ്രേഡ്' },
            },
        },
        ooc = { help = 'OOC ചാറ്റ് സന്ദേശം' },
        me = {
            help = 'പ്രാദേശിക സന്ദേശം കാണിക്കുക',
            params = {
                message = { name = 'സന്ദേശം', help = 'അയയ്ക്കാനുള്ള സന്ദേശം' }
            },
        },
    },
}

if GetConvar('qb_locale', 'en') == 'ml' then
    Lang = Locale:new({
        phrases = Translations,
        warnOnMissing = true,
        fallbackLang = Lang,
    })
end
