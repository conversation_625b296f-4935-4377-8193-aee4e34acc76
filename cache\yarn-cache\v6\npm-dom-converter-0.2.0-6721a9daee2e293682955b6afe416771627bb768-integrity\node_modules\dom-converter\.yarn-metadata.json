{"manifest": {"name": "dom-converter", "version": "0.2.0", "description": "converts bare objects to DOM objects or xml representations", "main": "lib/domConverter.js", "dependencies": {"utila": "~0.4"}, "devDependencies": {"chai": "^1.10.0", "chai-changes": "^1.3.4", "chai-fuzzy": "^1.4.0", "coffee-script": "^1.8.0", "jitter": "^1.3.0", "mocha": "^2.0.1", "mocha-pretty-spec-reporter": "0.1.0-beta.1", "sinon": "^1.12.2", "sinon-chai": "^2.6.0"}, "scripts": {"test": "mocha \"test/**/*.coffee\"", "test:watch": "mocha \"test/**/*.coffee\" --watch", "compile": "coffee --bare --compile --output ./lib ./src", "compile:watch": "jitter src lib -b", "watch": "npm run compile:watch & npm run test:watch", "winwatch": "start/b npm run compile:watch & npm run test:watch", "prepublish": "npm run compile"}, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/AriaMinaei/dom-converter"}, "bugs": {"url": "https://github.com/AriaMinaei/dom-converter/issues"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-dom-converter-0.2.0-6721a9daee2e293682955b6afe416771627bb768-integrity\\node_modules\\dom-converter\\package.json", "readmeFilename": "README.md", "readme": "Converts bare objects to DOM objects, compatible with htmlparser2's DOM objects.\n\nThis is useful when you want to work with DOM without having to compose/parse html.", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2013 Aria Minaei\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/dom-converter/-/dom-converter-0.2.0.tgz#6721a9daee2e293682955b6afe416771627bb768", "type": "tarball", "reference": "https://registry.yarnpkg.com/dom-converter/-/dom-converter-0.2.0.tgz", "hash": "6721a9daee2e293682955b6afe416771627bb768", "integrity": "sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA==", "registry": "npm", "packageName": "dom-converter", "cacheIntegrity": "sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA== sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g="}, "registry": "npm", "hash": "6721a9daee2e293682955b6afe416771627bb768"}