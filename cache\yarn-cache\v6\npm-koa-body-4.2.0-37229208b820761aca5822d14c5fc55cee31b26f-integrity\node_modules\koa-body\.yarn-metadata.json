{"manifest": {"name": "koa-body", "version": "4.2.0", "description": "A Koa body parser middleware. Supports multipart, urlencoded and JSON request bodies.", "main": "index.js", "types": "./index.d.ts", "scripts": {"test": "mocha test/unit/", "examples-multer": "node examples/multer.js", "examples-koa-router": "node examples/koa-router.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dlau"}, "repository": {"type": "git", "url": "git://github.com/dlau/koa-body.git"}, "keywords": ["koa", "u<PERSON><PERSON><PERSON>", "multipart", "json", "body", "parser", "form"], "files": ["LICENSE", "README.md", "index.js", "index.d.ts", "package.json", "unparsed.js"], "dependencies": {"@types/formidable": "^1.0.31", "co-body": "^5.1.1", "formidable": "^1.1.1"}, "devDependencies": {"@types/koa": "^2.0.39", "koa": "^2.0.0", "koa-router": "^7.0.1", "mocha": "5.2.0", "should": "13.2.1", "sinon": "^7.2.2", "supertest": "3.1.0"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dlau"}, {"name": "<PERSON><PERSON><PERSON> Reagent", "email": "<EMAIL>", "url": "https://github.com/tunnckoCore"}], "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-koa-body-4.2.0-37229208b820761aca5822d14c5fc55cee31b26f-integrity\\node_modules\\koa-body\\package.json", "readmeFilename": "README.md", "readme": "koa-body [![Build Status](https://travis-ci.org/dlau/koa-body.svg?branch=koa2)](https://travis-ci.org/dlau/koa-body) [![Dependencies Status](https://david-dm.org/dlau/koa-body/status.svg)](https://david-dm.org/dlau/koa-body) [![KoaJs Slack](https://img.shields.io/badge/Koa.Js-Slack%20Channel-Slack.svg?longCache=true)](https://communityinviter.com/apps/koa-js/koajs)\n================\n\n> A full-featured [`koa`](https://github.com/koajs/koa) body parser middleware. Supports `multipart`, `urlencoded`, and `json` request bodies. Provides the same functionality as Express's bodyParser - [`multer`](https://github.com/expressjs/multer).\n\n## Install\n>Install with [npm](https://github.com/npm/npm)\n\n```\nnpm install koa-body\n```\n\n## Features\n- can handle requests such as:\n  * **multipart/form-data**\n  * **application/x-www-urlencoded**\n  * **application/json**\n  * **application/json-patch+json**\n  * **application/vnd.api+json**\n  * **application/csp-report**\n  * **text/xml**\n- option for patch to Koa or Node, or either\n- file uploads\n- body, fields and files size limiting\n\n## Hello World - Quickstart\n\n```sh\nnpm install koa koa-body # Note that Koa requires Node.js 7.6.0+ for async/await support\n```\n\nindex.js:\n```js\nconst Koa = require('koa');\nconst koaBody = require('koa-body');\n\nconst app = new Koa();\n\napp.use(koaBody());\napp.use(ctx => {\n  ctx.body = `Request Body: ${JSON.stringify(ctx.request.body)}`;\n});\n\napp.listen(3000);\n```\n\n```sh\nnode index.js\ncurl -i http://localhost:3000/users -d \"name=test\"\n```\n\nOutput:\n```text\nHTTP/1.1 200 OK\nContent-Type: text/plain; charset=utf-8\nContent-Length: 29\nDate: Wed, 03 May 2017 02:09:44 GMT\nConnection: keep-alive\n\nRequest Body: {\"name\":\"test\"}%\n```\n\n**For a more comprehensive example, see** `examples/multipart.js`\n\n## Usage with [koa-router](https://github.com/alexmingoia/koa-router)\nIt's generally better to only parse the body as needed, if using a router that supports middleware composition, we can inject it only for certain routes.\n\n```js\nconst Koa = require('koa');\nconst app = new Koa();\nconst router = require('koa-router')();\nconst koaBody = require('koa-body');\n\nrouter.post('/users', koaBody(),\n  (ctx) => {\n    console.log(ctx.request.body);\n    // => POST body\n    ctx.body = JSON.stringify(ctx.request.body);\n  }\n);\n\napp.use(router.routes());\n\napp.listen(3000);\nconsole.log('curl -i http://localhost:3000/users -d \"name=test\"');\n```\n\n## Usage with unsupported text body type\nFor unsupported text body type, for example, `text/xml`, you can use the unparsed request body at `ctx.request.body`. For the text content type, the `includeUnparsed` setting is not required.\n\n```js\n// xml-parse.js:\nconst Koa = require('koa');\nconst koaBody = require('koa-body');\nconst convert = require('xml-js');\n\nconst app = new Koa();\n\napp.use(koaBody());\napp.use(ctx => {\n  const obj = convert.xml2js(ctx.request.body)\n  ctx.body = `Request Body: ${JSON.stringify(obj)}`;\n});\n\napp.listen(3000);\n```\n\n```sh\nnode xml-parse.js\ncurl -i http://localhost:3000/users -H \"Content-Type: text/xml\" -d '<?xml version=\"1.0\"?><catalog id=\"1\"></catalog>'\n```\n\nOutput:\n```text\nHTTP/1.1 200 OK\nContent-Type: text/plain; charset=utf-8\nContent-Length: 135\nDate: Tue, 09 Jun 2020 11:17:38 GMT\nConnection: keep-alive\n\nRequest Body: {\"declaration\":{\"attributes\":{\"version\":\"1.0\"}},\"elements\":[{\"type\":\"element\",\"name\":\"catalog\",\"attributes\":{\"id\":\"1\"}}]}%\n```\n\n## Options\n> Options available for `koa-body`. Four custom options, and others are from `raw-body` and `formidable`.\n\n- `patchNode` **{Boolean}** Patch request body to Node's `ctx.req`, default `false`\n- `patchKoa` **{Boolean}** Patch request body to Koa's `ctx.request`, default `true`\n- `jsonLimit` **{String|Integer}** The byte (if integer) limit of the JSON body, default `1mb`\n- `formLimit` **{String|Integer}** The byte (if integer) limit of the form body, default `56kb`\n- `textLimit` **{String|Integer}** The byte (if integer) limit of the text body, default `56kb`\n- `encoding` **{String}** Sets encoding for incoming form fields, default `utf-8`\n- `multipart` **{Boolean}** Parse multipart bodies, default `false`\n- `urlencoded` **{Boolean}** Parse urlencoded bodies, default `true`\n- `text` **{Boolean}** Parse text bodies, such as XML, default `true`\n- `json` **{Boolean}** Parse JSON bodies, default `true`\n- `jsonStrict` **{Boolean}** Toggles co-body strict mode; if set to true - only parses arrays or objects, default `true`\n- `includeUnparsed` **{Boolean}** Toggles co-body returnRawBody option; if set to true, for form encodedand and JSON requests the raw, unparsed requesty body will be attached to `ctx.request.body` using a `Symbol`, default `false`\n- `formidable` **{Object}** Options to pass to the formidable multipart parser\n- `onError` **{Function}** Custom error handle, if throw an error, you can customize the response - onError(error, context), default will throw\n- `strict` **{Boolean}** ***DEPRECATED*** If enabled, don't parse GET, HEAD, DELETE requests, default `true`\n- `parsedMethods` **{String[]}** Declares the HTTP methods where bodies will be parsed, default `['POST', 'PUT', 'PATCH']`. Replaces `strict` option.\n\n## A note about `parsedMethods`\n> see [http://tools.ietf.org/html/draft-ietf-httpbis-p2-semantics-19#section-6.3](http://tools.ietf.org/html/draft-ietf-httpbis-p2-semantics-19#section-6.3)\n- `GET`, `HEAD`, and `DELETE` requests have no defined semantics for the request body, but this doesn't mean they may not be valid in certain use cases.\n- koa-body is strict by default, parsing only `POST`, `PUT`, and `PATCH` requests\n\n## File Support\nUploaded files are accessible via `ctx.request.files`.\n\n## A note about unparsed request bodies\nSome applications require crytopgraphic verification of request bodies, for example webhooks from slack or stripe. The unparsed body can be accessed if `includeUnparsed` is `true` in koa-body's options. When enabled, import the symbol for accessing the request body from `unparsed = require('koa-body/unparsed.js')`, or define your own accessor using `unparsed = Symbol.for('unparsedBody')`. Then the unparsed body is available using `ctx.request.body[unparsed]`.\n\n## Some options for formidable\n> See [node-formidable](https://github.com/felixge/node-formidable) for a full list of options\n- `maxFields` **{Integer}** Limits the number of fields that the querystring parser will decode, default `1000`\n- `maxFieldsSize` **{Integer}** Limits the amount of memory all fields together (except files) can allocate in bytes. If this value is exceeded, an 'error' event is emitted, default `2mb (2 * 1024 * 1024)`\n- `uploadDir` **{String}** Sets the directory for placing file uploads in, default `os.tmpDir()`\n- `keepExtensions` **{Boolean}** Files written to `uploadDir` will include the extensions of the original files, default `false`\n- `hash` **{String}** If you want checksums calculated for incoming files, set this to either `'sha1'` or `'md5'`, default `false`\n- `multiples` **{Boolean}** Multiple file uploads or no, default `true`\n- `onFileBegin` **{Function}** Special callback on file begin. The function is executed directly by formidable. It can be used to rename files before saving them to disk. [See the docs](https://github.com/felixge/node-formidable#filebegin)\n\n## Changelog\nPlease see the [Changelog](./CHANGELOG.md) for a summary of changes.\n\n## Tests\n```\n$ npm test\n```\n\n## License\nThe MIT License, 2014 [Charlike Mike Reagent](https://github.com/tunnckoCore) ([@tunnckoCore](https://twitter.com/tunnckoCore)) and [Daryl Lau](https://github.com/dlau) ([@daryllau](https://twitter.com/daryllau))\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 Charlike Mike Reagent <<EMAIL>> and <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE."}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/koa-body/-/koa-body-4.2.0.tgz#37229208b820761aca5822d14c5fc55cee31b26f", "type": "tarball", "reference": "https://registry.yarnpkg.com/koa-body/-/koa-body-4.2.0.tgz", "hash": "37229208b820761aca5822d14c5fc55cee31b26f", "integrity": "sha512-wdGu7b9amk4Fnk/ytH8GuWwfs4fsB5iNkY8kZPpgQVb04QZSv85T0M8reb+cJmvLE8cjPYvBzRikD3s6qz8OoA==", "registry": "npm", "packageName": "koa-body", "cacheIntegrity": "sha512-wdGu7b9amk4Fnk/ytH8GuWwfs4fsB5iNkY8kZPpgQVb04QZSv85T0M8reb+cJmvLE8cjPYvBzRikD3s6qz8OoA== sha1-NyKSCLggdhrKWCLRTF/FXO4xsm8="}, "registry": "npm", "hash": "37229208b820761aca5822d14c5fc55cee31b26f"}