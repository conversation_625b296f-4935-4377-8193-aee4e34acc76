{"manifest": {"name": "renderkid", "version": "2.0.5", "description": "Stylish console.log for node", "main": "lib/RenderKid.js", "dependencies": {"css-select": "^2.0.2", "dom-converter": "^0.2", "htmlparser2": "^3.10.1", "lodash": "^4.17.20", "strip-ansi": "^3.0.0"}, "devDependencies": {"chai": "^4.1.2", "chai-changes": "^1.3.4", "chai-fuzzy": "^1.5.0", "coffee-script": "^1.9.1", "jitter": "^1.3.0", "mocha": "^5.2.0", "mocha-pretty-spec-reporter": "0.1.0-beta.2", "sinon": "^1.14.1", "sinon-chai": "^2.7.0", "underscore": "^1.8.3"}, "scripts": {"test": "mocha \"test/**/*.coffee\"", "test:watch": "mocha \"test/**/*.coffee\" --watch", "compile": "coffee --bare --compile --output ./lib ./src", "compile:watch": "jitter src lib -b", "watch": "npm run compile:watch & npm run test:watch", "winwatch": "start/b npm run compile:watch & npm run test:watch", "prepublish": "npm run compile"}, "repository": {"type": "git", "url": "https://github.com/AriaMinaei/RenderKid.git"}, "bugs": {"url": "https://github.com/AriaMinaei/RenderKid/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-renderkid-2.0.5-483b1ac59c6601ab30a7a596a5965cabccfdd0a5-integrity\\node_modules\\renderkid\\package.json", "readmeFilename": "README.md", "readme": "# RenderKid\n[![Build Status](https://secure.travis-ci.org/AriaMinaei/RenderKid.png)](http://travis-ci.org/AriaMinaei/RenderKid)\n\nRenderKid allows you to use HTML and CSS to style your CLI output, making it easy to create a beautiful, readable, and consistent look for your nodejs tool.\n\n## Installation\n\nInstall with npm:\n```\n$ npm install renderkid\n```\n\n## Usage\n\n```coffeescript\nRenderKid = require('renderkid')\n\nr = new RenderKid()\n\nr.style({\n  \"ul\": {\n    display: \"block\"\n    margin: \"2 0 2\"\n  }\n\n  \"li\": {\n    display: \"block\"\n    marginBottom: \"1\"\n  }\n\n  \"key\": {\n    color: \"grey\"\n    marginRight: \"1\"\n  }\n\n  \"value\": {\n    color: \"bright-white\"\n  }\n})\n\noutput = r.render(\"\n<ul>\n  <li>\n    <key>Name:</key>\n    <value>RenderKid</value>\n  </li>\n  <li>\n    <key>Version:</key>\n    <value>0.2</value>\n  </li>\n  <li>\n    <key>Last Update:</key>\n    <value>Jan 2015</value>\n  </li>\n</ul>\n\")\n\nconsole.log(output)\n```\n\n![screenshot of usage](https://github.com/AriaMinaei/RenderKid/raw/master/docs/images/usage.png)\n\n## Stylesheet properties\n\n### Display mode\n\nElements can have a `display` of either `inline`, `block`, or `none`:\n```coffeescript\nr.style({\n  \"div\": {\n    display: \"block\"\n  }\n\n  \"span\": {\n    display: \"inline\" # default\n  }\n\n  \"hidden\": {\n    display: \"none\"\n  }\n})\n\noutput = r.render(\"\n<div>This will fill one or more rows.</div>\n<span>These</span> <span>will</span> <span>be</span> in the same <span>line.</span>\n<hidden>This won't be displayed.</hidden>\n\")\n\nconsole.log(output)\n```\n\n![screenshot of usage](https://github.com/AriaMinaei/RenderKid/raw/master/docs/images/display.png)\n\n\n### Margin\n\nMargins work just like they do in browsers:\n```coffeescript\nr.style({\n  \"li\": {\n    display: \"block\"\n\n    marginTop: \"1\"\n    marginRight: \"2\"\n    marginBottom: \"3\"\n    marginLeft: \"4\"\n\n    # or the shorthand version:\n    \"margin\": \"1 2 3 4\"\n  },\n\n  \"highlight\": {\n    display: \"inline\"\n    marginLeft: \"2\"\n    marginRight: \"2\"\n  }\n})\n\nr.render(\"\n<ul>\n  <li>Item <highlgiht>1</highlight></li>\n  <li>Item <highlgiht>2</highlight></li>\n  <li>Item <highlgiht>3</highlight></li>\n</ul>\n\")\n```\n\n### Padding\n\nSee margins above. Paddings work the same way, only inward.\n\n### Width and Height\n\nBlock elements can have explicit width and height:\n```coffeescript\nr.style({\n  \"box\": {\n    display: \"block\"\n    \"width\": \"4\"\n    \"height\": \"2\"\n  }\n})\n\nr.render(\"<box>This is a box and some of its text will be truncated.</box>\")\n```\n\n### Colors\n\nYou can set a custom color and background color for each element:\n\n```coffeescript\nr.style({\n  \"error\": {\n    color: \"black\"\n    background: \"red\"\n  }\n})\n```\n\nList of colors currently supported are `black`, `red`, `green`, `yellow`, `blue`, `magenta`, `cyan`, `white`, `grey`, `bright-red`, `bright-green`, `bright-yellow`, `bright-blue`, `bright-magenta`, `bright-cyan`, `bright-white`.\n\n### Bullet points\n\nBlock elements can have bullet points on their margins. Let's start with an example:\n```coffeescript\nr.style({\n  \"li\": {\n    # To add bullet points to an element, first you\n    # should make some room for the bullet point by\n    # giving your element some margin to the left:\n    marginLeft: \"4\",\n\n    # Now we can add a bullet point to our margin:\n    bullet: '\"-\"'\n  }\n})\n\n# The four hyphens are there for visual reference\nr.render(\"\n----\n<li>Item 1</li>\n<li>Item 2</li>\n<li>Item 3</li>\n----\n\")\n```\nAnd here is the result:\n\n![screenshot of bullet points, 1](https://github.com/AriaMinaei/RenderKid/raw/master/docs/images/bullets-1.png)\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2015 Aria Minaei\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/renderkid/-/renderkid-2.0.5.tgz#483b1ac59c6601ab30a7a596a5965cabccfdd0a5", "type": "tarball", "reference": "https://registry.yarnpkg.com/renderkid/-/renderkid-2.0.5.tgz", "hash": "483b1ac59c6601ab30a7a596a5965cabccfdd0a5", "integrity": "sha512-ccqoLg+HLOHq1vdfYNm4TBeaCDIi1FLt3wGojTDSvdewUv65oTmI3cnT2E4hRjl1gzKZIPK+KZrXzlUYKnR+vQ==", "registry": "npm", "packageName": "renderkid", "cacheIntegrity": "sha512-ccqoLg+HLOHq1vdfYNm4TBeaCDIi1FLt3wGojTDSvdewUv65oTmI3cnT2E4hRjl1gzKZIPK+KZrXzlUYKnR+vQ== sha1-SDsaxZxmAaswp6WWpZZcq8z90KU="}, "registry": "npm", "hash": "483b1ac59c6601ab30a7a596a5965cabccfdd0a5"}