{"name": "safe-regex", "version": "1.1.0", "description": "detect possibly catastrophic, exponential-time regular expressions", "main": "index.js", "dependencies": {"ret": "~0.1.10"}, "devDependencies": {"tape": "^3.5.0"}, "scripts": {"test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/8", "ie/9", "ie/10", "firefox/latest", "chrome/latest", "opera/latest", "safari/latest"]}, "repository": {"type": "git", "url": "git://github.com/substack/safe-regex.git"}, "homepage": "https://github.com/substack/safe-regex", "keywords": ["catastrophic", "exponential", "regex", "safe", "sandbox"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT"}