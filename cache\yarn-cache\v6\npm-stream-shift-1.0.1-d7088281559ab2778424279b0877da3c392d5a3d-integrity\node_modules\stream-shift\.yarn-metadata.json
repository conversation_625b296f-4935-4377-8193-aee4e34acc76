{"manifest": {"name": "stream-shift", "version": "1.0.1", "description": "Returns the next buffer/object in a stream's readable queue", "main": "index.js", "dependencies": {}, "devDependencies": {"standard": "^7.1.2", "tape": "^4.6.0", "through2": "^2.0.1"}, "scripts": {"test": "standard && tape test.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/stream-shift.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/stream-shift/issues"}, "homepage": "https://github.com/mafintosh/stream-shift", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-stream-shift-1.0.1-d7088281559ab2778424279b0877da3c392d5a3d-integrity\\node_modules\\stream-shift\\package.json", "readmeFilename": "README.md", "readme": "# stream-shift\n\nReturns the next buffer/object in a stream's readable queue\n\n```\nnpm install stream-shift\n```\n\n[![build status](http://img.shields.io/travis/mafintosh/stream-shift.svg?style=flat)](http://travis-ci.org/mafintosh/stream-shift)\n\n## Usage\n\n``` js\nvar shift = require('stream-shift')\n\nconsole.log(shift(someStream)) // first item in its buffer\n```\n\n## Credit\n\nThanks [@dignifiedquire](https://github.com/dignifiedquire) for making this work on node 6\n\n## License\n\nMIT\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016 Mathias Buus\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/stream-shift/-/stream-shift-1.0.1.tgz#d7088281559ab2778424279b0877da3c392d5a3d", "type": "tarball", "reference": "https://registry.yarnpkg.com/stream-shift/-/stream-shift-1.0.1.tgz", "hash": "d7088281559ab2778424279b0877da3c392d5a3d", "integrity": "sha512-AiisoFqQ0vbGcZgQPY1cdP2I76glaVA/RauYR4G4thNFgkTqr90yXTo4LYX60Jl+sIlPNHHdGSwo01AvbKUSVQ==", "registry": "npm", "packageName": "stream-shift", "cacheIntegrity": "sha512-AiisoFqQ0vbGcZgQPY1cdP2I76glaVA/RauYR4G4thNFgkTqr90yXTo4LYX60Jl+sIlPNHHdGSwo01AvbKUSVQ== sha1-1wiCgVWasneEJCebCHfaPDktWj0="}, "registry": "npm", "hash": "d7088281559ab2778424279b0877da3c392d5a3d"}