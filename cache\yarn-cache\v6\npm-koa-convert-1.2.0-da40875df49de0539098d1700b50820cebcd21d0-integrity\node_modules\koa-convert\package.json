{"name": "koa-convert", "version": "1.2.0", "keywords": ["koa", "middleware", "convert"], "description": "convert koa legacy generator-based middleware to promise-based middleware", "repository": {"type": "git", "url": "git+https://github.com/gyson/koa-convert.git"}, "main": "index.js", "scripts": {"test": "standard && mocha test.js"}, "author": "gyson <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/gyson/koa-convert/issues"}, "homepage": "https://github.com/gyson/koa-convert#readme", "dependencies": {"co": "^4.6.0", "koa-compose": "^3.0.0"}, "devDependencies": {"koa": "^2.0.0-alpha.2", "koa-v1": "^1.0.0", "mocha": "^2.3.3", "standard": "^5.3.1", "supertest": "^1.1.0"}, "engines": {"node": ">= 4"}}