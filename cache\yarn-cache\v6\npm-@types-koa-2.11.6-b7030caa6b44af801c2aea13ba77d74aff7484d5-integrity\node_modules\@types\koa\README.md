# Installation
> `npm install --save @types/koa`

# Summary
This package contains type definitions for Koa (http://koajs.com).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/koa.

### Additional Details
 * Last updated: Tu<PERSON>, 20 Oct 2020 14:53:22 GMT
 * Dependencies: [@types/accepts](https://npmjs.com/package/@types/accepts), [@types/cookies](https://npmjs.com/package/@types/cookies), [@types/http-assert](https://npmjs.com/package/@types/http-assert), [@types/http-errors](https://npmjs.com/package/@types/http-errors), [@types/keygrip](https://npmjs.com/package/@types/keygrip), [@types/koa-compose](https://npmjs.com/package/@types/koa-compose), [@types/content-disposition](https://npmjs.com/package/@types/content-disposition), [@types/node](https://npmjs.com/package/@types/node)
 * Global values: none

# Credits
These definitions were written by [<PERSON><PERSON>ai1993](https://github.com/DavidCai1993), [j<PERSON>ey Lu](https://github.com/jkeylu), [Brice Bernard](https://github.com/brikou), [harryparkdotio](https://github.com/harryparkdotio), and [Wooram Jun](https://github.com/chatoo2412).
