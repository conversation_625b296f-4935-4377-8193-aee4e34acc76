{"manifest": {"name": "braces", "description": "Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.", "version": "2.3.2", "homepage": "https://github.com/micromatch/braces", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "url": "https://github.com/eush77"}, {"name": "hemanth.hm", "url": "http://h3manth.com"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "https://github.com/micromatch/braces.git"}, "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "license": "MIT", "files": ["index.js", "lib"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "dependencies": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^2.0.0", "brace-expansion": "^1.1.8", "cross-spawn": "^5.1.0", "gulp": "^3.9.1", "gulp-eslint": "^4.0.0", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.4", "mocha": "^3.2.0", "noncharacters": "^1.1.0", "text-table": "^0.2.0", "time-diff": "^0.3.1", "yargs-parser": "^8.0.0"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "lint": {"reflinks": true}, "plugins": ["gulp-format-md"], "related": {"list": ["expand-brackets", "extglob", "fill-range", "micromatch", "nanomatch"]}}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-braces-2.3.2-5979fd3f14cd531565e5fa2df1abfff1dfaee729-integrity\\node_modules\\braces\\package.json", "readmeFilename": "README.md", "readme": "# braces [![NPM version](https://img.shields.io/npm/v/braces.svg?style=flat)](https://www.npmjs.com/package/braces) [![NPM monthly downloads](https://img.shields.io/npm/dm/braces.svg?style=flat)](https://npmjs.org/package/braces) [![NPM total downloads](https://img.shields.io/npm/dt/braces.svg?style=flat)](https://npmjs.org/package/braces) [![Linux Build Status](https://img.shields.io/travis/micromatch/braces.svg?style=flat&label=Travis)](https://travis-ci.org/micromatch/braces) [![Windows Build Status](https://img.shields.io/appveyor/ci/micromatch/braces.svg?style=flat&label=AppVeyor)](https://ci.appveyor.com/project/micromatch/braces)\n\n> Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save braces\n```\n\n## Why use braces?\n\nBrace patterns are great for matching ranges. Users (and implementors) shouldn't have to think about whether or not they will break their application (or yours) from accidentally defining an aggressive brace pattern. _Braces is the only library that offers a [solution to this problem](#performance)_.\n\n* **Safe(r)**: Braces isn't vulnerable to DoS attacks like [brace-expansion](https://github.com/juliangruber/brace-expansion), [minimatch](https://github.com/isaacs/minimatch) and [multimatch](https://github.com/sindresorhus/multimatch) (a different bug than the [other regex DoS bug](https://medium.com/node-security/minimatch-redos-vulnerability-590da24e6d3c#.jew0b6mpc)).\n* **Accurate**: complete support for the [Bash 4.3 Brace Expansion](www.gnu.org/software/bash/) specification (passes all of the Bash braces tests)\n* **[fast and performant](#benchmarks)**: Starts fast, runs fast and [scales well](#performance) as patterns increase in complexity.\n* **Organized code base**: with parser and compiler that are eas(y|ier) to maintain and update when edge cases crop up.\n* **Well-tested**: thousands of test assertions. Passes 100% of the [minimatch](https://github.com/isaacs/minimatch) and [brace-expansion](https://github.com/juliangruber/brace-expansion) unit tests as well (as of the writing of this).\n\n## Usage\n\nThe main export is a function that takes one or more brace `patterns` and `options`.\n\n```js\nvar braces = require('braces');\nbraces(pattern[, options]);\n```\n\nBy default, braces returns an optimized regex-source string. To get an array of brace patterns, use `brace.expand()`.\n\nThe following section explains the difference in more detail. _(If you're curious about \"why\" braces does this by default, see [brace matching pitfalls](#brace-matching-pitfalls)_.\n\n### Optimized vs. expanded braces\n\n**Optimized**\n\nBy default, patterns are optimized for regex and matching:\n\n```js\nconsole.log(braces('a/{x,y,z}/b'));\n//=> ['a/(x|y|z)/b']\n```\n\n**Expanded**\n\nTo expand patterns the same way as Bash or [minimatch](https://github.com/isaacs/minimatch), use the [.expand](#expand) method:\n\n```js\nconsole.log(braces.expand('a/{x,y,z}/b'));\n//=> ['a/x/b', 'a/y/b', 'a/z/b']\n```\n\nOr use [options.expand](#optionsexpand):\n\n```js\nconsole.log(braces('a/{x,y,z}/b', {expand: true}));\n//=> ['a/x/b', 'a/y/b', 'a/z/b']\n```\n\n## Features\n\n* [lists](#lists): Supports \"lists\": `a/{b,c}/d` => `['a/b/d', 'a/c/d']`\n* [sequences](#sequences): Supports alphabetical or numerical \"sequences\" (ranges): `{1..3}` => `['1', '2', '3']`\n* [steps](#steps): Supports \"steps\" or increments: `{2..10..2}` => `['2', '4', '6', '8', '10']`\n* [escaping](#escaping)\n* [options](#options)\n\n### Lists\n\nUses [fill-range](https://github.com/jonschlinkert/fill-range) for expanding alphabetical or numeric lists:\n\n```js\nconsole.log(braces('a/{foo,bar,baz}/*.js'));\n//=> ['a/(foo|bar|baz)/*.js']\n\nconsole.log(braces.expand('a/{foo,bar,baz}/*.js'));\n//=> ['a/foo/*.js', 'a/bar/*.js', 'a/baz/*.js']\n```\n\n### Sequences\n\nUses [fill-range](https://github.com/jonschlinkert/fill-range) for expanding alphabetical or numeric ranges (bash \"sequences\"):\n\n```js\nconsole.log(braces.expand('{1..3}'));     // ['1', '2', '3']\nconsole.log(braces.expand('a{01..03}b')); // ['a01b', 'a02b', 'a03b']\nconsole.log(braces.expand('a{1..3}b'));   // ['a1b', 'a2b', 'a3b']\nconsole.log(braces.expand('{a..c}'));     // ['a', 'b', 'c']\nconsole.log(braces.expand('foo/{a..c}')); // ['foo/a', 'foo/b', 'foo/c']\n\n// supports padded ranges\nconsole.log(braces('a{01..03}b'));   //=> [ 'a(0[1-3])b' ]\nconsole.log(braces('a{001..300}b')); //=> [ 'a(0{2}[1-9]|0[1-9][0-9]|[12][0-9]{2}|300)b' ]\n```\n\n### Steps\n\nSteps, or increments, may be used with ranges:\n\n```js\nconsole.log(braces.expand('{2..10..2}'));\n//=> ['2', '4', '6', '8', '10']\n\nconsole.log(braces('{2..10..2}'));\n//=> ['(2|4|6|8|10)']\n```\n\nWhen the [.optimize](#optimize) method is used, or [options.optimize](#optionsoptimize) is set to true, sequences are passed to [to-regex-range](https://github.com/jonschlinkert/to-regex-range) for expansion.\n\n### Nesting\n\nBrace patterns may be nested. The results of each expanded string are not sorted, and left to right order is preserved.\n\n**\"Expanded\" braces**\n\n```js\nconsole.log(braces.expand('a{b,c,/{x,y}}/e'));\n//=> ['ab/e', 'ac/e', 'a/x/e', 'a/y/e']\n\nconsole.log(braces.expand('a/{x,{1..5},y}/c'));\n//=> ['a/x/c', 'a/1/c', 'a/2/c', 'a/3/c', 'a/4/c', 'a/5/c', 'a/y/c']\n```\n\n**\"Optimized\" braces**\n\n```js\nconsole.log(braces('a{b,c,/{x,y}}/e'));\n//=> ['a(b|c|/(x|y))/e']\n\nconsole.log(braces('a/{x,{1..5},y}/c'));\n//=> ['a/(x|([1-5])|y)/c']\n```\n\n### Escaping\n\n**Escaping braces**\n\nA brace pattern will not be expanded or evaluted if _either the opening or closing brace is escaped_:\n\n```js\nconsole.log(braces.expand('a\\\\{d,c,b}e'));\n//=> ['a{d,c,b}e']\n\nconsole.log(braces.expand('a{d,c,b\\\\}e'));\n//=> ['a{d,c,b}e']\n```\n\n**Escaping commas**\n\nCommas inside braces may also be escaped:\n\n```js\nconsole.log(braces.expand('a{b\\\\,c}d'));\n//=> ['a{b,c}d']\n\nconsole.log(braces.expand('a{d\\\\,c,b}e'));\n//=> ['ad,ce', 'abe']\n```\n\n**Single items**\n\nFollowing bash conventions, a brace pattern is also not expanded when it contains a single character:\n\n```js\nconsole.log(braces.expand('a{b}c'));\n//=> ['a{b}c']\n```\n\n## Options\n\n### options.maxLength\n\n**Type**: `Number`\n\n**Default**: `65,536`\n\n**Description**: Limit the length of the input string. Useful when the input string is generated or your application allows users to pass a string, et cetera.\n\n```js\nconsole.log(braces('a/{b,c}/d', { maxLength: 3 }));  //=> throws an error\n```\n\n### options.expand\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n**Description**: Generate an \"expanded\" brace pattern (this option is unncessary with the `.expand` method, which does the same thing).\n\n```js\nconsole.log(braces('a/{b,c}/d', {expand: true}));\n//=> [ 'a/b/d', 'a/c/d' ]\n```\n\n### options.optimize\n\n**Type**: `Boolean`\n\n**Default**: `true`\n\n**Description**: Enabled by default.\n\n```js\nconsole.log(braces('a/{b,c}/d'));\n//=> [ 'a/(b|c)/d' ]\n```\n\n### options.nodupes\n\n**Type**: `Boolean`\n\n**Default**: `true`\n\n**Description**: Duplicates are removed by default. To keep duplicates, pass `{nodupes: false}` on the options\n\n### options.rangeLimit\n\n**Type**: `Number`\n\n**Default**: `250`\n\n**Description**: When `braces.expand()` is used, or `options.expand` is true, brace patterns will automatically be [optimized](#optionsoptimize) when the difference between the range minimum and range maximum exceeds the `rangeLimit`. This is to prevent huge ranges from freezing your application.\n\nYou can set this to any number, or change `options.rangeLimit` to `Inifinity` to disable this altogether.\n\n**Examples**\n\n```js\n// pattern exceeds the \"rangeLimit\", so it's optimized automatically\nconsole.log(braces.expand('{1..1000}'));\n//=> ['([1-9]|[1-9][0-9]{1,2}|1000)']\n\n// pattern does not exceed \"rangeLimit\", so it's NOT optimized\nconsole.log(braces.expand('{1..100}'));\n//=> ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '80', '81', '82', '83', '84', '85', '86', '87', '88', '89', '90', '91', '92', '93', '94', '95', '96', '97', '98', '99', '100']\n```\n\n### options.transform\n\n**Type**: `Function`\n\n**Default**: `undefined`\n\n**Description**: Customize range expansion.\n\n```js\nvar range = braces.expand('x{a..e}y', {\n  transform: function(str) {\n    return 'foo' + str;\n  }\n});\n\nconsole.log(range);\n//=> [ 'xfooay', 'xfooby', 'xfoocy', 'xfoody', 'xfooey' ]\n```\n\n### options.quantifiers\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n**Description**: In regular expressions, quanitifiers can be used to specify how many times a token can be repeated. For example, `a{1,3}` will match the letter `a` one to three times.\n\nUnfortunately, regex quantifiers happen to share the same syntax as [Bash lists](#lists)\n\nThe `quantifiers` option tells braces to detect when [regex quantifiers](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp#quantifiers) are defined in the given pattern, and not to try to expand them as lists.\n\n**Examples**\n\n```js\nvar braces = require('braces');\nconsole.log(braces('a/b{1,3}/{x,y,z}'));\n//=> [ 'a/b(1|3)/(x|y|z)' ]\nconsole.log(braces('a/b{1,3}/{x,y,z}', {quantifiers: true}));\n//=> [ 'a/b{1,3}/(x|y|z)' ]\nconsole.log(braces('a/b{1,3}/{x,y,z}', {quantifiers: true, expand: true}));\n//=> [ 'a/b{1,3}/x', 'a/b{1,3}/y', 'a/b{1,3}/z' ]\n```\n\n### options.unescape\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n**Description**: Strip backslashes that were used for escaping from the result.\n\n## What is \"brace expansion\"?\n\nBrace expansion is a type of parameter expansion that was made popular by unix shells for generating lists of strings, as well as regex-like matching when used alongside wildcards (globs).\n\nIn addition to \"expansion\", braces are also used for matching. In other words:\n\n* [brace expansion](#brace-expansion) is for generating new lists\n* [brace matching](#brace-matching) is for filtering existing lists\n\n<details>\n<summary><strong>More about brace expansion</strong> (click to expand)</summary>\n\nThere are two main types of brace expansion:\n\n1. **lists**: which are defined using comma-separated values inside curly braces: `{a,b,c}`\n2. **sequences**: which are defined using a starting value and an ending value, separated by two dots: `a{1..3}b`. Optionally, a third argument may be passed to define a \"step\" or increment to use: `a{1..100..10}b`. These are also sometimes referred to as \"ranges\".\n\nHere are some example brace patterns to illustrate how they work:\n\n**Sets**\n\n```\n{a,b,c}       => a b c\n{a,b,c}{1,2}  => a1 a2 b1 b2 c1 c2\n```\n\n**Sequences**\n\n```\n{1..9}        => 1 2 3 4 5 6 7 8 9\n{4..-4}       => 4 3 2 1 0 -1 -2 -3 -4\n{1..20..3}    => 1 4 7 10 13 16 19\n{a..j}        => a b c d e f g h i j\n{j..a}        => j i h g f e d c b a\n{a..z..3}     => a d g j m p s v y\n```\n\n**Combination**\n\nSets and sequences can be mixed together or used along with any other strings.\n\n```\n{a,b,c}{1..3}   => a1 a2 a3 b1 b2 b3 c1 c2 c3\nfoo/{a,b,c}/bar => foo/a/bar foo/b/bar foo/c/bar\n```\n\nThe fact that braces can be \"expanded\" from relatively simple patterns makes them ideal for quickly generating test fixtures, file paths, and similar use cases.\n\n## Brace matching\n\nIn addition to _expansion_, brace patterns are also useful for performing regular-expression-like matching.\n\nFor example, the pattern `foo/{1..3}/bar` would match any of following strings:\n\n```\nfoo/1/bar\nfoo/2/bar\nfoo/3/bar\n```\n\nBut not:\n\n```\nbaz/1/qux\nbaz/2/qux\nbaz/3/qux\n```\n\nBraces can also be combined with [glob patterns](https://github.com/jonschlinkert/micromatch) to perform more advanced wildcard matching. For example, the pattern `*/{1..3}/*` would match any of following strings:\n\n```\nfoo/1/bar\nfoo/2/bar\nfoo/3/bar\nbaz/1/qux\nbaz/2/qux\nbaz/3/qux\n```\n\n## Brace matching pitfalls\n\nAlthough brace patterns offer a user-friendly way of matching ranges or sets of strings, there are also some major disadvantages and potential risks you should be aware of.\n\n### tldr\n\n**\"brace bombs\"**\n\n* brace expansion can eat up a huge amount of processing resources\n* as brace patterns increase _linearly in size_, the system resources required to expand the pattern increase exponentially\n* users can accidentally (or intentially) exhaust your system's resources resulting in the equivalent of a DoS attack (bonus: no programming knowledge is required!)\n\nFor a more detailed explanation with examples, see the [geometric complexity](#geometric-complexity) section.\n\n### The solution\n\nJump to the [performance section](#performance) to see how Braces solves this problem in comparison to other libraries.\n\n### Geometric complexity\n\nAt minimum, brace patterns with sets limited to two elements have quadradic or `O(n^2)` complexity. But the complexity of the algorithm increases exponentially as the number of sets, _and elements per set_, increases, which is `O(n^c)`.\n\nFor example, the following sets demonstrate quadratic (`O(n^2)`) complexity:\n\n```\n{1,2}{3,4}      => (2X2)    => 13 14 23 24\n{1,2}{3,4}{5,6} => (2X2X2)  => 135 136 145 146 235 236 245 246\n```\n\nBut add an element to a set, and we get a n-fold Cartesian product with `O(n^c)` complexity:\n\n```\n{1,2,3}{4,5,6}{7,8,9} => (3X3X3) => 147 148 149 157 158 159 167 168 169 247 248 \n                                    249 257 258 259 267 268 269 347 348 349 357 \n                                    358 359 367 368 369\n```\n\nNow, imagine how this complexity grows given that each element is a n-tuple:\n\n```\n{1..100}{1..100}         => (100X100)     => 10,000 elements (38.4 kB)\n{1..100}{1..100}{1..100} => (100X100X100) => 1,000,000 elements (5.76 MB)\n```\n\nAlthough these examples are clearly contrived, they demonstrate how brace patterns can quickly grow out of control.\n\n**More information**\n\nInterested in learning more about brace expansion?\n\n* [linuxjournal/bash-brace-expansion](http://www.linuxjournal.com/content/bash-brace-expansion)\n* [rosettacode/Brace_expansion](https://rosettacode.org/wiki/Brace_expansion)\n* [cartesian product](https://en.wikipedia.org/wiki/Cartesian_product)\n\n</details>\n\n## Performance\n\nBraces is not only screaming fast, it's also more accurate the other brace expansion libraries.\n\n### Better algorithms\n\nFortunately there is a solution to the [\"brace bomb\" problem](#brace-matching-pitfalls): _don't expand brace patterns into an array when they're used for matching_.\n\nInstead, convert the pattern into an optimized regular expression. This is easier said than done, and braces is the only library that does this currently.\n\n**The proof is in the numbers**\n\nMinimatch gets exponentially slower as patterns increase in complexity, braces does not. The following results were generated using `braces()` and `minimatch.braceExpand()`, respectively.\n\n| **Pattern** | **braces** | **[minimatch](https://github.com/isaacs/minimatch)** | \n| --- | --- | --- |\n| `{1..9007199254740991}`<sup class=\"footnote-ref\"><a href=\"#fn1\" id=\"fnref1\">[1]</a></sup> | `298 B` (5ms 459μs) | N/A (freezes) |\n| `{1..1000000000000000}` | `41 B` (1ms 15μs) | N/A (freezes) |\n| `{1..100000000000000}` | `40 B` (890μs) | N/A (freezes) |\n| `{1..10000000000000}` | `39 B` (2ms 49μs) | N/A (freezes) |\n| `{1..1000000000000}` | `38 B` (608μs) | N/A (freezes) |\n| `{1..100000000000}` | `37 B` (397μs) | N/A (freezes) |\n| `{1..10000000000}` | `35 B` (983μs) | N/A (freezes) |\n| `{1..1000000000}` | `34 B` (798μs) | N/A (freezes) |\n| `{1..100000000}` | `33 B` (733μs) | N/A (freezes) |\n| `{1..10000000}` | `32 B` (5ms 632μs) | `78.89 MB` (16s 388ms 569μs) |\n| `{1..1000000}` | `31 B` (1ms 381μs) | `6.89 MB` (1s 496ms 887μs) |\n| `{1..100000}` | `30 B` (950μs) | `588.89 kB` (146ms 921μs) |\n| `{1..10000}` | `29 B` (1ms 114μs) | `48.89 kB` (14ms 187μs) |\n| `{1..1000}` | `28 B` (760μs) | `3.89 kB` (1ms 453μs) |\n| `{1..100}` | `22 B` (345μs) | `291 B` (196μs) |\n| `{1..10}` | `10 B` (533μs) | `20 B` (37μs) |\n| `{1..3}` | `7 B` (190μs) | `5 B` (27μs) |\n\n### Faster algorithms\n\nWhen you need expansion, braces is still much faster.\n\n_(the following results were generated using `braces.expand()` and `minimatch.braceExpand()`, respectively)_\n\n| **Pattern** | **braces** | **[minimatch](https://github.com/isaacs/minimatch)** | \n| --- | --- | --- |\n| `{1..10000000}` | `78.89 MB` (2s 698ms 642μs) | `78.89 MB` (18s 601ms 974μs) |\n| `{1..1000000}` | `6.89 MB` (458ms 576μs) | `6.89 MB` (1s 491ms 621μs) |\n| `{1..100000}` | `588.89 kB` (20ms 728μs) | `588.89 kB` (156ms 919μs) |\n| `{1..10000}` | `48.89 kB` (2ms 202μs) | `48.89 kB` (13ms 641μs) |\n| `{1..1000}` | `3.89 kB` (1ms 796μs) | `3.89 kB` (1ms 958μs) |\n| `{1..100}` | `291 B` (424μs) | `291 B` (211μs) |\n| `{1..10}` | `20 B` (487μs) | `20 B` (72μs) |\n| `{1..3}` | `5 B` (166μs) | `5 B` (27μs) |\n\nIf you'd like to run these comparisons yourself, see [test/support/generate.js](test/support/generate.js).\n\n## Benchmarks\n\n### Running benchmarks\n\nInstall dev dependencies:\n\n```bash\nnpm i -d && npm benchmark\n```\n\n### Latest results\n\n```bash\nBenchmarking: (8 of 8)\n · combination-nested\n · combination\n · escaped\n · list-basic\n · list-multiple\n · no-braces\n · sequence-basic\n · sequence-multiple\n\n# benchmark/fixtures/combination-nested.js (52 bytes)\n  brace-expansion x 4,756 ops/sec ±1.09% (86 runs sampled)\n  braces x 11,202,303 ops/sec ±1.06% (88 runs sampled)\n  minimatch x 4,816 ops/sec ±0.99% (87 runs sampled)\n\n  fastest is braces\n\n# benchmark/fixtures/combination.js (51 bytes)\n  brace-expansion x 625 ops/sec ±0.87% (87 runs sampled)\n  braces x 11,031,884 ops/sec ±0.72% (90 runs sampled)\n  minimatch x 637 ops/sec ±0.84% (88 runs sampled)\n\n  fastest is braces\n\n# benchmark/fixtures/escaped.js (44 bytes)\n  brace-expansion x 163,325 ops/sec ±1.05% (87 runs sampled)\n  braces x 10,655,071 ops/sec ±1.22% (88 runs sampled)\n  minimatch x 147,495 ops/sec ±0.96% (88 runs sampled)\n\n  fastest is braces\n\n# benchmark/fixtures/list-basic.js (40 bytes)\n  brace-expansion x 99,726 ops/sec ±1.07% (83 runs sampled)\n  braces x 10,596,584 ops/sec ±0.98% (88 runs sampled)\n  minimatch x 100,069 ops/sec ±1.17% (86 runs sampled)\n\n  fastest is braces\n\n# benchmark/fixtures/list-multiple.js (52 bytes)\n  brace-expansion x 34,348 ops/sec ±1.08% (88 runs sampled)\n  braces x 9,264,131 ops/sec ±1.12% (88 runs sampled)\n  minimatch x 34,893 ops/sec ±0.87% (87 runs sampled)\n\n  fastest is braces\n\n# benchmark/fixtures/no-braces.js (48 bytes)\n  brace-expansion x 275,368 ops/sec ±1.18% (89 runs sampled)\n  braces x 9,134,677 ops/sec ±0.95% (88 runs sampled)\n  minimatch x 3,755,954 ops/sec ±1.13% (89 runs sampled)\n\n  fastest is braces\n\n# benchmark/fixtures/sequence-basic.js (41 bytes)\n  brace-expansion x 5,492 ops/sec ±1.35% (87 runs sampled)\n  braces x 8,485,034 ops/sec ±1.28% (89 runs sampled)\n  minimatch x 5,341 ops/sec ±1.17% (87 runs sampled)\n\n  fastest is braces\n\n# benchmark/fixtures/sequence-multiple.js (51 bytes)\n  brace-expansion x 116 ops/sec ±0.77% (77 runs sampled)\n  braces x 9,445,118 ops/sec ±1.32% (84 runs sampled)\n  minimatch x 109 ops/sec ±1.16% (76 runs sampled)\n\n  fastest is braces\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [expand-brackets](https://www.npmjs.com/package/expand-brackets): Expand POSIX bracket expressions (character classes) in glob patterns. | [homepage](https://github.com/jonschlinkert/expand-brackets \"Expand POSIX bracket expressions (character classes) in glob patterns.\")\n* [extglob](https://www.npmjs.com/package/extglob): Extended glob support for JavaScript. Adds (almost) the expressive power of regular expressions to glob… [more](https://github.com/micromatch/extglob) | [homepage](https://github.com/micromatch/extglob \"Extended glob support for JavaScript. Adds (almost) the expressive power of regular expressions to glob patterns.\")\n* [fill-range](https://www.npmjs.com/package/fill-range): Fill in a range of numbers or letters, optionally passing an increment or `step` to… [more](https://github.com/jonschlinkert/fill-range) | [homepage](https://github.com/jonschlinkert/fill-range \"Fill in a range of numbers or letters, optionally passing an increment or `step` to use, or create a regex-compatible range with `options.toRegex`\")\n* [micromatch](https://www.npmjs.com/package/micromatch): Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. | [homepage](https://github.com/micromatch/micromatch \"Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.\")\n* [nanomatch](https://www.npmjs.com/package/nanomatch): Fast, minimal glob matcher for node.js. Similar to micromatch, minimatch and multimatch, but complete Bash… [more](https://github.com/micromatch/nanomatch) | [homepage](https://github.com/micromatch/nanomatch \"Fast, minimal glob matcher for node.js. Similar to micromatch, minimatch and multimatch, but complete Bash 4.3 wildcard support only (no support for exglobs, posix brackets or braces)\")\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 188 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 4 | [doowb](https://github.com/doowb) |\n| 1 | [es128](https://github.com/es128) |\n| 1 | [eush77](https://github.com/eush77) |\n| 1 | [hemanth](https://github.com/hemanth) |\n\n### Author\n\n**Jon Schlinkert**\n\n* [linkedin/in/jonschlinkert](https://linkedin.com/in/jonschlinkert)\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on February 17, 2018._\n\n<hr class=\"footnotes-sep\">\n<section class=\"footnotes\">\n<ol class=\"footnotes-list\">\n<li id=\"fn1\"  class=\"footnote-item\">this is the largest safe integer allowed in JavaScript. <a href=\"#fnref1\" class=\"footnote-backref\">↩</a>\n\n</li>\n</ol>\n</section>", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2018, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/braces/-/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729", "type": "tarball", "reference": "https://registry.yarnpkg.com/braces/-/braces-2.3.2.tgz", "hash": "5979fd3f14cd531565e5fa2df1abfff1dfaee729", "integrity": "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==", "registry": "npm", "packageName": "braces", "cacheIntegrity": "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w== sha1-WXn9PxTNUxVl5fot8av/8d+u5yk="}, "registry": "npm", "hash": "5979fd3f14cd531565e5fa2df1abfff1dfaee729"}