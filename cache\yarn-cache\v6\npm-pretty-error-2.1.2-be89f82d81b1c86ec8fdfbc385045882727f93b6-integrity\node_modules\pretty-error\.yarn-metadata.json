{"manifest": {"name": "pretty-error", "version": "2.1.2", "description": "See nodejs errors with less clutter", "main": "./lib/PrettyError.js", "typings": "index.d.ts", "scripts": {"test": "mocha \"test/**/*.coffee\"", "test:watch": "mocha \"test/**/*.coffee\" --watch", "compile": "coffee --bare --compile --output ./lib ./src", "compile:watch": "jitter src lib -b", "watch": "npm run compile:watch & npm run test:watch", "winwatch": "start/b npm run compile:watch & npm run test:watch", "prepublish": "npm run compile"}, "dependencies": {"lodash": "^4.17.20", "renderkid": "^2.0.4"}, "devDependencies": {"coffee-script": "~1.8.0", "chai": "~1.9.2", "mocha": "~2.0.1", "jitter": "^1.3.0"}, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/AriaMinaei/pretty-error.git"}, "bugs": {"url": "https://github.com/AriaMinaei/pretty-error/issues"}, "keywords": ["pretty", "error", "exception", "debug", "error-handling", "readable", "colorful", "prettify", "format", "human"], "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-pretty-error-2.1.2-be89f82d81b1c86ec8fdfbc385045882727f93b6-integrity\\node_modules\\pretty-error\\package.json", "readmeFilename": "README.md", "readme": "# pretty-error\n\n[![Dependency status](https://david-dm.org/AriaMinaei/pretty-error.svg)](https://david-dm.org/AriaMinaei/pretty-error)\n[![Build Status](https://secure.travis-ci.org/AriaMinaei/pretty-error.svg?branch=master)](https://travis-ci.org/AriaMinaei/pretty-error) [![npm](https://img.shields.io/npm/dm/pretty-error.svg)](https://npmjs.org/package/pretty-error)\n\nA small tool to see node.js errors with less clutter:\n\n![screenshot of pretty-error](https://github.com/AriaMinaei/pretty-error/raw/master/docs/images/pretty-error-screenshot.png)\n\n... which is more readable compared to node's unformatted errors:\n\n![screenshot of normal errors](https://github.com/AriaMinaei/pretty-error/raw/master/docs/images/normal-error-screenshot.png)\n\n## Installation\n\nInstall with npm:\n\n\t$ npm install pretty-error\n\n## Usage and Examples\n\nTo see an error rendered with colors, you can do this:\n\n```javascript\nvar PrettyError = require('pretty-error');\nvar pe = new PrettyError();\n\nvar renderedError = pe.render(new Error('Some error message'));\nconsole.log(renderedError);\n```\n\nOf course, you can render caught exceptions too:\n\n```javascript\ntry {\n   doSomethingThatThrowsAnError();\n} catch (error) {\n   console.log(pe.render(error));\n}\n```\n\nBut if you want pretty-error to render all errors, there is a shortcut for it:\n\n```javascript\nrequire('pretty-error').start();\n```\n\n... which is essentially equal to:\n\n```javascript\nvar PrettyError = require('pretty-error');\n\n// instantiate PrettyError, which can then be used to render error objects\nvar pe = new PrettyError();\npe.start();\n```\n\nYou can also preload pretty-error into your code using node's [`--require`](https://nodejs.org/api/cli.html#cli_r_require_module) argument:\n\n```\n$ node --require pretty-error/start your-module.js\n```\n\n## How it Works\n\nPrettyError turns error objects into something similar to an html document, and then uses [RenderKid](https://github.com/AriaMinaei/renderkid) to render the document using simple html/css-like commands. This allows PrettyError to be themed using simple css-like declarations.\n\n## Theming\n\nPrettyError's default theme is a bunch of simple css-like rules. [Here](https://github.com/AriaMinaei/pretty-error/blob/master/src/defaultStyle.coffee) is the source of the default theme.\n\nSince the default theme is all css, you can customize it to fit your taste. Let's do a minimal one:\n\n```javascript\n// the start() shortcut returns an instance of PrettyError ...\npe = require('pretty-error').start();\n\n// ... which we can then use to customize like this:\npe.appendStyle({\n   // this is a simple selector to the element that says 'Error'\n   'pretty-error > header > title > kind': {\n      // which we can hide:\n      display: 'none'\n   },\n\n   // the 'colon' after 'Error':\n   'pretty-error > header > colon': {\n      // we hide that too:\n      display: 'none'\n   },\n\n   // our error message\n   'pretty-error > header > message': {\n      // let's change its color:\n      color: 'bright-white',\n\n      // we can use black, red, green, yellow, blue, magenta, cyan, white,\n      // grey, bright-red, bright-green, bright-yellow, bright-blue,\n      // bright-magenta, bright-cyan, and bright-white\n\n      // we can also change the background color:\n      background: 'cyan',\n\n      // it understands paddings too!\n      padding: '0 1' // top/bottom left/right\n   },\n\n   // each trace item ...\n   'pretty-error > trace > item': {\n      // ... can have a margin ...\n      marginLeft: 2,\n\n      // ... and a bullet character!\n      bullet: '\"<grey>o</grey>\"'\n\n      // Notes on bullets:\n      //\n      // The string inside the quotation mark gets used as the character\n      // to show for the bullet point.\n      //\n      // You can set its color/background color using tags.\n      //\n      // This example sets the background color to white, and the text color\n      // to cyan, the character will be a hyphen with a space character\n      // on each side:\n      // example: '\"<bg-white><cyan> - </cyan></bg-white>\"'\n      //\n      // Note that we should use a margin of 3, since the bullet will be\n      // 3 characters long.\n   },\n\n   'pretty-error > trace > item > header > pointer > file': {\n      color: 'bright-cyan'\n   },\n\n   'pretty-error > trace > item > header > pointer > colon': {\n      color: 'cyan'\n   },\n\n   'pretty-error > trace > item > header > pointer > line': {\n      color: 'bright-cyan'\n   },\n\n   'pretty-error > trace > item > header > what': {\n      color: 'bright-white'\n   },\n\n   'pretty-error > trace > item > footer > addr': {\n      display: 'none'\n   }\n});\n```\n\nThis is how our minimal theme will look like: ![screenshot of our custom theme](https://github.com/AriaMinaei/pretty-error/raw/master/docs/images/custom-theme-screenshot.png)\n\nRead [RenderKid](https://github.com/AriaMinaei/renderkid)'s docs to learn about all the css rules that are supported.\n\n## Customization\n\nThere are a few methods to help you customize the contents of your error logs.\n\nLet's instantiate first:\n\n```javascript\nPrettyError = require('pretty-error');\npe = new PrettyError();\n\n// or:\npe = require('pretty-error').start();\n```\n\n#### Shortening paths\n\nYou might want to substitute long paths with shorter, more readable aliases:\n\n```javascript\npe.alias('E:/open-source/theatrejs/lib', '(Theare.js)');\n```\n\n#### Skipping packages\n\nYou might want to skip trace lines that belong to specific packages (chai, when, socket.io):\n\n```javascript\npe.skipPackage('chai', 'when', 'socket.io');\n```\n\n#### Skipping node files\n\n```javascript\n// this will skip node.js, path.js, event.js, etc.\npe.skipNodeFiles();\n```\n\n#### Skipping paths\n\n```javascript\npe.skipPath('/home/<USER>/someFile.js');\n```\n\n#### Skipping by callback\n\nYou can customize which trace lines get logged and which won't:\n```javascript\npe.skip(function(traceLine, lineNumber){\n   // if we know which package this trace line comes from, and it isn't\n   // our 'demo' package ...\n   if (typeof traceLine.packageName !== 'undefined' && traceLine.packageName !== 'demo') {\n      // then skip this line\n      return true;\n   }\n\n   // You can console.log(traceLine) to see all of it's properties.\n   // Don't expect all these properties to be present, and don't assume\n   // that our traceLine is always an object.\n});\n```\n\n#### Modifying each trace line's contents\n\n```javascript\npe.filter(function(traceLine, lineNumber){\n   // the 'what' clause is something like:\n   // 'DynamicTimeline.module.exports.DynamicTimeline._verifyProp'\n   if (typeof traceLine.what !== 'undefined'){\n\n      // we can shorten it with a regex:\n      traceLine.what = traceLine.what.replace(\n         /(.*\\.module\\.exports\\.)(.*)/, '$2'\n      );\n   }\n});\n```\n\n## Disabling colors\n```javascript\npe.withoutColors(); // Errors will be rendered without coloring\n```\n\n## Integrating with frameworks\n\nPrettyError is very simple to set up, so it should be easy to use within other frameworks.\n\n### Integrating with [express](https://github.com/visionmedia/express)\n\nMost frameworks such as express, catch errors automatically and provide a mechanism to handle those errors. Here is an example of how you can use PrettyError to log unhandled errors in express:\n\n```javascript\n// this is app.js\n\nvar express = require('express');\nvar PrettyError = require('pretty-error');\n\nvar app = express();\n\napp.get('/', function(req, res) {\n   // this will throw an error:\n   var a = b;\n});\n\nvar server = app.listen(3000, function(){\n   console.log('Server started \\n');\n});\n\n\n// we can now instantiaite Prettyerror:\npe = new PrettyError();\n\n// and use it for our app's error handler:\napp.use(function(err, req, res, next){\n   console.log(pe.render(err));\n   next();\n});\n\n// we can optionally configure prettyError to simplify the stack trace:\n\npe.skipNodeFiles(); // this will skip events.js and http.js and similar core node files\npe.skipPackage('express'); // this will skip all the trace lines about express` core and sub-modules\n```\n\n## Troubleshooting\n\n`PrettyError.start()` modifies the stack traces of all errors thrown anywhere in your code, so it could potentially break packages that rely on node's original stack traces. I've only encountered this problem once, and it was with BlueBird when `Promise.longStackTraces()` was on.\n\nIn order to avoid this problem, it's better to not use `PrettyError.start()` and instead, manually catch errors and render them with PrettyError:\n\n```javascript\nvar PrettyError = require('pretty-error');\nvar pe = new PrettyError();\n\n// To render exceptions thrown in non-promies code:\nprocess.on('uncaughtException', function(error){\n   console.log(pe.render(error));\n});\n\n// To render unhandled rejections created in BlueBird:\nprocess.on('unhandledRejection', function(reason){\n   console.log(\"Unhandled rejection\");\n   console.log(pe.render(reason));\n});\n\n// While PrettyError.start() works out of the box with when.js` unhandled rejections,\n// now that wer'e manually rendering errors, we have to instead use npmjs.org/packages/pretty-monitor\n// to handle when.js rejections.\n\n```\n\nThe only drawback with this approach is that exceptions thrown in the first tick are not prettified. To fix that, you can delay your application's startup for one tick:\n\n```javascript\n// (continued form above)\n\nthrow new Error(); // not prettified\nprocess.nextTick(function(){\n   throw new Error(); // prettified\n});\n\n```\n\n## License\n\nMIT\n", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2013 Aria Minaei\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/pretty-error/-/pretty-error-2.1.2.tgz#be89f82d81b1c86ec8fdfbc385045882727f93b6", "type": "tarball", "reference": "https://registry.yarnpkg.com/pretty-error/-/pretty-error-2.1.2.tgz", "hash": "be89f82d81b1c86ec8fdfbc385045882727f93b6", "integrity": "sha512-EY5oDzmsX5wvuynAByrmY0P0hcp+QpnAKbJng2A2MPjVKXCxrDSUkzghVJ4ZGPIv+JC4gX8fPUWscC0RtjsWGw==", "registry": "npm", "packageName": "pretty-error", "cacheIntegrity": "sha512-EY5oDzmsX5wvuynAByrmY0P0hcp+QpnAKbJng2A2MPjVKXCxrDSUkzghVJ4ZGPIv+JC4gX8fPUWscC0RtjsWGw== sha1-von4LYGxyG7I/fvDhQRYgnJ/k7Y="}, "registry": "npm", "hash": "be89f82d81b1c86ec8fdfbc385045882727f93b6"}