{"manifest": {"name": "has-flag", "version": "3.0.0", "description": "Check if argv has a specific flag", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/has-flag.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["has", "check", "detect", "contains", "find", "flag", "cli", "command-line", "argv", "process", "arg", "args", "argument", "arguments", "getopt", "minimist", "optimist"], "devDependencies": {"ava": "*", "xo": "*"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-has-flag-3.0.0-b5d454dc2199ae225699f3467e5a07f3b955bafd-integrity\\node_modules\\has-flag\\package.json", "readmeFilename": "readme.md", "readme": "# has-flag [![Build Status](https://travis-ci.org/sindresorhus/has-flag.svg?branch=master)](https://travis-ci.org/sindresorhus/has-flag)\n\n> Check if [`argv`](https://nodejs.org/docs/latest/api/process.html#process_process_argv) has a specific flag\n\nCorrectly stops looking after an `--` argument terminator.\n\n\n## Install\n\n```\n$ npm install has-flag\n```\n\n\n## Usage\n\n```js\n// foo.js\nconst hasFlag = require('has-flag');\n\nhasFlag('unicorn');\n//=> true\n\nhasFlag('--unicorn');\n//=> true\n\nhasFlag('f');\n//=> true\n\nhasFlag('-f');\n//=> true\n\nhasFlag('foo=bar');\n//=> true\n\nhasFlag('foo');\n//=> false\n\nhasFlag('rainbow');\n//=> false\n```\n\n```\n$ node foo.js -f --unicorn --foo=bar -- --rainbow\n```\n\n\n## API\n\n### hasFlag(flag, [argv])\n\nReturns a boolean for whether the flag exists.\n\n#### flag\n\nType: `string`\n\nCLI flag to look for. The `--` prefix is optional.\n\n#### argv\n\nType: `string[]`<br>\nDefault: `process.argv`\n\nCLI arguments.\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com)\n", "licenseText": "MIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/has-flag/-/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd", "type": "tarball", "reference": "https://registry.yarnpkg.com/has-flag/-/has-flag-3.0.0.tgz", "hash": "b5d454dc2199ae225699f3467e5a07f3b955bafd", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "registry": "npm", "packageName": "has-flag", "cacheIntegrity": "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw== sha1-tdRU3CGZriJWmfNGfloH87lVuv0="}, "registry": "npm", "hash": "b5d454dc2199ae225699f3467e5a07f3b955bafd"}