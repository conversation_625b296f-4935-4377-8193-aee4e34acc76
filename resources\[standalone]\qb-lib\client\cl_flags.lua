function SetPedFlag(object, flag, enabled)
    exports['qb-flags']:SetPed<PERSON>lag(object, flag, enabled)
end

function HasPedFlag(object, flag)
    return exports['qb-flags']:HasPedFlag(object, flag)
end

function GetPedFlags(object)
    return exports['qb-flags']:GetPedFlags(object)
end

function SetPedFlags(object, flag)
    exports['qb-flags']:SetPedFlags(object, flag)
end

function SetVehicleFlag(vehicle, flag, enabled)
    exports['qb-flags']:SetVehicleFlag(vehicle, flag, enabled)
end

function HasVehicleFlag(vehicle, flag)
    return exports['qb-flags']:HasVehicleFlag(vehicle, flag)
end

function GetVehicleFlags(vehicle)
    return exports['qb-flags']:GetVehicleFlags(vehicle)
end

function SetVehicleFlags(object, flag)
    exports['qb-flags']:SetVehicleFlags(object, flag)
end

function SetObjectFlag(object, flag, enabled)
    exports['qb-flags']:SetObjectFlag(object, flag, enabled)
end

function HasObjectFlag(object, flag)
    return exports['qb-flags']:HasObjectFlag(object, flag)
end

function GetObjectFlags(object)
    return exports['qb-flags']:GetObjectFlags(object)
end

function SetObjectFlags(object, flag)
    exports['qb-flags']:SetObjectFlags(object, flag)
end