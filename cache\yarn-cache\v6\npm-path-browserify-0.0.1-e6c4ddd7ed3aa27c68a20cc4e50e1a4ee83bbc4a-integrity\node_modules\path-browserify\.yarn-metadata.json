{"manifest": {"name": "path-browserify", "version": "0.0.1", "description": "the path module from node core for browsers", "main": "index.js", "dependencies": {}, "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "node test/test-path.js"}, "repository": {"type": "git", "url": "git://github.com/substack/path-browserify.git"}, "homepage": "https://github.com/substack/path-browserify", "keywords": ["path", "browser", "browserify"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-path-browserify-0.0.1-e6c4ddd7ed3aa27c68a20cc4e50e1a4ee83bbc4a-integrity\\node_modules\\path-browserify\\package.json", "readmeFilename": "readme.markdown", "readme": "# path-browserify\n\nthe path module from node core for browsers\n", "licenseText": "This software is released under the MIT license:\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\n<PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/path-browserify/-/path-browserify-0.0.1.tgz#e6c4ddd7ed3aa27c68a20cc4e50e1a4ee83bbc4a", "type": "tarball", "reference": "https://registry.yarnpkg.com/path-browserify/-/path-browserify-0.0.1.tgz", "hash": "e6c4ddd7ed3aa27c68a20cc4e50e1a4ee83bbc4a", "integrity": "sha512-BapA40NHICOS+USX9SN4tyhq+A2RrN/Ws5F0Z5aMHDp98Fl86lX8Oti8B7uN93L4Ifv4fHOEA+pQw87gmMO/lQ==", "registry": "npm", "packageName": "path-browserify", "cacheIntegrity": "sha512-BapA40NHICOS+USX9SN4tyhq+A2RrN/Ws5F0Z5aMHDp98Fl86lX8Oti8B7uN93L4Ifv4fHOEA+pQw87gmMO/lQ== sha1-5sTd1+06onxoogzE5Q4aTug7vEo="}, "registry": "npm", "hash": "e6c4ddd7ed3aa27c68a20cc4e50e1a4ee83bbc4a"}