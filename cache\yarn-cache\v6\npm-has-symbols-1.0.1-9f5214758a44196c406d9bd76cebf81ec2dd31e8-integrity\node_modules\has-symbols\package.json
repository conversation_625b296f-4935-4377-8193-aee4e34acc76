{"name": "has-symbols", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "description": "Determine if the JS environment has Symbol support. Supports spec, or shams.", "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "posttest": "npx aud", "tests-only": "npm run --silent test:stock && npm run --silent test:staging && npm run --silent test:shams", "test:stock": "node test", "test:staging": "node --harmony --es-staging test", "test:shams": "npm run --silent test:shams:getownpropertysymbols && npm run --silent test:shams:corejs", "test:shams:corejs": "node test/shams/core-js.js", "test:shams:getownpropertysymbols": "node test/shams/get-own-property-symbols.js", "lint": "eslint *.js", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/ljharb/has-symbols.git"}, "keywords": ["Symbol", "symbols", "typeof", "sham", "polyfill", "native", "core-js", "ES6"], "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^15.0.1", "auto-changelog": "^1.16.2", "core-js": "^2.6.10", "eslint": "^6.6.0", "get-own-property-symbols": "^0.9.4", "safe-publish-latest": "^1.1.4", "tape": "^4.11.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}}