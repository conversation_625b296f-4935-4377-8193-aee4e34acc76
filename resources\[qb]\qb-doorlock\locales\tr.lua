local Translations = {
    error = {
        not_authorized = "Yet<PERSON><PERSON> değil",
        lockpick_fail = "Başarısız",
        screwdriverset_not_found = "Tornavida setiniz yok",
        door_not_locked = "Ka<PERSON><PERSON> kilitli değil",
        door_not_lockpickable = "Bu kapı maymuncuklanamaz!"
    },
    success = {
        lockpick_success = "Başarılı"
    },
    general = {
        locking = "~r~Kilitleniyor..",
        unlocking = "~g~Açılıyor..",
        locked = "~r~<PERSON><PERSON><PERSON>",
        unlocked = "~g~Açık",
        locked_button = "[~g~E~w~] - <PERSON><PERSON><PERSON>",
        unlocked_button = "[~g~E~w~] - Açık"
    }
}

if GetConvar('qb_locale', 'en') == 'tr' then
    Lang = Locale:new({
        phrases = Translations,
        warnOnMissing = true,
        fallbackLang = Lang,
    })
end