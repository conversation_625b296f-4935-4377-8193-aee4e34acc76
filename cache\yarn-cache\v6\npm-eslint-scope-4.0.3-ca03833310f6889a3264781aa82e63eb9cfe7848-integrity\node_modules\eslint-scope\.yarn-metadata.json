{"manifest": {"name": "eslint-scope", "description": "ECMAScript scope analyzer for ESLint", "homepage": "http://github.com/eslint/eslint-scope", "main": "lib/index.js", "version": "4.0.3", "engines": {"node": ">=4.0.0"}, "repository": {"type": "git", "url": "https://github.com/eslint/eslint-scope.git"}, "bugs": {"url": "https://github.com/eslint/eslint-scope/issues"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "node Makefile.js test", "lint": "node Makefile.js lint", "generate-release": "eslint-generate-release", "generate-alpharelease": "eslint-generate-prerelease alpha", "generate-betarelease": "eslint-generate-prerelease beta", "generate-rcrelease": "eslint-generate-prerelease rc", "publish-release": "eslint-publish-release"}, "files": ["LICENSE", "README.md", "lib"], "dependencies": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "devDependencies": {"chai": "^3.4.1", "eslint": "^3.15.0", "eslint-config-eslint": "^4.0.0", "eslint-release": "^1.0.0", "espree": "^3.1.1", "istanbul": "^0.4.5", "mocha": "^3.2.0", "npm-license": "^0.3.3", "shelljs": "^0.7.6", "typescript": "~2.0.10", "typescript-eslint-parser": "^1.0.0"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-eslint-scope-4.0.3-ca03833310f6889a3264781aa82e63eb9cfe7848-integrity\\node_modules\\eslint-scope\\package.json", "readmeFilename": "README.md", "readme": "# ESLint Scope\n\nESLint Scope is the [ECMAScript](http://www.ecma-international.org/publications/standards/Ecma-262.htm) scope analyzer used in ESLint. It is a fork of [escope](http://github.com/estools/escope).\n\n## Usage\n\nInstall:\n\n```\nnpm i eslint-scope --save\n```\n\nExample:\n\n```js\nvar eslintScope = require('eslint-scope');\nvar espree = require('espree');\nvar estraverse = require('estraverse');\n\nvar ast = espree.parse(code);\nvar scopeManager = eslintScope.analyze(ast);\n\nvar currentScope = scopeManager.acquire(ast);   // global scope\n\nestraverse.traverse(ast, {\n    enter: function(node, parent) {\n        // do stuff\n\n        if (/Function/.test(node.type)) {\n            currentScope = scopeManager.acquire(node);  // get current function scope\n        }\n    },\n    leave: function(node, parent) {\n        if (/Function/.test(node.type)) {\n            currentScope = currentScope.upper;  // set to parent scope\n        }\n\n        // do stuff\n    }\n});\n```\n\n## Contributing\n\nIssues and pull requests will be triaged and responded to as quickly as possible. We operate under the [ESLint Contributor Guidelines](http://eslint.org/docs/developer-guide/contributing), so please be sure to read them before contributing. If you're not sure where to dig in, check out the [issues](https://github.com/eslint/eslint-scope/issues).\n\n## Build Commands\n\n* `npm test` - run all linting and tests\n* `npm run lint` - run all linting\n\n## License\n\nESLint Scope is licensed under a permissive BSD 2-clause license.\n", "licenseText": "Copyright JS Foundation and other contributors, https://js.foundation\nCopyright (C) 2012-2013 <PERSON><PERSON> (twitter: @Constellation) and other contributors.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are met:\n\n  * Redistributions of source code must retain the above copyright\n    notice, this list of conditions and the following disclaimer.\n  * Redistributions in binary form must reproduce the above copyright\n    notice, this list of conditions and the following disclaimer in the\n    documentation and/or other materials provided with the distribution.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\nAND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\nIMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\nARE DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\nDIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\nLOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\nON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\nTHIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/eslint-scope/-/eslint-scope-4.0.3.tgz#ca03833310f6889a3264781aa82e63eb9cfe7848", "type": "tarball", "reference": "https://registry.yarnpkg.com/eslint-scope/-/eslint-scope-4.0.3.tgz", "hash": "ca03833310f6889a3264781aa82e63eb9cfe7848", "integrity": "sha512-p7VutNr1O/QrxysMo3E45FjYDTeXBy0iTltPFNSqKAIfjDSXC+4dj+qfyuD8bfAXrW/y6lW3O76VaYNPKfpKrg==", "registry": "npm", "packageName": "eslint-scope", "cacheIntegrity": "sha512-p7VutNr1O/QrxysMo3E45FjYDTeXBy0iTltPFNSqKAIfjDSXC+4dj+qfyuD8bfAXrW/y6lW3O76VaYNPKfpKrg== sha1-ygODMxD2iJoyZHgaqC5j65z+eEg="}, "registry": "npm", "hash": "ca03833310f6889a3264781aa82e63eb9cfe7848"}