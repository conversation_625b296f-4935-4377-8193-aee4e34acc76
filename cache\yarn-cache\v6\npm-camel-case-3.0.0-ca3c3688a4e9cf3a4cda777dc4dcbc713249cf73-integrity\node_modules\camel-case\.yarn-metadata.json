{"manifest": {"name": "camel-case", "version": "3.0.0", "description": "Camel case a string", "main": "camel-case.js", "typings": "camel-case.d.ts", "files": ["camel-case.js", "camel-case.d.ts", "LICENSE"], "scripts": {"lint": "standard", "test-spec": "mocha -- -R spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail", "test": "npm run lint && npm run test-cov"}, "repository": {"type": "git", "url": "git://github.com/blakeembrey/camel-case.git"}, "keywords": ["camel", "case", "camelcase", "camel-case", "dash", "hyphen", "dot", "underscore", "lodash", "separator", "string", "text", "convert"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/blakeembrey/camel-case/issues"}, "homepage": "https://github.com/blakeembrey/camel-case", "devDependencies": {"istanbul": "^0.4.3", "mocha": "^2.2.1", "standard": "^7.1.2"}, "dependencies": {"no-case": "^2.2.0", "upper-case": "^1.1.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-camel-case-3.0.0-ca3c3688a4e9cf3a4cda777dc4dcbc713249cf73-integrity\\node_modules\\camel-case\\package.json", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 <PERSON> (<EMAIL>)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/camel-case/-/camel-case-3.0.0.tgz#ca3c3688a4e9cf3a4cda777dc4dcbc713249cf73", "type": "tarball", "reference": "https://registry.yarnpkg.com/camel-case/-/camel-case-3.0.0.tgz", "hash": "ca3c3688a4e9cf3a4cda777dc4dcbc713249cf73", "integrity": "sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=", "registry": "npm", "packageName": "camel-case", "cacheIntegrity": "sha512-+MbKztAYHXPr1jNTSKQF52VpcFjwY5RkR7fxksV8Doo4KAYc5Fl4UJRgthBbTmEx8C54DqahhbLJkDwjI3PI/w== sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M="}, "registry": "npm", "hash": "ca3c3688a4e9cf3a4cda777dc4dcbc713249cf73"}