{"manifest": {"name": "memory-fs", "version": "0.5.0", "description": "A simple in-memory filesystem. Holds data in a javascript object.", "main": "lib/MemoryFileSystem.js", "files": ["lib/"], "scripts": {"test": "mocha", "lint": "eslint lib/*", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly && npm run lint"}, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}, "repository": {"type": "git", "url": "https://github.com/webpack/memory-fs.git"}, "keywords": ["fs", "memory"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "bugs": {"url": "https://github.com/webpack/memory-fs/issues"}, "homepage": "https://github.com/webpack/memory-fs", "devDependencies": {"bl": "^1.0.0", "codecov.io": "^0.1.4", "coveralls": "^2.11.2", "eslint": "^4.0.0", "istanbul": "0.4.5", "mocha": "3.2.0", "should": "^4.0.4"}, "dependencies": {"errno": "^0.1.3", "readable-stream": "^2.0.1"}, "_registry": "npm", "_loc": "D:\\txData\\QBCore_4070F0.base\\cache\\yarn-cache\\v6\\npm-memory-fs-0.5.0-324c01288b88652966d161db77838720845a8e3c-integrity\\node_modules\\memory-fs\\package.json", "readmeFilename": "README.md", "readme": "# memory-fs\n\nA simple in-memory filesystem. Holds data in a javascript object.\n\n``` javascript\nvar MemoryFileSystem = require(\"memory-fs\");\nvar fs = new MemoryFileSystem(); // Optionally pass a javascript object\n\nfs.mkdirpSync(\"/a/test/dir\");\nfs.writeFileSync(\"/a/test/dir/file.txt\", \"Hello World\");\nfs.readFileSync(\"/a/test/dir/file.txt\"); // returns Buffer(\"Hello World\")\n\n// Async variants too\nfs.unlink(\"/a/test/dir/file.txt\", function(err) {\n\t// ...\n});\n\nfs.readdirSync(\"/a/test\"); // returns [\"dir\"]\nfs.statSync(\"/a/test/dir\").isDirectory(); // returns true\nfs.rmdirSync(\"/a/test/dir\");\n\nfs.mkdirpSync(\"C:\\\\use\\\\windows\\\\style\\\\paths\");\n```\n\n## License\n\nCopyright (c) 2012-2014 <PERSON> (http://www.opensource.org/licenses/mit-license.php)\n", "licenseText": "Copyright JS Foundation and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/memory-fs/-/memory-fs-0.5.0.tgz#324c01288b88652966d161db77838720845a8e3c", "type": "tarball", "reference": "https://registry.yarnpkg.com/memory-fs/-/memory-fs-0.5.0.tgz", "hash": "324c01288b88652966d161db77838720845a8e3c", "integrity": "sha512-jA0rdU5KoQMC0e6ppoNRtpp6vjFq6+NY7r8hywnC7V+1Xj/MtHwGIbB1QaK/dunyjWteJzmkpd7ooeWg10T7GA==", "registry": "npm", "packageName": "memory-fs", "cacheIntegrity": "sha512-jA0rdU5KoQMC0e6ppoNRtpp6vjFq6+NY7r8hywnC7V+1Xj/MtHwGIbB1QaK/dunyjWteJzmkpd7ooeWg10T7GA== sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw="}, "registry": "npm", "hash": "324c01288b88652966d161db77838720845a8e3c"}